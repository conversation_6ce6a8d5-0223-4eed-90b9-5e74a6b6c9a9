#服务器端口
server:
  port: 8085

#数据源配置
spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: officedb
      datasource:
        officedb:
          url: ${get.datasource.local.officedb.url}
          username: ${get.datasource.local.officedb.username}
          password: ${get.datasource.local.officedb.password}
        officedb-doris:
          url: ${get.datasource.local.officedb-doris.url}
          username: ${get.datasource.local.officedb-doris.username}
          password: ${get.datasource.local.officedb-doris.password}
wx:
  cptp:
    corpId: ww26b22f8ae71108e8
    corpsecret: BrQEHtIqQvz6IA7AU41poiwvBaDA6KpkUkwN9HHAK-Y
    appConfigs:
      - suiteId: wwf953aba73d7d8344
        secret: oHXPyxDqbhXo7oSVTRfIUCABWgJlchLhp4GESdJEeqA
        token: vlwt
        aesKey: 78PpahUMtFAmfpkntBWITaXye16Nl35TusHm1xIF717

