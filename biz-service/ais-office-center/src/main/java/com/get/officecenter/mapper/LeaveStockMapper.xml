<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.officecenter.mapper.LeaveStockMapper">
    <select id="selectLeaveStock" resultType="com.get.officecenter.entity.LeaveStock">
        SELECT * FROM m_leave_stock
        <where>
            (DATE_FORMAT(effective_deadline,'%Y-%m-%d %H:%i:%S') &gt;= DATE_FORMAT(NOW(),'%Y-%m-%d %H:%i:%S') OR effective_deadline IS NULL)
            AND leave_stock &lt;&gt; 0
            <if test="fkCompanyId !=null">
                AND fk_company_id = #{fkCompanyId}
            </if>
            <if test="leaveTypeKey != null and leaveTypeKey != ''">
                AND leave_type_key = #{leaveTypeKey}
            </if>
            <if test="effectiveDeadline != null">
                AND DATE_FORMAT(effective_deadline,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{effectiveDeadline},'%Y-%m-%d' )
            </if>
            <if test="fkStaffIds != null and fkStaffIds.size()>0">
                AND fk_staff_id IN
                <foreach collection="fkStaffIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null and id !=''">
                AND id =#{id}
            </if>
            ORDER BY effective_deadline ASC
        </where>
    </select>
    <select id="getLeaveStockByStaffIds" resultType="com.get.officecenter.vo.LeaveStockVo">
        SELECT fk_staff_id AS fkStaffId,IFNULL(SUM(leave_stock),0) AS leaveStockSum
        FROM m_leave_stock
        <where>
             AND fk_company_id = #{fkCompanyId}
        <if test="fkCompanyId != null and fkCompanyId !=3">
           AND (DATE_FORMAT(effective_deadline,'%Y-%m-%d') &gt;= DATE_FORMAT(#{endTime},'%Y-%m-%d')
            OR effective_deadline IS NULL)
        </if>
            AND leave_type_key=#{leaveTypeKey}
            <if test="fkStaffIds != null">
                AND fk_staff_id IN
                <foreach collection="fkStaffIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
         GROUP BY fk_staff_id
    </select>

    <select id="getLeaveStockByStaffId" resultType="com.get.officecenter.vo.LeaveStockVo">
        SELECT fk_staff_id AS fkStaffId,IFNULL(SUM(leave_stock),0) AS leaveStockSum
                FROM m_leave_stock
                <where>
                    AND leave_type_key=#{leaveTypeKey}
                    AND fk_staff_id = #{fkStaffId}
                    AND (DATE_FORMAT(effective_deadline,'%Y%m%d') >= DATE_FORMAT(CURRENT_DATE,'%Y%m%d') or effective_deadline is null)
                </where>
    </select>

    <select id="getAnnualVacationByLastYear" resultType="com.get.officecenter.vo.LeaveStockVo">
        SELECT fk_staff_id AS fkStaffId,IFNULL(SUM(leave_stock),0) AS leaveStockSum
        FROM m_leave_stock
        WHERE YEAR(DATE_SUB(effective_deadline,INTERVAL 1 YEAR)) = YEAR(DATE_SUB(#{date},INTERVAL 1 YEAR))
        AND leave_type_key = #{leaveTypeKey}
        AND fk_company_id = #{fkCompanyId}
        <if test="fkStaffIds != null">
            AND fk_staff_id IN
            <foreach collection="fkStaffIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY fk_staff_id
    </select>
    <select id="getAnnualVacationByThisYear" resultType="com.get.officecenter.vo.LeaveStockVo">
        SELECT fk_staff_id AS fkStaffId,IFNULL(SUM(leave_stock),0) AS leaveStockSum
        FROM m_leave_stock
        WHERE  YEAR(DATE_SUB(effective_deadline,INTERVAL 1 YEAR)) = YEAR(#{date})
        AND leave_type_key = #{leaveTypeKey}
        AND fk_company_id = #{fkCompanyId}
        <if test="fkStaffIds != null">
            AND fk_staff_id IN
            <foreach collection="fkStaffIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY fk_staff_id
    </select>
    <select id="getDiseaseVacationQuarter" resultType="com.get.officecenter.vo.LeaveStockVo">
        SELECT fk_staff_id AS fkStaffId , IFNULL(SUM(leave_stock),0) AS leaveStockSum
        FROM m_leave_stock
        <where>
            AND (
            (YEAR(effective_deadline) = YEAR(#{date}) AND QUARTER(effective_deadline)= QUARTER(#{date}))
            OR effective_deadline IS NULL
            )
            AND leave_type_key = #{leaveTypeKey}
            AND fk_company_id = #{fkCompanyId}
            <if test="fkStaffIds != null">
                AND fk_staff_id IN
                <foreach collection="fkStaffIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY fk_staff_id
    </select>
    <select id="getStaffOfLeaveStockList" resultType="com.get.officecenter.vo.StaffOfLeaveStockVo">
        SELECT
            b.id as fkStaffId,
            concat(if(b.is_on_duty=1,'','【离职】'),b.name,IFNULL(concat('(',b.name_en,')'),'')) as staffFullName,
            c.`name` as departmentName
        FROM
            ( SELECT fk_staff_id FROM m_leave_stock
            WHERE 1=1
              and leave_stock > 0
              and (DATE_FORMAT(effective_deadline,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(CURDATE(),'%Y-%m-%d') OR effective_deadline IS NULL)
              <if test="staffOfLeaveStockDto.fkCompanyId !=null">
                and fk_company_id = #{staffOfLeaveStockDto.fkCompanyId}
              </if>
              <if test="staffOfLeaveStockDto.leaveTypeKey !=null and staffOfLeaveStockDto.leaveTypeKey !=''">
                and leave_type_key like #{staffOfLeaveStockDto.leaveTypeKey}
              </if>
            GROUP BY fk_staff_id ) a
                LEFT JOIN ais_permission_center.m_staff b on b.id = a.fk_staff_id
                LEFT JOIN ais_permission_center.m_department c on c.id = b.fk_department_id
        WHERE b.is_on_duty = 1
        <if test="staffOfLeaveStockDto.fkStaffName !=null">
            and (
                b.name like concat("%",#{staffOfLeaveStockDto.fkStaffName},"%")
                or
                b.name_en like concat("%",#{staffOfLeaveStockDto.fkStaffName},"%")
            )
        </if>
        ORDER BY b.name_en
    </select>
    <select id="selectEfficientLeaveStock" resultType="com.get.officecenter.entity.LeaveStock">
        SELECT * FROM m_leave_stock
        <where>
            (DATE_FORMAT(effective_deadline,'%Y-%m-%d') &gt;= DATE_FORMAT(CURDATE(),'%Y-%m-%d') OR effective_deadline IS NULL)
            <if test="fkCompanyId !=null">
                AND fk_company_id = #{fkCompanyId}
            </if>
            <if test="leaveTypeKey != null and leaveTypeKey != ''">
                AND leave_type_key = #{leaveTypeKey}
            </if>
            <if test="effectiveDeadline != null">
                AND DATE_FORMAT(effective_deadline,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{effectiveDeadline},'%Y-%m-%d' )
            </if>
            <if test="fkStaffIds != null and fkStaffIds.size()>0">
                AND fk_staff_id IN
                <foreach collection="fkStaffIds" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="id != null and id !=''">
                AND id =#{id}
            </if>
            ORDER BY effective_deadline ASC
        </where>
    </select>


</mapper>