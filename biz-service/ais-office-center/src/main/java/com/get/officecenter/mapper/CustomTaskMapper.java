package com.get.officecenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.officecenter.vo.CustomTaskVo;
import com.get.officecenter.entity.Task;
import com.get.officecenter.dto.CustomTaskSearchDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:cream
 * @Date: 2023/5/30  9:46
 */
@Mapper
public interface CustomTaskMapper extends GetMapper<Task> {

    /**
     * 获取任务列表
     *
     * @param iPage              分页
     * @param customTaskSearchDto 查询参数
     * @param curStaffId         当前登录人Id
     * @param staffFollowerIds   下属Id集合
     * @param staffIds           登录人 + 下属Id集合
     * @return
     */
    @DS("officedb-doris")
    List<CustomTaskVo> taskList(IPage<CustomTaskVo> iPage,
                                @Param("customTaskSearchDto") CustomTaskSearchDto customTaskSearchDto,
                                @Param("curStaffId") Long curStaffId,
                                @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                @Param("staffIds") List<Long> staffIds);

    List<Task> getTaskByEndTime();
}
