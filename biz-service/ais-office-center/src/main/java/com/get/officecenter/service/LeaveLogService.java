package com.get.officecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.service.GetService;
import com.get.officecenter.vo.LeaveLogVo;
import com.get.officecenter.vo.StaffOfLeaveLogVo;
import com.get.officecenter.entity.LeaveLog;
import com.get.officecenter.dto.LeaveLogDto;
import com.get.officecenter.dto.StaffOfLeaveLogDto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/1/14
 * @TIME: 11:22
 * @Description:
 **/
public interface LeaveLogService extends GetService<LeaveLog> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    LeaveLogVo findLeaveLogById(Long id);

    /**
     * 列表数据
     *
     * @param leaveLogDto
     * @param page
     * @return
     */
    List<LeaveLogVo> getLeaveLogs(LeaveLogDto leaveLogDto, Page page);

    /**
     * 新增公休日志
     *
     * @param leaveLogDto
     */
    void addLeaveLog(LeaveLogDto leaveLogDto);

    /**
     * 新增公休日志(工作流)
     *
     * @param leaveLogDto
     */
    Boolean addSystemLeaveLog(LeaveLogDto leaveLogDto);

    /**
     * 根据员工IDs获取公休记录
     * @param fkStaffIds
     * @param fkCompanyId
     * @param startTime
     * @param endTime
     * @return
     */
    List<LeaveLogVo> getLeaveLogByfkStaffIds(Set<Long> fkStaffIds, Long fkCompanyId, Date startTime, Date endTime);

    /**
     * 获取指定时间之后所有影响补休时长的日志记录合计时长
     *
     * @param fkStaffIds
     * @param endTime
     * @return
     */
    Map<Long, BigDecimal> getLeaveLogsAboutTakeDeferredHoliday(Set<Long> fkStaffIds,Long fkCompanyId, Date endTime);

    /**
     * 获取指定时间之后所有影响上一年年假时长的日志记录合计时长
     *
     * @param fkStaffIds
     * @param date
     * @return
     */
    Map<Long, BigDecimal> getLeaveLogsAboutAnnualVacationLastYear(Set<Long> fkStaffIds,Long fkCompanyId,Date endTime, Date date);

    /**
     * 获取考勤范围时间之后所有变更今年年假时长的日志记录合计时长
     *
     * @param fkStaffIds
     * @param date
     * @return
     * @
     */
    Map<Long, BigDecimal> getLeaveLogsAboutAnnualVacationThisYear(Set<Long> fkStaffIds,Long fkCompanyId,Date endTime,Date date);

    /**
     * 获取指定时间之后所有影响病假时长的日志记录合计时长
     *
     * @param fkStaffIds
     * @param endTime
     * @return
     * @
     */
    Map<Long, BigDecimal> getLeaveLogsAboutDiseaseVacationQuarter(Set<Long> fkStaffIds,Set<Long> fkLeaveStockIds,Long fkCompanyId, Date endTime);

    /**
     * 获取考勤时间范围已休上一年年假记录
     *
     * @param fkStaffIds
     * @return
     */
    List<LeaveLogVo> getAnnualVacationLastYearLog(Set<Long> fkStaffIds, Long fkCompanyId, Date startTime, Date endTime, Date date);

    /**
     *获取考勤时间范围已休今年年假记录
     *
     * @param fkStaffIds
     * @return
     */
    List<LeaveLogVo> getAnnualVacationThisYearLog(Set<Long> fkStaffIds, Long fkCompanyId, Date startTime, Date endTime, Date date);

    /**
     * 获取系统日志
     *
     * @param leaveLogDto
     * @return
     */
    List<LeaveLogVo> getSystemLeaveLog(LeaveLogDto leaveLogDto);

    /**
     * 移动端休假记录员工列表
     *
     * @param staffOfLeaveLogDto
     * @param page
     * @return
     */
    List<StaffOfLeaveLogVo> getStaffOfLeaveLogList(StaffOfLeaveLogDto staffOfLeaveLogDto, Page page);
}
