package com.get.officecenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.officecenter.entity.LeaveApplicationFormTypeCompany;
import com.get.officecenter.mapper.LeaveApplicationFormTypeCompanyMapper;
import com.get.officecenter.service.ILeaveApplicationFormTypeCompanyService;
import com.get.officecenter.dto.LeaveApplicationFormTypeCompanyDto;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.google.common.collect.Lists;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2023/3/29 15:20
 * @verison: 1.0
 * @description:
 */
@Service
public class LeaveApplicationFormTypeCompanyServiceImpl extends GetServiceImpl<LeaveApplicationFormTypeCompanyMapper, LeaveApplicationFormTypeCompany> implements ILeaveApplicationFormTypeCompanyService {

    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    /**
     * 工休申请类型-公司安全配置
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void editLeaveApplicationFormTypeCompanyRelation(List<LeaveApplicationFormTypeCompanyDto> leaveApplicationFormTypeCompanyDtos) {
        if (GeneralTool.isEmpty(leaveApplicationFormTypeCompanyDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Long fkCompanyId = leaveApplicationFormTypeCompanyDtos.get(0).getFkCompanyId();
        Long fkLeaveApplicationFormTypeId = leaveApplicationFormTypeCompanyDtos.get(0).getFkLeaveApplicationFormTypeId();
        if (GeneralTool.isEmpty(fkCompanyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("edit_companyId"));
        }
        List<LeaveApplicationFormTypeCompany> leaveApplicationFormTypeCompanies = BeanCopyUtils.copyListProperties(leaveApplicationFormTypeCompanyDtos, LeaveApplicationFormTypeCompany::new);
        //删除之前的记录
        List<Long> companyIds = SecureUtil.getCompanyIds();
        boolean removeFlag = remove(Wrappers.<LeaveApplicationFormTypeCompany>lambdaQuery().eq(LeaveApplicationFormTypeCompany::getFkLeaveApplicationFormTypeId, fkLeaveApplicationFormTypeId).in(LeaveApplicationFormTypeCompany::getFkCompanyId,companyIds));
        if (!removeFlag){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        //插入记录
        leaveApplicationFormTypeCompanies.forEach(leaveApplicationFormTypeCompany -> utilService.setCreateInfo(leaveApplicationFormTypeCompany));
        boolean saveBatchFlag = saveBatch(leaveApplicationFormTypeCompanies);
        if (!saveBatchFlag){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
    }

    /**
     * 回显工休申请类型和公司的关系
     * @param id
     * @return
     */
    @Override
    public List<CompanyTreeVo> getLeaveApplicationFormTypeCompanyRelation(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = getCompanyTreeDto();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }
        List<LeaveApplicationFormTypeCompany> relation = list(Wrappers.<LeaveApplicationFormTypeCompany>lambdaQuery().eq(LeaveApplicationFormTypeCompany::getFkLeaveApplicationFormTypeId, id));
        setAgentFlag(companyTreeVo, relation);
        return getTreeList(companyTreeVo);
    }

    private void setAgentFlag(List<CompanyTreeVo> companyTreeVo, List<LeaveApplicationFormTypeCompany> relation) {
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (LeaveApplicationFormTypeCompany leaveApplicationFormTypeCompany : relation) {
                if (treeDto.getId().equals(leaveApplicationFormTypeCompany.getFkCompanyId())) {
                    treeDto.setFlag(true);
                }
            }
        }
    }


    private List<CompanyTreeVo> getCompanyTreeDto() {
        Result<List<CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            JsonConfig config = new JsonConfig();
            config.setExcludes(new String[]{"departmentTree", "totalNum"});
            JSONArray jsonArray = JSONArray.fromObject(result.getData(), config);
            return JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
        }
        return Lists.newArrayList();
    }

    private List<CompanyTreeVo> getTreeList(List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                //获取父节点的部门信息
                resultList.add(entity);
            }
        }
        //假如没有父节点
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点
            CompanyTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(CompanyTreeVo::getFkParentCompanyId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点
                List<CompanyTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentCompanyId().equals(minTreeNode.getFkParentCompanyId()) &&
                                !treeDto.getId().equals(minTreeNode.getId())).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        // 获取每个顶层元素的子数据集合
        for (CompanyTreeVo entity : resultList) {
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        return resultList;
    }

    private List<CompanyTreeVo> getSubList(Long id, List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> childList = new ArrayList<>();
        Long parentId;
        // 子集的直接子对象
        for (CompanyTreeVo entity : entityList) {
            parentId = entity.getFkParentCompanyId();
            if (id.equals(parentId)) {
                //获取子节点的部门信息
                childList.add(entity);
            }
        }
        // 子集的间接子对象
        for (CompanyTreeVo entity : childList) {
            //递归调用
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }

}
