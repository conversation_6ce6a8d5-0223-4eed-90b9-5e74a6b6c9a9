package com.get.officecenter.service;

import com.get.core.mybatis.service.GetService;
import com.get.officecenter.entity.LeaveApplicationFormTypeCompany;
import com.get.officecenter.dto.LeaveApplicationFormTypeCompanyDto;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/3/29 15:19
 * @verison: 1.0
 * @description:
 */
public interface ILeaveApplicationFormTypeCompanyService extends GetService<LeaveApplicationFormTypeCompany> {

    /**
     * 工休申请类型-公司安全配置
     * @param leaveApplicationFormTypeCompanyDtos
     */
    void editLeaveApplicationFormTypeCompanyRelation(List<LeaveApplicationFormTypeCompanyDto> leaveApplicationFormTypeCompanyDtos);

    /**
     * 回显工休申请类型和公司的关系
     * @param id
     * @return
     */
    List<CompanyTreeVo> getLeaveApplicationFormTypeCompanyRelation(Long id);
}
