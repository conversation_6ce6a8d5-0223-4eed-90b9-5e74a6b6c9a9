<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.officecenter.mapper.CustomTaskMapper">

    <select id="taskList" resultType="com.get.officecenter.vo.CustomTaskVo">
        SELECT
        t.id AS id,
        ANY_VALUE(t.num) AS num,
        ANY_VALUE(t.fk_staff_id_from) AS fkStaffIdFrom,
        ANY_VALUE(t.fk_staff_id_to) AS fkStaffIdTo,
        ANY_VALUE(t.task_description) AS taskDescription,
        ANY_VALUE(t.task_deadline) AS taskDeadline,
        ANY_VALUE(t.status_modified_time) AS statusModifiedTime,
        ANY_VALUE(t.gmt_create) AS gmtCreate,
        ANY_VALUE(t.gmt_create_user) AS gmtCreateUser,
        ANY_VALUE(t.gmt_modified) AS gmtModified,
        ANY_VALUE(t.gmt_modified_user) AS gmtModifiedUser,
        ANY_VALUE(
        CASE
        WHEN t.task_deadline IS NOT NULL AND DATE_FORMAT(t.task_deadline, '%Y-%m-%d') &lt; DATE_FORMAT(CURRENT_DATE(), '%Y-%m-%d') AND t.status != 1 THEN 2
        ELSE t.status
        END
        ) AS status,
        ANY_VALUE(latest_item.status) AS taskItemStatus,
        ANY_VALUE(latest_comments.comment) AS comment,
        ANY_VALUE(latest_item.status_modified_time) AS statusModifiedTimeItem,
        CASE WHEN ANY_VALUE(mi.fk_staff_id_to) IS NOT NULL THEN 2 ELSE 3 END AS taskType,
        MAX(COALESCE(task_stats.todo_count, 0)) AS taskItemTodoCount,
        MAX(COALESCE(task_stats.finished_count, 0)) AS taskItemFinishedCount,
        MAX(COALESCE(task_stats.unfinished_count, 0)) AS taskItemUnfinishedCount,
        MAX(COALESCE(task_stats.total_count, 0)) AS taskItemTotal,
        <!-- =============== 新增开始：当前登录人的子任务统计字段 =============== -->
        MAX(COALESCE(cur_user_stats.todo_count, 0)) AS subTaskPendingCount,
        MAX(COALESCE(cur_user_stats.finished_count, 0)) AS subTaskCompletedCount,
        MAX(COALESCE(cur_user_stats.unfinished_count, 0)) AS subTaskUncompletedCount,
        MAX(COALESCE(cur_user_stats.total_count, 0)) AS subTaskTotal,
        <!-- =============== 新增结束 =============== -->
        <!--最新子任务统计状态 -->
        GREATEST(
        IFNULL(ANY_VALUE(t.gmt_create), '1000-01-01 00:00:00'),
        IFNULL(ANY_VALUE(t.gmt_modified), '1000-01-01 00:00:00'),
        IFNULL(ANY_VALUE(t.status_modified_time), '1000-01-01 00:00:00')
        ) AS max_date
        FROM
        m_task t
                <!--最新子任务状态 -->
        LEFT JOIN (
        SELECT
        any_value(ti1.fk_task_id) AS fkTaskId,
        MAX(ti1.status) AS status,
        any_value(ti1.status_modified_time) AS status_modified_time,
        MAX(ti1.id) AS item_id
        FROM
        m_task_item ti1
        INNER JOIN (
        SELECT
        fk_task_id,
        MAX(status_modified_time) AS max_modified_time
        FROM
        m_task_item
        GROUP BY
        fk_task_id,status_modified_time
        ) ti2 ON ti1.fk_task_id = ti2.fk_task_id
        AND ti1.status_modified_time = ti2.max_modified_time
        GROUP BY ti1.fk_task_id
        ) latest_item ON t.id = latest_item.fkTaskId
                <!--最新主任务评论状态 -->
        LEFT JOIN (
        SELECT
        task_id,
        comment,
        comment_time,
        ROW_NUMBER() OVER (PARTITION BY task_id ORDER BY comment_time DESC) AS rn
        FROM (
        <!-- 任务自身的最新评论-->
        SELECT
        fk_table_id AS task_id,
        comment,
        gmt_create AS comment_time
        FROM (
        SELECT
        fk_table_id,
        comment,
        gmt_create,
        ROW_NUMBER() OVER (PARTITION BY fk_table_id ORDER BY gmt_create DESC) AS rn
        FROM
        s_comment
        WHERE
        fk_table_name = 'm_task'
        ) t
        WHERE t.rn = 1

        UNION ALL

         <!--任务下子任务的最新评论 -->
        SELECT
        mti.fk_task_id AS task_id,
        sc.comment,
        sc.gmt_create AS comment_time
        FROM
        s_comment AS sc
        INNER JOIN
        m_task_item AS mti ON mti.id = sc.fk_table_id AND sc.fk_table_name = 'm_task_item'
        INNER JOIN (
        SELECT
        mti2.fk_task_id,
        MAX(sc2.id) AS sc_id
        FROM
        s_comment AS sc2
        INNER JOIN
        m_task_item AS mti2 ON mti2.id = sc2.fk_table_id AND sc2.fk_table_name = 'm_task_item'
        GROUP BY
        mti2.fk_task_id
        ) latest ON mti.fk_task_id = latest.fk_task_id AND sc.id = latest.sc_id
        ) combined_comments
        ) latest_comments ON t.id = latest_comments.task_id AND latest_comments.rn = 1
        <!--每一种状态的数量 -->
        <!--该申请计划是否包含待办状态，是否包含已完成状态，是否包含未完成状态 -->
        LEFT JOIN (
        SELECT
        fk_task_id,
        SUM(is_todo) AS todo_count,
        SUM(is_finished) AS finished_count,
        SUM(is_unfinished) AS unfinished_count,
        COUNT(*) AS total_count
        FROM (
        SELECT
        fk_task_id,
        fk_table_id,
        MAX(status = 0) AS is_todo,
        MAX(status = 1) AS is_finished,
        MAX(status = 2) AS is_unfinished
        FROM
        m_task_item
        <!--先按申请计划分组 -->
        GROUP BY
        fk_task_id, fk_table_id
        ) plan_stats
        GROUP BY fk_task_id
        ) task_stats ON t.id = task_stats.fk_task_id

        <!-- =============== 新增开始：当前登录人的子任务统计JOIN =============== -->
        LEFT JOIN (
        SELECT
        fk_task_id,
        SUM(is_todo) AS todo_count,
        SUM(is_finished) AS finished_count,
        SUM(is_unfinished) AS unfinished_count,
        COUNT(*) AS total_count
        FROM (
        SELECT
        fk_task_id,
        fk_table_id,
        MAX(status = 0) AS is_todo,
        MAX(status = 1) AS is_finished,
        MAX(status = 2) AS is_unfinished
        FROM
        m_task_item
        WHERE fk_staff_id_to = #{curStaffId}  <!-- 只统计当前登录人的子任务 -->
        GROUP BY
        fk_task_id, fk_table_id
        ) plan_stats
        GROUP BY fk_task_id
        ) cur_user_stats ON t.id = cur_user_stats.fk_task_id
         <!--根据当前的登录人判断每个任务的类型 -->
        LEFT JOIN (
        SELECT
        fk_task_id,
        MAX(fk_staff_id_to) AS fk_staff_id_to
        FROM
        m_task_item
        WHERE
        fk_staff_id_to = #{curStaffId}
        GROUP BY
        fk_task_id
        ) mi ON mi.fk_task_id = t.id

        <!-- 根据类型选择对应任务-->
        INNER JOIN (
        SELECT DISTINCT id FROM (
        <choose>
            <!-- 没有选择【我的委派】【我的任务】【下属任务】 -->
            <when test="!customTaskSearchDto.isMyDelegation and !customTaskSearchDto.isMyTask and !customTaskSearchDto.isSubordinateTask">
                <if test="staffIds != null and staffIds.size() > 0">
                    SELECT id FROM m_task WHERE fk_staff_id_from IN
                    <foreach collection="staffIds" item="staffId" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>
                    OR fk_staff_id_to IN
                    <foreach collection="staffIds" item="staffId" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>
                    OR
                    <foreach collection="staffIds" item="staffId" separator=" OR ">
                         FIND_IN_SET(#{staffId}, fk_staff_ids_view) > 0
                    </foreach>
                    UNION ALL
                    SELECT fk_task_id AS id FROM m_task_item WHERE fk_staff_id_to IN
                    <foreach collection="staffIds" item="staffId" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>
                </if>
            </when>
            <!-- 选择了【我的委派】【我的任务】【下属任务】 -->
            <otherwise>
                <trim suffixOverrides="UNION ALL">
                    <trim suffix="UNION ALL">
                        <if test="customTaskSearchDto.isMyDelegation and curStaffId != null">
                            SELECT id FROM m_task WHERE fk_staff_id_from = #{curStaffId}
                        </if>
                    </trim>
                    <trim suffix="UNION ALL">
                        <if test="customTaskSearchDto.isMyTask and curStaffId != null">
                            SELECT id FROM m_task WHERE fk_staff_id_to = #{curStaffId}
                            UNION ALL
                            SELECT fk_task_id AS id FROM m_task_item WHERE fk_staff_id_to = #{curStaffId}
                        </if>
                    </trim>
                    <trim suffix="UNION ALL">
                        <if test="customTaskSearchDto.isSubordinateTask and staffFollowerIds != null and staffFollowerIds.size() > 0">
                            SELECT id FROM m_task WHERE fk_staff_id_to IN
                            <foreach collection="staffFollowerIds" item="staffId" open="(" separator="," close=")">
                                #{staffId}
                            </foreach>
                            UNION ALL
                            SELECT fk_task_id AS id FROM m_task_item WHERE fk_staff_id_to IN
                            <foreach collection="staffFollowerIds" item="staffId" open="(" separator="," close=")">
                                #{staffId}
                            </foreach>
                        </if>
                    </trim>
                    <!-- 默认不存在的值，避免报错 -->
                    <trim suffix="UNION ALL">
                        SELECT -100 AS id
                    </trim>
                </trim>
            </otherwise>
        </choose>
        ) temp
        ) x ON x.id = t.id
        WHERE 1 = 1
        <if test="customTaskSearchDto.delegate!=null and customTaskSearchDto.delegate!=''">
            AND t.fk_staff_id_from = #{customTaskSearchDto.delegate}
        </if>
        <if test="customTaskSearchDto.recipient!=null and customTaskSearchDto.recipient!=''">
            AND t.fk_staff_id_to = #{customTaskSearchDto.recipient}
        </if>
        <if test="customTaskSearchDto.taskDescription!=null and customTaskSearchDto.taskDescription!=''">
            AND t.task_description LIKE CONCAT('%',#{customTaskSearchDto.taskDescription},'%')
        </if>
        <if test="customTaskSearchDto.taskStatus!=null">
            AND t.status = #{customTaskSearchDto.taskStatus}
        </if>
        <if test="customTaskSearchDto.isToday!=null and customTaskSearchDto.isToday">
            AND DATE(t.gmt_create) = CURDATE()
        </if>
        <if test="customTaskSearchDto.isWithinAWeek!=null and customTaskSearchDto.isWithinAWeek">
            AND t.gmt_create >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        </if>
        <if test="customTaskSearchDto.isWithinOneMonth!=null and customTaskSearchDto.isWithinOneMonth">
            AND t.gmt_create >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
        </if>
        <if test="customTaskSearchDto.isWithinThreeMonths!=null and customTaskSearchDto.isWithinThreeMonths">
            AND t.gmt_create >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
        </if>
        <if test="customTaskSearchDto.isMultiTask!=null and customTaskSearchDto.isMultiTask == true">
            AND  t.fk_staff_id_to =-1
        </if>
        <if test="customTaskSearchDto.isMultiTask!=null and customTaskSearchDto.isMultiTask == false">
            AND t.fk_staff_id_to !=-1
        </if>
        <if test="customTaskSearchDto.createTimeStart != null">
            AND DATE_FORMAT( t.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{customTaskSearchDto.createTimeStart}, '%Y-%m-%d')
        </if>
        <if test="customTaskSearchDto.createTimeEnd != null">
            AND DATE_FORMAT(t.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{customTaskSearchDto.createTimeEnd},'%Y-%m-%d')
        </if>
        <if test="customTaskSearchDto.isTimeout != null and customTaskSearchDto.isTimeout">
            AND t.task_deadline IS NOT NULL
            AND DATE_FORMAT(t.task_deadline, '%Y-%m-%d') &lt; DATE_FORMAT(CURRENT_DATE(), '%Y-%m-%d') AND t.status != 1
        </if>
        <if test="customTaskSearchDto.delegateDeptId!=null and customTaskSearchDto.delegateDeptId.size()>0">
            AND t.fk_staff_id_from IN (
            SELECT s.id
            FROM ais_permission_center.m_staff s
            WHERE s.fk_department_id IN
            <foreach collection="customTaskSearchDto.delegateDeptId" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            AND s.is_on_duty = 1  <!-- 只查询在职员工 -->
            )
        </if>
        <if test="customTaskSearchDto.recipientDeptId!=null and customTaskSearchDto.recipientDeptId.size()>0">
            AND t.fk_staff_id_to IN (
            SELECT s.id
            FROM ais_permission_center.m_staff s
            WHERE s.fk_department_id IN
            <foreach collection="customTaskSearchDto.recipientDeptId" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
            )
        </if>
        GROUP BY
        t.id
        ORDER BY
        CASE WHEN ANY_VALUE(
        CASE
        WHEN t.task_deadline IS NOT NULL
        AND DATE_FORMAT(t.task_deadline, '%Y-%m-%d') &lt; DATE_FORMAT(CURRENT_DATE(), '%Y-%m-%d') AND t.status != 1
        THEN 2
        ELSE t.status
        END
        ) = 2 THEN 0 ELSE 1 END,
        max_date DESC
    </select>
    <select id="getTaskByEndTime" resultType="com.get.officecenter.entity.Task">
        SELECT * FROM m_task WHERE task_deadline IS NOT NULL AND task_deadline = CURRENT_DATE() AND status != 1
    </select>


    <!--    <select id="taskList" resultType="com.get.officecenter.vo.CustomTaskVo">-->
<!--        SELECT-->
<!--            t.*,-->
<!--            GREATEST(IFNULL(gmt_create, '1000-01-01 00:00:00'),-->
<!--                    IFNULL(gmt_modified, '1000-01-01 00:00:00'),-->
<!--                    IFNULL(status_modified_time, '1000-01-01 00:00:00')) AS max_date-->
<!--        FROM-->
<!--            m_task t-->
<!--        INNER JOIN (-->
<!--            SELECT DISTINCT id FROM (-->
<!--                <choose>-->
<!--                    &lt;!&ndash; 没有选择【我的委派】【我的任务】【下属任务】 &ndash;&gt;-->
<!--                    <when test="!customTaskSearchDto.isMyDelegation and !customTaskSearchDto.isMyTask and !customTaskSearchDto.isSubordinateTask">-->
<!--                        <if test="staffIds != null and staffIds.size() > 0">-->
<!--                            SELECT id FROM m_task WHERE fk_staff_id_from IN-->
<!--                            <foreach collection="staffIds" item="staffId" open="(" separator="," close=")">-->
<!--                                #{staffId}-->
<!--                            </foreach>-->
<!--                            OR fk_staff_id_to IN-->
<!--                            <foreach collection="staffIds" item="staffId" open="(" separator="," close=")">-->
<!--                                #{staffId}-->
<!--                            </foreach>-->
<!--                            UNION ALL-->
<!--                            SELECT fk_task_id AS id FROM m_task_item WHERE fk_staff_id_to IN-->
<!--                            <foreach collection="staffIds" item="staffId" open="(" separator="," close=")">-->
<!--                                #{staffId}-->
<!--                            </foreach>-->
<!--                        </if>-->
<!--                    </when>-->
<!--                    &lt;!&ndash; 选择了【我的委派】【我的任务】【下属任务】 &ndash;&gt;-->
<!--                    <otherwise>-->
<!--                        <trim suffixOverrides="UNION ALL">-->
<!--                            <trim suffix="UNION ALL">-->
<!--                                <if test="customTaskSearchDto.isMyDelegation and curStaffId != null">-->
<!--                                    SELECT id FROM m_task WHERE fk_staff_id_from = #{curStaffId}-->
<!--                                </if>-->
<!--                            </trim>-->
<!--                            <trim suffix="UNION ALL">-->
<!--                                <if test="customTaskSearchDto.isMyTask and curStaffId != null">-->
<!--                                    SELECT id FROM m_task WHERE fk_staff_id_to = #{curStaffId}-->
<!--                                    UNION ALL-->
<!--                                    SELECT fk_task_id AS id FROM m_task_item WHERE fk_staff_id_to = #{curStaffId}-->
<!--                                </if>-->
<!--                            </trim>-->
<!--                            <trim suffix="UNION ALL">-->
<!--                                <if test="customTaskSearchDto.isSubordinateTask and staffFollowerIds != null and staffFollowerIds.size() > 0">-->
<!--                                    SELECT id FROM m_task WHERE fk_staff_id_to IN-->
<!--                                    <foreach collection="staffFollowerIds" item="staffId" open="(" separator="," close=")">-->
<!--                                        #{staffId}-->
<!--                                    </foreach>-->
<!--                                    UNION ALL-->
<!--                                    SELECT fk_task_id AS id FROM m_task_item WHERE fk_staff_id_to IN-->
<!--                                    <foreach collection="staffFollowerIds" item="staffId" open="(" separator="," close=")">-->
<!--                                        #{staffId}-->
<!--                                    </foreach>-->
<!--                                </if>-->
<!--                            </trim>-->
<!--                            &lt;!&ndash; 默认不存在的值，避免报错 &ndash;&gt;-->
<!--                            <trim suffix="UNION ALL">-->
<!--                                SELECT -100 AS id-->
<!--                            </trim>-->
<!--                        </trim>-->
<!--                    </otherwise>-->
<!--                </choose>-->
<!--            ) temp-->
<!--        ) x ON x.id = t.id-->
<!--        WHERE 1 = 1-->
<!--        <if test="customTaskSearchDto.delegate!=null and customTaskSearchDto.delegate!=''">-->
<!--            AND t.fk_staff_id_from = #{customTaskSearchDto.delegate}-->
<!--        </if>-->
<!--        <if test="customTaskSearchDto.recipient!=null and customTaskSearchDto.recipient!=''">-->
<!--            AND t.fk_staff_id_to = #{customTaskSearchDto.recipient}-->
<!--        </if>-->
<!--        <if test="customTaskSearchDto.taskDescription!=null and customTaskSearchDto.taskDescription!=''">-->
<!--            AND t.task_description LIKE CONCAT('%',#{customTaskSearchDto.taskDescription},'%')-->
<!--        </if>-->
<!--        <if test="customTaskSearchDto.taskStatus!=null">-->
<!--            AND t.status = #{customTaskSearchDto.taskStatus}-->
<!--        </if>-->
<!--        <if test="customTaskSearchDto.isToday!=null and customTaskSearchDto.isToday">-->
<!--            AND DATE(t.gmt_create) = CURDATE()-->
<!--        </if>-->
<!--        <if test="customTaskSearchDto.isWithinAWeek!=null and customTaskSearchDto.isWithinAWeek">-->
<!--            AND t.gmt_create >= DATE_SUB(NOW(), INTERVAL 7 DAY)-->
<!--        </if>-->
<!--        <if test="customTaskSearchDto.isWithinOneMonth!=null and customTaskSearchDto.isWithinOneMonth">-->
<!--            AND t.gmt_create >= DATE_SUB(NOW(), INTERVAL 1 MONTH)-->
<!--        </if>-->
<!--        <if test="customTaskSearchDto.isWithinThreeMonths!=null and customTaskSearchDto.isWithinThreeMonths">-->
<!--            AND t.gmt_create >= DATE_SUB(NOW(), INTERVAL 3 MONTH)-->
<!--        </if>-->
<!--        ORDER BY max_date DESC-->
<!--    </select>-->

</mapper>
