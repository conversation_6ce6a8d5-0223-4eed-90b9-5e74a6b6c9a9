package com.get.officecenter.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.officecenter.vo.LeaveStockVo;
import com.get.officecenter.vo.StaffOfLeaveStockVo;
import com.get.officecenter.entity.LeaveStock;
import com.get.officecenter.dto.StaffOfLeaveStockDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/1/13
 * @TIME: 18:43
 * @Description:
 **/
@Mapper
public interface LeaveStockMapper extends GetMapper<LeaveStock> {
    /**
     * 列表数据
     *
     * @param fkCompanyId
     * @param leaveTypeKey
     * @param effectiveDeadline
     * @param fkStaffIds
     * @return
     */
    List<LeaveStock> selectLeaveStock(IPage<LeaveStock> iPage, @Param("fkCompanyId") Long fkCompanyId,
                                      @Param("leaveTypeKey") String leaveTypeKey,
                                      @Param("effectiveDeadline") Date effectiveDeadline,
                                      @Param("fkStaffIds") Set<Long> fkStaffIds,
                                      @Param("id") Long id);


    /**
     * 根据员工ID获取员工工休库存
     *
     * @param fkStaffIds
     * @return
     */
    List<LeaveStockVo> getLeaveStockByStaffIds(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                               @Param("endTime") Date endTime,
                                               @Param("fkCompanyId") Long fkCompanyId,
                                               @Param("leaveTypeKey") String leaveTypeKey);

    /**
     * 根据员工ID获取工休库存
     * <AUTHOR>
     * @DateTime 2023/2/3 15:01
     */
    LeaveStockVo getLeaveStockByStaffId(@Param("fkStaffId") Long fkStaffId, @Param("leaveTypeKey") String leaveTypeKey);

    /**
     * 当前实时剩余上一年年假
     *
     * @param fkStaffIds
     * @return
     */
    List<LeaveStockVo> getAnnualVacationByLastYear(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                                   @Param("date") Date date,
                                                   @Param("fkCompanyId") Long fkCompanyId,
                                                   @Param("leaveTypeKey") String leaveTypeKey);

    /**
     * 当前实时剩余今年年假
     *
     * @param fkStaffIds
     * @return
     */
    List<LeaveStockVo> getAnnualVacationByThisYear(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                                   @Param("date") Date date,
                                                   @Param("fkCompanyId") Long fkCompanyId,
                                                   @Param("leaveTypeKey") String leaveTypeKey);

    /**
     * 获取本季度剩余病假时长
     *
     * @param fkStaffIds
     * @return
     */
    List<LeaveStockVo> getDiseaseVacationQuarter(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                                 @Param("date") Date date,
                                                 @Param("fkCompanyId") Long fkCompanyId,
                                                 @Param("leaveTypeKey") String leaveTypeKey);

    /**
     * 移动端休假管理员工列表
     * @param iPage
     * @param staffOfLeaveStockDto
     * @return
     */
    List<StaffOfLeaveStockVo> getStaffOfLeaveStockList(IPage<StaffOfLeaveStockVo> iPage, @Param("staffOfLeaveStockDto") StaffOfLeaveStockDto staffOfLeaveStockDto);

    /**
     * 获取有效期内库存 可为0
     * @param iPage
     * @param fkCompanyId
     * @param leaveTypeKey
     * @param effectiveDeadline
     * @param fkStaffIds
     * @param id
     * @return
     */
    List<LeaveStock> selectEfficientLeaveStock(IPage<LeaveStock> iPage, @Param("fkCompanyId") Long fkCompanyId,
                                               @Param("leaveTypeKey") String leaveTypeKey,
                                               @Param("effectiveDeadline") Date effectiveDeadline,
                                               @Param("fkStaffIds") Set<Long> fkStaffIds,
                                               @Param("id") Long id);
}
