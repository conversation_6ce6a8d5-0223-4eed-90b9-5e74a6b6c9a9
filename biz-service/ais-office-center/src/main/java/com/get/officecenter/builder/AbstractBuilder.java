package com.get.officecenter.builder;

import me.chanjar.weixin.cp.bean.message.WxCpTpXmlMessage;
import me.chanjar.weixin.cp.bean.message.WxCpXmlOutMessage;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *  <AUTHOR>
 */
public abstract class AbstractBuilder {
  protected final Logger logger = LoggerFactory.getLogger(getClass());

  public abstract WxCpXmlOutMessage build(String content, WxCpTpXmlMessage wxMessage, WxCpTpService wxCpTpservice);
}
