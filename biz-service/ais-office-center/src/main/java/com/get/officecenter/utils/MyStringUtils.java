package com.get.officecenter.utils;

import java.text.SimpleDateFormat;
import java.util.Calendar;

/**
 * <AUTHOR>
 * @DATE: 2020/8/14
 * @TIME: 11:28
 * @Description: 财务中心编码规则工具类
 **/
public class MyStringUtils {
    /**
     * @return java.lang.String
     * @Description :工休申请单编号
     * @Param []
     * <AUTHOR>
     */
    public static String getLeaveApplicationFormNum() {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return "LAF" + formatter.format(calendar.getTime());
    }

    public static String getCustomTaskNum(Long num){
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        return "T" + code;
    }

}
