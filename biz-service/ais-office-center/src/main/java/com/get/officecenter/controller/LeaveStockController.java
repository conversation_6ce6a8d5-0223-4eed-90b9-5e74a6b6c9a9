package com.get.officecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.officecenter.vo.AttendanceStatisticsExcelVo;
import com.get.officecenter.vo.IaeAttendanceStatisticsExcelVo;
import com.get.officecenter.vo.LeaveStockVo;
import com.get.officecenter.vo.StaffOfLeaveStockVo;
import com.get.officecenter.service.LeaveStockService;
import com.get.officecenter.dto.AttendanceStatisticsExcelDto;
import com.get.officecenter.dto.LeaveStockDto;
import com.get.officecenter.dto.StaffOfLeaveStockDto;
import com.get.officecenter.dto.query.LeaveStockQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/1/13
 * @TIME: 15:40
 * @Description:工休库存-休假管理
 **/
@Api(tags = "工休库存-休假管理")
@RestController
@RequestMapping("office/leaveStock")
public class LeaveStockController {
    @Resource
    private LeaveStockService leaveStockService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DETAIL, description = "办公中心/工休库存管理/工休库存详情")
    @GetMapping("/{id}")
    public ResponseBo<LeaveStockVo> detail(@PathVariable("id") Long id) {
        LeaveStockVo data = leaveStockService.findLeaveStockById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 新增信息
     *
     * @param leaveStockDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/工休库存管理/新增工休库存")
    @PostMapping("add")
    public ResponseBo add(@RequestBody LeaveStockDto leaveStockDto) {
        leaveStockService.add(leaveStockDto);
        return SaveResponseBo.ok();
    }

    /**
     * 新增信息(工作流feign调用）
     *
     * @param leaveStockDto
     * @return
     * @
     */
    @ApiIgnore
    @VerifyLogin(IsVerify = false)
    @PostMapping("addSystem")
    public void addSystem(@RequestBody LeaveStockDto leaveStockDto) {
        leaveStockService.addSystem(leaveStockDto);
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "办公中心/工休库存管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        leaveStockService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param leaveStockDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/工休库存管理/更新排班日期设定")
    @PostMapping("update")
    public ResponseBo<LeaveStockVo> update(@RequestBody @Validated(LeaveStockDto.Update.class) LeaveStockDto leaveStockDto) {
        return UpdateResponseBo.ok(leaveStockService.updateLeaveStock(leaveStockDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/工休库存管理/查询工休库存")
    @PostMapping("datas")
    public ResponseBo<LeaveStockVo> datas(@RequestBody SearchBean<LeaveStockQueryDto> page) {
        List<LeaveStockVo> datas = leaveStockService.getLeaveStocks(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 调整时长
     *
     * @param leaveStockDto
     * @return
     * @
     */
    @ApiOperation(value = "调整时长", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/工休库存管理/调整时长")
    @PostMapping("adjustLeavetock")
    public ResponseBo<LeaveStockVo> updateLeavetock(@RequestBody @Validated(LeaveStockDto.Update.class) LeaveStockDto leaveStockDto) {
        return UpdateResponseBo.ok(leaveStockService.adjustLeavetock(leaveStockDto));
    }

    /**
     * 调整时长(工作流feign调用)
     *
     * @param leaveStockDto
     * @return
     * @
     */
    @ApiIgnore
    @PostMapping("updateSystemLeavetock")
    public void updateSystemLeavetock(@RequestBody LeaveStockDto leaveStockDto) {
        leaveStockService.adjustSystemLeavetock(leaveStockDto);
    }

    /**
     * 员工下拉
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "员工下拉", notes = "")
    @GetMapping("getStaffSelect")
    public ResponseBo<BaseSelectEntity> getStaffSelect(@RequestParam("fkCompanyId") Long fkCompanyId) {
        return new ListResponseBo<BaseSelectEntity>(leaveStockService.getStaffSelect(fkCompanyId));
    }

    /**
     * 考勤统计
     * @return
     * @
     */
    @ApiOperation(value = "考勤统计", notes = "")
    @PostMapping("/attendanceStatistics")
    @ResponseBody
    public ResponseBo<AttendanceStatisticsExcelVo> attendanceStatistics(@RequestBody AttendanceStatisticsExcelDto attendanceStatisticsExcelDto) throws Exception {
        return new ResponseBo(leaveStockService.attendanceStatistics(attendanceStatisticsExcelDto));
    }

    /**
     * IAE考勤统计
     * @return
     * @
     */
    @ApiOperation(value = "IAE考勤统计", notes = "")
    @PostMapping("/iaeAttendanceStatistics")
    @ResponseBody
    public ResponseBo<IaeAttendanceStatisticsExcelVo> iaeAttendanceStatistics(@RequestBody AttendanceStatisticsExcelDto attendanceStatisticsExcelDto) throws Exception {
        return new ResponseBo(leaveStockService.iaeAttendanceStatistics(attendanceStatisticsExcelDto));
    }

    /**
     * 考勤统计EXCEL导出
     *
     * @return
     * @
     */
    @ApiOperation(value = "考勤统计EXCEL导出", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/工休库存管理/考勤统计EXCEL导出")
    @PostMapping("/exportAttendanceStatisticsExcel")
    @ResponseBody
    public void exportAttendanceStatisticsExcel(HttpServletResponse response, @RequestBody AttendanceStatisticsExcelDto attendanceStatisticsExcelDto) throws Exception {
        leaveStockService.exportAttendanceStatisticsExcel(response, attendanceStatisticsExcelDto);
    }


    /**
     * IAE考勤统计EXCEL导出
     *
     * @return
     * @
     */
    @ApiOperation(value = "IAE考勤统计EXCEL导出", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/工休库存管理/IAE考勤统计EXCEL导出")
    @PostMapping("/exportIaeAttendanceStatisticsExcel")
    @ResponseBody
    public void exportIaeAttendanceStatisticsExcel(HttpServletResponse response, @RequestBody AttendanceStatisticsExcelDto attendanceStatisticsExcelDto) throws Exception {
        leaveStockService.exportIaeAttendanceStatisticsExcel(response, attendanceStatisticsExcelDto);
    }

    /**
     * 获取库存
     *
     * @return
     * @
     */
    @ApiIgnore
    @PostMapping("getLeaveStockDtos")
    public List<LeaveStockVo> getLeaveStockDtos(@RequestBody LeaveStockDto leaveStockDto) {
        return leaveStockService.getLeaveStockDtos(leaveStockDto);
    }



    /**
     * 调整时长
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "移动端休假管理员工列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/休假管理/移动端休假管理员工列表")
    @PostMapping("getStaffOfLeaveStockList")
    public ResponseBo<StaffOfLeaveStockVo> getStaffOfLeaveStockList(@RequestBody SearchBean<StaffOfLeaveStockDto> page) {
        List<StaffOfLeaveStockVo> datas = leaveStockService.getStaffOfLeaveStockList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


}
