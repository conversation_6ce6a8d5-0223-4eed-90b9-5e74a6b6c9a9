package com.get.officecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.service.GetService;
import com.get.officecenter.vo.AttendanceStatisticsExcelVo;
import com.get.officecenter.vo.IaeAttendanceStatisticsExcelVo;
import com.get.officecenter.vo.LeaveStockVo;
import com.get.officecenter.vo.StaffOfLeaveStockVo;
import com.get.officecenter.entity.LeaveStock;
import com.get.officecenter.dto.AttendanceStatisticsExcelDto;
import com.get.officecenter.dto.LeaveStockDto;
import com.get.officecenter.dto.StaffOfLeaveStockDto;
import com.get.officecenter.dto.query.LeaveStockQueryDto;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/1/13
 * @TIME: 18:37
 * @Description:
 **/
public interface LeaveStockService extends GetService<LeaveStock> {
    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    LeaveStockVo findLeaveStockById(Long id);

    /**
     * 新增信息
     *
     * @param leaveStockDto
     * @
     */
    void add(LeaveStockDto leaveStockDto);

    /**
     * 删除信息
     *
     * @param id
     * @
     */
    void delete(Long id);

    /**
     * 修改信息
     *
     * @param leaveStockDto
     * @return
     * @
     */
    LeaveStockVo updateLeaveStock(LeaveStockDto leaveStockDto);

    /**
     * 列表数据
     *
     * @param leaveStockQueryDto
     * @param page
     * @return
     * @
     */
    List<LeaveStockVo> getLeaveStocks(LeaveStockQueryDto leaveStockQueryDto, Page page);

    /**
     * 调整时长
     *
     * @param leaveStockDto
     * @return
     * @
     */
    LeaveStockVo adjustLeavetock(LeaveStockDto leaveStockDto);

    /**
     * 员工下拉
     *
     * @param fkCompanyId
     * @return
     * @
     */
    List<BaseSelectEntity> getStaffSelect(Long fkCompanyId);

    /**
     * 根据员工Id获取对应工休库存
     * <AUTHOR>
     * @DateTime 2023/2/3 15:07
     */
    LeaveStockVo getLeaveStockByStaffId(Long fkStaffId, String leaveTypeKey);

    /**
     * IAE考勤统计
     * @param attendanceStatisticsExcelDto
     * @return
     * @throws Exception
     */
    List<IaeAttendanceStatisticsExcelVo> iaeAttendanceStatistics(AttendanceStatisticsExcelDto attendanceStatisticsExcelDto) throws Exception;

    /**
     * 考勤统计
     *
     * @param attendanceStatisticsExcelDto
     * @
     */
    List<AttendanceStatisticsExcelVo> attendanceStatistics(AttendanceStatisticsExcelDto attendanceStatisticsExcelDto) throws Exception;


    /**
     * 考勤统计EXCEL导出
     *
     * @param response
     * @param attendanceStatisticsExcelDto
     * @
     */
    void exportAttendanceStatisticsExcel(HttpServletResponse response, AttendanceStatisticsExcelDto attendanceStatisticsExcelDto) throws Exception;


    /**
     * IAE考勤统计EXCEL导出
     *
     * @param response
     * @param attendanceStatisticsExcelDto
     * @
     */
    void exportIaeAttendanceStatisticsExcel(HttpServletResponse response, AttendanceStatisticsExcelDto attendanceStatisticsExcelDto) throws Exception;

    /**
     * 调整时长(工作流feign调用)
     *
     * @param leaveStockDto
     */
    Boolean adjustSystemLeavetock(LeaveStockDto leaveStockDto);

    /**
     * 新增信息(工作流feign调用）
     *
     * @param leaveStockDto
     */
    Long addSystem(LeaveStockDto leaveStockDto);

    /**
     * 获取库存信息
     *
     * @param leaveStockDto
     * @return
     */
    List<LeaveStockVo> getLeaveStockDtos(LeaveStockDto leaveStockDto);

    /**
     * 移动端休假管理员工列表
     *
     * @param staffOfLeaveStockDto
     * @param page
     * @return
     */
    List<StaffOfLeaveStockVo> getStaffOfLeaveStockList(StaffOfLeaveStockDto staffOfLeaveStockDto, Page page);

    /**
     * 获取有效期内库存 可为0
     * @param leaveStockDto
     * @return
     */
    List<LeaveStockVo> getEfficientLeaveStockDtos(LeaveStockDto leaveStockDto);
}
