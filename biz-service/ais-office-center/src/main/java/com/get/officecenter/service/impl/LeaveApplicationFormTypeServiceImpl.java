package com.get.officecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.service.impl.GetServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.officecenter.vo.LeaveApplicationFormTypeVo;
import com.get.officecenter.entity.LeaveApplicationFormType;
import com.get.officecenter.entity.LeaveApplicationFormTypeCompany;
import com.get.officecenter.mapper.LeaveApplicationFormTypeMapper;
import com.get.officecenter.service.ILeaveApplicationFormTypeCompanyService;
import com.get.officecenter.service.ILeaveApplicationFormTypeService;
import com.get.officecenter.dto.LeaveApplicationFormTypeCompanyDto;
import com.get.officecenter.dto.LeaveApplicationFormTypeDto;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.google.common.collect.Lists;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/4/12 16:03
 * @verison: 1.0
 * @description:
 */
@Service
public class LeaveApplicationFormTypeServiceImpl extends GetServiceImpl<LeaveApplicationFormTypeMapper,LeaveApplicationFormType> implements ILeaveApplicationFormTypeService {
    @Resource
    private LeaveApplicationFormTypeMapper leaveApplicationFormTypeMapper;
    @Resource
    private UtilService utilService;
    @Lazy
    @Resource
    private ILeaveApplicationFormTypeCompanyService leaveApplicationFormTypeCompanyService;

    @Override
    public LeaveApplicationFormTypeVo findLeaveApplicationFormTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LeaveApplicationFormType leaveApplicationFormType = leaveApplicationFormTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(leaveApplicationFormType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return BeanCopyUtils.objClone(leaveApplicationFormType, LeaveApplicationFormTypeVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<LeaveApplicationFormTypeDto> leaveApplicationFormTypeDtos) {
        if (GeneralTool.isEmpty(leaveApplicationFormTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<LeaveApplicationFormType> upList = Lists.newArrayList();
        List<LeaveApplicationFormTypeCompany> ltc = Lists.newArrayList();
        Integer maxViewOrder = leaveApplicationFormTypeMapper.getMaxViewOrder();
        for (LeaveApplicationFormTypeDto leaveApplicationFormTypeDto : leaveApplicationFormTypeDtos) {
            if (GeneralTool.isEmpty(leaveApplicationFormTypeDto.getId())) {
                if (validateAdd(leaveApplicationFormTypeDto)) {
                    LeaveApplicationFormType leaveApplicationFormType = BeanCopyUtils.objClone(leaveApplicationFormTypeDto, LeaveApplicationFormType::new);
                    leaveApplicationFormType.setViewOrder(maxViewOrder);
                    utilService.setCreateInfo(leaveApplicationFormType);
                    boolean saveFlag = save(leaveApplicationFormType);
                    if (!saveFlag){
                        throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                    }
                    LeaveApplicationFormTypeCompany leaveApplicationFormTypeCompany = new LeaveApplicationFormTypeCompany();
                    leaveApplicationFormTypeCompany.setFkCompanyId(leaveApplicationFormTypeDto.getFkCompanyId());
                    leaveApplicationFormTypeCompany.setFkLeaveApplicationFormTypeId(leaveApplicationFormType.getId());
                    utilService.setCreateInfo(leaveApplicationFormTypeCompany);
                    ltc.add(leaveApplicationFormTypeCompany);
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("remind_event_type_key_exist"));
                }
            } else {
                if (validateUpdate(leaveApplicationFormTypeDto)) {
                    LeaveApplicationFormType leaveApplicationFormType = BeanCopyUtils.objClone(leaveApplicationFormTypeDto, LeaveApplicationFormType::new);
                    utilService.setCreateInfo(leaveApplicationFormType);
                    upList.add(leaveApplicationFormType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("remind_event_type_key_exist"));
                }
            }
        }
        if (GeneralTool.isNotEmpty(ltc)) {
            boolean saveBatchFlag = leaveApplicationFormTypeCompanyService.saveBatch(ltc);
            if (!saveBatchFlag){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }
        if (GeneralTool.isNotEmpty(upList)) {
            boolean updateBatchFlag = updateBatchById(upList);
            if (!updateBatchFlag){
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }
    }

    private boolean validateAdd(LeaveApplicationFormTypeDto leaveApplicationFormTypeDto) {
        LambdaQueryWrapper<LeaveApplicationFormType> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LeaveApplicationFormType::getTypeKey, leaveApplicationFormTypeDto.getTypeKey());
        List<LeaveApplicationFormType> leaveApplicationFormTypes = list(wrapper);
        return GeneralTool.isEmpty(leaveApplicationFormTypes);
    }

    private boolean validateUpdate(LeaveApplicationFormTypeDto leaveApplicationFormTypeDto) {
        LambdaQueryWrapper<LeaveApplicationFormType> wrapper = new LambdaQueryWrapper();
        wrapper.eq(LeaveApplicationFormType::getTypeKey, leaveApplicationFormTypeDto.getTypeKey());
        List<LeaveApplicationFormType> list = list(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(leaveApplicationFormTypeDto.getId());
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (leaveApplicationFormTypeMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        leaveApplicationFormTypeMapper.deleteById(id);
    }

    @Override
    public LeaveApplicationFormTypeVo updateLeaveApplicationFormType(LeaveApplicationFormTypeDto leaveApplicationFormTypeDto) {
        if (leaveApplicationFormTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        LeaveApplicationFormType result = leaveApplicationFormTypeMapper.selectById(leaveApplicationFormTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        LeaveApplicationFormType leaveApplicationFormType = BeanCopyUtils.objClone(leaveApplicationFormTypeDto, LeaveApplicationFormType::new);
        utilService.updateUserInfoToEntity(leaveApplicationFormType);
        leaveApplicationFormTypeMapper.updateById(leaveApplicationFormType);
        return findLeaveApplicationFormTypeById(leaveApplicationFormTypeDto.getId());
    }

    @Override
    public List<LeaveApplicationFormTypeVo> getLeaveApplicationFormTypes(LeaveApplicationFormTypeDto leaveApplicationFormTypeDto, Page page) {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        IPage<LeaveApplicationFormType> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<LeaveApplicationFormTypeVo> leaveApplicationFormTypes = leaveApplicationFormTypeMapper.getLeaveApplicationFormTypes(iPage, leaveApplicationFormTypeDto,companyIds);
        page.setAll((int) iPage.getTotal());
        return leaveApplicationFormTypes;
    }

    @Override
    public void movingOrder(List<LeaveApplicationFormTypeDto> leaveApplicationFormTypeDtos) {
        if (GeneralTool.isEmpty(leaveApplicationFormTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        LeaveApplicationFormType ro = BeanCopyUtils.objClone(leaveApplicationFormTypeDtos.get(0), LeaveApplicationFormType::new);
        Integer oneorder = ro.getViewOrder();
        LeaveApplicationFormType rt = BeanCopyUtils.objClone(leaveApplicationFormTypeDtos.get(1), LeaveApplicationFormType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        leaveApplicationFormTypeMapper.updateById(ro);
        leaveApplicationFormTypeMapper.updateById(rt);
    }

    @Override
    public List<LeaveApplicationFormTypeVo> getLeaveApplicationFormTypeSelect(Long companyId) {
        //兼容处理
        if (GeneralTool.isEmpty(companyId)){
            List<LeaveApplicationFormType> leaveApplicationFormTypes = leaveApplicationFormTypeMapper.selectList(Wrappers.<LeaveApplicationFormType>query().lambda().orderByDesc(LeaveApplicationFormType::getViewOrder));
            return leaveApplicationFormTypes.stream().map(leaveApplicationFormType -> BeanCopyUtils.objClone(leaveApplicationFormType, LeaveApplicationFormTypeVo::new)).collect(Collectors.toList());
        }
        List<LeaveApplicationFormTypeCompany> leaveApplicationFormTypeCompanies = leaveApplicationFormTypeCompanyService.list(Wrappers.lambdaQuery(LeaveApplicationFormTypeCompany.class)
                .eq(LeaveApplicationFormTypeCompany::getFkCompanyId, companyId));
        if (GeneralTool.isEmpty(leaveApplicationFormTypeCompanies)){
            return Collections.emptyList();
        }
        Set<Long> typeIds = leaveApplicationFormTypeCompanies.stream().map(LeaveApplicationFormTypeCompany::getFkLeaveApplicationFormTypeId).collect(Collectors.toSet());
        List<LeaveApplicationFormType> leaveApplicationFormTypes = list(Wrappers.lambdaQuery(LeaveApplicationFormType.class).in(LeaveApplicationFormType::getId, typeIds).orderByDesc(LeaveApplicationFormType::getViewOrder));
        return BeanCopyUtils.copyListProperties(leaveApplicationFormTypes, LeaveApplicationFormTypeVo::new);
    }

    @Override
    public String getLeaveApplicationFormTypeNameById(Long leaveApplicationFormTypeId) {
        String leaveApplicationFormTypeName = null;
        LeaveApplicationFormType leaveApplicationFormType = leaveApplicationFormTypeMapper.selectById(leaveApplicationFormTypeId);
        if (GeneralTool.isNotEmpty(leaveApplicationFormType)) {
            leaveApplicationFormTypeName = leaveApplicationFormType.getTypeName();
        }
        return leaveApplicationFormTypeName;
    }

    @Override
    public String getLeaveApplicationFormTypeKeyById(Long fkLeaveApplicationFormTypeId) {
        String leaveApplicationFormTypeKey = null;
        LeaveApplicationFormType leaveApplicationFormType = leaveApplicationFormTypeMapper.selectById(fkLeaveApplicationFormTypeId);
        if (GeneralTool.isNotEmpty(leaveApplicationFormType)) {
            leaveApplicationFormTypeKey = leaveApplicationFormType.getTypeKey();
        }
        return leaveApplicationFormTypeKey;
    }

    @Override
    public String getLeaveApplicationFormTypeNameByKey(String leaveTypeKey) {
        return leaveApplicationFormTypeMapper.getLeaveApplicationFormTypeNameByKey(leaveTypeKey);
    }

    /**
     *工休申请类型-公司安全配置
     * @param leaveApplicationFormTypeCompanyDtos
     */
    @Override
    public void editLeaveApplicationFormTypeCompanyRelation(List<LeaveApplicationFormTypeCompanyDto> leaveApplicationFormTypeCompanyDtos) {
        leaveApplicationFormTypeCompanyService.editLeaveApplicationFormTypeCompanyRelation(leaveApplicationFormTypeCompanyDtos);
    }

    /**
     * 回显工休申请类型和公司的关系
     * @param id
     * @return
     */
    @Override
    public List<CompanyTreeVo> getLeaveApplicationFormTypeCompanyRelation(Long id) {
        return leaveApplicationFormTypeCompanyService.getLeaveApplicationFormTypeCompanyRelation(id);
    }
}
