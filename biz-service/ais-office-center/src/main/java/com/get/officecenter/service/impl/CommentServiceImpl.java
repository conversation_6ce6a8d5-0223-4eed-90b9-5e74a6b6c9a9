package com.get.officecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.officecenter.entity.Comment;
import com.get.officecenter.mapper.CommentMapper;
import com.get.officecenter.service.CommentService;
import com.get.officecenter.dto.CommentDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author:cream
 * @Date: 2023/5/30  12:36
 */
@Service
public class CommentServiceImpl implements CommentService {

    @Resource
    private UtilService<Object> utilService;

    @Resource
    private CommentMapper commentMapper;

    /**
     * Author Cream
     * Description : //增加评论
     * Date 2023/5/30 12:49
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo addComment(CommentDto commentDto) {
        Comment comment = new Comment();
        comment.setComment(commentDto.getComment());
        comment.setFkTableName(commentDto.getFkTableName());
        comment.setFkTableId(commentDto.getFkTableId());
        utilService.setCreateInfo(comment);
        commentMapper.insert(comment);
        return SaveResponseBo.ok(comment.getId());
    }

    /**
     * Author Cream
     * Description : //编辑评论
     * Date 2023/5/30 12:49
     * Params:
     * Return
     */
    @Override
    public SaveResponseBo updateComment(CommentDto commentDto) {
        if (Objects.isNull(commentDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Comment comment = commentMapper.selectById(commentDto.getId());
        if (Objects.isNull(comment)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        comment.setComment(commentDto.getComment());
        utilService.setUpdateInfo(comment);
        commentMapper.updateById(comment);
        return SaveResponseBo.ok(comment.getId());
    }

    /**
     * Author Cream
     * Description : //获取评论列表
     * Date 2023/5/30 12:49
     * Params:
     * Return
     */
    @Override
    public List<Comment> getCommentList(String fkTableName, List<Long> fkTableIds) {
        if (fkTableIds.isEmpty()) {
            return Collections.emptyList();
        }
        //return commentMapper.selectList(Wrappers.<Comment>lambdaQuery().eq(Comment::getFkTableName, fkTableName).in(Comment::getFkTableId, fkTableIds));
         return commentMapper.getCommentList(fkTableName, fkTableIds);
    }

    @Override
    public List<Comment> getCommentList(String fkTableName, Long fkTableId, Page page) {
        IPage<Comment> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        Wrapper<Comment> queryWrapper = Wrappers.<Comment>lambdaQuery().eq(Comment::getFkTableName, fkTableName).in(Comment::getFkTableId, fkTableId);
        IPage<Comment> commentIPage = commentMapper.selectPage(iPage, queryWrapper);
        page.setAll((int) commentIPage.getTotal());
        return commentIPage.getRecords();
    }

    /**
     * Author Cream
     * Description : //删除评论
     * Date 2023/5/30 14:32
     * Params:
     * Return
     */
    @Override
    public void deleteComment(String fkTableName, Set<Long> fkTableIds) {
        if (commentMapper.selectCount(Wrappers.<Comment>lambdaQuery().eq(Comment::getFkTableName, fkTableName).in(Comment::getFkTableId, fkTableIds)) > 0) {
            commentMapper.delete(Wrappers.<Comment>lambdaQuery().eq(Comment::getFkTableName, fkTableName).in(Comment::getFkTableId, fkTableIds));
        }
    }

    /**
     * Author Cream
     * Description : //删除单个评论
     * Date 2023/6/2 11:21
     * Params:
     * Return
     */
    @Override
    public void removeComment(Long commentId) {
        if (Objects.isNull(commentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (commentMapper.selectCount(Wrappers.<Comment>lambdaQuery().eq(Comment::getId,commentId))>0) {
            commentMapper.deleteById(commentId);
        }
    }

    @Override
    public void addCommentBatch(List<CommentDto> commentBatch) {
        if (CollectionUtils.isEmpty(commentBatch)) {
            return;
        }

        // 转换为Entity列表
        List<Comment> comments = commentBatch.stream().map(dto -> {
            Comment comment = new Comment();
            comment.setComment(dto.getComment());
            comment.setFkTableName(dto.getFkTableName());
            comment.setFkTableId(dto.getFkTableId());
            utilService.setCreateInfo(comment); // 设置创建信息
            return comment;
        }).collect(Collectors.toList());

        // 执行批量插入（建议每批1000条左右）
        int batchSize = 1000;
        for (int i = 0; i < comments.size(); i += batchSize) {
            int end = Math.min(i + batchSize, comments.size());
            List<Comment> subList = comments.subList(i, end);
            commentMapper.insertBatch(subList);
        }
    }
}
