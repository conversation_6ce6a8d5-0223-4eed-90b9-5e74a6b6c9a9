<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.officecenter.mapper.LeaveApplicationFormTypeMapper">

  <insert id="insertSelective" parameterType="com.get.officecenter.entity.LeaveApplicationFormType" keyProperty="id" useGeneratedKeys="true">
    insert into u_leave_application_form_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="typeName != null">
        type_name,
      </if>
      <if test="typeKey != null">
        type_key,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="typeKey != null">
        #{typeKey,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     u_leave_application_form_type

  </select>

  <select id="getLeaveApplicationFormTypeByKey"
          resultType="com.get.officecenter.vo.LeaveApplicationFormVo">
      SELECT
        a.id,
        a.type_key
      FROM
        u_leave_application_form_type AS a
      where  a.type_key=#{key} order by gmt_create desc limit 1
    </select>
  <select id="getLeaveApplicationFormTypeNameByKey" resultType="java.lang.String">
    SELECT type_name FROM u_leave_application_form_type WHERE type_key=#{leaveTypeKey}
  </select>
  <select id="getLeaveApplicationFormTypes"
          resultType="com.get.officecenter.vo.LeaveApplicationFormTypeVo">
    SELECT
    a.*,
    GROUP_CONCAT(DISTINCT c.short_name ORDER BY c.view_order,c.id) as companyName,
    GROUP_CONCAT(DISTINCT c.id ORDER BY c.view_order,c.id) fkCompanyIds
    FROM
    u_leave_application_form_type a
    LEFT JOIN r_leave_application_form_type_company b on a.id = b.fk_leave_application_form_type_id
    LEFT JOIN ais_permission_center.m_company c on c.id = b.fk_company_id
    where 1=1
    <if test="leaveApplicationFormTypeDto.keyWord !=null and leaveApplicationFormTypeDto.keyWord !=''">
      and (
      a.type_name like concat("%",#{leaveApplicationFormTypeDto.keyWord},"%")
      or
      a.type_name like concat("%",#{leaveApplicationFormTypeDto.keyWord},"%")
      )
    </if>
    and
    b.fk_company_id in
    <foreach item="id" index="index" collection="companyIds" open="(" separator="," close=")">
      #{id}
    </foreach>
    GROUP BY a.id
    <if test="leaveApplicationFormTypeDto.fkCompanyId !=null">
      having  FIND_IN_SET(#{leaveApplicationFormTypeDto.fkCompanyId},fkCompanyIds)
    </if>
    ORDER BY a.view_order desc
  </select>
</mapper>