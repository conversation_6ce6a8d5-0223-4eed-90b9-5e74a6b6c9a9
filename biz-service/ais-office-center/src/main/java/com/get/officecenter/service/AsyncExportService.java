package com.get.officecenter.service;

import com.get.common.result.FocExportVo;
import com.get.core.secure.UserInfo;
import com.get.officecenter.dto.TaskItemDto;
import com.get.officecenter.entity.Task;
import com.get.officecenter.entity.TaskItem;

import java.util.List;
import java.util.Map;

public interface AsyncExportService {

    /**
     * 异步导出任务项列表
     * @param taskItemDto
     * @param user
     * @param locale
     */
    void exportTaskItemList(TaskItemDto taskItemDto, UserInfo user, String locale, Task task,Map<String, String> headerMap);



}
