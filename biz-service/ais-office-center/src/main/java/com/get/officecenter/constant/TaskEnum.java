package com.get.officecenter.constant;



import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.tool.utils.GeneralTool;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public enum TaskEnum {


    /**
     * 多人任务状态
     */
    TASK_UNFINISHED(0, "待解决"),
    TASK_FINISHED(1, "已完成"),
    TASK_TIMED_OUT(2, "超时任务"),

    /**
     * 任务类型
     */
    DELEGATED_TASK(1, "我的委派"),
    OWN_TASK(2, "我的任务"),
    SUBORDINATE_TASK(3, "下属任务")
    ;


    /**
     * 多人任务类型
     */
    public static final TaskEnum[] TASK_TYPE = new TaskEnum[]{
            DELEGATED_TASK ,OWN_TASK, SUBORDINATE_TASK
    };

    /**
     * 多人任务状态
     */
    public static final TaskEnum[] MULTI_TASK_STATUS = new TaskEnum[]{
            TASK_UNFINISHED,TASK_FINISHED,TASK_TIMED_OUT
    };

    public Integer key;
    public String value;

    TaskEnum(Integer key, String value) {
        this.key = key;
        this.value = value;
    }


    /**
     * 根据key获取value
     *
     * @param key
     * @return
     */
    public static String getValueByKey(Integer key, TaskEnum[] enums) {
        return Optional.ofNullable(enums)
                .map(Arrays::stream)
                .flatMap(stream -> stream.filter(e -> key != null && key.equals(e.key)).findFirst())
                .map(e -> e.value)
                .orElse("");
    }
}
