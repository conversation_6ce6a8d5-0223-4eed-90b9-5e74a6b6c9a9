package com.get.officecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.officecenter.vo.ClockInDataVo;
import com.get.officecenter.service.ClockInDataService;
import com.get.officecenter.dto.ClockInDataDto;
import com.get.officecenter.dto.ClockInDataListDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/5/10
 * @TIME: 10:35
 * @Description:
 **/
@Api(tags = "打卡记录")
@RestController
@RequestMapping("office/ClockInData")
public class ClockInDataController {

    @Resource
    private ClockInDataService clockInDataService;

    /**
     * 打卡记录列表
     * @return
     */
    @ApiOperation(value = "打卡记录列表", notes = "")
    @PostMapping("/getAllClockInData")
    public ResponseBo<ClockInDataVo> getAllClockInData(@RequestBody SearchBean<ClockInDataListDto> page){
        List<ClockInDataVo> datas = clockInDataService.getAllClockInData(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 导出打卡记录列表Excel
     * @return
     */
    @ApiOperation(value = "导出打卡记录列表Excel", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/打卡记录/导出打卡记录列表Excel")
    @PostMapping("/exportClockInDataExcel")
    public void exportClockInDataExcel(HttpServletResponse response, @RequestBody ClockInDataListDto clockInDataListDto) throws Exception{
         clockInDataService.exportClockInDataExcel(response, clockInDataListDto);
    }


    /**
     * 补卡
     * @return
     */
    @ApiOperation(value = "补卡", notes = "")
    @PostMapping("/addClockInData")
    public ResponseBo addClockInData(@RequestBody ClockInDataDto clockInDataDto){
        clockInDataService.addClockInData(clockInDataDto);
        return ResponseBo.ok();
    }

    /**
     * 是否有效设置
     * @return
     */
    @ApiOperation(value = "是否有效设置", notes = "")
    @PostMapping("/updateActive")
    public ResponseBo updateActive(@RequestParam("id") Long id,@RequestParam("isActive") boolean isActive){
        clockInDataService.updateActive(id,isActive);
        return ResponseBo.ok();
    }

    /**
     * 考勤机数据录入
     * @param file
     * @return
     */
    @ApiOperation(value = "考勤机数据录入", notes = "")
    @PostMapping("/importAttendanceMachineData")
    public ResponseBo importAttendanceMachineData(@RequestParam("fkCompanyId") Long fkCompanyId,@RequestParam MultipartFile file){
        return clockInDataService.importAttendanceMachineData(fkCompanyId,file);
    }

    /**
     * 钉钉考勤数据录入
     * @param file
     * @return
     */
    @ApiOperation(value = "钉钉数据录入", notes = "")
    @PostMapping("/importDingTalkAttendanceData")
    public ResponseBo importDingTalkAttendanceData(@RequestParam("fkCompanyId") Long fkCompanyId,@RequestParam MultipartFile file){
        return clockInDataService.importDingTalkAttendanceData(fkCompanyId,file);
    }

}
