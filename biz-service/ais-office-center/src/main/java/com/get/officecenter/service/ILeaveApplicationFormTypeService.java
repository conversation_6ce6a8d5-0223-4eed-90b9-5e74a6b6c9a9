package com.get.officecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.service.GetService;
import com.get.officecenter.vo.LeaveApplicationFormTypeVo;
import com.get.officecenter.entity.LeaveApplicationFormType;
import com.get.officecenter.dto.LeaveApplicationFormTypeCompanyDto;
import com.get.officecenter.dto.LeaveApplicationFormTypeDto;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/12 16:03
 * @verison: 1.0
 * @description:
 */
public interface ILeaveApplicationFormTypeService extends GetService<LeaveApplicationFormType> {
    /**
     * @return com.get.officecenter.vo.LeaveApplicationFormTypeVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    LeaveApplicationFormTypeVo findLeaveApplicationFormTypeById(Long id);

    /**
     * @return void
     * @Description :批量新增
     * @Param [leaveApplicationFormTypeDtos]
     * <AUTHOR>
     */
    void batchAdd(List<LeaveApplicationFormTypeDto> leaveApplicationFormTypeDtos);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.officecenter.vo.LeaveApplicationFormTypeVo
     * @Description :修改
     * @Param [leaveApplicationFormTypeDto]
     * <AUTHOR>
     */
    LeaveApplicationFormTypeVo updateLeaveApplicationFormType(LeaveApplicationFormTypeDto leaveApplicationFormTypeDto);

    /**
     * @return java.util.List<com.get.officecenter.vo.LeaveApplicationFormTypeVo>
     * @Description :列表
     * @Param [leaveApplicationFormTypeDto, page]
     * <AUTHOR>
     */
    List<LeaveApplicationFormTypeVo> getLeaveApplicationFormTypes(LeaveApplicationFormTypeDto leaveApplicationFormTypeDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [leaveApplicationFormTypeDtos]
     * <AUTHOR>
     */
    void movingOrder(List<LeaveApplicationFormTypeDto> leaveApplicationFormTypeDtos);

    /**
     * @return java.util.List<com.get.officecenter.vo.LeaveApplicationFormTypeVo>
     * @Description :供应商类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<LeaveApplicationFormTypeVo> getLeaveApplicationFormTypeSelect(Long companyId);

    /**
     * @return java.lang.String
     * @Description :根据id查找对应名称
     * @Param [leaveApplicationFormTypeId]
     * <AUTHOR>
     */
    String getLeaveApplicationFormTypeNameById(Long leaveApplicationFormTypeId);

    /**
     * @return java.lang.String
     * @Description :根据id查找对应名称key
     * @Param [leaveApplicationFormTypeId]
     * <AUTHOR>
     */
    String getLeaveApplicationFormTypeKeyById(Long fkLeaveApplicationFormTypeId);

    /**
     * 根据key获取对应名称
     *
     * @param leaveTypeKey
     * @return
     */
    String getLeaveApplicationFormTypeNameByKey(String leaveTypeKey);

    /**
     * 工休申请类型-公司安全配置
     * @param leaveApplicationFormTypeCompanyDtos
     */
    void editLeaveApplicationFormTypeCompanyRelation(List<LeaveApplicationFormTypeCompanyDto> leaveApplicationFormTypeCompanyDtos);

    /**
     * 回显工休申请类型和公司的关系
     * @param id
     * @return
     */
    List<CompanyTreeVo> getLeaveApplicationFormTypeCompanyRelation(Long id);
}
