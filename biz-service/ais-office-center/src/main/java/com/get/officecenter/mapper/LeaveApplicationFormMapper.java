package com.get.officecenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.officecenter.vo.LeaveApplicationFormVo;
import com.get.officecenter.entity.LeaveApplicationForm;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Mapper
public interface LeaveApplicationFormMapper extends BaseMapper<LeaveApplicationForm> {

    /**
     * 根据工休单Ids获取指定状态的工休单Ids
     *
     * @param formIds
     * @param status
     * @return
     */
    List<Long> getLeaveApplicationFormByStatus(@Param("formIds") List<Long> formIds, @Param("status") Integer status);

    /**
     * 根据员工Ids获取工休单
     *
     * @return
     */
    List<LeaveApplicationFormVo> getLeaveApplicationFormByFkStaffIds(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                                                     @Param("fkCompanyId") Long fkCompanyId,
                                                                     @Param("startTime") Date startTime,
                                                                     @Param("endTime") Date endTime);

    /**
     * 获取工休单状态
     *
     * @param ids
     * @return
     */
    List<LeaveApplicationForm> getLeaveApplicationFormStatus(@Param("ids") List<Long> ids);

    /**
     * 根据员工ID以及时间范围获取工休单信息
     * @param fkStaffIds
     * @param startTime
     * @param endTime
     * @return
     */
    List<LeaveApplicationFormVo> getLeaveApplicationFormList(@Param("fkStaffIds") Set<Long> fkStaffIds,
                                                             @Param("startTime")Date startTime,
                                                             @Param("endTime") Date endTime,
                                                             @Param("fkCompanyId") Long fkCompanyId);
}