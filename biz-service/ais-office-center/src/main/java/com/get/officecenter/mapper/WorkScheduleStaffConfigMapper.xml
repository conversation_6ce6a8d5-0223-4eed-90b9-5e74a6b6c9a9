<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.officecenter.mapper.WorkScheduleStaffConfigMapper">

    <sql id="Base_Column_List">
		t.id,
		t.fk_company_id,
		t.fk_staff_id,
		t.working_start_time,
		t.working_end_time,
		t.noon_break_start_time,
		t.noon_break_end_time,
		t.working_duration,
		t.working_week_cycle,
		t.effective_start_time,
		t.effective_end_time,
		t.gmt_create,
		t.gmt_create_user,
		t.gmt_modified,
		t.gmt_modified_user
	</sql>

    <select id="getWorkScheduleStaffConfig" resultType="com.get.officecenter.entity.WorkScheduleStaffConfig">
        SELECT working_start_time,working_end_time,working_week_cycle
        FROM m_work_schedule_staff_config
        <where>
            AND fk_company_id = #{timeConfigDto.fkCompanyId}
            AND fk_staff_id = #{timeConfigDto.fkStaffId}
            <!--<if test="timeConfigDto.week != null">-->
                <!--AND FIND_IN_SET(#{timeConfigDto.week},working_week_cycle)-->
            <!--</if>-->
            AND DATE_FORMAT(effective_start_time,'%Y-%m-%d') &lt;= DATE_FORMAT(#{timeConfigDto.applicationTime},'%Y-%m-%d')
            AND DATE_FORMAT(effective_end_time,'%Y-%m-%d') &gt;= DATE_FORMAT(#{timeConfigDto.applicationTime},'%Y-%m-%d')
        </where>
    </select>

    <select id="selectWorkScheduleStaffConfigs"
            resultType="com.get.officecenter.vo.WorkScheduleStaffConfigVo">
        SELECT mwssc.*, ms.attendance_num AS attendanceNum FROM ais_office_center.m_work_schedule_staff_config AS mwssc
        INNER JOIN ais_permission_center.m_staff AS ms ON ms.id = mwssc.fk_staff_id
        <where>
            <if test="data.fkCompanyId != null  and data.fkCompanyId != '' ">
                AND mwssc.fk_company_id = #{data.fkCompanyId}
            </if>
            <if test="data.fkStaffName != null  and data.fkStaffName != '' ">
                AND (
                ms.name like concat("%",#{data.fkStaffName},"%")
                or
                ms.name_en like concat("%",#{data.fkStaffName},"%")
                )
            </if>
            <if test="data.attendanceNum != null  and data.attendanceNum != '' ">
                AND ms.attendance_num = #{data.attendanceNum}
            </if>
        </where>
    </select>


    <select id="getWorkScheduleStaffConfigByStaffId" resultType="com.get.officecenter.vo.WorkScheduleStaffConfigVo">
        SELECT <include refid="Base_Column_List" />
        FROM m_work_schedule_staff_config AS t
        WHERE fk_company_id = #{fkCompanyId} AND fk_staff_id = #{fkStaffId}
    </select>
</mapper>