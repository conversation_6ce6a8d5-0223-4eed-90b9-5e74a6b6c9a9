<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.officecenter.mapper.LeaveLogMapper">

    <select id="getLeaveLog" resultType="com.get.officecenter.vo.LeaveLogVo">
        SELECT  ll.*,
        ABS( IFNULL(ll.duration, 0 ) ) AS durationSum,
        laf.start_time AS startTime,
        laf.end_time AS endTime
        FROM m_leave_log AS ll
        LEFT JOIN m_leave_application_form AS laf
        ON ll.fk_leave_application_form_id=laf.id
        WHERE ll.fk_company_id = #{fkCompanyId}
        <if test="fkStaffIds != null">
            AND  ll.fk_staff_id IN
            <foreach collection="fkStaffIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND laf.fk_leave_application_form_id_revoke is null
        AND laf.status=#{status}
        AND (
            (DATE_FORMAT(laf.start_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{startDate},'%Y-%m-%d %H:%i:%s')
            AND DATE_FORMAT(laf.start_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endDate},'%Y-%m-%d %H:%i:%s'))
            OR
            (DATE_FORMAT(laf.end_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{startDate},'%Y-%m-%d %H:%i:%s')
            AND DATE_FORMAT(laf.end_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endDate},'%Y-%m-%d %H:%i:%s'))
        )
    </select>
    <select id="getLeaveLogsAboutTakeDeferredHoliday" resultType="com.get.officecenter.vo.LeaveLogVo">
        SELECT ll.fk_staff_id AS fkStaffId,COALESCE(SUM(ll.duration),0) AS durationSum
        FROM m_leave_log AS ll
        LEFT JOIN m_leave_application_form AS laf ON ll.fk_leave_application_form_id=laf.id
        WHERE
        ll.fk_company_id = #{fkCompanyId}
        AND ll.leave_type_key IN ('takeDeferredHolidays','overtime')
        <if test="fkStaffIds != null">
            AND  ll.fk_staff_id IN
            <foreach collection="fkStaffIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND DATE_FORMAT(laf.start_time,'%Y-%m-%d %H:%i:%s') &gt; DATE_FORMAT(#{endTime},'%Y-%m-%d %H:%i:%s')
        AND laf.fk_leave_application_form_id_revoke is null
        AND laf.status=#{status}
        GROUP BY ll.fk_staff_id
    </select>

    <select id="getLeaveLogsAboutAnnualVacationLastYear" resultType="com.get.officecenter.vo.LeaveLogVo">
        SELECT ll.fk_staff_id AS fkStaffId,COALESCE(SUM(ll.duration),0) AS durationSum
        FROM m_leave_log AS ll
        LEFT JOIN m_leave_stock AS ls ON ll.fk_leave_Stock_Id = ls.id
        LEFT JOIN m_leave_application_form AS laf ON ll.fk_leave_application_form_id=laf.id
        WHERE
        ll.fk_company_id = #{fkCompanyId}
        AND ll.leave_type_key = 'annualVacation'
        AND YEAR(DATE_SUB(ls.effective_deadline,INTERVAL 1 YEAR)) = YEAR(DATE_SUB(#{date},INTERVAL 1 YEAR))
        <if test="fkStaffIds != null">
            AND  ll.fk_staff_id IN
            <foreach collection="fkStaffIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND DATE_FORMAT(laf.start_time,'%Y-%m-%d %H:%i:%s') &gt; DATE_FORMAT(#{endTime},'%Y-%m-%d %H:%i:%s')
        AND laf.fk_leave_application_form_id_revoke is null
        AND laf.status=#{status}
        GROUP BY ll.fk_staff_id
    </select>

    <select id="getLeaveLogsAboutAnnualVacationThisYear" resultType="com.get.officecenter.vo.LeaveLogVo">
        SELECT ll.fk_staff_id AS fkStaffId,COALESCE(SUM(ll.duration),0) AS durationSum
        FROM m_leave_log AS ll
        LEFT JOIN m_leave_stock AS ls ON ll.fk_leave_Stock_Id = ls.id
        LEFT JOIN m_leave_application_form AS laf ON ll.fk_leave_application_form_id=laf.id
        WHERE
        ll.fk_company_id = #{fkCompanyId}
        AND ll.leave_type_key = 'annualVacation'
        AND YEAR(DATE_SUB(ls.effective_deadline,INTERVAL 1 YEAR)) = YEAR(#{date})
        <if test="fkStaffIds != null">
            AND  ll.fk_staff_id IN
            <foreach collection="fkStaffIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND DATE_FORMAT(laf.start_time,'%Y-%m-%d %H:%i:%s') &gt; DATE_FORMAT(#{endTime},'%Y-%m-%d %H:%i:%s')
        AND laf.fk_leave_application_form_id_revoke is null
        AND laf.status=#{status}
        GROUP BY ll.fk_staff_id
    </select>

    <select id="getLeaveLogsAboutDiseaseVacationQuarter" resultType="com.get.officecenter.vo.LeaveLogVo">
        SELECT ll.fk_staff_id AS fkStaffId,COALESCE(SUM(ll.duration),0) AS durationSum
        FROM m_leave_log AS ll
        LEFT JOIN m_leave_application_form AS laf ON ll.fk_leave_application_form_id=laf.id
        WHERE
        ll.fk_company_id = #{fkCompanyId}
        AND ll.leave_type_key ='diseaseVacation'
        <if test="fkStaffIds != null and fkStaffIds.size>0">
            AND  ll.fk_staff_id IN
            <foreach collection="fkStaffIds" item="fkStaffId" open="(" separator="," close=")">
                #{fkStaffId}
            </foreach>
        </if>
        <if test="fkLeaveStockIds != null and fkLeaveStockIds.size>0">
            AND ll.fk_leave_stock_id IN
            <foreach collection="fkLeaveStockIds" item="fkLeaveStockId" open="(" separator="," close=")">
                #{fkLeaveStockId}
            </foreach>
        </if>
        AND DATE_FORMAT(laf.start_time,'%Y-%m-%d %H:%i:%s') &gt; DATE_FORMAT(#{endTime},'%Y-%m-%d %H:%i:%s')
        AND laf.fk_leave_application_form_id_revoke is null
        AND laf.status=#{status}
        GROUP BY ll.fk_staff_id
    </select>

    <select id="getAnnualVacationLastYearLog" resultType="com.get.officecenter.vo.LeaveLogVo">
        SELECT ll.*,
        ABS(IFNULL(ll.duration,0)) AS durationSum,
        laf.start_time AS startTime,
        laf.end_time AS endTime
        FROM m_leave_log AS ll
        LEFT JOIN m_leave_stock AS ls
        ON ll.fk_leave_Stock_Id = ls.id
        LEFT JOIN m_leave_application_form AS laf
        ON ll.fk_leave_application_form_id=laf.id
        WHERE
        ll.opt_type_key=#{optTypeKey}
        AND ll.fk_company_id = #{fkCompanyId}
        <if test="fkStaffIds != null">
            AND  ll.fk_staff_id IN
            <foreach collection="fkStaffIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND ll.leave_type_key= #{leaveType}
        AND YEAR(ls.gmt_create) = YEAR(DATE_SUB(#{startDate},INTERVAL 1 YEAR))
        AND (
            (DATE_FORMAT(laf.start_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{startDate},'%Y-%m-%d %H:%i:%s')
            AND DATE_FORMAT(laf.start_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endDate},'%Y-%m-%d %H:%i:%s'))
            OR
            (DATE_FORMAT(laf.end_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{startDate},'%Y-%m-%d %H:%i:%s')
            AND DATE_FORMAT(laf.end_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endDate},'%Y-%m-%d %H:%i:%s'))
        )
        AND laf.fk_leave_application_form_id_revoke is null
        AND laf.status=#{status}
    </select>

    <select id="getAnnualVacationThisYearLog" resultType="com.get.officecenter.vo.LeaveLogVo">
        SELECT ll.*,
        ABS(IFNULL(ll.duration,0)) AS durationSum,
        laf.start_time AS startTime,
        laf.end_time AS endTime
        FROM m_leave_log AS ll
        LEFT JOIN m_leave_stock AS ls
        ON ll.fk_leave_Stock_Id = ls.id
        LEFT JOIN m_leave_application_form AS laf
        ON ll.fk_leave_application_form_id=laf.id
        WHERE
        ll.opt_type_key=#{optTypeKey}
        AND ll.fk_company_id = #{fkCompanyId}
        <if test="fkStaffIds != null">
            AND  ll.fk_staff_id IN
            <foreach collection="fkStaffIds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        AND ll.leave_type_key= #{leaveType}
        AND YEAR(ls.effective_deadline) - 1 = YEAR(#{date})
        AND (
            (DATE_FORMAT(laf.start_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{startDate},'%Y-%m-%d %H:%i:%s')
            AND DATE_FORMAT(laf.start_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endDate},'%Y-%m-%d %H:%i:%s'))
            OR
            (DATE_FORMAT(laf.end_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{startDate},'%Y-%m-%d %H:%i:%s')
            AND DATE_FORMAT(laf.end_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endDate},'%Y-%m-%d %H:%i:%s'))
        )
        AND laf.fk_leave_application_form_id_revoke is null
        AND laf.status=#{status}
    </select>
    <select id="getStaffOfLeaveLogList" resultType="com.get.officecenter.vo.StaffOfLeaveLogVo">
        SELECT
        b.id as fkStaffId,
        concat(if(b.is_on_duty=1,'','【离职】'),b.name,IFNULL(concat('(',b.name_en,')'),'')) as staffFullName,
        c.`name` as departmentName
        FROM
        ( SELECT fk_staff_id FROM m_leave_log
        WHERE 1=1
        <if test="staffOfLeaveLogDto.fkCompanyId !=null">
            and fk_company_id = #{staffOfLeaveLogDto.fkCompanyId}
        </if>
        GROUP BY fk_staff_id ) a
        LEFT JOIN ais_permission_center.m_staff b on b.id = a.fk_staff_id
        LEFT JOIN ais_permission_center.m_department c on c.id = b.fk_department_id
        WHERE b.is_on_duty = 1
        <if test="staffOfLeaveLogDto.fkStaffName !=null">
            and (
            b.name like concat("%",#{staffOfLeaveLogDto.fkStaffName},"%")
            or
            b.name_en like concat("%",#{staffOfLeaveLogDto.fkStaffName},"%")
            )
        </if>
        ORDER BY b.name_en
    </select>
</mapper>