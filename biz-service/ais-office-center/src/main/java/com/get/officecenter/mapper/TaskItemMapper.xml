<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.officecenter.mapper.TaskItemMapper">
    <update id="updateBatchById">
        UPDATE m_task_item
        SET status = #{status},
        status_modified_time = NOW(),
        gmt_modified = NOW(),
        gmt_modified_user = #{gmtModifiedUser}
        WHERE fk_table_id IN
        <foreach collection="taskItemBatch" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND fk_table_name = 'm_student_offer_item' AND fk_task_id = #{taskId}
    </update>


    <select id="getTaskItemList" resultType="com.get.officecenter.entity.TaskItem">
        SELECT * FROM m_task_item
        WHERE fk_task_id IN
        <foreach collection="taskIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <insert id="batchInsert">
        INSERT INTO m_task_item (
        fk_task_id,
        fk_staff_id_to,
        fk_role_key,
        fk_table_name,
        fk_table_id,
        status,
        status_modified_time,
        gmt_create,
        gmt_create_user,
        gmt_modified,
        gmt_modified_user
        ) VALUES
        <foreach collection="taskItems" item="item" separator=",">
            (
            #{item.fkTaskId},
            #{item.fkStaffIdTo},
            #{item.fkRoleKey},
            #{item.fkTableName},
            #{item.fkTableId},
            #{item.status},
            #{item.statusModifiedTime},
            #{item.gmtCreate},
            #{item.gmtCreateUser},
            #{item.gmtModified},
            #{item.gmtModifiedUser}
            )
        </foreach>
    </insert>




    <select id="getTaskItems" resultType="com.get.officecenter.vo.TaskItemVo">
        SELECT
        any_value(res.id) AS id,
        any_value(res.fk_task_id) AS fkTaskId,
        any_value(res.fk_staff_id_to) AS fkStaffIdTo,
        any_value(res.fk_table_name)  AS fkTableName,
        any_value(res.fk_table_id) AS fkTableId,
        any_value(res.status) AS status,
        any_value(res.status_modified_time) AS statusModifiedTime,
        any_value(res.gmt_create) AS gmtCreate,
        any_value(res.gmt_create_user) AS gmtCreateUser,
        any_value(res.gmt_modified) AS gmtModified,
        any_value(res.gmt_modified_user) AS gmtModifiedUser,
        any_value(res.max_date) AS max_date,
        any_value(res.agentName) AS agentName,
        any_value(res.agentId) AS agentId,
        any_value(res.offerItemId) AS offerItemId,
        any_value(res.fkStudentOfferId) AS fkStudentOfferId,
        any_value(res.fkStudentId) AS fkStudentId,
        any_value(res.taskRelatedContent) AS taskRelatedContent,
        any_value(res.lastComment) AS lastComment,
        any_value(res.bd_code) AS bd_code,
        any_value(res.fkAreaCountryId) AS fkAreaCountryId,
        GROUP_CONCAT(CAST(res.fk_staff_id_to AS CHAR)) AS fkStaffIdTos,
        GROUP_CONCAT(res.recipientStaffName) AS recipientStaffName
            FROM (
            SELECT
                a.*,
                GREATEST(IFNULL(a.gmt_create, '1000-01-01 00:00:00'),
                IFNULL(a.gmt_modified, '1000-01-01 00:00:00'),
                IFNULL(a.status_modified_time, '1000-01-01 00:00:00')) AS max_date,
                m.name AS agentName,
                offerItem.agentId AS agentId,
                CASE WHEN IFNULL(ms.name_en,'')='' THEN ms.`name` ELSE CONCAT(ms.`name`,'（',ms.name_en,'）') END AS recipientStaffName,
                offerItem.offerItemId,
                offerItem.fkStudentOfferId,
                offerItem.fkStudentId,
                offerItem.fkAreaCountryId ,
                rc.bd_code,
                latest_comment.comment AS lastComment,
                CASE
                    WHEN a.fk_table_name = 'm_student_offer_item' THEN offerItem.taskRelatedContent
                    ELSE ''
                END AS taskRelatedContent
            FROM
                m_task_item a
            LEFT JOIN (
                SELECT
                    id AS offerItemId,
                    fkStudentOfferId,
                    fkStudentId,
                    fkAreaCountryId,
                    agentId,
        CONCAT_WS('，',
        stepName,
        studentName,
        fkAreaCountryName,
        institutionFullName,
        courseFullName,
        deferOpeningTime) AS taskRelatedContent
                FROM (
                    SELECT
                    a.id,
                    a.fk_student_offer_id AS fkStudentOfferId,
                    a.fk_student_id AS fkStudentId,
                    a.fk_area_country_id AS fkAreaCountryId,
                    a.fk_agent_id AS agentId,
                    CONCAT('【', f.step_name, '】') AS stepName,
                    CONCAT(b.`name`, IF (b.first_name IS NULL AND b.last_name IS NULL, '', CONCAT("（", b.first_name, " ", b.last_name, "）"))) AS studentName,
                    c.`name` AS fkAreaCountryName,
                    CONCAT(d.`name`, IF (d.name_chn IS NULL OR d.name_chn = '', '', CONCAT("（", d.name_chn, "）"))) AS institutionFullName,
                    CASE
                        WHEN a.fk_institution_course_id = -1 THEN a.old_course_custom_name
                        ELSE CONCAT(e.`name`, IF (e.name_chn IS NULL OR e.name_chn = '', '', CONCAT("（", e.name_chn, "）")))
                    END AS courseFullName,
                    a.defer_opening_time AS deferOpeningTime
                    FROM ais_sale_center.m_student_offer_item a
                    LEFT JOIN ais_sale_center.m_student b on a.fk_student_id = b.id
                    LEFT JOIN ais_institution_center.u_area_country c ON a.fk_area_country_id = c.id
                    LEFT JOIN ais_institution_center.m_institution d ON a.fk_institution_id = d.id
                    LEFT JOIN ais_institution_center.m_institution_course e ON a.fk_institution_course_id = e.id
                    LEFT JOIN ais_sale_center.u_student_offer_item_step f ON a.fk_student_offer_item_step_id = f.id
                ) temp
            ) offerItem ON a.fk_table_name = 'm_student_offer_item' AND a.fk_table_id = offerItem.offerItemId
        LEFT JOIN (
        SELECT
        latest.fk_table_id AS itemId,
        latest.fk_task_id AS taskId,
        sc1.COMMENT AS COMMENT,
        sc1.gmt_create AS comment_time
        FROM
        s_comment sc1
        INNER JOIN (
        SELECT
        mti2.fk_table_id,
        mti2.fk_task_id,
        MAX(sc2.id) AS max_id
        FROM
        s_comment AS sc2
        INNER JOIN m_task_item AS mti2 ON mti2.id = sc2.fk_table_id
        AND sc2.fk_table_name = 'm_task_item'
        GROUP BY
        mti2.fk_table_id,mti2.fk_task_id ) latest ON sc1.id = latest.max_id
        WHERE
        sc1.fk_table_name = 'm_task_item' ) latest_comment ON  latest_comment.itemId = a.fk_table_id AND latest_comment.taskId = a.fk_task_id
        LEFT JOIN ais_sale_center.m_agent m ON m.id = offerItem.agentId
        LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = a.fk_staff_id_to
        LEFT JOIN ais_sale_center.r_staff_bd_code AS rc ON rc.fk_staff_id = a.fk_staff_id_to
        )  res
        WHERE 1 = 1
        <if test="taskItemDto.fkTaskId != null">
            AND res.fk_task_id = #{taskItemDto.fkTaskId}
        </if>
        <if test="fkAreaCountryIds!=null and fkAreaCountryIds.size()>0">
          AND res.fkAreaCountryId IN
              <foreach collection="fkAreaCountryIds" item="fkAreaCountryId" open="(" separator="," close=")">
                  #{fkAreaCountryId}
            </foreach>
        </if>
        <if test="taskItemDto != null and taskItemDto.recipientStaffName != null and taskItemDto.recipientStaffName != ''">
          AND  res.recipientStaffName LIKE CONCAT('%', #{taskItemDto.recipientStaffName}, '%')
        </if>

        <if test="taskItemDto.agentName != null and taskItemDto.agentName != ''">
            AND REPLACE(LOWER(res.agentName),' ','')LIKE CONCAT('%', #{taskItemDto.agentName}, '%')
        </if>
        <if test="staffIds != null and staffIds.size() > 0">
            AND res.fk_staff_id_to IN
            <foreach collection="staffIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="taskItemDto.taskStatus != null">
            AND res.status = #{taskItemDto.taskStatus}
        </if>
        <if test="taskItemDto.taskRelatedKeyword != null and taskItemDto.taskRelatedKeyword != ''">
            AND LOWER(res.taskRelatedContent) LIKE CONCAT('%', #{taskItemDto.taskRelatedKeyword}, '%')
        </if>
        <if test="isFlag ==true">
            GROUP BY
            res.offerItemId
        </if>
        <if test="isFlag ==false">
            GROUP BY
            res.fk_staff_id_to
        </if>
        <choose>
            <when test="isFlag == true">
                ORDER BY
                MAX(res.bd_code),
                MAX(res.agentId),
                MAX(res.fkStudentId),
                res.offerItemId,
                MAX(res.max_date) DESC
            </when>
            <otherwise>
                ORDER BY
                MAX(res.bd_code),
                MAX(res.agentId),
                MAX(res.fkStudentId),
                res.fk_staff_id_to,
                MAX(res.max_date) DESC
            </otherwise>
        </choose>

</select>



    <select id="getFkStaffIdTo" resultType="java.lang.Long">
        SELECT fk_staff_id_to FROM m_task_item
        WHERE fk_task_id IN
        <foreach collection="taskIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY fk_staff_id_to
    </select>
    <select id="getStaffIdByStatus" resultType="java.lang.Long">
        SELECT fk_staff_id_to FROM m_task_item
        WHERE fk_task_id IN
        <foreach collection="taskIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND status !=1
        GROUP BY fk_staff_id_to
    </select>

    <select id="getPersonalTaskStatistics" resultType="com.get.officecenter.vo.TaskStatisticsVo">
        SELECT
        t.id AS taskId,
        i.fk_staff_id_to AS staffId,
        s.NAME AS recipientStaffName,
        COUNT(DISTINCT i.fk_table_id) AS taskItemTotal,
        COUNT(DISTINCT CASE WHEN i.STATUS = 1 THEN soi.id END) AS taskItemFinishedCount,
        COUNT(DISTINCT CASE WHEN i.STATUS = 2 THEN soi.id END) AS taskItemUnfinishedCount,
        COUNT(DISTINCT CASE WHEN i.STATUS = 0 THEN soi.id END) AS taskItemTodoCount,
        MIN(CASE WHEN i.STATUS = 1 THEN i.status_modified_time END) AS firstTime,
        MAX(CASE WHEN i.STATUS = 1 THEN i.status_modified_time END) AS lastTime,
        GROUP_CONCAT(DISTINCT soi.fk_area_country_id) AS countryIds,
        GROUP_CONCAT(DISTINCT uac.NAME) AS countryNames,
        COUNT(DISTINCT soi.fk_student_id) AS studentCount
        FROM
        m_task t
        LEFT JOIN m_task_item i ON t.id = i.fk_task_id
        LEFT JOIN ais_permission_center.m_staff s ON i.fk_staff_id_to = s.id
        LEFT JOIN ais_sale_center.m_student_offer_item soi ON i.fk_table_name = 'm_student_offer_item'
        AND i.fk_table_id = soi.id
        LEFT JOIN ais_institution_center.u_area_country uac ON soi.fk_area_country_id = uac.id
        WHERE
        1=1
        <if test="taskItemDto.fkTaskId != null">
            AND t.id = #{taskItemDto.fkTaskId}
        </if>
        <if test="staffIds != null and staffIds.size() > 0">
            AND i.fk_staff_id_to IN
            <foreach collection="staffIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="taskItemDto.taskStatus != null">
            AND i.status = #{taskItemDto.taskStatus}
        </if>
        <if test="fkAreaCountryIds != null and fkAreaCountryIds.size() > 0">
            AND soi.fk_area_country_id IN
            <foreach collection="fkAreaCountryIds" item="countryId" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>
        <if test="taskItemDto.recipientStaffName != null and taskItemDto.recipientStaffName != ''">
            AND (
            LOWER(s.name) LIKE CONCAT('%', LOWER(#{taskItemDto.recipientStaffName}), '%') OR
            LOWER(s.name_en) LIKE CONCAT('%', LOWER(#{taskItemDto.recipientStaffName}), '%')
            )
        </if>
        GROUP BY
         i.fk_staff_id_to
        ORDER BY
        taskItemFinishedCount DESC,
        lastTime DESC,
        s.name
    </select>
    <select id="getTaskItemListByTaskItemIds" resultType="com.get.officecenter.entity.TaskItem">
        SELECT id,fk_task_id,fk_table_id,fk_table_name FROM m_task_item WHERE id IN
        <foreach collection="taskItemIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>



    <select id="getTaskFeedbackCountByStatus" resultType="com.get.officecenter.vo.CommentVo">
        SELECT
            <if test="isFlag==true">
                mi.fk_staff_id_to AS staffId,
                latest_comment.COMMENT,
                ms.name AS staffName,
                latest_comment.commentId AS commentId,
                COUNT(COMMENT) AS commentCount
            </if>
            <if test="isFlag==false">
                latest_comment.COMMENT,
                COUNT(COMMENT) AS commentCount
            </if>

        FROM m_task m INNER JOIN m_task_item mi ON m.id = mi.fk_task_id
        INNER JOIN (
        SELECT
        latest.fk_table_id AS itemId,
        sc1.COMMENT AS COMMENT,
        sc1.id AS commentId
        FROM
        s_comment sc1
        INNER JOIN (
        SELECT
        mti2.fk_table_id,
        MAX(sc2.id) AS max_id
        FROM
        s_comment AS sc2
        INNER JOIN
        m_task_item AS mti2 ON mti2.id = sc2.fk_table_id
        AND sc2.fk_table_name = 'm_task_item'
        WHERE mti2.fk_task_id = #{taskId}
        GROUP BY
        mti2.fk_table_id
        ) latest ON sc1.id = latest.max_id
        WHERE
        sc1.fk_table_name = 'm_task_item'
        AND sc1.COMMENT IS NOT NULL
        ) latest_comment ON latest_comment.itemId = mi.fk_table_id
        LEFT JOIN ais_sale_center.m_student_offer_item soi ON mi.fk_table_name = 'm_student_offer_item'
        AND mi.fk_table_id = soi.id
        LEFT JOIN ais_permission_center.m_staff ms on ms.id = mi.fk_staff_id_to
        WHERE mi.`status`in (1,2)
        <if test="taskId != null">
            AND m.id = #{taskId}
        </if>
        <if test="staffIds != null and staffIds.size() > 0">
            AND mi.fk_staff_id_to IN
            <foreach collection="staffIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="fkAreaCountryIds != null and fkAreaCountryIds.size() > 0">
            AND soi.fk_area_country_id IN
            <foreach collection="fkAreaCountryIds" item="countryId" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>
        <if test="typeKey!=null and typeKey!=''">
            AND LOWER(fk_role_key) LIKE CONCAT('%', LOWER(#{typeKey}), '%')
        </if>
        <if test="isFlag==true">
            group by mi.fk_staff_id_to,latest_comment.COMMENT,ms.name
        </if>
        <if test="isFlag==false">
            group by latest_comment.COMMENT
        </if>

    </select>

    <select id="getTasksPendingFeedback" resultType="com.get.officecenter.vo.CommentVo">
        SELECT mi.fk_staff_id_to AS staffId,
        ms.name AS staffName
        FROM m_task m INNER JOIN m_task_item mi ON m.id  = mi.fk_task_id
                      LEFT JOIN (
            SELECT
                latest.fk_table_id AS itemId,
                sc1.COMMENT AS COMMENT
            FROM
                s_comment sc1
                    INNER JOIN (
                    SELECT
                        mti2.fk_table_id,
                        MAX(sc2.id) AS max_id
                    FROM
                        s_comment AS sc2
                            INNER JOIN
                        m_task_item AS mti2 ON mti2.id = sc2.fk_table_id
                            AND sc2.fk_table_name = 'm_task_item'
                    WHERE mti2.fk_task_id = #{taskId}
                    GROUP BY
                        mti2.fk_table_id
                ) latest ON sc1.id = latest.max_id
            WHERE
                sc1.fk_table_name = 'm_task_item'
        ) latest_comment ON latest_comment.itemId = mi.fk_table_id
        LEFT JOIN ais_sale_center.m_student_offer_item soi ON mi.fk_table_name = 'm_student_offer_item'
        AND mi.fk_table_id = soi.id
                      LEFT JOIN ais_permission_center.m_staff ms on ms.id = mi.fk_staff_id_to
        WHERE     mi.`status`in (1,2) AND COMMENT IS NULL
        <if test="taskId != null">
            AND m.id = #{taskId}
        </if>
          <if test="staffIds != null and staffIds.size() > 0">
            AND mi.fk_staff_id_to IN
            <foreach collection="staffIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
          <if test="fkAreaCountryIds != null and fkAreaCountryIds.size() > 0">
            AND soi.fk_area_country_id IN
            <foreach collection="fkAreaCountryIds" item="countryId" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>
        <if test="typeKey!=null and typeKey!=''">
            AND LOWER(fk_role_key) LIKE CONCAT('%', LOWER(#{typeKey}), '%')
        </if>
        group by mi.fk_staff_id_to,latest_comment.COMMENT,ms.name,mi.fk_table_id
    </select>
    <select id="getCountUnfinishedTasks" resultType="com.get.officecenter.vo.CommentVo">
        SELECT mi.fk_staff_id_to AS staffId,
        ms.name AS staffName
        FROM m_task m INNER JOIN m_task_item mi ON m.id  = mi.fk_task_id
        LEFT JOIN (
        SELECT
        latest.fk_table_id AS itemId,
        sc1.COMMENT AS COMMENT
        FROM
        s_comment sc1
        INNER JOIN (
        SELECT
        mti2.fk_table_id,
        MAX(sc2.id) AS max_id
        FROM
        s_comment AS sc2
        INNER JOIN
        m_task_item AS mti2 ON mti2.id = sc2.fk_table_id
        AND sc2.fk_table_name = 'm_task_item'
        WHERE mti2.fk_task_id = #{taskId}
        GROUP BY
        mti2.fk_table_id
        ) latest ON sc1.id = latest.max_id
        WHERE
        sc1.fk_table_name = 'm_task_item'
        ) latest_comment ON latest_comment.itemId = mi.fk_table_id
        LEFT JOIN ais_sale_center.m_student_offer_item soi ON mi.fk_table_name = 'm_student_offer_item'
        AND mi.fk_table_id = soi.id
        LEFT JOIN ais_permission_center.m_staff ms on ms.id = mi.fk_staff_id_to
        WHERE     mi.`status`in (0)
        <if test="taskId != null">
            AND m.id = #{taskId}
        </if>
        <if test="staffIds != null and staffIds.size() > 0">
            AND mi.fk_staff_id_to IN
            <foreach collection="staffIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="fkAreaCountryIds != null and fkAreaCountryIds.size() > 0">
            AND soi.fk_area_country_id IN
            <foreach collection="fkAreaCountryIds" item="countryId" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>
        <if test="typeKey!=null and typeKey!=''">
            AND LOWER(fk_role_key) LIKE CONCAT('%', LOWER(#{typeKey}), '%')
        </if>
        group by mi.fk_staff_id_to,latest_comment.COMMENT,ms.name,mi.fk_table_id
    </select>
    <select id="getTaskAllStaff" resultType="com.get.officecenter.vo.CommentVo">
        SELECT
            mi.fk_staff_id_to AS staffId,
        CASE WHEN IFNULL(ms.name_en,'')='' THEN ms.`name` ELSE CONCAT(ms.`name`,'（',ms.name_en,'）') END AS staffName
        FROM m_task m INNER JOIN m_task_item mi ON m.id = mi.fk_task_id
        LEFT JOIN ais_sale_center.m_student_offer_item soi ON mi.fk_table_name = 'm_student_offer_item'
        AND mi.fk_table_id = soi.id
        LEFT JOIN ais_permission_center.m_staff ms on ms.id = mi.fk_staff_id_to
                                                          where 1=1
        <if test="taskId != null">
            AND m.id = #{taskId}
        </if>
        <if test="staffIds != null and staffIds.size() > 0">
            AND mi.fk_staff_id_to IN
            <foreach collection="staffIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="fkAreaCountryIds != null and fkAreaCountryIds.size() > 0">
            AND soi.fk_area_country_id IN
            <foreach collection="fkAreaCountryIds" item="countryId" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>
        <if test="typeKey!=null and typeKey!=''">
            AND LOWER(fk_role_key) LIKE CONCAT('%', LOWER(#{typeKey}), '%')
        </if>
            GROUP by mi.fk_staff_id_to
    </select>
    <select id="getTotalByItem" resultType="com.get.officecenter.vo.CommentVo">
        SELECT sc1.`comment` AS comment, count(1) AS commentCount FROM
            s_comment AS sc1
                INNER JOIN
            (
                SELECT
                    mti2.fk_table_id,
                    MAX( sc2.id ) AS max_id
                FROM
                    s_comment AS sc2
                        INNER JOIN m_task_item AS mti2 ON mti2.id = sc2.fk_table_id
                        AND sc2.fk_table_name = 'm_task_item'
                        LEFT JOIN
                    ais_sale_center.m_student_offer_item soi
                    ON mti2.fk_table_name = 'm_student_offer_item'
                        AND mti2.fk_table_id = soi.id
                        LEFT JOIN
                    ais_permission_center.m_staff ms
                    on ms.id = mti2.fk_staff_id_to
                WHERE
                    mti2.fk_task_id = #{taskId} AND mti2.`status` in (1,2)
        <if test="staffIds != null and staffIds.size() > 0">
            AND mti2.fk_staff_id_to IN
            <foreach collection="staffIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="fkAreaCountryIds != null and fkAreaCountryIds.size() > 0">
            AND soi.fk_area_country_id IN
            <foreach collection="fkAreaCountryIds" item="countryId" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>
                GROUP BY
                    mti2.fk_table_id
            )a ON a.max_id = sc1.id
        GROUP BY sc1.`comment`
    </select>
    <select id="getTasksFeedback" resultType="java.lang.Integer">
        SELECT
            COUNT(DISTINCT mi.fk_table_id)
        FROM
            m_task m
                INNER JOIN m_task_item mi ON m.id = mi.fk_task_id
        LEFT JOIN (
        SELECT
        latest.fk_table_id AS itemId,
        sc1.COMMENT AS COMMENT
        FROM
        s_comment sc1
        INNER JOIN (
        SELECT
        mti2.fk_table_id,
        MAX(sc2.id) AS max_id
        FROM
        s_comment AS sc2
        INNER JOIN
        m_task_item AS mti2 ON mti2.id = sc2.fk_table_id
        AND sc2.fk_table_name = 'm_task_item'
        WHERE mti2.fk_task_id = #{taskId} AND mti2.fk_table_name = 'm_student_offer_item'
        GROUP BY
        mti2.fk_table_id
        ) latest ON sc1.id = latest.max_id
        WHERE
        sc1.fk_table_name = 'm_task_item'
        ) latest_comment ON latest_comment.itemId = mi.fk_table_id
                LEFT JOIN ais_permission_center.m_staff ms ON ms.id = mi.fk_staff_id_to
                LEFT JOIN ais_sale_center.m_student_offer_item soi ON mi.fk_table_name = 'm_student_offer_item'
                AND mi.fk_table_id = soi.id
        WHERE
            mi.`status` IN (1, 2)
        <if test="staffIds != null and staffIds.size() > 0">
            AND mi.fk_staff_id_to IN
            <foreach collection="staffIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="fkAreaCountryIds != null and fkAreaCountryIds.size() > 0">
            AND soi.fk_area_country_id IN
            <foreach collection="fkAreaCountryIds" item="countryId" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>
          AND latest_comment.COMMENT IS NULL
          AND mi.fk_table_name = 'm_student_offer_item'
          AND m.id = #{taskId}
    </select>
    <select id="getCountUnfinish" resultType="java.lang.Integer">
        SELECT count(DISTINCT soi.id)
        FROM m_task m INNER JOIN m_task_item mi ON m.id  = mi.fk_task_id
        LEFT JOIN ais_sale_center.m_student_offer_item soi ON mi.fk_table_name = 'm_student_offer_item'
        AND mi.fk_table_id = soi.id
        LEFT JOIN ais_permission_center.m_staff ms on ms.id = mi.fk_staff_id_to
        WHERE     mi.`status`=0
        <if test="taskId != null">
            AND m.id = #{taskId}
        </if>
        <if test="staffIds != null and staffIds.size() > 0">
            AND mi.fk_staff_id_to IN
            <foreach collection="staffIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="fkAreaCountryIds != null and fkAreaCountryIds.size() > 0">
            AND soi.fk_area_country_id IN
            <foreach collection="fkAreaCountryIds" item="countryId" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>
    </select>

</mapper>
