<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.officecenter.mapper.LeaveApplicationFormMapper">
  <select id="getLeaveApplicationFormByStatus" resultType="java.lang.Long">
    SELECT id FROM m_leave_application_form
    WHERE
    status = #{status}
    AND id IN
    <foreach collection="formIds" item="formId" open="(" separator="," close=")">
      #{formId}
    </foreach>
  </select>

  <select id="getLeaveApplicationFormByFkStaffIds" resultType="com.get.officecenter.vo.LeaveApplicationFormVo">
    SELECT mlaf.*,ulaft.type_name AS leaveApplicationFormTypeName FROM m_leave_application_form AS mlaf
    LEFT JOIN u_leave_application_form_type AS ulaft
    ON mlaf.fk_leave_application_form_type_id=ulaft.id
    <where>
      (
        (DATE_FORMAT(mlaf.start_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{startTime},'%Y-%m-%d %H:%i:%s')
        AND DATE_FORMAT(mlaf.start_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endTime},'%Y-%m-%d %H:%i:%s'))
        OR
        (DATE_FORMAT(mlaf.end_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{startTime},'%Y-%m-%d %H:%i:%s')
        AND DATE_FORMAT(mlaf.end_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endTime},'%Y-%m-%d %H:%i:%s'))
      )

      AND mlaf.status = 1
      AND mlaf.fk_company_id = #{fkCompanyId}
      AND mlaf.fk_leave_application_form_id_revoke is null
      <if test="fkStaffIds!=null">
        AND mlaf.fk_staff_id IN
        <foreach collection="fkStaffIds" item="fkStaffId" open="(" separator="," close=")">
          #{fkStaffId}
        </foreach>
      </if>
    </where>
  </select>
  <select id="getLeaveApplicationFormStatus" resultType="com.get.officecenter.entity.LeaveApplicationForm">
    SELECT * FROM m_leave_application_form WHERE
    id IN
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </select>

  <select id="getLeaveApplicationFormList" resultType="com.get.officecenter.vo.LeaveApplicationFormVo">
    SELECT mlaf.*,ulaft.type_name AS leaveApplicationFormTypeName
    FROM m_leave_application_form AS mlaf
    LEFT JOIN u_leave_application_form_type AS ulaft ON mlaf.fk_leave_application_form_type_id=ulaft.id
    <where>
      AND mlaf.fk_company_id = #{fkCompanyId}
      <if test="startTime != null and endTime != null">
      AND (
          (DATE_FORMAT(mlaf.start_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{startTime},'%Y-%m-%d %H:%i:%s')
          AND DATE_FORMAT(mlaf.start_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endTime},'%Y-%m-%d %H:%i:%s'))
          OR
          (DATE_FORMAT(mlaf.end_time,'%Y-%m-%d %H:%i:%s') &gt;= DATE_FORMAT(#{startTime},'%Y-%m-%d %H:%i:%s')
          AND DATE_FORMAT(mlaf.end_time,'%Y-%m-%d %H:%i:%s') &lt;= DATE_FORMAT(#{endTime},'%Y-%m-%d %H:%i:%s'))
      )
      </if>
      AND mlaf.status = 1
      <if test="fkStaffIds!=null">
        AND mlaf.fk_staff_id IN
        <foreach collection="fkStaffIds" item="fkStaffId" open="(" separator="," close=")">
          #{fkStaffId}
        </foreach>
      </if>
    </where>
  </select>
</mapper>