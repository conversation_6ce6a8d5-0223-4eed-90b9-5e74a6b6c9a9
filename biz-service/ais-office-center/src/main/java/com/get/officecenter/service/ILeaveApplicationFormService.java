package com.get.officecenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.utils.ValidList;
import com.get.officecenter.vo.*;
import com.get.officecenter.vo.LeaveApplicationFormVo;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.officecenter.entity.WorkScheduleDateConfig;
import com.get.officecenter.entity.WorkScheduleStaffConfig;
import com.get.officecenter.entity.WorkScheduleTimeConfig;
import com.get.officecenter.dto.*;
import com.get.officecenter.dto.LeaveApplicationFormDto;
import com.get.officecenter.dto.query.LeaveApplicationFormQueryDto;
import com.get.remindercenter.vo.ReminderTaskCountVo;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2021/4/12 19:14
 * @verison: 1.0
 * @description:
 */
public interface ILeaveApplicationFormService {
    /**
     * @return com.get.financecenter.vo.LeaveApplicationFormVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    LeaveApplicationFormVo findLeaveApplicationFormById(Long id);

    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [leaveApplicationFormDto]
     * <AUTHOR>
     */
    Long addLeaveApplicationForm(LeaveApplicationFormDto leaveApplicationFormDto);

    /**
     *
     * @param leaveApplicationFormDtos
     */
    void batchAddLeaveApplicationForm(ValidList<LeaveApplicationFormDto> leaveApplicationFormDtos);

    /**
     * @return com.get.financecenter.vo.LeaveApplicationFormVo
     * @Description :修改
     * @Param [leaveApplicationFormDto]
     * <AUTHOR>
     */
    LeaveApplicationFormVo updateLeaveApplicationForm(LeaveApplicationFormDto leaveApplicationFormDto);

    /**
     * @return java.util.List<com.get.financecenter.vo.LeaveApplicationFormVo>
     * @Description :列表
     * @Param [leaveApplicationFormVo, page]
     * <AUTHOR>
     */
    List<LeaveApplicationFormVo> getLeaveApplicationForms(LeaveApplicationFormQueryDto leaveApplicationFormQueryDto, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedVo>
     * @Description: 附件列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<OfficeMediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.lang.Long
     * @Description: 新增附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<OfficeMediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return void
     * @Description :开启费用报销单流程
     * @Param [companyId, businessKey, procdefKey]
     * <AUTHOR>
     */
    void startProcess(Long companyId, Long businessKey, String procdefKey);

    /**
     * @return void
     * @Description :作废
     * @Param [id]
     * <AUTHOR>
     */
    void updateStatus(Long id);

    /**
     * @return String
     * @Description :根据工休单id获取请假类型
     * @Param [id]
     * <AUTHOR>
     */
    String getLeaveTypeNameById(Long id);

    /**
     * @return String
     * @Description :根据工休单id获取请假类型key
     * @Param [id]
     * <AUTHOR>
     */
    String getLeaveTypeKeyById(Long id);

    /**
     * 根据id获取表单
     *
     * @param id
     * @return LeaveApplicationForm
     */
    LeaveApplicationForm getLeaveApplicationForm(Long id);

    /**
     * 根据被撤单id回显撤销单的内容
     *
     * @param id
     * @return
     */
    LeaveApplicationFormVo revokeLeaveApplicationForm(Long id);


    /**
     * 根据员工Ids获取工休单信息
     */
    Map<Long, String> getLeaveApplicationFormByFkStaffIds(Set<Long> fkStaffIds,
                                                          Date startTime,
                                                          Date endTime,
                                                          Long fkCompanyId,
                                                          Map<Long, Long> departmentIdMap,
                                                          List<WorkScheduleTimeConfig> workScheduleTimeConfigList,
                                                          Map<Long,List<WorkScheduleStaffConfig>> workScheduleStaffConfigListMap,
                                                          List<WorkScheduleDateConfig> workScheduleDateConfigList);

    /**
     * 根据员工IDs以及时间范围获取工休单信息
     * @return
     */
    List<LeaveApplicationFormVo> getLeaveApplicationFormList(Set<Long> fkStaffIds, Date startTime, Date endTime, Long fkCompanyId);

    /**
     * 获取我的申请数量和我的审批数量
     *
     * @param staffId
     * @return
     */
    List<ReminderTaskCountVo> getApplicationAndApprovalCount(Long staffId);

    /**
     * 是否有附件
     *
     * @param key
     * @param id
     * @return
     */
    Boolean hasMediaAndAttach(String key, Long id);

    /**
     * 发送企业微信提醒
     */
    Boolean sendWxCpMessage(String businessKey) throws Exception;

    /**
     * 获取某一休假类型的剩余时长
     *
     * @param remainingDayDto
     * @return
     */
    RemainingDayVo getRemainingDays(RemainingDayDto remainingDayDto);

    /**
    * 获取上下班时间配置
    * <AUTHOR>
    * @DateTime 2023/1/10 12:37
    */
    TimeConfigVo getTimeConfig(TimeConfigDto timeConfigDto);

    /**
     * 计算工休申请时长
     * <AUTHOR>
     * @DateTime 2023/2/28 12:29
     */
    ApplicationFormDaysVo getApplicationFormDays(GetApplicationFormDaysDto getApplicationFormDaysDto);

    /**
     * 作废工休单（行政人员权限）
     * @param id
     */
    void cancelLeaveApplicationForm(Long id);

    /**
     * 删除
     * @param id
     */
    void delete(Long id);

    /**
     * 延迟时间及是否上传附件
     * @param id
     * @return
     */
    ApprovalDelayConfigVo getApprovalDelayConfig(Long id);

    List<LeaveApplicationFormExportVo> getLeaveApplicationFormExportDtos(LeaveApplicationFormQueryDto leaveApplicationFormQueryDto);
}
