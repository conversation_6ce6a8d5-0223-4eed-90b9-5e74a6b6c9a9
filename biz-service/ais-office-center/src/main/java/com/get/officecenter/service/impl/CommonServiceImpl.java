package com.get.officecenter.service.impl;

import com.get.officecenter.mapper.CommonMapper;
import com.get.officecenter.service.ICommonService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * @author: Sea
 * @create: 2021/4/8 15:54
 * @verison: 1.0
 * @description:
 */
@Service
public class CommonServiceImpl implements ICommonService {
    @Resource
    private CommonMapper commonMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Boolean changeStatus(Integer status, String tableName, Long businessKey) {
        commonMapper.changeStatus(status, tableName, businessKey);
        return true;
    }
}
