package com.get.officecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.officecenter.vo.WorkScheduleStaffConfigVo;
import com.get.officecenter.entity.WorkScheduleStaffConfig;
import com.get.officecenter.mapper.WorkScheduleStaffConfigMapper;
import com.get.officecenter.service.WorkScheduleStaffConfigService;
import com.get.officecenter.dto.TimeConfigDto;
import com.get.officecenter.dto.WorkScheduleStaffConfigAddDto;
import com.get.officecenter.dto.WorkScheduleStaffConfigListDto;
import com.get.officecenter.dto.WorkScheduleStaffConfigUpdateDto;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 特殊人员工作时间管理
 *
 * @Date 17:23 2022/11/7
 * <AUTHOR>
 */
@Service
public class WorkScheduleStaffServiceImpl implements WorkScheduleStaffConfigService {
    @Resource
    private WorkScheduleStaffConfigMapper workScheduleStaffConfigMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private UtilService utilService;


    /**
     * 查询特殊人员工作时间设置
     *
     * @Date 12:47 2022/11/7
     * <AUTHOR>
     */
    @Override
    public List<WorkScheduleStaffConfigVo> getWorkScheduleStaffConfigs(WorkScheduleStaffConfigListDto data, SearchBean<WorkScheduleStaffConfigListDto> page) {
        //获取分页数据
        IPage<WorkScheduleStaffConfig> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<WorkScheduleStaffConfigVo> datas = workScheduleStaffConfigMapper.selectWorkScheduleStaffConfigs(pages, data);
        page.setAll((int) pages.getTotal());
        //公司ids
        Set<Long> fkCompanyIds = datas.stream().map(WorkScheduleStaffConfigVo::getFkCompanyId).collect(Collectors.toSet());
        //员工id
        Set<Long> fkStaffIds = datas.stream().map(WorkScheduleStaffConfigVo::getFkStaffId).collect(Collectors.toSet());
        Map<Long, String> companyNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkCompanyIds)) {
            Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(fkCompanyIds);
            if (result.isSuccess() && result.getData() != null) {
                companyNamesByIds = result.getData();
            }
        }
        Map<Long, Staff> staffsByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkStaffIds)) {
            Result<Map<Long, Staff>> result = permissionCenterClient.getStaffMapByStaffIds(fkStaffIds);
            if (result.isSuccess() && result.getData() != null) {
                staffsByIds = result.getData();
            }
        }
        for (WorkScheduleStaffConfigVo workScheduleStaffConfigVo : datas) {
            workScheduleStaffConfigVo.setFkCompanyName(companyNamesByIds.get(workScheduleStaffConfigVo.getFkCompanyId()));
            Staff staff = staffsByIds.get(workScheduleStaffConfigVo.getFkStaffId());
            if (GeneralTool.isNotEmpty(staff)) {
                workScheduleStaffConfigVo.setAttendanceNum(staff.getAttendanceNum());
                StringBuffer str = new StringBuffer();
                str.append(staff.getName()).append("（").append(staff.getNameEn()).append( "）");
                workScheduleStaffConfigVo.setFkStaffName(str.toString());
            }
        }
        return datas;
    }

    /**
     * 工作时间设置详情
     *
     * @Date 12:54 2022/11/7
     * <AUTHOR>
     */
    @Override
    public WorkScheduleStaffConfigVo findWorkScheduleStaffConfigById(Long id) {
        WorkScheduleStaffConfig workScheduleStaffConfig = workScheduleStaffConfigMapper.selectById(id);
        return BeanCopyUtils.objClone(workScheduleStaffConfig, WorkScheduleStaffConfigVo::new);
    }

    /**
     * 新增工作时间设置
     *
     * @Date 12:50 2022/11/7
     * <AUTHOR>
     */
    @Override
    public void add(WorkScheduleStaffConfigAddDto workScheduleDateConfigVo) {
        WorkScheduleStaffConfig workScheduleStaffConfig = BeanCopyUtils.objClone(workScheduleDateConfigVo, WorkScheduleStaffConfig::new);
        List<WorkScheduleStaffConfigVo> workScheduleStaffConfigVoList = workScheduleStaffConfigMapper.getWorkScheduleStaffConfigByStaffId(workScheduleDateConfigVo.getFkCompanyId(), workScheduleDateConfigVo.getFkStaffId());
        //校验特殊人员时间配置有效时间是否重叠
        if(GeneralTool.isNotEmpty(workScheduleStaffConfigVoList)){
            Date newEffectiveStartTime = workScheduleDateConfigVo.getEffectiveStartTime();
            Date newEffectiveEndTime = workScheduleDateConfigVo.getEffectiveEndTime();
            List<WorkScheduleStaffConfigVo> collect = workScheduleStaffConfigVoList.stream()
                    .filter(c -> (c.getEffectiveStartTime().compareTo(newEffectiveStartTime) < 0 && c.getEffectiveEndTime().compareTo(newEffectiveStartTime) > 0) ||
                            (c.getEffectiveStartTime().compareTo(newEffectiveEndTime) < 0 && c.getEffectiveEndTime().compareTo(newEffectiveEndTime) > 0))
                    .collect(Collectors.toList());
            if(GeneralTool.isNotEmpty(collect)){
                throw new GetServiceException(LocaleMessageUtils.getMessage("effective_time_overlap"));
            }
        }
        utilService.setCreateInfo(workScheduleStaffConfig);
        workScheduleStaffConfigMapper.insert(workScheduleStaffConfig);
    }


    /**
     * 更新工作时间设置
     *
     * @Date 12:50 2022/11/7
     * <AUTHOR>
     */
    @Override
    public void updateWorkScheduleStaffConfig(WorkScheduleStaffConfigUpdateDto workScheduleDateConfigVo) {
        WorkScheduleStaffConfig workScheduleStaffConfig = BeanCopyUtils.objClone(workScheduleDateConfigVo, WorkScheduleStaffConfig::new);
        List<WorkScheduleStaffConfigVo> workScheduleStaffConfigVoList = workScheduleStaffConfigMapper.getWorkScheduleStaffConfigByStaffId(workScheduleDateConfigVo.getFkCompanyId(), workScheduleDateConfigVo.getFkStaffId());
        //校验特殊人员时间配置有效时间是否重叠
        if(GeneralTool.isNotEmpty(workScheduleStaffConfigVoList)){
            Date newEffectiveStartTime = workScheduleDateConfigVo.getEffectiveStartTime();
            Date newEffectiveEndTime = workScheduleDateConfigVo.getEffectiveEndTime();
            List<WorkScheduleStaffConfigVo> collect = workScheduleStaffConfigVoList.stream()
                    .filter(c -> !c.getId().equals(workScheduleDateConfigVo.getId()) && ((c.getEffectiveStartTime().compareTo(newEffectiveStartTime) < 0 && c.getEffectiveStartTime().compareTo(newEffectiveStartTime) > 0) ||
                            (c.getEffectiveStartTime().compareTo(newEffectiveEndTime) < 0 && c.getEffectiveEndTime().compareTo(newEffectiveEndTime) > 0)))
                    .collect(Collectors.toList());
            if(GeneralTool.isNotEmpty(collect)){
                throw new GetServiceException(LocaleMessageUtils.getMessage("effective_time_overlap"));
            }
        }

            utilService.setUpdateInfo(workScheduleStaffConfig);
            workScheduleStaffConfigMapper.updateByIdWithNull(workScheduleStaffConfig);
    }

    /**
     * 删除特殊人员工作时间
     *
     * @Date 15:23 2022/11/16
     * <AUTHOR>
     */
    @Override
    public void delete(Long id) {
        workScheduleStaffConfigMapper.deleteById(id);
    }

    @Override
    public Map<Long, List<WorkScheduleStaffConfig>> getWorkScheduleStaffConfigList(Long fkCompanyId){
        if(GeneralTool.isEmpty(fkCompanyId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<WorkScheduleStaffConfig> staffConfigList = workScheduleStaffConfigMapper.selectList(Wrappers.<WorkScheduleStaffConfig>lambdaQuery()
                .eq(WorkScheduleStaffConfig::getFkCompanyId,fkCompanyId));
        //校验特殊人员排班时间设定是否存在重复
         return staffConfigList.stream().collect(Collectors.groupingBy(WorkScheduleStaffConfig::getFkStaffId));
    }

    @Override
    public WorkScheduleStaffConfig getWorkScheduleStaffConfig(TimeConfigDto timeConfigDto){
        return workScheduleStaffConfigMapper.getWorkScheduleStaffConfig(timeConfigDto);
    }
}
