package com.get.officecenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.officecenter.vo.OfficeMediaAndAttachedVo;
import com.get.officecenter.entity.OfficeMediaAndAttached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MediaAndAttachedMapper extends BaseMapper<OfficeMediaAndAttached> {

    int insertSelective(OfficeMediaAndAttached record);

    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);

    List<OfficeMediaAndAttachedVo> getMediaAndAttachedList(@Param("fkTableId") Long fkTableId);
}