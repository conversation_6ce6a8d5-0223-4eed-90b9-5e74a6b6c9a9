package com.get.officecenter.config;

import com.get.officecenter.handler.ContactChangeHandler;
import com.get.officecenter.handler.EnterAgentHandler;
import com.get.officecenter.handler.LocationHandler;
import com.get.officecenter.handler.LogHandler;
import com.get.officecenter.handler.MenuHandler;
import com.get.officecenter.handler.MsgHandler;
import com.get.officecenter.handler.NullHandler;
import com.get.officecenter.handler.SubscribeHandler;
import com.get.officecenter.handler.UnsubscribeHandler;
import com.get.officecenter.service.impl.WxCpTpRedisConfigImpl;
import com.get.core.redis.cache.GetRedis;
import com.google.common.collect.Maps;
import lombok.val;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.cp.constant.WxCpConsts;
import me.chanjar.weixin.cp.tp.message.WxCpTpMessageRouter;
import me.chanjar.weixin.cp.tp.service.WxCpTpService;
import me.chanjar.weixin.cp.tp.service.impl.WxCpTpServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wang(https://github.com/binarywang)
 */
@Configuration
@EnableConfigurationProperties(WxCpTpProperties.class)
public class WxCpTpConfiguration {
    private LogHandler logHandler;
    private NullHandler nullHandler;
    private LocationHandler locationHandler;
    private MenuHandler menuHandler;
    private MsgHandler msgHandler;
    private UnsubscribeHandler unsubscribeHandler;
    private SubscribeHandler subscribeHandler;


    @Autowired
    private WxCpTpProperties properties;

    private static Map<String, WxCpTpMessageRouter> routers = Maps.newHashMap();

    private static Map<String, WxCpTpService> cpTpServices = Maps.newHashMap();

    @Resource
    private GetRedis getRedis;

    @Autowired
    public WxCpTpConfiguration(LogHandler logHandler, NullHandler nullHandler, LocationHandler locationHandler,
                               MenuHandler menuHandler, MsgHandler msgHandler, UnsubscribeHandler unsubscribeHandler,
                               SubscribeHandler subscribeHandler, WxCpTpProperties properties) {
        this.logHandler = logHandler;
        this.nullHandler = nullHandler;
        this.locationHandler = locationHandler;
        this.menuHandler = menuHandler;
        this.msgHandler = msgHandler;
        this.unsubscribeHandler = unsubscribeHandler;
        this.subscribeHandler = subscribeHandler;
        this.properties = properties;
    }


    public static Map<String, WxCpTpMessageRouter> getRouters() {
        return routers;
    }

    public static WxCpTpService getCpTpService(String suitId) {
        return cpTpServices.get(suitId);
    }

    @PostConstruct
    public void initServices() {

        cpTpServices = this.properties.getAppConfigs().stream().map(a -> {
            val configStorage = new WxCpTpRedisConfigImpl(getRedis);
            configStorage.setSuiteId(a.getSuiteId());
            configStorage.setAesKey(a.getAesKey());
            configStorage.setToken(a.getToken());
            configStorage.setSuiteSecret(a.getSecret());
            configStorage.setCorpId(properties.getCorpId());
            val service = new WxCpTpServiceImpl();
            service.setWxCpTpConfigStorage(configStorage);
            routers.put(a.getSuiteId(), this.newRouter(service));
            return service;
        }).collect(Collectors.toMap(service -> service.getWxCpTpConfigStorage().getSuiteId(), a -> a));
    }



    private WxCpTpMessageRouter newRouter(WxCpTpService wxCpTpService) {
        final val newRouter = new WxCpTpMessageRouter(wxCpTpService);

        // 记录所有事件的日志 （异步执行）
        newRouter.rule().handler(this.logHandler).next();

        // 自定义菜单事件
        newRouter.rule().async(false).msgType(WxConsts.XmlMsgType.EVENT)
            .event(WxConsts.MenuButtonType.CLICK).handler(this.menuHandler).end();

        // 点击菜单链接事件（这里使用了一个空的处理器，可以根据自己需要进行扩展）
        newRouter.rule().async(false).msgType(WxConsts.XmlMsgType.EVENT)
            .event(WxConsts.MenuButtonType.VIEW).handler(this.nullHandler).end();

        // 关注事件
        newRouter.rule().async(false).msgType(WxConsts.XmlMsgType.EVENT)
            .event(WxConsts.EventType.SUBSCRIBE).handler(this.subscribeHandler)
            .end();

        // 取消关注事件
        newRouter.rule().async(false).msgType(WxConsts.XmlMsgType.EVENT)
            .event(WxConsts.EventType.UNSUBSCRIBE)
            .handler(this.unsubscribeHandler).end();

        // 上报地理位置事件
        newRouter.rule().async(false).msgType(WxConsts.XmlMsgType.EVENT)
            .event(WxConsts.EventType.LOCATION).handler(this.locationHandler)
            .end();

        // 接收地理位置消息
        newRouter.rule().async(false).msgType(WxConsts.XmlMsgType.LOCATION)
            .handler(this.locationHandler).end();

        // 扫码事件（这里使用了一个空的处理器，可以根据自己需要进行扩展）
        newRouter.rule().async(false).msgType(WxConsts.XmlMsgType.EVENT)
            .event(WxConsts.EventType.SCAN).handler(this.nullHandler).end();

        newRouter.rule().async(false).msgType(WxConsts.XmlMsgType.EVENT)
            .event(WxCpConsts.EventType.CHANGE_CONTACT).handler(new ContactChangeHandler()).end();

        newRouter.rule().async(false).msgType(WxConsts.XmlMsgType.EVENT)
            .event(WxCpConsts.EventType.ENTER_AGENT).handler(new EnterAgentHandler()).end();

        // 默认
        newRouter.rule().async(false).handler(this.msgHandler).end();

        return newRouter;
    }
}
