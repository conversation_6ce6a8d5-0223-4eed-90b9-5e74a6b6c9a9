package com.get.officecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.officecenter.vo.WorkScheduleStaffConfigVo;
import com.get.officecenter.service.WorkScheduleStaffConfigService;
import com.get.officecenter.dto.WorkScheduleStaffConfigAddDto;
import com.get.officecenter.dto.WorkScheduleStaffConfigListDto;
import com.get.officecenter.dto.WorkScheduleStaffConfigUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/7 12:42
 */
@Api(tags = "特殊人员工作时间管理")
@RestController
@RequestMapping("office/workScheduleStaffConfig")
public class WorkScheduleStaffConfigController {

    @Resource
    private WorkScheduleStaffConfigService workScheduleStaffConfigService;

    /**
     * 查询特殊人员工作时间
     *
     * @Date 12:47 2022/11/7
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/特殊人员工作时间管理/查询特殊人员工作时间")
    @PostMapping("datas")
    public ResponseBo<WorkScheduleStaffConfigVo> datas(@RequestBody SearchBean<WorkScheduleStaffConfigListDto> page) {
        List<WorkScheduleStaffConfigVo> datas = workScheduleStaffConfigService.getWorkScheduleStaffConfigs(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 特殊人员工作时间详情
     *
     * @Date 12:54 2022/11/7
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DETAIL, description = "办公中心/特殊人员工作时间管理/特殊人员工作时间详情")
    @GetMapping("/{id}")
    public ResponseBo<WorkScheduleStaffConfigVo> detail(@PathVariable("id") Long id) {
        WorkScheduleStaffConfigVo data = workScheduleStaffConfigService.findWorkScheduleStaffConfigById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 新增特殊人员工作时间
     *
     * @Date 12:50 2022/11/7
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "办公中心/特殊人员工作时间管理/新增特殊人员工作时间")
    @PostMapping("add")
    public ResponseBo add(@RequestBody WorkScheduleStaffConfigAddDto workScheduleStaffConfigAddDto) {
        workScheduleStaffConfigService.add(workScheduleStaffConfigAddDto);
        return SaveResponseBo.ok();
    }

    /**
     * 更新特殊人员工作时间
     *
     * @Date 12:50 2022/11/7
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.EDIT, description = "办公中心/特殊人员工作时间管理/更新特殊人员工作时间")
    @PostMapping("update")
    public ResponseBo<WorkScheduleStaffConfigVo> update(@RequestBody @Validated(WorkScheduleStaffConfigUpdateDto.Update.class) WorkScheduleStaffConfigUpdateDto workScheduleStaffConfigUpdateDto) {
        workScheduleStaffConfigService.updateWorkScheduleStaffConfig(workScheduleStaffConfigUpdateDto);
        return UpdateResponseBo.ok();
    }

    /**
     * 删除特殊人员工作时间
     *
     * @Date 15:23 2022/11/16
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DELETE, description = "办公中心/特殊人员工作时间管理/删除特殊人员工作时间")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        workScheduleStaffConfigService.delete(id);
        return DeleteResponseBo.ok();
    }


}
