package com.get.officecenter.service;

import com.get.common.result.SearchBean;
import com.get.officecenter.vo.WorkScheduleTimeConfigVo;
import com.get.officecenter.entity.WorkScheduleTimeConfig;
import com.get.officecenter.dto.WorkScheduleTimeConfigAddDto;
import com.get.officecenter.dto.WorkScheduleTimeConfigListDto;
import com.get.officecenter.dto.WorkScheduleTimeConfigUpdateDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/10/27
 * @TIME: 17:50
 * @Description:
 **/
public interface WorkScheduleTimeConfigService {

    /**
     * 查询所有排班时间设定
     *
     * @return
     */
    List<WorkScheduleTimeConfig> getWorkScheduleTimeConfigList(Long fkCompanyId);

    /**
     * 查询工作时间设置
     *
     * @Date 12:53 2022/11/7
     * <AUTHOR>
     */
    List<WorkScheduleTimeConfigVo> getWorkScheduleTimeConfigs(WorkScheduleTimeConfigListDto data, SearchBean<WorkScheduleTimeConfigListDto> page);

    /**
     * 工作时间设置详情
     *
     * @Date 12:54 2022/11/7
     * <AUTHOR>
     */
    WorkScheduleTimeConfigVo findWorkScheduleTimeConfigById(Long id);

    /**
     * 新增工作时间设置
     *
     * @Date 12:50 2022/11/7
     * <AUTHOR>
     */
    void add(WorkScheduleTimeConfigAddDto workScheduleDateConfigVo);

    /**
     * 更新工作时间设置
     *
     * @Date 12:50 2022/11/7
     * <AUTHOR>
     */
    void updateWorkScheduleTimeConfig(WorkScheduleTimeConfigUpdateDto workScheduleDateConfigVo);

    /**
     * 获取当前登录人部门对应的工作时间配置
     * @return
     */
    WorkScheduleTimeConfigVo getWorkScheduleTimeConfigDto();

    /**
     * 删除工作时间设定
     *
     * @Date 12:04 2022/11/21
     * <AUTHOR>
     */
    void delete(Long id);
}
