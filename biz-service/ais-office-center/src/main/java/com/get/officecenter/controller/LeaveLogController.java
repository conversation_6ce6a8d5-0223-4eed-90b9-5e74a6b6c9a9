package com.get.officecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.officecenter.vo.LeaveLogVo;
import com.get.officecenter.vo.StaffOfLeaveLogVo;
import com.get.officecenter.service.LeaveLogService;
import com.get.officecenter.dto.LeaveLogDto;
import com.get.officecenter.dto.StaffOfLeaveLogDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2022/1/14
 * @TIME: 11:12
 * @Description:
 **/
@Api(tags = "工休日志")
@RestController
@RequestMapping("office/leaveLog")
public class LeaveLogController {
    @Resource
    private LeaveLogService leaveLogService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.DETAIL, description = "办公中心/工休日志管理/工休日志详情")
    @GetMapping("/{id}")
    public ResponseBo<LeaveLogVo> detail(@PathVariable("id") Long id) {
        LeaveLogVo data = leaveLogService.findLeaveLogById(id);
        return new ResponseBo<>(data);
    }


    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/工休日志管理/查询工休日志")
    @PostMapping("datas")
    public ResponseBo<LeaveLogVo> datas(@RequestBody SearchBean<LeaveLogDto> page) {
        List<LeaveLogVo> datas = leaveLogService.getLeaveLogs(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 操作类型下拉
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取操作类型下拉下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, description = "办公中心/工休日志管理/获取操作类型下拉下拉")
    @PostMapping("getOptTypeSelect")
    public ResponseBo<Map<String, Object>> getOptTypeSelect() {
        return new ListResponseBo<>(ProjectKeyEnum.enumsTranslation2Arrays(ProjectKeyEnum.OPT_TYPE));
    }


    /**
     * feign调用,添加工休日志
     *
     * @param leaveLogDto
     * @
     */
    @ApiIgnore
    @VerifyLogin(IsVerify = false)
    @PostMapping("addSystemLeaveLog")
    public void addSystemLeaveLog(@RequestBody LeaveLogDto leaveLogDto) {
        leaveLogService.addSystemLeaveLog(leaveLogDto);
    }


    /**
     * 列表数据
     *
     * @param leaveLogDto
     * @return
     * @
     */
    @ApiIgnore
    @PostMapping("getSystemLeaveLog")
    public List<LeaveLogVo> getSystemLeaveLog(@RequestBody LeaveLogDto leaveLogDto) {
        return leaveLogService.getSystemLeaveLog(leaveLogDto);
    }


    /**
     * 移动端休假记录员工列表
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "移动端休假记录员工列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.OFFICECENTER, type = LoggerOptTypeConst.LIST, description = "办公中心/工休日志管理/移动端休假记录员工列表")
    @PostMapping("getStaffOfLeaveLogList")
    public ResponseBo<StaffOfLeaveLogVo> getStaffOfLeaveLogList(@RequestBody SearchBean<StaffOfLeaveLogDto> page) {
        List<StaffOfLeaveLogVo> datas = leaveLogService.getStaffOfLeaveLogList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }
}
