package com.get.officecenter.service.impl;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.StyleSet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.file.utils.FileUtils;
import com.get.officecenter.dto.CommentDto;
import com.get.officecenter.dto.TaskItemDto;
import com.get.officecenter.entity.Comment;
import com.get.officecenter.entity.Task;
import com.get.officecenter.entity.TaskItem;
import com.get.officecenter.mapper.CommentMapper;
import com.get.officecenter.mapper.CustomTaskMapper;
import com.get.officecenter.mapper.TaskItemMapper;
import com.get.officecenter.service.AsyncExportService;
import com.get.officecenter.service.CommentService;
import com.get.officecenter.service.ITaskItemService;
import com.get.officecenter.vo.CommentStatisticsVo;
import com.get.officecenter.vo.CommentVo;
import com.get.officecenter.vo.TaskItemVo;
import com.get.officecenter.vo.TaskStatisticsExportVo;
import com.get.officecenter.vo.TaskStatisticsVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.google.common.collect.Sets;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 子任务Service实现类
 */
@Service
public class TaskItemServiceImpl extends ServiceImpl<TaskItemMapper, TaskItem> implements ITaskItemService {

    @Resource
    private TaskItemMapper taskItemMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private CustomTaskMapper customTaskMapper;
    @Resource
    private CommentService commentService;
    @Resource
    private CommentMapper commentMapper;

    @Resource
    @Lazy
    private AsyncExportService asyncExportService;


    @Override
    public List<TaskItem> getTaskItemList(List<Long> taskIds) {
        if (GeneralTool.isEmpty(taskIds)) {
            return Collections.emptyList();
        }
       // List<TaskItem> taskItems = taskItemMapper.selectList(Wrappers.<TaskItem>lambdaQuery().in(TaskItem::getFkTaskId, taskIds));
        List<TaskItem> taskItems = taskItemMapper.getTaskItemList(taskIds);
        if (GeneralTool.isEmpty(taskItems)) {
            return Collections.emptyList();
        }
        return taskItems;
    }


    public List<Long>   getFkStaffIdTo(List<Long> taskIds) {
        if (GeneralTool.isEmpty(taskIds)) {
            return Collections.emptyList();
        }
        List<Long> taskItems = taskItemMapper.getFkStaffIdTo(taskIds);
        if (GeneralTool.isEmpty(taskItems)) {
            return Collections.emptyList();
        }
        return taskItems;
    }

    @Override
    public List<Long> getStaffIdByStatus(List<Long> taskIds) {
        if (GeneralTool.isEmpty(taskIds)) {
            return Collections.emptyList();
        }
        List<Long> taskItems = taskItemMapper.getStaffIdByStatus(taskIds);
        if (GeneralTool.isEmpty(taskItems)) {
            return Collections.emptyList();
        }
        return taskItems;
    }


    @Override
    public List<TaskStatisticsVo> getPersonalTaskStatistics(TaskItemDto taskItemDto,Page page ) {
        if (GeneralTool.isEmpty(taskItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(taskItemDto.getIsMyTask())) {
            taskItemDto.setIsMyTask(false);
        }
        if (GeneralTool.isEmpty(taskItemDto.getIsSubordinateTask())) {
            taskItemDto.setIsSubordinateTask(false);
        }
        //获取当前登录人ID
        Long curStaffId = SecureUtil.getStaffId();
        Set<Long> staffIds  = Sets.newHashSet();

        // 获取主任务
        Task task = customTaskMapper.selectById(taskItemDto.getFkTaskId());
        if (GeneralTool.isEmpty(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        //获取任务查看人员
        Set<Long> fkStaffIdsViewSet = new HashSet<>();
        //获取查看人员
        String fkStaffIdsView = task.getFkStaffIdsView();
        if (GeneralTool.isNotEmpty(fkStaffIdsView)) {
            fkStaffIdsViewSet  = Arrays.stream(fkStaffIdsView.split(","))
                    .map(String::trim)  // 去除前后空格
                    .filter(s -> !s.isEmpty())  // 过滤空字符串
                    .map(Long::valueOf)  // 转换为Long
                    .collect(Collectors.toSet());
        }

        //获取查看人员所属业务国家
        Set<Long> fkAreaCountryIds = new HashSet<>();
        //如果登录包含在查看人员里面
        if (GeneralTool.isNotEmpty(fkStaffIdsViewSet)&&fkStaffIdsViewSet.contains(curStaffId)) {
            fkAreaCountryIds = permissionCenterClient.getStaffAreaCountryIdsByfkStaffId(curStaffId).getData();
        }

        // 获取下属人员Ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(curStaffId).getData();


        // 没选择【我的任务】【下属任务】
        if (!taskItemDto.getIsMyTask() && !taskItemDto.getIsSubordinateTask()) {
            // 如果登录人是该主任务的委派人，则他能看到到该主任务的所有子任务
            if (curStaffId.equals(task.getFkStaffIdFrom())) {
                // 清除过滤条件就能看到所有子任务
                staffIds.clear();
            } else {
                staffIds.add(curStaffId);
                staffIds.addAll(staffFollowerIds);
            }
        }



        //如果是查看人的话，只显示查看人是自己业务国家的子任务数据
        if(GeneralTool.isNotEmpty(fkAreaCountryIds)){
            staffIds.clear();
        }

        // 选择【我的任务】
        if (taskItemDto.getIsMyTask()) {
            staffIds.add(curStaffId);
            fkAreaCountryIds.clear();
        }
        // 选择【下属任务】
        if (taskItemDto.getIsSubordinateTask()) {
            staffIds.addAll(staffFollowerIds);
            if (GeneralTool.isEmpty(staffFollowerIds)) {
                // 当登录人没有下属时，给一个不存在的默认值
                staffIds.add(-100L);
            }
            fkAreaCountryIds.clear();
        }


        IPage<TaskStatisticsVo> iPage = null;
        if(GeneralTool.isNotEmpty(page)){
            iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        }
        if(GeneralTool.isEmpty(page)){
            iPage = null;
        }
        List<TaskStatisticsVo> taskStatistics = null;
        if(GeneralTool.isNotEmpty(iPage)){
            taskStatistics = taskItemMapper.getPersonalTaskStatistics(iPage,taskItemDto, staffIds, fkAreaCountryIds);
        }else {
            taskStatistics = taskItemMapper.getPersonalTaskStatistics(null,taskItemDto, staffIds, fkAreaCountryIds);
        }

        if(GeneralTool.isNotEmpty(iPage)){
            page.setAll((int) iPage.getTotal());
        }

        return taskStatistics;
    }

    @Override
    public void exportTaskStatistics(HttpServletResponse response, TaskItemDto taskItemDto) {
        // 准备要导出的数据
        List<TaskStatisticsVo> dataList = getPersonalTaskStatistics(taskItemDto,null);
        if (dataList == null || dataList.isEmpty()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        List<TaskStatisticsExportVo> exportTaskStatisticsVoList = new ArrayList<>();
        for (TaskStatisticsVo data:dataList){
            TaskStatisticsExportVo exportVo= BeanCopyUtils.objClone(data, TaskStatisticsExportVo::new);
            exportTaskStatisticsVoList.add(exportVo);
        }
        FileUtils.exportExcelNotWrapText(response, exportTaskStatisticsVoList, "exportTaskStatistics", TaskStatisticsExportVo.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseBo batchSetup(Integer type, Set<Long> taskItemIds) {
        List<TaskItem> taskItems = taskItemMapper.getTaskItemListByTaskItemIds(taskItemIds);
        Long taskId = null;
        if (GeneralTool.isNotEmpty(taskItems)) {
            taskId = taskItems.get(0).getFkTaskId();
        }
        if(GeneralTool.isEmpty(taskItems)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        if (isLastTodoTaskItem(taskId)) {
            Task task = customTaskMapper.selectById(taskId);
            if (GeneralTool.isEmpty(task)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
            }
           task.setStatus(type);
            task.setStatusModifiedTime(new Date());
            utilService.setUpdateInfo(task);
            customTaskMapper.updateById(task);
        }
        Set<Long> tableIds = new HashSet<>();
        tableIds = taskItems.stream().map(TaskItem::getFkTableId).collect(Collectors.toSet());
        if(GeneralTool.isNotEmpty(tableIds)){
            // 设置每批处理的大小
            int batchSize = 1000;
            List<Long> tableIdsList = new ArrayList<>(tableIds); // 如果tableIds不是List

            // 分批处理
            for (int i = 0; i < tableIdsList.size(); i += batchSize) {
                int end = Math.min(i + batchSize, tableIdsList.size());
                List<Long> batchIds = tableIdsList.subList(i, end);

                // 查询当前批次的TaskItem
                List<TaskItem> batchItems = taskItemMapper.selectList(
                        Wrappers.<TaskItem>lambdaQuery().in(TaskItem::getFkTableId, batchIds).eq(TaskItem::getFkTaskId, taskId));
                // 更新当前批次的TaskItem
                for (TaskItem item : batchItems) {
                    item.setStatus(type);
                    item.setStatusModifiedTime(new Date());
                    utilService.setUpdateInfo(item);
                    taskItemMapper.updateById(item);
                }
            }
        }
        return ResponseBo.ok();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaveResponseBo batchAddTaskItemComment(Set<Long> taskItemIds, String comment, Integer status) {
        Long id = -1L;
        if (GeneralTool.isEmpty(taskItemIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<TaskItem> taskItems = taskItemMapper.getTaskItemListByTaskItemIds(taskItemIds);
        if (GeneralTool.isEmpty(taskItems)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        Long taskId = taskItems.get(0).getFkTaskId();
        // 设置批量处理的大小
        int batchSize = 1000; // 根据数据库性能调整
        List<CommentDto> commentBatch = new ArrayList<>(batchSize);
        Set<Long> taskItemBatch = new HashSet<>(batchSize);
        String gmtModifiedUser = null;
        for (TaskItem taskItem : taskItems) {
            // 1. 准备评论数据
            CommentDto commentDto = new CommentDto();
            commentDto.setFkTableName(TableEnum.M_TASK_ITEM.key);
            commentDto.setFkTableId(taskItem.getId());
            commentDto.setComment(comment);
            commentBatch.add(commentDto);
            taskItem.setStatus(status);
            // 2. 准备更新数据
            utilService.setUpdateInfo(taskItem);
            taskItemBatch.add(taskItem.getFkTableId());
            gmtModifiedUser = taskItem.getGmtModifiedUser();
            // 3. 批量处理
            if (commentBatch.size() >= batchSize) {
                commentService.addCommentBatch(commentBatch); // 需要实现批量插入

                taskItemMapper.updateBatchById(taskItemBatch,status,gmtModifiedUser,taskId); // MyBatis-Plus的批量更新
                commentBatch.clear();
                taskItemBatch.clear();
            }
        }
        // 处理剩余数据
        if (!commentBatch.isEmpty()) {
            commentService.addCommentBatch(commentBatch);
          Integer  num = taskItemMapper.updateBatchById(taskItemBatch,status,gmtModifiedUser,taskId);
          id = Long.valueOf(num);
        }
//        for(TaskItem taskItem : taskItems){
//            CommentDto commentDto = new CommentDto();
//            commentDto.setFkTableName(TableEnum.M_TASK_ITEM.key);
//            commentDto.setFkTableId(taskItem.getId());
//            commentDto.setComment(comment);
//            commentService.addComment(commentDto);
//            utilService.setUpdateInfo(taskItem);
//            taskItemMapper.updateById(taskItem);
//            id = 1L;
//        }

        return SaveResponseBo.ok(id);
    }



    @Override
    public List<CommentStatisticsVo> getTaskFeedbackStatistics(Long taskId,String typeKey) {

        // 获取当前登录人ID
        Long curStaffId = SecureUtil.getStaffId();
        Set<Long> staffIds = Sets.newHashSet();

        // 获取主任务
        Task task = customTaskMapper.selectById(taskId);
        if (GeneralTool.isEmpty(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }

        // 获取任务查看人员
        Set<Long> fkStaffIdsViewSet = new HashSet<>();
        String fkStaffIdsView = task.getFkStaffIdsView();
        if (GeneralTool.isNotEmpty(fkStaffIdsView)) {
            fkStaffIdsViewSet = Arrays.stream(fkStaffIdsView.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::valueOf)
                    .collect(Collectors.toSet());
        }

        // 获取查看人员所属业务国家
        Set<Long> fkAreaCountryIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(fkStaffIdsViewSet) && fkStaffIdsViewSet.contains(curStaffId)) {
            fkAreaCountryIds = permissionCenterClient.getStaffAreaCountryIdsByfkStaffId(curStaffId).getData();
        }

        // 获取下属人员Ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(curStaffId).getData();

        // 设置可见范围
        if (curStaffId.equals(task.getFkStaffIdFrom())) {
            // 委派人可以看到所有子任务
            staffIds.clear();
        } else if (GeneralTool.isNotEmpty(fkAreaCountryIds)) {
            // 查看人且有限制业务国家
            staffIds.clear();
        } else {
            // 只能看到自己和自己下属的任务
            staffIds.add(curStaffId);
            staffIds.addAll(staffFollowerIds);
        }


        // 1. 获取所有接收人（基础人员信息）
        List<CommentVo> taskItemStaffList = taskItemMapper.getTaskAllStaff(
                taskId, staffIds, fkAreaCountryIds, typeKey);

// 2. 转换为CommentStatisticsVo列表（初始化所有人）
        List<CommentStatisticsVo> result = taskItemStaffList.stream()
                .filter(Objects::nonNull) // 过滤掉可能为null的元素
                .collect(Collectors.collectingAndThen(
                        Collectors.groupingBy(
                                comment -> comment != null ? comment.getStaffId() : null,
                                Collectors.toList()
                        ),
                        map -> map.entrySet().stream()
                                .map(entry -> {
                                    CommentStatisticsVo vo = new CommentStatisticsVo();
                                    vo.setStaffId(entry.getKey());

                                    // 安全获取staffName
                                    if (!entry.getValue().isEmpty() && entry.getValue().get(0) != null) {
                                        vo.setStaffName(entry.getValue().get(0).getStaffName());
                                    } else {
                                        vo.setStaffName(null); // 或者设置默认值如""
                                    }

                                    vo.setComments(new ArrayList<>());
                                    vo.setTotal(0);
                                    vo.setPending(0);
                                    vo.setFeedback(0);
                                    return vo;
                                })
                                .collect(Collectors.toList())
                ));
//        List<CommentStatisticsVo> result = taskItemStaffList.stream()
//                .collect(Collectors.collectingAndThen(
//                        Collectors.groupingBy(CommentVo::getStaffId),
//                        map -> map.entrySet().stream()
//                                .map(entry -> {
//                                    CommentStatisticsVo vo = new CommentStatisticsVo();
//                                    vo.setStaffId(entry.getKey());
//                                    vo.setStaffName(entry.getValue().get(0).getStaffName());
//                                    vo.setComments(new ArrayList<>()); // 初始化空列表
//                                    vo.setTotal(0);
//                                    vo.setPending(0);
//                                    vo.setFeedback(0);
//                                    return vo;
//                                })
//                                .collect(Collectors.toList())
//                ));

// 3. 转换为以staffId为key的Map便于查找
        Map<Long, CommentStatisticsVo> resultMap = result.stream()
                .collect(Collectors.toMap(
                        CommentStatisticsVo::getStaffId,
                        vo -> vo,
                        (existing, replacement) -> existing));

// 4. 获取有评论的子任务数据并合并到结果
        List<CommentVo> taskItemCommentList = taskItemMapper.getTaskFeedbackCountByStatus(
                taskId, staffIds, fkAreaCountryIds, typeKey, true);
        taskItemCommentList.forEach(comment -> {
            CommentStatisticsVo vo = resultMap.get(comment.getStaffId());
            if (vo != null) {
                vo.getComments().add(comment);
                vo.setTotal(vo.getTotal() + comment.getCommentCount());
            }
        });

// 5. 获取待反馈任务数量并更新
        taskItemMapper.getTasksPendingFeedback(taskId, staffIds, fkAreaCountryIds, typeKey)
                .forEach(t -> {
                    CommentStatisticsVo vo = resultMap.get(t.getStaffId());
                    if (vo != null) {
                        vo.setFeedback(vo.getFeedback() + 1);
                        vo.setTotal(vo.getTotal() + 1);
                    }
                });

// 6. 获取待解决任务数量并更新
        taskItemMapper.getCountUnfinishedTasks(taskId, staffIds, fkAreaCountryIds, typeKey)
                .forEach(t -> {
                    CommentStatisticsVo vo = resultMap.get(t.getStaffId());
                    if (vo != null) {
                        vo.setPending(vo.getPending() + 1);
                        vo.setTotal(vo.getTotal() + 1);
                    }
                });
        return result;
    }

    public CommentStatisticsVo getTaskFeedbackStatisticsTotal(Long taskId,String typeKey){
        // 获取当前登录人ID
        Long curStaffId = SecureUtil.getStaffId();
        Set<Long> staffIds = Sets.newHashSet();

        // 获取主任务
        Task task = customTaskMapper.selectById(taskId);
        if (GeneralTool.isEmpty(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }

        // 获取任务查看人员
        Set<Long> fkStaffIdsViewSet = new HashSet<>();
        String fkStaffIdsView = task.getFkStaffIdsView();
        if (GeneralTool.isNotEmpty(fkStaffIdsView)) {
            fkStaffIdsViewSet = Arrays.stream(fkStaffIdsView.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::valueOf)
                    .collect(Collectors.toSet());
        }

        // 获取查看人员所属业务国家
        Set<Long> fkAreaCountryIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(fkStaffIdsViewSet) && fkStaffIdsViewSet.contains(curStaffId)) {
            fkAreaCountryIds = permissionCenterClient.getStaffAreaCountryIdsByfkStaffId(curStaffId).getData();
        }

        // 获取下属人员Ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(curStaffId).getData();

        // 设置可见范围
        if (curStaffId.equals(task.getFkStaffIdFrom())) {
            // 委派人可以看到所有子任务
            staffIds.clear();
        } else if (GeneralTool.isNotEmpty(fkAreaCountryIds)) {
            // 查看人且有限制业务国家
            staffIds.clear();
        } else {
            // 只能看到自己和自己下属的任务
            staffIds.add(curStaffId);
            staffIds.addAll(staffFollowerIds);
        }
        //获取所有反馈
        List<String> taskJsonList = JSON.parseArray(task.getTaskJson(), String.class);;
        CommentStatisticsVo taskItemsVo = new CommentStatisticsVo();

        // 获取有评论的子任务列表
        List<CommentVo> taskItemCommentList = null;
        int pending = 0;
        int feedback = 0;
        int total = 0;
        if(GeneralTool.isNotEmpty(typeKey)){
            List<CommentStatisticsVo> taskItems = getTaskFeedbackStatistics(taskId,typeKey);
            taskItemCommentList = taskItemMapper.getTaskFeedbackCountByStatus(
                    taskId, staffIds, fkAreaCountryIds, typeKey, true);
            List<CommentVo> commentVos = new ArrayList<>();
            for(String taskJson:taskJsonList){
                CommentVo commentVo = new CommentVo();
                commentVo.setCommentCount(0);
                commentVo.setComment(taskJson);
                commentVos.add(commentVo);
            }
            taskItemsVo.setComments(commentVos);
            for(CommentVo commentVo:taskItemsVo.getComments()){
                commentVo.setCommentCount(taskItemCommentList.stream()
                        .filter(t -> t.getComment().equals(commentVo.getComment()))
                        .mapToInt(CommentVo::getCommentCount)
                        .sum());
            }
            //待解决
             pending = taskItems.stream()
                    .mapToInt(CommentStatisticsVo::getPending)
                    .sum();
             feedback = taskItems.stream()
                    .mapToInt(CommentStatisticsVo::getFeedback)
                    .sum();
             total = taskItems.stream()
                    .mapToInt(CommentStatisticsVo::getTotal)
                    .sum();
        }else {
            //获取所有反馈对应的数量
            taskItemCommentList = taskItemMapper.getTotalByItem(
                    taskId, staffIds, fkAreaCountryIds);
            List<CommentVo> commentVos = new ArrayList<>();
            for(String taskJson:taskJsonList){
                CommentVo commentVo = new CommentVo();
                commentVo.setCommentCount(0);
                commentVo.setComment(taskJson);
                commentVos.add(commentVo);
            }
            taskItemsVo.setComments(commentVos);
            for(CommentVo commentVo:taskItemsVo.getComments()){
                commentVo.setCommentCount(
                        taskItemCommentList.stream()
                                // 过滤非空元素
                                .filter(Objects::nonNull)
                                // 过滤非空comment
                                .filter(t -> t.getComment() != null)
                                // 安全比较
                                .filter(t -> t.getComment().equals(commentVo.getComment()))
                                // 处理可能的null commentCount
                                .mapToInt(t -> t.getCommentCount() != null ? t.getCommentCount() : 0)
                                .sum()
                );
            }
            //获取待反馈任务数量并更新
            int back  =taskItemMapper.getTasksFeedback(taskId, staffIds, fkAreaCountryIds);
            feedback = back;
            // 获取待解决任务数量
             int unFinished  =taskItemMapper.getCountUnfinish(taskId, staffIds, fkAreaCountryIds);
            pending = unFinished;
            int sum = 0;
            for(CommentVo commentVo:taskItemCommentList){
                if (GeneralTool.isNotEmpty(commentVo.getCommentCount())) {
                    sum+=commentVo.getCommentCount();
                }

            }
            total = feedback + pending+ sum;
        }
        taskItemsVo.setPending(pending);
        taskItemsVo.setFeedback(feedback);
        taskItemsVo.setTotal(total);
        taskItemsVo.setStaffName("总数");
        taskItemsVo.setStaffId(0L);
        return  taskItemsVo;
    }

    @Override
    public void exportTaskFeedbackStatistics(HttpServletResponse response, Long taskId,String typeKey) {
        //先设置标题
        List<String> titles = new ArrayList<>();
        titles.add("BD");
        // 获取主任务
        Task task = customTaskMapper.selectById(taskId);
        if (GeneralTool.isEmpty(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        List<String> taskJsonList = JSON.parseArray(task.getTaskJson(), String.class);;
        titles.addAll(taskJsonList);
        titles.add("没反馈");
        titles.add("待解决");
        titles.add("总数");
        //设置内容

        List<CommentStatisticsVo> taskFeedbackStatistics = getTaskFeedbackStatistics(task.getId(), typeKey);
        for(CommentStatisticsVo commentStatisticsVo:taskFeedbackStatistics){
            // 获取原始的comments数据
            List<CommentVo> originalComments = commentStatisticsVo.getComments();
            List<CommentVo> commentVos = new ArrayList<>();
            for(String taskJson:taskJsonList){
                CommentVo commentVo = new CommentVo();
                commentVo.setCommentCount(0);
                commentVo.setComment(taskJson);
                commentVos.add(commentVo);
            }


            // 用实际数据覆盖初始化值
            for (CommentVo original : originalComments) {
                for (CommentVo full : commentVos) {
                    if (full.getComment().equals(original.getComment())) {
                        full.setCommentCount(original.getCommentCount());
                        break;
                    }
                }
            }

            // 设置回统计对象
            commentStatisticsVo.setComments(commentVos);
        }
        if(GeneralTool.isNotEmpty(typeKey)){
            CommentStatisticsVo taskFeedbackStatisticsTotal = getTaskFeedbackStatisticsTotal(task.getId(), typeKey);
            taskFeedbackStatistics.add(taskFeedbackStatisticsTotal);
        }
        CommentStatisticsVo taskFeedbackStatisticsTotal = getTaskFeedbackStatisticsTotal(task.getId(), null);


        //第二个表格的标题
        List<String> titles2 = new ArrayList<>();
        titles2.add("反馈内容");
        titles2.add("数量");



        // 转换为适合导出的数据结构
        List<Map<String, Object>> exportData = new ArrayList<>();
        for (CommentStatisticsVo stat : taskFeedbackStatistics) {
            Map<String, Object> rowData = new LinkedHashMap<>();
            rowData.put("BD", stat.getStaffName());

            // 初始化所有动态列为0
            for (String title : taskJsonList) {
                rowData.put(title, 0);
            }

            // 填充实际数据
            for (CommentVo comment : stat.getComments()) {
                if (rowData.containsKey(comment.getComment())) {
                    rowData.put(comment.getComment(), comment.getCommentCount());
                }
            }

            // 添加固定列
            rowData.put("没反馈", stat.getFeedback());
            rowData.put("待解决", stat.getPending());
            rowData.put("总数", stat.getTotal());

            exportData.add(rowData);
        }

        try {
            // 设置响应头
            String fileName = URLEncoder.encode("任务反馈统计.xlsx", "UTF-8");
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
            response.setCharacterEncoding("UTF-8");

            // 1. 清理数据源
            exportData.removeIf(row -> row == null || row.isEmpty());

            // 2. 初始化 Writer（禁用分页）
            BigExcelWriter writer = (BigExcelWriter) ExcelUtil.getBigWriter(-1);
            writer.renameSheet("任务反馈统计");

            // 设置样式
            StyleSet styleSet = writer.getStyleSet();

            // 创建浅蓝色背景样式
            CellStyle lightBlueStyle = writer.getWorkbook().createCellStyle();
            lightBlueStyle.cloneStyleFrom(styleSet.getHeadCellStyle());
            ((XSSFCellStyle) lightBlueStyle).setFillForegroundColor(new XSSFColor(new byte[]{(byte)185, (byte)226, (byte)243}, null));
            lightBlueStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 表头样式
            CellStyle headStyle = styleSet.getHeadCellStyle();
            Font headFont = writer.createFont();
            headFont.setBold(true);
            headFont.setFontName("微软雅黑");
            headStyle.setFont(headFont);
            ((XSSFCellStyle) headStyle).setFillForegroundColor(new XSSFColor(new byte[]{(byte)185, (byte)226, (byte)243}, null));
            headStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 内容样式
            CellStyle contentStyle = styleSet.getCellStyle();
            contentStyle.setWrapText(false);

            // ========== 第一个表格 - 完全手动创建 ==========
            // 设置表头映射（保留这一部分）
            for (String title : titles) {
                writer.addHeaderAlias(title, title);
            }

// 获取sheet对象
            SXSSFSheet sheet = (SXSSFSheet) writer.getSheet();

// 完全手动创建所有行，不使用writer.write()
// 创建表头
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < titles.size(); i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(titles.get(i));
            }
            headerRow.setHeight((short)(40 * 40));

// 创建数据行（包括普通数据和汇总行）
            // 创建数据行（包括普通数据和汇总行）
            System.out.println("开始创建数据行，总数据数: " + exportData.size());
            for (int i = 0; i < exportData.size(); i++) {
                Map<String, Object> rowData = exportData.get(i);
                Row dataRow = sheet.createRow(i + 1); // +1 因为第0行是表头

                System.out.println("创建第" + (i+1) + "行数据: " + rowData);

                // 按标题顺序填充数据
                for (int j = 0; j < titles.size(); j++) {
                    String title = titles.get(j);
                    Object value = rowData.get(title);
                    Cell cell = dataRow.createCell(j);

                    if (value instanceof Number) {
                        cell.setCellValue(((Number) value).doubleValue());
                    } else {
                        cell.setCellValue(value != null ? value.toString() : "");
                    }
                }

                dataRow.setHeight((short)(20 * 20));
            }
            System.out.println("数据行创建完成");

// 应用样式
// 表头样式
            headerRow = sheet.getRow(0);
            if (headerRow != null) {
                for (int i = 0; i < titles.size(); i++) {
                    Cell cell = headerRow.getCell(i);
                    if (cell != null) {
                        cell.setCellStyle(headStyle);
                    }
                }
            }

// 汇总行样式（最后一行数据行）
            Row summaryRow = sheet.getRow(exportData.size()); // 汇总行是最后一行数据
            if (summaryRow != null) {
                for (int i = 0; i < titles.size(); i++) {
                    Cell cell = summaryRow.getCell(i);
                    if (cell != null) {
                        cell.setCellStyle(lightBlueStyle);
                    }
                }
            }

// ========== 第一个表格样式调整 ==========
// 设置内容居中对齐
            CellStyle centerStyle = writer.getWorkbook().createCellStyle();
            centerStyle.cloneStyleFrom(contentStyle);
            centerStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
            centerStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

// 应用居中样式到所有数据单元格（除了表头和汇总行）
            for (int rowIndex = 1; rowIndex < exportData.size(); rowIndex++) { // 从1开始，跳过表头
                Row dataRow = sheet.getRow(rowIndex);
                if (dataRow != null) {
                    for (int colIndex = 0; colIndex < titles.size(); colIndex++) {
                        Cell cell = dataRow.getCell(colIndex);
                        if (cell != null) {
                            cell.setCellStyle(centerStyle);
                        }
                    }
                }
            }

// 设置第一列（BD列）宽度更宽
            sheet.setColumnWidth(0, 25 * 256); // 设置为25个字符宽度

// 其他列自适应宽度
            for (int i = 1; i < titles.size(); i++) { // 从1开始，跳过第一列
                sheet.trackColumnForAutoSizing(i);
                sheet.autoSizeColumn(i);
            }


            // ========== 第二个表格 ==========
            // 计算第二个表格的起始列（第一个表格最后一列+2，留出间隔）
            int secondTableStartCol = titles.size() + 2;

// 第二个表格的标题
            List<String> secondTableTitles = Arrays.asList("反馈内容", "数量");

// 创建内容样式 - 左对齐（用于第一列）
            CellStyle leftAlignStyle = writer.getWorkbook().createCellStyle();
            Font contentFont = writer.createFont();
            contentFont.setFontName("微软雅黑");
            contentFont.setFontHeightInPoints((short)11);
            leftAlignStyle.setFont(contentFont);
            leftAlignStyle.setWrapText(true);
            leftAlignStyle.setAlignment(HorizontalAlignment.LEFT);
            leftAlignStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            leftAlignStyle.setBorderTop(BorderStyle.THIN);
            leftAlignStyle.setBorderBottom(BorderStyle.THIN);
            leftAlignStyle.setBorderLeft(BorderStyle.THIN);
            leftAlignStyle.setBorderRight(BorderStyle.THIN);

// 创建内容样式 - 右对齐（用于第二列数量）
            CellStyle rightAlignStyle = writer.getWorkbook().createCellStyle();
            rightAlignStyle.cloneStyleFrom(leftAlignStyle);
            rightAlignStyle.setAlignment(HorizontalAlignment.RIGHT); // 设置为右对齐

// 创建带边框的表头样式
            CellStyle borderedHeaderStyle = writer.getWorkbook().createCellStyle();
            borderedHeaderStyle.cloneStyleFrom(headStyle);
            borderedHeaderStyle.setBorderTop(BorderStyle.THIN);
            borderedHeaderStyle.setBorderBottom(BorderStyle.THIN);
            borderedHeaderStyle.setBorderLeft(BorderStyle.THIN);
            borderedHeaderStyle.setBorderRight(BorderStyle.THIN);

// 创建"总数"行样式（带背景色和边框）
            CellStyle totalRowStyle = writer.getWorkbook().createCellStyle();
            totalRowStyle.cloneStyleFrom(leftAlignStyle);
            ((XSSFCellStyle) totalRowStyle).setFillForegroundColor(new XSSFColor(new byte[]{(byte)185, (byte)226, (byte)243}, null));
            totalRowStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

// 创建第二个表格的表头（在第0行创建新的单元格，不覆盖已有单元格）
            Row secondHeaderRow = sheet.getRow(0);
            if (secondHeaderRow == null) {
                secondHeaderRow = sheet.createRow(0);
            }

// 在第二个表格的列位置创建表头单元格
            for (int i = 0; i < secondTableTitles.size(); i++) {
                // 检查这个单元格是否已经被第一个表格使用
                Cell existingCell = secondHeaderRow.getCell(secondTableStartCol + i);
                if (existingCell == null) {
                    Cell cell = secondHeaderRow.createCell(secondTableStartCol + i);
                    cell.setCellValue(secondTableTitles.get(i));
                    cell.setCellStyle(borderedHeaderStyle);
                }
                // 如果单元格已存在，可以选择是否覆盖样式
            }

// 写入第二个表格的数据（从第1行开始）
            int dataRowIndex = 1;
            for (CommentVo comment : taskFeedbackStatisticsTotal.getComments()) {
                Row dataRow = sheet.getRow(dataRowIndex);
                if (dataRow == null) {
                    dataRow = sheet.createRow(dataRowIndex);
                }

                // 反馈内容 - 左对齐
                Cell commentCell = dataRow.getCell(secondTableStartCol);
                if (commentCell == null) {
                    commentCell = dataRow.createCell(secondTableStartCol);
                }
                commentCell.setCellValue(comment.getComment());
                commentCell.setCellStyle(leftAlignStyle);

                // 数量 - 右对齐
                Cell countCell = dataRow.getCell(secondTableStartCol + 1);
                if (countCell == null) {
                    countCell = dataRow.createCell(secondTableStartCol + 1);
                }
                countCell.setCellValue(comment.getCommentCount());
                countCell.setCellStyle(rightAlignStyle);

                dataRow.setHeight((short)(30 * 20));
                dataRowIndex++;
            }

// 添加汇总信息
            Row feedbackRow = sheet.getRow(dataRowIndex);
            if (feedbackRow == null) {
                feedbackRow = sheet.createRow(dataRowIndex);
            }
            Cell feedbackLabelCell = feedbackRow.getCell(secondTableStartCol);
            if (feedbackLabelCell == null) {
                feedbackLabelCell = feedbackRow.createCell(secondTableStartCol);
            }
            feedbackLabelCell.setCellValue("没反馈");
            feedbackLabelCell.setCellStyle(leftAlignStyle);

            Cell feedbackValueCell = feedbackRow.getCell(secondTableStartCol + 1);
            if (feedbackValueCell == null) {
                feedbackValueCell = feedbackRow.createCell(secondTableStartCol + 1);
            }
            feedbackValueCell.setCellValue(taskFeedbackStatisticsTotal.getFeedback());
            feedbackValueCell.setCellStyle(rightAlignStyle);
            feedbackRow.setHeight((short)(30 * 20));

            dataRowIndex++;

            Row pendingRow = sheet.getRow(dataRowIndex);
            if (pendingRow == null) {
                pendingRow = sheet.createRow(dataRowIndex);
            }
            Cell pendingLabelCell = pendingRow.getCell(secondTableStartCol);
            if (pendingLabelCell == null) {
                pendingLabelCell = pendingRow.createCell(secondTableStartCol);
            }
            pendingLabelCell.setCellValue("待解决");
            pendingLabelCell.setCellStyle(leftAlignStyle);

            Cell pendingValueCell = pendingRow.getCell(secondTableStartCol + 1);
            if (pendingValueCell == null) {
                pendingValueCell = pendingRow.createCell(secondTableStartCol + 1);
            }
            pendingValueCell.setCellValue(taskFeedbackStatisticsTotal.getPending());
            pendingValueCell.setCellStyle(rightAlignStyle);
            pendingRow.setHeight((short)(30 * 20));

            dataRowIndex++;

// "总数"行
            Row totalRow = sheet.getRow(dataRowIndex);
            if (totalRow == null) {
                totalRow = sheet.createRow(dataRowIndex);
            }
            Cell totalLabelCell = totalRow.getCell(secondTableStartCol);
            if (totalLabelCell == null) {
                totalLabelCell = totalRow.createCell(secondTableStartCol);
            }
            totalLabelCell.setCellValue("总数");
            totalLabelCell.setCellStyle(totalRowStyle);

            CellStyle totalValueStyle = writer.getWorkbook().createCellStyle();
            totalValueStyle.cloneStyleFrom(totalRowStyle);
            totalValueStyle.setAlignment(HorizontalAlignment.RIGHT);

            Cell totalValueCell = totalRow.getCell(secondTableStartCol + 1);
            if (totalValueCell == null) {
                totalValueCell = totalRow.createCell(secondTableStartCol + 1);
            }
            totalValueCell.setCellValue(taskFeedbackStatisticsTotal.getTotal());
            totalValueCell.setCellStyle(totalValueStyle);
            totalRow.setHeight((short)(30 * 20));

// 调整第二个表格的列宽
            sheet.trackColumnForAutoSizing(secondTableStartCol);
            sheet.autoSizeColumn(secondTableStartCol);
            sheet.trackColumnForAutoSizing(secondTableStartCol + 1);
            sheet.autoSizeColumn(secondTableStartCol + 1);

// 设置最小列宽
            int minColWidth = 8 * 256;
            if (sheet.getColumnWidth(secondTableStartCol) < minColWidth) {
                sheet.setColumnWidth(secondTableStartCol, minColWidth);
            }
            if (sheet.getColumnWidth(secondTableStartCol + 1) < minColWidth) {
                sheet.setColumnWidth(secondTableStartCol + 1, minColWidth);
            }

            // 输出Excel
            ServletOutputStream out = response.getOutputStream();
            writer.flush(out, true);
            writer.close();
            out.flush();
        } catch (Exception e) {
            log.error("导出任务反馈统计数据异常", e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        }

    }

    @Override
    public TaskItem getTaskItem(Long taskItemId) {
        return taskItemMapper.selectById(taskItemId);
    }


    @Override
    public List<TaskItemVo> getTaskItems(TaskItemDto taskItemDto, Page page) {
        if (GeneralTool.isEmpty(taskItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(taskItemDto.getIsMyTask())) {
            taskItemDto.setIsMyTask(false);
        }
        if (GeneralTool.isEmpty(taskItemDto.getIsSubordinateTask())) {
            taskItemDto.setIsSubordinateTask(false);
        }
        if (GeneralTool.isNotEmpty(taskItemDto.getTaskRelatedKeyword())) {
            // 任务项关键字转小写
            taskItemDto.setTaskRelatedKeyword(taskItemDto.getTaskRelatedKeyword().toLowerCase());
        }
        if (GeneralTool.isNotEmpty(taskItemDto.getAgentName())){
            taskItemDto.setAgentName(taskItemDto.getAgentName().replace(" ", "").trim().toLowerCase());
        }

        //获取当前登录人ID
        Long curStaffId = SecureUtil.getStaffId();
        Set<Long> staffIds  = Sets.newHashSet();

        // 获取主任务
        Task task = customTaskMapper.selectById(taskItemDto.getFkTaskId());
        if (GeneralTool.isEmpty(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }

        //获取任务查看人员
        Set<Long> fkStaffIdsViewSet = new HashSet<>();
        //获取查看人员
        String fkStaffIdsView = task.getFkStaffIdsView();
        if (GeneralTool.isNotEmpty(fkStaffIdsView)) {
            fkStaffIdsViewSet  = Arrays.stream(fkStaffIdsView.split(","))
                    .map(String::trim)  // 去除前后空格
                    .filter(s -> !s.isEmpty())  // 过滤空字符串
                    .map(Long::valueOf)  // 转换为Long
                    .collect(Collectors.toSet());
        }

        //获取查看人员所属业务国家
        Set<Long> fkAreaCountryIds = new HashSet<>();
        //如果登录包含在查看人员里面
        if (GeneralTool.isNotEmpty(fkStaffIdsViewSet)&&fkStaffIdsViewSet.contains(curStaffId)) {
            fkAreaCountryIds = permissionCenterClient.getStaffAreaCountryIdsByfkStaffId(curStaffId).getData();
        }

        // 获取下属人员Ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(curStaffId).getData();
        // 没选择【我的任务】【下属任务】
        if (!taskItemDto.getIsMyTask() && !taskItemDto.getIsSubordinateTask()) {
            // 如果登录人是该主任务的委派人，则他能看到到该主任务的所有子任务
            if (curStaffId.equals(task.getFkStaffIdFrom())) {
                // 清除过滤条件就能看到所有子任务
                staffIds.clear();
            } else {
                staffIds.add(curStaffId);
                staffIds.addAll(staffFollowerIds);
            }
        }
        // 选择【我的任务】
        if (taskItemDto.getIsMyTask()) {
            staffIds.add(curStaffId);
            fkAreaCountryIds.clear();
        }
        // 选择【下属任务】
        if (taskItemDto.getIsSubordinateTask()) {
            staffIds.addAll(staffFollowerIds);
            if (GeneralTool.isEmpty(staffFollowerIds)) {
                // 当登录人没有下属时，给一个不存在的默认值
                staffIds.add(-100L);
            }
            fkAreaCountryIds.clear();
        }
        staffIds.removeIf(Objects::isNull);
        IPage<TaskItemVo> iPage = null;
        if(GeneralTool.isNotEmpty(page)){
            iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        }
        if(GeneralTool.isEmpty(page)){
            iPage = null;
        }

        //如果是查看人的话，只显示查看人是自己业务国家的子任务数据
        if(GeneralTool.isNotEmpty(fkAreaCountryIds)){
            staffIds.clear();
        }

        List<TaskItemVo> taskItemVoList = taskItemMapper.getTaskItems(iPage, taskItemDto, staffIds,fkAreaCountryIds,true);
        if(GeneralTool.isNotEmpty(iPage)){
            page.setAll((int) iPage.getTotal());
        }
        List<TaskItemVo> taskItemVoList2 = taskItemMapper.getTaskItems(null, taskItemDto, staffIds,null,false);
        if (taskItemVoList.isEmpty()) {
            return Collections.emptyList();
        }
        // 获取员工的名字
        Set<Long> toIds = taskItemVoList2.stream().map(TaskItemVo::getFkStaffIdTo).collect(Collectors.toSet());
        if (!taskItemDto.getIsMyTask() && !taskItemDto.getIsSubordinateTask()) {
            // 如果登录人是该主任务的委派人，则他能看到到该主任务的所有子任务，并且也会显示对应的权限按钮
            if (curStaffId.equals(task.getFkStaffIdFrom())) {
                // 清除过滤条件就能看到所有子任务
                staffIds.add(curStaffId);
                staffIds.addAll(staffFollowerIds);
            }
        }
        // 获取员工操作权限
        staffIds.add(curStaffId);
        staffIds.addAll(staffFollowerIds);
        //boolean isVisible = toIds.stream().anyMatch(staffIds::contains);


//        Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(toIds);
//        Map<Long, String> staffNameMap = new HashMap<>();
//        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//            staffNameMap = result.getData();
//        }



//        Map<Long, String> finalStaffNameMap = staffNameMap;
//        Map<Long, String> result1 = taskItemVoList2.stream()
//                .collect(Collectors.groupingBy(
//                        TaskItemVo::getOfferItemId,
//                        Collectors.collectingAndThen(
//                                Collectors.toList(),
//                                list -> {
//                                    // 拼接 RecipientStaffName
//                                    String receivers = list.stream()
//                                            .map(item -> finalStaffNameMap.get(item.getFkStaffIdTo()))
//                                            .filter(Objects::nonNull)
//                                            .distinct()
//                                            .collect(Collectors.joining(", "));
//
//                                    // 返回 Map.Entry<Long, String>（TaskItemVo.id, 拼接后的名字）
//                                    return new AbstractMap.SimpleEntry<>(
//                                            list.get(0).getFkTableId(), // 取第一个 TaskItemVo 的 id 作为 key
//                                            receivers            // 拼接后的名字作为 value
//                                    );
//                                }
//                        )
//                ))
//                .values() // 获取所有 Map.Entry<Long, String>
//                .stream()
//                .collect(Collectors.toMap(
//                        Map.Entry::getKey,   // TaskItemVo.id
//                        Map.Entry::getValue, // RecipientStaffName
//                        (oldVal, newVal) -> oldVal // 如果有重复 key，保留旧值（一般不会发生）
//                ));

        // 设置其他属性项
        // Step 1: 提取所有 fkTableId
        Set<Long> fkTableIds = taskItemVoList.stream()
                .map(TaskItemVo::getFkTableId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());


// Step 2: 一次性查询 TaskItem 列表
        List<TaskItem> taskItems = taskItemMapper.selectList(
                Wrappers.<TaskItem>lambdaQuery().in(TaskItem::getFkTableId, fkTableIds)
        );

// Step 3: 构建 fkTableId -> List<TaskItem>
        Map<Long, List<TaskItem>> taskItemMap = taskItems.stream()
                .collect(Collectors.groupingBy(TaskItem::getFkTableId));

// Step 4: 提取所有 taskItemId 用于查询 comment
        List<Long> allTaskItemIds = taskItems.stream()
                .map(TaskItem::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

// Step 5: 一次性查询 Comment 列表
//        List<Comment> commentList = new ArrayList<>();
//        if (!allTaskItemIds.isEmpty()) {
//            commentList = commentService.getCommentList(TableEnum.M_TASK_ITEM.key, allTaskItemIds);
//        }

// Step 6: 构建 fkTableId -> 最新 Comment 的映射
        Map<Long, String> lastCommentMap = new HashMap<>();

//        if (!commentList.isEmpty()) {
//            // 先按 fkTableId 分组
//            Map<Long, List<Comment>> commentGroupMap = commentList.stream()
//                    .collect(Collectors.groupingBy(Comment::getFkTableId));
//
//            for (Map.Entry<Long, List<Comment>> entry : commentGroupMap.entrySet()) {
//                Long fkTableId = entry.getKey();
//                List<Comment> comments = entry.getValue();
//
//                if (!comments.isEmpty()) {
//                    Comment maxComment = Collections.max(comments, Comparator.comparingLong(Comment::getId));
//                    lastCommentMap.put(fkTableId, maxComment.getComment());
//                }
//            }
//        }

// Step 7: 遍历 VO 并设置值（不再查询数据库）
        for (TaskItemVo taskItemVo : taskItemVoList) {
            Long fkTableId = taskItemVo.getFkTableId();
            Long fkStaffIdTo = taskItemVo.getFkStaffIdTo();


            // 设置接收人姓名（假设 staffNameMap 已经提前加载）
//            if (GeneralTool.isNotEmpty(fkStaffIdTo)) {
//                taskItemVo.setRecipientStaffName(staffNameMap.get(fkStaffIdTo));
//                taskItemVo.setRecipientStaffName(result1.get(fkTableId)); // 如果 result1 是 Map<Long, String>
//            }

            // 设置任务类型名称
            if (GeneralTool.isNotEmpty(taskItemVo.getFkTableName())) {
                taskItemVo.setTaskItemRelatedTypeName(TableEnum.getValue(taskItemVo.getFkTableName()));
            }

            // 设置最后一条评论
//            if (lastCommentMap.containsKey(fkTableId)) {
//                taskItemVo.setLastComment(lastCommentMap.get(fkTableId));
//            }

            // 设置任务类型
            if (curStaffId.equals(fkStaffIdTo)) {
                taskItemVo.setTaskItemType(2);
            } else if (staffFollowerIds.contains(fkStaffIdTo)) {
                taskItemVo.setTaskItemType(3);
            }
            //设置权限
            boolean isVisible = false;
            if (GeneralTool.isNotEmpty(taskItemVo.getFkStaffIdTos())) {

                String  fkStaffIdTos = taskItemVo.getFkStaffIdTos();
                // 分割字符串，如 "1,2" → ["1", "2"]
                String[] staffIdArray = fkStaffIdTos.split(",");
                // 检查是否有任意一个 ID 在 staffIds 中
                for (String staffId : staffIdArray) {
                    if (staffIds.contains(Long.valueOf(staffId.trim()))) {
                        isVisible = true;
                        break;  // 只要有一个匹配就可以停止检查
                    }
                }

            }
            taskItemVo.setIsVisible(isVisible);

        }
//        for (TaskItemVo taskItemVo : taskItemVoList) {
//            Long fkStaffIdTo = taskItemVo.getFkStaffIdTo();
//            if (GeneralTool.isNotEmpty(fkStaffIdTo)) {
//                //taskItemVo.setRecipientStaffName(staffNameMap.get(fkStaffIdTo));
//                taskItemVo.setRecipientStaffName(result1.get(taskItemVo.getFkTableId()));
//            }
//            if (GeneralTool.isNotEmpty(taskItemVo.getFkTableName())) {
//                taskItemVo.setTaskItemRelatedTypeName(TableEnum.getValue(taskItemVo.getFkTableName()));
//            }
//            List<TaskItem> taskItems = taskItemMapper.selectList(Wrappers.<TaskItem>lambdaQuery().eq(TaskItem::getFkTableId, taskItemVo.getFkTableId()));
//            // 获取子任务的评论数据
//            List<Long> taskItemIds = taskItems.stream().map(TaskItem::getId).collect(Collectors.toList());
//            List<Comment> commentList = commentService.getCommentList(TableEnum.M_TASK_ITEM.key, taskItemIds);
//            Map<Long, List<Comment>> commentMap = commentList.stream().collect(Collectors.groupingBy(Comment::getFkTableId));
//            //List<Comment> comments = commentMap.getOrDefault(taskItemVo.getId(), Collections.emptyList());
//            commentList.removeIf(Objects::isNull);
//            if (GeneralTool.isNotEmpty(commentList)) {
////                Comment maxComment = Collections.max(comments,
////                        Comparator.comparingLong(comment -> {
////                            // 防止空指针
////                            Long gmtCreate = Optional.ofNullable(comment.getGmtCreate()).map(Date::getTime).orElse(0L);
////                            Long gmtModified = Optional.ofNullable(comment.getGmtModified()).map(Date::getTime).orElse(0L);
////                            return Math.max(comment.getId());
////                        })
//                 //);
//                Comment maxComment = Collections.max(commentList, Comparator.comparingLong(Comment::getId));
//                taskItemVo.setLastComment(maxComment.getComment());
//            }
//            if (curStaffId.equals(fkStaffIdTo)) {
//                taskItemVo.setTaskItemType(2);
//            } else if (staffFollowerIds.contains(fkStaffIdTo)) {
//                taskItemVo.setTaskItemType(3);
//            }
//            taskItemVo.setIsVisible(isVisible);
//        }

        return taskItemVoList;
    }

    @Override
    public List<TaskItemVo> getTaskItemExportData(TaskItemDto taskItemDto,Long loginId,String locale) {
        if (GeneralTool.isEmpty(taskItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(taskItemDto.getIsMyTask())) {
            taskItemDto.setIsMyTask(false);
        }
        if (GeneralTool.isEmpty(taskItemDto.getIsSubordinateTask())) {
            taskItemDto.setIsSubordinateTask(false);
        }
        if (GeneralTool.isNotEmpty(taskItemDto.getTaskRelatedKeyword())) {
            // 任务项关键字转小写
            taskItemDto.setTaskRelatedKeyword(taskItemDto.getTaskRelatedKeyword().toLowerCase());
        }

        // 获取主任务
        Task task = customTaskMapper.selectById(taskItemDto.getFkTaskId());
        if (GeneralTool.isEmpty(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }

        Long curStaffId = loginId;
        Set<Long> staffIds  = Sets.newHashSet();


        //获取任务查看人员
        Set<Long> fkStaffIdsViewSet = new HashSet<>();
        //获取查看人员
        String fkStaffIdsView = task.getFkStaffIdsView();
        if (GeneralTool.isNotEmpty(fkStaffIdsView)) {
            fkStaffIdsViewSet  = Arrays.stream(fkStaffIdsView.split(","))
                    .map(String::trim)  // 去除前后空格
                    .filter(s -> !s.isEmpty())  // 过滤空字符串
                    .map(Long::valueOf)  // 转换为Long
                    .collect(Collectors.toSet());
        }

        //获取查看人员所属业务国家
        Set<Long> fkAreaCountryIds = new HashSet<>();
        //如果登录包含在查看人员里面
        if (GeneralTool.isNotEmpty(fkStaffIdsViewSet)&&fkStaffIdsViewSet.contains(curStaffId)) {
            fkAreaCountryIds = permissionCenterClient.getStaffAreaCountryIdsByfkStaffId(curStaffId).getData();
        }


        // 获取下属人员Ids
        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(curStaffId).getData();
        // 没选择【我的任务】【下属任务】
        if (!taskItemDto.getIsMyTask() && !taskItemDto.getIsSubordinateTask()) {
            // 如果登录人是该主任务的委派人，则他能看到到该主任务的所有子任务
            if (curStaffId.equals(task.getFkStaffIdFrom())) {
                // 清除过滤条件就能看到所有子任务
                staffIds.clear();
            } else {
                staffIds.add(curStaffId);
                staffIds.addAll(staffFollowerIds);
            }
        }
        // 选择【我的任务】
        if (taskItemDto.getIsMyTask()) {
            staffIds.add(curStaffId);
        }
        // 选择【下属任务】
        if (taskItemDto.getIsSubordinateTask()) {
            staffIds.addAll(staffFollowerIds);
            if (GeneralTool.isEmpty(staffFollowerIds)) {
                // 当登录人没有下属时，给一个不存在的默认值
                staffIds.add(-100L);
            }
        }
        staffIds.removeIf(Objects::isNull);

        List<TaskItemVo> taskItemVoList =null;
       taskItemVoList = taskItemMapper.getTaskItems(null, taskItemDto, staffIds,fkAreaCountryIds,true);

        // 获取员工的名字
        for (TaskItemVo taskItemVo : taskItemVoList) {
            Long fkStaffIdTo = taskItemVo.getFkStaffIdTo();

            // 设置任务类型名称
            if (GeneralTool.isNotEmpty(taskItemVo.getFkTableName())) {
                taskItemVo.setTaskItemRelatedTypeName(TableEnum.getValue(taskItemVo.getFkTableName(),  locale));
            }

            // 设置任务类型
            if (curStaffId.equals(fkStaffIdTo)) {
                taskItemVo.setTaskItemType(2);
            } else if (staffFollowerIds.contains(fkStaffIdTo)) {
                taskItemVo.setTaskItemType(3);
            }
        }
        return taskItemVoList;
    }

    /**
     * 判断是否为最后一个待解决状态的子任务
     *
     * @param taskId 主任务id
     * @return
     */
    private Boolean isLastTodoTaskItem(Long taskId) {
        Integer count = taskItemMapper.selectCount(Wrappers.<TaskItem>lambdaQuery().eq(TaskItem::getFkTaskId, taskId)
                .eq(TaskItem::getStatus, 0));
        return count <= 1;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public UpdateResponseBo setupFinish(Long taskItemId) {
        TaskItem taskItem = taskItemMapper.selectById(taskItemId);
        if (GeneralTool.isEmpty(taskItem)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }

        if (isLastTodoTaskItem(taskItem.getFkTaskId())) {
            Task task = customTaskMapper.selectById(taskItem.getFkTaskId());
            if (GeneralTool.isEmpty(task)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
            }
            task.setStatus(1);
            task.setStatusModifiedTime(new Date());
            utilService.setUpdateInfo(task);
            customTaskMapper.updateById(task);
        }

        List<TaskItem> taskItems = taskItemMapper.selectList(Wrappers.<TaskItem>lambdaQuery().eq(TaskItem::getFkTableId, taskItem.getFkTableId()).eq(TaskItem::getFkTaskId, taskItem.getFkTaskId()));
        if(GeneralTool.isNotEmpty(taskItems)){
            for (TaskItem item : taskItems){
                item.setStatus(1);
                item.setStatusModifiedTime(new Date());
                utilService.setUpdateInfo(item);
                taskItemMapper.updateById(item);
            }

        }
        return UpdateResponseBo.ok(taskItem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public UpdateResponseBo setupUnFinish(Long taskItemId) {
        TaskItem taskItem = taskItemMapper.selectById(taskItemId);
        if (GeneralTool.isEmpty(taskItem)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }

        if (isLastTodoTaskItem(taskItem.getFkTaskId())) {
            Task task = customTaskMapper.selectById(taskItem.getFkTaskId());
            if (GeneralTool.isEmpty(task)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
            }
            task.setStatus(1);
            task.setStatusModifiedTime(new Date());
            utilService.setUpdateInfo(task);
            customTaskMapper.updateById(task);
        }
        List<TaskItem> taskItems = taskItemMapper.selectList(Wrappers.<TaskItem>lambdaQuery().eq(TaskItem::getFkTableId, taskItem.getFkTableId()).eq(TaskItem::getFkTaskId, taskItem.getFkTaskId()));
        if(GeneralTool.isNotEmpty(taskItems)){
            for (TaskItem item : taskItems){
                item.setStatus(2);
                item.setStatusModifiedTime(new Date());
                utilService.setUpdateInfo(item);
                taskItemMapper.updateById(item);
            }
        }

        return UpdateResponseBo.ok(taskItem);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public SaveResponseBo addTaskItemComment(Long taskItemId, String comment,Integer status) {
        if (GeneralTool.isEmpty(taskItemId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        TaskItem taskItem = taskItemMapper.selectById(taskItemId);
        if (GeneralTool.isEmpty(taskItem)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        CommentDto commentDto = new CommentDto();
        commentDto.setFkTableName(TableEnum.M_TASK_ITEM.key);
        commentDto.setFkTableId(taskItemId);
        commentDto.setComment(comment);
        commentService.addComment(commentDto);

        LambdaQueryWrapper<TaskItem>queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TaskItem::getFkTableId,taskItem.getFkTableId());
        queryWrapper.eq(TaskItem::getFkTaskId,taskItem.getFkTaskId());
        List<TaskItem> taskItemList = taskItemMapper.selectList(queryWrapper);
        for(TaskItem taskItem1:taskItemList){
            taskItem1.setStatus(status);
            taskItem1.setStatusModifiedTime(new Date());
            utilService.setUpdateInfo(taskItem1);
            taskItemMapper.updateById(taskItem1);
        }
        return SaveResponseBo.ok(taskItemId);
    }

    @Override
    public ListResponseBo getTaskItemCommentList(Long taskItemId, Page page) {
        if (GeneralTool.isEmpty(taskItemId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        IPage<Comment> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        IPage<Comment> commentIPage = commentMapper.selectPage(iPage, Wrappers.<Comment>lambdaQuery()
                .eq(Comment::getFkTableName, TableEnum.M_TASK_ITEM.key)
                .eq(Comment::getFkTableId, taskItemId));
        page.setAll((int) commentIPage.getTotal());
        List<Comment> comments = commentIPage.getRecords();
        return new ListResponseBo<>(comments, BeanCopyUtils.objClone(page, Page::new));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public SaveResponseBo updateTaskItemComment(Long taskItemId, Long commentId, String comment) {
        if (Objects.isNull(taskItemId) || Objects.isNull(commentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        TaskItem taskItem = taskItemMapper.selectById(taskItemId);
        if (GeneralTool.isEmpty(taskItem)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_item_obj_null"));
        }
        CommentDto commentDto = new CommentDto();
        commentDto.setComment(comment);
        commentDto.setId(commentId);
        commentService.updateComment(commentDto);
        utilService.setUpdateInfo(taskItem);
        taskItemMapper.updateById(taskItem);
        return SaveResponseBo.ok(taskItemId);
    }

    @Override
    public void exportTaskItemList(HttpServletResponse response, TaskItemDto taskItemDto,Page page) {
        com.get.core.secure.UserInfo user = GetAuthInfo.getUser();
        String locale = SecureUtil.getLocale();
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        if (GeneralTool.isNotEmpty(taskItemDto.getFkTaskId())) {
            Task task = customTaskMapper.selectById(taskItemDto.getFkTaskId());
            if (GeneralTool.isEmpty(task)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
            }
            asyncExportService.exportTaskItemList(taskItemDto, user, locale,task,headerMap);
        }

    }



}
