<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.financecenter.dao.PaymentMethodTypeMapper">
    <select id="getPaymentMethodTypes" resultType="com.get.financecenter.vo.PaymentMethodTypeVo">
        SELECT
        pmt.id,
        pmt.type_name,
        pmt.fk_accounting_item_id,
        pmt.vouch_type,
        pmt.view_order,
        pmt.gmt_create,
        pmt.gmt_modified,
        pmt.gmt_create_user,
        pmt.gmt_modified_user
        FROM u_payment_method_type pmt
        WHERE 1=1
        <if test="paymentMethodTypeDto.fkAccountingItemId != null and paymentMethodTypeDto.fkAccountingItemId !=''">
            AND pmt.fk_accounting_item_id = #{paymentMethodTypeDto.fkAccountingItemId}
        </if>
<!--        <if test="paymentMethodTypeDto.typeName != null and paymentMethodTypeDto.typeName !=''">-->
<!--            AND pmt.type_name like concat('%',#{paymentMethodTypeDto.typeName},'%')-->
<!--        </if>-->
        <if test="paymentMethodTypeDto.keyWord != null and paymentMethodTypeDto.keyWord !=''">
            AND (pmt.type_name like concat('%',#{paymentMethodTypeDto.keyWord},'%')
            OR pmt.vouch_type like concat('%',#{paymentMethodTypeDto.keyWord},'%')
            OR pmt.gmt_create_user like concat('%',#{paymentMethodTypeDto.keyWord},'%')
            OR pmt.gmt_modified_user like concat('%',#{paymentMethodTypeDto.keyWord},'%'))
        </if>
        ORDER BY pmt.view_order DESC
    </select>


    <select id="checkName" resultType="java.lang.Integer">
        select count(*) from u_payment_method_type where type_name=#{typeName}
    </select>

    <select id="getMaxViewOrder" resultType="java.lang.Integer">
        SELECT
            IFNULL(max(view_order)+1,0) view_order
        FROM u_payment_method_type
    </select>

    <update id="updateBatchById">
        <foreach collection="updateList" item="item" index="index" separator=";">
            UPDATE u_payment_method_type
            SET
            <if test="item.viewOrder != null and item.viewOrder !=''">
                view_order = #{item.viewOrder},
            </if>
            gmt_modified = #{item.gmtModified},
            gmt_modified_user = #{item.gmtModifiedUser}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByFkAccountingItemId" resultType="com.get.financecenter.entity.PaymentMethodType">
        SELECT
            *
        FROM u_payment_method_type
        WHERE fk_accounting_item_id = #{fkAccountingItemId}
    </select>
</mapper>
