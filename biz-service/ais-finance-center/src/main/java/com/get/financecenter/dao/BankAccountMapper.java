package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.vo.BankAccountVo;
import com.get.financecenter.entity.BankAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface BankAccountMapper extends BaseMapper<BankAccount> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(BankAccount record);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param [companyId]
     * <AUTHOR>
     */
    Integer getMaxViewOrder(Long companyId);


    List<BankAccountVo> getBankAccountSelect(@Param("fkCompanyId") Long fkCompanyId);

    String getBankAccountNameById(Long id);

    /**
     * 根据id集合获取银行名称
     *
     * @param ids id集合
     * @return
     */
    List<BankAccountVo> getBankNameByIds(@Param("ids") Set<Long> ids);
}