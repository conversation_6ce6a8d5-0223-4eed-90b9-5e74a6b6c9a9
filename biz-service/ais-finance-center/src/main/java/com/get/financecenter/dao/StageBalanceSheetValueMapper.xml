<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.StageBalanceSheetValueMapper">

    <insert id="insertBatch">
        INSERT INTO ais_finance_center.m_stage_balance_sheet_value (`fk_company_id`, `year`, `month`, `fk_balance_sheet_type_id`, `fk_accounting_item_id`, `direction_value`, `amount_opening_balance`, `amount_dr`, `amount_cr`, `amount_closing_balance`, `is_sum`, `gmt_create`, `gmt_create_user`) VALUES
        <foreach collection="stageBalanceSheetValueList" item="item" separator=",">
            (#{item.fkCompanyId}, #{item.year}, #{item.month}, #{item.fkBalanceSheetTypeId}, #{item.fkAccountingItemId}, #{item.directionValue}, #{item.amountOpeningBalance}, #{item.amountDr}, #{item.amountCr}, #{item.amountClosingBalance}, #{item.isSum}, #{item.gmtCreate}, #{item.gmtCreateUser})
        </foreach>
    </insert>

    <select id="getProfitAndLossStatementTitle" resultType="com.get.financecenter.vo.ProfitAndLossStatementItemVo">
        SELECT
            msp.title, mcp.color_code
        FROM
            ais_finance_center.m_stage_profit_and_loss_value AS msp
        LEFT JOIN ais_finance_center.m_company_profit_and_loss_item AS mcp ON msp.fk_company_profit_and_loss_item_id = mcp.id
        where msp.fk_company_id = #{probAndLossStatementDto.companyId}
        AND msp.year = #{probAndLossStatementDto.year}
        GROUP BY msp.title
        ORDER BY msp.item_index asc, msp.id desc
    </select>
    <select id="getStageBalanceSheetValueList" resultType="com.get.financecenter.entity.StageBalanceSheetValue">
        SELECT
            msb.*
        FROM
            ais_finance_center.m_stage_balance_sheet_value AS msb
                INNER JOIN ais_finance_center.m_accounting_item AS mai ON mai.id = msb.fk_accounting_item_id
        WHERE
            msb.fk_company_id = #{companyId}
          AND msb.year = #{year}
          AND msb.month = #{month}
          AND mai.type = #{type}
    </select>

</mapper>