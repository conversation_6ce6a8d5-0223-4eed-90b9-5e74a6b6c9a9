<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.ReceiptFormMapper">

    <select id="getReceiptFormList" resultType="com.get.financecenter.vo.ReceiptFormVo">
        SELECT p.*,SUM(i.amount_receipt) AS amountReceipt, SUM(i.exchange_rate_receivable) AS exchangeRateReceivable, SUM(i.amount_receivable) AS amountReceivable,
               SUM(i.amount_exchange_rate) AS amountExchangeRate,SUM(i.service_fee) as receipt_fee
        from m_receipt_form p
                 left join m_receipt_form_item i on p.id =i.fk_receipt_form_id
        where i.fk_receivable_plan_id=#{planId}
        GROUP BY p.id
    </select>

    <select id="getReceiptFormListByBindingStatus" resultType="com.get.financecenter.vo.ReceiptFormVo">
        select a.* from (SELECT
        mrf.id,
        mrf.fk_company_id,
        mrf.fk_type_key,
        mrf.fk_type_target_id,
        mrf.amount_exchange_rate,
        mrf.fk_bank_account_id,
        mrf.fk_receipt_fee_type_id,
        mrf.fk_invoice_num,
        mrf.num_system,
        mrf.num_bank,
        mrf.fk_currency_type_num,
        mrf.exchange_rate,
        mrf.exchange_rate_hkd,
        mrf.amount_hkd,
        mrf.exchange_rate_rmb,
        mrf.amount_rmb,
        mrf.service_fee,
        mrf.summary,
        mrf.settlement_status,
        mrf.`status`,
        mrf.gmt_create,
        mrf.gmt_create_user,
        mrf.gmt_modified,
        mrf.gmt_modified_user,
        IFNULL(mrf.amount,0) + IFNULL(mrf.service_fee,0) AS amount,
        CAST(IFNULL(sum(mrfi.amount_receipt), 0) + IFNULL(sum(mrfi.service_fee), 0) AS DECIMAL(18,2)) AS amountReceipt,
        CAST(( IFNULL(mrf.amount,0) + IFNULL(mrf.service_fee,0) + IFNULL(mrf.amount_exchange_rate,0) ) AS DECIMAL (18, 2))  - CAST(( IFNULL(sum(mrfi.amount_receipt),0) + IFNULL(sum(mrfi.service_fee),0) + IFNULL(sum(mrfi.amount_exchange_rate),0) ) AS DECIMAL(18,2))  AS diffAmount
        FROM
        m_receipt_form mrf
        left join m_receipt_form_item mrfi on mrf.id = mrfi.fk_receipt_form_id
        group by mrf.id) a
        <where>
            <if test="bindingStatus != null and bindingStatus == 0">
                and a.amount = abs(a.diffAmount)  <!-- 未绑定：差额等于付款金额;abs函数将负数转为正数 -->
            </if>
            <if test="bindingStatus != null and bindingStatus == 1">
                and a.amount != abs(a.diffAmount) and a.diffAmount != 0       <!-- 绑定部分：差额小于0-->
            </if>
            <if test="bindingStatus != null and bindingStatus == 2">
                and a.diffAmount = 0        <!--#绑定完成：差额等于0-->
            </if>
            <if test="fkReceiptFormIds != null">
                and a.id in
                <foreach collection="fkReceiptFormIds" item="fkReceiptFormId" index="index" open="(" separator="," close=")">
                    #{fkReceiptFormId}
                </foreach>
            </if>
        </where>
        order by a.status desc,a.gmt_create desc
    </select>
    <select id="getReceiptFormListFeignByPlanIds" resultType="com.get.financecenter.vo.ReceiptFormVo">
        SELECT p.*,i.amount_receipt AS amountReceipt, i.exchange_rate_receivable AS exchangeRateReceivable, i.amount_receivable AS amountReceivable,
        i.amount_exchange_rate AS amountExchangeRate,i.fk_receivable_plan_id AS fkReceivablePlanId
        from m_receipt_form p
        left join m_receipt_form_item i on p.id =i.fk_receipt_form_id
        where i.fk_receivable_plan_id IN
        <foreach collection="planIds" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getReceiptFormCountByTypeId" resultType="java.lang.Integer">
        SELECT count(0) FROM m_receipt_form WHERE fk_receipt_fee_type_id=#{fkReceiptFeeTypeId}
    </select>


    <select id="checkForReceiptForm" resultType="java.lang.Boolean">
        SELECT
            IFNULL(MAX(msoi.id),0)
        FROM
            ais_finance_center.m_receipt_form_item AS mrfi
                INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.id = mrfi.fk_receivable_plan_id
                INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = mrp.fk_type_target_id
                AND mrp.fk_type_key = 'm_student_offer_item'
                AND mrp.STATUS = 1
                AND msoi.STATUS = 1
                AND msoi.status_settlement = 4
        WHERE msoi.num_settlement_batch = #{numSettlementBatch}
    </select>
    <select id="getReceiptForms" resultType="com.get.financecenter.entity.ReceiptForm">
        select a.*
        from m_receipt_form a
        LEFT JOIN (
        SELECT
        mrf.id,
        IFNULL(mrf.amount,0) + IFNULL(mrf.service_fee,0) AS amount,
        CAST(IFNULL(sum(mrfi.amount_receipt), 0) + IFNULL(sum(mrfi.service_fee), 0) AS DECIMAL(18,2)) AS amountReceipt,
        CAST(( IFNULL(mrf.amount,0) + IFNULL(mrf.service_fee,0) + IFNULL(mrf.amount_exchange_rate,0) ) AS DECIMAL (18, 2))  - CAST(( IFNULL(sum(mrfi.amount_receipt),0) + IFNULL(sum(mrfi.service_fee),0) + IFNULL(sum(mrfi.amount_exchange_rate),0) ) AS DECIMAL(18,2))  AS diffAmount
        FROM
        m_receipt_form mrf
        left join m_receipt_form_item mrfi on mrf.id = mrfi.fk_receipt_form_id
        group by mrf.id
        )b ON b.id = a.id
        <if test="receiptFormDto.isHideSupplementary != null and receiptFormDto.isHideSupplementary">
            inner join r_receipt_form_invoice i on i.fk_receipt_form_id = a.id and i.fk_invoice_id > 0
        </if>
        where 1=1
        <if test="receiptFormDto.fkCompanyId != null">
            and a.fk_company_id = #{receiptFormDto.fkCompanyId}
        </if>
        <if test="receiptFormDto.status != null">
            and a.status = #{receiptFormDto.status}
        </if>
        <if test="receiptFormDto.settlementStatus != null">
            and a.settlement_status = #{receiptFormDto.settlementStatus}
        </if>
        <if test="receiptFormDto.fkCompanyIds != null and receiptFormDto.fkCompanyIds.size()>0">
            and a.fk_company_id in
            <foreach collection="receiptFormDto.fkCompanyIds" item="fkCompanyId" index="index" open="(" separator=","
                     close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="receiptFormDto.bindingStatus != null and receiptFormDto.bindingStatus == 0">
            and b.amount = abs(b.diffAmount)  <!-- 未绑定：差额等于付款金额;abs函数将负数转为正数 -->
        </if>
        <if test="receiptFormDto.bindingStatus != null and receiptFormDto.bindingStatus == 1">
            and b.amount != abs(b.diffAmount) and b.diffAmount != 0       <!-- 绑定部分：差额小于0-->
        </if>
        <if test="receiptFormDto.bindingStatus != null and receiptFormDto.bindingStatus == 2">
            and b.diffAmount = 0        <!--#绑定完成：差额等于0-->
        </if>
        <if test="receiptFormDto.fkTypeKey != null and receiptFormDto.fkTypeKey != '' ">
            and a.fk_type_key = #{receiptFormDto.fkTypeKey}
        </if>
        <if test="receiptFormDto.fkTypeTargetId != null">
            and a.fk_type_target_id = #{receiptFormDto.fkTypeTargetId}
        </if>
        <if test="receiptFormDto.startTime != null and receiptFormDto.startTime != ''">
            and DATE_FORMAT( a.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{receiptFormDto.startTime},
            '%Y-%m-%d' )
        </if>
        <if test="receiptFormDto.endTime != null and receiptFormDto.endTime != ''">
            and DATE_FORMAT( a.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{receiptFormDto.endTime},
            '%Y-%m-%d' )
        </if>
        <if test="receiptFormDto.keyWord != null and receiptFormDto.keyWord != '' ">
            AND (a.num_system LIKE CONCAT('%', #{receiptFormDto.keyWord}, '%') OR a.num_bank LIKE CONCAT('%',
            #{receiptFormDto.keyWord}, '%'))
        </if>
        <if test="receiptFormDto.amountStart != null and receiptFormDto.amountStart != '' ">
            and a.amount + a.service_fee <![CDATA[>= ]]> #{receiptFormDto.amountStart}
        </if>
        <if test="receiptFormDto.amountEnd != null and receiptFormDto.amountEnd != '' ">
            and a.amount + a.service_fee <![CDATA[<= ]]> #{receiptFormDto.amountEnd}
        </if>
        <if test="receiptFormDto.fkInvoiceNum != null and receiptFormDto.fkInvoiceNum !=''">
            and EXISTS (
            SELECT
            1
            FROM
            ais_finance_center.r_receipt_form_invoice AS rfii
            INNER JOIN ais_finance_center.m_invoice AS mi ON mi.id = rfii.fk_invoice_id
            WHERE rfii.fk_receipt_form_id = a.id AND rfii.fk_invoice_id = mi.id
            AND mi.num LIKE CONCAT('%', #{receiptFormDto.fkInvoiceNum}, '%')
            )
        </if>

        <if test="receiptFormDto.startTime != null and receiptFormDto.startReceiptTime != ''">
            and DATE_FORMAT( a.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{receiptFormDto.startReceiptTime},
            '%Y-%m-%d' )
        </if>
        <if test="receiptFormDto.endTime != null and receiptFormDto.endReceiptTime != ''">
            and DATE_FORMAT( a.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{receiptFormDto.endReceiptTime},
            '%Y-%m-%d' )
        </if>

        <if test="receiptFormDto.fkBankAccountId !=null and receiptFormDto.fkBankAccountId !=''">
            and a.fk_bank_account_id = #{receiptFormDto.fkBankAccountId}
        </if>

        ORDER BY
        a.status DESC ,
        CASE
        WHEN a.num_bank = 'HKR20220425' THEN 0
        ELSE 1
        END DESC,
        a.gmt_create DESC
    </select>
    <select id="getReceiptFormsByTargetIds" resultType="com.get.financecenter.vo.ReceiptFormVo">
        SELECT mrp.fk_type_target_id AS fkReceivablePlanTargetId, mrf.*
        FROM
        ais_sale_center.m_receivable_plan AS mrp
        INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
        INNER JOIN ais_finance_center.m_receipt_form AS mrf ON mrf.id = mrfi.fk_receipt_form_id

        where mrf.status = 1
        AND mrp.fk_type_key = 'm_student_service_fee'
        AND mrp.fk_type_target_id in
        <foreach collection="feeIds" item="feeId" index="index" open="(" separator=","
                 close=")">
            #{feeId}
        </foreach>
        GROUP BY mrp.fk_type_target_id, mrf.id
    </select>
</mapper>