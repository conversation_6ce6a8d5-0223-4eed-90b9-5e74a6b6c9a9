<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.CostMapper">
    <select id="getCostByActivityData" resultType="com.get.financecenter.vo.CostVo">
        SELECT claim_type AS claimType,
               id,
               num,
               fk_staff_id,
               status,
               gmt_create,
               amount,
               fk_event_table_name,
               fk_event_table_id
        FROM (
            -- 费用报销部分
        SELECT 'EXPENSE' AS claim_type,
               ecfi.id,
               ecf.num,
               ecf.fk_currency_type_num,
               ecf.fk_staff_id,
               ecf.status,
               ecf.gmt_create,
               ecfi.amount,
               ecfi.fk_event_table_name,
               ecfi.fk_event_table_id
        FROM m_expense_claim_form ecf
                 JOIN
             m_expense_claim_form_item ecfi ON ecf.id = ecfi.fk_expense_claim_form_id
        WHERE 1 = 1
        <if test="searchActivityDataDto.fkEventTableName != null and searchActivityDataDto.fkEventTableName != ''">
            AND ecfi.fk_event_table_name = #{searchActivityDataDto.fkEventTableName}
        </if>
        <if test="searchActivityDataDto.fkEventTableId != null and searchActivityDataDto.fkEventTableId != ''">
            AND ecfi.fk_event_table_id = #{searchActivityDataDto.fkEventTableId}
        </if>
        <if test="statusList != null and statusList.size() > 0">
            AND ecf.status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>

        UNION ALL

        -- 差旅报销部分
        SELECT 'TRAVEL' AS claim_type,
               tcfi.id,
               tcf.num,
               tcf.fk_currency_type_num,
               tcf.fk_staff_id,
               tcf.status,
               tcf.gmt_create,
               tcfi.amount,
               tcfi.fk_event_table_name,
               tcfi.fk_event_table_id
        FROM m_travel_claim_form tcf
                 JOIN
             m_travel_claim_form_item tcfi ON tcf.id = tcfi.fk_travel_claim_form_id
        WHERE 1 = 1
        <if test="searchActivityDataDto.fkEventTableName != null and searchActivityDataDto.fkEventTableName != ''">
            AND tcfi.fk_event_table_name = #{searchActivityDataDto.fkEventTableName}
        </if>
        <if test="searchActivityDataDto.fkEventTableId != null and searchActivityDataDto.fkEventTableId != ''">
            AND tcfi.fk_event_table_id = #{searchActivityDataDto.fkEventTableId}
        </if>
        <if test="statusList != null and statusList.size() > 0">
            AND tcf.status IN
            <foreach collection="statusList" item="status" open="(" separator="," close=")">
                #{status}
            </foreach>
        </if>
        ) unified_claims
        ORDER BY gmt_create DESC
    </select>
</mapper>