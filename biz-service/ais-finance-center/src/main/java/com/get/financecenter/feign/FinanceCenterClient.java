package com.get.financecenter.feign;

import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dto.ExchangeRateDto;
import com.get.financecenter.dto.InsurancePaymentFormDto;
import com.get.financecenter.dto.InvoiceDto;
import com.get.financecenter.dto.InvoiceReceivablePlanDto;
import com.get.financecenter.dto.OneClickSettlementDto;
import com.get.financecenter.dto.ServiceFeePaymentFormDto;
import com.get.financecenter.dto.ServiceFeeReceiptFormDto;
import com.get.financecenter.dto.SettlementInstallmentBatchUpdateDto;
import com.get.financecenter.dto.SettlementInstallmentUpdateDto;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.entity.Invoice;
import com.get.financecenter.entity.InvoiceReceivablePlan;
import com.get.financecenter.entity.PaymentApplicationForm;
import com.get.financecenter.entity.PaymentForm;
import com.get.financecenter.entity.PrepayApplicationForm;
import com.get.financecenter.entity.TravelClaimForm;
import com.get.financecenter.service.AgencyCommissionSettlementService;
import com.get.financecenter.service.HtiAgencyCommissionSettlementService;
import com.get.financecenter.service.IBankAccountService;
import com.get.financecenter.service.ICommonService;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IExchangeRateService;
import com.get.financecenter.service.IExpenseClaimFormService;
import com.get.financecenter.service.IInvoiceReceivablePlanService;
import com.get.financecenter.service.IInvoiceService;
import com.get.financecenter.service.IPaymentApplicationFormService;
import com.get.financecenter.service.IPaymentFormItemService;
import com.get.financecenter.service.IPaymentFormService;
import com.get.financecenter.service.IPrepayApplicationFormService;
import com.get.financecenter.service.IReceiptFormInvoiceService;
import com.get.financecenter.service.IReceiptFormItemService;
import com.get.financecenter.service.IReceiptFormService;
import com.get.financecenter.service.ImportService;
import com.get.financecenter.service.TravelClaimFormService;
import com.get.financecenter.vo.AlreadyPayVo;
import com.get.financecenter.vo.CurrencyTypeVo;
import com.get.financecenter.vo.ExpenseClaimFormVo;
import com.get.financecenter.vo.FormInformationVo;
import com.get.financecenter.vo.InsurancePaymentFormVo;
import com.get.financecenter.vo.InvoiceVo;
import com.get.financecenter.vo.OccVo;
import com.get.financecenter.vo.PaymentApplicationFormVo;
import com.get.financecenter.vo.PaymentFormVo;
import com.get.financecenter.vo.PrepayApplicationFormVo;
import com.get.financecenter.vo.PrepaymentButtonHtiVo;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.financecenter.vo.ReceiptFormVo;
import com.get.financecenter.vo.TravelClaimFormVo;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.RestController;

@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class FinanceCenterClient implements IFinanceCenterClient {
    private final ICurrencyTypeService currencyTypeService;
    private final IExchangeRateService exchangeRateService;
    private final IPaymentFormService paymentFormService;
    private final IReceiptFormInvoiceService receiptFormInvoiceService;
    private final IPaymentFormItemService paymentFormItemService;
    private final IReceiptFormItemService receiptFormItemService;
    private final IPaymentApplicationFormService paymentApplicationFormService;
    private final IPrepayApplicationFormService iPrepayApplicationFormService;
    private final HtiAgencyCommissionSettlementService htiAgencyCommissionSettlementService;
    private ImportService importService;

    private final ICommonService commonService;
    private IReceiptFormService receiptFormService;
    private IInvoiceService invoiceService;
    private IInvoiceReceivablePlanService iInvoiceReceivablePlanService;
    private final IBankAccountService iBankAccountService;
    private final AgencyCommissionSettlementService agencyCommissionSettlementService;
    private final TravelClaimFormService travelClaimFormService;
    private final IExpenseClaimFormService expenseClaimFormService;

    @Override
    public Result<String> getCurrencyNameByNum(String fkCurrencyTypeNum) {
        return Result.data(currencyTypeService.getCurrencyNameByNum(fkCurrencyTypeNum));
    }

    @Override
    public List<AlreadyPayVo> getAlreadyPayByPlanIds(Set<Long> planIds) {
        return paymentFormItemService.getAlreadyPayByPlanIds(planIds);
    }

    @Override
    public Result<Map<String, String>> getCurrencyNamesByNums(Set<String> fkCurrencyTypeNums) {
        return Result.data(currencyTypeService.getCurrencyNamesByNums(fkCurrencyTypeNums));
    }

    @Override
    public Result<Map<String, String>> getNewCurrencyNamesByNums(Set<String> nums) {
        return Result.data(currencyTypeService.getNewCurrencyNamesByNums(nums));
    }

    @Override
    public Result<List<PaymentFormVo>> getPayFormList(Long planId) {
        List<PaymentFormVo> datas = paymentFormItemService.getPayFormList(planId);
        return Result.data(datas.stream().map(paymentFormDto -> BeanCopyUtils.objClone(paymentFormDto, PaymentFormVo::new)).collect(Collectors.toList()));
    }

    @Override
    public Result<List<ReceiptFormVo>> getReceiptFormList(Long planId) {
        List<ReceiptFormVo> receiptFormList = receiptFormItemService.getReceiptFormList(planId);
        return Result.data(receiptFormList.stream().map(receiptForm -> BeanCopyUtils.objClone(receiptForm, ReceiptFormVo::new)).collect(Collectors.toList()));
    }

    @Override
    public Result<BigDecimal> getExchangeRate(String fromCurrency, String toCurrency) {
        return Result.data(exchangeRateService.getRateByCurrency(fromCurrency, toCurrency));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> batchAddAuTo(List<ExchangeRateDto> exchangeRateDtos) {
        return Result.data(exchangeRateService.batchAddAuTo(exchangeRateDtos));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<BigDecimal> getLastExchangeRate(Boolean last, String fromCurrency, String toCurrency) {
        return Result.data(exchangeRateService.getLastExchangeRate(last, fromCurrency, toCurrency).getExchangeRate());
    }

    @Override
    public Result<PaymentApplicationFormVo> getMpayById(Long id) {
        return Result.data(paymentApplicationFormService.getMpayById(id));
    }

    @Override
    public Result<Boolean> updateMpay(PaymentApplicationForm payForm) {
        return Result.data(paymentApplicationFormService.updateMpay(payForm));
    }

    @Override
    public Result<Boolean> updateBorrowMoneyStatus(PrepayApplicationForm prepayApplicationForm) {
        return Result.data(iPrepayApplicationFormService.updateBorrowMoneyStatus(prepayApplicationForm));
    }

    @Override
    public Result<PrepayApplicationFormVo> getBorrowMoneyById(Long id) {
        return Result.data(iPrepayApplicationFormService.getBorrowMoneyById(id));
    }

    @Override
    public Result<Boolean> changeStatus(Integer status, String tableName, Long businessKey) {
        return Result.data(commonService.changeStatus(status, tableName, businessKey));
    }

    @Override
    public List<ReceiptFormVo> getReceiptFormListFeignByPlanIds(Set<Long> planIds) {
        return receiptFormItemService.getReceiptFormListFeignByPlanIds(planIds);
    }

    @Override
    public List<ReceiptFormItemVo> getReceiptFormItemListFeignByPlanIds(Set<Long> planIds) {
        return receiptFormItemService.getReceiptFormItemListFeignByPlanIds(planIds);
    }

    @Override
    public List<ReceiptFormItemVo> getReceiptFormItemListFeignByFormItemIds(Set<Long> receiptFormItemIds) {
        return receiptFormItemService.getReceiptFormItemListFeignByFormItemIds(receiptFormItemIds);
    }

    @Override
    public List<PaymentFormVo> getPayFormListFeignByPlanIds(Set<Long> planIds) {
        return paymentFormItemService.getPayFormListFeignByPlanIds(planIds);
    }

    @Override
    public ReceiptFormVo getReceiptFormByFormId(Long id) {
        return receiptFormService.getReceiptFormByFormId(id);
    }

    @Override
    public Map<Long, String> getFkInvoiceNum(Set<Long> planIds) {
        return invoiceService.getFkInvoiceNum(planIds);
    }


    @Override
    public Result<List<ReceiptFormVo>> getReceiptFormListFeign(Long planId) {
        List<ReceiptFormVo> receiptFormList = receiptFormItemService.getReceiptFormList(planId);
        return Result.data(receiptFormList.stream().map(receiptForm -> BeanCopyUtils.objClone(receiptForm, ReceiptFormVo::new)).collect(Collectors.toList()));
    }

    /**
     * feign 根据应付计划id获取应付折合金额 + 已生成佣金金额
     *
     * @param payablePlanId
     * @Date 15:47 2022/6/9
     * <AUTHOR>
     */
    @Override
    public BigDecimal getAmountPaidByPayablePlanId(Long payablePlanId) {
        return paymentFormItemService.getAmountPaidByPayablePlanId(payablePlanId);
    }

    @Override
    public List<ReceiptFormItemVo> getSettlementReceiptFormItemListByPlanId(Long receivablePlanId) {
        return receiptFormItemService.getSettlementReceiptFormItemListByPlanId(receivablePlanId);
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<BigDecimal> importReGea(List<OccVo> receivedOcList, Long receivablePlanId, Long targetId, String currency) {
        return importService.importReceivableData(receivedOcList,receivablePlanId,targetId,currency);
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public void importPayGea(List<OccVo> hiPaidInfoList, Long payablePlanId, String currency, Long agentId, Double payableAmount, Double receivableFee) {
        importService.importPayableData(hiPaidInfoList,payablePlanId,currency,agentId,BigDecimal.valueOf(payableAmount),receivableFee);
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public void importPaymentNewRecord(OccVo occVo, Long payablePlanId, String currency, Long agentId, Double payableAmount, Long backId) {
        importService.importPaymentNewRecord(occVo,payablePlanId,currency,agentId,BigDecimal.valueOf(payableAmount),backId);
    }

//    @Override
//    public Result isPayInAdvanceInsertSettlementInstallment(Long fkReceivablePlanId) {
//        return Result.data(receiptFormItemService.isPayInAdvanceInsertSettlementInstallment(fkReceivablePlanId));
//    }

    @Override
    public Result<Long> generateInvoice(InvoiceDto invoiceDto) {
        return Result.data(invoiceService.generateInvoice(invoiceDto));
    }

    @Override
    public Result generateInvoiceReceivablePlan(InvoiceReceivablePlanDto invoiceReceivablePlanDto) {
        invoiceService.generateInvoiceReceivablePlan(invoiceReceivablePlanDto);
        return Result.success("绑定成功");
    }

    @Override
    public Result<Boolean> isReceivablePlanBound(Long fkReceivablePlanId) {
        return Result.data(receiptFormItemService.isReceivablePlanBound(fkReceivablePlanId));
    }

    @Override
    public Result updateInvoice(InvoiceDto invoiceDto) {
        return Result.data(invoiceService.updateInvoice(invoiceDto));
    }

    @Override
    public Result<List<Invoice>> getInvoiceByNum(String invoiceNum) {
        return Result.data(invoiceService.getInvoiceByNum(invoiceNum));
    }

    @Override
    public List<Long> getReceivablePlanIdsFeign(Long invoiceId) {
        return invoiceService.doGetReceivablePlanIds(invoiceId);
    }

    @Override
    public void createInvoiceAndTargetMapping(String typeKey, Long invoiceId, Long targetId) {
        invoiceService.createInvoiceAndTargetMapping(typeKey,invoiceId,targetId);
    }

    @Override
    public void updateInvoiceAndTargetMapping(String typeKey, Long invoiceId, Long targetId) {
        invoiceService.updateInvoiceAndTargetMapping(typeKey,invoiceId,targetId);
    }

    @Override
    public Result<List<ReceiptFormItemVo>> getReceiptFormItemsByFormIds(Set<Long> ids) {
        return Result.data(receiptFormItemService.getReceiptFormItemsByFormIds(ids));
    }

    @Override
    public Result<List<InvoiceVo>> getInvoiceByNumList(Set<String> numSet) {
        return Result.data(invoiceService.getInvoiceByNumList(numSet));
    }

    @Override
    public Result<Integer> getMaxPoNum(String num) {
        return Result.data(invoiceService.getMaxPoNum(num));
    }

    @Override
    public Result<Invoice> isExistNum(Integer poNum, String invoiceNum) {
        return Result.data(invoiceService.isExistNum(poNum,invoiceNum));
    }

    @Override
    public void updateFee(Set<Long> payItemIds) {
        importService.updateFee(payItemIds);
    }

    @Override
    public Result<Integer> getPayFormItemCount(Set<Long> payPlanIds) {
        return Result.data(paymentFormItemService.getPayFormItemCount(payPlanIds));
    }

    @Override
    public Map<Long, String> getInvoiceNumByReceivableId(Set<Long> ids) {
        return invoiceService.getInvoiceNumByReceivableId(ids);
    }

    @Override
    public Result<Map<String, BigDecimal>> getLastExchangeRateHkd(Set<String> toCurrency) {
        return  Result.data(exchangeRateService.getLastExchangeRateHkd(toCurrency));
    }

    @Override
    public void receivableBindingInvoice(Long receivablePlanId, BigDecimal amount, Long invoiceId) {
        iInvoiceReceivablePlanService.receivableBindingInvoice(receivablePlanId,amount,invoiceId);
    }

    @Override
    public PaymentForm getPayFormById(Long paymentFormId) {
        return paymentFormService.getPayFormById(paymentFormId);
    }

    @Override
    public Map<Long, String> getInvoiceCommissionNotice(Long fkInvoiceId) {
        return iInvoiceReceivablePlanService.getInvoiceCommissionNotice(fkInvoiceId);
    }

    @Override
    public List<ReceiptFormItemVo> getReceiptFormByInvoiceId(Long fkInvoiceId) {
        return receiptFormItemService.getReceiptFormByInvoiceId(fkInvoiceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateReceiptAndPaymentFormStatus(Set<Long> paymentFormIds, Set<Long> receiptFormIds) {
        if(GeneralTool.isNotEmpty(paymentFormIds))
        {
            paymentFormService.updateStatus(paymentFormIds);
        }
        if(GeneralTool.isNotEmpty(receiptFormIds))
        {
            for(Long id: paymentFormIds)
            {
                receiptFormService.updateStatus(id);
            }
        }
        return true;
    }

    @Override
    public Boolean checkReceiptInvoiceMappingExist(Long invoiceId) {
        return receiptFormInvoiceService.checkReceiptInvoiceMappingExist(invoiceId);
    }

    @Override
    public Boolean unBindInvoiceByReceivablePlanIds(List<Long> receivablePlanIdset) {
        return invoiceService.unBindInvoiceByReceivablePlanIds(receivablePlanIdset);
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<List<CurrencyTypeVo>> getCurrencyByPublicLevel(Integer key) {
        return Result.data(currencyTypeService.getCurrencyByPublicLevel(key));
    }

    @Override
    public Result updateInvoiceReceivablePlan(Long invoiceId, Long receivablePlanId, BigDecimal amount) {
        return Result.data(iInvoiceReceivablePlanService.updateInvoiceReceivablePlan(invoiceId,receivablePlanId,amount));
    }

    @Override
    public Result<List<Long>> getInvoiceIdsByReceivablePlanId(Long receivablePlanId) {
        return Result.data(invoiceService.getInvoiceIdsByReceivablePlanId(receivablePlanId));
    }

    @Override
    public Result prepaymentButtonHti(List<PrepaymentButtonHtiVo> prepaymentButtonHtiDtoList) {
        return Result.data(iInvoiceReceivablePlanService.prepaymentButtonHti(prepaymentButtonHtiDtoList));
    }

    @Override
    public Result<Map<Long, InvoiceReceivablePlan>> getInvoiceAmountByIds(List<Long> fkInvoiceReceivablePlanIds) {
        return Result.data(iInvoiceReceivablePlanService.getInvoiceAmountByIds(fkInvoiceReceivablePlanIds));
    }

    @Override
    public Result updateInvoiceReceivablePlanAmount(Map<Long, BigDecimal> map) {
        return Result.data(iInvoiceReceivablePlanService.updateInvoiceReceivablePlanAmount(map));
    }

    @Override
    public Result<Map<Long, Integer>> getPaymentFormItemList(List<String> keys, Set<Long> studentIds) {
        return Result.data(paymentFormItemService.getPaymentFormItemList(keys, studentIds));
    }

    @Override
    public Result saveBatchReceiptForms(ServiceFeeReceiptFormDto serviceFeeReceiptFormDto) {
        return Result.data(receiptFormService.saveBatchReceiptForms(serviceFeeReceiptFormDto));
    }

    @Override
    public Result saveBatchPaymentForms(ServiceFeePaymentFormDto serviceFeePaymentFormDto) {
        return Result.data(paymentFormService.saveBatchPaymentForms(serviceFeePaymentFormDto));
    }

    @Override
    public Result<Map<String, BigDecimal>> getBatchLastExchangeRate(Set<String> fromCurrencySet, String toCurrency) {
        return Result.data(exchangeRateService.getLastExchangeRate(fromCurrencySet, toCurrency));
    }

    @Override
    public Result<Map<Long, String>> getBankNameByIds(Set<Long> ids) {
        return Result.data(iBankAccountService.getBankAccountNameByIds(ids));
    }

    @Override
    public Result<Map<Long, List<ReceiptFormVo>>> getReceiptFormsByTargetIds(String fkTypeKey, Set<Long> fkTypeTargetIds) {
        return Result.data(receiptFormService.getReceiptFormsByTargetIds(fkTypeKey, fkTypeTargetIds));
    }

    @Override
    public Result<Boolean> activateCommissionSettlementByReceivablePlanIds(Set<Long> receivablePlanIds) {
        return Result.data(receiptFormItemService.activateCommissionSettlementByReceivablePlanIds(receivablePlanIds));
    }

    @Override
    public Result<Boolean> activateCommissionSettlement(Set<Long> receiptFormItemIds) {
        return Result.data(receiptFormItemService.activateCommissionSettlement(receiptFormItemIds));
    }

    @Override
    public Result<InvoiceReceivablePlan> getInvoiceReceivablePlanById(Long fkInvoiceReceivablePlanId) {
        return Result.data(iInvoiceReceivablePlanService.getInvoiceReceivablePlanById(fkInvoiceReceivablePlanId));
    }

    @Override
    public Result<Map<Long, List<ReceiptFormVo>>> getReceiptFormsByFeeIds(List<Long> feeIds) {
        return Result.data(receiptFormService.getReceiptFormsByTargetIds(feeIds));
    }

    @Override
    public Result<Integer> getAccountCommissionSettlementStatus(Long accountId) {
        return Result.data(agencyCommissionSettlementService.getAccountCommissionSettlementStatus(accountId));
    }

    @Override
    public Result updateCommissionSettlementAccountCurrencyTypeNum(Long accountId, String fkCurrencyTypeNum) {
        return Result.data(agencyCommissionSettlementService.updateCommissionSettlementAccountCurrencyTypeNum(accountId, fkCurrencyTypeNum));
    }

    @Override
    public Result<Boolean> getCommissionSettlementAccountInfo(Long accountId) {
        return Result.data(agencyCommissionSettlementService.getCommissionSettlementAccountInfo(accountId));
    }

    @Override
    public Result<Boolean> checkExistAccountCommissionSettlement(Long agentAccountId) {
        return Result.data(htiAgencyCommissionSettlementService.getCommissionSettlementAccountInfo(agentAccountId));
    }

    @Override
    public Result<Boolean> settlementInstallmentUpdate(SettlementInstallmentUpdateDto settlementInstallmentUpdateDto) {
        return Result.data(htiAgencyCommissionSettlementService.settlementInstallmentUpdate(settlementInstallmentUpdateDto));
    }

    @Override
    public Result<Boolean> checkPayableInfo(Set<Long> payablePlanIds) {
        return Result.data(htiAgencyCommissionSettlementService.checkPayableInfo(payablePlanIds));
    }

    @Override
    public Result insertSettlementInstallmentBatch(List<SettlementInstallmentBatchUpdateDto> settlementInstallmentBatchUpdateDtos) {
        return Result.data(htiAgencyCommissionSettlementService.insertSettlementInstallmentBatch(settlementInstallmentBatchUpdateDtos));
    }

    @Override
    public Result<Boolean> deleteSettlementCommissionByPayablePlanId(Long payablePlanId) {
        return Result.data(htiAgencyCommissionSettlementService.deleteSettlementCommissionByPayablePlanId(payablePlanId));
    }

    @Override
    public Result<Boolean> oneClickSettlement(OneClickSettlementDto oneClickSettlementDto) {
        return Result.data(htiAgencyCommissionSettlementService.oneClickSettlement(oneClickSettlementDto));
    }

    @Override
    public Result<List<Long>> getSettlementFlagAgentIds() {
        return Result.data(htiAgencyCommissionSettlementService.getSettlementFlagAgentIds());
    }

    @Override
    public Result<Boolean> deleteUnsettledCommissionByAdmissionFailure(List<Long> payablePlanIdSet) {
        return Result.data(htiAgencyCommissionSettlementService.deleteUnsettledCommissionByAdmissionFailure(payablePlanIdSet));
    }

    /**
     * 根据id获取差旅报销单
     * @param targetId
     * @return
     */
    @Override
    public Result<TravelClaimForm> getTravelClaimFormById(Long targetId) {
        return Result.data(travelClaimFormService.getTravelClaimFormById(targetId));
    }

    /**
     * 根据id获取费用报销单
     * @param targetId
     * @return
     */
    @Override
    public Result<ExpenseClaimForm> getExpenseClaimFormById(Long targetId) {
        return Result.data(expenseClaimFormService.getExpenseClaimFormById(targetId));
    }

    /**
     * 根据id获取借款申请单
     * @param targetId
     * @return
     */
    @Override
    public Result<PrepayApplicationForm> getPrepayApplicationFormById(Long targetId) {
        return Result.data(iPrepayApplicationFormService.getPrepayApplicationFormById(targetId));
    }

    @Override
    public Result<PaymentApplicationForm> getPaymentApplicationFormById(Long targetId) {
        return Result.data(paymentApplicationFormService.getPaymentApplicationFormById(targetId));
    }

    @Override
    public Result<Boolean> updateExpenseClaimFormStatus(ExpenseClaimForm expenseClaimForm) {
        return Result.data(expenseClaimFormService.updateExpenseClaimFormStatus(expenseClaimForm));
    }

    @Override
    public Result<Boolean> updateTravelClaimFormStatus(TravelClaimForm travelClaimForm) {
        return Result.data(travelClaimFormService.updateTravelClaimFormStatus(travelClaimForm));
    }

    @Override
    public Result<Boolean> updatePaymentApplicationFormStatus(PaymentApplicationForm paymentApplicationForm) {
        return Result.data(paymentApplicationFormService.updatePaymentApplicationFormStatus(paymentApplicationForm));
    }

    @Override
    public Result<BigDecimal> getExpenseClaimFormTotalAmount(Long id) {
        return Result.data(expenseClaimFormService.getExpenseClaimFormTotalAmount(id));
    }

    @Override
    public Result<BigDecimal> getTravelClaimFormTotalAmount(Long id) {
        return Result.data(travelClaimFormService.getTravelClaimFormTotalAmount(id));
    }

    @Override
    public Result<BigDecimal> getPaymentApplicationFormTotalAmount(Long id) {
        return Result.data(paymentApplicationFormService.getPaymentApplicationFormTotalAmount(id));
    }

    @Override
    public Result<List<InsurancePaymentFormVo>> createInsurancePaymentForm(InsurancePaymentFormDto insurancePaymentFormDto) {
        return Result.data(paymentFormService.createInsurancePaymentForm(insurancePaymentFormDto));
    }

    @Override
    public Result<FormInformationVo> getFormInformationByFormId(String fkTableName, Long fkTableId) {
        FormInformationVo formInformationVo = new FormInformationVo();
        if (fkTableName.equals(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key)) {
            ExpenseClaimFormVo expenseClaimFormVo = expenseClaimFormService.findExpenseClaimFormById(fkTableId);
            //报销单id
            formInformationVo.setId(expenseClaimFormVo.getId());
            //报销单编号
            formInformationVo.setNum(expenseClaimFormVo.getNum());
            //报销金额
            formInformationVo.setAmount(expenseClaimFormVo.getAmountSum().toString());
            //报销摘要
            formInformationVo.setSummary(expenseClaimFormVo.getSummary());
            //申请人
            formInformationVo.setApplicant(expenseClaimFormVo.getFkStaffIdVouchCreatedName());
            //申请部门
            formInformationVo.setDepartmentName(expenseClaimFormVo.getDepartmentName());
            //申请日期
            formInformationVo.setCreateDate(expenseClaimFormVo.getGmtCreate());
            //币种名称
            formInformationVo.setCurrencyTypeName(expenseClaimFormVo.getCurrencyTypeName());
        }else if (fkTableName.equals(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key)) {
            TravelClaimFormVo travelClaimFormVo = travelClaimFormService.findTravelClaimFormById(fkTableId);
            //报销单id
            formInformationVo.setId(travelClaimFormVo.getId());
            //报销单编号
            formInformationVo.setNum(travelClaimFormVo.getNum());
            //报销金额
            formInformationVo.setAmount(travelClaimFormVo.getAmountSum().toString());
            //报销摘要
            formInformationVo.setSummary(travelClaimFormVo.getSummary());
            //申请人
            formInformationVo.setApplicant(travelClaimFormVo.getFkStaffIdVouchCreatedName());
            //申请部门
            formInformationVo.setDepartmentName(travelClaimFormVo.getDepartmentName());
            //申请日期
            formInformationVo.setCreateDate(travelClaimFormVo.getGmtCreate());
            //币种名称
            formInformationVo.setCurrencyTypeName(travelClaimFormVo.getCurrencyTypeName());

        }else if (fkTableName.equals(TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key)) {
            PrepayApplicationFormVo prepayApplicationFormVo = iPrepayApplicationFormService.getPrepayDetailData(fkTableId);
            //报销单id
            formInformationVo.setId(prepayApplicationFormVo.getId());
            //报销单编号
            formInformationVo.setNum(prepayApplicationFormVo.getNum());
            //报销金额
            formInformationVo.setAmount(prepayApplicationFormVo.getAmount().toString());
            //报销摘要
            formInformationVo.setSummary(prepayApplicationFormVo.getSummary());
            //申请人
            formInformationVo.setApplicant(prepayApplicationFormVo.getGmtCreateUser());
            //申请部门
            formInformationVo.setDepartmentName(prepayApplicationFormVo.getDepartmentName());
            //申请日期
            formInformationVo.setCreateDate(prepayApplicationFormVo.getGmtCreate());
            //币种名称
            formInformationVo.setCurrencyTypeName(prepayApplicationFormVo.getFkCurrencyTypeNumName());

        }else if (fkTableName.equals(TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key)) {
            PaymentApplicationFormVo mpayDetailData = paymentApplicationFormService.getMpayDetailData(fkTableId);
            //报销单id
            formInformationVo.setId(mpayDetailData.getId());
            //报销单编号
            formInformationVo.setNum(mpayDetailData.getNum());
            //报销金额
            formInformationVo.setAmount(mpayDetailData.getAmountSum().toString());
            //报销摘要
            formInformationVo.setSummary(mpayDetailData.getSummary());
            //申请人
            formInformationVo.setApplicant(mpayDetailData.getGmtCreateUser());
            //申请部门
            formInformationVo.setDepartmentName(mpayDetailData.getDepartmentName());
            //申请日期
            formInformationVo.setCreateDate(mpayDetailData.getGmtCreate());
            //币种名称
            formInformationVo.setCurrencyTypeName(mpayDetailData.getFkCurrencyTypeNumName());
        }
        return Result.data(formInformationVo);
    }

}
