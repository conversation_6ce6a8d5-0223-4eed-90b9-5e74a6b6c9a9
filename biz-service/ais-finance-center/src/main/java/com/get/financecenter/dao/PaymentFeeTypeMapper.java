package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.PaymentFeeType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface PaymentFeeTypeMapper extends BaseMapper<PaymentFeeType> {


    Integer getMaxViewOrder();

    PaymentFeeType selectByFkAccountingItemId(@Param("fkAccountingItemId") Long id);
}