package com.get.financecenter.utils;

import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.enums.ActivityTypeEnum;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.EventIncentiveVo;
import com.get.salecenter.vo.EventVo;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Consumer;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class ActivityTypeUtils {

    @Resource
    private ISaleCenterClient saleCenterClient;

    private Map<String, String> displayNameMap = new HashMap<>();
    @PostConstruct
    public void init() {
        // 初始化关系目标类型与显示名称的映射
        for (ActivityTypeEnum type : ActivityTypeEnum.values()) {
            displayNameMap.put(type.activityTypeCode, type.name);
        }
    }

    public <T> void processRelationTarget(String eventTableName,
                                          Long eventTableId,
                                          Consumer<String> nameHandler, //单独的名称
                                          Consumer<Long> companyIdHandler,
                                          Consumer<String> displayNameHandler //拼接的名称
    ) {
        if (eventTableName == null || eventTableId == null) return;

        String name = null;
        Long companyId = null;

        if (eventTableName.equals(ActivityTypeEnum.M_EVENT_INCENTIVE.activityTypeCode)) {
            EventIncentiveVo eventIncentiveVo = saleCenterClient.getEventIncentiveById(eventTableId).getData();
            if (GeneralTool.isNotEmpty(eventIncentiveVo)) {
                name = eventIncentiveVo.getEventTitle();
                if (name != null) {

                }
                companyId = eventIncentiveVo.getFkCompanyId();
            }

        } else if (eventTableName.equals(ActivityTypeEnum.M_EVENT.activityTypeCode)) {
            EventVo eventVo = saleCenterClient.getEventById(eventTableId).getData();
            if (GeneralTool.isNotEmpty(eventVo)) {
                name = eventVo.getEventTheme();
                companyId = eventVo.getFkCompanyId();
            }
        }

        // 处理回调
        if (name != null) {
            if (nameHandler != null) nameHandler.accept(name);
            if (companyIdHandler != null) companyIdHandler.accept(companyId);
            if (displayNameHandler != null) {
                String displayName = displayNameMap.getOrDefault(eventTableName, "") + ":" + name;
                displayNameHandler.accept(displayName);
            }
        }
    }

}
