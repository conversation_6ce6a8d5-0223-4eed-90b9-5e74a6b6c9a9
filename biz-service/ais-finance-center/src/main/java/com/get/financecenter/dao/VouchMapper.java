package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.VouchDto;
import com.get.financecenter.entity.Vouch;
import com.get.financecenter.vo.VouchVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Mapper
public interface VouchMapper extends BaseMapper<Vouch> {

    List<VouchVo> getVouchs(IPage<Vouch> pages, @Param("vouchDto") VouchDto vouchDto);

    /**
     * 获取期数
     *
     * @param companyId         公司
     * @param accountingItemIds 科目ids(包含子科目)
     * @param startDate         业务开始时间
     * @param endDate           业务结束时间
     * @param relationTargetKey 关联目标类型
     * @param relationTargetId  关联目标id
     * @param direction         余额方向：0借/1贷。  2:DR累加 3：CR累加
     * @return
     */
    BigDecimal getAccountingItemBalance(@Param("companyId") Long companyId,
                                        @Param("accountingItemIds") List<Long> accountingItemIds,
                                        @Param("startDate") Date startDate,
                                        @Param("endDate") Date endDate,
                                        @Param("relationTargetKey") String relationTargetKey,
                                        @Param("relationTargetId") Long relationTargetId,
                                        @Param("direction") Integer direction);



}