package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.vo.AlreadyReceiptVo;
import com.get.financecenter.vo.ReceiptFormItemVo;
import com.get.financecenter.entity.ReceiptFormItem;
import com.get.financecenter.dto.ReceiptFormItemDto;
import com.get.salecenter.vo.SaleReceiptFormItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Mapper
public interface ReceiptFormItemMapper extends BaseMapper<ReceiptFormItem> {
    int insertSelective(ReceiptFormItem record);


    /**
     * @return java.lang.String
     * @Description: 获取上次付款单的支付币种
     * @Param [planId]
     * <AUTHOR>
     */
    List<AlreadyReceiptVo> getAlreadyReceiptByPlanId(Long planId);

    /**
     * 根据计划ids获取AlreadyReceipts
     *
     * @param planIds
     * @return
     */
    List<AlreadyReceiptVo> getAlreadyReceiptByPlanIds(@Param("planIds") Set<Long> planIds);

    /**
     * @return java.lang.Long
     * @Description: 查询公司id
     * @Param [itemId]
     * <AUTHOR>
     */
    Long getCompanyIdByItemId(Long itemId);


    BigDecimal getAmountByFormId(@Param("formId") Long formId, @Param("formItemId") Long formItemId);

    /**
     * 获取绑定发票单号的计划ids
     *
     * @param fkInvoiceNum
     * @return
     */
    List<Long> getPlanIdByInvoiceNum(@Param("fkInvoiceNum") String fkInvoiceNum);

    /**
     * feign 查找对应的应收计划是否绑定 未结算过的收款单子单
     *
     * @Date 16:49 2021/11/19
     * <AUTHOR>
     */
    List<ReceiptFormItemVo> getSettlementReceiptFormItemListByPlanId(@Param("receivablePlanId") Long receivablePlanId);

    /**
     * feign 根据应收计划id获取绑定的收款单子单列表
     *
     * @Date 17:58 2022/4/21
     * <AUTHOR>
     */
    List<ReceiptFormItemVo> getReceiptFormItemListFeignByPlanIds(@Param("receivablePlanIds") Set<Long> receivablePlanIds);

    /**
     * 找出不在分期表内（既未结算的收款）的收款单，生成分期表
     *
     * @Date 23:38 2022/4/21
     * <AUTHOR>
     */
    List<SaleReceiptFormItemVo> getUnsettledReceiptFormItem(@Param("receivablePlanId") Long receivablePlanId);

    /**
     * feign 根据收款单子单ids获取收款单子单信息
     *
     * @Date 17:58 2022/4/21
     * <AUTHOR>
     */
    List<ReceiptFormItemVo> getReceiptFormItemListFeignByFormItemIds(@Param("receiptFormItemIds") Set<Long> receiptFormItemIds);

    List<ReceiptFormItem> getReceiptFormItemData(@Param("receiptFormItemDto") ReceiptFormItemDto receiptFormItemDto, IPage page);

    List<ReceiptFormItemVo> getReceiptFormByInvoiceId(Long fkInvoiceId);
}