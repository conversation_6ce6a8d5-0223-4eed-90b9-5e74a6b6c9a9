<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.PaymentFormItemMapper">
  <insert id="insert" parameterType="com.get.financecenter.entity.PaymentFormItem" keyProperty="id"
          useGeneratedKeys="true">
    insert into m_payment_form_item (id, fk_payment_form_id, fk_payable_plan_id, 
      amount_payment, service_fee, exchange_rate_payable, 
      amount_payable, amount_exchange_rate, exchange_rate_hkd, 
      amount_hkd, exchange_rate_rmb, amount_rmb, 
      summary, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkPaymentFormId,jdbcType=BIGINT}, #{fkPayablePlanId,jdbcType=BIGINT}, 
      #{amountPayment,jdbcType=DECIMAL}, #{serviceFee,jdbcType=DECIMAL}, #{exchangeRatePayable,jdbcType=DECIMAL}, 
      #{amountPayable,jdbcType=DECIMAL}, #{amountExchangeRate,jdbcType=DECIMAL}, #{exchangeRateHkd,jdbcType=DECIMAL}, 
      #{amountHkd,jdbcType=DECIMAL}, #{exchangeRateRmb,jdbcType=DECIMAL}, #{amountRmb,jdbcType=DECIMAL}, 
      #{summary,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.financecenter.entity.PaymentFormItem" keyProperty="id"
          useGeneratedKeys="true">
    insert into m_payment_form_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkPaymentFormId != null">
        fk_payment_form_id,
      </if>
      <if test="fkPayablePlanId != null">
        fk_payable_plan_id,
      </if>
      <if test="amountPayment != null">
        amount_payment,
      </if>
      <if test="serviceFee != null">
        service_fee,
      </if>
      <if test="exchangeRatePayable != null">
        exchange_rate_payable,
      </if>
      <if test="amountPayable != null">
        amount_payable,
      </if>
      <if test="amountExchangeRate != null">
        amount_exchange_rate,
      </if>
      <if test="exchangeRateHkd != null">
        exchange_rate_hkd,
      </if>
      <if test="amountHkd != null">
        amount_hkd,
      </if>
      <if test="exchangeRateRmb != null">
        exchange_rate_rmb,
      </if>
      <if test="amountRmb != null">
        amount_rmb,
      </if>
      <if test="summary != null">
        summary,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkPaymentFormId != null">
        #{fkPaymentFormId,jdbcType=BIGINT},
      </if>
      <if test="fkPayablePlanId != null">
        #{fkPayablePlanId,jdbcType=BIGINT},
      </if>
      <if test="amountPayment != null">
        #{amountPayment,jdbcType=DECIMAL},
      </if>
      <if test="serviceFee != null">
        #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="exchangeRatePayable != null">
        #{exchangeRatePayable,jdbcType=DECIMAL},
      </if>
      <if test="amountPayable != null">
        #{amountPayable,jdbcType=DECIMAL},
      </if>
      <if test="amountExchangeRate != null">
        #{amountExchangeRate,jdbcType=DECIMAL},
      </if>
      <if test="exchangeRateHkd != null">
        #{exchangeRateHkd,jdbcType=DECIMAL},
      </if>
      <if test="amountHkd != null">
        #{amountHkd,jdbcType=DECIMAL},
      </if>
      <if test="exchangeRateRmb != null">
        #{exchangeRateRmb,jdbcType=DECIMAL},
      </if>
      <if test="amountRmb != null">
        #{amountRmb,jdbcType=DECIMAL},
      </if>
      <if test="summary != null">
        #{summary,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <update id="updateFee">
        update m_payment_form_item set service_fee=service_fee + #{fee} where id=#{id} and fk_payment_form_id=#{payFormId}
    </update>

    <select id="getCompanyIdByItemId" resultType="java.lang.Long">
        SELECT f.fk_company_id
        from m_payment_form_item p
                 left join m_payment_form f on p.fk_payment_form_id = f.id
        where p.id = #{itemId}
    </select>


    <select id="getCompanyIdByItemIds" resultType="com.get.salecenter.vo.SelItem">
        SELECT f.fk_company_id as val,p.id as key_id
        from m_payment_form_item p
                 left join m_payment_form f on p.fk_payment_form_id = f.id
        where p.id in
        <foreach collection="itemIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="getAlreadyPayByPlanId" resultType="com.get.financecenter.vo.AlreadyPayVo">
        SELECT i.id,
               f.fk_currency_type_num            alreadyPayCurrency,
               i.amount_payment                  alreadyPayAmount,
               IFNULL(i.amount_exchange_rate, 0) alreadyExchangeRate,
               i.amount_payable AS               amountPayable
        from m_payment_form_item i
                 left join m_payment_form f on i.fk_payment_form_id = f.id
        where i.fk_payable_plan_id = #{planId}
          and f.status = 1
    </select>

    <select id="getAmountByFormId" resultType="java.math.BigDecimal">
        SELECT IFNULL(sum(i.amount_payment),0) alreadyPayAmount from m_payment_form_item i
        left join m_payment_form f on i.fk_payment_form_id=f.id
        where i.fk_payment_form_id=#{formId}
        <if test="formItemId != null">
            AND i.id != #{formItemId}
        </if>
        and f.status=1
    </select>
    <!--    <select id="agentSettlementList" resultType="com.get.common.entity.fegin.AgentSettlementVo">-->
    <!--        SELECT-->
    <!--            a.*,-->
    <!--            b.planCount-->
    <!--        FROM-->
    <!--        ais_sale_center.m_agent AS a-->
    <!--        INNER JOIN (-->
    <!--        SELECT-->
    <!--        a1.id,-->
    <!--        COUNT( DISTINCT mpp.id ) AS planCount-->
    <!--        FROM-->
    <!--        ais_sale_center.m_agent AS a1-->
    <!--        INNER JOIN ais_sale_center.m_student_offer AS mso ON mso.fk_agent_id = a1.id-->
    <!--        AND mso.STATUS = 1-->
    <!--        AND mso.status_workflow IN ( 0, 3, 4 )-->
    <!--        AND a1.is_active = 1-->
    <!--        INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.fk_student_offer_id = mso.id-->
    <!--        AND msoi.STATUS = 1-->
    <!--        INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = msoi.id-->
    <!--        AND mpp.fk_type_key = 'm_student_offer_item'-->
    <!--        AND mpp.STATUS = 1-->
    <!--        AND mpp.status_settlement = #{statusSettlement}-->
    <!--        INNER JOIN ais_sale_center.m_student AS s ON s.id = mso.fk_student_id-->
    <!--        <where>-->
    <!--            <if test="agentIdList != null">-->
    <!--            AND a1.id IN-->
    <!--                <foreach collection="agentIdList" index="index" item="agentId" open="(" separator="," close=")">-->
    <!--                    #{agentId}-->
    <!--                </foreach>-->
    <!--            </if>-->
    <!--            <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName !=''">-->
    <!--                AND s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')-->
    <!--            </if>-->
    <!--            <if test="agentSettlementDto.startTime!=null and agentSettlementDto.startTime!=''">-->
    <!--                and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]>#{agentSettlementDto.agentSettlementDto.startTime}-->
    <!--            </if>-->
    <!--            <if test="agentSettlementDto.endTime!=null and agentSettlementDto.endTime!=''">-->
    <!--                and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]>#{agentSettlementDto.endTime}-->
    <!--            </if>-->
    <!--            <if test="agentSettlementDto.openingStartTime!=null and agentSettlementDto.openingStartTime!=''">-->
    <!--                and DATE_FORMAT( msoi.opening_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.openingStartTime},
                '%Y-%m-%d' )-->
    <!--            </if>-->
    <!--            <if test="agentSettlementDto.openingEndTime!=null and agentSettlementDto.openingEndTime!=''">-->
    <!--                and DATE_FORMAT( msoi.opening_time, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.openingEndTime},
                '%Y-%m-%d' )-->
    <!--            </if>-->
    <!--            <if test="agentSettlementDto.agentNameOrNum != null and  agentSettlementDto.agentNameOrNum !=''">-->
    <!--                and (a1.num =#{agentSettlementDto.agentNameOrNum} OR a1.name = #{agentSettlementDto.agentNameOrNum})-->
    <!--            </if>-->
    <!--        </where>-->
    <!--                GROUP BY-->
    <!--                    a1.id-->
    <!--            ) b ON b.id = a.id-->
    <!--        order by a.name-->
    <!-- </select>-->

    <select id="agentSettlementOfferItemList" resultType="com.get.financecenter.vo.AgentSettlementOfferItemVo">
        SELECT
        mpp.id,
        mpp.payable_amount,
        mpp.commission_rate as payableCommissionRate,
        mpp.split_rate as payableSplitRate,
        mpp.fk_currency_type_num,
        mpp.fk_receivable_plan_id as fkReceivablePlanId,
        mpp.fk_type_key,
        GREATEST(mpp.is_pay_in_advance, rppsi.is_pay_in_advance) AS is_pay_in_advance,
        mpp.summary,
        c.paidAmount,
        <!--【应付金额】-【实际手续费金额】-【实际支付金额】-【已付金额】=【应付未付】-->
        c.differenceAmount - IFNULL(commissionSettlement.settlementAmount,0)  AS differenceAmount,
        s.NAME,s.num as studentNum,
        IF
        ( b.STATUS, b.STATUS, 0 ) AS STATUS,
        msoi.id as student_offer_itemId,
        msoi.fk_area_country_id,
        msoi.fk_institution_id,
        msoi.fk_institution_provider_id as fkInstitutionProviderId,
        msoi.fk_institution_course_id,
        IFNULL(msoi.is_defer_entrance,0) as is_defer_entrance,
        <!-- msoi.opening_time, -->
        msoi.defer_opening_time AS opening_time,
        msoi.gmt_create,
        msoi.duration_type,
        msoi.duration,
        msoi.old_course_custom_name,
        msoi.old_institution_name,
        msoi.old_institution_full_name,
        a.id as agentId,
        <!-- 导出排序用 -->
        <if test="exportFlag">
        a1.maxTime,
        </if>
        CONCAT(rsbc.bd_code, a.num) as agentNum,
        CASE WHEN (a.name_note IS NOT NULL AND a.name_note != '')
            THEN
            CONCAT(a.name,'（',IFNULL(a.name_note,''),'）')
            ELSE
                a.name
            END AS agentName,
        usois.step_name,
        rppsi.settlementCreateTime,
        rppsi.amount_actual,
        rppsi.serviceFeeActual,
        rppsi.account_export_time,
        rppsi.installmentCreateTime,
        rppsi.settlementIds,
        rppsi.amount_receivable,
        rppsi.maxFkReceiptFormItemId,
        rppsi.fk_agent_contract_account_id,
        rppsi.fk_currency_type_num AS accountCurrencyTypeNum,
        rppsi.prepaidMark,
        mso.gmt_create as studentOfferCreateTime,
        rppsi.is_roll_back AS rollBack,
        IF(ucsot.type_mark is not null, CONCAT(cliCompany.short_name, "." , ucsot.type_mark),'') AS commissionMark,
        ma.companyName
        FROM
        ais_sale_center.m_student_offer_item AS msoi
        LEFT JOIN ais_sale_center.m_student_offer mso ON mso.id = msoi.fk_student_offer_id
        LEFT JOIN ais_sale_center.r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = msoi.fk_staff_id
        INNER JOIN ais_sale_center.m_student AS s ON s.id = msoi.fk_student_id
        LEFT JOIN ais_sale_center.m_client AS mc ON mc.id = s.fk_client_id
        LEFT JOIN ais_permission_center.m_company AS cliCompany ON cliCompany.id = mc.fk_company_id
        LEFT JOIN (SELECT fk_client_id,MAX(fk_table_name) AS fk_table_name FROM ais_sale_center.s_client_source GROUP BY fk_client_id) AS scs ON scs.fk_client_id = mc.id
        LEFT JOIN ais_sale_center.u_client_source_type AS ucsot ON ucsot.type_key = scs.fk_table_name
        LEFT JOIN (
        SELECT
            ma.id,
            GROUP_CONCAT(mc.num) AS companyName
            FROM
            ais_sale_center.m_agent AS ma
            INNER JOIN ais_sale_center.r_agent_company AS rac ON rac.fk_agent_id = ma.id
            INNER JOIN ais_permission_center.m_company AS mc ON mc.id = rac.fk_company_id
            GROUP BY
            ma.id
        ) ma ON ma.id = msoi.fk_agent_id
        <if test="agentSettlementDto.statusSettlement == 2">
            <if test="agentSettlementDto.fkCompanyId != null and agentSettlementDto.fkCompanyId !=''">
                AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
            </if>
            <if test="agentSettlementDto.fkCompanyIds != null and agentSettlementDto.fkCompanyIds.size()>0">
                AND s.fk_company_id in
                <foreach collection="agentSettlementDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
        </if>
        INNER JOIN ais_sale_center.m_agent AS a ON a.id = msoi.fk_agent_id
        LEFT JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = msoi.fk_student_offer_item_step_id
        INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = msoi.id
        AND mpp.fk_type_key = 'm_student_offer_item'
        AND mpp.STATUS = 1
        -- 实际支付金额
        INNER JOIN (
        SELECT rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num, CASE WHEN rppsi1.status = 0 THEN SUM(rppsi1.amount_actual) ELSE MAX(rppsi1.amount_actual) END amount_actual,
               CASE WHEN rppsi1.status = 0 THEN SUM(rppsi1.service_fee_actual) ELSE MAX(rppsi1.service_fee_actual) END serviceFeeActual, MAX( CASE WHEN rppsi1.fk_receipt_form_item_id IS NULL OR  rppsi1.fk_receipt_form_item_id = 0 THEN rppsi1.gmt_create ELSE NULL END ) AS settlementCreateTime,
               CASE WHEN COUNT(fk_receipt_form_item_id) = COUNT(*) THEN 0 ELSE 1 END AS prepaidMark,
               MAX(rppsi1.account_export_time) AS account_export_time, MIN(rppsi1.is_roll_back) AS is_roll_back,
                COALESCE(
                    CASE
                            WHEN IFNULL(mrf1.receipt_date, CAST('9999-01-01 00:00:00' AS DATETIME)) <![CDATA[ <  ]]> IFNULL(invoice.receipt_date, CAST('9999-01-01 00:00:00' AS DATETIME)) THEN mrf1.receipt_date
                            ELSE invoice.receipt_date
                        END
                    ) AS installmentCreateTime,
               SUM( IFNULL(mrfi1.amount_receivable, 0) ) AS amount_receivable, MAX(rppsi1.fk_receipt_form_item_id) AS maxFkReceiptFormItemId, GROUP_CONCAT(rppsi1.id) AS settlementIds, IFNULL(MAX(rirp.is_pay_in_advance), 0) AS is_pay_in_advance,
            MIN(rppsi1.fk_receipt_form_item_id) AS fk_receipt_form_item_id
        FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.id = rppsi1.fk_receipt_form_item_id
        LEFT JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
        LEFT JOIN ais_finance_center.r_invoice_receivable_plan AS rirp ON rirp.id = rppsi1.fk_invoice_receivable_plan_id
        LEFT JOIN (
            SELECT mi.id, MIN(mrf2.receipt_date) AS receipt_date FROM ais_finance_center.m_invoice AS mi
            INNER JOIN ais_finance_center.r_receipt_form_invoice AS rrfi ON rrfi.fk_invoice_id = mi.id
            INNER JOIN ais_finance_center.m_receipt_form AS mrf2 ON mrf2.id = rrfi.fk_receipt_form_id
            GROUP BY mi.id
        ) AS invoice ON invoice.id = rppsi1.fk_invoice_id
        <where>
            AND rppsi1.status_settlement = #{agentSettlementDto.statusSettlement}
            <if test="agentSettlementDto.receiptStartTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
            <if test="agentSettlementDto.receiptEndTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
        </where>
        GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
        <if test="agentSettlementDto.prepaidMark != null">
            HAVING
            <if test="agentSettlementDto.prepaidMark">
                prepaidMark = 1
            </if>
            <if test="!agentSettlementDto.prepaidMark">
                prepaidMark = 0
            </if>
        </if>
        ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
        LEFT JOIN (
            SELECT a.fk_payable_plan_id, SUM(amount_actual) + SUM(service_fee_actual) AS settlementAmount FROM (
                SELECT
                    fk_payable_plan_id,
                    MAX( amount_actual ) AS amount_actual,
                    MAX( service_fee_actual ) AS service_fee_actual
                FROM
                ais_finance_center.r_payable_plan_settlement_installment
                WHERE
                    STATUS != 2
                    AND status_settlement != 0
                GROUP BY
                    fk_payable_plan_id,
                    status_settlement,
                    fk_agent_contract_account_id

                    UNION ALL

                    SELECT
                    fk_payable_plan_id,
                    SUM( amount_actual ) AS amount_actual,
                    SUM( service_fee_actual ) AS service_fee_actual
                FROM
                ais_finance_center.r_payable_plan_settlement_installment
                WHERE
                    status_settlement = 0
                GROUP BY
                    fk_payable_plan_id
                    ) AS a
                    GROUP BY a.fk_payable_plan_id
        ) AS commissionSettlement ON commissionSettlement.fk_payable_plan_id = mpp.id
        -- 计算收款状态
        LEFT JOIN (
        SELECT
        mpp1.id,
        CASE
        MAX( mrp.receivable_amount ) - IFNULL(SUM( mrfi.amount_receivable ),0) - IFNULL(SUM( mrfi.amount_exchange_rate ),0)
        WHEN SUM( mrp.receivable_amount ) THEN
        0
        WHEN 0 THEN
        2 ELSE 1
        END AS STATUS
        FROM
        ais_sale_center.m_payable_plan AS mpp1
        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.id = mpp1.fk_receivable_plan_id
        AND mrp.fk_type_key = 'm_student_offer_item'
        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
        LEFT JOIN ais_finance_center.m_receipt_form AS mrf ON mrf.id = mrfi.fk_receipt_form_id
        WHERE
        mrp.STATUS = 1 AND mrf.status = 1
        <if test="agentSettlementDto.statusSettlement == 0 or agentSettlementDto.statusSettlement == 1">
            AND mrf.settlement_status = 1
        </if>
        GROUP BY
        mpp1.id
        ) b ON b.id = mpp.id
        --      显示已付金额、差额
        LEFT JOIN (
        SELECT
        mpp2.id,
        IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
        mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp2
        LEFT JOIN (
        SELECT
        mpp3.id,
        IFNULL( SUM( mpfi2.amount_payable ), 0 ) + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp3
        LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
        AND mpp3.fk_type_key = 'm_student_offer_item'
        LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
        WHERE
        mpp3.STATUS = 1 AND mpf2.status = 1
        GROUP BY
        mpp3.id
        ) mpp4 ON mpp4.id = mpp2.id
        ) c ON c.id = mpp.id
        <!-- 导出排序用 -->
        <if test="exportFlag">
            INNER JOIN (
            SELECT a1.id, MAX(rppss.gmt_create) AS maxTime
            FROM
            ais_sale_center.m_agent AS a1
            INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.fk_agent_id = a1.id
            INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = msoi.id
            AND mpp.fk_type_key = 'm_student_offer_item'
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id  AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}
            group by a1.id
            )a1 ON a1.id = a.id
        </if>
        <if test="agentSettlementDto.stepSubmissionStartTime!=null or agentSettlementDto.stepSubmissionEndTime!=null">
            INNER JOIN (
            SELECT
            mpp1.id, MAX(rppss.gmt_create) AS maxTime
            FROM
            ais_sale_center.m_agent AS a1
            INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.fk_agent_id = a1.id
            INNER JOIN ais_sale_center.m_payable_plan AS mpp1 ON mpp1.fk_type_target_id = msoi.id
            AND mpp1.fk_type_key = 'm_student_offer_item'
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp1.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id  AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}
            group by mpp1.id
        <if test="agentSettlementDto.stepSubmissionStartTime!=null or agentSettlementDto.stepSubmissionEndTime!=null">
            HAVING 1=1
            <if test="agentSettlementDto.stepSubmissionStartTime!=null">
                AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionStartTime}, '%Y-%m-%d' )
            </if>
            <if test="agentSettlementDto.stepSubmissionEndTime!=null">
                AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionEndTime}, '%Y-%m-%d' )
            </if>
        </if>
            )a2 ON a2.id = mpp.id
        </if>

        <where>
            AND msoi.is_follow_hidden = 0
            <if test="agentIds!=null and agentIds.size>0">
                AND a.id IN
                <foreach collection="agentIds" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>

            --  佣金结算第一步，只显示enrolled状态的申请计划
            <if test="agentSettlementDto.statusSettlement == 0">
                AND (
                ( msoi.fk_student_offer_item_step_id IN (SELECT id FROM ais_sale_center.u_student_offer_item_step where step_key = #{enrolledKey} or step_key = #{stepEnrolledTbc}) )
                    OR
                rppsi.amount_actual <![CDATA[< ]]> 0
                    OR
                rppsi.fk_receipt_form_item_id = 0
                <if test="payInAdvanceFlag">
                    OR ( mpp.is_pay_in_advance = 1 or rppsi.is_pay_in_advance = 1 )
                </if>
                )
            </if>

            <if test="agentSettlementDto.studentName != null and  agentSettlementDto.studentName !=''">
                AND
                (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.`name`,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.last_name,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.first_name,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ',''))
            </if>
            <!-- 过滤学生佣金结算标记关键字 -->
<!--            <if test="agentSettlementDto.commissionMark != null and agentSettlementDto.commissionMark != ''">-->
<!--                AND LOWER(IF(ucsot.type_mark is not null, CONCAT(cliCompany.short_name, "." , ucsot.type_mark),'')) LIKE CONCAT('%',#{agentSettlementDto.commissionMark},'%')-->
<!--            </if>-->
            <if test="agentSettlementDto.applyStartTime!= null">
                and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.applyStartTime}, '%Y-%m-%d' )
            </if>
            <if test="agentSettlementDto.applyEndTime!=null">
                and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.applyEndTime}, '%Y-%m-%d' )
            </if>
            <if test="agentSettlementDto.openingStartTime != null">
                AND  (
                DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{agentSettlementDto.openingStartTime},'%Y-%m-%d')
                )
            </if>
            <if test="agentSettlementDto.openingEndTime != null">
                AND (
                DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{agentSettlementDto.openingEndTime},'%Y-%m-%d')
                )
            </if>
            <if test="agentSettlementDto.agentNameOrNum != null and  agentSettlementDto.agentNameOrNum !=''">
                AND (a.num =#{agentSettlementDto.agentNameOrNum} OR a.name LIKE CONCAT('%', #{agentSettlementDto.agentNameOrNum}, '%') )
            </if>
            <!--            <if test="agentIdList != null and agentIdList.size() > 0">-->
            <!--                AND a.id IN-->
            <!--                <foreach collection="agentIdList" index="index" item="agentId" open="(" separator="," close=")">-->
            <!--                    #{agentId}-->
            <!--                </foreach>-->
            <!--            </if>-->
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
        </where>
        ORDER BY
        <!-- 导出排序用 -->
        <if test="exportFlag">
            a1.maxTime desc,
            a1.id,
        </if>
        status DESC,
        msoi.defer_opening_time ASC,
        s.NAME
    </select>


    <select id="getHtiNoCommission" resultType="com.get.financecenter.vo.AgentSettlementOfferItemVo">
        SELECT
        mpp.id,
        mpp.payable_amount,
        mpp.commission_rate as payableCommissionRate,
        mpp.split_rate as payableSplitRate,
        mpp.fk_currency_type_num,
        mpp.fk_type_key,
        mpp.is_pay_in_advance,
        mpp.summary,
        mrp.id as fkReceivablePlanId,
        c.paidAmount,
        c.differenceAmount,
        s.NAME,
        s.num as studentNum,
        IF
        ( b.STATUS, b.STATUS, 0 ) AS STATUS,
        f.amount_receivable,
        f.installmentCreateTime,
        msoi.id as student_offer_itemId,
        msoi.fk_area_country_id,
        msoi.fk_institution_id,
        msoi.fk_institution_provider_id as fkInstitutionProviderId,
        msoi.fk_institution_course_id,
        IFNULL(msoi.is_defer_entrance,0) as is_defer_entrance,
        msoi.defer_opening_time AS opening_time,
        msoi.gmt_create,
        msoi.duration_type,
        msoi.duration,
        msoi.old_course_custom_name,
        msoi.old_institution_name,
        msoi.old_institution_full_name,
        a.id as agentId,
        null AS maxTime,
        CONCAT(rsbc.bd_code, a.num) as agentNum,
        CASE WHEN (a.name_note IS NOT NULL AND a.name_note != '')
        THEN
        CONCAT(a.name,'（',IFNULL(a.name_note,''),'）')
        ELSE
        a.name
        END AS agentName,
        usois.step_name,
        mso.gmt_create as studentOfferCreateTime
        FROM
        ais_sale_center.m_student_offer_item AS msoi
        LEFT JOIN ais_sale_center.m_student_offer mso ON mso.id = msoi.fk_student_offer_id
        LEFT JOIN ais_sale_center.r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = msoi.fk_staff_id
        LEFT JOIN ais_sale_center.m_student AS s ON s.id = msoi.fk_student_id
        <if test="agentSettlementDto.statusSettlement == 2">
            <if test="agentSettlementDto.fkCompanyId != null and agentSettlementDto.fkCompanyId !=''">
                AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
            </if>
            <if test="agentSettlementDto.fkCompanyIds != null and agentSettlementDto.fkCompanyIds.size()>0">
                AND s.fk_company_id in
                <foreach collection="agentSettlementDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
        </if>
        INNER JOIN ais_sale_center.m_agent AS a ON a.id = msoi.fk_agent_id
        LEFT JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = msoi.fk_student_offer_item_step_id
        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msoi.id
        AND mrp.fk_type_key = 'm_student_offer_item'
        AND mrp.STATUS = 1
        LEFT JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_receivable_plan_id = mrp.id
        AND mpp.STATUS = 1

        -- 计算实际到账金额
        INNER JOIN (
        SELECT
            mrfi1.fk_receivable_plan_id,
            SUM( IFNULL( mrfi1.amount_receivable, 0 ) ) AS amount_receivable,
            MIN(mrf1.receipt_date) AS installmentCreateTime
        FROM
            ais_sale_center.m_receivable_plan AS mrp1
            INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.fk_receivable_plan_id = mrp1.id
            INNER JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
        <where>
            <if test="agentSettlementDto.receiptStartTime!=null">
                AND DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
            </if>
            <if test="agentSettlementDto.receiptEndTime!=null">
                AND DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
            </if>
        </where>
        GROUP BY
            mrfi1.fk_receivable_plan_id
        )f ON f.fk_receivable_plan_id = mrp.id

        -- 计算收款状态
        LEFT JOIN (
        SELECT
        mpp1.id,
        CASE
        MAX( mrp.receivable_amount ) - IFNULL(SUM( mrfi.amount_receivable ),0) - IFNULL(SUM( mrfi.amount_exchange_rate ),0)
        WHEN SUM( mrp.receivable_amount ) THEN
        0
        WHEN 0 THEN
        2 ELSE 1
        END AS STATUS
        FROM
        ais_sale_center.m_payable_plan AS mpp1
        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.id = mpp1.fk_receivable_plan_id
        AND mrp.fk_type_key = 'm_student_offer_item'
        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
        LEFT JOIN ais_finance_center.m_receipt_form AS mrf ON mrf.id = mrfi.fk_receipt_form_id
        WHERE
        mrp.STATUS = 1 AND mrf.status = 1
        <if test="agentSettlementDto.statusSettlement == 0 or agentSettlementDto.statusSettlement == 1">
            AND mrf.settlement_status = 1
        </if>
        GROUP BY
        mpp1.id
        ) b ON b.id = mpp.id
        --      显示已付金额、差额
        LEFT JOIN (
        SELECT
        mpp2.id,
        IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
        mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp2
        LEFT JOIN (
        SELECT
        mpp3.id,
        IFNULL( SUM( mpfi2.amount_payable ), 0 ) + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp3
        LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
        AND mpp3.fk_type_key = 'm_student_offer_item'
        LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
        WHERE
        mpp3.STATUS = 1 AND mpf2.status = 1
        GROUP BY
        mpp3.id
        ) mpp4 ON mpp4.id = mpp2.id
        ) c ON c.id = mpp.id


        <where>
            (mpp.id IS NULL or mpp.payable_amount = 0 )
            <if test="agentSettlementDto.studentName != null and  agentSettlementDto.studentName !=''">
                AND
                (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                OR s.`name` like concat('%',#{agentSettlementDto.studentName},'%')
                OR s.last_name like concat('%',#{agentSettlementDto.studentName},'%')
                OR s.first_name like concat('%',#{agentSettlementDto.studentName},'%'))
            </if>
            <if test="agentSettlementDto.applyStartTime!= null">
                and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.applyStartTime}, '%Y-%m-%d' )
            </if>
            <if test="agentSettlementDto.applyEndTime!=null">
                and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.applyEndTime}, '%Y-%m-%d' )
            </if>
            <if test="agentSettlementDto.openingStartTime != null">
                AND  (
                DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{agentSettlementDto.openingStartTime},'%Y-%m-%d')
                )
            </if>
            <if test="agentSettlementDto.openingEndTime != null">
                AND (
                DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{agentSettlementDto.openingEndTime},'%Y-%m-%d')
                )
            </if>
            <if test="agentSettlementDto.agentNameOrNum != null and  agentSettlementDto.agentNameOrNum !=''">
                AND (a.num =#{agentSettlementDto.agentNameOrNum} OR a.name LIKE CONCAT('%', #{agentSettlementDto.agentNameOrNum}, '%') )
            </if>
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
        </where>
        ORDER BY
        a.id,
        msoi.defer_opening_time ASC,
        s.NAME
    </select>

    <select id="agentSettlementOfferConfirmationItemList" resultType="com.get.financecenter.vo.AgentSettlementOfferItemVo">
<!--        SELECT-->
<!--        mpp.id,-->
<!--        mpp.payable_amount,-->
<!--        mpp.fk_currency_type_num,-->
<!--        mpp.fk_type_key,-->
<!--        s.NAME,-->
<!--        IF-->
<!--        ( b.STATUS, b.STATUS, 0 ) AS STATUS,-->
<!--        msoi.fk_area_country_id,-->
<!--        msoi.fk_institution_id,-->
<!--        msoi.fk_institution_course_id,-->
<!--        msoi.opening_time,-->
<!--        msoi.gmt_create,-->
<!--        msoi.duration_type,-->
<!--        msoi.duration,-->
<!--        msoi.old_course_custom_name,-->
<!--        msoi.old_institution_name,-->
<!--        msoi.old_institution_full_name,-->
<!--        usois.step_name-->
<!--        FROM-->
<!--        ais_sale_center.m_agent AS a-->
<!--        INNER JOIN ais_sale_center.r_payable_plan_settlement_agent AS rppsa ON rppsa.fk_agent_id = a.id AND a.is_active = 1-->
<!--        INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.id = rppsa.fk_payable_plan_id-->
<!--        AND mpp.fk_type_key = 'm_student_offer_item'-->
<!--        AND mpp.STATUS = 1-->
<!--        AND mpp.status_settlement IN-->
<!--        <foreach collection="statusSettlementList" item="statusSettlement" index="index" open="(" separator="," close=")">-->
<!--            #{statusSettlement}-->
<!--        </foreach>-->
<!--        INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id-->
<!--        INNER JOIN ais_sale_center.m_student_offer AS mso ON mso.id = msoi.fk_student_offer_id-->
<!--        AND mso.STATUS = 1-->
<!--        AND mso.status_workflow IN ( 0, 3, 4 )-->
<!--        AND msoi.STATUS = 1-->
<!--        INNER JOIN ais_sale_center.m_student AS s ON s.id = mso.fk_student_id AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}-->
<!--        INNER JOIN ais_sale_center.u_student_offer_item_step AS usois ON usois.id = msoi.fk_student_offer_item_step_id-->

<!--        LEFT JOIN (&#45;&#45; 计算收款状态-->
<!--        SELECT-->
<!--        msoi1.id,-->
<!--        CASE-->
<!--        MAX( mrp.receivable_amount ) - IFNULL(SUM( mrfi.amount_receivable ),0) - IFNULL(SUM( mrfi.amount_exchange_rate ),0)-->
<!--        WHEN SUM( mrp.receivable_amount ) THEN-->
<!--        0-->
<!--        WHEN 0 THEN-->
<!--        2 ELSE 1-->
<!--        END AS STATUS-->
<!--        FROM-->
<!--        ais_sale_center.m_student_offer_item AS msoi1-->
<!--        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msoi1.id-->
<!--        AND mrp.fk_type_key = 'm_student_offer_item'-->
<!--        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id-->
<!--        WHERE-->
<!--        msoi1.STATUS = 1-->
<!--        AND mrp.STATUS = 1-->
<!--        GROUP BY-->
<!--        msoi1.id-->
<!--        ) b ON b.id = msoi.id-->

<!--        &#45;&#45; 只有有收款记录的学习计划才能显示-->
<!--        INNER JOIN (-->
<!--        SELECT DISTINCT msoi2.id FROM ais_sale_center.m_student_offer_item AS msoi2-->
<!--        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msoi2.id AND mrp.fk_type_key =-->
<!--        'm_student_offer_item'-->
<!--        INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id-->
<!--        )z ON z.id = msoi.id-->

<!--        <where>-->
<!--            <if test="agentSettlementDto.studentName != null and  agentSettlementDto.studentName !=''">-->
<!--                AND s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')-->
<!--            </if>-->
<!--            <if test="agentSettlementDto.applyStartTime!= null">-->
<!--                and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.applyStartTime}, '%Y-%m-%d' )-->
<!--            </if>-->
<!--            <if test="agentSettlementDto.applyEndTime!=null">-->
<!--                and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.applyEndTime}, '%Y-%m-%d' )-->
<!--            </if>-->
<!--            <if test="agentSettlementDto.openingStartTime!=null">-->
<!--                and DATE_FORMAT( msoi.opening_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.openingStartTime}, '%Y-%m-%d' )-->
<!--            </if>-->
<!--            <if test="agentSettlementDto.openingEndTime!=null">-->
<!--                and DATE_FORMAT( msoi.opening_time, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.openingEndTime},-->
<!--                '%Y-%m-%d' )-->
<!--            </if>-->
<!--            <if test="agentSettlementDto.agentNameOrNum != null and  agentSettlementDto.agentNameOrNum !=''">-->
<!--                AND a.num =#{agentSettlementDto.agentNameOrNum} OR a.name LIKE CONCAT('%', #{agentSettlementDto.agentNameOrNum}, '%')-->
<!--            </if>-->
<!--            <if test="agentIdList != null">-->
<!--                AND a.id IN-->
<!--                <foreach collection="agentIdList" index="index" item="agentId" open="(" separator="," close=")">-->
<!--                    #{agentId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="agentSettlementDto.negativeFlag">-->
<!--                AND mpp.payable_amount <![CDATA[< ]]> 0-->
<!--            </if>-->
<!--        </where>-->
<!--        ORDER BY-->
<!--        status DESC,-->
<!--        msoi.opening_time ASC,-->
<!--        s.NAME-->
    </select>

    <select id="agentSettlementInsuranceList" resultType="com.get.financecenter.vo.AgentSettlementInsuranceVo">
        SELECT
        mpp.id,
        CASE
        WHEN msi.type = 1
        THEN msi.insurant_name
        ELSE s.NAME END AS studentName,
        msi.id AS insuranceId,
        msi.fk_area_country_id,
        CASE
        WHEN msi.type = 1
        THEN msi.insurant_passport_num
        ELSE s.passport_num END AS passportNum,
        msi.insurance_num,
        msi.insurance_start_time,
        msi.insurance_end_time,
        msi.gmt_create,
        mpp.fk_currency_type_num,
        mpp.payable_amount,
        mpp.fk_type_key,
        c.paidAmount,
        <!--【应付金额】-【实际手续费金额】-【实际支付金额】-【已付金额】=【应付未付】-->
        c.differenceAmount - IFNULL(commissionSettlement.settlementAmount,0)  AS differenceAmount,
        msi.gmt_create,
        IF
        ( b.STATUS, b.STATUS, 0 ) AS STATUS,
        rppsi.settlementCreateTime,
        rppsi.amount_actual,
        rppsi.serviceFeeActual,
        rppsi.account_export_time,
        rppsi.settlementIds,
        rppsi.fk_agent_contract_account_id,
        rppsi.fk_currency_type_num AS accountCurrencyTypeNum,
        rppsi.prepaidMark,
        rppsi.is_roll_back AS rollBack,
        a.id AS agentId
        FROM
        ais_sale_center.m_payable_plan AS mpp
        INNER JOIN ais_sale_center.m_student_insurance AS msi ON msi.id = mpp.fk_type_target_id AND mpp.fk_type_key = 'm_student_insurance'
        <!--<if test="agentSettlementDto.statusSettlement != null">
            <if test="agentSettlementDto.statusSettlement != 2">
                AND mpp.status_settlement = #{agentSettlementDto.statusSettlement}
            </if>
            <if test="agentSettlementDto.statusSettlement == 2">
                AND (mpp.status_settlement = #{agentSettlementDto.statusSettlement} OR (mpp.status_settlement IN (0,1) AND mpp.payable_amount <![CDATA[< ]]> 0))
            </if>
        </if>-->
        INNER JOIN ais_sale_center.m_agent AS a ON a.id = msi.fk_agent_id
        INNER JOIN ais_sale_center.m_student AS s ON s.id = msi.fk_student_id
        <if test="agentSettlementDto.statusSettlement == 2">
            <if test="agentSettlementDto.fkCompanyId != null and agentSettlementDto.fkCompanyId !=''">
                AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
            </if>
            <if test="agentSettlementDto.fkCompanyIds != null and agentSettlementDto.fkCompanyIds.size()>0">
                AND s.fk_company_id in
                <foreach collection="agentSettlementDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
        </if>
        <!--AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}-->
        -- 实际支付金额
        INNER JOIN (
        SELECT rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num, CASE WHEN rppsi1.status = 0 THEN SUM(rppsi1.amount_actual) ELSE MAX(rppsi1.amount_actual) END amount_actual,
        CASE WHEN rppsi1.status = 0 THEN SUM(rppsi1.service_fee_actual) ELSE MAX(rppsi1.service_fee_actual) END serviceFeeActual, MAX( CASE WHEN rppsi1.fk_receipt_form_item_id IS NULL OR  rppsi1.fk_receipt_form_item_id = 0 THEN rppsi1.gmt_create ELSE NULL END ) AS settlementCreateTime,
        CASE WHEN COUNT(fk_receipt_form_item_id) = COUNT(*) THEN 0 ELSE 1 END AS prepaidMark,
        MAX(rppsi1.account_export_time) AS account_export_time, MIN(rppsi1.is_roll_back) AS is_roll_back, GROUP_CONCAT(rppsi1.id) AS settlementIds
        FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.id = rppsi1.fk_receipt_form_item_id
        LEFT JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
        LEFT JOIN ais_finance_center.r_invoice_receivable_plan AS rirp ON rirp.id = rppsi1.fk_invoice_receivable_plan_id
        LEFT JOIN (
            SELECT mi.id, MIN(mrf2.receipt_date) AS receipt_date FROM ais_finance_center.m_invoice AS mi
            INNER JOIN ais_finance_center.r_receipt_form_invoice AS rrfi ON rrfi.fk_invoice_id = mi.id
            INNER JOIN ais_finance_center.m_receipt_form AS mrf2 ON mrf2.id = rrfi.fk_receipt_form_id
            GROUP BY mi.id
        ) AS invoice ON invoice.id = rppsi1.fk_invoice_id
        <where>
            AND rppsi1.status_settlement = #{agentSettlementDto.statusSettlement}
            <if test="agentSettlementDto.receiptStartTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
            <if test="agentSettlementDto.receiptEndTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
        </where>
        GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
        <if test="agentSettlementDto.prepaidMark != null">
            HAVING
            <if test="agentSettlementDto.prepaidMark">
                prepaidMark = 1
            </if>
            <if test="!agentSettlementDto.prepaidMark">
                prepaidMark = 0
            </if>
        </if>
        ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
        LEFT JOIN (
            SELECT a.fk_payable_plan_id, SUM(amount_actual) + SUM(service_fee_actual) AS settlementAmount FROM (
                SELECT
                    fk_payable_plan_id,
                    MAX( amount_actual ) AS amount_actual,
                    MAX( service_fee_actual ) AS service_fee_actual
                FROM
                ais_finance_center.r_payable_plan_settlement_installment
                WHERE
                    STATUS != 2
                    AND status_settlement != 0
                GROUP BY
                    fk_payable_plan_id,
                    status_settlement,
                    fk_agent_contract_account_id

                    UNION ALL

                    SELECT
                    fk_payable_plan_id,
                    SUM( amount_actual ) AS amount_actual,
                    SUM( service_fee_actual ) AS service_fee_actual
                FROM
                ais_finance_center.r_payable_plan_settlement_installment
                WHERE
                    status_settlement = 0
                GROUP BY
                    fk_payable_plan_id
                    ) AS a
                    GROUP BY a.fk_payable_plan_id
        ) AS commissionSettlement ON commissionSettlement.fk_payable_plan_id = mpp.id
        LEFT JOIN (
        SELECT
        mpp1.id,
        CASE
        MAX( mrp.receivable_amount ) - IFNULL(SUM( mrfi.amount_receivable ),0) - IFNULL(SUM( mrfi.amount_exchange_rate ),0)
        WHEN SUM( mrp.receivable_amount ) THEN
        0
        WHEN 0 THEN
        2 ELSE 1
        END AS status
        FROM
        ais_sale_center.m_payable_plan AS mpp1
        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.id = mpp1.fk_receivable_plan_id
        AND mrp.fk_type_key = 'm_student_insurance'
        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
        LEFT JOIN ais_finance_center.m_receipt_form AS mrf ON mrf.id = mrfi.fk_receipt_form_id
        WHERE
        mrp.STATUS = 1 AND mrf.status = 1
        <if test="agentSettlementDto.statusSettlement == 0 or agentSettlementDto.statusSettlement == 1">
            AND mrf.settlement_status = 1
        </if>
        GROUP BY mpp1.id
        )b ON b.id = mpp.id
        --      显示已付金额、差额
        LEFT JOIN (
        SELECT
        mpp2.id,
        IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
        mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp2
        LEFT JOIN (
        SELECT
        mpp3.id,
        IFNULL( SUM( mpfi2.amount_payable ), 0 ) + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp3
        LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
        AND mpp3.fk_type_key = 'm_student_insurance'
        LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
        WHERE
        mpp3.STATUS = 1 AND mpf2.status = 1
        GROUP BY
        mpp3.id
        ) mpp4 ON mpp4.id = mpp2.id
        ) c ON c.id = mpp.id

        <if test="agentSettlementDto.stepSubmissionStartTime!=null or agentSettlementDto.stepSubmissionEndTime!=null">
            INNER JOIN (
            SELECT
            mpp1.id, MAX(rppss.gmt_create) AS maxTime
            FROM
            ais_sale_center.m_agent AS a1
            INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.fk_agent_id = a1.id
            INNER JOIN ais_sale_center.m_payable_plan AS mpp1 ON mpp1.fk_type_target_id = msoi.id
            AND mpp1.fk_type_key = 'm_student_offer_item'
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp1.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id  AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}
            group by mpp1.id
            <if test="agentSettlementDto.stepSubmissionStartTime!=null or agentSettlementDto.stepSubmissionEndTime!=null">
                HAVING 1=1
                <if test="agentSettlementDto.stepSubmissionStartTime!=null">
                    AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionStartTime}, '%Y-%m-%d' )
                </if>
                <if test="agentSettlementDto.stepSubmissionEndTime!=null">
                    AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionEndTime},
                    '%Y-%m-%d' )
                </if>
            </if>
            )a2 ON a2.id = mpp.id
        </if>

        <where>
            <if test="agentSettlementDto.studentName != null and  agentSettlementDto.studentName !=''">
                AND
                (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.`name`,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.last_name,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.first_name,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(msi.`insurant_name`,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(msi.insurant_last_name,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(msi.insurant_first_name,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ',''))
            </if>
            <if test="agentIdList != null and agentIdList.size() > 0">
                AND a.id IN
                <foreach collection="agentIdList" index="index" item="agentId" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
            </if>
            <if test="agentSettlementDto.agentNameOrNum != null and  agentSettlementDto.agentNameOrNum !=''">
                AND (a.num =#{agentSettlementDto.agentNameOrNum} OR a.name LIKE CONCAT('%', #{agentSettlementDto.agentNameOrNum}, '%') )
            </if>
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
        </where>
        order by b.status DESC, msi.gmt_create desc, s.name ASC
    </select>


    <select id="agentSettlementConfirmationInsuranceList" resultType="com.get.financecenter.vo.AgentSettlementInsuranceVo">
<!--        SELECT-->
<!--        mpp.id,-->
<!--        s.NAME AS studentName,-->
<!--        msi.fk_area_country_id,-->
<!--        s.passport_num,-->
<!--        msi.insurance_num,-->
<!--        msi.insurance_start_time,-->
<!--        msi.insurance_end_time,-->
<!--        msi.gmt_create,-->
<!--        mpp.fk_currency_type_num,-->
<!--        mpp.payable_amount,-->
<!--        mpp.fk_type_key,-->
<!--        msi.gmt_create,-->
<!--        b.status-->
<!--        FROM-->
<!--        ais_sale_center.m_agent AS a-->
<!--        INNER JOIN ais_sale_center.r_payable_plan_settlement_agent AS rppsa ON rppsa.fk_agent_id = a.id AND a.is_active = 1-->
<!--        INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.id = rppsa.fk_payable_plan_id-->
<!--        AND mpp.fk_type_key = 'm_student_insurance'-->
<!--        AND mpp.STATUS = 1-->
<!--        AND mpp.status_settlement = #{agentSettlementDto.statusSettlement}-->
<!--        INNER JOIN ais_sale_center.m_student_insurance AS msi ON msi.id = mpp.fk_type_target_id-->
<!--        AND msi.STATUS = 1-->
<!--        INNER JOIN ais_sale_center.m_student AS s ON s.id = msi.fk_student_id AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}-->
<!--        LEFT JOIN (-->
<!--        SELECT-->
<!--        msi.id,-->
<!--        CASE-->
<!--        MAX( mrp.receivable_amount ) - IFNULL(SUM( mrfi.amount_receivable ),0) - IFNULL(SUM( mrfi.amount_exchange_rate ),0)-->
<!--        WHEN SUM( mrp.receivable_amount ) THEN-->
<!--        0-->
<!--        WHEN 0 THEN-->
<!--        2 ELSE 1-->
<!--        END AS status-->
<!--        FROM-->
<!--        ais_sale_center.m_student_insurance AS msi-->
<!--        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msi.id-->
<!--        AND mrp.fk_type_key = 'm_student_insurance'-->
<!--        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id-->
<!--        GROUP BY msi.id-->
<!--        )b ON b.id = msi.id-->

<!--        &#45;&#45; 只有有收款记录的才能显示-->
<!--        INNER JOIN (-->
<!--        SELECT DISTINCT msi2.id FROM ais_sale_center.m_student_insurance AS msi2-->
<!--        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msi2.id AND mrp.fk_type_key =-->
<!--        'm_student_insurance'-->
<!--        INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id-->
<!--        )z ON z.id = msi.id-->

<!--        <where>-->
<!--            <if test="agentSettlementDto.studentName != null and  agentSettlementDto.studentName !=''">-->
<!--                AND s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')-->
<!--            </if>-->
<!--            <if test="agentIdList != null">-->
<!--                AND a.id IN-->
<!--                <foreach collection="agentIdList" index="index" item="agentId" open="(" separator="," close=")">-->
<!--                    #{agentId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="agentSettlementDto.negativeFlag">-->
<!--                AND mpp.payable_amount <![CDATA[< ]]> 0-->
<!--            </if>-->
<!--        </where>-->
<!--        order by b.status DESC, msi.gmt_create desc, s.name ASC-->
    </select>
    <select id="agentSettlementAccommodationList"
            resultType="com.get.financecenter.vo.AgentSettlementAccommodationVo">
        SELECT
        mpp.id,
        s.NAME AS studentName,
        msa.id AS accommodationId,
        msa.fk_area_country_id,
        msa.apartment_name,
        msa.check_in_date,
        msa.check_out_date,
        msa.duration,
        msa.gmt_create,
        mpp.fk_currency_type_num,
        mpp.payable_amount,
        mpp.fk_type_key,
        c.paidAmount,
        <!--【应付金额】-【实际手续费金额】-【实际支付金额】-【已付金额】=【应付未付】-->
        c.differenceAmount - IFNULL(commissionSettlement.settlementAmount,0)  AS differenceAmount,
        msa.gmt_create,
        IF
        ( b.STATUS, b.STATUS, 0 ) AS STATUS,
        rppsi.settlementCreateTime,
        rppsi.settlementIds,
        rppsi.amount_actual,
        rppsi.serviceFeeActual,
        rppsi.account_export_time,
        rppsi.fk_agent_contract_account_id,
        rppsi.fk_currency_type_num AS accountCurrencyTypeNum,
        rppsi.prepaidMark,
        rppsi.is_roll_back AS rollBack,
        a.id AS agentId
        FROM
        ais_sale_center.m_payable_plan AS mpp
        INNER JOIN ais_sale_center.m_student_accommodation AS msa ON msa.id = mpp.fk_type_target_id AND mpp.fk_type_key = 'm_student_accommodation'
        <!-- <if test="agentSettlementDto.statusSettlement != null">
            <if test="agentSettlementDto.statusSettlement != 2">
                AND mpp.status_settlement = #{agentSettlementDto.statusSettlement}
            </if>
            <if test="agentSettlementDto.statusSettlement == 2">
                AND (mpp.status_settlement = #{agentSettlementDto.statusSettlement} OR (mpp.status_settlement IN (0,1) AND mpp.payable_amount <![CDATA[< ]]> 0))
            </if>
        </if> -->
        INNER JOIN ais_sale_center.m_agent AS a ON a.id = msa.fk_agent_id
        INNER JOIN ais_sale_center.m_student AS s ON s.id = msa.fk_student_id
        <if test="agentSettlementDto.statusSettlement == 2">
            <if test="agentSettlementDto.fkCompanyId != null and agentSettlementDto.fkCompanyId !=''">
                AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
            </if>
            <if test="agentSettlementDto.fkCompanyIds != null and agentSettlementDto.fkCompanyIds.size()>0">
                AND s.fk_company_id in
                <foreach collection="agentSettlementDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
        </if>
        <!--AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}-->
        -- 实际支付金额
        INNER JOIN (
        SELECT rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num, CASE WHEN rppsi1.status = 0 THEN SUM(rppsi1.amount_actual) ELSE MAX(rppsi1.amount_actual) END amount_actual,
        CASE WHEN rppsi1.status = 0 THEN SUM(rppsi1.service_fee_actual) ELSE MAX(rppsi1.service_fee_actual) END serviceFeeActual, MAX( CASE WHEN rppsi1.fk_receipt_form_item_id IS NULL OR  rppsi1.fk_receipt_form_item_id = 0 THEN rppsi1.gmt_create ELSE NULL END ) AS settlementCreateTime,
        CASE WHEN COUNT(fk_receipt_form_item_id) = COUNT(*) THEN 0 ELSE 1 END AS prepaidMark,
        MAX(rppsi1.account_export_time) AS account_export_time, MIN(rppsi1.is_roll_back) AS is_roll_back, GROUP_CONCAT(rppsi1.id) AS settlementIds
        FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.id = rppsi1.fk_receipt_form_item_id
        LEFT JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
        LEFT JOIN ais_finance_center.r_invoice_receivable_plan AS rirp ON rirp.id = rppsi1.fk_invoice_receivable_plan_id
        LEFT JOIN (
            SELECT mi.id, MIN(mrf2.receipt_date) AS receipt_date FROM ais_finance_center.m_invoice AS mi
            INNER JOIN ais_finance_center.r_receipt_form_invoice AS rrfi ON rrfi.fk_invoice_id = mi.id
            INNER JOIN ais_finance_center.m_receipt_form AS mrf2 ON mrf2.id = rrfi.fk_receipt_form_id
            GROUP BY mi.id
        ) AS invoice ON invoice.id = rppsi1.fk_invoice_id
        <where>
            AND rppsi1.status_settlement = #{agentSettlementDto.statusSettlement}
            <if test="agentSettlementDto.receiptStartTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
            <if test="agentSettlementDto.receiptEndTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
        </where>
        GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
        <if test="agentSettlementDto.prepaidMark != null">
            HAVING
            <if test="agentSettlementDto.prepaidMark">
                prepaidMark = 1
            </if>
            <if test="!agentSettlementDto.prepaidMark">
                prepaidMark = 0
            </if>
        </if>
        ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
        LEFT JOIN (
        SELECT
        mpp1.id,
        CASE
        MAX( mrp.receivable_amount ) - IFNULL(SUM( mrfi.amount_receivable ),0) - IFNULL(SUM( mrfi.amount_exchange_rate ),0)
        WHEN SUM( mrp.receivable_amount ) THEN
        0
        WHEN 0 THEN
        2 ELSE 1
        END AS status
        FROM
        ais_sale_center.m_payable_plan AS mpp1
        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.id = mpp1.fk_receivable_plan_id
        AND mrp.fk_type_key = 'm_student_accommodation'
        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
        LEFT JOIN ais_finance_center.m_receipt_form AS mrf ON mrf.id = mrfi.fk_receipt_form_id
        WHERE
        mrp.STATUS = 1 AND mrf.status = 1
        <if test="agentSettlementDto.statusSettlement == 0 or agentSettlementDto.statusSettlement == 1">
            AND mrf.settlement_status = 1
        </if>
        GROUP BY mpp1.id
        )b ON b.id = mpp.id
        LEFT JOIN (
            SELECT a.fk_payable_plan_id, SUM(amount_actual) + SUM(service_fee_actual) AS settlementAmount FROM (
                SELECT
                    fk_payable_plan_id,
                    MAX( amount_actual ) AS amount_actual,
                    MAX( service_fee_actual ) AS service_fee_actual
                FROM
                ais_finance_center.r_payable_plan_settlement_installment
                WHERE
                    STATUS != 2
                    AND status_settlement != 0
                GROUP BY
                    fk_payable_plan_id,
                    status_settlement,
                    fk_agent_contract_account_id

                    UNION ALL

                    SELECT
                    fk_payable_plan_id,
                    SUM( amount_actual ) AS amount_actual,
                    SUM( service_fee_actual ) AS service_fee_actual
                FROM
                ais_finance_center.r_payable_plan_settlement_installment
                WHERE
                    status_settlement = 0
                GROUP BY
                    fk_payable_plan_id
                    ) AS a
                    GROUP BY a.fk_payable_plan_id
        ) AS commissionSettlement ON commissionSettlement.fk_payable_plan_id = mpp.id
        --      显示已付金额、差额
        LEFT JOIN (
        SELECT
        mpp2.id,
        IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
        mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp2
        LEFT JOIN (
        SELECT
        mpp3.id,
        IFNULL( SUM( mpfi2.amount_payable ), 0 ) + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp3
        LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
        AND mpp3.fk_type_key = 'm_student_accommodation'
        LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
        WHERE
        mpp3.STATUS = 1 AND mpf2.status = 1
        GROUP BY
        mpp3.id
        ) mpp4 ON mpp4.id = mpp2.id
        ) c ON c.id = mpp.id

        <if test="agentSettlementDto.stepSubmissionStartTime!=null or agentSettlementDto.stepSubmissionEndTime!=null">
            INNER JOIN (
            SELECT
            mpp1.id, MAX(rppss.gmt_create) AS maxTime
            FROM
            ais_sale_center.m_agent AS a1
            INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.fk_agent_id = a1.id
            INNER JOIN ais_sale_center.m_payable_plan AS mpp1 ON mpp1.fk_type_target_id = msoi.id
            AND mpp1.fk_type_key = 'm_student_offer_item'
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp1.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id  AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}
            group by mpp1.id
            <if test="agentSettlementDto.stepSubmissionStartTime!=null or agentSettlementDto.stepSubmissionEndTime!=null">
                HAVING 1=1
                <if test="agentSettlementDto.stepSubmissionStartTime!=null">
                    AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionStartTime}, '%Y-%m-%d' )
                </if>
                <if test="agentSettlementDto.stepSubmissionEndTime!=null">
                    AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionEndTime},
                    '%Y-%m-%d' )
                </if>
            </if>
            )a2 ON a2.id = mpp.id
        </if>

        <where>
            <if test="agentSettlementDto.studentName != null and  agentSettlementDto.studentName !=''">
                AND
                (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.`name`,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.last_name,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.first_name,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ',''))
            </if>
            <if test="agentIdList != null and agentIdList.size() > 0">
                AND a.id IN
                <foreach collection="agentIdList" index="index" item="agentId" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
            </if>
            <if test="agentSettlementDto.agentNameOrNum != null and  agentSettlementDto.agentNameOrNum !=''">
                AND (a.num =#{agentSettlementDto.agentNameOrNum} OR a.name LIKE CONCAT('%', #{agentSettlementDto.agentNameOrNum}, '%') )
            </if>
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
        </where>
        order by b.status DESC, msa.gmt_create desc, s.name ASC
    </select>

    <select id="agentSettlementServiceFeeList"
            resultType="com.get.financecenter.vo.AgentSettlementServiceFeeVo">
        SELECT
        mpp.id,
        s.NAME,
        mssf.id AS serviceFeeId,
        mssf.fk_area_country_ids,
        mssf.gmt_create,
        CONCAT(ussft.type_name, "（" ,ussft.type_key,"）") AS studentServiceFeeTypeName,
        mpp.fk_currency_type_num,
        mpp.payable_amount,
        mpp.fk_type_key,
        c.paidAmount,
        <!--【应付金额】-【实际手续费金额】-【实际支付金额】-【已付金额】=【应付未付】-->
        c.differenceAmount - IFNULL(commissionSettlement.settlementAmount,0)  AS differenceAmount,
        IF
        ( b.STATUS, b.STATUS, 0 ) AS STATUS,
        rppsi.settlementCreateTime,
        rppsi.amount_actual,
        rppsi.serviceFeeActual,
        rppsi.account_export_time,
        rppsi.settlementIds,
        rppsi.fk_agent_contract_account_id,
        rppsi.fk_currency_type_num AS accountCurrencyTypeNum,
        rppsi.prepaidMark,
        rppsi.is_roll_back AS rollBack,
        a.id AS agentId
        FROM
        ais_sale_center.m_payable_plan AS mpp
        INNER JOIN ais_sale_center.m_student_service_fee AS mssf ON mssf.id = mpp.fk_type_target_id AND mpp.fk_type_key = 'm_student_service_fee'
        INNER JOIN ais_sale_center.m_agent AS a ON a.id = mssf.fk_agent_id
        INNER JOIN ais_sale_center.m_student AS s ON s.id = mssf.fk_student_id
        INNER JOIN ais_sale_center.u_student_service_fee_type AS ussft ON ussft.id = mssf.fk_student_service_fee_type_id
        <if test="agentSettlementDto.statusSettlement == 2">
            <if test="agentSettlementDto.fkCompanyId != null and agentSettlementDto.fkCompanyId !=''">
                AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
            </if>
            <if test="agentSettlementDto.fkCompanyIds != null and agentSettlementDto.fkCompanyIds.size()>0">
                AND s.fk_company_id in
                <foreach collection="agentSettlementDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
        </if>
        <!--AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}-->
        -- 实际支付金额
        INNER JOIN (
        SELECT rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num, CASE WHEN rppsi1.status = 0 THEN SUM(rppsi1.amount_actual) ELSE MAX(rppsi1.amount_actual) END amount_actual,
        CASE WHEN rppsi1.status = 0 THEN SUM(rppsi1.service_fee_actual) ELSE MAX(rppsi1.service_fee_actual) END serviceFeeActual, MAX( CASE WHEN rppsi1.fk_receipt_form_item_id IS NULL OR  rppsi1.fk_receipt_form_item_id = 0 THEN rppsi1.gmt_create ELSE NULL END ) AS settlementCreateTime,
        CASE WHEN COUNT(fk_receipt_form_item_id) = COUNT(*) THEN 0 ELSE 1 END AS prepaidMark,
        MAX(rppsi1.account_export_time) AS account_export_time, MIN(rppsi1.is_roll_back) AS is_roll_back, GROUP_CONCAT(rppsi1.id) AS settlementIds
        FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.id = rppsi1.fk_receipt_form_item_id
        LEFT JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
        LEFT JOIN ais_finance_center.r_invoice_receivable_plan AS rirp ON rirp.id = rppsi1.fk_invoice_receivable_plan_id
        LEFT JOIN (
            SELECT mi.id, MIN(mrf2.receipt_date) AS receipt_date FROM ais_finance_center.m_invoice AS mi
            INNER JOIN ais_finance_center.r_receipt_form_invoice AS rrfi ON rrfi.fk_invoice_id = mi.id
            INNER JOIN ais_finance_center.m_receipt_form AS mrf2 ON mrf2.id = rrfi.fk_receipt_form_id
            GROUP BY mi.id
        ) AS invoice ON invoice.id = rppsi1.fk_invoice_id
        <where>
            AND rppsi1.status_settlement = #{agentSettlementDto.statusSettlement}
            <if test="agentSettlementDto.receiptStartTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
            <if test="agentSettlementDto.receiptEndTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
        </where>
        GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
        <if test="agentSettlementDto.prepaidMark != null">
            HAVING
            <if test="agentSettlementDto.prepaidMark">
                prepaidMark = 1
            </if>
            <if test="!agentSettlementDto.prepaidMark">
                prepaidMark = 0
            </if>
        </if>
        ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
        LEFT JOIN (
        SELECT
        mpp1.id,
        CASE
        MAX( mrp.receivable_amount ) - IFNULL(SUM( mrfi.amount_receivable ),0) - IFNULL(SUM( mrfi.amount_exchange_rate ),0)
        WHEN SUM( mrp.receivable_amount ) THEN
        0
        WHEN 0 THEN
        2 ELSE 1
        END AS status
        FROM
        ais_sale_center.m_payable_plan AS mpp1
        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.id = mpp1.fk_receivable_plan_id
        AND mrp.fk_type_key = 'm_student_service_fee'
        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
        LEFT JOIN ais_finance_center.m_receipt_form AS mrf ON mrf.id = mrfi.fk_receipt_form_id
        WHERE
        mrp.STATUS = 1 AND mrf.status = 1
        <if test="agentSettlementDto.statusSettlement == 0 or agentSettlementDto.statusSettlement == 1">
            AND mrf.settlement_status = 1
        </if>
        GROUP BY mpp1.id
        )b ON b.id = mpp.id
        LEFT JOIN (
            SELECT a.fk_payable_plan_id, SUM(amount_actual) + SUM(service_fee_actual) AS settlementAmount FROM (
                SELECT
                    fk_payable_plan_id,
                    MAX( amount_actual ) AS amount_actual,
                    MAX( service_fee_actual ) AS service_fee_actual
                FROM
                ais_finance_center.r_payable_plan_settlement_installment
                WHERE
                    STATUS != 2
                    AND status_settlement != 0
                GROUP BY
                    fk_payable_plan_id,
                    status_settlement,
                    fk_agent_contract_account_id

                    UNION ALL

                    SELECT
                    fk_payable_plan_id,
                    SUM( amount_actual ) AS amount_actual,
                    SUM( service_fee_actual ) AS service_fee_actual
                FROM
                ais_finance_center.r_payable_plan_settlement_installment
                WHERE
                    status_settlement = 0
                GROUP BY
                    fk_payable_plan_id
                    ) AS a
                    GROUP BY a.fk_payable_plan_id
        ) AS commissionSettlement ON commissionSettlement.fk_payable_plan_id = mpp.id
        --      显示已付金额、差额
        LEFT JOIN (
        SELECT
        mpp2.id,
        IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
        mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp2
        LEFT JOIN (
        SELECT
        mpp3.id,
        IFNULL( SUM( mpfi2.amount_payable ), 0 ) + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp3
        LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
        AND mpp3.fk_type_key = 'm_student_service_fee'
        LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
        WHERE
        mpp3.STATUS = 1 AND mpf2.status = 1
        GROUP BY
        mpp3.id
        ) mpp4 ON mpp4.id = mpp2.id
        ) c ON c.id = mpp.id

        <if test="agentSettlementDto.stepSubmissionStartTime!=null or agentSettlementDto.stepSubmissionEndTime!=null">
            INNER JOIN (
            SELECT
            mpp1.id, MAX(rppss.gmt_create) AS maxTime
            FROM
            ais_sale_center.m_agent AS a1
            INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.fk_agent_id = a1.id
            INNER JOIN ais_sale_center.m_payable_plan AS mpp1 ON mpp1.fk_type_target_id = msoi.id
            AND mpp1.fk_type_key = 'm_student_offer_item'
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp1.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id  AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}
            group by mpp1.id
            <if test="agentSettlementDto.stepSubmissionStartTime!=null or agentSettlementDto.stepSubmissionEndTime!=null">
                HAVING 1=1
                <if test="agentSettlementDto.stepSubmissionStartTime!=null">
                    AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionStartTime}, '%Y-%m-%d' )
                </if>
                <if test="agentSettlementDto.stepSubmissionEndTime!=null">
                    AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionEndTime},
                    '%Y-%m-%d' )
                </if>
            </if>
            )a2 ON a2.id = mpp.id
        </if>

        <where>
            <if test="agentSettlementDto.studentName != null and  agentSettlementDto.studentName !=''">
                AND
                (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.`name`,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.last_name,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.first_name,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ',''))
            </if>
            <if test="agentIdList != null and agentIdList.size() > 0">
                AND a.id IN
                <foreach collection="agentIdList" index="index" item="agentId" open="(" separator="," close=")">
                    #{agentId}
                </foreach>
            </if>
            <if test="agentSettlementDto.agentNameOrNum != null and  agentSettlementDto.agentNameOrNum !=''">
                AND (a.num =#{agentSettlementDto.agentNameOrNum} OR a.name LIKE CONCAT('%', #{agentSettlementDto.agentNameOrNum}, '%') )
            </if>
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
        </where>
        order by b.status DESC, mssf.gmt_create desc, s.name ASC
    </select>

    <select id="agentSettlementConfirmationAccommodationList"
            resultType="com.get.financecenter.vo.AgentSettlementAccommodationVo">
<!--        SELECT-->
<!--        mpp.id,-->
<!--        s.NAME,-->
<!--        msa.fk_area_country_id,-->
<!--        msa.apartment_name,-->
<!--        msa.check_in_date,-->
<!--        msa.check_out_date,-->
<!--        msa.duration,-->
<!--        msa.gmt_create,-->
<!--        mpp.fk_currency_type_num,-->
<!--        mpp.payable_amount,-->
<!--        mpp.fk_type_key,-->
<!--        msa.gmt_create,-->
<!--        b.status-->
<!--        FROM-->
<!--        ais_sale_center.m_agent AS a-->
<!--        INNER JOIN ais_sale_center.r_payable_plan_settlement_agent AS rppsa ON rppsa.fk_agent_id = a.id AND a.is_active = 1-->
<!--        INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.id = rppsa.fk_payable_plan_id-->
<!--        AND mpp.fk_type_key = 'm_student_accommodation'-->
<!--        AND mpp.STATUS = 1-->
<!--        AND mpp.status_settlement IN-->
<!--        <foreach collection="statusSettlementList" item="statusSettlement" index="index" open="(" separator="," close=")">-->
<!--            #{statusSettlement}-->
<!--        </foreach>-->
<!--        INNER JOIN ais_sale_center.m_student_accommodation AS msa ON msa.id = mpp.fk_type_target_id-->
<!--        AND msa.STATUS = 1-->
<!--        INNER JOIN ais_sale_center.m_student AS s ON s.id = msa.fk_student_id AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}-->
<!--        LEFT JOIN (-->
<!--        SELECT-->
<!--        msa.id,-->
<!--        CASE-->
<!--        MAX( mrp.receivable_amount ) - IFNULL(SUM( mrfi.amount_receivable ),0) - IFNULL(SUM( mrfi.amount_exchange_rate ),0)-->
<!--        WHEN SUM( mrp.receivable_amount ) THEN-->
<!--        0-->
<!--        WHEN 0 THEN-->
<!--        2 ELSE 1-->
<!--        END AS status-->
<!--        FROM-->
<!--        ais_sale_center.m_student_accommodation AS msa-->
<!--        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msa.id-->
<!--        AND mrp.fk_type_key = 'm_student_accommodation'-->
<!--        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id-->
<!--        GROUP BY msa.id-->
<!--        )b ON b.id = msa.id-->
<!--        &#45;&#45; 只有有收款记录的才能显示-->
<!--        INNER JOIN (-->
<!--        SELECT DISTINCT msa2.id FROM ais_sale_center.m_student_accommodation AS msa2-->
<!--        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msa2.id AND mrp.fk_type_key =-->
<!--        'm_student_accommodation'-->
<!--        INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id-->
<!--        )z ON z.id = msa.id-->

<!--        <where>-->
<!--            <if test="agentSettlementDto.studentName != null and  agentSettlementDto.studentName !=''">-->
<!--                AND s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')-->
<!--            </if>-->
<!--            <if test="agentIdList != null">-->
<!--                AND a.id IN-->
<!--                <foreach collection="agentIdList" index="index" item="agentId" open="(" separator="," close=")">-->
<!--                    #{agentId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--            <if test="agentSettlementDto.negativeFlag">-->
<!--                AND mpp.payable_amount <![CDATA[< ]]> 0-->
<!--            </if>-->
<!--        </where>-->
<!--        order by b.status DESC, msa.gmt_create desc, s.name ASC-->
    </select>

    <select id="agentSettlementAccommodationConfirmationList"
            resultType="com.get.financecenter.vo.AgentSettlementAccommodationVo">
<!--        SELECT-->
<!--        mpp.id,-->
<!--        s.NAME,-->
<!--        msa.fk_area_country_id,-->
<!--        msa.apartment_name,-->
<!--        msa.check_in_date,-->
<!--        msa.check_out_date,-->
<!--        msa.duration,-->
<!--        msa.gmt_create,-->
<!--        mpp.fk_currency_type_num,-->
<!--        mpp.payable_amount,-->
<!--        mpp.fk_type_key,-->
<!--        msa.gmt_create,-->
<!--        b.status-->
<!--        FROM-->
<!--        ais_sale_center.m_agent AS a-->
<!--        INNER JOIN ais_sale_center.r_payable_plan_settlement_agent AS rppsa ON rppsa.fk_agent_id = a.id AND a.is_active = 1-->
<!--        INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.id = rppsa.fk_payable_plan_id-->
<!--        AND mpp.fk_type_key = 'm_student_accommodation'-->
<!--        AND mpp.STATUS = 1-->
<!--        AND mpp.status_settlement IN-->
<!--        <foreach collection="statusSettlementList" item="statusSettlement" index="index" open="(" separator="," close=")">-->
<!--            #{statusSettlement}-->
<!--        </foreach>-->
<!--        INNER JOIN ais_sale_center.m_student_accommodation AS msa ON msa.id = mpp.fk_type_target_id-->
<!--        AND msa.STATUS = 1-->
<!--        INNER JOIN ais_sale_center.m_student AS s ON s.id = msa.fk_student_id AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}-->
<!--        LEFT JOIN (-->
<!--        SELECT-->
<!--        msa.id,-->
<!--        CASE-->
<!--        MAX( mrp.receivable_amount ) - IFNULL(SUM( mrfi.amount_receivable ),0) - IFNULL(SUM( mrfi.amount_exchange_rate ),0)-->
<!--        WHEN SUM( mrp.receivable_amount ) THEN-->
<!--        0-->
<!--        WHEN 0 THEN-->
<!--        2 ELSE 1-->
<!--        END AS status-->
<!--        FROM-->
<!--        ais_sale_center.m_student_accommodation AS msa-->
<!--        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msa.id-->
<!--        AND mrp.fk_type_key = 'm_student_accommodation'-->
<!--        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id-->
<!--        GROUP BY msa.id-->
<!--        )b ON b.id = msa.id-->
<!--        &#45;&#45; 只有有收款记录的才能显示-->
<!--        INNER JOIN (-->
<!--        SELECT DISTINCT msa2.id FROM ais_sale_center.m_student_accommodation AS msa2-->
<!--        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msa2.id AND mrp.fk_type_key =-->
<!--        'm_student_accommodation'-->
<!--        INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id-->
<!--        )z ON z.id = msa.id-->

<!--        <where>-->
<!--            <if test="agentSettlementDto.studentName != null and  agentSettlementDto.studentName !=''">-->
<!--                AND s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')-->
<!--            </if>-->
<!--            <if test="agentIdList != null">-->
<!--                AND a.id IN-->
<!--                <foreach collection="agentIdList" index="index" item="agentId" open="(" separator="," close=")">-->
<!--                    #{agentId}-->
<!--                </foreach>-->
<!--            </if>-->
<!--        </where>-->
<!--        order by b.status DESC, msa.gmt_create desc, s.name ASC-->
    </select>

    <select id="agentSettlementItemIntermediaryExport"
            resultType="com.get.financecenter.excelmodel.AgentSettlementIntermediaryModel">
        SELECT
        mpp.id,
        s.NAME AS studentName,
        uac.num AS countryNum,
        i.NAME AS fkInstitutionName,
        DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') AS openTime,
        IF
        ( msoi.fk_institution_course_id!=-1, mic.NAME, msoi.old_course_custom_name ) AS fkCourseName,
        mpp.fk_currency_type_num AS payablePlanCurrency,
        mpp.tuition_amount,
        CASE WHEN mpp.tuition_amount > 0 AND mpp.commission_rate IS NOT NULL THEN rppsi.amount_actual / (mpp.commission_rate/100) ELSE mpp.tuition_amount END AS tuitionAmountIae,
        mpp.commission_rate,
        mpp.split_rate,
        mpp.payable_amount,
        IF(b.paid, b.paid, 0) AS paid,
        rppsi.amount_actual AS outstanding
        FROM
        ais_sale_center.m_agent AS a
        INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.fk_agent_id = a.id
        INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = msoi.id
        AND mpp.fk_type_key = 'm_student_offer_item'
        AND mpp.STATUS = 1
        INNER JOIN ais_sale_center.m_student AS s ON s.id = msoi.fk_student_id
        INNER JOIN ais_institution_center.u_area_country AS uac ON uac.id = msoi.fk_area_country_id
        INNER JOIN ais_institution_center.m_institution AS i ON i.id = msoi.fk_institution_id
        LEFT JOIN ais_institution_center.m_institution_course AS mic ON mic.id = msoi.fk_institution_course_id
        -- 实际支付金额
        INNER JOIN (
        SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual
        FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
        WHERE rppsi1.status = 1
        AND
        <foreach collection="agentSettlementBatchExportVoList" item="agentSettlementBatchExportDto" index="index" open="(" separator=" or " close=")">
            ( rppsi1.fk_payable_plan_id = #{agentSettlementBatchExportDto.payablePlanId} AND rppsi1.status_settlement = #{agentSettlementBatchExportDto.statusSettlement} AND rppsi1.fk_agent_contract_account_id = #{agentSettlementBatchExportDto.fkAgentContractAccountId} )
        </foreach>
        GROUP BY rppsi1.fk_payable_plan_id
        ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
        -- 计算已付金额
        LEFT JOIN (
            SELECT
            mpp2.id,
            IFNULL( mpp4.paidAmount, 0 ) AS paid
            FROM
            ais_sale_center.m_payable_plan AS mpp2
            LEFT JOIN (
            SELECT
            mpp3.id,
            IFNULL( SUM( mpfi2.amount_payable ), 0 ) + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
            FROM
            ais_sale_center.m_payable_plan AS mpp3
            LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
            AND mpp3.fk_type_key = 'm_student_offer_item'
            LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
            WHERE
            mpp3.STATUS = 1 AND mpf2.status = 1
            AND mpf2.STATUS = 1
            GROUP BY
            mpp3.id
            ) mpp4 ON mpp4.id = mpp2.id
        ) b ON b.id = mpp.id
        ORDER BY
        msoi.defer_opening_time ASC,
        s.NAME
    </select>
    <select id="agentSettlementItemTransferExport"
            resultType="com.get.financecenter.excelmodel.AgentSettlementTransferModel">
        SELECT
        mpp.id,
        a.NAME AS agentName,
        maca.bank_account,
        a.id as agentId,
        CONCAT(maca.bank_name, IFNULL(maca.bank_branch_name, "")) AS bankName,
        maca.bank_account_num,
        mpp.fk_currency_type_num AS payablePlanCurrency,
--         mpp.payable_amount,
        rppsi.amount_actual AS payableAmount,
        CONCAT(
        "[",
        uac.num,
        "] ",
        uac.`name`,
        IF
        (
        uac.name_chn IS NULL
        OR uac.name_chn = '',
        '',
        CONCAT( "（", uac.name_chn, "）" ))) AS countryName,
        CONCAT(
        i.NAME,
        IF
        (
        i.name_chn IS NULL
        OR i.name_chn = '',
        '',
        CONCAT( "（", i.name_chn, "）" ))) AS institutionName,
        s.`name` AS studentName,
        IF(msoi.fk_institution_course_id=-1,msoi.old_course_custom_name,
        CONCAT(
        mic.NAME,
        IF
        (
        mic.name_chn IS NULL
        OR mic.name_chn = '',
        '',
        CONCAT( "（", mic.name_chn, "）" )))) AS courseName,
        msoi.tuition_amount,
        mpp.commission_rate as rebateRatio,
        <!-- a.legal_person as legalPersonName,
        a.id_card_num as idCard,
        a.nature,
        a.tax_code as socialCreditCode, -->
        maca.account_card_type as accountCardType,
        maca.bank_address as bankAddress
        FROM
        ais_sale_center.m_payable_plan AS mpp
        INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id
        AND mpp.fk_type_key = 'm_student_offer_item'
        AND mpp.STATUS = 1
        INNER JOIN ais_sale_center.m_agent AS a ON a.id = msoi.fk_agent_id
        AND a.is_active = 1
        INNER JOIN ais_sale_center.m_student AS s ON s.id = msoi.fk_student_id
        INNER JOIN ais_institution_center.u_area_country AS uac ON uac.id = msoi.fk_area_country_id
        INNER JOIN ais_institution_center.m_institution AS i ON i.id = msoi.fk_institution_id
        LEFT JOIN ais_institution_center.m_institution_course AS mic ON mic.id = msoi.fk_institution_course_id
        -- 实际支付金额
        INNER JOIN (
        SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual, rppsi1.fk_agent_contract_account_id
        FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
        WHERE rppsi1.status = 1
        AND rppsi1.fk_payable_plan_id IN
        <foreach collection="payablePlanIdList" item="payablePlanId" index="index" open="(" separator="," close=")">
            #{payablePlanId}
        </foreach>
        AND rppsi1.fk_currency_type_num = #{currency}
        GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
        ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
        LEFT JOIN ais_sale_center.m_agent_contract_account AS maca ON maca.id = rppsi.fk_agent_contract_account_id
    </select>
    <select id="agentSettlementItemInsuranceExport"
            resultType="com.get.financecenter.excelmodel.AgentSettlementInsuranceModel">
        SELECT
        CASE
        WHEN msi.type = 1
        THEN msi.insurant_name
        ELSE s.NAME END AS studentName,
        mpp.id,
        mpp.fk_currency_type_num AS payablePlanCurrency,
        mpp.tuition_amount,
        mpp.commission_rate,
        mpp.payable_amount,
        CONCAT(uac.`name`,IF(uac.name_chn is null or uac.name_chn = '','',CONCAT("（",uac.name_chn,"）"))) AS countryName,
        IF(b.paid, b.paid, 0) AS paid,
        rppsi.amount_actual AS outstanding
        FROM
        ais_sale_center.m_agent AS a
        INNER JOIN ais_sale_center.m_student_insurance AS msi ON msi.fk_agent_id = a.id AND msi.status = 1
        INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = msi.id
        AND mpp.fk_type_key = 'm_student_insurance'
        AND mpp.STATUS = 1

        --         ais_sale_center.m_payable_plan AS mpp
        --         INNER JOIN ais_sale_center.m_student_insurance AS msi ON msi.id = mpp.fk_type_target_id
        --         AND mpp.fk_type_key = 'm_student_insurance'
        --         AND mpp.STATUS = 1
        --         AND mpp.status_settlement = 1
        --         INNER JOIN ais_sale_center.m_agent AS a ON a.id = msi.fk_agent_id
        --         AND a.is_active = 1
        --         INNER JOIN ais_sale_center.r_payable_plan_settlement_agent_account AS rppsaa ON rppsaa.fk_payable_plan_id =
        --         mpp.id
        --         INNER JOIN ais_sale_center.m_agent_contract_account AS maca
        --         ON maca.id = rppsaa.fk_agent_contract_account_id
        INNER JOIN ais_sale_center.m_student AS s ON s.id = msi.fk_student_id
        INNER JOIN ais_institution_center.u_area_country AS uac ON uac.id = msi.fk_area_country_id
        -- 实际支付金额
        INNER JOIN (
        SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual
        FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
        WHERE rppsi1.status = 1
        AND
        <foreach collection="agentSettlementBatchExportVoList" item="agentSettlementBatchExportDto" index="index" open="(" separator=" or " close=")">
            ( rppsi1.fk_payable_plan_id = #{agentSettlementBatchExportDto.payablePlanId} AND rppsi1.status_settlement = #{agentSettlementBatchExportDto.statusSettlement} AND rppsi1.fk_agent_contract_account_id = #{agentSettlementBatchExportDto.fkAgentContractAccountId} )
        </foreach>
        AND rppsi1.fk_currency_type_num = #{currency}
        GROUP BY rppsi1.fk_payable_plan_id
        ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
        -- 计算已付金额
        LEFT JOIN (
        SELECT
        mpp2.id,
        IFNULL( mpp4.paidAmount, 0 ) AS paid
        FROM
        ais_sale_center.m_payable_plan AS mpp2
        LEFT JOIN (
        SELECT
        mpp3.id,
        IFNULL( SUM( mpfi2.amount_payable ), 0 ) + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp3
        LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
        AND mpp3.fk_type_key = 'm_student_insurance'
        LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
        WHERE
        mpp3.STATUS = 1 AND mpf2.STATUS = 1
        GROUP BY
        mpp3.id
        ) mpp4 ON mpp4.id = mpp2.id
        ) b ON b.id = mpp.id
    </select>
    <select id="agentSettlementAccommodationExport"
            resultType="com.get.financecenter.excelmodel.AgentSettlementAccommodationModel">
        SELECT
        mpp.id,
        s.NAME AS studentName,
        CONCAT(uac.`name`,IF(uac.name_chn is null or uac.name_chn = '','',CONCAT("（",uac.name_chn,"）"))) AS countryName,
        msa.fk_area_country_id,
        msa.apartment_name,
        DATE_FORMAT(msa.check_in_date,'%Y-%m-%d') AS checkInDate,
        msa.duration,
        mpp.fk_currency_type_num AS payablePlanCurrency,
        mpp.payable_amount,
        b.paid,
        mpp.payable_amount,
        rppsi.amount_actual AS outstanding
        FROM
        ais_sale_center.m_agent AS a
        INNER JOIN ais_sale_center.m_student_accommodation AS msa ON msa.fk_agent_id = a.id
        INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = msa.id
        AND mpp.fk_type_key = 'm_student_accommodation'
        AND mpp.STATUS = 1
        --         INNER JOIN ais_sale_center.r_payable_plan_settlement_agent_account AS rppsaa ON rppsaa.fk_payable_plan_id =
        --         mpp.id
        --         INNER JOIN ais_sale_center.m_agent_contract_account AS maca
        --         ON maca.id = rppsaa.fk_agent_contract_account_id
        INNER JOIN ais_sale_center.m_student AS s ON s.id = msa.fk_student_id
        INNER JOIN ais_institution_center.u_area_country AS uac ON uac.id = msa.fk_area_country_id
        -- 实际支付金额
        INNER JOIN (
        SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual
        FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
        WHERE rppsi1.status = 1
        AND
            <foreach collection="agentSettlementBatchExportVoList" item="agentSettlementBatchExportDto" index="index" open="(" separator=" or " close=")">
                ( rppsi1.fk_payable_plan_id = #{agentSettlementBatchExportDto.payablePlanId} AND rppsi1.status_settlement = #{agentSettlementBatchExportDto.statusSettlement} AND rppsi1.fk_agent_contract_account_id = #{agentSettlementBatchExportDto.fkAgentContractAccountId} )
            </foreach>
        AND rppsi1.fk_currency_type_num = #{currency}
        GROUP BY rppsi1.fk_payable_plan_id
        ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
        -- 计算已付金额
        LEFT JOIN (
        SELECT
        mpp2.id,
        IFNULL( mpp4.paidAmount, 0 ) AS paid
        FROM
        ais_sale_center.m_payable_plan AS mpp2
        LEFT JOIN (
        SELECT
        mpp3.id,
        IFNULL( SUM( mpfi2.amount_payable ), 0 ) + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp3
        LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
        AND mpp3.fk_type_key = 'm_student_accommodation'
        LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
        WHERE
        mpp3.STATUS = 1 AND mpf2.STATUS = 1
        GROUP BY
        mpp3.id
        ) mpp4 ON mpp4.id = mpp2.id
        ) b ON b.id = mpp.id
    </select>
    <select id="agentSettlementServiceFeeExport"
            resultType="com.get.financecenter.excelmodel.AgentSettlementServiceFeeModel">
        SELECT
        mpp.id,
        s.NAME AS studentName,
        CONCAT(ussft.`type_name`,IF(ussft.type_key is null or ussft.type_key = '','',CONCAT("（",ussft.type_key,"）"))) AS serviceFeeTypeName,
        mssf.fk_area_country_ids,
        mssf.num,
        mssf.fk_currency_type_num,
        mssf.amount,
        mssf.remark,
        mssf.fk_student_service_fee_type_id,
        (SELECT GROUP_CONCAT(name) FROM ais_institution_center.u_area_country WHERE FIND_IN_SET(id,mssf.fk_area_country_ids)) AS countryStr,
        mpp.fk_currency_type_num AS payablePlanCurrency,
        mpp.payable_amount,
        b.paid,
        mpp.payable_amount,
        rppsi.amount_actual AS outstanding
        FROM
        ais_sale_center.m_agent AS a
        INNER JOIN ais_sale_center.m_student_service_fee AS mssf ON mssf.fk_agent_id = a.id
        INNER JOIN ais_sale_center.u_student_service_fee_type AS ussft ON ussft.id = mssf.fk_student_service_fee_type_id
        INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = mssf.id
        AND mpp.fk_type_key = 'm_student_service_fee'
        AND mpp.STATUS = 1
        INNER JOIN ais_sale_center.m_student AS s ON s.id = mssf.fk_student_id
        -- 实际支付金额
        INNER JOIN (
        SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual
        FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
        WHERE rppsi1.status = 1
        AND
        <foreach collection="agentSettlementBatchExportVoList" item="agentSettlementBatchExportDto" index="index" open="(" separator=" or " close=")">
            ( rppsi1.fk_payable_plan_id = #{agentSettlementBatchExportDto.payablePlanId} AND rppsi1.status_settlement = #{agentSettlementBatchExportDto.statusSettlement} AND rppsi1.fk_agent_contract_account_id = #{agentSettlementBatchExportDto.fkAgentContractAccountId} )
        </foreach>
        AND rppsi1.fk_currency_type_num = #{currency}
        GROUP BY rppsi1.fk_payable_plan_id
        ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
        -- 计算已付金额
        LEFT JOIN (
        SELECT
        mpp2.id,
        IFNULL( mpp4.paidAmount, 0 ) AS paid
        FROM
        ais_sale_center.m_payable_plan AS mpp2
        LEFT JOIN (
        SELECT
        mpp3.id,
        IFNULL( SUM( mpfi2.amount_payable ), 0 ) + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
        FROM
        ais_sale_center.m_payable_plan AS mpp3
        LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
        AND mpp3.fk_type_key = 'm_student_service_fee'
        LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
        WHERE
        mpp3.STATUS = 1 AND mpf2.STATUS = 1
        GROUP BY
        mpp3.id
        ) mpp4 ON mpp4.id = mpp2.id
        ) b ON b.id = mpp.id

    </select>
    <select id="getAmountPaidByPayablePlanId" resultType="java.math.BigDecimal">
        SELECT
            IFNULL(SUM(a.amountPaid), 0)
        FROM
            (
                SELECT
                    IFNULL( SUM( mpfi.amount_payable + mpfi.amount_exchange_rate ), 0 ) AS amountPaid
                FROM
                    ais_finance_center.m_payment_form_item AS mpfi
                        INNER JOIN m_payment_form AS mpf ON mpf.id = mpfi.fk_payment_form_id
                WHERE
                    mpfi.fk_payable_plan_id = #{payablePlanId}
                  AND mpf.STATUS = 1

            <!--  UNION ALL

                SELECT
                    SUM( amount_actual ) + SUM( service_fee_actual ) AS amountPaid
                FROM
                    ais_finance_center.r_payable_plan_settlement_installment
                WHERE
                    fk_payable_plan_id = #{payablePlanId}
                  AND STATUS = 0
                GROUP BY
                    fk_payable_plan_id

                UNION ALL

                SELECT
                    MAX( amount_actual ) + MAX( service_fee_actual ) AS amountPaid
                FROM
                    ais_finance_center.r_payable_plan_settlement_installment
                WHERE
                    fk_payable_plan_id = #{payablePlanId}
                  AND STATUS = 1
                GROUP BY
                    fk_payable_plan_id -->
            )a

    </select>
    <select id="getAlreadyPayByPlanIds" resultType="com.get.financecenter.vo.AlreadyPayVo">
        SELECT i.id,
               f.fk_currency_type_num            alreadyPayCurrency,
               i.amount_payment                  alreadyPayAmount,
               IFNULL(i.amount_exchange_rate, 0) alreadyExchangeRate,
               i.amount_payable AS               amountPayable,
               i.fk_payable_plan_id as fkPayablePlanId
        from m_payment_form_item i
                 left join m_payment_form f on i.fk_payment_form_id = f.id
        where i.fk_payable_plan_id in
              <foreach collection="planIds" item="planId" index="index" open="(" separator="," close=")">
                  #{planId}
              </foreach>
          and f.status = 1

    </select>
    <select id="getPayFormItemCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM m_payment_form_item
        WHERE fk_payable_plan_id IN
        <foreach collection="planIds" item="pid" open="(" close=")" separator=",">
            #{pid}
        </foreach>
        LIMIT 1
    </select>

    <select id="getPaymentFormItemList" resultType="com.get.financecenter.vo.PaymentFormItemCountVo">
        SELECT res.fk_student_id AS fkStudentId, COUNT(*) AS num FROM (
        <trim suffixOverrides="UNION ALL">
            <trim suffix="UNION ALL">
                <if test="payablePlanTypeKeyDto.mStudentOfferItem != null and payablePlanTypeKeyDto.mStudentOfferItem">
                    SELECT a.*, c.fk_student_id
                    FROM ais_finance_center.m_payment_form_item a
                    INNER JOIN ais_sale_center.m_payable_plan b
                    ON a.fk_payable_plan_id = b.id
                    INNER JOIN ais_sale_center.m_student_offer_item c
                    ON b.fk_type_key = 'm_student_offer_item' AND b.fk_type_target_id = c.id
                    WHERE 1 = 1
                    <if test="studentIds != null and studentIds.size() > 0">
                        AND c.fk_student_id IN
                        <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
                            #{studentId}
                        </foreach>
                    </if>
                </if>
            </trim>
            <trim suffix="UNION ALL">
                <if test="payablePlanTypeKeyDto.mStudentInsurance != null and payablePlanTypeKeyDto.mStudentInsurance">
                    SELECT a.*, c.fk_student_id
                    FROM ais_finance_center.m_payment_form_item a
                    INNER JOIN ais_sale_center.m_payable_plan b
                    ON a.fk_payable_plan_id = b.id
                    INNER JOIN ais_sale_center.m_student_insurance c
                    ON b.fk_type_key = 'm_student_insurance' AND b.fk_type_target_id = c.id
                    WHERE 1 = 1
                    <if test="studentIds != null and studentIds.size() > 0">
                        AND c.fk_student_id IN
                        <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
                            #{studentId}
                        </foreach>
                    </if>
                </if>
            </trim>
            <trim suffix="UNION ALL">
                <if test="payablePlanTypeKeyDto.mStudentAccommodation != null and payablePlanTypeKeyDto.mStudentAccommodation">
                    SELECT a.*, c.fk_student_id
                    FROM ais_finance_center.m_payment_form_item a
                    INNER JOIN ais_sale_center.m_payable_plan b
                    ON a.fk_payable_plan_id = b.id
                    INNER JOIN ais_sale_center.m_student_accommodation c
                    ON b.fk_type_key = 'm_student_accommodation' AND b.fk_type_target_id = c.id
                    WHERE 1 = 1
                    <if test="studentIds != null and studentIds.size() > 0">
                        AND c.fk_student_id IN
                        <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
                            #{studentId}
                        </foreach>
                    </if>
                </if>
            </trim>
            <trim suffix="UNION ALL">
                <if test="payablePlanTypeKeyDto.mStudentServiceFee != null and payablePlanTypeKeyDto.mStudentServiceFee">
                    SELECT a.*, c.fk_student_id
                    FROM ais_finance_center.m_payment_form_item a
                    INNER JOIN ais_sale_center.m_payable_plan b
                    ON a.fk_payable_plan_id = b.id
                    INNER JOIN ais_sale_center.m_student_service_fee c
                    ON b.fk_type_key = 'm_student_service_fee' AND b.fk_type_target_id = c.id
                    WHERE 1 = 1
                    <if test="studentIds != null and studentIds.size() > 0">
                        AND c.fk_student_id IN
                        <foreach collection="studentIds" item="studentId" open="(" close=")" separator=",">
                            #{studentId}
                        </foreach>
                    </if>
                </if>
            </trim>
        </trim>
        ) res
        GROUP BY res.fk_student_id
    </select>
    <select id="getAllSettlementBillItem" resultType="com.get.financecenter.vo.MSettlementBillItemVo">
        select DISTINCT mSettlementBill.fk_payable_plan_id,mSettlementBill.fk_settlement_bill_id
        , partnerFile.file_name_orc AS file_name_orc
        , partnerFile.file_key AS file_key

        FROM app_partner_center.m_settlement_bill_item mSettlementBill
        LEFT JOIN app_partner_center.s_media_and_attached sAttached ON sAttached.fk_table_name='m_settlement_bill' AND sAttached.fk_table_id=mSettlementBill.fk_settlement_bill_id
        LEFT JOIN app_file_center.m_file_partner partnerFile ON partnerFile.file_guid = sAttached.fk_file_guid


        WHERE 1=1 AND mSettlementBill.fk_payable_plan_id IN
        <foreach collection="fkPayablePlanIds" item="planId" open="(" close=")" separator=",">
            #{planId}
        </foreach>
    </select>
    <select id="selectstatusSettlement" resultType="java.lang.Integer">
        SELECT COUNT(*) AS  num
        from app_partner_center.m_settlement_bill mSettlementBill
                 INNER JOIN app_partner_center.m_settlement_bill_item  mSettlementBillItem
                            ON mSettlementBill.id=mSettlementBillItem.fk_settlement_bill_id
                 INNER JOIN app_partner_center.r_settlement_bill_settlement_installment rSettlementBill ON rSettlementBill.fk_settlement_bill_id=mSettlementBill.id
                 INNER JOIN ais_finance_center.r_payable_plan_settlement_installment  rPayablePlanSettlement
                            ON rSettlementBill.fk_payable_plan_settlement_installment_id=rPayablePlanSettlement.id

        WHERE mSettlementBill.ID IN(SELECT MAX(ID) FROM app_partner_center.m_settlement_bill WHERE fk_agent_id=#{agentId})
          AND rPayablePlanSettlement.status_settlement != 1 AND rPayablePlanSettlement.status = 2


    </select>


</mapper>