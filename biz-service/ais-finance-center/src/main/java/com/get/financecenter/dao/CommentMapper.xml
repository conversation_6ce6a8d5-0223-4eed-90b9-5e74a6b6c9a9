<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.CommentMapper">
  <resultMap id="BaseResultMap" type="com.get.financecenter.entity.FComment">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_table_name" jdbcType="VARCHAR" property="fkTableName" />
    <result column="fk_table_id" jdbcType="BIGINT" property="fkTableId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.get.financecenter.entity.FComment">
    <result column="comment" jdbcType="LONGVARCHAR" property="comment" />
  </resultMap>
  <sql id="Blob_Column_List">
    comment
  </sql>
  <insert id="insert" parameterType="com.get.financecenter.entity.FComment">
    insert into s_comment (id, fk_table_name, fk_table_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user, comment)
    values (#{id,jdbcType=BIGINT}, #{fkTableName,jdbcType=VARCHAR}, #{fkTableId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR}, #{comment,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.financecenter.entity.FComment">
    insert into s_comment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkTableName != null">
        fk_table_name,
      </if>
      <if test="fkTableId != null">
        fk_table_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="comment != null">
        comment,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkTableName != null">
        #{fkTableName,jdbcType=VARCHAR},
      </if>
      <if test="fkTableId != null">
        #{fkTableId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="comment != null">
        #{comment,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
</mapper>