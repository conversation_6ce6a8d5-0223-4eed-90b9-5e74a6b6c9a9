package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.service.IActivityTypeService;
import com.get.financecenter.vo.ActivityTypeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "活动类型管理")
@RestController
@RequestMapping("finance/activityType")
public class ActivityTypeController {

    @Resource
    private IActivityTypeService iActivityTypeService;

    @ApiOperation("活动类型key下拉")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/活动类型管理/活动类型key下拉")
    @GetMapping("getActivityType")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<ActivityTypeVo> getActivityType() {
        return new ListResponseBo<>(iActivityTypeService.getActivityType());
    }

}
