package com.get.financecenter.service.impl;

import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.GetDateUtil;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.AccountingItemMapper;
import com.get.financecenter.dao.CompanyAccountingItemMapper;
import com.get.financecenter.dao.StageBalanceSheetValueMapper;
import com.get.financecenter.dao.StageProfitAndLossValueMapper;
import com.get.financecenter.dao.StageValueTaskQueueMapper;
import com.get.financecenter.dao.VouchMapper;
import com.get.financecenter.dto.BalanceSheetStatementDto;
import com.get.financecenter.dto.RecalculateTheBalanceSheetDto;
import com.get.financecenter.entity.AccountingItem;
import com.get.financecenter.entity.StageBalanceSheetValue;
import com.get.financecenter.entity.StageProfitAndLossValue;
import com.get.financecenter.entity.StageValueTaskQueue;
import com.get.financecenter.service.AccountingItemService;
import com.get.financecenter.service.BalanceSheetService;
import com.get.financecenter.vo.BalanceSheetStatementItemVo;
import com.get.financecenter.vo.BalanceSheetStatementVo;
import com.get.financecenter.vo.CompanyAccountingItemVo;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class StageBalanceSheetServiceImpl implements BalanceSheetService {

    @Resource
    private StageBalanceSheetValueMapper stageBalanceSheetValueMapper;
    @Resource
    private CompanyAccountingItemMapper companyAccountingItemMapper;
    @Resource
    private VouchMapper vouchMapper;
    @Resource
    private AccountingItemService accountingItemService;
    @Resource
    private AccountingItemMapper accountingItemMapper;
    @Resource
    private StageValueTaskQueueMapper stageValueTaskQueueMapper;
    @Resource
    private StageProfitAndLossValueMapper stageProfitAndLossValueMapper;
    @Resource
    @Lazy
    private BalanceSheetService balanceSheetService;


    /**
     * 生成并查询资产负债表
     *
     * @param balanceSheetStatementDto
     * @return
     */
    @Override
    @Transactional
    public BalanceSheetStatementVo createBalanceSheetStatement(BalanceSheetStatementDto balanceSheetStatementDto) {
        BalanceSheetStatementVo balanceSheetStatementVo = new BalanceSheetStatementVo();

        Date startTime = balanceSheetStatementDto.getTime();
        Instant instant = startTime.toInstant();
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());

        // 获取当前年份
        int year = localDateTime.getYear();
        // 获取当前月份（1-12）
        int month = localDateTime.getMonthValue();
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        Long companyId = balanceSheetStatementDto.getCompanyId();

        //创建资产负债表数据
        List<StageBalanceSheetValue> stageBalanceSheetValueList = createStageBalanceSheetValue(companyId, year, month, staffInfo);
        if (GeneralTool.isEmpty(stageBalanceSheetValueList)) {
            return balanceSheetStatementVo;
        }
        //科目ids
        List<Long> accountingItemIds = stageBalanceSheetValueList.stream().map(StageBalanceSheetValue::getFkAccountingItemId).collect(Collectors.toList());
        //有涉及到的科目
        List<AccountingItem> accountingItems = accountingItemMapper.selectBatchIds(accountingItemIds);
        //科目分组
        Map<Long, AccountingItem> accountingItemMap = accountingItems.stream().collect(Collectors.toMap(AccountingItem::getId, item -> item));

        //-------- 构建返回结果 ----------
        //资产类
        List<StageBalanceSheetValue> assetsStageBalanceSheetValueList = stageBalanceSheetValueMapper.getStageBalanceSheetValueList(companyId, year, month, 1);
        BigDecimal assetsAmountOpeningBalance = BigDecimal.ZERO;
        BigDecimal assetsAmountDr = BigDecimal.ZERO;
        BigDecimal assetsAmountCr = BigDecimal.ZERO;
        BigDecimal assetsAmountClosingBalance = BigDecimal.ZERO;
        List<BalanceSheetStatementItemVo> assetsVoList = new ArrayList<>();
        for (StageBalanceSheetValue stageBalanceSheetValue : assetsStageBalanceSheetValueList) {
            BalanceSheetStatementItemVo assetsVo = new BalanceSheetStatementItemVo();
            AccountingItem accountingItem = accountingItemMap.get(stageBalanceSheetValue.getFkAccountingItemId());
            StringBuilder title = new StringBuilder();
            if (stageBalanceSheetValue.getDirectionValue() == -1) {
                title.append("减：");
            }
            title.append(accountingItem.getCodeName());
            assetsVo.setTitle(title.toString());
            assetsVo.setAmountOpeningBalance(stageBalanceSheetValue.getAmountOpeningBalance().multiply(new BigDecimal(stageBalanceSheetValue.getDirectionValue())));
            assetsVo.setAmountDr(stageBalanceSheetValue.getAmountDr().multiply(new BigDecimal(stageBalanceSheetValue.getDirectionValue())));
            assetsVo.setAmountCr(stageBalanceSheetValue.getAmountCr().multiply(new BigDecimal(stageBalanceSheetValue.getDirectionValue())));
            assetsVo.setAmountClosingBalance(stageBalanceSheetValue.getAmountClosingBalance().multiply(new BigDecimal(stageBalanceSheetValue.getDirectionValue())));
            assetsVoList.add(assetsVo);
            assetsAmountOpeningBalance = assetsAmountOpeningBalance.add(assetsVo.getAmountOpeningBalance());
            assetsAmountDr = assetsAmountDr.add(assetsVo.getAmountDr());
            assetsAmountCr = assetsAmountCr.add(assetsVo.getAmountCr());
            assetsAmountClosingBalance = assetsAmountClosingBalance.add(assetsVo.getAmountClosingBalance());
        }
        balanceSheetStatementVo.setAssetsVoList(assetsVoList);
        balanceSheetStatementVo.setAssetsAmountOpeningBalanceTotal(assetsAmountOpeningBalance);
        balanceSheetStatementVo.setAssetsAmountDrTotal(assetsAmountDr);
        balanceSheetStatementVo.setAssetsAmountCrTotal(assetsAmountCr);
        balanceSheetStatementVo.setAssetsAmountClosingBalanceTotal(assetsAmountClosingBalance);

        //负债类
        List<StageBalanceSheetValue> liabilitiesStageBalanceSheetValueList = stageBalanceSheetValueMapper.getStageBalanceSheetValueList(companyId, year, month, 2);
        BigDecimal liabilitiesAmountOpeningBalance = BigDecimal.ZERO;
        BigDecimal liabilitiesAmountDr = BigDecimal.ZERO;
        BigDecimal liabilitiesAmountCr = BigDecimal.ZERO;
        BigDecimal liabilitiesAmountClosingBalance = BigDecimal.ZERO;
        List<BalanceSheetStatementItemVo> liabilitiesVoList = new ArrayList<>();
        for (StageBalanceSheetValue stageBalanceSheetValue : liabilitiesStageBalanceSheetValueList) {
            BalanceSheetStatementItemVo liabilitiesVo = new BalanceSheetStatementItemVo();
            AccountingItem accountingItem = accountingItemMap.get(stageBalanceSheetValue.getFkAccountingItemId());
            StringBuilder title = new StringBuilder();
            if (stageBalanceSheetValue.getDirectionValue() == -1) {
                title.append("减：");
            }
            title.append(accountingItem.getCodeName());
            liabilitiesVo.setTitle(title.toString());
            liabilitiesVo.setAmountOpeningBalance(stageBalanceSheetValue.getAmountOpeningBalance().multiply(new BigDecimal(stageBalanceSheetValue.getDirectionValue())));
            liabilitiesVo.setAmountDr(stageBalanceSheetValue.getAmountDr().multiply(new BigDecimal(stageBalanceSheetValue.getDirectionValue())));
            liabilitiesVo.setAmountCr(stageBalanceSheetValue.getAmountCr().multiply(new BigDecimal(stageBalanceSheetValue.getDirectionValue())));
            liabilitiesVo.setAmountClosingBalance(stageBalanceSheetValue.getAmountClosingBalance().multiply(new BigDecimal(stageBalanceSheetValue.getDirectionValue())));
            liabilitiesVoList.add(liabilitiesVo);
            liabilitiesAmountOpeningBalance = liabilitiesAmountOpeningBalance.add(liabilitiesVo.getAmountOpeningBalance());
            liabilitiesAmountDr = liabilitiesAmountDr.add(liabilitiesVo.getAmountDr());
            liabilitiesAmountCr = liabilitiesAmountCr.add(liabilitiesVo.getAmountCr());
            liabilitiesAmountClosingBalance = liabilitiesAmountClosingBalance.add(liabilitiesVo.getAmountClosingBalance());
        }
        balanceSheetStatementVo.setLiabilitiesVoList(liabilitiesVoList);
        balanceSheetStatementVo.setLiabilitiesAmountOpeningBalanceTotal(liabilitiesAmountOpeningBalance);
        balanceSheetStatementVo.setLiabilitiesAmountDrTotal(liabilitiesAmountDr);
        balanceSheetStatementVo.setLiabilitiesAmountCrTotal(liabilitiesAmountCr);
        balanceSheetStatementVo.setLiabilitiesAmountClosingBalanceTotal(liabilitiesAmountClosingBalance);

        //权益类
        List<StageBalanceSheetValue> equityStageBalanceSheetValueList = stageBalanceSheetValueMapper.getStageBalanceSheetValueList(companyId, year, month, 2);
        BigDecimal equityAmountOpeningBalance = BigDecimal.ZERO;
        BigDecimal equityAmountDr = BigDecimal.ZERO;
        BigDecimal equityAmountCr = BigDecimal.ZERO;
        BigDecimal equityAmountClosingBalance = BigDecimal.ZERO;
        List<BalanceSheetStatementItemVo> equityVoList = new ArrayList<>();
        for (StageBalanceSheetValue stageBalanceSheetValue : equityStageBalanceSheetValueList) {
            BalanceSheetStatementItemVo equityVo = new BalanceSheetStatementItemVo();
            AccountingItem accountingItem = accountingItemMap.get(stageBalanceSheetValue.getFkAccountingItemId());
            StringBuilder title = new StringBuilder();
            if (stageBalanceSheetValue.getDirectionValue() == -1) {
                title.append("减：");
            }
            title.append(accountingItem.getCodeName());
            equityVo.setTitle(title.toString());
            equityVo.setAmountOpeningBalance(stageBalanceSheetValue.getAmountOpeningBalance().multiply(new BigDecimal(stageBalanceSheetValue.getDirectionValue())));
            equityVo.setAmountDr(stageBalanceSheetValue.getAmountDr().multiply(new BigDecimal(stageBalanceSheetValue.getDirectionValue())));
            equityVo.setAmountCr(stageBalanceSheetValue.getAmountCr().multiply(new BigDecimal(stageBalanceSheetValue.getDirectionValue())));
            equityVo.setAmountClosingBalance(stageBalanceSheetValue.getAmountClosingBalance().multiply(new BigDecimal(stageBalanceSheetValue.getDirectionValue())));
            equityVoList.add(equityVo);
            equityAmountOpeningBalance = equityAmountOpeningBalance.add(equityVo.getAmountOpeningBalance());
            equityAmountDr = equityAmountDr.add(equityVo.getAmountDr());
            equityAmountCr = equityAmountCr.add(equityVo.getAmountCr());
            equityAmountClosingBalance = equityAmountClosingBalance.add(equityVo.getAmountClosingBalance());
        }
        balanceSheetStatementVo.setEquityVoList(equityVoList);
        //未分配利润
        List<StageProfitAndLossValue> stageProfitAndLossValueList  = stageProfitAndLossValueMapper.getStageProfitAndLossValueSumList(companyId, year, month);
        if (GeneralTool.isNotEmpty(stageProfitAndLossValueList)) {
            stageProfitAndLossValueList.stream().filter(item -> item.getIsSum() == true).forEach(item -> {
                balanceSheetStatementVo.setUndistributedProfitAmountOpeningBalance(balanceSheetStatementVo.getUndistributedProfitAmountOpeningBalance().add(item.getAmountOpeningBalance()));
                balanceSheetStatementVo.setUndistributedProfitAmountDr(balanceSheetStatementVo.getUndistributedProfitAmountDr().add(item.getAmountDr()));
                balanceSheetStatementVo.setUndistributedProfitAmountCr(balanceSheetStatementVo.getUndistributedProfitAmountCr().add(item.getAmountCr()));
                balanceSheetStatementVo.setUndistributedProfitAmountClosingBalance(balanceSheetStatementVo.getUndistributedProfitAmountClosingBalance().add(item.getAmountClosingBalance()));
            });
        }
        //权益合计 = 累加 + 未分配利润
        balanceSheetStatementVo.setEquityAmountOpeningBalanceTotal(equityAmountOpeningBalance.add(balanceSheetStatementVo.getUndistributedProfitAmountOpeningBalance() == null ? BigDecimal.ZERO : balanceSheetStatementVo.getUndistributedProfitAmountOpeningBalance()));
        balanceSheetStatementVo.setEquityAmountDrTotal(equityAmountDr.add(balanceSheetStatementVo.getUndistributedProfitAmountDr() == null ? BigDecimal.ZERO : balanceSheetStatementVo.getUndistributedProfitAmountDr()));
        balanceSheetStatementVo.setEquityAmountCrTotal(equityAmountCr.add(balanceSheetStatementVo.getUndistributedProfitAmountCr() == null ? BigDecimal.ZERO : balanceSheetStatementVo.getUndistributedProfitAmountCr()));
        balanceSheetStatementVo.setEquityAmountClosingBalanceTotal(equityAmountClosingBalance.add(balanceSheetStatementVo.getUndistributedProfitAmountClosingBalance() == null ? BigDecimal.ZERO : balanceSheetStatementVo.getUndistributedProfitAmountClosingBalance()));

        //负债及所有者权益总计
        balanceSheetStatementVo.setTotalLiabilitiesAndEquityAmountOpeningBalance(balanceSheetStatementVo.getLiabilitiesAmountOpeningBalanceTotal().add(balanceSheetStatementVo.getEquityAmountOpeningBalanceTotal()));
        balanceSheetStatementVo.setTotalLiabilitiesAndEquityAmountDr(balanceSheetStatementVo.getLiabilitiesAmountDrTotal().add(balanceSheetStatementVo.getEquityAmountDrTotal()));
        balanceSheetStatementVo.setTotalLiabilitiesAndEquityAmountCr(balanceSheetStatementVo.getLiabilitiesAmountCrTotal().add(balanceSheetStatementVo.getEquityAmountCrTotal()));
        balanceSheetStatementVo.setTotalLiabilitiesAndEquityAmountClosingBalance(balanceSheetStatementVo.getLiabilitiesAmountClosingBalanceTotal().add(balanceSheetStatementVo.getEquityAmountClosingBalanceTotal()));
        return balanceSheetStatementVo;
    }

    /**
     * 创建资产负债表数据
     *
     * @param companyId
     * @param year
     * @param month
     * @param staffInfo
     */
    @Transactional
    public List<StageBalanceSheetValue> createStageBalanceSheetValue(Long companyId, int year, int month, StaffInfo staffInfo) {
        //删除资产负债表统计数值
        stageBalanceSheetValueMapper.delete(Wrappers.<StageBalanceSheetValue>lambdaQuery()
                .eq(StageBalanceSheetValue::getFkCompanyId, companyId)
                .eq(StageBalanceSheetValue::getYear, year)
                .eq(StageBalanceSheetValue::getMonth, month));
        List<StageBalanceSheetValue> stageBalanceSheetValueList = new ArrayList<>();


        //---------资产类表格--------
        //资产类 账套科目配置. 只要一级的科目
        List<CompanyAccountingItemVo> companyAccountingItemList = companyAccountingItemMapper.getCompanyAccountingConfigure(companyId, 1, 1);
        if (GeneralTool.isNotEmpty(companyAccountingItemList)) {
            stageBalanceSheetValueList.addAll(buildBalanceSheet(companyAccountingItemList, year, month, companyId, staffInfo, 1));
        }

        //负债类表格 只要一级的科目
        //负债类 账套科目配置
        companyAccountingItemList = companyAccountingItemMapper.getCompanyAccountingConfigure(companyId, 2, 1);
        if (GeneralTool.isNotEmpty(companyAccountingItemList)) {
            stageBalanceSheetValueList.addAll(buildBalanceSheet(companyAccountingItemList, year, month, companyId, staffInfo, 2));
        }
        //权益类表格
        //资产类 账套科目配置 只要一级的科目
        companyAccountingItemList = companyAccountingItemMapper.getCompanyAccountingConfigure(companyId, 3, 1);
        if (GeneralTool.isNotEmpty(companyAccountingItemList)) {
            stageBalanceSheetValueList.addAll(buildBalanceSheet(companyAccountingItemList, year, month, companyId, staffInfo, 3));
        }
        if (GeneralTool.isNotEmpty(stageBalanceSheetValueList)) {
            stageBalanceSheetValueMapper.insertBatch(stageBalanceSheetValueList);
        }
        return stageBalanceSheetValueList;
    }

    /**
     * 重新统计资产负债表
     *
     * @param recalculateTheBalanceSheetDto
     */
    @Override
    public void recalculateTheBalanceSheet(RecalculateTheBalanceSheetDto recalculateTheBalanceSheetDto) {
        StaffInfo staffInfo = SecureUtil.getStaffInfo();
        //插入异步任务信息
        StageValueTaskQueue stageValueTaskQueue = new StageValueTaskQueue();
        stageValueTaskQueue.setFkTableName(TableEnum.FINANCE_STAGE_BALANCE_SHEET_VALUE.key);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");
        stageValueTaskQueue.setStartYearMonth(sdf.format(recalculateTheBalanceSheetDto.getStartTime()));
        stageValueTaskQueue.setFkCompanyIds(StringUtils.join(recalculateTheBalanceSheetDto.getCompanyIds(), ","));
        stageValueTaskQueue.setStartTime(new Date());
        stageValueTaskQueue.setStatus(1);
        stageValueTaskQueue.setGmtCreateUser(staffInfo.getLoginId());
        stageValueTaskQueue.setGmtCreate(new Date());
        stageValueTaskQueueMapper.insert(stageValueTaskQueue);
        balanceSheetService.recalculateTheBalanceSheetAsync(recalculateTheBalanceSheetDto, staffInfo, stageValueTaskQueue);
    }

    /**
     * 异步创建
     *
     * @param recalculateTheBalanceSheetDto
     * @param staffInfo
     * @param stageValueTaskQueue
     */
    @Async
    @Transactional
    public void recalculateTheBalanceSheetAsync(RecalculateTheBalanceSheetDto recalculateTheBalanceSheetDto, StaffInfo staffInfo, StageValueTaskQueue stageValueTaskQueue) {
        try {
            List<Long> companyIds = recalculateTheBalanceSheetDto.getCompanyIds();
            Date startTime = recalculateTheBalanceSheetDto.getStartTime();
            // 获取系统当前时间
            Date currentDate = new Date();
            // 获取从传入时间到当前时间的所有YearMonth集合
            List<YearMonth> yearMonthList = GetDateUtil.getYearMonthRange(startTime, currentDate);
            for (Long companyId : companyIds) {
                for (YearMonth yearMonth : yearMonthList) {
                    //创建资产负债表数据
                    createStageBalanceSheetValue(companyId, yearMonth.getYear(), yearMonth.getMonthValue(), staffInfo);
                }
            }
            stageValueTaskQueue.setEndTime(new Date());
            stageValueTaskQueue.setStatus(2);
            stageValueTaskQueue.setGmtCreateUser(staffInfo.getLoginId());
            stageValueTaskQueue.setGmtCreate(new Date());
            stageValueTaskQueueMapper.updateById(stageValueTaskQueue);
        } catch (Exception e) {
            e.printStackTrace();
            updateTaskStatus(stageValueTaskQueue, e.getMessage(), staffInfo);
            throw new RuntimeException(e);
        }
    }

    /**
     * 使用REQUIRES_NEW确保创建新事务
     *
     * @param queue
     * @param message
     * @param staff
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateTaskStatus(StageValueTaskQueue queue, String message, StaffInfo staff) {
        queue.setEndTime(new Date());
        queue.setStatus(3);
        queue.setMessage(message);
        queue.setGmtCreateUser(staff.getLoginId());
        queue.setGmtCreate(new Date());
        stageValueTaskQueueMapper.updateById(queue);
    }

    /**
     * 根据科目构建损益表数据
     *
     * @param companyAccountingItemList 配置的科目
     * @param year
     * @param month
     * @param companyId
     * @param staffInfo
     * @param type                      科目类型枚举：1=资产/2=负债/3=权益/4=成本/5=损益/6=共同
     * @return
     */
    private List<StageBalanceSheetValue> buildBalanceSheet(List<CompanyAccountingItemVo> companyAccountingItemList, int year, int month, Long companyId, StaffInfo staffInfo, Integer type) {
        List<StageBalanceSheetValue> stageBalanceSheetValueList = new ArrayList<>();
        for (CompanyAccountingItemVo companyAccountingItemVo : companyAccountingItemList) {
            AccountingItem accountingItem = accountingItemMapper.selectById(companyAccountingItemVo.getFkAccountingItemId());
            //所有科目id(包含下级所有科目)
            List<Long> accountingItemList = accountingItemService.getChildrenAccountingItems(Collections.singletonList(accountingItem.getId()));
            accountingItemList.add(accountingItem.getId());

            StageBalanceSheetValue stageBalanceSheetValue = new StageBalanceSheetValue();
            stageBalanceSheetValue.setFkCompanyId(companyAccountingItemVo.getFkCompanyId());
            stageBalanceSheetValue.setYear(year);
            stageBalanceSheetValue.setMonth(month);
            stageBalanceSheetValue.setFkBalanceSheetTypeId(companyAccountingItemVo.getFkBalanceSheetTypeId());
            stageBalanceSheetValue.setFkAccountingItemId(companyAccountingItemVo.getFkAccountingItemId());
            //资产类型 借是1 贷是-1，其他类型则相反
            if (type == 1) {
                stageBalanceSheetValue.setDirectionValue(Objects.equals(companyAccountingItemVo.getDirection(), "0") ? 1 : -1);
            } else {
                stageBalanceSheetValue.setDirectionValue(Objects.equals(companyAccountingItemVo.getDirection(), "0") ? -1 : 1);
            }
            //当月第一天
            Date firstDayOfMonth = GetDateUtil.getBeginTime(year, month);
            //获取当月的前一天时间
            Date lastDayOfPrevMonth = GetDateUtil.getYesterdayDate(firstDayOfMonth);
            //当月最后一天
            Date lastDayOfMonth = GetDateUtil.getLastDayOfMonth(firstDayOfMonth);
            //期初数计算当月第一天的前一天
            stageBalanceSheetValue.setAmountOpeningBalance(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, null, lastDayOfPrevMonth, null, null, accountingItem.getDirection()));
            //期末数计算当月最后一天
            stageBalanceSheetValue.setAmountClosingBalance(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, null, lastDayOfMonth, null, null, accountingItem.getDirection()));
            //借方发生额：当月第一条和最后一天 dr累加
            stageBalanceSheetValue.setAmountDr(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, firstDayOfMonth, lastDayOfMonth, null, null, 2));
            //贷方发生额：当月第一条和最后一天 cr累加
            stageBalanceSheetValue.setAmountCr(vouchMapper.getAccountingItemBalance(companyId, accountingItemList, firstDayOfMonth, lastDayOfMonth, null, null, 3));
            stageBalanceSheetValue.setIsSum(true);
            stageBalanceSheetValue.setGmtCreateUser(staffInfo.getLoginId());
            stageBalanceSheetValue.setGmtCreate(new Date());
            stageBalanceSheetValueList.add(stageBalanceSheetValue);
        }
        return stageBalanceSheetValueList;
    }

}
