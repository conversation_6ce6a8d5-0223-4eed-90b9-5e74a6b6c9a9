package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.CostMapper;
import com.get.financecenter.dto.SearchActivityDataDto;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.service.ICostService;
import com.get.financecenter.service.TravelClaimFormItemService;
import com.get.financecenter.vo.CostVo;
import com.get.financecenter.vo.ExpenseClaimFormAndItemVo;
import com.get.financecenter.vo.TravelClaimFormAndItemVo;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

@Service
public class CostServiceImpl implements ICostService {
    @Resource
    private CostMapper costMapper;

    @Resource
    private ExpenseClaimFormItemServiceImpl expenseClaimFormItemService;
    @Resource
    private TravelClaimFormItemService travelClaimFormItemService;


    /**
     * 通过活动信息分页查询费用列表（费用报销和差旅费用报销）
     *
     * @param searchActivityDataDto
     * @param page
     * @return
     */
    @Override
    public List<CostVo> getCostByActivityData(SearchActivityDataDto searchActivityDataDto, Page page) {
        verifyActivityParameters(searchActivityDataDto);
        List<CostVo> convertDatas = new ArrayList<>();
        IPage<ExpenseClaimForm> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        // 获取状态为：1审批结束 的费用 （费用报销和差旅费用报销）
        List<Integer> statusList = new ArrayList<>(Arrays.asList(1));
        List<CostVo> costVos = costMapper.getCostByActivityData(searchActivityDataDto, statusList, pages);
        if (GeneralTool.isEmpty(costVos)) {
            return Collections.emptyList();
        }
        page.setAll((int) pages.getTotal());
        //处理费用报销数据 转换为map
        Map<Long, ExpenseClaimFormAndItemVo> expenseItemsMap = expenseClaimFormItemService
                .getAllExpenseClaimFormItemByActivityData(searchActivityDataDto)
                .stream()
                .collect(Collectors.toMap(
                        ExpenseClaimFormAndItemVo::getId,
                        Function.identity()
                ));
        //处理差旅费用报销数据 转换为map
        Map<Long, TravelClaimFormAndItemVo> travelItemsMap = travelClaimFormItemService
                .getAllTravelClaimFormItemByActivityData(searchActivityDataDto)
                .stream()
                .collect(Collectors.toMap(
                        TravelClaimFormAndItemVo::getId,
                        Function.identity()
                ));
        //参数处理
        for (CostVo costVo : costVos) {
            //费用报销
            if ("EXPENSE".equals(costVo.getClaimType())) {
                ExpenseClaimFormAndItemVo expenseClaimFormAndItemVo = expenseItemsMap.get(costVo.getId());
                if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo)) {
                    //报销类型
                    costVo.setClaimTypeName("费用报销");
                    //编号
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getNum())) {
                        costVo.setNum(expenseClaimFormAndItemVo.getNum());
                    }
                    //构建报销业务参数
                    StringBuilder claimBusiness = new StringBuilder();
                    //费用类型 expenseClaimFeeTypeName
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getExpenseClaimFeeTypeName())) {
                        claimBusiness.append(expenseClaimFormAndItemVo.getExpenseClaimFeeTypeName());
                    }
                    //摘要 summary
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getSummary())) {
                        claimBusiness.append(";").append(expenseClaimFormAndItemVo.getSummary());
                    }
                    //关联类型名称 relationTargetName
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getRelationTargetName())) {
                        claimBusiness.append(";").append(expenseClaimFormAndItemVo.getRelationTargetName());
                    }
                    //涉及代理 agentName
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getAgentName())) {
                        claimBusiness.append(";").append(expenseClaimFormAndItemVo.getAgentName());
                    }
                    //添加报销业务
                    costVo.setClaimBusiness(claimBusiness.toString());
                    //币种名称
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getFkCurrencyTypeName())) {
                        costVo.setCurrencyName(expenseClaimFormAndItemVo.getFkCurrencyTypeName());
                    }
                    //币种编号
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getFkCurrencyTypeNum())) {
                        costVo.setFkCurrencyTypeNum(expenseClaimFormAndItemVo.getFkCurrencyTypeNum());
                    }
                    //报销金额
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getAmount())) {
                        costVo.setClaimAmount(expenseClaimFormAndItemVo.getAmount());
                    }
                    //发票金额
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getInvoiceAmount())) {
                        costVo.setInvoiceAmount(expenseClaimFormAndItemVo.getInvoiceAmount());
                    }
                    //关联活动名称
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getFkEventTableName())) {
                        costVo.setFkEventTableName(expenseClaimFormAndItemVo.getFkEventTableName());
                    }
                    //关联活动Id
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getFkEventTableId())) {
                        costVo.setFkEventTableId(expenseClaimFormAndItemVo.getFkEventTableId());
                    }
                    //活动类型名称 eventTableName
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getEventTableName())) {
                        //冒号前的部分
                        costVo.setFkEventTableNameName(expenseClaimFormAndItemVo.getEventTableName().substring(0, expenseClaimFormAndItemVo.getEventTableName().indexOf(":")));
                        //冒号后的部分
                        costVo.setFkEventTableTitle(expenseClaimFormAndItemVo.getEventTableName().substring(expenseClaimFormAndItemVo.getEventTableName().indexOf(":") + 1));
                    }
                    //关联活动公司Id
                    if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getEventTableCompanyId())) {
                        costVo.setEventTableCompanyId(expenseClaimFormAndItemVo.getEventTableCompanyId());
                    }

                }

            }
            //差旅费用
            else if ("TRAVEL".equals(costVo.getClaimType())) {
                TravelClaimFormAndItemVo travelClaimFormAndItemVo = travelItemsMap.get(costVo.getId());
                if (GeneralTool.isNotEmpty(travelClaimFormAndItemVo)) {
                    //报销类型
                    costVo.setClaimTypeName("差旅费用");
                    //编号
                    if (GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getNum())) {
                        costVo.setNum(travelClaimFormAndItemVo.getNum());
                    }
                    //报销业务
                    if (GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getSummary())) {
                        costVo.setClaimBusiness(travelClaimFormAndItemVo.getSummary());
                    }
                    //币种名称
                    if (GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getFkCurrencyTypeName())) {
                        costVo.setCurrencyName(travelClaimFormAndItemVo.getFkCurrencyTypeName());
                    }
                    //币种编号
                    if (GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getFkCurrencyTypeNum())) {
                        costVo.setFkCurrencyTypeNum(travelClaimFormAndItemVo.getFkCurrencyTypeNum());
                    }
                    //报销金额
                    if (GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getAmount())) {
                        costVo.setClaimAmount(travelClaimFormAndItemVo.getAmount());
                    }
                    //关联活动名称
                    if (GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getFkEventTableName())) {
                        costVo.setFkEventTableName(travelClaimFormAndItemVo.getFkEventTableName());
                    }
                    //关联活动Id
                    if (GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getFkEventTableId())) {
                        costVo.setFkEventTableId(travelClaimFormAndItemVo.getFkEventTableId());
                    }
                    //活动类型名称 eventTableName
                    if (GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getEventTableName())) {
                        //冒号前的部分
                        costVo.setFkEventTableNameName(travelClaimFormAndItemVo.getEventTableName().substring(0, travelClaimFormAndItemVo.getEventTableName().indexOf(":")));
                        //冒号后的部分
                        costVo.setFkEventTableTitle(travelClaimFormAndItemVo.getEventTableName().substring(travelClaimFormAndItemVo.getEventTableName().indexOf(":") + 1));
                    }
                    //关联活动公司Id
                    if (GeneralTool.isNotEmpty(travelClaimFormAndItemVo.getEventTableCompanyId())) {
                        costVo.setEventTableCompanyId(travelClaimFormAndItemVo.getEventTableCompanyId());
                    }
                }


            }
            convertDatas.add(costVo);

        }


        return costVos;
    }

    @Override
    public ResponseBo getCostTotalAmount(SearchActivityDataDto searchActivityDataDto) {
        try {
            // 费用报销总额（处理可能的空集合）
            BigDecimal expenseTotal = Optional.ofNullable(
                            expenseClaimFormItemService.getAllExpenseClaimFormItemByActivityData(searchActivityDataDto))
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(item -> item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // 差旅报销总额（处理可能的空集合）
            BigDecimal travelTotal = Optional.ofNullable(
                            travelClaimFormItemService.getAllTravelClaimFormItemByActivityData(searchActivityDataDto))
                    .orElse(Collections.emptyList())
                    .stream()
                    .map(item -> item.getAmount() != null ? item.getAmount() : BigDecimal.ZERO)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            return new ResponseBo(expenseTotal.add(travelTotal).setScale(2, RoundingMode.HALF_UP));
        } catch (Exception e) {
//            log.error("计算报销总额失败", e);
            return new ResponseBo(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        }
    }

    private static void verifyActivityParameters(SearchActivityDataDto searchActivityDataDto) {
        if (GeneralTool.isEmpty(searchActivityDataDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(searchActivityDataDto.getFkEventTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing") + "fkEventTableName");
        }
        if (GeneralTool.isEmpty(searchActivityDataDto.getFkEventTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing") + "fkEventTableId");
        }
    }
}
