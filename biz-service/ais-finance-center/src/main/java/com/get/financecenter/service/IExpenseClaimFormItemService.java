package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.financecenter.dto.ExpenseClaimFormItemDto;
import com.get.financecenter.dto.SearchActivityDataDto;
import com.get.financecenter.entity.ExpenseClaimFormItem;
import com.get.financecenter.vo.ActivityFinancialSummaryVo;
import com.get.financecenter.vo.ExpenseClaimFormAndItemVo;
import com.get.financecenter.vo.ExpenseClaimFormItemVo;
import java.util.List;
import javax.validation.Valid;

/**
 * @author: Sea
 * @create: 2021/4/7 16:22
 * @verison: 1.0
 * @description:
 */
public interface IExpenseClaimFormItemService extends BaseService<ExpenseClaimFormItem> {
    /**
     * @return void
     * @Description :批量新增
     * @Param [expenseClaimFormItemVos]
     * <AUTHOR>
     */
    void batchAdd(List<ExpenseClaimFormItemDto> expenseClaimFormItemDtos);

    /**
     * @return java.util.List<com.get.financecenter.vo.ExpenseClaimFormItemDto>
     * @Description :根据费用报销单id查找所有对应费用报销单子项对象集合
     * @Param [id]
     * <AUTHOR>
     */
    List<ExpenseClaimFormItemVo> getDtoByExpenseClaimFormId(Long id);

    /**
     * @return void
     * @Description :根据费用报销单id删除
     * @Param [expenseClaimFormId]
     * <AUTHOR>
     */
    void deleteByFkid(Long expenseClaimFormId);

    /**
     * 根据活动信息获取费用报销单子项
     * @param searchActivityDataDto
     * @return
     */
    List<ExpenseClaimFormItemVo> getDtoBySearchActivityDataDto(SearchActivityDataDto searchActivityDataDto);

    /**
     * 根据活动信息获取费用报销单子项和费用报销单
     * @param searchActivityDataDto
     * @return
     */
    List<ExpenseClaimFormAndItemVo> getAllExpenseClaimFormItemByActivityData(SearchActivityDataDto searchActivityDataDto);


    /**
     * 根据活动表名和id查询活动费用和费用报销单相关金额
     * @param searchActivityDataDto
     * @return
     */
    ActivityFinancialSummaryVo getActivityFinancialSummary(SearchActivityDataDto searchActivityDataDto);

    /**
     * 根据活动信息分页查询费用报销单列表数据
     * @param searchActivityDataDto
     * @param page
     * @return
     */
    List<ExpenseClaimFormAndItemVo> getExpenseClaimFormByActivityData(@Valid SearchActivityDataDto searchActivityDataDto, Page page);


}
