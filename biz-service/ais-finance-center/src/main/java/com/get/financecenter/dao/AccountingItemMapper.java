package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.AccountingItemDto;
import com.get.financecenter.entity.AccountingItem;
import com.get.financecenter.vo.AccountingItemDropdownMenuVo;
import com.get.financecenter.vo.AccountingItemSelectVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface AccountingItemMapper extends BaseMapper<AccountingItem> {

    List<AccountingItemSelectVo> getAccountingItemAll(IPage<AccountingItem> pages,@Param("accountingItemDto") AccountingItemDto accountingItemDto);

    List<AccountingItemDropdownMenuVo> getAccountingItemByGrade(@Param("grade") int grade);

    List<AccountingItem> getAccountingItemDropdownMenu();

    Integer checkNameOrCode(@Param("code") String code, @Param("codeName") String codeName);

    Long getFkBalanceSheetTypeIdByAccountingItemId(Long fkAccountingItemId);
}
