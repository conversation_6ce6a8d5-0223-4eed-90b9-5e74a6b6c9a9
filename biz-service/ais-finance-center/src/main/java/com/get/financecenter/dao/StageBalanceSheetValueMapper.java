package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.dto.ProfitAndLossStatementDto;
import com.get.financecenter.entity.StageBalanceSheetValue;
import com.get.financecenter.entity.StageProfitAndLossValue;
import com.get.financecenter.vo.ProfitAndLossStatementItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StageBalanceSheetValueMapper extends BaseMapper<StageBalanceSheetValue> {

    void insertBatch(List<StageBalanceSheetValue> stageBalanceSheetValueList);

    /**
     * 获取资产负债表中间表数据
     * @param companyId
     * @param year
     * @param month
     * @param type 科目类型枚举：1=资产/2=负债/3=权益/4=成本/5=损益/6=共同
     * @return
     */
    List<StageBalanceSheetValue> getStageBalanceSheetValueList(Long companyId, int year, int month, int type);

    /**
     * 获取损益表标题
     * @param probAndLossStatementDto
     * @return
     */
//    List<ProfitAndLossStatementItemVo> getProfitAndLossStatementTitle(@Param("probAndLossStatementDto") ProfitAndLossStatementDto probAndLossStatementDto);
}
