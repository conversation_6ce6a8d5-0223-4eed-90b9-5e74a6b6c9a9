package com.get.financecenter.service;

import com.get.core.secure.StaffInfo;
import com.get.financecenter.dto.BalanceSheetStatementDto;
import com.get.financecenter.dto.RecalculateTheBalanceSheetDto;
import com.get.financecenter.entity.StageValueTaskQueue;
import com.get.financecenter.vo.BalanceSheetStatementVo;

public interface BalanceSheetService {

    /**
     * 生成并查询资产负债表
     * @param balanceSheetStatementDto
     * @return
     */
    BalanceSheetStatementVo createBalanceSheetStatement(BalanceSheetStatementDto balanceSheetStatementDto);

    /**
     * 重新统计资产负债表
     * @param recalculateTheBalanceSheetDto
     */
    void recalculateTheBalanceSheet(RecalculateTheBalanceSheetDto recalculateTheBalanceSheetDto);

    void recalculateTheBalanceSheetAsync(RecalculateTheBalanceSheetDto recalculateTheBalanceSheetDto, StaffInfo staffInfo, StageValueTaskQueue stageValueTaskQueue);
}
