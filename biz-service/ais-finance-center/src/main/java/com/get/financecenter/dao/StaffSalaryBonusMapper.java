package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.financecenter.dto.AccountingItemDto;
import com.get.financecenter.dto.query.StaffSalaryBonusQueryDto;
import com.get.financecenter.entity.AccountingItem;
import com.get.financecenter.entity.StaffSalaryBonus;
import com.get.financecenter.vo.AccountingItemDropdownMenuVo;
import com.get.financecenter.vo.AccountingItemSelectVo;
import com.get.financecenter.vo.StaffSalaryBonusVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface StaffSalaryBonusMapper extends BaseMapper<StaffSalaryBonus> {


    List<StaffSalaryBonusVo> datas(IPage<StaffSalaryBonusVo> pages,@Param("salaryBonusDto") StaffSalaryBonusQueryDto data);

    StaffSalaryBonusVo findStaffSalaryBonusById(@Param("id")Long id);
}
