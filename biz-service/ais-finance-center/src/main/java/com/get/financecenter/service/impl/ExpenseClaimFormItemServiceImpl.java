package com.get.financecenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dao.CurrencyTypeMapper;
import com.get.financecenter.dao.ExpenseClaimAgentContentMapper;
import com.get.financecenter.dao.ExpenseClaimFormItemMapper;
import com.get.financecenter.dao.ExpenseClaimFormMapper;
import com.get.financecenter.dto.ExpenseClaimFormItemDto;
import com.get.financecenter.dto.SearchActivityDataDto;
import com.get.financecenter.entity.ExpenseClaimAgentContent;
import com.get.financecenter.entity.ExpenseClaimFormItem;
import com.get.financecenter.enums.ActivityTypeEnum;
import com.get.financecenter.service.ICurrencyTypeService;
import com.get.financecenter.service.IExpenseClaimFeeTypeService;
import com.get.financecenter.service.IExpenseClaimFormItemService;
import com.get.financecenter.utils.ActivityTypeUtils;
import com.get.financecenter.utils.CurrencyConverterUtils;
import com.get.financecenter.utils.RelationTargetProcessorUtils;
import com.get.financecenter.vo.ActivityFinancialSummaryVo;
import com.get.financecenter.vo.ExpenseClaimFormAndItemVo;
import com.get.financecenter.vo.ExpenseClaimFormItemVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import com.get.workflowcenter.vo.ActRuTaskVo;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @author: Sea
 * @create: 2021/4/7 16:23
 * @verison: 1.0
 * @description:
 */
@Service
public class ExpenseClaimFormItemServiceImpl extends BaseServiceImpl<ExpenseClaimFormItemMapper, ExpenseClaimFormItem> implements IExpenseClaimFormItemService {
    @Resource
    private ExpenseClaimFormItemMapper expenseClaimFormItemMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private IExpenseClaimFeeTypeService expenseClaimFeeTypeService;

    @Resource
    private ExpenseClaimAgentContentMapper expenseClaimAgentContentMapper;
    @Resource
    private CurrencyTypeMapper currencyTypeMapper;
    @Resource
    private RelationTargetProcessorUtils relationTargetProcessorUtils;
    @Resource
    private ActivityTypeUtils activityTypeUtils;
    @Resource
    private ExpenseClaimFormMapper expenseClaimFormMapper;
    @Resource
    private IWorkflowCenterClient workflowCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ICurrencyTypeService currencyTypeService;
    @Resource
    private CurrencyConverterUtils currencyConverterUtils;


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<ExpenseClaimFormItemDto> expenseClaimFormItemDtos) {
        if (GeneralTool.isEmpty(expenseClaimFormItemDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (ExpenseClaimFormItemDto expenseClaimFormItemDto : expenseClaimFormItemDtos) {
            ExpenseClaimFormItem expenseClaimFormItem = BeanCopyUtils.objClone(expenseClaimFormItemDto, ExpenseClaimFormItem::new);
            utilService.updateUserInfoToEntity(expenseClaimFormItem);
            expenseClaimFormItemMapper.insert(expenseClaimFormItem);
        }
    }

    @Override
    public List<ExpenseClaimFormItemVo> getDtoByExpenseClaimFormId(Long id) {
        List<ExpenseClaimFormItemVo> convertDatas = new ArrayList<>();
        List<ExpenseClaimFormItem> expenseClaimFormItems = this.expenseClaimFormItemMapper.selectList(Wrappers.<ExpenseClaimFormItem>query().lambda()
                .eq(ExpenseClaimFormItem::getFkExpenseClaimFormId, id));
        // 2021/9/16 费用类型名称改为获取Map 一次性sql查询
        setExpenseClaimFormItemParaments(expenseClaimFormItems, convertDatas);
        return convertDatas;
    }

    /**
     * 根据活动信息查询费用项
     *
     * @param searchActivityDataDto
     * @return
     */
    @Override
    public List<ExpenseClaimFormItemVo> getDtoBySearchActivityDataDto(SearchActivityDataDto searchActivityDataDto) {
        List<ExpenseClaimFormItemVo> convertDatas = new ArrayList<>();
        List<ExpenseClaimFormItem> expenseClaimFormItems = this.expenseClaimFormItemMapper.selectList(Wrappers.<ExpenseClaimFormItem>query().lambda()
                .eq(ExpenseClaimFormItem::getFkEventTableName, searchActivityDataDto.getFkEventTableName()).eq(ExpenseClaimFormItem::getFkEventTableId, searchActivityDataDto.getFkEventTableId()));
        //设置对应的参数
        setExpenseClaimFormItemParaments(expenseClaimFormItems, convertDatas);
        return convertDatas;
    }


    /**
     * 根据活动信息查询所有费用报销单列表数据
     *
     * @param searchActivityDataDto
     * @return
     */
    @Override
    public List<ExpenseClaimFormAndItemVo> getAllExpenseClaimFormItemByActivityData(SearchActivityDataDto searchActivityDataDto) {
        if (GeneralTool.isEmpty(searchActivityDataDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(searchActivityDataDto.getFkEventTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing") + "fkEventTableName");
        }
        if (GeneralTool.isEmpty(searchActivityDataDto.getFkEventTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing") + "fkEventTableId");
        }
        // 获取状态为：1审批结束 的费用 （费用报销和差旅费用报销）
        List<Integer> statusList = new ArrayList<>(Arrays.asList(1));
        List<ExpenseClaimFormAndItemVo> expenseClaimFormAndItemVos = expenseClaimFormItemMapper.getAllExpenseClaimFormItemByActivityData(searchActivityDataDto, statusList);
        if (GeneralTool.isEmpty(expenseClaimFormAndItemVos)) {
            return Collections.emptyList();
        }
        //代理id集合
        Set<Long> agentIds = expenseClaimFormAndItemVos.stream().filter(item -> GeneralTool.isNotEmpty(item.getFkAgentId())).map(ExpenseClaimFormAndItemVo::getFkAgentId).collect(Collectors.toSet());
        Map<Long, String> agentNameMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentIds)) {
            //feign调用查找代理名称map
            Result<Map<Long, String>> result1 = saleCenterClient.getAgentNamesByIds(agentIds);
            agentNameMap = result1.getData();
        }

        for (ExpenseClaimFormAndItemVo expenseClaimFormAndItemVo : expenseClaimFormAndItemVos) {
            //设置学生代理名称
            Long agentId = expenseClaimFormAndItemVo.getFkAgentId();
            expenseClaimFormAndItemVo.setAgentName(agentNameMap.get(agentId));
            //费用类型名称
            expenseClaimFormAndItemVo.setExpenseClaimFeeTypeName(expenseClaimFeeTypeService.getExpenseClaimFeeTypeNameById(expenseClaimFormAndItemVo.getFkExpenseClaimFeeTypeId()));
            relationTargetProcessorUtils.processRelationTarget(
                    expenseClaimFormAndItemVo.getRelationTargetKey(),
                    expenseClaimFormAndItemVo.getRelationTargetId(),
                    null,
                    expenseClaimFormAndItemVo::setRelationTargetCompanyId,
                    expenseClaimFormAndItemVo::setRelationTargetName
            );

            if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getFkEventTableName()) && GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getFkEventTableId())) {
                activityTypeUtils.processRelationTarget(
                        expenseClaimFormAndItemVo.getFkEventTableName(),
                        expenseClaimFormAndItemVo.getFkEventTableId(),
                        null,
                        expenseClaimFormAndItemVo::setEventTableCompanyId,
                        expenseClaimFormAndItemVo::setEventTableName
                );
            }


            StringBuilder detail = new StringBuilder();
            if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getFkAgentId())) {
                Agent agent = saleCenterClient.getAgentById(expenseClaimFormAndItemVo.getFkAgentId()).getData();
                detail.append("Subagent:").append(agent.getNum()).append(" (").append(agent.getName()).append(")");
            }
            if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getFkExpenseClaimAgentContentId())) {
                ExpenseClaimAgentContent expenseClaimAgentContent = expenseClaimAgentContentMapper.selectById(expenseClaimFormAndItemVo.getFkExpenseClaimAgentContentId());
                detail.append("内容");
                if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getPeopleCount())) {
                    detail.append("/人数:").append(expenseClaimAgentContent.getName()).append("/").append(expenseClaimFormAndItemVo.getPeopleCount());
                } else {
                    detail.append(":").append(expenseClaimAgentContent.getName());
                }
            } else if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getPeopleCount())) {
                detail.append("人数:").append(expenseClaimFormAndItemVo.getPeopleCount());
            }
            expenseClaimFormAndItemVo.setDetail(detail.toString());
            String currencyName = currencyTypeMapper.getCurrencyNameByNum(expenseClaimFormAndItemVo.getFkCurrencyTypeNum());
            expenseClaimFormAndItemVo.setFkCurrencyTypeName(currencyName);

        }

        return expenseClaimFormAndItemVos;

    }

    private void setExpenseClaimFormItemParaments(List<ExpenseClaimFormItem> expenseClaimFormItems, List<ExpenseClaimFormItemVo> convertDatas) {
        //代理id集合
        Set<Long> agentIds = expenseClaimFormItems.stream().filter(item -> GeneralTool.isNotEmpty(item.getFkAgentId())).map(ExpenseClaimFormItem::getFkAgentId).collect(Collectors.toSet());
        Map<Long, String> agentNameMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentIds)) {
            //feign调用查找代理名称map
            Result<Map<Long, String>> result1 = saleCenterClient.getAgentNamesByIds(agentIds);
            agentNameMap = result1.getData();
        }

        for (ExpenseClaimFormItem expenseClaimFormItem : expenseClaimFormItems) {
            ExpenseClaimFormItemVo expenseClaimFormItemVo = BeanCopyUtils.objClone(expenseClaimFormItem, ExpenseClaimFormItemVo::new);
            //设置学生代理名称
            Long agentId = expenseClaimFormItemVo.getFkAgentId();
            expenseClaimFormItemVo.setAgentName(agentNameMap.get(agentId));
            //费用类型名称
            expenseClaimFormItemVo.setExpenseClaimFeeTypeName(expenseClaimFeeTypeService.getExpenseClaimFeeTypeNameById(expenseClaimFormItemVo.getFkExpenseClaimFeeTypeId()));
            relationTargetProcessorUtils.processRelationTarget(
                    expenseClaimFormItemVo.getRelationTargetKey(),
                    expenseClaimFormItemVo.getRelationTargetId(),
                    null,
                    expenseClaimFormItemVo::setRelationTargetCompanyId,
                    expenseClaimFormItemVo::setRelationTargetName
            );

            if (GeneralTool.isNotEmpty(expenseClaimFormItemVo.getFkEventTableName()) && GeneralTool.isNotEmpty(expenseClaimFormItemVo.getFkEventTableId())) {
                activityTypeUtils.processRelationTarget(
                        expenseClaimFormItemVo.getFkEventTableName(),
                        expenseClaimFormItemVo.getFkEventTableId(),
                        null,
                        expenseClaimFormItemVo::setEventTableCompanyId,
                        expenseClaimFormItemVo::setEventTableName
                );
            }


            StringBuilder detail = new StringBuilder();
            if (GeneralTool.isNotEmpty(expenseClaimFormItemVo.getFkAgentId())) {
                Agent agent = saleCenterClient.getAgentById(expenseClaimFormItemVo.getFkAgentId()).getData();
                detail.append("Subagent:").append(agent.getNum()).append(" (").append(agent.getName()).append(")");
            }
            if (GeneralTool.isNotEmpty(expenseClaimFormItemVo.getFkExpenseClaimAgentContentId())) {
                ExpenseClaimAgentContent expenseClaimAgentContent = expenseClaimAgentContentMapper.selectById(expenseClaimFormItemVo.getFkExpenseClaimAgentContentId());
                detail.append("内容");
                if (GeneralTool.isNotEmpty(expenseClaimFormItemVo.getPeopleCount())) {
                    detail.append("/人数:").append(expenseClaimAgentContent.getName()).append("/").append(expenseClaimFormItemVo.getPeopleCount());
                } else {
                    detail.append(":").append(expenseClaimAgentContent.getName());
                }
            } else if (GeneralTool.isNotEmpty(expenseClaimFormItemVo.getPeopleCount())) {
                detail.append("人数:").append(expenseClaimFormItemVo.getPeopleCount());
            }
            expenseClaimFormItemVo.setDetail(detail.toString());
            String currencyName = currencyTypeMapper.getCurrencyNameByNum(expenseClaimFormItemVo.getFkCurrencyTypeNum());
            expenseClaimFormItemVo.setFkCurrencyTypeName(expenseClaimFormItemVo.getFkCurrencyTypeNum() + "(" + currencyName + ")");
            convertDatas.add(expenseClaimFormItemVo);
        }
    }

    @Override
    public void deleteByFkid(Long expenseClaimFormId) {
//        Example example = new Example(ExpenseClaimFormItem.class);
//        example.createCriteria().andEqualTo("fkExpenseClaimFormId",expenseClaimFormId);
//        expenseClaimFormItemMapper.deleteByExample(example);
        this.expenseClaimFormItemMapper.delete(Wrappers.<ExpenseClaimFormItem>query().lambda().eq(ExpenseClaimFormItem::getFkExpenseClaimFormId, expenseClaimFormId));

    }

    /**
     * 根据活动信息分页查询费用报销单列表数据
     *
     * @param searchActivityDataDto
     * @param page
     * @return
     */
    @Override
    public List<ExpenseClaimFormAndItemVo> getExpenseClaimFormByActivityData(SearchActivityDataDto searchActivityDataDto, Page page) {
        verifyActivityParameters(searchActivityDataDto);
        List<ExpenseClaimFormAndItemVo> convertDatas = new ArrayList<>();
        IPage<ExpenseClaimFormAndItemVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        //只展示审核中和审核通过的数据
        List<ExpenseClaimFormAndItemVo> expenseClaimFormAndItemVos = expenseClaimFormItemMapper.getExpenseClaimFormByActivityData(searchActivityDataDto, pages);
        if (GeneralTool.isEmpty(expenseClaimFormAndItemVos)) {
            return Collections.emptyList();
        }
        page.setAll((int) pages.getTotal());
        Result<Map<Long, Integer>> result = workflowCenterClient.getFromIdsByStaffId(SecureUtil.getStaffId(), TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        //公司id集合
        Set<Long> companyIds = new HashSet<>();
        //部门id集合
        Set<Long> departmentIds = new HashSet<>();
//        //办公室id集合
//        Set<Long> officeIds = new HashSet<>();
        //报销人id集合
        Set<Long> staffIds = new HashSet<>();
        //报销单id集合
        List<Long> expenseClaimFormIds = new ArrayList<>();
        //获取各自集合的值
        for (ExpenseClaimFormAndItemVo expenseClaimFormAndItemVo : expenseClaimFormAndItemVos) {
            companyIds.add(expenseClaimFormAndItemVo.getFkCompanyId());
            departmentIds.add(expenseClaimFormAndItemVo.getFkDepartmentId());
//            officeIds.add(expenseClaimForm.getFkOfficeId());
            staffIds.add(expenseClaimFormAndItemVo.getFkStaffId());
            expenseClaimFormIds.add(expenseClaimFormAndItemVo.getId());
        }
        //feign调用 获取公司id-name的map
        companyIds.removeIf(Objects::isNull);
        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(companyIds).getData();
        //feign调用 获取部门id-name的map
        departmentIds.removeIf(Objects::isNull);
        Map<Long, String> departmentNameMap = permissionCenterClient.getDepartmentNamesByIds(departmentIds).getData();
        //feign调用 获取办公室id-name的map
//        officeIds.removeIf(Objects::isNull);
//        Map<Long, String> officeNameMap = permissionCenterClient.getofficeNamesByIds(officeIds).getData();
        //feign调用 获取报销人id-name的map
        staffIds.removeIf(Objects::isNull);
        Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameMap(staffIds).getData();
        //feign调用 获取流程方面dto 报销单id-actRuTaskDot的map
        expenseClaimFormIds.removeIf(Objects::isNull);
        Map<String, List<Long>> businessIdsWithProcdefKey = new HashMap<>();
        businessIdsWithProcdefKey.put(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key, expenseClaimFormIds);
        Result<Map<Long, ActRuTaskVo>> result1 = workflowCenterClient.getActRuTaskDtosByBusinessKey(businessIdsWithProcdefKey);
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }
        Map<Long, ActRuTaskVo> actRuTaskDtoMap = result1.getData();
        //币种编号nums
//        fkCurrencyTypeNum
        Set<String> currencyTypeNums = expenseClaimFormAndItemVos.stream().map(ExpenseClaimFormAndItemVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //根据币种编号nums获取名称
        Map<String, String> currencyNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNums)) {
            currencyNamesByNums = currencyTypeService.getCurrencyNamesByNums(currencyTypeNums);
        }
        //代理id集合
        Set<Long> agentIds = expenseClaimFormAndItemVos.stream().filter(item -> GeneralTool.isNotEmpty(item.getFkAgentId())).map(ExpenseClaimFormAndItemVo::getFkAgentId).collect(Collectors.toSet());
        Map<Long, String> agentNameMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentIds)) {
            //feign调用查找代理名称map
            Result<Map<Long, String>> agentNameMapByIds = saleCenterClient.getAgentNamesByIds(agentIds);
            if (agentNameMapByIds.isSuccess()) {
                agentNameMap = agentNameMapByIds.getData();
            }
        }
        for (ExpenseClaimFormAndItemVo expenseClaimFormAndItemVo : expenseClaimFormAndItemVos) {
            if (GeneralTool.isNotEmpty(companyNameMap.get(expenseClaimFormAndItemVo.getFkCompanyId()))) {
                expenseClaimFormAndItemVo.setCompanyName(companyNameMap.get(expenseClaimFormAndItemVo.getFkCompanyId()));
            }
            if (GeneralTool.isNotEmpty(departmentNameMap.get(expenseClaimFormAndItemVo.getFkDepartmentId()))) {
                expenseClaimFormAndItemVo.setDepartmentName(departmentNameMap.get(expenseClaimFormAndItemVo.getFkDepartmentId()));
            }
            if (GeneralTool.isNotEmpty(staffNameMap.get(expenseClaimFormAndItemVo.getFkStaffId()))) {
                expenseClaimFormAndItemVo.setStaffName(staffNameMap.get(expenseClaimFormAndItemVo.getFkStaffId()));
            }
            if (GeneralTool.isNotEmpty(currencyNamesByNums.get(expenseClaimFormAndItemVo.getFkCurrencyTypeNum()))) {
                expenseClaimFormAndItemVo.setFkCurrencyTypeName(currencyNamesByNums.get(expenseClaimFormAndItemVo.getFkCurrencyTypeNum()));
            }
            if (GeneralTool.isNotEmpty(result.getData().get(expenseClaimFormAndItemVo.getId()))) {
                expenseClaimFormAndItemVo.setExpenseClaimFormStatus(result.getData().get(expenseClaimFormAndItemVo.getId()));
            }
            if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getFkAgentId())) {
                expenseClaimFormAndItemVo.setAgentName(agentNameMap.get(expenseClaimFormAndItemVo.getFkAgentId()));
            }

            //费用类型名称
            expenseClaimFormAndItemVo.setExpenseClaimFeeTypeName(expenseClaimFeeTypeService.getExpenseClaimFeeTypeNameById(expenseClaimFormAndItemVo.getFkExpenseClaimFeeTypeId()));
            relationTargetProcessorUtils.processRelationTarget(
                    expenseClaimFormAndItemVo.getRelationTargetKey(),
                    expenseClaimFormAndItemVo.getRelationTargetId(),
                    null,
                    expenseClaimFormAndItemVo::setRelationTargetCompanyId,
                    expenseClaimFormAndItemVo::setRelationTargetName
            );

            if (GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getFkEventTableName()) && GeneralTool.isNotEmpty(expenseClaimFormAndItemVo.getFkEventTableId())) {
                activityTypeUtils.processRelationTarget(
                        expenseClaimFormAndItemVo.getFkEventTableName(),
                        expenseClaimFormAndItemVo.getFkEventTableId(),
                        null,
                        expenseClaimFormAndItemVo::setEventTableCompanyId,
                        expenseClaimFormAndItemVo::setEventTableName
                );
            }


            //流程对象
//            ActRuTaskVo actRuTaskVo = actRuTaskDtoMap.get(expenseClaimFormAndItemVo.getId());
            convertDatas.add(expenseClaimFormAndItemVo);
        }
        return convertDatas;
    }

    /**
     * 根据活动表名和id查询活动费用和费用报销单相关金额
     *
     * @param searchActivityDataDto
     * @return
     */
    @Override
    public ActivityFinancialSummaryVo getActivityFinancialSummary(SearchActivityDataDto searchActivityDataDto) {
        verifyActivityParameters(searchActivityDataDto);
        ActivityFinancialSummaryVo activityFinancialSummaryVo = new ActivityFinancialSummaryVo();
        if (searchActivityDataDto.getFkEventTableName().equals(ActivityTypeEnum.M_EVENT_INCENTIVE.activityTypeCode)) {
            //1.获取奖励推广活动费用明细
            Result<BigDecimal> eventIncentiveCostSubtotal = saleCenterClient.getEventIncentiveCostSubtotal(searchActivityDataDto.getFkEventTableId());
            if (eventIncentiveCostSubtotal.isSuccess()) {
                activityFinancialSummaryVo.setTotalEventCost(eventIncentiveCostSubtotal.getData().setScale(2));
                // 获取奖励推广活动费用小计
                activityFinancialSummaryVo.setEventCostSubtotal(eventIncentiveCostSubtotal.getData().setScale(2));
            }
        } else if (searchActivityDataDto.getFkEventTableName().equals(ActivityTypeEnum.M_EVENT.activityTypeCode)) {
            //2.获取活动费用明细
            Result<BigDecimal> eventCostSubtotal = saleCenterClient.getEventCostSubtotal(searchActivityDataDto.getFkEventTableId());
            if (eventCostSubtotal.isSuccess()) {
                activityFinancialSummaryVo.setTotalEventCost(eventCostSubtotal.getData().setScale(2));
                // 获取活动汇总费用小计
                activityFinancialSummaryVo.setEventCostSubtotal(eventCostSubtotal.getData().setScale(2));
            }
        }
        // 状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
        //已支出费用
//        获取状态为：1审批结束/2审批中 的支出费用
        //统一币种为人民币
        String defaultCurrency = "CNY";
        activityFinancialSummaryVo.setFkCurrencyTypeNum(defaultCurrency);
        activityFinancialSummaryVo.setCurrencyTypeName(currencyTypeService.getCurrencyNameByNum(defaultCurrency));

        List<Integer> claimedEventCoststatusList = new ArrayList<>(Arrays.asList(1, 2));
        //根据币种转换费用
        //获取币种
        String currencyTypeByActivityData = expenseClaimFormItemMapper.getCurrencyTypeByActivityData(searchActivityDataDto, claimedEventCoststatusList);
        BigDecimal claimedEventCost = expenseClaimFormItemMapper.getClaimedEventCostByActivityData(searchActivityDataDto, claimedEventCoststatusList).setScale(2);

        //转换人民币后的支出费用
        BigDecimal claimedEventCostCny = currencyConverterUtils.convertAmount(currencyTypeByActivityData, defaultCurrency, claimedEventCost);
        activityFinancialSummaryVo.setClaimedEventCost(claimedEventCostCny);
        //待报销费用总计
        activityFinancialSummaryVo.setTotalClaimAmount(claimedEventCostCny);

//        待报销费用小计 审批中的报销费用之和
        List<Integer> claimFormSubtotalstatusList = new ArrayList<>(Arrays.asList(2));
        BigDecimal claimFormSubtotal = expenseClaimFormItemMapper.getClaimedEventCostByActivityData(searchActivityDataDto, claimFormSubtotalstatusList).setScale(2);
        BigDecimal claimFormSubtotalCny = currencyConverterUtils.convertAmount(currencyTypeByActivityData, defaultCurrency, claimFormSubtotal);
        activityFinancialSummaryVo.setClaimFormSubtotal(claimFormSubtotalCny);

//        当前申请单报销费用 availableClaimAmount 获取状态为：1审批结束 以及 id为当前申请单 的待报销s所有总费用
        List<Integer> availableClaimAmountstatusList = new ArrayList<>(Arrays.asList(1));
        BigDecimal availableClaimAmount = expenseClaimFormItemMapper.getClaimedEventCostByActivityData(searchActivityDataDto, availableClaimAmountstatusList).setScale(2);
        BigDecimal availableClaimAmountCny = currencyConverterUtils.convertAmount(currencyTypeByActivityData, defaultCurrency, availableClaimAmount);
        activityFinancialSummaryVo.setAvailableClaimAmount(availableClaimAmountCny);

        if (activityFinancialSummaryVo.getTotalEventCost() != null && activityFinancialSummaryVo.getClaimedEventCost() != null) {
//            剩余可用费用 availableEventCost
            activityFinancialSummaryVo.setAvailableEventCost(activityFinancialSummaryVo.getTotalEventCost().subtract(activityFinancialSummaryVo.getClaimedEventCost()));

            //已超出费用金额 exceedingTheCostAmount
            activityFinancialSummaryVo.setExceedingTheCostAmount(activityFinancialSummaryVo.getClaimedEventCost().subtract(activityFinancialSummaryVo.getTotalEventCost()));
        }

        return activityFinancialSummaryVo;

    }

    private static void verifyActivityParameters(SearchActivityDataDto searchActivityDataDto) {
        if (GeneralTool.isEmpty(searchActivityDataDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(searchActivityDataDto.getFkEventTableName())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing") + "fkEventTableName");
        }
        if (GeneralTool.isEmpty(searchActivityDataDto.getFkEventTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing") + "fkEventTableId");
        }
    }
}
