package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.financecenter.dto.query.StaffSalaryBonusQueryDto;
import com.get.financecenter.vo.ImportSalaryBonusVo;
import com.get.financecenter.vo.StaffSalaryBonusVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface StaffSalaryBonusService {

    /**
     * 工资/奖金文件下载   salary：工资 bonus：奖金
     * @param response
     * @param type
     */
    void downloadTemplateFile(HttpServletResponse response, String type);

    /**
     * 工资/奖金数据导入
     * @param file
     * @param type salary：工资 bonus：奖金
     * @return
     */
    ImportSalaryBonusVo importSalaryBonus(MultipartFile file, String type);

    /**
     * 列表数据
     * @param page
     * @return
     */
    List<StaffSalaryBonusVo> datas(StaffSalaryBonusQueryDto data, Page  page);

    /**
     * 配置详情接口
     * @param id
     * @return
     */
    StaffSalaryBonusVo findStaffSalaryBonusById(Long id);

    /**
     * 解析模板  salary：工资 bonus：奖金
     * @param file
     * @param type
     * @return
     */
    List<StaffSalaryBonusVo> parseExcelTemplate(MultipartFile file,String type);
}
