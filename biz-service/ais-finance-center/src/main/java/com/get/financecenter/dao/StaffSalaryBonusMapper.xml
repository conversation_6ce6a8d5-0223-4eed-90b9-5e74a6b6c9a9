<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.financecenter.dao.StaffSalaryBonusMapper">


    <select id="datas" resultType="com.get.financecenter.vo.StaffSalaryBonusVo">
        SELECT
        mb.id,
        mb.type_key,
        mb.fk_staff_id,
        mb.fk_company_id,
        mb.`year_month`,
        mb.fk_currency_type_num,
        mb.amount,
        mb.json_detail,
        mb.remark,
        mb.import_file_name,
        m.name AS staffName,
        m.num AS staffNum,
        c.num AS companyName,
        sd.name AS departmentName,
        sp.name AS postName,
        mb.gmt_create_user,
        mb.gmt_create,
        mb.gmt_modified_user,
        mb.gmt_modified
        FROM m_staff_salary_bonus AS mb
        LEFT JOIN ais_permission_center.m_staff AS m ON m.id = mb.fk_staff_id
        LEFT JOIN ais_permission_center.m_company AS c ON c.id = mb.fk_company_id
        LEFT JOIN ais_permission_center.m_department AS sd ON sd.id = m.fk_department_id
        LEFT JOIN ais_permission_center.m_position AS sp ON sp.id = m.fk_position_id
        WHERE 1=1
        <if test="salaryBonusDto.id != null">
            AND mb.id = #{salaryBonusDto.id}
        </if>
        <if test="salaryBonusDto.yearMonth != null and salaryBonusDto.yearMonth != ''">
            AND mb.year_month = #{salaryBonusDto.yearMonth}
        </if>
        <if test="salaryBonusDto.yearMonth == null or salaryBonusDto.yearMonth==''">
            AND mb.year_month BETWEEN
            DATE_FORMAT(DATE_SUB(CURDATE(), INTERVAL 11 MONTH), '%Y%m')
            AND DATE_FORMAT(CURDATE(), '%Y%m')
        </if>
        <if test="salaryBonusDto.typeKey != null and salaryBonusDto.typeKey != ''">
            AND mb.type_key = #{salaryBonusDto.typeKey}
        </if>
        ORDER BY `year_month` DESC
    </select>
    <select id="findStaffSalaryBonusById" resultType="com.get.financecenter.vo.StaffSalaryBonusVo">
        SELECT
        mb.id,
        mb.type_key,
        mb.fk_staff_id,
        mb.fk_company_id,
        mb.`year_month`,
        mb.fk_currency_type_num,
        mb.amount,
        mb.json_detail,
        mb.remark,
        mb.import_file_name,
        m.name AS staffName,
        m.num AS staffNum,
        c.num AS companyName,
        sd.name AS departmentName,
        sp.name AS postName,
        mb.gmt_create_user,
        mb.gmt_create,
        mb.gmt_modified_user,
        mb.gmt_modified
        FROM m_staff_salary_bonus AS mb
        LEFT JOIN ais_permission_center.m_staff AS m ON m.id = mb.fk_staff_id
        LEFT JOIN ais_permission_center.m_company AS c ON c.id = mb.fk_company_id
        LEFT JOIN ais_permission_center.m_department AS sd ON sd.id = m.fk_department_id
        LEFT JOIN ais_permission_center.m_position AS sp ON sp.id = m.fk_position_id
      <where>
          <if test="id != null">
              AND mb.id = #{id}
          </if>
      </where>
    </select>
</mapper>
