<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.ExchangeRateMapper">
    <resultMap id="BaseResultMap" type="com.get.financecenter.entity.ExchangeRate">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_currency_type_num_from" jdbcType="VARCHAR" property="fkCurrencyTypeNumFrom"/>
        <result column="fk_currency_type_num_to" jdbcType="VARCHAR" property="fkCurrencyTypeNumTo"/>
        <result column="get_date" jdbcType="DATE" property="getDate"/>
        <result column="exchange_rate" jdbcType="DECIMAL" property="exchangeRate"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <insert id="insertSelective" parameterType="com.get.financecenter.entity.ExchangeRate" keyProperty="id"
            useGeneratedKeys="true">
        insert into u_exchange_rate
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkCurrencyTypeNumFrom != null">
                fk_currency_type_num_from,
            </if>
            <if test="fkCurrencyTypeNumTo != null">
                fk_currency_type_num_to,
            </if>
            <if test="getDate != null">
                get_date,
            </if>
            <if test="exchangeRate != null">
                exchange_rate,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkCurrencyTypeNumFrom != null">
                #{fkCurrencyTypeNumFrom,jdbcType=VARCHAR},
            </if>
            <if test="fkCurrencyTypeNumTo != null">
                #{fkCurrencyTypeNumTo,jdbcType=VARCHAR},
            </if>
            <if test="getDate != null">
                #{getDate,jdbcType=DATE},
            </if>
            <if test="exchangeRate != null">
                #{exchangeRate,jdbcType=DECIMAL},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getRateByCurrency" resultType="java.math.BigDecimal">
        SELECT exchange_rate
        from u_exchange_rate
        where fk_currency_type_num_from = #{fromCurrency}
          and fk_currency_type_num_to = #{toCurrency}
        ORDER BY get_date desc limit 1
    </select>
    <select id="getRateByCurrencyByGetDate" resultType="com.get.financecenter.entity.ExchangeRate">
        SELECT *
        from u_exchange_rate
        where fk_currency_type_num_from = #{fromCurrency}
          and fk_currency_type_num_to = #{toCurrency}
          and get_date = DATE_FORMAT(#{date}, '%Y%m%d') limit 1
    </select>

    <select id="getLastExchangeRateHkd" resultType="com.get.financecenter.vo.ExchangeRateVo">
        SELECT *
        from u_exchange_rate
        where fk_currency_type_num_to = #{currencyNum}
          and get_date= DATE_FORMAT(NOW(),'%Y%m%d')
        <if test="toCurrencys != null and toCurrencys.size() >0">
            and (
            <foreach collection="toCurrencys" item="toCurrency" index="index" separator="OR">
                fk_currency_type_num_from = #{toCurrency}
            </foreach>
                )
        </if>
    </select>

</mapper>