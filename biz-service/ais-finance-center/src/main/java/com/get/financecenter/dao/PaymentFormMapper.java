package com.get.financecenter.dao;

import com.get.core.mybatis.mapper.GetMapper;
import com.get.financecenter.vo.PaymentFormVo;
import com.get.financecenter.entity.PaymentForm;
import com.get.salecenter.vo.SelItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Mapper
public interface PaymentFormMapper extends GetMapper<PaymentForm> {

    int updateFee(@Param("fee") BigDecimal fee,@Param("id") Long id);


    List<SelItem> getCurrencyByFormIds(@Param("formIds") Set<Long> formIds);

    /**
     * @return java.util.List<com.get.financecenter.vo.PaymentFormDto>
     * @Description: 应付列表
     * @Param [payFormItemId]
     * <AUTHOR>
     */
    List<PaymentFormVo> getPayFormList(Long planId);

    /**
     * feign根据应付计划ids获取所绑定的付款单子项
     *
     * @Date 16:53 2021/12/2
     * <AUTHOR>
     */
    List<PaymentFormVo> getPayFormListFeignByPlanIds(@Param("planIds") Set<Long> planIds);


    /**
     * @Description: 根据绑定状态查询信息
     * @Author: Jerry
     * @Date:20:45 2021/11/22
     */
    List<PaymentFormVo> getPayFormListByBindingStatus(@Param("bindingStatus") Integer bindingStatus,
                                                      @Param("fkPayFormIds") Set<Long> fkPayFormIds);

    /**
     * 根据支付类型获取付款单
     *
     * @param fkPaymentFeeTypeId
     * @return
     */
    Integer getPayMentFormCountByTypeId(@Param("fkPaymentFeeTypeId") Long fkPaymentFeeTypeId);

    /**
     * 根据批次号找到生成的付款单
     *
     * @Date 10:15 2021/12/30
     * <AUTHOR>
     */
    List<PaymentForm> getPayFormListByNumSettlementBatch(@Param("numSettlementBatch") String numSettlementBatch);

}