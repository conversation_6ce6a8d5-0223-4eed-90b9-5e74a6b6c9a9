package com.get.financecenter.enums;

import com.get.financecenter.vo.ActivityTypeVo;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;

/**
 * 活动类型枚举
 */
@Getter
public enum ActivityTypeEnum {

    M_EVENT_INCENTIVE("m_event_incentive", "奖励推广活动")

    ,M_EVENT("m_event", "活动汇总")

//    SCHOOL_DISPATCH("school_dispatch", "学校外派活动")
    ;

    /**
     * 活动类型编码
     */
    public String activityTypeCode;


    /**
     * 活动类型名称
     */
    public String name;

    ActivityTypeEnum(String activityTypeCode, String name) {
        this.activityTypeCode = activityTypeCode;
        this.name = name;
    }

    public static String getNameByActivityTypeCode(String activityTypeCode) {
        for (ActivityTypeEnum value : ActivityTypeEnum.values()) {
            if (value.getActivityTypeCode().equals(activityTypeCode)) {
                return value.getName();
            }
        }
        return null;
    }

    public static List<ActivityTypeVo> getOptions() {
        return Arrays.stream(values())
                .map(type -> {
                    ActivityTypeVo selectEntity = new ActivityTypeVo();
                    selectEntity.setActivityTypeCode(type.activityTypeCode);
                    selectEntity.setActivityTypeName(type.name);
                    return selectEntity;
                })
                .collect(Collectors.toList());
    }
}
