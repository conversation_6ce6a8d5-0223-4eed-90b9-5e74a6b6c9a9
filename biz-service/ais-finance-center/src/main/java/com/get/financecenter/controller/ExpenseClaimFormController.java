package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.ExpenseClaimFormDto;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.query.ExpenseClaimFormQueryDto;
import com.get.financecenter.service.IExpenseClaimFormService;
import com.get.financecenter.vo.ExpenseClaimFormVo;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @author: Sea
 * @create: 2021/4/7 10:48
 * @verison: 1.0
 * @description:
 */
@Api(tags = "费用报销单管理")
@RestController
@RequestMapping("finance/expenseClaimForm")
public class ExpenseClaimFormController {
    @Resource
    private IExpenseClaimFormService expenseClaimFormService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ExpenseClaimFormDto>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/费用报销单管理/费用报销单详情")
    @GetMapping("/{id}")
    public ResponseBo<ExpenseClaimFormVo> detail(@PathVariable("id") Long id) {
        ExpenseClaimFormVo data = expenseClaimFormService.findExpenseClaimFormById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [expenseClaimFormVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/费用报销单管理/新增费用报销单")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated ExpenseClaimFormDto expenseClaimFormDto) {
        return SaveResponseBo.ok(expenseClaimFormService.addExpenseClaimForm(expenseClaimFormDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ExpenseClaimFormDto>
     * @Description :修改信息
     * @Param [expenseClaimFormVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/费用报销单管理/更新费用报销单")
    @PostMapping("update")
    public ResponseBo<ExpenseClaimFormVo> update(@RequestBody  @Validated(ExpenseClaimFormDto.Update.class) ExpenseClaimFormDto expenseClaimFormDto) {
        return UpdateResponseBo.ok(expenseClaimFormService.updateExpenseClaimForm(expenseClaimFormDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.financecenter.vo.ExpenseClaimFormDto>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/费用报销单管理/查询费用报销单")
    @PostMapping("datas")
    public ResponseBo<ExpenseClaimFormVo> datas(@RequestBody SearchBean<ExpenseClaimFormQueryDto> page) {
        List<ExpenseClaimFormVo> datas = expenseClaimFormService.getExpenseClaimForms(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 查询费用报销单附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询费用报销单附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/费用报销单管理/查询附件")
    @PostMapping("getItemMedia")
    public ResponseBo<FMediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<FMediaAndAttachedVo> staffMedia = expenseClaimFormService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 保存费用报销单附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存费用报销单附件")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/费用报销单管理/保存附件")
    @PostMapping("upload")
    public ResponseBo<FMediaAndAttachedVo> addItemMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class)  ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(expenseClaimFormService.addItemMedia(mediaAttachedVo));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :开启费用报销单流程
     * @Param [businessKey, procdefKey]
     * <AUTHOR>
     */
    @ApiOperation(value = "开启费用报销单流程", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/费用报销单管理/开启费用报销单流程")
    @PostMapping("startProcess")
    public ResponseBo startProcess(@RequestParam("companyId") Long companyId, @RequestParam("businessKey") Long businessKey, @RequestParam("procdefKey") String procdefKey) {
        expenseClaimFormService.startProcess(companyId, businessKey, procdefKey);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :作废接口
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "作废接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/费用报销单管理/作废")
    @GetMapping("updateStatus/{id}")
    public ResponseBo updateStatus(@PathVariable("id") Long id) {
        expenseClaimFormService.updateStatus(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return ExpenseClaimFormDto
     * @Description :费用报销单撤单
     * @Param [id]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "撤单", notes = "id为被撤销工休单的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/费用报销单管理/撤单")
    @GetMapping("getRevokeExpenseClaimForm")
    public ResponseBo getRevokeExpenseClaimForm(@RequestParam("id") Long id, @RequestParam("summary") String summary) {
        expenseClaimFormService.getRevokeExpenseClaimForm(id, summary);
        return ResponseBo.ok();
    }


}
