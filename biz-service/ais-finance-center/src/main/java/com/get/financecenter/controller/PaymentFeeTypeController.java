package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.PaymentFeeTypeDto;
import com.get.financecenter.service.IPaymentFeeTypeService;
import com.get.financecenter.service.IProviderService;
import com.get.financecenter.vo.PaymentFeeTypeVo;
import com.get.financecenter.vo.ProviderVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/6 14:27
 */

@Api(tags = "支付费用类型管理")
@RestController
@RequestMapping("finance/paymentFeeType")
public class PaymentFeeTypeController {
    @Resource
    private IPaymentFeeTypeService iPaymentFeeTypeService;

    @Autowired
    private IProviderService iProviderService;

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/BD团队配置管理/BD团队配置详情")
    @GetMapping("/{id}")
    public ResponseBo<PaymentFeeTypeVo> detail(@PathVariable("id") Long id) {
        PaymentFeeTypeVo data = iPaymentFeeTypeService.findPayTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @ Description :
     * @ Param [paymentFeeTypeVos]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/支付费用类型管理/新增")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(PaymentFeeTypeDto.Add.class) ValidList<PaymentFeeTypeDto> paymentFeeTypeDtos) {
        iPaymentFeeTypeService.batchAdd(paymentFeeTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/支付费用类型管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        iPaymentFeeTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param paymentFeeTypeDto
     * @return
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/支付费用类型管理/更新")
    @PostMapping("update")
    public ResponseBo<PaymentFeeTypeVo> update(@RequestBody @Validated(PaymentFeeTypeDto.Update.class) PaymentFeeTypeDto paymentFeeTypeDto) {
        return UpdateResponseBo.ok(iPaymentFeeTypeService.updatePayFlowType(paymentFeeTypeDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/支付费用类型管理/查询")
    @PostMapping("datas")
    public ResponseBo<PaymentFeeTypeVo> datas(@RequestBody SearchBean<PaymentFeeTypeDto> page) {
        List<PaymentFeeTypeVo> datas = iPaymentFeeTypeService.getPayTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [eventTypeVos]
     * <AUTHOR>
     */
//    @ApiOperation(value = "上移下移", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/支付费用类型管理/移动顺序")
//    @PostMapping("movingOrder")
//    public ResponseBo movingOrder(@RequestBody List<PaymentFeeTypeDto> workFlowTypeVos) {
//        ipaymentFeeType.sort(workFlowTypeVos);
//        return ResponseBo.ok();
//    }
    @ApiOperation(value = "排序（拖拽）", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/支付费用类型管理/排序（拖拽）")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestParam("start") Integer start, @RequestParam("end") Integer end) {
        iPaymentFeeTypeService.movingOrder(start, end);
        return ResponseBo.ok();
    }

    /**
     * 流程类型下拉框数据
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "流程类型下拉框数据", notes = "")
    @GetMapping("getWorkFlowTypeList")
    public ResponseBo<PaymentFeeTypeVo> getWorkFlowTypeList() {
        List<PaymentFeeTypeVo> datas = iPaymentFeeTypeService.getPayTypeList();
        return new ListResponseBo<>(datas);
    }

    /**
     * 目标类型下拉框数据
     *
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "目标类型下拉", notes = "")
    @GetMapping("findTypeKeySelect")
    public ResponseBo findTargetType() {
        List<Map<String, Object>> datas = iPaymentFeeTypeService.findTargetType();
        return new ListResponseBo<>(datas);
    }

    @ApiOperation("供应商查询")
    @GetMapping("getProviderList")
    public ListResponseBo<ProviderVo> getProviderList() {
        List<ProviderVo> providerList = iProviderService.getProviderList();
        return new ListResponseBo<>(providerList);
    }

    @ApiOperation("关联类型key下拉")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/支付费用类型管理/关联类型key下拉")
    @GetMapping("getRelationTargetKey")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<PaymentFeeTypeVo> getRelationTargetKey() {
        return new ListResponseBo<>(iPaymentFeeTypeService.getRelationTargetKey());
    }

}
