package com.get.financecenter.service.impl;


import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.TemplateExcelUtils;
import com.get.financecenter.dao.StaffSalaryBonusMapper;
import com.get.financecenter.dto.query.StaffSalaryBonusQueryDto;
import com.get.financecenter.entity.BankAccount;
import com.get.financecenter.entity.ReceiptMethodType;
import com.get.financecenter.entity.StaffSalaryBonus;
import com.get.financecenter.service.StaffSalaryBonusService;
import com.get.financecenter.vo.ImportSalaryBonusVo;
import com.get.financecenter.vo.StaffSalaryBonusVo;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.salecenter.entity.KpiPlanGroup;
import com.get.salecenter.entity.KpiPlanGroupItem;
import com.get.salecenter.vo.KpiPlanGroupItemImportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.checkerframework.checker.units.qual.A;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.StringJoiner;
import java.util.UUID;
import java.util.stream.Collectors;

import static cn.hutool.poi.excel.cell.CellUtil.getCellValue;

@Service
@Slf4j
public class StaffSalaryBonusServiceImpl extends ServiceImpl<StaffSalaryBonusMapper, StaffSalaryBonus> implements StaffSalaryBonusService {


    @Autowired
    private StaffSalaryBonusMapper staffSalaryBonusMapper;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private UtilService utilService;



    @Override
    public void downloadTemplateFile(HttpServletResponse response, String type) {
        String fileName = null;
        if (type == null) {
            fileName = "SalaryTemplate.xls";
        }

        if (type.equals("salary")) { //工资
            fileName = "SalaryTemplate.xls";
        } else if (type.equals("bonus")) { //奖金
            fileName = "BonusTemplate.xls";
        }
        //设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");

        // 输出流
        OutputStream outputStream = null;
        InputStream inputStream = null;
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            outputStream = response.getOutputStream();
            inputStream = TemplateExcelUtils.class.getClassLoader().getResourceAsStream("template/" + fileName);
            byte[] bytes = IOUtils.toByteArray(inputStream);
            outputStream.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.flush();
                    outputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    @Override
    public ImportSalaryBonusVo importSalaryBonus(MultipartFile file, String type) {
        InputStream inputStream = null;
        ImportSalaryBonusVo importSalaryBonusVo = new ImportSalaryBonusVo();
        try {
            inputStream = file.getInputStream();
            // 2. 读取Excel文件
            Workbook workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            // 1. 生成批次ID
            String batchId = UUID.randomUUID().toString();
            List<Staff> staffList = permissionCenterClient.getAllStaff().getData();
            //获取所有员工
            Map<String, Staff> staffMap = staffList.stream().collect(Collectors.toMap(Staff::getNum, staff -> staff));

            //工号无效
            List<String> invalidNum = new ArrayList<>();

            //姓名无效
            List<String> invalidName = new ArrayList<>();

            //其他原因
            List<String> invalidOther = new ArrayList<>();

            //导入总数
            Integer totalCount = 0;

            //成功导入数
            Integer successCount = 0;

            //失败导入数
            Integer failCount = 0;


            // 3. 遍历每一行数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) { // 假设第一行是标题

                Row row = sheet.getRow(i);
                if (row == null) continue;
                //总数
                totalCount++;
                // 4. 解析基础字段
                String staffCode = (String) getCellValue(row.getCell(0)); // 工号
                String staffName = (String) getCellValue(row.getCell(2)); // 姓名
                String yearMonth = null;

                // 6. 构建JSON明细
                JSONObject detailJson = new JSONObject(true);
                // 7. 获取amount(工资卡发放) h=或者奖金
                BigDecimal amount = null;
                if (type.equals("salary")) {
                    Object yearMonthObj = getCellValue(row.getCell(3));
                    yearMonth = yearMonthObj != null ? yearMonthObj.toString() : "";
                    String[] headers = {"基本工资", "工资浮动", "交通补贴", "其他补贴", "餐费", "奖金",
                            "扣事假", "扣病假", "考勤扣款", "应付工资", "扣社保费", "扣公积金",
                            "补扣社保", "补扣公积金", "税前工资", "专项附加扣除", "代扣税1",
                            "12月奖金", "年终奖", "奖金合计", "年终奖代扣税2", "入卡工资",
                            "工资卡发放", "其他"};
                    for (int j = 4; j < row.getLastCellNum(); j++) {
                        if (j - 4 < headers.length) {
                            Object cellValueObj = getCellValue(row.getCell(j));
                            String value;
                            if (cellValueObj == null) {
                                value = "";
                            } else if (cellValueObj instanceof String) {
                                value = (String) cellValueObj;
                            } else if (cellValueObj instanceof Double) {
                                Double doubleValue = (Double) cellValueObj;
                                // 判断是否为整数
                                if (doubleValue.doubleValue() == doubleValue.intValue()) {
                                    value = String.valueOf(doubleValue.intValue());
                                } else {
                                    value = String.valueOf(doubleValue);
                                }
                            } else {
                                value = cellValueObj.toString();
                            }
                            detailJson.put(headers[j - 4], value);
                        }
                    }
                    amount =new BigDecimal(detailJson.getString("工资卡发放"));
                }else if(type.equals("bonus")){
                    Object yearMonthObj = getCellValue(row.getCell(5));
                    yearMonth = yearMonthObj != null ? yearMonthObj.toString() : "";
                    String[] bonusHeaders = {"签约奖", "结案奖", "海佣提成", "美高社大", "加拿大低龄",
                            "咨询外联（CRM）", "咨询外联（AIS）", "网申", "KPI", "加拿大产品",
                            "BD月度申请及助理奖金", "增长率", "悉尼推荐奖励", "保险推荐",
                            "学费支付", "BD年度利润考核", "BD年度Bonus利润", "住宿奖励",
                            "签证文案", "国际课程", "市场及非业务人员", "其他", "年终奖",
                            "扣款", "缓发","总计","备注"};

                    for (int j = 6; j < row.getLastCellNum(); j++) {
                        if (j - 6 < bonusHeaders.length) {
                            Object cellValueObj = getCellValue(row.getCell(j));
                            String value;
                            if (cellValueObj == null) {
                                value = "";
                            } else if (cellValueObj instanceof String) {
                                value = (String) cellValueObj;
                            } else if (cellValueObj instanceof Double) {
                                Double doubleValue = (Double) cellValueObj;
                                // 判断是否为整数
                                if (doubleValue.doubleValue() == doubleValue.intValue()) {
                                    value = String.valueOf(doubleValue.intValue());
                                } else {
                                    value = String.valueOf(doubleValue);
                                }
                            } else {
                                value = cellValueObj.toString();
                            }
                            detailJson.put(bonusHeaders[j - 6], value);
                        }
                    }
                    amount =new BigDecimal(detailJson.getString("总计"));
                }

                String remark = detailJson.getString("备注");

                // 查询员工和公司ID
                Staff staff = staffMap.get(staffCode);
                //验证工号
                if(GeneralTool.isEmpty(staff)){
                    String reason = staffCode + ","+staffName;
                    invalidNum.add(reason);
                    failCount++;
                    continue;
                }
                //验证姓名
                if(GeneralTool.isNotEmpty(staff) && !staffName.equals(staff.getName())){
                    String reason = staffCode + ","+staffName;
                    invalidName.add(reason);
                    failCount++;
                    continue;
                }

                //根据条件看看是否已经存在，存在就删除
                LambdaQueryWrapper<StaffSalaryBonus>wrapper = new LambdaQueryWrapper();
                wrapper.eq(StaffSalaryBonus::getFkStaffId, staff.getId())
                        .eq(StaffSalaryBonus::getTypeKey, type)
                        .eq(StaffSalaryBonus::getYearMonth, yearMonth);
                StaffSalaryBonus staffSalaryBonus = staffSalaryBonusMapper.selectOne(wrapper);
               if(GeneralTool.isNotEmpty(staffSalaryBonus)){
                   QueryWrapper<StaffSalaryBonus> deleteWrapper = new QueryWrapper<>();
                   deleteWrapper.lambda()
                           .eq(StaffSalaryBonus::getId, staffSalaryBonus.getId());
                   int deletedCount = staffSalaryBonusMapper.delete(deleteWrapper);
                   if (deletedCount > 0) {
                       log.info("成功删除{}条记录", deletedCount);
                   }
               }


                // 构建实体并保存
                    StaffSalaryBonus entity = new StaffSalaryBonus();
                    entity.setTypeKey(type);
                    entity.setFkStaffId(staff.getId());
                    entity.setFkCompanyId(staff.getFkCompanyId());
                    entity.setYearMonth(yearMonth);
                    entity.setFkCurrencyTypeNum("CNY");
                    entity.setAmount(amount);
                    entity.setRemark(remark);
                    entity.setJsonDetail(detailJson.toJSONString());
                    entity.setImportFileName(batchId);
                    utilService.setCreateInfo(entity);
                    int  insertCount =staffSalaryBonusMapper.insert(entity);

                if(insertCount>0){
                    successCount++;
                }else {
                    String reason = staffCode + ","+staffName;
                    invalidOther.add(reason);
                    failCount++;
                }

            }

            importSalaryBonusVo.setTotalNum(totalCount);
            importSalaryBonusVo.setSuccessNum(successCount);
            importSalaryBonusVo.setFailNum(failCount);
            importSalaryBonusVo.setInvalidNum(invalidNum);
            importSalaryBonusVo.setInvalidName(invalidName);
            importSalaryBonusVo.setInvalidOther(invalidOther);
        } catch (Exception e) {
            e.printStackTrace();
//            return ResponseBo.error(ErrorCodeEnum.INVALID_PARAM.getCode(), "导入文件异常");
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        ResponseBo responseBo = new ResponseBo(true);
        responseBo.setCode(ErrorCodeEnum.REQUEST_OK.getCode());
        responseBo.setMessage("数据导入成功");
        return importSalaryBonusVo;
    }

    @Override
    public List<StaffSalaryBonusVo> datas(StaffSalaryBonusQueryDto data, Page page) {
        if (GeneralTool.isEmpty(data)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        IPage<StaffSalaryBonusVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StaffSalaryBonusVo> datas = staffSalaryBonusMapper.datas(pages,data);

        datas.forEach(item -> {
            if (item.getTypeKey().equals("salary")) {
                item.setTypeKeyName("工资");
            }else if (item.getTypeKey().equals("bonus")) {
                item.setTypeKeyName("奖金");
            }
            if (GeneralTool.isNotEmpty(item.getFkCurrencyTypeNum())&& item.getFkCurrencyTypeNum().equals("CNY")) {
                item.setFkCurrencyTypeNum(item.getFkCurrencyTypeNum()+" (人民币)");
            }
        });
        page.setAll((int) pages.getTotal());
        return datas;
    }

    @Override
    public StaffSalaryBonusVo findStaffSalaryBonusById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StaffSalaryBonusVo staffSalaryBonusVo = staffSalaryBonusMapper.findStaffSalaryBonusById(id);
        staffSalaryBonusVo.setTypeKeyName(staffSalaryBonusVo.getTypeKey().equals("salary") ? "工资" : "奖金");
        staffSalaryBonusVo.setFkCurrencyTypeNum(staffSalaryBonusVo.getFkCurrencyTypeNum().equals("CNY") ? "CNY (人民币)" : staffSalaryBonusVo.getFkCurrencyTypeNum());
        if (GeneralTool.isEmpty(staffSalaryBonusVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        return staffSalaryBonusVo;
    }

    @Override
    public List<StaffSalaryBonusVo> parseExcelTemplate(MultipartFile file,String type) {
        InputStream inputStream = null;
        List<StaffSalaryBonusVo> staffSalaryBonusVos = new ArrayList<>();
        try {
            inputStream = file.getInputStream();
            //  读取Excel文件
            Workbook workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0);

            // 遍历每一行数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) { // 假设第一行是标题

                Row row = sheet.getRow(i);
                if (row == null) continue;

                // 4. 解析基础字段
                String staffCode = (String) getCellValue(row.getCell(0)); // 工号
                String companyName = (String) getCellValue(row.getCell(1)); // 所属公司
                String staffName = (String) getCellValue(row.getCell(2)); // 姓名
                String yearMonth = null; // 年月
                String departmentName = null; // 部门名称
                String postName = null; // 岗位名称
                // 构建JSON明细
                JSONObject detailJson = new JSONObject(true);
                //  获取amount(工资卡发放) h=或者奖金
                BigDecimal amount = null;
                if (type.equals("salary")) {
                    Object yearMonthObj = getCellValue(row.getCell(3));
                    yearMonth = yearMonthObj != null ? yearMonthObj.toString() : "";
                    String[] headers = {"基本工资", "工资浮动", "交通补贴", "其他补贴", "餐费", "奖金",
                            "扣事假", "扣病假", "考勤扣款", "应付工资", "扣社保费", "扣公积金",
                            "补扣社保", "补扣公积金", "税前工资", "专项附加扣除", "代扣税1",
                            "12月奖金", "年终奖", "奖金合计", "年终奖代扣税2", "入卡工资",
                            "工资卡发放", "其他"};
                    for (int j = 4; j < row.getLastCellNum(); j++) {
                        if (j - 4 < headers.length) {
                            Object cellValueObj = getCellValue(row.getCell(j));
                            String value;
                            if (cellValueObj == null) {
                                value = "";
                            } else if (cellValueObj instanceof String) {
                                value = (String) cellValueObj;
                            } else if (cellValueObj instanceof Double) {
                                Double doubleValue = (Double) cellValueObj;
                                // 判断是否为整数
                                if (doubleValue.doubleValue() == doubleValue.intValue()) {
                                    value = String.valueOf(doubleValue.intValue());
                                } else {
                                    value = String.valueOf(doubleValue);
                                }
                            } else {
                                value = cellValueObj.toString();
                            }
                            detailJson.put(headers[j - 4], value);
                        }
                    }
                    amount =new BigDecimal(detailJson.getString("工资卡发放"));
                }else if(type.equals("bonus")){
                    Object yearMonthObj = getCellValue(row.getCell(5));
                    yearMonth = yearMonthObj != null ? yearMonthObj.toString() : "";
                    String[] bonusHeaders = {"签约奖", "结案奖", "海佣提成", "美高社大", "加拿大低龄",
                            "咨询外联（CRM）", "咨询外联（AIS）", "网申", "KPI", "加拿大产品",
                            "BD月度申请及助理奖金", "增长率", "悉尼推荐奖励", "保险推荐",
                            "学费支付", "BD年度利润考核", "BD年度Bonus利润", "住宿奖励",
                            "签证文案", "国际课程", "市场及非业务人员", "其他", "年终奖",
                            "扣款", "缓发","总计","备注"};

                    for (int j = 6; j < row.getLastCellNum(); j++) {
                        if (j - 6 < bonusHeaders.length) {
                            Object cellValueObj = getCellValue(row.getCell(j));
                            String value;
                            if (cellValueObj == null) {
                                value = "";
                            } else if (cellValueObj instanceof String) {
                                value = (String) cellValueObj;
                            } else if (cellValueObj instanceof Double) {
                                Double doubleValue = (Double) cellValueObj;
                                // 判断是否为整数
                                if (doubleValue.doubleValue() == doubleValue.intValue()) {
                                    value = String.valueOf(doubleValue.intValue());
                                } else {
                                    value = String.valueOf(doubleValue);
                                }
                            } else {
                                value = cellValueObj.toString();
                            }
                            detailJson.put(bonusHeaders[j - 6], value);
                        }
                    }
                    amount =new BigDecimal(detailJson.getString("总计"));
                }

                String remark = detailJson.getString("备注");

                // 构建实体并保存
                StaffSalaryBonusVo entity = new StaffSalaryBonusVo();
                entity.setYearMonth(yearMonth);
                entity.setAmount(amount);
                entity.setStaffNum(staffCode);
                entity.setStaffName(staffName);
                entity.setDepartmentName(departmentName);
                entity.setPostName(postName);
                entity.setRemark(remark);
                entity.setCompanyName(companyName);
                entity.setJsonDetail(detailJson.toJSONString());
                staffSalaryBonusVos.add(entity);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
       return  staffSalaryBonusVos;
    }
}
