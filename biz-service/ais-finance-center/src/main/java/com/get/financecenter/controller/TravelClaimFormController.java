package com.get.financecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.TravelClaimFormDto;
import com.get.financecenter.dto.query.TravelClaimFormQueryDto;
import com.get.financecenter.service.TravelClaimFormService;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.vo.TravelClaimFeeTypeVo;
import com.get.financecenter.vo.TravelClaimFormVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Api(tags = "差旅报销申请单管理")
@RestController
@RequestMapping("finance/travelClaimForm")
public class TravelClaimFormController {
    @Resource
    private TravelClaimFormService travelClaimFormService;


    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/差旅报销申请单管理/查询差旅报销申请单")
    @PostMapping("datas")
    public ResponseBo<TravelClaimFormVo> datas(@RequestBody SearchBean<TravelClaimFormQueryDto> page) {
        List<TravelClaimFormVo> datas = travelClaimFormService.getTravelClaimForms(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "新增差旅报销申请单", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/差旅报销申请单管理/新增差旅报销申请单")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated TravelClaimFormDto travelClaimFormDto) {
        return SaveResponseBo.ok(travelClaimFormService.addTravelClaimForm(travelClaimFormDto));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "差旅报销费用类型下拉框数据", notes = "")
    @GetMapping("getTravelClaimFeeTypeSelect")
    public ResponseBo<TravelClaimFeeTypeVo> getTravelClaimFeeTypeSelect() {
        List<TravelClaimFeeTypeVo> datas = travelClaimFormService.getTravelClaimFeeTypeSelect();
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "更新差旅报销申请单", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "财务中心/差旅报销申请单管理/更新差旅报销申请单")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated TravelClaimFormDto travelClaimFormDto) {
        travelClaimFormService.updateTravelClaimForm(travelClaimFormDto);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "差旅报销申请单详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/差旅报销申请单管理/差旅报销申请单详情")
    @GetMapping("/{id}")
    public ResponseBo<TravelClaimFormVo> detail(@PathVariable("id") Long id) {
        TravelClaimFormVo data = travelClaimFormService.findTravelClaimFormById(id);
        return new ResponseBo<>(data);
    }

    @ApiOperation(value = "查询差旅报销申请单附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.LIST, description = "财务中心/差旅报销申请单管理/查询差旅报销申请单附件")
    @PostMapping("getItemMedia")
    public ResponseBo<FMediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<FMediaAndAttachedVo> staffMedia = travelClaimFormService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    @ApiOperation(value = "保存差旅报销申请单附件")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/差旅报销申请单管理/保存附件")
    @PostMapping("upload")
    public ResponseBo<FMediaAndAttachedVo> addItemMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(travelClaimFormService.addItemMedia(mediaAttachedVo));
    }

    @ApiOperation(value = "开启差旅报销申请单流程", notes = "")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/差旅报销申请单管理/开启差旅报销申请单流程")
    @PostMapping("startProcess")
    public ResponseBo startProcess(@RequestParam("companyId") Long companyId, @RequestParam("businessKey") Long businessKey, @RequestParam("procdefKey") String procdefKey) {
        travelClaimFormService.startProcess(companyId, businessKey, procdefKey);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "作废接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.DELETE, description = "财务中心/差旅报销申请单管理/作废")
    @GetMapping("updateStatus/{id}")
    public ResponseBo updateStatus(@PathVariable("id") Long id) {
        travelClaimFormService.updateStatus(id);
        return DeleteResponseBo.ok();
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "撤单", notes = "id为被撤销工休单的id")
    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.ADD, description = "财务中心/差旅报销申请单管理/撤单")
    @GetMapping("getRevokeExpenseClaimForm")
    public ResponseBo getRevokeExpenseClaimForm(@RequestParam("id") Long id, @RequestParam("summary") String summary) {
        travelClaimFormService.getRevokeExpenseClaimForm(id, summary);
        return ResponseBo.ok();
    }


}
