<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.TravelClaimFeeTypeMapper">
    <select id="getTravelClaimFeeTypes" resultType="com.get.financecenter.vo.TravelClaimFeeTypeVo">
        select
        utcft.id,
        utcft.type_name,
        utcft.fk_accounting_item_id,
        utcft.view_order,
        utcft.gmt_create_user,
        utcft.gmt_create,
        utcft.gmt_modified_user,
        utcft.gmt_modified
        from u_travel_claim_fee_type utcft
        where 1=1
        <if test="travelClaimFeeTypeDto.fkAccountingItemId != null and travelClaimFeeTypeDto.fkAccountingItemId != ''">
            and utcft.fk_accounting_item_id = #{travelClaimFeeTypeDto.fkAccountingItemId}
        </if>
        <if test="travelClaimFeeTypeDto.typeName != null and travelClaimFeeTypeDto.typeName != ''">
            and utcft.type_name like concat('%',#{travelClaimFeeTypeDto.typeName},'%')
        </if>
        <if test="travelClaimFeeTypeDto.keyWord != null and travelClaimFeeTypeDto.keyWord != ''">
            and (utcft.type_name like concat('%',#{travelClaimFeeTypeDto.keyWord},'%')
            or utcft.gmt_create_user like concat('%',#{travelClaimFeeTypeDto.keyWord},'%')
            or utcft.gmt_modified_user like concat('%',#{travelClaimFeeTypeDto.keyWord},'%'))
        </if>
        order by utcft.view_order desc
    </select>
    <select id="getMaxViewOrder" resultType="java.lang.Integer">
        SELECT
            IFNULL(max(view_order)+1,0) view_order
        FROM u_travel_claim_fee_type
    </select>

    <select id="selectByTypeName" resultType="int">
        select count(*) from u_travel_claim_fee_type utcft where utcft.type_name = #{typeName}
    </select>

    <select id="checkName" resultType="int">
        select count(*) from u_travel_claim_fee_type utcft where utcft.type_name = #{typeName}
    </select>

    <update id="updateBatchById">
        <foreach collection="updateList" item="item" index="index" separator=";">
            UPDATE u_travel_claim_fee_type
            SET
            <if test="item.viewOrder != null and item.viewOrder !=''">
                view_order = #{item.viewOrder},
            </if>
            gmt_modified = #{item.gmtModified},
            gmt_modified_user = #{item.gmtModifiedUser}
            WHERE id = #{item.id}
        </foreach>
    </update>

    <select id="selectByFkAccountingItemId" resultType="com.get.financecenter.entity.TravelClaimFeeType">
        select * from u_travel_claim_fee_type where fk_accounting_item_id = #{fkAccountingItemId}
    </select>
</mapper>