package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.core.mybatis.utils.ValidList;
import com.get.financecenter.dto.ExpenseClaimFormDto;
import com.get.financecenter.dto.MediaAndAttachedDto;
import com.get.financecenter.dto.query.ExpenseClaimFormQueryDto;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.vo.ExpenseClaimFormVo;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/7 10:47
 * @verison: 1.0
 * @description:
 */
public interface IExpenseClaimFormService extends BaseService<ExpenseClaimForm> {
    /**
     * @return com.get.financecenter.vo.ExpenseClaimFormDto
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    ExpenseClaimFormVo findExpenseClaimFormById(Long id);

    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [expenseClaimFormVo]
     * <AUTHOR>
     */
    Long addExpenseClaimForm(ExpenseClaimFormDto expenseClaimFormDto);

    /**
     * @return com.get.financecenter.vo.ExpenseClaimFormDto
     * @Description :修改
     * @Param [expenseClaimFormVo]
     * <AUTHOR>
     */
    ExpenseClaimFormVo updateExpenseClaimForm(ExpenseClaimFormDto expenseClaimFormDto);

    /**
     * @return java.util.List<com.get.financecenter.vo.ExpenseClaimFormDto>
     * @Description :列表
     * @Param [expenseClaimFormVo, page]
     * <AUTHOR>
     */
    List<ExpenseClaimFormVo> getExpenseClaimForms(ExpenseClaimFormQueryDto expenseClaimFormVo, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.lang.Long
     * @Description: 新增附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<FMediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return void
     * @Description :开启费用报销单流程
     * @Param [companyId, businessKey, procdefKey]
     * <AUTHOR>
     */
    void startProcess(Long companyId, Long businessKey, String procdefKey);


    /**
     * @return void
     * @Description :作废
     * @Param [id]
     * <AUTHOR>
     */
    void updateStatus(Long id);

    /**
     * @return ExpenseClaimFormDto
     * @Description :报销单撤单
     * @Param [id]
     * <AUTHOR>
     */
    void getRevokeExpenseClaimForm(Long id, String summary);

    /**
     * 根据id获取费用报销单
     * @param targetId
     * @return
     */
    ExpenseClaimForm getExpenseClaimFormById(Long targetId);

    /**
     * 修改费用报销单状态
     * @param expenseClaimForm
     * @return
     */
    Boolean updateExpenseClaimFormStatus(ExpenseClaimForm expenseClaimForm);

    /**
     * 获取费用报销单总金额
     * @param id
     * @return
     */
    BigDecimal getExpenseClaimFormTotalAmount(Long id);
}
