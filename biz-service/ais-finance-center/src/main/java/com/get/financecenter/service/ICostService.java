package com.get.financecenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.financecenter.dto.SearchActivityDataDto;
import com.get.financecenter.vo.CostVo;
import java.util.List;
import javax.validation.Valid;

public interface ICostService {

    /**
     * 通过活动信息分页查询费用列表（费用报销和差旅费用报销）
     * @param searchActivityDataDto
     * @param page
     * @return
     */
    List<CostVo> getCostByActivityData(@Valid SearchActivityDataDto searchActivityDataDto, Page page);

    ResponseBo getCostTotalAmount(@Valid SearchActivityDataDto data);
}
