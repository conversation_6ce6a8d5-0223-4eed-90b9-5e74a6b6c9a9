<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.BankAccountMapper">
  <insert id="insertSelective" parameterType="com.get.financecenter.entity.BankAccount" keyProperty="id" useGeneratedKeys="true">
    insert into u_bank_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkCompanyId != null">
        fk_company_id,
      </if>
      <if test="fkCurrencyTypeNum != null">
        fk_currency_type_num,
      </if>
      <if test="bankAccount != null">
        bank_account,
      </if>
      <if test="bankAccountNum != null">
        bank_account_num,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="bankBranchName != null">
        bank_branch_name,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id,
      </if>
      <if test="fkAreaCityId != null">
        fk_area_city_id,
      </if>
      <if test="fkAreaCityDivisionId != null">
        fk_area_city_division_id,
      </if>
      <if test="bankAddress != null">
        bank_address,
      </if>
      <if test="swiftCode != null">
        swift_code,
      </if>
      <if test="otherCode != null">
        other_code,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkCompanyId != null">
        #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="fkCurrencyTypeNum != null">
        #{fkCurrencyTypeNum,jdbcType=VARCHAR},
      </if>
      <if test="bankAccount != null">
        #{bankAccount,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountNum != null">
        #{bankAccountNum,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="bankBranchName != null">
        #{bankBranchName,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityId != null">
        #{fkAreaCityId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityDivisionId != null">
        #{fkAreaCityDivisionId,jdbcType=BIGINT},
      </if>
      <if test="bankAddress != null">
        #{bankAddress,jdbcType=VARCHAR},
      </if>
      <if test="swiftCode != null">
        #{swiftCode,jdbcType=VARCHAR},
      </if>
      <if test="otherCode != null">
        #{otherCode,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
      IFNULL(max(view_order)+1,0) view_order
    from
      u_bank_account
    where
      fk_company_id = #{companyId}

  </select>
  <select id="getBankAccountSelect" resultType="com.get.financecenter.vo.BankAccountVo">
    SELECT
      ba.id id,
      CONCAT(
              '【',
              CASE
                WHEN IFNULL(ct.type_name, '') = '' THEN
                  ct.num
                ELSE
                  CONCAT(
                          ct.num,
                          '（',
                          ct.type_name,
                          '）'
                    )
                END,
              '】',
              ba.bank_account,
              ',',
              ba.bank_name
        ) bankName
    FROM
      u_bank_account ba,
      u_currency_type ct
    WHERE
      ba.fk_currency_type_num = ct.num
      AND ba.fk_company_id =#{fkCompanyId} and  ba.is_active = 1
  </select>
  <select id="getBankAccountNameById" resultType="java.lang.String">
    SELECT
      CONCAT(
              '【',
              CASE
                WHEN IFNULL(ct.type_name, '') = '' THEN
                  ct.num
                ELSE
                  CONCAT(
                          ct.num,
                          '（',
                          ct.type_name,
                          '）'
                    )
                END,
              '】',
              ba.bank_account,
              ',',
              ba.bank_name
        ) bankName
    FROM
      u_bank_account ba,
      u_currency_type ct
    WHERE
      ba.fk_currency_type_num = ct.num
      AND ba.id =#{id}
  </select>

  <select id="getBankNameByIds" resultType="com.get.financecenter.vo.BankAccountVo">
    SELECT
        ba.id,
        CONCAT(
                '【',
                CASE
                  WHEN IFNULL(ct.type_name, '') = '' THEN
                    ct.num
                  ELSE
                    CONCAT(
                            ct.num,
                            '（',
                            ct.type_name,
                            '）'
                    )
                  END,
                '】',
                ba.bank_account,
                ',',
                ba.bank_name
        ) bankName
    FROM
      u_bank_account ba,
      u_currency_type ct
    WHERE
        ba.fk_currency_type_num = ct.num
        AND ba.id IN
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
  </select>

</mapper>