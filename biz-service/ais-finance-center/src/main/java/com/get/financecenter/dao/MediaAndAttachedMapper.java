package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.vo.FMediaAndAttachedVo;
import com.get.financecenter.entity.FMediaAndAttached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MediaAndAttachedMapper extends BaseMapper<FMediaAndAttached> {

    /**
     * 添加
     *
     * @param record
     * @return
     */

    int insertSelective(FMediaAndAttached record);

    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);

    List<FMediaAndAttachedVo> getMediaAndAttachedList(@Param("fkTableId") Long fkTableId);
}