package com.get.financecenter.service;

import com.get.core.secure.StaffInfo;
import com.get.financecenter.dto.ProfitAndLossStatementDto;
import com.get.financecenter.dto.RecomputeProfitLossStatementDto;
import com.get.financecenter.entity.StageValueTaskQueue;
import com.get.financecenter.vo.FinancialStatsAsyncVo;
import com.get.financecenter.vo.ProfitAndLossStatementVo;

public interface ProfitAndLossService {

    /**
     * 创建损益表
     *
     * @param probAndLossStatementDto
     * @return
     */
    ProfitAndLossStatementVo createProfitAndLossStatement(ProfitAndLossStatementDto probAndLossStatementDto);

    /**
     * 重新统计损益表
     *
     * @param recomputeProfitLossStatement
     * @return
     */
    void recomputeProfitLossStatement(RecomputeProfitLossStatementDto recomputeProfitLossStatement);

    /**
     * 异步信息查询
     * @param fkTableName
     * @return
     */
    FinancialStatsAsyncVo financialStatsAsyncQuery(String fkTableName);

    void recomputeProfitLossStatementAsync(RecomputeProfitLossStatementDto recomputeProfitLossStatement, StaffInfo staffInfo, StageValueTaskQueue stageValueTaskQueue);
}
