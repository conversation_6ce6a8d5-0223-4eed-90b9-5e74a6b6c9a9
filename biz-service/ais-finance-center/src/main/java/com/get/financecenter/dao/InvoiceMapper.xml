<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.financecenter.dao.InvoiceMapper">
  <resultMap id="BaseResultMap" type="com.get.financecenter.entity.Invoice">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="local_invoice_num" jdbcType="VARCHAR" property="localInvoiceNum" />
    <result column="fk_type_key" jdbcType="VARCHAR" property="fkTypeKey" />
    <result column="type_target_payment_num" jdbcType="VARCHAR" property="typeTargetPaymentNum" />
    <result column="invoice_date" jdbcType="DATE" property="invoiceDate" />
    <result column="receipt_date" jdbcType="DATE" property="receiptDate" />
    <result column="fk_currency_type_num" jdbcType="VARCHAR" property="fkCurrencyTypeNum" />
    <result column="fk_currency_type_num_orc" jdbcType="VARCHAR" property="fkCurrencyTypeNumOrc" />
    <result column="invoice_amount" jdbcType="DECIMAL" property="invoiceAmount" />
    <result column="summary" jdbcType="VARCHAR" property="summary" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

  <select id="getReceivablePlanIds" resultType="java.lang.Long">
        SELECT fk_receivable_plan_id from r_invoice_receivable_plan where fk_invoice_id=#{fkInvoiceId}
  </select>


  <select id="getFkInvoiceNum" resultType="com.get.financecenter.vo.InvoiceReceivablePlanVo">
    SELECT rirp.*,
    GROUP_CONCAT(DISTINCT mi.num) AS fkInvoiceNum
    FROM r_invoice_receivable_plan AS rirp
    LEFT JOIN m_invoice AS mi
    ON rirp.fk_invoice_id=mi.id
    <where>
      <if test="planIds!=null">
        rirp.fk_receivable_plan_id IN
        <foreach collection="planIds" item="item" open="(" separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    GROUP BY rirp.fk_receivable_plan_id
  </select>
  <select id="getInvoiceSelectByTargetId" resultType="com.get.financecenter.vo.InvoiceSelectVo">
    SELECT mi.id,CASE WHEN mi.status=1 THEN mi.num ELSE CONCAT(mi.num,'【作废】') END num,mi.invoice_amount,mi.fk_currency_type_num
    FROM m_invoice mi
    LEFT JOIN r_receipt_form_invoice ri
    ON mi.id=ri.fk_invoice_id
    <if test="fkReceiptFormId != null and fkReceiptFormId != ''">
      AND ri.fk_receipt_form_id &lt;&gt; #{fkReceiptFormId}
    </if>
    WHERE mi.fk_type_key=#{fkTypeKey}
    AND mi.fk_type_target_id=#{fkTypeTargetId}
    AND mi.status=1
    AND ri.fk_receipt_form_id IS NULL
  </select>

  <select id="getInvoiceSelect" resultType="com.get.financecenter.vo.InvoiceSelectVo">
    SELECT
	    mi.id,
	CASE
    WHEN mi. STATUS = 1 THEN
        CONCAT(mi.num,"(",mi.invoice_amount,mi.fk_currency_type_num,IF(GROUP_CONCAT(DISTINCT ip.`name`) is null,'',CONCAT(',',GROUP_CONCAT(DISTINCT ip.`name`))),IF(mi.summary is null or mi.summary='','',CONCAT("，摘要：",mi.summary)),")")
    ELSE
	    CONCAT(mi.num, '【作废】')
    END num,
        mi.invoice_amount,
        mi.fk_currency_type_num,
    CASE
        WHEN eb.id is not null
            then 1
        else 0
        END isActivityType
	FROM
		m_invoice mi
    INNER JOIN r_invoice_type_key_target ri ON ri.fk_invoice_id = mi.id
    AND ri.fk_type_target_id = #{invoiceReceiptFormDto.fkTypeTargetId}
    AND ri.fk_type_key = #{invoiceReceiptFormDto.fkTypeKey}
    LEFT JOIN
        ais_sale_center.m_event_bill eb on eb.fk_invoice_num = mi.num
    LEFT JOIN
        r_invoice_type_key_target itkt on itkt.fk_invoice_id = mi.id

    LEFT JOIN
        ais_institution_center.m_institution_provider	ip on itkt.fk_type_key = 'm_institution_provider' and itkt.fk_type_target_id = ip.id
    LEFT join r_receipt_form_invoice rfi on mi.id = rfi.fk_invoice_id
    WHERE
      (mi. STATUS = 1
      OR (mi.id = - 1))
      <!--发票有绑定应收金额则不显示,同时需要过滤历史记录-->
      and (rfi.id is null
      <if test="invoiceReceiptFormDto.fkInvoiceIds!=null and invoiceReceiptFormDto.fkInvoiceIds.size>0">
          or mi.id in
          <foreach collection="invoiceReceiptFormDto.fkInvoiceIds" item="item" open="(" close=")" separator=",">
              #{item}
          </foreach>
      </if>
      )
      <if test="invoiceReceiptFormDto.fkCompanyId!=null and invoiceReceiptFormDto.fkCompanyId!=''">
          AND mi.fk_company_id = #{invoiceReceiptFormDto.fkCompanyId}
      </if>
      GROUP BY mi.id
    ORDER BY
        mi.gmt_create DESC
  </select>

  <select id="getInvoiceTotalAmountByIds" resultType="java.math.BigDecimal">
    SELECT sum(invoice_amount) from m_invoice where id in
    <foreach collection="list" separator="," item="item"  open="(" close=")">
      #{item}
    </foreach>
  </select>
    <select id="getReceivablePlanByInvoice" resultType="com.get.salecenter.vo.ReceivablePlanVo">
        SELECT
         a.id,
         a.fk_type_key,
         a.fk_type_target_id,
         a.fk_currency_type_num,
         a.fk_company_id,
         a.tuition_amount,
         a.commission_rate,
         a.net_rate,
         a.commission_amount,
         a.fixed_amount,
         a.bonus_type,
         a.bonus_amount,
         a.receivable_plan_date,
         a.id_gea_finance,
         a.gmt_modified,
         a.gmt_modified_user,
         a.summary,
         a.gmt_create_user,
         a.gmt_create,
         c.type_name,
         r.id as record_id,
         r.amount,
        a.status,
        SUM(IFNULL(a.receivable_amount,0)) receivable_amount, <!--#应收金额-->
        SUM(IFNULL(b.sum_amount_receivable,0)) sum_amount_receivable, <!--#实收折合金额（这个金额币种和应收一致，因为已经是折合了）-->
        SUM(IFNULL(b.sum_amount_exchange_rate,0)) sum_amount_exchange_rate, <!--#汇率调整-->
        SUM(IFNULL(b.sum_amount_hkd,0)) sum_amount_hkd, <!--#港币金额-->
        SUM(IFNULL(b.sum_amount_rmb,0)) sum_amount_rmb, <!--#人民币金额-->
        (SUM(IFNULL(b.sum_amount_receivable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0))) actual_receivable_amount, <!--#实收（折合金额+汇率调整）-->
        (SUM(IFNULL(b.sum_amount_receivable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0)) - SUM(IFNULL(a.receivable_amount,0))) diff_receivable_amount,<!--#差额-->
        CASE
        WHEN  (SUM(IFNULL(b.sum_amount_receivable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0)) - SUM(IFNULL(a.receivable_amount,0)))=0.00 THEN 2
        WHEN (SUM(IFNULL(b.sum_amount_receivable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0)) - SUM(IFNULL(a.receivable_amount,0))) > (-SUM(IFNULL(a.receivable_amount,0))) 	THEN 1
        ELSE 0 END as receiveStatus
        FROM ais_sale_center.m_receivable_plan a
        INNER JOIN r_invoice_receivable_plan  r on a.id = r.fk_receivable_plan_id and r.fk_invoice_id=#{fkInvoiceId}
        LEFT JOIN (
        <!--#计算每条应收计划里累计的实收金额-->
        SELECT a.fk_receivable_plan_id,
        SUM(IFNULL(a.amount_receivable,0)) sum_amount_receivable,
        SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd,0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb,0)) sum_amount_rmb
        FROM ais_finance_center.m_receipt_form_item a
        LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id=b.id
        WHERE b.`status`!=0 <!--关闭的收款单不作计算-->
        GROUP BY a.fk_receivable_plan_id
        ) b ON a.id=b.fk_receivable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
        WHERE 1=1
        GROUP BY r.id
    </select>
    <select id="getAllInvoice" resultType="com.get.financecenter.vo.InvoiceSelectVo">
        SELECT
            mi.id,
            mi.num,
            mi.fk_type_key,
            mi.gmt_create
        FROM
            m_invoice mi
        INNER JOIN r_invoice_type_key_target rt ON mi.id = rt.fk_invoice_id
        WHERE
            1=1
             <if test="invoiceIds!=null and invoiceIds.size>0">
                 AND rt.fk_invoice_id IN
                 <foreach collection="invoiceIds" item="item" open="(" close=")" separator=",">
                     #{item}
                 </foreach>
             </if>
        AND mi.fk_company_id = #{fkCompanyId}
        AND mi. STATUS = 1
        GROUP BY
            rt.fk_invoice_id
    </select>
    <select id="getMaxPoNum" resultType="java.lang.Integer">
        SELECT
            IFNULL(MAX(po_num+0)+1,1) po_num
        FROM
            m_invoice
        where num like concat("%",#{num},"%")
    </select>
    <select id="getInvoiceList" resultType="com.get.financecenter.entity.Invoice">
        SELECT i.*
        FROM m_invoice i
        <if test="invoiceDto.isAccount!=null">
            <choose>
                <when test="invoiceDto.isAccount">
                    INNER JOIN r_receipt_form_invoice f ON i.id = f.fk_invoice_id
                </when>
                <otherwise>
                    LEFT JOIN r_receipt_form_invoice f ON i.id = f.fk_invoice_id
                </otherwise>
            </choose>
        </if>
        INNER JOIN r_invoice_type_key_target t ON t.fk_invoice_id = i.id
        <if test="(invoiceDto.businessCountryId!=null and invoiceDto.businessCountryId!='')
        or (invoiceDto.studentName!=null and invoiceDto.studentName!='')">
            INNER JOIN r_invoice_receivable_plan rp ON rp.fk_invoice_id = i.id
            INNER JOIN(
                SELECT DISTINCT p.id FROM ais_sale_center.m_receivable_plan p
                INNER JOIN ais_sale_center.m_student_offer_item f ON f.id = p.fk_type_target_id
                <if test="invoiceDto.studentName!=null and invoiceDto.studentName!=''">
                    INNER JOIN ais_sale_center.m_student a ON a.id = f.fk_student_id
                    AND (
                    REPLACE(CONCAT(LOWER(a.first_name),LOWER(a.last_name)),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(CONCAT(LOWER(a.last_name),LOWER(a.first_name)),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(LOWER(a.`name`),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(LOWER(a.last_name),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(LOWER(a.first_name),' ','') like concat('%',#{invoiceDto.studentName},'%')) -- 过滤学生中英文名字
                </if>
                WHERE
                    p.fk_type_key = 'm_student_offer_item'
                <if test="invoiceDto.businessCountryId!=null and invoiceDto.businessCountryId!=''">
                    AND f.fk_area_country_id = #{invoiceDto.businessCountryId}
                </if>
                UNION ALL
                SELECT DISTINCT p.id FROM ais_sale_center.m_receivable_plan p
                INNER JOIN ais_sale_center.m_student_insurance f ON f.id = p.fk_type_target_id
                <if test="invoiceDto.studentName!=null and invoiceDto.studentName!=''">
                    INNER JOIN ais_sale_center.m_student a ON a.id = f.fk_student_id
                    AND (
                    REPLACE(CONCAT(LOWER(a.first_name),LOWER(a.last_name)),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(CONCAT(LOWER(a.last_name),LOWER(a.first_name)),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(LOWER(a.`name`),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(LOWER(a.last_name),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(LOWER(a.first_name),' ','') like concat('%',#{invoiceDto.studentName},'%')) -- 过滤学生中英文名字
                </if>
                WHERE
                p.fk_type_key = 'm_student_insurance'
            <if test="invoiceDto.businessCountryId!=null and invoiceDto.businessCountryId!=''">
                AND f.fk_area_country_id = #{invoiceDto.businessCountryId}
            </if>

                UNION ALL
                SELECT DISTINCT p.id FROM ais_sale_center.m_receivable_plan p
                INNER JOIN ais_sale_center.m_student_accommodation f ON f.id = p.fk_type_target_id
                <if test="invoiceDto.studentName!=null and invoiceDto.studentName!=''">
                    INNER JOIN ais_sale_center.m_student a ON a.id = f.fk_student_id
                    AND (
                    REPLACE(CONCAT(LOWER(a.first_name),LOWER(a.last_name)),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(CONCAT(LOWER(a.last_name),LOWER(a.first_name)),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(LOWER(a.`name`),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(LOWER(a.last_name),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(LOWER(a.first_name),' ','') like concat('%',#{invoiceDto.studentName},'%')) -- 过滤学生中英文名字
                </if>
                WHERE
                p.fk_type_key = 'm_student_accommodation'
            <if test="invoiceDto.businessCountryId!=null and invoiceDto.businessCountryId!=''">
                AND f.fk_area_country_id = #{invoiceDto.businessCountryId}
            </if>

                UNION ALL
                SELECT DISTINCT p.id FROM ais_sale_center.m_receivable_plan p
                INNER JOIN ais_sale_center.m_student_service_fee f ON f.id = p.fk_type_target_id
                <if test="invoiceDto.studentName!=null and invoiceDto.studentName!=''">
                    INNER JOIN ais_sale_center.m_student a ON a.id = f.fk_student_id
                    AND (
                    REPLACE(CONCAT(LOWER(a.first_name),LOWER(a.last_name)),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(CONCAT(LOWER(a.last_name),LOWER(a.first_name)),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(LOWER(a.`name`),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(LOWER(a.last_name),' ','') like concat('%',#{invoiceDto.studentName},'%')
                    OR REPLACE(LOWER(a.first_name),' ','') like concat('%',#{invoiceDto.studentName},'%')) -- 过滤学生中英文名字
                </if>
                WHERE
                p.fk_type_key = 'm_student_service_fee'
            <if test="invoiceDto.businessCountryId!=null and invoiceDto.businessCountryId!=''">
                AND FIND_IN_SET(#{invoiceDto.businessCountryId},f.fk_area_country_ids) > 0
            </if>
                <if test="(invoiceDto.businessCountryId!=null and invoiceDto.businessCountryId!='') and (invoiceDto.studentName==null or invoiceDto.studentName=='')">
                    UNION ALL
                    SELECT DISTINCT p.id FROM ais_sale_center.m_receivable_plan p
                    INNER JOIN ais_institution_center.m_institution_provider f ON f.id = p.fk_type_target_id
                    WHERE
                    p.fk_type_key = 'm_institution_provider'
                    AND f.fk_area_country_id = #{invoiceDto.businessCountryId}
                </if>
            ) p ON p.id = rp.fk_receivable_plan_id
        </if>
        WHERE
            1=1
            <if test="invoiceDto.fkCompanyIds!=null and invoiceDto.fkCompanyIds.size>0">
                AND i.fk_company_id IN
                <foreach collection="invoiceDto.fkCompanyIds" item="cid" open="(" close=")" separator=",">
                    #{cid}
                </foreach>
            </if>
            <if test="invoiceDto.isAccount!=null and !invoiceDto.isAccount">
                AND f.fk_invoice_id IS NULL
            </if>
            <if test="invoiceDto.num!=null and invoiceDto.num!=''">
                AND i.num LIKE CONCAT('%',#{invoiceDto.num},'%')
            </if>
            <if test="invoiceDto.fkCurrencyTypeNum!=null and invoiceDto.fkCurrencyTypeNum!=''">
                AND i.fk_currency_type_num = #{invoiceDto.fkCurrencyTypeNum}
            </if>
            <if test="invoiceDto.invoiceAmount!=null and invoiceDto.invoiceAmount!=''">
                AND i.invoice_amount = #{invoiceDto.invoiceAmount}
            </if>
            <if test="invoiceDto.fkTypeKey!=null and invoiceDto.fkTypeKey!=''">
                AND i.fk_type_key = #{invoiceDto.fkTypeKey}
            </if>
            <if test="invoiceDto.fkTypeTargetId!=null and invoiceDto.fkTypeTargetId!=''">
                AND t.fk_type_target_id = #{invoiceDto.fkTypeTargetId}
            </if>
            <!--查询条件-开始日期-->
            <if test="invoiceDto.invoiceDateBeg != null and invoiceDto.invoiceDateBeg.toString() !=''">
                AND DATE_FORMAT(i.invoice_date,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{invoiceDto.invoiceDateBeg},'%Y-%m-%d')
            </if>
            <!--查询条件-结束日期-->
            <if test="invoiceDto.invoiceDateEnd!= null and invoiceDto.invoiceDateEnd.toString() !=''">
                AND DATE_FORMAT(i.invoice_date,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{invoiceDto.invoiceDateEnd},'%Y-%m-%d')
            </if>
            <if test="invoiceDto.summary!=null and invoiceDto.summary!=''">
                AND i.summary LIKE CONCAT('%',#{invoiceDto.summary},'%')
            </if>
<!--            <if test="invoiceDto.invoiceIds!=null and invoiceDto.invoiceIds.size>0">-->
<!--                AND i.id IN-->
<!--                <foreach collection="invoiceDto.invoiceIds" item="cid" open="(" close=")" separator=",">-->
<!--                    #{cid}-->
<!--                </foreach>-->
<!--            </if>-->
            <if test="invoiceDto.status!=null">
                AND i.status = #{invoiceDto.status}
            </if>
            <if test="invoiceDto.createUser!=null and invoiceDto.createUser!=''">
                AND i.gmt_create_user like CONCAT('%',#{invoiceDto.createUser},'%')
            </if>
            GROUP BY i.id
            ORDER BY i.status DESC,i.gmt_create DESC

    </select>
    <select id="getInvoiceIdsByReceivablePlanId" resultType="java.lang.Long">
        SELECT
            fk_invoice_id
        FROM
            `r_invoice_receivable_plan`
        where fk_receivable_plan_id = #{receivablePlanId}
    </select>
    <select id="getMaxVouchQty" resultType="java.lang.Integer">
        SELECT
            IFNULL(MAX(vouch_qty),0) + 1
        FROM
            `m_vouch`
        WHERE
            vouch_month = #{vouchMonth}
    </select>
</mapper>