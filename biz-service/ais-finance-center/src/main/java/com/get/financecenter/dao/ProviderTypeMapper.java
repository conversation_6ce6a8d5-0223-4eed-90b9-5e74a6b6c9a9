package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.ProviderType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ProviderTypeMapper extends BaseMapper<ProviderType> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */

    int insertSelective(ProviderType record);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * @return java.lang.String
     * @Description :根据id查找对应供应商类型名称
     * @Param [providerTypeId]
     * <AUTHOR>
     */
    String getProviderTypeNameById(@Param("providerTypeId") Long providerTypeId);
}