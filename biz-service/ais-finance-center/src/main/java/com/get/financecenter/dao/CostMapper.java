package com.get.financecenter.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.dto.SearchActivityDataDto;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.vo.CostVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface CostMapper {

    List<CostVo> getCostByActivityData(@Param("searchActivityDataDto") SearchActivityDataDto searchActivityDataDto,@Param("statusList") List<Integer> statusList, IPage<ExpenseClaimForm> pages);
}
