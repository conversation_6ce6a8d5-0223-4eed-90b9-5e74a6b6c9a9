package com.get.votingcenter.service;

import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.votingcenter.vo.VotingVo;
import com.get.votingcenter.dto.VotingListDto;
import com.get.votingcenter.dto.VotingUpdateDto;

import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2021/9/23 10:57
 * @verison: 1.0
 * @description:
 */
public interface IVotingService {

    /**
     * @return Long
     * @Description :新增投票主题
     * @Param [votingUpdateDto]
     * <AUTHOR>
     */
    Long addVoting(VotingUpdateDto votingUpdateDto);

    /**
     * @return VotingVo
     * @Description :修改
     * @Param [votingUpdateDto]
     * <AUTHOR>
     */
    VotingVo updateVoting(VotingUpdateDto votingUpdateDto);

    /**
     * @return VotingVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    VotingVo findVotingById(Long id);

    /**
     * @return List<VotingVo>
     * @Description :列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<VotingVo> getVotings(VotingListDto data, SearchBean<VotingListDto> page);

    /**
     * @return void
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    void deleteVoting(Long id);

    /**
     * @return List<BaseSelectEntity>
     * @Description :投票主题下拉框
     * @Param
     * <AUTHOR>
     */
    List<BaseSelectEntity> votingSelect(VotingListDto votingListDto);
}
