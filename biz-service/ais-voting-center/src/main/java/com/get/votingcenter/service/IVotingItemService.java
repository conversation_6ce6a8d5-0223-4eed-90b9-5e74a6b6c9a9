package com.get.votingcenter.service;

import com.get.common.result.SearchBean;
import com.get.votingcenter.vo.VotingItemVo;
import com.get.votingcenter.dto.VotingItemListDto;
import com.get.votingcenter.dto.VotingItemUpdateDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/9/24 11:18
 * @verison: 1.0
 * @description:
 */
public interface IVotingItemService {

    /**
     * @return Long
     * @Description :新增投票项
     * @Param [votingItemUpdateDto]
     * <AUTHOR>
     */
    Long addVotingItem(VotingItemUpdateDto votingItemUpdateDto);

    /**
     * @return VotingItemVo
     * @Description :修改
     * @Param [votingItemUpdateDto]
     * <AUTHOR>
     */
    VotingItemVo updateVotingItem(VotingItemUpdateDto votingItemUpdateDto);

    /**
     * @return VotingItemVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    VotingItemVo findVotingItemById(Long id);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteVotingItem(Long id);

    /**
     * @return List<VotingItemVo>
     * @Description :
     * @Param [votingItemListDto, page]
     * <AUTHOR>
     */
    List<VotingItemVo> getVotingItems(VotingItemListDto votingItemListDto, SearchBean<VotingItemListDto> page);

    /**
     * 上下移
     *
     * @param votingItemListDtos
     */
    void movingOrder(List<VotingItemListDto> votingItemListDtos);
}
