package com.get.votingcenter.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.aisplatformcenterap.feign.IPlatformCenterClient;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.platformconfigcenter.feign.IPlatformConfigCenterClient;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.votingcenter.dao.appvoting.UserVotingAwardMapper;
import com.get.votingcenter.dao.appvoting.UserVotingMapper;
import com.get.votingcenter.dao.voting.VotingItemMapper;
import com.get.votingcenter.dao.voting.VotingItemOptionMapper;
import com.get.votingcenter.vo.UserAwardVo;
import com.get.votingcenter.vo.UserVotingVo;
import com.get.votingcenter.entity.UserVotingAward;
import com.get.votingcenter.entity.VotingItem;
import com.get.votingcenter.entity.VotingItemOption;
import com.get.votingcenter.service.IUserAwardService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

;

/**
 * Created by Jerry.
 * Time: 10:24
 * Date: 2021/10/20
 * Description:随机抽奖管理实现类
 */
@Service
public class UserAwardServiceImpl implements IUserAwardService {

    @Resource
    private VotingItemMapper votingItemMapper;
    @Resource
    private VotingItemOptionMapper votingItemOptionMapper;
    @Resource
    private UserVotingMapper userVotingMapper;
    @Resource
    private UserVotingAwardMapper userVotingAwardMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ISaleCenterClient saleCenterClient;
//    @Resource
//    private IPlatformConfigCenterClient platformConfigCenterClient;
    @Resource
    private IPlatformCenterClient platformCenterClient;
    /**
     * @Description: 随机抽奖
     * @Author: Jerry
     * @Date:10:25 2021/10/20
     */
    @Override
    public void generateUserAward(Long fkVotingId, Integer generateCount) {
//        Example example = new Example(VotingItem.class);
//        example.createCriteria().andEqualTo("fkVotingId", fkVotingId);
//        example.orderBy("viewOrder").desc();
//        List<VotingItem> votingItems = votingItemMapper.selectByExample(example);

        List<VotingItem> votingItems = votingItemMapper.selectList(Wrappers.<VotingItem>lambdaQuery().eq(VotingItem::getFkVotingId, fkVotingId).orderByDesc(VotingItem::getViewOrder));
        if (GeneralTool.isEmpty(votingItems)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("voting_item_null"));
        }
        Set<Long> votingItemIds = votingItems.stream().map(VotingItem::getId).collect(Collectors.toSet());
        //根据投票项ids获取所有的投票选项id
//        example.clear();
//        example = new Example(VotingItemOption.class);
//        example.createCriteria().andIn("fkVotingItemId", votingItemIds);
//        example.orderBy("viewOrder").desc();
//        List<VotingItemOption> votingItemOptions = votingItemOptionMapper.selectByExample(example);
        List<VotingItemOption> votingItemOptions = votingItemOptionMapper.selectList(Wrappers.<VotingItemOption>lambdaQuery().in(VotingItemOption::getFkVotingItemId, votingItemIds).orderByDesc(VotingItemOption::getViewOrder));

        if (GeneralTool.isEmpty(votingItems)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("voting_item_option_null"));
        }
        Set<Long> votingItemOptionIds = votingItemOptions.stream().map(VotingItemOption::getId).collect(Collectors.toSet());
        //根据主题id查询哪些人员已抽过奖，如果抽取过则不能继续抽取
//        example.clear();
//        example = new Example(UserVotingAward.class);
//        example.createCriteria().andEqualTo("fkVotingId", fkVotingId);
//        List<UserVotingAward> userVotingAwards = userVotingAwardMapper.selectByExample(example);
        List<UserVotingAward> userVotingAwards = userVotingAwardMapper.selectList(Wrappers.<UserVotingAward>lambdaQuery().eq(UserVotingAward::getFkVotingId, fkVotingId));
        Set<Long> fkUserIds = null;
        //有记录，则代表这些人已经抽过奖，不允许再次抽奖
        if (GeneralTool.isNotEmpty(userVotingAwards)) {
            fkUserIds = userVotingAwards.stream().map(UserVotingAward::getFkUserId).collect(Collectors.toSet());
        }
        //生成抽奖人员
        List<UserVotingVo> userVotingVos = userVotingMapper.generateUserAward(votingItemOptionIds, fkUserIds, generateCount);
        if (GeneralTool.isEmpty(userVotingVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_more_person_luck_draw"));
        }
        UserVotingAward userVotingAward = null;
        String optKey = UUID.randomUUID().toString();
        //将生成的抽奖人员插入到抽奖记录表
        for (UserVotingVo userVotingVo : userVotingVos) {
            userVotingAward = new UserVotingAward();
            userVotingAward.setFkUserId(userVotingVo.getFkUserId());
            userVotingAward.setFkVotingId(fkVotingId);
            userVotingAward.setOptKey(optKey);
            utilService.updateUserInfoToEntity(userVotingAward);
            userVotingAwardMapper.insert(userVotingAward);
        }
    }


    /**
     * @Description: 抽奖列表数据
     * @Author: Jerry
     * @Date:11:05 2021/10/20
     */
    @Override
    public List<UserAwardVo> datas(Long fkVotingId) {
        List<UserAwardVo> userAwardVos = new ArrayList<>();
//        Example example = new Example(UserVotingAward.class);
//        example.createCriteria().andEqualTo("fkVotingId", fkVotingId);
//        List<UserVotingAward> userVotingAwards = userVotingAwardMapper.selectByExample(example);

        List<UserVotingAward> userVotingAwards = userVotingAwardMapper.selectList(Wrappers.<UserVotingAward>lambdaQuery().eq(UserVotingAward::getFkVotingId, fkVotingId));
        if (GeneralTool.isEmpty(userVotingAwards)) {
            return userAwardVos;
        }
        //获取用户ids
        Set<Long> fkUserIds = userVotingAwards.stream().map(UserVotingAward::getFkUserId).collect(Collectors.toSet());
        Map<Long, String> userNamesByUserIds = new HashMap<>();
        Map<Long, String> mobileByUserIds = new HashMap<>();
        Map<String, String> namesByMobiles = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkUserIds)) {
            //根据userid获取名称
            Result<Map<Long, String>> result1 = platformCenterClient.getUserNickNamesByUserIds(fkUserIds);
            if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
                userNamesByUserIds = result1.getData();
            }
            //根据userid获取手机号
            Result<Map<Long, String>> result2 = platformCenterClient.getMobileByUserIds(fkUserIds);
            if (result2.isSuccess() && GeneralTool.isNotEmpty(result2.getData())) {
                mobileByUserIds = result2.getData();
            }

        }
        //获取用户所有的手机号
        if (GeneralTool.isNotEmpty(mobileByUserIds)) {
            Set<String> mobiles = new HashSet<>();
            for (String mobile : mobileByUserIds.values()) {
                mobiles.add(mobile);
            }
            //根据手机号获取对应的峰会人员姓名
            Result<Map<String, String>> result = saleCenterClient.getNamesByMobiles(mobiles);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                namesByMobiles = result.getData();
            }
        }
        for (UserVotingAward userVotingAward : userVotingAwards) {
            UserAwardVo userAwardVo = BeanCopyUtils.objClone(userVotingAward, UserAwardVo::new);
            //用户名称
            userAwardVo.setFkUserName(userNamesByUserIds.get(userAwardVo.getFkUserId()));
            String mobile = mobileByUserIds.get(userAwardVo.getFkUserId());
            //手机号
            userAwardVo.setMobile(mobile);
            //峰会用户名称
            if (GeneralTool.isNotEmpty(mobile)) {
                userAwardVo.setConventionPersonName(namesByMobiles.get(mobile));
            }
            userAwardVos.add(userAwardVo);
        }
        return userAwardVos;
    }


    /**
     * @Description: 删除中奖人员名单
     * @Author: Jerry
     * @Date:16:54 2021/10/21
     */
    @Override
    public void delete(Long fkVotingAwardId) {
        if (GeneralTool.isEmpty(fkVotingAwardId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        UserVotingAward userVotingAward = userVotingAwardMapper.selectById(fkVotingAwardId);
        if (GeneralTool.isEmpty(userVotingAward)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        userVotingAwardMapper.deleteById(fkVotingAwardId);
    }
}
