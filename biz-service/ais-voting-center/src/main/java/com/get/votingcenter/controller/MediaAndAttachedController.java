package com.get.votingcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.votingcenter.service.IMediaAndAttachedService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2021/9/26 17:21
 * @verison: 1.0
 * @description:
 */
@Api(tags = "附件管理")
@RestController
@RequestMapping("voting/media")
public class MediaAndAttachedController {

    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传文件")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.ADD, description = "投票中心/上传文件")
    @PostMapping("/upload")
    public ResponseBo upload(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", mediaAndAttachedService.upload(files, LoggerModulesConsts.VOTINGCENTER));
        return responseBo;
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传附件")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.ADD, description = "投票中心/上传附件")
    @PostMapping("/uploadAppendix")
    public ResponseBo uploadAppendix(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", mediaAndAttachedService.uploadAppendix(files, LoggerModulesConsts.VOTINGCENTER));
        return responseBo;
    }
}
