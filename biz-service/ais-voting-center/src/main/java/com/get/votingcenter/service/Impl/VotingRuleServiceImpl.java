package com.get.votingcenter.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.votingcenter.dao.voting.VotingRuleMapper;
import com.get.votingcenter.vo.VotingRuleVo;
import com.get.votingcenter.entity.VotingRule;
import com.get.votingcenter.service.IVotingRuleService;
import com.get.votingcenter.dto.VotingRuleListDto;
import com.get.votingcenter.dto.VotingRuleUpdateDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

;

/**
 * @author: Hardy
 * @create: 2021/9/23 14:43
 * @verison: 1.0
 * @description:
 */
@Service
public class VotingRuleServiceImpl implements IVotingRuleService {

    @Resource
    private VotingRuleMapper votingRuleMapper;
    @Resource
    private UtilService utilService;

    /**
     * 新增
     *
     * @param votingRuleUpdateDto
     * @return
     */
    @Override
    public Long addVotingRule(VotingRuleUpdateDto votingRuleUpdateDto) {
        if (GeneralTool.isEmpty(votingRuleUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (votingRuleUpdateDto.getVoteLimit() != 1) {
            if (GeneralTool.isEmpty(votingRuleUpdateDto.getIsRepeatVoting())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("is_repeat_voting_can_not_null"));
            }
        }
        VotingRule votingRule = BeanCopyUtils.objClone(votingRuleUpdateDto, VotingRule::new);
        utilService.updateUserInfoToEntity(votingRule);
        //获取最大vieworder
        Integer maxViewOrder = votingRuleMapper.getMaxViewOrder();
        maxViewOrder = GeneralTool.isEmpty(maxViewOrder) ? 0 : maxViewOrder;
        votingRule.setViewOrder(maxViewOrder);
        int i = votingRuleMapper.insertSelective(votingRule);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return votingRule.getId();
    }

    /**
     * 修改
     *
     * @param votingRuleUpdateDto
     * @return VotingRuleVo
     */
    @Override
    public VotingRuleVo updateVotingRule(VotingRuleUpdateDto votingRuleUpdateDto) {
        if (GeneralTool.isEmpty(votingRuleUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(votingRuleUpdateDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        VotingRule votingRule = BeanCopyUtils.objClone(votingRuleUpdateDto, VotingRule::new);
        utilService.updateUserInfoToEntity(votingRule);
        votingRuleMapper.updateById(votingRule);

        return findVotingRuleById(votingRuleUpdateDto.getId());
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @Override
    public VotingRuleVo findVotingRuleById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        VotingRule votingRule = votingRuleMapper.selectById(id);
        if (GeneralTool.isEmpty(votingRule)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        VotingRuleVo votingRuleVo = BeanCopyUtils.objClone(votingRule, VotingRuleVo::new);
        return votingRuleVo;
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void deleteVotingRule(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        votingRuleMapper.deleteById(id);
    }

    /**
     * 列表
     *
     * @param votingRuleListDto
     * @return
     */
    @Override
    public List<VotingRuleVo> getVotingRules(VotingRuleListDto votingRuleListDto) {
//        Example example = new Example(VotingRule.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(votingRuleListDto)) {
//            criteria.andEqualTo("fkVotingId",votingRuleListDto.getFkVotingId());
//        }
//        example.orderBy("viewOrder").desc();
////        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<VotingRule> votingRules = votingRuleMapper.selectByExample(example);
////        page.restPage(votingRules);

        LambdaQueryWrapper<VotingRule> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(votingRuleListDto)) {
            lambdaQueryWrapper.eq(VotingRule::getFkVotingId, votingRuleListDto.getFkVotingId());
        }
        lambdaQueryWrapper.orderByDesc(VotingRule::getViewOrder);
        List<VotingRule> votingRules = votingRuleMapper.selectList(lambdaQueryWrapper);
        return votingRules.stream().map(votingRule -> BeanCopyUtils.objClone(votingRule, VotingRuleVo::new)).collect(Collectors.toList());
    }

    /**
     * 上下移
     *
     * @param votingRuleListDtos
     * @
     */
    @Override
    public void movingOrder(List<VotingRuleListDto> votingRuleListDtos) {
        if (GeneralTool.isEmpty(votingRuleListDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        VotingRule ro = BeanCopyUtils.objClone(votingRuleListDtos.get(0), VotingRule::new);
        Integer oneorder = ro.getViewOrder();
        VotingRule rt = BeanCopyUtils.objClone(votingRuleListDtos.get(1), VotingRule::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        votingRuleMapper.updateById(ro);
        votingRuleMapper.updateById(rt);
    }
}
