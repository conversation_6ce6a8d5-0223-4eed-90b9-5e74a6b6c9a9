package com.get.votingcenter.service.Impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.aisplatformcenterap.feign.IPlatformCenterClient;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.platformconfigcenter.feign.IPlatformConfigCenterClient;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.votingcenter.dao.appvoting.UserVotingMapper;
import com.get.votingcenter.dao.voting.VotingItemMapper;
import com.get.votingcenter.dao.voting.VotingItemOptionMapper;
import com.get.votingcenter.vo.UserAwardVo;
import com.get.votingcenter.vo.UserVotingVo;
import com.get.votingcenter.vo.VotingItemOptionVo;
import com.get.votingcenter.vo.VotingResultVo;
import com.get.votingcenter.entity.VotingItem;
import com.get.votingcenter.entity.VotingItemOption;
import com.get.votingcenter.service.IUserVotingService;
import com.get.votingcenter.service.IVotingItemOptionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * @author: Hardy
 * @create: 2021/9/27 16:55
 * @verison: 1.0
 * @description:
 */
@Service
public class UserVotingServiceImpl implements IUserVotingService {

    @Resource
    private UserVotingMapper userVotingMapper;
    @Resource
    private VotingItemOptionMapper votingItemOptionMapper;
    @Resource
    private IVotingItemOptionService votingItemOptionService;
    @Resource
    private VotingItemMapper votingItemMapper;
    @Resource
    private ISaleCenterClient saleCenterClient;
//    @Resource
//    private IPlatformConfigCenterClient platformConfigCenterClient;
    @Resource
    private IPlatformCenterClient platformCenterClient;

    /**
     * 获取投票结果
     *
     * @param id
     * @return VotingResultVo
     */
    @Override
    public VotingResultVo getVotingResult(Long id) {
//        Example example = new Example(VotingItemOption.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkVotingItemId", id);
//        example.orderBy("viewOrder").desc();
//        List<VotingItemOption> votingItemOptions = votingItemOptionMapper.selectByExample(example);

        List<VotingItemOption> votingItemOptions = votingItemOptionMapper.selectList(Wrappers.<VotingItemOption>lambdaQuery().eq(VotingItemOption::getFkVotingItemId, id).orderByDesc(VotingItemOption::getViewOrder));
        List<Long> optionIds = votingItemOptions.stream().map(VotingItemOption::getId).collect(Collectors.toList());
        VotingResultVo votingResultVo = new VotingResultVo();
        List<VotingItemOptionVo> votingItemOptionVos = new ArrayList<>();
        Long sumVotingCount = 0L;
        for (Long optionId : optionIds) {
            //获取选项详情
            VotingItemOptionVo votingItemOptionVo = votingItemOptionService.findVotingItemOptionById(optionId);
            Long votingCount = userVotingMapper.getVotingCountByOptionId(optionId);
            if (GeneralTool.isNotEmpty(votingCount)) {
                sumVotingCount += votingCount;
                votingItemOptionVo.setVotingCount(votingCount);
            } else {
                votingItemOptionVo.setVotingCount(0L);
            }
            votingItemOptionVos.add(votingItemOptionVo);
        }
        if (sumVotingCount != 0) {
            for (VotingItemOptionVo votingItemOptionVo : votingItemOptionVos) {
                Double votingCountRate = votingItemOptionVo.getVotingCount() * 100.0 / sumVotingCount;
                String f = Double.toString(votingCountRate);
                BigDecimal b = new BigDecimal(f);
                // 这里的 2 就是你要保留的小数位数。
                double votingCountRateResult = b.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
                votingItemOptionVo.setVotingRate(votingCountRateResult);
            }
        }
        votingResultVo.setSumVotingCount(sumVotingCount);
        votingResultVo.setVotingItemOptionDtos(votingItemOptionVos);
        return votingResultVo;
    }


    /**
     * @Description: 根据主题id获取投票用户明细
     * @Author: Jerry
     * @Date:9:54 2021/10/21
     */
    @Override
    public List<UserAwardVo> getVotingResultByVotingId(Long fkVotingId) {
        List<UserAwardVo> userAwardVos = new ArrayList<>();
//        Example example = new Example(VotingItem.class);
//        example.createCriteria().andEqualTo("fkVotingId", fkVotingId);
//        example.orderBy("viewOrder").desc();
//        List<VotingItem> votingItems = votingItemMapper.selectByExample(example);
//

        List<VotingItem> votingItems = votingItemMapper.selectList(Wrappers.<VotingItem>lambdaQuery().eq(VotingItem::getFkVotingId, fkVotingId).orderByDesc(VotingItem::getViewOrder));

        if (GeneralTool.isEmpty(votingItems)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("voting_item_null"));
        }
        Set<Long> votingItemIds = votingItems.stream().map(VotingItem::getId).collect(Collectors.toSet());
        //根据投票项ids获取所有的投票选项id
//        example.clear();
//        example = new Example(VotingItemOption.class);
//        example.createCriteria().andIn("fkVotingItemId", votingItemIds);
//        example.orderBy("viewOrder").desc();
//        List<VotingItemOption> votingItemOptions = votingItemOptionMapper.selectByExample(example);
        List<VotingItemOption> votingItemOptions = votingItemOptionMapper.selectList(Wrappers.<VotingItemOption>lambdaQuery().in(VotingItemOption::getFkVotingItemId, votingItemIds).orderByDesc(VotingItemOption::getViewOrder));
        if (GeneralTool.isEmpty(votingItems)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("voting_item_option_null"));
        }
        Set<Long> votingItemOptionIds = votingItemOptions.stream().map(VotingItemOption::getId).collect(Collectors.toSet());
        List<UserVotingVo> votingResult = userVotingMapper.getVotingResult(votingItemOptionIds);
        if (GeneralTool.isEmpty(votingResult)) {
            return userAwardVos;
        }
        //获取用户ids
        Set<Long> fkUserIds = votingResult.stream().map(UserVotingVo::getFkUserId).collect(Collectors.toSet());
        Map<Long, String> userNamesByUserIds = new HashMap<>();
        Map<Long, String> mobileByUserIds = new HashMap<>();
        Map<String, String> namesByMobiles = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkUserIds)) {
            Result<Map<Long, String>> result1 = platformCenterClient.getUserNickNamesByUserIds(fkUserIds);
            if (result1.isSuccess() && GeneralTool.isNotEmpty(result1.getData())) {
                userNamesByUserIds = result1.getData();
            }
            //获取用户的所在城市
            Result<Map<Long, String>> result2 = platformCenterClient.getMobileByUserIds(fkUserIds);
            if (result2.isSuccess() && GeneralTool.isNotEmpty(result2.getData())) {
                mobileByUserIds = result2.getData();
            }
        }
        //获取用户所有的手机号
        if (GeneralTool.isNotEmpty(mobileByUserIds)) {
            Set<String> mobiles = new HashSet<>();
            for (String mobile : mobileByUserIds.values()) {
                mobiles.add(mobile);
            }
            //根据手机号获取对应的峰会人员姓名
            Result<Map<String, String>> result = saleCenterClient.getNamesByMobiles(mobiles);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                namesByMobiles = result.getData();
            }
        }
        UserAwardVo userAwardVo = null;
        for (UserVotingVo userVotingVo : votingResult) {
            userAwardVo = new UserAwardVo();
            userAwardVo.setFkUserId(userVotingVo.getFkUserId());
            //用户名称
            userAwardVo.setFkUserName(userNamesByUserIds.get(userVotingVo.getFkUserId()));
            String mobile = mobileByUserIds.get(userVotingVo.getFkUserId());
            //手机号
            userAwardVo.setMobile(mobile);
            //峰会用户名称
            if (GeneralTool.isNotEmpty(mobile)) {
                userAwardVo.setConventionPersonName(namesByMobiles.get(mobile));
            }
            userAwardVos.add(userAwardVo);
        }
        return userAwardVos;
    }
}
