//package com.get.votingcenter.utils;
//
//import org.mybatis.generator.api.IntrospectedColumn;
//import org.mybatis.generator.api.IntrospectedTable;
//import org.mybatis.generator.api.PluginAdapter;
//import org.mybatis.generator.api.dom.java.Method;
//import org.mybatis.generator.api.dom.java.TopLevelClass;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @DATE: 2020/6/24
// * @TIME: 10:10
// **/
//public class IgnoreSetterAndGetterPlugin extends PluginAdapter {
//
//
//    @Override
//    public boolean validate(List<String> list) {
//        return true;
//    }
//
//    @Override
//    public boolean modelSetterMethodGenerated(Method method, TopLevelClass topLevelClass, IntrospectedColumn introspectedColumn, IntrospectedTable introspectedTable, ModelClassType modelClassType) {
//        //不生成getter
//        return false;
//    }
//
//    @Override
//    public boolean modelGetterMethodGenerated(Method method, TopLevelClass topLevelClass, IntrospectedColumn introspectedColumn, IntrospectedTable introspectedTable, ModelClassType modelClassType) {
//        //不生成setter
//        return false;
//    }
//}