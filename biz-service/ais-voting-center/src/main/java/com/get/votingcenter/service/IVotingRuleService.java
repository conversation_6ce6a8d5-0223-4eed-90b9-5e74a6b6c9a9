package com.get.votingcenter.service;

import com.get.votingcenter.vo.VotingRuleVo;
import com.get.votingcenter.dto.VotingRuleListDto;
import com.get.votingcenter.dto.VotingRuleUpdateDto;

import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2021/9/23 14:43
 * @verison: 1.0
 * @description:
 */
public interface IVotingRuleService {

    /**
     * @return Long
     * @Description :新增
     * @Param [votingRuleUpdateDto]
     * <AUTHOR>
     */
    Long addVotingRule(VotingRuleUpdateDto votingRuleUpdateDto);

    /**
     * @return VotingRuleVo
     * @Description :修改
     * @Param [votingRuleUpdateDto]
     * <AUTHOR>
     */
    VotingRuleVo updateVotingRule(VotingRuleUpdateDto votingRuleUpdateDto);

    /**
     * @return VotingRuleVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    VotingRuleVo findVotingRuleById(Long id);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteVotingRule(Long id);

    /**
     * 列表
     *
     * @param votingRuleListDto
     * @param page
     * @return List<VotingRuleVo>
     */
    List<VotingRuleVo> getVotingRules(VotingRuleListDto votingRuleListDto);

    /**
     * 上下移
     *
     * @param votingRuleListDtos
     */
    void movingOrder(List<VotingRuleListDto> votingRuleListDtos);

}
