package com.get.votingcenter.service;

import com.get.filecenter.dto.FileDto;
import com.get.votingcenter.vo.VotingMediaAndAttachedVo;
import com.get.votingcenter.dto.MediaAndAttachedDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2021/9/26 17:22
 * @verison: 1.0
 * @description:
 */
public interface IMediaAndAttachedService {

    /**
     * @return List<FileDto>
     * @Description :上传文件
     * @Param [files, type]
     * <AUTHOR>
     */
    List<FileDto> upload(MultipartFile[] files, String type);

    /**
     * @return List<FileDto>
     * @Description :上传附件
     * @Param [files, type]
     * <AUTHOR>
     */
    List<FileDto> uploadAppendix(MultipartFile[] files, String type);

    /**
     * 通过附件GUID获取DTO
     *
     * @param mediaAndAttachedDto
     * @return
     * @
     */
    List<VotingMediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto mediaAndAttachedDto);

    /**
     * @Description:保存文件
     * @Param
     * @Date 12:28 2021/5/12
     * <AUTHOR>
     */
    VotingMediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo);
}
