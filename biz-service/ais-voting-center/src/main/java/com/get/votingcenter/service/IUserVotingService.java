package com.get.votingcenter.service;


import com.get.votingcenter.vo.UserAwardVo;
import com.get.votingcenter.vo.VotingResultVo;

import java.util.List;

/**
 * @author: <PERSON>
 * @create: 2021/9/27 16:55
 * @verison: 1.0
 * @description:
 */
public interface IUserVotingService {

    /**
     * @return VotingResultVo
     * @Description :投票结果
     * @Param [id]
     * <AUTHOR>
     */
    VotingResultVo getVotingResult(Long id);


    /**
     * @Description: 根据主题id获取投票用户明细
     * @Author: Jerry
     * @Date:9:54 2021/10/21
     */
    List<UserAwardVo> getVotingResultByVotingId(Long fkVotingId);
}
