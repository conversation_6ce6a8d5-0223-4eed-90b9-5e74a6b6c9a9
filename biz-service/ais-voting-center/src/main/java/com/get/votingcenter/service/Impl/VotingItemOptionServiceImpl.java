package com.get.votingcenter.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.votingcenter.dao.voting.MediaAndAttachedMapper;
import com.get.votingcenter.dao.voting.VotingItemOptionMapper;
import com.get.votingcenter.vo.VotingItemOptionVo;
import com.get.votingcenter.vo.VotingMediaAndAttachedVo;
import com.get.votingcenter.entity.VotingMediaAndAttached;
import com.get.votingcenter.entity.VotingItemOption;
import com.get.votingcenter.service.IMediaAndAttachedService;
import com.get.votingcenter.service.IVotingItemOptionService;
import com.get.votingcenter.dto.MediaAndAttachedDto;
import com.get.votingcenter.dto.VotingItemOptionListDto;
import com.get.votingcenter.dto.VotingItemOptionUpdateDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

;

/**
 * @author: Hardy
 * @create: 2021/9/26 16:45
 * @verison: 1.0
 * @description:
 */
@Service
public class VotingItemOptionServiceImpl implements IVotingItemOptionService {

    @Resource
    private VotingItemOptionMapper votingItemOptionMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;

    /**
     * 新增
     *
     * @param votingItemOptionUpdateDto
     * @return
     */
    @Override
    public Long addVotingItemOption(VotingItemOptionUpdateDto votingItemOptionUpdateDto) {
        if (GeneralTool.isEmpty(votingItemOptionUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        VotingItemOption votingItemOption = BeanCopyUtils.objClone(votingItemOptionUpdateDto, VotingItemOption::new);
        utilService.updateUserInfoToEntity(votingItemOption);
        //获取最大vieworder
        Integer maxViewOrder = votingItemOptionMapper.getMaxViewOrder();
        maxViewOrder = GeneralTool.isEmpty(maxViewOrder) ? 0 : maxViewOrder;
        votingItemOption.setViewOrder(maxViewOrder);
        int i = votingItemOptionMapper.insertSelective(votingItemOption);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        if (GeneralTool.isNotEmpty(votingItemOptionUpdateDto.getMediaAttachedVos())) {
            //保存图片
            List<MediaAndAttachedDto> mediaAndAttachedDtoList = votingItemOptionUpdateDto.getMediaAttachedVos();
            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtoList) {
                mediaAndAttachedDto.setTypeKey(FileTypeEnum.VOTING_ITEM_OPTION_PIC.key);
                mediaAndAttachedDto.setFkTableId(votingItemOption.getId());
                mediaAndAttachedDto.setFkTableName(TableEnum.VOTING_ITEM_OPTION.key);
                mediaAndAttachedService.addMediaAndAttached(mediaAndAttachedDto);
            }
        }
        return votingItemOption.getId();
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void deleteVotingItemOption(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        votingItemOptionMapper.deleteById(id);
    }

    /**
     * 修改
     *
     * @param votingItemOptionUpdateDto
     * @return
     */
    @Override
    public VotingItemOptionVo updateVotingItemOption(VotingItemOptionUpdateDto votingItemOptionUpdateDto) {
        if (GeneralTool.isEmpty(votingItemOptionUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(votingItemOptionUpdateDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        VotingItemOption votingItemOption = BeanCopyUtils.objClone(votingItemOptionUpdateDto, VotingItemOption::new);
        utilService.updateUserInfoToEntity(votingItemOption);
        votingItemOptionMapper.updateById(votingItemOption);
        if (GeneralTool.isNotEmpty(votingItemOptionUpdateDto.getMediaAttachedVos())) {
            List<MediaAndAttachedDto> mediaAttachedVos = votingItemOptionUpdateDto.getMediaAttachedVos();
//            Example example = new Example(MediaAndAttached.class);
//            example.createCriteria().andEqualTo("fkTableName", TableEnum.VOTING_ITEM_OPTION.key)
//                    .andEqualTo("fkTableId", votingItemOptionUpdateDto.getId()).andEqualTo("typeKey", FileTypeEnum.VOTING_ITEM_OPTION_PIC.key);
//            mediaAndAttachedMapper.deleteByExample(example);
            mediaAndAttachedMapper.delete(Wrappers.<VotingMediaAndAttached>lambdaQuery()
                    .eq(VotingMediaAndAttached::getFkTableName, TableEnum.VOTING_ITEM_OPTION.key)
                    .eq(VotingMediaAndAttached::getFkTableId, votingItemOptionUpdateDto.getId())
                    .eq(VotingMediaAndAttached::getTypeKey, FileTypeEnum.VOTING_ITEM_OPTION_PIC.key));
            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
                mediaAndAttachedDto.setTypeKey(FileTypeEnum.VOTING_ITEM_OPTION_PIC.key);
                mediaAndAttachedDto.setFkTableId(votingItemOptionUpdateDto.getId());
                mediaAndAttachedDto.setFkTableName(TableEnum.VOTING_ITEM_OPTION.key);
                mediaAndAttachedService.addMediaAndAttached(mediaAndAttachedDto);
            }
        }
        return findVotingItemOptionById(votingItemOptionUpdateDto.getId());
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public VotingItemOptionVo findVotingItemOptionById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        VotingItemOption votingItemOption = votingItemOptionMapper.selectById(id);
        if (GeneralTool.isEmpty(votingItemOption)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        VotingItemOptionVo votingItemOptionVo = BeanCopyUtils.objClone(votingItemOption, VotingItemOptionVo::new);
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.VOTING_ITEM_OPTION.key);
        attachedVo.setFkTableId(id);
        List<VotingMediaAndAttachedVo> mediaAndAttachedDtos = mediaAndAttachedService.getMediaAndAttachedDto(attachedVo);
        votingItemOptionVo.setMediaAndAttachedDtoList(mediaAndAttachedDtos);
        return votingItemOptionVo;
    }

    /**
     * 列表
     *
     * @param votingItemOptionListDto
     * @return
     */
    @Override
    public List<VotingItemOptionVo> getVotingItemOptions(VotingItemOptionListDto votingItemOptionListDto) {
        if (GeneralTool.isEmpty(votingItemOptionListDto.getFkVotingItemId())) {
            throw new IllegalArgumentException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        Example example = new Example(VotingItemOption.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkVotingItemId",votingItemOptionListDto.getFkVotingItemId());
//        if (GeneralTool.isNotEmpty(votingItemOptionListDto.getName())) {
//            criteria.andEqualTo("name", votingItemOptionListDto.getName());
//        }
//        example.orderBy("viewOrder").desc();
//        List<VotingItemOption> votingItemOptions = votingItemOptionMapper.selectByExample(example);
        LambdaQueryWrapper<VotingItemOption> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(VotingItemOption::getFkVotingItemId, votingItemOptionListDto.getFkVotingItemId());
        if (GeneralTool.isNotEmpty(votingItemOptionListDto.getName())) {
            lambdaQueryWrapper.eq(VotingItemOption::getName, votingItemOptionListDto.getName());
        }
        lambdaQueryWrapper.orderByDesc(VotingItemOption::getViewOrder);
        List<VotingItemOption> votingItemOptions = votingItemOptionMapper.selectList(lambdaQueryWrapper);
        List<VotingItemOptionVo> votingItemOptionVos = votingItemOptions.stream().map(votingItemOption -> BeanCopyUtils.objClone(votingItemOption, VotingItemOptionVo::new)).collect(Collectors.toList());
        for (VotingItemOptionVo votingItemOptionVo : votingItemOptionVos) {
            MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
            attachedVo.setFkTableName(TableEnum.VOTING_ITEM_OPTION.key);
            attachedVo.setFkTableId(votingItemOptionVo.getId());
            List<VotingMediaAndAttachedVo> mediaAndAttachedDtos = mediaAndAttachedService.getMediaAndAttachedDto(attachedVo);
            votingItemOptionVo.setMediaAndAttachedDtoList(mediaAndAttachedDtos);
        }

        return votingItemOptionVos;
    }

    /**
     * 上移下移
     *
     * @param votingItemOptionListDtos
     */
    @Override
    public void movingOrder(List<VotingItemOptionListDto> votingItemOptionListDtos) {
        if (GeneralTool.isEmpty(votingItemOptionListDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        VotingItemOption ro = BeanCopyUtils.objClone(votingItemOptionListDtos.get(0), VotingItemOption::new);
        Integer oneorder = ro.getViewOrder();
        VotingItemOption rt = BeanCopyUtils.objClone(votingItemOptionListDtos.get(1), VotingItemOption::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        votingItemOptionMapper.updateById(ro);
        votingItemOptionMapper.updateById(rt);
    }

}
