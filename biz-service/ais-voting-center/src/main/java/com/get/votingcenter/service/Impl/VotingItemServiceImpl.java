package com.get.votingcenter.service.Impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.votingcenter.dao.voting.MediaAndAttachedMapper;
import com.get.votingcenter.dao.voting.VotingItemMapper;
import com.get.votingcenter.dao.voting.VotingItemOptionMapper;
import com.get.votingcenter.dao.voting.VotingMapper;
import com.get.votingcenter.vo.VotingItemVo;
import com.get.votingcenter.vo.VotingMediaAndAttachedVo;
import com.get.votingcenter.entity.VotingMediaAndAttached;
import com.get.votingcenter.entity.Voting;
import com.get.votingcenter.entity.VotingItem;
import com.get.votingcenter.entity.VotingItemOption;
import com.get.votingcenter.service.IMediaAndAttachedService;
import com.get.votingcenter.service.IVotingItemService;
import com.get.votingcenter.dto.MediaAndAttachedDto;
import com.get.votingcenter.dto.VotingItemListDto;
import com.get.votingcenter.dto.VotingItemUpdateDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

;

/**
 * @author: Hardy
 * @create: 2021/9/24 11:18
 * @verison: 1.0
 * @description:
 */
@Service
public class VotingItemServiceImpl implements IVotingItemService {

    @Resource
    private VotingItemMapper votingItemMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private VotingItemOptionMapper votingItemOptionMapper;
    @Resource
    private VotingMapper votingMapper;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;
    /**
     * 新增
     *
     * @param votingItemUpdateDto
     * @return
     */
    @Override
    public Long addVotingItem(VotingItemUpdateDto votingItemUpdateDto) {
        if (GeneralTool.isEmpty(votingItemUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isNotEmpty(votingItemUpdateDto.getStartTime()) && GeneralTool.isNotEmpty(votingItemUpdateDto.getEndTime())) {
            //校验日期
            if (votingItemUpdateDto.getStartTime().after(votingItemUpdateDto.getEndTime())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("start_time_end_time_error"));
            }
        }
        VotingItem votingItem = BeanCopyUtils.objClone(votingItemUpdateDto, VotingItem::new);
        utilService.updateUserInfoToEntity(votingItem);
        //获取最大vieworder
        Integer maxViewOrder = votingItemMapper.getMaxViewOrder();
        maxViewOrder = GeneralTool.isEmpty(maxViewOrder) ? 0 : maxViewOrder;
        votingItem.setViewOrder(maxViewOrder);
        int i = votingItemMapper.insertSelective(votingItem);

        //保存图片
        List<MediaAndAttachedDto> mediaAndAttachedDtoList = votingItemUpdateDto.getMediaAttachedVos();
        if (GeneralTool.isNotEmpty(mediaAndAttachedDtoList)) {
            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtoList) {
                mediaAndAttachedDto.setTypeKey(FileTypeEnum.VOTING_ITEM_PIC.key);
                mediaAndAttachedDto.setFkTableId(votingItem.getId());
                mediaAndAttachedDto.setFkTableName(TableEnum.VOTING_ITEM.key);
                mediaAndAttachedService.addMediaAndAttached(mediaAndAttachedDto);
            }
        }

        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return votingItem.getId();
    }

    /**
     * 修改
     *
     * @param votingItemUpdateDto
     * @return
     * @
     */
    @Override
    public VotingItemVo updateVotingItem(VotingItemUpdateDto votingItemUpdateDto) {
        if (GeneralTool.isEmpty(votingItemUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(votingItemUpdateDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isNotEmpty(votingItemUpdateDto.getStartTime()) && GeneralTool.isNotEmpty(votingItemUpdateDto.getEndTime())) {
            //校验日期
            if (votingItemUpdateDto.getStartTime().after(votingItemUpdateDto.getEndTime())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("start_time_end_time_error"));
            }
        }
        VotingItem item = votingItemMapper.selectById(votingItemUpdateDto.getId());
        VotingItem votingItem = BeanCopyUtils.objClone(votingItemUpdateDto, VotingItem::new);
        votingItem.setViewOrder(item.getViewOrder());
        votingItem.setGmtCreate(item.getGmtCreate());
        votingItem.setGmtCreateUser(item.getGmtCreateUser());
        utilService.updateUserInfoToEntity(votingItem);
        votingItemMapper.updateById(votingItem);

        //删除图片
        mediaAndAttachedMapper.delete(Wrappers.<VotingMediaAndAttached>lambdaQuery()
                .eq(VotingMediaAndAttached::getFkTableName, TableEnum.VOTING_ITEM.key)
                .eq(VotingMediaAndAttached::getFkTableId, votingItem.getId())
                .eq(VotingMediaAndAttached::getTypeKey, FileTypeEnum.VOTING_ITEM_PIC.key));
        //保存图片
        List<MediaAndAttachedDto> mediaAndAttachedDtoList = votingItemUpdateDto.getMediaAttachedVos();
        if (GeneralTool.isNotEmpty(mediaAndAttachedDtoList)) {
            for (MediaAndAttachedDto mediaAndAttachedDto : mediaAndAttachedDtoList) {
                mediaAndAttachedDto.setTypeKey(FileTypeEnum.VOTING_ITEM_PIC.key);
                mediaAndAttachedDto.setFkTableId(votingItem.getId());
                mediaAndAttachedDto.setFkTableName(TableEnum.VOTING_ITEM.key);
                mediaAndAttachedService.addMediaAndAttached(mediaAndAttachedDto);
            }
        }
        return findVotingItemById(votingItemUpdateDto.getId());
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public VotingItemVo findVotingItemById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        VotingItem votingItem = votingItemMapper.selectById(id);
        if (GeneralTool.isEmpty(votingItem)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        VotingItemVo votingItemVo = BeanCopyUtils.objClone(votingItem, VotingItemVo::new);
        if (GeneralTool.isNotEmpty(votingItemVo.getStatus())) {
            setStatusName(votingItemVo);
        }
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.VOTING_ITEM.key);
        attachedVo.setFkTableId(id);
        List<VotingMediaAndAttachedVo> mediaAndAttachedDtos = mediaAndAttachedService.getMediaAndAttachedDto(attachedVo);
        votingItemVo.setMediaAttachedVos(mediaAndAttachedDtos);
        return votingItemVo;
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void deleteVotingItem(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        validateDelete(id);
        votingItemMapper.deleteById(id);
    }

    /**
     * 验证删除
     *
     * @param id
     */
    private void validateDelete(Long id) {
//        Example example = new Example(VotingItemOption.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkVotingItemId", id);
//        List<VotingItemOption> votingItemOptions = votingItemOptionMapper.selectByExample(example);
        List<VotingItemOption> votingItemOptions = votingItemOptionMapper.selectList(Wrappers.<VotingItemOption>lambdaQuery().eq(VotingItemOption::getFkVotingItemId, id));
        if (GeneralTool.isNotEmpty(votingItemOptions)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("voting_item_option_associated"));
        }

    }

    /**
     * 列表
     *
     * @param votingItemListDto
     * @param page
     * @return
     */
    @Override
    public List<VotingItemVo> getVotingItems(VotingItemListDto votingItemListDto, SearchBean<VotingItemListDto> page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        IPage<VotingItemVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<VotingItemVo> votingItemVos = votingItemMapper.getVotingItems(iPage, votingItemListDto);
        page.setAll((int) iPage.getTotal());

        Set<Long> companyIds = votingItemVos.stream().map(VotingItemVo::getFkCompanyId).collect(Collectors.toSet());
        Set<Long> votingIds = votingItemVos.stream().map(VotingItemVo::getFkVotingId).collect(Collectors.toSet());
        votingIds.removeIf(Objects::isNull);
        Map<Long, String> companyNamesMap = new HashMap<>();
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
        if (result.isSuccess() && result.getData() != null) {
            companyNamesMap = result.getData();
        }
//        Example example = new Example(Voting.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(votingIds)) {
//            criteria.andIn("id", votingIds);
//        }
        Map<Long, String> themeNameMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(votingIds)) {
            List<Voting> votings = votingMapper.selectList(Wrappers.<Voting>lambdaQuery().in(Voting::getId, votingIds));
            themeNameMap = votings.stream().collect(Collectors.toMap(Voting::getId, Voting::getThemeName));
        }
        for (VotingItemVo votingItemVo : votingItemVos) {
            votingItemVo.setCompanyName(companyNamesMap.get(votingItemVo.getFkCompanyId()));
            if (GeneralTool.isNotEmpty(themeNameMap)) {
                votingItemVo.setThemeName(themeNameMap.get(votingItemVo.getFkVotingId()));
            }
            setStatusName(votingItemVo);
        }
        return votingItemVos;
    }

    /**
     * 上下移动
     *
     * @param votingItemListDtos
     */
    @Override
    public void movingOrder(List<VotingItemListDto> votingItemListDtos) {
        if (GeneralTool.isEmpty(votingItemListDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (!votingItemListDtos.get(0).getFkVotingId().equals(votingItemListDtos.get(1).getFkVotingId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("voting_theme_not_equal"));
        }
        VotingItem ro = BeanCopyUtils.objClone(votingItemListDtos.get(0), VotingItem::new);
        Integer oneorder = ro.getViewOrder();
        VotingItem rt = BeanCopyUtils.objClone(votingItemListDtos.get(1), VotingItem::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        votingItemMapper.updateById(ro);
        votingItemMapper.updateById(rt);
    }

    /**
     * 设置状态名称
     *
     * @param votingItemVo
     */
    private void setStatusName(VotingItemVo votingItemVo) {
        switch (votingItemVo.getStatus()) {
            case 0:
                votingItemVo.setStatusName(ProjectExtraEnum.VOTING_ITEM_NOT_STARTED.value);
                break;
            case 1:
                votingItemVo.setStatusName(ProjectExtraEnum.VOTING_ITEM_HAVE_IN_HAND.value);
                break;
            case 2:
                votingItemVo.setStatusName(ProjectExtraEnum.VOTING_ITEM_HAS_END.value);
                break;
            default:
                break;
        }
    }

}
