package com.get.votingcenter.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.votingcenter.dao.voting.VotingMapper;
import com.get.votingcenter.dao.voting.VotingRuleMapper;
import com.get.votingcenter.vo.VotingVo;
import com.get.votingcenter.entity.Voting;
import com.get.votingcenter.entity.VotingRule;
import com.get.votingcenter.service.IVotingService;
import com.get.votingcenter.dto.VotingListDto;
import com.get.votingcenter.dto.VotingUpdateDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

;

/**
 * @author: Hardy
 * @create: 2021/9/23 10:58
 * @verison: 1.0
 * @description:
 */
@Service
public class VotingServiceImpl implements IVotingService {

    @Resource
    private VotingMapper votingMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private VotingRuleMapper votingRuleMapper;

    /**
     * 新增投票主题
     *
     * @param votingUpdateDto
     * @return Long
     */
    @Override
    public Long addVoting(VotingUpdateDto votingUpdateDto) {
        if (GeneralTool.isEmpty(votingUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        validateUpdate(votingUpdateDto);
        Voting voting = BeanCopyUtils.objClone(votingUpdateDto, Voting::new);
        utilService.updateUserInfoToEntity(voting);
        int i = votingMapper.insertSelective(voting);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return voting.getId();
    }

    /**
     * 更新
     *
     * @param votingUpdateDto
     * @return
     */
    @Override
    public VotingVo updateVoting(VotingUpdateDto votingUpdateDto) {
        if (GeneralTool.isEmpty(votingUpdateDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(votingUpdateDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        validateUpdate(votingUpdateDto);
        Voting voting = BeanCopyUtils.objClone(votingUpdateDto, Voting::new);
        utilService.updateUserInfoToEntity(voting);
        votingMapper.updateById(voting);

        return findVotingById(votingUpdateDto.getId());
    }

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @Override
    public VotingVo findVotingById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Voting voting = votingMapper.selectById(id);
        if (GeneralTool.isEmpty(voting)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        VotingVo votingVo = BeanCopyUtils.objClone(voting, VotingVo::new);
        String companyName = "";
        Result<String> result = permissionCenterClient.getCompanyNameById(voting.getFkCompanyId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            companyName = result.getData();
            votingVo.setCompanyName(companyName);
        }
        return votingVo;
    }

    /**
     * 列表
     *
     * @param votingListDto
     * @param page
     * @return
     */
    @Override
    public List<VotingVo> getVotings(VotingListDto votingListDto, SearchBean<VotingListDto> page) {
        LambdaQueryWrapper<Voting> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(votingListDto)) {
            //查询条件-主题名称
            if (GeneralTool.isNotEmpty(votingListDto.getThemeName())) {
                lambdaQueryWrapper.like(Voting::getThemeName, votingListDto.getThemeName());
            }
            //查询条件-所属公司
            if (GeneralTool.isNotEmpty(votingListDto.getFkCompanyId())) {
                lambdaQueryWrapper.like(Voting::getFkCompanyId, votingListDto.getFkCompanyId());
            }
        }
        IPage<Voting> pages = votingMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<Voting> votings = pages.getRecords();
        page.setAll((int) pages.getTotal());
        Set<Long> companyIds = votings.stream().map(Voting::getFkCompanyId).collect(Collectors.toSet());
        Map<Long, String> companyNamesMap = new HashMap<>();
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(companyIds);
        if (result.isSuccess() && result.getData() != null) {
            companyNamesMap = result.getData();
        }
        List<VotingVo> votingVos = votings.stream().map(voting -> BeanCopyUtils.objClone(voting, VotingVo::new)).collect(Collectors.toList());
        Map<Long, String> finalCompanyNamesMap = companyNamesMap;
        votingVos.forEach(votingVo -> votingVo.setCompanyName(finalCompanyNamesMap.get(votingVo.getFkCompanyId())));
        return votingVos;
    }

    /**
     * 删除
     *
     * @param id
     */
    @Override
    public void deleteVoting(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        validateDelete(id);
        votingMapper.deleteById(id);
    }

    /**
     * 投票主题下拉框
     *
     * @return
     */
    @Override
    public List<BaseSelectEntity> votingSelect(VotingListDto votingListDto) {
        if (GeneralTool.isEmpty(votingListDto.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        List<BaseSelectEntity> baseSelectEntities = new ArrayList<>();
//        Example example = new Example(Voting.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkCompanyId",votingListDto.getFkCompanyId());
//        example.orderBy("gmtCreate").desc();
//        List<Voting> votings = votingMapper.selectByExample(example);
        List<Voting> votings = votingMapper.selectList(Wrappers.<Voting>lambdaQuery().eq(Voting::getFkCompanyId, votingListDto.getFkCompanyId()).orderByDesc(Voting::getGmtCreate));
        if (GeneralTool.isEmpty(votings)) {
            return baseSelectEntities;
        }
        for (Voting voting : votings) {
            BaseSelectEntity baseSelectEntity = BeanCopyUtils.objClone(voting, BaseSelectEntity::new);
            baseSelectEntity.setName(voting.getThemeName());
            baseSelectEntities.add(baseSelectEntity);
        }
        return baseSelectEntities;
    }

    /**
     * 验证是否有关联数据
     *
     * @param id
     */
    private void validateDelete(Long id) {
//        Example example = new Example(VotingRule.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkVotingId",id);
//        List<VotingRule> votingRules = votingRuleMapper.selectByExample(example);

        List<VotingRule> votingRules = votingRuleMapper.selectList(Wrappers.<VotingRule>lambdaQuery().eq(VotingRule::getFkVotingId, id));

        if (GeneralTool.isNotEmpty(votingRules)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("votingRule_associated"));
        }
    }

    /**
     * 校验主题Key是否唯一
     *
     * @param votingUpdateDto
     */
    private void validateUpdate(VotingUpdateDto votingUpdateDto) {
//        Example example = new Example(Voting.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("themeKey", votingUpdateDto.getThemeKey());
//        //有id则是更新
//        if (GeneralTool.isNotEmpty(votingUpdateDto.getId())) {
//            criteria.andNotEqualTo("id", votingUpdateDto.getId());
//        }
//        List<Voting> votings = votingMapper.selectByExample(example);
        LambdaQueryWrapper<Voting> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(Voting::getThemeKey, votingUpdateDto.getThemeKey());
        //有id则是更新
        if (GeneralTool.isNotEmpty(votingUpdateDto.getId())) {
            lambdaQueryWrapper.ne(Voting::getId, votingUpdateDto.getId());
        }
        List<Voting> votings = votingMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(votings)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("keyCode_exists"));
        }
    }
}
