package com.get.votingcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.votingcenter.vo.UserAwardVo;
import com.get.votingcenter.vo.VotingResultVo;
import com.get.votingcenter.service.IUserVotingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: Hardy
 * @create: 2021/9/27 16:47
 * @verison: 1.0
 * @description:用户投票管理控制器
 */
@Api(tags = "用户投票管理")
@RestController
@RequestMapping("voting/userVoting")
public class UserVotingController {

    @Resource
    private IUserVotingService userVotingService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.votingcenter.vo.VotingItemVo>
     * @Description :投票结果
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "投票结果", notes = "")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.LIST, description = "投票中心/用户投票管理/投票结果")
    @PostMapping("getVotingResult")
    public ResponseBo<VotingResultVo> getVotingResult(@RequestParam("id") Long id) {
        VotingResultVo data = userVotingService.getVotingResult(id);
        return new ResponseBo<>(data);
    }


    /**
     * @Description: 根据主题id获取投票用户明细
     * @Author: Jerry
     * @Date:9:52 2021/10/21
     */
    @ApiOperation(value = "根据主题id获取投票用户明细", notes = "")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.LIST, description = "投票中心/用户投票管理/根据主题id获取投票用户明细")
    @PostMapping("getVotingResultByVotingId")
    public ResponseBo<UserAwardVo> getVotingResultByVotingId(@RequestParam("fkVotingId") Long fkVotingId) {
        return new ListResponseBo(userVotingService.getVotingResultByVotingId(fkVotingId));
    }
}
