package com.get.votingcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.votingcenter.vo.VotingVo;
import com.get.votingcenter.service.IVotingService;
import com.get.votingcenter.dto.VotingListDto;
import com.get.votingcenter.dto.VotingUpdateDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

;

/**
 * @author: Hardy
 * @create: 2021/9/23 10:57
 * @verison: 1.0
 * @description:
 */
@Api(tags = "投票主题管理")
@RestController
@RequestMapping("/voting/voting")
@Slf4j
public class VotingController {

    @Resource
    private IVotingService votingService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.votingcenter.vo.VotingVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.LIST, description = "投票中心/投票主题管理/查询投票主题")
    @PostMapping("datas")
    public ResponseBo<VotingVo> datas(@RequestBody SearchBean<VotingListDto> page) {
        List<VotingVo> datas = votingService.getVotings(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [votingUpdateDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.ADD, description = "投票中心/投票主题管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(VotingUpdateDto.Add.class) VotingUpdateDto votingUpdateDto) {
        return SaveResponseBo.ok(votingService.addVoting(votingUpdateDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [votingUpdateDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.EDIT, description = "投票中心/投票主题管理/修改")
    @PostMapping("update")
    public ResponseBo<VotingVo> update(@RequestBody @Validated(VotingUpdateDto.Update.class) VotingUpdateDto votingUpdateDto) {
        return UpdateResponseBo.ok(votingService.updateVoting(votingUpdateDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.DELETE, description = "投票中心/投票主题管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        votingService.deleteVoting(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.votingcenter.vo.VotingVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "投票主题详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.DETAIL, description = "投票中心/投票主题管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<VotingVo> detail(@PathVariable("id") Long id) {
        VotingVo data = votingService.findVotingById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @Description: 投票主题下拉框
     * @Author: Jerry
     * @Date:9:47 2021/9/3
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "投票主题下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.EXAMCENTER, type = LoggerOptTypeConst.LIST, description = "投票中心/投票主题管理/投票主题下拉框")
    @PostMapping("/votingSelect")
    public ResponseBo<BaseSelectEntity> votingSelect(@RequestBody VotingListDto votingListDto) {
        return new ListResponseBo<>(votingService.votingSelect(votingListDto));
    }
}
