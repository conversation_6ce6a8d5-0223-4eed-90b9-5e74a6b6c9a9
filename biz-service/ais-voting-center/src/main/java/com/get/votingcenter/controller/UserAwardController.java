package com.get.votingcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.votingcenter.vo.UserAwardVo;
import com.get.votingcenter.service.IUserAwardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Description: 随机抽奖管理
 * @Author: Jerry
 * @Date:10:18 2021/10/20
 */
@Api(tags = "随机抽奖管理")
@RestController
@RequestMapping("voting/userAward")
public class UserAwardController {

    @Resource
    private IUserAwardService userAwardService;

    /**
     * @Description: 随机抽奖
     * @Author: Jerry
     * @Date:10:18 2021/10/20
     */
    @ApiOperation(value = "随机抽奖", notes = "fkVotingId为投票主题id，generateCount为生成的数量")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.ADD, description = "投票中心/随机抽奖管理/随机抽奖")
    @PostMapping("generateUserAward")
    public ResponseBo generateUserAward(@RequestParam("fkVotingId") Long fkVotingId,
                                        @RequestParam("generateCount") Integer generateCount) {
        userAwardService.generateUserAward(fkVotingId, generateCount);
        return ResponseBo.ok();
    }


    /**
     * @Description: 抽奖列表数据
     * @Author: Jerry
     * @Date:10:56 2021/10/20
     */
    @ApiOperation(value = "抽奖列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.LIST, description = "投票中心/随机抽奖管理/抽奖列表数据")
    @PostMapping("datas")
    public ResponseBo<UserAwardVo> datas(@RequestParam("fkVotingId") Long fkVotingId) {
        return new ListResponseBo<>(userAwardService.datas(fkVotingId));
    }


    /**
     * @Description: 删除中奖人员名单
     * @Author: Jerry
     * @Date:16:51 2021/10/21
     */
    @ApiOperation(value = "删除中奖人员名单", notes = "")
    @OperationLogger(module = LoggerModulesConsts.VOTINGCENTER, type = LoggerOptTypeConst.DELETE, description = "投票中心/随机抽奖管理/删除中奖人员名单")
    @PostMapping("delete")
    public ResponseBo delete(@RequestParam("fkVotingAwardId") Long fkVotingAwardId) {
        userAwardService.delete(fkVotingAwardId);
        return ResponseBo.ok();
    }
}
