#服务器端口
server:
  port: 9088

#数据源配置
spring:
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: institutiondb
      datasource:
        institutiondb:
          url: ${get.datasource.test.institutiondb.url}
          username: ${get.datasource.test.institutiondb.username}
          password: ${get.datasource.test.institutiondb.password}
        institutiondb-doris:
          url: ${get.datasource.test.institutiondb-doris.url}
          username: ${get.datasource.test.institutiondb-doris.username}
          password: ${get.datasource.test.institutiondb-doris.password}

#旧生产平台
institution:
  #测试环境
  oldplatform:
    addresses: http://************:5012
  #生产环境
#  oldplatform:
#    addresses: http://***********:5015