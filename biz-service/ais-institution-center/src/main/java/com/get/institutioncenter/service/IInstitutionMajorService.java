package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.filecenter.dto.FileDto;
import com.get.institutioncenter.vo.InstitutionMajorVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.InstitutionMajor;
import com.get.institutioncenter.dto.InstitutionMajorDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/8/6
 * @TIME: 11:38
 * @Description:专业学科业务类
 **/
public interface IInstitutionMajorService extends BaseService<InstitutionMajor> {
    /**
     * 列表数据
     *
     * @param institutionMajorDto
     * @param page
     * @return
     */
    List<InstitutionMajorVo> datas(InstitutionMajorDto institutionMajorDto, Page page);

    /**
     * 保存
     *
     * @param institutionMajorDto
     * @return
     */
    Long addInstitutionMajor(InstitutionMajorDto institutionMajorDto);


    /**
     * 获取课程等级下拉
     * @param keyword
     * @return
     */
    List<BaseSelectEntity> getInstitutionMajorSelect(String keyword);

    /**
     * 获取课程等级名称
     * @param ids
     * @return
     */
    Map<Long,String> getInstitutionMajorNamesByIds(Set<Long> ids);

    /**
     * 获取课程等级名称
     * @param id
     * @return
     */
    String getInstitutionMajorNameById(Long id);
    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionMajorVo findInstitutionMajorById(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 上传
     *
     * @return
     */
    FileDto upload(MultipartFile[] multipartFiles);


    /**
     * 修改
     *
     * @param institutionMajorDto
     * @return
     */
    InstitutionMajorVo updateInstitutionMajor(InstitutionMajorDto institutionMajorDto);

    /**
     * 保存附件
     *
     * @param mediaAttachedVo
     * @return
     */
    MediaAndAttachedVo addInstitutionMajorMedia(MediaAndAttachedDto mediaAttachedVo);

    /**
     * 获取专业附件类型
     *
     * @return
     */
    List<Map<String, Object>> findMediaAndAttachedType();
}
