package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.vo.InstitutionProviderAreaCountryVo;
import com.get.institutioncenter.entity.InstitutionProviderAreaCountry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionProviderAreaCountryMapper extends BaseMapper<InstitutionProviderAreaCountry> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(InstitutionProviderAreaCountry record);


    /**
     * @return java.lang.String
     * @Description: 获取国家名称
     * @Param [providerId]
     * <AUTHOR>
     */
    String getAreaCountryNameByProviderId(Long providerId);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionProviderAreaCountryVo>
     * @Description :公式国家区域下拉框
     * @Param [institutionProviderId]
     * <AUTHOR>
     */
    List<InstitutionProviderAreaCountryVo> getInstitutionProviderAreaCountrySelect(@Param("institutionProviderId") Long institutionProviderId);

    /**
     * 删除数据
     *
     * @return
     */
    void deleteByProviderId(@Param("id") Long id);


    /**
     * 获取业务国家名称
     *
     * @param providerIds
     * @return
     */
    List<InstitutionProviderAreaCountryVo> getInstitutionProviderAreaCountryStr(@Param("providerIds") List<Long> providerIds);

    /**
     * 根据提供商Id获取业务国家ids
     *
     * @param providerId
     * @return
     */
    List<Long> getAreaCountryIdsByProviderId(@Param("providerId") Long providerId);


}