package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.dto.InstitutionChannelDto;
import com.get.institutioncenter.vo.InstitutionChannelVo;
import com.get.institutioncenter.dto.InstitutionChannelCompanyDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2021/4/12
 * @TIME: 15:21
 * @Description:
 **/
public interface IInstitutionChannelService{
    /**
     * @return com.get.institutioncenter.vo.InstitutionGroupVo
     * @Description：详情
     * @Param [id]
     * <AUTHOR>
     **/
    InstitutionChannelVo findInstitutionChannelById(Long id);

    /**
     * @return void
     * @Description：批量新增
     * @Param [institutionGroupVos]
     * <AUTHOR>
     **/
    void batchAdd(ValidList<InstitutionChannelDto> institutionChannelDtos);


    /**
     * @return void
     * @Description：删除
     * @Param [id]
     * <AUTHOR>
     **/
    void delete(Long id);

    /**
     * @return com.get.institutioncenter.vo.InstitutionGroupVo
     * @Description：修改
     * @Param [institutionGroupVo]
     * <AUTHOR>
     **/
    InstitutionChannelVo updateInstitutionChannel(InstitutionChannelDto institutionChannelDto);


    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionGroupVo>
     * @Description：列表
     * @Param [institutionGroupVo, page]
     * <AUTHOR>
     **/
    List<InstitutionChannelVo> getInstitutionChannels(InstitutionChannelDto institutionChannelDto, Page page);

    ResponseBo editInstitutionChannelCompanyRelation(ValidList<InstitutionChannelCompanyDto> channelCompanyVos);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionGroupVo>
     * @Description：渠道下拉框
     * @Param []
     * <AUTHOR>
     **/
    List<BaseSelectEntity> getInstitutionChannelSelect();

    /**
     * 获取渠道名称
     *
     * @param ids
     * @return
     */
    Map<Long, String> getChannelByIds(Set<Long> ids);

    /**
     * 通过名称返回渠道ids
     *
     * @param channelName
     * @return
     */
    List<Long> getInstitutionProviderChannelIdsByName(String channelName);


    /**
     * 模糊搜索渠道名称
     * @param keyword
     * @param companyIds
     * @return
     */
    List<BaseSelectEntity> fuzzSearchInstitutionChannel(String keyword,List<Long> companyIds);
}
