package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.vo.InstitutionFacultyVo;
import com.get.institutioncenter.entity.InstitutionFaculty;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface InstitutionFacultyMapper extends BaseMapper<InstitutionFaculty> {
    @Override
    int insert(InstitutionFaculty record);

    int insertSelective(InstitutionFaculty record);

    /**
     * 根据学院id查找学院名称
     *
     * @param id
     * @return
     */
    String getInstitutionFacultyNameById(Long id);

    /**
     * 根据课程ids查找学院名称
     *
     * @param courseIds
     * @return
     */
    List<InstitutionFacultyVo> getInstitutionFacultyNameByCourseIds(@Param("courseIds") Set<Long> courseIds);

    /**
     * 根据学校查找学院数量
     *
     * @param id
     * @return
     */
    Integer getFacultyCountByInstitutionId(Long id);

    String getInstitutionFacultyNameByid(@Param("id") Long id);

    List<BaseSelectEntity> getFacultyList(@Param("keyword") String keyword,@Param("institutionId") Long institutionId);
}