package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.NewsTypeVo;
import com.get.institutioncenter.entity.NewsType;
import com.get.institutioncenter.dto.NewsTypeDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/8/6 11:44
 * @verison: 1.0
 * @description:
 */
public interface INewsTypeService extends BaseService<NewsType> {

    /**
     * 详情
     *
     * @param id
     * @return
     */
    NewsTypeVo findNewsTypeById(Long id);

    /**
     * 批量新增
     *
     * @param newsTypeDtos
     * @return
     */
    void batchAdd(List<NewsTypeDto> newsTypeDtos);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param newsTypeDto
     * @return
     */
    NewsTypeVo updateNewsType(NewsTypeDto newsTypeDto);


    /**
     * 列表
     *
     * @param newsTypeDto
     * @param page
     * @return
     */
    List<NewsTypeVo> getNewsTypes(NewsTypeDto newsTypeDto, Page page);

    /**
     * 上移下移
     *
     * @param newsTypeDtos
     * @return
     */
    void movingOrder(List<NewsTypeDto> newsTypeDtos);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :新闻类型下拉框
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getNewsTypeList();


    /**
     * 根据新闻类型ids获取名称map
     *
     * @param ids
     * @return
     */
    Map<Long, String> getNewTypeNamesByIds(Set<Long> ids);
}
