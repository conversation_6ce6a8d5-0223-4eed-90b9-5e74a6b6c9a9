package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.InstitutionAlumnusVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.InstitutionAlumnus;
import com.get.institutioncenter.dto.InstitutionAlumnusDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 14:38
 * @Description:
 **/
public interface IInstitutionAlumnusService extends BaseService<InstitutionAlumnus> {
    /**
     * 列表数据
     *
     * @param institutionAlumnusDto
     * @param page
     * @return
     */
    List<InstitutionAlumnusVo> datas(InstitutionAlumnusDto institutionAlumnusDto, Page page);

    /**
     * 保存
     *
     * @param institutionAlumnusDto
     * @return
     */
    Long addInstitutionAlumnus(InstitutionAlumnusDto institutionAlumnusDto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionAlumnusVo findInstitutionAlumnusById(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);


    /**
     * 修改
     *
     * @param institutionAlumnusDto
     * @return
     */
    InstitutionAlumnusVo updateInstitutionAlumnus(InstitutionAlumnusDto institutionAlumnusDto);

    /**
     * 保存附件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addInstitutionAlumnusMedia(List<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * 获取知名校友附件类型
     *
     * @return
     */
    List<Map<String, Object>> findMediaAndAttachedType();

    /**
     * 查询知名校友附件
     *
     * @param data
     * @param page
     * @return
     * @
     */
    List<MediaAndAttachedVo> getInstitutionAlumnusMedia(MediaAndAttachedDto data, Page page);
}
