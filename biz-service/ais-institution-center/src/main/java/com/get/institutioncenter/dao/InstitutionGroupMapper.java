package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.entity.InstitutionGroup;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionGroupMapper extends BaseMapper<InstitutionGroup> {
    @Override
    int insert(InstitutionGroup record);

    int insertSelective(InstitutionGroup record);

    /**
     * 根据id查找名称
     *
     * @param id
     * @return
     */
    String getNameById(Long id);

    List<BaseSelectEntity> getInstitutionGroupSelect();

    /**
     * feign调用 通过集团ids 查找对应的集团名称map
     *
     * @Date 17:55 2021/6/3
     * <AUTHOR>
     */
    String getInstitutionGroupNameById(Long id);

    /**
     * 查询学校集团列表
     *
     * @Date 12:50 2021/6/23
     * <AUTHOR>
     */
    List<InstitutionGroup> getInstitutionGroups(IPage<InstitutionGroup> page, @Param("name") String name);

}