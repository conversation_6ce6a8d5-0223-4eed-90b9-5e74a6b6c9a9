package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.component.TranslationHelp;
import com.get.institutioncenter.dao.AreaCityInfoTypeMapper;
import com.get.institutioncenter.dto.AreaCityInfoTypeDto;
import com.get.institutioncenter.po.AreaCityInfoTypePo;
import com.get.institutioncenter.vo.AreaCityInfoTypeVo;
import com.get.institutioncenter.entity.AreaCityInfoType;
import com.get.institutioncenter.service.IAreaCityInfoTypeService;
import com.get.institutioncenter.service.IDeleteService;
import com.get.institutioncenter.service.ITranslationMappingService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/3/1 17:05
 * @verison: 1.0
 * @description:
 */
@Service
public class AreaCityInfoTypeServiceImpl extends BaseServiceImpl<AreaCityInfoTypeMapper, AreaCityInfoType> implements IAreaCityInfoTypeService {
    @Resource
    private AreaCityInfoTypeMapper areaCityInfoTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private GetRedis redisClient;
    @Resource
    private TranslationHelp translationHelp;

    @Override
    public AreaCityInfoTypeVo findAreaCityInfoTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaCityInfoType areaCityInfoType = areaCityInfoTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(areaCityInfoType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaCityInfoTypeVo areaCityInfoTypeVo = BeanCopyUtils.objClone(areaCityInfoType, AreaCityInfoTypeVo::new);
        //语言
        areaCityInfoTypeVo.setFkTableName(TableEnum.INSTITUTION_AREA_CITY_INFO_TYPE.key);
        String language = SecureUtil.getLocale();
        AreaCityInfoTypePo areaCityInfoTypePo = BeanCopyUtils.objClone(areaCityInfoType, AreaCityInfoTypePo::new);
        if (GeneralTool.isNotEmpty(areaCityInfoTypePo) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(Collections.singletonList(areaCityInfoTypePo), ProjectKeyEnum.getInitialValue(language));
        }
        if (GeneralTool.isNotEmpty(areaCityInfoTypePo)){
           areaCityInfoTypeVo = BeanCopyUtils.objClone(areaCityInfoTypePo, AreaCityInfoTypeVo::new);
        }
        return areaCityInfoTypeVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<AreaCityInfoTypeDto> areaCityInfoTypeDtos) {
        if (GeneralTool.isEmpty(areaCityInfoTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }

        Integer maxViewOrder = areaCityInfoTypeMapper.getMaxViewOrder();
        for (AreaCityInfoTypeDto areaCityInfoTypeDto : areaCityInfoTypeDtos) {
            //批量修改回显
            if (GeneralTool.isEmpty(areaCityInfoTypeDto.getId())) {
                if (validateAdd(areaCityInfoTypeDto)) {
                    //获取最大排序
                    areaCityInfoTypeDto.setViewOrder(maxViewOrder);
                    AreaCityInfoType areaCityInfoType = BeanCopyUtils.objClone(areaCityInfoTypeDto, AreaCityInfoType::new);
                    utilService.updateUserInfoToEntity(areaCityInfoType);
                    int i = areaCityInfoTypeMapper.insertSelective(areaCityInfoType);
                    if (i <= 0) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                    }
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
                }
            } else {
                AreaCityInfoType areaCityInfoType = BeanCopyUtils.objClone(areaCityInfoTypeDto, AreaCityInfoType::new);
                utilService.updateUserInfoToEntity(areaCityInfoType);
                areaCityInfoTypeMapper.updateById(areaCityInfoType);
            }
            maxViewOrder++;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (areaCityInfoTypeMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //删除验证
        deleteService.deleteValidateAreaCityInfoType(id);
        areaCityInfoTypeMapper.deleteById(id);

        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_AREA_CITY_INFO_TYPE.key, id);
    }

    @Override
    public AreaCityInfoTypeVo updateAreaCityInfoType(AreaCityInfoTypeDto areaCityInfoTypeDto) {
        if (areaCityInfoTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AreaCityInfoType result = areaCityInfoTypeMapper.selectById(areaCityInfoTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (validateUpdate(areaCityInfoTypeDto)) {
            AreaCityInfoType areaCityInfoType = BeanCopyUtils.objClone(areaCityInfoTypeDto, AreaCityInfoType::new);
            utilService.updateUserInfoToEntity(areaCityInfoType);
            int i = areaCityInfoTypeMapper.updateById(areaCityInfoType);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
        }
        return findAreaCityInfoTypeById(areaCityInfoTypeDto.getId());
    }

    @Override
    public List<AreaCityInfoTypeVo> getAreaCityInfoTypes(AreaCityInfoTypeDto areaCityInfoTypeDto, Page page) {
        LambdaQueryWrapper<AreaCityInfoType> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(areaCityInfoTypeDto)) {
            if (GeneralTool.isNotEmpty(areaCityInfoTypeDto)) {
                //查询条件-城市资讯类型
                if (GeneralTool.isNotEmpty(areaCityInfoTypeDto.getTypeName())) {
                    wrapper.like(AreaCityInfoType::getTypeName, areaCityInfoTypeDto.getTypeName());
                }
            }
        }
        wrapper.orderByDesc(AreaCityInfoType::getViewOrder);
        //获取分页数据
        IPage<AreaCityInfoType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<AreaCityInfoType> areaCityInfoTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<AreaCityInfoTypeVo> convertDatas = new ArrayList<>();
        for (AreaCityInfoType areaCityInfoType : areaCityInfoTypes) {
            AreaCityInfoTypeVo areaCityInfoTypeVo = BeanCopyUtils.objClone(areaCityInfoType, AreaCityInfoTypeVo::new);
            areaCityInfoTypeVo.setFkTableName(TableEnum.INSTITUTION_AREA_CITY_INFO_TYPE.key);
            convertDatas.add(areaCityInfoTypeVo);
        }
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(convertDatas) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(convertDatas, ProjectKeyEnum.getInitialValue(language));
        }
        return convertDatas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<AreaCityInfoTypeDto> areaCityInfoTypeDtos) {
        if (GeneralTool.isEmpty(areaCityInfoTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AreaCityInfoType ro = BeanCopyUtils.objClone(areaCityInfoTypeDtos.get(0), AreaCityInfoType::new);
        Integer oneorder = ro.getViewOrder();
        AreaCityInfoType rt = BeanCopyUtils.objClone(areaCityInfoTypeDtos.get(1), AreaCityInfoType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        areaCityInfoTypeMapper.updateById(ro);
        areaCityInfoTypeMapper.updateById(rt);
    }

    @Override
    public List<AreaCityInfoTypeVo> getAreaCityInfoTypeList() {
        List<AreaCityInfoType> areaCityInfoTypes = this.areaCityInfoTypeMapper.selectList(Wrappers.<AreaCityInfoType>query().lambda().orderByDesc(AreaCityInfoType::getViewOrder));
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(areaCityInfoTypes) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(Collections.singletonList(areaCityInfoTypes), ProjectKeyEnum.getInitialValue(language));
        }
        return areaCityInfoTypes.stream().map(areaCityInfoType -> BeanCopyUtils.objClone(areaCityInfoType, AreaCityInfoTypeVo::new)).collect(Collectors.toList());
    }

    @Override
    public String getAreaCityInfoTypeNameById(Long areaCityInfoTypeId) {
        return areaCityInfoTypeMapper.getAreaCityInfoTypeNameById(areaCityInfoTypeId);
    }

    private boolean validateAdd(AreaCityInfoTypeDto areaCityInfoTypeDto) {
        List<AreaCityInfoType> areaCityInfoTypes = this.areaCityInfoTypeMapper.selectList(Wrappers.<AreaCityInfoType>query().lambda().eq(AreaCityInfoType::getTypeName, areaCityInfoTypeDto.getTypeName()).orderByDesc(AreaCityInfoType::getViewOrder));
        return GeneralTool.isEmpty(areaCityInfoTypes);
    }

    private boolean validateUpdate(AreaCityInfoTypeDto areaCityInfoTypeDto) {
        List<AreaCityInfoType> list = this.areaCityInfoTypeMapper.selectList(Wrappers.<AreaCityInfoType>query().lambda().eq(AreaCityInfoType::getTypeName, areaCityInfoTypeDto.getTypeName()));
        return list.size() <= 0 || list.get(0).getId().equals(areaCityInfoTypeDto.getId());
    }
}
