package com.get.institutioncenter.service;

import com.get.institutioncenter.dto.BatchUpdateDto;
import com.get.permissioncenter.dto.BatchModifyConfigDto;

import java.util.List;
import java.util.Map;

/**
 * @author: Sea
 * @create: 2021/3/1 12:17
 * @verison: 1.0
 * @description:批量修改操作接口
 */
public interface IBatchUpdateService {
    /**
     * @Description :加载课程批量修改列表接口
     * @Param [batchModifyConfigDto]
     * <AUTHOR>
     */
    List<Map<String, String>> getBatchUpdates(BatchModifyConfigDto batchModifyConfigDto);

    /**
     * @return void
     * @Description :批量修改接口
     * @Param [batchUpdateVoList]
     * <AUTHOR>
     */
    void batchUpdate(List<BatchUpdateDto> batchUpdateVoList);
}
