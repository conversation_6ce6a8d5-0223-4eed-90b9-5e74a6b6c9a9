package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.InstitutionCourseAcademicScoreVo;
import com.get.institutioncenter.service.IInstitutionCourseAcademicScoreService;
import com.get.institutioncenter.dto.InstitutionCourseAcademicScoreDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/10
 * @TIME: 15:09
 * @Description:
 **/
@Api(tags = "学术成绩管理")
@RestController
@RequestMapping("/institution/institutionCourseAcademicScore")
public class InstitutionCourseAcademicScoreController {
    @Resource
    private IInstitutionCourseAcademicScoreService institutionCourseAcademicScoreService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/学术成绩管理/学术成绩详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionCourseAcademicScoreVo> detail(@PathVariable("id") Long id) {
        InstitutionCourseAcademicScoreVo data = institutionCourseAcademicScoreService.findInstitutionCourseAcademicScoreById(id);
        return new ResponseBo<>(data);
    }


    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学术成绩管理/删除学术成绩")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.institutionCourseAcademicScoreService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param institutionCourseAcademicScoreDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学术成绩管理/更新学术成绩")
    @PostMapping("update")
    public ResponseBo<InstitutionCourseAcademicScoreVo> update(@RequestBody @Validated(InstitutionCourseAcademicScoreDto.Update.class) InstitutionCourseAcademicScoreDto institutionCourseAcademicScoreDto) {
        return UpdateResponseBo.ok(institutionCourseAcademicScoreService.updateInstitutionCourseAcademicScore(institutionCourseAcademicScoreDto));
    }

    /**
     * 批量新增信息
     *
     * @param resources
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学术成绩管理/批量保存")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(InstitutionCourseAcademicScoreDto.Add.class) ValidList<InstitutionCourseAcademicScoreDto> resources) {
        institutionCourseAcademicScoreService.batchAdd(resources);
        return ResponseBo.ok();
    }

    /**
     * 分数类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "分数类型下拉框数据", notes = "")
    @GetMapping("getScoreTypeByCondition/{id}")
    public ResponseBo getScoreTypeByCondition(@PathVariable("id") Long id) {
        List<String> datas = institutionCourseAcademicScoreService.getScoreTypeByCondition(id);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 代理附件类型
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "标准测试类型", notes = "")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getStandardTestType")
    public ResponseBo getStandardTestType() {
        List<Map<String, Object>> datas = institutionCourseAcademicScoreService.getStandardTestType();
        return new ListResponseBo<>(datas);
    }

    @ApiOperation("学术成绩枚举下拉")
    @VerifyPermission(IsVerify = false)
    @GetMapping("getAcademicGrades")
    public ResponseBo getAcademicGrades() {
        return new ListResponseBo<>(ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.ACADEMIC_SUB_TABLE_CONDITION_TYPE));
    }
}
