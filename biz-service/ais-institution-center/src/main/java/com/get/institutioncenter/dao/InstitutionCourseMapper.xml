<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionCourseMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionCourse">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_chn" jdbcType="VARCHAR" property="nameChn" />
    <result column="name_display" jdbcType="VARCHAR" property="nameDisplay"/>
    <result column="start_month" jdbcType="VARCHAR" property="startMonth" />
    <result column="start_date_note" jdbcType="VARCHAR" property="startDateNote" />
    <result column="apply_date_note" jdbcType="VARCHAR" property="applyDateNote" />
    <result column="introduction" jdbcType="VARCHAR" property="introduction" />
    <result column="apply_month" jdbcType="VARCHAR" property="applyMonth" />
    <result column="fee" jdbcType="DECIMAL" property="fee" />
    <result column="fee_max" jdbcType="DECIMAL" property="feeMax" />
    <result column="fee_note" jdbcType="VARCHAR" property="feeNote" />
    <result column="duration_year" jdbcType="DECIMAL" property="durationYear" />
    <result column="duration_note" jdbcType="VARCHAR" property="durationNote" />
    <result column="core_course" jdbcType="VARCHAR" property="coreCourse" />
    <result column="entry_standards" jdbcType="VARCHAR" property="entryStandards" />
    <result column="occupation_development" jdbcType="VARCHAR" property="occupationDevelopment" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="public_level" jdbcType="VARCHAR" property="publicLevel" />
    <result column="data_level" jdbcType="INTEGER" property="dataLevel" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
    <result column="id_gea" jdbcType="VARCHAR" property="idGea" />
    <result column="id_iae" jdbcType="VARCHAR" property="idIae" />
    <result column="fee_cny" jdbcType="DECIMAL" property="feeCny" />
  </resultMap>
  <select id="getInstitutionCourseByInstitution" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select id,name,name_chn,concat(if(is_active=0,'【无效】',''),
                                   CASE
                                     WHEN IFNULL(name_chn,'') = ''
                                       THEN
                                       name ELSE CONCAT(name, '（', name_chn, '）' )
                                     END) fullName,is_active status from m_institution_course where fk_institution_id =#{institutionId}  ORDER BY
                                                                                                                                           is_active DESC,view_order,name ASC
  </select>

  <select id="getInstitutionCourseByInstitutionIdList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select id,name,name_chn,concat(if(is_active=0,'【无效】',''),
    CASE
    WHEN IFNULL(name_chn,'') = ''
    THEN
    name ELSE CONCAT(name, '（', name_chn, '）' )
    END) fullName,is_active status from m_institution_course where fk_institution_id IN
    <foreach collection="institutionIdList" item="item" index="index" open="(" separator="," close=")">
      #{item}
    </foreach>
    ORDER BY
    is_active DESC,view_order, CONVERT(name USING gbk) ASC
  </select>

  <select id="isExistByCourseType" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id from m_institution_course where fk_course_type_id=#{fkCourseTypeId}
  </select>
  <select id="getTarget" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select id,name,name_chn,concat(if(is_active=0,'【无效】',''),
                                   CASE
                                     WHEN IFNULL(name_chn,'') = ''
                                       THEN
                                       name ELSE CONCAT(name, '（', name_chn, '）' )
                                     END) fullName,is_active status from ${tableName}
  </select>
  <select id="getBridgeInstitutionCourseSelect"  resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select i.id,i.name,i.name_chn as nameChn,concat(if(i.is_active=0,'【无效】',''),
    CASE
    WHEN IFNULL(i.name_chn,'') = ''
    THEN
    name ELSE CONCAT(i.name, '（', i.name_chn, '）' )
    END) fullName,i.is_active status from m_institution_course i
    <where>
      <if test="fkInstitutionId != null">
        i.fk_institution_id = #{fkInstitutionId,jdbcType=BIGINT}
      </if>
      <if test="ids != null and ids.size()>0">
        and i.id not in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>
    ORDER BY
    i.is_active DESC,i.view_order, CONVERT(i.name USING gbk) ASC
  </select>
  <select id="getNonBridgeInstitutionCoursePathwayByInstitution"  resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select i.id,i.name,i.name_chn as nameChn,concat(if(i.is_active=0,'【无效】',''),
    CASE
    WHEN IFNULL(i.name_chn,'') = ''
    THEN
    name ELSE CONCAT(i.name, '（', i.name_chn, '）' )
    END) fullName,i.is_active status from m_institution_course i
    <where>
      <if test="fkInstitutionId != null">
        i.fk_institution_id = #{fkInstitutionId,jdbcType=BIGINT}
      </if>
      <if test="ids != null and ids.size()>0">
        and i.id not in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>
    ORDER BY
    i.is_active DESC,i.view_order, CONVERT(i.name USING gbk) ASC
  </select>
  <select id="getNameById" parameterType="java.lang.Long" resultType="string">
    select
      CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from
      m_institution_course i
    where
      i.id = #{id}
  </select>
  <select id="datas" resultType="com.get.institutioncenter.vo.InstitutionCourseVo">
    select ic.*
    from m_institution_course ic left join r_institution_course_faculty icf
    on ic.id = icf.fk_institution_course_id left join r_institution_course_type ict
    on ic.id = ict.fk_institution_course_id
    <!--    <if test="fkMajorLevelId != null">-->
    <!--      LEFT join (select * from r_institution_course_major_level where fk_major_level_id = #{fkMajorLevelId}) icml-->
    <!--      on ic.id = icml.fk_institution_course_id-->
    <!--    </if>-->
    LEFT join r_institution_course_major_level AS icml on ic.id = icml.fk_institution_course_id
    left join m_institution i
    on ic.fk_institution_id = i.id
    LEFT JOIN r_institution_course_zone AS icz ON icz.fk_institution_course_id = ic.id
    <where>
      <if test="institutionCourseDto.fkMajorLevelId != null and institutionCourseDto.fkMajorLevelId != 0">
        AND icml.fk_major_level_id = #{institutionCourseDto.fkMajorLevelId}
      </if>
      <if test="institutionCourseDto.fkMajorLevelId != null and institutionCourseDto.fkMajorLevelId == 0">
        AND icml.fk_major_level_id IS NULL
      </if>
      <if test="institutionCourseDto.name != null and institutionCourseDto.name !=''">
        and (ic.num like  CONCAT(CONCAT('%', #{institutionCourseDto.name}), '%') or ic.name like CONCAT(CONCAT('%', #{institutionCourseDto.name}), '%') or ic.name_chn like CONCAT(CONCAT('%', #{institutionCourseDto.name}), '%'))
      </if>
      <if test="institutionCourseDto.fkInstitutionId != null and institutionCourseDto.fkInstitutionId != 0">
        and ic.fk_institution_id=#{institutionCourseDto.fkInstitutionId}
      </if>
      <if test="institutionCourseDto.fkInstitutionId != null and institutionCourseDto.fkInstitutionId == 0">
        and ic.fk_institution_id IS NULL
      </if>
      <if test="institutionCourseDto.fkInstitutionFacultyId != null and institutionCourseDto.fkInstitutionFacultyId != 0">
        and icf.fk_institution_faculty_id=#{institutionCourseDto.fkInstitutionFacultyId}
      </if>
      <if test="institutionCourseDto.fkInstitutionFacultyId != null and institutionCourseDto.fkInstitutionFacultyId == 0">
        and icf.fk_institution_faculty_id IS NULL
      </if>
      <if test="institutionCourseDto.fkInstitutionZoneId != null and institutionCourseDto.fkInstitutionZoneId == 0">
        and icz.fk_institution_zone_id IS NULL
      </if>
      <if test="institutionCourseDto.fkInstitutionZoneId != null and institutionCourseDto.fkInstitutionZoneId != 0">
        and icz.fk_institution_zone_id=#{institutionCourseDto.fkInstitutionZoneId}
      </if>
      <if test="institutionCourseDto.publicLevel!=null and institutionCourseDto.publicLevel !='' and institutionCourseDto.publicLevel !='0'">
        and  position(#{institutionCourseDto.publicLevel} in ic.public_level)
      </if>
      <if test="institutionCourseDto.publicLevel!=null and institutionCourseDto.publicLevel !='' and institutionCourseDto.publicLevel =='0'">
        and  ic.public_level IS NULL
      </if>
      <if test="institutionCourseDto.dataLevel == 0 or institutionCourseDto.dataLevel != null and institutionCourseDto.dataLevel != ''">
        and ic.data_level=#{institutionCourseDto.dataLevel}
      </if>
      <if test="institutionCourseDto.fkCourseTypeId != null and institutionCourseDto.fkCourseTypeId != 0">
        and ict.fk_course_type_id=#{institutionCourseDto.fkCourseTypeId}
      </if>
      <if test="institutionCourseDto.fkCourseTypeId != null and institutionCourseDto.fkCourseTypeId == 0">
        and ict.fk_course_type_id IS NULL
      </if>
      <if test="institutionCourseDto.fkAreaCountryId != null">
        and i.fk_area_country_id=#{institutionCourseDto.fkAreaCountryId}
      </if>
      <if test="institutionCourseDto.fkInstitutionTypeId != null ">
        and i.fk_institution_type_id=#{institutionCourseDto.fkInstitutionTypeId}
      </if>
      <if test="institutionCourseDto.fkMajorLevelIds!=null and institutionCourseDto.fkMajorLevelIds.size()>0">
        and ( 1=0
        <foreach collection="institutionCourseDto.fkMajorLevelIds" item="id" index="index">
          <if test="id == 0">
            OR icml.fk_major_level_id IS NULL
          </if>
          <if test="id != 0">
            OR FIND_IN_SET(#{id},icml.fk_major_level_id)
          </if>
        </foreach>
        )
      </if>
    </where>
    group by ic.id
    <if test="institutionCourseDto.orderBy == null and institutionCourseDto.orderBy == ''">
      order by ic.is_active,i.id DESC,ic.view_order, CONVERT(ic.name USING gbk) ASC
    </if>
    <if test="institutionCourseDto.orderBy != null and institutionCourseDto.orderBy != ''">
      order by ${institutionCourseDto.orderBy}
    </if>
  </select>
  <select id="getCourseIdsByTypeAndLevel" resultType="java.lang.Long">
    select ic.id from m_institution_course ic
    left join r_institution_course_type ict on ic.id = ict.fk_institution_course_id
    left join r_institution_course_major_level inml on ic.id = inml.fk_institution_course_id
    <where>
      <if test="fkMajorLevelIds != null and fkMajorLevelIds.size()>0">
        and inml.fk_major_level_id  in
        <foreach collection="fkMajorLevelIds" item="fkMajorLevelId" index="index" open="(" separator="," close=")">
          #{fkMajorLevelId}
        </foreach>
      </if>
      <if test="fkCourseTypeIds != null and fkCourseTypeIds.size()>0">
        and ict.fk_course_type_id in
        <foreach collection="fkCourseTypeIds" item="fkCourseTypeId" index="index" open="(" separator="," close=")">
          #{fkCourseTypeId}
        </foreach>
      </if>
    </where>
  </select>
  <select id="getFeeById" resultType="java.lang.Double">
    select IF(fee IS NULL,0, fee)  from m_institution_course where id =#{id}
  </select>
  <select id="getSumFeeByIds" resultType="java.lang.Double">
    select SUM(fee) from m_institution_course
    <where>
      <if test="ids != null and ids.size()>0">
        and id in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
    </where>
  </select>


  <select id="getAllcourseDel" resultType="com.get.institutioncenter.vo.InstitutionCourseVo">
    SELECT
    instc.id,
    instc.fee,
    inst.fk_currency_type_num,
    instc.fk_institution_id
    FROM
    m_institution_course instc
    LEFT JOIN m_institution inst ON instc.fk_institution_id = inst.id
    <where>
      instc.fee IS NOT NULL
      <if test="updateType = 'updateAny'">
        and fee_cny is null;
      </if>
    </where>


  </select>

  <select id="getAllSchoolFkCurrencyTypeNum" resultType="java.lang.String">
    SELECT
      inst.fk_currency_type_num
    FROM
      m_institution_course instc
        LEFT JOIN m_institution inst ON instc.fk_institution_id = inst.id
    where
        instc.fk_institution_id in (SELECT fk_institution_id from m_institution_course WHERE fee is not NULL GROUP BY fk_institution_id) GROUP BY inst.fk_currency_type_num

  </select>

  <update id="updateAllCourseFerCny" >
    update m_institution_course set fee_cny = #{feeCny,jdbcType=BIGINT}
    <where>
      id = #{id,jdbcType=BIGINT}
    </where>
  </update>

  <select id="getInstitutionCourseByName" resultType="java.lang.Long">
        select mic.id from m_institution_course mic
        where 1=1 and (
            mic.name
            LIKE CONCAT('%',#{courseName},'%')
            or mic.name_chn LIKE CONCAT('%',#{courseName},'%')
            )
    <if test="institutionId!=null and institutionId !=''">
            and mic.fk_institution_id = #{institutionId}
    </if>
  </select>

  <select id="getInstitutionCourseIdsByLevelAndName" resultType="java.lang.Long">
    select mic.id from m_institution_course mic left join  r_institution_course_major_level micml
    on mic.id = micml.fk_institution_course_id
    where 1=1
    <if test="name!=null">
      and  (mic.name LIKE CONCAT('%',#{name},'%')
      or mic.name_chn LIKE CONCAT('%',#{name},'%'))
    </if>
    <if test="levelId!=null">
      and micml.fk_major_level_id = #{levelId}
    </if>
  </select>

  <select id="getInstitutionCourseWesiteByName" resultType = "com.get.institutioncenter.vo.InstitutionCourseVo">
      SELECT a.type_value ,c.*
      from m_institution_course c
               LEFT JOIN s_app_info a ON a.fk_table_id = c.id and a.type_key ='app_course_website' and a.fk_table_name = 'm_institution_course'
      where c.name = #{courseNames}
      <!--  and c.is_active = 1 -->
        and c.fk_institution_id = #{institutionId}
      ORDER BY a.gmt_create DESC limit 1
  </select>

  <select id="InstitutionCourseListByName" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select mic.id,CONCAT(mic.name,'【',mis.`name`,'】') as full_name,mic.name,mic.name_chn as nameChn
    from m_institution_course mic
    left join m_institution mis on mic.fk_institution_id = mis.id
    where 1=1
    <if test="courseName!=null and courseName!=''">
      and
      (
      REPLACE(LOWER(mic.name),'','') LIKE CONCAT('%',#{courseName},'%')
      or REPLACE(LOWER(mic.name_chn),'','') LIKE CONCAT('%',#{courseName},'%')
      )
    </if>
    <if test="institutionIds!=null and institutionIds.size()>0">
      and mic.fk_institution_id in
      <foreach collection="institutionIds" item="institutionId" index="index" open="(" separator="," close=")">
        #{institutionId}
      </foreach>
    </if>
    <if test="courseIds!=null and courseIds.size>0">
      and mic.id IN
      <foreach collection="courseIds" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
    order by mic.name
    limit 100
  </select>
    <select id="getCourseStatistics" resultType="com.get.institutioncenter.dto.CaseStudyResultsDto$Statistics">
      SELECT
          g.id,
          g.type_group_name_chn AS `name`,
          COUNT(g.id) AS num,
          2 as type
      FROM
          u_course_type_group g
      INNER JOIN r_course_type_group_course_type r ON r.fk_course_type_group_id = g.id
      INNER JOIN r_institution_course_type c ON c.fk_course_type_id = r.fk_course_type_id
      <if test="ids!=null and ids.size>0">
        WHERE c.fk_institution_course_id IN
        <foreach collection="ids" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
      GROUP BY
          g.id
    </select>
    <select id="getCourseSelectedByKeyword" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      SELECT
          id,
          NAME AS NAME,
          name_chn AS nameChn,
          CASE
      WHEN IFNULL(name_chn, '') = '' THEN
          `name`
      ELSE
          CONCAT(
              `name`,
              '（',
              name_chn,
              '）'
          )
      END fullName
      FROM
          m_institution_course
      WHERE
        1=1
      <if test="institutionId!=null and institutionId!=''">
        AND fk_institution_id = #{institutionId}
      </if>
      <if test="keyword!=null and keyword!=''">
        AND (`name` LIKE concat('%',#{keyword},'%') OR name_chn LIKE concat('%',#{keyword},'%'))
      </if>
      LIMIT 50
    </select>
    <select id="getInstitutionCourseList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      SELECT
      c.id,
      c.NAME,
      c.name_chn,
      concat(
      '【',i.name_chn,'】',
      CASE
      WHEN IFNULL(c.name_chn, '') = '' THEN
      c.NAME
      ELSE CONCAT(c.NAME, '（', c.name_chn, '）')
      END
      ) fullName,
      c.is_active status
      FROM m_institution_course c
      left join m_institution i on i.id = c.fk_institution_id
      where 1=1
      <if test="name!=null and name!=''">
        AND (c.name
        LIKE CONCAT('%',#{name},'%')
        or c.name_chn LIKE CONCAT('%',#{name},'%'))
      </if>
      <if test="courseIds!=null and courseIds.size>0">
        and c.id IN
        <foreach collection="courseIds" item="item" separator="," open="(" close=")">
          #{item}
        </foreach>
      </if>
      ORDER BY c.is_active DESC, c.name ASC
      limit 50
    </select>

  <select id="getInstitutionCourseListByIds" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select mic.id,CONCAT(mic.name,'【',mis.`name`,'】') as full_name,mic.name,mic.name_chn as nameChn
    from m_institution_course mic
    left join m_institution mis on mic.fk_institution_id = mis.id
    where 1=1
    <if test="courseIds!=null and courseIds.size>0">
      and mic.id IN
      <foreach collection="courseIds" item="item" separator="," open="(" close=")">
        #{item}
      </foreach>
    </if>
    limit 100
  </select>

  <select id="getInstitutionCourseIdsByColumnName" resultType="java.lang.Long">
    SELECT id FROM m_institution_course WHERE ${columnName} IS NOT NULL AND ${columnName} != ""
  </select>

  <select id="getInstitutionCourseById" resultType="com.get.institutioncenter.entity.InstitutionCourse">
    SELECT * FROM m_institution_course WHERE id = #{id}
  </select>

</mapper>