package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaMajorLevel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ContractFormulaMajorLevelMapper extends BaseMapper<ContractFormulaMajorLevel> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContractFormulaMajorLevel record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应课程等级ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getMajorLevelIdListByFkid(@Param("contractFormulaId") Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :通过合同公式id 查找对应课程等级名称
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<String> getMajorLevelNameByFkid(@Param("contractFormulaId") Long contractFormulaId);
}