package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.dto.AreaCountryInfoDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.AreaCountryInfoVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/13 17:05
 * @verison: 1.0
 * @description:
 */
public interface IAreaCountryInfoService {

    /**
     * @return com.get.institutioncenter.vo.AreaCountryInfoVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    AreaCountryInfoVo findAreaCountryInfoById(Long id);

    /**
     * @return void
     * @Description :新增
     * @Param [areaCountryInfoDto]
     * <AUTHOR>
     */
    Long addAreaCountryInfo(AreaCountryInfoDto areaCountryInfoDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.institutioncenter.vo.AreaCountryInfoVo
     * @Description :修改
     * @Param [areaCountryInfoDto]
     * <AUTHOR>
     */
    AreaCountryInfoVo updateAreaCountryInfo(AreaCountryInfoDto areaCountryInfoDto);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.AreaCountryInfoVo>
     * @Description :列表
     * @Param [areaCountryInfoDto, page]
     * <AUTHOR>
     */
    List<AreaCountryInfoVo> getAreaCountryInfos(AreaCountryInfoDto areaCountryInfoDto, Page page);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.lang.Long
     * @Description: 新增附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);
}
