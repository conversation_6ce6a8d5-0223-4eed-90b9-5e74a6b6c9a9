package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.InfoTypeVo;
import com.get.institutioncenter.service.IInfoTypeService;
import com.get.institutioncenter.dto.InfoTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/5 10:31
 * @verison: 1.0
 * @description: 资讯类型管理控制器
 */
@Api(tags = "资讯类型管理")
@RestController
@RequestMapping("/institution/infoType")
public class InfoTypeController {

    @Resource
    private IInfoTypeService infoTypeService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/资讯类型管理/资讯类型详情")
    @GetMapping("/{id}")
    public ResponseBo<InfoTypeVo> detail(@PathVariable("id") Long id) {
        InfoTypeVo data = infoTypeService.findInfoTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 批量新增信息
     *
     * @param infoTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/资讯类型管理/新增资讯类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(InfoTypeDto.Add.class) ValidList<InfoTypeDto> infoTypeDtos) {
        infoTypeService.batchAdd(infoTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/资讯类型管理/删除资讯类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        infoTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param infoTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/资讯类型管理/更新资讯类型")
    @PostMapping("update")
    public ResponseBo<InfoTypeVo> update(@RequestBody @Validated(InfoTypeDto.Update.class) InfoTypeDto infoTypeDto) {
        return UpdateResponseBo.ok(infoTypeService.updateInfoType(infoTypeDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字(typeName类型名称)")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/资讯类型管理/查询资讯类型")
    @PostMapping("datas")
    public ResponseBo<InfoTypeVo> datas(@RequestBody SearchBean<InfoTypeDto> page) {
        List<InfoTypeVo> datas = infoTypeService.getInfoTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 上移下移
     *
     * @param infoTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/资讯类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<InfoTypeDto> infoTypeDtos) {
        infoTypeService.movingOrder(infoTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * 资讯类型下拉框
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "资讯类型下拉框", notes = "")
    @GetMapping("getInfoTypeList")
    public ResponseBo<BaseSelectEntity> getInfoTypeList() {
        List<BaseSelectEntity> infoTypeList = infoTypeService.getInfoTypeList();
        return new ListResponseBo<>(infoTypeList);
    }
}
