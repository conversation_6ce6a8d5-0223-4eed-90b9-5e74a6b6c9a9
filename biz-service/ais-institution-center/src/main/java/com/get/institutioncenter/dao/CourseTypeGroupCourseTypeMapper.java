package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.CourseTypeGroupCourseType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Description：课程类型组别-课程类型关系 Mapper
 * @Param
 * @return
 * @Date 11:44 2021/4/27
 * <AUTHOR>
 */
@Mapper
public interface CourseTypeGroupCourseTypeMapper extends BaseMapper<CourseTypeGroupCourseType> {

    int deleteByPrimaryKey(Long id);

    int insert(CourseTypeGroupCourseType record);

    int insertSelective(CourseTypeGroupCourseType record);

    /**
     * @Description：id获取对象
     * @Param
     * @Date 14:25 2021/5/10
     * <AUTHOR>
     */
    CourseTypeGroupCourseType selectById(Long id);

    int updateByPrimaryKeySelective(CourseTypeGroupCourseType record);

    int updateByPrimaryKey(CourseTypeGroupCourseType record);

    /**
     * @Description：根据类型组别查看是否存在
     * @Param
     * @Date 14:25 2021/5/10
     * <AUTHOR>
     */
    boolean isExistByCourseTypeGroup(@Param("fkCourseTypeGroupId") Long fkCourseTypeGroupId);

    /**
     * @Description：校验课程类型和课程类型分组是否有数据关联
     * @Param
     * @Date 14:25 2021/5/10
     * <AUTHOR>
     */
    boolean isExistByCourseTypeId(@Param("fkCourseTypeId") Long fkCourseTypeId);

    /**
     * @Description：查询名称
     * @Param
     * @Date 14:25 2021/5/10
     * <AUTHOR>
     */

    String getNamesByCourseTypeId(@Param("id") Long id);


}