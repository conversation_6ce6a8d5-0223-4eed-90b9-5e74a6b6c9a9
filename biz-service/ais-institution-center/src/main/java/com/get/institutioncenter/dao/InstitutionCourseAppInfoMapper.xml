<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.institutioncenter.dao.InstitutionCourseAppInfoMapper">

    <select id="selectByKeyAndVal" resultType="java.lang.Integer">
        SELECT
            count(*)
        FROM
            (
                SELECT
                    c.fk_table_id,
                    GROUP_CONCAT(
                        distinct
                        c.fk_table_name_type
                        ORDER BY
                            c.fk_table_name_type
                    ) AS fk,
                    GROUP_CONCAT(
                        c.fk_table_id_type
                        ORDER BY
                            c.fk_table_id_type
                    ) AS fk_id,
                    c.group_key
                FROM
                     r_institution_course_app_info c
                WHERE
                    c.fk_table_name = #{tableName}
                GROUP BY
                    c.fk_table_id
            ) x
        WHERE
            x.fk = #{key}
        AND x.fk_id = #{val}
        AND x.fk_table_id != #{tableId}
        AND x.group_key = #{effectiveDate}
    </select>
    <select id="getCourseInfo" resultType="com.get.institutioncenter.dto.WeScholarshipAppDto">
        SELECT
            f.id as facultyId,
            g.id as courseGroupTypeId,
            t1.id as courseTypeId,
            l.id as courseLevelId,
            c.id as courseId
        FROM
            m_institution_course c
        INNER JOIN m_institution m ON m.id = c.fk_institution_id
        INNER JOIN m_institution_faculty f ON f.fk_institution_id = m.id
        INNER JOIN r_institution_course_type ct ON ct.fk_institution_course_id = c.id
        INNER JOIN u_course_type t1 ON t1.id = ct.fk_course_type_id
        INNER JOIN r_course_type_group_course_type rg ON rg.fk_course_type_id = t1.id
        INNER JOIN u_course_type_group g ON g.id = rg.fk_course_type_group_id
        INNER JOIN r_institution_course_major_level cl ON cl.fk_institution_course_id = c.id
        INNER JOIN u_major_level l ON l.id = cl.fk_major_level_id
        WHERE
            m.id = #{institutionId}
        AND c.id = #{courseId}
        GROUP BY
            c.id
    </select>
    <select id="priorityMatchingQuery" resultType="com.get.institutioncenter.vo.CourseAppInfoPriorityVo">
        SELECT * FROM
        (
        SELECT
        x.id,
        GROUP_CONCAT(
        DISTINCT x.fkTableName
        ORDER BY
        x.fkTableName
        ) AS fk,
        GROUP_CONCAT(
        DISTINCT a.fk_table_name_type
        ORDER BY
        a.fk_table_name_type
        ) AS fc,
        COUNT(DISTINCT x.fkTableName) as priority
        FROM
        (
        <!-- 匹配国家-->
        <if test="weScholarshipAppDto.countryId!=null and weScholarshipAppDto.countryId!=''">

            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            ${fkTableName} s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name =  #{fkTableName}
            AND a.fk_table_name_type = 'u_area_country'
            AND a.fk_table_id_type =#{weScholarshipAppDto.countryId}
        </if>
        <!-- 匹配学校-->
        <if test="weScholarshipAppDto.institutionId!=null and weScholarshipAppDto.institutionId!=''">
            UNION ALL
            SELECT
            s.*, a.
            fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            ${fkTableName} s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name = #{fkTableName}
            AND a.fk_table_name_type = 'm_institution'
            AND a.fk_table_id_type =#{weScholarshipAppDto.institutionId}
        </if>
        <if test="weScholarshipAppDto.facultyId!=null and weScholarshipAppDto.facultyId!=''">
            UNION ALL
            SELECT
            s.*, a.
            fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            ${fkTableName} s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name = #{fkTableName}
            AND a.fk_table_name_type = 'm_institution_faculty'
            AND a.fk_table_id_type =#{weScholarshipAppDto.facultyId}
        </if>
        <if test="weScholarshipAppDto.courseGroupId!=null and weScholarshipAppDto.courseGroupId!=''">
            UNION ALL
            SELECT
            s.*, a.
            fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            ${fkTableName} s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name = #{fkTableName}
            AND a.fk_table_name_type = 'u_course_type_group'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseGroupId}
        </if>
        <if test="weScholarshipAppDto.courseTypeId!=null and weScholarshipAppDto.courseTypeId!=''">
            UNION ALL
            SELECT
            s.*, a.
            fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            ${fkTableName} s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name = #{fkTableName}
            AND a.fk_table_name_type = 'u_course_type'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseTypeId}
        </if>
        <if test="weScholarshipAppDto.courseLevelId!=null and weScholarshipAppDto.courseLevelId!=''">
            UNION ALL
            SELECT
            s.*, a.
            fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            ${fkTableName} s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name = #{fkTableName}
            AND a.fk_table_name_type = 'u_major_level'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseLevelId}
        </if>
        <!-- 匹配课程-->
        <if test="weScholarshipAppDto.courseId!=null and weScholarshipAppDto.courseId!=''">
            UNION ALL
            SELECT
            s.*, a.fk_table_name_type AS fkTableName,
            a.fk_table_id_type AS fkTableId
            FROM
            ${fkTableName} s
            INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
            WHERE
            a.fk_table_name = #{fkTableName}
            AND a.fk_table_name_type = 'm_institution_course'
            AND a.fk_table_id_type =#{weScholarshipAppDto.courseId}
        </if>
        ) x
        INNER JOIN r_institution_course_app_info a ON x.id = a.fk_table_id
        GROUP BY
        x.id
        ) f
        WHERE
        <foreach collection="priorityTypeKey.entrySet()" separator="OR" open="(" close=")" item="val">
            f.fk = #{val}
        </foreach>
        ORDER BY f.priority desc
    </select>
    <select id="getOtherInfo" resultType="com.get.institutioncenter.dto.WeScholarshipAppDto">
        SELECT
            m.fk_area_country_id as countryId,
            f.id facultyId,
            g.id courseGroupId,
            t.id courseTypeId,
            l.fk_major_level_id courseLevelId,
            c.id as courseId,
            m.id as institutionId
        FROM
            m_institution_course c
        INNER JOIN m_institution m ON m.id = c.fk_institution_id
        LEFT JOIN m_institution_faculty f ON f.fk_institution_id = m.id
        INNER JOIN r_institution_course_type rt ON rt.fk_institution_course_id = c.id
        INNER JOIN u_course_type t ON t.id = rt.fk_course_type_id
        INNER JOIN r_course_type_group_course_type rg ON rg.fk_course_type_id = t.id
        INNER JOIN u_course_type_group g ON g.id = rg.fk_course_type_group_id
        INNER JOIN r_institution_course_major_level l ON l.fk_institution_course_id = c.id
        WHERE	c.id = #{courseId}
    </select>
    <select id="priorityMatchingQueryTwo" resultType="com.get.institutioncenter.vo.CourseAppInfoPriorityVo">
        SELECT DISTINCT
            x.id,
            x.group_key as effectiveDate,
            a.fk_table_name_type as fkTableType,
            a.fk_table_id_type as fkTypeId,
            a.gmt_create as gmtPriorityTime
        FROM
            (
                <!-- 匹配国家
                <if test="weScholarshipAppDto.countryId!=null and weScholarshipAppDto.countryId!=''">
                    SELECT
                    s.*, a.fk_table_name_type AS fkTableName,a.group_key,
                    a.fk_table_id_type AS fkTableId
                    FROM
                    ${fkTableName} s
                    INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                    WHERE
                    a.fk_table_name =  #{fkTableName}
                    AND a.fk_table_name_type = 'u_area_country'
                    AND a.fk_table_id_type =#{weScholarshipAppDto.countryId}
                </if>
                UNION ALL
                -->
                <!-- 匹配学校-->
                <if test="weScholarshipAppDto.institutionId!=null and weScholarshipAppDto.institutionId!=''">
                    SELECT
                    s.*, a.
                    fk_table_name_type AS fkTableName,a.group_key,
                    a.fk_table_id_type AS fkTableId
                    FROM
                    ${fkTableName} s
                    INNER JOIN r_institution_course_app_info a ON s.id = a.fk_table_id
                    WHERE
                    a.fk_table_name = #{fkTableName}
                    AND a.fk_table_name_type = 'm_institution'
                    AND a.fk_table_id_type =#{weScholarshipAppDto.institutionId}
                </if>
            ) x
        INNER JOIN r_institution_course_app_info a ON x.id = a.fk_table_id
        AND a.fk_table_name = #{fkTableName}
        ORDER BY
	        x.id
    </select>
    <select id="getCourseOtherInfo" resultType="com.get.institutioncenter.vo.CourseOtherInfoVo">
        SELECT
            c.num as courseNum,
            c.name as courseName,
            m.name as institutionName,
            m.name_chn as institutionChnName,
            f.name as facultyName,
            f.name_chn as facultyChnName,
            GROUP_CONCAT(DISTINCT t.type_name) AS courseTypeName,
            GROUP_CONCAT(DISTINCT t.type_name_chn) AS courseTypeChnName,
            GROUP_CONCAT(DISTINCT ml.level_name) AS courseLevelName,
            GROUP_CONCAT(DISTINCT ml.level_name_chn) AS courseLevelChnName
        FROM
            m_institution_course c
        INNER JOIN m_institution m ON m.id = c.fk_institution_id
        LEFT JOIN m_institution_faculty f ON f.fk_institution_id = m.id
        LEFT JOIN r_institution_course_type rt ON rt.fk_institution_course_id = c.id
        LEFT JOIN u_course_type t ON t.id = rt.fk_course_type_id
        LEFT JOIN r_course_type_group_course_type rg ON rg.fk_course_type_id = t.id
        LEFT JOIN u_course_type_group g ON g.id = rg.fk_course_type_group_id
        LEFT JOIN r_institution_course_major_level l ON l.fk_institution_course_id = c.id
        LEFT JOIN u_major_level ml ON ml.id = l.fk_major_level_id
        LEFT JOIN r_institution_course_faculty as ricf ON ricf.fk_institution_course_id = c.id
        LEFT JOIN r_institution_course_type AS rict ON rict.fk_institution_course_id = c.id
        LEFT JOIN r_course_type_group_course_type AS rctgct ON rctgct.fk_course_type_id = rict.fk_course_type_id
        WHERE
            1=1
            <if test="courseIds!=null and courseIds.size>0">
                AND c.id IN
                <foreach collection="courseIds" item="courseId" open="(" separator="," close=")">
                    #{courseId}
                </foreach>
            </if>
            <if test="notInCourseIds!=null and notInCourseIds.size>0">
                AND c.id NOT IN
                <foreach collection="notInCourseIds" item="notInCourseId" open="(" separator="," close=")">
                    #{notInCourseId}
                </foreach>
            </if>

            <if test="institutionIds!=null and institutionIds.size>0">
                AND m.id IN
                <foreach collection="institutionIds" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
            <if test="majorLevelIds!=null and majorLevelIds.size>0">
                AND ml.id IN
                <foreach collection="majorLevelIds" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
            <if test="institutionFacultyIds!=null and institutionFacultyIds.size>0">
                AND ricf.fk_institution_faculty_id IN
                <foreach collection="institutionFacultyIds" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
            <if test="courseTypeIds!=null and courseTypeIds.size>0">
                AND rict.fk_course_type_id IN
                <foreach collection="courseTypeIds" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
            <if test="courseTypeGroupIds!=null and courseTypeGroupIds.size>0">
                AND rctgct.fk_course_type_group_id IN
                <foreach collection="courseTypeGroupIds" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
        GROUP BY c.id
    </select>
    <select id="getAppInfoMappingInfo" resultType="com.get.institutioncenter.vo.AppCourseMappingInfoVo">
        SELECT
            fk_table_name_type as typeKey,
            GROUP_CONCAT(fk_table_id_type) as typeKeyIdStr,
            GROUP_CONCAT(DISTINCT group_key) as groupKeyStr
        FROM
            r_institution_course_app_info
        WHERE
            fk_table_id = #{fkTableId}
            AND fk_table_name = #{fkTableName}
        GROUP BY
            fk_table_name_type
    </select>
</mapper>
