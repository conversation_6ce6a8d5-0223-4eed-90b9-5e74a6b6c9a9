package com.get.institutioncenter.utils;

import com.alibaba.nacos.common.utils.Objects;
import com.get.core.mybatis.base.BaseSelectEntity;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 学校类型工具类（包含K12类型和住宿类型处理）
 */
public class InstitutionTypeUtil {
    // K12类型
    private static final Map<Long, String> K12_TYPE_MAP = Collections.unmodifiableMap(new HashMap<Long, String>() {{
        put(0L, "幼儿园");
        put(1L, "小学");
        put(2L, "初中");
        put(3L, "高中");
    }});

    // 住宿类型
    private static final Map<Long, String> ACCOMMODATION_TYPE_MAP = Collections.unmodifiableMap(new HashMap<Long, String>() {{
        put(1L, "走读");
        put(2L, "寄宿");
        put(3L, "混合");
    }});

    /**
     * 通用规范化方法(中文逗号转英文逗号)
     */
    public static final String normalized(String keys){
        // 将中文逗号替换为英文逗号
        String normalizedKeys = keys.replaceAll("，", ",");
        return normalizedKeys;
    }

//    /**
//     * 获取完整的K12类型列表
//     */
//    public static List<BaseSelectEntity> getK12TypeList() {
//        return K12_TYPE_MAP.entrySet().stream()
//                .map(entry -> {
//                    BaseSelectEntity entity = new BaseSelectEntity();
//                    entity.setId(entry.getKey());
//                    entity.setName(entry.getValue());
//                    return entity;
//                })
//                .collect(Collectors.toList());
//    }
//    /**
//     * 根据逗号分隔的键字符串获取拼接后的值（自动兼容中英文逗号）
//     * @param keys 如 "0，1,3"（混合中英文逗号）
//     * @return 拼接结果如 "幼儿园,小学,高中"
//     */
//    public static String getK12TypeNames(String keys) {
//        if (keys == null || keys.isEmpty()) {
//            return "";
//        }
//        String normalizedKeys = normalized(keys);
//        return Arrays.stream(normalizedKeys.split(","))
//                .map(String::trim)
//                .filter(s -> !s.isEmpty())
//                .map(s -> {
//                    try {
//                        return Long.parseLong(s);
//                    } catch (NumberFormatException e) {
//                        return null;
//                    }
//                })
//                .filter(Objects::nonNull)
//                .map(K12_TYPE_MAP::get)
//                .filter(Objects::nonNull)
//                .collect(Collectors.joining(","));
//    }
    /*
     * 住宿类型方法（新增实现）
     */
    public static List<BaseSelectEntity> getK12TypeList() {
        return convertMapToList(K12_TYPE_MAP);
    }

    public static String getK12TypeNames(String keys) {
        return convertKeysToNames(keys, K12_TYPE_MAP);
    }

    /*
     * 住宿类型方法（新增实现）
    */
    public static List<BaseSelectEntity> getAccommodationTypeList() {
        return convertMapToList(ACCOMMODATION_TYPE_MAP);
    }

    public static String getAccommodationTypeNames(String keys) {
        return convertKeysToNames(keys, ACCOMMODATION_TYPE_MAP);
    }


    private static List<BaseSelectEntity> convertMapToList(Map<Long, String> typeMap) {
        return typeMap.entrySet().stream()
                .map(entry -> {
                    BaseSelectEntity entity = new BaseSelectEntity();
                    entity.setId(entry.getKey());
                    entity.setName(entry.getValue());
                    return entity;
                })
                .collect(Collectors.toList());
    }

    private static String convertKeysToNames(String keys, Map<Long, String> typeMap) {
        if (keys == null || keys.isEmpty()) {
            return "";
        }
        String normalizedKeys = normalized(keys);
        return Arrays.stream(normalizedKeys.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(s -> {
                    try {
                        return Long.parseLong(s);
                    } catch (NumberFormatException e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .map(typeMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.joining(","));
    }


}