package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.entity.ContractFormulaAreaCountryStudentExclude;

/**
 * <AUTHOR>
 * @DATE: 2022/3/2
 * @TIME: 15:56
 * @Description:
 **/
public interface IContractFormulaAreaCountryStudentExcludeService extends BaseService<ContractFormulaAreaCountryStudentExclude> {
    /**
     * @return void
     * @Description :根据contractFormulaId删除
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);
}
