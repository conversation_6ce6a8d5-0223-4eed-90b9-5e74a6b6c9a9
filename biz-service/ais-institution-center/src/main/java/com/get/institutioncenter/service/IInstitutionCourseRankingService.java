package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.InstitutionCourseRankingVo;
import com.get.institutioncenter.entity.InstitutionCourseRanking;
import com.get.institutioncenter.dto.InstitutionCourseRankingDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/18
 * @TIME: 17:55
 * @Description:
 **/
public interface IInstitutionCourseRankingService extends BaseService<InstitutionCourseRanking> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionCourseRankingVo findInstitutionCourseRankingById(Long id);

    /**
     * 列表数据
     *
     * @param institutionCourseRankingDto
     * @param page
     * @return
     */
    List<InstitutionCourseRankingVo> getInstitutionCourseRankings(InstitutionCourseRankingDto institutionCourseRankingDto, Page page);

    /**
     * 修改
     *
     * @param institutionCourseRankingDto
     * @return
     */
    InstitutionCourseRankingVo updateInstitutionCourseRanking(InstitutionCourseRankingDto institutionCourseRankingDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param institutionCourseRankingDtos
     * @return
     */
    void batchAdd(List<InstitutionCourseRankingDto> institutionCourseRankingDtos);

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findType();

    /**
     * 删除记录
     *
     * @return
     */
    void deleteCourseRankingByCourseId(Long id);
}
