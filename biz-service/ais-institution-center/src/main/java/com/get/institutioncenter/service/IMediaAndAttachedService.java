package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.filecenter.dto.FileDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.MediaAndAttached;
import com.get.institutioncenter.dto.MediaAndAttachedQueryDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/8/6
 * @TIME: 9:51
 * @Description:
 **/
public interface IMediaAndAttachedService extends BaseService<MediaAndAttached> {

    /**
     * 上传文件
     *
     * @param multipartFiles
     * @return
     * @
     */
    List<FileDto> upload(MultipartFile[] multipartFiles);

    /**
     * 上传附件
     *
     * @param multipartFiles
     * @return
     * @
     */
    List<FileDto> uploadAttached(MultipartFile[] multipartFiles);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void deleteMediaAttached(Long id);

    /**
     * 上移下移
     *
     * @param mediaAttachedVos
     * @return
     */
    void movingOrder(List<MediaAndAttachedDto> mediaAttachedVos);

    /**
     * 保存附件
     *
     * @param mediaAttachedVo
     * @return
     */
    MediaAndAttachedVo addMediaAndAttached(MediaAndAttachedDto mediaAttachedVo);

    /**
     * 通过附件GUID获取DTO
     *
     * @param attachedVo
     * @return
     * @
     */
    List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo);

    Map<Long, MediaAndAttachedVo> getMediaAndAttachedDtos(MediaAndAttachedQueryDto queryVo);

    /**
     * 通过附件GUID分页获取DTO
     *
     * @param attachedVo
     * @return
     * @
     */
    List<MediaAndAttachedVo> getMediaAndAttachedDto(MediaAndAttachedDto attachedVo, Page page);

    /**
     * 根据媒体附件id修改表id
     *
     * @param id
     * @param tableId
     */
    void updateTableId(Long id, Long tableId);

    /**
     * @return java.util.List<com.get.institutioncenter.entity.MediaAndAttached>
     * @Description: 根据表id查找对应媒体附件集合
     * @Param [tableId, tableName]
     */
    List<MediaAndAttached> findMediaAndAttachedByTableId(Long tableId, String tableName, String key);

    /**
     * 根据表id删除对应媒体附件
     *
     * @param tableId
     * @return
     */
    void deleteMediaAndAttachedByTableId(Long tableId, String tableName);


    /**
     * 获取学校附件类型
     *
     * @return
     */
    List<Map<String, Object>> findMediaAndAttachedType();


    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 获取学校提供商附件类型
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> getProviderMediaType();

    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 获取合同附件类型
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> getContractMediaType();

    Map<Long,List<MediaAndAttachedVo>> getMediaAndAttachedDtoByTableIds(Set<Long> tableIds, String tableName);

}
