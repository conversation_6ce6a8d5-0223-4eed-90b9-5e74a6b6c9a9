package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.TranslationDto;
import com.get.institutioncenter.vo.TranslationMappingVo;
import com.get.institutioncenter.service.ITranslationMappingService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/16
 * @TIME: 17:55
 * @Description:
 **/

@Api(tags = "语言翻译管理")
@RestController
@RequestMapping("/institution/translationMapping")
public class TranslationMappingController {
    @Resource
    private ITranslationMappingService translationMappingService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/语言翻译管理/查询翻译")
    @PostMapping("getTranslationMappingDtos")
    public ResponseBo<TranslationMappingVo> getTranslationMappingDtos(@RequestBody TranslationDto translationDto) {
        List<TranslationMappingVo> datas = translationMappingService.getTranslationMappingDtos(translationDto);
        return new ListResponseBo<>(datas);
    }
    /**
     * 保存翻译配置
     *
     * @param translationDtos
     * @return
     * @
     */

    @ApiOperation(value = "保存翻译接口")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/翻译管理/保存翻译")
    @PostMapping("updateTranslations")
    public ResponseBo updateTranslations(@RequestBody @Validated(TranslationDto.Add.class) List<TranslationDto> translationDtos) {
        translationMappingService.updateTranslations(translationDtos);
        return ResponseBo.ok();
    }

    /**
     * 峰会附件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "语言类型下拉框数据", notes = "")
    @PostMapping("findLanguageType")
    public ResponseBo findLanguageType() {
        List<Map<String, Object>> datas = translationMappingService.findLanguageType();
        return new ListResponseBo<>(datas);
    }
}
