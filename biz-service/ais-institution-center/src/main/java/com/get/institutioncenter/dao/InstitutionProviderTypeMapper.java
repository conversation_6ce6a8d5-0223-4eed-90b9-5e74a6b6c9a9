package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.entity.InstitutionProviderType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/12 11:23
 * @verison: 1.0
 * @description: 学校提供商类型管理mapper
 */
@Mapper
public interface InstitutionProviderTypeMapper extends BaseMapper<InstitutionProviderType> {

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(InstitutionProviderType record);

    /**
     * 根据id查找名称
     *
     * @param id
     * @return
     */
    String getNameById(Long id);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionProviderTypeVo>
     * @Description :学校提供商类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getinstitutionProviderTypeList();
}