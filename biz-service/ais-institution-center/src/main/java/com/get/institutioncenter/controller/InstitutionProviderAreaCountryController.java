package com.get.institutioncenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.InstitutionProviderAreaCountryVo;
import com.get.institutioncenter.service.IInstitutionProviderAreaCountryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: Sea
 * @create: 2021/3/29 10:15
 * @verison: 1.0
 * @description:
 */
@Api(tags = "学校提供商和业务国家关联表管理")
@RestController
@RequestMapping("/institution/institutionProviderAreaCountry")
public class InstitutionProviderAreaCountryController {
    @Resource
    private IInstitutionProviderAreaCountryService institutionProviderAreaCountryService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionProviderAreaCountryVo>
     * @Description :公式国家区域下拉框
     * @Param [institutionProviderId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "公式国家区域下拉框", notes = "")
    @GetMapping("getInstitutionProviderAreaCountrySelect")
    public ResponseBo<InstitutionProviderAreaCountryVo> getInstitutionProviderAreaCountrySelect(@RequestParam Long institutionProviderId) {
        return new ListResponseBo<>(institutionProviderAreaCountryService.getInstitutionProviderAreaCountrySelect(institutionProviderId));
    }
}
