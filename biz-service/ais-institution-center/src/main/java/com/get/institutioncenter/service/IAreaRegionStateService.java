package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.entity.AreaRegionState;
import com.get.institutioncenter.dto.AreaRegionDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 业务区域管理逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/7/28 14:44
 */
public interface IAreaRegionStateService extends BaseService<AreaRegionState> {

    /**
     * 树状图
     *
     * @Date 14:49 2021/7/28
     * <AUTHOR>
     */
    List<AreaCountryVo> getTreeList();

    /**
     * 业务区域列表
     *
     * @Date 15:05 2021/7/28
     * <AUTHOR>
     */
    List<AreaRegionVo> getAreaRegionStates(AreaRegionDto areaRegionDto, Page page);

    void saveBatch(List<AreaRegionState> list);

    /**
     * 批量新增
     *
     * @param areaRegionDtos
     * @
     */
    void batchAdd(ValidList<AreaRegionDto> areaRegionDtos);

    /**
     * 删除
     *
     * @param id
     * @
     */
    void delete(Long id);


    /**
     * 修改
     *
     * @param areaRegionDto
     * @
     */
    void updateAreaState(AreaRegionDto areaRegionDto);


    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    AreaRegionVo findAreaRegionById(Long id);

    /**
     * 获取当前国家的业务区域
     *
     * @param fkCountryId
     * @return
     * @
     */
    List<BaseSelectEntity> areaRegionSelectByCountryId(Long fkCountryId);

    /**
     * feign调用,根据ids获取对象集合
     *
     * @param ids
     * @return
     * @
     */
    Map<Long, AreaRegionVo> getAreaRegionDtoByIds(Set<Long> ids);


    /**
     * @Description: 上移下移
     * @Author: Jerry
     * @Date:9:57 2021/10/9
     */
    void movingOrder(List<AreaRegionDto> areaRegionDtos);
}
