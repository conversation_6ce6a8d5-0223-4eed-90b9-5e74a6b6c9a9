<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionDeadlineInfoMapper2">

    <select id="getWcInstitutionDeadlineInfoList" resultType="com.get.institutioncenter.vo.InstitutionDeadlineInfoVo2">
        select mis.id,mis.fk_institution_id,GROUP_CONCAT(mis.public_level)as public_level from
        m_institution_deadline_info2 mis
        left join m_institution mi on mi.id = mis.fk_institution_id
        left join r_institution_view_order vo on vo.fk_institution_id=mis.fk_institution_id
        where mis.is_active=1 and mi.fk_area_country_id=#{fkCountryId} and vo.type=0
        <if test="schoolName !=''">
            and(
            mi.name like concat('%',#{schoolName},'%')
            or mi.name_chn like concat('%',#{schoolName},'%')
            or mi.short_name like concat('%',#{schoolName},'%')
            or mi.short_name_chn like concat('%',#{schoolName},'%')
            or CONCAT(mi.name,'（',mi.name_chn,'）')like concat('%',#{schoolName},'%'))
        </if>
        group by mis.fk_institution_id order by vo.view_order asc
    </select>
    <select id="getNewCreateTime" resultType="java.util.Date">
        select gmt_create from m_institution_deadline_info2 order by gmt_create desc limit 0,1
    </select>
</mapper>