package com.get.institutioncenter.service;


import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.ContractFormulaAreaCountryDto;
import com.get.institutioncenter.entity.ContractFormulaAreaCountry;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/8 10:16
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaAreaCountryService extends BaseService<ContractFormulaAreaCountry> {
    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaAreaCountryDto]
     * <AUTHOR>
     */
    Long addContractFormulaAreaCountry(ContractFormulaAreaCountryDto contractFormulaAreaCountryDto);

    /**
     * @return void
     * @Description :根据contractFormulaId删除
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应国家ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCountryIdListByFkid(Long contractFormulaId);

    /**
     * @return java.lang.String
     * @Description :通过合同公式id 查找对应国家名称
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    String getCountryNameByFkid(Long contractFormulaId);

    List<Long> getCountryIdNotInListByFkid(Long contractFormulaId);
}
