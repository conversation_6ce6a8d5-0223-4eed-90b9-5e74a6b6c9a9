package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaPreMajorLevel;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ContractFormulaPreMajorLevelMapper extends BaseMapper<ContractFormulaPreMajorLevel> {
    int insert(ContractFormulaPreMajorLevel record);

    int insertSelective(ContractFormulaPreMajorLevel record);

    int updateByPrimaryKeySelective(ContractFormulaPreMajorLevel record);

    int updateByPrimaryKey(ContractFormulaPreMajorLevel record);

    /**
     * @return list
     * @Description :查询级别id
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getMajorLevelIdListByFkId(Long contractFormulaId);

}