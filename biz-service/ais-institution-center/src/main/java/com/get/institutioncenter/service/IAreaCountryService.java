package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.AreaCountryHtiVo;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.vo.NewsVo;
import com.get.institutioncenter.entity.AreaCountry;
import com.get.institutioncenter.dto.AreaCountryDto;
import com.get.institutioncenter.dto.NewsDto;
import com.get.institutioncenter.dto.query.AreaCountryQueryDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/17 11:25
 * @verison: 1.0
 * @description: 区域管理-国家配置接口
 */
public interface IAreaCountryService {

    /**
     * 详情
     *
     * @param id
     * @return
     */
    AreaCountryVo findAreaCountryById(Long id);

    /**
     * 新增
     *
     * @param areaCountryDto
     * @return
     */
    Long addAreaCountry(AreaCountryDto areaCountryDto);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param areaCountryDto
     * @return
     */
    AreaCountryVo updateAreaCountry(AreaCountryDto areaCountryDto);


    /**
     * 列表
     *
     * @param areaCountryVo
     * @param page
     * @return
     */
    List<AreaCountryVo> getAreaCountrys(AreaCountryQueryDto areaCountryVo, Page page);


    /**
     * 列表数据
     *
     * @param keyWord
     * @return
     * @
     */
    List<AreaCountryVo> getAreaCountrys(String keyWord);


    /**
     * feign调用 根据国家编号查找国家名称
     *
     * @param countryKey
     * @return
     */
    String getCountryNameByKey(String countryKey);


    String getCountryNameEnByKey(String countryKey);


    /**
     * 当前登录人国家下拉框
     *
     * @return
     */
    List<BaseSelectEntity> getAreaCountryList();

    /**
     * HTI国家下拉框
     *
     * @Date 11:17 2023/12/14
     * <AUTHOR>
     */
    List<AreaCountryHtiVo> getAreaCountryListHti();


    /**
     * 国家下拉框
     * @param keyword
     * @return
     */
    List<BaseSelectEntity> getAreaCountryListByKeyword(String keyword);

    /**
     * 全部国家名称下拉框
     *
     * @return
     */
    List<AreaCountryVo> getAllAreaCountryList();

    /**
     * 保存文件   String tableName = "u_area_country"
     *
     * @param mediaAttachedVos
     * @return
     * @
     */
    List<MediaAndAttachedVo> addInstitutionFacultyMedias(List<MediaAndAttachedDto> mediaAttachedVos);

    /**
     * 获取国家附件类型
     *
     * @return
     */
    List<Map<String, Object>> findMediaAndAttachedType();

    /**
     * feign调用 根据key查找id
     *
     * @param keys
     * @return
     * @
     */
    List<Long> getCountryIdByKey(List<String> keys);

    /**
     * 国家详情下新增新闻
     *
     * @param newsDto
     * @return
     * @
     */
    Long addNews(NewsDto newsDto);

    /**
     * 上移下移
     *
     * @param areaCountryDtos
     */
    void movingOrder(List<AreaCountryDto> areaCountryDtos);

    /**
     * 国家详情下的新闻列表
     *
     * @param newsVo
     * @param page
     * @return
     * @
     */
    List<NewsVo> getNewsList(NewsQueryDto newsVo, Page page);

    /**
     * @return java.lang.String
     * @Description :根据国家id查找国家名称
     * @Param [id]
     * <AUTHOR>
     */
    String getCountryNameById(Long id);

    /**
     * @return java.lang.String
     * @Description :根据国家id查找国家key
     * @Param [id]
     * <AUTHOR>
     */
    String getCountryKeyById(Long id);

    /**
     * @Description :根据国家ids查找国家名称map
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getCountryNamesByIds(Set<Long> ids);

    /**
     * Author Cream
     * Description : // 根据国家获取货币编号
     * Date 2022/8/11 12:24
     * Params:
     * Return
     */
    String getCurrencyNumByCountryName(String countryName);

    /**
     * @return java.util.List<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description: 附件列表
     * @Param [data, page]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto data, Page page);

    /**
     * @return java.lang.Long
     * @Description: 新增附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVo);

    /**
     * @return java.lang.String
     * @Description :根据国家id查找国家名称
     * @Param [id]
     * <AUTHOR>
     */
    String getCountryChnNameById(Long id);

    /**
     * feign调用 通过国家ids 查找对应的国家名称
     *
     * @param ids
     * @return
     */
    Map<Long, String> getCountryChnNameByIds(Set<Long> ids);

    Set<Long> getAllCountryId();

    /**
     * feign调用 查询全部 公开首页国家id
     *
     * @Date 17:00 2021/11/16
     * <AUTHOR>
     */
    List<Map<String, String>> selectPublicCountryHomeNums();

    /**
     * 根据国家id获取国家名和国家编号
     *
     * @Date 12:13 2022/1/18
     * <AUTHOR>
     */
    String getCountryNameAndNumById(Long id);

    /**
     * 根据国家keys获取国家列表
     *
     * @param keys
     * @return
     */
    List<AreaCountryVo> getCountryByKey(List<String> keys);

    /**
     * 根据国家名称模糊查询国家Id
     * <AUTHOR>
     * @DateTime 2022/11/24 14:43
     */
    List<Long> getCountryByName(String name);

    List<AreaCountryVo> getAreaCode();
    
    

    /**
     * 根据国家ids 获取国家编号
     *
     * @Date 16:31 2022/3/5
     * <AUTHOR>
     */
    Map<Long, String> getCountryNumByCountryIds(Set<Long> countryIdIdSet);


    Map<Long, String> getCountryFullNamesByIds(Set<Long> ids);

    Map<String, String> getCityChnNameById(Set<String> cNums);

    /**
     * 获取对应公司下有申请计划的 业务国家下拉框数据
     *
     * @Date 12:27 2023/1/5
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsOfferItemAreaCountryList(Long companyId);

    /**
     * 获取对应公司下有申请计划的代理所在的 国家下拉框数据
     *
     * @Date 15:22 2023/1/5
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsAgentOfferItemAreaCountryList(Long companyId);

    /**
     * 获取对应公司下的代理所在的 国家下拉框数据
     *
     * @Date 10:40 2023/3/15
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsAgentAreaCountryList(Long companyId);

    /**
     * 获取新闻国家下拉数据
     * <AUTHOR>
     * @DateTime 2023/7/11 14:10
     */
    List<BaseSelectEntity> getNewsAreaCountryList();

    /**
     * 国家下拉
     * @param publicLevel
     * @return
     */
    List<AreaCountryVo> getCountryByPublicLevel(Integer publicLevel);

    /**
     * 根据国家ids 获取国家对象Map
     *
     * @Date 17:44 2023/12/18
     * <AUTHOR>
     */
    Map<Long, AreaCountryVo> getCountryDtoMapByIds(Set<Long> countryIds);

    AreaCountry getCountryById(Long id);

    /**
     * 根据国家key集合获取对应的id
     *
     * @param keys 国家keys
     * @return
     */
    Map<String, Long> getCountryIdByKeys(Set<String> keys);

    List<AreaCountryVo> getCommonAreaCodes();

    /**
     * 获取国家名称map
     * @return
     */
    Map<Long, String> getCountryNameMap();

    List<AreaRegionVo> getGeographicalRegions();

    /**
     * 峰会表单常用国家下拉框
     * @param publicLevel
     * @return
     */
    List<AreaCountryVo> getCountryByPublicLevelBySummit(Integer publicLevel);
}
