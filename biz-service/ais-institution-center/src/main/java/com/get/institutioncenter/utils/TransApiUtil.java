package com.get.institutioncenter.utils;

import com.get.core.tool.utils.GeneralTool;
import org.springframework.stereotype.Component;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/2/5 18:57
 */
@Component
public class TransApiUtil {
    private static final String TRANS_API_HOST = "http://api.fanyi.baidu.com/api/trans/vip/translate";
    private final static String APPID = "20240415002024562";
    private final static String SECURITY_KEY = "XJ8MbndBXZ9HKVHO6WJH";


    public String getTransResult(String query, String from, String to,String appid, String securityKey) throws UnsupportedEncodingException {
        Map<String, String> params = this.buildParams(query, from, to, appid, securityKey);
        return HttpGet.get("http://api.fanyi.baidu.com/api/trans/vip/translate", params);
    }

    private Map<String, String> buildParams(String query, String from, String to, String appid, String securityKey) throws UnsupportedEncodingException {
        appid = GeneralTool.isNotEmpty(appid)? appid : APPID;
        securityKey = GeneralTool.isNotEmpty(securityKey)? securityKey : SECURITY_KEY;
        Map<String, String> params = new HashMap();
        params.put("q", query);
        params.put("from", from);
        params.put("to", to);
        params.put("appid", appid);
        String salt = String.valueOf(System.currentTimeMillis());
        params.put("salt", salt);
        String src = appid + query + salt + securityKey;
        params.put("sign", MD5.md5(src));
        return params;
    }

}
