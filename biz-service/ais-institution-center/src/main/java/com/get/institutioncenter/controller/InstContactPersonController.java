package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.ContactPersonDto;
import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.vo.ContactPersonVo;
import com.get.institutioncenter.service.IContactPersonService;
import com.get.institutioncenter.dto.ContactPersonCompanyDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/8/17
 * @TIME: 11:46
 * @Description: 联系人管理
 **/

@Api(tags = "联系人管理")
@RestController
@RequestMapping("institution/contactPerson")
public class InstContactPersonController {
    @Autowired
    private IContactPersonService personService;

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/联系人管理/详情")
    @GetMapping("/{id}")
    public ResponseBo detail(@PathVariable("id") Long id) {
        ContactPersonVo contactPersonVo = personService.finContactPersonById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", contactPersonVo);
        return responseBo;
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/联系人管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        personService.deleteContactPerson(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.ContactPersonDto>
     * @Description: 修改信息
     * @Param [contactPersonDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改代理联系人", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/联系人管理/修改代理联系人")
    @PostMapping("updateContactPerson")
    public ResponseBo<ContactPersonVo> updateContactPerson(@RequestBody @Validated(ContactPersonDto.Update.class)  ContactPersonDto contactPersonDto) {
        return UpdateResponseBo.ok(personService.updateContactPerson(contactPersonDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新增提供商联系人
     * @Param [contactPersonDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增联系人")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/联系人管理/新增联系人")
    @PostMapping("addContactPerson")
    public ResponseBo addContactPerson(@RequestBody @Validated(ContactPersonDto.Add.class) ContactPersonDto contactPersonDto) {
        return SaveResponseBo.ok(personService.addContactPerson(contactPersonDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.ContactPersonDto>
     * @Description: 联系人列表
     * @Param [searchBean]
     * <AUTHOR>
     **/
    @ApiOperation(value = "联系人列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/联系人管理/联系人列表")
    @PostMapping("datas")
    public ResponseBo<ContactPersonVo> datas(@RequestBody SearchBean<ContactPersonDto> searchBean) {
        List<ContactPersonVo> contactPersonVos = personService.getContactPersonDtos(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(contactPersonVos, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 联系人安全配置
     * @Param [validList]
     * <AUTHOR>
     */
    @ApiOperation(value = "联系人-公司安全配置")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/联系人管理/安全配置")
    @PostMapping("editCompanyRelation")
    public ResponseBo editCompanyRelation(@RequestBody @Validated(ContactPersonCompanyDto.Add.class) ValidList<ContactPersonCompanyDto> validList) {
        personService.editContactPersonCompany(validList);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 回显联系人和公司的关系
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "回显联系人和公司的关系", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/联系人管理/联系人和公司的关系（数据回显）")
    @GetMapping("getContactRelation/{contactId}")
    public ResponseBo<CompanyTreeVo> getContractCompanyRelation(@PathVariable("contactId") Long id) {
        List<CompanyTreeVo> contractCompanyRelation = personService.getContactRelation(id);
        return new ListResponseBo<>(contractCompanyRelation);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 目标类型下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "目标类型下拉", notes = "")
    @GetMapping("findTargetType")
    public ResponseBo findContractMediaType() {
        List<Map<String, Object>> datas = personService.findTargetType();
        return new ListResponseBo<>(datas);
    }
}
