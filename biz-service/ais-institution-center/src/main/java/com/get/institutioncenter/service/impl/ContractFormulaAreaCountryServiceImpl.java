package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaAreaCountryMapper;
import com.get.institutioncenter.dao.ContractFormulaAreaCountryStudentExcludeMapper;
import com.get.institutioncenter.entity.ContractFormulaAreaCountry;
import com.get.institutioncenter.service.IAreaCountryService;
import com.get.institutioncenter.service.IContractFormulaAreaCountryService;
import com.get.institutioncenter.dto.ContractFormulaAreaCountryDto;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/8 10:16
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaAreaCountryServiceImpl extends BaseServiceImpl<ContractFormulaAreaCountryMapper, ContractFormulaAreaCountry> implements IContractFormulaAreaCountryService {
    @Resource
    private ContractFormulaAreaCountryMapper contractFormulaAreaCountryMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IAreaCountryService areaCountryService;
    @Resource
    private ContractFormulaAreaCountryStudentExcludeMapper excludeMapper;

    @Override
    public Long addContractFormulaAreaCountry(ContractFormulaAreaCountryDto contractFormulaAreaCountryDto) {
        if (GeneralTool.isEmpty(contractFormulaAreaCountryDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContractFormulaAreaCountry contractFormulaAreaCountry = BeanCopyUtils.objClone(contractFormulaAreaCountryDto, ContractFormulaAreaCountry::new);
        utilService.updateUserInfoToEntity(contractFormulaAreaCountry);
        contractFormulaAreaCountryMapper.insertSelective(contractFormulaAreaCountry);
        return contractFormulaAreaCountry.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaAreaCountry> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaAreaCountry::getFkContractFormulaId, contractFormulaId);
        contractFormulaAreaCountryMapper.delete(wrapper);
    }

    @Override
    public List<Long> getCountryIdListByFkid(Long contractFormulaId) {
        return contractFormulaAreaCountryMapper.getCountryIdListByFkid(contractFormulaId);
    }

    @Override
    public String getCountryNameByFkid(Long contractFormulaId) {
        List<String> countryNameList = contractFormulaAreaCountryMapper.getCountryNameByFkid(contractFormulaId);
        String result = "";
        if (GeneralTool.isNotEmpty(countryNameList)) {
            result = StringUtils.join(countryNameList, ",");
        }
        return result;
    }

    @Override
    public List<Long> getCountryIdNotInListByFkid(Long contractFormulaId) {
        return excludeMapper.getCountryIdNotInListByFkid(contractFormulaId);


    }
}
