package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.CharacterVo;
import com.get.institutioncenter.service.ICharacterService;
import com.get.institutioncenter.dto.CharacterDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/30
 * @TIME: 16:31
 * @Description:
 **/
@Api(tags = "特性管理")
@RestController
@RequestMapping("/institution/character")
public class CharacterController {
    @Resource
    private ICharacterService characterService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/课程管理/学校特性详情")
    @GetMapping("/{id}")
    public ResponseBo<CharacterVo> detail(@PathVariable("id") Long id) {
        CharacterVo data = characterService.findCharacterById(id);
        return new ResponseBo<>(data);
    }


    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/课程管理/删除学校特性")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.characterService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param institutionCourseEngScoreVo
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/课程管理/更新学校特性")
    @PostMapping("update")
    public ResponseBo<CharacterVo> update(@RequestBody @Validated(CharacterDto.Update.class) CharacterDto institutionCourseEngScoreVo) {
        return UpdateResponseBo.ok(characterService.updateCharacter(institutionCourseEngScoreVo));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 申请资料类型下拉
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "特性类型下拉", notes = "")
    @GetMapping("findType")
    public ResponseBo findType() {
        List<Map<String, Object>> datas = characterService.findType();
        return new ListResponseBo<>(datas);
    }

}
