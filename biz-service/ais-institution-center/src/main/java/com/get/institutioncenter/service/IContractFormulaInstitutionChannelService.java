package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.entity.ContractFormulaInstitutionChannel;
import com.get.institutioncenter.dto.ContractFormulaInstitutionChannelDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/9/22 17:18
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaInstitutionChannelService extends BaseService<ContractFormulaInstitutionChannel> {

    /**
     * 新增关系表
     *
     * @param contractFormulaInstitutionChannelDto
     * @return
     */
    Long addContractFormulaInstitutionChannel(ContractFormulaInstitutionChannelDto contractFormulaInstitutionChannelDto);

    /**
     * 删除关系表
     *
     * @param id
     */
    void deleteByContractFormulaId(Long id);

    /**
     * 根据合同id获取渠道名称
     *
     * @param id
     * @return
     */
    String getInstitutionChannelNamesById(Long id);

    /**
     * 根据合同id获取渠道ids
     *
     * @param id
     * @return
     */
    List<Long> getInstitutionChannelIdsById(Long id);
}
