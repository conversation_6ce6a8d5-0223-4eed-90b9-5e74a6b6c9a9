<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.TranslationMappingMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionTranslationMapping">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_table_name" jdbcType="VARCHAR" property="fkTableName" />
    <result column="fk_column_name" jdbcType="VARCHAR" property="fkColumnName" />
    <result column="input_title" jdbcType="VARCHAR" property="inputTitle" />
    <result column="input_type" jdbcType="INTEGER" property="inputType" />
    <result column="max_length" jdbcType="INTEGER" property="maxLength" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionTranslationMapping">
    insert into s_translation_mapping (id, fk_table_name, fk_column_name, 
      input_title, input_type, max_length, 
      view_order, is_active, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkTableName,jdbcType=VARCHAR}, #{fkColumnName,jdbcType=VARCHAR}, 
      #{inputTitle,jdbcType=VARCHAR}, #{inputType,jdbcType=INTEGER}, #{maxLength,jdbcType=INTEGER}, 
      #{viewOrder,jdbcType=INTEGER}, #{isActive,jdbcType=BIT}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionTranslationMapping">
    insert into s_translation_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkTableName != null">
        fk_table_name,
      </if>
      <if test="fkColumnName != null">
        fk_column_name,
      </if>
      <if test="inputTitle != null">
        input_title,
      </if>
      <if test="inputType != null">
        input_type,
      </if>
      <if test="maxLength != null">
        max_length,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkTableName != null">
        #{fkTableName,jdbcType=VARCHAR},
      </if>
      <if test="fkColumnName != null">
        #{fkColumnName,jdbcType=VARCHAR},
      </if>
      <if test="inputTitle != null">
        #{inputTitle,jdbcType=VARCHAR},
      </if>
      <if test="inputType != null">
        #{inputType,jdbcType=INTEGER},
      </if>
      <if test="maxLength != null">
        #{maxLength,jdbcType=INTEGER},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <select id="getTranslationMappingById" resultType="com.get.institutioncenter.entity.InstitutionTranslationMapping">
      select * from s_translation_mapping where id = #{id}
    </select>
    <select id="getMappingIdByTableNameAndColumnName" resultType="java.lang.Long">
      select id from s_translation_mapping where fk_table_name=#{tableName} and fk_column_name = #{columnName}
    </select>
  <!-- 根据表名，字段名，字段值，获取Dto的id -->
  <select id="getTableId" resultType="java.lang.Long">
    select id from ${tableName} where ${columnDto} = #{DtoValue}
  </select>
</mapper>