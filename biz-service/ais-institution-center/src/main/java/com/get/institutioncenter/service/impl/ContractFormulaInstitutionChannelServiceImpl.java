package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaInstitutionChannelMapper;
import com.get.institutioncenter.entity.ContractFormulaInstitutionChannel;
import com.get.institutioncenter.service.IContractFormulaInstitutionChannelService;
import com.get.institutioncenter.dto.ContractFormulaInstitutionChannelDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2021/9/22 17:19
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaInstitutionChannelServiceImpl extends BaseServiceImpl<ContractFormulaInstitutionChannelMapper, ContractFormulaInstitutionChannel> implements IContractFormulaInstitutionChannelService {

    @Resource
    private ContractFormulaInstitutionChannelMapper contractFormulaInstitutionChannelMapper;
    @Resource
    private UtilService utilService;

    /**
     * 新增关系表
     *
     * @param contractFormulaInstitutionChannelDto
     * @return
     */
    @Override
    public Long addContractFormulaInstitutionChannel(ContractFormulaInstitutionChannelDto contractFormulaInstitutionChannelDto) {
        if (GeneralTool.isEmpty(contractFormulaInstitutionChannelDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContractFormulaInstitutionChannel contractFormulaInstitutionChannel = BeanCopyUtils.objClone(contractFormulaInstitutionChannelDto, ContractFormulaInstitutionChannel::new);
        utilService.updateUserInfoToEntity(contractFormulaInstitutionChannel);
        contractFormulaInstitutionChannelMapper.insertSelective(contractFormulaInstitutionChannel);
        return contractFormulaInstitutionChannel.getId();
    }

    @Override
    public void deleteByContractFormulaId(Long id) {
        LambdaQueryWrapper<ContractFormulaInstitutionChannel> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaInstitutionChannel::getFkContractFormulaId, id);
        contractFormulaInstitutionChannelMapper.delete(wrapper);
    }

    @Override
    public String getInstitutionChannelNamesById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        return contractFormulaInstitutionChannelMapper.getInstitutionChannelNamesById(id);
    }

    @Override
    public List<Long> getInstitutionChannelIdsById(Long id) {
        LambdaQueryWrapper<ContractFormulaInstitutionChannel> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaInstitutionChannel::getFkContractFormulaId, id);
        List<ContractFormulaInstitutionChannel> contractFormulaInstitutionChannels = contractFormulaInstitutionChannelMapper.selectList(wrapper);
        return contractFormulaInstitutionChannels.stream().map(ContractFormulaInstitutionChannel::getFkInstitutionChannelId).collect(Collectors.toList());
    }
}
