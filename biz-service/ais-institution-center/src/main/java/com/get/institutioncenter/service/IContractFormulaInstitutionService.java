package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.ContractFormulaInstitutionDto;
import com.get.institutioncenter.entity.ContractFormulaInstitution;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/22 11:10
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaInstitutionService extends BaseService<ContractFormulaInstitution> {
    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaInstitutionDto]
     * <AUTHOR>
     */
    Long addContractFormulaInstitution(ContractFormulaInstitutionDto contractFormulaInstitutionDto);

    /**
     * @return void
     * @Description :根据contractFormulaId删除
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应学校ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getInstitutionIdListByFkid(Long contractFormulaId);

    /**
     * @return java.lang.String
     * @Description :通过合同公式id 查找对应学校名称
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    String getInstitutionNameByFkid(Long contractFormulaId);
}
