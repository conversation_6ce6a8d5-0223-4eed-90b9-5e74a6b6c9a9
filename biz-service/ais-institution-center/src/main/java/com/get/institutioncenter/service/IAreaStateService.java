package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.dto.AreaStateDto;
import com.get.institutioncenter.vo.AreaCountryVo;
import com.get.institutioncenter.vo.AreaStateVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 区域管理-州省配置接口
 */
public interface IAreaStateService {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    AreaStateVo findAreaStateById(Long id);

    /**
     * @return
     * @Description :批量新增
     * @Param
     * <AUTHOR>
     **/
    void batchAdd(ValidList<AreaStateDto> areaStateDtos);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

    /**
     * @return com.get.institutioncenter.vo.AreaStateVo
     * @Description : 修改
     * @Param [areaStateDto]
     * <AUTHOR>
     */
    AreaStateVo updateAreaState(AreaStateDto areaStateDto);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.AreaCountryVo>
     * @Description :树状图
     * @Param []
     * <AUTHOR>
     */
    List<AreaCountryVo> getTreeList();

    /**
     * 查询国家下面的州省
     *
     * @param id
     * @return
     */
    List<AreaStateVo> getByFkAreaCountryId(Long id);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.AreaStateVo>
     * @Description :列表
     * @Param [areaStateDto, page]
     * <AUTHOR>
     */
    List<AreaStateVo> getAreaStates(AreaStateDto areaStateDto, Page page);

    /**
     * @return java.lang.String
     * @Description :通过州省id 查找对应的州省名称
     * @Param [id]
     * <AUTHOR>
     */
    String getStateNameById(Long id);

    /**
     * 通过州省id 查找对应的州省名称
     *
     * @param id
     * @return
     */
    String getStateFullNameById(Long id);

    /**
     * @Description :feign调用 通过州省ids 查找对应的州省名称map
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getStateNamesByIds(Set<Long> ids);

    /**
     * 通过州省ids 查找对应的州省名称map（拼接name_cn）
     *
     * @param ids
     * @return
     */
    Map<Long, String> getStateFullNamesByIds(Set<Long> ids);

    /**
     * 根据州省IDs获取国家IDs
     * @param fkAreaStateIds
     * @return
     */
    Map<Long,Long> getCountryIdsByStateIds(Set<Long> fkAreaStateIds);

    /**
     * @Description :feign调用 通过州省ids 查找对应的州省名称简写map
     * @Param [ids]
     * <AUTHOR>
    Map<Long, String> getSimplifyStateNamesByIds(Set<Long> ids);

    /**
     * 获取对应国家、公司下 有申请计划的代理所在的 州省下拉框数据
     *
     * @Date 18:51 2023/1/5
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsAgentOfferItemAreaStateList(Long companyId, Long countryId);

    String getAreaStateNameByIds(Set<Long> ids);

    /**
     * 获取对应国家、公司下 的代理所在的 州省下拉框数据
     *
     * @Date 11:05 2023/3/15
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsAgentAreaStateList(Long companyId, Long countryId);

    /**
     * 获取所有州省名称
     */
    Map<Long, String> getStateFullNames();
}
