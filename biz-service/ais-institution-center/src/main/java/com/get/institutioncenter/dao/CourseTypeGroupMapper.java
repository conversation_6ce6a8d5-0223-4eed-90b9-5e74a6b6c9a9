package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.dto.CourseTypeGroupDto;
import com.get.institutioncenter.entity.CourseTypeGroup;
import com.get.salecenter.vo.SelItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @Description：课程类型组别Mapper
 * @Param
 * @return
 * @Date 11:44 2021/4/27
 * <AUTHOR>
 */
@Mapper
public interface CourseTypeGroupMapper extends BaseMapper<CourseTypeGroup> {
    int deleteByPrimaryKey(Long id);

    int insert(CourseTypeGroup record);

    int insertSelective(CourseTypeGroup record);

    /**
     * 不知道为什么会这接口
     * @param record
     * @return
     */
   // CourseTypeGroup selectById(Long id);

    int updateByPrimaryKeySelective(CourseTypeGroup record);

    int updateByPrimaryKey(CourseTypeGroup record);

    /**
     * @Description：获取最大排序
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * @Description：课程类型组别下拉框数据
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    List<BaseSelectEntity> getCourseTypeGroupList();

    List<BaseSelectEntity> getCourseTypeGroupListModeThree();

    /**
     * @Description：提供商获取类型组别名称
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    String getTypeGroupNameByProviderId(Long id);


    String getGroupTypeIdsByCourseTypeIds(@Param("ids")List<String> ids);

    /**
     * @Author: Eric
     * @param pages
     * @param courseTypeGroupDto
     * @param str
     * @return
     */
    List<CourseTypeGroup> getBdyData(IPage<CourseTypeGroup> pages, @Param("courseTypeGroupDto") CourseTypeGroupDto courseTypeGroupDto, @Param("str") List<String> str);

    List<BaseSelectEntity> getCourseGroupList(String keyword);

    List<SelItem> getCourseGroupByIds(@Param("ids") Set<Long> ids);

    String getCourseTypeGroupNameById(Long id);
}