package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.AppInfoMapper;
import com.get.institutioncenter.dto.AppInfoDto;
import com.get.institutioncenter.vo.AppInfoVo;
import com.get.institutioncenter.vo.AppInfoFeignVo;
import com.get.institutioncenter.entity.AppInfo;
import com.get.institutioncenter.service.IAppInfoService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/10
 * @TIME: 15:03
 * @Description:
 **/
@Service
public class AppInfoServiceImpl extends BaseServiceImpl<AppInfoMapper, AppInfo> implements IAppInfoService {
    @Resource
    private AppInfoMapper appInfoMapper;
    @Resource
    private UtilService utilService;

    @Override
    public AppInfoVo findAppInfoById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AppInfo appInfo = appInfoMapper.selectById(id);
        if (GeneralTool.isEmpty(appInfo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AppInfoVo appInfoVo = BeanCopyUtils.objClone(appInfo, AppInfoVo::new);
        if (GeneralTool.isNotEmpty(appInfoVo.getTypeKey())) {
            appInfoVo.setTypeName(ProjectKeyEnum.getValue(appInfoVo.getTypeKey()));
        }
        return appInfoVo;
    }

    @Override
    public List<AppInfoVo> getAppInfos(AppInfoDto appInfoDto, Page page) {
        LambdaQueryWrapper<AppInfo> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(appInfoDto)) {
            if (GeneralTool.isNotEmpty(appInfoDto.getFkTableId())) {
                wrapper.eq(AppInfo::getFkTableId, appInfoDto.getFkTableId());
            }
            if (GeneralTool.isNotEmpty(appInfoDto.getFkTableName())) {
                wrapper.eq(AppInfo::getFkTableName, appInfoDto.getFkTableName());
            }
        }
        wrapper.orderByDesc(AppInfo::getGmtCreate);
        //获取分页数据
        IPage<AppInfo> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<AppInfo> appInfos = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<AppInfoVo> convertDatas = new ArrayList<>();
        for (AppInfo c : appInfos) {
            AppInfoVo appInfoVo = BeanCopyUtils.objClone(c, AppInfoVo::new);
            if (GeneralTool.isNotEmpty(appInfoVo.getTypeKey())) {
                appInfoVo.setTypeName(ProjectKeyEnum.getValue(appInfoVo.getTypeKey()));
            }
            convertDatas.add(appInfoVo);
        }
        return convertDatas;
    }

    @Override
    public AppInfoVo updateAppInfo(AppInfoDto appInfoDto) {
        if (appInfoDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(appInfoDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AppInfo ct = appInfoMapper.selectById(appInfoDto.getId());
        if (ct == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AppInfo appInfo = BeanCopyUtils.objClone(appInfoDto, AppInfo::new);
        utilService.updateUserInfoToEntity(appInfo);
        appInfoMapper.updateById(appInfo);
        return findAppInfoById(appInfo.getId());
    }

    @Override
    public void delete(Long id) {
        //TODO 改过
        //AppInfo appInfo = findAppInfoById(id);
        AppInfoVo appInfo = findAppInfoById(id);
        if (appInfo == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = appInfoMapper.deleteById(appInfo);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<AppInfoDto> appInfoDtos) {
        for (AppInfoDto appInfoDto : appInfoDtos) {
            AppInfo appInfo = BeanCopyUtils.objClone(appInfoDto, AppInfo::new);
            utilService.updateUserInfoToEntity(appInfo);
            appInfoMapper.insert(appInfo);
        }
    }

    @Override
    public List<Map<String, Object>> findType() {
        return ProjectKeyEnum.enumsTranslation2Arrays(ProjectKeyEnum.APP_INFO_TYPE);
    }

    @Override
    public void deleteAppInfoByCourseId(Long id) {
        LambdaQueryWrapper<AppInfo> wrapper = new LambdaQueryWrapper();
        wrapper.eq(AppInfo::getFkTableName, "m_institution_course");
        wrapper.eq(AppInfo::getFkTableId, id);
        int j = appInfoMapper.delete(wrapper);
        if (j < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "delete_fail"));
        }
    }

    /**
     * 获取官网链接
     *
     * @Date 11:37 2021/6/23
     * <AUTHOR>
     */
    @Override
    public Map<Long, String> getWebSiteByTable(String tableName, String typeKey) {
        LambdaQueryWrapper<AppInfo> wrapper = new LambdaQueryWrapper();
        wrapper.eq(AppInfo::getFkTableName, tableName);
        wrapper.eq(AppInfo::getTypeKey, typeKey);
        long start = System.currentTimeMillis();
        List<AppInfo> appInfos = appInfoMapper.selectList(wrapper);
        long end = System.currentTimeMillis();
        System.out.println("======================================用时："+String.valueOf(end-start));
        Map<Long, String> map = new HashMap<>();
        for (AppInfo appInfo : appInfos) {
            map.put(appInfo.getFkTableId(), appInfo.getTypeValue());
        }
        return map;
    }

    @Override
    public Map<Long, String> getWebSiteByAppInfoFeignDto(AppInfoFeignVo appInfoFeignVo) {
        if(GeneralTool.isEmpty(appInfoFeignVo.getFkTableName()) || GeneralTool.isEmpty(appInfoFeignVo.getTypeKey()) )
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("lack_of_necessary_parameters"));
        }
        LambdaQueryWrapper<AppInfo> wrapper = new LambdaQueryWrapper();
        wrapper.eq(AppInfo::getFkTableName, appInfoFeignVo.getFkTableName());
        wrapper.eq(AppInfo::getTypeKey, appInfoFeignVo.getTypeKey());
        if(GeneralTool.isNotEmpty(appInfoFeignVo.getFkTableIds()))
        {
            wrapper.in(AppInfo::getFkTableId, appInfoFeignVo.getFkTableIds());
        }
        long start = System.currentTimeMillis();
        List<AppInfo> appInfos = appInfoMapper.selectList(wrapper);
        long end = System.currentTimeMillis();
        System.out.println("======================================用时2222："+String.valueOf(end-start));
        Map<Long, String> map = new HashMap<>();
        for (AppInfo appInfo : appInfos) {
            map.put(appInfo.getFkTableId(), appInfo.getTypeValue());
        }
        return map;
    }
}
