package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaCourseType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ContractFormulaCourseTypeMapper extends BaseMapper<ContractFormulaCourseType> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContractFormulaCourseType record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应国家id
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCourseTypeIdListByFkid(@Param("contractFormulaId") Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :通过合同公式id 查找对应课程类型名称
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<String> getCourseTypeNameByFkid(@Param("contractFormulaId") Long contractFormulaId);
}