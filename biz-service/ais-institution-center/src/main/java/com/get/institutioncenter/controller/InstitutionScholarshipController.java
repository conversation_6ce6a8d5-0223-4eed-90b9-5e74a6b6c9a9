package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.TableEnum;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.CourseOtherInfoVo;
import com.get.institutioncenter.vo.InstitutionScholarshipVo;
import com.get.institutioncenter.vo.InstitutionScholarshipVo2;
import com.get.institutioncenter.service.IInstitutionCourseAppInfoService;
import com.get.institutioncenter.service.IInstitutionScholarshipService;
import com.get.institutioncenter.dto.*;
import com.get.institutioncenter.dto.query.InstitutionScholarshipQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 12:06
 */
@Api(tags = "奖学金信息管理")
@RestController
@RequestMapping("/institution/institutionScholarship")
public class InstitutionScholarshipController {

    @Resource
    private IInstitutionScholarshipService iInstitutionScholarshipService;

    @Resource
    private IInstitutionCourseAppInfoService iInstitutionCourseAppInfoService;

    @ApiOperation("新增")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/奖学金信息管理/新增奖学金信息")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(InstitutionScholarshipDto.Add.class) InstitutionScholarshipDto institutionScholarshipDto) {
        return SaveResponseBo.ok(iInstitutionScholarshipService.addInstitutionScholarship(institutionScholarshipDto));

    }


    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/奖学金信息管理/奖学金信息详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionScholarshipVo> detail(@PathVariable("id") Long id) {
        InstitutionScholarshipVo data = iInstitutionScholarshipService.findInstitutionScholarshipById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", data);
        return responseBo;
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "业务类型下拉框", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/奖学金信息管理/获取模板信息下拉")
    @GetMapping("getScholarshipTargetSelect")
    public ResponseBo getScholarshipTargetSelect() {
        return new ListResponseBo<>(TableEnum.enums2Arrays(TableEnum.S_P));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取目标对象", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/奖学金信息管理/获取目标对象")
    @PostMapping("getSelectedByTarget")
    public ListResponseBo<BaseSelectEntity> getSelectedByTarget(@RequestBody @Validated ScholarshipTargetDto scholarshipTargetDto){
        return new ListResponseBo<>(iInstitutionScholarshipService.getSelectedByTarget(scholarshipTargetDto));
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "获取课程其他信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/奖学金信息管理/获取课程其他信息")
    @PostMapping("getOtherCourseInfo")
    public ListResponseBo<CourseOtherInfoVo> getCourseOtherInfo(@RequestBody SearchBean<QueryOtherCourseDto> searchBean){
        return iInstitutionCourseAppInfoService.getOtherCourseInfo(searchBean.getData(),searchBean);
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/奖学金信息管理/删除奖学金信息")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        iInstitutionScholarshipService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/奖学金信息管理/更新奖学金信息")
    @PostMapping("update")
    public ResponseBo<InstitutionScholarshipVo> update(@RequestBody @Validated(InstitutionScholarshipDto.Update.class) InstitutionScholarshipDto institutionScholarshipDto) {
        return UpdateResponseBo.ok(iInstitutionScholarshipService.updateInstitutionScholarship(institutionScholarshipDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/奖学金信息管理/查询奖学金信息")
    @PostMapping("datas")
    public ResponseBo<InstitutionScholarshipVo> datas(@RequestBody SearchBean<InstitutionScholarshipQueryDto> page) {
        List<InstitutionScholarshipVo> datas = iInstitutionScholarshipService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @ Description :
     * @ Param [fkInstitutionId, fkMajorLevelId]
     * @ return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionScholarshipVo>
     * @ author LEO
     */
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "小程序奖学金学校详情", notes = "")
    @GetMapping("getWcInstitutionScholarshipDatas")
    public ResponseBo<InstitutionScholarshipVo2> getWcInstitutionScholarshipDatas(@RequestParam("fkInstitutionId") Long fkInstitutionId, @RequestParam(value = "fkMajorLevelId", required = false) Integer fkMajorLevelId) {
        List<InstitutionScholarshipVo2> datas = iInstitutionScholarshipService.getWcInstitutionScholarshipDatas(fkInstitutionId, fkMajorLevelId);
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "奖学金优先匹配查询", notes = "")
    @VerifyLogin(IsVerify = false)
    @PostMapping("priorityMatchingQuery")
    public ResponseBo<InstitutionScholarshipVo> priorityMatchingQuery(@RequestBody @Validated WeScholarshipAppDto weScholarshipAppDto){
        return new ListResponseBo<>(iInstitutionScholarshipService.priorityMatchingQuery(weScholarshipAppDto));
    }

    /**
     * @ Description :
     * @ Param [fkInstitutionId, fkMajorLevelId]
     * @ return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionScholarshipVo>
     * @ author LEO
     */
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "小程序奖学金学校列表", notes = "")
    @PostMapping("getWcInstitutionScholarshipList")
    public ResponseBo<InstitutionScholarshipVo2> getWcInstitutionScholarshipList(@RequestBody SearchBean<InstitutionScholarshipDto2> page) {
        List<InstitutionScholarshipVo2> datas = iInstitutionScholarshipService.getWcInstitutionScholarshipList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @VerifyLogin(IsVerify = false)
    @ApiOperation("小程序判断学校是否有其他模块数据")
    @GetMapping("getIsOtherModule")
    public ResponseBo getIsOtherModule(@RequestParam("fkInstitutionId") Long fkInstitutionId) {
        InstitutionScholarshipVo isOtherModule = iInstitutionScholarshipService.getIsOtherModule(fkInstitutionId);
        return new ResponseBo(isOtherModule);
    }
}
