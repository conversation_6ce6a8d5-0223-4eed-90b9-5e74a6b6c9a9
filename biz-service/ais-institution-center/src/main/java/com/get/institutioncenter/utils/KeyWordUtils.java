package com.get.institutioncenter.utils;

import com.get.institutioncenter.utils.keyWord.KeywordFilter;
import com.get.institutioncenter.utils.keyWord.KeywordFilterBuilder;
import com.get.institutioncenter.utils.keyWord.ReplaceStrategy;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2021/6/16 17:42
 * @verison: 1.0
 * @description:
 */
public class KeyWordUtils {

    private final static String FILE = "config/code.txt";

    public static String replace(String src) {
        Map<String, String> fromFile = new HashMap<>();
        try {
            fromFile = getFromFile(FILE);
            for (String s : fromFile.keySet()) {
                String keyWords = s;
                KeywordFilterBuilder builder = new KeywordFilterBuilder();
                builder.setKeywords(Arrays.asList(keyWords));
                KeywordFilter filter = builder.build();

                Map<String, String> finalFromFile = fromFile;
                final ReplaceStrategy ss = new ReplaceStrategy() {
                    @Override
                    public String replaceWith(String keywords) {
                        return finalFromFile.get(s);
                    }
                };
                src = filter.replace(src, ss);
            }
            return src;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return src;
    }

    private static Map<String, String> getFromFile(String file) throws IOException {
        Resource resource = new ClassPathResource(file);
        BufferedReader br = new BufferedReader(new InputStreamReader(resource.getInputStream()));
//        Resource resource = new ClassPathResource("code.txt");
//        BufferedReader br = new BufferedReader(new FileReader(file));
        String str = null;
        Map<String, String> map = new HashMap<>();
        while ((str = br.readLine()) != null) {
            String[] array = str.split("=", 2);
            if (array.length == 2) {
                map.put(array[0], array[1]);
            }
        }
        br.close();
        return map;
    }

}
