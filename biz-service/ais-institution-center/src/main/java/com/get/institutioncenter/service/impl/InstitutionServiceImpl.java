package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.bo.InstitutionApplicationStaticsQueryBo;
import com.get.institutioncenter.component.TranslationHelp;
import com.get.institutioncenter.dao.*;
import com.get.institutioncenter.dto.*;
import com.get.institutioncenter.dto.query.InstitutionQueryDto;
import com.get.institutioncenter.dto.query.InstitutionZoneQueryDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import com.get.institutioncenter.entity.*;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.utils.InstitutionTypeUtil;
import com.get.institutioncenter.utils.MyStringUtils;
import com.get.institutioncenter.vo.*;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.salecenter.entity.NameLabel;
import com.get.salecenter.feign.ISaleCenterClient;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.get.institutioncenter.utils.InstitutionTypeUtil.getAccommodationTypeNames;
import static com.get.institutioncenter.utils.InstitutionTypeUtil.getK12TypeNames;

/**
 * @author: Sea
 * @create: 2020/7/13 15:39
 * @verison: 1.0
 * @description:
 */
@Service
public class InstitutionServiceImpl extends BaseServiceImpl<InstitutionMapper, Institution> implements IInstitutionService {
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private AreaCountryMapper areaCountryMapper;
    @Resource
    private UtilService utilService;
    @Resource
    @Lazy
    private IContactPersonService contactPersonService;
    @Resource
    @Lazy
    private IInstitutionProviderService providerService;
    @Resource
    private INewsService newsService;
    @Resource
    private InstitutionRpaMappingRelationMapper institutionRpaMappingRelationMapper;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private InstitutionProviderInstitutionMapper institutionProviderInstitutionMapper;
    @Resource
    @Lazy
    private IInstitutionCourseService courseService;
    @Resource
    private InstitutionTypeMapper institutionTypeMapper;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private InstitutionPathwayMapper institutionPathwayMapper;
    @Resource
    private AreaStateMapper areaStateMapper;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private AreaCityMapper areaCityMapper;
    @Resource
    private ICharacterService characterService;
    @Resource
    private IInstitutionZoneService institutionZoneService;
    @Resource
    private IAreaCountryService areaCountryService;
    @Resource
    private IAreaStateService areaStateService;
    @Resource
    @Lazy
    private IAreaCityService areaCityService;
    @Resource
    private IInstitutionTypeService institutionTypeService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private TranslationHelp translationHelp;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private InstitutionProviderCompanyMapper institutionProviderCompanyMapper;

    @Override
    public List<Long> getInstitutionIdsByName(String institutionName) {
        List<Long> institutionIds = institutionMapper.getInstitutionIds("%" + institutionName + "%");

        if (GeneralTool.isEmpty(institutionIds)) {
            institutionIds.add(0L);
        }
        return institutionIds;
    }

    @Override
    public List<Long> getInstitutionIdsByNames(String institutionName) {
        List<Long> institutionIds = institutionMapper.getInstitutionIds("%" + institutionName + "%");
        return institutionIds;
    }

    @Override
    public String getInstitutionNameById(Long id) {
        return institutionMapper.getInstitutionNameById(id);
    }

    @Override
    public Institution getInstitutionById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return institutionMapper.selectById(id);
    }

    @Override
    public Long addNews(NewsDto newsDto) {
        newsDto.setFkTableName(TableEnum.INSTITUTION.key);
        return newsService.addNews(newsDto);
    }

    @Override
    public List<NewsVo> getNewsData(NewsQueryDto newsVo, Page page) {
        if (GeneralTool.isEmpty(newsVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        //查询学校下面的新闻
        newsVo.setFkTableId(newsVo.getFkTableId());
        newsVo.setFkTableName(TableEnum.INSTITUTION.key);
        return newsService.datas(newsVo, page);
    }


    @Override
    public List<InstitutionZoneVo> getInstitutionZoneData(InstitutionZoneQueryDto data, Page page) {
        if (GeneralTool.isEmpty(data.getFkInstitutionId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        return institutionZoneService.datas(data, page);
    }



    /**
     * 学校下拉框数据
     *
     * @Date 16:12 2021/7/22
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getInstitutionSelect(Long id) {
        List<Long> ids = institutionPathwayMapper.getNonBridgeInstitutionSelect(id);
        return institutionMapper.getNonBridgeInstitutionSelect(ids);
    }

    /**
     * 学校桥梁学校下拉框数据
     *
     * @param id
     * @return
     * @
     */
    @Override
    public List<BaseSelectEntity> getBridgeInstitutionSelect(Long id) {
        List<Long> ids = institutionPathwayMapper.getBridgeInstitutionIds(id);
        List<BaseSelectEntity> list = institutionMapper.getBridgeInstitutionSelect(ids);
        return list;
    }

    /**
     * 非桥梁学校下拉框数据
     *
     * @Date 16:12 2021/7/22
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getNonBridgeInstitutionSelect(Long id) {
        List<Long> ids = institutionPathwayMapper.getNonBridgeInstitutionSelect(id);
        return institutionMapper.getNonBridgeInstitutionSelect(ids);
    }

    /**
     * 课程 非桥梁学校下拉框数据
     *
     * @Date 18:02 2021/7/27
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getNonBridgeInstitutionSelectByCourse(Long id) {
        List<Long> ids = institutionPathwayMapper.getNonBridgeInstitutionSelect(id);
        return institutionMapper.getNonBridgeInstitutionSelectByCourse(ids, id);
    }

    /**
     * 课程桥梁学校下拉框数据
     *
     * @param
     * @return
     * @
     */
    @Override
    public List<BaseSelectEntity> getBridgeInstitutionSelectByCourse(Long institutionId) {
        List<BaseSelectEntity> list = institutionMapper.getBridgeInstitutionSelectByCourse(institutionId);
        return list;
    }

    /**
     * 已绑定提供商学校下拉框数据
     *
     * @return
     */
    @Override
    public List<BaseSelectEntityPlus> getProviderInstitutionList(Long countryId) {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        List<BaseSelectEntity> providerInstitutionList = institutionMapper.getProviderInstitutionList(companyIds, countryId);
        List<BaseSelectEntityPlus> resultList = BeanCopyUtils.copyListProperties(providerInstitutionList, BaseSelectEntityPlus::new);

        //查询学校业务标签
        Result<Map<Long, NameLabel>> result = saleCenterClient.getNameLabelListByFkTableName(TableEnum.INSTITUTION.key);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            Map<Long, NameLabel> nameLabelMap = result.getData();
            resultList.forEach(b -> {
                NameLabel nameLabel = nameLabelMap.get(b.getId());
                if (GeneralTool.isNotEmpty(nameLabel)) {
                    b.setLabel(nameLabel.getLabel());
                    b.setPositionType(nameLabel.getPositionType());
                    if (nameLabel.getPositionType() == 1) {
                        b.setName(b.getName() + " " + b.getLabel());
                        b.setNameChn(b.getNameChn() + " " + b.getLabel());
                        b.setFullName(b.getFullName() + " " + b.getLabel());
                    } else {
                        b.setName(b.getLabel() + " " + b.getName());
                        b.setNameChn(b.getLabel() + " " + b.getNameChn());
                        b.setFullName(b.getLabel() + " " + b.getFullName());
                    }
                }
            });
        }
        return resultList;
    }

    /**
     * 根据学校id 获取对应的RPA地图学校id
     *
     * @Date 20:50 2022/4/23
     * <AUTHOR>
     */
    @Override
    public Map<Long, Long> getInstitutionRpaMappingRelation(Set<Long> institutionIds) {
        Map<Long, Long> map = new HashMap<>();
        List<InstitutionRpaMappingRelation> institutionRpaMappingRelations = institutionRpaMappingRelationMapper.selectList(Wrappers.<InstitutionRpaMappingRelation>lambdaQuery().in(InstitutionRpaMappingRelation::getFkInstitutionId, institutionIds));
        if (GeneralTool.isNotEmpty(institutionRpaMappingRelations)) {
            for (InstitutionRpaMappingRelation institutionRpaMappingRelation : institutionRpaMappingRelations) {
                map.put(institutionRpaMappingRelation.getFkInstitutionId(), institutionRpaMappingRelation.getFkInstitutionRpaMappingId());
            }
        }
        return map;
    }

    @Override
    public List<InstitutionVo> datas(InstitutionQueryDto institutionVo, Page page) {
//        LambdaQueryWrapper<Institution> wrapper = new LambdaQueryWrapper();
//        IPage<Institution> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        if (GeneralTool.isNotEmpty(institutionVo.getKeyWord())){
            institutionVo.setKeyWord(institutionVo.getKeyWord().trim());
        }
        IPage<Institution> IPage = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())));
        List<InstitutionVo> institutionVos = institutionMapper.getByVo(IPage, institutionVo);
        page.setAll((int) IPage.getTotal());

        Set<Long> institutionIds = institutionVos.stream().map(InstitutionVo::getId).collect(Collectors.toSet());
        //获取集合的所有国家ids
        Set<Long> countryIdSet = institutionVos.stream().map(InstitutionVo::getFkAreaCountryId).collect(Collectors.toSet());
        //获取集合的所有州省ids
        Set<Long> stateIdSet = institutionVos.stream().map(InstitutionVo::getFkAreaStateId).collect(Collectors.toSet());
        //获取集合的所有城市ids
        Set<Long> cityIdSet = institutionVos.stream().map(InstitutionVo::getFkAreaCityId).collect(Collectors.toSet());
        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIdSet)) {
            countryNamesByIds = areaCountryService.getCountryNamesByIds(countryIdSet);
        }
        //根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIdSet)) {
            stateNamesByIds = areaStateService.getStateNamesByIds(stateIdSet);
        }
        //根据城市ids获取城市名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIdSet)) {
            cityNamesByIds = areaCityService.getCityNamesByIds(cityIdSet);
        }

        //获取学校类型ids
        Set<Long> institutionTypeIds = institutionVos.stream().map(InstitutionVo::getFkInstitutionTypeId).collect(Collectors.toSet());
        //根据学校类型ids获取名称
        Map<Long, String> institutionTypeNameByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionTypeIds)) {
            institutionTypeNameByIds = institutionTypeService.getInstitutionTypeNameByIds(institutionTypeIds);
        }

        //获取币种编号nums
        Set<String> currencyTypeNums = institutionVos.stream().map(InstitutionVo::getFkCurrencyTypeNum).collect(Collectors.toSet());
        //根据币种编号nums查找币种名称
        Map<String, String> currencyNamesByNums = new HashMap<>();
        if (GeneralTool.isNotEmpty(currencyTypeNums)) {
            Result<Map<String, String>> result = financeCenterClient.getCurrencyNamesByNums(currencyTypeNums);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            currencyNamesByNums = result.getData();
        }

        Map<Long, Boolean> commissionActiveStatusMap = saleCenterClient.getCommissionActiveStatusByInstitutionIds(institutionIds).getData();

        for (InstitutionVo institutionDto : institutionVos) {
            //查询国家名称
            if (GeneralTool.isNotEmpty(institutionDto.getFkAreaCountryId())) {
                institutionDto.setFkAreaCountryName(countryNamesByIds.get(institutionDto.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(institutionDto.getFkAreaStateId())) {
                institutionDto.setFkAreaStateName(stateNamesByIds.get(institutionDto.getFkAreaStateId()));
            }
            if (GeneralTool.isNotEmpty(institutionDto.getFkAreaCountryId())) {
                institutionDto.setFkAreaCityName(cityNamesByIds.get(institutionDto.getFkAreaCityId()));
            }
            //查询学校类型名称
            if (GeneralTool.isNotEmpty(institutionDto.getFkInstitutionTypeId())) {
                institutionDto.setFkInstitutionTypeName(institutionTypeNameByIds.get(institutionDto.getFkInstitutionTypeId()));
            }
            //查询币种名称
            if (GeneralTool.isNotEmpty(institutionDto.getFkCurrencyTypeNum())) {
                institutionDto.setCurrencyName(currencyNamesByNums.get(institutionDto.getFkCurrencyTypeNum()));
            }
            StringJoiner stringJoiner = new StringJoiner(" ");
            if (GeneralTool.isNotEmpty(institutionDto.getPublicLevel())) {
                List<String> result = Arrays.asList(institutionDto.getPublicLevel().split(","));
                for (String name : result) {
                    stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
                }
                institutionDto.setPublicLevelName(stringJoiner.toString());
            }
            //课程数
            institutionDto.setCourseCount(courseService.getCourseCountByInstitutionId(institutionDto.getId()));
            String fullName = institutionDto.getName();
            if (GeneralTool.isNotEmpty(institutionDto.getNameChn())) {
                fullName = fullName + "（" + institutionDto.getNameChn() + "）";
            }
            institutionDto.setName(fullName);
            if (GeneralTool.isNotEmpty(commissionActiveStatusMap)){
                institutionDto.setIsActiveCommission(commissionActiveStatusMap.get(institutionDto.getId()) != null && commissionActiveStatusMap.get(institutionDto.getId()));
            }
        }
        return institutionVos;
    }

    @Override
    public List<BaseSelectEntity> getInstitutionList() {
        return institutionMapper.getInstitutionList();
    }

    @Override
    public List<BaseSelectEntity> getInstitutionListByKeyword(String keyword) {
        return institutionMapper.getInstitutionListByKeyword(keyword, null);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionListByKeyword(String keyword, Long countryId) {
        return institutionMapper.getInstitutionListByKeyword(keyword, countryId);
    }

    /**
     * 根据国家获取学校下拉框数据
     *
     * @Date 16:29 2021/5/31
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getInstitutionListByCountryId(Long countryId) {
        return institutionMapper.getInstitutionListByCountryId(countryId);
    }

    /**
     * 根据国家(多选)获取学校下拉框数据
     *
     * @param fkCountryIdList
     * @return
     */
    @Override
    public List<BaseSelectEntity> getInstitutionListByCountryIdList(List<Long> fkCountryIdList) {
        return institutionMapper.getInstitutionListByCountryIdList(fkCountryIdList);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionListByProviderId(Long providerId) {
        return institutionMapper.getInstitutionListByProviderId(providerId);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long addInstitution(InstitutionDto institutionDto) {
        if (institutionDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //学校名字不能相同
        LambdaQueryWrapper<Institution> wrapper = new LambdaQueryWrapper();
        wrapper.eq(Institution::getName, institutionDto.getName());
        List<Institution> institutions = institutionMapper.selectList(wrapper);
        if (GeneralTool.isNotEmpty(institutions)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_name_exists"));
        }
        Institution institution = BeanCopyUtils.objClone(institutionDto, Institution::new);
        if (GeneralTool.isNotEmpty(institution.getK12Type())){
            institution.setK12Type(InstitutionTypeUtil.normalized(institution.getK12Type()));
        }
        if (GeneralTool.isNotEmpty(institution.getAccommodationType())){
            institution.setAccommodationType(InstitutionTypeUtil.normalized(institution.getAccommodationType()));
        }
        //是否教会学校（默认为否）
        final int isChurch = 0;
        if (GeneralTool.isEmpty(institution.getIsChurch())){
            institution.setIsChurch(isChurch);
        }
        utilService.updateUserInfoToEntity(institution);
        institutionMapper.insert(institution);
        AreaCountry areaCountry = areaCountryMapper.selectById(institution.getFkAreaCountryId());
        if (GeneralTool.isNotEmpty(areaCountry)) {
            institution.setNum(MyStringUtils.getInstitutionNum(areaCountry.getNum(), institution.getId()));
        }
        institutionMapper.updateById(institution);
        return institution.getId();
    }

    @Override
    public InstitutionVo updateInstitution(InstitutionDto institutionDto) {
        if (institutionDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //学校名字不能相同
        LambdaQueryWrapper<Institution> wrapper = new LambdaQueryWrapper();
        wrapper.eq(Institution::getName, institutionDto.getName());
        wrapper.ne(Institution::getId, institutionDto.getId());
        List<Institution> institutions = institutionMapper.selectList(wrapper);
        if (GeneralTool.isNotEmpty(institutions)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("institution_name_exists"));
        }
        Institution institution = institutionMapper.selectById(institutionDto.getId());
        if (institution == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Institution is = BeanCopyUtils.objClone(institutionDto, Institution::new);
        if (GeneralTool.isNotEmpty(institutionDto.getK12Type())){
            is.setK12Type(InstitutionTypeUtil.normalized(institutionDto.getK12Type()));
        }
        if (GeneralTool.isNotEmpty(institutionDto.getAccommodationType())){
            is.setAccommodationType(InstitutionTypeUtil.normalized(institutionDto.getAccommodationType()));
        }
        if (institutionPathwayMapper.institutionIdPathwayIsEmpty(institutionDto.getId()) && !institutionDto.getFkInstitutionTypeId().equals(institution.getFkInstitutionTypeId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("types_not_allowed_modified"));
        }
        utilService.updateUserInfoToEntity(is);
        institutionMapper.updateByIdWithNull(is);
        return findInstitutionById(is.getId());
    }

    @Override
    public InstitutionVo findInstitutionById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Institution institution = institutionMapper.selectById(id);
        if (GeneralTool.isEmpty(institution)) {
            return null;
        }
        InstitutionVo institutionVo = BeanCopyUtils.objClone(institution, InstitutionVo::new);
        if (GeneralTool.isNotEmpty(institutionVo.getFkAreaCountryId())) {
            institutionVo.setFkAreaCountryName(areaCountryMapper.getCountryNameById(institutionVo.getFkAreaCountryId()));
        }
        if (GeneralTool.isNotEmpty(institutionVo.getFkAreaStateId())) {
            institutionVo.setFkAreaStateName(areaStateMapper.getStateFullNameById(institutionVo.getFkAreaStateId()));
        }
        if (GeneralTool.isNotEmpty(institutionVo.getFkAreaCountryId())) {
            institutionVo.setFkAreaCityName(areaCityMapper.getCityFullNameById(institutionVo.getFkAreaCityId()));
        }
        if (GeneralTool.isNotEmpty(institutionVo.getK12Type())){
            institutionVo.setK12Type(institutionVo.getK12Type());
            institutionVo.setK12TypeName(getK12TypeNames(institutionVo.getK12Type()));
        }
        if (GeneralTool.isNotEmpty(institutionVo.getAccommodationType())){
            institutionVo.setAccommodationType(institutionVo.getAccommodationType());
            institutionVo.setAccommodationTypeName(getAccommodationTypeNames(institutionVo.getAccommodationType()));
        }
        if (GeneralTool.isNotEmpty(institutionVo.getIsChurch())){
            institutionVo.setIsChurch(institution.getIsChurch());
        }
 /*       String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(institutionVo) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(Collections.singletonList(institutionVo), ProjectKeyEnum.getInitialValue(language));
        }*/
        //获取学校类型ids
        Set<Long> institutionTypeIds = new HashSet<>();
        ;
        institutionTypeIds.add(institutionVo.getFkInstitutionTypeId());
        //根据学校类型ids获取名称
        Map<Long, String> institutionTypeNameByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionTypeIds)) {
            institutionTypeNameByIds = institutionTypeService.getInstitutionTypeNameByIds(institutionTypeIds);
        }
        //查询学校类型名称
        if (GeneralTool.isNotEmpty(institutionVo.getFkInstitutionTypeId())) {
            institutionVo.setFkInstitutionTypeName(institutionTypeNameByIds.get(institutionVo.getFkInstitutionTypeId()));
        }
        //查询学校类型key
        if (GeneralTool.isNotEmpty(institutionVo.getFkInstitutionTypeId())) {
            institutionVo.setFkInstitutionTypeKey(institutionTypeMapper.getInstitutionTypeKeyById(institutionVo.getFkInstitutionTypeId()));
        }
        if (GeneralTool.isNotEmpty(institutionVo.getKpiLevel())) {
            institutionVo.setKpiLevelName(ProjectExtraEnum.getValueByKey(institutionVo.getKpiLevel(), ProjectExtraEnum.KEI_LEVEL));
        }
        StringJoiner stringJoiner = new StringJoiner(" ");
        if (GeneralTool.isNotEmpty(institutionVo.getPublicLevel())) {
            List<String> result = Arrays.asList(institutionVo.getPublicLevel().split(","));
            for (String name : result) {
                stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
            }
            institutionVo.setPublicLevelName(stringJoiner.toString());
        }
        //数据等级名
        if (GeneralTool.isNotEmpty(institutionVo.getDataLevel())) {
            institutionVo.setDataLevelName(ProjectExtraEnum.getValueByKey(institutionVo.getDataLevel(), ProjectExtraEnum.DATA_LEVEL));
        }
        institutionVo.setFkTableName(TableEnum.INSTITUTION.key);
        //设置属性
        institutionVo.setMediaAndAttachedDto(getIconImg(id));
        String fullName = institutionVo.getName();
        if (GeneralTool.isNotEmpty(institutionVo.getNameChn())) {
            fullName = fullName + "（" + institutionVo.getNameChn() + "）";
        }
        institutionVo.setFullName(fullName);
        //查询币种名称
        if (GeneralTool.isNotEmpty(institutionVo.getFkCurrencyTypeNum())) {
            Result<String> result = financeCenterClient.getCurrencyNameByNum(institutionVo.getFkCurrencyTypeNum());
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                String currencyName = result.getData();
                institutionVo.setCurrencyName(currencyName);
            }
        }

        HashSet<Long> institutionIds = Sets.newHashSet(id);
        Map<Long, Boolean> commissionActiveStatusMap = saleCenterClient.getCommissionActiveStatusByInstitutionIds(institutionIds).getData();

        InstitutionVo countByInstitutionId = institutionMapper.getCountByInstitutionId(id);
        institutionVo.setZoneNum(countByInstitutionId.getZoneNum());
        institutionVo.setFacultyNum(countByInstitutionId.getFacultyNum());
        institutionVo.setAlumnusNum(countByInstitutionId.getAlumnusNum());
        institutionVo.setCourseNum(countByInstitutionId.getCourseNum());
        institutionVo.setFaqNum(countByInstitutionId.getFaqNum());
        institutionVo.setInfoNum(countByInstitutionId.getInfoNum());
        institutionVo.setScholarshipNum(countByInstitutionId.getScholarshipNum());
        institutionVo.setDeadlineInfoNum(countByInstitutionId.getDeadlineInfoNum());
        institutionVo.setAppFeeNum(countByInstitutionId.getAppFeeNum());
        institutionVo.setIsActiveCommission(commissionActiveStatusMap.get(id) != null && commissionActiveStatusMap.get(id));

        return institutionVo;
    }

    //获取图片
    private MediaAndAttachedVo getIconImg(Long id) {
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.INSTITUTION.key);
        attachedVo.setFkTableId(id);
        attachedVo.setTypeKey(FileTypeEnum.INSTITUTION_LOGO.key);

        List<MediaAndAttachedVo> headIcon = attachedService.getMediaAndAttachedDto(attachedVo);
        if (GeneralTool.isNotEmpty(headIcon)) {
            return headIcon.get(0);
        }
        return null;
    }

    @Override
    public List<InstitutionVo> getInstitutionDto(InstitutionDto institutionDto, Page page) {
        LambdaQueryWrapper<Institution> wrapper = new LambdaQueryWrapper();
        List<InstitutionVo> institutionVos = new ArrayList<>();
        if (page == null ){
            institutionVos = institutionMapper.getInstitutions(null, institutionDto, SecureUtil.getCompanyIds());
        }else {
            IPage<Institution> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
            institutionVos = institutionMapper.getInstitutions(pages, institutionDto, SecureUtil.getCompanyIds());
            page.setAll((int) pages.getTotal());
        }
        //设置课程数
        setCourseCount(institutionVos);
        //移除空元素
        institutionVos.removeIf(Objects::isNull);

        Map<Long, String> companyNameMap = permissionCenterClient.getAllCompanyDto().getData().stream().collect(Collectors.toMap(CompanyTreeVo::getId, CompanyTreeVo::getShortName));

        for (InstitutionVo institutionVo : institutionVos) {
            if (GeneralTool.isNotEmpty(institutionVo.getFkCompanyIds())) {
                StringBuilder str = new StringBuilder();
                String[] companyIds = institutionVo.getFkCompanyIds().split(",");
                List<Long> companyIdList = Arrays.stream(companyIds).map(Long::valueOf).collect(Collectors.toList());
                companyIdList.retainAll(SecureUtil.getCompanyIds());
                for (Long companyId : companyIdList) {
                    str.append(companyNameMap.get(companyId)).append(",");
                }
                str.deleteCharAt(str.length() - 1);
                institutionVo.setProviderInstitutionCompanyName(str.toString());
            }
            if (GeneralTool.isNotEmpty(institutionVo.getFkAreaCountryId())) {
                institutionVo.setFkAreaCountryName(areaCountryMapper.getCountryNameById(institutionVo.getFkAreaCountryId()));
            }
        }
        return institutionVos;
    }

    @Override
    public Long addContactPerson(ContactPersonDto contactPersonDto) {
        if (GeneralTool.isEmpty(contactPersonDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        contactPersonDto.setFkTableName(TableEnum.INSTITUTION.key);
        return contactPersonService.addContactPerson(contactPersonDto);
    }

    @Override
    public List<ContactPersonVo> getContactPersonDtos(ContactPersonDto contactPersonDto, Page page) {
        if (GeneralTool.isEmpty(contactPersonDto.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("tableId_null"));
        }
        contactPersonDto.setFkTableName(TableEnum.INSTITUTION.key);
        return contactPersonService.getContactPersonDtos(contactPersonDto, page);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addInstitutionProviderInstitution(InstitutionDto institutionDto) {
        if (GeneralTool.isEmpty(institutionDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(institutionDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LambdaQueryWrapper<InstitutionProviderInstitution> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionProviderInstitution::getFkInstitutionId, institutionDto.getId());
        //不能全删了-查原来有多少-对比新加的
        List<InstitutionProviderInstitution> institutionProviderInstitutions = institutionProviderInstitutionMapper.selectList(wrapper);
        List<Long> institutionProviderIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(institutionProviderInstitutions)) {
            institutionProviderIds = institutionProviderInstitutions.stream().map(InstitutionProviderInstitution::getFkInstitutionProviderId).collect(Collectors.toList());
        }

//        int k = institutionProviderInstitutionMapper.delete(wrapper);
//        if (k < 0) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
//        }

        List<InstitutionProviderCompany> institutionProviderCompanyList = institutionProviderCompanyMapper.selectList(Wrappers.<InstitutionProviderCompany>lambdaQuery()
                .in(InstitutionProviderCompany::getFkInstitutionProviderId, institutionDto.getIds()));
        Map<Long, List<Long>> companyIdsMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(institutionProviderCompanyList)) {
            companyIdsMap = institutionProviderCompanyList.stream().collect(Collectors.groupingBy(InstitutionProviderCompany::getFkInstitutionProviderId, Collectors.mapping(InstitutionProviderCompany::getFkCompanyId, Collectors.toList())));
        }


        for (Long id : institutionDto.getIds()) {
            InstitutionProviderInstitution i = new InstitutionProviderInstitution();
            if (institutionProviderIds.contains(id)) {
                continue;
            }
            List<Long> companyIdList = companyIdsMap.get(id);
            String companyIds = companyIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
            i.setFkCompanyIds(companyIds);
            i.setFkInstitutionId(institutionDto.getId());
            i.setFkInstitutionProviderId(id);
            i.setIsActive(true);
            i.setActiveDate(new Date());
            utilService.setCreateInfo(i);
            int j = institutionProviderInstitutionMapper.insert(i);
            if (j < 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }


    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        boolean succuee = deleteService.deleteInstitutionRelation(id);
        if (succuee) {
            int i = institutionMapper.deleteById(id);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        }
        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION.key, id);
    }

    @Override
    public List<MediaAndAttachedVo> getInstitutionMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.INSTITUTION.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addInstitutionMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        if (mediaAttachedVos.get(0).getTypeKey().equals(FileTypeEnum.INSTITUTION_LOGO.key)) {
            LambdaQueryWrapper<MediaAndAttached> wrapper = new LambdaQueryWrapper();
            wrapper.eq(MediaAndAttached::getFkTableName, TableEnum.INSTITUTION.key);
            wrapper.eq(MediaAndAttached::getFkTableId, mediaAttachedVos.get(0).getFkTableId());
            wrapper.eq(MediaAndAttached::getTypeKey, mediaAttachedVos.get(0).getTypeKey());
            mediaAndAttachedMapper.delete(wrapper);
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAttachedVo : mediaAttachedVos) {
            //设置插入的表
            mediaAttachedVo.setFkTableName(TableEnum.INSTITUTION.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAttachedVo));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<InstitutionProviderVo> getProviderList(InstitutionProviderDto providerVo, Page page) {
        if (GeneralTool.isNotEmpty(providerVo.getFkCompanyId())){
            if (!SecureUtil.validateCompany(providerVo.getFkCompanyId())){
                return new ArrayList<>();
            }
        }else if (GeneralTool.isNotEmpty(providerVo.getFkCompanyIds())){
            if (!SecureUtil.validateCompanys(providerVo.getFkCompanyIds())){
                return new ArrayList<>();
            }
        }
        if (GeneralTool.isEmpty(providerVo.getFkInstitutionId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        return providerService.getInstitutionProviderList(providerVo, page);
    }

    private void setCourseCount(List<InstitutionVo> institutionVos) {
        //学校类型ids
        Set<Long> fkInstitutionTypeIds = institutionVos.stream().map(InstitutionVo::getFkInstitutionTypeId).collect(Collectors.toSet());
        //根据学校类型ids获取名称
        Map<Long, String> institutionTypeNameByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkInstitutionTypeIds)) {
            institutionTypeNameByIds = institutionTypeService.getInstitutionTypeNameByIds(fkInstitutionTypeIds);
        }
        //设置课程数
        for (InstitutionVo institutionVo : institutionVos) {
            if (GeneralTool.isNotEmpty(institutionVo)) {
                institutionVo.setCourseCount(courseService.getCourseCountByInstitutionId(institutionVo.getId()));
            }
            //查询学校类型名称
            if (GeneralTool.isNotEmpty(institutionVo.getFkInstitutionTypeId())) {
                institutionVo.setFkInstitutionTypeName(institutionTypeNameByIds.get(institutionVo.getFkInstitutionTypeId()));
            }
        }
    }

    private boolean validateAdd(InstitutionDto institutionDto) {
        LambdaQueryWrapper<Institution> wrapper = new LambdaQueryWrapper();
        wrapper.and(wrapper_ ->
                wrapper_.like(Institution::getName, institutionDto.getName()).or().like(Institution::getNameChn, institutionDto.getNameChn()));
        List<Institution> list = this.institutionMapper.selectList(wrapper);
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(InstitutionDto institutionDto) {
        LambdaQueryWrapper<Institution> wrapper = new LambdaQueryWrapper();
        wrapper.and(wrapper_ ->
                wrapper_.like(Institution::getName, institutionDto.getName()).or().like(Institution::getNameChn, institutionDto.getNameChn()));
        List<Institution> list = this.institutionMapper.selectList(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(institutionDto.getId());
    }

    @Override
    public List<CharacterVo> getCharacterDatas(CharacterDto data, Page page) {
        return characterService.getCharacters(data, page);
    }

    @Override
    public void batchAddCharacter(List<CharacterDto> characterDtos) {
        if (GeneralTool.isEmpty(characterDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (CharacterDto characterDto : characterDtos) {
            characterDto.setFkTableName(TableEnum.INSTITUTION.key);
        }
        characterService.batchAdd(characterDtos);
    }

    @Override
    public Map<Long, String> getInstitutionNamesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<Institution> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Institution::getId, ids);
        List<Institution> institutions = institutionMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutions)) {
            return map;
        }
        for (Institution institution : institutions) {
            String name = GeneralTool.isEmpty(institution.getName()) ? "" : institution.getName();
            StringBuilder sb = new StringBuilder(name);
            if (GeneralTool.isNotEmpty(institution.getNameChn())) {
                sb.append("（");
                sb.append(institution.getNameChn());
                sb.append("）");
            }
            map.put(institution.getId(), sb.toString());
        }
        return map;
    }

    /**
     * feign调用 根据学校id查找学校国家
     *
     * @Date 18:08 2021/7/13
     * <AUTHOR>
     */
    @Override
    public Map<Long, Long> getCountryIdByInstitutionId(Set<Long> institutionIdSet) {
        Map<Long, Long> map = new HashMap<>();
        if (GeneralTool.isEmpty(institutionIdSet)) {
            return map;
        }
        LambdaQueryWrapper<Institution> wrapper = new LambdaQueryWrapper();
        wrapper.in(Institution::getId, institutionIdSet);
        List<Institution> institutions = institutionMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutions)) {
            return map;
        }
        for (Institution institution : institutions) {
            map.put(institution.getId(), institution.getFkAreaCountryId());
        }
        return map;
    }

    /**
     * feign调用 根据学校id查找学校名字
     *
     * @Date 16:19 2021/5/24
     * <AUTHOR>
     */
    @Override
    public String getInstitutionNamesById(Long id) {
        return institutionMapper.getInstitutionNameById(id);
    }

    @Override
    public List<Map<String, Object>> getDataLevelSelect() {
        List<Map<String, Object>> maps = ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.DATA_LEVEL);
        for (Map<String, Object> map : maps) {
            map.get("value");
        }
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.DATA_LEVEL);
    }

    /**
     * feign调用 根据学校ids查找国家名字
     *
     * @Date 10:33 2021/8/26
     * <AUTHOR>
     */
    @Override
    public Map<Long, String> getCountryNamesByInstitutionIds(Set<Long> institutionIds) {
        List<InstitutionVo> institutionVos = institutionMapper.getInstitutionCountryInfoByInstitutionIds(institutionIds);
        Map<Long, String> map = new HashMap<>();
        for (InstitutionVo institutionVo : institutionVos) {
            map.put(institutionVo.getId(), institutionVo.getFkAreaCountryName());
        }
        return map;
    }

    /**
     * 根据学校id获取学校国家下拉框数据
     *
     * @return
     * @
     */
    @Override
    public List<BaseSelectEntity> getCountryByInstitutionIdSelect(Long id) {
        return institutionMapper.getCountryByInstitutionIdSelect(id);
    }

    @Override
    public List<BaseSelectEntity> getInstitutionSfList() {
        return institutionMapper.getInstitutionSfList();
    }


    @Override
    public Map<Long, Institution> getInstitutionByIds(Set<Long> ids) {
        LambdaQueryWrapper<Institution> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(Institution::getId, ids);
        return institutionMapper.selectList(lambdaQueryWrapper).stream().collect(Collectors.toMap(Institution::getId, d -> d));

    }

    @Override
    public List<BaseSelectEntity> getInstitutionByName(InstitutionByNameDto institutionByNameDto) {
//        if (GeneralTool.isEmpty(institutionByNameDto.getInstitutionName()) && GeneralTool.isEmpty(institutionByNameDto.getInstitutionIds())){
//            ArrayList<BaseSelectEntity> list = new ArrayList<>();
//            return list;
//        }
        if (GeneralTool.isNotEmpty(institutionByNameDto.getInstitutionName())) {
            institutionByNameDto.setInstitutionName(institutionByNameDto.getInstitutionName().toLowerCase());
        }
        List<BaseSelectEntity> institutionIds = institutionMapper.getInstitutionByName(institutionByNameDto);
        return institutionIds;
    }

    @Override
    public CaseStudyResultsDto getCaseStudyResults(CaseStudyQueryDto caseStudyQueryDto) {
        CaseStudyResultsDto resultsVo = new CaseStudyResultsDto();
        if (GeneralTool.isNotEmpty(caseStudyQueryDto.getInstitutionIds())) {
            List<CaseStudyResultsDto.Statistics> s1 = institutionMapper.getRankingTypeCount(caseStudyQueryDto.getInstitutionIds());
            resultsVo.setSchoolBackGroundSuc(s1);
        }
        resultsVo.setProfessionalBackGroundSuc(courseService.getCourseStatic(caseStudyQueryDto.getCourseIds()));
        return resultsVo;
    }

    @Override
    public Set<Long> getLikeInstitutionIds(InstitutionApplicationStaticsDto institutionApplicationStaticsDto) {
        return institutionMapper.getLikeInstitutionIds(institutionApplicationStaticsDto);
    }

    @Override
    public List<Long> getInstitutionIdsByKeyword(String keyword) {
        return institutionMapper.getInstitutionIdsByKeyword(keyword);
    }

    @Override
    public List<InstitutionApplicationStatisticsVo> getInstitutionDtoList(InstitutionApplicationStaticsQueryBo staticsQueryBo) {
        //获取所有绑定学校的提供商
        List<ApplicationStatisticsProviderVo> providerList = institutionMapper.getInstitutionProviderList(staticsQueryBo);

        if (GeneralTool.isNotEmpty(staticsQueryBo.getFkInstitutionProviderName())) {
            Set<Long> fkInstitutionIds = providerList.stream().map(ApplicationStatisticsProviderVo::getFkInstitutionId).collect(Collectors.toSet());
            staticsQueryBo.setFkInstitutionIds(fkInstitutionIds);
        }
        //获取学校列表
        List<InstitutionApplicationStatisticsVo> institutionList = institutionMapper.getInstitutionDtoList(staticsQueryBo);
        institutionList.stream().forEach(d -> {
            List<ApplicationStatisticsProviderVo> list = new ArrayList<>();
            for (ApplicationStatisticsProviderVo p : providerList) {
                if (d.getFkInstitutionId().equals(p.getFkInstitutionId())) {
                    list.add(p);
                }
            }
            d.setProviderList(list);
        });
        return institutionList;
    }

    /**
     * 有新闻数据的 学校下拉框数据包括简称
     *
     * @Date 11:50 2023/7/11
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getNewsInstitutionSfList() {
        return institutionMapper.getNewsInstitutionSfList();
    }

    /**
     * 根据学校ids 查找学校对象map
     *
     * @Date 18:22 2023/12/18
     * <AUTHOR>
     */
    @Override
    public Map<Long, InstitutionVo> getInstitutionDtoMapByIds(Set<Long> institutionIdSet) {
        Map<Long, InstitutionVo> map = new HashMap<>();
        if (GeneralTool.isEmpty(institutionIdSet)) {
            return map;
        }
        List<Institution> institutionList = this.institutionMapper.selectList(Wrappers.<Institution>query().lambda().in(Institution::getId, institutionIdSet));
        if (GeneralTool.isEmpty(institutionList)) {
            return map;
        }
        return institutionList.stream().collect(Collectors.toMap(Institution::getId, institutionDto -> BeanCopyUtils.objClone(institutionDto, InstitutionVo::new)));
    }

    @Override
    public Map<Long, InstitutionVo> getInstitutionDtoByIds(Set<Long> institutionIdSet) {
        Map<Long, InstitutionVo> map = new HashMap<>();
        if (GeneralTool.isEmpty(institutionIdSet)) {
            return map;
        }
        List<Institution> institutionList = this.institutionMapper.selectList(Wrappers.<Institution>query().lambda().in(Institution::getId, institutionIdSet));
        if (GeneralTool.isEmpty(institutionList)) {
            return map;
        }
        //查询国家名称
        Set<Long> fkAreaCountryIds = institutionList.stream().map(Institution::getFkAreaCountryId).collect(Collectors.toSet());
        Map<Long, String> countryNamesMap = areaCountryService.getCountryNamesByIds(fkAreaCountryIds);
        Set<Long> fkAreaStateIds = institutionList.stream().map(Institution::getFkAreaStateId).collect(Collectors.toSet());
        Map<Long, String> stateNamesMap = areaStateService.getStateNamesByIds(fkAreaStateIds);
        List<InstitutionVo> institutionVos = BeanCopyUtils.copyListProperties(institutionList, InstitutionVo::new);
        for (InstitutionVo dto : institutionVos) {
            String name = GeneralTool.isEmpty(dto.getName()) ? "" : dto.getName();
            StringBuilder sb = new StringBuilder(name);
            if (GeneralTool.isNotEmpty(dto.getNameChn())) {
                sb.append("（");
                sb.append(dto.getNameChn());
                sb.append("）");
            }
            dto.setFullName(sb.toString());
            dto.setFkAreaCountryName(countryNamesMap.get(dto.getFkAreaCountryId()));
            dto.setFkAreaStateName(stateNamesMap.get(dto.getFkAreaStateId()));
        }
        return institutionVos.stream().collect(Collectors.toMap(InstitutionVo::getId, Function.identity()));
    }

    @Override
    public List<InstitutionVo> getInstitutionByCountryIds(Set<Long> fkAreaCountryIds) {
        if (GeneralTool.isEmpty(fkAreaCountryIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Institution> wrapper = Wrappers.lambdaQuery();
        // 激活的
        wrapper.eq(Institution::getIsActive, 1);
        wrapper.in(Institution::getFkAreaCountryId, fkAreaCountryIds);
        List<Institution> institutionList = this.institutionMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutionList)) {
            return Collections.emptyList();
        }
        return BeanCopyUtils.copyListProperties(institutionList, InstitutionVo::new);
    }

    /**
     * AI获取学校信息
     *
     * @param aiInstitutionDto
     * @return
     */
    @Override
    public List<AiInstitutionVo> getAiInstitutionInfo(AiInstitutionDto aiInstitutionDto) {
        List<AiInstitutionInfoVo> aiInstitutionInfoVoList =  institutionMapper.getAiInstitutionInfo(aiInstitutionDto);
        if (GeneralTool.isEmpty(aiInstitutionInfoVoList)) {
            throw new GetServiceException("找不到该名字的学校");
        }
        List<AiInstitutionVo> aiInstitutionVoList = new ArrayList<>();
        List<Long> institutionIds = aiInstitutionInfoVoList.stream().map(AiInstitutionInfoVo::getId).collect(Collectors.toList());
        List<AiCourseVo> aiCourseVoList = institutionMapper.getAiCourseInfo(institutionIds);
        Map<Long, List<AiCourseVo>> map = aiCourseVoList.stream().collect(Collectors.groupingBy(AiCourseVo::getInstitutionId));
        for (AiInstitutionInfoVo aiInstitutionInfoVo : aiInstitutionInfoVoList) {
            AiInstitutionVo aiInstitutionVo = new AiInstitutionVo();
            aiInstitutionVo.setAiInstitutionInfo(aiInstitutionInfoVo);
            aiInstitutionVo.setAiCourseVoList(map.get(aiInstitutionInfoVo.getId()));
            aiInstitutionVoList.add(aiInstitutionVo);
        }
        return aiInstitutionVoList;
    }

}
