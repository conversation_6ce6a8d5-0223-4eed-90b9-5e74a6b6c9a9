package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.institutioncenter.vo.InstitutionAppFeeVo2;
import com.get.institutioncenter.entity.InstitutionAppFee2;
import com.get.institutioncenter.dto.InstitutionAppFeeDto2;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionAppFeeMapper2 extends BaseMapper<InstitutionAppFee2> {


    List<InstitutionAppFeeVo2> getWcInstitutionAppFeeList(IPage<InstitutionAppFeeVo2> ipage,
                                                          @Param("schoolName") String schoolName, @Param("fkCountryId") Long fkCountryId);

    List<InstitutionAppFeeVo2> getWcInstitutionAppFeeDatas(IPage<InstitutionAppFeeVo2> ipage,
                                                           @Param("data") InstitutionAppFeeDto2 data,
                                                           @Param("fkInstitutionIds")List<Long> institutionIds);
}