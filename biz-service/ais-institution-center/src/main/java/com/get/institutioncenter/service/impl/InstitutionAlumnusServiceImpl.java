package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionAlumnusMapper;
import com.get.institutioncenter.dao.InstitutionMapper;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.InstitutionAlumnusVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.InstitutionAlumnus;
import com.get.institutioncenter.entity.MediaAndAttached;
import com.get.institutioncenter.service.IInstitutionAlumnusService;
import com.get.institutioncenter.service.IInstitutionService;
import com.get.institutioncenter.service.IMediaAndAttachedService;
import com.get.institutioncenter.service.ITranslationMappingService;
import com.get.institutioncenter.dto.InstitutionAlumnusDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 15:31
 * @Description:
 **/
@Service
public class InstitutionAlumnusServiceImpl extends BaseServiceImpl<InstitutionAlumnusMapper, InstitutionAlumnus> implements IInstitutionAlumnusService {
    @Resource
    private InstitutionAlumnusMapper institutionAlumnusMapper;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    @Lazy
    private IInstitutionService institutionService;
    @Resource
    private ITranslationMappingService translationMappingService;

    @Override
    public List<InstitutionAlumnusVo> datas(InstitutionAlumnusDto institutionAlumnusDto, Page page) {
        QueryWrapper<InstitutionAlumnus> wrapper = new QueryWrapper();
        if (GeneralTool.isNotEmpty(institutionAlumnusDto)) {
            if (GeneralTool.isNotEmpty(institutionAlumnusDto.getFkInstitutionId())) {
                wrapper.lambda().eq(InstitutionAlumnus::getFkInstitutionId, institutionAlumnusDto.getFkInstitutionId());
            }
            if (GeneralTool.isNotEmpty(institutionAlumnusDto.getKeyWord())) {
                wrapper.lambda().like(InstitutionAlumnus::getName, institutionAlumnusDto.getKeyWord());
            }
            //example.orderBy("viewOrder").desc().orderBy("name");
//            wrapper.last(" order by IFNULL(view_order,0) DESC,CONVERT(name USING gbk)");
            wrapper.orderByDesc("IFNULL(view_order,0)");
            wrapper.orderByAsc("CONVERT(name USING gbk)");
        }
        //获取分页数据
        //获取分页数据
        IPage<InstitutionAlumnus> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionAlumnus> institutionAlumnuss = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<InstitutionAlumnusVo> convertDatas = new ArrayList<>();

        //学校ids
        Set<Long> institutionIds = institutionAlumnuss.stream().map(InstitutionAlumnus::getFkInstitutionId).collect(Collectors.toSet());
        //根据学校ids获取名称
        Map<Long, String> institutionNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionIds)) {
            institutionNamesByIds = institutionService.getInstitutionNamesByIds(institutionIds);
        }

        for (InstitutionAlumnus institutionAlumnus : institutionAlumnuss) {
            InstitutionAlumnusVo institutionAlumnusVo = BeanCopyUtils.objClone(institutionAlumnus, InstitutionAlumnusVo::new);
            if (GeneralTool.isNotEmpty(institutionAlumnusVo.getFkInstitutionId())) {
                institutionAlumnusVo.setInstitutionName(institutionNamesByIds.get(institutionAlumnusVo.getFkInstitutionId()));
            }
            convertDatas.add(institutionAlumnusVo);
        }
        return convertDatas;
    }

    @Override
    public Long addInstitutionAlumnus(InstitutionAlumnusDto institutionAlumnusDto) {
        InstitutionAlumnus institutionAlumnus = BeanCopyUtils.objClone(institutionAlumnusDto, InstitutionAlumnus::new);
        utilService.updateUserInfoToEntity(institutionAlumnus);
        int i = institutionAlumnusMapper.insertSelective(institutionAlumnus);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return institutionAlumnus.getId();
    }

    @Override
    public InstitutionAlumnusVo findInstitutionAlumnusById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionAlumnus institutionAlumnus = institutionAlumnusMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionAlumnus)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionAlumnusVo institutionAlumnusVo = BeanCopyUtils.objClone(institutionAlumnus, InstitutionAlumnusVo::new);
        String tableName = "m_institution_alumnus";
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(tableName);
        attachedVo.setFkTableId(id);
        List<MediaAndAttachedVo> mediaAndAttachedVo = attachedService.getMediaAndAttachedDto(attachedVo);
        institutionAlumnusVo.setMediaAndAttachedDtos(mediaAndAttachedVo);
        if (GeneralTool.isNotEmpty(institutionAlumnusVo.getFkInstitutionId())) {
            institutionAlumnusVo.setInstitutionName(institutionMapper.getInstitutionNameById(institutionAlumnusVo.getFkInstitutionId()));
        }
        institutionAlumnusVo.setFkTableName(TableEnum.INSTITUTION_ALUMNUS.key);
        return institutionAlumnusVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        //TODO 改过
        //InstitutionAlumnus institutionAlumnus = findInstitutionAlumnusById(id);
        InstitutionAlumnusVo institutionAlumnus = findInstitutionAlumnusById(id);
        if (institutionAlumnus == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = institutionAlumnusMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        //同时删除该表id下的所有媒体附件
        List<MediaAndAttached> mediaAndAttachedByTableIds = attachedService.findMediaAndAttachedByTableId(id, TableEnum.INSTITUTION_ALUMNUS.key, null);
        for (MediaAndAttached mediaAndAttached : mediaAndAttachedByTableIds) {
            attachedService.deleteMediaAttached(mediaAndAttached.getId());
        }
        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_ALUMNUS.key, id);
    }

    @Override
    public InstitutionAlumnusVo updateInstitutionAlumnus(InstitutionAlumnusDto institutionAlumnusDto) {
        if (institutionAlumnusDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(institutionAlumnusDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionAlumnus rs = institutionAlumnusMapper.selectById(institutionAlumnusDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionAlumnus institutionAlumnus = BeanCopyUtils.objClone(institutionAlumnusDto, InstitutionAlumnus::new);
        utilService.updateUserInfoToEntity(institutionAlumnus);
        institutionAlumnusMapper.updateById(institutionAlumnus);
        return findInstitutionAlumnusById(institutionAlumnus.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addInstitutionAlumnusMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            String tableName = "m_institution_alumnus";
            mediaAndAttachedDto.setFkTableName(tableName);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<Map<String, Object>> findMediaAndAttachedType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.INSTITUTIONALUMNUS);
    }

    @Override
    public List<MediaAndAttachedVo> getInstitutionAlumnusMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.INSTITUTION_ALUMNUS.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }
}
