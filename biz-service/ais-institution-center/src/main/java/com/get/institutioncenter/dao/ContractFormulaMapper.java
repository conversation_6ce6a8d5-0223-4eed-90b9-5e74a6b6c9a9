package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.institutioncenter.entity.ContractFormula;
import com.get.institutioncenter.dto.ContractFormulaDto;
import com.get.institutioncenter.dto.InstitutionStudentOfferItemDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@Mapper
public interface ContractFormulaMapper extends BaseMapper<ContractFormula>,GetMapper<ContractFormula> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContractFormula record);

    int insert(ContractFormula record);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param [institutionProviderId]
     * <AUTHOR>
     */
    Integer getMaxViewOrder(@Param("institutionProviderId") Long institutionProviderId);

    /**
     * @return java.util.List<com.get.institutioncenter.entity.ContractFormula>
     * @Description: 查询符合学习计划的公式
     * @Param [companyId, studentOfferItem]
     * <AUTHOR>
     **/
    List<ContractFormula> getContractFormulasByOfferItem(@Param("companyId") Long companyId, @RequestParam(value = "areaCountryId") Long areaCountryId, @Param("studentOfferItem") InstitutionStudentOfferItemDto studentOfferItem);

    /**
     * 校验 合同中是否有对应的学校提供商绑定
     *
     * @Date 11:16 2021/4/16
     * <AUTHOR>
     */
    Boolean checkProviderInfoIsEmptyByProviderId(Long providerId);

    List<ContractFormula> getContractFormulas(IPage<ContractFormula> pages,
                                              @Param("contractFormulaDto") ContractFormulaDto contractFormulaDto,
                                              @Param("companyIds") List<Long> companyIds);

}