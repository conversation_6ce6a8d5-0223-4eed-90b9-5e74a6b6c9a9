package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaInstitutionFacultyMapper;
import com.get.institutioncenter.entity.ContractFormulaInstitutionFaculty;
import com.get.institutioncenter.service.IContractFormulaInstitutionFacultyService;
import com.get.institutioncenter.dto.ContractFormulaInstitutionFacultyDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/26 14:59
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaInstitutionFacultyServiceImpl extends BaseServiceImpl<ContractFormulaInstitutionFacultyMapper, ContractFormulaInstitutionFaculty> implements IContractFormulaInstitutionFacultyService {
    @Resource
    private ContractFormulaInstitutionFacultyMapper contractFormulaInstitutionFacultyMapper;
    @Resource
    private UtilService utilService;

    @Override
    public Long addContractFormulaInstitutionFaculty(ContractFormulaInstitutionFacultyDto contractFormulaInstitutionFacultyDto) {
        if (GeneralTool.isEmpty(contractFormulaInstitutionFacultyDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContractFormulaInstitutionFaculty contractFormulaInstitutionFaculty = BeanCopyUtils.objClone(contractFormulaInstitutionFacultyDto, ContractFormulaInstitutionFaculty::new);
        utilService.updateUserInfoToEntity(contractFormulaInstitutionFaculty);
        contractFormulaInstitutionFacultyMapper.insertSelective(contractFormulaInstitutionFaculty);
        return contractFormulaInstitutionFaculty.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaInstitutionFaculty> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaInstitutionFaculty::getFkContractFormulaId, contractFormulaId);
        contractFormulaInstitutionFacultyMapper.delete(wrapper);
    }

    @Override
    public List<Long> getFacultyIdListByFkid(Long contractFormulaId) {
        return contractFormulaInstitutionFacultyMapper.getFacultyIdListByFkid(contractFormulaId);
    }
}
