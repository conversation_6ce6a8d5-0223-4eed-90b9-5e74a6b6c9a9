package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionProviderCompanyMapper;
import com.get.institutioncenter.entity.InstitutionProviderCompany;
import com.get.institutioncenter.service.IInstitutionProviderCompanyService;
import com.get.institutioncenter.dto.InstitutionProviderCompanyDto;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/21
 * @TIME: 16:19
 * @Description: 学校提供商管理-公司绑定配置实现类
 **/
@Service
public class InstitutionProviderCompanyServiceImpl extends BaseServiceImpl<InstitutionProviderCompanyMapper, InstitutionProviderCompany> implements IInstitutionProviderCompanyService {
    @Resource
    private InstitutionProviderCompanyMapper providerCompanyMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private UtilService utilService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editProviderCompanyRelation(List<InstitutionProviderCompanyDto> providerCompanyVos) {
        if (GeneralTool.isEmpty(providerCompanyVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        List<Long> collect = providerCompanyVos.stream()
                .filter(Objects::nonNull)
                .map(InstitutionProviderCompanyDto::getFkCompanyId).collect(Collectors.toList());

        //移除空元素
        collect.removeIf(Objects::isNull);

        //删除原先记录
        List<Long> companyIds = SecureUtil.getCompanyIds();
        Long providerId = providerCompanyVos.get(0).getFkInstitutionProviderId();
        LambdaQueryWrapper<InstitutionProviderCompany> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionProviderCompany::getFkInstitutionProviderId, providerId).in(InstitutionProviderCompany::getFkCompanyId,companyIds);
        providerCompanyMapper.delete(wrapper);

        if (GeneralTool.isNotEmpty(collect)) {
            List<InstitutionProviderCompany> providerCompanies = providerCompanyVos.stream()
                    .map(institutionProviderCompanyVo ->
                            BeanCopyUtils.objClone(institutionProviderCompanyVo, InstitutionProviderCompany::new)).collect(Collectors.toList());
            //循环插入
            providerCompanies.forEach(institutionProviderCompany -> {
                utilService.setCreateInfo(institutionProviderCompany);
                providerCompanyMapper.insertSelective(institutionProviderCompany);
            });

        }
    }

    @Override
    public Long addRelation(InstitutionProviderCompanyDto providerCompanyVo) {
        InstitutionProviderCompany providerCompany = BeanCopyUtils.objClone(providerCompanyVo, InstitutionProviderCompany::new);
        utilService.updateUserInfoToEntity(providerCompany);
        providerCompanyMapper.insertSelective(providerCompany);
        return providerCompany.getId();
    }

    @Override
    public List<CompanyTreeVo> getProviderCompanyRelation(Long providerId) {
        if (GeneralTool.isEmpty(providerId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = getCompanyTreeDto();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }
        //获取中间表数据
        List<InstitutionProviderCompany> relation = getRelationsByProviderId(providerId);
        setFlag(companyTreeVo, relation);
        return getTreeList(companyTreeVo);
    }

    @Override
    public List<Long> getRelationByCompanyId(List<Long> companyIds) {
        if (GeneralTool.isEmpty(companyIds)) {
            return null;
        }
        LambdaQueryWrapper<InstitutionProviderCompany> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionProviderCompany::getFkCompanyId, companyIds);
        List<InstitutionProviderCompany> providerCompanies = providerCompanyMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(providerCompanies)) {
            return null;
        }
        return providerCompanies.stream().map(InstitutionProviderCompany::getFkInstitutionProviderId).collect(Collectors.toList());
    }

    @Override
    public List<Long> getRelationByProviderId(Long providerId) {
        if (GeneralTool.isEmpty(providerId)) {
            return null;
        }
        List<InstitutionProviderCompany> relations = getRelationsByProviderId(providerId);
        if (GeneralTool.isEmpty(relations)) {
            return null;
        }
        return relations.stream().map(InstitutionProviderCompany::getFkCompanyId).collect(Collectors.toList());
    }

    @Override
    public Map<Long, LinkedList<Long>> getRelationByProviderIds(Set<Long> providerIds) {
        Map<Long, LinkedList<Long>> map = new HashMap<>();
        if (GeneralTool.isEmpty(providerIds)) {
            return map;
        }
        List<InstitutionProviderCompany> relations = providerCompanyMapper.getRelationsByProviderIds(providerIds);
        if (GeneralTool.isEmpty(relations)) {
            return map;
        }

        for (InstitutionProviderCompany relation : relations) {
            //如果集合包含这个提供商id,则往原来的数据添加公司id
            if (map.containsKey(relation.getFkInstitutionProviderId())) {
                LinkedList<Long> beforeRelationIds = map.get(relation.getFkInstitutionProviderId());
                beforeRelationIds.add(relation.getFkCompanyId());
                map.put(relation.getFkInstitutionProviderId(), beforeRelationIds);
                continue;
            }
            //如果没有包含,则往里面添加新数据
            LinkedList<Long> relationSet = new LinkedList<>();
            relationSet.add(relation.getFkCompanyId());
            map.put(relation.getFkInstitutionProviderId(), relationSet);
        }
        return map;
    }

    @Override
    public List<CompanyVo> getProviderCompanyName(Long id) {
        if (id == null){
            return null;
        }

        List<Long> companyIds = SecureUtil.getCompanyIds();
        return providerCompanyMapper.getCompanyNameById(id, companyIds);
    }

    @Override
    public List<Long> getCompanyIdByContractId(Long contractId) {
        if (GeneralTool.isEmpty(contractId)) {
            return null;
        }
        return providerCompanyMapper.getCompanyIdByContractId(contractId);
    }

    private void setFlag(List<CompanyTreeVo> companyTreeVo, List<InstitutionProviderCompany> relation) {
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (InstitutionProviderCompany providerCompany : relation) {
                if (treeDto.getId().longValue() == (providerCompany.getFkCompanyId().longValue())) {
                    treeDto.setFlag(true);
                }
            }
        }
    }

    private List<InstitutionProviderCompany> getRelationsByProviderId(Long providerId) {
        LambdaQueryWrapper<InstitutionProviderCompany> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionProviderCompany::getFkInstitutionProviderId, providerId);
        return providerCompanyMapper.selectList(wrapper);
    }

    private List<CompanyTreeVo> getCompanyTreeDto() {
        //111111
        Result<List<CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        return result.getData();
    }


    private List<CompanyTreeVo> getTreeList(List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                //获取父节点的部门信息
                resultList.add(entity);
            }
        }
        //假如没有父节点
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点
            CompanyTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(CompanyTreeVo::getFkParentCompanyId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点
                List<CompanyTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentCompanyId().equals(minTreeNode.getFkParentCompanyId()) &&
                                treeDto.getId().longValue() != (minTreeNode.getId().longValue())).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        // 获取每个顶层元素的子数据集合
        for (CompanyTreeVo entity : resultList) {
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        return resultList;
    }

    private List<CompanyTreeVo> getSubList(Long id, List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> childList = new ArrayList<>();
        // 子集的直接子对象
        for (CompanyTreeVo entity : entityList) {
            if (id.longValue() == (entity.getFkParentCompanyId())) {
                //获取子节点的部门信息
                childList.add(entity);
            }
        }
        // 子集的间接子对象
        for (CompanyTreeVo entity : childList) {
            //递归调用
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }

}
