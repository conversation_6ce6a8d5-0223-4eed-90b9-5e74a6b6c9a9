package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.*;
import com.get.institutioncenter.dto.TranslationDto;
import com.get.institutioncenter.vo.TranslationMappingVo;
import com.get.institutioncenter.entity.*;
import com.get.institutioncenter.service.ITranslationMappingService;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/12/16
 * @TIME: 14:07
 * @Description:
 **/
@Service
public class TranslationMappingServiceImpl extends BaseServiceImpl<TranslationMappingMapper, InstitutionTranslationMapping> implements ITranslationMappingService {
    @Resource
    private TranslationMappingMapper translationMappingMapper;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private InstitutionFacultyMapper institutionFacultyMapper;
    @Resource
    private InstitutionCourseMapper institutionCourseMapper;
    @Resource
    private InstitutionFaqMapper institutionFaqMapper;
    @Resource
    private InstitutionAlumnusMapper institutionAlumnusMapper;
    @Resource
    private InstitutionInfoMapper institutionInfoMapper;
    @Resource
    private TranslationMapper translationMapper;
    @Resource
    private AreaCountryMapper areaCountryMapper;
    @Resource
    private InstitutionTypeMapper institutionTypeMapper;
    @Resource
    private MajorLevelMapper majorLevelMapper;
    @Resource
    private CourseTypeMapper courseTypeMapper;
    @Resource
    private InfoTypeMapper infoTypeMapper;
    @Resource
    private AreaCountryInfoMapper areaCountryInfoMapper;
    @Resource
    private NewsTypeMapper newsTypeMapper;
    @Resource
    private NewsMapper newsMapper;
    @Resource
    private AreaCountryInfoTypeMapper areaCountryInfoTypeMapper;
    @Resource
    private AreaCityMapper areaCityMapper;
    @Resource
    private AreaCityInfoMapper areaCityInfoMapper;
    @Resource
    private AreaCityDivisionMapper areaCityDivisionMapper;
    @Resource
    private InstitutionZoneMapper institutionZoneMapper;
    @Resource
    private AreaCityInfoTypeMapper areaCityInfoTypeMapper;
    @Resource
    private CourseTypeGroupMapper courseTypeGroupMapper;
    @Resource
    private AreaStateMapper areaStateMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private AreaRegionMapper areaRegionMapper;


    @Mapper
    private InstitutionIengineScoreMapper institutionIengineScoreMapper;

    @Override
    public List<TranslationMappingVo> getTranslationMappingDtos(TranslationDto translationDto) {
        if (GeneralTool.isEmpty(translationDto.getType()) || GeneralTool.isEmpty(translationDto.getLanguageCode())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        LambdaQueryWrapper<InstitutionTranslationMapping> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(translationDto)) {
            if (GeneralTool.isNotEmpty(translationDto.getFkTableName())) {
                wrapper.eq(InstitutionTranslationMapping::getFkTableName, translationDto.getFkTableName());
            }
        }
        wrapper.orderByDesc(InstitutionTranslationMapping::getViewOrder);
        Map<String, Object> map = fromJavaBean(translationDto.getFkTableName(), translationDto.getFkTableId());
        List<InstitutionTranslationMapping> translationMappings = translationMappingMapper.selectList(wrapper);
        List<TranslationMappingVo> translationMappingVos = translationMappings.stream().map(TranslationMapping -> BeanCopyUtils.objClone(TranslationMapping, TranslationMappingVo::new)).collect(Collectors.toList());
        if (translationDto.getType() == 1 && translationDto.getLanguageCode().equals(ProjectKeyEnum.ZH_CN.key)) {
            for (TranslationMappingVo translationMappingVo : translationMappingVos) {
                for (String key : map.keySet()) {
                    if (key.equals(translationMappingVo.getFkColumnName())) {
                        /*translationMappingVo.setStandardContent(map.get(key).toString());
                        translationDto.setFkTranslationMappingId(translationMappingVo.getId());
                        translationMappingVo.setTranslationContent(translationMapper.getTranslation(translationDto));*/
                        translationDto.setFkTranslationMappingId(translationMappingVo.getId());
                        if (GeneralTool.isNotEmpty(map.get(key))) {
                            translationMappingVo.setTranslationContent(map.get(key).toString());
                        }
                    }
                }
            }
        } else {
            for (TranslationMappingVo translationMappingVo : translationMappingVos) {
                for (String key : map.keySet()) {
                    if (key.equals(translationMappingVo.getFkColumnName())) {
                        translationDto.setFkTranslationMappingId(translationMappingVo.getId());
                        translationMappingVo.setTranslationContent(translationMapper.getTranslation(translationDto));
                    }
                }
            }
        }

        return translationMappingVos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateTranslations(List<TranslationDto> translationDtos) {
        if (GeneralTool.isEmpty(translationDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (translationDtos.get(0).getLanguageCode().equals(ProjectKeyEnum.ZH_CN.key)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("Simplified_Chinese_not_translation"));
        }
        for (TranslationDto translationDto : translationDtos) {
            LambdaQueryWrapper<InstitutionTranslation> wrapper = new LambdaQueryWrapper();
            if (GeneralTool.isNotEmpty(translationDto)) {
                if (GeneralTool.isNotEmpty(translationDto.getFkTableName())) {
                    wrapper.eq(InstitutionTranslation::getFkTableName, translationDto.getFkTableName());
                }
                if (GeneralTool.isNotEmpty(translationDto.getFkTableId())) {
                    wrapper.eq(InstitutionTranslation::getFkTableId, translationDto.getFkTableId());
                }
                if (GeneralTool.isNotEmpty(translationDto.getLanguageCode())) {
                    wrapper.eq(InstitutionTranslation::getLanguageCode, translationDto.getLanguageCode());
                }
                if (GeneralTool.isNotEmpty(translationDto.getFkTranslationMappingId())) {
                    wrapper.eq(InstitutionTranslation::getFkTranslationMappingId, translationDto.getFkTranslationMappingId());
                }
            }
            int i = translationMapper.delete(wrapper);
            if (i < 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
            InstitutionTranslation translation = BeanCopyUtils.objClone(translationDto, InstitutionTranslation::new);
            utilService.updateUserInfoToEntity(translation);
            translationMapper.insert(translation);
        }
    }

    @Override
    public List<Map<String, Object>> findLanguageType() {
        return ProjectKeyEnum.enumsTranslation2Arrays(ProjectKeyEnum.LANGUAGE_TYPE);
    }

    /**
     * 删除翻译内容
     *
     * @Date 12:11 2021/8/17
     * <AUTHOR>
     */
    @Override
    public void deleteTranslations(String fkTableName, Long fkTableId) {
        LambdaQueryWrapper<InstitutionTranslation> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionTranslation::getFkTableName, fkTableName).eq(InstitutionTranslation::getFkTableId, fkTableId);
        translationMapper.delete(wrapper);
    }

    private Map<String, Object> fromJavaBean(String fkTableName, Long fkTableId) {
        if (GeneralTool.isEmpty(fkTableName) || GeneralTool.isEmpty(fkTableId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Map<String, Object> map = null;
        if (fkTableName.equals(TableEnum.INSTITUTION.key)) {
            Institution institution = institutionMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(institution)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(institution);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_FACULTY.key)) {
            InstitutionFaculty institutionFaculty = institutionFacultyMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(institutionFaculty)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(institutionFaculty);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_COURSE.key)) {
            InstitutionCourse institutionCourse = institutionCourseMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(institutionCourse)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(institutionCourse);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_FAQ.key)) {
            InstitutionFaq institutionFaq = institutionFaqMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(institutionFaq)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(institutionFaq);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_ALUMNUS.key)) {
            InstitutionAlumnus institutionAlumnus = institutionAlumnusMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(institutionAlumnus)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(institutionAlumnus);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_INFO.key)) {
            InstitutionInfo institutionInfo = institutionInfoMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(institutionInfo)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(institutionInfo);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_COUNTRY.key)) {
            AreaCountry areaCountry = areaCountryMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(areaCountry)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(areaCountry);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_TYPE.key)) {
            InstitutionType institutionType = institutionTypeMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(institutionType)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(institutionType);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_MAJOR_LEVEL.key)) {
            MajorLevel majorLevel = majorLevelMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(majorLevel)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(majorLevel);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_COURSE_TYPE.key)) {
            CourseType courseType = courseTypeMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(courseType)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(courseType);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_INFO_TYPE.key)) {
            InfoType infoType = infoTypeMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(infoType)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(infoType);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_COUNTRY_INFO.key)) {
            AreaCountryInfo infoType = areaCountryInfoMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(infoType)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(infoType);
        } else if (fkTableName.equals(TableEnum.NEWS_TYPE.key)) {
            NewsType infoType = newsTypeMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(infoType)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(infoType);
        } else if (fkTableName.equals(TableEnum.NEWS.key)) {
            News infoType = newsMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(infoType)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(infoType);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_COUNTRY_INFO_TYPE.key)) {
            AreaCountryInfoType areaCountryInfoType = areaCountryInfoTypeMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(areaCountryInfoType)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(areaCountryInfoType);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_AREA_CITY.key)) {
            AreaCity areaCity = areaCityMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(areaCity)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(areaCity);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_AREA_CITY_INFO.key)) {
            AreaCityInfo areaCityInfo = areaCityInfoMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(areaCityInfo)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(areaCityInfo);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_AREA_CITY_INFO_TYPE.key)) {
            AreaCityInfoType areaCityInfoType = areaCityInfoTypeMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(areaCityInfoType)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(areaCityInfoType);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_AREA_CITY_DIVISION.key)) {
            AreaCityDivision areaCityDivision = areaCityDivisionMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(areaCityDivision)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(areaCityDivision);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_ZONE.key)) {
            InstitutionZone institutionZone = institutionZoneMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(institutionZone)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(institutionZone);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key)) {
            CourseTypeGroup courseTypeGroup = courseTypeGroupMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(courseTypeGroup)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(courseTypeGroup);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key)) {
            CourseTypeGroup courseTypeGroup = courseTypeGroupMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(courseTypeGroup)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(courseTypeGroup);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_AREA_STATE.key)) {
            AreaState areaState = areaStateMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(areaState)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(areaState);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_IENGINE_SCORE.key)) {
            InstitutionIengineScore institutionIengineScore = institutionIengineScoreMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(institutionIengineScore)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(institutionIengineScore);
        } else if (fkTableName.equals(TableEnum.INSTITUTION_AREA_REGION.key)) {
            AreaRegion areaRegion = areaRegionMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(areaRegion)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(areaRegion);
        }
        return map;
    }
}
