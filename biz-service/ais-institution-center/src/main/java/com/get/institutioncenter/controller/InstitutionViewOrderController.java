package com.get.institutioncenter.controller;

import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.institutioncenter.vo.InstitutionVo;
import com.get.institutioncenter.vo.InstitutionViewOrderVo;
import com.get.institutioncenter.service.IInstitutionViewOrderService;
import com.get.institutioncenter.dto.InstitutionViewOrderDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/6/8 17:14
 */
@Api(tags = "学校排序管理")
@RestController
@RequestMapping("/institution/institutionViewOrder")
public class InstitutionViewOrderController {


    @Resource
    private IInstitutionViewOrderService iInstitutionViewOrderService;

    @ApiOperation(value = "上移下移", notes = "")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<InstitutionViewOrderDto> institutionViewOrderDtos) {
        iInstitutionViewOrderService.movingOrder(institutionViewOrderDtos);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "上移下移选择", notes = "")
    @PostMapping("movingOrderSelect")
    public ResponseBo movingOrderSelect(@RequestParam("end")int end,@RequestParam("start")int start) {
        iInstitutionViewOrderService.movingOrderSelect(end,start);
        return ResponseBo.ok();
    }





    @ApiOperation(value = "删除", notes = "")
    @PostMapping("delete")
    public ResponseBo movingOrder(@RequestParam("id") Long id) {
        iInstitutionViewOrderService.delete(id);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "列表数据", notes = "")
    @PostMapping("datas")
    public ResponseBo<InstitutionViewOrderVo> datas(@RequestBody SearchBean<InstitutionViewOrderDto> page) {
        List<InstitutionViewOrderVo> datas = iInstitutionViewOrderService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "保存", notes = "")
    @PostMapping("adds")
    public ResponseBo<InstitutionViewOrderVo> adds(@RequestBody List<InstitutionViewOrderDto> institutionViewOrderDtos) {
        iInstitutionViewOrderService.adds(institutionViewOrderDtos);
        return ResponseBo.ok();
    }

    @ApiOperation("学校搜索")
    @GetMapping("searchInstitution")
    public ResponseBo<InstitutionVo> searchInstitution(@RequestParam("fkInstitutionName") String fkInstitutionName) {
        return new ListResponseBo<>(iInstitutionViewOrderService.searchInstitution(fkInstitutionName));
    }

    @ApiOperation("展示类型下拉")
    @GetMapping("getTypeSelect")
    public ResponseBo getTypeSelect() {

        return new ListResponseBo<>(ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.InstitutionType));
    }

}
