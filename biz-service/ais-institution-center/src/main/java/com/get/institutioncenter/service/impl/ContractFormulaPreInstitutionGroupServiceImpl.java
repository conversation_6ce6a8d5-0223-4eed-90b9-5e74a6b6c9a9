package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaPreInstitutionGroupMapper;
import com.get.institutioncenter.entity.ContractFormulaPreInstitutionGroup;
import com.get.institutioncenter.service.IContractFormulaPreInstitutionGroupService;
import com.get.institutioncenter.dto.ContractFormulaInstitutionGroupDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/26 15:02
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaPreInstitutionGroupServiceImpl extends BaseServiceImpl<ContractFormulaPreInstitutionGroupMapper, ContractFormulaPreInstitutionGroup> implements IContractFormulaPreInstitutionGroupService {
    @Resource
    private ContractFormulaPreInstitutionGroupMapper contractFormulaPreInstitutionGroupMapper;
    @Resource
    private UtilService utilService;

    @Override
    public Long addContractFormulaInstitutionGroup(ContractFormulaInstitutionGroupDto contractFormulaInstitutionGroupDto) {
        if (GeneralTool.isEmpty(contractFormulaInstitutionGroupDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContractFormulaPreInstitutionGroup contractFormulaPreInstitutionGroup = BeanCopyUtils.objClone(contractFormulaInstitutionGroupDto, ContractFormulaPreInstitutionGroup::new);
        utilService.updateUserInfoToEntity(contractFormulaPreInstitutionGroup);
        contractFormulaPreInstitutionGroupMapper.insertSelective(contractFormulaPreInstitutionGroup);
        return contractFormulaPreInstitutionGroup.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaPreInstitutionGroup> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaPreInstitutionGroup::getFkContractFormulaId, contractFormulaId);
        contractFormulaPreInstitutionGroupMapper.delete(wrapper);
    }

    @Override
    public List<Long> getGroupIdListByFkid(Long contractFormulaId) {
        return contractFormulaPreInstitutionGroupMapper.getGroupIdListByFkid(contractFormulaId);
    }
}
