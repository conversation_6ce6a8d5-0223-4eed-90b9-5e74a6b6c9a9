package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.redis.cache.GetRedis;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.translation.baidu.api.TransApi;
import com.get.core.translation.baidu.result.TransResult;
import com.get.core.translation.baidu.result.TransVo;
import com.get.core.translation.baidu.utils.HtmlUtils;
import com.get.institutioncenter.dao.*;
import com.get.institutioncenter.dto.*;
import com.get.institutioncenter.entity.*;
import com.get.institutioncenter.service.AsyncCompletions;
import com.get.institutioncenter.service.AsyncTranslationJob;
import com.get.institutioncenter.service.ITranslationService;
import com.get.institutioncenter.utils.KeyWordUtils;
import com.get.institutioncenter.vo.BatchTranslationResultVo;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.get.institutioncenter.service.impl.TranslationServiceImpl.ColumnNameEnum.*;

/**
 * @author: Hardy
 * @create: 2021/6/4 12:40
 * @verison: 1.0
 * @description:
 */
@Service
public class TranslationServiceImpl extends BaseServiceImpl<TranslationMapper, InstitutionTranslation> implements ITranslationService {

    private final Logger log = LoggerFactory.getLogger(this.getClass());

    private final ICount newsCount = new ICount();

    private final ICount institutionInfoCount = new ICount();

    private final ICount areaCountryCount = new ICount();

    private final ICount institutionCount = new ICount();

    private final ICount institutionCourseCount = new ICount();

    private final ICount areaStateCount = new ICount();

    private final ICount areaCityCount = new ICount();

    private final ICount areaCityDiversionCount = new ICount();

    @Resource
    private TranslationMapper translationMapper;
    @Resource
    private NewsMapper newsMapper;
    @Resource
    private TranslationMappingMapper translationMappingMapper;
    @Resource
    private TranslationMappingServiceImpl translationMappingService;
    @Resource
    private InstitutionInfoMapper institutionInfoMapper;
    @Resource
    private AreaCountryMapper areaCountryMapper;
    @Resource
    private AreaStateMapper areaStateMapper;
    @Resource
    private AreaCityMapper areaCityMapper;
    @Resource
    private AreaCityDivisionMapper areaCityDivisionMapper;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private InstitutionCourseMapper institutionCourseMapper;
    @Resource
    private AsyncTranslationJob asyncTranslationJob;
    @Resource
    private AsyncCompletions asyncCompletions;
    @Resource
    private GetRedis getRedis;

    @Async("taskExecutor")
    @Override
    public void batchTranslationNews(TransVo transVo, String createUser) {//设置日期格式
        newsCount.getSumCount().set(0);
        newsCount.getCompletedCount().set(0);
        newsCount.getErrorCount().set(0);
        String languageCode = "";
        if (GeneralTool.isEmpty(transVo.getTo())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if ("zh-cn".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_CN);
            languageCode = "zh-cn";
        }
        if ("en-us".equals(transVo.getTo())) {
            transVo.setTo(TransApi.EN_US);
            languageCode = "en-us";
        }
        if ("zh-hk".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_HK);
            languageCode = "zh-hk";
        }
        if ("zh-tw".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_TW);
            languageCode = "zh-tw";
        }
        List<TranslationDto> translationDtos = new ArrayList<>(10);
        //翻译title字段内容
        doTranslate(transVo, languageCode, translationDtos, ColumnNameEnum.TITLE.key, TableEnum.NEWS.key, createUser);
        //翻译description字段内容
        doTranslate(transVo, languageCode, translationDtos, ColumnNameEnum.DESCRIPTION.key, TableEnum.NEWS.key, createUser);
        //翻译profile字段内容
        doTranslate(transVo, languageCode, translationDtos, ColumnNameEnum.PROFILE.key, TableEnum.NEWS.key, createUser);
        //程序是一边翻译一边插入
        if (translationDtos.size() > 0) {
            translationMappingService.updateTranslations(translationDtos);
        }
        log.info("批量翻译完成");
        newsCount.getSumCount().set(0);
        newsCount.getCompletedCount().set(0);
        newsCount.getErrorCount().set(0);
    }


    @Async("taskExecutor")
    @Override
    public void batchTranslationInstitutionInfos(TransVo transVo, String createUser) {//设置日期格式
        institutionInfoCount.getSumCount().set(0);
        institutionInfoCount.getCompletedCount().set(0);
        institutionInfoCount.getErrorCount().set(0);
        String languageCode = "";
        if (GeneralTool.isEmpty(transVo.getTo())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if ("zh-cn".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_CN);
            languageCode = "zh-cn";
        }
        if ("en-us".equals(transVo.getTo())) {
            transVo.setTo(TransApi.EN_US);
            languageCode = "en-us";
        }
        if ("zh-hk".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_HK);
            languageCode = "zh-hk";
        }
        if ("zh-tw".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_TW);
            languageCode = "zh-tw";
        }
        List<TranslationDto> translationDtos = new ArrayList<>();
        //翻译title字段
        doTranslateInstitutionInfo(transVo, languageCode, translationDtos, ColumnNameEnum.TITLE.key, TableEnum.INSTITUTION_INFO.key, createUser);
        //翻译description字段
        doTranslateInstitutionInfo(transVo, languageCode, translationDtos, ColumnNameEnum.DESCRIPTION.key, TableEnum.INSTITUTION_INFO.key, createUser);
        //翻译profile字段
        doTranslateInstitutionInfo(transVo, languageCode, translationDtos, ColumnNameEnum.PROFILE.key, TableEnum.INSTITUTION_INFO.key, createUser);
        if (translationDtos.size() > 0) {
            translationMappingService.updateTranslations(translationDtos);
        }
        log.info("批量翻译完成");
        institutionInfoCount.getSumCount().set(0);
        institutionInfoCount.getCompletedCount().set(0);
        institutionInfoCount.getErrorCount().set(0);
    }

    @Async("taskExecutor")
    @Override
    public void batchTranslationAreaCountrys(TransVo transVo, String createUser) {//设置日期格式
        //计数
        areaCountryCount.getSumCount().set(0);
        areaCountryCount.getCompletedCount().set(0);
        areaCountryCount.getErrorCount().set(0);
        String languageCode = "";
        if (GeneralTool.isEmpty(transVo.getTo())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if ("zh-cn".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_CN);
            languageCode = "zh-cn";
        }
        if ("en-us".equals(transVo.getTo())) {
            transVo.setTo(TransApi.EN_US);
            languageCode = "en-us";
        }
        if ("zh-hk".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_HK);
            languageCode = "zh-hk";
        }
        if ("zh-tw".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_TW);
            languageCode = "zh-tw";
        }
        List<TranslationDto> translationDtos = new ArrayList<>();
        doTranslateAreaCountrys(transVo, languageCode, translationDtos, ColumnNameEnum.NAME_CHN.key, TableEnum.INSTITUTION_COUNTRY.key, createUser);
        doTranslateAreaCountrys(transVo, languageCode, translationDtos, ColumnNameEnum.CAPITAL.key, TableEnum.INSTITUTION_COUNTRY.key, createUser);
        doTranslateAreaCountrys(transVo, languageCode, translationDtos, ColumnNameEnum.POPULATION.key, TableEnum.INSTITUTION_COUNTRY.key, createUser);
        doTranslateAreaCountrys(transVo, languageCode, translationDtos, ColumnNameEnum.AREA.key, TableEnum.INSTITUTION_COUNTRY.key, createUser);
        doTranslateAreaCountrys(transVo, languageCode, translationDtos, ColumnNameEnum.LANGUAGE.key, TableEnum.INSTITUTION_COUNTRY.key, createUser);
        doTranslateAreaCountrys(transVo, languageCode, translationDtos, ColumnNameEnum.RELIGION.key, TableEnum.INSTITUTION_COUNTRY.key, createUser);
        doTranslateAreaCountrys(transVo, languageCode, translationDtos, ColumnNameEnum.TIME_DIFFERENCE.key, TableEnum.INSTITUTION_COUNTRY.key, createUser);
        doTranslateAreaCountrys(transVo, languageCode, translationDtos, ColumnNameEnum.PRESIDENT.key, TableEnum.INSTITUTION_COUNTRY.key, createUser);
        doTranslateAreaCountrys(transVo, languageCode, translationDtos, ColumnNameEnum.FLAG_MEANING.key, TableEnum.INSTITUTION_COUNTRY.key, createUser);
        doTranslateAreaCountrys(transVo, languageCode, translationDtos, ColumnNameEnum.EMBLEM_MEANING.key, TableEnum.INSTITUTION_COUNTRY.key, createUser);
        doTranslateAreaCountrys(transVo, languageCode, translationDtos, ColumnNameEnum.REMARK.key, TableEnum.INSTITUTION_COUNTRY.key, createUser);
        if (translationDtos.size() > 0) {
            translationMappingService.updateTranslations(translationDtos);
        }
        areaCountryCount.getSumCount().set(0);
        areaCountryCount.getCompletedCount().set(0);
        areaCountryCount.getErrorCount().set(0);
        log.info("批量翻译完成");
    }

    @Async("taskExecutor")
    @Override
    public void batchTranslationInstitutions(TransVo transVo, String createUser) {//设置日期格式
        //计数
        institutionCount.getSumCount().set(0);
        institutionCount.getCompletedCount().set(0);
        institutionCount.getErrorCount().set(0);
        String languageCode = "";
        if (GeneralTool.isEmpty(transVo.getTo())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if ("zh-cn".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_CN);
            languageCode = "zh-cn";
        }
        if ("en-us".equals(transVo.getTo())) {
            transVo.setTo(TransApi.EN_US);
            languageCode = "en-us";
        }
        if ("zh-hk".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_HK);
            languageCode = "zh-hk";
        }
        if ("zh-tw".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_TW);
            languageCode = "zh-tw";
        }
        List<TranslationDto> translationDtos = new ArrayList<>();
        doTranslateInstitutions(transVo, languageCode, translationDtos, ColumnNameEnum.NAME.key, TableEnum.INSTITUTION.key, createUser);
        doTranslateInstitutions(transVo, languageCode, translationDtos, ColumnNameEnum.SHORT_NAME.key, TableEnum.INSTITUTION.key, createUser);
        doTranslateInstitutions(transVo, languageCode, translationDtos, ColumnNameEnum.NATURE.key, TableEnum.INSTITUTION.key, createUser);
        doTranslateInstitutions(transVo, languageCode, translationDtos, ColumnNameEnum.DETAIL.key, TableEnum.INSTITUTION.key, createUser);
        doTranslateInstitutions(transVo, languageCode, translationDtos, ColumnNameEnum.NAME_CHN.key, TableEnum.INSTITUTION.key, createUser);
        doTranslateInstitutions(transVo, languageCode, translationDtos, ColumnNameEnum.NAME_DISPLAY.key, TableEnum.INSTITUTION.key, createUser);
        doTranslateInstitutions(transVo, languageCode, translationDtos, ColumnNameEnum.SHORT_NAME_CHN.key, TableEnum.INSTITUTION.key, createUser);
        doTranslateInstitutions(transVo, languageCode, translationDtos, ColumnNameEnum.ADDRESS.key, TableEnum.INSTITUTION.key, createUser);
        if (translationDtos.size() > 0) {
            translationMappingService.updateTranslations(translationDtos);
        }
        institutionCount.getSumCount().set(0);
        institutionCount.getCompletedCount().set(0);
        institutionCount.getErrorCount().set(0);
        log.info("批量翻译完成");
    }

    @Async("taskExecutor")
    @Override
    public void batchTranslationInstitutionCourses(TransVo transVo, String createUser) {//设置日期格式
        //计数
        institutionCourseCount.getSumCount().set(0);
        institutionCourseCount.getCompletedCount().set(0);
        institutionCourseCount.getErrorCount().set(0);
        String languageCode = "";
        if (GeneralTool.isEmpty(transVo.getTo())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if ("zh-cn".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_CN);
            languageCode = "zh-cn";
        }
        if ("en-us".equals(transVo.getTo())) {
            transVo.setTo(TransApi.EN_US);
            languageCode = "en-us";
        }
        if ("zh-hk".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_HK);
            languageCode = "zh-hk";
        }
        if ("zh-tw".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_TW);
            languageCode = "zh-tw";
        }
        List<TranslationDto> translationDtos = new ArrayList<>();
        doTranslateInstitutionCourses(transVo, languageCode, translationDtos, ColumnNameEnum.NAME.key, TableEnum.INSTITUTION_COURSE.key, createUser);
        doTranslateInstitutionCourses(transVo, languageCode, translationDtos, ColumnNameEnum.START_DATE_NOTE.key, TableEnum.INSTITUTION_COURSE.key, createUser);
        doTranslateInstitutionCourses(transVo, languageCode, translationDtos, ColumnNameEnum.APPLY_DATE_NOTE.key, TableEnum.INSTITUTION_COURSE.key, createUser);
        doTranslateInstitutionCourses(transVo, languageCode, translationDtos, ColumnNameEnum.FEE_NOTE.key, TableEnum.INSTITUTION_COURSE.key, createUser);
        doTranslateInstitutionCourses(transVo, languageCode, translationDtos, ColumnNameEnum.DURATION_NOTE.key, TableEnum.INSTITUTION_COURSE.key, createUser);
        doTranslateInstitutionCourses(transVo, languageCode, translationDtos, ColumnNameEnum.CORE_COURSE.key, TableEnum.INSTITUTION_COURSE.key, createUser);
        doTranslateInstitutionCourses(transVo, languageCode, translationDtos, ColumnNameEnum.ENTRY_STANDARDS.key, TableEnum.INSTITUTION_COURSE.key, createUser);
        doTranslateInstitutionCourses(transVo, languageCode, translationDtos, ColumnNameEnum.OCCUPATION_DEVELOPMENT.key, TableEnum.INSTITUTION_COURSE.key, createUser);
        doTranslateInstitutionCourses(transVo, languageCode, translationDtos, ColumnNameEnum.REMARK.key, TableEnum.INSTITUTION_COURSE.key, createUser);
        doTranslateInstitutionCourses(transVo, languageCode, translationDtos, ColumnNameEnum.NAME_CHN.key, TableEnum.INSTITUTION_COURSE.key, createUser);
        doTranslateInstitutionCourses(transVo, languageCode, translationDtos, ColumnNameEnum.NAME_DISPLAY.key, TableEnum.INSTITUTION_COURSE.key, createUser);
        doTranslateInstitutionCourses(transVo, languageCode, translationDtos, ColumnNameEnum.INTRODUCTION.key, TableEnum.INSTITUTION_COURSE.key, createUser);
        if (translationDtos.size() > 0) {
            translationMappingService.updateTranslations(translationDtos);
        }
        institutionCourseCount.getSumCount().set(0);
        institutionCourseCount.getCompletedCount().set(0);
        institutionCourseCount.getErrorCount().set(0);
        log.info("批量翻译完成");
    }

    //执行翻译
    private void doTranslate(TransVo transVo, String languageCode, List<TranslationDto> translationDtos, String cloumnName, String tableName, String createUser) {
        //指定表 指定字段 未翻译的ids
        List<Long> selectNewsIds = getNotTranslateIds(languageCode, cloumnName, tableName);
        newsCount.getSumCount().addAndGet(selectNewsIds.size());
        //指定表 指定字段的mappids
        Long mappingId = getMappingIdByTableNameAndCloumnName(tableName, cloumnName);
        if (selectNewsIds.size() > 0) {
            for (Long newsId : selectNewsIds) {
                News news = newsMapper.getNewsById(newsId);
                transVo.setFrom(TransApi.ZH_CN);
                TranslationDto translationDto = new TranslationDto();
                String transResult = "";
                if (ColumnNameEnum.TITLE.key.equals(cloumnName)) {
                    transVo.setQuery(news.getTitle());
                    if (news.getTitle() != null) {
                        if ("".equals(news.getTitle().trim())) {
                            newsCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                newsCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        newsCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.DESCRIPTION.key.equals(cloumnName)) {
                    transVo.setQuery(news.getDescription());
                    if (news.getDescription() != null) {
                        if ("".equals(news.getDescription().trim())) {
                            newsCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                //transResult = TransApi.translate(transVo.getQuery(), transVo.getFrom(), transVo.getTo());
                                transResult = HtmlUtils.htmlTranslate(transVo.getQuery(), transVo.getFrom(), transVo.getTo());
                            } catch (Exception e) {
                                e.printStackTrace();
                                newsCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        newsCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.PROFILE.key.equals(cloumnName)) {
                    transVo.setQuery(news.getProfile());
                    if (news.getProfile() != null) {
                        if ("".equals(news.getProfile().trim())) {
                            newsCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.translate(transVo.getQuery(), transVo.getFrom(), transVo.getTo());
                            } catch (Exception e) {
                                e.printStackTrace();
                                newsCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        newsCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if ("".equals(transResult) || transResult == null) {
                    newsCount.getErrorCount().incrementAndGet();
                    return;
                }
                translationDto.setTranslation(transResult);
                translationDto.setLanguageCode(languageCode);
                translationDto.setFkTableId(newsId);
                translationDto.setFkTranslationMappingId(mappingId);
                translationDto.setFkTableName(TableEnum.NEWS.key);
                try {
                    BeanUtils.setProperty(translationDto, "gmtCreate", new Date());
                    BeanUtils.setProperty(translationDto, "gmtCreateUser", createUser);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                newsCount.getCompletedCount().incrementAndGet();
                translationDtos.add(translationDto);
                if (translationDtos.size() >= 10) {
                    translationMappingService.updateTranslations(translationDtos);
                    translationDtos.clear();
                    log.info("insert success--");
                }
                log.info("translate success--");
            }
        }
    }

    //执行翻译
    private void doTranslateInstitutionInfo(TransVo transVo, String languageCode, List<TranslationDto> translationDtos, String cloumnName, String tableName, String createUser) {
        List<Long> ids = getNotTranslateIds(languageCode, cloumnName, tableName);
        institutionInfoCount.getSumCount().addAndGet(ids.size());
        Long mappingId = getMappingIdByTableNameAndCloumnName(tableName, cloumnName);
        if (ids.size() > 0) {
            for (Long id : ids) {
                InstitutionInfo institutionInfo = institutionInfoMapper.getInstitutionInfoById(id);
                transVo.setFrom(TransApi.AUTO);//自动
                TranslationDto translationDto = new TranslationDto();
                String transResult = "";
                if (ColumnNameEnum.TITLE.key.equals(cloumnName)) {
                    transVo.setQuery(institutionInfo.getTitle());
                    if (institutionInfo.getTitle() != null) {
                        if ("".equals(institutionInfo.getTitle().trim())) {
                            institutionInfoCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                institutionInfoCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        institutionInfoCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.DESCRIPTION.key.equals(cloumnName)) {
                    transVo.setQuery(institutionInfo.getDescription());
                    if (institutionInfo.getDescription() != null) {
                        if ("".equals(institutionInfo.getDescription().trim())) {
                            institutionInfoCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = HtmlUtils.htmlTranslate(transVo.getQuery(), transVo.getFrom(), transVo.getTo());
                            } catch (Exception e) {
                                e.printStackTrace();
                                institutionInfoCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        institutionInfoCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.PROFILE.key.equals(cloumnName)) {
                    transVo.setQuery(institutionInfo.getProfile());
                    if (institutionInfo.getProfile() != null) {
                        if ("".equals(institutionInfo.getProfile().trim())) {
                            institutionInfoCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                institutionInfoCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        institutionInfoCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if ("".equals(transResult) || transResult == null) {
                    institutionInfoCount.getErrorCount().incrementAndGet();
                    return;
                }
                translationDto.setTranslation(transResult);
                translationDto.setLanguageCode(languageCode);
                translationDto.setFkTableId(id);
                translationDto.setFkTranslationMappingId(mappingId);
                translationDto.setFkTableName(TableEnum.INSTITUTION_INFO.key);
                try {
                    BeanUtils.setProperty(translationDto, "gmtCreate", new Date());
                    BeanUtils.setProperty(translationDto, "gmtCreateUser", createUser);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                institutionInfoCount.getCompletedCount().incrementAndGet();
                translationDtos.add(translationDto);
                if (translationDtos.size() >= 10) {
                    translationMappingService.updateTranslations(translationDtos);
                    translationDtos.clear();
                    log.info("insert success--");
                }
                log.info("translate success--");
            }
        }
    }

    private void doTranslateInstitutions(TransVo transVo, String languageCode, List<TranslationDto> translationDtos, String cloumnName, String tableName, String createUser){
        List<Long> ids = getNotTranslateIds(languageCode, cloumnName, tableName);
        institutionCount.getSumCount().addAndGet(ids.size());
        Long mappingId = getMappingIdByTableNameAndCloumnName(tableName, cloumnName);
        if(ids.size()>0){
            for(Long id : ids){
                Institution institution = institutionMapper.getInstitutionById(id);
                String query = getInstitutionQuery(institution,cloumnName);
                transVo.setFrom(TransApi.ZH_CN);
                TranslationDto translationDto = new TranslationDto();
                String transResult = "";
                transVo.setQuery(query);
                if (query != null) {
                    if ("".equals(query.trim())) {
                        institutionCount.getErrorCount().incrementAndGet();
                        break;
                    } else {
                        try {
                            transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                        } catch (Exception e) {
                            e.printStackTrace();
                            institutionCount.getErrorCount().incrementAndGet();
                        }
                    }
                } else {
                    institutionCount.getErrorCount().incrementAndGet();
                    break;
                }
                if ("".equals(transResult) || transResult == null) {
                    institutionCount.getErrorCount().incrementAndGet();
                    return;
                }
                translationDto.setTranslation(transResult);
                translationDto.setLanguageCode(languageCode);
                translationDto.setFkTableId(id);
                translationDto.setFkTranslationMappingId(mappingId);
                translationDto.setFkTableName(TableEnum.INSTITUTION.key);
                try {
                    BeanUtils.setProperty(translationDto, "gmtCreate", new Date());
                    BeanUtils.setProperty(translationDto, "gmtCreateUser", createUser);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                institutionCount.getCompletedCount().incrementAndGet();
                translationDtos.add(translationDto);
                if (translationDtos.size() >= 10) {
                    translationMappingService.updateTranslations(translationDtos);
                    translationDtos.clear();
                    log.info("insert success--");
                }
                log.info("translate success--");
            }
        }
    }

    private String getInstitutionQuery(Institution institution,String cloumnName){
        if (ColumnNameEnum.NAME.key.equals(cloumnName)){
            return institution.getName();
        }
        if(ColumnNameEnum.SHORT_NAME.key.equals(cloumnName)){
            return institution.getShortName();
        }
        if(ColumnNameEnum.NATURE.key.equals(cloumnName)){
            return institution.getNature();
        }
        if(ColumnNameEnum.DETAIL.key.equals(cloumnName)){
            return institution.getDetail();
        }
        if(ColumnNameEnum.NAME_CHN.key.equals(cloumnName)){
            return institution.getNameChn();
        }
        if(ColumnNameEnum.NAME_DISPLAY.key.equals(cloumnName)){
            return institution.getNameDisplay();
        }
        if(ColumnNameEnum.SHORT_NAME_CHN.key.equals(cloumnName)){
            return institution.getShortNameChn();
        }
        if(ColumnNameEnum.ADDRESS.key.equals(cloumnName)){
            return institution.getAddress();
        }
        return null;
    }

    private void doTranslateInstitutionCourses(TransVo transVo, String languageCode, List<TranslationDto> translationDtos, String cloumnName, String tableName, String createUser){
        List<Long> ids = getNotTranslateIds(languageCode, cloumnName, tableName);
        institutionCourseCount.getSumCount().addAndGet(ids.size());
        Long mappingId = getMappingIdByTableNameAndCloumnName(tableName, cloumnName);
        if(ids.size()>0){
            for(Long id:ids){
                InstitutionCourse course = institutionCourseMapper.getInstitutionCourseById(id);
                String query = getInstitutionCourseQuery(course,cloumnName);
                transVo.setFrom(TransApi.ZH_CN);
                TranslationDto translationDto = new TranslationDto();
                String transResult = "";
                transVo.setQuery(query);
                if (query != null) {
                    if ("".equals(query.trim())) {
                        institutionCourseCount.getErrorCount().incrementAndGet();
                        break;
                    } else {
                        try {
                            transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                        } catch (Exception e) {
                            e.printStackTrace();
                            institutionCourseCount.getErrorCount().incrementAndGet();
                        }
                    }
                } else {
                    institutionCourseCount.getErrorCount().incrementAndGet();
                    break;
                }
                if ("".equals(transResult) || transResult == null) {
                    institutionCourseCount.getErrorCount().incrementAndGet();
                    return;
                }
                translationDto.setTranslation(transResult);
                translationDto.setLanguageCode(languageCode);
                translationDto.setFkTableId(id);
                translationDto.setFkTranslationMappingId(mappingId);
                translationDto.setFkTableName(TableEnum.INSTITUTION_COURSE.key);
                try {
                    BeanUtils.setProperty(translationDto, "gmtCreate", new Date());
                    BeanUtils.setProperty(translationDto, "gmtCreateUser", createUser);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                institutionCourseCount.getCompletedCount().incrementAndGet();
                translationDtos.add(translationDto);
                if (translationDtos.size() >= 10) {
                    translationMappingService.updateTranslations(translationDtos);
                    translationDtos.clear();
                    log.info("insert success--");
                }
                log.info("translate success--");
            }
        }
    }

    private String getInstitutionCourseQuery(InstitutionCourse course,String cloumnName){
        if (ColumnNameEnum.NAME.key.equals(cloumnName)){
            return course.getName();
        }
        if(ColumnNameEnum.START_DATE_NOTE.key.equals(cloumnName)){
            return course.getStartDateNote();
        }
        if(ColumnNameEnum.APPLY_DATE_NOTE.key.equals(cloumnName)){
            return course.getApplyDateNote();
        }
        if(ColumnNameEnum.FEE_NOTE.key.equals(cloumnName)){
            return course.getFeeNote();
        }
        if(ColumnNameEnum.DURATION_NOTE.key.equals(cloumnName)){
            return course.getDurationNote();
        }
        if(ColumnNameEnum.CORE_COURSE.key.equals(cloumnName)){
            return course.getCoreCourse();
        }
        if(ColumnNameEnum.ENTRY_STANDARDS.key.equals(cloumnName)){
            return course.getEntryStandards();
        }
        if(ColumnNameEnum.OCCUPATION_DEVELOPMENT.key.equals(cloumnName)){
            return course.getOccupationDevelopment();
        }
        if(ColumnNameEnum.REMARK.key.equals(cloumnName)){
            return course.getRemark();
        }
        if(ColumnNameEnum.NAME_CHN.key.equals(cloumnName)){
            return course.getNameChn();
        }
        if(ColumnNameEnum.NAME_DISPLAY.key.equals(cloumnName)){
            return course.getNameDisplay();
        }
        if(ColumnNameEnum.INTRODUCTION.key.equals(cloumnName)){
            return course.getIntroduction();
        }
        return null;
    }


    private void doTranslateAreaCountrys(TransVo transVo, String languageCode, List<TranslationDto> translationDtos, String cloumnName, String tableName, String createUser) {
        List<Long> ids = getNotTranslateIds(languageCode, cloumnName, tableName);
        areaCountryCount.getSumCount().addAndGet(ids.size());
        Long mappingId = getMappingIdByTableNameAndCloumnName(tableName, cloumnName);
        if (ids.size() > 0) {
            for (Long id : ids) {
                AreaCountry areaCountry = areaCountryMapper.getAreaCountryById(id);
                transVo.setFrom(TransApi.ZH_CN);
                TranslationDto translationDto = new TranslationDto();
                String transResult = "";
                if (NAME_CHN.key.equals(cloumnName)) {
                    transVo.setQuery(areaCountry.getNameChn());
                    if (areaCountry.getNameChn() != null) {
                        if ("".equals(areaCountry.getNameChn().trim())) {
                            areaCountryCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCountryCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCountryCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.CAPITAL.key.equals(cloumnName)) {
                    transVo.setQuery(areaCountry.getCapital());
                    if (areaCountry.getCapital() != null) {
                        if ("".equals(areaCountry.getCapital().trim())) {
                            areaCountryCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCountryCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCountryCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.POPULATION.key.equals(cloumnName)) {
                    transVo.setQuery(areaCountry.getPopulation());
                    if (areaCountry.getPopulation() != null) {
                        if ("".equals(areaCountry.getPopulation().trim())) {
                            areaCountryCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCountryCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCountryCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.AREA.key.equals(cloumnName)) {
                    transVo.setQuery(areaCountry.getArea());
                    if (areaCountry.getArea() != null) {
                        if ("".equals(areaCountry.getArea().trim())) {
                            areaCountryCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCountryCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCountryCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.LANGUAGE.key.equals(cloumnName)) {
                    transVo.setQuery(areaCountry.getLanguage());
                    if (areaCountry.getLanguage() != null) {
                        if ("".equals(areaCountry.getLanguage().trim())) {
                            areaCountryCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCountryCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCountryCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.RELIGION.key.equals(cloumnName)) {
                    transVo.setQuery(areaCountry.getReligion());
                    if (areaCountry.getReligion() != null) {
                        if ("".equals(areaCountry.getReligion().trim())) {
                            areaCountryCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCountryCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCountryCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.TIME_DIFFERENCE.key.equals(cloumnName)) {
                    transVo.setQuery(areaCountry.getTimeDifference());
                    if (areaCountry.getTimeDifference() != null) {
                        if ("".equals(areaCountry.getTimeDifference().trim())) {
                            areaCountryCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCountryCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCountryCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.PRESIDENT.key.equals(cloumnName)) {
                    transVo.setQuery(areaCountry.getPresident());
                    if (areaCountry.getPresident() != null) {
                        if ("".equals(areaCountry.getPresident().trim())) {
                            areaCountryCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCountryCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCountryCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.FLAG_MEANING.key.equals(cloumnName)) {
                    transVo.setQuery(areaCountry.getFlagMeaning());
                    if (areaCountry.getFlagMeaning() != null) {
                        if ("".equals(areaCountry.getFlagMeaning().trim())) {
                            areaCountryCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCountryCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCountryCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.EMBLEM_MEANING.key.equals(cloumnName)) {
                    transVo.setQuery(areaCountry.getEmblemMeaning());
                    if (areaCountry.getEmblemMeaning() != null) {
                        if ("".equals(areaCountry.getEmblemMeaning().trim())) {
                            areaCountryCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCountryCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCountryCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.REMARK.key.equals(cloumnName)) {
                    transVo.setQuery(areaCountry.getRemark());
                    if (areaCountry.getRemark() != null) {
                        if ("".equals(areaCountry.getRemark().trim())) {
                            areaCountryCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCountryCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCountryCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                translationDto.setTranslation(transResult);
                translationDto.setLanguageCode(languageCode);
                translationDto.setFkTableId(id);
                translationDto.setFkTranslationMappingId(mappingId);
                translationDto.setFkTableName(TableEnum.INSTITUTION_COUNTRY.key);
                try {
                    BeanUtils.setProperty(translationDto, "gmtCreate", new Date());
                    BeanUtils.setProperty(translationDto, "gmtCreateUser", createUser);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                areaCountryCount.getCompletedCount().incrementAndGet();
                translationDtos.add(translationDto);
                if (translationDtos.size() >= 10) {
                    translationMappingService.updateTranslations(translationDtos);
                    translationDtos.clear();
                    log.info("insert success--");
                }
                    log.info("translate success--");
            }
        }
    }

    //根据表名和字段名获取mappingId
    private Long getMappingIdByTableNameAndCloumnName(String tableName, String columnName) {
        return translationMappingMapper.getMappingIdByTableNameAndColumnName(tableName, columnName);
    }

    //获取所有对应字段不为null或""的newsIds
    private List<Long> getNewsIds(String columnName) {
        return newsMapper.getNewsIds(columnName);
    }

    //获取所有对应字段不为null或""的InstitutionInfoIds
    private List<Long> getInstitutionInfoIds(String columnName) {
        return institutionInfoMapper.getInstitutionInfoIds(columnName);
    }

    //获取所有对应字段不为null或""的AreaCountryIds
    private List<Long> getAreaCountryIds(String columnName) {
        return areaCountryMapper.getAreaCountryIds(columnName);
    }

    //获取所有对应表名、字段名、语言的原表的ids
    private List<Long> getTranslationIds(String fkTableName, String fkColumnName, String languageCode) {
        return translationMapper.getTranslationIdsByTableNameAndColumnAndLanguageCode(fkTableName, fkColumnName, languageCode);
    }

    //获取所有对应表名、字段名、语言的ids
    private List<Long> getTranslationIdList(String fkTableName, String fkColumnName, String languageCode) {
        return translationMapper.getTranslationIdListByTableNameAndColumnAndLanguageCode(fkTableName, fkColumnName, languageCode);
    }

    //根据id获得Translation
    private InstitutionTranslation getTranslationById(Long translationId) {
        return translationMapper.getTranslationById(translationId);
    }

    @Override
    public BatchTranslationResultVo getBatchTranslationResultInfo(TransVo transVo, String tableName) {
        String languageCode = "";
        if (GeneralTool.isEmpty(transVo.getTo())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if ("zh-cn".equals(transVo.getTo())) {
            languageCode = "zh-cn";
        }
        if ("en-us".equals(transVo.getTo())) {
            languageCode = "en-us";
        }
        if ("zh-hk".equals(transVo.getTo())) {
            languageCode = "zh-hk";
        }
        if ("zh-tw".equals(transVo.getTo())) {
            languageCode = "zh-tw";
        }
        BatchTranslationResultVo batchTranslationResultVo = new BatchTranslationResultVo();
        Integer translationCount = 0;
        Integer estimatedTime;
        List<Long> titleIds = getNotTranslateIds(languageCode, ColumnNameEnum.TITLE.key, tableName);
        List<Long> descriptionIds = getNotTranslateIds(languageCode, ColumnNameEnum.DESCRIPTION.key, tableName);
        List<Long> profileIds = getNotTranslateIds(languageCode, ColumnNameEnum.PROFILE.key, tableName);
        translationCount = titleIds.size() + descriptionIds.size() + profileIds.size();
        estimatedTime = translationCount * 3;
        batchTranslationResultVo.setTranslationCount(translationCount);
        batchTranslationResultVo.setEstimatedTime(estimatedTime);
        return batchTranslationResultVo;
    }

    @Override
    public BatchTranslationResultVo getBatchTranslationCountryInfo(TransVo transVo, String tableName) {
        String languageCode = "";
        if (GeneralTool.isEmpty(transVo.getTo())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if ("zh-cn".equals(transVo.getTo())) {
            languageCode = "zh-cn";
        }
        if ("en-us".equals(transVo.getTo())) {
            languageCode = "en-us";
        }
        if ("zh-hk".equals(transVo.getTo())) {
            languageCode = "zh-hk";
        }
        if ("zh-tw".equals(transVo.getTo())) {
            languageCode = "zh-tw";
        }
        BatchTranslationResultVo batchTranslationResultVo = new BatchTranslationResultVo();
        Integer translationCount = 0;
        Integer estimatedTime;
        List<Long> nameChnIds = getNotTranslateIds(languageCode, NAME_CHN.key, tableName);
        List<Long> capitalIds = getNotTranslateIds(languageCode, ColumnNameEnum.CAPITAL.key, tableName);
        List<Long> populationIds = getNotTranslateIds(languageCode, ColumnNameEnum.POPULATION.key, tableName);
        List<Long> areaIds = getNotTranslateIds(languageCode, ColumnNameEnum.AREA.key, tableName);
        List<Long> languageIds = getNotTranslateIds(languageCode, ColumnNameEnum.LANGUAGE.key, tableName);
        List<Long> religionIds = getNotTranslateIds(languageCode, ColumnNameEnum.RELIGION.key, tableName);
        List<Long> timeDifferenceIds = getNotTranslateIds(languageCode, ColumnNameEnum.TIME_DIFFERENCE.key, tableName);
        List<Long> presidentIds = getNotTranslateIds(languageCode, ColumnNameEnum.PRESIDENT.key, tableName);
        List<Long> flagMeaningIds = getNotTranslateIds(languageCode, ColumnNameEnum.FLAG_MEANING.key, tableName);
        List<Long> emblemMeaningIds = getNotTranslateIds(languageCode, ColumnNameEnum.EMBLEM_MEANING.key, tableName);
        List<Long> remarkIds = getNotTranslateIds(languageCode, ColumnNameEnum.REMARK.key, tableName);
        translationCount = nameChnIds.size() + capitalIds.size() + populationIds.size() + areaIds.size() +
                languageIds.size() + religionIds.size() + timeDifferenceIds.size() + presidentIds.size() + flagMeaningIds.size() +
                emblemMeaningIds.size() + remarkIds.size();
        estimatedTime = translationCount * 11;
        batchTranslationResultVo.setTranslationCount(translationCount);
        batchTranslationResultVo.setEstimatedTime(estimatedTime);
        return batchTranslationResultVo;
    }

    @Override
    public BatchTranslationResultVo getBatchTranslationInstitutionInfo(TransVo transVo, String tableName) {
        String languageCode = "";
        if (GeneralTool.isEmpty(transVo.getTo())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if ("zh-cn".equals(transVo.getTo())) {
            languageCode = "zh-cn";
        }
        if ("en-us".equals(transVo.getTo())) {
            languageCode = "en-us";
        }
        if ("zh-hk".equals(transVo.getTo())) {
            languageCode = "zh-hk";
        }
        if ("zh-tw".equals(transVo.getTo())) {
            languageCode = "zh-tw";
        }
        BatchTranslationResultVo batchTranslationResultVo = new BatchTranslationResultVo();
        Integer translationCount = 0;
        Integer estimatedTime;
        List<Long> nameIds = getNotTranslateIds(languageCode, NAME.key, tableName);
        List<Long> shortNameIds = getNotTranslateIds(languageCode, ColumnNameEnum.SHORT_NAME.key, tableName);
        List<Long> natureIds = getNotTranslateIds(languageCode, ColumnNameEnum.NATURE.key, tableName);
        List<Long> detailIds = getNotTranslateIds(languageCode, ColumnNameEnum.DETAIL.key, tableName);
        List<Long> nameChnIds = getNotTranslateIds(languageCode, NAME_CHN.key, tableName);
        List<Long> nameDisplayIds = getNotTranslateIds(languageCode, ColumnNameEnum.NAME_DISPLAY.key, tableName);
        List<Long> shortNameChnIds = getNotTranslateIds(languageCode, ColumnNameEnum.SHORT_NAME_CHN.key, tableName);
        List<Long> addressIds = getNotTranslateIds(languageCode, ColumnNameEnum.ADDRESS.key, tableName);

        translationCount = nameIds.size() + shortNameIds.size() + natureIds.size() + detailIds.size() +
                nameChnIds.size() + nameDisplayIds.size() + shortNameChnIds.size() + addressIds.size() ;
        estimatedTime = translationCount * 11;
        batchTranslationResultVo.setTranslationCount(translationCount);
        batchTranslationResultVo.setEstimatedTime(estimatedTime);
        return batchTranslationResultVo;
    }

    @Override
    public BatchTranslationResultVo getBatchTranslationInstitutionCourseInfo(TransVo transVo, String tableName) {
        String languageCode = "";
        if (GeneralTool.isEmpty(transVo.getTo())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if ("zh-cn".equals(transVo.getTo())) {
            languageCode = "zh-cn";
        }
        if ("en-us".equals(transVo.getTo())) {
            languageCode = "en-us";
        }
        if ("zh-hk".equals(transVo.getTo())) {
            languageCode = "zh-hk";
        }
        if ("zh-tw".equals(transVo.getTo())) {
            languageCode = "zh-tw";
        }
        BatchTranslationResultVo batchTranslationResultVo = new BatchTranslationResultVo();
        Integer translationCount = 0;
        Integer estimatedTime;

        List<Long> nameIds = getNotTranslateIds(languageCode, NAME.key, tableName);
        List<Long> startDateNoteIds = getNotTranslateIds(languageCode, ColumnNameEnum.START_DATE_NOTE.key, tableName);
        List<Long> applyDateNoteIds = getNotTranslateIds(languageCode, ColumnNameEnum.APPLY_DATE_NOTE.key, tableName);
        List<Long> feeNoteIds = getNotTranslateIds(languageCode, ColumnNameEnum.FEE_NOTE.key, tableName);
        List<Long> durationNoteIds = getNotTranslateIds(languageCode, DURATION_NOTE.key, tableName);
        List<Long> coreCourseIds = getNotTranslateIds(languageCode, ColumnNameEnum.CORE_COURSE.key, tableName);
        List<Long> entryStandardsIds = getNotTranslateIds(languageCode, ColumnNameEnum.ENTRY_STANDARDS.key, tableName);
        List<Long> occupationDevelopmentIds = getNotTranslateIds(languageCode, ColumnNameEnum.OCCUPATION_DEVELOPMENT.key, tableName);
        List<Long> remarkIds = getNotTranslateIds(languageCode, ColumnNameEnum.REMARK.key, tableName);
        List<Long> nameChnIds = getNotTranslateIds(languageCode, NAME_CHN.key, tableName);
        List<Long> nameDisplayIds = getNotTranslateIds(languageCode, NAME_DISPLAY.key, tableName);
        List<Long> introductionIds = getNotTranslateIds(languageCode, INTRODUCTION.key, tableName);

        translationCount = nameIds.size() + startDateNoteIds.size() + applyDateNoteIds.size() + feeNoteIds.size() +
                durationNoteIds.size() + coreCourseIds.size() + entryStandardsIds.size() + occupationDevelopmentIds.size() +
                remarkIds.size() + nameChnIds.size() + nameDisplayIds.size() + introductionIds.size();
        estimatedTime = translationCount * 11;
        batchTranslationResultVo.setTranslationCount(translationCount);
        batchTranslationResultVo.setEstimatedTime(estimatedTime);
        return batchTranslationResultVo;
    }

    @Override
    public BatchTranslationResultVo batchTranslationCount() {
        BatchTranslationResultVo batchTranslationResultVo = new BatchTranslationResultVo();
        batchTranslationResultVo.setTranslationCount(newsCount.getSumCount().get());
        batchTranslationResultVo.setCompleteCount(newsCount.getCompletedCount().get());
        batchTranslationResultVo.setErrorCount(newsCount.getErrorCount().get());
        return batchTranslationResultVo;
    }

    @Override
    public BatchTranslationResultVo batchTranslateInstitutionInfosCount() {
        BatchTranslationResultVo batchTranslationResultVo = new BatchTranslationResultVo();
        batchTranslationResultVo.setTranslationCount(institutionInfoCount.getSumCount().get());
        batchTranslationResultVo.setCompleteCount(institutionInfoCount.getCompletedCount().get());
        batchTranslationResultVo.setErrorCount(institutionInfoCount.getErrorCount().get());
        return batchTranslationResultVo;
    }

    @Override
    public BatchTranslationResultVo batchTranslateAreaCountrysCount() {
        BatchTranslationResultVo batchTranslationResultVo = new BatchTranslationResultVo();
        batchTranslationResultVo.setTranslationCount(areaCountryCount.getSumCount().get());
        batchTranslationResultVo.setCompleteCount(areaCountryCount.getCompletedCount().get());
        batchTranslationResultVo.setErrorCount(areaCountryCount.getErrorCount().get());
        return batchTranslationResultVo;
    }

    @Override
    public BatchTranslationResultVo batchTranslateInstitutionsCount() {
        BatchTranslationResultVo batchTranslationResultVo = new BatchTranslationResultVo();
        batchTranslationResultVo.setTranslationCount(institutionCount.getSumCount().get());
        batchTranslationResultVo.setCompleteCount(institutionCount.getCompletedCount().get());
        batchTranslationResultVo.setErrorCount(institutionCount.getErrorCount().get());
        return batchTranslationResultVo;
    }

    @Override
    public BatchTranslationResultVo batchTranslateInstitutionCoursesCount() {
        BatchTranslationResultVo batchTranslationResultVo = new BatchTranslationResultVo();
        batchTranslationResultVo.setTranslationCount(institutionCourseCount.getSumCount().get());
        batchTranslationResultVo.setCompleteCount(institutionCourseCount.getCompletedCount().get());
        batchTranslationResultVo.setErrorCount(institutionCourseCount.getErrorCount().get());
        return batchTranslationResultVo;
    }

    private List<Long> getNotTranslateIds(String languageCode, String columnName, String tableName) {
        List<Long> ids = new ArrayList<>();
        if (tableName.equals(TableEnum.NEWS.key)) {
            ids = getNewsIds(columnName);
        }
        if (tableName.equals(TableEnum.INSTITUTION_INFO.key)) {
            ids = getInstitutionInfoIds(columnName);
        }
        if (tableName.equals(TableEnum.INSTITUTION_COUNTRY.key)) {
            ids = getAreaCountryIds(columnName);
        }
        if (tableName.equals(TableEnum.INSTITUTION_AREA_CITY_DIVISION.key)) {
            ids = getAreaCityDiversionIds(columnName);
        }
        if(tableName.equals(TableEnum.INSTITUTION.key)){
            ids = getInstitutionIdsByColumnName(columnName);
        }
        if(tableName.equals(TableEnum.INSTITUTION_COURSE.key)){
            ids = getInstitutionCourseIdsByColumnName(columnName);
        }
        List<Long> translateIds = getTranslationIds(tableName, columnName, languageCode);
        Set<Long> idsSet = new HashSet<>(ids);
        Set<Long> translateIdsSet = new HashSet<>(translateIds);
        idsSet.removeAll(translateIdsSet);
        List<Long> selectNewsIds = new ArrayList<>();
        selectNewsIds.addAll(idsSet);
        return selectNewsIds;
    }

    private List<Long> getAreaCityDiversionIds(String columnName) {
        return areaCityDivisionMapper.getAreaCityDiversionIds(columnName);
    }

    //获取所有对应字段不为null或""的InstitutionInfoIds
    private List<Long> getInstitutionIdsByColumnName(String columnName) {
        return institutionMapper.getInstitutionIdsByColumnName(columnName);
    }

    private List<Long> getInstitutionCourseIdsByColumnName(String columnName){
        return institutionCourseMapper.getInstitutionCourseIdsByColumnName(columnName);
    }

    @Override
    public void batchTranformNews(String tableName, String languageCode) {
        if (tableName.equals(TableEnum.NEWS.key)) {
            ColumnNameEnum[] newsColumnName = ColumnNameEnum.NEWS_COLUMN_NAME;
            for (ColumnNameEnum columnNameEnum : newsColumnName) {
                switch (columnNameEnum) {
                    case TITLE:
                        List<Long> translationIds = getTranslationIdList(tableName, ColumnNameEnum.TITLE.key, languageCode);
                        updateTranslation(translationIds);
                        break;
                    case DESCRIPTION:
                        List<Long> descriptionIds = getTranslationIdList(tableName, ColumnNameEnum.DESCRIPTION.key, languageCode);
                        updateTranslation(descriptionIds);
                        break;
                    case PROFILE:
                        List<Long> profileIds = getTranslationIdList(tableName, ColumnNameEnum.PROFILE.key, languageCode);
                        updateTranslation(profileIds);
                        break;
                    default:
                        break;
                }

            }
        }
    }

    @Async("taskExecutor")
    @Override
    public void batchSetAreaStateName() {
        //batch set
        AreaStateDto areaStateDto = new AreaStateDto();
        List<AreaState> areaStates = areaStateMapper.selectAreaStateByVo(areaStateDto);
        areaStateCount.getSumCount().set(areaStates.size());
        areaStateCount.getCompletedCount().set(0);
        areaStateCount.getErrorCount().set(0);
        for (AreaState areaState : areaStates) {
            if (GeneralTool.isEmpty(areaState.getNameChn())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("name_is_null"));
            }
            try {
                TransResult transResult = TransApi.getTransResult(areaState.getNameChn(), TransApi.ZH_CN, TransApi.EN_US);
                String dst = transResult.getTrans_result().get(0).getDst();
                areaState.setName(dst);
                try {
                    BeanUtils.setProperty(areaState, "gmtModified", new Date());
                    BeanUtils.setProperty(areaState, "gmtModifiedUser", "[system]");
                } catch (Exception e) {
                    e.printStackTrace();
                    areaStateCount.getErrorCount().incrementAndGet();
                }
                areaStateMapper.updateById(areaState);
                areaStateCount.getCompletedCount().incrementAndGet();
            } catch (Exception e) {
                areaStateCount.getErrorCount().incrementAndGet();
                e.printStackTrace();
                log.error("id=" + areaState.getId().toString() + "  " + "translate error");
            }
        }

    }

    @Override
    public BatchTranslationResultVo getBatchSetStateName() {
        BatchTranslationResultVo batchTranslationResultVo = new BatchTranslationResultVo();
        batchTranslationResultVo.setTranslationCount(areaStateCount.getSumCount().get());
        return batchTranslationResultVo;
    }

    @Override
    public BatchTranslationResultVo getBatchSetCityName() {
        BatchTranslationResultVo batchTranslationResultVo = new BatchTranslationResultVo();
        batchTranslationResultVo.setTranslationCount(areaCityCount.getSumCount().get());
        return batchTranslationResultVo;
    }

    @Async("taskExecutor")
    @Override
    public void batchSetAreaCityName() {
        //batch set
        AreaCityDto areaCityDto = new AreaCityDto();
        List<AreaCity> areaCitys = areaCityMapper.selectAreaCityByVo(areaCityDto);
        areaCityCount.getSumCount().set(areaCitys.size() * 2);
        areaCityCount.getCompletedCount().set(0);
        areaCityCount.getErrorCount().set(0);
        for (AreaCity areaCity : areaCitys) {
            if (GeneralTool.isEmpty(areaCity.getNameChn())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("name_is_null"));
            }
            try {
                TransResult transResult = TransApi.getTransResult(areaCity.getNameChn(), TransApi.ZH_CN, TransApi.EN_US);
                String dst = transResult.getTrans_result().get(0).getDst();
                areaCity.setName(dst);
                try {
                    BeanUtils.setProperty(areaCity, "gmtModified", new Date());
                    BeanUtils.setProperty(areaCity, "gmtModifiedUser", "[system]");
                } catch (Exception e) {
                    e.printStackTrace();
                    areaCityCount.getErrorCount().incrementAndGet();
                }
                areaCityMapper.updateById(areaCity);
                areaCityCount.getCompletedCount().incrementAndGet();
            } catch (Exception e) {
                areaCityCount.getErrorCount().incrementAndGet();
                e.printStackTrace();
                log.error("id=" + areaCity.getId().toString() + "  " + "translate error");
            }
        }

        for (AreaCity areaCity : areaCitys) {
            if (GeneralTool.isEmpty(areaCity.getName())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("name_is_null"));
            }
            try {
                TransResult transResult = TransApi.getTransResult(areaCity.getName(), TransApi.EN_US, TransApi.ZH_CN);
                String dst = transResult.getTrans_result().get(0).getDst();
                areaCity.setNameChn(dst);
                try {
                    BeanUtils.setProperty(areaCity, "gmtModified", new Date());
                    BeanUtils.setProperty(areaCity, "gmtModifiedUser", "[system]");
                } catch (Exception e) {
                    e.printStackTrace();
                    areaCityCount.getErrorCount().incrementAndGet();
                }
                areaCityMapper.updateById(areaCity);
                areaCityCount.getCompletedCount().incrementAndGet();
            } catch (Exception e) {
                areaCityCount.getErrorCount().incrementAndGet();
                e.printStackTrace();
                log.error("id=" + areaCity.getId().toString() + "  " + "translate error");
            }
        }
    }

    @Async("taskExecutor")
    @Override
    public void batchTranslationCityDivisions(TransVo transVo, String createUser) {
        //计数
        areaCityDiversionCount.getSumCount().set(0);
        areaCityDiversionCount.getCompletedCount().set(0);
        areaCityDiversionCount.getErrorCount().set(0);
        String languageCode = "";
        if (GeneralTool.isEmpty(transVo.getTo())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if ("zh-cn".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_CN);
            languageCode = "zh-cn";
        }
        if ("en-us".equals(transVo.getTo())) {
            transVo.setTo(TransApi.EN_US);
            languageCode = "en-us";
        }
        if ("zh-hk".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_HK);
            languageCode = "zh-hk";
        }
        if ("zh-tw".equals(transVo.getTo())) {
            transVo.setTo(TransApi.ZH_TW);
            languageCode = "zh-tw";
        }
        List<TranslationDto> translationDtos = new ArrayList<>();
//        doTranslateAreaCityDiversions(transVo, languageCode, translationDtos, ColumnNameEnum.NAME.key, TableEnum.INSTITUTION_AREA_CITY_DIVISION.key, createUser);
        doTranslateAreaCityDiversions(transVo, languageCode, translationDtos, ColumnNameEnum.STREETS_NAME.key, TableEnum.INSTITUTION_AREA_CITY_DIVISION.key, createUser);
        if (translationDtos.size() > 0) {
            translationMappingService.updateTranslations(translationDtos);
        }
        areaCityDiversionCount.getSumCount().set(0);
        areaCityDiversionCount.getCompletedCount().set(0);
        areaCityDiversionCount.getErrorCount().set(0);
        log.info("批量翻译完成");
    }

    @Override
    public BatchTranslationResultVo getBatchTranslationCityDivisionInfo(TransVo transVo, String tableName) {
        String languageCode = "";
        if (GeneralTool.isEmpty(transVo.getTo())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if ("zh-cn".equals(transVo.getTo())) {
            languageCode = "zh-cn";
        }
        if ("en-us".equals(transVo.getTo())) {
            languageCode = "en-us";
        }
        if ("zh-hk".equals(transVo.getTo())) {
            languageCode = "zh-hk";
        }
        if ("zh-tw".equals(transVo.getTo())) {
            languageCode = "zh-tw";
        }
        BatchTranslationResultVo batchTranslationResultVo = new BatchTranslationResultVo();
        Integer translationCount = 0;
        Integer estimatedTime;
        List<Long> nameIds = getNotTranslateIds(languageCode, NAME.key, tableName);
        List<Long> streetsNameIds = getNotTranslateIds(languageCode, ColumnNameEnum.STREETS_NAME.key, tableName);
        translationCount = nameIds.size() + streetsNameIds.size();
        estimatedTime = translationCount * 11;
        batchTranslationResultVo.setTranslationCount(translationCount);
        batchTranslationResultVo.setEstimatedTime(estimatedTime);
        return batchTranslationResultVo;
    }




    @Override
    public ResponseBo batchTranslationInstitutionAndCourseInfo(BatchTranslationInstitutionAndCourseInfoDto batchTranslationInstitutionAndCourseInfoDto) {
        Object o = getRedis.get(CacheKeyConstants.BATCH_TRANSLATION_INSTITUTION_AND_COURSE_INFO_CACHE_KEY);
        if (GeneralTool.isNotEmpty(o)){
            return new ResponseBo("翻译执行中，请勿重复点击！");
        }
        asyncTranslationJob.batchTranslationInstitutionAndCourseInfo(batchTranslationInstitutionAndCourseInfoDto);
        return new ResponseBo("开始批量翻译！");
    }

    @Override
    public ResponseBo batchTranslationInstitutionAndCourse() {
        Object o = getRedis.get(CacheKeyConstants.BATCH_TRANSLATION_INSTITUTION_AND_COURSE_INFO_CACHE_KEY);
        if (GeneralTool.isNotEmpty(o)){
            return new ResponseBo("翻译执行中，请勿重复点击！");
        }
        asyncCompletions.batchTranslationInstitutionAndCourseInfo();
        return new ResponseBo("开始批量翻译！");
    }


//    @Override
//    public void batchTranslationInstitutionFaculty() {
//        List<InstitutionFaculty> institutionFaculties = translationMapper.getInstitutionFacultyForTranslation();
//        institutionFaculties = institutionFaculties.stream().filter(i->GeneralTool.isEmpty(i.getNameChn())).collect(Collectors.toList());
//        if (GeneralTool.isEmpty(institutionFaculties)){
//            return;
//        }
//        for (InstitutionFaculty institutionFaculty : institutionFaculties) {
//            String result = null;
//            try {
//                result = TransApi.getTransResult(institutionFaculty.getName(), TransApi.EN_US, TransApi.ZH_CN).getTrans_result().get(0).getDst();
//            } catch (Exception e) {
//                continue;
//            }
//            institutionFaculty.setNameChn(result);
//            institutionFaculty.setGmtModifiedUser("[system]");
//            institutionFaculty.setGmtModified(new Date());
//            institutionFacultyMapper.updateById(institutionFaculty);
//            log.info("facultyid=" + institutionFaculty.getId().toString() + "####" + "translate success");
//        }
//        log.info("translate end!!");
//    }

//    @Resource
//    private TransApiUtil apiUtil;

//    @Override
//    public void batchTranslationInstitutionCourseSubject() {
//        List<InstitutionCourseSubject> institutionCourseSubjects = translationMapper.getInstitutionCourseSubjectForTranslation();
//        if (GeneralTool.isEmpty(institutionCourseSubjects)){
//            return;
//        }
//        institutionCourseSubjects = institutionCourseSubjects.stream().filter(i -> GeneralTool.isEmpty(i.getSubjectNameChn())).collect(Collectors.toList());
//        if (GeneralTool.isEmpty(institutionCourseSubjects)) {
//            return;
//        }
//
//        int batchSize = 10; // 每批处理数量为10个
//
//        ExecutorService pool = Executors.newFixedThreadPool(10);
//
//        AtomicInteger failureCount = new AtomicInteger(0);
//
//        int total = institutionCourseSubjects.size();
//        int batches = (total + batchSize - 1) / batchSize;
//
//        for (int i = 0; i < batches && failureCount.get() == 0; i++) {
//
//            final List<InstitutionCourseSubject> batch = institutionCourseSubjects.subList(i * batchSize, Math.min((i + 1) * batchSize, total));
//
//                try {
//                    List<CompletableFuture<Void>> futures = new ArrayList<>();
//                    for (InstitutionCourseSubject subject : batch) {
//                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> translateAndSave(subject), pool)
//                                .exceptionally(e -> {
//                                    failureCount.incrementAndGet();
//                                    log.error("翻译和保存过程中发生错误，记录该错误并继续处理其他任务。", e);
//                                    return null;
//                                });
//                        futures.add(future);
//                    }
//
//                    // 所有翻译任务完成后，再执行批量更新
//                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])
//                    ).thenRunAsync(() -> {
//                        if (failureCount.get() == 0) {
//                            translationMapper.updateBatchInstitutionCourseSubject(batch, new Date(), "[system]");
//                        }
//                    }, pool);
//                    translationMapper.updateBatchInstitutionCourseSubject(batch, new Date(), "[system]");
//                    Thread.sleep(1005);
//                } catch (Exception e) {
//                    log.error("翻译和保存过程中发生错误，终止所有任务。", e);
//                    failureCount.incrementAndGet();
//                    pool.shutdownNow(); // 关闭线程池并尝试取消所有已提交但尚未开始的任务
//                }
//        }
//
//        // 如果在循环过程中没有出现错误，则等待所有任务完成
//        if (failureCount.get() == 0) {
//            // 在循环结束后，关闭线程池
//            pool.shutdown();
//            while (!pool.isTerminated()) {
//                // 等待所有任务完成
//            }
//        }
//        log.info("translate end!!");
//    }

//    @Override
//    public void batchTranslationInstitutionCourse() {
//        List<InstitutionCourse> institutionCourses = translationMapper.getInstitutionCourseForTranslation();
//        if (GeneralTool.isEmpty(institutionCourses)){
//            return;
//        }
//        institutionCourses = institutionCourses.stream().filter(i -> GeneralTool.isEmpty(i.getNameChn())).collect(Collectors.toList());
//        if (GeneralTool.isEmpty(institutionCourses)) {
//            return;
//        }
//
//        int batchSize = 10; // 每批处理数量为10个
//
//        ExecutorService pool = Executors.newFixedThreadPool(10);
//
//        AtomicInteger failureCount = new AtomicInteger(0);
//
//        int total = institutionCourses.size();
//        int batches = (total + batchSize - 1) / batchSize;
//
//        for (int i = 0; i < batches && failureCount.get() == 0; i++) {
//
//            final List<InstitutionCourse> batch = institutionCourses.subList(i * batchSize, Math.min((i + 1) * batchSize, total));
//
//            try {
//                List<CompletableFuture<Void>> futures = new ArrayList<>();
//                for (InstitutionCourse institutionCourse : batch) {
//                    CompletableFuture<Void> future = CompletableFuture.runAsync(() -> translateInstitutionCourse(institutionCourse), pool)
//                            .exceptionally(e -> {
//                                failureCount.incrementAndGet();
//                                log.error("翻译和保存过程中发生错误，记录该错误并继续处理其他任务。", e);
//                                return null;
//                            });
//                    futures.add(future);
//                }
//
//                // 所有翻译任务完成后，再执行批量更新
//                CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])
//                ).thenRunAsync(() -> {
//                    if (failureCount.get() == 0) {
//                        translationMapper.updateBatchInstitutionCourse(batch, new Date(), "[system]");
//                    }
//                }, pool);
//
//                Thread.sleep(1005);
//            } catch (Exception e) {
//                log.error("翻译和保存过程中发生错误，终止所有任务。", e);
//                failureCount.incrementAndGet();
//                pool.shutdownNow(); // 关闭线程池并尝试取消所有已提交但尚未开始的任务
//            }
//        }
//
//        // 如果在循环过程中没有出现错误，则等待所有任务完成
//        if (failureCount.get() == 0) {
//            // 在循环结束后，关闭线程池
//            pool.shutdown();
//            while (!pool.isTerminated()) {
//                // 等待所有任务完成
//            }
//        }
//        log.info("translate end!!");
//    }

//    @Override
//    public void batchTranslationInstitutionCourseIntroduction() {
//        List<InstitutionCourse> institutionCourses = translationMapper.getInstitutionCourseForTranslation();
//        if (GeneralTool.isEmpty(institutionCourses)){
//            return;
//        }
//        for (InstitutionCourse institutionCourse : institutionCourses) {
////            List<InstitutionTranslation> institutionTranslations = translationMapper.selectList(Wrappers.lambdaQuery(InstitutionTranslation.class)
////                    .eq(InstitutionTranslation::getFkTableName, TableEnum.INSTITUTION_COURSE.key)
////                    .eq(InstitutionTranslation::getFkTableId, institutionCourse.getId())
////                    .eq(InstitutionTranslation::getFkTranslationMappingId, 90L)
////                    .eq(InstitutionTranslation::getLanguageCode, "en-us")
////            );
////            if (GeneralTool.isNotEmpty(institutionTranslations)){
////                continue;
////            }
//            if(GeneralTool.isEmpty(institutionCourse.getIntroduction())){
//                continue;
//            }
//            //判断institutionCourse.getIntroduction()是否有中文
//            boolean b = MyStringUtils.containsChineseCharacters(institutionCourse.getIntroduction());
//            if (b){
//                continue;
//            }
//
//            if (GeneralTool.isNotEmpty(institutionCourse.getIntroduction())){
//                String result = null;
//                try {
//                    result = apiUtil.getTransResult(institutionCourse.getIntroduction(),TransApi.EN_US,TransApi.ZH_CN);
//                    if (GeneralTool.isNotEmpty(result)){
//                        institutionCourse.setIntroduction(result);
//                        institutionCourse.setGmtModified(new Date());
//                        institutionCourse.setGmtModifiedUser("[system]");
//                        institutionCourseMapper.updateById(institutionCourse);
//                    }
//                    Thread.sleep(105);
//                } catch (Exception e) {
//                    log.error("Translation error for institutionCourseId: {}", institutionCourse.getId(), e);
//                    throw new GetServiceException("翻译报错！");
//                }
//                log.info("institutionCourseId={}####translate success", institutionCourse.getId().toString());
//            }
//        }
//        log.info("translate end!!");
//
//    }

//    @Override
//    public void batchTranslationInstitutionCourEngScore() {
//        List<InstitutionCourseEngScore> institutionCourseEngScores = translationMapper.getInstitutionCourEngScoreForTranslation();
//        if (GeneralTool.isEmpty(institutionCourseEngScores)){
//            return;
//        }
//        Map<String, String> tranMap = Maps.newHashMap();
//        for (InstitutionCourseEngScore institutionCourseEngScore : institutionCourseEngScores) {
//
//            List<InstitutionTranslation> institutionTranslations = translationMapper.selectList(Wrappers.lambdaQuery(InstitutionTranslation.class)
//                    .eq(InstitutionTranslation::getFkTableName, "m_institution_course_eng_score")
//                    .eq(InstitutionTranslation::getFkTableId, institutionCourseEngScore.getId())
//                    .eq(InstitutionTranslation::getFkTranslationMappingId, 91L)
//                    .eq(InstitutionTranslation::getLanguageCode, "en-us")
//            );
//
//            if (GeneralTool.isNotEmpty(institutionTranslations)){
//                List<InstitutionTranslation> institutionTranslationList = translationMapper.selectList(Wrappers.lambdaQuery(InstitutionTranslation.class)
//                        .eq(InstitutionTranslation::getFkTableName, "m_institution_course_eng_score")
//                        .eq(InstitutionTranslation::getFkTableId, institutionCourseEngScore.getId())
//                        .eq(InstitutionTranslation::getFkTranslationMappingId, 91L)
//                        .eq(InstitutionTranslation::getLanguageCode, "zh-cn")
//                );
//                if (GeneralTool.isNotEmpty(institutionTranslationList)){
//                    continue;
//                }
//
//                InstitutionTranslation institutionTranslation = institutionTranslations.get(0);
//                InstitutionTranslation cnTran = BeanCopyUtils.objClone(institutionTranslation, InstitutionTranslation::new);
//                cnTran.setId(null);
//                cnTran.setLanguageCode("zh-cn");
//                cnTran.setGmtCreate(new Date());
//                cnTran.setGmtCreateUser("[system]");
//                cnTran.setGmtModified(null);
//                cnTran.setGmtModifiedUser(null);
//                String translation = institutionTranslation.getTranslation();
//                String result = tranMap.get(institutionTranslation.getTranslation());
//                if (GeneralTool.isEmpty(result)){
//                    try {
//                        result = apiUtil.getTransResult(translation,TransApi.EN_US,TransApi.ZH_CN);
//                        tranMap.put(institutionTranslation.getTranslation(),result);
//                    } catch (Exception e) {
//                        log.error("Translation error for institutionId: {}", institutionCourseEngScore.getId(), e);
//                        throw new GetServiceException("翻译报错！");
//                    }
//                }
//                if (GeneralTool.isNotEmpty(result)){
//                    cnTran.setTranslation(result);
//                    log.info("institutionId={}####translate success", institutionCourseEngScore.getId().toString());
//                    translationMapper.insert(cnTran);
//                    institutionCourseEngScore.setDescription(result);
//                    institutionCourseEngScore.setGmtModified(new Date());
//                    institutionCourseEngScore.setGmtModifiedUser("[system]");
//                    institutionalCourseEngScoreMapper.updateById(institutionCourseEngScore);
//                }
//
//            }
//
////            if (GeneralTool.isNotEmpty(institutionCourseEngScore.getDescriptionSource())){
////                InstitutionTranslation institutionTranslation = new InstitutionTranslation();
////                institutionTranslation.setFkTableId(institutionCourseEngScore.getId());
////                institutionTranslation.setFkTableName("m_institution_course_eng_score");
////                institutionTranslation.setLanguageCode("en-us");
////                institutionTranslation.setFkTranslationMappingId(92L);
////                institutionTranslation.setGmtCreate(new Date());
////                institutionTranslation.setGmtCreateUser("[system]");
////
////                institutionTranslation.setTranslation(institutionCourseEngScore.getDescriptionSource());
////                log.info("institutionId={}####translate success", institutionCourseEngScore.getId().toString());
////                translationMapper.insert(institutionTranslation);
////            }
//
//
//        }
//
//        log.info("translate end!!");
//    }

//    @Override
//    public void batchTranslationInstitutionCourseIntroduction() {
//        List<InstitutionCourse> institutionCourses = translationMapper.getInstitutionCourseForTranslation();
////        List<InstitutionCourseAcademicScore> institutionCourseAcademicScores = translationMapper.getInstitutionCourAcademicScoreForTranslation();
//        if (GeneralTool.isEmpty(institutionCourses)){
//            return;
//        }
//
//        Map<Long, List<InstitutionTranslation>> enTranMap = Maps.newHashMap();
//        Map<Long, List<InstitutionTranslation>> cnTranMap = Maps.newHashMap();
//
//
//        List<Long> courseIds = institutionCourses.stream().map(InstitutionCourse::getId).collect(Collectors.toList());
//
//        List<InstitutionTranslation> institutionTranslations = translationMapper.selectList(Wrappers.lambdaQuery(InstitutionTranslation.class)
//                .eq(InstitutionTranslation::getFkTableName, "m_institution_course")
//                .in(InstitutionTranslation::getFkTableId, courseIds)
//                .eq(InstitutionTranslation::getFkTranslationMappingId, 90L)
//                .eq(InstitutionTranslation::getLanguageCode, "en-us")
//        );
//        if (GeneralTool.isNotEmpty(institutionTranslations)){
//            enTranMap = institutionTranslations.stream().collect(Collectors.groupingBy(InstitutionTranslation::getFkTableId));
//        }
//
//        List<InstitutionTranslation> institutionTranslationList = translationMapper.selectList(Wrappers.lambdaQuery(InstitutionTranslation.class)
//                .eq(InstitutionTranslation::getFkTableName, "m_institution_course")
//                .in(InstitutionTranslation::getFkTableId, courseIds)
//                .eq(InstitutionTranslation::getFkTranslationMappingId, 90L)
//                .eq(InstitutionTranslation::getLanguageCode, "zh-cn")
//        );
//        if (GeneralTool.isNotEmpty(institutionTranslationList)){
//            cnTranMap = institutionTranslationList.stream().collect(Collectors.groupingBy(InstitutionTranslation::getFkTableId));
//        }
//
//        Map<String, String> tranMap = Maps.newHashMap();
//
//        for (InstitutionCourse institutionCourse : institutionCourses) {
//            List<InstitutionTranslation> enList = enTranMap.get(institutionCourse.getId());
//            List<InstitutionTranslation> cnList = cnTranMap.get(institutionCourse.getId());
//
//            if (GeneralTool.isEmpty(enList) && GeneralTool.isNotEmpty(institutionCourse.getIntroduction())){
//                InstitutionTranslation enTrans = new InstitutionTranslation();
//                enTrans.setLanguageCode("en-us");
//                enTrans.setFkTranslationMappingId(90L);
//                enTrans.setFkTableId(institutionCourse.getId());
//                enTrans.setFkTableName("m_institution_course");
//                enTrans.setTranslation(institutionCourse.getIntroduction());
//                enTrans.setGmtCreate(new Date());
//                enTrans.setGmtCreateUser("[system]");
//                translationMapper.insert(enTrans);
//                log.info("institutionCourseId={}####en-us save success", institutionCourse.getId().toString());
//            }else {
//
//                if (GeneralTool.isNotEmpty(cnList) || GeneralTool.isEmpty(institutionCourse.getIntroduction())){
//                    continue;
//                }
//
//                InstitutionTranslation cnTrans = new InstitutionTranslation();
//                cnTrans.setLanguageCode("zh-cn");
//                cnTrans.setFkTranslationMappingId(90L);
//                cnTrans.setFkTableId(institutionCourse.getId());
//                cnTrans.setFkTableName("m_institution_course");
//                cnTrans.setTranslation(institutionCourse.getIntroduction());
//                cnTrans.setGmtCreate(new Date());
//                cnTrans.setGmtCreateUser("[system]");
//                String translation = institutionCourse.getIntroduction();
//                String result = tranMap.get(institutionCourse.getIntroduction());
//                if (GeneralTool.isEmpty(result)){
//                    try {
//                        result = apiUtil.getTransResult(translation,TransApi.EN_US,TransApi.ZH_CN);
//                        log.info("institutionId={}####translate success", institutionCourse.getId().toString());
//                        tranMap.put(institutionCourse.getIntroduction(),result);
//                    } catch (Exception e) {
//                        log.error("Translation error for institutionId: {}", institutionCourse.getId(), e);
//                        throw new GetServiceException("翻译报错！");
//                    }
//                }
//                if (GeneralTool.isNotEmpty(result)){
//                    cnTrans.setTranslation(result);
//                    log.info("institutionId={}####save success", institutionCourse.getId().toString());
//                    translationMapper.insert(cnTrans);
//                }
//            }
//        }
//        log.info("translate end!!");
//    }

//    @Override
//    public void batchTranslationInstitutionDetail() {
//        List<Institution> institutions = translationMapper.getInstitutionForTranslationDetail();
//        if (GeneralTool.isEmpty(institutions)){
//            return;
//        }
//        for (Institution institution : institutions) {
//            List<InstitutionTranslation> institutionTranslations = translationMapper.selectList(Wrappers.lambdaQuery(InstitutionTranslation.class)
//                    .eq(InstitutionTranslation::getFkTableName, TableEnum.INSTITUTION.key)
//                    .eq(InstitutionTranslation::getFkTableId, institution.getId())
//                    .eq(InstitutionTranslation::getFkTranslationMappingId, 37L)
//                    .eq(InstitutionTranslation::getLanguageCode, "en-us")
//            );
//            if (GeneralTool.isNotEmpty(institutionTranslations)){
//                continue;
//            }
//
//            InstitutionTranslation institutionTranslation = new InstitutionTranslation();
//            institutionTranslation.setFkTableId(institution.getId());
//            institutionTranslation.setFkTableName(TableEnum.INSTITUTION.key);
//            institutionTranslation.setLanguageCode("en-us");
//            institutionTranslation.setFkTranslationMappingId(37L);
//            institutionTranslation.setGmtCreate(new Date());
//            institutionTranslation.setGmtCreateUser("[system]");
//
//            if (GeneralTool.isNotEmpty(institution.getDetail())){
//                String result = null;
//                try {
//                    result = apiUtil.getTransResult(institution.getDetail(),TransApi.ZH_CN,TransApi.EN_US);
//                } catch (Exception e) {
//                    log.error("Translation error for institutionId: {}", institution.getId(), e);
//                    throw new GetServiceException("翻译报错！");
//                }
//
//                institutionTranslation.setTranslation(result);
//                log.info("institutionId={}####translate success", institution.getId().toString());
//                translationMapper.insert(institutionTranslation);
//            }
//        }
//        log.info("translate end!!");
//    }


//    private void translateAndSave(InstitutionCourseSubject institutionCourseSubject) {
//        String result = null;
//        try {
//            result = apiUtil.getTransResult(institutionCourseSubject.getSubjectName(), TransApi.EN_US, TransApi.ZH_CN);
//        } catch (Exception e) {
//            log.error("Translation error for subjectId: {}", institutionCourseSubject.getId(), e);
//            throw new GetServiceException("翻译报错！");
//        }
//
//        institutionCourseSubject.setSubjectNameChn(result);
//        log.info("institutionCourseSubjectId={}####translate success", institutionCourseSubject.getId().toString());
//    }
//
//    private void translateInstitutionCourse(InstitutionCourse institutionCourse) {
//        String result = null;
//        try {
//            result = apiUtil.getTransResult(institutionCourse.getName(), TransApi.EN_US, TransApi.ZH_CN);
//        } catch (Exception e) {
//            log.error("Translation error for subjectId: {}", institutionCourse.getId(), e);
//            throw new GetServiceException("翻译报错！");
//        }
//
//        institutionCourse.setNameChn(result);
//        log.info("institutionCourseSubjectId={}####translate success", institutionCourse.getId().toString());
//    }

    private void doTranslateAreaCityDiversions(TransVo transVo, String languageCode, List<TranslationDto> translationDtos, String cloumnName, String tableName, String createUser) {
        List<Long> ids = getNotTranslateIds(languageCode, cloumnName, tableName);
        areaCityDiversionCount.getSumCount().addAndGet(ids.size());
        Long mappingId = getMappingIdByTableNameAndCloumnName(tableName, cloumnName);
        List<AreaCityDivision> areaCityDivisions = areaCityDivisionMapper.selectList(Wrappers.<AreaCityDivision>lambdaQuery().in(AreaCityDivision::getId, ids));
        Map<Long, List<AreaCityDivision>> collect = areaCityDivisions.stream().collect(Collectors.groupingBy(AreaCityDivision::getId));
//        AreaCityDivision areaCityDivision = areaCityDivisionMapper.selectById(id);
        if (ids.size() > 0) {
            for (Long id : ids) {
                transVo.setFrom(TransApi.ZH_CN);
                TranslationDto translationDto = new TranslationDto();
                String transResult = "";
                AreaCityDivision areaCityDivision = collect.get(id).get(0);
                if (NAME.key.equals(cloumnName)) {
                    transVo.setQuery(areaCityDivision.getName());
                    if (areaCityDivision.getName() != null) {
                        if ("".equals(areaCityDivision.getName().trim())) {
                            areaCityDiversionCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCityDiversionCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCityDiversionCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }
                if (ColumnNameEnum.STREETS_NAME.key.equals(cloumnName)) {
                    transVo.setQuery(areaCityDivision.getStreetsName());
                    if (areaCityDivision.getStreetsName() != null) {
                        if ("".equals(areaCityDivision.getStreetsName().trim())) {
                            areaCityDiversionCount.getErrorCount().incrementAndGet();
                            break;
                        } else {
                            try {
                                transResult = TransApi.getTransResult(transVo.getQuery(), transVo.getFrom(), transVo.getTo()).getTrans_result().get(0).getDst();
                            } catch (Exception e) {
                                e.printStackTrace();
                                areaCityDiversionCount.getErrorCount().incrementAndGet();
                            }
                        }
                    } else {
                        areaCityDiversionCount.getErrorCount().incrementAndGet();
                        break;
                    }
                }

                translationDto.setTranslation(transResult);
                translationDto.setLanguageCode(languageCode);
                translationDto.setFkTableId(id);
                translationDto.setFkTranslationMappingId(mappingId);
                translationDto.setFkTableName(TableEnum.INSTITUTION_AREA_CITY_DIVISION.key);
                try {
                    BeanUtils.setProperty(translationDto, "gmtCreate", new Date());
                    BeanUtils.setProperty(translationDto, "gmtCreateUser", createUser);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                areaCityDiversionCount.getCompletedCount().incrementAndGet();
                translationDtos.add(translationDto);
                if (translationDtos.size() >= 10) {
                    translationMappingService.updateTranslations(translationDtos);
                    translationDtos.clear();
                    log.info("insert success--");
                }
                log.info("----------------translate success:tableId:{}--------------------", translationDto.getFkTableId());
            }
        }
    }

    private void updateTranslation(List<Long> descriptionIds) {
        String src;
        for (Long descriptionId : descriptionIds) {
            InstitutionTranslation traslation = getTranslationById(descriptionId);
            src = traslation.getTranslation();
            String dst = KeyWordUtils.replace(src);
            traslation.setTranslation(dst);
            List<TranslationDto> list = new ArrayList<>();
            TranslationDto translationDto = BeanCopyUtils.objClone(traslation, TranslationDto::new);
            list.add(translationDto);
            if (list.size() > 0) {
                translationMappingService.updateTranslations(list);
            }
        }
    }

    public void batchtrans(TransVo transVo, String tableName, String createUser) {
        String languageCode = "";
        if (GeneralTool.isEmpty(transVo.getTo())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if ("zh-cn".equals(transVo.getTo())) {
            languageCode = "zh-cn";
        }
        if ("en-us".equals(transVo.getTo())) {
            languageCode = "en-us";
        }
        if ("zh-hk".equals(transVo.getTo())) {
            languageCode = "zh-hk";
        }
        if ("zh-tw".equals(transVo.getTo())) {
            languageCode = "zh-tw";
        }
        if (tableName.equals(TableEnum.NEWS.key)) {
            transVo.getTo();
        }
    }

    enum ColumnNameEnum {
        /**
         * 标题
         */
        TITLE("title", "标题"),
        /**
         * 描述
         */
        DESCRIPTION("description", "描述"),
        /**
         * 简介
         */
        PROFILE("profile", "简介"),
        /**
         * 中文名称
         */
        NAME_CHN("name_chn", "中文名称"),
        /**
         * 首都
         */
        CAPITAL("capital", "首都"),
        /**
         * 人口
         */
        POPULATION("population", "人口"),
        /**
         * 面积
         */
        AREA("area", "面积"),
        /**
         * 语言
         */
        LANGUAGE("language", "语言"),
        /**
         * 宗教
         */
        RELIGION("religion", "宗教"),
        /**
         * 时差
         */
        TIME_DIFFERENCE("time_difference", "时差"),
        /**
         * 现任国家元首
         */
        PRESIDENT("president", "现任国家元首"),
        /**
         * 国旗意义
         */
        FLAG_MEANING("flag_meaning", "国旗意义"),
        /**
         * 国徽意义
         */
        EMBLEM_MEANING("emblem_meaning", "国徽意义"),
        /**
         * 备注
         */
        REMARK("remark", "备注"),
        /**
         * 街道名称
         */
        STREETS_NAME("streets_name", "街道名称"),
        /**
         * 名称
         */
        NAME("name", "名称"),
        /**
         * 学校简称
         */
        SHORT_NAME("short_name","学校简称"),
        /**
         * 学校性质
         */
        NATURE("nature","学校性质"),
        /**
         * 学校描述
         */
        DETAIL("detail","学校描述"),
        /**
         * 显示名称
         */
        NAME_DISPLAY("name_display","显示名称"),
        /**
         * 学校中文简称
         */
        SHORT_NAME_CHN("short_name_chn","学校中文简称"),
        /**
         * 地址
         */
        ADDRESS("address","地址"),
        /**
         * 开学时间描述
         */
        START_DATE_NOTE("start_date_note","开学时间描述"),
        /**
         * 申请时间描述
         */
        APPLY_DATE_NOTE("apply_date_note","申请时间描述"),
        /**
         * 课程学费说明
         */
        FEE_NOTE("fee_note","课程学费说明"),
        /**
         * 课程总时长说明
         */
        DURATION_NOTE("duration_note","课程总时长说明"),
        /**
         * 核心课程
         */
        CORE_COURSE("core_course","核心课程"),
        /**
         * 录取标准
         */
        ENTRY_STANDARDS("entry_standards","录取标准"),
        /**
         * 职业发展
         */
        OCCUPATION_DEVELOPMENT("occupation_development","职业发展"),
        /**
         * 简介
         */
        INTRODUCTION("introduction","简介");

        /**
         * 新闻表字段枚举
         */
        public static final ColumnNameEnum[] NEWS_COLUMN_NAME = new ColumnNameEnum[]{TITLE, DESCRIPTION};
        /**
         * 资讯表字段枚举
         */
        public static final ColumnNameEnum[] INSTITUTION_INFO_COLUMN_NAME = new ColumnNameEnum[]{TITLE, DESCRIPTION};
        /**
         * 国家表字段枚举
         */
        public static final ColumnNameEnum[] INSTITUTION_COUNTRY_COLUMN_NAME = new ColumnNameEnum[]{NAME_CHN, CAPITAL, POPULATION, AREA, LANGUAGE, RELIGION, TIME_DIFFERENCE, PRESIDENT, FLAG_MEANING, EMBLEM_MEANING, REMARK};
        /**
         * key
         */
        public String key;
        /**
         * value
         */
        public String value;

        ColumnNameEnum(String key, String value) {
            this.key = key;
            this.value = value;
        }

        public static String getValue(String key) {
            ColumnNameEnum[] columnNameEnums = values();
            for (ColumnNameEnum columnNameEnum : columnNameEnums) {
                if (columnNameEnum.key.equals(key)) {
                    return columnNameEnum.value();
                }
            }
            return null;
        }

        public static String getKey(String value) {
            ColumnNameEnum[] columnNameEnums = values();
            for (ColumnNameEnum columnNameEnum : columnNameEnums) {
                if (columnNameEnum.value.equals(value)) {
                    return columnNameEnum.key();
                }
            }
            return null;
        }

        /**
         * 将枚举转换为list
         *
         * @param enums
         * @return
         */
        public static List<Map<String, Object>> enums2Arrays(ColumnNameEnum[] enums) {
            if (GeneralTool.isEmpty(enums)) {
                return new ArrayList<>();
            }
            List<Map<String, Object>> list = new ArrayList<>();
            Arrays.stream(enums).forEach(item -> {
                Map<String, Object> map = new HashMap<>();
                map.put("key", item.key);
                map.put("value", item.value);
                list.add(map);
            });
            return list;
        }

        private String key() {
            return this.key;
        }

        private String value() {
            return this.value;
        }
    }


}
