package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionProviderTypeMapper;
import com.get.institutioncenter.dto.InstitutionProviderTypeDto;
import com.get.institutioncenter.vo.InstitutionProviderTypeVo;
import com.get.institutioncenter.entity.InstitutionProviderType;
import com.get.institutioncenter.service.IInstitutionProviderTypeService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/12 11:22
 * @verison: 1.0
 * @description: 学校提供商类型管理实现类
 */
@Service
public class InstitutionProviderTypeServiceImpl extends BaseServiceImpl<InstitutionProviderTypeMapper, InstitutionProviderType> implements IInstitutionProviderTypeService {

    @Resource
    private InstitutionProviderTypeMapper institutionProviderTypeMapper;

    @Resource
    private UtilService utilService;

    @Override
    public InstitutionProviderTypeVo findInstitutionProviderTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionProviderType institutionProviderType = institutionProviderTypeMapper.selectById(id);
        return BeanCopyUtils.objClone(institutionProviderType, InstitutionProviderTypeVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<InstitutionProviderTypeDto> institutionProviderTypeDtos) {
        if (GeneralTool.isEmpty(institutionProviderTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = institutionProviderTypeMapper.getMaxViewOrder();
        for (InstitutionProviderTypeDto institutionProviderTypeDto : institutionProviderTypeDtos) {
            if (GeneralTool.isEmpty(institutionProviderTypeDto.getId())) {
                if (validateAdd(institutionProviderTypeDto)) {
                    InstitutionProviderType institutionProviderType = BeanCopyUtils.objClone(institutionProviderTypeDto, InstitutionProviderType::new);
                    institutionProviderType.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(institutionProviderType);
                    institutionProviderTypeMapper.insertSelective(institutionProviderType);
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("provider_type_name_or_key_exists"));
                }
            } else {
                if (validateUpdate(institutionProviderTypeDto)) {
                    InstitutionProviderType institutionProviderType = BeanCopyUtils.objClone(institutionProviderTypeDto, InstitutionProviderType::new);
                    utilService.updateUserInfoToEntity(institutionProviderType);
                    institutionProviderTypeMapper.updateById(institutionProviderType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("provider_type_name_or_key_exists"));
                }
            }

        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findInstitutionProviderTypeById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = institutionProviderTypeMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public InstitutionProviderTypeVo updateInstitutionProviderType(InstitutionProviderTypeDto institutionProviderTypeDto) {
        if (institutionProviderTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InstitutionProviderType result = institutionProviderTypeMapper.selectById(institutionProviderTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (validateUpdate(institutionProviderTypeDto)) {
            InstitutionProviderType institutionProviderType = BeanCopyUtils.objClone(institutionProviderTypeDto, InstitutionProviderType::new);
            utilService.updateUserInfoToEntity(institutionProviderType);
            institutionProviderTypeMapper.updateById(institutionProviderType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("provider_type_name_or_key_exists"));
        }
        return findInstitutionProviderTypeById(institutionProviderTypeDto.getId());
    }

    @Override
    public List<InstitutionProviderTypeVo> getInstitutionProviderTypes(InstitutionProviderTypeDto institutionProviderTypeDto, Page page) {
        LambdaQueryWrapper<InstitutionProviderType> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(institutionProviderTypeDto)) {
            if (GeneralTool.isNotEmpty(institutionProviderTypeDto.getKeyWord())) {
                wrapper.and(wrapper_ ->
                        wrapper_.like(InstitutionProviderType::getTypeName, institutionProviderTypeDto.getKeyWord()).or().like(InstitutionProviderType::getTypeKey, institutionProviderTypeDto.getKeyWord()));
            }
        }
        wrapper.orderByDesc(InstitutionProviderType::getViewOrder);
        //获取分页数据
        IPage<InstitutionProviderType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionProviderType> institutionProviderTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<InstitutionProviderTypeVo> convertDatas = new ArrayList<>();
        for (InstitutionProviderType institutionProviderType : institutionProviderTypes) {
            InstitutionProviderTypeVo institutionProviderTypeVo = BeanCopyUtils.objClone(institutionProviderType, InstitutionProviderTypeVo::new);
            convertDatas.add(institutionProviderTypeVo);
        }
        return convertDatas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<InstitutionProviderTypeDto> institutionProviderTypeDtos) {

        if (GeneralTool.isEmpty(institutionProviderTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InstitutionProviderType ro = BeanCopyUtils.objClone(institutionProviderTypeDtos.get(0), InstitutionProviderType::new);
        Integer oneorder = ro.getViewOrder();
        InstitutionProviderType rt = BeanCopyUtils.objClone(institutionProviderTypeDtos.get(1), InstitutionProviderType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        institutionProviderTypeMapper.updateById(ro);
        institutionProviderTypeMapper.updateById(rt);

    }

    @Override
    public List<BaseSelectEntity> getinstitutionProviderTypeList() {
        return institutionProviderTypeMapper.getinstitutionProviderTypeList();
    }

    private boolean validateAdd(InstitutionProviderTypeDto institutionProviderTypeDto) {
        LambdaQueryWrapper<InstitutionProviderType> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(institutionProviderTypeDto)) {
            wrapper.eq(InstitutionProviderType::getTypeName, institutionProviderTypeDto.getTypeName());
            wrapper.or().eq(InstitutionProviderType::getTypeKey, institutionProviderTypeDto.getTypeKey());
//            if (GeneralTool.isNotEmpty(institutionProviderTypeDto.getKeyWord())) {
//                wrapper.and(wrapper_ ->
//                        wrapper_.eq(InstitutionProviderType::getTypeName,institutionProviderTypeDto.getTypeName()).or().like(InstitutionProviderType::getTypeKey, institutionProviderTypeDto.getTypeKey()));
//            }
        }
        List<InstitutionProviderType> list = this.institutionProviderTypeMapper.selectList(wrapper);
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(InstitutionProviderTypeDto institutionProviderTypeDto) {
        LambdaQueryWrapper<InstitutionProviderType> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(institutionProviderTypeDto)) {
            wrapper.eq(InstitutionProviderType::getTypeName, institutionProviderTypeDto.getTypeName());
            wrapper.or().eq(InstitutionProviderType::getTypeKey, institutionProviderTypeDto.getTypeKey());
//            if (GeneralTool.isNotEmpty(institutionProviderTypeDto.getKeyWord())) {
//                wrapper.and(wrapper_ ->
//                        wrapper_.eq(InstitutionProviderType::getTypeName,institutionProviderTypeDto.getTypeName()).or().like(InstitutionProviderType::getTypeKey, institutionProviderTypeDto.getTypeKey()));
//            }
        }
        List<InstitutionProviderType> list = this.institutionProviderTypeMapper.selectList(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(institutionProviderTypeDto.getId());
    }
}
