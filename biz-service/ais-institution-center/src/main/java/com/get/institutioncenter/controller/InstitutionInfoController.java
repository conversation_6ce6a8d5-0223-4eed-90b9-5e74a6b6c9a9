package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.InstitutionInfoVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.service.IInstitutionInfoService;
import com.get.institutioncenter.service.IMediaAndAttachedService;
import com.get.institutioncenter.dto.InstitutionInfoDto;
import com.get.institutioncenter.dto.query.InstitutionInfoQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/8/11
 * @TIME: 12:49
 * @Description:
 **/
@Api(tags = "资讯管理")
@RestController
@RequestMapping("/institution/institutionInfo")
public class InstitutionInfoController {
    @Resource
    private IInstitutionInfoService institutionInfoService;
    @Resource
    private IMediaAndAttachedService mediaAndAttachedService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/学校资讯管理/学校资讯详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionInfoVo> detail(@PathVariable("id") Long id) {
        InstitutionInfoVo data = institutionInfoService.findInstitutionInfoById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", data);
        return responseBo;
    }

    /**
     * 新增信息
     *
     * @param institutionInfoDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校资讯管理/新增学校资讯")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(InstitutionInfoDto.Add.class) InstitutionInfoDto institutionInfoDto) {
        return SaveResponseBo.ok(institutionInfoService.addInstitutionInfo(institutionInfoDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校资讯管理/删除学校资讯")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionInfoService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param institutionInfoDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校资讯管理/更新学校资讯")
    @PostMapping("update")
    public ResponseBo<InstitutionInfoVo> update(@RequestBody @Validated(InstitutionInfoDto.Update.class) InstitutionInfoDto institutionInfoDto) {
        return UpdateResponseBo.ok(institutionInfoService.updateInstitutionInfo(institutionInfoDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校资讯管理/查询学校资讯")
    @PostMapping("datas")
    public ResponseBo<InstitutionInfoVo> datas(@RequestBody SearchBean<InstitutionInfoQueryDto> page) {
        List<InstitutionInfoVo> datas = institutionInfoService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @param id
     * @return
     * @
     */

    @ApiOperation(value = "删除文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校资讯管理/删除文件")
    @PostMapping("deleteFile/{id}")
    public ResponseBo upload(@PathVariable("id") Long id) {
        mediaAndAttachedService.deleteMediaAttached(id);
        return ResponseBo.ok();
    }


    /**
     * @param mediaAttachedVo
     * @return
     * @
     */

    @ApiOperation(value = "保存文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校资讯管理/保存附件资料")
    @PostMapping("addMediaAndAttached")
    public ResponseBo addMediaAndAttached(@RequestBody @Validated(MediaAndAttachedDto.Add.class) List<MediaAndAttachedDto> mediaAttachedVo) {
        return UpdateResponseBo.ok(institutionInfoService.addInstitutionInfoMedia(mediaAttachedVo));
    }

    /**
     * 附件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "附件类型下拉框数据", notes = "")
    @GetMapping("findMediaAndAttachedType")
    public ResponseBo findMediaAndAttachedType() {
        List<Map<String, Object>> datas = institutionInfoService.findMediaAndAttachedType();
        return new ListResponseBo<>(datas);
    }

    /**
     * 查询学校资讯附件
     *
     * @param voSearchBean
     * @return
     * @
     */
    @ApiOperation(value = "查询学校资讯附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校资讯管理/查询学校资讯附件")
    @PostMapping("getInstitutionInfoMedia")
    public ResponseBo<MediaAndAttachedVo> getInstitutionInfoMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> institutionMedia = institutionInfoService.getInstitutionInfoMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(institutionMedia, page);
    }

    /**
     * 公开对象下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "公开对象下拉框数据", notes = "")
    @GetMapping("getPublicObjectsSelect")
    public ResponseBo getPublicObjectsSelect() {
        List<Map<String, Object>> datas = institutionInfoService.getPublicObjectsSelect();
        return new ListResponseBo<>(datas);
    }
}
