package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.InstitutionCourseAcademicScoreVo;
import com.get.institutioncenter.entity.InstitutionCourseAcademicScore;
import com.get.institutioncenter.dto.InstitutionCourseAcademicScoreDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/8
 * @TIME: 11:45
 * @Description:
 **/
public interface IInstitutionCourseAcademicScoreService extends BaseService<InstitutionCourseAcademicScore> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionCourseAcademicScoreVo findInstitutionCourseAcademicScoreById(Long id);

    /**
     * 列表数据
     *
     * @param institutionCourseAcademicScoreDto
     * @param page
     * @return
     */
    List<InstitutionCourseAcademicScoreVo> getInstitutionCourseAcademicScores(InstitutionCourseAcademicScoreDto institutionCourseAcademicScoreDto, Page page);

    /**
     * 修改
     *
     * @param institutionCourseAcademicScoreDto
     * @return
     */
    InstitutionCourseAcademicScoreVo updateInstitutionCourseAcademicScore(InstitutionCourseAcademicScoreDto institutionCourseAcademicScoreDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param institutionCourseAcademicScoreDtos
     * @return
     */
    void batchAdd(List<InstitutionCourseAcademicScoreDto> institutionCourseAcademicScoreDtos);

    /**
     * 条件查找
     *
     * @return
     */
    List<String> getScoreTypeByCondition(Long id);

    /**
     * 标准测试类型下拉
     *
     * @return
     */
    List<Map<String, Object>> getStandardTestType();

    /**
     * 删除记录
     *
     * @return
     */
    void deleteCourseAcademicScoreByCourseId(Long id);

}
