package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.vo.AreaCountryInfoTypeVo;
import com.get.institutioncenter.dto.AreaCountryInfoTypeDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/13 16:40
 * @verison: 1.0
 * @description:
 */
public interface IAreaCountryInfoTypeService {
    /**
     * @return com.get.institutioncenter.vo.AreaCountryInfoTypeVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    AreaCountryInfoTypeVo findAreaCountryInfoTypeById(Long id);

    /**
     * @return void
     * @Description :批量新增信息
     * @Param [areaCountryInfoTypeDtos]
     * <AUTHOR>
     */
    void batchAdd(ValidList<AreaCountryInfoTypeDto> areaCountryInfoTypeDtos);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.institutioncenter.vo.AreaCountryInfoTypeVo
     * @Description :修改
     * @Param [areaCountryInfoTypeDto]
     * <AUTHOR>
     */
    AreaCountryInfoTypeVo updateAreaCountryInfoType(AreaCountryInfoTypeDto areaCountryInfoTypeDto);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.AreaCountryInfoTypeVo>
     * @Description :列表
     * @Param [areaCountryInfoTypeDto, page]
     * <AUTHOR>
     */
    List<AreaCountryInfoTypeVo> getAreaCountryInfoTypes(AreaCountryInfoTypeDto areaCountryInfoTypeDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [areaCountryInfoTypeDtos]
     * <AUTHOR>
     */
    void movingOrder(List<AreaCountryInfoTypeDto> areaCountryInfoTypeDtos);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.AreaCountryInfoTypeVo>
     * @Description :国家资讯类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<AreaCountryInfoTypeVo> getAreaCountryInfoTypeList();

    /**
     * @return java.lang.String
     * @Description :通过id获取类型名称
     * @Param [areaCountryInfoTypeId]
     * <AUTHOR>
     */
    String getAreaCountryInfoTypeNameById(Long areaCountryInfoTypeId);
}
