package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.Assert;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.institutioncenter.dao.InstitutionAppFeeMapper;
import com.get.institutioncenter.dao.InstitutionAppFeeMapper2;
import com.get.institutioncenter.dao.InstitutionCourseAppInfoMapper;
import com.get.institutioncenter.dao.InstitutionMapper;
import com.get.institutioncenter.vo.InstitutionAppFeeVo;
import com.get.institutioncenter.vo.InstitutionAppFeeVo2;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.entity.InstitutionAppFee;
import com.get.institutioncenter.entity.InstitutionAppFee2;
import com.get.institutioncenter.entity.InstitutionCourseAppInfo;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.dto.InstitutionAppFeeDto;
import com.get.institutioncenter.dto.InstitutionAppFeeDto2;
import com.get.institutioncenter.dto.InstitutionCourseAppInfoDataProcessDto;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import com.get.institutioncenter.dto.query.InstitutionAppFeeQueryDto;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.get.common.eunms.ProjectExtraEnum.InstitutionAppFeeLevelType;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/5 21:51
 */
@Service
public class InstitutionAppFeeServiceImpl extends BaseServiceImpl<InstitutionAppFeeMapper, InstitutionAppFee> implements IInstitutionAppFeeService {

    @Resource
    private InstitutionAppFeeMapper institutionAppFeeMapper;
    @Resource
    private InstitutionAppFeeMapper2 institutionAppFeeMapper2;
    @Resource
    private IInstitutionFacultyService iInstitutionFacultyService;
    @Resource
    private ICourseTypeGroupService courseTypeGroupService;
    @Resource
    private ICourseTypeService courseTypeService;
    @Resource
    private IInstitutionMajorService iInstitutionMajorService;
    @Resource
    private IInstitutionCourseService iInstitutionCourseService;
    @Resource
    private IAreaCountryService areaCountryService;
    @Resource
    private UtilService<Object> utilService;
    @Resource
    @Lazy
    private IInstitutionService institutionService;
    @Resource
    @Lazy
    private IInstitutionCourseAppInfoService iInstitutionCourseAppInfoService;

    @Resource
    private InstitutionCourseAppInfoMapper institutionCourseAppInfoMapper;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private MediaAndAttachedServiceImpl mediaAndAttachedService;
    @Resource
    private IFileCenterClient iFileCenterClient;

    @Override
    public Long addInstitutionAppFee(InstitutionAppFeeDto institutionAppFeeDto) {
        InstitutionAppFee institutionAppFee = BeanCopyUtils.objClone(institutionAppFeeDto, InstitutionAppFee::new);
        utilService.setCreateInfo(institutionAppFee);
        int i = institutionAppFeeMapper.insert(institutionAppFee);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        List<InstitutionCourseAppInfoDataProcessDto.CourseAppInfoResultMappingVo> courseAppInfos = institutionAppFeeDto.getCourseAppInfos();
        iInstitutionCourseAppInfoService.batchAdd(courseAppInfos,institutionAppFee.getId(),TableEnum.INSTITUTION_APP_INFO_FEE.key, institutionAppFeeDto.getEffectiveDate());
        return institutionAppFee.getId();
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        InstitutionAppFee institutionAppFee = institutionAppFeeMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionAppFee)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = institutionAppFeeMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        institutionCourseAppInfoMapper.delete(Wrappers.<InstitutionCourseAppInfo>lambdaQuery()
                .eq(InstitutionCourseAppInfo::getFkTableName, TableEnum.INSTITUTION_APP_INFO_FEE.key)
                .eq(InstitutionCourseAppInfo::getFkTableId, id));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public InstitutionAppFeeVo updateInstitutionAppFee(InstitutionAppFeeDto institutionAppFeeDto) {
        if (GeneralTool.isEmpty(institutionAppFeeDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InstitutionAppFeeVo institutionAppFee = institutionAppFeeMapper.selectInfoById(institutionAppFeeDto.getId());
        if (GeneralTool.isEmpty(institutionAppFee)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionAppFee institutionAppFee1 = BeanCopyUtils.objClone(institutionAppFeeDto, InstitutionAppFee::new);
        utilService.updateUserInfoToEntity(institutionAppFee1);
        institutionAppFeeMapper.updateById(institutionAppFee1);
        iInstitutionCourseAppInfoService.batchUpdate(institutionAppFeeDto.getCourseAppInfos(),institutionAppFee1.getId(),TableEnum.INSTITUTION_APP_INFO_FEE.key, institutionAppFeeDto.getEffectiveDate());
        return findInstitutionAppFeeById(institutionAppFee.getId());
    }

    @Override
    public List<InstitutionAppFeeVo> datas(InstitutionAppFeeQueryDto data, SearchBean<InstitutionAppFeeQueryDto> page) {
        IPage<InstitutionAppFee> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())));
        List<Long> countryIds = SecureUtil.getCountryIds();
        List<InstitutionAppFeeVo> datas = institutionAppFeeMapper.datas(pages, data,countryIds);
        page.setAll((int) pages.getTotal());
        //数据封装
        packProcess(datas,false);
        return datas;
    }
    /**
     * 结果处理
     * @param institutionAppFeeVos
     * @param flag 是否要获取图片url
     */
    private void packProcess(List<InstitutionAppFeeVo> institutionAppFeeVos, boolean flag){
        List<InstitutionCourseAppInfoDataProcessDto> processVos = BeanCopyUtils.copyListProperties(institutionAppFeeVos, InstitutionCourseAppInfoDataProcessDto::new);
        iInstitutionCourseAppInfoService.packageInfo(processVos,flag,TableEnum.INSTITUTION_APP_INFO_FEE.key);
        Map<Long, InstitutionCourseAppInfoDataProcessDto> voMap = processVos.stream().collect(Collectors.toMap(InstitutionCourseAppInfoDataProcessDto::getId, Function.identity()));
        for (InstitutionAppFeeVo scholarship : institutionAppFeeVos) {
            InstitutionCourseAppInfoDataProcessDto processVo = voMap.get(scholarship.getId());
            BeanUtils.copyProperties(processVo,scholarship);
        }
    }



    @Override
    public InstitutionAppFeeVo findInstitutionAppFeeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionAppFeeVo institutionAppFeeVo = institutionAppFeeMapper.selectInfoById(id);
        InstitutionCourseAppInfoDataProcessDto processVo = BeanCopyUtils.objClone(institutionAppFeeVo, InstitutionCourseAppInfoDataProcessDto::new);
        List<InstitutionCourseAppInfoDataProcessDto> list = new ArrayList<>(1);
        list.add(processVo);
        iInstitutionCourseAppInfoService.packageInfo(list,false,TableEnum.INSTITUTION_APP_INFO_FEE.key);
        Assert.notNull(processVo,"processVo is null");
        BeanUtils.copyProperties(processVo, institutionAppFeeVo);
        return institutionAppFeeVo;
    }


    /**
     * Author Cream
     * Description : //申请费用信息优先匹配查询
     * Date 2022/11/22 10:11
     * Params:
     * Return
     * @return
     */
    @Override
    public List<InstitutionAppFeeVo> priorityMatchingQuery(WeScholarshipAppDto weScholarshipAppDto) {
        List<InstitutionCourseAppInfoDataProcessDto> processVo = iInstitutionCourseAppInfoService.testPriority(weScholarshipAppDto, TableEnum.INSTITUTION_APP_INFO_FEE.key);
        if (GeneralTool.isEmpty(processVo)) {
            return null;
        }
        List<Long> ids = processVo.stream().map(InstitutionCourseAppInfoDataProcessDto::getId).collect(Collectors.toList());
        List<InstitutionAppFeeVo> institutionScholarshipDtos = institutionAppFeeMapper.selectInfoByIds(ids);
        Map<Long, InstitutionCourseAppInfoDataProcessDto> collect = processVo.stream().collect(Collectors.toMap(InstitutionCourseAppInfoDataProcessDto::getId, Function.identity()));
        for (InstitutionAppFeeVo appFeeDto : institutionScholarshipDtos) {
            BeanUtils.copyProperties(collect.get(appFeeDto.getId()),appFeeDto);
        }
        institutionScholarshipDtos.sort(Comparator.comparing(InstitutionAppFeeVo::getGmtPriorityTime).reversed());
        return institutionScholarshipDtos;
    }
    /**
     * 获取最优先匹配信息
     * @param list
     * @return
     */
    private InstitutionAppFeeVo priorityGet(List<InstitutionAppFeeVo> list){
        list.sort(((o1, o2) -> o2.getPriority()-o1.getPriority()));
        Integer maxPriority = list.get(0).getPriority();
        Map<Integer, List<InstitutionAppFeeVo>> collect = list.stream().collect(Collectors.groupingBy(InstitutionAppFeeVo::getPriority));
        //匹配数的最高优先级集合
        List<InstitutionAppFeeVo> maxPriorityList = collect.get(maxPriority);
        for (InstitutionAppFeeVo dto : maxPriorityList) {
            if (dto.getFk().equals(dto.getFc())) {
                return dto;
            }
            dto.setPriority(dto.getFc().split(",").length);
        }
        maxPriorityList.sort(Comparator.comparing(InstitutionAppFeeVo::getPriority));
        return maxPriorityList.get(0);
    }

    @Override
    public List<InstitutionAppFeeVo2> getWcInstitutionAppFeeDatas(InstitutionAppFeeDto2 data, SearchBean<InstitutionAppFeeDto2> page) {

        List<Long> institutionIds = null;
        if (GeneralTool.isNotEmpty(data.getFkCountryId())) {
            List<BaseSelectEntity> institutionListByCountryId = institutionMapper.getInstitutionListByCountryId(data.getFkCountryId());
            institutionIds = institutionListByCountryId.stream().filter(Objects::nonNull).map(BaseSelectEntity::getId).collect(Collectors.toList());
        }


        IPage<InstitutionAppFeeVo2> ipage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<InstitutionAppFeeVo2> institutionAppFeeDtos = institutionAppFeeMapper2.getWcInstitutionAppFeeDatas(ipage, data, institutionIds);
        page.setAll((int) ipage.getTotal());
        page.setCurrentResult((int) ipage.getSize());

        if (GeneralTool.isEmpty(institutionAppFeeDtos)) {
            return institutionAppFeeDtos;
        }


        Iterator<InstitutionAppFeeVo2> iterator = institutionAppFeeDtos.iterator();
        while (iterator.hasNext()) {
            InstitutionAppFeeVo2 next = iterator.next();
            boolean b = !next.getPublicLevel().contains("0");
            if (!b) {
                iterator.remove();
            }
        }
        if (GeneralTool.isEmpty(institutionAppFeeDtos)) {
            return institutionAppFeeDtos;
        }


        Set<Long> collect = institutionAppFeeDtos.stream().map(InstitutionAppFeeVo2::getFkInstitutionId).collect(Collectors.toSet());
        Map<Long, Institution> institutionNamesByIds = institutionService.getInstitutionByIds(collect);
        institutionAppFeeDtos.stream().forEach(d -> {
            Institution institution = institutionNamesByIds.get(d.getFkInstitutionId());
            d.setFkInstitutionNameEn(institution.getName());
            Optional.ofNullable(institution.getNameChn()).ifPresent(dd -> d.setFkInstitutionNameZh(dd));
            d.setLevelTypeName(ProjectExtraEnum.getValueByKey(d.getLevelType(), InstitutionAppFeeLevelType));
        });
        return institutionAppFeeDtos;
    }

    @Override
    public List<InstitutionAppFeeVo2> getWcInstitutionAppFeeList(InstitutionAppFeeDto2 data, SearchBean<InstitutionAppFeeDto2> page) {
        IPage<InstitutionAppFeeVo2> ipage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<InstitutionAppFeeVo2> wcInstitutionAppFeeList = institutionAppFeeMapper2.getWcInstitutionAppFeeList(ipage, data.getSchoolName(), data.getFkCountryId());
        page.setAll((int) ipage.getTotal());
        page.setCurrentResult((int) ipage.getSize());

        if (GeneralTool.isEmpty(wcInstitutionAppFeeList)) {
            return wcInstitutionAppFeeList;
        }

        Iterator<InstitutionAppFeeVo2> iterator = wcInstitutionAppFeeList.iterator();
        while (iterator.hasNext()) {
            InstitutionAppFeeVo2 next = iterator.next();
            boolean b = !next.getPublicLevel().contentEquals("0");
            if (!b) {
                iterator.remove();
            }
        }
        if (GeneralTool.isEmpty(wcInstitutionAppFeeList)) {
            return wcInstitutionAppFeeList;
        }
        //TODO 改过
        //Set<Long> schoolId = wcInstitutionAppFeeList.stream().map(InstitutionAppFee2::getFkInstitutionId).collect(Collectors.toSet());
        Set<Long> schoolId = wcInstitutionAppFeeList.stream().map(InstitutionAppFeeVo2::getFkInstitutionId).collect(Collectors.toSet());
        Map<Long, Institution> institutionNamesByIds = institutionService.getInstitutionByIds(schoolId);
        //获取文件guid
        List<MediaAndAttachedVo> guIdByIds = mediaAndAttachedService.getGuIdByIds(schoolId, "m_institution", "institution_cover");
        Map<Long, String> url = new HashMap<>();
        if (GeneralTool.isNotEmpty(guIdByIds)) {
            //获取文件具体url
            List<String> collect = guIdByIds.stream().map(MediaAndAttachedVo::getFkFileGuid).collect(Collectors.toList());
            Result<Map<String, String>> filePathByGuids = iFileCenterClient.getFilePathByGuids(collect);
            if (filePathByGuids.isSuccess() && GeneralTool.isNotEmpty(filePathByGuids.getData())) {
                guIdByIds.stream().forEach(d -> url.put(d.getFkTableId(), filePathByGuids.getData().get(d.getFkFileGuid())));
            }
        }
        List<InstitutionAppFeeVo2> institutionAppFeeDtos = BeanCopyUtils.copyListProperties(wcInstitutionAppFeeList, InstitutionAppFeeVo2::new);
        institutionAppFeeDtos.stream().forEach(d -> {
            d.setCoversUrl(url.containsKey(d.getFkInstitutionId()) ? url.get(d.getFkInstitutionId()) : null);
            Institution institution = institutionNamesByIds.get(d.getFkInstitutionId());
            if (GeneralTool.isBlank(institution.getNameDisplay())) {
                d.setFkInstitutionNameEn(institution.getName());
                if (GeneralTool.isNotBlank(institution.getNameChn())) {
                    d.setFkInstitutionNameZh(institution.getNameChn());
                }
            } else {
                d.setFkInstitutionNameEn(institution.getNameDisplay());
            }
        });

        return institutionAppFeeDtos;
    }

    @Override
    public List<InstitutionAppFeeVo> setDatas(MultipartFile multipartFile) throws Exception {
        return null;
    }

    /**
     * 根据目标表名和ID获取申请费信息
     *
     * @param fkTableName 目标表名
     * @param fkTableId   主键ID
     * @return
     */
    @Override
    public List<InstitutionAppFeeVo> getAppFees(String fkTableName, Long fkTableId) {
        return institutionAppFeeMapper.getAppFees(fkTableName, fkTableId);
    }

}

