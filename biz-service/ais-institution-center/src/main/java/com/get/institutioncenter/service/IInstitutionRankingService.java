package com.get.institutioncenter.service;

import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.InstitutionRankingVo;
import com.get.institutioncenter.entity.InstitutionRanking;
import com.get.institutioncenter.dto.InstitutionRankingDto;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/25 11:05
 */
public interface IInstitutionRankingService extends BaseService<InstitutionRanking> {


    List<InstitutionRankingVo> getWcComprehensiveRanking(InstitutionRankingDto data, SearchBean<InstitutionRankingDto> page);

    List<InstitutionRankingVo> getWcComprehensiveRankingHome();


    InstitutionRankingVo getWcCourseTypeKey(String typeKey);

    List<InstitutionRankingVo> getWcMajorRanking(String typeKey);
}
