package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.vo.MajorLevelVo;
import com.get.institutioncenter.entity.InstitutionCourseMajorLevel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface InstitutionCourseMajorLevelMapper extends BaseMapper<InstitutionCourseMajorLevel> {
    int insert(InstitutionCourseMajorLevel record);

    int insertSelective(InstitutionCourseMajorLevel record);

    String getNamesByCourseId(@Param("id") Long id);

    String getFullNamesByCourseId(@Param("id") Long id);

    List<MajorLevelVo> getNamesByCourseIds(@Param("fkInstitutionCourseIds") Set<Long> fkInstitutionCourseIds);

    void deleteByByCourseId(@Param("id") Long id);

    List<Long> getMajorLevelIdsByCourseId(@Param("id") Long id);

    String getMajorLevelIdStringByCourseId(@Param("id") Long id);

    boolean isExistByCourseId(@Param("courseId") Long courseId);
}