package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaCompany;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


@Mapper
public interface ContractFormulaCompanyMapper extends BaseMapper<ContractFormulaCompany> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContractFormulaCompany record);

    /**
     * @return list
     * @Description :查询公司id
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCompanyIdListByFkid(Long contractFormulaId);
}