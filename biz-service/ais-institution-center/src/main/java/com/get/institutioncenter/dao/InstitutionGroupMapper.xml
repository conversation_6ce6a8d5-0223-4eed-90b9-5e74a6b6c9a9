<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionGroupMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionGroup">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_chn" jdbcType="VARCHAR" property="nameChn" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionGroup" keyProperty="id" useGeneratedKeys="true">
    insert into m_institution_group (id, num, name, 
      name_chn, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{nameChn,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionGroup" keyProperty="id" useGeneratedKeys="true">
    insert into m_institution_group
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameChn != null">
        name_chn,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameChn != null">
        #{nameChn,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getNameById" parameterType="java.lang.Long" resultType="java.lang.String">
    select
      CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from
      m_institution_group i
    where
      i.id = #{id}
  </select>

  <select id="getInstitutionGroupSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select id,CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName ,`name`,name_chn from m_institution_group
  </select>


  <select id="getInstitutionGroupNameById" parameterType="java.lang.Long" resultType="string">
    select
      CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from
      m_institution_group
    where
      id = #{id}
  </select>

  <select id="getInstitutionGroups"  resultType="com.get.institutioncenter.entity.InstitutionGroup">
    SELECT * FROM m_institution_group
    WHERE 1=1
    <if test="name != null and name!=''">
      AND (name like concat("%",#{name},"%") or name_chn like
      concat("%",#{name},"%"))
    </if>
    ORDER BY CONVERT(name USING gbk)
  </select>

</mapper>