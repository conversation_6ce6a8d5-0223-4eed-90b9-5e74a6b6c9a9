package com.get.institutioncenter.service;

import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import com.get.institutioncenter.vo.InstitutionAppFeeVo;
import com.get.institutioncenter.vo.InstitutionAppFeeVo2;
import com.get.institutioncenter.entity.InstitutionAppFee;
import com.get.institutioncenter.dto.InstitutionAppFeeDto;
import com.get.institutioncenter.dto.InstitutionAppFeeDto2;
import com.get.institutioncenter.dto.query.InstitutionAppFeeQueryDto;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/5 21:50
 */
public interface IInstitutionAppFeeService extends BaseService<InstitutionAppFee> {

    Long addInstitutionAppFee(InstitutionAppFeeDto institutionAppFeeDto);

    InstitutionAppFeeVo findInstitutionAppFeeById(Long id);
    /**
     * 优先匹配查询
     * @param weScholarshipAppDto
     * @return
     */
    List<InstitutionAppFeeVo> priorityMatchingQuery(WeScholarshipAppDto weScholarshipAppDto);
    void delete(Long id);

    InstitutionAppFeeVo updateInstitutionAppFee(InstitutionAppFeeDto institutionAppFeeDto);

    List<InstitutionAppFeeVo> datas(InstitutionAppFeeQueryDto data, SearchBean<InstitutionAppFeeQueryDto> page);

    List<InstitutionAppFeeVo2> getWcInstitutionAppFeeDatas(InstitutionAppFeeDto2 data, SearchBean<InstitutionAppFeeDto2> page);

    List<InstitutionAppFeeVo2> getWcInstitutionAppFeeList(InstitutionAppFeeDto2 data, SearchBean<InstitutionAppFeeDto2> page);

    List<InstitutionAppFeeVo> setDatas(MultipartFile multipartFile) throws Exception;

    /**
     * 根据目标表名和ID获取申请费信息
     *
     * @param fkTableName 目标表名
     * @param fkTableId   主键ID
     * @return
     */
    List<InstitutionAppFeeVo> getAppFees(String fkTableName, Long fkTableId);
}
