package com.get.institutioncenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.service.IInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: InformationController
 * @Author: Eric
 * @Date: 2023/4/26 17:42
 * @Version: 1.0
 */
@Api(tags = "资讯类型管理")
@RestController
@RequestMapping("/institution/information")
public class InformationController {

    @Resource
    private IInformationService informationService;


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "所有资讯下拉框", notes = "")
    @GetMapping("getAllInformationSelect")
    public ResponseBo<BaseSelectEntity> getAllInformationSelect(){

        List<BaseSelectEntity> datas  =  informationService.getAllInformationSelect();

        return new ListResponseBo<>(datas);
    }


}
