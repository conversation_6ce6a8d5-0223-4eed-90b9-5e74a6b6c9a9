package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InisContactPersonType;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ContactPersonTypeMapper extends BaseMapper<InisContactPersonType> {

    @Override
    int insert(InisContactPersonType record);


    int insertSelective(InisContactPersonType record);

    Integer getMaxViewOrder();
}