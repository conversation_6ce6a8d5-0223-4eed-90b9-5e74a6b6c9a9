package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaInstitutionMapper;
import com.get.institutioncenter.dto.ContractFormulaInstitutionDto;
import com.get.institutioncenter.entity.ContractFormulaInstitution;
import com.get.institutioncenter.service.IContractFormulaInstitutionService;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/22 11:10
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaInstitutionServiceImpl extends BaseServiceImpl<ContractFormulaInstitutionMapper, ContractFormulaInstitution> implements IContractFormulaInstitutionService {
    @Resource
    private ContractFormulaInstitutionMapper contractFormulaInstitutionMapper;
    @Resource
    private UtilService utilService;

    @Override
    public Long addContractFormulaInstitution(ContractFormulaInstitutionDto contractFormulaInstitutionDto) {
        if (GeneralTool.isEmpty(contractFormulaInstitutionDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContractFormulaInstitution contractFormulaInstitution = BeanCopyUtils.objClone(contractFormulaInstitutionDto, ContractFormulaInstitution::new);
        utilService.updateUserInfoToEntity(contractFormulaInstitution);
        contractFormulaInstitutionMapper.insertSelective(contractFormulaInstitution);
        return contractFormulaInstitution.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaInstitution> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaInstitution::getFkContractFormulaId, contractFormulaId);
        contractFormulaInstitutionMapper.delete(wrapper);
    }

    @Override
    public List<Long> getInstitutionIdListByFkid(Long contractFormulaId) {
        return contractFormulaInstitutionMapper.getInstitutionIdListByFkid(contractFormulaId);
    }

    @Override
    public String getInstitutionNameByFkid(Long contractFormulaId) {
        List<String> institutionNameList = contractFormulaInstitutionMapper.getInstitutionNameByFkid(contractFormulaId);
        String result = "";
        if (GeneralTool.isNotEmpty(institutionNameList)) {
            result = StringUtils.join(institutionNameList, ",");
        }
        return result;
    }
}
