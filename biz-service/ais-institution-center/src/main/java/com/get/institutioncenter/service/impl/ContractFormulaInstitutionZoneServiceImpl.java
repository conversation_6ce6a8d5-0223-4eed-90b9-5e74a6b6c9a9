package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaInstitutionZoneMapper;
import com.get.institutioncenter.dto.ContractFormulaInstitutionZoneDto;
import com.get.institutioncenter.entity.ContractFormulaInstitutionZone;
import com.get.institutioncenter.service.IContractFormulaInstitutionZoneService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/26 15:03
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaInstitutionZoneServiceImpl extends BaseServiceImpl<ContractFormulaInstitutionZoneMapper, ContractFormulaInstitutionZone> implements IContractFormulaInstitutionZoneService {
    @Resource
    private ContractFormulaInstitutionZoneMapper contractFormulaInstitutionZoneMapper;
    @Resource
    private UtilService utilService;

    @Override
    public Long addContractFormulaInstitutionZone(ContractFormulaInstitutionZoneDto contractFormulaInstitutionZoneDto) {
        if (GeneralTool.isEmpty(contractFormulaInstitutionZoneDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContractFormulaInstitutionZone contractFormulaInstitutionZone = BeanCopyUtils.objClone(contractFormulaInstitutionZoneDto, ContractFormulaInstitutionZone::new);
        utilService.updateUserInfoToEntity(contractFormulaInstitutionZone);
        contractFormulaInstitutionZoneMapper.insertSelective(contractFormulaInstitutionZone);
        return contractFormulaInstitutionZone.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaInstitutionZone> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaInstitutionZone::getFkContractFormulaId, contractFormulaId);
        contractFormulaInstitutionZoneMapper.delete(wrapper);
    }

    @Override
    public List<Long> getZoneIdListByFkid(Long contractFormulaId) {
        return contractFormulaInstitutionZoneMapper.getZoneIdListByFkid(contractFormulaId);
    }
}
