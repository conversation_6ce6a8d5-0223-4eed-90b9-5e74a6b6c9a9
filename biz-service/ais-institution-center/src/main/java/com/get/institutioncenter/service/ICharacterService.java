package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.institutioncenter.vo.CharacterVo;
import com.get.institutioncenter.dto.CharacterDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/30
 * @TIME: 15:38
 * @Description:
 **/
public interface ICharacterService {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    CharacterVo findCharacterById(Long id);

    /**
     * 列表数据
     *
     * @param characterDto
     * @param page
     * @return
     */
    List<CharacterVo> getCharacters(CharacterDto characterDto, Page page);

    /**
     * 修改
     *
     * @param characterDto
     * @return
     */
    CharacterVo updateCharacter(CharacterDto characterDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param characterDtos
     * @return
     */
    void batchAdd(List<CharacterDto> characterDtos);

    /**
     * 查找類型
     *
     * @return
     */
    List<Map<String, Object>> findType();
}
