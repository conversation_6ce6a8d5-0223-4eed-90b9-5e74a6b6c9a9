<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionTypeMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionType">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="type_name_chn" jdbcType="VARCHAR" property="typeNameChn" />
    <result column="type_key" jdbcType="VARCHAR" property="typeKey" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionType" keyProperty="id" useGeneratedKeys="true">
    insert into u_institution_type (id, type_name,type_name_chn, view_order, type_key,
      gmt_create, gmt_create_user, gmt_modified,
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{typeName,jdbcType=VARCHAR}, #{typeNameChn,jdbcType=VARCHAR},#{viewOrder,jdbcType=INTEGER}, #{typeKey,jdbcType=INTEGER},
            #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionType" keyProperty="id" useGeneratedKeys="true">
    insert into u_institution_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="typeName != null">
        type_name,
      </if>
      <if test="typeNameChn != null">
        type_name_chn,
      </if>
      <if test="typeKey != null">
        type_key,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="typeNameChn != null">
        #{typeNameChn,jdbcType=VARCHAR},
      </if>
      <if test="typeKey != null">
        #{typeKey,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     u_institution_type

  </select>

  <select id="getInstitutionTypeNameById" parameterType="java.lang.Long" resultType="string">
      select
          CASE WHEN IFNULL(i.type_name_chn, '') = '' THEN i.type_name ELSE CONCAT(i.type_name, '（', i.type_name_chn, '）') END
        from
      u_institution_type i
      where
      i.id = #{id}
  </select>
<!--    <select id="getInstitutionTypeKeyById" parameterType="java.lang.Long" resultType="string">-->
<!--        select-->
<!--            i.type_key-->
<!--        from-->
<!--            u_institution_type i-->
<!--        where-->
<!--            i.id = #{id}-->
<!--    </select>-->
  <select id="getInstitutionTypeList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      select
      i.id,CASE WHEN IFNULL(i.type_name_chn, '') = '' THEN i.type_name ELSE CONCAT(i.type_name, '（', i.type_name_chn, '）') END name
      from
      u_institution_type AS i
       <where>
          <if test="ids != null and ids.size()>0">
              and i.id in
              <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                  #{id}
              </foreach>
          </if>
      </where>
      order by
      i.view_order
      desc
  </select>
    <select id="getInstitutionTypeKeyById" parameterType="java.lang.Long" resultType="string">
        select
            i.type_key
        from
            u_institution_type i
        where
            i.id = #{id}
    </select>
  <select id="pathwayInstitutionIsEmpty" resultType="java.lang.Boolean">
    select IFNULL(max(id),0) id from u_institution_type where id = #{id} and type_key = 'BRIDGE_INSTITUTION' LIMIT 1
  </select>
</mapper>