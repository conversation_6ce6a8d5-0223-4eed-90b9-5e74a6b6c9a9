package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.component.TranslationHelp;
import com.get.institutioncenter.dao.NewsTypeMapper;
import com.get.institutioncenter.po.NewsTypePo;
import com.get.institutioncenter.vo.NewsTypeVo;
import com.get.institutioncenter.entity.NewsType;
import com.get.institutioncenter.service.INewsTypeService;
import com.get.institutioncenter.service.ITranslationMappingService;
import com.get.institutioncenter.dto.NewsTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: Sea
 * @create: 2020/8/6 11:44
 * @verison: 1.0
 * @description:
 */
@Service
public class NewsTypeServiceImpl extends BaseServiceImpl<NewsTypeMapper, NewsType> implements INewsTypeService {

    @Resource
    private NewsTypeMapper newsTypeMapper;

    @Resource
    private UtilService utilService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private GetRedis redisClient;
    @Resource
    private TranslationHelp translationHelp;

    @Override
    public NewsTypeVo findNewsTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        NewsType newsType = newsTypeMapper.selectById(id);
        NewsTypeVo newsTypeVo = BeanCopyUtils.objClone(newsType, NewsTypeVo::new);
        newsTypeVo.setFkTableName(TableEnum.NEWS_TYPE.key);
        String language = SecureUtil.getLocale();
        NewsTypePo newsTypePo = BeanCopyUtils.objClone(newsTypeVo, NewsTypePo::new);
        if (GeneralTool.isNotEmpty(newsTypePo) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(Collections.singletonList(newsTypePo), ProjectKeyEnum.getInitialValue(language));
        }
        if(GeneralTool.isNotEmpty(newsTypePo)){
            newsTypeVo = BeanCopyUtils.objClone(newsTypePo, NewsTypeVo::new);
        }
        return newsTypeVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<NewsTypeDto> newsTypeDtos) {
        if (GeneralTool.isEmpty(newsTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = newsTypeMapper.getMaxViewOrder();
        for (NewsTypeDto newsTypeDto : newsTypeDtos) {
            if (GeneralTool.isEmpty(newsTypeDto.getId())) {
                if (validateAdd(newsTypeDto)) {
                    NewsType newsType = BeanCopyUtils.objClone(newsTypeDto, NewsType::new);
                    newsType.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(newsType);
                    newsTypeMapper.insertSelective(newsType);
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            } else {
                if (validateUpdate(newsTypeDto)) {
                    NewsType infoType = BeanCopyUtils.objClone(newsTypeDto, NewsType::new);
                    utilService.updateUserInfoToEntity(infoType);
                    newsTypeMapper.updateById(infoType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findNewsTypeById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = newsTypeMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }

        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.NEWS_TYPE.key, id);
    }

    @Override
    public NewsTypeVo updateNewsType(NewsTypeDto newsTypeDto) {
        if (newsTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        NewsType result = newsTypeMapper.selectById(newsTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (validateUpdate(newsTypeDto)) {
            NewsType infoType = BeanCopyUtils.objClone(newsTypeDto, NewsType::new);
            utilService.updateUserInfoToEntity(infoType);
            newsTypeMapper.updateById(infoType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findNewsTypeById(newsTypeDto.getId());
    }

    @Override
    public List<NewsTypeVo> getNewsTypes(NewsTypeDto newsTypeDto, Page page) {
        LambdaQueryWrapper<NewsType> wrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(newsTypeDto)) {
            if (GeneralTool.isNotEmpty(newsTypeDto.getKeyWord())) {
                wrapper.like(NewsType::getTypeName, newsTypeDto.getKeyWord());
            }
        }
        wrapper.orderByDesc(NewsType::getViewOrder);
        IPage<NewsType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<NewsType> newsTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<NewsTypeVo> convertDatas = new ArrayList<>();
        for (NewsType newsType : newsTypes) {
            NewsTypeVo newsTypeVo = BeanCopyUtils.objClone(newsType, NewsTypeVo::new);
            newsTypeVo.setFkTableName(TableEnum.NEWS_TYPE.key);
            convertDatas.add(newsTypeVo);
        }
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(convertDatas) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(convertDatas, ProjectKeyEnum.getInitialValue(language));
        }
        return convertDatas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<NewsTypeDto> newsTypeDtos) {
        if (GeneralTool.isEmpty(newsTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        NewsType ro = BeanCopyUtils.objClone(newsTypeDtos.get(0), NewsType::new);
        Integer oneorder = ro.getViewOrder();
        NewsType rt = BeanCopyUtils.objClone(newsTypeDtos.get(1), NewsType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        newsTypeMapper.updateById(ro);
        newsTypeMapper.updateById(rt);

    }

    @Override
    public List<BaseSelectEntity> getNewsTypeList() {
        return newsTypeMapper.getNewsTypeList();
    }

    @Override
    public Map<Long, String> getNewTypeNamesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<NewsType> wrapper = new LambdaQueryWrapper();
        wrapper.in(NewsType::getId, ids);
        List<NewsType> newsTypes = newsTypeMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(newsTypes)) {
            return map;
        }
        for (NewsType newsType : newsTypes) {
            map.put(newsType.getId(), newsType.getTypeName());
        }
        return map;
    }

    private boolean validateAdd(NewsTypeDto newsTypeDto) {
        LambdaQueryWrapper<NewsType> wrapper = new LambdaQueryWrapper();
        wrapper.eq(NewsType::getTypeName, newsTypeDto.getTypeName());
        List<NewsType> list = this.newsTypeMapper.selectList(wrapper);
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(NewsTypeDto newsTypeDto) {
        LambdaQueryWrapper<NewsType> wrapper = new LambdaQueryWrapper();
        wrapper.eq(NewsType::getTypeName, newsTypeDto.getTypeName());
        List<NewsType> list = this.newsTypeMapper.selectList(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(newsTypeDto.getId());
    }
}
