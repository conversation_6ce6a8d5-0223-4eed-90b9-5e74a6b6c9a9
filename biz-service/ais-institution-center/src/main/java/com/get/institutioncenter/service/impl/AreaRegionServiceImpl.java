package com.get.institutioncenter.service.impl;

import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.AreaRegionMapper;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.entity.AreaRegion;
import com.get.institutioncenter.service.IAreaRegionService;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2021/7/30
 * @TIME: 11:33
 * @Description:
 **/
@Service
public class AreaRegionServiceImpl extends BaseServiceImpl<AreaRegionMapper, AreaRegion> implements IAreaRegionService {
    @Resource
    private AreaRegionMapper areaRegionMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public AreaRegionVo findAreaRegionById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaRegion areaRegion = areaRegionMapper.selectById(id);
        if (GeneralTool.isEmpty(areaRegion)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaRegionVo areaRegionVo = BeanCopyUtils.objClone(areaRegion, AreaRegionVo::new);
        if (GeneralTool.isNotEmpty(areaRegion.getFkCompanyId())){
            permissionCenterClient.getCompanyNameById(areaRegion.getFkCompanyId());
        }
        //语言
        areaRegionVo.setFkTableName(TableEnum.INSTITUTION_AREA_REGION.key);
        return areaRegionVo;
    }

    @Override
    public List<BaseSelectEntity> getAreaRegionSelect(Long fkCompanyId) {
        return areaRegionMapper.getAreaRegionSelect(fkCompanyId, SecureUtil.getCompanyIds());
    }

    @Override
    public List<BaseSelectEntity> getAreaRegionSelectByCompanyIds(List<Long> fkCompanyIds){
        return areaRegionMapper.getAreaRegionSelectByCompanyIds(fkCompanyIds);
    }

    @Override
    public Map<Long, String> getAllAreaRegionChnNames() {
        List<AreaRegion> areaRegions = areaRegionMapper.selectList(null);
        return areaRegions.stream().collect(Collectors.toMap(AreaRegion::getId,AreaRegion::getNameChn));
    }

    /**
     * 根据州省ids获取对应的大区对象Map
     *
     * @Date 17:25 2023/12/19
     * <AUTHOR>
     */
    @Override
    public Map<Long, AreaRegionVo> getRegionMapByStateIds(List<Long> stateIds) {
        List<AreaRegionVo> areaRegionVos = areaRegionMapper.getRegionMapByStateIds(stateIds);
        Map<Long, AreaRegionVo> collect = areaRegionVos.stream().collect(Collectors.toMap(AreaRegionVo::getFkAreaStateId, areaRegionDto -> areaRegionDto));
        return collect;
    }

    @Override
    public Map<Long, String> getAreaRegionNameByIds(Set<Long> fkAreaRegionIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(fkAreaRegionIds)) {
            return map;
        }
        List<AreaRegion> areaRegions = areaRegionMapper.selectBatchIds(fkAreaRegionIds);
        if (GeneralTool.isEmpty(areaRegions)) {
            return map;
        }
        for (AreaRegion areaRegion : areaRegions) {
            StringBuilder sb = new StringBuilder();
            if (GeneralTool.isNotEmpty(areaRegion.getNameChn())) {
                sb.append(areaRegion.getName()).append("（").append(areaRegion.getNameChn()).append("）");
            } else {
                sb.append(areaRegion.getName());
            }

            map.put(areaRegion.getId(), sb.toString());
        }
        return map;
    }
}
