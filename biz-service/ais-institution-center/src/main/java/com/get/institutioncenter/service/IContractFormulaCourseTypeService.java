package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.entity.ContractFormulaCourseType;
import com.get.institutioncenter.dto.ContractFormulaCourseTypeDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/8 10:27
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaCourseTypeService extends BaseService<ContractFormulaCourseType> {

    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaCourseTypeDto]
     * <AUTHOR>
     */
    Long addContractFormulaCourseType(ContractFormulaCourseTypeDto contractFormulaCourseTypeDto);

    /**
     * @return void
     * @Description :根据contractFormulaId删除
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应课程类型ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCourseTypeIdListByFkid(Long contractFormulaId);

    /**
     * @return java.lang.String
     * @Description :通过合同公式id 查找对应课程类型名称
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    String getCourseTypeNameByFkid(Long contractFormulaId);
}
