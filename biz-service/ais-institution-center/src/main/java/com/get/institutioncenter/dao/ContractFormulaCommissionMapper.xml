<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.ContractFormulaCommissionMapper">
  <insert id="insert" parameterType="com.get.institutioncenter.entity.ContractFormulaCommission" keyProperty="id" useGeneratedKeys="true">
    insert into m_contract_formula_commission (id, fk_contract_formula_id, step,
                                               commission_rate, commission_rate_ag, fixed_amount,
                                               fixed_amount_ag, limit_amount, limit_amount_ag,
                                               remark, gmt_create, gmt_create_user,
                                               gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkContractFormulaId,jdbcType=BIGINT}, #{step,jdbcType=INTEGER},
            #{commissionRate,jdbcType=DECIMAL}, #{commissionRateAg,jdbcType=DECIMAL}, #{fixedAmount,jdbcType=DECIMAL},
            #{fixedAmountAg,jdbcType=DECIMAL}, #{limitAmount,jdbcType=DECIMAL}, #{limitAmountAg,jdbcType=DECIMAL},
            #{remark,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR},
            #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.ContractFormulaCommission" keyProperty="id" useGeneratedKeys="true">
    insert into m_contract_formula_commission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkContractFormulaId != null">
        fk_contract_formula_id,
      </if>
      <if test="step != null">
        step,
      </if>
      <if test="commissionRate != null">
        commission_rate,
      </if>
      <if test="commissionRateAg != null">
        commission_rate_ag,
      </if>
      <if test="fixedAmount != null">
        fixed_amount,
      </if>
      <if test="fixedAmountAg != null">
        fixed_amount_ag,
      </if>
      <if test="limitAmount != null">
        limit_amount,
      </if>
      <if test="limitAmountAg != null">
        limit_amount_ag,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkContractFormulaId != null">
        #{fkContractFormulaId,jdbcType=BIGINT},
      </if>
      <if test="step != null">
        #{step,jdbcType=INTEGER},
      </if>
      <if test="commissionRate != null">
        #{commissionRate,jdbcType=DECIMAL},
      </if>
      <if test="commissionRateAg != null">
        #{commissionRateAg,jdbcType=DECIMAL},
      </if>
      <if test="fixedAmount != null">
        #{fixedAmount,jdbcType=DECIMAL},
      </if>
      <if test="fixedAmountAg != null">
        #{fixedAmountAg,jdbcType=DECIMAL},
      </if>
      <if test="limitAmount != null">
        #{limitAmount,jdbcType=DECIMAL},
      </if>
      <if test="limitAmountAg != null">
        #{limitAmountAg,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getMaxStep" resultType="java.lang.Integer">
    select
     IFNULL(max(step)+1,1) step
    from
     m_contract_formula_commission
    where
     fk_contract_formula_id = #{contractFormulaId}

  </select>
</mapper>