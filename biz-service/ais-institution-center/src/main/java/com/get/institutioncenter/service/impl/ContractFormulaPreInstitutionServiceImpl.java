package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractFormulaPreInstitutionMapper;
import com.get.institutioncenter.entity.ContractFormulaPreInstitution;
import com.get.institutioncenter.service.IContractFormulaPreInstitutionService;
import com.get.institutioncenter.dto.ContractFormulaPreInstitutionDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/26 15:02
 * @verison: 1.0
 * @description:
 */
@Service
public class ContractFormulaPreInstitutionServiceImpl extends BaseServiceImpl<ContractFormulaPreInstitutionMapper, ContractFormulaPreInstitution> implements IContractFormulaPreInstitutionService {
    @Resource
    private ContractFormulaPreInstitutionMapper contractFormulaPreInstitutionMapper;
    @Resource
    private UtilService utilService;

    @Override
    public Long addContractFormulaPreInstitution(ContractFormulaPreInstitutionDto contractFormulaPreInstitutionDto) {
        if (GeneralTool.isEmpty(contractFormulaPreInstitutionDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ContractFormulaPreInstitution contractFormulaPreInstitution = BeanCopyUtils.objClone(contractFormulaPreInstitutionDto, ContractFormulaPreInstitution::new);
        utilService.updateUserInfoToEntity(contractFormulaPreInstitution);
        contractFormulaPreInstitutionMapper.insertSelective(contractFormulaPreInstitution);
        return contractFormulaPreInstitution.getId();
    }

    @Override
    public void deleteByFkid(Long contractFormulaId) {
        LambdaQueryWrapper<ContractFormulaPreInstitution> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractFormulaPreInstitution::getFkContractFormulaId, contractFormulaId);
        contractFormulaPreInstitutionMapper.delete(wrapper);
    }

    @Override
    public List<Long> getInstiutionIdListByFkId(Long contractFormulaId) {
        return contractFormulaPreInstitutionMapper.getInstiutionIdListByFkId(contractFormulaId);
    }
}
