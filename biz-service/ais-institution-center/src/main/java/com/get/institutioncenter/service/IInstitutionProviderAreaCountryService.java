package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.InstitutionProviderAreaCountryVo;
import com.get.institutioncenter.entity.InstitutionProviderAreaCountry;
import com.get.institutioncenter.dto.InstitutionProviderAreaCountryDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/3/29 10:16
 * @verison: 1.0
 * @description:
 */

public interface IInstitutionProviderAreaCountryService extends BaseService<InstitutionProviderAreaCountry> {

    /**
     * @return void
     * @Description: 添加业务国家
     * @Param [providerAreaCountryVo]
     * <AUTHOR>
     */
    void addProviderAreaCountry(InstitutionProviderAreaCountryDto providerAreaCountryVo);


    /**
     * @return void
     * @Description: 根据提供商id删除
     * @Param [providerId]
     * <AUTHOR>
     */
    void deleteByProviderId(Long providerId);


    /**
     * @return java.lang.String
     * @Description: 根据提供商id 查询业务国家
     * @Param [providerId]
     * <AUTHOR>
     */
    String getAreaCountryNameByProviderId(Long providerId);


    /**
     * @return java.lang.String
     * @Description: 根据提供商id 查询业务国家ids
     * @Param [providerId]
     * <AUTHOR>
     */
    List<Long> getAreaCountryIdByProviderId(Long providerId);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据业务国家查询提供商
     * @Param [areaCountryIds]
     * <AUTHOR>
     */
    List<Long> getProviderIds(List<Long> areaCountryIds);


    /**
     * @return java.util.List<com.get.institutioncenter.vo.InstitutionProviderAreaCountryVo>
     * @Description :公式国家区域下拉框
     * @Param [institutionProviderId]
     * <AUTHOR>
     */
    List<InstitutionProviderAreaCountryVo> getInstitutionProviderAreaCountrySelect(Long institutionProviderId);
}
