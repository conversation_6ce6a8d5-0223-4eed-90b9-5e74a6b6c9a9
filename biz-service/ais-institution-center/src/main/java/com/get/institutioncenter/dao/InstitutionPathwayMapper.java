package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.entity.InstitutionPathway;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionPathwayMapper extends BaseMapper<InstitutionPathway> {
    @Override
    int insert(InstitutionPathway record);

    int insertSelective(InstitutionPathway record);

    /**
     * 课程桥梁学校下拉框数据
     *
     * @return
     */
    List<BaseSelectEntity> getBridgeInstitutionSelect(@Param("id") Long id);


    List<Long> getBridgeInstitutionIds(@Param("id") Long id);

    /**
     * 非桥梁学校下拉框数据
     *
     * @Date 16:16 2021/7/22
     * <AUTHOR>
     */
    List<Long> getNonBridgeInstitutionSelect(@Param("id") Long id);


    boolean institutionIdPathwayIsEmpty(@Param("id") Long id);


    List<InstitutionPathway> selectInstitutionPathway(IPage<InstitutionPathway> pages, @Param("fkInstitutionId") Long fkInstitutionId, @Param("fkInstitutionIdPathway") Long fkInstitutionIdPathway);
}