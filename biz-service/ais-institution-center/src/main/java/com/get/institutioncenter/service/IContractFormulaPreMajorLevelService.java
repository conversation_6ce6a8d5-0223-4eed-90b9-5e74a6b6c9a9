package com.get.institutioncenter.service;

import com.get.institutioncenter.dto.ContractFormulaPreMajorLevelDto;

import java.util.List;

/**
 * 合同公式 前置专业等级逻辑处理类
 *
 * @Date 16:20 2021/6/2
 * <AUTHOR>
 */
public interface IContractFormulaPreMajorLevelService {
    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaMajorLevelVo]
     * <AUTHOR>
     */
    Long addContractFormulaPreMajorLevel(ContractFormulaPreMajorLevelDto contractFormulaPreMajorLevelDto);

    /**
     * @return void
     * @Description :
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * 通过合同公式id 查找对应前置等级ids
     *
     * @Date 16:23 2021/6/2
     * <AUTHOR>
     */
    List<Long> getMajorLevelIdListByFkid(Long contractFormulaId);
}
