package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.institutioncenter.vo.InstitutionChannelVo;
import com.get.institutioncenter.entity.InstitutionChannel;
import com.get.institutioncenter.dto.InstitutionChannelDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionChannelMapper extends BaseMapper<InstitutionChannel>, GetMapper<InstitutionChannel> {
    int insert(InstitutionChannel record);

    int insertSelective(InstitutionChannel record);


    InstitutionChannelVo queryById(@Param("id") Long id, @Param("companyIds") List<Long> companyIds);

    /**
     * 查找学校渠道下拉框
     *
     * @return
     */
    List<BaseSelectEntity> getInstitutionChannelSelect(List<Long> companyIds);

    /**
     * 根据id查找名称
     *
     * @param id
     * @return
     */
    String getNameById(Long id);

    BaseSelectEntity getInfo(@Param("providerId") Long providerId,@Param("channelId") Long channelId);

    List<Long> getInstitutionProviderChannelIdsByName(@Param("channelName") String channelName);

    /**
     * 模糊匹配去渠道名称
     * @param keyword
     * @return
     */
    List<BaseSelectEntity> fuzzQueryChannelName(@Param("keyword")String keyword,List<Long> companyIds);


    List<InstitutionChannelVo> getInstitutionChannelInformation(IPage<InstitutionChannel> pages,
                                                                @Param("institutionChannelDto") InstitutionChannelDto institutionChannelDto, @Param("companyIds") List<Long> companyIds);
}