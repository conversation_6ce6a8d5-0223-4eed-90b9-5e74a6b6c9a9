package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.institutioncenter.vo.AreaCityInfoTypeVo;
import com.get.institutioncenter.dto.AreaCityInfoTypeDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/3/1 17:05
 * @verison: 1.0
 * @description:
 */
public interface IAreaCityInfoTypeService {
    /**
     * @return com.get.salecenter.vo.AreaCityInfoTypeVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    AreaCityInfoTypeVo findAreaCityInfoTypeById(Long id);

    /**
     * @return void
     * @Description :批量新增
     * @Param [areaCityInfoTypeDtos]
     * <AUTHOR>
     */
    void batchAdd(List<AreaCityInfoTypeDto> areaCityInfoTypeDtos);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.AreaCityInfoTypeVo
     * @Description :修改
     * @Param [areaCityInfoTypeDto]
     * <AUTHOR>
     */
    AreaCityInfoTypeVo updateAreaCityInfoType(AreaCityInfoTypeDto areaCityInfoTypeDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.AreaCityInfoTypeVo>
     * @Description :列表
     * @Param [areaCityInfoTypeDto, page]
     * <AUTHOR>
     */
    List<AreaCityInfoTypeVo> getAreaCityInfoTypes(AreaCityInfoTypeDto areaCityInfoTypeDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [areaCityInfoTypeDtos]
     * <AUTHOR>
     */
    void movingOrder(List<AreaCityInfoTypeDto> areaCityInfoTypeDtos);

    /**
     * @return java.util.List<com.get.salecenter.vo.AreaCityInfoTypeVo>
     * @Description :城市资讯类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<AreaCityInfoTypeVo> getAreaCityInfoTypeList();

    /**
     * @return java.lang.String
     * @Description :通过id获取城市资讯类型名称
     * @Param [areaCityInfoTypeId]
     * <AUTHOR>
     */
    String getAreaCityInfoTypeNameById(Long areaCityInfoTypeId);
}
