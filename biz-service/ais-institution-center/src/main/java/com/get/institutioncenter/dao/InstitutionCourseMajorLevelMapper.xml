<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionCourseMajorLevelMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionCourseMajorLevel">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_institution_course_id" jdbcType="BIGINT" property="fkInstitutionCourseId" />
    <result column="fk_major_level_id" jdbcType="BIGINT" property="fkMajorLevelId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionCourseMajorLevel">
    insert into r_institution_course_major_level (id, fk_institution_course_id, fk_major_level_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkInstitutionCourseId,jdbcType=BIGINT}, #{fkMajorLevelId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionCourseMajorLevel">
    insert into r_institution_course_major_level
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInstitutionCourseId != null">
        fk_institution_course_id,
      </if>
      <if test="fkMajorLevelId != null">
        fk_major_level_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionCourseId != null">
        #{fkInstitutionCourseId,jdbcType=BIGINT},
      </if>
      <if test="fkMajorLevelId != null">
        #{fkMajorLevelId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getNamesByCourseId" resultType="java.lang.String">
    select  GROUP_CONCAT(DISTINCT itf.level_name) from r_institution_course_major_level cf LEFT JOIN u_major_level itf
     on cf.fk_major_level_id = itf.id where cf.fk_institution_course_id = #{id}
  </select>
  <select id="getFullNamesByCourseId" resultType="java.lang.String">
    select  GROUP_CONCAT(DISTINCT CASE WHEN IFNULL(itf.level_name_chn, '') = '' THEN itf.level_name ELSE CONCAT(itf.level_name, '（', itf.level_name_chn, '）') END) from r_institution_course_major_level cf LEFT JOIN u_major_level itf
             on cf.fk_major_level_id = itf.id where cf.fk_institution_course_id = #{id}
  </select>
  <select id="getNamesByCourseIds" resultType="com.get.institutioncenter.vo.MajorLevelVo">
    select  cf.fk_institution_course_id AS fkInstitutionCourseId,GROUP_CONCAT(DISTINCT itf.level_name) AS levelName
    from r_institution_course_major_level cf LEFT JOIN
    u_major_level itf
    on cf.fk_major_level_id = itf.id where cf.fk_institution_course_id in
    <foreach collection="fkInstitutionCourseIds" item="fkInstitutionCourseId" index="index" open="(" separator="," close=")">
      #{fkInstitutionCourseId}
    </foreach>
    group by cf.fk_institution_course_id
  </select>

  <select id="deleteByByCourseId">
    delete from r_institution_course_major_level where fk_institution_course_id = #{id}
  </select>
  <select id="getMajorLevelIdsByCourseId" resultType="java.lang.Long">
    select  fk_major_level_id from r_institution_course_major_level  where fk_institution_course_id = #{id}
  </select>

  <select id="getMajorLevelIdStringByCourseId" resultType="java.lang.String">
    select  GROUP_CONCAT(fk_major_level_id) from r_institution_course_major_level  where fk_institution_course_id = #{id}
  </select>

  <select id="isExistByCourseId" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id from r_institution_course_major_level where fk_institution_course_id=#{courseId}
  </select>
</mapper>