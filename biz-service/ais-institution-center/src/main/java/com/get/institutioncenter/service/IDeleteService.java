package com.get.institutioncenter.service;

import com.get.institutioncenter.entity.InstitutionPathway;

/**
 * <AUTHOR>
 * @DATE: 2020/11/25
 * @TIME: 14:39
 * @Description:
 **/
public interface IDeleteService {
    /**
     * @return boolean
     * @Description: 删除学校逻辑验证
     * @Param [institutionId]
     * <AUTHOR>
     **/
    boolean deleteInstitutionRelation(Long institutionId);

    /**
     * @return boolean
     * @Description: 删除课程逻辑验证
     * @Param [courseId]
     * <AUTHOR>
     **/
    void deleteCourseRelation(Long courseId);

    /**
     * @return boolean
     * @Description :删除国家资讯类型校验
     * @Param [areaCountryInfoTypeId]
     * <AUTHOR>
     */
    boolean deleteValidateAreaCountryInfoType(Long areaCountryInfoTypeId);

    /**
     * @return boolean
     * @Description :删除国家校验
     * @Param [areaCountryId]
     * <AUTHOR>
     */
    boolean deleteValidateAreaCountry(Long areaCountryId);

    /**
     * @return boolean
     * @Description :删除国家资讯类型校验
     * @Param [areaCityInfoTypeId]
     * <AUTHOR>
     */
    boolean deleteValidateAreaCityInfoType(Long areaCityInfoTypeId);

    /**
     * @return boolean
     * @Description :删除城市校验
     * @Param [areaCityId]
     * <AUTHOR>
     */
    boolean deleteValidateAreaCity(Long areaCityId);

    /**
     * 删除学校提供商校验
     *
     * @Date 11:34 2021/4/16
     * <AUTHOR>
     */
    boolean deleteValidateProviderByProviderId(Long providerId);

    /**
     * @Description：删除提供商类型校验
     * @Param
     * @Date 11:46 2021/4/22
     * <AUTHOR>
     */
    boolean deleteValidateProviderType(Long providerTypeId);

    /**
     * 删除桥梁学校校验
     *
     * @Date 16:08 2021/7/27
     * <AUTHOR>
     */
    boolean deleteValidatePathwayCourse(InstitutionPathway institutionPathway);

}
