package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.translation.baidu.result.TransVo;
import com.get.institutioncenter.dto.BatchTranslationInstitutionAndCourseInfoDto;
import com.get.institutioncenter.vo.BatchTranslationResultVo;
import com.get.institutioncenter.service.ITranslationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;

/**
 * @author: Hardy
 * @create: 2021/6/4 12:37
 * @verison: 1.0
 * @description:
 */
@Api(tags = "翻译管理")
@RestController
@RequestMapping("/institution/translation")
public class TranslationController {
    @Resource
    private ITranslationService translationService;


    /**
     * 批量翻译新闻
     *
     * @param transVo
     * @return
     * @
     */
    @ApiOperation(value = "批量翻译新闻")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "注册中心/翻译管理/批量翻译新闻")
    @PostMapping("batchTranslationNews")
    public ResponseBo<BatchTranslationResultVo> batchTranslationNews(@RequestBody TransVo transVo) throws Exception {
        String createUser = "[system]";
        BatchTranslationResultVo batchTranslationResultVo = translationService.getBatchTranslationResultInfo(transVo, TableEnum.NEWS.key);
        translationService.batchTranslationNews(transVo, createUser);
        return new ResponseBo<>(batchTranslationResultVo);
    }

    /**
     * 批量翻译新闻
     *
     * @param transVo
     * @return
     * @
     */
    @ApiOperation(value = "批量翻译资讯")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "注册中心/翻译管理/批量翻译资讯")
    @PostMapping("batchTranslationInstitutionInfos")
    public ResponseBo<BatchTranslationResultVo> batchTranslationInstitutionInfos(@RequestBody TransVo transVo) throws Exception {
        String createUser = "[system]";
        BatchTranslationResultVo batchTranslationResultVo = translationService.getBatchTranslationResultInfo(transVo, TableEnum.INSTITUTION_INFO.key);
        translationService.batchTranslationInstitutionInfos(transVo, createUser);
        return new ResponseBo<>(batchTranslationResultVo);
    }

    /**
     * 批量翻译新闻
     *
     * @param transVo
     * @return
     * @
     */
    @ApiOperation(value = "批量翻译国家")
    @PostMapping("batchTranslationAreaCountrys")
    public ResponseBo<BatchTranslationResultVo> batchTranslationAreaCountrys(@RequestBody TransVo transVo) throws Exception {
        String createUser = "[system]";
        BatchTranslationResultVo batchTranslationResultVo = translationService.getBatchTranslationCountryInfo(transVo, TableEnum.INSTITUTION_COUNTRY.key);
        translationService.batchTranslationAreaCountrys(transVo, createUser);
        return new ResponseBo<>(batchTranslationResultVo);
    }

    /**
     * 批量翻译学校
     *
     * @param transVo
     * @return
     * @
     */
    @ApiOperation(value = "批量翻译学校")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "注册中心/翻译管理/批量翻译学校")
    @PostMapping("batchTranslationInstitutions")
    public ResponseBo<BatchTranslationResultVo> batchTranslationInstitutions(@RequestBody TransVo transVo) throws Exception {
        String createUser = "[system]";
        BatchTranslationResultVo batchTranslationResultVo = translationService.getBatchTranslationInstitutionInfo(transVo, TableEnum.INSTITUTION.key);
        translationService.batchTranslationInstitutions(transVo, createUser);
        return new ResponseBo<>(batchTranslationResultVo);
    }

    /**
     * 批量翻译课程
     *
     * @param transVo
     * @return
     * @
     */
    @ApiOperation(value = "批量翻译课程")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "注册中心/翻译管理/批量翻译课程")
    @PostMapping("batchTranslationInstitutionCourses")
    public ResponseBo<BatchTranslationResultVo> batchTranslationInstitutionCourses(@RequestBody TransVo transVo) throws Exception {
        String createUser = "[system]";
        BatchTranslationResultVo batchTranslationResultVo = translationService.getBatchTranslationInstitutionCourseInfo(transVo, TableEnum.INSTITUTION_COURSE.key);
        translationService.batchTranslationInstitutionCourses(transVo, createUser);
        return new ResponseBo<>(batchTranslationResultVo);
    }


    /**
     * @return
     * @Description :批量翻译新闻进度
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "批量翻译新闻进度")
    @PostMapping("batchTranslationCount")
    public ResponseBo<BatchTranslationResultVo> batchTranslationCount() {
        BatchTranslationResultVo batchTranslationResultVo = translationService.batchTranslationCount();
        return new ResponseBo<>(batchTranslationResultVo);
    }


    /**
     * @return
     * @Description :批量翻译资讯进度
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "批量翻译资讯进度")
    @PostMapping("batchTranslateInstitutionInfosCount")
    public ResponseBo<BatchTranslationResultVo> batchTranslateInstitutionInfosCount() {
        BatchTranslationResultVo batchTranslationResultVo = translationService.batchTranslateInstitutionInfosCount();
        return new ResponseBo<>(batchTranslationResultVo);
    }

    /**
     * @return
     * @Description :批量翻译国家进度
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "批量翻译国家进度")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "注册中心/翻译管理/批量翻译国家进度")
    @PostMapping("batchTranslateAreaCountrysCount")
    public ResponseBo<BatchTranslationResultVo> batchTranslateAreaCountrysCount() {
        BatchTranslationResultVo batchTranslationResultVo = translationService.batchTranslateAreaCountrysCount();
        return new ResponseBo<>(batchTranslationResultVo);
    }

    /**
     * @return
     * @Description :批量翻译学校进度
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "批量翻译学校进度")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "注册中心/翻译管理/批量翻译学校进度")
    @PostMapping("batchTranslateInstitutionsCount")
    public ResponseBo<BatchTranslationResultVo> batchTranslateInstitutionsCount() {
        BatchTranslationResultVo batchTranslationResultVo = translationService.batchTranslateInstitutionsCount();
        return new ResponseBo<>(batchTranslationResultVo);
    }

    /**
     * @return
     * @Description :批量翻译课程进度
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "批量翻译课程进度")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "注册中心/翻译管理/批量翻译课程进度")
    @PostMapping("batchTranslateInstitutionCoursesCount")
    public ResponseBo<BatchTranslationResultVo> batchTranslateInstitutionCoursesCount() {
        BatchTranslationResultVo batchTranslationResultVo = translationService.batchTranslateInstitutionCoursesCount();
        return new ResponseBo<>(batchTranslationResultVo);
    }


    /**
     * @return
     * @Description :批量翻译设置地区名
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "批量翻译设置地区名")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "注册中心/翻译管理/批量翻译设置地区名")
    @PostMapping("batchSetAreaStateName")
    public ResponseBo<BatchTranslationResultVo> batchSetAreaStateName() {
        BatchTranslationResultVo batchTranslationResultVo = translationService.getBatchSetStateName();
        translationService.batchSetAreaStateName();
        return new ResponseBo<>(batchTranslationResultVo);
    }

    /**
     * @return
     * @Description :批量翻译设置地区名
     * @Param
     * <AUTHOR>
     */
    @ApiOperation(value = "批量翻译设置州省名")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "注册中心/翻译管理/批量翻译设置州省名")
    @PostMapping("batchSetAreaCityName")
    public ResponseBo<BatchTranslationResultVo> batchSetAreaCityName() {
        BatchTranslationResultVo batchTranslationResultVo = translationService.getBatchSetCityName();
        translationService.batchSetAreaCityName();
        return new ResponseBo<>(batchTranslationResultVo);
    }

    /**
     * @return
     * @Description :字库转换
     * @Param
     * <AUTHOR>
     */
    @ApiIgnore
    @ApiOperation(value = "字库转换")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "注册中心/翻译管理/字库转换")
    @GetMapping("batchTranformNews")
    public ResponseBo batchTranformNews(@RequestParam("tableName") String tableName, @RequestParam("languageCode") String languageCode) {
        translationService.batchTranformNews(tableName, languageCode);
        return ResponseBo.ok();
    }

    /**
     * 批量翻译新闻
     *
     * @param transVo
     * @return
     * @
     */
    @ApiOperation(value = "批量翻译地区街道")
    @PostMapping("batchTranslationAreaCityDivisions")
    public ResponseBo<BatchTranslationResultVo> batchTranslationAreaCityDivisions(@RequestBody TransVo transVo) throws Exception {
        String createUser = "[system]";
        BatchTranslationResultVo batchTranslationResultVo = translationService.getBatchTranslationCityDivisionInfo(transVo, TableEnum.INSTITUTION_AREA_CITY_DIVISION.key);
        translationService.batchTranslationCityDivisions(transVo, createUser);
        return new ResponseBo<>(batchTranslationResultVo);
    }


    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "批量翻译学校数据")
    @PostMapping("batchTranslationInstitutionAndCourseInfo")
    public ResponseBo batchTranslationInstitutionAndCourseInfo(@RequestBody BatchTranslationInstitutionAndCourseInfoDto batchTranslationInstitutionAndCourseInfoDto){
        return translationService.batchTranslationInstitutionAndCourseInfo(batchTranslationInstitutionAndCourseInfoDto);
    }


    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "新的批量翻译学校数据")
    @PostMapping("batchTranslationInstitutionAndCourse")
    public ResponseBo batchTranslationInstitutionAndCourse(){
        return translationService.batchTranslationInstitutionAndCourse();
    }

}
