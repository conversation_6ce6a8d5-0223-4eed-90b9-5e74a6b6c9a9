package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.SecureUtil;
import com.get.institutioncenter.dto.CommentDto;
import com.get.institutioncenter.dto.ContractCompanyDto;
import com.get.institutioncenter.dto.ContractDto;
import com.get.institutioncenter.vo.CommentVo;
import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.vo.ContractVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.service.IContractService;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.dto.query.ContractQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/8/21
 * @TIME: 11:21
 * @Description: 学校提供商合同管理
 **/

@Api(tags = "合同管理")
@RestController
@RequestMapping("institution/contract")
public class ContractController {

    @Autowired
    private IContractService contractService;


    /**
     * 合同列表数据
     *
     * @param page
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "合同列表数据", notes = "keyWord为查询关键字")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/合同管理/查询")
    @PostMapping("datas")
    public ResponseBo<ContractVo> datas(@RequestBody SearchBean<ContractQueryDto> page) {
        List<ContractVo> allContract = contractService.getAllContract(page.getData(), page, SecureUtil.getCompanyIds());
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(allContract, p);
    }


    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/合同管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ContractVo> detail(@PathVariable("id") Long id) {
        ContractVo contractVo = contractService.getContractById(id);
        return new ResponseBo<>(contractVo);
    }

    /**
     * 新增信息
     *
     * @param contractDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/合同管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(ContractDto.Add.class)  ContractDto contractDto) {
        return SaveResponseBo.ok(contractService.addContract(contractDto));
    }


    /**
     * 修改信息
     *
     * @param contractDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/合同管理/更新")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(ContractDto.Update.class) ContractDto contractDto) {
        return UpdateResponseBo.ok(contractService.updateContract(contractDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/合同管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        contractService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 保存合同附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存合同附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/合同管理/保存合同")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> upload(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(contractService.addContractMedia(mediaAttachedVo));
    }

    /**
     * 查询合同附件列表
     *
     * @param voSearchBean
     * @return
     * @
     */
    @ApiOperation(value = "查询合同附件列表", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.PERMISSIONCENTER, type = LoggerOptTypeConst.LIST, description = "权限中心/合同管理/查询员工附件")
    @PostMapping("getStaffMedia")
    public ResponseBo<MediaAndAttachedVo> getStaffMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = contractService.getContractMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 安全配置
     * @Param [validList]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存合同-公司配置接口")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/合同管理-公司绑定配置/保存配置")
    @PostMapping("editContractCompanyRelation")
    public ResponseBo editProviderCompanyRelation(@RequestBody @Validated(ContractCompanyDto.Add.class)
                                                          ValidList<ContractCompanyDto> validList) {
        contractService.editContractCompanyRelation(validList);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.CompanyTreeVo>
     * @Description: 回显合同和公司的关系
     * @Param [fkProviderId]
     * <AUTHOR>
     */
    @ApiOperation(value = "回显合同和公司的关系", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/合同管理/回显合同和公司的关系")
    @PostMapping("getContractCompanyRelation/{contractId}")
    public ResponseBo<CompanyTreeVo> getProviderCompanyRelation(@PathVariable("contractId") Long contractId) {
        return new ListResponseBo<>(contractService.getContractCompanyRelation(contractId));
    }

    /**
     * @param commentDto
     * @return
     * @
     */
    @ApiOperation(value = "编辑评论")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/合同管理/编辑评论")
    @PostMapping("editComment")
    public ResponseBo editComment(@RequestBody @Validated(CommentDto.Add.class) CommentDto commentDto) {
        return SaveResponseBo.ok(contractService.editComment(commentDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AgentContractVo>
     * @Description: 评论列表数据
     * @Param [searchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "评论列表数据")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/合同管理/查询")
    @PostMapping("getComments")
    public ResponseBo<CommentVo> getComment(@RequestBody SearchBean<CommentDto> searchBean) {
        List<CommentVo> datas = contractService.getComments(searchBean.getData(), searchBean);
        Page p = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @ Description : feign调用，根据id查询表
     * @ Param [id]
     * @ return com.get.institutioncenter.vo.ContractVo
     * @ author LEO
     */
/*    @ApiIgnore
    @ApiOperation("feign调用，根据id查询表")
    @GetMapping("getInstitutionContractById")
    public ContractVo getInstitutionContractById(@RequestParam("id") Long id) {
        ContractVo contractDto = contractService.getInstitutionContractById(id);
        return contractDto;
    }*/

    /**
     * @ Description :定义下拉枚举：0新签/1续签
     * @ Param []
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("定义下拉枚举：0新签/1续签")
    @GetMapping("getContractApprovalMode")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getContractApprovalMode() {
        List<Map<String, Object>> datas = contractService.getContractApprovalMode();
        return new ListResponseBo<>(datas);

    }

    /**
     * @ Description :feign调用，更新表状态
     * @ Param [contract]
     * @ return void
     * @ author LEO
     */
/*    @ApiIgnore
    @ApiOperation("feign调用，更新表状态")
    @PostMapping("updateChangeStatus")
    public boolean updateChangeStatus(@RequestBody Contract contract)  {
        contractService.updateChangeStatus(contract);
        return true;
    }*/

    /**
     * @ Description :学校提供商流程开始
     * @ Param [businessKey, procdefKey, companyId]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("学校提供商流程开始")
    @GetMapping("startInstitutionContractFlow")
    public ResponseBo startInstitutionContractFlow(@RequestParam("businessKey") String businessKey,
                                                   @RequestParam("procdefKey") String procdefKey, @RequestParam("companyId") String companyId) {
        if (businessKey == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        contractService.startInstitutionContractFlow(businessKey, procdefKey, companyId);
        return ResponseBo.ok();
    }

    /**
     * @ Description :表单作废
     * @ Param [id]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation(value = "表单作废")
    @PostMapping("updateCancellationBusiness")
    public ResponseBo updateCancellationBusiness(@RequestParam("id") Long id) {
        contractService.updateCancellationBusiness(id);
        return ResponseBo.ok();
    }

    /**
     * @ Description :fegin调用根据表名更新状态
     * @ Param [status, tableName, businessKey]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
/*    @ApiIgnore
    @ApiOperation("fegin调用根据表名更新状态")
    @PostMapping("/changeStatus")
    public Boolean changeStatus(@RequestParam("status") Integer status, @RequestParam("tableName") String tableName, @RequestParam("businessKey") Long businessKey) {
        contractService.changeStatus(status, tableName, businessKey);
        return true;
    }*/

    /**
     * @ Description :重新提交/发起
     * @ Param [taskId, status]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation(value = "重新提交/发起")
    @GetMapping("/getContractUserSubmit")
    public ResponseBo getContractUserSubmit(@RequestParam("taskId") String taskId, @RequestParam("status") String status) {
        contractService.getUserSubmit(taskId, status);
        return ResponseBo.ok();
    }

    /**
     * @ Description :合同表单撤回
     * @ Param [id, summary]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation(value = "合同表单撤回")
    @GetMapping("/getRevokeContract")
    @VerifyPermission(IsVerify = false)
    public ResponseBo getRevokeContract(@RequestParam("id") Long id, @RequestParam("summary") String summary) {
        contractService.getRevokeContract(id, summary);
        return ResponseBo.ok();
    }


}
