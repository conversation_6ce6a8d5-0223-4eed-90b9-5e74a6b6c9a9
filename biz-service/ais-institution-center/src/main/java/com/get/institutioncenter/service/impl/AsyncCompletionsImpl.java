package com.get.institutioncenter.service.impl;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.CacheKeyConstants;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.redis.cache.GetRedis;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.*;
import com.get.institutioncenter.entity.*;
import com.get.institutioncenter.service.AsyncCompletions;
import com.get.institutioncenter.utils.TranslateApiUtils;
import com.get.institutioncenter.utils.TranslatorsApiUtils;
import com.get.remindercenter.feign.IReminderCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Author: Smail
 * Date: 15/5/2024
 */
@Slf4j
@Service
public class AsyncCompletionsImpl implements AsyncCompletions {

    @Resource
    private GetRedis getRedis;
    @Resource
    private TranslationMapper translationMapper;
    @Resource
    private TranslatorsApiUtils translatorsApiUtils;
    @Resource
    private TranslateApiUtils translateApiUtils;
    @Resource
    private InstitutionFacultyMapper institutionFacultyMapper;
    @Resource
    private InstitutionCourseSubjectMapper institutionCourseSubjectMapper;
    @Resource
    private InstitutionCourseEngScoreMapper institutionalCourseEngScoreMapper;
    @Resource
    private IReminderCenterClient iReminderCenterClient;
    @Resource
    private InstitutionCourseAcademicScoreMapper institutionCourseAcademicScoreMapper;
    @Resource
    private InstitutionCourseMapper institutionCourseMapper;
    @Resource(name = "institutionTaskExecutor")
    private ThreadPoolTaskExecutor executor;

    @Override
    public void batchTranslationInstitutionAndCourseInfo() {
        long initialTTL = 60L*60*3; // 初始TTL: 3小时
        long renewalInterval = initialTTL / 3; // 续期间隔: 初始TTL的1/3
        boolean lockAcquired = false;
        try {
            // 尝试获取锁
            lockAcquired = getRedis.setNx(CacheKeyConstants.BATCH_TRANSLATION_INSTITUTION_AND_COURSE_INFO_CACHE_KEY, 1, initialTTL);
            if (lockAcquired) {
                List<Institution> institutions = translationMapper.getInstitutionForTranslationDetails();
                List<Long> institutionIds = institutions.stream().map(Institution::getId).collect(Collectors.toList());
//                List<Long> institutionIds = batchTranslationInstitutionAndCourseInfoVo.getInstitutionIds();
                if (GeneralTool.isEmpty(institutionIds)){
                    return;
                }

                // 使用周期性任务来更新锁的过期时间，而不是创建一个新的线程
                executor.submit(() -> {
                    log.info("守护线程开启！！！");
                    while (true) {
                        try {
                            log.info("休眠1h！！！");
                            Thread.sleep(renewalInterval*1000);
                            Object o = getRedis.get(CacheKeyConstants.BATCH_TRANSLATION_INSTITUTION_AND_COURSE_INFO_CACHE_KEY);
                            if (GeneralTool.isEmpty(o)){
                                log.info("锁已过期！！！");
                                break;
                            }
                            Boolean expire = getRedis.expire(CacheKeyConstants.BATCH_TRANSLATION_INSTITUTION_AND_COURSE_INFO_CACHE_KEY, initialTTL);
                            if (!expire){
                                log.info("key续期失败！！！");
                            }else {
                                log.info("key续期成功！！！");
                            }
                        } catch (InterruptedException e) {
                            // 在捕获到InterruptedException后，尝试一次重新设置过期时间
                            Object o = getRedis.get(CacheKeyConstants.BATCH_TRANSLATION_INSTITUTION_AND_COURSE_INFO_CACHE_KEY);
                            if (GeneralTool.isNotEmpty(o)){
                                getRedis.expire(CacheKeyConstants.BATCH_TRANSLATION_INSTITUTION_AND_COURSE_INFO_CACHE_KEY, initialTTL);
                            }
                            Thread.currentThread().interrupt(); // 重新设置中断状态
                            break;
                        }
                    }
                });

                //翻译英语成绩
                translationInstitutionCourseEngScore();
                //翻译学术成绩
                translationInstitutionCourseAcademicScore();
                //翻译学校描述
                translationInstitutionDetails();
                //翻译学院名称
                translationInstitutionFacultyName();
                //翻译课程和简介
                translationInstitutionCourseInfo();
                //翻译课程专业科目
                translationInstitutionCourseSubject();


            } else {
                // 获取锁失败，直接返回
                log.info("获取锁失败");
            }
        } catch (Exception e) {
            log.info("异步翻译报错！", e);
        } finally {
            if (lockAcquired) {
                // 对删除操作增加重试机制
                int retryCount = 3;
                while (retryCount > 0) {
                    try {
                        getRedis.del(CacheKeyConstants.BATCH_TRANSLATION_INSTITUTION_AND_COURSE_INFO_CACHE_KEY);
                        log.info("释放锁institution_admin_batch_translation_institution_and_course_info_cache_key成功！！");
                        break;
                    } catch (Exception ex) {
                        log.info("释放锁失败", ex);
                        retryCount--;
                        try {
                            Thread.sleep(500); // 等待500ms后再次尝试
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt(); // 重新设置中断状态
                            break;
                        }
                    }
                }
            }
        }
    }

    public void translationInstitutionDetails() throws IOException {
        Map<String, String> cacheMap = Maps.newHashMap();
        Map<String, String> cacheMapZn = Maps.newHashMap();
        //先查询学校详情信息
        List<Institution> institutions = translationMapper.getInstitutionForTranslationDetails();
        List<Long> institutionIds = institutions.stream().map(Institution::getId).collect(Collectors.toList());
        if (GeneralTool.isEmpty(institutions)){
            return;
        }
        //查询翻译表中学校详情的信息
        List<InstitutionTranslation> institutionTranslationList = translationMapper.selectList(Wrappers.lambdaQuery(InstitutionTranslation.class)
                .in(InstitutionTranslation::getFkTableId, institutionIds)
                .eq(InstitutionTranslation::getFkTableName, "m_institution")
                .eq(InstitutionTranslation::getFkTranslationMappingId, 37L)
        );
        Map<Long, List<InstitutionTranslation>> translationListMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(institutionTranslationList)){
            translationListMap = institutionTranslationList.stream().collect(Collectors.groupingBy(InstitutionTranslation::getFkTableId));
        }
        if (GeneralTool.isNotEmpty(institutions)){
            for (Institution institution : institutions) {
                try{
                List<InstitutionTranslation> institutionTranslations = translationListMap.get(institution.getId());
                List<InstitutionTranslation> enTrans = Lists.newArrayList();
                List<InstitutionTranslation> cnTrans = Lists.newArrayList();
                if (GeneralTool.isNotEmpty(institutionTranslations)){
                    enTrans = institutionTranslations.stream().filter(institutionTranslation -> "en-us".equals(institutionTranslation.getLanguageCode())).collect(Collectors.toList());
                    cnTrans = institutionTranslations.stream().filter(institutionTranslation -> "zh-cn".equals(institutionTranslation.getLanguageCode())).collect(Collectors.toList());
                }

                if (GeneralTool.isEmpty(enTrans)&& GeneralTool.isNotEmpty(institution.getDetail())){
                    //如果是中文，调用翻译接口
                    if (containsChinese(institution.getDetail())){
                        String enContent = cacheMapZn.get(institution.getDetail());
                        if (GeneralTool.isEmpty(enContent)) {
                            try {
                                //中翻英
//                                enContent = translateApiUtils.getTrans(TranslateApiUtils.EN_USER, TranslateApiUtils.EN_US, institution.getDetail());
//                                enContent = translateApiUtils.getTrans(TranslateApiUtils.EN_USER, institution.getDetail(),TranslateApiUtils.MODEL)
//                                        .replaceFirst("```text", "").replaceFirst("```$", "").replaceAll("\\\\n","").trim();
                                //如果还存在中文
//                                if (containsChinese(enContent)){
//                                    enContent=translateApiUtils.getTrans(TranslateApiUtils.EN_USER, institution.getDetail(),TranslateApiUtils.MODEL_PLUS)
//                                            .replaceFirst("```text", "").replaceFirst("```$", "").replaceAll("\\\\n","").trim();
//                                }
                                enContent= translatorsApiUtils.getTrans(institution.getDetail(),TranslatorsApiUtils.Ch_lang, TranslatorsApiUtils.En_lang);
                                //添加翻译缓存 下次有一样的内容 直接不用翻译
                                cacheMapZn.put(institution.getDetail(), enContent);
                            } catch (Exception e) {
                                log.info("Translation error for institutionId: {}", institution.getId(), e);
                                throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
                            }
                            log.info("institutionId={}####translate success", institution.getId().toString());
                        }
                        if (GeneralTool.isNotEmpty(enContent)) {
                            InstitutionTranslation cnTranslation = new InstitutionTranslation();
                            cnTranslation.setLanguageCode("en-us");
                            cnTranslation.setFkTranslationMappingId(37L);
                            cnTranslation.setTranslation(enContent);
                            cnTranslation.setFkTableName("m_institution");
                            cnTranslation.setFkTableId(institution.getId());
                            cnTranslation.setGmtCreateUser("[system]");
                            cnTranslation.setGmtCreate(new Date());
                            translationMapper.insert(cnTranslation);
                        }
                    }else {
                        InstitutionTranslation enTranslation = new InstitutionTranslation();
                        enTranslation.setLanguageCode("en-us");
                        enTranslation.setFkTranslationMappingId(37L);
                        enTranslation.setTranslation(institution.getDetail());
                        enTranslation.setFkTableName("m_institution");
                        enTranslation.setFkTableId(institution.getId());
                        enTranslation.setGmtCreateUser("[system]");
                        enTranslation.setGmtCreate(new Date());
                        translationMapper.insert(enTranslation);
                    }
                }

                if (GeneralTool.isEmpty(cnTrans)&& GeneralTool.isNotEmpty(institution.getDetail())) {
                    //判断是否是中文
                    if (containsChinese(institution.getDetail())) {
                        InstitutionTranslation cnTranslation = new InstitutionTranslation();
                        cnTranslation.setLanguageCode("zh-cn");
                        cnTranslation.setFkTranslationMappingId(37L);
                        cnTranslation.setTranslation(institution.getDetail());
                        cnTranslation.setFkTableName("m_institution");
                        cnTranslation.setFkTableId(institution.getId());
                        cnTranslation.setGmtCreateUser("[system]");
                        cnTranslation.setGmtCreate(new Date());
                        translationMapper.insert(cnTranslation);
                    } else {
                        //如果是英文，调用翻译接口
                        String cnContent = cacheMap.get(institution.getDetail());
                        if (GeneralTool.isEmpty(cnContent)) {
                            try {
//                                cnContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER, TranslateApiUtils.ZH_DETAILS, institution.getDetail());
//                                cnContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER, institution.getDetail(),TranslateApiUtils.MODEL)
//                                        .replaceFirst("```text", "").replaceFirst("```$", "").replaceAll("\\\\n","").trim();

                                cnContent= translatorsApiUtils.getTrans(institution.getDetail(),TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang);
                                //添加翻译缓存 下次有一样的内容 直接不用翻译
                                cacheMap.put(institution.getDetail(), cnContent);
                            } catch (Exception e) {
                                log.info("Translation error for institutionId: {}", institution.getId(), e);
                                throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
                            }
                            log.info("institutionId={}####translate success", institution.getId().toString());
                        }
                        if (GeneralTool.isNotEmpty(cnContent)) {
                            InstitutionTranslation cnTranslation = new InstitutionTranslation();
                            cnTranslation.setLanguageCode("zh-cn");
                            cnTranslation.setFkTranslationMappingId(37L);
                            cnTranslation.setTranslation(cnContent);
                            cnTranslation.setFkTableName("m_institution");
                            cnTranslation.setFkTableId(institution.getId());
                            cnTranslation.setGmtCreateUser("[system]");
                            cnTranslation.setGmtCreate(new Date());
                            translationMapper.insert(cnTranslation);
                        }
                    }
                }
                }catch (Exception e){
                    iReminderCenterClient.sendMail("institution翻译报错", institution.getId()+" institution : "+ translatorsApiUtils.getTrans(institution.getDetail(), TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang),"<EMAIL>","<EMAIL>");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
                }
            }
        }
        cacheMap.clear();
        log.info("institution detail####translate end！");
    }

    private void translationInstitutionCourseAcademicScore() throws IOException {
        Map<String, String> cacheMap = com.google.common.collect.Maps.newHashMap();
        List<InstitutionCourseAcademicScore> institutionCourseAcademicScores = translationMapper.getInstitutionCourAcademicScoreForTranslations();
        if (GeneralTool.isEmpty(institutionCourseAcademicScores)){
            return;
        }
        List<Long> institutionCourseAcademicScoreIds = institutionCourseAcademicScores.stream().map(InstitutionCourseAcademicScore::getId).collect(Collectors.toList());

        List<InstitutionTranslation> descriptionTranslations = translationMapper.selectList(Wrappers.lambdaQuery(InstitutionTranslation.class)
                .in(InstitutionTranslation::getFkTableId, institutionCourseAcademicScoreIds)
                .eq(InstitutionTranslation::getFkTableName, "m_institution_course_academic_score")
                .eq(InstitutionTranslation::getFkTranslationMappingId, 93L)
        );
        Map<Long, List<InstitutionTranslation>> descriptionTranslationsMap = com.google.common.collect.Maps.newHashMap();
        if (GeneralTool.isNotEmpty(descriptionTranslations)){
            descriptionTranslationsMap = descriptionTranslations.stream().collect(Collectors.groupingBy(InstitutionTranslation::getFkTableId));
        }

        List<InstitutionTranslation> descriptionSorceTranslations = translationMapper.selectList(Wrappers.lambdaQuery(InstitutionTranslation.class)
                .in(InstitutionTranslation::getFkTableId, institutionCourseAcademicScoreIds)
                .eq(InstitutionTranslation::getFkTableName, "m_institution_course_academic_score")
                .eq(InstitutionTranslation::getFkTranslationMappingId, 94L)
        );
        Map<Long, List<InstitutionTranslation>> descriptionSorceTranslationsMap = com.google.common.collect.Maps.newHashMap();
        if (GeneralTool.isNotEmpty(descriptionSorceTranslations)){
            descriptionSorceTranslationsMap = descriptionSorceTranslations.stream().collect(Collectors.groupingBy(InstitutionTranslation::getFkTableId));
        }


        for (InstitutionCourseAcademicScore institutionCourseAcademicScore : institutionCourseAcademicScores) {
            try {
                List<InstitutionTranslation> descriptionTranslationList = descriptionTranslationsMap.get(institutionCourseAcademicScore.getId());
                List<InstitutionTranslation> enDescriptionTrans = com.google.common.collect.Lists.newArrayList();
                List<InstitutionTranslation> cnDescriptionTrans = com.google.common.collect.Lists.newArrayList();
                if (GeneralTool.isNotEmpty(descriptionTranslationList)) {
                    enDescriptionTrans = descriptionTranslationList.stream().filter(institutionTranslation -> "en-us".equals(institutionTranslation.getLanguageCode())).collect(Collectors.toList());
                    cnDescriptionTrans = descriptionTranslationList.stream().filter(institutionTranslation -> "zh-cn".equals(institutionTranslation.getLanguageCode())).collect(Collectors.toList());
                }

                if (GeneralTool.isEmpty(enDescriptionTrans) && GeneralTool.isNotEmpty(institutionCourseAcademicScore.getDescription())) {
                    InstitutionTranslation enTranslation = new InstitutionTranslation();
                    enTranslation.setLanguageCode("en-us");
                    enTranslation.setFkTranslationMappingId(93L);
                    enTranslation.setTranslation(institutionCourseAcademicScore.getDescription());
                    enTranslation.setFkTableName("m_institution_course_academic_score");
                    enTranslation.setFkTableId(institutionCourseAcademicScore.getId());
                    enTranslation.setGmtCreateUser("[system]");
                    enTranslation.setGmtCreate(new Date());
                    translationMapper.insert(enTranslation);
                }
                if (GeneralTool.isEmpty(cnDescriptionTrans) && GeneralTool.isNotEmpty(institutionCourseAcademicScore.getDescription())) {

                    String cnDescriptionContent = cacheMap.get(institutionCourseAcademicScore.getDescription());
                    if (GeneralTool.isEmpty(cnDescriptionContent)) {
                        try {
//                        cnDescriptionContent= translateApiUtils.getTrans(TranslateApiUtils.ZH_USER,TranslateApiUtils.ZH_ACADEMIC,institutionCourseAcademicScore.getDescription());
//                            cnDescriptionContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER, institutionCourseAcademicScore.getDescription(), TranslateApiUtils.MODEL)
//                                    .replaceFirst("```text", "").replaceFirst("```$", "").replaceAll("\\\\n", "").trim();
                            cnDescriptionContent = translatorsApiUtils.getTrans(institutionCourseAcademicScore.getDescription(),TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang);
                            //添加翻译缓存 下次有一样的内容 直接不用翻译
                            cacheMap.put(institutionCourseAcademicScore.getDescription(), cnDescriptionContent);
                        } catch (Exception e) {
                            log.info("Translation error for institutionCourseAcademicScoreId: {}", institutionCourseAcademicScore.getId(), e);
                            throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
                        }
                        log.info("institutionCourseAcademicScoreId={}####translate success", institutionCourseAcademicScore.getId().toString());
                    }
                    if (GeneralTool.isNotEmpty(cnDescriptionContent)) {
                        InstitutionTranslation cnTranslation = new InstitutionTranslation();
                        cnTranslation.setLanguageCode("zh-cn");
                        cnTranslation.setFkTranslationMappingId(93L);
                        cnTranslation.setTranslation(cnDescriptionContent);
                        cnTranslation.setFkTableName("m_institution_course_academic_score");
                        cnTranslation.setFkTableId(institutionCourseAcademicScore.getId());
                        cnTranslation.setGmtCreateUser("[system]");
                        cnTranslation.setGmtCreate(new Date());
                        translationMapper.insert(cnTranslation);
                    }
                }

            List<InstitutionTranslation> descriptionSourceTranslationList = descriptionSorceTranslationsMap.get(institutionCourseAcademicScore.getId());
            List<InstitutionTranslation> enDescriptionSourceTrans = com.google.common.collect.Lists.newArrayList();
            List<InstitutionTranslation> cnDescriptionSourceTrans = com.google.common.collect.Lists.newArrayList();
            if (GeneralTool.isNotEmpty(descriptionSourceTranslationList)){
                enDescriptionSourceTrans = descriptionSourceTranslationList.stream().filter(institutionTranslation -> "en-us".equals(institutionTranslation.getLanguageCode())).collect(Collectors.toList());
                cnDescriptionSourceTrans = descriptionSourceTranslationList.stream().filter(institutionTranslation -> "zh-cn".equals(institutionTranslation.getLanguageCode())).collect(Collectors.toList());
            }

            if (GeneralTool.isEmpty(enDescriptionSourceTrans)&& GeneralTool.isNotEmpty(institutionCourseAcademicScore.getDescriptionSource())){
                InstitutionTranslation enTranslation = new InstitutionTranslation();
                enTranslation.setLanguageCode("en-us");
                enTranslation.setFkTranslationMappingId(94L);
                enTranslation.setTranslation(institutionCourseAcademicScore.getDescriptionSource());
                enTranslation.setFkTableName("m_institution_course_academic_score");
                enTranslation.setFkTableId(institutionCourseAcademicScore.getId());
                enTranslation.setGmtCreateUser("[system]");
                enTranslation.setGmtCreate(new Date());
                translationMapper.insert(enTranslation);
            }
            if (GeneralTool.isEmpty(cnDescriptionSourceTrans)&& GeneralTool.isNotEmpty(institutionCourseAcademicScore.getDescriptionSource())){

                String cnDescriptionSourceContent = cacheMap.get(institutionCourseAcademicScore.getDescriptionSource());
                if (GeneralTool.isEmpty(cnDescriptionSourceContent)){
                    try {
//                        cnDescriptionSourceContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER,TranslateApiUtils.ZH_ACADEMIC,institutionCourseAcademicScore.getDescriptionSource());
//                            cnDescriptionSourceContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER, institutionCourseAcademicScore.getDescriptionSource(), TranslateApiUtils.MODEL)
//                                    .replaceFirst("```text", "").replaceFirst("```$", "").replaceAll("\\\\n", "").trim();
                            cnDescriptionSourceContent = translatorsApiUtils.getTrans(institutionCourseAcademicScore.getDescriptionSource(),TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang);
                            //添加翻译缓存 下次有一样的内容 直接不用翻译
                            cacheMap.put(institutionCourseAcademicScore.getDescriptionSource(), cnDescriptionSourceContent);
                        } catch (Exception e) {
                            log.info("2222 Translation error for institutionCourseAcademicScoreId: {}", institutionCourseAcademicScore.getId(), e);
                            throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
                        }
                        log.info("2222 institutionCourseAcademicScoreId={}####translate success", institutionCourseAcademicScore.getId().toString());
                    }
                    if (GeneralTool.isNotEmpty(cnDescriptionSourceContent)) {
                        InstitutionTranslation cnTranslation = new InstitutionTranslation();
                        cnTranslation.setLanguageCode("zh-cn");
                        cnTranslation.setFkTranslationMappingId(94L);
                        cnTranslation.setTranslation(cnDescriptionSourceContent);
                        cnTranslation.setFkTableName("m_institution_course_academic_score");
                        cnTranslation.setFkTableId(institutionCourseAcademicScore.getId());
                        cnTranslation.setGmtCreateUser("[system]");
                        cnTranslation.setGmtCreate(new Date());
                        translationMapper.insert(cnTranslation);
                    }
                }
            }catch (Exception e){
                iReminderCenterClient.sendMail("institution翻译报错", institutionCourseAcademicScore.getId()+" institution : "+ translatorsApiUtils.getTrans(institutionCourseAcademicScore.getDescriptionSource(), TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang),"<EMAIL>","<EMAIL>");
                throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
            }
        }
        cacheMap.clear();
        log.info("institution detail####translate end！");
    }

    private void translationInstitutionCourseEngScore() throws IOException {
        Map<String, String> cacheMap = com.google.common.collect.Maps.newHashMap();
        List<InstitutionCourseEngScore> institutionCourseEngScores = translationMapper.getInstitutionCourEngScoreForTranslations();
        if (GeneralTool.isEmpty(institutionCourseEngScores)){
            return;
        }
        List<Long> institutionCourseEngScoreIds = institutionCourseEngScores.stream().map(InstitutionCourseEngScore::getId).collect(Collectors.toList());

        List<InstitutionTranslation> descriptionTranslations = translationMapper.selectList(Wrappers.lambdaQuery(InstitutionTranslation.class)
                .in(InstitutionTranslation::getFkTableId, institutionCourseEngScoreIds)
                .eq(InstitutionTranslation::getFkTableName, "m_institution_course_eng_score")
                .eq(InstitutionTranslation::getFkTranslationMappingId, 91L)
        );
        Map<Long, List<InstitutionTranslation>> descriptionTranslationsMap = com.google.common.collect.Maps.newHashMap();
        if (GeneralTool.isNotEmpty(descriptionTranslations)){
            descriptionTranslationsMap = descriptionTranslations.stream().collect(Collectors.groupingBy(InstitutionTranslation::getFkTableId));
        }

        List<InstitutionTranslation> descriptionSorceTranslations = translationMapper.selectList(Wrappers.lambdaQuery(InstitutionTranslation.class)
                .in(InstitutionTranslation::getFkTableId, institutionCourseEngScoreIds)
                .eq(InstitutionTranslation::getFkTableName, "m_institution_course_eng_score")
                .eq(InstitutionTranslation::getFkTranslationMappingId, 92L)
        );
        Map<Long, List<InstitutionTranslation>> descriptionSorceTranslationsMap = com.google.common.collect.Maps.newHashMap();
        if (GeneralTool.isNotEmpty(descriptionSorceTranslations)){
            descriptionSorceTranslationsMap = descriptionSorceTranslations.stream().collect(Collectors.groupingBy(InstitutionTranslation::getFkTableId));
        }

        for (InstitutionCourseEngScore institutionCourseEngScore : institutionCourseEngScores) {
            try {
                List<InstitutionTranslation> descriptionTranslationList = descriptionTranslationsMap.get(institutionCourseEngScore.getId());
                List<InstitutionTranslation> enDescriptionTrans = com.google.common.collect.Lists.newArrayList();
                List<InstitutionTranslation> cnDescriptionTrans = com.google.common.collect.Lists.newArrayList();
                if (GeneralTool.isNotEmpty(descriptionTranslationList)) {
                    enDescriptionTrans = descriptionTranslationList.stream().filter(institutionTranslation -> "en-us".equals(institutionTranslation.getLanguageCode())).collect(Collectors.toList());
                    cnDescriptionTrans = descriptionTranslationList.stream().filter(institutionTranslation -> "zh-cn".equals(institutionTranslation.getLanguageCode())).collect(Collectors.toList());
                }

            if (GeneralTool.isEmpty(enDescriptionTrans)&& GeneralTool.isNotEmpty(institutionCourseEngScore.getDescription())){
                InstitutionTranslation enTranslation = new InstitutionTranslation();
                enTranslation.setLanguageCode("en-us");
                enTranslation.setFkTranslationMappingId(91L);
                enTranslation.setTranslation(institutionCourseEngScore.getDescription());
                enTranslation.setFkTableName("m_institution_course_eng_score");
                enTranslation.setFkTableId(institutionCourseEngScore.getId());
                enTranslation.setGmtCreateUser("[system]");
                enTranslation.setGmtCreate(new Date());
                translationMapper.insert(enTranslation);
            }
            if (GeneralTool.isEmpty(cnDescriptionTrans)&& GeneralTool.isNotEmpty(institutionCourseEngScore.getDescription())){

                String cnDescriptionContent = cacheMap.get(institutionCourseEngScore.getDescription());
                if (GeneralTool.isEmpty(cnDescriptionContent)){
                    try {
//                        cnDescriptionContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER,TranslateApiUtils.ZH_ACADEMIC,institutionCourseEngScore.getDescription()) ;
//                            cnDescriptionContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER, institutionCourseEngScore.getDescription(), TranslateApiUtils.MODEL)
//                                    .replaceFirst("```text", "").replaceFirst("```$", "").replaceAll("\\\\n", "").trim();
                            cnDescriptionContent = translatorsApiUtils.getTrans(institutionCourseEngScore.getDescription(),TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang);
                            //添加翻译缓存 下次有一样的内容 直接不用翻译
                            cacheMap.put(institutionCourseEngScore.getDescription(), cnDescriptionContent);
                        } catch (Exception e) {
                            log.info("Translation error for institutionCourseEngScoreId: {}", institutionCourseEngScore.getId(), e);
                            throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
                        }
                        log.info("institutionCourseEngScoreId={}####translate success", institutionCourseEngScore.getId().toString());
                    }
                    if (GeneralTool.isNotEmpty(cnDescriptionContent)) {
                        InstitutionTranslation cnTranslation = new InstitutionTranslation();
                        cnTranslation.setLanguageCode("zh-cn");
                        cnTranslation.setFkTranslationMappingId(91L);
                        cnTranslation.setTranslation(cnDescriptionContent);
                        cnTranslation.setFkTableName("m_institution_course_eng_score");
                        cnTranslation.setFkTableId(institutionCourseEngScore.getId());
                        cnTranslation.setGmtCreateUser("[system]");
                        cnTranslation.setGmtCreate(new Date());
                        translationMapper.insert(cnTranslation);
                    }
                }

            List<InstitutionTranslation> descriptionSourceTranslationList = descriptionSorceTranslationsMap.get(institutionCourseEngScore.getId());
            List<InstitutionTranslation> enDescriptionSourceTrans = com.google.common.collect.Lists.newArrayList();
            List<InstitutionTranslation> cnDescriptionSourceTrans = com.google.common.collect.Lists.newArrayList();
            if (GeneralTool.isNotEmpty(descriptionSourceTranslationList)){
                enDescriptionSourceTrans = descriptionSourceTranslationList.stream().filter(institutionTranslation -> "en-us".equals(institutionTranslation.getLanguageCode())).collect(Collectors.toList());
                cnDescriptionSourceTrans = descriptionSourceTranslationList.stream().filter(institutionTranslation -> "zh-cn".equals(institutionTranslation.getLanguageCode())).collect(Collectors.toList());
            }

            if (GeneralTool.isEmpty(enDescriptionSourceTrans)&& GeneralTool.isNotEmpty(institutionCourseEngScore.getDescriptionSource())){
                InstitutionTranslation enTranslation = new InstitutionTranslation();
                enTranslation.setLanguageCode("en-us");
                enTranslation.setFkTranslationMappingId(92L);
                enTranslation.setTranslation(institutionCourseEngScore.getDescriptionSource());
                enTranslation.setFkTableName("m_institution_course_eng_score");
                enTranslation.setFkTableId(institutionCourseEngScore.getId());
                enTranslation.setGmtCreateUser("[system]");
                enTranslation.setGmtCreate(new Date());
                translationMapper.insert(enTranslation);
            }
            if (GeneralTool.isEmpty(cnDescriptionSourceTrans)&& GeneralTool.isNotEmpty(institutionCourseEngScore.getDescriptionSource())){

                    String cnDescriptionSourceContent = cacheMap.get(institutionCourseEngScore.getDescriptionSource());
                    if (GeneralTool.isEmpty(cnDescriptionSourceContent)) {
                        try {
//                            cnDescriptionSourceContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER, institutionCourseEngScore.getDescriptionSource(), TranslateApiUtils.MODEL)
//                                    .replaceFirst("```text", "").replaceFirst("```$", "").replaceAll("\\\\n", "").trim();
//                        cnDescriptionSourceContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER,TranslateApiUtils.ZH_ACADEMIC,institutionCourseEngScore.getDescriptionSource());
                            cnDescriptionSourceContent = translatorsApiUtils.getTrans(institutionCourseEngScore.getDescriptionSource(),TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang);
                            //添加翻译缓存 下次有一样的内容 直接不用翻译
                            cacheMap.put(institutionCourseEngScore.getDescriptionSource(), cnDescriptionSourceContent);
                        } catch (Exception e) {
                            log.info("2222 Translation error for institutionCourseEngScoreId: {}", institutionCourseEngScore.getId(), e);
                            throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
                        }
                        log.info("2222 institutionCourseEngScoreId={}####translate success", institutionCourseEngScore.getId().toString());
                    }
                    if (GeneralTool.isNotEmpty(cnDescriptionSourceContent)) {
                        InstitutionTranslation cnTranslation = new InstitutionTranslation();
                        cnTranslation.setLanguageCode("zh-cn");
                        cnTranslation.setFkTranslationMappingId(92L);
                        cnTranslation.setTranslation(cnDescriptionSourceContent);
                        cnTranslation.setFkTableName("m_institution_course_eng_score");
                        cnTranslation.setFkTableId(institutionCourseEngScore.getId());
                        cnTranslation.setGmtCreateUser("[system]");
                        cnTranslation.setGmtCreate(new Date());
                        translationMapper.insert(cnTranslation);
                    }
                }
            }catch (Exception e){
                iReminderCenterClient.sendMail("institutionCourseEngScore翻译报错", institutionCourseEngScore.getId()+" institutionCourseEngScore : "+ translatorsApiUtils.getTrans(institutionCourseEngScore.getDescriptionSource(), TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang),"<EMAIL>","<EMAIL>");
                throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
            }
        }
        cacheMap.clear();
        log.info("institution detail####translate end！");
    }

    private void translationInstitutionCourseSubject() throws IOException {
        Map<String, String> cacheMap = com.google.common.collect.Maps.newHashMap();
        List<InstitutionCourseSubject> institutionCourseSubjects = translationMapper.getInstitutionCourseSubjectForTranslations();
        if (GeneralTool.isEmpty(institutionCourseSubjects)){
            return;
        }

        for (InstitutionCourseSubject institutionCourseSubject : institutionCourseSubjects) {
            try{
                if (GeneralTool.isNotEmpty(institutionCourseSubject.getSubjectNameChn()) || GeneralTool.isEmpty(institutionCourseSubject.getSubjectName())){
                    continue;
                }
                String cnContent = cacheMap.get(institutionCourseSubject.getSubjectName());
                //如果是有中文就不用翻译
                if (GeneralTool.isEmpty(cnContent)){
                    try {
//                    cnContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER,TranslateApiUtils.ZH_COURSE_SUBJECT,institutionCourseSubject.getSubjectName())
                    cnContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_COURSE_USER,institutionCourseSubject.getSubjectName(),TranslateApiUtils.MODEL)
                            .replaceFirst("```text", "").replaceAll("```", "").replaceAll("\\\\n","").trim();

//                        cnContent = translatorsApiUtils.getTrans(institutionCourseSubject.getSubjectName(), TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang);
                        //添加翻译缓存 下次有一样的内容 直接不用翻译
                        cacheMap.put(institutionCourseSubject.getSubjectName(),cnContent);
                    } catch (Exception e) {
                        log.info("Translation error for institutionCourseSubjectId: {}", institutionCourseSubject.getId(), e);
                        throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
                    }
                    log.info("institutionCourseSubjectId={}####translate success", institutionCourseSubject.getId().toString());
                }
                if (GeneralTool.isNotEmpty(cnContent)&& cnContent.length()<=50){
                    institutionCourseSubject.setSubjectNameChn(cnContent);
                    institutionCourseSubject.setGmtModified(new Date());
                    institutionCourseSubject.setGmtModifiedUser("[system]");
                    institutionCourseSubjectMapper.updateById(institutionCourseSubject);
                }
            }catch (Exception e){
               iReminderCenterClient.sendMail("institutionCourseSubject翻译报错", institutionCourseSubject.getId()+" institutionCourseSubject : "+ translatorsApiUtils.getTrans(institutionCourseSubject.getSubjectName(), TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang),"<EMAIL>","<EMAIL>");
               throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
            }

        }

        cacheMap.clear();
        log.info("institution Course Subject ####translate end！");
    }

    private void translationInstitutionCourseInfo() throws IOException {
        Map<String, String> cacheMap = com.google.common.collect.Maps.newHashMap();
        List<InstitutionCourse> institutionCourses = translationMapper.getInstitutionCourseForTranslations();
        if (GeneralTool.isEmpty(institutionCourses)){
            return;
        }
        List<Long> courseIds = institutionCourses.stream().map(InstitutionCourse::getId).collect(Collectors.toList());
        List<InstitutionTranslation> institutionTranslationList = translationMapper.selectList(Wrappers.lambdaQuery(InstitutionTranslation.class)
                .in(InstitutionTranslation::getFkTableId, courseIds)
                .eq(InstitutionTranslation::getFkTableName, "m_institution_course")
                .eq(InstitutionTranslation::getFkTranslationMappingId, 90L)
        );
        Map<Long, List<InstitutionTranslation>> translationListMap = com.google.common.collect.Maps.newHashMap();
        if (GeneralTool.isNotEmpty(institutionTranslationList)){
            translationListMap = institutionTranslationList.stream().collect(Collectors.groupingBy(InstitutionTranslation::getFkTableId));
        }

        for (InstitutionCourse institutionCourse : institutionCourses) {
            try{


            if (GeneralTool.isNotEmpty(institutionCourse.getNameChn()) || GeneralTool.isEmpty(institutionCourse.getName())){
                continue;
            }
            String cnContent = cacheMap.get(institutionCourse.getName());
            if (GeneralTool.isEmpty(cnContent)){
                try {
//                    cnContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER,TranslateApiUtils.ZH_COURSE_SUBJECT,institutionCourse.getName())
                    cnContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_COURSE_USER,institutionCourse.getName(),TranslateApiUtils.MODEL)
                            .replaceFirst("```text", "").replaceAll("```", "").replaceAll("\\\\n","").trim();
//                    cnContent =translatorsApiUtils.getTrans(institutionCourse.getName(),TranslatorsApiUtils.En_lang,TranslatorsApiUtils.Ch_lang);
                    //添加翻译缓存 下次有一样的内容 直接不用翻译
                    cacheMap.put(institutionCourse.getName(),cnContent);
                } catch (Exception e) {
                    log.info("Translation error for institutionCourseId: {}", institutionCourse.getId(), e);
                    throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
                }
                log.info("institutionCourseId={}####translate success", institutionCourse.getId().toString());
            }

            if (GeneralTool.isNotEmpty(cnContent)){
                institutionCourse.setNameChn(cnContent);
                institutionCourse.setGmtModified(new Date());
                institutionCourse.setGmtModifiedUser("[system]");
                institutionCourseMapper.updateById(institutionCourse);
            }

            List<InstitutionTranslation> institutionTranslations = translationListMap.get(institutionCourse.getId());
            List<InstitutionTranslation> enTrans = com.google.common.collect.Lists.newArrayList();
            List<InstitutionTranslation> cnTrans = com.google.common.collect.Lists.newArrayList();
            if (GeneralTool.isNotEmpty(institutionTranslations)){
                enTrans = institutionTranslations.stream().filter(institutionTranslation -> "en-us".equals(institutionTranslation.getLanguageCode())).collect(Collectors.toList());
                cnTrans = institutionTranslations.stream().filter(institutionTranslation -> "zh-cn".equals(institutionTranslation.getLanguageCode())).collect(Collectors.toList());
            }

            if (GeneralTool.isEmpty(enTrans)&& GeneralTool.isNotEmpty(institutionCourse.getIntroduction())){
                InstitutionTranslation enTranslation = new InstitutionTranslation();
                enTranslation.setLanguageCode("en-us");
                enTranslation.setFkTranslationMappingId(90L);
                enTranslation.setTranslation(institutionCourse.getIntroduction());
                enTranslation.setFkTableName("m_institution_course");
                enTranslation.setFkTableId(institutionCourse.getId());
                enTranslation.setGmtCreateUser("[system]");
                enTranslation.setGmtCreate(new Date());
                translationMapper.insert(enTranslation);
            }
            if (GeneralTool.isEmpty(cnTrans)&& GeneralTool.isNotEmpty(institutionCourse.getIntroduction())){

                String cnIntroduction = cacheMap.get(institutionCourse.getIntroduction());
                if (GeneralTool.isEmpty(cnIntroduction)){
                    try {
//                        cnIntroduction = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER,TranslateApiUtils.ZH_COURSE,institutionCourse.getIntroduction());
//                        cnIntroduction = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER,institutionCourse.getIntroduction(),TranslateApiUtils.MODEL)
//                                .replaceFirst("```text", "").replaceFirst("```$", "").replaceAll("\\\\n","").trim();
                        cnIntroduction = translatorsApiUtils.getTrans(institutionCourse.getIntroduction(),TranslatorsApiUtils.En_lang,TranslatorsApiUtils.Ch_lang);
                        //添加翻译缓存 下次有一样的内容 直接不用翻译
                        cacheMap.put(institutionCourse.getIntroduction(),cnIntroduction);
                    } catch (Exception e) {
                        log.info("222 Translation error for institutionCourseId: {}", institutionCourse.getId(), e);
                        throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
                    }
                    log.info("222 institutionCourseId={}####translate success", institutionCourse.getId().toString());
                }
                if (GeneralTool.isNotEmpty(cnIntroduction)){
                    InstitutionTranslation cnTranslation = new InstitutionTranslation();
                    cnTranslation.setLanguageCode("zh-cn");
                    cnTranslation.setFkTranslationMappingId(90L);
                    cnTranslation.setTranslation(cnIntroduction);
                    cnTranslation.setFkTableName("m_institution_course");
                    cnTranslation.setFkTableId(institutionCourse.getId());
                    cnTranslation.setGmtCreateUser("[system]");
                    cnTranslation.setGmtCreate(new Date());
                    translationMapper.insert(cnTranslation);
                }
            }
            }catch (Exception e){
                iReminderCenterClient.sendMail("institutionCourse翻译报错", institutionCourse.getId()+" institutionCourse : "+ translatorsApiUtils.getTrans(institutionCourse.getIntroduction(), TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang),"<EMAIL>","<EMAIL>");
                throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
            }
        }
        cacheMap.clear();
        log.info("institution Course Info ####translate end！");

    }

    private void translationInstitutionFacultyName() throws IOException {
        Map<String, String> cacheMap = com.google.common.collect.Maps.newHashMap();
        List<InstitutionFaculty> institutionFaculties = translationMapper.getInstitutionFacultyForTranslations();
        if (GeneralTool.isEmpty(institutionFaculties)){
            return;
        }
        for (InstitutionFaculty institutionFaculty : institutionFaculties) {
            try {
                if (GeneralTool.isNotEmpty(institutionFaculty.getNameChn()) || GeneralTool.isEmpty(institutionFaculty.getName())) {
                    continue;
                }
                String cnContent = cacheMap.get(institutionFaculty.getName());
                if (GeneralTool.isEmpty(cnContent)) {
                    try {
//                    cnContent = apiUtil.getTransResult(institutionFaculty.getName(), TransApi.EN_US,TransApi.ZH_CN,batchTranslationInstitutionAndCourseInfoVo.getAppId(),batchTranslationInstitutionAndCourseInfoVo.getKey());
//                    cnContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER,TranslateApiUtils.ZH_FACULTY,institutionFaculty.getName());
//                        cnContent = translateApiUtils.getTrans(TranslateApiUtils.ZH_USER, institutionFaculty.getName(), TranslateApiUtils.MODEL)
//                                .replaceFirst("```text", "").replaceFirst("```$", "").replaceAll("\\\\n", "").trim();
                        cnContent = translatorsApiUtils.getTrans(institutionFaculty.getName(),TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang);
                        //添加翻译缓存 下次有一样的内容 直接不用翻译
                        cacheMap.put(institutionFaculty.getName(), cnContent);
                    } catch (Exception e) {
                        log.info("Translation error for institutionFacultyId: {}", institutionFaculty.getId(), e);
                        throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
                    }
                    log.info("institutionFacultyId={}####translate success", institutionFaculty.getId().toString());
                }

                if (GeneralTool.isNotEmpty(cnContent)) {
                    institutionFaculty.setNameChn(cnContent);
                    institutionFaculty.setGmtModified(new Date());
                    institutionFaculty.setGmtModifiedUser("[system]");
                    institutionFacultyMapper.updateById(institutionFaculty);
                }
            }catch (Exception e){
                iReminderCenterClient.sendMail("institutionFaculty翻译报错", institutionFaculty.getId()+" institutionFaculty : "+ translatorsApiUtils.getTrans(institutionFaculty.getName(), TranslatorsApiUtils.En_lang, TranslatorsApiUtils.Ch_lang),"<EMAIL>","<EMAIL>");
                throw new GetServiceException(LocaleMessageUtils.getMessage("translation_error"));
            }
        }
        cacheMap.clear();
        log.info("institution Faculty Name ####translate end！");

    }

    public static boolean containsChinese(String s) {
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(s);
        return m.find();
    }
}
