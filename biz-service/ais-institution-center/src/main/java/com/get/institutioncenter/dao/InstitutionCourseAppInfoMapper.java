package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import com.get.institutioncenter.vo.AppCourseMappingInfoVo;
import com.get.institutioncenter.vo.CourseAppInfoPriorityVo;
import com.get.institutioncenter.vo.CourseOtherInfoVo;
import com.get.institutioncenter.entity.InstitutionCourseAppInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Mapper
public interface InstitutionCourseAppInfoMapper extends GetMapper<InstitutionCourseAppInfo> {

    /**
     * 获取是否存在相同目标类型目标对象信息的结果 比如奖学金
     * @param key
     * @param val
     * @return
     */
    Integer selectByKeyAndVal(@Param("tableName") String tableName,@Param("key") String key,@Param("val") String val
                    ,@Param("tableId") Long tableId,@Param("effectiveDate") String effectiveDate);

    WeScholarshipAppDto getCourseInfo(@Param("institutionId") Long institutionId, @Param("courseId") Long courseId);

    List<CourseAppInfoPriorityVo> priorityMatchingQuery(@Param("weScholarshipAppDto") WeScholarshipAppDto weScholarshipAppDto,
                                                        @Param("priorityTypeKey") Map<Integer,String> priorityTypeKey, @Param("fkTableName")String fkTableName);


    List<CourseAppInfoPriorityVo> priorityMatchingQueryTwo(@Param("weScholarshipAppDto") WeScholarshipAppDto weScholarshipAppDto,
                                                           @Param("fkTableName")String fkTableName);

    List<WeScholarshipAppDto> getOtherInfo(Long courseId);

    List<CourseOtherInfoVo> getCourseOtherInfo(IPage<CourseOtherInfoVo> iPage, @Param("institutionIds") List<Long> institutionIds,
                                               @Param("majorLevelIds") List<Long> majorLevelIds,
                                               @Param("courseIds") List<Long> courseIds,
                                               @Param("notInCourseIds") List<Long> notInCourseIds,
                                               @Param("institutionFacultyIds") List<Long> institutionFacultyIds,
                                               @Param("courseTypeGroupIds") List<Long> courseTypeGroupIds,
                                               @Param("courseTypeIds") List<Long> courseTypeIds);

    List<AppCourseMappingInfoVo> getAppInfoMappingInfo(@Param("fkTableId") Long fkTableId, @Param("fkTableName")String fkTableName);
}
