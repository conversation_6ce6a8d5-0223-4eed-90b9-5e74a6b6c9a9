<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionCourseEngScoreMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionCourseEngScore">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_institution_course_id" jdbcType="BIGINT" property="fkInstitutionCourseId" />
    <result column="condition_type" jdbcType="INTEGER" property="conditionType" />
    <result column="overall" jdbcType="DECIMAL" property="overall" />
    <result column="listening" jdbcType="DECIMAL" property="listening" />
    <result column="speaking" jdbcType="DECIMAL" property="speaking" />
    <result column="reading" jdbcType="DECIMAL" property="reading" />
    <result column="writing" jdbcType="DECIMAL" property="writing" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionCourseEngScore">
    insert into m_institution_course_eng_score (id, fk_institution_course_id, condition_type, 
      overall, listening, speaking, 
      reading, writing, description, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkInstitutionCourseId,jdbcType=BIGINT}, #{conditionType,jdbcType=INTEGER}, 
      #{overall,jdbcType=DECIMAL}, #{listening,jdbcType=DECIMAL}, #{speaking,jdbcType=DECIMAL}, 
      #{reading,jdbcType=DECIMAL}, #{writing,jdbcType=DECIMAL}, #{description,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionCourseEngScore">
    insert into m_institution_course_eng_score
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInstitutionCourseId != null">
        fk_institution_course_id,
      </if>
      <if test="conditionType != null">
        condition_type,
      </if>
      <if test="overall != null">
        overall,
      </if>
      <if test="listening != null">
        listening,
      </if>
      <if test="speaking != null">
        speaking,
      </if>
      <if test="reading != null">
        reading,
      </if>
      <if test="writing != null">
        writing,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionCourseId != null">
        #{fkInstitutionCourseId,jdbcType=BIGINT},
      </if>
      <if test="conditionType != null">
        #{conditionType,jdbcType=INTEGER},
      </if>
      <if test="overall != null">
        #{overall,jdbcType=DECIMAL},
      </if>
      <if test="listening != null">
        #{listening,jdbcType=DECIMAL},
      </if>
      <if test="speaking != null">
        #{speaking,jdbcType=DECIMAL},
      </if>
      <if test="reading != null">
        #{reading,jdbcType=DECIMAL},
      </if>
      <if test="writing != null">
        #{writing,jdbcType=DECIMAL},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>