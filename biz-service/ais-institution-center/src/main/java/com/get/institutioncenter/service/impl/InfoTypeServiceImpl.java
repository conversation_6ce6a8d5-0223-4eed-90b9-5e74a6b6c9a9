package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.component.TranslationHelp;
import com.get.institutioncenter.dao.InfoTypeMapper;
import com.get.institutioncenter.po.InfoTypePo;
import com.get.institutioncenter.vo.InfoTypeVo;
import com.get.institutioncenter.entity.InfoType;
import com.get.institutioncenter.service.IInfoTypeService;
import com.get.institutioncenter.service.ITranslationMappingService;
import com.get.institutioncenter.dto.InfoTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * @author: Sea
 * @create: 2020/8/5 10:32
 * @verison: 1.0
 * @description: 资讯类型管理实现类
 */
@Service
public class InfoTypeServiceImpl extends BaseServiceImpl<InfoTypeMapper, InfoType> implements IInfoTypeService {

    @Resource
    private InfoTypeMapper infoTypeMapper;

    @Resource
    private UtilService utilService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private GetRedis redisClient;
    @Resource
    private TranslationHelp translationHelp;

    @Override
    public InfoTypeVo findInfoTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InfoType infoType = infoTypeMapper.selectById(id);
        InfoTypeVo infoTypeVo = BeanCopyUtils.objClone(infoType, InfoTypeVo::new);
        infoTypeVo.setFkTableName(TableEnum.INSTITUTION_INFO_TYPE.key);
        String language = SecureUtil.getLocale();
        InfoTypePo infoTypePo = BeanCopyUtils.objClone(infoTypeVo, InfoTypePo::new);
        if (GeneralTool.isNotEmpty(infoTypePo) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(Collections.singletonList(infoTypePo), ProjectKeyEnum.getInitialValue(language));
        }
        if (GeneralTool.isNotEmpty(infoTypePo)){
            infoTypeVo = BeanCopyUtils.objClone(infoTypePo, InfoTypeVo::new);
        }
        return infoTypeVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<InfoTypeDto> infoTypeDtos) {
        if (GeneralTool.isEmpty(infoTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = infoTypeMapper.getMaxViewOrder();
        for (InfoTypeDto infoTypeDto : infoTypeDtos) {
            if (GeneralTool.isEmpty(infoTypeDto.getId())) {
                if (validateAdd(infoTypeDto)) {
                    InfoType infoType = BeanCopyUtils.objClone(infoTypeDto, InfoType::new);
                    infoType.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(infoType);
                    infoTypeMapper.insertSelective(infoType);
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            } else {
                if (validateUpdate(infoTypeDto)) {
                    InfoType infoType = BeanCopyUtils.objClone(infoTypeDto, InfoType::new);
                    utilService.updateUserInfoToEntity(infoType);
                    infoTypeMapper.updateById(infoType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }

        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findInfoTypeById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        infoTypeMapper.deleteById(id);
        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_INFO_TYPE.key, id);
    }

    @Override
    public InfoTypeVo updateInfoType(InfoTypeDto infoTypeDto) {
        if (infoTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InfoType result = infoTypeMapper.selectById(infoTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (validateUpdate(infoTypeDto)) {
            InfoType infoType = BeanCopyUtils.objClone(infoTypeDto, InfoType::new);
            utilService.updateUserInfoToEntity(infoType);
            infoTypeMapper.updateById(infoType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findInfoTypeById(infoTypeDto.getId());
    }

    @Override
    public List<InfoTypeVo> getInfoTypes(InfoTypeDto infoTypeDto, Page page) {
        LambdaQueryWrapper<InfoType> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(infoTypeDto)) {
            if (GeneralTool.isNotEmpty(infoTypeDto.getKeyWord())) {
                wrapper.like(InfoType::getTypeName, infoTypeDto.getKeyWord());
            }
        }
        wrapper.orderByDesc(InfoType::getViewOrder);
        //获取分页数据
        IPage<InfoType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InfoType> infoTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<InfoTypeVo> convertDatas = new ArrayList<>();
        for (InfoType infoType : infoTypes) {
            InfoTypeVo infoTypeVo = BeanCopyUtils.objClone(infoType, InfoTypeVo::new);
            infoTypeVo.setFkTableName(TableEnum.INSTITUTION_INFO_TYPE.key);
            convertDatas.add(infoTypeVo);
        }
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(convertDatas) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(convertDatas, ProjectKeyEnum.getInitialValue(language));
        }
        return convertDatas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<InfoTypeDto> infoTypeDtos) {
        if (GeneralTool.isEmpty(infoTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InfoType ro = BeanCopyUtils.objClone(infoTypeDtos.get(0), InfoType::new);
        Integer oneorder = ro.getViewOrder();
        InfoType rt = BeanCopyUtils.objClone(infoTypeDtos.get(1), InfoType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        infoTypeMapper.updateById(ro);
        infoTypeMapper.updateById(rt);
    }

    @Override
    public List<BaseSelectEntity> getInfoTypeList() {
        String language = SecureUtil.getLocale();
        List<BaseSelectEntity> infoTypeList = infoTypeMapper.getInfoTypeList("en".equals(language));
        return infoTypeList;
    }

    @Override
    public Map<Long, String> getInfoTypeNamesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<InfoType> wrapper = new LambdaQueryWrapper();
        wrapper.in(InfoType::getId, ids);
        List<InfoType> infoTypes = infoTypeMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(infoTypes)) {
            return map;
        }
        for (InfoType infoType : infoTypes) {
            map.put(infoType.getId(), infoType.getTypeName());
        }
        return map;
    }

    private boolean validateAdd(InfoTypeDto infoTypeDto) {
        LambdaQueryWrapper<InfoType> wrapper = new LambdaQueryWrapper();
        wrapper.in(InfoType::getTypeName, infoTypeDto.getTypeName());
        List<InfoType> list = infoTypeMapper.selectList(wrapper);
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(InfoTypeDto infoTypeDto) {
        LambdaQueryWrapper<InfoType> wrapper = new LambdaQueryWrapper();
        wrapper.in(InfoType::getTypeName, infoTypeDto.getTypeName());
        List<InfoType> list = infoTypeMapper.selectList(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(infoTypeDto.getId());
    }
}
