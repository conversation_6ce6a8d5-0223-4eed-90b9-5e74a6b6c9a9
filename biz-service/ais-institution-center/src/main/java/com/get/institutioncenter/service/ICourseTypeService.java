package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.CourseTypeVo;
import com.get.institutioncenter.entity.CourseType;
import com.get.institutioncenter.dto.CourseTypeDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/12/7
 * @TIME: 11:19
 * @Description:
 **/
public interface ICourseTypeService extends BaseService<CourseType> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    CourseTypeVo findCourseTypeById(Long id);

    /**
     * 列表数据
     *
     * @param courseTypeDto
     * @param page
     * @return
     */
    List<CourseTypeVo> getCourseTypes(CourseTypeDto courseTypeDto, Page page);

    /**
     * 修改
     *
     * @param courseTypeDto
     * @return
     */
    CourseTypeVo updateCourseType(CourseTypeDto courseTypeDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param courseTypeDtos
     * @return
     */
    void batchAdd(List<CourseTypeDto> courseTypeDtos);

    /**
     * 上移下移
     *
     * @param courseTypeDtos
     * @return
     */
    void movingOrder(List<CourseTypeDto> courseTypeDtos);

    /**
     * 全部名称下拉框
     *
     * @return
     */
    List<BaseSelectEntity> getCourseTypeList();

    /**
     * 获取课程名称
     * @param keyword
     * @return
     */
    List<BaseSelectEntity> getCourseTypeList(String keyword);
    /**
     * mode获取全部名称下拉框
     *
     * @return
     */
    List<BaseSelectEntity> getCourseTypeListByMode();

    /**
     * @Description :通过课程类型ids 查找对应的课程类型名称map
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getCourseTypeNamesByIds(Set<Long> ids);

    /**
     * 获取课程类型名称
     * @param id
     * @return
     */
    String getCourseTypeNameById(Long id);

    /**
     * @Description :通过课程ids 查找对应的课程类型名称map
     * @Param [ids]
     * <AUTHOR>
     */
    Map<Long, String> getCourseTypeNamesByCourseIds(Set<Long> ids);
}
