package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.log.model.LogLogin;
import com.get.institutioncenter.entity.InstitutionCoursePathway;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionCoursePathwayMapper extends BaseMapper<InstitutionCoursePathway> {
    int insert(InstitutionCoursePathway record);

    int insertSelective(InstitutionCoursePathway record);

    /**
     * 获取桥梁课程
     *
     * @return
     */
    List<Long> getBridgeInstitutionCourseIds(@Param("id") Long id, @Param("updateCourseId") Long updateCourseId);

    /**
     * 获取非桥梁课程
     *
     * @Date 17:37 2021/7/22
     * <AUTHOR>
     */
    List<Long> getNonBridgeInstitutionCoursePathwayByInstitution(@Param("id") Long id, @Param("updateCourseId") Long updateCourseId);

    /**
     * 桥梁课程列表数据
     *
     * @Date 9:57 2021/7/27
     * <AUTHOR>
     */
    List<InstitutionCoursePathway> selectInstitutionCoursePathway(IPage<LogLogin> page, @Param("fkInstitutionCourseId") Long fkInstitutionCourseId,
                                                                  @Param("fkInstitutionCourseIdPathway") Long fkInstitutionCourseIdPathway);


}