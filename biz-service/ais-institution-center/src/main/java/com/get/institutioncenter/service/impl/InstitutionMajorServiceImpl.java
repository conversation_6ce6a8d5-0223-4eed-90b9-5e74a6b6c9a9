package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.institutioncenter.dao.InstitutionMajorMapper;
import com.get.institutioncenter.dao.InstitutionMapper;
import com.get.institutioncenter.dao.MajorLevelMapper;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.InstitutionMajorVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.InstitutionMajor;
import com.get.institutioncenter.entity.MediaAndAttached;
import com.get.institutioncenter.service.IInstitutionMajorService;
import com.get.institutioncenter.service.IMediaAndAttachedService;
import com.get.institutioncenter.utils.MyStringUtils;
import com.get.institutioncenter.dto.InstitutionMajorDto;
import com.get.salecenter.vo.SelItem;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @DATE: 2020/8/6
 * @TIME: 12:00
 * @Description:
 **/
@Service
public class InstitutionMajorServiceImpl extends BaseServiceImpl<InstitutionMajorMapper, InstitutionMajor> implements IInstitutionMajorService {
    @Resource
    private UtilService utilService;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private InstitutionMajorMapper institutionMajorMapper;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private MajorLevelMapper majorLevelMapper;

    @Override
    public List<InstitutionMajorVo> datas(InstitutionMajorDto institutionMajorDto, Page page) {
        LambdaQueryWrapper<InstitutionMajor> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(institutionMajorDto)) {
            if (GeneralTool.isNotEmpty(institutionMajorDto.getFkInstitutionId())) {
                wrapper.eq(InstitutionMajor::getFkInstitutionId, institutionMajorDto.getName());
            }
            if (GeneralTool.isNotEmpty(institutionMajorDto.getFkInstitutionFacultyId())) {
                wrapper.eq(InstitutionMajor::getFkInstitutionFacultyId, institutionMajorDto.getFkInstitutionFacultyId());
            }
            if (GeneralTool.isNotEmpty(institutionMajorDto.getName())) {
                wrapper.like(InstitutionMajor::getName, institutionMajorDto.getName());
            }
            wrapper.eq(InstitutionMajor::getIsActive, institutionMajorDto.getFkInstitutionFacultyId());
            wrapper.orderByDesc(InstitutionMajor::getViewOrder);
        }
        //获取分页数据
        IPage<InstitutionMajor> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionMajor> institutionMajors = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<InstitutionMajorVo> convertDatas = new ArrayList<>();
        for (InstitutionMajor institutionMajor : institutionMajors) {
            InstitutionMajorVo institutionMajorVo = BeanCopyUtils.objClone(institutionMajor, InstitutionMajorVo::new);
            if (GeneralTool.isNotEmpty(institutionMajorVo.getFkInstitutionId())) {
                institutionMajorVo.setInstitutionName(institutionMapper.getInstitutionNameById(institutionMajorVo.getFkInstitutionId()));
            }
            if (GeneralTool.isNotEmpty(institutionMajorVo.getFkInstitutionFacultyId())) {
                institutionMajorVo.setInstitutionFacultyName(institutionMapper.getInstitutionNameById(institutionMajorVo.getFkInstitutionFacultyId()));
            }
            if (GeneralTool.isNotEmpty(institutionMajorVo.getFkMajorLevelId())) {
                institutionMajorVo.setMajorLevelName(majorLevelMapper.getMajorLevelNameById(institutionMajorVo.getFkMajorLevelId()));
            }
            convertDatas.add(institutionMajorVo);
        }
        return convertDatas;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long addInstitutionMajor(InstitutionMajorDto institutionMajorDto) {
        InstitutionMajor institutionMajor = BeanCopyUtils.objClone(institutionMajorDto, InstitutionMajor::new);
        utilService.updateUserInfoToEntity(institutionMajor);
        int i = institutionMajorMapper.insertSelective(institutionMajor);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        institutionMajor.setNum(MyStringUtils.getMajorNum(institutionMajor.getId()));
        institutionMajorMapper.updateById(institutionMajor);
        return institutionMajor.getId();
    }

    @Override
    public List<BaseSelectEntity> getInstitutionMajorSelect(String keyword) {
        return institutionMajorMapper.getInstitutionMajorSelect(keyword);
    }

    @Override
    public Map<Long, String> getInstitutionMajorNamesByIds(Set<Long> ids) {
        if (ids.isEmpty()) {
            return Collections.emptyMap();
        }
        List<SelItem> group = institutionMajorMapper.getInstitutionMajorNamesByIds(ids);
        if (group.isEmpty()) {
            return Collections.emptyMap();
        }
        return group.stream().collect(HashMap<Long, String>::new, (m, v) -> m.put(v.getKeyId(), String.valueOf(v.getVal())), HashMap::putAll);
    }

    @Override
    public String getInstitutionMajorNameById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return institutionMajorMapper.getInstitutionMajorNameById(id);
    }

    @Override
    public InstitutionMajorVo findInstitutionMajorById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionMajor institutionMajor = institutionMajorMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionMajor)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionMajorVo institutionMajorVo = BeanCopyUtils.objClone(institutionMajor, InstitutionMajorVo::new);
        String tableName = "m_institution_major";
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(tableName);
        attachedVo.setFkTableId(id);
        List<MediaAndAttachedVo> mediaAndAttachedVo = attachedService.getMediaAndAttachedDto(attachedVo);
        institutionMajorVo.setMediaAndAttachedDtos(mediaAndAttachedVo);
        if (GeneralTool.isNotEmpty(institutionMajorVo.getFkInstitutionId())) {
            institutionMajorVo.setInstitutionName(institutionMapper.getInstitutionNameById(institutionMajorVo.getFkInstitutionId()));
        }
        if (GeneralTool.isNotEmpty(institutionMajorVo.getFkInstitutionFacultyId())) {
            institutionMajorVo.setInstitutionFacultyName(institutionMapper.getInstitutionNameById(institutionMajorVo.getFkInstitutionFacultyId()));
        }
        if (GeneralTool.isNotEmpty(institutionMajorVo.getFkMajorLevelId())) {
            institutionMajorVo.setMajorLevelName(majorLevelMapper.getMajorLevelNameById(institutionMajorVo.getFkMajorLevelId()));
        }
        return institutionMajorVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        //TODO 改过
        //InstitutionMajor institutionMajor = findInstitutionMajorById(id);
        InstitutionMajorVo institutionMajor = findInstitutionMajorById(id);
        if (institutionMajor == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = institutionMajorMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        //同时删除该表id下的所有媒体附件
        List<MediaAndAttached> mediaAndAttachedByTableIds = attachedService.findMediaAndAttachedByTableId(id, TableEnum.INSTITUTION_MAJOR.key, null);
        for (MediaAndAttached mediaAndAttached : mediaAndAttachedByTableIds) {
            attachedService.deleteMediaAttached(mediaAndAttached.getId());
        }
    }

    @Override
    public FileDto upload(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        //文件上传
        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.INSTITUTIONCENTER);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        if (GeneralTool.isEmpty(result.getData())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }

        return result.getData().get(0);

    }

    @Override
    public InstitutionMajorVo updateInstitutionMajor(InstitutionMajorDto institutionMajorDto) {
        if (institutionMajorDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(institutionMajorDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionMajor rs = institutionMajorMapper.selectById(institutionMajorDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionMajor institutionMajor = BeanCopyUtils.objClone(institutionMajorDto, InstitutionMajor::new);
        utilService.updateUserInfoToEntity(institutionMajor);
        institutionMajorMapper.updateById(institutionMajor);
        return findInstitutionMajorById(institutionMajor.getId());
    }

    @Override
    public MediaAndAttachedVo addInstitutionMajorMedia(MediaAndAttachedDto mediaAttachedVo) {
        if (GeneralTool.isEmpty(mediaAttachedVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        //设置插入的表
        String tableName = "m_institution_major";
        mediaAttachedVo.setFkTableName(tableName);
        return attachedService.addMediaAndAttached(mediaAttachedVo);
    }

    @Override
    public List<Map<String, Object>> findMediaAndAttachedType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.INSTITUTIONMAJOR);
    }


}
