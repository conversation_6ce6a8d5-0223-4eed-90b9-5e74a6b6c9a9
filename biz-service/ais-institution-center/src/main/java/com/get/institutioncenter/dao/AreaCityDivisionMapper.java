package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.AreaCityDivision;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/3/3 14:46
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface AreaCityDivisionMapper extends BaseMapper<AreaCityDivision> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AreaCityDivision record);

    /**
     * @return boolean
     * @Description :校验城市能否删除
     * @Param [areaCityId]
     * <AUTHOR>
     */
    boolean areaCityDivisionIsEmpty(@Param("areaCityId") Long areaCityId);

    /**
     * @return java.lang.Integer
     * @Description :获取排序最大值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     *
     * @param columnName
     * @return
     */
    List<Long> getAreaCityDiversionIds(String columnName);
}