package com.get.institutioncenter.service;

import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.dto.ContactPersonCompanyDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/6
 * @TIME: 17:54
 * @Description:
 **/
public interface IContactPersonCompanyService {

    /**
     * 安全配置
     *
     * @param contactPersonCompanyDtos
     * @
     */
    void editContactPersonCompany(List<ContactPersonCompanyDto> contactPersonCompanyDtos);


    /**
     * @return java.util.List<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 代理联系人和公司的关系（数据回显）
     * @Param [agentId]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getContactCompanyRelation(Long contactId);

    /**
     * @return java.lang.Long
     * @Description: 添加
     * @Param [relation]
     * <AUTHOR>
     */
    Long addRelation(ContactPersonCompanyDto relation);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据公司查询联系人ids
     * @Param [companyId]
     * <AUTHOR>
     */
    List<Long> getRelationByCompanyId(List<Long> companyId);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据联系人id查询公司ids
     * @Param [contactPersonId]
     * <AUTHOR>
     */
    List<Long> getRelationsByContactId(Long contactPersonId);


    /**
     * 根据联系人ids查询公司ids
     *
     * @param contactIds
     * @return
     */
    Map<Long, Set<Long>> getCompanyIdsByContactIds(Set<Long> contactIds);
}
