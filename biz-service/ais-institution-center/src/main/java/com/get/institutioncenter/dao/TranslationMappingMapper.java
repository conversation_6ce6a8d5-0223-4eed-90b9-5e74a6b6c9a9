package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionTranslationMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TranslationMappingMapper extends BaseMapper<InstitutionTranslationMapping> {
    int insert(InstitutionTranslationMapping record);

    int insertSelective(InstitutionTranslationMapping record);

    InstitutionTranslationMapping getTranslationMappingById(@Param("id") Long mappingId);

    Long getMappingIdByTableNameAndColumnName(@Param("tableName") String tableName, @Param("columnName") String columnName);

    List<Long> getTableId(@Param("DtoValue") Object o, @Param("tableName") String tableName, @Param("columnDto") String columnDto);
}