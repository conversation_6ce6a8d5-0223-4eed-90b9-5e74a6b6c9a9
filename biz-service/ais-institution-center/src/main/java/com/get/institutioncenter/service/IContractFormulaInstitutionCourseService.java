package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.entity.ContractFormulaInstitutionCourse;
import com.get.institutioncenter.dto.ContractFormulaInstitutionCourseDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/22 14:06
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaInstitutionCourseService extends BaseService<ContractFormulaInstitutionCourse> {

    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaInstitutionCourseDto]
     * <AUTHOR>
     */
    Long addContractFormulaInstitutionCourse(ContractFormulaInstitutionCourseDto contractFormulaInstitutionCourseDto);

    /**
     * @return void
     * @Description :
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应课程ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCourseIdListByFkid(Long contractFormulaId);

    /**
     * @return java.lang.String
     * @Description :通过合同公式id 查找对应课程名称
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    String getCourseNameByFkid(Long contractFormulaId);

    /**
     * 根据合同公式筛选课程
     *
     * @Date 14:39 2021/6/15
     * <AUTHOR>
     */
    List<Long> getCourseIdListByFaculty(List<Long> courseIdList, List<Long> zoneIdList, List<Long> facultyIdList, List<Long> majorLevelIdList, List<Long> courseTypeIdList);
}
