<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionProviderCompanyMapper">
    <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionProviderCompany">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_institution_provider_id" jdbcType="BIGINT" property="fkInstitutionProviderId"/>
        <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionProviderCompany" keyProperty="id"
            useGeneratedKeys="true">
    insert into r_institution_provider_company (id, fk_institution_provider_id, fk_company_id,
      gmt_create, gmt_create_user, gmt_modified,
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkInstitutionProviderId,jdbcType=BIGINT}, #{fkCompanyId,jdbcType=BIGINT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>

    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionProviderCompany"
            keyProperty="id" useGeneratedKeys="true">
        insert into r_institution_provider_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkInstitutionProviderId != null">
                fk_institution_provider_id,
            </if>
            <if test="fkCompanyId != null">
                fk_company_id,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionProviderId != null">
                #{fkInstitutionProviderId,jdbcType=BIGINT},
            </if>
            <if test="fkCompanyId != null">
                #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getCompanyIdByContractId" resultType="java.lang.Long">
        SELECT p.fk_company_id FROM  r_institution_provider_company p
        left join m_contract m on p.fk_institution_provider_id=m.fk_institution_provider_id
        where m.id=#{contractId}
    </select>

    <select id="deleteByProviderId">
        delete from r_institution_provider_company where
        <if test="id!=null and id !=''" >
            fk_institution_provider_id = #{id}
        </if>
    </select>
    <select id="getCompanyNameById" resultType="com.get.permissioncenter.vo.CompanyVo">
        SELECT
            m.id,
            m.short_name
        FROM
            m_institution_provider p
        INNER JOIN r_institution_provider_company r ON r.fk_institution_provider_id = p.id
        INNER JOIN ais_permission_center.m_company m ON m.id = r.fk_company_id
        WHERE
            p.id = #{id} AND m.id IN
            <foreach collection="companyIds" separator="," open="(" close=")" item="cid">
                #{cid}
            </foreach>
    </select>

    <select id="getRelationsByProviderIds" resultMap="BaseResultMap">
        SELECT ripc.* FROM ais_institution_center.`r_institution_provider_company` AS ripc
                               INNER JOIN ais_permission_center.m_company AS mc ON mc.id = ripc.fk_company_id
        <where>
            ripc.fk_institution_provider_id IN
            <foreach collection="providerIds" item="providerId" separator="," open="(" close=")">
                #{providerId}
            </foreach>
        </where>
        ORDER BY ripc.fk_institution_provider_id, mc.view_order desc
    </select>
</mapper>