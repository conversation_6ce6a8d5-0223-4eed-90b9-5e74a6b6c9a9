package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.CourseTypeGroupDto;
import com.get.institutioncenter.vo.CourseTypeGroupVo;
import com.get.institutioncenter.entity.CourseTypeGroup;
import com.get.institutioncenter.service.ICourseTypeGroupService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Description：课程类型组别管理类
 * @Param
 * @return
 * @Date 10:43 2021/4/27
 * <AUTHOR>
 */
@Api(tags = "课程类型组别管理")
@RestController
@RequestMapping("/institution/courseTypeGroup")
public class CourseTypeGroupController {
    @Resource
    private ICourseTypeGroupService courseTypeGroupService;

    /**
     * @return
     * @Description：列表数据
     * @Param
     * @Date 12:10 2021/4/27
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/课程类型组别管理/查询课程类型组别")
    @PostMapping("datas")
    public ResponseBo<CourseTypeGroupVo> datas(@RequestBody SearchBean<CourseTypeGroupDto> page) {
        List<CourseTypeGroupVo> datas = courseTypeGroupService.getCourseTypeGroups(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @author: Neil
     * @description: 根据课程组别名称查询课程组别下拉框
     * @date: 2022/5/24 16:32
     * @return
     */
    @ApiOperation(value = "根据课程组别名称查询课程组别下拉框")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/课程类型组别管理/根据课程组别名称查询课程组别下拉框")
    @GetMapping("getCourseTypeListByName")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<CourseTypeGroup> getCourseTypeListByName() {
        List<CourseTypeGroup> datas = courseTypeGroupService.getCourseTypeListByName();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return
     * @Description：课程类型组别详情
     * @Param
     * @Date 12:12 2021/4/27
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/课程类型组别管理/类型组别详情")
    @GetMapping("/{id}")
    public ResponseBo<CourseTypeGroupVo> detail(@PathVariable("id") Long id) {
        CourseTypeGroupVo courseTypeGroupVo = courseTypeGroupService.findCourseTypeGroupById(id);
        return new ResponseBo<>(courseTypeGroupVo);
    }


    /**
     * @return
     * @Description：删除类型组别
     * @Param
     * @Date 12:12 2021/4/27
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/课程类型组别管理/删除类型组别")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.courseTypeGroupService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * @Description：修改组别信息
     * @Param
     * @Date 12:31 2021/4/27
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/课程类型组别管理/更新类型组别")
    @PostMapping("update")
    public ResponseBo<CourseTypeGroupVo> update(@RequestBody  @Validated(CourseTypeGroupDto.Update.class)  CourseTypeGroupDto courseTypeGroupDto) {
        return UpdateResponseBo.ok(courseTypeGroupService.updateCourseType(courseTypeGroupDto));
    }


    /**
     * @Description：批量新增类型组别信息
     * @Param
     * @Date 12:51 2021/4/27
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/课程类型组别管理/批量保存类型组别")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(CourseTypeGroupDto.Add.class) ValidList<CourseTypeGroupDto> resources) {
        courseTypeGroupService.batchAdd(resources);
        return ResponseBo.ok();
    }

    /**
     * @Description：上移下移
     * @Param
     * @Date 14:20 2021/4/27
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/课程类型组别管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<CourseTypeGroupDto> resources) {
        courseTypeGroupService.movingOrder(resources);
        return ResponseBo.ok();
    }

    /**
     * @Description：课程类型组别下拉框数据
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "课程类型组别下拉框数据", notes = "")
    @GetMapping("getCourseTypeGroupList")
    public ResponseBo<BaseSelectEntity> getCourseTypeList() {
        List<BaseSelectEntity> datas = courseTypeGroupService.getCourseTypeGroupList();
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "课程类型组别下拉框数据", notes = "")
    @GetMapping("getBmsCourseGroupList")
    public ResponseBo<BaseSelectEntity> getBmsCourseGroupList() {
        return new ListResponseBo<>(courseTypeGroupService.getCourseGroupList(null));
    }

    /**
     * @Description：课程类型组别下拉框数据mode为3
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "课程类型组别新下拉框数据", notes = "")
    @GetMapping("getCourseTypeGroupListModeThree")
    public ResponseBo<BaseSelectEntity> getCourseTypeGroupListModeThree() {
        List<BaseSelectEntity> datas = courseTypeGroupService.getCourseTypeGroupListModeThree();
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 课程类型组别分类模式下拉框数据
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "课程类型组别分类模式下拉框数据", notes = "")
    @GetMapping("findModeType")
    public ResponseBo findFormulaType() {
        List<Map<String, Object>> datas = courseTypeGroupService.findModeType();
        return new ListResponseBo<>(datas);
    }


}
