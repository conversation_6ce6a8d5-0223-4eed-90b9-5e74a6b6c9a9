package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaInstitutionZone;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ContractFormulaInstitutionZoneMapper extends BaseMapper<ContractFormulaInstitutionZone> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContractFormulaInstitutionZone record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应校区ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getZoneIdListByFkid(Long contractFormulaId);
}