package com.get.institutioncenter.utils;

import cn.hutool.json.JSONObject;
import org.springframework.stereotype.Component;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

/**
 * Author: Smail
 * Date: 25/11/2024
 */
@Component
public class TranslatorsApiUtils {
//    private static final String BASE_URL = "http://192.168.1.96:3002/translate/";
    private static final String BASE_URL = "http://183.6.124.151:3002/translate/";

    public static final String MODEL = "qwen2.5-32b";
    public static final String En_lang = "English";
    public static final String Ch_lang = "Chinese";


    public static String getTrans(String trans,String en_lang,String ch_lang) throws IOException {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("source_lang", en_lang);
        jsonObject.put("target_lang", ch_lang);
        jsonObject.put("source_text", trans);
        jsonObject.put("country", "China");
        jsonObject.put("model", MODEL);
        jsonObject.put("chunk_model", "Qwen/Qwen2.5-32B-Instruct-GPTQ-Int4");
        jsonObject.put("max_tokens", 512);

        String jsonInputString = jsonObject.toString();

        URL url = new URL(BASE_URL);
        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setRequestMethod("POST");
        conn.setRequestProperty("Content-Type", "application/json; utf-8");
        conn.setRequestProperty("Accept", "application/json");
        conn.setDoOutput(true);

        try (OutputStream os = conn.getOutputStream()) {
            byte[] input = jsonInputString.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }

        int responseCode = conn.getResponseCode();
        if (responseCode == HttpURLConnection.HTTP_OK) { // 200
            try (BufferedReader br = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8))) {
                StringBuilder response = new StringBuilder();
                String responseLine;
                while ((responseLine = br.readLine()) != null) {
                    response.append(responseLine.trim());
                }
                //返回响应字符串
                String res = response.toString();

                return res.substring(1,res.length()-1); // 去掉双引号

            }
        } else {
            throw new IOException("Failed to post translation: HTTP error code : " + responseCode);
        }
    }


//    public static void main(String[] args) throws IOException {
//        String a="Essentials of Internal Auditing";
//        String trans = getTrans(a, En_lang, Ch_lang);
//        System.out.println(trans);
//
//    }
}
