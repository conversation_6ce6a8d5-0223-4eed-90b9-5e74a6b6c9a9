package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.ContractFormulaAreaCountry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ContractFormulaAreaCountryMapper extends BaseMapper<ContractFormulaAreaCountry> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContractFormulaAreaCountry record);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应国家id
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getCountryIdListByFkid(@Param("contractFormulaId") Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :通过合同公式id 查找对应国家名称
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<String> getCountryNameByFkid(@Param("contractFormulaId") Long contractFormulaId);
}