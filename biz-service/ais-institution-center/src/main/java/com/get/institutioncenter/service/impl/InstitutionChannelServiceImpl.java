package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionChannelCompanyMapper;
import com.get.institutioncenter.dao.InstitutionChannelMapper;
import com.get.institutioncenter.dto.InstitutionChannelDto;
import com.get.institutioncenter.vo.InstitutionChannelVo;
import com.get.institutioncenter.entity.InstitutionChannel;
import com.get.institutioncenter.entity.RInstitutionChannelCompany;
import com.get.institutioncenter.service.IInstitutionChannelCompanyService;
import com.get.institutioncenter.service.IInstitutionChannelService;
import com.get.institutioncenter.utils.MyStringUtils;
import com.get.institutioncenter.dto.InstitutionChannelCompanyDto;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2021/4/12
 * @TIME: 15:33
 * @Description:
 **/
@Service
public class InstitutionChannelServiceImpl extends ServiceImpl<InstitutionChannelMapper, InstitutionChannel> implements IInstitutionChannelService {
    @Resource
    private InstitutionChannelMapper institutionChannelMapper;
    @Resource
    private IInstitutionChannelCompanyService institutionChannelCompanyService;
    @Resource
    private InstitutionChannelCompanyMapper institutionChannelCompanyMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private UtilService utilService;

    @Override
    public InstitutionChannelVo findInstitutionChannelById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<Long> companyIds = SecureUtil.getCompanyIds();
        InstitutionChannelVo institutionChannel = institutionChannelMapper.queryById(id,companyIds);
        if (GeneralTool.isEmpty(institutionChannel)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        String fullName = institutionChannel.getName();
        if (GeneralTool.isNotEmpty(institutionChannel.getNameChn())) {
            fullName = fullName + "（" + institutionChannel.getNameChn() + "）";
        }
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(new HashSet<>(companyIds));
        Map<Long,String> comm = new HashMap<>();
        if (result.isSuccess()&& result.getData()!=null) {
            comm = result.getData();
        }
        String[] ids = institutionChannel.getFkCompanyIds().split(",");
        StringBuilder cName = new StringBuilder();
        for (String cId : ids) {
            cName.append(comm.get(Long.valueOf(cId))).append("，");
        }
        String s = cName.toString();
        if (s.endsWith("，")){
            s= s.substring(0,s.length()-1);
        }
        institutionChannel.setCompanyName(s);
        institutionChannel.setFullName(fullName);
        return institutionChannel;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ValidList<InstitutionChannelDto> institutionChannelDtos) {
        if (GeneralTool.isEmpty(institutionChannelDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<InstitutionChannel> upList = new ArrayList<>();
        List<RInstitutionChannelCompany> icc = new ArrayList<>();
        for (InstitutionChannelDto institutionChannelDto : institutionChannelDtos) {
            if (GeneralTool.isEmpty(institutionChannelDto.getId())) {
                if (validateAdd(institutionChannelDto)) {
                    InstitutionChannel institutionChannel = BeanCopyUtils.objClone(institutionChannelDto, InstitutionChannel::new);
                    utilService.updateUserInfoToEntity(institutionChannel);
                    institutionChannelMapper.insert(institutionChannel);
                    //自动生成编号
                    institutionChannel.setNum(MyStringUtils.getChannelNum(institutionChannel.getId()));
                    institutionChannelMapper.updateById(institutionChannel);
                    RInstitutionChannelCompany channelCompany = new RInstitutionChannelCompany();
                    channelCompany.setFkCompanyId(institutionChannelDto.getFkCompanyId());
                    channelCompany.setFkInstitutionChannelId(institutionChannel.getId());
                    utilService.setCreateInfo(channelCompany);
                    icc.add(channelCompany);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("code_or_name_exists"));
                }
            } else {
                if (validateUpdate(institutionChannelDto)) {
                    InstitutionChannel institutionChannel = BeanCopyUtils.objClone(institutionChannelDto, InstitutionChannel::new);
                    utilService.updateUserInfoToEntity(institutionChannel);
                    upList.add(institutionChannel);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("code_or_name_exists"));
                }
            }
        }
        if (GeneralTool.isNotEmpty(icc)) {
            institutionChannelCompanyService.saveBatch(icc);
        }
        if (GeneralTool.isNotEmpty(upList)) {
            updateBatchById(upList);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findInstitutionChannelById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        institutionChannelMapper.deleteById(id);
        institutionChannelCompanyMapper.delete(Wrappers.<RInstitutionChannelCompany>lambdaQuery().eq(RInstitutionChannelCompany::getFkInstitutionChannelId,id));
    }

    @Override
    public InstitutionChannelVo updateInstitutionChannel(InstitutionChannelDto institutionChannelDto) {
        if (institutionChannelDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        InstitutionChannel i = institutionChannelMapper.selectById(institutionChannelDto.getId());
        if (i == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (validateUpdate(institutionChannelDto)) {
            InstitutionChannel institutionChannel = BeanCopyUtils.objClone(institutionChannelDto, InstitutionChannel::new);
            utilService.updateUserInfoToEntity(institutionChannel);
            institutionChannelMapper.updateById(institutionChannel);
            return findInstitutionChannelById(institutionChannelDto.getId());
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("code_or_name_exists"));
        }
    }

    @Override
    public ResponseBo editInstitutionChannelCompanyRelation(ValidList<InstitutionChannelCompanyDto> channelCompanyVos) {
        if (GeneralTool.isEmpty(channelCompanyVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<Long> companyIds = SecureUtil.getCompanyIds();
        institutionChannelCompanyMapper.delete(Wrappers.<RInstitutionChannelCompany>lambdaQuery().eq(RInstitutionChannelCompany::getFkInstitutionChannelId,channelCompanyVos.get(0).getFkInstitutionChannelId()).in(RInstitutionChannelCompany::getFkCompanyId,companyIds));
        List<RInstitutionChannelCompany> collect = channelCompanyVos.stream().map(c -> {
            RInstitutionChannelCompany RInstitutionChannelCompany = new RInstitutionChannelCompany();
            BeanUtils.copyProperties(c, RInstitutionChannelCompany);
            utilService.setCreateInfo(RInstitutionChannelCompany);
            return RInstitutionChannelCompany;
        }).collect(Collectors.toList());
        if (GeneralTool.isNotEmpty(collect)) {
            institutionChannelCompanyService.saveBatch(collect);
        }
        return SaveResponseBo.ok();
    }

    @Override
    public List<InstitutionChannelVo> getInstitutionChannels(InstitutionChannelDto institutionChannelDto, Page page) {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        IPage<InstitutionChannel> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())));
        List<InstitutionChannelVo> channelInformation = institutionChannelMapper.getInstitutionChannelInformation(pages, institutionChannelDto,companyIds);
//        Set<Long> collect = channelInformation.stream().map(InstitutionChannelVo::getFkCompanyId).collect(Collectors.toSet());
        page.setAll((int) pages.getTotal());
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(new HashSet<>(companyIds));
        Map<Long,String> comm = new HashMap<>();
        if (result.isSuccess()&& result.getData()!=null) {
            comm = result.getData();
        }
        for (InstitutionChannelVo channelDto : channelInformation) {
            String fullName = channelDto.getName();
            if (GeneralTool.isNotEmpty(channelDto.getNameChn())) {
                fullName = fullName + "（" + channelDto.getNameChn() + "）";
            }
            channelDto.setFullName(fullName);
            String[] ids = channelDto.getFkCompanyIds().split(",");
            StringBuilder cName = new StringBuilder();
            for (String id : ids) {
                cName.append(comm.get(Long.valueOf(id))).append("，");
            }
            String s = cName.toString();
            if (s.endsWith("，")){
                s= s.substring(0,s.length()-1);
            }
            channelDto.setCompanyName(s);
        }
//        Map<Long, List<InstitutionChannelVo>> map = channelInformation.stream().collect(Collectors.groupingBy(InstitutionChannelVo::getId));
//        for (Map.Entry<Long, List<InstitutionChannelVo>> entry : map.entrySet()) {
//            if (GeneralTool.isNotEmpty(entry.getValue())) {
//                InstitutionChannelVo channelDto = entry.getValue().get(0);
//                String fullName = channelDto.getName();
//                if (GeneralTool.isNotEmpty(channelDto.getNameChn())) {
//                    fullName = fullName + "（" + channelDto.getNameChn() + "）";
//                }
//                channelDto.setFullName(fullName);
//                Set<Long> ids = entry.getValue().stream().map(InstitutionChannelVo::getFkCompanyId).collect(Collectors.toSet());
//                StringBuilder cName = new StringBuilder();
//                for (Long id : ids) {
//                    cName.append(comm.get(id)).append("，");
//                }
//                String s = cName.toString();
//                if (s.endsWith("，")){
//                    s= s.substring(0,s.length()-1);
//                }
//                channelDto.setCompanyName(s);
//                channelDto.setFkCompanyId(null);
//                channelDto.setFkCompanyIds(ids);
//                resultData.add(channelDto);
//            }
//        }
//        for (InstitutionChannelVo institutionChannel : channelInformation) {
//            String fullName = institutionChannel.getName();
//            if (GeneralTool.isNotEmpty(institutionChannel.getNameChn())) {
//                fullName = fullName + "（" + institutionChannel.getNameChn() + "）";
//            }
//            institutionChannel.setFullName(fullName);
//            institutionChannel.setCompanyName(comm.get(institutionChannel.getFkCompanyId()));
//        }
        return channelInformation;
    }

    @Override
    public List<BaseSelectEntity> getInstitutionChannelSelect() {
        Long staffId = SecureUtil.getStaffId();
        List<Long> companyIds = SecureUtil.getCompanyIdsByStaffId(staffId);
        return institutionChannelMapper.getInstitutionChannelSelect(companyIds);
    }

    @Override
    public Map<Long, String> getChannelByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<InstitutionChannel> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(InstitutionChannel::getId, ids);
        List<InstitutionChannel> institutionChannels = institutionChannelMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(institutionChannels)) {
            return map;
        }
        for (InstitutionChannel institutionChannel : institutionChannels) {
            if (GeneralTool.isNotEmpty(institutionChannel.getNameChn())) {
                map.put(institutionChannel.getId(), "【" + institutionChannel.getNameChn()+ "】");
            } else {
                map.put(institutionChannel.getId(), "【" + institutionChannel.getName() + "】");
            }
        }
        return map;
    }

    @Override
    public List<Long> getInstitutionProviderChannelIdsByName(String channelName) {
        List<Long> institutionProviderChannelIds = institutionChannelMapper.getInstitutionProviderChannelIdsByName("%"+channelName+"%");
        if (GeneralTool.isEmpty(institutionProviderChannelIds)) {
            institutionProviderChannelIds.add(0L);
        }
        return institutionProviderChannelIds;
    }

    @Override
    public List<BaseSelectEntity> fuzzSearchInstitutionChannel(String keyword, List<Long> companyIds) {
        if (StringUtils.isNotBlank(keyword)) {
            keyword= keyword.replace(" ","").trim();
        }else {
            return null;
        }
        return institutionChannelMapper.fuzzQueryChannelName(keyword,companyIds);
    }

    private boolean validateAdd(InstitutionChannelDto institutionChannelDto) {
        LambdaQueryWrapper<InstitutionChannel> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(InstitutionChannel::getName, institutionChannelDto.getName());
        List<InstitutionChannel> institutionChannels = institutionChannelMapper.selectList(wrapper);
        return GeneralTool.isEmpty(institutionChannels);
    }

    private boolean validateUpdate(InstitutionChannelDto institutionChannelDto) {
        LambdaQueryWrapper<InstitutionChannel> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionChannel::getName, institutionChannelDto.getName());
        List<InstitutionChannel> list = institutionChannelMapper.selectList(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(institutionChannelDto.getId());
    }
}
