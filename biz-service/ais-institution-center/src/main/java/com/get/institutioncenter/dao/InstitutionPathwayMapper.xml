<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionPathwayMapper">
    <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionPathway">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId"/>
        <result column="fk_institution_id_pathway" jdbcType="BIGINT" property="fkInstitutionIdPathway"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionPathway">
        insert into r_institution_pathway (id, fk_institution_id, fk_institution_id_pathway,
                                           gmt_create, gmt_create_user, gmt_modified,
                                           gmt_modified_user)
        values (#{id,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, #{fkInstitutionIdPathway,jdbcType=BIGINT},
                #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
                #{gmtModifiedUser,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionPathway">
        insert into r_institution_pathway
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkInstitutionId != null">
                fk_institution_id,
            </if>
            <if test="fkInstitutionIdPathway != null">
                fk_institution_id_pathway,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionId != null">
                #{fkInstitutionId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionIdPathway != null">
                #{fkInstitutionIdPathway,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="getBridgeInstitutionSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select i.id,i.name,i.name_chn as nameChn,concat(if(i.is_active=0,'【无效】',''),
        CASE WHEN IFNULL(i.name_chn,'')='' THEN `i.name` ELSE
        CONCAT(`i.name`,'（',i.name_chn,'）') END fullName,i.is_active status from m_institution i left join r_institution_pathway ip on i.id =
        ip.fk_institution_id_pathway
        <where>
            <if test="id!=null">
                ip.fk_institution_id =#{id}
            </if>
        </where>
    </select>
    <select id="getBridgeInstitutionIds" resultType="java.lang.Long">
        SELECT fk_institution_id_pathway FROM r_institution_pathway
        <where>
            <if test="id!=null">
                and fk_institution_id =#{id}
            </if>
        </where>
    </select>
    <select id="getNonBridgeInstitutionSelect" resultType="java.lang.Long">
        SELECT fk_institution_id FROM r_institution_pathway
        <where>
            <if test="id!=null">
                and fk_institution_id_pathway =#{id}
            </if>
        </where>
    </select>
    <select id="institutionIdPathwayIsEmpty" resultType="java.lang.Boolean">
        select IFNULL(max(id), 0) id
        from r_institution_pathway
        where fk_institution_id_pathway = #{id} LIMIT 1
    </select>
    <select id="selectInstitutionPathway" resultType="com.get.institutioncenter.entity.InstitutionPathway">
        SELECT
        ip.*
        FROM
        r_institution_pathway AS ip
        <if test="fkInstitutionIdPathway != null">
            INNER JOIN m_institution AS i ON i.id = ip.fk_institution_id
        </if>
        <if test="fkInstitutionId != null">
            INNER JOIN m_institution AS i ON i.id = ip.fk_institution_id_pathway
        </if>
        <where>
            <if test="fkInstitutionIdPathway != null">
                AND fk_institution_id_pathway = #{fkInstitutionIdPathway}
            </if>
            <if test="fkInstitutionId != null">
                AND fk_institution_id = #{fkInstitutionId}
            </if>
        </where>
        ORDER BY
        CONVERT ( i.name USING gbk ) ASC
    </select>
</mapper>