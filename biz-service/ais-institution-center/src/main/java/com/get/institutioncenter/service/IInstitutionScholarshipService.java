package com.get.institutioncenter.service;

import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.InstitutionScholarshipVo;
import com.get.institutioncenter.vo.InstitutionScholarshipVo2;
import com.get.institutioncenter.entity.InstitutionScholarship;
import com.get.institutioncenter.dto.InstitutionScholarshipDto;
import com.get.institutioncenter.dto.InstitutionScholarshipDto2;
import com.get.institutioncenter.dto.ScholarshipTargetDto;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import com.get.institutioncenter.dto.query.InstitutionScholarshipQueryDto;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 12:07
 */
public interface IInstitutionScholarshipService extends BaseService<InstitutionScholarship> {
    Long addInstitutionScholarship(InstitutionScholarshipDto institutionScholarshipDto);

    InstitutionScholarshipVo findInstitutionScholarshipById(Long id);

    void delete(Long id);

    InstitutionScholarshipVo updateInstitutionScholarship(InstitutionScholarshipDto institutionScholarshipDto);

    List<InstitutionScholarshipVo> datas(InstitutionScholarshipQueryDto data, SearchBean<InstitutionScholarshipQueryDto> page);

    List<InstitutionScholarshipVo2> getWcInstitutionScholarshipDatas(Long fkInstitutionId, Integer fkMajorLevelId);

    List<InstitutionScholarshipVo2> getWcInstitutionScholarshipList(InstitutionScholarshipDto2 data, SearchBean<InstitutionScholarshipDto2> page);

    InstitutionScholarshipVo getIsOtherModule(Long fkInstitutionId);

    /**
     * 优先匹配查询
     * @param weScholarshipAppDto
     * @return
     */
    List<InstitutionScholarshipVo> priorityMatchingQuery(WeScholarshipAppDto weScholarshipAppDto);


    /**
     * 获取目标对象下拉
     * @param scholarshipTargetDto
     * @return
     */
    List<BaseSelectEntity> getSelectedByTarget(ScholarshipTargetDto scholarshipTargetDto);
}
