package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionRankingMapper;
import com.get.institutioncenter.vo.InstitutionRankingVo;
import com.get.institutioncenter.vo.InstitutionScholarshipVo;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.entity.InstitutionRanking;
import com.get.institutioncenter.service.IAreaStateService;
import com.get.institutioncenter.service.IInstitutionRankingService;
import com.get.institutioncenter.service.IInstitutionService;
import com.get.institutioncenter.dto.InstitutionRankingDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/25 11:21
 */
@Service
public class InstitutionRankingServiceImpl extends BaseServiceImpl<InstitutionRankingMapper, InstitutionRanking> implements IInstitutionRankingService {

    @Resource
    private InstitutionRankingMapper rankingMapper;
    @Resource
    private IInstitutionService iInstitutionService;
    @Resource
    private IAreaStateService iAreaStateService;


    @Override
    public List<InstitutionRankingVo> getWcComprehensiveRanking(InstitutionRankingDto data, SearchBean<InstitutionRankingDto> page) {

        IPage<InstitutionScholarshipVo> ipage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<InstitutionRankingVo> institutionRankings = rankingMapper.getWcComprehensiveRanking(ipage, data);
        page.setAll((int) ipage.getTotal());
        page.setCurrentResult((int) ipage.getSize());
        if (GeneralTool.isEmpty(institutionRankings)) {
            return institutionRankings;
        }
        Set<Long> collect = institutionRankings.stream().map(InstitutionRankingVo::getFkInstitutionId).collect(Collectors.toSet());
        Map<Long, Institution> institutionByIds = iInstitutionService.getInstitutionByIds(collect);

        List<Institution> Objs = institutionByIds.entrySet().stream().map(d -> d.getValue()).collect(Collectors.toList());
        Set<Long> areaIds = Objs.stream().map(Institution::getFkAreaStateId).collect(Collectors.toSet());
        Map<Long, String> stateNamesByIds = iAreaStateService.getSimplifyStateNamesByIds(areaIds);
        institutionRankings.stream().forEach(d -> {
            Optional.ofNullable(institutionByIds.get(d.getFkInstitutionId())).ifPresent(dd -> Optional.ofNullable(dd.getFkAreaStateId()).ifPresent(ddd -> Optional.ofNullable(stateNamesByIds.get(ddd))
                    .ifPresent(dddd -> d.setStateName(dddd))));
        });


        return institutionRankings;
    }

    @Override
    public List<InstitutionRankingVo> getWcComprehensiveRankingHome() {
        List<InstitutionRankingVo> institutionRankingDtoList2 = rankingMapper.getWcComprehensiveRankingHome();
        List<InstitutionRankingVo> institutionRankingDtoList = institutionRankingDtoList2.stream().filter(d -> GeneralTool.isNotBlank(d.getTitle())).collect(Collectors.toList());
        return institutionRankingDtoList;
    }


    @Override
    public InstitutionRankingVo getWcCourseTypeKey(String typeKey) {
        InstitutionRankingVo institutionRankingDtoList = rankingMapper.getWcCourseTypeKey(typeKey);

        return institutionRankingDtoList;
    }

    @Override
    public List<InstitutionRankingVo> getWcMajorRanking(String typeKey) {
        List<InstitutionRankingVo> wcMajorRanking = rankingMapper.getWcMajorRanking(typeKey);

        List<InstitutionRankingVo> returnData = new ArrayList<>();
        List<String> groupList = wcMajorRanking.stream().map(d -> d.getGroupNameChn()).distinct().collect(Collectors.toList());
        for (String s : groupList) {
            InstitutionRankingVo institutionRankingVo = new InstitutionRankingVo();
            institutionRankingVo.setGroupNameChn(s);
            for (InstitutionRankingVo rankingDto : wcMajorRanking) {
                if (!s.equals(rankingDto.getGroupNameChn())) {
                    continue;
                }
                institutionRankingVo.getInstitutionRankingDtos().add(rankingDto);
            }
            returnData.add(institutionRankingVo);
        }
        return returnData;
    }


}
