package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.core.secure.UserInfo;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.dto.NewsDto;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.vo.NewsEmailTagDto;
import com.get.institutioncenter.vo.NewsVo;
import com.get.institutioncenter.entity.News;
import com.get.institutioncenter.dto.NewEmailToAgentDto;
import com.get.institutioncenter.dto.NewsCompanyDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.remindercenter.dto.AliyunSendMailDto;
import com.get.remindercenter.vo.AliyunSendMailVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/8/18
 * @TIME: 17:41
 * @Description:
 **/
public interface INewsService extends BaseService<News> {
    /**
     * 列表数据
     *
     * @param newsVo
     * @param page
     * @return
     */
    List<NewsVo> datas(NewsQueryDto newsVo, Page page);

    /**
     * 保存
     *
     * @param newsDto
     * @return
     */
    Long addNews(NewsDto newsDto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    NewsVo findNewsById(Long id);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param newsDto
     * @return
     */
    NewsVo updateNews(NewsDto newsDto);

    /**
     * 获取目标类型
     *
     * @return
     */
    List<Map<String, Object>> findTargetType();

    /**
     * 通过表和id删除新闻
     *
     * @return
     */
    void deleteNewsByTableId(Long id, String tableName);

    /**
     * 目标详情里面的新闻列表
     *
     * @param newsDto
     * @return
     */
    List<NewsVo> getNewsDtoByTarget(NewsDto newsDto);

    /**
     * 目标类型获取目标
     *
     * @return
     */
    List<BaseSelectEntity> findTarget(String tableName);

    /**
     * @return java.util.List<com.get.institutioncenter.vo.CompanyTreeVo>
     * @Description: 新闻公司回显
     * @Param [id]
     * <AUTHOR>
     **/
    //List<CompanyTreeVo> getNewRelation(Long id);
    List<CompanyTreeVo> getNewRelation(Long id);
    /**
     * @return void
     * @Description: 安全配置
     * @Param [validList]
     * <AUTHOR>
     **/
    void editNewsCompany(List<NewsCompanyDto> validList);

    /**
     * 保存附件
     *
     * @param mediaAttachedVo
     * @return
     */
    List<MediaAndAttachedVo> addNewsMedia(List<MediaAndAttachedDto> mediaAttachedVo);


    /**
     * 查询附件
     *
     * @param data
     * @param page
     * @return
     * @
     */
    List<MediaAndAttachedVo> getNewsMedia(MediaAndAttachedDto data, Page page);

    /**
     * 所有新闻下拉框
     *
     * @Date 11:54 2021/7/28
     * <AUTHOR>
     */
    List<BaseSelectEntity> getAllNewsSelect();

    /**
     * feign调用 查询新闻标题Map
     *
     * @Date 12:40 2021/7/28
     * <AUTHOR>
     */
    Map<Long, String> getNewsTitlesByIds(Set<Long> ids);

    /**
     * 发送新闻电邮通知
     * @param id
     */
    void sendNewsMail(Long id) throws Exception;

    Boolean checkNews(NewsDto newsDto);

    /**
     * 发送新闻电邮通知to代理
     *
     * @param newEmailToAgentDto
     * @param headerMap
     * @param user
     * @param locale
     */
    void sendNewsMailtoAgent(NewEmailToAgentDto newEmailToAgentDto, Map<String, String> headerMap, UserInfo user, String locale);

    /**
     * 发送新闻电邮to所有代理
     *
     * @param id
     * @param headerMap
     * @param user
     * @param locale
     * @param type
     */
    void sendNewsMailtoAllAgent(Long id, Map<String, String> headerMap, UserInfo user, String locale, Integer type);

    /**
     * 发送新闻电邮to自己
     * @param id
     */
    void sendNewsMailtoMe(Long id);

    /**
     * 为某一个新闻绑定Tag标签（阿里云服务统计数据用）
     * @param newsEmailTagDto
     * @return
     */
    Long addNewsEmailTage(NewsEmailTagDto newsEmailTagDto);

    /**
     *
     * @param id 新闻id
     * @param type 发送类型 0不区分/1hubs/2市场
     * @return  获取新闻模板
     */
    AliyunSendMailDto getNewEmailTmpelte(Long id, Integer type);
}
