package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.MediaAndAttached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MediaAndAttachedMapper extends BaseMapper<MediaAndAttached> {
//    @Override
//    int insert(MediaAndAttached record);

    int insertSelective(MediaAndAttached record);

    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);

    List<String> getFileGuids(@Param("tableName") String tableName, @Param("id") Long id);

    MediaAndAttached getByGuid(@Param("guid") String guid);
}