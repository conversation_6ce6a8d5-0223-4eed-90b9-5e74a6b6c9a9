<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionProviderInstitutionMapper">

  <select id="getCountByInstitutionId" parameterType="java.lang.Long" resultType="java.lang.Integer">
    select count(*) from r_institution_provider_institution  where
      fk_institution_id = #{id}
  </select>


  <select id="providerInfoIsEmpty"  resultType="java.lang.Boolean">
    select IFNULL(max(id),0) id from r_institution_provider_institution where fk_institution_provider_id = #{providerId} LIMIT 1
  </select>
  <select id="getInstitutionCountByInstitutionProviderIds"
          resultType="com.get.institutioncenter.vo.InstitutionProviderVo">
    SELECT
      fk_institution_provider_id as id,
      count(*) as institutionCount
    FROM
      r_institution_provider_institution
    WHERE
      ( 1=2
      <foreach collection="companyIds" item="companyId" index="index">
          or FIND_IN_SET(#{companyId}, fk_company_ids)
      </foreach>
      )
    <if test="institutionProviderIdSet != null and institutionProviderIdSet.size()>0">
     AND
        fk_institution_provider_id
     IN
      <foreach collection="institutionProviderIdSet" item="item" index="index" open="(" separator="," close=")">
      #{item}
     </foreach>
    </if>
    GROUP BY
      fk_institution_provider_id
  </select>
</mapper>