package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.ContractFormulaMajorLevelDto;
import com.get.institutioncenter.entity.ContractFormulaMajorLevel;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/8 10:28
 * @verison: 1.0
 * @description:
 */
public interface IContractFormulaMajorLevelService extends BaseService<ContractFormulaMajorLevel> {

    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaMajorLevelDto]
     * <AUTHOR>
     */
    Long addContractFormulaMajorLevel(ContractFormulaMajorLevelDto contractFormulaMajorLevelDto);

    /**
     * @return void
     * @Description :根据contractFormulaId删除
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过合同公式id 查找对应课程等级ids
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    List<Long> getMajorLevelIdListByFkid(Long contractFormulaId);

    /**
     * @return java.lang.String
     * @Description :通过合同公式id 查找对应课程等级名称
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    String getMajorLevelNameByFkid(Long contractFormulaId);
}
