package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.InstitutionChannelDto;
import com.get.institutioncenter.vo.InstitutionChannelVo;
import com.get.institutioncenter.service.IInstitutionChannelCompanyService;
import com.get.institutioncenter.service.IInstitutionChannelService;
import com.get.institutioncenter.dto.InstitutionChannelCompanyDto;
import com.get.salecenter.vo.CompanyTreeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/10/20
 * @TIME: 12:07
 * @Description:学校集团控制器
 **/
@Api(tags = "学校中心-学校集团渠道")
@RestController
@RequestMapping("/institution/institutionChannel")
public class InstitutionChannelController {
    @Resource
    private IInstitutionChannelService institutionChannelService;
    @Resource
    private IInstitutionChannelCompanyService institutionChannelCompanyService;


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionGroupVo>
     * @Description：详情
     * @Param [id]
     * <AUTHOR>
     **/
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/集团渠道管理/渠道详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionChannelVo> detail(@PathVariable("id") Long id) {
        InstitutionChannelVo data = institutionChannelService.findInstitutionChannelById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description：批量新增信息
     * @Param [institutionGroupVos]
     * <AUTHOR>
     **/
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/集团渠道管理/新增集团渠道")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(InstitutionChannelDto.Add.class) ValidList<InstitutionChannelDto> institutionChannelDtos) {
        institutionChannelService.batchAdd(institutionChannelDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description：删除信息
     * @Param [id]
     * <AUTHOR>
     **/
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/集团渠道管理/删除集团渠道")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionChannelService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionGroupVo>
     * @Description：修改信息
     * @Param [institutionGroupVo]
     * <AUTHOR>
     **/
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/集团渠道管理/更新集团渠道")
    @PostMapping("update")
    public ResponseBo<InstitutionChannelVo> update(@RequestBody @Validated(InstitutionChannelDto.Update.class) InstitutionChannelDto institutionChannelDto) {
        return UpdateResponseBo.ok(institutionChannelService.updateInstitutionChannel(institutionChannelDto));
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionGroupVo>
     * @Description：列表数据
     * @Param [page]
     * <AUTHOR>
     **/
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/集团渠道管理/查询集团渠道")
    @PostMapping("datas")
    public ResponseBo<InstitutionChannelVo> datas(@RequestBody SearchBean<InstitutionChannelDto> page) {
        List<InstitutionChannelVo> datas = institutionChannelService.getInstitutionChannels(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "学校渠道-公司安全配置")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/集团渠道管理/安全配置")
    @PostMapping("editInstitutionChannelCompanyRelation")
    public ResponseBo editInstitutionChannelCompanyRelation(@RequestBody @Validated(BaseVoEntity.Add.class) ValidList<InstitutionChannelCompanyDto> validList) {
        institutionChannelService.editInstitutionChannelCompanyRelation(validList);
        return UpdateResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 回显代理和公司的关系
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "回显渠道和公司的关系", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/集团渠道管理/渠道和公司的关系（数据回显）")
    @GetMapping("getChannelCompanyRelation/{channelId}")
    public ResponseBo<CompanyTreeVo> getChannelCompanyRelation(@PathVariable("channelId") Long channelId) {
        List<CompanyTreeVo> channelCompanyRelation = institutionChannelCompanyService.getChannelCompanyRelation(channelId);
        return new ListResponseBo<>(channelCompanyRelation);
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionGroupVo>
     * @Description：下拉框数据
     * @Param []
     * <AUTHOR>
     **/
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "下拉框数据", notes = "")
    @GetMapping("getInstitutionChannelSelect")
    public ResponseBo<BaseSelectEntity> getInstitutionChannelSelect() {
        List<BaseSelectEntity> datas = institutionChannelService.getInstitutionChannelSelect();
        return new ListResponseBo<>(datas);
    }

    /**
     * feign调用 通过渠道ids 查找对应的国家名称
     *
     * @param ids
     * @return
     */
/*    @ApiIgnore
    @GetMapping(value = "getChannelByIds")
    public Map<Long,String> getChannelByIds(@RequestParam(required = false) Set<Long> ids) {
        return institutionChannelService.getChannelByIds(ids);
    }*/
}
