package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionCourseEngScoreMapper;
import com.get.institutioncenter.vo.InstitutionCourseEngScoreVo;
import com.get.institutioncenter.entity.InstitutionCourseEngScore;
import com.get.institutioncenter.service.IInstitutionCourseEngScoreService;
import com.get.institutioncenter.dto.InstitutionCourseEngScoreDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.get.common.eunms.ProjectExtraEnum.ENGLISH_TEST_TYPE;

/**
 * <AUTHOR>
 * @DATE: 2020/12/7
 * @TIME: 16:39
 * @Description:
 **/
@Service
public class InstitutionCourseEngScoreServiceImpl extends BaseServiceImpl<InstitutionCourseEngScoreMapper, InstitutionCourseEngScore> implements IInstitutionCourseEngScoreService {
    @Resource
    private InstitutionCourseEngScoreMapper institutionCourseEngScoreMapper;
    @Resource
    private UtilService utilService;

    @Override
    public InstitutionCourseEngScoreVo findInstitutionCourseEngScoreById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionCourseEngScore institutionCourseEngScore = institutionCourseEngScoreMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionCourseEngScore)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionCourseEngScoreVo institutionCourseEngScoreVo = BeanCopyUtils.objClone(institutionCourseEngScore, InstitutionCourseEngScoreVo::new);
        return institutionCourseEngScoreVo;
    }

    @Override
    public List<InstitutionCourseEngScoreVo> getInstitutionCourseEngScores(InstitutionCourseEngScoreDto institutionCourseEngScoreDto, Page page) {

        LambdaQueryWrapper<InstitutionCourseEngScore> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(institutionCourseEngScoreDto)) {
            if (GeneralTool.isNotEmpty(institutionCourseEngScoreDto.getFkInstitutionCourseId())) {
                wrapper.eq(InstitutionCourseEngScore::getFkInstitutionCourseId, institutionCourseEngScoreDto.getFkInstitutionCourseId());
            }
        }
        wrapper.orderByDesc(InstitutionCourseEngScore::getGmtCreate);
        //获取分页数据
        IPage<InstitutionCourseEngScore> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionCourseEngScore> ct = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<InstitutionCourseEngScoreVo> convertDatas = new ArrayList<>();
        for (InstitutionCourseEngScore c : ct) {
            InstitutionCourseEngScoreVo institutionCourseEngScoreVo = BeanCopyUtils.objClone(c, InstitutionCourseEngScoreVo::new);
            convertDatas.add(institutionCourseEngScoreVo);
        }
        return convertDatas;
    }

    @Override
    public InstitutionCourseEngScoreVo updateInstitutionCourseEngScore(InstitutionCourseEngScoreDto institutionCourseEngScoreDto) {
        if (institutionCourseEngScoreDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(institutionCourseEngScoreDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionCourseEngScore ct = institutionCourseEngScoreMapper.selectById(institutionCourseEngScoreDto.getId());
        if (ct == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionCourseEngScore institutionCourseEngScore = BeanCopyUtils.objClone(institutionCourseEngScoreDto, InstitutionCourseEngScore::new);
        utilService.updateUserInfoToEntity(institutionCourseEngScore);
        institutionCourseEngScoreMapper.updateById(institutionCourseEngScore);
        return findInstitutionCourseEngScoreById(institutionCourseEngScore.getId());
    }

    @Override
    public void delete(Long id) {
        //TODO 改过
        //InstitutionCourseEngScore institutionCourseEngScore = findInstitutionCourseEngScoreById(id);
        InstitutionCourseEngScoreVo institutionCourseEngScore = findInstitutionCourseEngScoreById(id);
        if (institutionCourseEngScore == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = institutionCourseEngScoreMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<InstitutionCourseEngScoreDto> institutionCourseEngScoreDtos) {
        for (InstitutionCourseEngScoreDto institutionCourseEngScoreDto : institutionCourseEngScoreDtos) {
            InstitutionCourseEngScore institutionCourseEngScore = BeanCopyUtils.objClone(institutionCourseEngScoreDto, InstitutionCourseEngScore::new);
            utilService.updateUserInfoToEntity(institutionCourseEngScore);
            institutionCourseEngScoreMapper.insert(institutionCourseEngScore);
        }
    }

    @Override
    public List<Map<String, Object>> getEnglishTestType() {
        return ProjectExtraEnum.enums2Arrays(ENGLISH_TEST_TYPE);
    }

    @Override
    public void deleteInstitutionCourseEngScoreByCourseId(Long id) {
        LambdaQueryWrapper<InstitutionCourseEngScore> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionCourseEngScore::getFkInstitutionCourseId, id);
        int j = institutionCourseEngScoreMapper.delete(wrapper);
        if (j < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

}
