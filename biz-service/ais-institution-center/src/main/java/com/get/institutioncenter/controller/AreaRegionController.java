package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.AreaRegionVo;
import com.get.institutioncenter.service.IAreaRegionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/7/30
 * @TIME: 11:40
 * @Description:
 **/
@Api(tags = "区域管理-大区配置")
@RestController
@RequestMapping("/institution/areaRegion")
public class AreaRegionController {
    @Resource
    private IAreaRegionService areaRegionService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/区域管理-国家配置/国家详情")
    @GetMapping("/{id}")
    public ResponseBo<AreaRegionVo> detail(@PathVariable("id") Long id) {
        AreaRegionVo data = areaRegionService.findAreaRegionById(id);
        return new ResponseBo<>(data);
    }


    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "大区下拉", notes = "")
    @GetMapping("getAreaRegionSelect")
    public ResponseBo<BaseSelectEntity> getAreaRegionSelect(@RequestParam(value = "fkCompanyId", required = false)Long fkCompanyId) {
        return new ListResponseBo<>(areaRegionService.getAreaRegionSelect(fkCompanyId));
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "大区下拉", notes = "")
    @GetMapping("getAreaRegionSelectByCompanyIds")
    public ResponseBo<BaseSelectEntity> getAreaRegionSelectByCompanyIds(@RequestParam("fkCompanyIds") List<Long> fkCompanyIds) {
        return new ListResponseBo<>(areaRegionService.getAreaRegionSelectByCompanyIds(fkCompanyIds));
    }
}
