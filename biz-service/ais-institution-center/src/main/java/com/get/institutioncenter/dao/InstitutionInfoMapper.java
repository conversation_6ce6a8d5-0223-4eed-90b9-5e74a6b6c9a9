package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.institutioncenter.entity.InstitutionInfo;
import com.get.institutioncenter.dto.query.InstitutionInfoQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionInfoMapper extends BaseMapper<InstitutionInfo> {
    @Override
    int insert(InstitutionInfo record);

    int insertSelective(InstitutionInfo record);

    List<InstitutionInfo> datas(IPage<InstitutionInfo> pages,@Param("institutionInfoDto") InstitutionInfoQueryDto institutionInfoVo);

    /**
     * @return
     * @Description :获取ids
     * @Param
     * <AUTHOR>
     */
    List<Long> getInstitutionInfoIds(@Param("columnName") String columnName);

    /**
     * @return
     * @Description :获取详情
     * @Param
     * <AUTHOR>
     */
    InstitutionInfo getInstitutionInfoById(@Param("id") Long id);
}