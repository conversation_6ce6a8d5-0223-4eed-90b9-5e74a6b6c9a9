package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.CommentMapper;
import com.get.institutioncenter.vo.CommentVo;
import com.get.institutioncenter.entity.Comment;
import com.get.institutioncenter.service.ICommentService;
import com.get.institutioncenter.dto.CommentDto;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/5
 * @TIME: 15:44
 * @Description:
 **/
@Service
public class CommentServiceImpl extends BaseServiceImpl<CommentMapper, Comment> implements ICommentService {
    @Resource
    private CommentMapper commentMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public List<CommentVo> datas(CommentDto commentDto, Page page) {
        LambdaQueryWrapper<Comment> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getFkTableId())) {
                wrapper.eq(Comment::getFkTableId, commentDto.getFkTableId());
            }
            if (GeneralTool.isNotEmpty(commentDto.getFkTableName())) {
                wrapper.eq(Comment::getFkTableName, commentDto.getFkTableName());
            }
        }
        //获取分页数据
        IPage<Comment> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<Comment> comments = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<CommentVo> commentVos = comments.stream().map(comment -> BeanCopyUtils.objClone(comment, CommentVo::new)).collect(Collectors.toList());

        //返回创建人员工id
        Set<String> createUsers = commentVos.stream().map(CommentVo::getGmtCreateUser).collect(Collectors.toSet());
        List<StaffVo> staffVos = new ArrayList<>();
        Result<List<StaffVo>> resultstaffDtos = permissionCenterClient.getStaffByCreateUsers(createUsers);
        if (resultstaffDtos.isSuccess()) {
            staffVos = resultstaffDtos.getData();
        }
        Map<String, Long> staffMap = new HashMap<>();
        for (StaffVo staffVo : staffVos) {
            staffMap.put(staffVo.getLoginId(), staffVo.getId());
        }
        for (CommentVo commentVo : commentVos) {
            commentVo.setFkStaffId(staffMap.get(commentVo.getGmtCreateUser()));
        }
        return commentVos;
    }

    @Override
    public void addComment(Comment comment) {
        utilService.updateUserInfoToEntity(comment);
        commentMapper.insertSelective(comment);
    }

    @Override
    public void updateComment(Comment comment) {
        utilService.updateUserInfoToEntity(comment);
        commentMapper.updateById(comment);
    }

    @Override
    public void delete(Long id) {
        commentMapper.deleteById(id);
    }
}
