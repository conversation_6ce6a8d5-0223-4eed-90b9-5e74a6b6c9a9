<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionCourseRankingMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionCourseRanking">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_institution_course_id" jdbcType="BIGINT" property="fkInstitutionCourseId" />
    <result column="ranking_type" jdbcType="INTEGER" property="rankingType" />
    <result column="ranking_first" jdbcType="INTEGER" property="rankingFirst" />
    <result column="ranking_last" jdbcType="INTEGER" property="rankingLast" />
    <result column="ranking_note" jdbcType="VARCHAR" property="rankingNote" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionCourseRanking">
    insert into m_institution_course_ranking (id, fk_institution_course_id, ranking_type, 
      ranking_first, ranking_last, ranking_note, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkInstitutionCourseId,jdbcType=BIGINT}, #{rankingType,jdbcType=INTEGER}, 
      #{rankingFirst,jdbcType=INTEGER}, #{rankingLast,jdbcType=INTEGER}, #{rankingNote,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionCourseRanking">
    insert into m_institution_course_ranking
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInstitutionCourseId != null">
        fk_institution_course_id,
      </if>
      <if test="rankingType != null">
        ranking_type,
      </if>
      <if test="rankingFirst != null">
        ranking_first,
      </if>
      <if test="rankingLast != null">
        ranking_last,
      </if>
      <if test="rankingNote != null">
        ranking_note,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionCourseId != null">
        #{fkInstitutionCourseId,jdbcType=BIGINT},
      </if>
      <if test="rankingType != null">
        #{rankingType,jdbcType=INTEGER},
      </if>
      <if test="rankingFirst != null">
        #{rankingFirst,jdbcType=INTEGER},
      </if>
      <if test="rankingLast != null">
        #{rankingLast,jdbcType=INTEGER},
      </if>
      <if test="rankingNote != null">
        #{rankingNote,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>