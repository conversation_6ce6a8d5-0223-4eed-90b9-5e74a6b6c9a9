<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.AcademicScoreConfigMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.AcademicScoreConfig">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="condition_type" jdbcType="INTEGER" property="conditionType" />
    <result column="score_type" jdbcType="VARCHAR" property="scoreType" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.AcademicScoreConfig">
    insert into s_academic_score_config (id, condition_type, score_type, 
      score, remark, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{conditionType,jdbcType=INTEGER}, #{scoreType,jdbcType=VARCHAR}, 
      #{score,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.AcademicScoreConfig">
    insert into s_academic_score_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="conditionType != null">
        condition_type,
      </if>
      <if test="scoreType != null">
        score_type,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="conditionType != null">
        #{conditionType,jdbcType=INTEGER},
      </if>
      <if test="scoreType != null">
        #{scoreType,jdbcType=VARCHAR},
      </if>
      <if test="score != null">
        #{score,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getScoreTypeByCondition" resultType="java.lang.String">
    select score_type from s_academic_score_config where condition_type = #{id}
  </select>
</mapper>