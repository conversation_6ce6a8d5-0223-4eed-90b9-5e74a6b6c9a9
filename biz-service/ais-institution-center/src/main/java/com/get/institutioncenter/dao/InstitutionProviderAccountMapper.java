package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.institutioncenter.vo.InstitutionProviderAccountVo;
import com.get.institutioncenter.entity.InstitutionProviderAccount;
import com.get.institutioncenter.dto.InstitutionProviderAccountDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionProviderAccountMapper extends GetMapper<InstitutionProviderAccount> {

    List<InstitutionProviderAccountVo> getProviderAccountList(@Param("iPage") IPage<InstitutionProviderAccountDto> iPage
            , @Param("providerAccountVo") InstitutionProviderAccountDto providerAccountVo);

    List<InstitutionProviderAccountVo> getContractAccountExist(@Param("providerId") Long providerId, @Param("bankAccount") String bankAccount, @Param("bankAccountNum") String bankAccountNum);

    /**
     * 提供商银行账户下拉框
     * @param fkTargetId
     * @return
     */
    List<BaseSelectEntity> getInstitutionProviderAccountList(Long fkTargetId);

    String getInstitutionProviderAccountById(Long fkBankAccountId);
}
