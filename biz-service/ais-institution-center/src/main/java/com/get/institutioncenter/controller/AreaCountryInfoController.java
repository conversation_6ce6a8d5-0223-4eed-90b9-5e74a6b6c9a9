package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.dto.AreaCountryInfoDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.AreaCountryInfoVo;
import com.get.institutioncenter.service.IAreaCountryInfoService;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/13 17:06
 * @verison: 1.0
 * @description:
 */
@Api(tags = "国家资讯管理")
@RestController
@RequestMapping("/institution/areaCountryInfo")
public class AreaCountryInfoController {
    @Resource
    private IAreaCountryInfoService areaCountryInfoService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.AreaCountryInfoVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/国家资讯管理/国家资讯详情")
    @GetMapping("/{id}")
    public ResponseBo<AreaCountryInfoVo> detail(@PathVariable("id") Long id) {
        AreaCountryInfoVo data = areaCountryInfoService.findAreaCountryInfoById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :新增信息
     * @Param [areaCountryInfoVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/国家资讯管理/新增国家资讯")
    @PostMapping("add")
    public ResponseBo add(@RequestBody  @Validated(AreaCountryInfoDto.Add.class)  AreaCountryInfoDto areaCountryInfoDto) {
        return SaveResponseBo.ok(areaCountryInfoService.addAreaCountryInfo(areaCountryInfoDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/国家资讯管理/删除国家资讯")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        areaCountryInfoService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.AreaCountryInfoVo>
     * @Description :修改信息
     * @Param [areaCountryInfoDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/国家资讯管理/更新国家资讯")
    @PostMapping("update")
    public ResponseBo<AreaCountryInfoVo> update(@RequestBody  @Validated(AreaCountryInfoDto.Update.class)   AreaCountryInfoDto areaCountryInfoDto) {
        return UpdateResponseBo.ok(areaCountryInfoService.updateAreaCountryInfo(areaCountryInfoDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.AreaCountryInfoVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "fkAreaCountryId 国家id(必传)")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/国家资讯管理/查询国家资讯")
    @PostMapping("datas")
    public ResponseBo<AreaCountryInfoVo> datas(@RequestBody SearchBean<AreaCountryInfoDto> page) {
        List<AreaCountryInfoVo> datas = areaCountryInfoService.getAreaCountryInfos(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 查询活动附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询活动附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/国家资讯管理/查询附件")
    @PostMapping("getItemMedia")
    public ResponseBo<MediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = areaCountryInfoService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 保存活动附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存活动附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/国家资讯管理/保存附件")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAgentMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(areaCountryInfoService.addItemMedia(mediaAttachedVo));
    }
}
