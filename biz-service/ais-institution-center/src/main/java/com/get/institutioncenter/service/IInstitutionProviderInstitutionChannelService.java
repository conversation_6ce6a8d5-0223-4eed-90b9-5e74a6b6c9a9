package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.entity.InstitutionProviderInstitutionChannel;
import com.get.institutioncenter.dto.InstitutionProviderInstitutionChannelDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/9/18 15:27
 * @verison: 1.0
 * @description:
 */
public interface IInstitutionProviderInstitutionChannelService extends BaseService<InstitutionProviderInstitutionChannel> {
    /**
     * 关系表添加
     *
     * @param institutionProviderInstitutionChannelDto
     * @return
     */
    Long addInstitutionProviderInstitutionChannel(InstitutionProviderInstitutionChannelDto institutionProviderInstitutionChannelDto);

    /**
     * 删除
     *
     * @param id
     */
    void deleteByProviderId(Long id);

    /**
     * 获取渠道名称
     *
     * @param id
     * @return
     */
    String getInstitutionChannelNamesById(Long id);

    /**
     * 获取所属渠道ids
     *
     * @param id
     * @return
     * @
     */
    List<Long> getInstitutionChannelIdsById(Long id);

    /**
     * 根据渠道ID获取提供商IDs
     * @param fkInstitutionChannelId
     * @return
     */
    List<Long> getInstitutionProviderIdsByChannelId(Long fkInstitutionChannelId);
}
