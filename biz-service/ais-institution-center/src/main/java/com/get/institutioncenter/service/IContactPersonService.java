package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.institutioncenter.dto.ContactPersonDto;
import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.vo.ContactPersonVo;
import com.get.institutioncenter.dto.ContactPersonCompanyDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/8/17
 * @TIME: 10:56
 * @Description: 联系人管理接口
 **/
public interface IContactPersonService {

    /**
     * 学校详情获取联系人
     *
     * @param contactPersonDto
     * @param page
     * @return
     * @
     */
    List<ContactPersonVo> getContactPersonDtos(ContactPersonDto contactPersonDto, Page page);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ContactPersonVo finContactPersonById(Long id);

    /**
     * 新增
     *
     * @param contactPersonDto
     * @return
     * @
     */
    Long addContactPerson(ContactPersonDto contactPersonDto);

    /**
     * 修改
     *
     * @param contactPersonDto
     * @return
     * @
     */
    ContactPersonVo updateContactPerson(ContactPersonDto contactPersonDto);


    /**
     * @param id
     * @
     */
    void deleteContactPerson(Long id);


    /**
     * @return void
     * @Description: 安全配置
     * @Param [validList]
     * <AUTHOR>
     */
    void editContactPersonCompany(List<ContactPersonCompanyDto> validList);

    /**
     * @return java.util.List<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 联系人公司回显
     * @Param [id]
     * <AUTHOR>
     */
    List<CompanyTreeVo> getContactRelation(Long id);

    /**
     * 新增
     *
     * @param contactPersonDto
     * @return
     * @
     */
    Long addProviderContactPerson(ContactPersonDto contactPersonDto);


    /**
     * @return java.util.List<java.util.Map < java.lang.String, java.lang.Object>>
     * @Description: 表名下拉
     * @Param []
     * <AUTHOR>
     */
    List<Map<String, Object>> findTargetType();

}
