package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.entity.ContractFormulaPreInstitution;
import com.get.institutioncenter.dto.ContractFormulaPreInstitutionDto;

import java.util.List;

/**
 * 合同公式 前置学校逻辑处理类
 *
 * @Date 16:20 2021/6/2
 * <AUTHOR>
 */
public interface IContractFormulaPreInstitutionService extends BaseService<ContractFormulaPreInstitution> {
    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [contractFormulaInstitutionVo]
     * <AUTHOR>
     */
    Long addContractFormulaPreInstitution(ContractFormulaPreInstitutionDto contractFormulaPreInstitutionDto);

    /**
     * @return void
     * @Description :
     * @Param [contractFormulaId]
     * <AUTHOR>
     */
    void deleteByFkid(Long contractFormulaId);

    /**
     * 通过合同公式id 查找对应前置学校ids
     *
     * @Date 16:23 2021/6/2
     * <AUTHOR>
     */
    List<Long> getInstiutionIdListByFkId(Long contractFormulaId);
}
