package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.AreaCityMapper;
import com.get.institutioncenter.dao.AreaStateMapper;
import com.get.institutioncenter.dto.AreaCityDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.AreaCityVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.AreaCity;
import com.get.institutioncenter.entity.AreaState;
import com.get.institutioncenter.service.IAreaCityService;
import com.get.institutioncenter.service.IDeleteService;
import com.get.institutioncenter.service.IMediaAndAttachedService;
import com.get.institutioncenter.service.ITranslationMappingService;
import com.get.institutioncenter.utils.MyStringUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2020/7/28 10:52
 * @verison: 1.0
 * @description: 区域管理-城市配置实现类
 */
@Service
public class AreaCityServiceImpl extends BaseServiceImpl<AreaCityMapper, AreaCity> implements IAreaCityService {
    @Resource
    private AreaCityMapper areaCityMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private AreaStateMapper areaStateMapper;

    @Override
    public AreaCityVo findAreaCityById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaCity areaCity = areaCityMapper.selectById(id);
        if (GeneralTool.isEmpty(areaCity)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaCityVo areaCityVo = BeanCopyUtils.objClone(areaCity, AreaCityVo::new);
        //语言
        areaCityVo.setFkTableName(TableEnum.INSTITUTION_AREA_CITY.key);
        //设置公开对象名称
        StringJoiner stringJoiner = new StringJoiner(" ");
        if (GeneralTool.isNotEmpty(areaCityVo.getPublicLevel())) {
            List<String> result = Arrays.asList(areaCityVo.getPublicLevel().split(","));
            for (String name : result) {
                stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
            }
            areaCityVo.setPublicLevelName(stringJoiner.toString());
        }
        return areaCityVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchAdd(ValidList<AreaCityDto> areaCityDtos) {
        if (GeneralTool.isEmpty(areaCityDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (AreaCityDto areaCityDto : areaCityDtos) {
            if (GeneralTool.isEmpty(areaCityDto.getId())) {
                AreaCity areaCity = BeanCopyUtils.objClone(areaCityDto, AreaCity::new);
                utilService.updateUserInfoToEntity(areaCity);
                areaCityMapper.insertSelective(areaCity);
                //自动生成编号
                areaCity.setNum(MyStringUtils.getCityNum(areaCity.getId()));
                areaCityMapper.updateById(areaCity);
            } else {
                AreaCity areaCountry = BeanCopyUtils.objClone(areaCityDto, AreaCity::new);
                utilService.updateUserInfoToEntity(areaCountry);
                areaCityMapper.updateById(areaCountry);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findAreaCityById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //删除验证
        deleteService.deleteValidateAreaCity(id);
        areaCityMapper.deleteById(id);

        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_AREA_CITY.key, id);
    }

    @Override
    public AreaCityVo updateAreaCity(AreaCityDto areaCityDto) {
        if (areaCityDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        AreaCity result = areaCityMapper.selectById(areaCityDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaCity areaCountry = BeanCopyUtils.objClone(areaCityDto, AreaCity::new);
        utilService.updateUserInfoToEntity(areaCountry);
        areaCityMapper.updateById(areaCountry);
        return findAreaCityById(areaCityDto.getId());
    }

    @Override
    public List<AreaCityVo> getAreaCitys(AreaCityDto areaCityDto, Page page) {
        IPage<AreaCity> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        //要按照最新记录排序
        List<AreaCity> areaCitys = areaCityMapper.getAreaCitys(pages, areaCityDto);
        page.setAll((int) pages.getTotal());
        List<AreaCityVo> convertDatas = new ArrayList<>();
        for (AreaCity areaCity : areaCitys) {
            AreaCityVo areaCityVo = BeanCopyUtils.objClone(areaCity, AreaCityVo::new);
            //语言
            areaCityVo.setFkTableName(TableEnum.INSTITUTION_AREA_CITY.key);
            convertDatas.add(areaCityVo);
        }
        return convertDatas;
    }

    @Override
    public List<AreaCityVo> getByFkAreaStateId(Long id) {
        LambdaQueryWrapper<AreaCity> wrapper = new LambdaQueryWrapper();
        wrapper.eq(AreaCity::getFkAreaStateId, id);
        //获取分页数据
        List<AreaCity> areaCitys = areaCityMapper.selectList(wrapper);
        List<AreaCityVo> areaCityDtoList = new ArrayList<>();
        for (AreaCity areaCity : areaCitys) {
            AreaCityVo areaCityVo = BeanCopyUtils.objClone(areaCity, AreaCityVo::new);
            areaCityVo.setFullName(areaCityMapper.getCityFullNameById(areaCity.getId()));
            areaCityDtoList.add(areaCityVo);
        }
        return areaCityDtoList;
    }

    /**
     * @Description: 查询国家下面城市
     * @Author: Jerry
     * @Date:12:38 2021/9/10
     */
    @Override
    public List<AreaCityVo> getByFkAreaCountryId(Long fkAreaCountryId) {
        List<AreaCity> areaCityList = null;
        if (GeneralTool.isEmpty(fkAreaCountryId)) {
            LambdaQueryWrapper<AreaCity> wrapper = new LambdaQueryWrapper();
            areaCityList = areaCityMapper.selectList(wrapper);
        } else {
            LambdaQueryWrapper<AreaState> wrapper = new LambdaQueryWrapper();
            wrapper.eq(AreaState::getFkAreaCountryId, fkAreaCountryId);
            List<AreaState> areaStateList = areaStateMapper.selectList(wrapper);
            if (GeneralTool.isEmpty(areaStateList)) {
                return new ArrayList<>();
            }
            //州省ids
            Set<Long> fkAreaStateIds = areaStateList.stream().map(AreaState::getId).collect(Collectors.toSet());
            LambdaQueryWrapper<AreaCity> wrapperAreaCity = new LambdaQueryWrapper();
            wrapperAreaCity.in(AreaCity::getFkAreaStateId, fkAreaStateIds);
            areaCityList = this.areaCityMapper.selectList(wrapperAreaCity);
        }
        if (GeneralTool.isEmpty(areaCityList)) {
            return new ArrayList<>();
        }
        List<AreaCityVo> areaCityDtoList = new ArrayList<>();
        Set<Long> ids = areaCityList.stream().map(AreaCity::getId).collect(Collectors.toSet());
        Map<Long, String> cityFullNamesByIds = getCityFullNamesByIds(ids);
        for (AreaCity areaCity : areaCityList) {
            AreaCityVo areaCityVo = BeanCopyUtils.objClone(areaCity, AreaCityVo::new);
            areaCityVo.setFullName(cityFullNamesByIds.get(areaCity.getId()));
            areaCityDtoList.add(areaCityVo);
        }
        return areaCityDtoList;
    }

    @Override
    public String getCityNameById(Long id) {
        return areaCityMapper.getCityNameById(id);
    }

    @Override
    public String getCityFullNameById(Long id) {
        return areaCityMapper.getCityFullNameById(id);
    }

    @Override
    public Map<Long, String> getCityNamesByIds(Set<Long> ids) {
        //up by Jerry 2021/07/16 10:36:00
        //批量查询改成一次性查询
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<AreaCity> wrapper = new LambdaQueryWrapper();
        wrapper.in(AreaCity::getId, ids);
        List<AreaCity> areaCityList = areaCityMapper.selectList(wrapper);
        for (AreaCity areaCity : areaCityList) {
            //map.put(areaCity.getId(), areaCity.getName());
            String cityName = GeneralTool.isEmpty(areaCity.getName()) ? "" : areaCity.getName();
            StringBuilder builder = new StringBuilder(cityName);
            if (GeneralTool.isNotEmpty(areaCity.getNameChn())) {
                builder.append("（");
                builder.append(areaCity.getNameChn());
                builder.append("）");
            }
            map.put(areaCity.getId(), builder.toString());
        }
        return map;
    }

    @Override
    public Map<Long, String> getCityFullNamesByIds(Set<Long> ids) {
        //up by Jerry 2021/07/16 10:36:00
        //批量查询改成一次性查询
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<AreaCity> wrapper = new LambdaQueryWrapper();
        wrapper.in(AreaCity::getId, ids);
        List<AreaCity> areaCityList = areaCityMapper.selectList(wrapper);
        for (AreaCity areaCity : areaCityList) {
            String cityName = GeneralTool.isEmpty(areaCity.getName()) ? "" : areaCity.getName();
            StringBuilder builder = new StringBuilder(cityName);
            if (GeneralTool.isNotEmpty(areaCity.getNameChn())) {
                builder.append("（");
                builder.append(areaCity.getNameChn());
                builder.append("）");
            }
            map.put(areaCity.getId(), builder.toString());
        }
        return map;
    }

    @Override
    public Map<Long, String> getCityNameChnsByIds(Set<Long> ids) {
        //up by Jerry 2021/07/19 09:49:00
        //批量查询改成一次性查询
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<AreaCity> wrapper = new LambdaQueryWrapper();
        wrapper.eq(AreaCity::getId, ids);
        List<AreaCity> areaCities = areaCityMapper.selectList(wrapper);
        for (AreaCity areaCity : areaCities) {
            map.put(areaCity.getId(), areaCity.getNameChn());
        }
        return map;
    }

    @Override
    public List<MediaAndAttachedVo> getItemMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.INSTITUTION_AREA_CITY.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);

    }

    @Override
    public List<MediaAndAttachedVo> addItemMedia(ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.INSTITUTION_AREA_CITY.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public String getCityChnNameById(Long id) {
        return areaCityMapper.getCityChnNameById(id);
    }

    /**
     * 获取对应国家、公司下 有申请计划的代理所在的 城市下拉框数据
     *
     * @Date 19:01 2023/1/5
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getExistsAgentOfferItemAreaStateCityList(Long companyId, String stateIds) {
        if (GeneralTool.isEmpty(SecureUtil.getCountryIds())) {
            return null;
        }
        return areaCityMapper.getExistsAgentOfferItemAreaStateCityList(companyId, stateIds, SecureUtil.getCountryIds());
    }

    /**
     * 获取对应国家、公司下的代理所在的 城市下拉框数据
     *
     * @Date 10:36 2023/3/23
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getExistsAgentAreaStateCityList(Long companyId, String stateIds) {
        if (GeneralTool.isEmpty(SecureUtil.getCountryIds())) {
            return null;
        }
        return areaCityMapper.getExistsAgentAreaStateCityList(companyId, stateIds, SecureUtil.getCountryIds());
    }

    @Override
    public String getAreaCityNameByIds(Set<Long> ids) {
        if(GeneralTool.isEmpty(ids)){
            return null;
        }
        StringBuilder stringBuilder = new StringBuilder();
        List<AreaCity> areaCities = areaCityMapper.selectList(new LambdaQueryWrapper<AreaCity>().in(AreaCity::getId,ids));
        if (GeneralTool.isNotEmpty(areaCities)){

            Set<Long> areaStateIds = areaCities.stream().map(AreaCity::getFkAreaStateId).collect(Collectors.toSet());
            List<AreaState> countryIdsByState = areaStateMapper.getCountryIdsByStateIds(areaStateIds);
            List<String> stateNames = countryIdsByState.stream().map(AreaState::getNameChn).collect(Collectors.toList());
            String stateName = StringUtils.join(stateNames.toArray(), ",");
            stringBuilder.append("【省份】").append(stateName);
            List<String> areaCityNames = areaCities.stream().map(AreaCity::getNameChn).collect(Collectors.toList());
            String cityName = StringUtils.join(areaCityNames.toArray(), ",");
            stringBuilder.append("【城市】").append(cityName);
        }
        return stringBuilder.toString();
    }

    /**
     * 获取城市全名
     * @return
     */
    @Override
    public Map<Long, String> getCityFullNames() {
        Map<Long, String> map = new HashMap<>();
        LambdaQueryWrapper<AreaCity> wrapper = new LambdaQueryWrapper();
        List<AreaCity> areaCityList = areaCityMapper.selectList(wrapper);
        for (AreaCity areaCity : areaCityList) {
            String cityName = GeneralTool.isEmpty(areaCity.getName()) ? "" : areaCity.getName();
            StringBuilder builder = new StringBuilder(cityName);
            if (GeneralTool.isNotEmpty(areaCity.getNameChn())) {
                builder.append("（");
                builder.append(areaCity.getNameChn());
                builder.append("）");
            }
            map.put(areaCity.getId(), builder.toString());
        }
        return map;
    }

}
