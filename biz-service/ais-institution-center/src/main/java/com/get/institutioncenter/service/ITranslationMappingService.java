package com.get.institutioncenter.service;

import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.TranslationDto;
import com.get.institutioncenter.vo.TranslationMappingVo;
import com.get.institutioncenter.entity.InstitutionTranslationMapping;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/12/16
 * @TIME: 12:58
 * @Description:
 **/
public interface ITranslationMappingService extends BaseService<InstitutionTranslationMapping> {
    /**
     * @return
     * @Description :语言接口获取翻译信息
     * @Param
     * <AUTHOR>
     */
    List<TranslationMappingVo> getTranslationMappingDtos(TranslationDto translationDto);

    /**
     * @return
     * @Description :保存翻译信息
     * @Param
     * <AUTHOR>
     */
    void updateTranslations(List<TranslationDto> translationDtos);

    /**
     * @return
     * @Description :语言类型下拉框
     * @Param
     * <AUTHOR>
     */
    List<Map<String, Object>> findLanguageType();

    /**
     * 删除翻译内容
     *
     * @Date 12:09 2021/8/17
     * <AUTHOR>
     */
    void deleteTranslations(String fkTableName, Long fkTableId);
}
