package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;

import com.get.core.secure.UserInfo;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.RequestContextUtil;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.dto.NewsDto;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.vo.NewsEmailTagDto;
import com.get.institutioncenter.vo.NewsVo;
import com.get.institutioncenter.service.INewsService;
import com.get.institutioncenter.dto.NewEmailToAgentDto;
import com.get.institutioncenter.dto.NewsCompanyDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/8/19
 * @TIME: 14:20
 * @Description:
 **/
@Api(tags = "新闻管理")
@RestController
@RequestMapping("/institution/news")
public class NewsController {
    @Resource
    private INewsService newsService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/新闻管理/新闻详情")
    @GetMapping("/{id}")
    public ResponseBo<NewsVo> detail(@PathVariable("id") Long id) {
        NewsVo data = newsService.findNewsById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 新增信息
     *
     * @param newsDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/新闻管理/新增新闻")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(NewsDto.Add.class) NewsDto newsDto) {
        return SaveResponseBo.ok(newsService.addNews(newsDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/新闻管理/删除新闻")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        newsService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param newsDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/新闻管理/更新新闻")
    @PostMapping("update")
    public ResponseBo<NewsVo> update(@RequestBody @Validated(NewsDto.Update.class) NewsDto newsDto) {
        return UpdateResponseBo.ok(newsService.updateNews(newsDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/新闻管理/查询新闻")
    @PostMapping("datas")
    public ResponseBo<NewsVo> datas(@RequestBody SearchBean<NewsQueryDto> page) {
        List<NewsVo> datas = newsService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 目标类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "目标类型下拉框数据", notes = "")
    @GetMapping("findTargetType")
    public ResponseBo findTargetType() {
        List<Map<String, Object>> datas = newsService.findTargetType();
        return new ListResponseBo<>(datas);
    }

    /**
     * 目标类型获取目标
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "目标类型获取目标", notes = "")
    @PostMapping("findTarget")
    public ResponseBo<BaseSelectEntity> findTarget(@RequestParam String tableName) {
        if("institution_news_bulletin_board".equals(tableName)){
            return new ResponseBo<>(null);
        }
        List<BaseSelectEntity> datas = newsService.findTarget(tableName);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.CompanyTreeVo>
     * @Description: 回显新闻和公司的关系
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "回显新闻和公司的关系", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/新闻管理/新闻和公司的关系（数据回显）")
    @GetMapping("getNewsRelation/{newId}")
    public ResponseBo<CompanyTreeVo> getNewsCompanyRelation(@PathVariable("newId") Long id) {
        List<CompanyTreeVo> newsCompanyRelation = newsService.getNewRelation(id);
        return new ListResponseBo<>(newsCompanyRelation);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新闻安全配置
     * @Param [validList]
     * <AUTHOR>
     */
    @ApiOperation(value = "联系人-公司安全配置")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/新闻管理/安全配置")
    @PostMapping("editNewsRelation")
    public ResponseBo editNewsRelation(@RequestBody @Validated(NewsCompanyDto.Add.class) ValidList<NewsCompanyDto> validList) {
        newsService.editNewsCompany(validList);
        return UpdateResponseBo.ok();
    }

    /**
     * @param mediaAttachedVo
     * @return
     * @
     */

    @ApiOperation(value = "保存文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/新闻管理/保存附件资料")
    @PostMapping("addMediaAndAttached")
    public ResponseBo addMediaAndAttached(@RequestBody @Validated(MediaAndAttachedDto.Add.class) List<MediaAndAttachedDto> mediaAttachedVo) {
        return UpdateResponseBo.ok(newsService.addNewsMedia(mediaAttachedVo));
    }

    /**
     * 查询新闻附件
     *
     * @param voSearchBean
     * @return
     * @
     */
    @ApiOperation(value = "查询新闻附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/新闻管理/查询新闻附件")
    @PostMapping("getNewsMedia")
    public ResponseBo<MediaAndAttachedVo> getNewsMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> newsMedia = newsService.getNewsMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(newsMedia, page);
    }

    /**
     * 所有新闻下拉框
     *
     * @Date 11:54 2021/7/28
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "所有新闻下拉框", notes = "")
    @GetMapping("getAllNewsSelect")
    public ResponseBo<BaseSelectEntity> getAllNewsSelect() {
        List<BaseSelectEntity> datas = newsService.getAllNewsSelect();
        return new ListResponseBo<>(datas);
    }



    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 发送新闻电邮
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "发送新闻电邮", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/新闻管理/发送新闻电邮")
    @GetMapping("sendNewsMail/{id}")
    public ResponseBo sendNewsMail(@PathVariable("id") Long id) throws Exception {
        newsService.sendNewsMail(id);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "发送新闻电邮to代理", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/新闻管理/发送新闻电邮to代理")
    @PostMapping("sendNewsMailtoAgent")
    public ResponseBo sendNewsMailtoAgent( @RequestBody @Validated(NewEmailToAgentDto.Update.class) NewEmailToAgentDto newEmailToAgentVo) throws Exception {
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        UserInfo user = GetAuthInfo.getUser();
        String locale = SecureUtil.getLocale();
        newsService.sendNewsMailtoAgent(newEmailToAgentVo, headerMap, user, locale);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "发送新闻电邮to所有代理", notes = "type:1Hubs/2市场")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/新闻管理/发送新闻电邮to所有代理")
    @PostMapping("sendNewsMailtoAllAgent")
    public ResponseBo sendNewsMailtoAllAgent( @RequestParam Long id, @RequestParam Integer type) throws Exception {
        Map<String, String> headerMap = RequestContextUtil.getHeaderMap();
        UserInfo user = GetAuthInfo.getUser();
        String locale = SecureUtil.getLocale();
        newsService.sendNewsMailtoAllAgent(id, headerMap, user, locale, type);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "发送新闻电邮to自己", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/新闻管理/发送新闻电邮to自己")
    @PostMapping("sendNewsMailtoMe")
    public ResponseBo sendNewsMailtoMe(@RequestParam Long id) throws Exception {
        newsService.sendNewsMailtoMe(id);
        return ResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 新闻附件类型
     * @Param []
     * <AUTHOR>
     */
    @ApiOperation(value = "新闻附件类型", notes = "")
    @PostMapping("getNewsMediaTypeSelect")
    public ResponseBo getNewsMediaTypeSelect() {
        return new ListResponseBo<>(FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.NEWS));
    }

    /**
     * feign调用 查询新闻标题Map
     *
     * @Date 16:01 2021/6/10
     * <AUTHOR>
     */
  /*  @ApiIgnore
    @PostMapping("getNewsTitlesByIds")
    @VerifyPermission(IsVerify = false)
    public Map<Long, String> getNewsTitlesByIds(@RequestBody Set<Long> ids)  {
        return newsService.getNewsTitlesByIds(ids);
    }*/

    /**
     * @Description: 新增邮件标签
     * @param newsEmailTagVo
     * @return
     * <AUTHOR>
     */
    @ApiOperation(value = "新增邮件标签", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/新闻管理/新增邮件标签")
    @PostMapping("addEmailTagName")
    public ResponseBo addEmailTagName(@RequestBody NewsEmailTagDto newsEmailTagVo) {
         newsService.addNewsEmailTage(newsEmailTagVo);
         return ResponseBo.ok();
    }
}
