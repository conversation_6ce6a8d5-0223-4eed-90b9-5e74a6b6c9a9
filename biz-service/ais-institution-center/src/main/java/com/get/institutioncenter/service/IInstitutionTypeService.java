package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.core.mybatis.utils.ValidList;
import com.get.institutioncenter.dto.InstitutionTypeDto;
import com.get.institutioncenter.entity.InstitutionType;
import com.get.institutioncenter.vo.InstitutionTypeVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/30 11:13
 * @verison: 1.0
 * @description: 学校类型管理接口
 */
public interface IInstitutionTypeService extends BaseService<InstitutionType> {

    /**
     * 详情
     *
     * @param id
     * @return
     */
    InstitutionTypeVo findInstitutionTypeById(Long id);

    /**
     * 批量新增
     *
     * @param institutionTypeDtos
     * @return
     */
    void batchAdd(ValidList<InstitutionTypeDto> institutionTypeDtos);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    void delete(Long id);

    /**
     * 修改
     *
     * @param institutionTypeDto
     * @return
     */
    InstitutionTypeVo updateInstitutionType(InstitutionTypeDto institutionTypeDto);


    /**
     * 列表
     *
     * @param institutionTypeDto
     * @param page
     * @return
     */
    List<InstitutionTypeVo> getInstitutionTypes(InstitutionTypeDto institutionTypeDto, Page page);

    /**
     * 上移下移
     *
     * @param institutionTypeDtos
     * @return
     */
    void movingOrder(List<InstitutionTypeDto> institutionTypeDtos);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :学校类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInstitutionTypeList();


    /**
     * 根据学校类型ids获取名称
     *
     * @param ids
     * @return
     * @
     */
    Map<Long, String> getInstitutionTypeNameByIds(Set<Long> ids);

    /**
     * 获取学校类型名称
     *
     * @return
     */
    Map<Long, String> getAllInstitutionTypeName();

    String getTypeIdStringByCourseId(Long id);

    /**
     * k12类型下拉框数据
     * @return
     */
    List<BaseSelectEntity> getInstitutionK12TypeList();

    /**
     * 住宿类型下拉框数据
     * @return
     */
    List<BaseSelectEntity> getAccommodationTypeList();
}
