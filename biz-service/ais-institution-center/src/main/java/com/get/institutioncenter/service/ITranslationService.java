package com.get.institutioncenter.service;

import com.get.common.result.ResponseBo;
import com.get.core.mybatis.base.BaseService;
import com.get.core.translation.baidu.result.TransVo;
import com.get.institutioncenter.dto.BatchTranslationInstitutionAndCourseInfoDto;
import com.get.institutioncenter.vo.BatchTranslationResultVo;
import com.get.institutioncenter.entity.InstitutionTranslation;

/**
 * @author: Hardy
 * @create: 2021/6/4 12:39
 * @verison: 1.0
 * @description:
 */
public interface ITranslationService extends BaseService<InstitutionTranslation> {

    /**
     * @return void
     * @Description :批量翻译新闻
     * @Param [transVo]
     * <AUTHOR>
     */
    void batchTranslationNews(TransVo transVo, String createUser);

    /**
     * @return void
     * @Description :批量翻译咨询
     * @Param [transVo]
     * <AUTHOR>
     */
    void batchTranslationInstitutionInfos(TransVo transVo, String createUser);

    /**
     * @return void
     * @Description :批量翻译国家配置
     * @Param [transVo]
     * <AUTHOR>
     */
    void batchTranslationAreaCountrys(TransVo transVo, String createUser);

    /**
     * @return void
     * @Description :批量翻译学校配置
     * @Param [transVo]
     * <AUTHOR>
     */
    void batchTranslationInstitutions(TransVo transVo, String createUser);

    /**
     * @return void
     * @Description :批量翻译课程配置
     * @Param [transVo]
     * <AUTHOR>
     */
    void batchTranslationInstitutionCourses(TransVo transVo, String createUser);

    /**
     * @return com.get.institutioncenter.vo.BatchTranslationResultVo
     * @Description :返回翻译数量，预计时间
     * @Param [transVo]
     * <AUTHOR>
     */
    BatchTranslationResultVo getBatchTranslationResultInfo(TransVo transVo, String tableName);

    /**
     * @return com.get.institutioncenter.vo.BatchTranslationResultVo
     * @Description :返回翻译数量，预计时间
     * @Param [transVo]
     * <AUTHOR>
     */
    BatchTranslationResultVo getBatchTranslationCountryInfo(TransVo transVo, String tableName);

    /**
     * @return com.get.institutioncenter.vo.BatchTranslationResultVo
     * @Description :返回翻译数量，预计时间
     * @Param [transVo]
     * <AUTHOR>
     */
    BatchTranslationResultVo getBatchTranslationInstitutionInfo(TransVo transVo, String tableName);

    /**
     * @return com.get.institutioncenter.vo.BatchTranslationResultVo
     * @Description :返回翻译数量，预计时间
     * @Param [transVo]
     * <AUTHOR>
     */
    BatchTranslationResultVo getBatchTranslationInstitutionCourseInfo(TransVo transVo, String tableName);

    /**
     * @return
     * @Description :批量翻译新闻进度
     * @Param
     * <AUTHOR>
     */
    BatchTranslationResultVo batchTranslationCount();

    /**
     * @return
     * @Description :批量翻译资讯进度
     * @Param
     * <AUTHOR>
     */
    BatchTranslationResultVo batchTranslateInstitutionInfosCount();

    /**
     * @return
     * @Description :批量翻译国家进度
     * @Param
     * <AUTHOR>
     */
    BatchTranslationResultVo batchTranslateAreaCountrysCount();
    /**
     * @return
     * @Description :批量翻译学校进度
     * @Param
     * <AUTHOR>
     */
    BatchTranslationResultVo batchTranslateInstitutionsCount();
    /**
     * @return
     * @Description :批量翻译课程进度
     * @Param
     * <AUTHOR>
     */
    BatchTranslationResultVo batchTranslateInstitutionCoursesCount();

    /**
     * @return
     * @Description :字库转换
     * @Param
     * <AUTHOR>
     */
    void batchTranformNews(String tableName, String languageCode);

    /**
     * @return
     * @Description :
     * @Param
     * <AUTHOR>
     */
    void batchSetAreaStateName();


    /**
     * @return com.get.institutioncenter.vo.BatchTranslationResultVo
     * @Description :返回翻译数量，预计时间
     * @Param [transVo]
     * <AUTHOR>
     */
    BatchTranslationResultVo getBatchSetStateName();

    /**
     * @return com.get.institutioncenter.vo.BatchTranslationResultVo
     * @Description :返回翻译数量，预计时间
     * @Param [transVo]
     * <AUTHOR>
     */
    BatchTranslationResultVo getBatchSetCityName();

    /**
     * @return
     * @Description :
     * @Param
     * <AUTHOR>
     */
    void batchSetAreaCityName();

    /**
     * 批量翻译地区街道
     *
     * @param transVo
     * @param createUser
     */
    void batchTranslationCityDivisions(TransVo transVo, String createUser);

    BatchTranslationResultVo getBatchTranslationCityDivisionInfo(TransVo transVo, String tableName);

    ResponseBo batchTranslationInstitutionAndCourseInfo(BatchTranslationInstitutionAndCourseInfoDto batchTranslationInstitutionAndCourseInfoDto);

    ResponseBo batchTranslationInstitutionAndCourse();

}
