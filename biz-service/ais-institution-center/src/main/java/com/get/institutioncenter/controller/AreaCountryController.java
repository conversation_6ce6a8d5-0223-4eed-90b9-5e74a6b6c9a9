package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.HtiAuthenticationUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dto.AreaCountryDto;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.dto.NewsDto;
import com.get.institutioncenter.dto.query.AreaCountryQueryDto;
import com.get.institutioncenter.dto.query.NewsQueryDto;
import com.get.institutioncenter.service.IAreaCountryService;
import com.get.institutioncenter.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/17 11:23
 * @verison: 1.0
 * @description: 区域管理-国家配置控制器
 */
@Api(tags = "区域管理-国家配置")
@RestController
@RequestMapping("/institution/areaCountry")
public class AreaCountryController {

    @Resource
    private IAreaCountryService areaCountryService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/区域管理-国家配置/国家详情")
    @GetMapping("/{id}")
    public ResponseBo<AreaCountryVo> detail(@PathVariable("id") Long id) {
        AreaCountryVo data = areaCountryService.findAreaCountryById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 国家详情下的新闻列表
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "国家详情下的新闻列表", notes = "fkTableId为此国家id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/区域管理-国家配置/国家详情下的新闻列表")
    @PostMapping("getNewsList")
    public ResponseBo<NewsVo> getNewsList(@RequestBody SearchBean<NewsQueryDto> page) {
        List<NewsVo> data = areaCountryService.getNewsList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(data, p);
    }






    /**
     * 新增信息
     *
     * @param areaCountryDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/区域管理-国家配置/新增国家")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(AreaCountryDto.Add.class)  AreaCountryDto areaCountryDto) {
        return SaveResponseBo.ok(areaCountryService.addAreaCountry(areaCountryDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/区域管理-国家配置/删除国家")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        areaCountryService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param areaCountryDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/区域管理-国家配置/更新国家")
    @PostMapping("update")
    public ResponseBo<AreaCountryVo> update(@RequestBody @Validated(AreaCountryDto.Update.class) AreaCountryDto areaCountryDto) {
        return UpdateResponseBo.ok(areaCountryService.updateAreaCountry(areaCountryDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字(name国家名称 nameChn国家中文名称)")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/区域管理-国家配置/查询国家")
    @PostMapping("datas")
    public ResponseBo<AreaCountryVo> datas(@RequestBody SearchBean<AreaCountryQueryDto> page) {
        List<AreaCountryVo> datas = areaCountryService.getAreaCountrys(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }



    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "地理大区下拉", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/区域管理-国家配置/地理大区下拉")
    @GetMapping("getGeographicalRegions")
    public ResponseBo<AreaRegionVo> getGeographicalRegions() {
        List<AreaRegionVo> datas = areaCountryService.getGeographicalRegions();
        return new ListResponseBo<>(datas);
    }




    /**
     * 国家详情下新增新闻
     *
     * @param newsDto
     * @return
     * @
     */
    @ApiOperation(value = "新增国家新闻")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/区域管理-国家配置/新增国家新闻")
    @PostMapping(value = "addContactPerson")
    public ResponseBo addContactPerson(@RequestBody @Validated(NewsDto.Add.class)  NewsDto newsDto) {
        return SaveResponseBo.ok(areaCountryService.addNews(newsDto));
    }

    @ApiOperation(value = "查询所有国家", notes = "keyWord为关键词")
    @ApiIgnore
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/国家管理/查询国家")
    @PostMapping("getAreaCountrys")
    public ResponseBo getAreaCountrys(@RequestParam(required = false) String keyWord) {
        List<AreaCountryVo> areaCountrys = areaCountryService.getAreaCountrys(keyWord);
        return new ListResponseBo<>(areaCountrys);
    }

    /**
     * 保存文件
     *
     * @param mediaAttachedVos
     * @return
     * @
     */
    @ApiOperation(value = "保存文件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/区域管理-国家配置/保存附件资料")
    @PostMapping("addMediaAndAttached")
    public ResponseBo addMediaAndAttached(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVos) {
        return new ListResponseBo<>(areaCountryService.addInstitutionFacultyMedias(mediaAttachedVos));
    }

    /**
     * feign调用 通过国家编号 查找对应的国家名称
     *
     * @param countryKey
     * @return
     */
   /* @ApiIgnore
    @GetMapping(value = "getCountryName")
    public String getCountryName(@RequestParam(required = false) String countryKey) {
        return areaCountryService.getCountryNameByKey(countryKey);
    }*/

    /**
     * feign调用 获取所有国家ids
     *
     * @param
     * @return
     */
/*    @ApiIgnore
    @VerifyLogin(IsVerify = false)
    @GetMapping(value = "getAllCountryId")
    public Set<Long> getAllCountryId() {
        return areaCountryService.getAllCountryId();
    }*/

    /**
     * feign调用 通过国家id 查找对应的国家名称
     *
     * @param id
     * @return
     */
/*    @ApiIgnore
    @GetMapping(value = "getCountryNameById")
    public String getCountryNameById(@RequestParam(required = false) Long id) {
        return areaCountryService.getCountryNameById(id);
    }*/
    /**
     * feign调用 通过国家id 查找对应的国家名称
     *
     * @param id
     * @return
     */
/*    @ApiIgnore
    @GetMapping(value = "getCountryChnNameById")
    public String getCountryChnNameById(@RequestParam(required = false) Long id) {
        return areaCountryService.getCountryChnNameById(id);
    }*/

    /**
     * feign调用 通过国家ids 查找对应的国家名称
     *
     * @param ids
     * @return
     */
/*
    @ApiIgnore
    @VerifyLogin(IsVerify = false)
    @GetMapping(value = "getCountryChnNameByIds")
    public Map<Long,String> getCountryChnNameByIds(@RequestParam(required = false) Set<Long> ids) {
        return areaCountryService.getCountryChnNameByIds(ids);
    }
*/

    /**
     * 根据国家ids 获取国家编号
     *
     * @Date 16:31 2022/3/5
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping(value = "getCountryNumByCountryIds")
    Map<Long, String> getCountryNumByCountryIds(@RequestBody Set<Long> countryIdIdSet) {
        return areaCountryService.getCountryNumByCountryIds(countryIdIdSet);

    }

    /**
     * 国家下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "当前登录人国家下拉框数据", notes = "")
    @GetMapping("getAreaCountryList")
    public ResponseBo getAreaCountryList() {
        List<BaseSelectEntity> datas = areaCountryService.getAreaCountryList();
        return new ListResponseBo<>(datas);
    }

    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "HTI国家下拉框", notes = "")
    @GetMapping("getCountryList")
    public ResponseBo getAreaCountryListHti(@RequestParam("currentTimeMillis") long currentTimeMillis, @RequestParam("secretKeyHti") String secretKeyHti) throws Exception {
        boolean htiFlag = HtiAuthenticationUtils.validateHti(currentTimeMillis, secretKeyHti);
        if (!htiFlag) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("authentication_failed"));
        }
        List<AreaCountryHtiVo> datas = areaCountryService.getAreaCountryListHti();
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取对应公司下有申请计划的 业务国家下拉框数据", notes = "")
    @GetMapping("getExistsOfferItemAreaCountryList/{companyId}")
    public ResponseBo getExistsOfferItemAreaCountryList(@PathVariable("companyId") Long companyId) {
        List<BaseSelectEntity> datas = areaCountryService.getExistsOfferItemAreaCountryList(companyId);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取对应公司下有申请计划的代理所在的 国家下拉框数据", notes = "")
    @GetMapping("getExistsAgentOfferItemAreaCountryList/{companyId}")
    public ResponseBo getExistsAgentOfferItemAreaCountryList(@PathVariable("companyId") Long companyId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<BaseSelectEntity> datas = areaCountryService.getExistsAgentOfferItemAreaCountryList(companyId);
        return new ListResponseBo<>(datas);
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取对应公司下的代理所在的 国家下拉框数据", notes = "")
    @GetMapping("getExistsAgentAreaCountryList/{companyId}")
    public ResponseBo getExistsAgentAreaCountryList(@PathVariable("companyId") Long companyId) {
        if (!SecureUtil.validateCompany(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        List<BaseSelectEntity> datas = areaCountryService.getExistsAgentAreaCountryList(companyId);
        return new ListResponseBo<>(datas);
    }

    /**
     * 国家下拉框数据
     *
     * @return
     * @
     */
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "所有国家下拉框数据", notes = "")
    @GetMapping("getAllAreaCountryList")
    public ResponseBo getAllAreaCountryList() {
        List<AreaCountryVo> datas = areaCountryService.getAllAreaCountryList();
        return new ListResponseBo<>(datas);
    }

    /**
     * 国家附件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "国家附件类型下拉框数据", notes = "")
    @GetMapping("findMediaAndAttachedType")
    public ResponseBo findMediaAndAttachedType() {
        List<Map<String, Object>> datas = areaCountryService.findMediaAndAttachedType();
        return new ListResponseBo<>(datas);
    }

    /**
     * feign调用 根据国家key查找国家id
     *
     * @param keys
     * @return
     * @
     */
/*    @VerifyLogin(IsVerify = false)
    @ApiIgnore
    @GetMapping(value = "getCountryIdByKey")
    public ResponseBo<List<Long>> getCountryIdByKey(@RequestParam List<String> keys)  {
        return new ResponseBo<>(areaCountryService.getCountryIdByKey(keys));
    }*/

    /**
     * 上移下移
     *
     * @param areaCountryDtos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/专业等级管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<AreaCountryDto> areaCountryDtos) {
        areaCountryService.movingOrder(areaCountryDtos);
        return ResponseBo.ok();
    }

    /**
     * @Description :feign调用 根据国家ids查找国家名称map
     * @Param [ids]
     * <AUTHOR>
     */
/*    @ApiIgnore
    @PostMapping(value = "getCountryNamesByIds")
    @VerifyLogin(IsVerify = false)
    public Map<Long, String> getCountryNamesByIds(@RequestBody Set<Long> ids) {
        return areaCountryService.getCountryNamesByIds(ids);
    }*/

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 查询国家附件
     * @Param [voSearchBean]
     * <AUTHOR>
     */
    @ApiOperation(value = "查询国家附件", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/国家管理/查询附件")
    @PostMapping("getItemMedia")
    public ResponseBo<MediaAndAttachedVo> getItemMedia(@RequestBody SearchBean<MediaAndAttachedDto> voSearchBean) {
        List<MediaAndAttachedVo> staffMedia = areaCountryService.getItemMedia(voSearchBean.getData(), voSearchBean);
        Page page = BeanCopyUtils.objClone(voSearchBean, Page::new);
        return new ListResponseBo<>(staffMedia, page);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.MediaAndAttachedDto>
     * @Description : 保存国家附件
     * @Param [mediaAttachedVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存国家附件")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/国家管理/保存附件")
    @PostMapping("upload")
    public ResponseBo<MediaAndAttachedVo> addAgentMedia(@RequestBody @Validated(MediaAndAttachedDto.Add.class) ValidList<MediaAndAttachedDto> mediaAttachedVo) {
        return new ListResponseBo<>(areaCountryService.addItemMedia(mediaAttachedVo));
    }

    /**
     * 枚举国家业务区域
     *
     * @return
     * @
     */
    @ApiOperation(value = "国家业务区域")
    @VerifyLogin(IsVerify = false)
    @GetMapping("getCountryBusinessArea")
    public ResponseBo getCountryBusinessArea() {
        return new ListResponseBo<>(ProjectKeyEnum.enums2Arrays(ProjectKeyEnum.COUNTRY_BUSINESS_AREA));
    }

    /**
     * @Description :feign调用 查询全部 公开首页国家id
     * <AUTHOR>
     */
    @ApiOperation(value = "首页国家下拉框")
    @VerifyPermission(IsVerify = false)
    @GetMapping(value = "getPublicCountryHomeIds")
    @VerifyLogin(IsVerify = false)
    public ResponseBo<SystemPageCountryVo> selectPublicCountryHomeNums() {
        List<SystemPageCountryVo> systemPageCountryDtoList = new ArrayList<>();
        List<Map<String, String>> maps = areaCountryService.selectPublicCountryHomeNums();
        if (GeneralTool.isNotEmpty(maps)) {
            for (Map<String, String> map : maps) {
                SystemPageCountryVo systemPageCountryVo = new SystemPageCountryVo();
                systemPageCountryVo.setName(map.get("name"));
                systemPageCountryVo.setZhCn(map.get("zhCn"));
                systemPageCountryVo.setId(map.get("id"));
                systemPageCountryDtoList.add(systemPageCountryVo);
            }
        }
        return new ListResponseBo<>(systemPageCountryDtoList);
    }

    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @GetMapping("getAreaCode")
    @ApiOperation("区号下拉")
    public ResponseBo getAreaCode() {
        return new ListResponseBo(areaCountryService.getAreaCode());
    }


    /**
     * 常用区号下拉
     * @return
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @GetMapping("getCommonAreaCodes")
    @ApiOperation("常用区号下拉")
    public ResponseBo getCommonAreaCodes() {
        return new ListResponseBo(areaCountryService.getCommonAreaCodes());
    }



    /**
     * 国家下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "获取新闻国家下拉框数据", notes = "")
    @GetMapping("getNewsAreaCountryList")
    public ResponseBo getNewsAreaCountryList() {
        List<BaseSelectEntity> datas = areaCountryService.getNewsAreaCountryList();
        return new ListResponseBo<>(datas);
    }
}
