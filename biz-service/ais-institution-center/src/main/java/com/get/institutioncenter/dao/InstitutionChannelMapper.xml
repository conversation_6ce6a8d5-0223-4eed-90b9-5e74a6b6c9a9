<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionChannelMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionChannel">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_chn" jdbcType="VARCHAR" property="nameChn" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionChannel" keyProperty="id" useGeneratedKeys="true">
    insert into m_institution_channel (id, num, name, 
      name_chn, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{nameChn,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionChannel" keyProperty="id" useGeneratedKeys="true">
    insert into m_institution_channel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameChn != null">
        name_chn,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameChn != null">
        #{nameChn,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getInstitutionChannelSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      SELECT
      m.id,
      CONCAT(
      CASE
      WHEN IFNULL( m.name_chn, '' ) = '' THEN
      m.`name` ELSE  m.name_chn
      END,
      "【",
      GROUP_CONCAT( c.short_name ORDER BY c.id SEPARATOR '，' ),
      "】"
      ) AS fullName,
      m.`name`,
      m.name_chn
      FROM
      m_institution_channel m LEFT JOIN r_institution_channel_company r on r.fk_institution_channel_id = m.id
      LEFT JOIN ais_permission_center.m_company c on c.id = r.fk_company_id
      <if test="companyIds!=null and companyIds.size>0">
          WHERE
          r.fk_company_id IN
          <foreach collection="companyIds" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
      </if>
      GROUP BY m.id
      ORDER BY
      CONVERT (m.NAME USING gbk),
      GROUP_CONCAT(c.id ORDER BY c.id)
  </select>

  <select id="getNameById" parameterType="java.lang.Long" resultType="java.lang.String">
    select
      CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from
      m_institution_channel i
    where
      i.id = #{id}
  </select>
    <select id="getInstitutionProviderChannelIdsByName" resultType="java.lang.Long">
      select
        ip.id
      from
        m_institution_channel ip
      where
          ip.name
          like
          #{channelName}
         or
          ip.name_chn
          like
          #{channelName}
         or
          CONCAT(ip.`name`,'（',ip.name_chn,'）')
          like
          #{channelName}
      ORDER BY
        LENGTH(ip.name),LENGTH(ip.name_chn)
    </select>
  <select id="fuzzQueryChannelName" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      SELECT
      m.id,
      m.NAME
      FROM
      m_institution_channel m
      LEFT JOIN r_institution_channel_company r on r.fk_institution_channel_id = m.id
      LEFT JOIN ais_permission_center.m_company c on c.id = r.fk_company_id
      WHERE
      <if test="companyIds!=null and companyIds.size>0">
          r.fk_company_id IN
          <foreach collection="companyIds" item="item" separator="," open="(" close=")">
              #{item}
          </foreach>
      </if>
      AND (REPLACE (m.NAME, ' ', '') LIKE CONCAT('%', #{keyword}, '%')
      OR REPLACE (m.name_chn, ' ', '') LIKE CONCAT('%', #{keyword}, '%'))
      order by m.NAME
  </select>
    <select id="getInstitutionChannelInformation"
            resultType="com.get.institutioncenter.vo.InstitutionChannelVo">
        SELECT
            i.id,
            i.num,
            i.NAME,
            i.name_chn,
            i.gmt_create,
            i.gmt_create_user,
            i.gmt_modified,
            i.gmt_modified_user,
<!--            GROUP_CONCAT(r.fk_company_id) as fk_company_ids-->
        GROUP_CONCAT(r.fk_company_id ORDER BY mc.view_order DESC) as fk_company_ids
        FROM
        ais_institution_center.m_institution_channel i
        INNER JOIN ais_institution_center.r_institution_channel_company r ON i.id = r.fk_institution_channel_id
        INNER JOIN ais_permission_center.m_company AS mc ON mc.id = r.fk_company_id
        WHERE 1=1
        <if test="companyIds!=null and companyIds.size>0">
            AND r.fk_company_id IN
            <foreach collection="companyIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="institutionChannelDto.fkCompanyId!=null">
            AND fk_institution_channel_id IN (
                SELECT
                    fk_institution_channel_id
                FROM
                    r_institution_channel_company
                    WHERE
                fk_company_id = #{institutionChannelDto.fkCompanyId}
            )
        </if>
        <if test="institutionChannelDto.name!=null and institutionChannelDto.name!=''">
            AND (i.name LIKE CONCAT('%',#{institutionChannelDto.name},'%')
                OR i.name_chn LIKE CONCAT('%',#{institutionChannelDto.name},'%'))
        </if>
        GROUP BY i.id
        ORDER BY mc.view_order DESC,CONVERT(i.name USING gbk)
    </select>
    <select id="queryById" resultType="com.get.institutioncenter.vo.InstitutionChannelVo">
        SELECT
            i.id,
            i.num,
            i. NAME,
            i.name_chn,
            i.gmt_create,
            i.gmt_create_user,
            i.gmt_modified,
            i.gmt_modified_user,
            GROUP_CONCAT(r.fk_company_id) as fk_company_ids
        FROM
        m_institution_channel i
        INNER JOIN r_institution_channel_company r ON i.id = r.fk_institution_channel_id
        WHERE i.id = #{id}
        <if test="companyIds!=null and companyIds.size>0">
            AND r.fk_company_id IN
            <foreach collection="companyIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="getInfo" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            CASE
        WHEN IFNULL(c.name_chn, '') = '' THEN
            CONCAT('【', c.`name`, '】')
        ELSE
            CONCAT(
                '【',
                c.`name`,
                '（',
                c.name_chn,
                '）',
                '】'
            )
        END NAME,
         CASE
        WHEN IFNULL(p.name_chn, '') = '' THEN
            p.`name`
        ELSE
            CONCAT(
                p.`name`,
                '（',
                p.name_chn,
                '）'
            )
        END name_chn
        FROM
            m_institution_channel c
        INNER JOIN r_institution_provider_institution_channel r ON r.fk_institution_channel_id = c.id
        INNER JOIN m_institution_provider p ON r.fk_institution_provider_id = p.id
        WHERE
            c.id = #{channelId}
        AND p.id = #{providerId}
    </select>


</mapper>