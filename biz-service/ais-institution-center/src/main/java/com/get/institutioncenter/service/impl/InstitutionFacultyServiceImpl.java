package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.institutioncenter.dao.*;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.InstitutionFacultyVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.InstitutionFaculty;
import com.get.institutioncenter.entity.MediaAndAttached;
import com.get.institutioncenter.service.IInstitutionFacultyService;
import com.get.institutioncenter.service.IInstitutionService;
import com.get.institutioncenter.service.IMediaAndAttachedService;
import com.get.institutioncenter.service.ITranslationMappingService;
import com.get.institutioncenter.dto.InstitutionFacultyDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/3
 * @TIME: 16:02
 * @Description:
 **/
@Service
public class InstitutionFacultyServiceImpl extends BaseServiceImpl<InstitutionFacultyMapper, InstitutionFaculty> implements IInstitutionFacultyService {
    @Resource
    private InstitutionFacultyMapper institutionFacultyMapper;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private MediaAndAttachedMapper attachedMapper;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private InstitutionCourseMapper institutionCourseMapper;
    @Resource
    private InstitutionCourseFacultyMapper institutionCourseFacultyMapper;
    @Resource
    private IInstitutionService institutionService;
    @Resource
    private ITranslationMappingService translationMappingService;

    @Override
    public List<InstitutionFacultyVo> datas(InstitutionFacultyDto institutionFacultyDto, Page page) {
        QueryWrapper<InstitutionFaculty> wrapper = new QueryWrapper();
        if (GeneralTool.isNotEmpty(institutionFacultyDto)) {
            if (GeneralTool.isNotEmpty(institutionFacultyDto.getName())) {
                wrapper.lambda().and(wrapper_ ->
                        wrapper_.like(InstitutionFaculty::getName, institutionFacultyDto.getName()).or().like(InstitutionFaculty::getNameChn, institutionFacultyDto.getName()));

            }
            if (GeneralTool.isNotEmpty(institutionFacultyDto.getFkInstitutionId())) {
                wrapper.lambda().eq(InstitutionFaculty::getFkInstitutionId, institutionFacultyDto.getFkInstitutionId());
            }
            //example.orderBy("viewOrder").desc().orderBy("name");
            wrapper.orderByDesc("IFNULL(view_order,0)");
            wrapper.orderByAsc("CONVERT(name USING gbk)");
        }
        //获取分页数据
        IPage<InstitutionFaculty> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionFaculty> institutionFacultys = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<InstitutionFacultyVo> convertDatas = new ArrayList<>();

        //学校ids
        Set<Long> institutionIds = institutionFacultys.stream().map(InstitutionFaculty::getFkInstitutionId).collect(Collectors.toSet());
        //根据学校ids获取名称
        Map<Long, String> institutionNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionIds)) {
            institutionNamesByIds = institutionService.getInstitutionNamesByIds(institutionIds);
        }

        for (InstitutionFaculty institutionFaculty : institutionFacultys) {
            InstitutionFacultyVo institutionFacultyVo = BeanCopyUtils.objClone(institutionFaculty, InstitutionFacultyVo::new);
            if (GeneralTool.isNotEmpty(institutionFacultyVo.getFkInstitutionId())) {
                institutionFacultyVo.setInstitutionName(institutionNamesByIds.get(institutionFacultyVo.getFkInstitutionId()));
            }
            String fullName = institutionFacultyVo.getName();
            if (GeneralTool.isNotEmpty(institutionFacultyVo.getNameChn())) {
                fullName = fullName + "（" + institutionFacultyVo.getNameChn() + "）";
            }
            institutionFacultyVo.setFullName(fullName);
            convertDatas.add(institutionFacultyVo);
        }
        return convertDatas;
    }

    @Override
    public Long addInstitutionFaculty(InstitutionFacultyDto institutionFacultyDto) {
        InstitutionFaculty institutionFaculty = BeanCopyUtils.objClone(institutionFacultyDto, InstitutionFaculty::new);
        utilService.updateUserInfoToEntity(institutionFaculty);
        int i = institutionFacultyMapper.insertSelective(institutionFaculty);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return institutionFaculty.getId();
    }

    @Override
    public InstitutionFacultyVo findInstitutionFacultyById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionFaculty institutionFaculty = institutionFacultyMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionFaculty)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionFacultyVo institutionFacultyVo = BeanCopyUtils.objClone(institutionFaculty, InstitutionFacultyVo::new);
        //获取GUID
        String tableName = TableEnum.INSTITUTION_FACULTY.key;
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(tableName);
        attachedVo.setFkTableId(id);
        List<MediaAndAttachedVo> mediaAndAttachedVo = attachedService.getMediaAndAttachedDto(attachedVo);
        institutionFacultyVo.setMediaAndAttachedDtos(mediaAndAttachedVo);
        institutionFacultyVo.setFkTableName(TableEnum.INSTITUTION_FACULTY.key);
        if (GeneralTool.isNotEmpty(institutionFacultyVo.getFkInstitutionId())) {
            institutionFacultyVo.setInstitutionName(institutionMapper.getInstitutionNameById(institutionFacultyVo.getFkInstitutionId()));
        }
        String fullName = institutionFacultyVo.getName();
        if (GeneralTool.isNotEmpty(institutionFacultyVo.getNameChn())) {
            fullName = fullName + "（" + institutionFacultyVo.getNameChn() + "）";
        }
        institutionFacultyVo.setFullName(fullName);
        return institutionFacultyVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        //TODO 改过
        //InstitutionFaculty institutionFaculty = findInstitutionFacultyById(id);
        InstitutionFacultyVo institutionFaculty = findInstitutionFacultyById(id);
        if (institutionFaculty == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Integer count = institutionCourseFacultyMapper.getCountByFaculty(id);
        if (count != 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("faculty_course_data_association"));
        }
        int i = institutionFacultyMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }

        //同时删除该表id下的所有媒体附件
        List<MediaAndAttached> mediaAndAttachedByTableIds = attachedService.findMediaAndAttachedByTableId(id, TableEnum.INSTITUTION_FACULTY.key, null);
        for (MediaAndAttached mediaAndAttached : mediaAndAttachedByTableIds) {
            attachedService.deleteMediaAttached(mediaAndAttached.getId());
        }

        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_FACULTY.key, id);
    }

    @Override
    public FileDto upload(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        //返回上传路劲
        //文件上传
        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.INSTITUTIONCENTER);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }

        if (GeneralTool.isEmpty(result.getData())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        return result.getData().get(0);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public InstitutionFacultyVo updateInstitutionFaculty(InstitutionFacultyDto institutionFacultyDto) {
        if (institutionFacultyDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(institutionFacultyDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionFaculty rs = institutionFacultyMapper.selectById(institutionFacultyDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionFaculty institutionFaculty = BeanCopyUtils.objClone(institutionFacultyDto, InstitutionFaculty::new);
        utilService.updateUserInfoToEntity(institutionFaculty);
        institutionFacultyMapper.updateById(institutionFaculty);
        return findInstitutionFacultyById(institutionFaculty.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addInstitutionFacultyMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            String tableName = "m_institution_faculty";
            mediaAndAttachedDto.setFkTableName(tableName);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<InstitutionFacultyVo> getByfkInstitutionId(Long id) {
        LambdaQueryWrapper<InstitutionFaculty> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(id)) {
            wrapper.eq(InstitutionFaculty::getFkInstitutionId, id);
        }
        List<InstitutionFaculty> institutionFacultys = institutionFacultyMapper.selectList(wrapper);
        List<InstitutionFacultyVo> convertDatas = new ArrayList<>();
        for (InstitutionFaculty igy : institutionFacultys) {
            InstitutionFacultyVo institutionFacultyVo = BeanCopyUtils.objClone(igy, InstitutionFacultyVo::new);
            String fullName = institutionFacultyVo.getName();
            if (GeneralTool.isNotEmpty(institutionFacultyVo.getNameChn())) {
                fullName = fullName + "（" + institutionFacultyVo.getNameChn() + "）";
            }
            institutionFacultyVo.setFullName(fullName);
            convertDatas.add(institutionFacultyVo);
        }
        return convertDatas;
    }

    @Override
    public List<Map<String, Object>> findMediaAndAttachedType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.INSTITUTIONFACULTY);
    }

    @Override
    public List<MediaAndAttachedVo> getInstitutionFacultyMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.INSTITUTION_FACULTY.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<InstitutionFacultyVo> getInstitutionFacultySelectByInstitutionIdList(List<Long> institutionIdList) {
        List<InstitutionFacultyVo> resultList = new ArrayList<>();
        for (Long institutionId : institutionIdList) {
            List<InstitutionFacultyVo> institutionFacultyDtoList = getByfkInstitutionId(institutionId);
            if (GeneralTool.isNotEmpty(institutionFacultyDtoList)) {
                resultList.addAll(institutionFacultyDtoList);
            }
        }
        return resultList;
    }


    @Override
    public Map<Long, String> getInstitutionFacultyNameByCourseIds(Set<Long> courseIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(courseIds)) {
            return map;
        }
        List<InstitutionFacultyVo> institutionFacultyNameByCourseIds = institutionFacultyMapper.getInstitutionFacultyNameByCourseIds(courseIds);
        if (GeneralTool.isEmpty(institutionFacultyNameByCourseIds)) {
            return map;
        }
        for (InstitutionFacultyVo institutionFacultyNameByCourseId : institutionFacultyNameByCourseIds) {
            map.put(institutionFacultyNameByCourseId.getFkInstitutionCourseId(), institutionFacultyNameByCourseId.getFullName());
        }
        return map;
    }

    @Override
    public String getInstitutionFacultyNameByid(Long id) {
        return  institutionFacultyMapper.getInstitutionFacultyNameByid(id);
    }

    @Override
    public Map<Long, String> getInstitutionFacultyNameByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
        LambdaQueryWrapper<InstitutionFaculty>lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(InstitutionFaculty::getId,ids);
        List<InstitutionFaculty> institutionFaculties = institutionFacultyMapper.selectList(lambdaQueryWrapper);

        institutionFaculties.stream().forEach(d->map.put(d.getId(), d.getName()+"("+d.getNameChn()+")"));
        return map;
    }

    @Override
    public List<BaseSelectEntity> getInstitutionFacultyList(String keyword, Long institutionId) {
        return institutionFacultyMapper.getFacultyList(keyword,institutionId);
    }


}
