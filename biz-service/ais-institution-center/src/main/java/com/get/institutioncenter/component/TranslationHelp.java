package com.get.institutioncenter.component;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.annotion.TableDto;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.TranslationMapper;
import com.get.institutioncenter.dao.TranslationMappingMapper;
import com.get.institutioncenter.entity.InstitutionTranslation;
import com.get.institutioncenter.entity.InstitutionTranslationMapping;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.persistence.Column;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/11/26 11:03
 */
@Component
public class TranslationHelp {
    @Resource
    private TranslationMapper translationMapper;
    @Resource
    private TranslationMappingMapper translationMappingMapper;

    public List<?> translation(List<?> objectList, String language) {
        //判断对象是否为空
        if (GeneralTool.isEmpty(objectList)) {
            return objectList;
        }
        //获取传参对象的类<?>，利用反射机制获取所有的
        Class clazz = objectList.get(0).getClass();

        //通过YEntity类获取对象的id的field(字段名字)
        Field idField = null;
        try {
            Class yEntityClazz = BaseEntity.class;
            idField = yEntityClazz.getDeclaredField("id");
        } catch (NoSuchFieldException e) {
            e.printStackTrace();
        }
        //获取传参的类的所有字段field，存放到fieldList
        String tableName = null;
        List<Field> fieldList = new ArrayList<>();

        while (clazz != null) {
            //实体类的注解
            TableName annotation = (TableName) clazz.getAnnotation(TableName.class);
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            //通过@Table注解获得实体类对应的数据表的表名
            if (GeneralTool.isNotEmpty(annotation)) {
                tableName = annotation.value();
                break;
            }
            clazz = clazz.getSuperclass();

        }
        List<Map<String, Object>> mapList = new ArrayList<>();
        //存在数据库名和字段列表和id字段，则到数据库查找相应的翻译,获取当前类的所有字段
        if (fieldList.size() > 0 && GeneralTool.isNotEmpty(idField)) {
            // -- start 当前类
            int fitstCount = 0;
            for (Object object : objectList) {
                Map<String, Object> firstMap = new HashMap<>(16);
                //设置实例变量为可修改状态
                idField.setAccessible(true);
                //定义二级map
                Map<String, Object> secondMap = new HashMap<>(16);
                //定义三级map
                List<Map<String, Object>> thirdDto = new ArrayList<>();
                Long tableId = null;
                //定义二级map的key,自增
                int secondCount = 0;
                //定义三级的map，自增
                //循环当前类的字段
                for (Field field : fieldList) {
                    field.setAccessible(true);
                    Map<String, Object> secondMapDto = new HashMap<>(16);
                    try {
                        tableId = (Long) idField.get(object);
                        if (GeneralTool.isNotEmpty(tableId)) {
                            Annotation annotation = field.getAnnotation(Column.class);
                            if (GeneralTool.isNotEmpty(annotation)) {
                                //别名
                                secondMap.put(String.valueOf(secondCount), ((Column) annotation).name().toLowerCase());
                                secondCount++;
                            }
                            //赋值连表查询
                            Annotation annotationDto = field.getAnnotation(TableDto.class);
                            if (GeneralTool.isNotEmpty(annotationDto)) {
                                String tableNameDto = ((TableDto) annotationDto).tableName();
                                String columnDto = ((TableDto) annotationDto).columnDto();
                                String columnDtoMainId = ((TableDto) annotationDto).columnDtoMainId();
                                String entityColumnDto = ((TableDto) annotationDto).entityColumnDto();
                                secondMapDto.put("tableNameDto", tableNameDto);
                                secondMapDto.put("columnDto", columnDto);
                                secondMapDto.put("columnDtoMainId", columnDtoMainId);
                                secondMapDto.put("entityColumnDto", entityColumnDto);
                                thirdDto.add(secondMapDto);
                            }

                        }
                    } catch (IllegalAccessException e) {
                        continue;
                    }

                }  // end 循环当前类的字段  Fields
                if (GeneralTool.isNotEmpty(tableId)) {
                    getFirstMap(firstMap, tableId, tableName, language, secondMap, thirdDto);
                    fitstCount++;
                }
                mapList.add(firstMap);
            } // end  当前类的List
            //获取s_translation 主表的内容以及字段名字
            List<Map<String, Object>> objectsList = translationMapper.selectByListMaps(mapList);
            //对结果集进行分析并赋值
            analysisResultSet(objectList, objectsList, idField, fieldList);
            //对结果集进行分析 end
        }
        return objectList;
    }

    public <T> T getTranslation(String value, String column, String tableName, String language) {
        T t = null;
        try {
            LambdaQueryWrapper<InstitutionTranslationMapping> wrapper = new LambdaQueryWrapper();
            wrapper.eq(InstitutionTranslationMapping::getFkTableName, tableName);
            wrapper.eq(InstitutionTranslationMapping::getFkColumnName, column);
            //获取翻译字段表id
            List<InstitutionTranslationMapping> translationMappingssDto = translationMappingMapper.selectList(wrapper);
            if (GeneralTool.isNotEmpty(translationMappingssDto)) {
                //根据传入的参数或者对应的主表id
                Long tableId = null;
                List<Long> dtoIds = translationMappingMapper.getTableId(value, tableName, column);
                if (GeneralTool.isNotEmpty(dtoIds)) {
                    tableId = dtoIds.get(0);

                    LambdaQueryWrapper<InstitutionTranslation> wrapper1 = new LambdaQueryWrapper();
                    wrapper1.eq(InstitutionTranslation::getFkTableName, tableName);
                    wrapper1.eq(InstitutionTranslation::getFkTableId, tableId);
                    wrapper1.eq(InstitutionTranslation::getFkTranslationMappingId, translationMappingssDto.get(0).getId());
                    wrapper1.eq(InstitutionTranslation::getLanguageCode, language);

                    List<InstitutionTranslation> translations = translationMapper.selectList(wrapper1);
                    if (GeneralTool.isNotEmpty(translations)) {
                        String valueTrans = translations.get(0).getTranslation();
                        t = (T) valueTrans;
                    }
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return t;
    }


    /**
     * @param field                  反射对象字段
     * @param obj                    反射对象
     * @param tableName              表名
     * @param tableId                表名id
     * @param fkTranslationMappingId 翻译字段表id
     * @param language               语言
     * <AUTHOR>
     * @Time 2021.06.09
     */
    public void setField(Field field, Object obj, String tableName, Long tableId, Long fkTranslationMappingId, String language) {
        try {
            //[begin] 若是存在相应的TranslationMapping映射字段，则到Translation表查找对应的翻译内容
            LambdaQueryWrapper<InstitutionTranslation> wrapper1 = new LambdaQueryWrapper();
            //对应表名
            wrapper1.eq(InstitutionTranslation::getFkTableName, tableName);
            //对应的对象id
            wrapper1.eq(InstitutionTranslation::getFkTableId, tableId);
            //对应的TranslationMapping映射的id
            wrapper1.eq(InstitutionTranslation::getFkTranslationMappingId, fkTranslationMappingId);
            //所需要翻译的语言类型 zh-hk：香港
            wrapper1.eq(InstitutionTranslation::getLanguageCode, language);

            List<InstitutionTranslation> translations = translationMapper.selectList(wrapper1);
            //若是可以查找到相应的翻译值，则用翻译值替换原本object的值
            if (GeneralTool.isNotEmpty(translations)) {
                String value = translations.get(0).getTranslation();
                if (GeneralTool.isNotEmpty(value)) {
                    //设置实例变量为可修改状态
                    field.setAccessible(true);
                    field.set(obj, value);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    /**
     * 分析结果集，并对其进行赋值处理
     *
     * @param objectList  被处理得对象
     * @param objectsList 结果集对象
     * @param idField     主键
     * @param fieldList   反射类
     * @return
     */
    private List<?> analysisResultSet(List<?> objectList, List<Map<String, Object>> objectsList, Field idField, List<Field> fieldList) {
        for (int obj = 0; obj < objectsList.size(); obj++) {
            for (String key : objectsList.get(obj).keySet()) {
                Object value = objectsList.get(obj).get(key);
                Long id = (Long) objectsList.get(obj).get("id");
                if (GeneralTool.isNotEmpty(value) && GeneralTool.isNotEmpty(id)) {
                    boolean checkFinish = false;
                    for (Object object : objectList) {
                        try {
                            if (!checkFinish) {
                                //获取主表id，如果主表一致则进行赋值
                                Long tableId = (Long) idField.get(object);
                                for (Field field : fieldList) {
                                    Annotation annotation = field.getAnnotation(Column.class);
                                    Annotation annotationDto = field.getAnnotation(TableDto.class);
                                    String columnName = null;
                                    if (GeneralTool.isNotEmpty(annotation)) {
                                        //获取表格有值的字段
                                        columnName = ((Column) annotation).name();
                                    } else if (GeneralTool.isNotEmpty(annotationDto)) {
                                        //获取表格有值的字段
                                        columnName = ((TableDto) annotationDto).entityColumnDto();
                                    }
                                    //对实体类赋值
                                    if (GeneralTool.isNotEmpty(columnName) && key.equals(columnName) && tableId.equals(objectsList.get(obj).get("id")) && GeneralTool.isNotEmpty(value)) {
                                        field.setAccessible(true);
                                        field.set(object, value);
                                        checkFinish = true;
                                        break;
                                    }
                                }
                            } else {
                                break;
                            }

                        } catch (IllegalAccessException e) {
                            continue;
                        }

                    }
                } else {
                    continue;
                }
            }

        } //对结果集进行分析 end

        return objectList;
    }

    /**
     * 对first进行赋值
     *
     * @param firstMap
     * @param tableId
     * @param tableName
     * @param language
     * @param secondMap
     * @param thirdDto
     */
    private void getFirstMap(Map<String, Object> firstMap, Long tableId, String tableName, String language, Map<String, Object> secondMap, List<Map<String, Object>> thirdDto) {
        firstMap.put("id", tableId);
        firstMap.put("tableName", tableName);
        firstMap.put("languageFromHeader", language);
        firstMap.put("map", secondMap);
        firstMap.put("mapDto", thirdDto);
    }

}
