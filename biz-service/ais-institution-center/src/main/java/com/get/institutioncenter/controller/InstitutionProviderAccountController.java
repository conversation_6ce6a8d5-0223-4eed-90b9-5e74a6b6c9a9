package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.institutioncenter.vo.InstitutionProviderAccountVo;
import com.get.institutioncenter.service.InstitutionProviderAccountService;
import com.get.institutioncenter.dto.InstitutionProviderAccountDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Api("提供商账户管理")
@RestController
@RequestMapping("/institution/institutionProviderAccount")
public class InstitutionProviderAccountController {

    @Resource
    private InstitutionProviderAccountService institutionProviderAccountService;

    @ApiOperation(value = "提供商账户列表")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/提供商账户管理/提供商账户列表")
    @PostMapping("getProviderAccountList")
    public ResponseBo<InstitutionProviderAccountVo> getProviderAccountList(@RequestBody SearchBean<InstitutionProviderAccountDto> searchBean){
        return institutionProviderAccountService.getProviderAccountList(searchBean.getData(),searchBean);
    }

    @ApiOperation(value = "账户详情")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/提供商账户管理/账户详情")
    @GetMapping("{id}")
    public ResponseBo<InstitutionProviderAccountVo> findInfoById(@PathVariable("id") Long id) {
        return new ResponseBo<>(institutionProviderAccountService.findInfoById(id));
    }

    @ApiOperation(value = "快捷设置首选合同")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/提供商账户管理/快捷设置首选合同")
    @GetMapping("quickFirstContractAccount")
    public SaveResponseBo quickFirstContractAccount(@RequestParam("providerId") Long providerId,@RequestParam("accountId") Long accountId) {
        return institutionProviderAccountService.quickFirstContractAccount(providerId,accountId);
    }

    @ApiOperation(value = "快速激活或屏蔽合同账户")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/提供商账户管理/快速激活或屏蔽合同账户")
    @GetMapping("quickActivationOrMask")
    public SaveResponseBo quickActivationOrMask(@RequestParam("providerId") Long providerId,@RequestParam("accountId") Long accountId,@RequestParam("status") Boolean status) {
        return institutionProviderAccountService.quickActivationOrMask(providerId,accountId,status);
    }

    @ApiOperation(value = "更新")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/提供商账户管理/更新")
    @PostMapping("update")
    public ResponseBo<InstitutionProviderAccountVo> update(@RequestBody @Validated(value = {BaseVoEntity.Update.class}) InstitutionProviderAccountDto providerAccountVo) {
        return new ResponseBo<>(institutionProviderAccountService.update(providerAccountVo));
    }


    @ApiOperation(value = "新增")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/提供商账户管理/新增")
    @PostMapping("add")
    public SaveResponseBo add(@RequestBody @Validated(value = {BaseVoEntity.Add.class}) InstitutionProviderAccountDto providerAccountVo) {
        return institutionProviderAccountService.add(providerAccountVo);
    }


    @ApiOperation(value = "删除")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/提供商账户管理/删除")
    @PostMapping("delete/{id}")
    public SaveResponseBo delete(@PathVariable("id") Long id) {
        return institutionProviderAccountService.delete(id);
    }

    @ApiOperation(value = "提供商合同账户列表账户重复提示")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/提供商账户管理/提供商合同账户列表账户重复提示")
    @GetMapping("getContractAccountExist")
    public ResponseBo<String> getContractAccountExist(@RequestParam(value = "id", required = false) Long id, @RequestParam("providerId") Long providerId, @RequestParam("bankAccount") String bankAccount,
                                                      @RequestParam("bankAccountNum") String bankAccountNum) {
        return institutionProviderAccountService.getContractAccountExist(id, providerId, bankAccount, bankAccountNum);
    }



}
