package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.CourseTypeGroupCourseTypeMapper;
import com.get.institutioncenter.dao.CourseTypeGroupMapper;
import com.get.institutioncenter.dao.CourseTypeMapper;
import com.get.institutioncenter.vo.CourseTypeGroupVo;
import com.get.institutioncenter.entity.CourseType;
import com.get.institutioncenter.entity.CourseTypeGroup;
import com.get.institutioncenter.entity.CourseTypeGroupCourseType;
import com.get.institutioncenter.service.ICourseTypeGroupService;
import com.get.institutioncenter.service.ITranslationMappingService;
import com.get.institutioncenter.dto.CourseTypeGroupDto;
import com.get.salecenter.vo.SelItem;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/12/7
 * @TIME: 11:31
 * @Description:
 **/
@Service
public class CourseTypeGroupServiceImpl extends BaseServiceImpl<CourseTypeGroupMapper, CourseTypeGroup> implements ICourseTypeGroupService {
    @Resource
    private CourseTypeGroupMapper courseTypeGroupMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private CourseTypeGroupCourseTypeMapper courseTypeGroupCourseTypeMapper;
    @Resource
    private CourseTypeMapper courseTypeMapper;
    @Resource
    private ITranslationMappingService translationMappingService;


    /**
     * @return
     * @Description：列表数据
     * @Param
     * @Date 12:10 2021/4/27
     * <AUTHOR>
     */
    @Override
    public List<CourseTypeGroupVo> getCourseTypeGroups(CourseTypeGroupDto courseTypeGroupDto, Page page) {

        LambdaQueryWrapper<CourseTypeGroup> wrapper = new LambdaQueryWrapper();
//        if (GeneralTool.isNotEmpty(courseTypeGroupDto)) {
//            if (GeneralTool.isNotEmpty(courseTypeGroupDto.getKeyWord())) {
//                wrapper.like(CourseTypeGroup::getTypeGroupName, courseTypeGroupDto.getKeyWord());
//            }
//            if(GeneralTool.isNotEmpty(courseTypeGroupDto.getPublicLevel())){
//
//            }
//        }
//        wrapper.orderByDesc(CourseTypeGroup::getViewOrder);
        //获取分页数据
        IPage<CourseTypeGroup> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())));
        List<String> strPublic = new ArrayList<>();
        if(GeneralTool.isNotEmpty(courseTypeGroupDto.getPublicLevel())){
            strPublic = Arrays.asList(courseTypeGroupDto.getPublicLevel().split(","));
        }


        List<CourseTypeGroup> courseTypeGroupList = courseTypeGroupMapper.getBdyData(pages, courseTypeGroupDto,strPublic);
        page.setAll((int) pages.getTotal());
        List<CourseTypeGroupVo> convertDatas = new ArrayList<>();
        for (CourseTypeGroup courseTypeGroup : courseTypeGroupList) {
            CourseTypeGroupVo courseTypeGroupVo = BeanCopyUtils.objClone(courseTypeGroup, CourseTypeGroupVo::new);
            courseTypeGroupVo.setFkTableName(TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key);
            courseTypeGroupVo.setModeName(ProjectExtraEnum.getValueByKey(courseTypeGroup.getMode(), ProjectExtraEnum.PATTERN_TYPE));
            StringJoiner stringJoiner = new StringJoiner(" ");
            if (GeneralTool.isNotEmpty(courseTypeGroupVo.getPublicLevel())) {
                String[] result = courseTypeGroupVo.getPublicLevel().split(",");
                for (String name : result) {
                    stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
                }
                courseTypeGroupVo.setPublicLevelName(stringJoiner.toString());
            }
            convertDatas.add(courseTypeGroupVo);
        }
        return convertDatas;
    }

    /**
     * @Description：课程类型组别详情
     * @Param
     * @Date 12:07 2021/4/27
     * <AUTHOR>
     */
    @Override
    public CourseTypeGroupVo findCourseTypeGroupById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        CourseTypeGroup courseTypeGroup = courseTypeGroupMapper.selectById(id);
        if (GeneralTool.isEmpty(courseTypeGroup)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        CourseTypeGroupVo courseTypeGroupVo = BeanCopyUtils.objClone(courseTypeGroup, CourseTypeGroupVo::new);
        courseTypeGroupVo.setFkTableName(TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key);
        courseTypeGroupVo.setModeName(ProjectExtraEnum.getValueByKey(courseTypeGroup.getMode(), ProjectExtraEnum.PATTERN_TYPE));
        return courseTypeGroupVo;
    }


    /**
     * @return
     * @Description：删除类型组别
     * @Param
     * @Date 12:12 2021/4/27
     * <AUTHOR>
     */
    @Override
    public void delete(Long id) {
        //TODO 改过
        //CourseTypeGroup courseTypeGroup = findCourseTypeGroupById(id);
        CourseTypeGroupVo courseTypeGroup = findCourseTypeGroupById(id);
        if (courseTypeGroup == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        boolean success = courseTypeGroupCourseTypeMapper.isExistByCourseTypeGroup(courseTypeGroup.getId());
        if (!success) {
            int i = courseTypeGroupMapper.deleteById(id);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("courseTypeGroup_courseType_data_association"));
        }
        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_COURSE_TYPE_GROUP.key, id);
    }

    @Override
    public List<Map<String, Object>> findModeType() {
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.PATTERN_TYPE);
    }

    @Override
    public List<CourseTypeGroup> getCourseTypeListByName() {
        LambdaQueryWrapper<CourseTypeGroup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(CourseTypeGroup::getMode, 3);
        List<CourseTypeGroup> courseTypeGroups = courseTypeGroupMapper.selectList(lambdaQueryWrapper);
        return courseTypeGroups;
    }

    @Override
    public String getGroupTypeIdsByCourseTypeIds(String fkInstitutionCourseTypeIds) {
        List<String> ids = Arrays.asList(fkInstitutionCourseTypeIds.split(","));
        return courseTypeGroupMapper.getGroupTypeIdsByCourseTypeIds(ids);
    }

    @Override
    public Map<Long, String> getCourseGroupTypeNameByIds(Set<Long> ids) {
        Map<Long,String> map = new HashMap<>();
        LambdaQueryWrapper<CourseTypeGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CourseTypeGroup::getId,ids);
        List<CourseTypeGroup> courseTypeGroupList = courseTypeGroupMapper.selectList(wrapper);
        for(CourseTypeGroup courseTypeGroup : courseTypeGroupList){
            map.put(courseTypeGroup.getId(),courseTypeGroup.getTypeGroupName());
        }
        return map;
    }

    @Override
    public Map<Long, String> getCourseGroupTypeNameChnByIds(Set<Long> ids) {
        if(GeneralTool.isEmpty(ids)){
            return null;
        }
        Map<Long,String> map = new HashMap<>();
        LambdaQueryWrapper<CourseTypeGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CourseTypeGroup::getId,ids);
        List<CourseTypeGroup> courseTypeGroupList = courseTypeGroupMapper.selectList(wrapper);
        for(CourseTypeGroup courseTypeGroup : courseTypeGroupList){
            map.put(courseTypeGroup.getId(),courseTypeGroup.getTypeGroupNameChn());
        }
        return map;
    }

    @Override
    public Map<Long, String> getCourseGroupTypeFullNameByIds(Set<Long> ids) {
        Map<Long,String> map = new HashMap<>();
        LambdaQueryWrapper<CourseTypeGroup> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CourseTypeGroup::getId,ids);
        List<CourseTypeGroup> courseTypeGroupList = courseTypeGroupMapper.selectList(wrapper);
        for(CourseTypeGroup courseTypeGroup : courseTypeGroupList){
            StringBuffer sb = new StringBuffer();
            sb.append(courseTypeGroup.getTypeGroupName())
                    .append("（")
                    .append(courseTypeGroup.getTypeGroupNameChn())
                    .append("）");
            map.put(courseTypeGroup.getId(),sb.toString());
        }
        return map;
    }

    @Override
    public Map<Long, String> getCourseTypeNamesByCourseGroupTypeIds(Set<Long> ids) {
        Map<Long,String> map = new HashMap<>();
        LambdaQueryWrapper<CourseTypeGroupCourseType> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(CourseTypeGroupCourseType::getFkCourseTypeGroupId,ids);
        List<CourseTypeGroupCourseType> courseTypeGroupList = courseTypeGroupCourseTypeMapper.selectList(wrapper);
        if(GeneralTool.isNotEmpty(courseTypeGroupList))
        {
            List<Long> courseTypeIds = courseTypeGroupList.stream().map(CourseTypeGroupCourseType::getFkCourseTypeId).collect(Collectors.toList());
            if(GeneralTool.isNotEmpty(courseTypeIds))
            {
                List<CourseType> list = courseTypeMapper.selectBatchIds(courseTypeIds);
                for(CourseType courseType : list){
                    map.put(courseType.getId(),courseType.getTypeName());
                }
            }
        }
        return map;
    }

    /**
     * @Description：修改组别信息
     * @Param
     * @Date 12:31 2021/4/27
     * <AUTHOR>
     */
    @Override
    public CourseTypeGroupVo updateCourseType(CourseTypeGroupDto courseTypeGroupDto) {
        if (courseTypeGroupDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(courseTypeGroupDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        CourseTypeGroup ct = courseTypeGroupMapper.selectById(courseTypeGroupDto.getId());
        if (ct == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        CourseTypeGroup courseTypeGroup = BeanCopyUtils.objClone(courseTypeGroupDto, CourseTypeGroup::new);
        if (validateUpdate(courseTypeGroupDto)) {
            utilService.updateUserInfoToEntity(courseTypeGroup);
            courseTypeGroupMapper.updateById(courseTypeGroup);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return findCourseTypeGroupById(courseTypeGroup.getId());
    }


    /**
     * @Description：批量新增类型组别信息
     * @Param
     * @Date 12:51 2021/4/27
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<CourseTypeGroupDto> courseTypeGroupDtos) {
        for (CourseTypeGroupDto courseTypeGroupDto : courseTypeGroupDtos) {
            if (GeneralTool.isEmpty(courseTypeGroupDto.getId())) {
                if (validateAdd(courseTypeGroupDto)) {
                    CourseTypeGroup courseTypeGroup = BeanCopyUtils.objClone(courseTypeGroupDto, CourseTypeGroup::new);
                    courseTypeGroup.setViewOrder(courseTypeGroupMapper.getMaxViewOrder());
                    utilService.updateUserInfoToEntity(courseTypeGroup);
                    courseTypeGroupMapper.insert(courseTypeGroup);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("courseTypeGroup_course_data_association"));
                }
            } else {
                if (validateUpdate(courseTypeGroupDto)) {
                    CourseTypeGroup courseTypeGroup = BeanCopyUtils.objClone(courseTypeGroupDto, CourseTypeGroup::new);
                    utilService.updateUserInfoToEntity(courseTypeGroup);
                    courseTypeGroupMapper.updateById(courseTypeGroup);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
                }
            }
        }
    }

    /**
     * @Description：上移下移
     * @Param
     * @Date 14:20 2021/4/27
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<CourseTypeGroupDto> courseTypeGroupDtos) {
        if (GeneralTool.isEmpty(courseTypeGroupDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        CourseTypeGroup ro = BeanCopyUtils.objClone(courseTypeGroupDtos.get(0), CourseTypeGroup::new);
        Integer oneorder = ro.getViewOrder();
        CourseTypeGroup rt = BeanCopyUtils.objClone(courseTypeGroupDtos.get(1), CourseTypeGroup::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        courseTypeGroupMapper.updateById(ro);
        courseTypeGroupMapper.updateById(rt);
    }


    /**
     * @Description：课程类型组别下拉框数据
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getCourseTypeGroupList() {
        List<BaseSelectEntity> baseSelectEntitys = courseTypeGroupMapper.getCourseTypeGroupList();
        for (BaseSelectEntity baseSelectEntity : baseSelectEntitys) {
            if (GeneralTool.isNotEmpty(baseSelectEntity.getMode())) {
                StringBuffer sb = new StringBuffer();
                sb.append("【").append(ProjectExtraEnum.getValueByKey(baseSelectEntity.getMode(), ProjectExtraEnum.PATTERN_TYPE))
                        .append("】").append(baseSelectEntity.getName());
                baseSelectEntity.setName(sb.toString());
            }
        }
        return baseSelectEntitys;
    }

    @Override
    public List<BaseSelectEntity> getCourseGroupList(String keyword) {
        return courseTypeGroupMapper.getCourseGroupList(keyword);
    }

    @Override
    public Map<Long, String> getCourseGroupByIds(Set<Long> ids) {
        if (ids.isEmpty()) {
            return Collections.emptyMap();
        }
        List<SelItem> group = courseTypeGroupMapper.getCourseGroupByIds(ids);
        if (group.isEmpty()) {
            return Collections.emptyMap();
        }
        return group.stream().collect(HashMap<Long, String>::new, (m, v) -> m.put(v.getKeyId(), String.valueOf(v.getVal())), HashMap::putAll);
    }

    /**
     * @Description：课程类型组别下拉框数据(mode=3)
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getCourseTypeGroupListModeThree() {
        List<BaseSelectEntity> baseSelectEntitys = courseTypeGroupMapper.getCourseTypeGroupListModeThree();
//        for(BaseSelectEntity baseSelectEntity:baseSelectEntitys){
//            if(GeneralTool.isNotEmpty(baseSelectEntity.getMode())){
//                StringBuffer sb = new StringBuffer();
//                sb.append("【").append(ProjectExtraEnum.getValueByKey(baseSelectEntity.getMode(),ProjectExtraEnum.PATTERN_TYPE))
//                        .append("】").append(baseSelectEntity.getName());
//                baseSelectEntity.setName(sb.toString());
//            }
//        }
        return baseSelectEntitys;
    }

    @Override
    public String getCourseTypeGroupNameByCourseTypeId(Long courseTypeId) {
        return courseTypeGroupMapper.getTypeGroupNameByProviderId(courseTypeId);
    }

    @Override
    public String getCourseTypeGroupNameById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return courseTypeGroupMapper.getCourseTypeGroupNameById(id);
    }

    @Override
    public List<Long> getCourseTypeIdByCourseTypeId(Long courseTypeId) {
        LambdaQueryWrapper<CourseTypeGroupCourseType> wrapper = new LambdaQueryWrapper();
        wrapper.eq(CourseTypeGroupCourseType::getFkCourseTypeId, courseTypeId);
        List<CourseTypeGroupCourseType> courseTypeGroupList = courseTypeGroupCourseTypeMapper.selectList(wrapper);
        return courseTypeGroupList.stream().map(CourseTypeGroupCourseType::getFkCourseTypeGroupId).collect(Collectors.toList());
    }

    private boolean validateAdd(CourseTypeGroupDto courseTypeGroupDto) {
        LambdaQueryWrapper<CourseTypeGroup> wrapper = new LambdaQueryWrapper();
        wrapper.eq(CourseTypeGroup::getTypeGroupName, courseTypeGroupDto.getTypeGroupName())
                .eq(CourseTypeGroup::getMode, courseTypeGroupDto.getMode());
        List<CourseTypeGroup> list = this.courseTypeGroupMapper.selectList(wrapper);
        return GeneralTool.isEmpty(list);
    }

    /**
     * @Description：校验是否有同名分组
     * @Param
     * @Date 12:39 2021/4/27
     * <AUTHOR>
     */
    private boolean validateUpdate(CourseTypeGroupDto courseTypeGroupDto) {
        LambdaQueryWrapper<CourseTypeGroup> wrapper = new LambdaQueryWrapper();
        wrapper.eq(CourseTypeGroup::getTypeGroupName, courseTypeGroupDto.getTypeGroupName())
                .eq(CourseTypeGroup::getMode, courseTypeGroupDto.getMode());
        List<CourseTypeGroup> list = this.courseTypeGroupMapper.selectList(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(courseTypeGroupDto.getId());
    }
}
