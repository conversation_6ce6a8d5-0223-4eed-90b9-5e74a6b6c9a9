package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.institutioncenter.dao.*;
import com.get.institutioncenter.dto.MediaAndAttachedDto;
import com.get.institutioncenter.vo.InstitutionZoneVo;
import com.get.institutioncenter.vo.MediaAndAttachedVo;
import com.get.institutioncenter.entity.InstitutionZone;
import com.get.institutioncenter.entity.MediaAndAttached;
import com.get.institutioncenter.service.*;
import com.get.institutioncenter.dto.InstitutionZoneDto;
import com.get.institutioncenter.dto.query.InstitutionZoneQueryDto;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2021/1/15
 * @TIME: 15:39
 * @Description:
 **/
@Service
public class InstitutionZoneServiceImpl extends BaseServiceImpl<InstitutionZoneMapper, InstitutionZone> implements IInstitutionZoneService {
    @Resource
    private InstitutionCourseZoneMapper institutionCourseZoneMapper;
    @Resource
    private InstitutionZoneMapper institutionZoneMapper;
    @Resource
    private InstitutionMapper institutionMapper;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private UtilService utilService;
    @Resource
    private AreaCityMapper areaCityMapper;
    @Resource
    private AreaCountryMapper areaCountryMapper;
    @Resource
    private AreaStateMapper areaStateMapper;
    @Resource
    private InstitutionCourseMapper institutionCourseMapper;
    @Resource
    @Lazy
    private IInstitutionService institutionService;
    @Resource
    private IAreaCountryService areaCountryService;
    @Resource
    private IAreaStateService areaStateService;
    @Resource
    @Lazy
    private IAreaCityService areaCityService;
    @Resource
    private ITranslationMappingService translationMappingService;

    @Override
    public List<InstitutionZoneVo> datas(InstitutionZoneQueryDto institutionZoneVo, Page page) {
//        LambdaQueryWrapper<InstitutionZone> wrapper = new LambdaQueryWrapper();
        QueryWrapper<InstitutionZone> wrapper = new QueryWrapper();
        if (GeneralTool.isNotEmpty(institutionZoneVo)) {
            if (GeneralTool.isNotEmpty(institutionZoneVo.getName())) {
                wrapper.lambda().and(wrapper_ ->
                        wrapper_.like(InstitutionZone::getName, institutionZoneVo.getName()).or()
                                .like(InstitutionZone::getNameChn, institutionZoneVo.getNameChn()));
            }
            if (GeneralTool.isNotEmpty(institutionZoneVo.getFkInstitutionId())) {
                wrapper.lambda().eq(InstitutionZone::getFkInstitutionId, institutionZoneVo.getFkInstitutionId());
            }
            //example.orderBy("fkInstitutionId").orderBy("viewOrder").desc().orderBy("name");
            wrapper.orderByDesc("fk_institution_id,IFNULL(view_order,0)");
            wrapper.orderByAsc("CONVERT(name USING gbk)");
        }
        //获取分页数据
        IPage<InstitutionZone> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionZone> institutionZones = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<InstitutionZoneVo> convertDatas = new ArrayList<>();

        //学校ids
        Set<Long> institutionIds = institutionZones.stream().map(InstitutionZone::getFkInstitutionId).collect(Collectors.toSet());
        //根据学校ids获取名称
        Map<Long, String> institutionNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(institutionIds)) {
            institutionNamesByIds = institutionService.getInstitutionNamesByIds(institutionIds);
        }

        //国家ids
        Set<Long> countryIds = institutionZones.stream().map(InstitutionZone::getFkAreaCountryId).collect(Collectors.toSet());
        //州省ids
        Set<Long> stateIds = institutionZones.stream().map(InstitutionZone::getFkAreaStateId).collect(Collectors.toSet());
        //城市ids
        Set<Long> cityIds = institutionZones.stream().map(InstitutionZone::getFkAreaCityId).collect(Collectors.toSet());
        //根据国家ids获取国家名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            countryNamesByIds = areaCountryService.getCountryNamesByIds(countryIds);
        }
        //根据州省ids获取州省名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            stateNamesByIds = areaStateService.getStateFullNamesByIds(stateIds);
        }
        //根据州省ids获取州省名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            cityNamesByIds = areaCityService.getCityFullNamesByIds(cityIds);
        }

        for (InstitutionZone institutionZone : institutionZones) {
            InstitutionZoneVo institutionZoneDto = BeanCopyUtils.objClone(institutionZone, InstitutionZoneVo::new);
            if (GeneralTool.isNotEmpty(institutionZoneDto.getFkInstitutionId())) {
                institutionZoneDto.setInstitutionName(institutionNamesByIds.get(institutionZoneDto.getFkInstitutionId()));
            }
            if (GeneralTool.isNotEmpty(institutionZoneDto.getFkAreaCountryId())) {
                institutionZoneDto.setAreaCountryName(countryNamesByIds.get(institutionZoneDto.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(institutionZoneDto.getFkAreaStateId())) {
                institutionZoneDto.setAreaStateName(stateNamesByIds.get(institutionZoneDto.getFkAreaStateId()));
            }
            if (GeneralTool.isNotEmpty(institutionZoneDto.getFkAreaCityId())) {
                institutionZoneDto.setAreaCityName(cityNamesByIds.get(institutionZoneDto.getFkAreaCityId()));
            }
            String fullName = institutionZoneDto.getName();
            if (GeneralTool.isNotEmpty(institutionZoneDto.getNameChn())) {
                fullName = fullName + "（" + institutionZoneDto.getNameChn() + "）";
            }
            institutionZoneDto.setFullName(fullName);
            convertDatas.add(institutionZoneDto);
        }
        return convertDatas;
    }

    @Override
    public Long addInstitutionZone(InstitutionZoneDto institutionZoneDto) {
        InstitutionZone institutionZone = BeanCopyUtils.objClone(institutionZoneDto, InstitutionZone::new);
        utilService.updateUserInfoToEntity(institutionZone);
        int i = institutionZoneMapper.insertSelective(institutionZone);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return institutionZone.getId();
    }

    @Override
    public InstitutionZoneVo findInstitutionZoneById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionZone institutionZone = institutionZoneMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionZone)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionZoneVo institutionZoneVo = BeanCopyUtils.objClone(institutionZone, InstitutionZoneVo::new);
        institutionZoneVo.setFkTableName(TableEnum.INSTITUTION_ZONE.key);
        if (GeneralTool.isNotEmpty(institutionZoneVo.getFkInstitutionId())) {
            institutionZoneVo.setInstitutionName(institutionMapper.getInstitutionNameById(institutionZoneVo.getFkInstitutionId()));
        }
        if (GeneralTool.isNotEmpty(institutionZoneVo.getFkAreaCountryId())) {
            institutionZoneVo.setAreaCountryName(areaCountryMapper.getCountryFullNameById(institutionZoneVo.getFkAreaCountryId()));
        }
        if (GeneralTool.isNotEmpty(institutionZoneVo.getFkAreaStateId())) {
            institutionZoneVo.setAreaStateName(areaStateMapper.getStateFullNameById(institutionZoneVo.getFkAreaStateId()));
        }
        if (GeneralTool.isNotEmpty(institutionZoneVo.getFkAreaCityId())) {
            institutionZoneVo.setAreaCityName(areaCityMapper.getCityFullNameById(institutionZoneVo.getFkAreaCityId()));
        }
        String fullName = institutionZoneVo.getName();
        if (GeneralTool.isNotEmpty(institutionZoneVo.getNameChn())) {
            fullName = fullName + "（" + institutionZoneVo.getNameChn() + "）";
        }
        institutionZoneVo.setFullName(fullName);
        return institutionZoneVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delete(Long id) {
        //TODO 改过
        //InstitutionZone institutionZone = findInstitutionZoneById(id);
        InstitutionZoneVo institutionZone = findInstitutionZoneById(id);
        if (institutionZone == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (institutionCourseZoneMapper.isExistByZone(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("zone_course_data_association"));
        }
        institutionZoneMapper.deleteById(id);

        //同时删除该表id下的所有媒体附件
        List<MediaAndAttached> mediaAndAttachedByTableIds = attachedService.findMediaAndAttachedByTableId(id, TableEnum.INSTITUTION_ZONE.key, null);
        for (MediaAndAttached mediaAndAttached : mediaAndAttachedByTableIds) {
            attachedService.deleteMediaAttached(mediaAndAttached.getId());
        }
        //删除资讯翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_ZONE.key, id);
    }

    @Override
    public FileDto upload(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }

        //返回上传路劲
        //文件上传
        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.INSTITUTIONCENTER);
        if (GeneralTool.isEmpty(result.getData())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        return result.getData().get(0);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public InstitutionZoneVo updateInstitutionZone(InstitutionZoneDto institutionZoneDto) {
        if (institutionZoneDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(institutionZoneDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionZone rs = institutionZoneMapper.selectById(institutionZoneDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionZone institutionZone = BeanCopyUtils.objClone(institutionZoneDto, InstitutionZone::new);
        utilService.updateUserInfoToEntity(institutionZone);
        institutionZoneMapper.updateById(institutionZone);
        return findInstitutionZoneById(institutionZone.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<MediaAndAttachedVo> addInstitutionZoneMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            String tableName = "m_institution_zone";
            mediaAndAttachedDto.setFkTableName(tableName);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<InstitutionZoneVo> getByfkInstitutionId(Long id) {
        LambdaQueryWrapper<InstitutionZone> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(id)) {
            wrapper.eq(InstitutionZone::getFkInstitutionId, id);
        }
        List<InstitutionZone> institutionZones = institutionZoneMapper.selectList(wrapper);
        List<InstitutionZoneVo> convertDatas = new ArrayList<>();
        for (InstitutionZone igy : institutionZones) {
            InstitutionZoneVo institutionZoneVo = BeanCopyUtils.objClone(igy, InstitutionZoneVo::new);
            String fullName = institutionZoneVo.getName();
            if (GeneralTool.isNotEmpty(institutionZoneVo.getNameChn())) {
                fullName = fullName + "（" + institutionZoneVo.getNameChn() + "）";
            }
            institutionZoneVo.setFullName(fullName);
            convertDatas.add(institutionZoneVo);
        }
        return convertDatas;
    }

    @Override
    public List<Map<String, Object>> findMediaAndAttachedType() {
        return FileTypeEnum.enumsTranslation2Arrays(FileTypeEnum.INSTITUTIONFACULTY);
    }

    @Override
    public List<MediaAndAttachedVo> getInstitutionZoneMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.INSTITUTION_ZONE.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<InstitutionZoneVo> getInstitutionZoneSelectByInstitutionIdList(List<Long> institutionIdList) {
        List<InstitutionZoneVo> resultList = new ArrayList<>();
        for (Long institutionId : institutionIdList) {
            List<InstitutionZoneVo> institutionZoneVos = getByfkInstitutionId(institutionId);
            if (GeneralTool.isNotEmpty(institutionZoneVos)) {
                resultList.addAll(institutionZoneVos);
            }
        }
        return resultList;
    }

    /**
     * feign 根据学校校区ids获取校区名字
     *
     * @Date 18:28 2022/2/18
     * <AUTHOR>
     */
    @Override
    public Map<Long, String> getInstitutionZoneNamesByIds(Set<Long> institutionZoneIdSet) {
        if (GeneralTool.isEmpty(institutionZoneIdSet)) {
            return null;
        }
        LambdaQueryWrapper<InstitutionZone> wrapper = new LambdaQueryWrapper();
        wrapper.in(InstitutionZone::getId, institutionZoneIdSet);
        List<InstitutionZone> institutionZones = institutionZoneMapper.selectList(wrapper);
        Map<Long, String> map = new HashMap<>();
        for (InstitutionZone institutionZone : institutionZones) {
            StringBuilder stringBuilder = new StringBuilder(institutionZone.getName());
            if (GeneralTool.isNotEmpty(institutionZone.getNameChn())) {
                stringBuilder.append(" （").append(institutionZone.getNameChn()).append("）");
            }
            map.put(institutionZone.getId(), stringBuilder.toString());
        }
        return map;
    }
}
