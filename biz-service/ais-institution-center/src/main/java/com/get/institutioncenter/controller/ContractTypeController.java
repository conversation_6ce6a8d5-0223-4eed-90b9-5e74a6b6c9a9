package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.vo.ContractTypeVo;
import com.get.institutioncenter.service.IContractTypeService;
import com.get.institutioncenter.dto.ContractTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/29 10:27
 * @verison: 1.0
 * @description: 合同类型管理控制器
 */
@Api(tags = "合同类型管理")
@RestController
@RequestMapping("/institution/contractType")
public class ContractTypeController {

    @Resource
    private IContractTypeService contractTypeService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/合同类型管理/合同类型详情")
    @GetMapping("/{id}")
    public ResponseBo<ContractTypeVo> detail(@PathVariable("id") Long id) {
        ContractTypeVo data = contractTypeService.findContractTypeById(id);
        return new ResponseBo<>(data);
    }


    /**
     * 批量新增信息
     *
     * @param contractTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/合同类型管理/新增合同类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody  @Validated(ContractTypeDto.Add.class)  ValidList<ContractTypeDto> contractTypeDtos) {
        contractTypeService.batchAdd(contractTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/合同类型管理/删除合同类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        contractTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param contractTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/合同类型管理/更新合同类型")
    @PostMapping("update")
    public ResponseBo<ContractTypeVo> update(@RequestBody @Validated(ContractTypeDto.Update.class)  ContractTypeDto contractTypeDto) {
        return UpdateResponseBo.ok(contractTypeService.updateContractType(contractTypeDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字(typeName类型名称)")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/合同类型管理/查询合同类型")
    @PostMapping("datas")
    public ResponseBo<ContractTypeVo> datas(@RequestBody SearchBean<ContractTypeDto> page) {
        List<ContractTypeVo> datas = contractTypeService.getContractTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 上移下移
     *
     * @param contractTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/合同类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<ContractTypeDto> contractTypeDtos) {
        contractTypeService.movingOrder(contractTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description:
     * @Param [contractTypeVos]
     * <AUTHOR>
     **/
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "合同类型下拉", notes = "")
    @GetMapping("getContractTypeSelect")
    public ResponseBo getContractTypeSelect() {
        return new ListResponseBo<>(contractTypeService.getContractTypeSelect());
    }


}
