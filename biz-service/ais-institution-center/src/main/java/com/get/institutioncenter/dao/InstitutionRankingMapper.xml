<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionRankingMapper">
    <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionRanking">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="year" jdbcType="INTEGER" property="year"/>
        <result column="ranking_type" jdbcType="INTEGER" property="rankingType"/>
        <result column="ranking_min" jdbcType="INTEGER" property="rankingMin"/>
        <result column="ranking_max" jdbcType="INTEGER" property="rankingMax"/>
        <result column="ranking_note" jdbcType="VARCHAR" property="rankingNote"/>
        <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId"/>
        <result column="institution_name" jdbcType="VARCHAR" property="institutionName"/>
        <result column="institution_name_chn" jdbcType="VARCHAR" property="institutionNameChn"/>
        <result column="institution_nature" jdbcType="VARCHAR" property="institutionNature"/>
        <result column="fk_area_country_id" jdbcType="BIGINT" property="fkAreaCountryId"/>
        <result column="area_country_name" jdbcType="VARCHAR" property="areaCountryName"/>
        <result column="fk_course_type_group_id" jdbcType="BIGINT" property="fkCourseTypeGroupId"/>
        <result column="course_type_group_name" jdbcType="VARCHAR" property="courseTypeGroupName"/>
        <result column="course_type_group_name_chn" jdbcType="VARCHAR" property="courseTypeGroupNameChn"/>
        <result column="fk_course_type_id" jdbcType="BIGINT" property="fkCourseTypeId"/>
        <result column="course_type_name" jdbcType="VARCHAR" property="courseTypeName"/>
        <result column="course_type_name_chn" jdbcType="VARCHAR" property="courseTypeNameChn"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, year, ranking_type, ranking_min, ranking_max, ranking_note, fk_institution_id, 
    institution_name, institution_name_chn, fk_area_country_id, area_country_name, fk_course_type_group_id, 
    course_type_group_name, course_type_group_name_chn,institution_nature, fk_course_type_id, course_type_name,
    course_type_name_chn, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
  </sql>

    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionRanking">
        insert into m_institution_ranking
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="year != null">
                year,
            </if>
            <if test="rankingType != null">
                ranking_type,
            </if>
            <if test="rankingMin != null">
                ranking_min,
            </if>
            <if test="rankingMax != null">
                ranking_max,
            </if>
            <if test="rankingNote != null">
                ranking_note,
            </if>
            <if test="fkInstitutionId != null">
                fk_institution_id,
            </if>
            <if test="institutionName != null">
                institution_name,
            </if>
            <if test="institutionNameChn != null">
                institution_name_chn,
            </if>
            <if test="institutionNature != null">
                institution_nature,
            </if>
            <if test="fkAreaCountryId != null">
                fk_area_country_id,
            </if>
            <if test="areaCountryName != null">
                area_country_name,
            </if>
            <if test="fkCourseTypeGroupId != null">
                fk_course_type_group_id,
            </if>
            <if test="courseTypeGroupName != null">
                course_type_group_name,
            </if>
            <if test="courseTypeGroupNameChn != null">
                course_type_group_name_chn,
            </if>
            <if test="fkCourseTypeId != null">
                fk_course_type_id,
            </if>
            <if test="courseTypeName != null">
                course_type_name,
            </if>
            <if test="courseTypeNameChn != null">
                course_type_name_chn,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="year != null">
                #{year,jdbcType=INTEGER},
            </if>
            <if test="rankingType != null">
                #{rankingType,jdbcType=INTEGER},
            </if>
            <if test="rankingMin != null">
                #{rankingMin,jdbcType=INTEGER},
            </if>
            <if test="rankingMax != null">
                #{rankingMax,jdbcType=INTEGER},
            </if>
            <if test="rankingNote != null">
                #{rankingNote,jdbcType=VARCHAR},
            </if>
            <if test="fkInstitutionId != null">
                #{fkInstitutionId,jdbcType=BIGINT},
            </if>
            <if test="institutionName != null">
                #{institutionName,jdbcType=VARCHAR},
            </if>
            <if test="institutionNameChn != null">
                #{institutionNameChn,jdbcType=VARCHAR},
            </if>
            <if test="institutionNature != null">
                #{institutionNature,jdbcType=VARCHAR},
            </if>
            <if test="fkAreaCountryId != null">
                #{fkAreaCountryId,jdbcType=BIGINT},
            </if>
            <if test="areaCountryName != null">
                #{areaCountryName,jdbcType=VARCHAR},
            </if>
            <if test="fkCourseTypeGroupId != null">
                #{fkCourseTypeGroupId,jdbcType=BIGINT},
            </if>
            <if test="courseTypeGroupName != null">
                #{courseTypeGroupName,jdbcType=VARCHAR},
            </if>
            <if test="courseTypeGroupNameChn != null">
                #{courseTypeGroupNameChn,jdbcType=VARCHAR},
            </if>
            <if test="fkCourseTypeId != null">
                #{fkCourseTypeId,jdbcType=BIGINT},
            </if>
            <if test="courseTypeName != null">
                #{courseTypeName,jdbcType=VARCHAR},
            </if>
            <if test="courseTypeNameChn != null">
                #{courseTypeNameChn,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="getWcComprehensiveRanking" resultType="com.get.institutioncenter.vo.InstitutionRankingVo">
        SELECT * FROM `m_institution_ranking`
        where ranking_type=#{data.rankingType}
        <if test="data.fkCountryId !=null">
            and fk_area_country_id=#{data.fkCountryId}
        </if>
        <if test="data.fkInstitutionName !=null and data.fkInstitutionName !=''">
            and (institution_name like concat('%',#{data.fkInstitutionName},'%')
            or institution_name_chn like concat('%',#{data.fkInstitutionName},'%')
            or CONCAT(institution_name,'（',institution_name_chn,'）')like concat('%',#{data.fkInstitutionName},'%'))
        </if>
        <if test="data.courseTypeGroupNameChn !=null and data.courseTypeGroupNameChn!=''">
            and(course_type_group_name_chn like concat('%',#{data.courseTypeGroupNameChn},'%')
            or course_type_group_name like concat('%',#{data.courseTypeGroupNameChn},'%')
            )
        </if>
        <if test="data.courseTypeName !=null and data.courseTypeName!=''">
            and(course_type_name like concat('%',#{data.courseTypeName},'%')
            or course_type_name_chn like concat('%',#{data.courseTypeName},'%')
            )
        </if>
        and `year`=(select `year` from m_institution_ranking where ranking_type=#{data.rankingType} order by `year` desc
        limit 1)
        order by ranking_min asc ,id desc
    </select>
    <select id="getWcComprehensiveRankingHome" resultType="com.get.institutioncenter.vo.InstitutionRankingVo">
SELECT 	case ranking_type
when 0 then CONCAT((select `year` from m_institution_ranking where ranking_type=0 order by `year` desc limit 1),' ','QS世界大学综合排名')
when 1 then CONCAT((select `year` from m_institution_ranking where ranking_type=1 order by `year` desc limit 1),' ','TIMES英国大学综合排名')
when 2 then CONCAT((select `year` from m_institution_ranking where ranking_type=2 order by `year` desc limit 1),' ','THE世界大学综合排名')
when 3 then CONCAT((select `year` from m_institution_ranking where ranking_type=3 order by `year` desc limit 1),' ','U.S.News全美最佳综合大学排名')
when 4 then CONCAT((select `year` from m_institution_ranking where ranking_type=4 order by `year` desc limit 1),' ','Maclean加拿大大学综合排名')
when 5 then CONCAT((select `year` from m_institution_ranking where ranking_type=5 order by `year` desc limit 1),' ','ARWU软科世界大学学术排名')
when 6 then CONCAT((select `year` from m_institution_ranking where ranking_type=6 order by `year` desc limit 1),' ','Guardian英国大学综合排名')
when 7 then CONCAT((select `year` from m_institution_ranking where ranking_type=6 order by `year` desc limit 1),' ','英国CUG学校排名')
end as title,ranking_type  FROM `m_institution_ranking` group by ranking_type order by FIELD(ranking_type,0,2,1,6,7,3,4,5)

    </select>
    <select id="getWcCountryByKey" resultType="com.get.institutioncenter.vo.InstitutionRankingVo">
        select GROUP_CONCAT(DISTINCT fk_area_country_id)as fkCountryIds FROM `m_institution_ranking` where
        `year`=YEAR(NOW()) and ranking_type=#{typeKey} group by ranking_type;
    </select>
    <select id="getWcCourseTypeKey" resultType="com.get.institutioncenter.vo.InstitutionRankingVo">
select GROUP_CONCAT(DISTINCT course_type_group_name_chn)as groupNameChn  FROM `m_institution_ranking` where
`year`=YEAR(NOW()) and ranking_type=#{typeKey} group by ranking_type order by ranking_type asc
    </select>
    <select id="getWcMajorRanking" resultType="com.get.institutioncenter.vo.InstitutionRankingVo">
        SELECT DISTINCT CONCAT(course_type_group_name,' ',course_type_group_name_chn)as
        groupNameChn,course_type_name,course_type_name_chn,ranking_type FROM `m_institution_ranking` where
        ranking_type=#{typeKey} order
         BY
        course_type_group_name asc,course_type_name asc;
    </select>
    <select id="getMaxYear" resultType="java.lang.Integer">
        SELECT MAX(year) FROM  `m_institution_ranking`
    </select>


</mapper>