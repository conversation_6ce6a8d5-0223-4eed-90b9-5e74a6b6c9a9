package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.vo.InstitutionZoneVo;
import com.get.institutioncenter.entity.InstitutionZone;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface InstitutionZoneMapper extends BaseMapper<InstitutionZone> {
    int insert(InstitutionZone record);

    int insertSelective(InstitutionZone record);


    /**
     * feign调用 根据id查找名称
     *
     * @param id
     * @return
     */
    String getNameById(Long id);

    /**
     * 根据课程ids查找校区名称
     *
     * @param fkInstitutionCourseIds
     * @return
     */
    List<InstitutionZoneVo> getNameByIds(@Param("fkInstitutionCourseIds") Set<Long> fkInstitutionCourseIds);
}