package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.AreaCityDivisionDto;
import com.get.institutioncenter.vo.AreaCityDivisionVo;
import com.get.institutioncenter.entity.AreaCityDivision;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2021/3/3 14:45
 * @verison: 1.0
 * @description:
 */
public interface IAreaCityDivisionService extends BaseService<AreaCityDivision> {
    /**
     * @return com.get.salecenter.vo.AreaCityDivisionVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    AreaCityDivisionVo findAreaCityDivisionById(Long id);

    /**
     * @return java.lang.Long
     * @Description :新增
     * @Param [areaCityDivisionDto]
     * <AUTHOR>
     */
    Long add(AreaCityDivisionDto areaCityDivisionDto);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.salecenter.vo.AreaCityDivisionVo
     * @Description :修改
     * @Param [areaCityDivisionDto]
     * <AUTHOR>
     */
    AreaCityDivisionVo updateAreaCityDivision(AreaCityDivisionDto areaCityDivisionDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.AreaCityDivisionVo>
     * @Description :列表
     * @Param [areaCityDivisionDto, page]
     * <AUTHOR>
     */
    List<AreaCityDivisionVo> getAreaCityDivisions(AreaCityDivisionDto areaCityDivisionDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [areaCityDivisionDtos]
     * <AUTHOR>
     */
    void movingOrder(List<AreaCityDivisionDto> areaCityDivisionDtos);

    /**
     * @Description: 查询城市下面的区域
     * @Author: Jerry
     * @Date:12:38 2021/9/10
     */
    List<AreaCityDivisionVo> getByFkAreaCityId(Long fkAreaCityId);

    /**
     * 通过城市区域ids 查找对应的城市区域名称map（拼接streetsName）
     *
     * @param ids
     * @return
     */
    Map<Long, String> getCityDivisionFullNamesByIds(Set<Long> ids);

}
