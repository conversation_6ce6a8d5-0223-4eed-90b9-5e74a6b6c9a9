package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.institutioncenter.vo.InstitutionProviderInstitutionVo;
import com.get.institutioncenter.service.IInstitutionProviderInstitutionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/25
 * @TIME: 14:58
 * @Description:
 **/
@Api(tags = "学校-学校提供商配置")
@RestController
@RequestMapping("/institution/InstitutionProviderInstitution")

public class InstitutionProviderInstitutionController {

    @Resource
    private IInstitutionProviderInstitutionService institutionProviderInstitutionService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "获取所有关系", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/学校-学校提供商配置管理/关系集合")
    @PostMapping("getAllRelation")
    public ResponseBo<InstitutionProviderInstitutionVo> getAllRelation(@RequestParam(value = "fkInstitutionProviderId", required = false) Long fkInstitutionProviderId,
                                                                       @RequestParam(value = "fkInstitutionId", required = false) Long fkInstitutionId) {
        List<InstitutionProviderInstitutionVo> institutionProviderDto = institutionProviderInstitutionService.getAllRelation(fkInstitutionProviderId, fkInstitutionId);
        return new ListResponseBo<>(institutionProviderDto);
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校管理/删除与提供商关联")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionProviderInstitutionService.delete(id);
        return DeleteResponseBo.ok();
    }

}
