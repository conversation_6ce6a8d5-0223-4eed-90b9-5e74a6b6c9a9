package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionCourseZone;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface InstitutionCourseZoneMapper extends BaseMapper<InstitutionCourseZone> {
    int insert(InstitutionCourseZone record);

    int insertSelective(InstitutionCourseZone record);

    /**
     * 根据id验证校区是否存在
     *
     * @return
     */
    boolean isExistByZone(@Param("zoneId") Long zoneId);

    /**
     * 课程获取校区
     *
     * @return
     */
    List<Long> getZoneIdsByCourseId(@Param("id") Long id);

    /**
     * 课程删除校区
     *
     * @return
     */
    void deleteByCourseId(@Param("id") Long id);

    /**
     * 根据课程id验证校区是否存在
     *
     * @return
     */
    boolean isExistByCourseId(@Param("courseId") Long courseId);
}