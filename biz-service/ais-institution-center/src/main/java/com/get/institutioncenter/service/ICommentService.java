package com.get.institutioncenter.service;


import com.get.common.result.Page;
import com.get.institutioncenter.dto.CommentDto;
import com.get.institutioncenter.entity.Comment;
import com.get.institutioncenter.vo.CommentVo;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/5
 * @TIME: 15:01
 * @Description:
 **/
public interface ICommentService {

    /**
     * @return java.util.List<com.get.salecenter.vo.CommentVo>
     * @Description: 获取所有评论
     * @Param [commentDto, page]
     * <AUTHOR>
     */
    List<CommentVo> datas(CommentDto commentDto, Page page);

    /**
     * 保存
     *
     * @param comment
     * @return
     */
    void addComment(Comment comment);

    /**
     * 更新
     *
     * @param comment
     * @return
     */
    void updateComment(Comment comment);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);
}
