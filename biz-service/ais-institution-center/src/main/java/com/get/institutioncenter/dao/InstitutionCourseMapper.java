package com.get.institutioncenter.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.institutioncenter.vo.InstitutionCourseVo;
import com.get.institutioncenter.entity.InstitutionCourse;
import com.get.institutioncenter.dto.CaseStudyResultsDto;
import com.get.institutioncenter.dto.query.InstitutionCourseQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Mapper
public interface InstitutionCourseMapper extends BaseMapper<InstitutionCourse> {

    /**
     * 查找学校课程
     *
     * @return
     */
    List<BaseSelectEntity> getInstitutionCourseByInstitution(@Param("institutionId") Long institutionId);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description :多个学校所有的课程下拉框数据
     * @Param [institutionIdList]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getInstitutionCourseByInstitutionIdList(@Param("institutionIdList") List<Long> institutionIdList);

    /**
     * 通过类型验证课程是否存在
     *
     * @return
     */
    boolean isExistByCourseType(@Param("fkCourseTypeId") Long fkCourseTypeId);


    /**
     * 目标类型获取目标
     *
     * @return
     */
    List<BaseSelectEntity> getTarget(@Param("tableName") String tableName);

    /**
     * 学校桥梁课程下拉框数据
     *
     * @return
     */
    List<BaseSelectEntity> getBridgeInstitutionCourseSelect(@Param("ids") List<Long> ids, @Param("fkInstitutionId") Long fkInstitutionId);

    /**
     * 学校非桥梁课程下拉框数据
     *
     * @Date 17:39 2021/7/22
     * <AUTHOR>
     */
    List<BaseSelectEntity> getNonBridgeInstitutionCoursePathwayByInstitution(@Param("ids") List<Long> ids, @Param("fkInstitutionId") Long fkInstitutionId);


    /**
     * feign调用 根据课程id查找课程名称
     *
     * @param id
     * @return
     */
    String getNameById(Long id);

    /**
     * 学校课程列表数据
     *
     * @return
     */
    List<InstitutionCourseVo> datas(IPage<InstitutionCourseVo> page,@Param("institutionCourseDto") InstitutionCourseQueryDto institutionCourseVo);

    /**
     * 级别和类型查找课程
     *
     * @return
     */
    List<Long> getCourseIdsByTypeAndLevel(@Param("fkMajorLevelIds") List<Long> fkMajorLevelIds, @Param("fkCourseTypeIds") List<Long> fkCourseTypeIds);

    /**
     * 获取课程学费
     *
     * @return
     */
    Double getFeeById(@Param("id") Long id);

    /**
     * 课程总学费
     *
     * @return
     */
    Double getSumFeeByIds(@Param("ids") List<Long> ids);

    /**
     * 获取所有课程费用信息
     *
     * @param updateType
     * @return
     */
    List<InstitutionCourseVo> getAllcourseDel(@Param("updateType") String updateType);

    /**
     * 获取所有学校币种编号(含有学费)
     */

    List<String> getAllSchoolFkCurrencyTypeNum();

    void updateAllCourseFerCny(@Param("id") Long id, @Param("feeCny") BigDecimal feeCny);

    List<Long> getInstitutionCourseByName(@Param("courseName") String courseName,@Param("institutionId") Long institutionId);

    List<Long> getInstitutionCourseIdsByLevelAndName(@Param("name") String name, @Param("levelId") Long levelId);

    //根据课程名称，学校id查询课程链接
    InstitutionCourseVo getInstitutionCourseWesiteByName(@Param("courseNames") String courseNames, @Param("institutionId") Long institutionId);

    @DS("institutiondb-doris")
    List<BaseSelectEntity> InstitutionCourseListByName(@Param("courseName")String courseName, @Param("institutionIds")List<Long> institutionId, @Param("courseIds")List<Long> courseIds);

    List<CaseStudyResultsDto.Statistics> getCourseStatistics(@Param("ids")Set<Long> ids);

    List<BaseSelectEntity> getCourseSelectedByKeyword(@Param("keyword") String keyword,@Param("institutionId") Long institutionId);

    List<BaseSelectEntity> getInstitutionCourseList(@Param("name") String name,@Param("courseIds")List<Long> courseIds);

    List<BaseSelectEntity> getInstitutionCourseListByIds(@Param("courseIds") List<Long> courseIds);

    /**
     * 获取所有对应字段不为null或""的InstitutionCourseIds
     * <AUTHOR>
     * @DateTime 2023/10/25 10:36
     */
    List<Long> getInstitutionCourseIdsByColumnName(@Param("columnName") String columnName);

    /**
     * 根据ID查询课程
     */
    InstitutionCourse getInstitutionCourseById(@Param("id") Long id);
}