package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.AreaCountryInfoTypeDto;
import com.get.institutioncenter.vo.AreaCountryInfoTypeVo;
import com.get.institutioncenter.service.IAreaCountryInfoTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/13 16:40
 * @verison: 1.0
 * @description:
 */
@Api(tags = "国家资讯类型管理")
@RestController
@RequestMapping("/institution/areaCountryInfoType")
public class AreaCountryInfoTypeController {
    @Resource
    private IAreaCountryInfoTypeService areaCountryInfoTypeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.AreaCountryInfoTypeVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/国家资讯类型管理/国家资讯类型详情")
    @GetMapping("/{id}")
    public ResponseBo<AreaCountryInfoTypeVo> detail(@PathVariable("id") Long id) {
        AreaCountryInfoTypeVo data = areaCountryInfoTypeService.findAreaCountryInfoTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [areaCountryInfoTypeDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/专业等级管理/新增专业等级")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(AreaCountryInfoTypeDto.Add.class) ValidList<AreaCountryInfoTypeDto> areaCountryInfoTypeDtos) {
        areaCountryInfoTypeService.batchAdd(areaCountryInfoTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/国家资讯类型管理/删除国家资讯类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        areaCountryInfoTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.AreaCountryInfoTypeVo>
     * @Description :修改信息
     * @Param [areaCountryInfoTypeDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/国家资讯类型管理/更新国家资讯类型")
    @PostMapping("update")
    public ResponseBo<AreaCountryInfoTypeVo> update(@RequestBody @Validated(AreaCountryInfoTypeDto.Update.class) AreaCountryInfoTypeDto areaCountryInfoTypeDto) {
        return UpdateResponseBo.ok(areaCountryInfoTypeService.updateAreaCountryInfoType(areaCountryInfoTypeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.AreaCountryInfoTypeVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/国家资讯类型管理/查询国家资讯类型")
    @PostMapping("datas")
    public ResponseBo<AreaCountryInfoTypeVo> datas(@RequestBody SearchBean<AreaCountryInfoTypeDto> page) {
        List<AreaCountryInfoTypeVo> datas = areaCountryInfoTypeService.getAreaCountryInfoTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [areaCountryInfoTypeDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/国家资讯类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<AreaCountryInfoTypeDto> areaCountryInfoTypeDtos) {
        areaCountryInfoTypeService.movingOrder(areaCountryInfoTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * 国家资讯类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "国家资讯类型下拉框数据", notes = "")
    @PostMapping("getAreaCountryInfoTypeList")
    public ResponseBo getAreaCountryInfoTypeList() {
        List<AreaCountryInfoTypeVo> datas = areaCountryInfoTypeService.getAreaCountryInfoTypeList();
        return new ListResponseBo<>(datas);
    }
}
