package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.dto.CourseTypeGroupDto;
import com.get.institutioncenter.vo.CourseTypeGroupVo;
import com.get.institutioncenter.entity.CourseTypeGroup;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/12/7
 * @TIME: 11:19
 * @Description:
 **/
public interface ICourseTypeGroupService extends BaseService<CourseTypeGroup> {
    /**
     * @return
     * @Description：课程类型组别详情
     * @Param
     * @Date 12:07 2021/4/27
     * <AUTHOR>
     */
    CourseTypeGroupVo findCourseTypeGroupById(Long id);

    /**
     * @return
     * @Description：列表数据
     * @Param
     * @Date 12:10 2021/4/27
     * <AUTHOR>
     */
    List<CourseTypeGroupVo> getCourseTypeGroups(CourseTypeGroupDto courseTypeGroupDto, Page page);


    /**
     * @return
     * @Description：删除类型组别
     * @Param
     * @Date 12:12 2021/4/27
     * <AUTHOR>
     */
    void delete(Long id);


    /**
     * @Description：修改组别信息
     * @Param
     * @Date 12:31 2021/4/27
     * <AUTHOR>
     */
    CourseTypeGroupVo updateCourseType(CourseTypeGroupDto courseTypeGroupDto);


    /**
     * @Description：批量新增类型组别信息
     * @Param
     * @Date 12:51 2021/4/27
     * <AUTHOR>
     */
    void batchAdd(List<CourseTypeGroupDto> courseTypeVos);

    /**
     * @Description：上移下移
     * @Param
     * @Date 14:20 2021/4/27
     * <AUTHOR>
     */
    void movingOrder(List<CourseTypeGroupDto> courseTypeVos);

    /**
     * @Description：课程类型组别下拉框数据
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    List<BaseSelectEntity> getCourseTypeGroupList();


    /**
     * 获取课程组别下拉
     * @param keyword
     * @return
     */
    List<BaseSelectEntity> getCourseGroupList(String keyword);



    /**
     * 获取课程组名称
     * @param ids
     * @return
     */
    Map<Long,String> getCourseGroupByIds(Set<Long> ids);
    /**
     * @Description：课程类型组别下拉框数据
     * @Param
     * @Date 14:32 2021/4/27
     * <AUTHOR>
     */
    List<BaseSelectEntity> getCourseTypeGroupListModeThree();

    /**
     * @Description：根据课程类型id 查询课程类型组别名字
     * @Param
     * @Date 18:29 2021/4/27
     * <AUTHOR>
     */
    String getCourseTypeGroupNameByCourseTypeId(Long courseTypeId);


    /**
     * 查询课程类型组别名字
     * @param id
     * @return
     */
    String getCourseTypeGroupNameById(Long id);
    /**
     * @Description：根据课程类型id 查询课程类型组别ids
     * @Param
     * @Date 18:29 2021/4/27
     * <AUTHOR>
     */
    List<Long> getCourseTypeIdByCourseTypeId(Long courseTypeId);

    /**
     * @Description：类型组别模块下拉框
     * @Param
     * @Date 18:29 2021/4/27
     * <AUTHOR>
     */
    List<Map<String, Object>> findModeType();

    List<CourseTypeGroup> getCourseTypeListByName();

    /**
     * 根据课程类型ids查询课程类型组别ids
     * @param fkInstitutionCourseTypeIds
     * @return
     */
    String getGroupTypeIdsByCourseTypeIds(String fkInstitutionCourseTypeIds);

    Map<Long,String> getCourseGroupTypeNameByIds(Set<Long> ids);

    Map<Long, String> getCourseGroupTypeNameChnByIds(Set<Long> ids);

    /**
     * 根据Ids获取课程类型组别全名-格式：name（nameChn）
     */
    Map<Long,String> getCourseGroupTypeFullNameByIds(Set<Long> ids);

    Map<Long, String>  getCourseTypeNamesByCourseGroupTypeIds(Set<Long> ids);
}
