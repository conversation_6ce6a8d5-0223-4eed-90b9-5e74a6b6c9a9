package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.AreaCountryInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AreaCountryInfoMapper extends BaseMapper<AreaCountryInfo> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AreaCountryInfo record);

    /**
     * @return boolean
     * @Description :校验国家能否删除
     * @Param [areaCountryId]
     * <AUTHOR>
     */
    boolean areaCountryInfoIsEmpty(@Param("areaCountryId") Long areaCountryId);
}