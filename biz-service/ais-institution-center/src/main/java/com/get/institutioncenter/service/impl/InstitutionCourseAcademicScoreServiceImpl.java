package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.AcademicScoreConfigMapper;
import com.get.institutioncenter.dao.InstitutionCourseAcademicScoreMapper;
import com.get.institutioncenter.vo.InstitutionCourseAcademicScoreVo;
import com.get.institutioncenter.entity.InstitutionCourseAcademicScore;
import com.get.institutioncenter.service.IAreaCountryService;
import com.get.institutioncenter.service.IInstitutionCourseAcademicScoreService;
import com.get.institutioncenter.dto.InstitutionCourseAcademicScoreDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.get.common.eunms.ProjectExtraEnum.STANDARD_TEST_TYPE;

/**
 * <AUTHOR>
 * @DATE: 2020/12/8
 * @TIME: 12:09
 * @Description:
 **/
@Service
public class InstitutionCourseAcademicScoreServiceImpl extends BaseServiceImpl<InstitutionCourseAcademicScoreMapper, InstitutionCourseAcademicScore> implements IInstitutionCourseAcademicScoreService {
    @Resource
    private InstitutionCourseAcademicScoreMapper institutionCourseAcademicScoreMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IAreaCountryService areaCountryService;
    @Resource
    private AcademicScoreConfigMapper academicScoreConfigMapper;

    @Override
    public InstitutionCourseAcademicScoreVo findInstitutionCourseAcademicScoreById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionCourseAcademicScore institutionCourseAcademicScore = institutionCourseAcademicScoreMapper.selectById(id);
        if (GeneralTool.isEmpty(institutionCourseAcademicScore)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionCourseAcademicScoreVo institutionCourseAcademicScoreVo = BeanCopyUtils.objClone(institutionCourseAcademicScore, InstitutionCourseAcademicScoreVo::new);
        if (GeneralTool.isNotEmpty(institutionCourseAcademicScoreVo.getFkAreaCountryId())) {
            institutionCourseAcademicScoreVo.setFkAreaCountryName(areaCountryService.getCountryNameById(institutionCourseAcademicScoreVo.getFkAreaCountryId()));
        }
        if (GeneralTool.isNotEmpty(institutionCourseAcademicScoreVo.getConditionType())) {
            institutionCourseAcademicScoreVo.setTypeName(ProjectExtraEnum.getValueByKey(institutionCourseAcademicScoreVo.getConditionType(), ProjectExtraEnum.STANDARD_TEST_TYPE));
        }
        return institutionCourseAcademicScoreVo;
    }

    @Override
    public List<InstitutionCourseAcademicScoreVo> getInstitutionCourseAcademicScores(InstitutionCourseAcademicScoreDto institutionCourseAcademicScoreDto, Page page) {
        LambdaQueryWrapper<InstitutionCourseAcademicScore> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(institutionCourseAcademicScoreDto)) {
            if (GeneralTool.isNotEmpty(institutionCourseAcademicScoreDto.getFkInstitutionCourseId())) {
                wrapper.eq(InstitutionCourseAcademicScore::getFkInstitutionCourseId, institutionCourseAcademicScoreDto.getFkInstitutionCourseId());
            }
        }
        wrapper.orderByDesc(InstitutionCourseAcademicScore::getGmtCreate);
        //获取分页数据
        IPage<InstitutionCourseAcademicScore> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<InstitutionCourseAcademicScore> ct = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<InstitutionCourseAcademicScoreVo> convertDatas = new ArrayList<>();

        //国家ids
        Set<Long> fkAreaCountryIds = ct.stream().map(InstitutionCourseAcademicScore::getFkAreaCountryId).collect(Collectors.toSet());
        //根据国家ids获取名称map
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAreaCountryIds)) {
            countryNamesByIds = areaCountryService.getCountryNamesByIds(fkAreaCountryIds);
        }

        for (InstitutionCourseAcademicScore c : ct) {
            InstitutionCourseAcademicScoreVo institutionCourseAcademicScoreVo = BeanCopyUtils.objClone(c, InstitutionCourseAcademicScoreVo::new);
            if (GeneralTool.isNotEmpty(institutionCourseAcademicScoreVo.getFkAreaCountryId())) {
                institutionCourseAcademicScoreVo.setFkAreaCountryName(countryNamesByIds.get(institutionCourseAcademicScoreVo.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(institutionCourseAcademicScoreVo.getConditionType())) {
                institutionCourseAcademicScoreVo.setTypeName(ProjectExtraEnum.getValueByKey(institutionCourseAcademicScoreVo.getConditionType(), ProjectExtraEnum.STANDARD_TEST_TYPE));
            }
            convertDatas.add(institutionCourseAcademicScoreVo);
        }
        return convertDatas;
    }

    @Override
    public InstitutionCourseAcademicScoreVo updateInstitutionCourseAcademicScore(InstitutionCourseAcademicScoreDto institutionCourseAcademicScoreDto) {
        if (institutionCourseAcademicScoreDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(institutionCourseAcademicScoreDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        InstitutionCourseAcademicScore ct = institutionCourseAcademicScoreMapper.selectById(institutionCourseAcademicScoreDto.getId());
        if (ct == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        InstitutionCourseAcademicScore institutionCourseAcademicScore = BeanCopyUtils.objClone(institutionCourseAcademicScoreDto, InstitutionCourseAcademicScore::new);
        utilService.updateUserInfoToEntity(institutionCourseAcademicScore);
        institutionCourseAcademicScoreMapper.updateById(institutionCourseAcademicScore);
        return findInstitutionCourseAcademicScoreById(institutionCourseAcademicScore.getId());
    }

    @Override
    public void delete(Long id) {
        //TODO 改过
        //InstitutionCourseAcademicScore institutionCourseAcademicScore = findInstitutionCourseAcademicScoreById(id);
        InstitutionCourseAcademicScoreVo institutionCourseAcademicScore = findInstitutionCourseAcademicScoreById(id);
        if (institutionCourseAcademicScore == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        int i = institutionCourseAcademicScoreMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<InstitutionCourseAcademicScoreDto> institutionCourseAcademicScoreDtos) {
        for (InstitutionCourseAcademicScoreDto institutionCourseAcademicScoreDto : institutionCourseAcademicScoreDtos) {
            InstitutionCourseAcademicScore institutionCourseAcademicScore = BeanCopyUtils.objClone(institutionCourseAcademicScoreDto, InstitutionCourseAcademicScore::new);
            utilService.updateUserInfoToEntity(institutionCourseAcademicScore);
            institutionCourseAcademicScoreMapper.insert(institutionCourseAcademicScore);
        }
    }

    @Override
    public List<String> getScoreTypeByCondition(Long id) {
        return academicScoreConfigMapper.getScoreTypeByCondition(id);
    }

    @Override
    public List<Map<String, Object>> getStandardTestType() {
        return ProjectExtraEnum.enums2Arrays(STANDARD_TEST_TYPE);
    }

    @Override
    public void deleteCourseAcademicScoreByCourseId(Long id) {
        LambdaQueryWrapper<InstitutionCourseAcademicScore> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionCourseAcademicScore::getFkInstitutionCourseId, id);
        int j = institutionCourseAcademicScoreMapper.delete(wrapper);
        if (j < 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

}
