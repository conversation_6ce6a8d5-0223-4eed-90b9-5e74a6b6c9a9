<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionFacultyMapper">
  <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionFaculty">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_institution_id" jdbcType="BIGINT" property="fkInstitutionId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_chn" jdbcType="VARCHAR" property="nameChn" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionFaculty" keyProperty="id" useGeneratedKeys="true">
    insert into m_institution_faculty (id, fk_institution_id, name, 
      name_chn, description, view_order, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{nameChn,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{viewOrder,jdbcType=INTEGER}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionFaculty" keyProperty="id" useGeneratedKeys="true">
    insert into m_institution_faculty
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInstitutionId != null">
        fk_institution_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameChn != null">
        name_chn,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionId != null">
        #{fkInstitutionId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameChn != null">
        #{nameChn,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getInstitutionFacultyNameById" parameterType="java.lang.Long" resultType="string">
    select
      CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
    from
      m_institution_faculty i
    where
      i.id = #{id}
  </select>

  <select id="getInstitutionFacultyNameByCourseIds" resultType="com.get.institutioncenter.vo.InstitutionFacultyVo">
    select ricf.fk_institution_course_id AS fkInstitutionCourseId, GROUP_CONCAT(distinct CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE
    CONCAT(`name`,'（',name_chn,'）') END separator '，')
      AS fullName
    from r_institution_course_faculty ricf
           left join m_institution_faculty mif on ricf.fk_institution_faculty_id = mif.id
    where fk_institution_course_id in
    <foreach collection="courseIds" item="courseId" index="index" open="(" separator="," close=")">
      #{courseId}
    </foreach>
    group by ricf.fk_institution_course_id
  </select>


  <select id="getFacultyCountByInstitutionId" parameterType="java.lang.Long" resultType="java.lang.Integer">
    select
      count(*)
    from
      m_institution_faculty
    where
      fk_institution_id = #{id}
  </select>
    <select id="getInstitutionFacultyNameByid" resultType="java.lang.String">
      SELECT CONCAT(`name`,'(',name_chn,')')as institutionFacultyName  FROM `m_institution_faculty` where id =#{id};
    </select>
    <select id="getFacultyList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT id,
               NAME,
               name_chn,
               CASE
              WHEN IFNULL(name_chn, '') = '' THEN
              `name`
              ELSE
              CONCAT(
              `name`,
              '（',
              name_chn,
              '）'
              )
              END fullName
        FROM m_institution_faculty
        WHERE
            1=1
        <if test="institutionId!=null and institutionId!=''">
          AND fk_institution_id = #{institutionId}
        </if>
        <if test="keyword!=null and keyword!=''">
          AND (`name` LIKE concat('%',#{keyword},'%') OR name_chn LIKE concat('%',#{keyword},'%'))
        </if>
        ORDER BY view_order DESC
      LIMIT 50
    </select>
</mapper>