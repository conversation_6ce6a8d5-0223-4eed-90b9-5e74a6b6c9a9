package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.ContractCompanyMapper;
import com.get.institutioncenter.dto.ContractCompanyDto;
import com.get.institutioncenter.vo.CompanyTreeVo;
import com.get.institutioncenter.vo.ContractCompanyVo;
import com.get.institutioncenter.entity.ContractCompany;
import com.get.institutioncenter.service.IContractCompanyService;
import com.get.institutioncenter.service.IInstitutionProviderCompanyService;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/10/29
 * @TIME: 10:27
 * @Description:
 **/
@Service
public class ContractCompanyServiceImpl extends BaseServiceImpl<ContractCompanyMapper, ContractCompany> implements IContractCompanyService {
    @Resource
    private ContractCompanyMapper companyMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionProviderCompanyService providerCompanyService;


    @Override
    public List<ContractCompanyVo> getContractCompany(List<Long> companyIds) {
        if (GeneralTool.isEmpty(companyIds)) {
            return null;
        }
        LambdaQueryWrapper<ContractCompany> wrapper = new LambdaQueryWrapper();
        wrapper.in(ContractCompany::getFkCompanyId, companyIds);
        List<ContractCompany> companies = companyMapper.selectList(wrapper);
        if (GeneralTool.isEmpty(companies)) {
            return null;
        }
        return companies.stream().map(contractCompany -> BeanCopyUtils.objClone(contractCompany, ContractCompanyVo::new)).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editContractCompanyRelation(List<ContractCompanyDto> contractCompanyDtos) {
        if (GeneralTool.isEmpty(contractCompanyDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //移除空元素
        contractCompanyDtos.removeIf(Objects::isNull);
        Long contractId = contractCompanyDtos.get(0).getFkContractId();

        List<ContractCompany> collect = contractCompanyDtos.stream().map(contractCompanyVo ->
                BeanCopyUtils.objClone(contractCompanyVo, ContractCompany::new)).collect(Collectors.toList());
        //获取所属提供商配置的公司id
        List<Long> companyIds = providerCompanyService.getCompanyIdByContractId(contractId);
        //合同关联公司id
        List<Long> contractCompanyIds = collect.stream().map(ContractCompany::getFkCompanyId).collect(Collectors.toList());
        contractCompanyIds.removeIf(Objects::isNull);
        if (GeneralTool.isNotEmpty(contractCompanyIds)) {
            if (!companyIds.containsAll(contractCompanyIds)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("greater_than_the_company_range_of_the_supplier"));
            }
        }

        //删除原先记
        List<Long> companyIds1 = SecureUtil.getCompanyIds();
        LambdaQueryWrapper<ContractCompany> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractCompany::getFkContractId, contractId).in(ContractCompany::getFkCompanyId,companyIds1);
        companyMapper.delete(wrapper);

        collect.forEach(contractCompany -> companyMapper.insertSelective(contractCompany));
    }

    @Override
    public Long addRelation(ContractCompanyDto contractCompanyDto) {
        ContractCompany contractCompany = BeanCopyUtils.objClone(contractCompanyDto, ContractCompany::new);
        utilService.updateUserInfoToEntity(contractCompany);
        companyMapper.insertSelective(contractCompany);
        return contractCompany.getId();
    }

    @Override
    public List<CompanyTreeVo> getContractCompanyRelation(Long contractId) {
        if (GeneralTool.isEmpty(contractId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = getCompanyTreeDto();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }
        //获取中间表数据
        List<ContractCompany> contractCompanies = getContractCompanies(contractId);
        setFlag(companyTreeVo, contractCompanies);
        return companyTreeVo;
    }

    private List<ContractCompany> getContractCompanies(Long contractId) {
        if (GeneralTool.isEmpty(contractId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取中间表数据
        LambdaQueryWrapper<ContractCompany> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ContractCompany::getFkContractId, contractId);
        return companyMapper.selectList(wrapper);
    }

    private void setFlag(List<CompanyTreeVo> companyTreeVo, List<ContractCompany> relation) {
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (ContractCompany providerCompany : relation) {
                if (treeDto.getId().equals(providerCompany.getFkCompanyId())) {
                    treeDto.setFlag(true);
                }
            }
        }
    }

    private List<CompanyTreeVo> getCompanyTreeDto() {
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        return BeanCopyUtils.copyListProperties(result.getData(), CompanyTreeVo::new);
    }

}
