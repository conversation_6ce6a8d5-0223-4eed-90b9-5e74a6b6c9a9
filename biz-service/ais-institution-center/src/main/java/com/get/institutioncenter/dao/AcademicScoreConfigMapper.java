package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.AcademicScoreConfig;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface AcademicScoreConfigMapper extends BaseMapper<AcademicScoreConfig> {
    int insert(AcademicScoreConfig record);

    int insertSelective(AcademicScoreConfig record);

    /**
     * @return list
     * @Description :学术分数查询
     * @Param ID
     * <AUTHOR>
     */
    List<String> getScoreTypeByCondition(Long id);
}