package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.dao.InstitutionChannelCompanyMapper;
import com.get.institutioncenter.entity.RInstitutionChannelCompany;
import com.get.institutioncenter.service.IInstitutionChannelCompanyService;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.vo.CompanyTreeVo;
import com.google.common.collect.Lists;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class InstitutionChannelCompanyServiceImpl extends BaseServiceImpl<InstitutionChannelCompanyMapper,RInstitutionChannelCompany> implements IInstitutionChannelCompanyService {

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private InstitutionChannelCompanyMapper institutionChannelCompanyMapper;

    @Override
    public void saveBatch(List<RInstitutionChannelCompany> list) {
        if (GeneralTool.isNotEmpty(list)) {
            saveBatch(list,list.size());
        }
    }

    @Override
    public List<CompanyTreeVo> getChannelCompanyRelation(Long channelId) {
        if (GeneralTool.isEmpty(channelId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //获取公司
        List<CompanyTreeVo> companyTreeVo = getCompanyTreeDto();
        if (GeneralTool.isEmpty(companyTreeVo)) {
            return null;
        }
        List<RInstitutionChannelCompany> relation = institutionChannelCompanyMapper.selectList(Wrappers.<RInstitutionChannelCompany>lambdaQuery()
                .eq(RInstitutionChannelCompany::getFkInstitutionChannelId,channelId));
        setAgentFlag(companyTreeVo, relation);
        return getTreeList(companyTreeVo);
    }

    private List<CompanyTreeVo> getCompanyTreeDto() {
        Result<List<com.get.permissioncenter.vo.tree.CompanyTreeVo>> result = permissionCenterClient.getAllCompanyDto();
        if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
            JsonConfig config = new JsonConfig();
            config.setExcludes(new String[]{"departmentTree", "totalNum"});
            JSONArray jsonArray = JSONArray.fromObject(result.getData(), config);
            return JSONArray.toList(jsonArray, new CompanyTreeVo(), new JsonConfig());
        }
        return Lists.newArrayList();
    }
    private void setAgentFlag(List<CompanyTreeVo> companyTreeVo, List<RInstitutionChannelCompany> relation) {
        for (CompanyTreeVo treeDto : companyTreeVo) {
            for (RInstitutionChannelCompany contractCompany : relation) {
                if (treeDto.getId().equals(String.valueOf(contractCompany.getFkCompanyId()))) {
                    treeDto.setFlag(true);
                }
            }
        }
    }
    private List<CompanyTreeVo> getTreeList(List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (parentId == null || "0".equals(parentId)) {
                //获取父节点的部门信息
                resultList.add(entity);
            }
        }
        //假如没有父节点
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点
            CompanyTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(CompanyTreeVo::getFkParentCompanyId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点
                List<CompanyTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentCompanyId().equals(minTreeNode.getFkParentCompanyId()) &&
                                !treeDto.getId().equals(minTreeNode.getId())).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        // 获取每个顶层元素的子数据集合
        for (CompanyTreeVo entity : resultList) {
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        return resultList;
    }
    private List<CompanyTreeVo> getSubList(String id, List<CompanyTreeVo> entityList) {
        List<CompanyTreeVo> childList = new ArrayList<>();
        String parentId;
        // 子集的直接子对象
        for (CompanyTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentCompanyId());
            if (id.equals(parentId)) {
                //获取子节点的部门信息
                childList.add(entity);
            }
        }
        // 子集的间接子对象
        for (CompanyTreeVo entity : childList) {
            //递归调用
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }
}
