package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.institutioncenter.dto.WeScholarshipAppDto;
import com.get.institutioncenter.vo.InstitutionAppFeeVo;
import com.get.institutioncenter.vo.InstitutionAppFeeVo2;
import com.get.institutioncenter.service.IInstitutionAppFeeService;
import com.get.institutioncenter.dto.InstitutionAppFeeDto;
import com.get.institutioncenter.dto.InstitutionAppFeeDto2;
import com.get.institutioncenter.dto.query.InstitutionAppFeeQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/5 21:49
 */
@Api(tags = "申请费信息管理")
@RestController
@RequestMapping("/institution/institutionAppFee")
public class InstitutionAppFeeController {

    @Resource
    IInstitutionAppFeeService iInstitutionAppFeeService;

    @ApiOperation("新增接口")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/申请费信息管理/新增申请费信息")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(InstitutionAppFeeDto.Add.class) InstitutionAppFeeDto institutionAppFeeDto) {
        return SaveResponseBo.ok(iInstitutionAppFeeService.addInstitutionAppFee(institutionAppFeeDto));
    }


    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/申请费信息管理/申请费信息详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionAppFeeVo> detail(@PathVariable("id") Long id) {
        InstitutionAppFeeVo data = iInstitutionAppFeeService.findInstitutionAppFeeById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", data);
        return responseBo;
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/申请费信息管理/删除申请费信息")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        iInstitutionAppFeeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/申请费信息管理/更新申请费信息")
    @PostMapping("update")
    public ResponseBo<InstitutionAppFeeVo> update(@RequestBody  @Validated(InstitutionAppFeeDto.Update.class) InstitutionAppFeeDto institutionAppFeeDto) {
        return UpdateResponseBo.ok(iInstitutionAppFeeService.updateInstitutionAppFee(institutionAppFeeDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/申请费信息管理/查询申请费信息")
    @PostMapping("datas")
    public ResponseBo<InstitutionAppFeeVo> datas(@RequestBody SearchBean<InstitutionAppFeeQueryDto> page) {
        List<InstitutionAppFeeVo> datas = iInstitutionAppFeeService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @ Description :
     * @ Param [fkInstitutionId]
     * @ return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionAppFeeVo>
     * @ author LEO
     */
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "小程序申请费用详情", notes = "")
    @PostMapping("getWcInstitutionAppFeeDatas")
    public ResponseBo<InstitutionAppFeeVo2> getWcInstitutionAppFeeDatas(@RequestBody SearchBean<InstitutionAppFeeDto2> page) {
        return new ListResponseBo(iInstitutionAppFeeService.getWcInstitutionAppFeeDatas(page.getData(),page),BeanCopyUtils.objClone(page, Page::new));
    }

    @ApiOperation(value = "申请费用信息优先匹配查询", notes = "")
    @PostMapping("priorityMatchingQuery")
    public ResponseBo<InstitutionAppFeeVo> priorityMatchingQuery(@RequestBody @Validated WeScholarshipAppDto weScholarshipAppDto){
        return new ListResponseBo<>(iInstitutionAppFeeService.priorityMatchingQuery(weScholarshipAppDto));
    }

    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "小程序申请费用列表", notes = "")
    @PostMapping("getWcInstitutionAppFeeList")
    public ResponseBo<InstitutionAppFeeVo2> getWcInstitutionAppFeeList(@RequestBody SearchBean<InstitutionAppFeeDto2> page) {
        return new ListResponseBo(iInstitutionAppFeeService.getWcInstitutionAppFeeList(page.getData(), page), BeanCopyUtils.objClone(page, Page::new));
    }


    @PostMapping("upddd")
    public ResponseBo setDatas(@RequestParam("file") MultipartFile multipartFile) throws Exception {
        return new ListResponseBo(iInstitutionAppFeeService.setDatas(multipartFile));
    }

    @VerifyLogin(IsVerify = false)
    @ApiOperation("申请费用等级类型")
    @GetMapping("getLevelType")
    public ResponseBo getLevelType() {
        return new ListResponseBo<>(ProjectExtraEnum.enums2Arrays(ProjectExtraEnum.InstitutionAppFeeLevelType));
    }

}
