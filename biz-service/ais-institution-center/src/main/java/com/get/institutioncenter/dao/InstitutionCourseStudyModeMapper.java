package com.get.institutioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionCourseStudyMode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface InstitutionCourseStudyModeMapper extends BaseMapper<InstitutionCourseStudyMode> {
//    int insert(InstitutionCourseStudyMode record);

//    int insertSelective(InstitutionCourseStudyMode record);

    /**
     * 检查获取课程链接
     *
     * @Date 18:50 2022/2/28
     * <AUTHOR>
     */
    Long checkCourseWebsite(@Param("fkInstitutionId") Long fkInstitutionId, @Param("courseWebsite") String courseWebsite);

}