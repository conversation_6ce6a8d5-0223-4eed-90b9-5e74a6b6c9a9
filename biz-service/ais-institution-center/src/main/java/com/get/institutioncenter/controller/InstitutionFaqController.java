package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.institutioncenter.vo.InstitutionFaqVo;
import com.get.institutioncenter.service.IInstitutionFaqService;
import com.get.institutioncenter.dto.InstitutionFaqDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 12:27
 * @Description:
 **/
@Api(tags = "常见问题管理")
@RestController
@RequestMapping("/institution/institutionFaq")
public class InstitutionFaqController {
    @Resource
    private IInstitutionFaqService institutionFaqService;

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/学校常见问题管理/学校常见问题详情")
    @GetMapping("/{id}")
    public ResponseBo<InstitutionFaqVo> detail(@PathVariable("id") Long id) {
        InstitutionFaqVo data = institutionFaqService.findInstitutionFaqById(id);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", data);
        return responseBo;
    }

    /**
     * 新增信息
     *
     * @param institutionFaqDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/学校常见问题管理/新增学校常见问题")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(InstitutionFaqDto.Add.class) InstitutionFaqDto institutionFaqDto) {
        return SaveResponseBo.ok(institutionFaqService.addInstitutionFaq(institutionFaqDto));
    }

    /**
     * 删除信息
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/学校常见问题管理/删除学校常见问题")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        institutionFaqService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param institutionFaqDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/学校常见问题管理/更新学校常见问题")
    @PostMapping("update")
    public ResponseBo<InstitutionFaqVo> update(@RequestBody @Validated(InstitutionFaqDto.Update.class) InstitutionFaqDto institutionFaqDto) {
        return UpdateResponseBo.ok(institutionFaqService.updateInstitutionFaq(institutionFaqDto));
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "学校中心/学校常见问题管理/查询学校常见问题")
    @PostMapping("datas")
    public ResponseBo<InstitutionFaqVo> datas(@RequestBody SearchBean<InstitutionFaqDto> page) {
        List<InstitutionFaqVo> datas = institutionFaqService.datas(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


}
