package com.get.institutioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.component.TranslationHelp;
import com.get.institutioncenter.dao.AreaCountryInfoTypeMapper;
import com.get.institutioncenter.dto.AreaCountryInfoTypeDto;
import com.get.institutioncenter.vo.AreaCountryInfoTypeVo;
import com.get.institutioncenter.entity.AreaCountryInfoType;
import com.get.institutioncenter.service.IAreaCountryInfoTypeService;
import com.get.institutioncenter.service.IDeleteService;
import com.get.institutioncenter.service.ITranslationMappingService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

;

/**
 * @author: Sea
 * @create: 2021/1/13 16:40
 * @verison: 1.0
 * @description:
 */
@Service
public class AreaCountryInfoTypeServiceImpl extends BaseServiceImpl<AreaCountryInfoTypeMapper, AreaCountryInfoType> implements IAreaCountryInfoTypeService {
    @Resource
    private AreaCountryInfoTypeMapper areaCountryInfoTypeMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private ITranslationMappingService translationMappingService;
    @Resource
    private GetRedis redisClient;
    @Resource
    private TranslationHelp translationHelp;


    @Override
    public AreaCountryInfoTypeVo findAreaCountryInfoTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaCountryInfoType areaCountryInfoType = areaCountryInfoTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(areaCountryInfoType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        AreaCountryInfoTypeVo areaCountryInfoTypeVo = BeanCopyUtils.objClone(areaCountryInfoType, AreaCountryInfoTypeVo::new);
        areaCountryInfoTypeVo.setFkTableName(TableEnum.INSTITUTION_COUNTRY_INFO_TYPE.key);
        return areaCountryInfoTypeVo;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ValidList<AreaCountryInfoTypeDto> areaCountryInfoTypeDtos) {
        if (GeneralTool.isEmpty(areaCountryInfoTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = areaCountryInfoTypeMapper.getMaxViewOrder();
        for (AreaCountryInfoTypeDto areaCountryInfoTypeDto : areaCountryInfoTypeDtos) {
            if (GeneralTool.isEmpty(areaCountryInfoTypeDto.getId())) {
                AreaCountryInfoType areaCountryInfoType = BeanCopyUtils.objClone(areaCountryInfoTypeDto, AreaCountryInfoType::new);
                //设置最大排序值
                areaCountryInfoType.setViewOrder(maxViewOrder);
                utilService.updateUserInfoToEntity(areaCountryInfoType);
                areaCountryInfoTypeMapper.insertSelective(areaCountryInfoType);
                maxViewOrder++;
            } else {
                AreaCountryInfoType areaCountryInfoType = BeanCopyUtils.objClone(areaCountryInfoTypeDto, AreaCountryInfoType::new);
                utilService.updateUserInfoToEntity(areaCountryInfoType);
                areaCountryInfoTypeMapper.updateById(areaCountryInfoType);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (areaCountryInfoTypeMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //删除校验
        deleteService.deleteValidateAreaCountryInfoType(id);
        areaCountryInfoTypeMapper.deleteById(id);
        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.INSTITUTION_AREA_CITY_INFO_TYPE.key, id);
    }

    @Override
    public AreaCountryInfoTypeVo updateAreaCountryInfoType(AreaCountryInfoTypeDto areaCountryInfoTypeDto) {
        if (areaCountryInfoTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaCountryInfoType result = areaCountryInfoTypeMapper.selectById(areaCountryInfoTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        AreaCountryInfoType areaCountryInfoType = BeanCopyUtils.objClone(areaCountryInfoTypeDto, AreaCountryInfoType::new);
        utilService.updateUserInfoToEntity(areaCountryInfoType);
        areaCountryInfoTypeMapper.updateById(areaCountryInfoType);
        return findAreaCountryInfoTypeById(areaCountryInfoType.getId());
    }

    @Override
    public List<AreaCountryInfoTypeVo> getAreaCountryInfoTypes(AreaCountryInfoTypeDto areaCountryInfoTypeDto, Page page) {
        LambdaQueryWrapper<AreaCountryInfoType> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(areaCountryInfoTypeDto)) {
            if (GeneralTool.isNotEmpty(areaCountryInfoTypeDto.getTypeName())) {
                wrapper.like(AreaCountryInfoType::getTypeName, areaCountryInfoTypeDto.getTypeName());
            }
        }
        wrapper.orderByDesc(AreaCountryInfoType::getViewOrder);
        //获取分页数据
        IPage<AreaCountryInfoType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<AreaCountryInfoType> areaCountryInfoTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<AreaCountryInfoTypeVo> dtos = new ArrayList<>();
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(areaCountryInfoTypes) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(areaCountryInfoTypes, ProjectKeyEnum.getInitialValue(language));
        }
        for (AreaCountryInfoType areaCountryInfoType : areaCountryInfoTypes) {
            AreaCountryInfoTypeVo areaCountryInfoTypeVo = BeanCopyUtils.objClone(areaCountryInfoType, AreaCountryInfoTypeVo::new);
            areaCountryInfoTypeVo.setFkTableName(TableEnum.INSTITUTION_COUNTRY_INFO_TYPE.key);
            dtos.add(areaCountryInfoTypeVo);
        }
        return dtos;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<AreaCountryInfoTypeDto> areaCountryInfoTypeDtos) {
        if (GeneralTool.isEmpty(areaCountryInfoTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_is_not_empty"));
        }
        AreaCountryInfoType ro = BeanCopyUtils.objClone(areaCountryInfoTypeDtos.get(0), AreaCountryInfoType::new);
        Integer oneorder = ro.getViewOrder();
        AreaCountryInfoType rt = BeanCopyUtils.objClone(areaCountryInfoTypeDtos.get(1), AreaCountryInfoType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        areaCountryInfoTypeMapper.updateById(ro);
        areaCountryInfoTypeMapper.updateById(rt);
    }

    @Override
    public List<AreaCountryInfoTypeVo> getAreaCountryInfoTypeList() {
        List<AreaCountryInfoType> areaCountryInfoTypes = this.areaCountryInfoTypeMapper.selectList(Wrappers.<AreaCountryInfoType>query().lambda().orderByDesc(AreaCountryInfoType::getViewOrder));
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(areaCountryInfoTypes) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(areaCountryInfoTypes, ProjectKeyEnum.getInitialValue(language));
        }
        List<AreaCountryInfoTypeVo> dtos = new ArrayList<>();
        for (AreaCountryInfoType areaCountryInfoType : areaCountryInfoTypes) {
            AreaCountryInfoTypeVo areaCountryInfoTypeVo = BeanCopyUtils.objClone(areaCountryInfoType, AreaCountryInfoTypeVo::new);
            areaCountryInfoTypeVo.setFkTableName(TableEnum.INSTITUTION_COUNTRY_INFO_TYPE.key);
            dtos.add(areaCountryInfoTypeVo);
        }
        return dtos;
    }

    @Override
    public String getAreaCountryInfoTypeNameById(Long areaCountryInfoTypeId) {
        return areaCountryInfoTypeMapper.getAreaCountryInfoTypeNameById(areaCountryInfoTypeId);
    }
}
