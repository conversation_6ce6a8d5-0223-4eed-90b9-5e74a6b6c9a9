package com.get.institutioncenter.service;

import com.get.common.result.Page;
import com.get.core.mybatis.base.BaseService;
import com.get.institutioncenter.vo.InstitutionProviderInstitutionVo;
import com.get.institutioncenter.vo.ProviderInstitutionRelationVo;
import com.get.institutioncenter.entity.InstitutionProviderInstitution;
import com.get.institutioncenter.dto.InstitutionProviderInstitutionDto;
import com.get.institutioncenter.dto.ProviderInstitutionRelationDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/24
 * @TIME: 16:40
 * @Description: 学校提供商管理-学校绑定配置
 **/
public interface IInstitutionProviderInstitutionService extends BaseService<InstitutionProviderInstitution> {

    /**
     * 编辑提供商和学校的关系
     *
     * @param providerInstitutionVos
     * @throws
     */
    void editProviderInstitutionRelation(List<InstitutionProviderInstitutionDto> providerInstitutionVos);


    /**
     * @return java.util.List<com.get.institutioncenter.vo.ProviderInstitutionRelationVo>
     * @Description: 获取提供商和学校的关系 （回显）
     * @Param [institutionVo, page]
     * <AUTHOR>
     */
    List<ProviderInstitutionRelationVo> getProviderInstitutionRelation(ProviderInstitutionRelationDto institutionVo, Page page);

    /**
     * @return com.get.institutioncenter.vo.InstitutionProviderInstitutionVo
     * @Description: 获取所有选中关系
     * @Param [institutionVo]
     * <AUTHOR>
     */
    List<InstitutionProviderInstitutionVo> getAllRelation(Long fkInstitutionProviderId, Long fkInstitutionId);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
    void delete(Long id);
}
