package com.get.institutioncenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.component.TranslationHelp;
import com.get.institutioncenter.dao.ContractTypeMapper;
import com.get.institutioncenter.vo.ContractTypeVo;
import com.get.institutioncenter.entity.ContractType;
import com.get.institutioncenter.service.IContractTypeService;
import com.get.institutioncenter.dto.ContractTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/29 10:27
 * @verison: 1.0
 * @description: 合同类型管理实现类
 */
@Service
public class ContractTypeServiceImpl extends BaseServiceImpl<ContractTypeMapper, ContractType> implements IContractTypeService {

    @Resource
    private ContractTypeMapper contractTypeMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private TranslationHelp translationHelp;

    @Override
    public ContractTypeVo findContractTypeById(Long id) {

        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ContractType contractType = contractTypeMapper.selectById(id);
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(contractType) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(Collections.singletonList(contractType), ProjectKeyEnum.getInitialValue(language));
        }
        return BeanCopyUtils.objClone(contractType, ContractTypeVo::new);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(ValidList<ContractTypeDto> contractTypeDtos) {
        if (GeneralTool.isEmpty(contractTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //获取最大排序(防止每次都要去查最大值，所以在外面查出一次，循环几次 就自增几次)
        Integer maxViewOrder = contractTypeMapper.getMaxViewOrder();
        for (ContractTypeDto contractTypeDto : contractTypeDtos) {
            if (GeneralTool.isEmpty(contractTypeDto.getId())) {
                if (validateAdd(contractTypeDto)) {
                    ContractType contractType = BeanCopyUtils.objClone(contractTypeDto, ContractType::new);
                    contractType.setViewOrder(maxViewOrder);
                    utilService.updateUserInfoToEntity(contractType);
                    contractTypeMapper.insertSelective(contractType);
                    maxViewOrder++;
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("Contract_type_name_exists"));
                }
            } else {
                if (validateUpdate(contractTypeDto)) {
                    ContractType contractType = BeanCopyUtils.objClone(contractTypeDto, ContractType::new);
                    utilService.updateUserInfoToEntity(contractType);
                    contractTypeMapper.updateById(contractType);
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("Contract_type_name_exists"));
                }
            }
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (findContractTypeById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        contractTypeMapper.deleteById(id);
    }

    @Override
    public ContractTypeVo updateContractType(ContractTypeDto contractTypeDto) {
        if (contractTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ContractType result = contractTypeMapper.selectById(contractTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (validateUpdate(contractTypeDto)) {
            ContractType contractType = BeanCopyUtils.objClone(contractTypeDto, ContractType::new);
            utilService.updateUserInfoToEntity(contractType);
            contractTypeMapper.updateById(contractType);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("Contract_type_name_exists"));
        }
        return findContractTypeById(contractTypeDto.getId());
    }

    @Override
    public List<ContractTypeVo> getContractTypes(ContractTypeDto contractTypeDto, Page page) {
        LambdaQueryWrapper<ContractType> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(contractTypeDto)) {
            if (GeneralTool.isNotEmpty(contractTypeDto.getKeyWord())) {
                wrapper.like(ContractType::getTypeName, contractTypeDto.getKeyWord());
            }
        }
        wrapper.orderByDesc(ContractType::getViewOrder);
        //获取分页数据
        IPage<ContractType> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<ContractType> contractTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<ContractTypeVo> convertDatas = new ArrayList<>();
        for (ContractType contractType : contractTypes) {
            ContractTypeVo contractTypeVo = BeanCopyUtils.objClone(contractType, ContractTypeVo::new);
            convertDatas.add(contractTypeVo);
        }
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(convertDatas) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(convertDatas, ProjectKeyEnum.getInitialValue(language));
        }
        return convertDatas;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void movingOrder(List<ContractTypeDto> contractTypeDtos) {
        if (GeneralTool.isEmpty(contractTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        ContractType ro = BeanCopyUtils.objClone(contractTypeDtos.get(0), ContractType::new);
        Integer oneorder = ro.getViewOrder();
        ContractType rt = BeanCopyUtils.objClone(contractTypeDtos.get(1), ContractType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        contractTypeMapper.updateById(ro);
        contractTypeMapper.updateById(rt);
    }

    @Override
    public List<ContractTypeVo> getContractTypeSelect() {
        LambdaQueryWrapper<ContractType> wrapper = new LambdaQueryWrapper();
        wrapper.orderByDesc(ContractType::getViewOrder);
        List<ContractType> contractTypes = contractTypeMapper.selectList(wrapper);
        List<ContractTypeVo> convertDatas = new ArrayList<>();
        for (ContractType contractType : contractTypes) {
            ContractTypeVo contractTypeVo = BeanCopyUtils.objClone(contractType, ContractTypeVo::new);
            convertDatas.add(contractTypeVo);
        }
        String language = SecureUtil.getLocale();
        if (GeneralTool.isNotEmpty(convertDatas) && GeneralTool.isNotEmpty(ProjectKeyEnum.getInitialValue(language))) {
            translationHelp.translation(convertDatas, ProjectKeyEnum.getInitialValue(language));
        }
        return convertDatas;
    }

    private boolean validateAdd(ContractTypeDto contractTypeDto) {
        LambdaQueryWrapper<ContractType> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractType::getTypeName, contractTypeDto.getTypeName());
        List<ContractType> list = contractTypeMapper.selectList(wrapper);
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(ContractTypeDto contractTypeDto) {
        LambdaQueryWrapper<ContractType> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ContractType::getTypeName, contractTypeDto.getTypeName());
        List<ContractType> list = contractTypeMapper.selectList(wrapper);
        return list.size() <= 0 || list.get(0).getId().equals(contractTypeDto.getId());
    }
}
