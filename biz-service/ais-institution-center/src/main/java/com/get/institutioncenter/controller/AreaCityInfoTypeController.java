package com.get.institutioncenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.institutioncenter.dto.AreaCityInfoTypeDto;
import com.get.institutioncenter.vo.AreaCityInfoTypeVo;
import com.get.institutioncenter.service.IAreaCityInfoTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/3/1 17:06
 * @verison: 1.0
 * @description:
 */
@Api(tags = "城市资讯类型管理")
@RestController
@RequestMapping("institution/areaCityInfoType")
public class AreaCityInfoTypeController {
    @Resource
    private IAreaCityInfoTypeService areaCityInfoTypeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AreaCityInfoTypeVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "学校中心/城市资讯类型管理/城市资讯类型详情")
    @GetMapping("/{id}")
    public ResponseBo<AreaCityInfoTypeVo> detail(@PathVariable("id") Long id) {
        AreaCityInfoTypeVo data = areaCityInfoTypeService.findAreaCityInfoTypeById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :批量新增信息
     * @Param [areaCityInfoTypeDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/城市资讯类型管理/新增城市资讯类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated(AreaCityInfoTypeDto.Add.class) ValidList<AreaCityInfoTypeDto> areaCityInfoTypeDtos) {
        areaCityInfoTypeService.batchAdd(areaCityInfoTypeDtos);
        return SaveResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :删除信息
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.DELETE, description = "学校中心/城市资讯类型管理/删除城市资讯类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        areaCityInfoTypeService.delete(id);
        return DeleteResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AreaCityInfoTypeVo>
     * @Description :修改信息
     * @Param [areaCityInfoTypeDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/城市资讯类型管理/更新城市资讯类型")
    @PostMapping("update")
    public ResponseBo<AreaCityInfoTypeVo> update(@RequestBody  @Validated(AreaCityInfoTypeDto.Update.class)   AreaCityInfoTypeDto areaCityInfoTypeDto) {
        return UpdateResponseBo.ok(areaCityInfoTypeService.updateAreaCityInfoType(areaCityInfoTypeDto));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.salecenter.vo.AreaCityInfoTypeVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为搜索关键字(类型名称)")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "学校中心/城市资讯类型管理/查询城市资讯类型")
    @PostMapping("datas")
    public ResponseBo<AreaCityInfoTypeVo> datas(@RequestBody SearchBean<AreaCityInfoTypeDto> page) {
        List<AreaCityInfoTypeVo> datas = areaCityInfoTypeService.getAreaCityInfoTypes(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [areaCityInfoTypeDtos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "学校中心/城市资讯类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<AreaCityInfoTypeDto> areaCityInfoTypeDtos) {
        areaCityInfoTypeService.movingOrder(areaCityInfoTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :城市资讯类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "城市资讯类型下拉框数据", notes = "")
    @GetMapping("getAreaCityInfoTypeList")
    public ResponseBo<AreaCityInfoTypeVo> getAreaCityInfoTypeList() {
        List<AreaCityInfoTypeVo> datas = areaCityInfoTypeService.getAreaCityInfoTypeList();
        return new ListResponseBo<>(datas);
    }
}
