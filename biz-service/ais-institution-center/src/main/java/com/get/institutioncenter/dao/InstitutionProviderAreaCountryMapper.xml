<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.institutioncenter.dao.InstitutionProviderAreaCountryMapper">

  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionProviderAreaCountry" keyProperty="id" useGeneratedKeys="true">
    insert into r_institution_provider_area_country
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInstitutionProviderId != null">
        fk_institution_provider_id,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionProviderId != null">
        #{fkInstitutionProviderId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

    <select id="getInstitutionProviderAreaCountrySelect" resultType="com.get.institutioncenter.vo.InstitutionProviderAreaCountryVo">
        SELECT
            a.fk_area_country_id,
            CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END areaCountryName
        FROM
            r_institution_provider_area_country a
            LEFT JOIN u_area_country b ON a.fk_area_country_id = b.id
        WHERE
            a.fk_institution_provider_id = #{institutionProviderId}
    </select>

    <select id="getAreaCountryNameByProviderId" resultType="java.lang.String">
        SELECT group_concat(c.name_chn separator '，') countryNames
        FROM  r_institution_provider_area_country p
        LEFT join u_area_country c on p.fk_area_country_id=c.id
        where p.fk_institution_provider_id=#{providerId}
    </select>

  <select id="deleteByProviderId">
    delete from r_institution_provider_area_country where
    <if test="id!=null and id !=''" >
        fk_institution_provider_id = #{id}
    </if>
  </select>
  <select id="getInstitutionProviderAreaCountryStr"
          resultType="com.get.institutioncenter.vo.InstitutionProviderAreaCountryVo">
    SELECT
      a.fk_institution_provider_id,
      GROUP_CONCAT( b.name,"（",b.name_chn,"）" ) AS areaCountryNames
    FROM
      `r_institution_provider_area_country` as a LEFT JOIN u_area_country as b on a.fk_area_country_id = b.id
    <where>
      <if test="providerIds != null and providerIds.size()>0">
        AND a.fk_institution_provider_id IN
        <foreach collection="providerIds" item="providerId" index="index" open="(" separator="," close=")">
          #{providerId}
        </foreach>
      </if>
    </where>
    GROUP BY
      a.fk_institution_provider_id
  </select>
  <select id="getAreaCountryIdsByProviderId" resultType="java.lang.Long">
    SELECT fk_area_country_id FROM r_institution_provider_area_country WHERE  fk_institution_provider_id = #{providerId}
  </select>
</mapper>