package com.get.resumecenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.utils.ValidList;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.resumecenter.vo.IndustryTypeVo;
import com.get.resumecenter.entity.IndustryType;
import com.get.resumecenter.service.IIndustryTypeService;
import com.get.resumecenter.dto.IndustryTypeDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/30
 * @TIME: 18:23
 * @Description:
 **/
@Api(tags = "行业类型管理")
@RestController
@RequestMapping("resume/industrytype")
public class IndustryTypeController {
    @Resource
    IIndustryTypeService industryTypeService;

    /**
     * @return com.get.common.result.ResponseBo<com.get.resumecenter.vo.IndustryTypeVo>
     * @Description: 列表数据
     * @Param [industryTypeDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "keyWord为关键词")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.LIST, description = "人才中心/行业类型管理/查询行业类型")
    @PostMapping("datas")
    public ResponseBo<IndustryTypeVo> datas(@RequestBody IndustryTypeDto industryTypeDto) {
        List<IndustryTypeVo> datas = industryTypeService.datas(industryTypeDto);
        return new ListResponseBo<>(datas);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DETAIL, description = "人才中心/行业类型管理/行业类型详情")
    @GetMapping("/{id}")
    public ResponseBo<IndustryTypeVo> detail(@PathVariable("id") Long id) {
        IndustryTypeVo data = industryTypeService.findIndustryTypeById(id);
        IndustryTypeVo industryTypeVo = BeanCopyUtils.objClone(data, IndustryTypeVo::new);
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", industryTypeVo);
        return responseBo;
    }


    /**
     * 新增信息
     *
     * @param industryTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/行业类型管理/新增行业类型")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(IndustryTypeDto.Add.class)  IndustryTypeDto industryTypeDto) {
        return SaveResponseBo.ok(this.industryTypeService.addIndustryType(industryTypeDto));
    }

    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除接口", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DELETE, description = "人才中心/行业类型管理/删除行业类型")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        this.industryTypeService.delete(id);
        return ResponseBo.ok();
    }

    /**
     * 修改信息
     *
     * @param industryTypeDto
     * @return
     * @
     */
    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/行业类型管理/更新行业类型")
    @PostMapping("update")
    public ResponseBo<IndustryTypeVo> update(@RequestBody @Validated(IndustryTypeDto.Update.class) IndustryTypeDto industryTypeDto) {
        return UpdateResponseBo.ok(industryTypeService.updateIndustryType(industryTypeDto));
    }


    /**
     * 批量新增信息
     *
     * @param industryTypeDtos
     * @return
     * @
     */
    @ApiOperation(value = "批量新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.ADD, description = "人才中心/行业类型管理/批量保存行业类型")
    @PostMapping("batchAdd")
    public ResponseBo batchAdd(@RequestBody @Validated ValidList<IndustryTypeDto> industryTypeDtos) {
        industryTypeService.batchAdd(industryTypeDtos);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 行业类型下拉
     * @Param [providerId]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "行业类型下拉")
    @PostMapping("getIndustryTypeSelect")
    public ResponseBo<BaseSelectEntity> getIndustryTypeSelect() {
        return new ListResponseBo<>(industryTypeService.getIndustryTypeSelect());
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :上移下移
     * @Param [studentOfferItemStepVos]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.EDIT, description = "人才中心/行业类型管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<IndustryTypeDto> industryTypeDtos) {
        industryTypeService.movingOrder(industryTypeDtos);
        return ResponseBo.ok();
    }

}
