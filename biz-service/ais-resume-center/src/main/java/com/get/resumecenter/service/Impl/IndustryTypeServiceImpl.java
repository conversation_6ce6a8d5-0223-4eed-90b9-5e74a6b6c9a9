package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.resumecenter.dao.IndustryTypeMapper;
import com.get.resumecenter.vo.IndustryTypeVo;
import com.get.resumecenter.entity.IndustryType;
import com.get.resumecenter.service.IDeleteService;
import com.get.resumecenter.service.IIndustryTypeService;
import com.get.resumecenter.dto.IndustryTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/30
 * @TIME: 15:49
 * @Description:
 **/
@Service
public class IndustryTypeServiceImpl implements IIndustryTypeService {
    @Resource
    private IndustryTypeMapper industryTypeMapper;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private UtilService utilService;

    @Override
    public List<IndustryTypeVo> datas(IndustryTypeDto industryTypeDto) {
//        Example example = new Example(IndustryType.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<IndustryType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(industryTypeDto)) {
            if (GeneralTool.isNotEmpty(industryTypeDto.getTypeName())) {
//                criteria.andEqualTo("typeName", industryTypeDto.getTypeName());
                lambdaQueryWrapper.eq(IndustryType::getTypeName, industryTypeDto.getTypeName());
            }
            lambdaQueryWrapper.orderByDesc(IndustryType::getViewOrder);
        }
        List<IndustryType> industryTypes = industryTypeMapper.selectList(lambdaQueryWrapper);
        List<IndustryTypeVo> convertDatas = new ArrayList<>();
        for (IndustryType industryType : industryTypes) {
            IndustryTypeVo industryTypeVo = BeanCopyUtils.objClone(industryType, IndustryTypeVo::new);
            convertDatas.add(industryTypeVo);
        }
        return convertDatas;
    }

    @Override
    public IndustryTypeVo findIndustryTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        IndustryType industryType = industryTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(industryType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        IndustryTypeVo industryTypeVo = BeanCopyUtils.objClone(industryType, IndustryTypeVo::new);
        return industryTypeVo;
    }

    @Override
    public IndustryTypeVo updateIndustryType(IndustryTypeDto industryTypeDto) {
        if (industryTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(industryTypeDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        IndustryType rs = industryTypeMapper.selectById(industryTypeDto.getId());
        if (rs == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        IndustryType industryType = BeanCopyUtils.objClone(industryTypeDto, IndustryType::new);
        utilService.updateUserInfoToEntity(industryType);
        industryTypeMapper.updateById(industryType);
        return findIndustryTypeById(industryType.getId());
    }

    @Override
    public Long addIndustryType(IndustryTypeDto industryTypeDto) {
        IndustryType industryType = BeanCopyUtils.objClone(industryTypeDto, IndustryType::new);
        utilService.updateUserInfoToEntity(industryType);
        industryType.setViewOrder(industryTypeMapper.getMaxViewOrder());
        industryTypeMapper.insert(industryType);
        return industryType.getId();
    }

    @Override
    public void delete(Long id) {
        IndustryTypeVo industryType = findIndustryTypeById(id);
        if (industryType == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        deleteService.deleteValidateIndustryType(id);
        int i = industryTypeMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<IndustryTypeDto> industryTypeDtos) {
       /* if (validateAdd(industryTypeDtos)) {

        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }*/
        for (IndustryTypeDto industryTypeDto : industryTypeDtos) {
            if (GeneralTool.isEmpty(industryTypeDto.getId())) {
                IndustryType industryType = BeanCopyUtils.objClone(industryTypeDto, IndustryType::new);
                industryType.setViewOrder(industryTypeMapper.getMaxViewOrder());
                utilService.updateUserInfoToEntity(industryType);
                industryTypeMapper.insert(industryType);
            } else {
                IndustryType industryType = BeanCopyUtils.objClone(industryTypeDto, IndustryType::new);
                utilService.updateUserInfoToEntity(industryType);
                industryTypeMapper.updateById(industryType);
            }
        }
    }

    @Override
    public String getNameByTypeId(Long industryTypeId) {
        if (GeneralTool.isEmpty(industryTypeId)) {
            return null;
        }
        return industryTypeMapper.getTypeNameByTypeId(industryTypeId);
    }

    @Override
    public List<BaseSelectEntity> getIndustryTypeSelect() {
        return industryTypeMapper.getIndustryTypeSelect();
    }

    @Override
    public void movingOrder(List<IndustryTypeDto> industryTypeDtos) {
        if (GeneralTool.isEmpty(industryTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }

        IndustryType industryType = BeanCopyUtils.objClone(industryTypeDtos.get(0), IndustryType::new);
        IndustryType industryType2 = BeanCopyUtils.objClone(industryTypeDtos.get(1), IndustryType::new);

        Integer viewOrder = industryType.getViewOrder();
        industryType.setViewOrder(industryType2.getViewOrder());
        industryType2.setViewOrder(viewOrder);

        utilService.updateUserInfoToEntity(industryType);
        utilService.updateUserInfoToEntity(industryType2);
        industryTypeMapper.updateById(industryType);
        industryTypeMapper.updateById(industryType2);
    }

    private boolean validateAdd(IndustryTypeDto industryTypeDto) {
//        Example example = new Example(IndustryType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", industryTypeDto.getTypeName());

        List<IndustryType> list = this.industryTypeMapper.selectList(Wrappers.<IndustryType>lambdaQuery().eq(IndustryType::getTypeName, industryTypeDto.getTypeName()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateAdd(List<IndustryTypeDto> industryTypeDtos) {
        boolean success = true;
        for (IndustryTypeDto industryTypeDto : industryTypeDtos) {
//            Example example = new Example(IndustryType.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("typeName", industryTypeDto.getTypeName());
//            List<IndustryType> list = this.industryTypeMapper.selectByExample(example);

            List<IndustryType> list = this.industryTypeMapper.selectList(Wrappers.<IndustryType>lambdaQuery().eq(IndustryType::getTypeName, industryTypeDto.getTypeName()));
            if (!GeneralTool.isEmpty(list)) {
                success = false;
            }
        }
        return success;
    }

    private boolean validateUpdate(IndustryTypeDto industryTypeDto) {
//        Example example = new Example(ResumeType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", industryTypeDto.getTypeName());
//        List<IndustryType> list = this.industryTypeMapper.selectByExample(example);
        List<IndustryType> list = this.industryTypeMapper.selectList(Wrappers.<IndustryType>lambdaQuery().eq(IndustryType::getTypeName, industryTypeDto.getTypeName()));
        return list.size() <= 0 || list.get(0).getId().equals(industryTypeDto.getId());
    }
}
