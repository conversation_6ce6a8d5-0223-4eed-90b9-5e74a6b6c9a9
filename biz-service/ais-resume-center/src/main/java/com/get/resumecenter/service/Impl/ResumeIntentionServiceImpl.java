package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.resumecenter.dao.ResumeIntentionMapper;
import com.get.resumecenter.vo.ResumeIntentionVo;
import com.get.resumecenter.entity.ResumeIntention;
import com.get.resumecenter.service.IResumeIntentionService;
import com.get.resumecenter.dto.ResumeIntentionDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/4
 * @TIME: 18:17
 * @Description:
 **/
@Service
public class ResumeIntentionServiceImpl implements IResumeIntentionService {

    @Resource
    private ResumeIntentionMapper intentionMapper;
    @Autowired
    private UtilService utilService;


    @Override
    public ResumeIntentionVo getResumeIntentionDto(Long resumeId) {
        if (GeneralTool.isEmpty(resumeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resume_id_null"));
        }
        ResumeIntentionVo resumeIntentionVo = null;
//        Example example = new Example(ResumeIntention.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
        List<ResumeIntention> resumeIntentions = intentionMapper.selectList(Wrappers.<ResumeIntention>lambdaQuery().eq(ResumeIntention::getFkResumeId, resumeId));


        if (GeneralTool.isNotEmpty(resumeIntentions)) {
            resumeIntentionVo = BeanCopyUtils.objClone(resumeIntentions.get(0), ResumeIntentionVo::new);
        }
        return resumeIntentionVo;
    }

    @Override
    @Transactional
    public Long updateResumeIntention(ResumeIntentionDto intentionVo) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(intentionVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //删除之前的职业意向
//        Example example = new Example(ResumeIntention.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", intentionVo.getFkResumeId());
//        intentionMapper.deleteByExample(example);
        intentionMapper.delete(Wrappers.<ResumeIntention>lambdaQuery().eq(ResumeIntention::getFkResumeId, intentionVo.getFkResumeId()));
        //插入新记录
        ResumeIntention resumeIntention = BeanCopyUtils.objClone(intentionVo, ResumeIntention::new);
        utilService.updateUserInfoToEntity(resumeIntention);
        int i = intentionMapper.insertSelective(resumeIntention);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return resumeIntention.getId();
    }

    @Override
    public ResumeIntentionVo getResumeIntentionById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ResumeIntention resumeIntention = intentionMapper.selectById(id);
        return BeanCopyUtils.objClone(resumeIntention, ResumeIntentionVo::new);
    }

}
