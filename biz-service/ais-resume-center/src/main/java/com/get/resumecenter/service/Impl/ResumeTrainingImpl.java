package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.resumecenter.dao.ResumeTrainingMapper;
import com.get.resumecenter.vo.ResumeTrainingVo;
import com.get.resumecenter.entity.ResumeTraining;
import com.get.resumecenter.service.IResumeTrainingService;
import com.get.resumecenter.dto.ResumeTrainingDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/11
 * @TIME: 11:01
 * @Description: 简历培训经历实现类
 **/
@Service
public class ResumeTrainingImpl implements IResumeTrainingService {
    @Resource
    private ResumeTrainingMapper trainingMapper;
    @Resource
    private UtilService utilService;

    @Override
    public List<ResumeTrainingVo> getResumeTrainingListDto(Long resumeId) {
        if (GeneralTool.isEmpty(resumeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resume_id_null"));
        }
//        Example example = new Example(ResumeTraining.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
        List<ResumeTraining> resumeTrainings = trainingMapper.selectList(Wrappers.<ResumeTraining>lambdaQuery().eq(ResumeTraining::getFkResumeId, resumeId));
        return resumeTrainings.stream()
                .map(resumeTraining -> BeanCopyUtils.objClone(resumeTraining, ResumeTrainingVo::new)).collect(Collectors.toList());
    }

    @Override
    public ResumeTrainingVo getResumeTrainingById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        ResumeTraining resumeTraining = trainingMapper.selectById(id);
        return BeanCopyUtils.objClone(resumeTraining, ResumeTrainingVo::new);
    }

    @Override
    public Long addResumeTraining(ResumeTrainingDto trainingVo) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(trainingVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ResumeTraining resumeTraining = BeanCopyUtils.objClone(trainingVo, ResumeTraining::new);
        utilService.updateUserInfoToEntity(resumeTraining);
        int i = trainingMapper.insertSelective(resumeTraining);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return resumeTraining.getId();
    }

    @Override
    public ResumeTrainingVo updateResumeTraining(ResumeTrainingDto trainingVo) {
//        if (!StaffContext.getStaff().getIsAdmin()) {
//            if (!StaffContext.getStaff().getIsModifiedResume()) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
//            }
//        }
        if (!GetAuthInfo.isAdmin()) {
            if (!SecureUtil.getIsModifiedResume()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("edit_resume_fail"));
            }
        }
        if (GeneralTool.isEmpty(trainingVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        ResumeTraining resumeTraining = BeanCopyUtils.objClone(trainingVo, ResumeTraining::new);
        utilService.updateUserInfoToEntity(resumeTraining);
        trainingMapper.updateById(resumeTraining);
        return getResumeTrainingById(resumeTraining.getId());
    }

    @Override
    public void deleteResumeTraining(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        trainingMapper.deleteById(id);
    }
}
