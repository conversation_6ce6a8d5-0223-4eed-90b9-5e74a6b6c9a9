package com.get.resumecenter.service;


import com.get.resumecenter.vo.ResumeEducationVo;
import com.get.resumecenter.dto.ResumeEducationDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 14:10
 * @Description: 教育经历
 **/
public interface IResumeEducationService {

    /**
     * 根据简历id 查询教育经历
     *
     * @param resumeId
     * @return
     */
    List<ResumeEducationVo> getResumeEducationListDto(Long resumeId);


    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    ResumeEducationVo getResumeEducationById(Long id);


    /**
     * 添加
     *
     * @param educationVo
     * @return
     */
    Long addResumeEducation(ResumeEducationDto educationVo);

    /**
     * 修改
     *
     * @param educationVo
     * @return
     */
    ResumeEducationVo updateResumeEducation(ResumeEducationDto educationVo);

    /**
     * 删除
     *
     * @param id
     */
    void deleteResumeEducation(Long id);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 根据学校查询简历id
     * @Param [institution]
     * <AUTHOR>
     */
    List<Long> getResumeIdByInstitution(String institution);

}
