package com.get.resumecenter.service;

import com.get.common.result.Page;
import com.get.resumecenter.dto.MediaAndAttachedDto;
import com.get.resumecenter.vo.MediaAndAttachedVo;
import com.get.resumecenter.vo.ResumeVo;
import com.get.resumecenter.dto.ResumeDto;
import com.get.resumecenter.dto.query.ResumeQueryDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/31
 * @TIME: 16:04
 * @Description: 简历管理
 **/
public interface IResumeService {

    /**
     * 分页查询所有简历
     *
     * @param searchVo
     * @param page
     * @return
     * @
     */
    List<ResumeVo> getResumeListDtos(ResumeQueryDto searchVo, Page page);


    /**
     * 新增
     *
     * @param resumeDto
     * @return
     */
    Long addResume(ResumeDto resumeDto);


    /**
     * 修改
     *
     * @param resumeDto
     * @return
     * @
     */
    ResumeVo updateResume(ResumeDto resumeDto);


    /**
     * @param resumeId
     * @return
     * @
     */
    ResumeVo getResumeById(Long resumeId);

    /**
     * @return java.util.List<com.get.resumecenter.vo.MediaAndAttachedDto>
     * @Description: 保存上传的文件
     * @Param [mediaAttachedVos]
     * <AUTHOR>
     */
    List<MediaAndAttachedVo> saveResumeMedia(List<MediaAndAttachedDto> mediaAttachedVos);

    /**
     * @return void
     * @Description: 删除
     * @Param [id]
     * <AUTHOR>
     */
    void deleteResume(Long id);

    /**
     * @param guid
     * @return
     * @
     */
    ResumeVo getResumeByGuid(String guid);
}
