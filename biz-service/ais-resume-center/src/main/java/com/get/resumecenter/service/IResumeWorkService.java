package com.get.resumecenter.service;


import com.get.resumecenter.vo.ResumeWorkVo;
import com.get.resumecenter.dto.ResumeWorkDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 11:20
 * @Description: 工作经验
 **/
public interface IResumeWorkService {

    /**
     * 获取简历的所有工作经验
     *
     * @param resumeId
     * @return
     */
    List<ResumeWorkVo> getResumeWorkListDto(Long resumeId);


    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    ResumeWorkVo getResumeWorkById(Long id);

    /**
     * 新增
     *
     * @param resumeWorkDto
     * @return
     */
    Long addResumeWork(ResumeWorkDto resumeWorkDto);

    /**
     * 修改
     *
     * @param resumeWorkDto
     * @return
     */
    ResumeWorkVo updateResumeWork(ResumeWorkDto resumeWorkDto);

    /**
     * @param id
     * @
     */
    void deleteResumeWork(Long id);

}
