package com.get.resumecenter.service;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.resumecenter.vo.OtherTypeVo;
import com.get.resumecenter.dto.OtherTypeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/30
 * @TIME: 10:54
 * @Description:简历其他类型业务类
 **/
public interface IOtherTypeService {
    /**
     * @return java.util.List<com.get.resumecenter.vo.OtherTypeVo>
     * @Description: 列表数据
     * @Param [otherTypeDto]
     * <AUTHOR>
     */
    List<OtherTypeVo> datas(OtherTypeDto otherTypeDto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    OtherTypeVo findOtherTypeById(Long id);

    /**
     * 修改
     *
     * @param otherTypeDto
     * @return
     */
    OtherTypeVo updateOtherType(OtherTypeDto otherTypeDto);

    /**
     * 保存
     *
     * @param otherTypeDto
     * @return
     */
    Long addOtherType(OtherTypeDto otherTypeDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param otherTypeDtos
     * @return
     */
    void batchAdd(List<OtherTypeDto> otherTypeDtos);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getOtherTypeSelect();

    /**
     * @return void
     * @Description: 上移下移
     * @Param [otherTypeDtos]
     * <AUTHOR>
     */
    void movingOrder(List<OtherTypeDto> otherTypeDtos);
}
