package com.get.resumecenter.service.Impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.resumecenter.dao.MediaAndAttachedMapper;
import com.get.resumecenter.dao.ResumeAttachmentMapper;
import com.get.resumecenter.dao.ResumeCertificateMapper;
import com.get.resumecenter.dao.ResumeEducationMapper;
import com.get.resumecenter.dao.ResumeIntentionMapper;
import com.get.resumecenter.dao.ResumeMapper;
import com.get.resumecenter.dao.ResumeOtherMapper;
import com.get.resumecenter.dao.ResumeSkillMapper;
import com.get.resumecenter.dao.ResumeTrainingMapper;
import com.get.resumecenter.dao.ResumeWorkMapper;
import com.get.resumecenter.entity.MediaAndAttached;
import com.get.resumecenter.entity.ResumeAttachment;
import com.get.resumecenter.entity.ResumeCertificate;
import com.get.resumecenter.entity.ResumeEducation;
import com.get.resumecenter.entity.ResumeIntention;
import com.get.resumecenter.entity.ResumeOther;
import com.get.resumecenter.entity.ResumeSkill;
import com.get.resumecenter.entity.ResumeTraining;
import com.get.resumecenter.entity.ResumeWork;
import com.get.resumecenter.service.IDeleteService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE: 2020/12/28
 * @TIME: 15:59
 * @Description:
 **/
@Service
public class DeleteServiceImpl implements IDeleteService {
    @Resource
    private ResumeCertificateMapper resumeCertificateMapper;
    @Resource
    private ResumeEducationMapper resumeEducationMapper;
    @Resource
    private ResumeIntentionMapper resumeIntentionMapper;
    @Resource
    private ResumeOtherMapper resumeOtherMapper;
    @Resource
    private ResumeSkillMapper resumeSkillMapper;
    @Resource
    private ResumeTrainingMapper resumeTrainingMapper;
    @Resource
    private ResumeWorkMapper resumeWorkMapper;
    @Resource
    private ResumeMapper resumeMapper;
    @Resource
    private ResumeAttachmentMapper resumeAttachmentMapper;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;


    @Override
    public Boolean deleteValidateResume(Long resumeId) {
        deleteResumeCertificate(resumeId);
        deleteResumeEducation(resumeId);
        deleteResumeIntention(resumeId);
        deleteResumeOther(resumeId);
        deleteResumeSkill(resumeId);
        deleteResumeTraining(resumeId);
        deleteResumeWork(resumeId);
        deleteResumeAttachment(resumeId);
        deleteMedia(resumeId, TableEnum.RESUME_RESUME.key);
        return true;
    }

    @Override
    public Boolean deleteValidateIndustryType(Long industryTypeId) {
        if (resumeWorkMapper.isExistByIndustryTypeId(industryTypeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("industryType_resumeWork_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateOtherType(Long otherTypeId) {
        if (resumeOtherMapper.isExistByOtherTypeId(otherTypeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("otherType_resumeOther_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateResumeType(Long resumeTypeId) {
        if (resumeMapper.isExistByResumeTypeId(resumeTypeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resumeType_resume_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateSkillType(Long skillTypeId) {
        if (resumeSkillMapper.isExistBySkillTypeId(skillTypeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("skillType_resumeSkill_data_association"));
        }
        return true;
    }

    private void deleteMedia(Long agentId, String tableName) {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableName", tableName);
//        criteria.andEqualTo("fkTableId", agentId);
//        mediaAndAttachedMapper.deleteByExample(example);
        mediaAndAttachedMapper.delete(Wrappers.<MediaAndAttached>lambdaQuery().eq(MediaAndAttached::getFkTableName, tableName).eq(MediaAndAttached::getFkTableId, agentId));
    }

    private void deleteResumeAttachment(Long resumeId) {
//        Example example = new Example(ResumeAttachment.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
//        resumeAttachmentMapper.deleteByExample(example);
        resumeAttachmentMapper.delete(Wrappers.<ResumeAttachment>lambdaQuery().eq(ResumeAttachment::getFkResumeId, resumeId));

    }

    private void deleteResumeWork(Long resumeId) {
//        Example example = new Example(ResumeWork.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
//        resumeWorkMapper.deleteByExample(example);
        resumeWorkMapper.delete(Wrappers.<ResumeWork>lambdaQuery().eq(ResumeWork::getFkResumeId, resumeId));

    }

    private void deleteResumeTraining(Long resumeId) {
//        Example example = new Example(ResumeTraining.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
//        resumeTrainingMapper.deleteByExample(example);
        resumeTrainingMapper.delete(Wrappers.<ResumeTraining>lambdaQuery().eq(ResumeTraining::getFkResumeId, resumeId));

    }

    private void deleteResumeSkill(Long resumeId) {
//        Example example = new Example(ResumeSkill.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
//        resumeSkillMapper.deleteByExample(example);
        resumeSkillMapper.delete(Wrappers.<ResumeSkill>lambdaQuery().eq(ResumeSkill::getFkResumeId, resumeId));
    }

    private void deleteResumeOther(Long resumeId) {
//        Example example = new Example(ResumeOther.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
//        resumeOtherMapper.deleteByExample(example);
        resumeOtherMapper.delete(Wrappers.<ResumeOther>lambdaQuery().eq(ResumeOther::getFkResumeId, resumeId));
    }

    private void deleteResumeIntention(Long resumeId) {
//        Example example = new Example(ResumeIntention.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
//        resumeIntentionMapper.deleteByExample(example);

        resumeIntentionMapper.delete(Wrappers.<ResumeIntention>lambdaQuery().eq(ResumeIntention::getFkResumeId, resumeId));

    }

    private void deleteResumeEducation(Long resumeId) {
//        Example example = new Example(ResumeEducation.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
//        resumeEducationMapper.deleteByExample(example);

        resumeEducationMapper.delete(Wrappers.<ResumeEducation>lambdaQuery().eq(ResumeEducation::getFkResumeId, resumeId));

    }

    private void deleteResumeCertificate(Long resumeId) {
//        Example example = new Example(ResumeCertificate.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeId", resumeId);
//        resumeCertificateMapper.deleteByExample(example);
        resumeCertificateMapper.delete(Wrappers.<ResumeCertificate>lambdaQuery().eq(ResumeCertificate::getFkResumeId, resumeId));
    }

}
