package com.get.resumecenter.service;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.resumecenter.vo.ResumeTypeVo;
import com.get.resumecenter.dto.ResumeTypeDto;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/7/30
 * @TIME: 10:55
 * @Description:简历类型业务类
 **/
public interface IResumeTypeService {
    /**
     * @return java.util.List<com.get.resumecenter.vo.ResumeTypeVo>
     * @Description: 列表数据
     * @Param [resumeTypeDto]
     * <AUTHOR>
     */
    List<ResumeTypeVo> datas(ResumeTypeDto resumeTypeDto);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    ResumeTypeVo findResumeTypeById(Long id);

    /**
     * 修改
     *
     * @param resumeTypeDto
     * @return
     */
    ResumeTypeVo updateResumeType(ResumeTypeDto resumeTypeDto);

    /**
     * 保存
     *
     * @param resumeTypeDto
     * @return
     */
    Long addResumeType(ResumeTypeDto resumeTypeDto);

    /**
     * 删除
     *
     * @param id
     */
    void delete(Long id);

    /**
     * 批量保存
     *
     * @param resumeTypeDtos
     * @return
     */
    void batchAdd(List<ResumeTypeDto> resumeTypeDtos);

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getResumeTypeSelect();

    /**
     * @return void
     * @Description: 上移下移
     * @Param [resumeTypeDtos]
     * <AUTHOR>
     */
    void movingOrder(List<ResumeTypeDto> resumeTypeDtos);
}
