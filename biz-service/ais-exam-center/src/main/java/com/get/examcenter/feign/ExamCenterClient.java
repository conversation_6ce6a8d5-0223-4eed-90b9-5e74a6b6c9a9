package com.get.examcenter.feign;

import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.examcenter.service.ExaminationService;
import com.get.examcenter.service.IQuestionTypeService;
import com.get.examcenter.service.UserService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Set;

@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class  ExamCenterClient implements IExamCenterClient {

    private final ExaminationService examinationService;
    private final IQuestionTypeService questionTypeService;
    private final UserService userService;

    @Override
    public Result<Map<Long, String>> getExaminationNamesByExaminationIds(Set<Long> examinationIds) {
        return Result.data(examinationService.getExaminationNamesByExaminationIds(examinationIds));
    }

    @Override
    public Result<Map<Long, String>> getNamesByQuestionTypeIds(Set<Long> questionTypeIds) {
        return Result.data(questionTypeService.getNamesByQuestionTypeIds(questionTypeIds));
    }

    @Override
    public Result<Set<Long>> getUserIdsByStaffIds(Set<Long> fkStaffIds) {
        return Result.data(userService.getUserIdsByStaffIds(fkStaffIds));
    }
}
