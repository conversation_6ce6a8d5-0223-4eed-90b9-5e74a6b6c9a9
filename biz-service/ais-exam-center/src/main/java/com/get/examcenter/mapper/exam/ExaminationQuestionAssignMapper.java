package com.get.examcenter.mapper.exam;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.examcenter.entity.ExaminationQuestionAssign;

import java.util.List;

@DS("examdb")
public interface ExaminationQuestionAssignMapper extends BaseMapper<ExaminationQuestionAssign> {
    int insert(ExaminationQuestionAssign record);

    int insertSelective(ExaminationQuestionAssign record);

    int updateByPrimaryKeySelective(ExaminationQuestionAssign record);

    int updateByPrimaryKey(ExaminationQuestionAssign record);

    List<ExaminationQuestionAssign> getListByPaperIdAndExist(Long fkExaminationPaperId);

    int updateActiveById(Long id);
}