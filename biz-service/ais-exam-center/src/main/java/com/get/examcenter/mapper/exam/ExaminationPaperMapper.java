package com.get.examcenter.mapper.exam;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.examcenter.vo.ExaminationActiveAndTimeVo;
import com.get.examcenter.vo.ExaminationPaperVo;
import com.get.examcenter.entity.ExaminationPaper;
import com.get.examcenter.dto.ExaminationPaperListDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DS("examdb")
public interface ExaminationPaperMapper extends BaseMapper<ExaminationPaper> {
    int insert(ExaminationPaper record);

    int insertSelective(ExaminationPaper record);

    int updateByPrimaryKeySelective(ExaminationPaper record);

    int updateByPrimaryKey(ExaminationPaper record);

    List<ExaminationPaperVo> getExaminationPaperList(IPage<ExaminationPaperVo> iPage, @Param("examinationPaperListDto") ExaminationPaperListDto examinationPaperListDto,
                                                     @Param("fkCompanyIds") Set<Long> fkCompanyIds);

    //获取考试和考场激活状态以及考试开始时间和结束时间
    ExaminationActiveAndTimeVo getExaminationByexaminationPaperNum(String fkExaminationPaperNum);

    List<ExaminationPaper> getExaminationPaper(@Param("fkCompanyId") String fkCompanyId);
}