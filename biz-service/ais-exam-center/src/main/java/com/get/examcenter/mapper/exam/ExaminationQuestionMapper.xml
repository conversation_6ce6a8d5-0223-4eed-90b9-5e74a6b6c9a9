<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.examcenter.mapper.exam.ExaminationQuestionMapper">
    <insert id="insert" parameterType="com.get.examcenter.entity.ExaminationQuestion">
    insert into m_examination_question (id, fk_question_type_id, num, 
      question_type, question, score, 
      time_limit, is_review, is_retest, 
      is_active, view_order, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkQuestionTypeId,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR}, 
      #{questionType,jdbcType=INTEGER}, #{question,jdbcType=VARCHAR}, #{score,jdbcType=INTEGER}, 
      #{timeLimit,jdbcType=INTEGER}, #{isReview,jdbcType=BIT}, #{isRetest,jdbcType=BIT}, 
      #{isActive,jdbcType=BIT}, #{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
    <insert id="insertSelective" useGeneratedKeys="true" keyProperty="id"
            parameterType="com.get.examcenter.entity.ExaminationQuestion">
        insert into m_examination_question
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkQuestionTypeId != null">
                fk_question_type_id,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="questionType != null">
                question_type,
            </if>
            <if test="question != null">
                question,
            </if>
            <if test="score != null">
                score,
            </if>
            <if test="timeLimit != null">
                time_limit,
            </if>
            <if test="isReview != null">
                is_review,
            </if>
            <if test="isRetest != null">
                is_retest,
            </if>
            <if test="isActive != null">
                is_active,
            </if>
            <if test="isRequired != null">
                is_required,
            </if>
            <if test="viewOrder != null">
                view_order,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
            <if test="fkCompanyId != null">
                fk_company_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkQuestionTypeId != null">
                #{fkQuestionTypeId,jdbcType=BIGINT},
            </if>
            <if test="num != null">
                #{num,jdbcType=VARCHAR},
            </if>
            <if test="questionType != null">
                #{questionType,jdbcType=INTEGER},
            </if>
            <if test="question != null">
                #{question,jdbcType=VARCHAR},
            </if>
            <if test="score != null">
                #{score,jdbcType=INTEGER},
            </if>
            <if test="timeLimit != null">
                #{timeLimit,jdbcType=INTEGER},
            </if>
            <if test="isReview != null">
                #{isReview,jdbcType=BIT},
            </if>
            <if test="isRetest != null">
                #{isRetest,jdbcType=BIT},
            </if>
            <if test="isActive != null">
                #{isActive,jdbcType=BIT},
            </if>
            <if test="isRequired != null">
                #{isRequired,jdbcType=BIT},
            </if>
            <if test="viewOrder != null">
                #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="fkCompanyId != null">
                #{fkCompanyId,jdbcType=BIGINT},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.get.examcenter.entity.ExaminationQuestion">
        update m_examination_question
        <set>
            <if test="fkQuestionTypeId != null">
                fk_question_type_id = #{fkQuestionTypeId,jdbcType=BIGINT},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=VARCHAR},
            </if>
            <if test="questionType != null">
                question_type = #{questionType,jdbcType=INTEGER},
            </if>
            <if test="question != null">
                question = #{question,jdbcType=VARCHAR},
            </if>
            <if test="score != null">
                score = #{score,jdbcType=INTEGER},
            </if>
            <if test="timeLimit != null">
                time_limit = #{timeLimit,jdbcType=INTEGER},
            </if>
            <if test="isReview != null">
                is_review = #{isReview,jdbcType=BIT},
            </if>
            <if test="isRetest != null">
                is_retest = #{isRetest,jdbcType=BIT},
            </if>
            <if test="isActive != null">
                is_active = #{isActive,jdbcType=BIT},
            </if>
            <if test="viewOrder != null">
                view_order = #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.examcenter.entity.ExaminationQuestion">
    update m_examination_question
    set fk_question_type_id = #{fkQuestionTypeId,jdbcType=BIGINT},
      num = #{num,jdbcType=VARCHAR},
      question_type = #{questionType,jdbcType=INTEGER},
      question = #{question,jdbcType=VARCHAR},
      score = #{score,jdbcType=INTEGER},
      time_limit = #{timeLimit,jdbcType=INTEGER},
      is_review = #{isReview,jdbcType=BIT},
      is_retest = #{isRetest,jdbcType=BIT},
      is_active = #{isActive,jdbcType=BIT},
      is_required = #{isRequired,jdbcType=BIT},
      view_order = #{viewOrder,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      fk_company_id = #{fkCompanyId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>


    <select id="getExaminationQuestionByIdsAndCount" resultType="com.get.examcenter.vo.ExaminationQuestionVo">
        SELECT
        id AS id,
        fk_question_type_id AS fkQuestionTypeId,
        num AS num,
        question_type AS questionType,
        question AS question,
        score AS score,
        time_limit AS timeLimit,
        is_review AS isReview,
        is_retest AS isRetest,
        is_active AS isActive,
        view_order AS viewOrder,
        gmt_create AS gmtCreate,
        gmt_create_user AS gmtCreateUser,
        gmt_modified AS gmtModified,
        gmt_modified_user AS gmtModifiedUser
        FROM
        m_examination_question where is_active = 1 and id in
        <foreach collection="questionIds" item="questionId" index="index" open="(" separator="," close=")">
            #{questionId}
        </foreach>
        ORDER BY view_order desc,RAND()
        <if test="questionCount != null">
            limit #{questionCount}
        </if>
    </select>


    <select id="getQuestionSumCount" resultType="java.lang.Integer">
        SELECT
            count(1)
        FROM
            m_examination_question where is_active = 1
    </select>
</mapper>