<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.examcenter.mapper.exam.MediaAndAttachedMapper">
  <insert id="insert" parameterType="com.get.examcenter.entity.ExamMediaAndAttached">
    insert into s_media_and_attached (id, fk_file_guid, fk_table_name, 
      fk_table_id, type_key, index_key, 
      link, remark, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkFileGuid,jdbcType=VARCHAR}, #{fkTableName,jdbcType=VARCHAR}, 
      #{fkTableId,jdbcType=BIGINT}, #{typeKey,jdbcType=VARCHAR}, #{indexKey,jdbcType=INTEGER}, 
      #{link,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.examcenter.entity.ExamMediaAndAttached">
    insert into s_media_and_attached
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkFileGuid != null">
        fk_file_guid,
      </if>
      <if test="fkTableName != null">
        fk_table_name,
      </if>
      <if test="fkTableId != null">
        fk_table_id,
      </if>
      <if test="typeKey != null">
        type_key,
      </if>
      <if test="indexKey != null">
        index_key,
      </if>
      <if test="link != null">
        link,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkFileGuid != null">
        #{fkFileGuid,jdbcType=VARCHAR},
      </if>
      <if test="fkTableName != null">
        #{fkTableName,jdbcType=VARCHAR},
      </if>
      <if test="fkTableId != null">
        #{fkTableId,jdbcType=BIGINT},
      </if>
      <if test="typeKey != null">
        #{typeKey,jdbcType=VARCHAR},
      </if>
      <if test="indexKey != null">
        #{indexKey,jdbcType=INTEGER},
      </if>
      <if test="link != null">
        #{link,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.examcenter.entity.ExamMediaAndAttached">
    update s_media_and_attached
    <set>
      <if test="fkFileGuid != null">
        fk_file_guid = #{fkFileGuid,jdbcType=VARCHAR},
      </if>
      <if test="fkTableName != null">
        fk_table_name = #{fkTableName,jdbcType=VARCHAR},
      </if>
      <if test="fkTableId != null">
        fk_table_id = #{fkTableId,jdbcType=BIGINT},
      </if>
      <if test="typeKey != null">
        type_key = #{typeKey,jdbcType=VARCHAR},
      </if>
      <if test="indexKey != null">
        index_key = #{indexKey,jdbcType=INTEGER},
      </if>
      <if test="link != null">
        link = #{link,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.examcenter.entity.ExamMediaAndAttached">
    update s_media_and_attached
    set fk_file_guid = #{fkFileGuid,jdbcType=VARCHAR},
      fk_table_name = #{fkTableName,jdbcType=VARCHAR},
      fk_table_id = #{fkTableId,jdbcType=BIGINT},
      type_key = #{typeKey,jdbcType=VARCHAR},
      index_key = #{indexKey,jdbcType=INTEGER},
      link = #{link,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getNextIndexKey" resultType="java.lang.Integer">
    select max(index_key)+1 from s_media_and_attached where fk_table_id=#{tableId} and fk_table_name=#{tableName}
  </select>
</mapper>