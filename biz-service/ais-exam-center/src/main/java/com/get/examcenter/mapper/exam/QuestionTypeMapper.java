package com.get.examcenter.mapper.exam;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.examcenter.entity.QuestionType;

@DS("examdb")
public interface QuestionTypeMapper extends BaseMapper<QuestionType> {

    int insertSelective(QuestionType record);

    /**
     * @ Description :找存在数据
     * @ Param [id]
     * @ return java.lang.Boolean
     * @ author LEO
     */
    Boolean isExistQuestion(Long id);

    /**
     * @Description: 获取最大排序值
     * @Author: Jerry
     * @Date:14:14 2021/9/3
     */
    Integer getMaxViewOrder();
}