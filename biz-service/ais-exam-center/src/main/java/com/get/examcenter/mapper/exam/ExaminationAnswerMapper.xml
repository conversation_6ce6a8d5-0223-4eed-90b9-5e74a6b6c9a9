<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.examcenter.mapper.exam.ExaminationAnswerMapper">

  <insert id="insertSelective" keyProperty="id" useGeneratedKeys="true" parameterType="com.get.examcenter.entity.ExaminationAnswer">
    insert into m_examination_answer
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkExaminationQuestionId != null">
        fk_examination_question_id,
      </if>
      <if test="answer != null">
        answer,
      </if>
      <if test="isRightAnswer != null">
        is_right_answer,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="score != null">
        score,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkExaminationQuestionId != null">
        #{fkExaminationQuestionId,jdbcType=BIGINT},
      </if>
      <if test="answer != null">
        #{answer,jdbcType=VARCHAR},
      </if>
      <if test="isRightAnswer != null">
        #{isRightAnswer,jdbcType=BIT},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="score != null">
        #{score,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

</mapper>