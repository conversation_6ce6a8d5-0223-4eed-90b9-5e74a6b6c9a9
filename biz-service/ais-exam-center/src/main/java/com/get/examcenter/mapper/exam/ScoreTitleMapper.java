package com.get.examcenter.mapper.exam;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.examcenter.entity.ScoreTitle;

@DS("examdb")
public interface ScoreTitleMapper extends BaseMapper<ScoreTitle> {
    int insert(ScoreTitle record);

    int insertSelective(ScoreTitle record);

    int updateByPrimaryKeySelective(ScoreTitle record);

    int updateByPrimaryKey(ScoreTitle record);

    /**
     * @Description: 获取最大排序值
     * @Author: Jerry
     * @Date:18:13 2021/8/27
     */
    Integer getMaxViewOrder();
}