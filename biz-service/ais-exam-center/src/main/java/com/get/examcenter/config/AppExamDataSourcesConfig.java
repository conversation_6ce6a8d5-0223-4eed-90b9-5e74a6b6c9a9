//package com.get.examcenter.config;
//
//import com.alibaba.druid.pool.DruidDataSource;
//import org.apache.ibatis.session.SqlSessionFactory;
//import org.mybatis.spring.SqlSessionFactoryBean;
//import org.mybatis.spring.SqlSessionTemplate;
//import org.mybatis.spring.annotation.MapperScan;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
//import org.springframework.jdbc.datasource.DataSourceTransactionManager;
//
//import javax.sql.DataSource;
//
///**
// * 次数据源配置
// */
//@Configuration
//@MapperScan(basePackages = "com.get.examcenter.dao.appexam", sqlSessionTemplateRef = "appExamSqlSessionTemplate")
//public class AppExamDataSourcesConfig {
//    @Bean(name = "appExamDataSource")
//    @ConfigurationProperties(prefix = "appexam.datasource")
//    public DataSource testDataSource() {
//        return new DruidDataSource();
//    }
//
//    @Bean(name = "appExamSqlSessionFactory")
//    public SqlSessionFactory testSqlSessionFactory(@Qualifier("appExamDataSource") DataSource dataSource) throws Exception {
//        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
//        bean.setDataSource(dataSource);
//        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/AppExam/*.xml"));
//        //mybatis 数据库字段与实体类属性驼峰映射配置
//        bean.getObject().getConfiguration().setMapUnderscoreToCamelCase(true);
//        return bean.getObject();
//    }
//
//    @Bean(name = "statisticsTransactionManager")
//    public DataSourceTransactionManager testTransactionManager(@Qualifier("appExamDataSource") DataSource dataSource) {
//        return new DataSourceTransactionManager(dataSource);
//    }
//
//    @Bean(name = "appExamSqlSessionTemplate")
//    public SqlSessionTemplate testSqlSessionTemplate(@Qualifier("appExamSqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
//        return new SqlSessionTemplate(sqlSessionFactory);
//    }
//}
