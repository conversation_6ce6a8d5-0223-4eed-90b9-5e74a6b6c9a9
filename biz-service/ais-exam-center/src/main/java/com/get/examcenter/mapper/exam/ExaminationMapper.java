package com.get.examcenter.mapper.exam;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.examcenter.entity.Examination;

@DS("examdb")
public interface ExaminationMapper extends BaseMapper<Examination> {
    int insert(Examination record);

    int insertSelective(Examination record);

    int updateByPrimaryKeySelective(Examination record);

    int updateByPrimaryKey(Examination record);
}