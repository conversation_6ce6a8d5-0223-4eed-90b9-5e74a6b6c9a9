<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.MStudentOfferMapper">

    <resultMap id="BaseResultMap" type="com.get.partnercenter.entity.MStudentOfferEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkStudentId" column="fk_student_id" jdbcType="BIGINT"/>
            <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
            <result property="fkStaffId" column="fk_staff_id" jdbcType="BIGINT"/>
            <result property="fkAreaCountryId" column="fk_area_country_id" jdbcType="BIGINT"/>
            <result property="fkCancelOfferReasonId" column="fk_cancel_offer_reason_id" jdbcType="BIGINT"/>
            <result property="num" column="num" jdbcType="VARCHAR"/>
            <result property="agentContactEmail" column="agent_contact_email" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="fkStaffIdWorkflow" column="fk_staff_id_workflow" jdbcType="BIGINT"/>
            <result property="statusWorkflow" column="status_workflow" jdbcType="INTEGER"/>
            <result property="idGea" column="id_gea" jdbcType="VARCHAR"/>
            <result property="idIae" column="id_iae" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>
