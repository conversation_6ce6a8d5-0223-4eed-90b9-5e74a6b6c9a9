package com.get.partnercenter.controller;

import com.alibaba.nacos.common.utils.StringUtils;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.partnercenter.dto.*;
import com.get.partnercenter.service.MailLogService;
import com.get.partnercenter.service.PartnerUserService;
import com.get.partnercenter.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * 小程序用户管理
 */
@Slf4j
@Api(tags = "小程序用户管理")
@RestController
@RequestMapping("/partnerUser")
public class PartnerUserController {

    @Autowired
    private PartnerUserService partnerUserService;

    @ApiOperation(value = "大区列表")
    @GetMapping("/getRegionList")
    public ResponseBo<List<ReginVo>> getRegionList() {
        return new ResponseBo<>(partnerUserService.getRegionList());
    }

    @ApiOperation(value = "代理账号列表")
    @PostMapping("/agentAccountPage")
    public ResponseBo<AgentAccountVo> agentAccountPage(@RequestBody SearchBean<AgentPageDto> page) {
        List<AgentAccountVo> list = partnerUserService.agentAccountPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "邮件发送日志列表")
    @PostMapping("/mailLogPage")
    public ResponseBo<MailLogVo> mailLogPage(@RequestBody SearchBean<MailLogDto> page) {
        List<MailLogVo> list = partnerUserService.mailLogPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "邮件发送日志详情")
    @GetMapping("/mailLogDetail/{id}")
    public ResponseBo<MailLogVo> mailLogDetail(@PathVariable("id") Long id) {
        return new ResponseBo<>(partnerUserService.getMailLogById(id));
    }

    @ApiOperation(value = "代理下的子账号列表")
    @PostMapping("/partnerUserPage")
    public ResponseBo<PartnerUserVo> partnerUserPage(@RequestBody SearchBean<IdDto> page) {
        List<PartnerUserVo> list = partnerUserService.partnerUserPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "启用/停用代理")
    @PostMapping("/saveAgentStatus")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "status", value = "状态1启用0禁用", required = true),
            @ApiImplicitParam(name = "id", value = "代理账号的代理id-agentId", required = true),
    })
    public ResponseBo<String> saveAgentStatus(@RequestBody @Valid StatusDto statusDto) {
        partnerUserService.saveAgentStatus(statusDto);
        return new ResponseBo<>("保存成功");
    }

    @ApiOperation(value = "代理子账号启用禁用")
    @PostMapping("/saveAccountStatus")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "status", value = "状态1启用0禁用", required = true),
            @ApiImplicitParam(name = "id", value = "代理下的子账号列表的伙伴用户id-partnerUserId", required = true),
    })
    public ResponseBo<String> saveAccountStatus(@RequestBody @Valid StatusDto statusDto) {
        partnerUserService.saveAccountStatus(statusDto);
        return new ResponseBo<>("保存成功");
    }

    @ApiOperation(value = "代理子账号重置密码")
    @PostMapping("/resetPassword")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", value = "代理下的子账号列表的伙伴用户id-partnerUserId", required = true),
    })
    public ResponseBo<String> resetPassword(@RequestBody @Valid IdDto idDto) {
        String result = partnerUserService.resetPassword(idDto);
        if (StringUtils.isNotBlank(result)) {
            return ResponseBo.error(ErrorCodeEnum.INVALID_PARAM.getCode(), result);
        }
        return new ResponseBo<>("保存成功");
    }

    @ApiOperation(value = "代理联系人列表-新建邮件页面")
    @GetMapping("/getAgentContactList")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "agentId", value = "代理id", required = true),
    })
    public ResponseBo<List<AgentContactVo>> getAgentContactList(Long agentId) {
        return new ResponseBo<>(partnerUserService.getAgentContactList(agentId));
    }

    @ApiOperation(value = "代理列表-新建邮件页面")
    @PostMapping("/getBdPage")
    public ResponseBo<BdVo> getBdList(@RequestBody SearchBean<BdPageDto> page) {
        List<BdVo> list = partnerUserService.getBdPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "代理列表-邮件发送列表筛选")
    @GetMapping("/getAgentList")
    public ResponseBo<List<BdVo>> getAgentList() {
        return new ResponseBo<>(partnerUserService.getAgentList());
    }

    @ApiOperation(value = "发送邀请邮件")
    @PostMapping("/sendInviteMail")
    public ResponseBo<String> sendInviteMail(@RequestBody @Valid SendEmailDto sendEmailDto) {
        Boolean result = partnerUserService.sendMail(sendEmailDto);
        if (!result) {
            return ResponseBo.error(ErrorCodeEnum.VERIFY_FAILED.getCode(), "邮件发送失败");
        }
        return new ResponseBo<>("成功");
    }

    @ApiOperation(value = "发件人列表-邮件发送记录列表")
    @GetMapping("/getSendUserList")
    public ResponseBo<List<BdVo>> getSendUserList() {
        return new ResponseBo<>(partnerUserService.getStaffList());
    }

//    @ApiOperation(value = "测试新增用户")
//    @PostMapping("/registerPartnerUser")
//    public ResponseBo<List<RegisterPartnerUserVo>> registerPartnerUser(@RequestBody List<RegisterPartnerUserDto> list) {
//        return new ResponseBo<>(partnerUserService.registerPartnerUser(list));
//    }

}
