package com.get.partnercenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.partnercenter.entity.UAgentCommissionTypeEntity;
import com.get.partnercenter.service.UAgentCommissionTypeService;
import com.get.partnercenter.mapper.UAgentCommissionTypeMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【u_agent_commission_type】的数据库操作Service实现
* @createDate 2025-03-24 16:53:09
*/
@Service
public class UAgentCommissionTypeServiceImpl extends ServiceImpl<UAgentCommissionTypeMapper, UAgentCommissionTypeEntity>
    implements UAgentCommissionTypeService{
    @Autowired
    private UAgentCommissionTypeMapper uAgentCommissionTypeMapper;


    @Override
    public List<UAgentCommissionTypeEntity> searchList() {
        UserInfo user = GetAuthInfo.getUser();
        Long fkCompanyId=user.getFkCompanyId();
        List<UAgentCommissionTypeEntity> resultlist=uAgentCommissionTypeMapper.selectList(new LambdaQueryWrapper<UAgentCommissionTypeEntity>()
                .eq(UAgentCommissionTypeEntity::getFkCompanyId,fkCompanyId));

        return resultlist;
    }
}




