package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.partnercenter.dto.AgentPageDto;
import com.get.partnercenter.entity.MPartnerUserEntity;
import com.get.partnercenter.vo.AgentAccountVo;
import com.get.partnercenter.vo.PartnerUserSimpleVo;
import com.get.partnercenter.vo.PartnerUserVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【m_partner_user】的数据库操作Mapper
 * @createDate 2024-12-20 14:13:45
 * @Entity com.partner.entity.MPartnerUser
 */
@Mapper
@DS("partnerdb")
public interface MPartnerUserMapper extends BaseMapper<MPartnerUserEntity> {

    List<AgentAccountVo> selectAgentAccount(IPage<AgentAccountVo> page, @Param("param") AgentPageDto param, @Param("staffFollowerIds") List<Long> staffFollowerIds);

    void batchUpdateStatus(@Param("ids") List<Long> ids, @Param("status") Integer status, String loginId);

    List<Long> selectPartnerUserCountryIds(@Param("partnerUserId") Long partnerUserId);

    List<PartnerUserVo> selectPartnerUser(IPage<PartnerUserVo> page, @Param("agentId") Long agentId);

    /**
     * 根据ids查询id和name
     * @param ids
     * @return
     */
    List<PartnerUserSimpleVo> selectIdAndNameByIds(@Param("list") List<Long> ids);

    /**
     * 根据partnerUserId查询
     * @param partnerUserId
     * @return
     */
    MPartnerUserEntity selectByPartnerUserId(@Param("partnerUserId") Long partnerUserId);
}




