package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.partnercenter.dto.MAppStudentCheckSerachDto;
import com.get.partnercenter.entity.MAppStudentCheckEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.partnercenter.vo.MAppStudentCheckSearchVo;
import com.get.partnercenter.vo.MAppStudentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_app_student_check】的数据库操作Mapper
* @createDate 2025-03-31 14:59:55
* @Entity com.get.partnercenter.entity.MAppStudentCheck
*/
@Mapper
@DS("saledb")
public interface MAppStudentCheckMapper extends BaseMapper<MAppStudentCheckEntity> {
    List<MAppStudentCheckSearchVo> searchPage(IPage<MAppStudentCheckSearchVo> page, @Param("query") MAppStudentCheckSerachDto params);

    MAppStudentVo searchDetail(Long id);
}




