package com.get.partnercenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.partnercenter.dto.CommissionConfirmStudentsDto;
import com.get.partnercenter.dto.CommissionParamsDto;
import com.get.partnercenter.entity.MStudentOfferItemAgentConfirmEntity;
import com.get.partnercenter.vo.CommissionAgentInfoVo;
import com.get.partnercenter.vo.CommissionConfirmStudentsVo;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_student_offer_item_agent_confirm】的数据库操作Service
* @createDate 2025-02-20 09:52:38
*/

public interface MStudentOfferItemAgentConfirmService extends IService<MStudentOfferItemAgentConfirmEntity> {
    /**
     * 代理佣金分页
     * @param params
     * @param page
     * @return
     */
    List<CommissionAgentInfoVo> searchAgentPage(CommissionParamsDto params, Page page);

    List<CommissionConfirmStudentsVo> searchOfferInfo(CommissionConfirmStudentsDto params);



}
