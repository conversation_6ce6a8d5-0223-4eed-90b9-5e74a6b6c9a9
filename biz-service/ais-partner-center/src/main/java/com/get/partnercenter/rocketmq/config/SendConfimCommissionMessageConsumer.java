package com.get.partnercenter.rocketmq.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.partnercenter.dto.SendEmailInfoDto;
import com.get.partnercenter.entity.MailLogEntity;
import com.get.partnercenter.entity.MailTemplateEntity;
import com.get.partnercenter.mapper.MailLogMapper;
import com.get.partnercenter.mapper.MailTemplateMapper;
import com.get.partnercenter.mapper.SaleCenterMapper;
import com.get.partnercenter.mqmessage.MQCommissionMessage;
import com.get.partnercenter.service.MailLogService;
import com.get.partnercenter.util.MyDateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@Slf4j
@RocketMQMessageListener(
        topic = "send_confim_commission_message_topic", // topic
        consumerGroup = "send_confim_commission_message_consumer", // 消费组
        maxReconsumeTimes = 3, //最大重试次数
        consumeMode = ConsumeMode.CONCURRENTLY // 并发消费-可选顺序消费
)
@Component
public class SendConfimCommissionMessageConsumer implements RocketMQListener<MQCommissionMessage> {

    @Resource
    private MailLogService mailLogService;
    @Resource
    private MailTemplateMapper mailTemplateMapper;

    @Resource
    private SaleCenterMapper saleCenterMapper;

    @Resource
    private MailLogMapper mailLogMapper;

    @Override
    public void onMessage(MQCommissionMessage message) {
        log.info("收到高佣提醒消息，MQCommissionMessage={}", message);
        MailLogEntity logEntitytmp=MailLogEntity.builder()
                .optType(3)
                .toUser(message.getName())
                .toEmail(message.getEmail())
                .fkAgentId(message.getAgentId())
                .sendDate(new Date())
                .build();
        logEntitytmp.setGmtCreate(new Date());
        logEntitytmp.setGmtCreateUser("MQSendMial");

        try {
            //业务逻辑
            MailTemplateEntity template = mailTemplateMapper.selectOne(new LambdaQueryWrapper<MailTemplateEntity>()
                    .eq(MailTemplateEntity::getTypeKey, "CONFIM_COMMISSION"));
            if (Objects.isNull(template)) {
                log.error("高佣提醒异常，邮件模板不存在={}", "CONFIM_COMMISSION");
                return;
            }
            if (Objects.isNull(message.getAgentId())) {
                log.error("高佣提醒异常，代理信息不存在={}", message.getName());
                return;
            }


            //查询代理邮箱、名称、邮件密码

            SendEmailInfoDto agentEmailInfo = saleCenterMapper.selectEmailInfoByAgentId(message.getAgentId());
            if (Objects.isNull(agentEmailInfo) ||
                    Objects.isNull(agentEmailInfo.getFromEmail()) || Objects.isNull(agentEmailInfo.getEmailPassword())
                    ) {
                log.error("高佣提醒异常，代理BD邮件不存在={} {} ", message.getName(),message.getAgentId());
                return;
            }

            logEntitytmp.setFromUser(agentEmailInfo.getFromUser());
            logEntitytmp.setFromEmail(agentEmailInfo.getFromEmail());
            logEntitytmp.setSubject(template.getTitle());

            // 替换账号和密码
            Map replacements = new HashMap();
            replacements.put("account", message.getName());
            replacements.put("year", String.valueOf(MyDateUtils.getYear(new Date())) );
            replacements.put("month", String.valueOf(MyDateUtils.getMonth(new Date())) );
            String content = replaceTemplate(template.getEmailTemplate(), replacements);
            //发邮件
            SendEmailInfoDto sendEmailInfo = new SendEmailInfoDto();
            sendEmailInfo.setToUser(message.getName());
            sendEmailInfo.setToEmail(message.getEmail());
            sendEmailInfo.setAgentId(message.getAgentId());
            sendEmailInfo.setCompanyId(message.getFkCompanyId());

            sendEmailInfo.setFromUser(agentEmailInfo.getFromUser());
            sendEmailInfo.setFromEmail(agentEmailInfo.getFromEmail());
            sendEmailInfo.setEmailPassword(agentEmailInfo.getEmailPassword());
            logEntitytmp.setBody(content);
            logEntitytmp.setSendMessage(JSON.toJSONString(sendEmailInfo));
            String result = mailLogService.sendEmail(sendEmailInfo, template.getTitle(), content);

            //邮件记录
            logEntitytmp.setSendStatus(StringUtils.isNotBlank(result) ? 0 : 1);
            mailLogMapper.insert(logEntitytmp);

        } catch (Exception e) {
            log.error("高佣提醒异常，用户信息={}，错误={}", message, e.getMessage());
            //邮件记录
            logEntitytmp.setSendStatus(0);
            mailLogMapper.insert(logEntitytmp);
            /*throw new RuntimeException("高佣提醒消费失败，触发重试机制", e);*/
        }
    }

    private String replaceTemplate(String template, Map<String, String> replacements) {
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            template = template.replace("#{" + entry.getKey() + "}", entry.getValue());
        }
        return template;
    }
}
