package com.get.partnercenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.partnercenter.entity.MFilePartnerEntity;
import com.get.partnercenter.service.AppFileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

@Api(tags = "后台管理-文件上传下载")
@RestController
@RequestMapping("partner/appFileManager")
public class AppFileController {

    @Resource
    private AppFileService appFileService;


    @ApiOperation(value = "上传附件-公开桶")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/高佣维护/列表查询")
    @PostMapping("uploadHtiPublicFile")
    public ResponseBo<List<MFilePartnerEntity>> uploadHtiPublicFile(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files){
        List<MFilePartnerEntity> result= appFileService.uploadAppendix(files,true);
        return new ResponseBo<>(result);

    }
}
