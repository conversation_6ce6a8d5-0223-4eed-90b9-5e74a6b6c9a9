package com.get.partnercenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.partnercenter.dto.CommissionConfirmStudentsDto;
import com.get.partnercenter.dto.CommissionParamsDto;
import com.get.partnercenter.service.MStudentOfferItemAgentConfirmService;
import com.get.partnercenter.vo.CommissionAgentInfoVo;
import com.get.partnercenter.vo.CommissionConfirmStudentsVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "后台管理-佣金确认查询页面")
@RestController
@RequestMapping("partner/commissionAffirm")
public class MStudentOfferItemAgentConfirmController {
    @Autowired
    private  MStudentOfferItemAgentConfirmService mStudentOfferItemAgentConfirmService;


    @ApiOperation(value = "佣金确认查询", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/佣金/佣金确认查询")
    @PostMapping("searchAgentPage")
    public ResponseBo searchAgentPage(@RequestBody  @Valid SearchBean<CommissionParamsDto> page){
        List<CommissionAgentInfoVo> datas =mStudentOfferItemAgentConfirmService.searchAgentPage(page.getData(),page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    @ApiOperation(value = "申请信息查询", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/佣金确认查询/申请信息查询")
    @PostMapping("searchOfferInfo")
    public ResponseBo searchOfferInfo(@RequestBody  @Valid CommissionConfirmStudentsDto params){
        List<CommissionConfirmStudentsVo> datas =mStudentOfferItemAgentConfirmService.searchOfferInfo(params);

        return new ListResponseBo<>(datas);
    }


}
