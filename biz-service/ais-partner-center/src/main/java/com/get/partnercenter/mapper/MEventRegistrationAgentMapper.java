package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.partnercenter.entity.MEventRegistrationAgentEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【m_event_registration_agent】的数据库操作Mapper
* @createDate 2025-04-29 18:48:18
* @Entity com.get.partnercenter.entity.MEventRegistrationAgent
*/
@Mapper
@DS("saledb")
public interface MEventRegistrationAgentMapper extends BaseMapper<MEventRegistrationAgentEntity> {

}




