package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.aisplatformcenterap.entity.MFeedbackOrderEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【m_feedback_order】的数据库操作Mapper
* @createDate 2025-05-26 16:46:43
* @Entity com.get.aisplatformcenter.entity.MFeedbackOrder
*/
@Mapper
@DS("platformdb")
public interface MFeedbackOrderMapper extends BaseMapper<MFeedbackOrderEntity> {


}




