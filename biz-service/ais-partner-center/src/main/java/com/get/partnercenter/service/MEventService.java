package com.get.partnercenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.partnercenter.dto.MEventParamsDto;
import com.get.partnercenter.dto.PutAwayParamsDto;
import com.get.partnercenter.entity.MEventEntity;
import com.get.partnercenter.entity.MEventRegistrationAgentEntity;
import com.get.partnercenter.mapper.MEventRegistrationAgentMapper;
import com.get.partnercenter.vo.MEventRegistrationAgentVo;
import com.get.partnercenter.vo.MEventVo;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_event】的数据库操作Service
* @createDate 2025-04-22 09:57:14
*/
public interface MEventService extends IService<MEventEntity> {
    /**
     * 活动列表查询
     * @param params
     * @param page
     * @return
     */
    List<MEventVo> searchPage(MEventParamsDto params, Page page);

    /**
     * 活动 新增或变更
     * @param dto
     * @return
     */
    Long saveOrUpdateMEvent(MEventParamsDto dto);

    boolean removeByIdInfo(Long id);

    /**
     * 活动详情
     * @param id
     * @return
     */
    MEventVo  selectByDetail(Long id);

    /**
     * 活动 上架 下架
     * @param vo
     * @return
     */
    Long putAway(PutAwayParamsDto vo);

    /**
     *
     * @param params
     * @param page
     * @return
     */
    List<MEventRegistrationAgentVo> searchRegistration(MEventParamsDto params, Page page);

    void  exportRegistration(HttpServletResponse response, MEventParamsDto paramsDto);

    Long modifyRegistration(MEventRegistrationAgentEntity params);

    Long quoteMevent(MEventParamsDto meventdto);
}
