package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.partnercenter.entity.SCommentEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【s_comment】的数据库操作Mapper
* @createDate 2025-04-23 15:08:07
* @Entity com.get.partnercenter.entity.SComment
*/
@Mapper
@DS("saledb")
public interface SCommentMapper extends BaseMapper<SCommentEntity> {

}




