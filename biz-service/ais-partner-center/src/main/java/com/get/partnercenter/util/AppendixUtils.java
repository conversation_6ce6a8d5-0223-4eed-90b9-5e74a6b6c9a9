package com.get.partnercenter.util;

import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

public class AppendixUtils {
    private static String FILE_ROOT_PATH = "/appendix";

    private static String FILE_HTI_ROOT_PATH = "/htipubfiles";

    public static String getFilePath(MultipartFile file) {
        String fileFileName = file.getOriginalFilename();
        String dateString = getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        String fileurl = FILE_ROOT_PATH + dateString + fileName;
        return fileurl;
    }

    public static String getFilePath(File file) {
        String fileFileName = file.getName();
        String dateString = getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        String fileurl = FILE_ROOT_PATH + dateString + fileName;
        return fileurl;
    }


    public static String getFileHtiPath(MultipartFile file) {
        String fileFileName = file.getOriginalFilename();
        String dateString = getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        String fileurl = FILE_HTI_ROOT_PATH + dateString + fileName;
        return fileurl;
    }
    public static String getFileHtiPath(File file) {
        String fileFileName = file.getName();
        String dateString = getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        String fileurl = FILE_HTI_ROOT_PATH + dateString + fileName;
        return fileurl;
    }


    private static String getDateString() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("/yyyy/MM/dd/");
        Date date = new Date();
        String dateString = simpleDateFormat.format(date);
        return dateString;
    }
}
