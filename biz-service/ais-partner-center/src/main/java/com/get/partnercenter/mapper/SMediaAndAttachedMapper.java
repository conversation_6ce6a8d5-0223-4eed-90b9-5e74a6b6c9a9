package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.partnercenter.dto.MEventParamsDto;
import com.get.partnercenter.dto.SMediaAndAttachedPublicDto;
import com.get.partnercenter.entity.SMediaAndAttachedEntity;
import com.get.partnercenter.vo.FileArray;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【s_media_and_attached】的数据库操作Mapper
* @createDate 2025-01-08 17:33:34
* @Entity com.partner.entity.SMediaAndAttached
*/
@Mapper
@DS(("partnerdb"))
public interface SMediaAndAttachedMapper extends BaseMapper<SMediaAndAttachedEntity> {

    List<FileArray> selectPublicFileArrays(SMediaAndAttachedPublicDto params);

    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);

    /**
     * 获取销售中心文件列表
     * @param tableName
     * @return
     */
    List<FileArray> selectSaleCenterFileArrays(@Param("tableName") String tableName,
                                             @Param("typeKey") String typeKey,
                                             @Param("tableId") Long tableId);


}




