<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.AppSystemCenterMapper">
    <update id="batchUpdateUserStatus">
        UPDATE system_user SET is_lock_flag = #{status},gmt_modified = now(),gmt_modified_user = #{loginId}
        WHERE id
        IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateUserPassword">
        UPDATE system_user_platform_login
        SET login_ps          = #{password},
            gmt_modified      = now(),
            gmt_modified_user = #{loginId}
        WHERE fk_user_id = #{userId}
    </update>

    <update id="updateUserNum">
        UPDATE system_user
        SET num = #{num}
        WHERE id = #{userId}
    </update>


    <select id="getUserEmails" resultType="java.lang.String">
        SELECT GROUP_CONCAT(u.email) AS email
        FROM system_user u
        WHERE id IN (SELECT fk_user_id FROM app_partner_center.m_partner_user WHERE fk_agent_id = #{agentId})
          AND EXISTS (SELECT 1
                      FROM system_user_role ur
                      WHERE ur.fk_user_id = u.id
                        AND EXISTS (SELECT 1
                                    FROM system_role r
                                    WHERE r.fk_platform_id = 2
                                      AND r.fk_platform_code = 'PARTNER'
                                      AND r.role_code = 'ADMIN'
                                      AND ur.fk_role_id = r.id))
    </select>

    <select id="getAdminRoleId" resultType="java.lang.Long">
        SELECT id
        FROM system_role
        WHERE fk_platform_code = 'PARTNER'
          AND role_code = 'ADMIN'
        limit 1
    </select>

    <select id="checkSaveEmail" resultType="java.lang.String">
        select login_id
        from system_user_platform_login
        where fk_platform_code = 'PARTNER'
        and fk_tenant_id = 2
        and login_id in
        <foreach collection="emails" item="email" open="(" separator="," close=")">
            #{email}
        </foreach>
    </select>

    <select id="getPartnerPlatFormId" resultType="java.lang.Long">
        select id
        from system_platform
        where code = 'PARTNER'
        limit 1
    </select>

    <select id="getPartnerDefaultRole" resultType="java.lang.Long">
        SELECT id
        FROM system_role
        WHERE fk_platform_code = 'PARTNER'
          AND role_code = 'PARTNER_DEFAULT'
        limit 1
    </select>
    <select id="selectSystemUserId" resultType="java.lang.Long">
        select fk_user_id
        from system_user_platform_login
        where login_id = #{loginId}
          and fk_platform_code = 'PARTNER'
        limit 1
    </select>

    <select id="getAdminUserEmails" resultType="java.lang.String">
        SELECT distinct (u.email)
        FROM app_partner_center.m_partner_user u
        WHERE u.fk_agent_id = #{agentId}
          AND EXISTS (SELECT 1
                      FROM app_partner_center.r_partner_user_partner_role pr
                      WHERE pr.fk_partner_user_id = u.id
                        AND EXISTS (SELECT 1
                                    FROM app_partner_center.m_partner_role r
                                    WHERE r.role_code = 'ADMIN'
                                      AND (r.fk_agent_id = 0 or r.fk_agent_id is null)))
    </select>


    <insert id="saveUser" useGeneratedKeys="true" keyProperty="userId">
        INSERT INTO system_user (fk_tenant_id, fk_from_platform_id, fk_from_platform_code, name, email, is_lock_flag,
                                 is_del_flag, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user)
        VALUES (#{param.tenantId}, #{param.platformId}, 'PARTNER', #{param.name}, #{param.email}, 0, 0, now(),
                #{param.createUser}, now(), #{param.createUser})
    </insert>

    <insert id="saveUserRole">
        INSERT INTO system_user_role (fk_tenant_id, fk_user_id, fk_role_id, gmt_create, gmt_create_user, gmt_modified,
                                      gmt_modified_user)
        VALUES (#{param.tenantId}, #{param.userId}, #{param.roleId}, now(), #{param.createUser}, now(),
                #{param.createUser})
    </insert>

    <insert id="saveUserLogin">
        INSERT INTO system_user_platform_login (fk_tenant_id, fk_user_id, fk_platform_id, fk_platform_code, login_id,
                                                login_ps,
                                                gmt_create, gmt_create_user, gmt_modified, gmt_modified_user)
        VALUES (#{param.tenantId}, #{param.userId}, #{param.platformId}, 'PARTNER', #{param.email}, #{param.password},
                now(),
                #{param.createUser}, now(),
                #{param.createUser})
    </insert>

</mapper>
