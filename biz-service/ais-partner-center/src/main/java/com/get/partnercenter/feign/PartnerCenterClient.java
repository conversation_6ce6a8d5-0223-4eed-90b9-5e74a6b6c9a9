package com.get.partnercenter.feign;

import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.partnercenter.dto.MediaAndAttachedDto;
import com.get.partnercenter.dto.RegisterPartnerUserDto;
import com.get.partnercenter.dto.wechat.LinkAndQrCodeVo;
import com.get.partnercenter.dto.wechat.MinProgramQrCodeDto;
import com.get.partnercenter.service.MediaAndAttachedService;
import com.get.partnercenter.service.PartnerUserService;
import com.get.partnercenter.service.WeChatService;
import com.get.partnercenter.vo.MediaAndAttachedVo;
import com.get.partnercenter.vo.RegisterPartnerUserVo;
import lombok.AllArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:
 */
@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class PartnerCenterClient implements IPartnerCenterClient {

    @Autowired
    private PartnerUserService partnerUserService;
    @Resource
    private MediaAndAttachedService mediaAndAttachedService;
    @Autowired
    private WeChatService weChatService;

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<RegisterPartnerUserVo>> registerPartnerUser(List<RegisterPartnerUserDto> list) {
        return Result.data(partnerUserService.registerPartnerUser(list));
    }

    @Override
    public Result<List<MediaAndAttachedVo>> addMediaAndAttachedList(List<MediaAndAttachedDto> mediaAttachedVos) {
        return Result.data(mediaAndAttachedService.addMediaAndAttachedList(mediaAttachedVos));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public Result<Object> getQrCode(MinProgramQrCodeDto minProgramQrCodeDto) {
        return Result.data(weChatService.getQrCode(null,minProgramQrCodeDto, Strings.EMPTY));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public Result<Object> getUrlLink(MinProgramQrCodeDto minProgramQrCodeDto) {
        return Result.data(weChatService.getUrlLink(null,minProgramQrCodeDto,Strings.EMPTY));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    public Result<LinkAndQrCodeVo> getLinkAndQrCode(MinProgramQrCodeDto minProgramQrCodeDto) {
        return Result.data(weChatService.getLinkAndQrCode(null,minProgramQrCodeDto));
    }
}
