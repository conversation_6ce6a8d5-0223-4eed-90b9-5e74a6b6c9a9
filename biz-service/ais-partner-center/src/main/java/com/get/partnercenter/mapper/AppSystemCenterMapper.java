package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.partnercenter.dto.SavePartnerUserDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * ais_institution_center
 */
@Mapper
@DS("systemdb")
public interface AppSystemCenterMapper {

    /**
     * 获取用户邮箱
     *
     * @param agentId
     * @return
     */
    String getUserEmails(@Param("agentId") Long agentId);

    /**
     * 获取管理员邮箱-伙伴角色
     * @param agentId
     * @return
     */
    List<String> getAdminUserEmails(@Param("agentId") Long agentId);

    void batchUpdateUserStatus(@Param("ids") List<Long> ids, @Param("status") Integer status, @Param("loginId") String loginId);

    void updateUserPassword(@Param("userId") Long userId, @Param("password") String password, @Param("loginId") String loginId);

    Long saveUser(@Param("param") SavePartnerUserDto partnerUserDto);

    Long saveUserRole(@Param("param") SavePartnerUserDto partnerUserDto);

    Long saveUserLogin(@Param("param") SavePartnerUserDto partnerUserDto);

    Long getAdminRoleId();

    void updateUserNum(@Param("userId") Long userId, @Param("num") String num);

    List<String> checkSaveEmail(@Param("emails") List<String> emails);

    /**
     * 获取平台id
     * @return
     */
    Long getPartnerPlatFormId();

    /**
     * 获取默认角色id-PARTNER
     * @return
     */
    Long getPartnerDefaultRole();

    /**
     * 获取系统用户id
     * @param loginId
     * @return
     */
    Long selectSystemUserId(@Param("loginId") String loginId);
}
