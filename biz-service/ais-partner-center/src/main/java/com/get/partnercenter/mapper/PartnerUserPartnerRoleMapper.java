package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.partnercenter.entity.PartnerUserPartnerRole;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("partnerdb")
public interface PartnerUserPartnerRoleMapper extends BaseMapper<PartnerUserPartnerRole> {

    /**
     * 获取partner管理员角色Id
     * @return
     */
    Long getPartnerAdminRoleId();
}
