package com.get.partnercenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.partnercenter.dto.CommissionConfirmStudentsDto;
import com.get.partnercenter.dto.CommissionParamsDto;
import com.get.partnercenter.entity.MStudentOfferItemAgentConfirmEntity;
import com.get.partnercenter.vo.CommissionAgentInfoVo;
import com.get.partnercenter.vo.CommissionConfirmStudentsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_student_offer_item_agent_confirm】的数据库操作Mapper
* @createDate 2025-02-20 09:52:38
* @Entity com.get.aispartnercenter.entity.MStudentOfferItemAgentConfirm
*/
@Mapper
public interface MStudentOfferItemAgentConfirmMapper extends BaseMapper<MStudentOfferItemAgentConfirmEntity> {


    List<CommissionAgentInfoVo> searchAgentPage(IPage<CommissionAgentInfoVo> page, @Param("query") CommissionParamsDto params);

    List<CommissionConfirmStudentsVo> searchOfferInfo(CommissionConfirmStudentsDto params);

}




