<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.partnercenter.mapper.PartnerUserPartnerRoleMapper">

    <select id="getPartnerAdminRoleId" resultType="java.lang.Long">
        SELECT id
        FROM app_partner_center.m_partner_role
        WHERE (fk_agent_id = 0 OR fk_agent_id IS NULL)
          AND role_code = 'ADMIN'
        limit 1
    </select>
</mapper>