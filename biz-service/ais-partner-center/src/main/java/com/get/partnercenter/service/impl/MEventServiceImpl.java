package com.get.partnercenter.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.FileUtils;
import com.get.partnercenter.dto.MEventParamsDto;
import com.get.partnercenter.dto.PutAwayParamsDto;
import com.get.partnercenter.entity.MEventEntity;
import com.get.partnercenter.entity.MEventRegistrationAgentEntity;
import com.get.partnercenter.entity.SCommentEntity;
import com.get.partnercenter.entity.SMediaAndAttachedEntity;
import com.get.partnercenter.mapper.MEventRegistrationAgentMapper;
import com.get.partnercenter.mapper.SCommentMapper;
import com.get.partnercenter.mapper.SMediaAndAttachedMapper;
import com.get.partnercenter.mapper.SaleCenterMapper;
import com.get.partnercenter.service.MEventService;
import com.get.partnercenter.mapper.MEventMapper;
import com.get.partnercenter.util.TencentCloudUtils;
import com.get.partnercenter.vo.FileArray;
import com.get.partnercenter.vo.MEventRegistrationAgentExportVo;
import com.get.partnercenter.vo.MEventRegistrationAgentVo;
import com.get.partnercenter.vo.MEventVo;
import com.get.salecenter.dto.MediaAndAttachedDto;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.MediaAndAttachedVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【m_event】的数据库操作Service实现
* @createDate 2025-04-22 09:57:14
*/
@Service
public class MEventServiceImpl extends ServiceImpl<MEventMapper, MEventEntity> implements MEventService{
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Autowired
    private MEventMapper mEventMapper;
    @Autowired
    private UtilService utilService;
    @Autowired
    private TencentCloudUtils tencentCloudUtils;

    @Autowired
    private SMediaAndAttachedMapper sMediaAndAttachedMapper;

    @Autowired
    private SCommentMapper sCommentMapper;
    @Resource
    private SaleCenterMapper saleCenterMapper;

    @Autowired
    private MEventRegistrationAgentMapper mEventRegistrationAgentMapper;


    @Override
    public List<MEventVo> searchPage(MEventParamsDto params, Page page) {
        IPage<MEventVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));

        String urlBase=tencentCloudUtils.getTencentCloudUrl();
        params.setMMageAddress(urlBase);

        List<MEventVo> result=mEventMapper.searchPage(pages,params);

        page.setAll((int) pages.getTotal());
        return result;
    }

    @Override
    @DSTransactional
    public Long saveOrUpdateMEvent(MEventParamsDto paramsdto) {
        if (paramsdto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        MEventEntity meventPo = BeanCopyUtils.objClone(paramsdto, MEventEntity::new);
        SCommentEntity sCommentEntity=new SCommentEntity();

        UserInfo user = GetAuthInfo.getUser();
        meventPo.setFkCompanyId(user.getFkCompanyId());

        try{
            if(meventPo.getDescription()!=null && meventPo.getDescription().length()>0){
                String comment=java.net.URLDecoder.decode(meventPo.getDescription(), "utf-8");

                sCommentEntity.setFkTableName("m_event");
                sCommentEntity.setComment(comment);


                meventPo.setDescription("");

            }
        }catch (Exception e){

        }

        if(meventPo.getId()==null){
            List<MediaAndAttachedDto> mediaAndAttachedDtoList = new ArrayList<>();
            meventPo.setPublicLevel("13");
            meventPo.setIsActive(true);
            utilService.setCreateInfo(meventPo);
            mEventMapper.insert(meventPo);
            meventPo.setNum(getEventNum(meventPo.getId()));
            mEventMapper.updateById(meventPo);
            if(paramsdto.getFileGuid()!=null && !"".equals(paramsdto.getFileGuid())){
                MediaAndAttachedDto sMediaAndAttachedEntity = new MediaAndAttachedDto();
                sMediaAndAttachedEntity.setFkFileGuid(paramsdto.getFileGuid());
                sMediaAndAttachedEntity.setFkTableName("m_event");
                sMediaAndAttachedEntity.setFkTableId(meventPo.getId());
                sMediaAndAttachedEntity.setTypeKey("m_event_partner_logo");
                sMediaAndAttachedEntity.setRemark("活动logo");
                sMediaAndAttachedEntity.setGmtCreateUser(user.getLoginId());
                sMediaAndAttachedEntity.setGmtCreate(new Date());
                mediaAndAttachedDtoList.add(sMediaAndAttachedEntity);
            }
            if(paramsdto.getFileMeventGuid()!=null && paramsdto.getFileMeventGuid().length>0){
                String[] fileArry= paramsdto.getFileMeventGuid();
                int size= fileArry.length;
                for(int i=0; i<size; i++){
                    String fileguid=fileArry[i];
                    MediaAndAttachedDto sMediaAndAttachedEntity=new MediaAndAttachedDto();
                    sMediaAndAttachedEntity.setFkFileGuid(fileguid);
                    sMediaAndAttachedEntity.setFkTableName("m_event");
                    sMediaAndAttachedEntity.setFkTableId(meventPo.getId());
                    sMediaAndAttachedEntity.setTypeKey("m_event_partner_file");
                    sMediaAndAttachedEntity.setRemark("活动文件");
                    sMediaAndAttachedEntity.setGmtCreateUser(user.getLoginId());
                    sMediaAndAttachedEntity.setGmtCreate(new Date());
                    mediaAndAttachedDtoList.add(sMediaAndAttachedEntity);
                }
            }
            if (GeneralTool.isNotEmpty(mediaAndAttachedDtoList)) {
                Result<List<MediaAndAttachedVo>> result = saleCenterClient.addMediaAndAttachedList(mediaAndAttachedDtoList);
                if (!result.isSuccess()) {
                    throw new GetServiceException(result.getMessage());
                }
            }
        }else {
            List<MediaAndAttachedDto> mediaAndAttachedDtoList = new ArrayList<>();
            if(paramsdto.getFileGuid()!=null && !"".equals(paramsdto.getFileGuid())){
                //上线临时修改兼容，后期建议重新写他的代码 前端重新对接
                MediaAndAttachedVo mediaAndAttachedVo =  saleCenterMapper.selectEventMediaAndAttached(meventPo.getId());
//                SMediaAndAttachedEntity attPo=sMediaAndAttachedMapper.selectOne(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
//                        .eq(SMediaAndAttachedEntity::getFkTableName, "m_event")
//                        .eq(SMediaAndAttachedEntity::getFkTableId,meventPo.getId())
//                        .eq(SMediaAndAttachedEntity::getTypeKey, "m_event_partner_logo")
//                );
                if(GeneralTool.isNotEmpty(mediaAndAttachedVo)){
                    saleCenterMapper.deleteMediaAndAttachedById(mediaAndAttachedVo.getId());
                }
                MediaAndAttachedDto sMediaAndAttachedEntity=new MediaAndAttachedDto();
                sMediaAndAttachedEntity.setFkFileGuid(paramsdto.getFileGuid());
                sMediaAndAttachedEntity.setFkTableName("m_event");
                sMediaAndAttachedEntity.setFkTableId(meventPo.getId());
                sMediaAndAttachedEntity.setTypeKey("m_event_partner_logo");
                sMediaAndAttachedEntity.setRemark("活动logo");
                sMediaAndAttachedEntity.setGmtCreateUser(user.getLoginId());
                sMediaAndAttachedEntity.setGmtCreate(new Date());
                mediaAndAttachedDtoList.add(sMediaAndAttachedEntity);
            }
            if(paramsdto.getFileMeventGuid()!=null && paramsdto.getFileMeventGuid().length>0){
                List<MediaAndAttachedVo> listEventFile = saleCenterMapper.selectEventMediaAndAttachedList(meventPo.getId(), "m_event_partner_file");
//                List<SMediaAndAttachedEntity> listEventFile= sMediaAndAttachedMapper.selectList(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
//                        .eq(SMediaAndAttachedEntity::getFkTableName, "m_event")
//                        .eq(SMediaAndAttachedEntity::getFkTableId,meventPo.getId())
//                        .eq(SMediaAndAttachedEntity::getTypeKey, "m_event_partner_file")
//                );
                Map<String,MediaAndAttachedVo> mapMventFile=new HashMap<String,MediaAndAttachedVo>();
                if(ObjectUtils.isNotEmpty(listEventFile)){
                    mapMventFile=listEventFile.stream().collect(Collectors.toMap(o->o.getFkFileGuid(), o->o,(v1, v2)->v1));
                }
                if(paramsdto.getFileMeventGuid()!=null && paramsdto.getFileMeventGuid().length>0){
                    String[] fileArry= paramsdto.getFileMeventGuid();
                    for(int i=0; i<fileArry.length; i++) {
                        String fileguid = fileArry[i];
                        MediaAndAttachedVo attPo = mapMventFile.get(fileguid);
                        if (attPo == null) {
                            MediaAndAttachedDto sMediaAndAttachedEntity = new MediaAndAttachedDto();
                            sMediaAndAttachedEntity.setFkFileGuid(fileguid);
                            sMediaAndAttachedEntity.setFkTableName("m_event");
                            sMediaAndAttachedEntity.setFkTableId(meventPo.getId());
                            sMediaAndAttachedEntity.setTypeKey("m_event_partner_file");
                            sMediaAndAttachedEntity.setRemark("活动文件");
                            sMediaAndAttachedEntity.setGmtCreateUser(user.getLoginId());
                            sMediaAndAttachedEntity.setGmtCreate(new Date());
                            mediaAndAttachedDtoList.add(sMediaAndAttachedEntity);
                        } else {
                            mapMventFile.remove(fileguid);
                        }
                    }
                    if(mapMventFile.size()>0){
                        Set<String> keyFileguidArr=mapMventFile.keySet();
                        for(String fileguid:keyFileguidArr){
                            MediaAndAttachedVo deletefile=mapMventFile.get(fileguid);
                            saleCenterMapper.deleteMediaAndAttachedById(deletefile.getId());
                        }
                    }
                }

            }
            mEventMapper.updateById(meventPo);
            if (GeneralTool.isNotEmpty(mediaAndAttachedDtoList)) {
                Result<List<MediaAndAttachedVo>> result = saleCenterClient.addMediaAndAttachedList(mediaAndAttachedDtoList);
                if (!result.isSuccess()) {
                    throw new GetServiceException(result.getMessage());
                }
            }
        }
        SCommentEntity sCommentDB= sCommentMapper.selectOne(new LambdaQueryWrapper<SCommentEntity>().eq(SCommentEntity::getFkTableName, "m_event")
                .eq(SCommentEntity::getFkTableId, meventPo.getId()).last("limit 1")
        );


        if(ObjectUtils.isNotEmpty(sCommentDB)){
            if(sCommentEntity.getComment()!=null && !sCommentEntity.getComment().equals("")){
                sCommentDB.setComment(sCommentEntity.getComment());
                sCommentMapper.updateById(sCommentDB);
            }else {
                sCommentMapper.deleteById(sCommentDB.getId());
            }

        }else {
            if(sCommentEntity.getComment()!=null && !sCommentEntity.getComment().equals("")){
                sCommentEntity.setFkTableId(meventPo.getId());
                utilService.setCreateInfo(meventPo);
                sCommentMapper.insert(sCommentEntity);
            }

        }



        return meventPo.getId();
    }


    @Override
    public boolean removeByIdInfo(Long id) {
        boolean result = true;
        mEventMapper.deleteById(id);
        List<SMediaAndAttachedEntity> attArrList=sMediaAndAttachedMapper.selectList(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
                .eq(SMediaAndAttachedEntity::getFkTableName, "m_event")
                .eq(SMediaAndAttachedEntity::getFkTableId, id)
        );

        if(!ObjectUtils.isEmpty(attArrList)){
            for(SMediaAndAttachedEntity tmppo:attArrList){
                sMediaAndAttachedMapper.deleteById(tmppo.getId());
            }

        }

        return result;
    }


    @Override
    public MEventVo selectByDetail(Long id) {
        MEventParamsDto params=new MEventParamsDto();
        String urlBase=tencentCloudUtils.getTencentCloudUrl();
        params.setId(id);
        params.setMMageAddress(urlBase);
        MEventVo result=mEventMapper.selectByDetail(params);

        //上线临时修改兼容，后期要优化他的代码
        MediaAndAttachedVo mediaAndAttachedVo =  saleCenterMapper.selectEventMediaAndAttached(id);
        if (GeneralTool.isNotEmpty(mediaAndAttachedVo)) {
            result.setFileGuid(mediaAndAttachedVo.getFkFileGuid());
            result.setFileKey(urlBase + mediaAndAttachedVo.getFileKey());
        }

//        SMediaAndAttachedEntity attPo=sMediaAndAttachedMapper.selectOne(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
//                .eq(SMediaAndAttachedEntity::getFkTableName, "m_event")
//                .eq(SMediaAndAttachedEntity::getTypeKey, "m_event_partner_logo")
//                .eq(SMediaAndAttachedEntity::getFkTableId, id)
//        );
//        if(attPo!=null){
//            result.setFileGuid(attPo.getFkFileGuid());
//        }


        List<FileArray> fileArrays= saleCenterMapper.selectEventFileArrays(params);
        if(ObjectUtils.isNotEmpty(fileArrays)){
            result.setEventFile(fileArrays);
        }

        return result;
    }

    @Override
    public Long putAway(PutAwayParamsDto params) {
        if (params == null) {
            throw new GetServiceException(ResultCode.VERIFY_FAILED,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","数据为空!"));
        }


        if(params.getType()==1) {//上架
            mEventMapper.updateByIds(params.getType(),params.getIds());
        }else if (params.getType()==0) {//下架
            mEventMapper.updateByIds(params.getType(),params.getIds());
        }
        return 0l;
    }

    @Override
    public List<MEventRegistrationAgentVo> searchRegistration(MEventParamsDto params, Page page) {

        IPage<MEventRegistrationAgentVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));

        String urlBase=tencentCloudUtils.getTencentCloudUrl();
        params.setMMageAddress(urlBase);
        List<MEventRegistrationAgentVo> result=mEventMapper.searchRegistration(pages,params);

        page.setAll((int) pages.getTotal());

        return result;
    }

    @Override
    public void exportRegistration(HttpServletResponse response, MEventParamsDto paramsDto) {

        IPage<MEventRegistrationAgentVo> pages = GetCondition.getPage(PageUtil.convertToQuery(1, 10000));
        List<MEventRegistrationAgentVo> result=mEventMapper.searchRegistration(pages,paramsDto);



        List<MEventRegistrationAgentExportVo> resultList= BeanCopyUtils.copyListProperties(result, MEventRegistrationAgentExportVo::new);
        FileUtils.exportExcelNotWrapText(response, resultList,"MEventRegistrationAgentExport",MEventRegistrationAgentExportVo.class);
    }


    @Override
    public Long modifyRegistration(MEventRegistrationAgentEntity params) {
        MEventRegistrationAgentEntity dbpo=mEventRegistrationAgentMapper.selectById(params.getId());
        dbpo.setStatus(params.getStatus());
        mEventRegistrationAgentMapper.updateById(dbpo);
        return 0l;
    }

    @Override
    public Long quoteMevent(MEventParamsDto meventdto) {

        if (meventdto.getId() == null) {
            throw new GetServiceException(ResultCode.VERIFY_FAILED,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","数据为空!"));
        }

        MEventEntity meventDbpo=mEventMapper.selectById(meventdto.getId());
        if(meventDbpo==null){
            throw new GetServiceException(ResultCode.VERIFY_FAILED,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","数据为空!"));
        }

        if(meventDbpo.getPublicLevel()!=null && meventDbpo.getPublicLevel().equals("13")){
            throw new GetServiceException(ResultCode.VERIFY_FAILED,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","数据已引用!"));
        }

        if(meventDbpo.getPublicLevel()!=null){
            meventDbpo.setPublicLevel(meventDbpo.getPublicLevel()+",13");
        }else {
            meventDbpo.setPublicLevel("13");
        }
        mEventMapper.updateById(meventDbpo);

        return 0l;
    }


    public  String getEventNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "EVT" + code;
    }



}




