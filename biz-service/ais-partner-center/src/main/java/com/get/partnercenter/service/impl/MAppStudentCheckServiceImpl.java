package com.get.partnercenter.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetStringUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.partnercenter.dto.*;
import com.get.partnercenter.entity.*;
import com.get.partnercenter.enums.ConfigTypeEnum;
import com.get.partnercenter.mapper.*;
import com.get.partnercenter.service.MAppStudentCheckService;
import com.get.partnercenter.util.NameToPinyin;
import com.get.partnercenter.util.TencentCloudUtils;
import com.get.partnercenter.vo.FileArray;
import com.get.partnercenter.vo.MAppStudentCheckSearchVo;
import com.get.partnercenter.vo.MAppStudentOfferItemVo;
import com.get.partnercenter.vo.MAppStudentVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.entity.Student;
import com.get.salecenter.feign.ISaleCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class MAppStudentCheckServiceImpl
        extends ServiceImpl<MAppStudentCheckMapper, MAppStudentCheckEntity>
        implements MAppStudentCheckService {

    @Resource
    private MAppStudentCheckMapper mAppStudentCheckMapper;
    @Resource
    private ISaleCenterClient salesCenterClient;

    @Resource
    private MAppStudentMapper mAppStudentMapper;

    @Resource
    private MStudentMapper studentMapper;

    @Resource
    private RStudentAgentMapper rStudentAgentMapper;

    @Resource
    private RPartnerUserStudentMapper rPartnerUserStudentMapper;

    @Resource
    private RStudentUuidMapper studentUuidMapper;

    @Resource
    private MAppStudentOfferItemMapper mAppStudentOfferItemMapper;

    @Resource
    private MStudentOfferItemMapper mStudentOfferItemMapper;
    @Resource
    private MStudentOfferMapper mStudentOfferMapper;
    @Resource
    private SStudentProjectRoleStaffMapper sStudentProjectRoleStaffMapper;


    @Resource
    private UtilService utilService;

    @Resource
    private SMediaAndAttachedMapper sMediaAndAttachedMapper;
    @Resource
    private TencentCloudUtils tencentCloudUtils;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public List<MAppStudentCheckSearchVo> searchPage(MAppStudentCheckSerachDto params, Page page) {
        IPage<MAppStudentCheckSearchVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));

        Long staffId = SecureUtil.getStaffId();

        List<Long> staffFollowerIds = new ArrayList<>();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIdsResult = permissionCenterClient.getStaffFollowerIds(staffId).getData();
        if (GeneralTool.isNotEmpty(staffFollowerIdsResult)) {
            staffFollowerIds = staffFollowerIdsResult.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
        staffFollowerIds.add(staffId);

        params.setStaffFollowerIds(staffFollowerIds);

        List<MAppStudentCheckSearchVo> resultArr = mAppStudentCheckMapper.searchPage(pages, params);
        page.setAll((int) pages.getTotal());

        return resultArr;
    }

    @Override
    public MAppStudentVo searchDetail(Long id) {
        MAppStudentVo result = new MAppStudentVo();

        result = mAppStudentMapper.searchDetail(id);

        List<MAppStudentCheckEntity> mAppStudentCheckList = mAppStudentCheckMapper.selectList(
                new LambdaQueryWrapper<MAppStudentCheckEntity>()
                        .eq(MAppStudentCheckEntity::getFkAppStudentId, id));
        result.setMAppStudentCheckList(mAppStudentCheckList);
        //学生审核
        List<MAppStudentOfferItemVo> mAppStudentOfferItemVoList = mAppStudentOfferItemMapper.selectItemList(id, 0);

        result.setMAppStudentOfferItemlist(mAppStudentOfferItemVoList);
        //加申审核
        List<MAppStudentOfferItemVo> mAppAddCheckStudentOfferItemlist = mAppStudentOfferItemMapper.selectItemList(id, 1);

        result.setMAppAddCheckStudentOfferItemlist(mAppAddCheckStudentOfferItemlist);

//        SMediaAndAttachedPublicDto params = new SMediaAndAttachedPublicDto(); //学生添加附件
//        String urlBase = tencentCloudUtils.getTencentCloudUrl();
//        params.setMMageAddress(urlBase);
//        params.setFkTableName("m_app_student");
//        params.setFkTableId(id);
//        params.setTypeKey("m_app_student_file");
//        //申请附件
//        List<FileArray> fileArr = sMediaAndAttachedMapper.selectPublicFileArrays(params);
        //获取销售中心附件-学生草稿附件
        List<FileArray> fileArrays = sMediaAndAttachedMapper.selectSaleCenterFileArrays("m_app_student", "m_app_student_file", id);
        result.setFileArray(fileArrays);


        return result;
    }


    @Override
    public MAppStudentVo getOfferItemDetail(Long id) {
        MAppStudentVo result = new MAppStudentVo();

        result = mAppStudentMapper.searchDetail(id);

        List<MAppStudentCheckEntity> mAppStudentCheckList = mAppStudentCheckMapper.selectList(
                new LambdaQueryWrapper<MAppStudentCheckEntity>()
                        .eq(MAppStudentCheckEntity::getFkAppStudentId, id));
        result.setMAppStudentCheckList(mAppStudentCheckList);
        //学生审核信息
        List<MAppStudentOfferItemVo> mAppStudentOfferItemVoList = mAppStudentOfferItemMapper.selectItemList(id, 0);

        result.setMAppStudentOfferItemlist(mAppStudentOfferItemVoList);
        //加申审核信息
        List<MAppStudentOfferItemVo> mAppAddCheckStudentOfferItemlist = mAppStudentOfferItemMapper.selectItemList(id, 1);

        result.setMAppAddCheckStudentOfferItemlist(mAppAddCheckStudentOfferItemlist);


        return result;
    }

    @DSTransactional
    public long checkUser(MAppStudentCheckDto checkdto) {
        long studentid = 0;

        //判断学生是否待审核
        MAppStudentEntity mAppStudentEntity = mAppStudentMapper.selectOne(new LambdaQueryWrapper<MAppStudentEntity>().
                eq(MAppStudentEntity::getId, checkdto.getFkAppStudentId())
        );
        if (GeneralTool.isNotEmpty(mAppStudentEntity) && mAppStudentEntity.getStatus() == 1) {

        } else {
            throw new GetServiceException(ResultCode.VERIFY_FAILED,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "", "非待审核用户!"));
        }
        if (GeneralTool.isNotEmpty(mAppStudentEntity) && mAppStudentEntity.getFkStudentId() != null) {
            //二次审核
            if (ObjectUtil.isNotEmpty(mAppStudentEntity.getStatus()) && mAppStudentEntity.getStatus().intValue() == 1) {
                List<MStudentOfferEntity> OfferArray = mStudentOfferItemMapper.selectByOfferArr(mAppStudentEntity.getFkStudentId());
                if (ObjectUtil.isNotEmpty(OfferArray)) {
                    List<Long> ids = OfferArray.stream().map(o -> o.getId()).collect(Collectors.toList());
                    //删除空方案
                    mStudentOfferMapper.deleteBatchIds(ids);

                    for (MStudentOfferEntity offerpo : OfferArray) {
                        List<SStudentProjectRoleStaffEntity> listRoleStaff = sStudentProjectRoleStaffMapper.selectList(new LambdaQueryWrapper<SStudentProjectRoleStaffEntity>()
                                .eq(SStudentProjectRoleStaffEntity::getFkTableName, "m_student_offer")
                                .eq(SStudentProjectRoleStaffEntity::getFkTableId, offerpo.getId())
                        );
                        if (ObjectUtil.isNotEmpty(listRoleStaff)) {
                            List<Long> ids_rolestaff = listRoleStaff.stream().map(o -> o.getId()).collect(Collectors.toList());
                            //清空空方案项目成员
                            sStudentProjectRoleStaffMapper.deleteBatchIds(ids_rolestaff);
                        }
                    }
                }
            }
            return mAppStudentEntity.getFkStudentId();
        }


        if (checkdto.getIsPassed()) {

            //判断学生是否存在
            Student studentValid = studentMapper.selectOne(new LambdaQueryWrapper<Student>()
                    .eq(Student::getName, mAppStudentEntity.getName())
                    .eq(Student::getBirthday, mAppStudentEntity.getBirthday()));
            if (GeneralTool.isNotEmpty(studentValid) && checkdto.getIsPassed()) {
                throw new GetServiceException(ResultCode.VERIFY_FAILED,
                        LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "", "学生:" + mAppStudentEntity.getName() + "已存在!"));
            }


            //插入学生到ais_sale_center.m_student
            Student student = BeanCopyUtils.objClone(mAppStudentEntity, Student::new);
            student.setId(null);
            student.setFkCompanyId(mAppStudentEntity.getFkCompanyId());
            utilService.setCreateInfo(student);
            try {
                String[] pinyinNames = NameToPinyin.convertChineseNameToPinyin(student.getName());
                student.setLastName(pinyinNames[0]);
                student.setFirstName(pinyinNames[1]);
            } catch (Exception e) {
                log.error("转换名称拼音失败忽略异常 {}", e.getMessage());
            }

            studentMapper.insert(student);

            student.setNum(GetStringUtils.getStudentNum(student.getId()));
            log.info(student.getId() + "=====getStudentNum=====" + student.getNum());
            studentMapper.updateById(student);

            studentid = student.getId();

            //copy附件
            Result<Boolean> result = salesCenterClient.copyPartnerStudentAttached(studentid, checkdto.getFkAppStudentId());
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            //插入学生代理关系ais_sale_center.r_student_agent
            RStudentAgentEntity rStudentAgentEntity = new RStudentAgentEntity();
            rStudentAgentEntity.setFkStudentId(student.getId());
            rStudentAgentEntity.setFkAgentId(mAppStudentEntity.getFkAgentId());
            rStudentAgentEntity.setIsActive(true);
            rStudentAgentEntity.setActiveDate(LocalDateTime.now());
            utilService.setCreateInfo(rStudentAgentEntity);
            rStudentAgentMapper.insert(rStudentAgentEntity);
            //插入学生r_student_uuid
            RStudentUuidEntity record = new RStudentUuidEntity();
            record.setFkStudentId(student.getId());
            record.setFkStudentUuid(UUID.randomUUID().toString());
            utilService.setCreateInfo(record);
            studentUuidMapper.insert(record);


            //修改ais_sale_center.m_app_student 状态为已生效且把生效学生id放入 草稿表字段fkStudentId
            //mAppStudentEntity.setStatus(2);
            mAppStudentEntity.setFkStudentId(student.getId());
            utilService.setUpdateInfo(mAppStudentEntity);
            mAppStudentMapper.updateById(mAppStudentEntity);
            //保存审核日志
            MAppStudentCheckEntity checkEntity = new MAppStudentCheckEntity();
            checkEntity.setFkAppStudentId(checkdto.getFkAppStudentId());
            checkEntity.setCheckComment(checkdto.getCheckComment());
            checkEntity.setIsPassed(checkdto.getIsPassed());
            utilService.setCreateInfo(checkEntity);
            mAppStudentCheckMapper.insert(checkEntity);


            //插入学生分配  app_partner_center.r_partner_user_student
            RPartnerUserStudentEntity rPartnerUserStudentEntity = new RPartnerUserStudentEntity();
            rPartnerUserStudentEntity.setFkTenantId(ConfigTypeEnum.PARTNER_TENANT_ID.key);
            rPartnerUserStudentEntity.setFkPartnerUserId(mAppStudentEntity.getFkPlatformCreateUserId());
            rPartnerUserStudentEntity.setFkStudentId(student.getId());
            rPartnerUserStudentEntity.setIsActive(true);
            rPartnerUserStudentEntity.setActiveDate(new Date());
            utilService.setCreateInfo(rPartnerUserStudentEntity);
            rPartnerUserStudentMapper.insert(rPartnerUserStudentEntity);


        } else {
            //不通过

            //修改ais_sale_center.m_app_student 状态为不生效且把生效学生id放入 草稿表字段fkStudentId
            mAppStudentEntity.setStatus(-1);
            utilService.setUpdateInfo(mAppStudentEntity);
            mAppStudentMapper.updateById(mAppStudentEntity);


            //保存审核日志
            MAppStudentCheckEntity checkEntity = new MAppStudentCheckEntity();
            checkEntity.setFkAppStudentId(checkdto.getFkAppStudentId());
            checkEntity.setCheckComment(checkdto.getCheckComment());
            checkEntity.setIsPassed(checkdto.getIsPassed());
            utilService.setCreateInfo(checkEntity);
            mAppStudentCheckMapper.insert(checkEntity);

        }
        return studentid;
    }

    @DSTransactional
    public boolean checkItemOffer(MAppStudentOfferItemCheckDto checkdto) {
        boolean flag = true;
        MAppStudentEntity mAppStudentEntity = mAppStudentMapper.selectById(checkdto.getFkAppStudentId());
        if (Objects.isNull(mAppStudentEntity)) {
            throw new GetServiceException(ResultCode.VERIFY_FAILED,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "", "学生草稿不存在!"));

        }

        List<MAppStudentOfferItemCheck> appOfferItem = checkdto.getListSub();
        Boolean flagjiashen = false;
        if (ObjectUtil.isNotEmpty(appOfferItem)) {
            for (MAppStudentOfferItemCheck itemOffer : appOfferItem) {
                MAppStudentOfferItemEntity tmppo = mAppStudentOfferItemMapper.selectById(itemOffer.getFkAppOfferItemId());
                if (tmppo == null) {
                    continue;
                } else if (tmppo.getIsAdditional() != null && tmppo.getIsAdditional()) {

                    flagjiashen = true;
                }
                tmppo.setStatusAdditional(2);//审核通过
                tmppo.setFkStudentOfferItemId(itemOffer.getFkStudentOfferItemId());
                mAppStudentOfferItemMapper.updateById(tmppo);
            }
        }

        if (flagjiashen) {

            //保存审核日志
            MAppStudentCheckEntity checkEntity = new MAppStudentCheckEntity();
            checkEntity.setFkAppStudentId(checkdto.getFkAppStudentId());
            checkEntity.setCheckComment("加申通过");
            checkEntity.setIsPassed(true);
            utilService.setCreateInfo(checkEntity);
            mAppStudentCheckMapper.insert(checkEntity);

        } else {
            mAppStudentEntity.setStatus(2);
        }


        utilService.setUpdateInfo(mAppStudentEntity);
        //审核通过后修改学生状态
        mAppStudentMapper.updateById(mAppStudentEntity);


        return flag;
    }


}
