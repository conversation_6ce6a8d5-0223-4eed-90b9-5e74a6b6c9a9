<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.MAppStudentMapper">

    <resultMap id="BaseResultMap" type="com.get.partnercenter.entity.MAppStudentEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
            <result property="fkCompanyId" column="fk_company_id" jdbcType="BIGINT"/>
            <result property="num" column="num" jdbcType="VARCHAR"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="lastName" column="last_name" jdbcType="VARCHAR"/>
            <result property="firstName" column="first_name" jdbcType="VARCHAR"/>
            <result property="gender" column="gender" jdbcType="VARCHAR"/>
            <result property="birthday" column="birthday" jdbcType="TIMESTAMP"/>
            <result property="fkAreaCountryIdNationality" column="fk_area_country_id_nationality" jdbcType="BIGINT"/>
            <result property="fkAreaCountryNameNationality" column="fk_area_country_name_nationality" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryIdGreenCard" column="fk_area_country_id_green_card" jdbcType="BIGINT"/>
            <result property="passportNum" column="passport_num" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryIdPassport" column="fk_area_country_id_passport" jdbcType="BIGINT"/>
            <result property="fkAreaCountryIdBirth" column="fk_area_country_id_birth" jdbcType="BIGINT"/>
            <result property="fkAreaStateIdBirth" column="fk_area_state_id_birth" jdbcType="BIGINT"/>
            <result property="fkAreaCityIdBirth" column="fk_area_city_id_birth" jdbcType="BIGINT"/>
            <result property="fkAreaCountryNameBirth" column="fk_area_country_name_birth" jdbcType="VARCHAR"/>
            <result property="fkAreaStateNameBirth" column="fk_area_state_name_birth" jdbcType="VARCHAR"/>
            <result property="fkAreaCityNameBirth" column="fk_area_city_name_birth" jdbcType="VARCHAR"/>
            <result property="mobileAreaCode" column="mobile_area_code" jdbcType="VARCHAR"/>
            <result property="mobile" column="mobile" jdbcType="VARCHAR"/>
            <result property="telAreaCode" column="tel_area_code" jdbcType="VARCHAR"/>
            <result property="tel" column="tel" jdbcType="VARCHAR"/>
            <result property="email" column="email" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryId" column="fk_area_country_id" jdbcType="BIGINT"/>
            <result property="fkAreaStateId" column="fk_area_state_id" jdbcType="BIGINT"/>
            <result property="fkAreaCityId" column="fk_area_city_id" jdbcType="BIGINT"/>
            <result property="fkAreaCountryName" column="fk_area_country_name" jdbcType="VARCHAR"/>
            <result property="fkAreaStateName" column="fk_area_state_name" jdbcType="VARCHAR"/>
            <result property="fkAreaCityName" column="fk_area_city_name" jdbcType="VARCHAR"/>
            <result property="zipcode" column="zipcode" jdbcType="VARCHAR"/>
            <result property="contactAddress" column="contact_address" jdbcType="VARCHAR"/>
            <result property="educationLevelType" column="education_level_type" jdbcType="VARCHAR"/>
            <result property="educationMajor" column="education_major" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryIdEducation" column="fk_area_country_id_education" jdbcType="BIGINT"/>
            <result property="fkAreaStateIdEducation" column="fk_area_state_id_education" jdbcType="BIGINT"/>
            <result property="fkAreaCityIdEducation" column="fk_area_city_id_education" jdbcType="BIGINT"/>
            <result property="fkAreaCountryNameEducation" column="fk_area_country_name_education" jdbcType="VARCHAR"/>
            <result property="fkAreaStateNameEducation" column="fk_area_state_name_education" jdbcType="VARCHAR"/>
            <result property="fkAreaCityNameEducation" column="fk_area_city_name_education" jdbcType="VARCHAR"/>
            <result property="fkInstitutionIdEducation" column="fk_institution_id_education" jdbcType="BIGINT"/>
            <result property="fkInstitutionNameEducation" column="fk_institution_name_education" jdbcType="VARCHAR"/>
            <result property="institutionTypeEducation" column="institution_type_education" jdbcType="VARCHAR"/>
            <result property="educationLevelType2" column="education_level_type2" jdbcType="VARCHAR"/>
            <result property="educationMajor2" column="education_major2" jdbcType="VARCHAR"/>
            <result property="fkAreaCountryIdEducation2" column="fk_area_country_id_education2" jdbcType="BIGINT"/>
            <result property="fkAreaStateIdEducation2" column="fk_area_state_id_education2" jdbcType="BIGINT"/>
            <result property="fkAreaCityIdEducation2" column="fk_area_city_id_education2" jdbcType="BIGINT"/>
            <result property="fkAreaCountryNameEducation2" column="fk_area_country_name_education2" jdbcType="VARCHAR"/>
            <result property="fkAreaStateNameEducation2" column="fk_area_state_name_education2" jdbcType="VARCHAR"/>
            <result property="fkAreaCityNameEducation2" column="fk_area_city_name_education2" jdbcType="VARCHAR"/>
            <result property="fkInstitutionIdEducation2" column="fk_institution_id_education2" jdbcType="BIGINT"/>
            <result property="fkInstitutionNameEducation2" column="fk_institution_name_education2" jdbcType="VARCHAR"/>
            <result property="educationProject" column="education_project" jdbcType="INTEGER"/>
            <result property="educationDegree" column="education_degree" jdbcType="INTEGER"/>
            <result property="isComplexEducation" column="is_complex_education" jdbcType="BIT"/>
            <result property="complexEducationRemark" column="complex_education_remark" jdbcType="VARCHAR"/>
            <result property="highSchoolTestType" column="high_school_test_type" jdbcType="VARCHAR"/>
            <result property="highSchoolTestScore" column="high_school_test_score" jdbcType="VARCHAR"/>
            <result property="standardTestType" column="standard_test_type" jdbcType="VARCHAR"/>
            <result property="standardTestScore" column="standard_test_score" jdbcType="VARCHAR"/>
            <result property="masterTestType" column="master_test_type" jdbcType="VARCHAR"/>
            <result property="masterTestScore" column="master_test_score" jdbcType="VARCHAR"/>
            <result property="englishTestType" column="english_test_type" jdbcType="VARCHAR"/>
            <result property="englishTestScore" column="english_test_score" jdbcType="DECIMAL"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="fkStudentId" column="fk_student_id" jdbcType="BIGINT"/>
            <result property="fkPlatformId" column="fk_platform_id" jdbcType="BIGINT"/>
            <result property="fkPlatformCode" column="fk_platform_code" jdbcType="VARCHAR"/>
            <result property="fkPlatformCreateUserId" column="fk_platform_create_user_id" jdbcType="BIGINT"/>
            <result property="status" column="status" jdbcType="INTEGER"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="searchDetail" resultType="com.get.partnercenter.vo.MAppStudentVo">
            SELECT mAppStudent.*,
                   mCompany.num AS fkCompanyName,
                   CASE WHEN uAreaCountry.name_chn IS NULL THEN uAreaCountry.name ELSE uAreaCountry.name_chn END  AS fkAreaCountryNamePassport,
                   CASE WHEN uAreaCountryGreen.name_chn IS NULL THEN uAreaCountryGreen.name ELSE uAreaCountryGreen.name_chn END  AS countryNameGreenCard,
                   uStudentEducationLevelType.type_name_chn AS domesticEducationName,
                   uStudentEducationLevelType2.type_name_chn AS internationalEducationName,
                   (
                           select count(*)  from   ais_sale_center.m_app_student_offer_item mAppStudentOfferItem
                           WHERE mAppStudentOfferItem.fk_app_student_id=mAppStudent.id AND mAppStudentOfferItem.is_additional=1
                             AND mAppStudentOfferItem.status_additional=1   LIMIT 1) AS checkItemNUm,
                   (
                           select count(*)  from   ais_sale_center.m_app_student_offer_item mAppStudentOfferItem
                           WHERE mAppStudentOfferItem.fk_app_student_id=mAppStudent.id AND mAppStudentOfferItem.is_additional=1   LIMIT 1) AS jiashenCheckItemNUm


            FROM ais_sale_center.m_app_student mAppStudent
                         LEFT JOIN ais_permission_center.m_company mCompany ON mAppStudent.fk_company_id=mCompany.id
                         LEFT JOIN ais_institution_center.u_area_country uAreaCountry ON uAreaCountry.id = mAppStudent.fk_area_country_id_passport
                         LEFT JOIN ais_institution_center.u_area_country uAreaCountryGreen ON uAreaCountryGreen.id = mAppStudent.fk_area_country_id_green_card

                         LEFT JOIN ais_sale_center.u_student_education_level_type uStudentEducationLevelType ON uStudentEducationLevelType.id=mAppStudent.education_level_type
                         LEFT JOIN ais_sale_center.u_student_education_level_type uStudentEducationLevelType2 ON uStudentEducationLevelType2.id=mAppStudent.education_level_type2



            WHERE mAppStudent.id=#{id} limit 1



    </select>


</mapper>
