package com.get.partnercenter.util;

import org.springframework.mail.javamail.JavaMailSenderImpl;

import java.util.Properties;


/**
 * 邮件发送工厂类
 */
public class MailSenderFactory {

    /**
     * 创建邮件发送器
     * @param email
     * @param password
     * @return
     */
    public static JavaMailSenderImpl buildMailSender(String email, String password) {
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();

        String host;
        int port = 465;
        String protocol = "smtps";

        if (email.endsWith("@qq.com")) {
            host = "smtp.qq.com";
        } else if (email.endsWith("@163.com")) {
            host = "smtp.163.com";
        } else if (email.endsWith("@geteducation.org") || email.endsWith("@ht-international.net")) {
            host = "smtp.exmail.qq.com";
        } else {
            host = "smtp.qiye.aliyun.com";
        }

        mailSender.setHost(host);
        mailSender.setPort(port);
        mailSender.setProtocol(protocol);
        mailSender.setUsername(email);
        mailSender.setPassword(password);
        mailSender.setDefaultEncoding("UTF-8");

        Properties props = new Properties();
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.ssl.enable", "true");
        props.put("mail.smtp.timeout", "5000");
        props.put("mail.smtp.connectiontimeout", "5000");

        mailSender.setJavaMailProperties(props);

        return mailSender;
    }
}
