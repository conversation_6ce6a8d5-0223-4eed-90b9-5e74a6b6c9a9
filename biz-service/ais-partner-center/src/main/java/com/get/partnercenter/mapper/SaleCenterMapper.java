package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.partnercenter.dto.MEventParamsDto;
import com.get.partnercenter.dto.SendEmailInfoDto;
import com.get.partnercenter.vo.AgentContactVo;
import com.get.partnercenter.vo.BdVo;
import com.get.partnercenter.vo.FileArray;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.vo.MediaAndAttachedVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * ais_institution_center
 */
@Mapper
@DS("saledb")
public interface SaleCenterMapper {

    /**
     * 代理联系人
     * @param agentId
     * @return
     */
    List<AgentContactVo> selectAgentContacts(@Param("agentId") Long agentId);

    /**
     * 根据代理id查询邮件信息
     * @param agentId
     * @return
     */
    SendEmailInfoDto selectEmailInfoByAgentId(@Param("agentId") Long agentId);

    /**
     * 代理列表
     *
     * @param staffFollowerIds
     * @param keyword
     * @param agentName
     * @return
     */
    List<BdVo> selectAgentList(IPage<BdVo> page, @Param("staffFollowerIds") List<Long> staffFollowerIds,
                               @Param("keyword") String keyword, @Param("agentName") String agentName);

    List<FileArray> selectEventFileArrays(MEventParamsDto params);

    /**
     * 查询热门活动附件guid
     *
     * @param fkTableId
     * @return
     */
    MediaAndAttachedVo selectEventMediaAndAttached(@Param("fkTableId") Long fkTableId);

    /**
     * 删除销售中心附件
     * @param id
     */
    void deleteMediaAndAttachedById(@Param("id") Long id);

    /**
     *
     * @param fkTableId
     * @param typeKey
     * @return
     */
    List<MediaAndAttachedVo> selectEventMediaAndAttachedList(@Param("fkTableId") Long fkTableId, @Param("typeKey") String typeKey);

}
