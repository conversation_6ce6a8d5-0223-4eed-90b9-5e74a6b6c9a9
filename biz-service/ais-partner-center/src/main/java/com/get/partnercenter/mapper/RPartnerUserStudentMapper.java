package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.partnercenter.entity.RPartnerUserStudentEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【r_partner_user_student】的数据库操作Mapper
* @createDate 2025-03-31 18:11:10
* @Entity com.get.partnercenter.entity.RPartnerUserStudent
*/

@Mapper
@DS(("partnerdb"))
public interface RPartnerUserStudentMapper extends BaseMapper<RPartnerUserStudentEntity> {

}




