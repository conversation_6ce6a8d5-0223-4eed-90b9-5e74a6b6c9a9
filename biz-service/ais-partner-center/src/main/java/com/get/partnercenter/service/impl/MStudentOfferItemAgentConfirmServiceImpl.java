package com.get.partnercenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.partnercenter.dto.CommissionConfirmStudentsDto;
import com.get.partnercenter.dto.CommissionParamsDto;
import com.get.partnercenter.entity.MStudentOfferItemAgentConfirmEntity;
import com.get.partnercenter.service.MStudentOfferItemAgentConfirmService;
import com.get.partnercenter.mapper.MStudentOfferItemAgentConfirmMapper;
import com.get.partnercenter.vo.CommissionAgentInfoVo;
import com.get.partnercenter.vo.CommissionConfirmStudentsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_student_offer_item_agent_confirm】的数据库操作Service实现
* @createDate 2025-02-20 09:52:38
*/
@Service
public class MStudentOfferItemAgentConfirmServiceImpl extends ServiceImpl<MStudentOfferItemAgentConfirmMapper, MStudentOfferItemAgentConfirmEntity>
    implements MStudentOfferItemAgentConfirmService{

    @Autowired
    private MStudentOfferItemAgentConfirmMapper mStudentOfferItemAgentConfirmMapper;

    @Override
    public List<CommissionAgentInfoVo> searchAgentPage(CommissionParamsDto params, Page page) {

        IPage<CommissionAgentInfoVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        if(ObjectUtils.isEmpty(params.getCompanyId())){
            params.setCompanyId(3l);
        }

        List<CommissionAgentInfoVo>  resultArr=mStudentOfferItemAgentConfirmMapper.searchAgentPage(pages,params);

        page.setAll((int) pages.getTotal());


        return resultArr;
    }

    @Override
    public List<CommissionConfirmStudentsVo> searchOfferInfo(CommissionConfirmStudentsDto params) {
        List<CommissionConfirmStudentsVo>  offeritem=mStudentOfferItemAgentConfirmMapper.searchOfferInfo(params);

        return offeritem;
    }


}




