package com.get.partnercenter.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.partnercenter.constant.WxConstant;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class WxUtils {

    private static final Logger log = LoggerFactory.getLogger(WxUtils.class);

    public static String getAccessToken(String APP_ID, String APP_SECRET) throws Exception {
        if (StringUtils.isEmpty(APP_ID) || StringUtils.isEmpty(APP_SECRET)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("lack_of_necessary_parameters", "缺少必要的参数"));
        }
        String requestUrl = WxConstant.WX_ACCESS_TOKEN_PATH + APP_ID + "&secret=" + APP_SECRET;
        URL url = new URL(requestUrl);
        // 打开和URL之间的连接
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        // 设置通用的请求属性
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Connection", "Keep-Alive");
        connection.setUseCaches(false);
        connection.setDoOutput(true);
        connection.setDoInput(true);
        // 得到请求的输出流对象
        DataOutputStream out = new DataOutputStream(connection.getOutputStream());
        out.writeBytes("");
        out.flush();
        out.close();
        // 建立实际的连接
        connection.connect();
        // 定义 BufferedReader输入流来读取URL的响应
        BufferedReader in = null;
        if (requestUrl.contains("nlp")) {
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "GBK"));
        } else {
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8));
        }
        String result = "";
        String getLine;
        while ((getLine = in.readLine()) != null) {
            result += getLine;
        }
        in.close();

        JSONObject jsonObject = JSONObject.parseObject(result);
        log.info("获取access_token结果:{}", jsonObject);
        if (!jsonObject.containsKey("access_token")) {
            log.error("获取access_token失败:{}", jsonObject);
            throw new GetServiceException("获取access_token失败");
        }
        return jsonObject.getString("access_token");
    }

    public static String encodeParam(String param) {
        try {
            return URLEncoder.encode(param, "UTF-8");
        } catch (Exception e) {
            return param;
        }
    }

    /**
     * GET 请求
     *
     * @param url
     * @param params
     * @param tTypeReference
     * @param <T>
     * @return
     */
    public static <T> T doRequest(String url,
                                  Map<String, String> params,
                                  TypeReference<T> tTypeReference) {
        String apiUrl = url;
        String urlParams = "";
        Set<String> keySet = params.keySet();
        for (String paramKey : keySet) {
            urlParams += "&" + paramKey + "=" + encodeParam(params.get(paramKey));
        }
        try {
            String result = doGet(apiUrl + "?" + urlParams, null);
            try {
                T response = JSON.parseObject(result, tTypeReference);
                return response;
            } catch (Exception e) {
                throw e;
            }
        } catch (Exception e) {
            throw e;
        }
    }

    public static String doGet(String url, Map<String, String> param) {
        if (StringUtils.isEmpty(url)) {
            return null;
        } else {
            RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(15000).setConnectTimeout(15000).build();
            CloseableHttpClient httpclient = HttpClients.custom().setDefaultRequestConfig(requestConfig).build();
            String resultString = "";
            CloseableHttpResponse response = null;

            try {
                URIBuilder builder = new URIBuilder(url);
                if (param != null) {
                    for (String key : param.keySet()) {
                        builder.addParameter(key, (String) param.get(key));
                    }
                }

                URI uri = builder.build();
                HttpGet httpGet = new HttpGet(uri);
                response = httpclient.execute(httpGet);
                if (response.getStatusLine().getStatusCode() == 200) {
                    resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
                }
            } catch (Exception e) {
                log.error("HttpClientUtil-doGet方法发生异常:", e);
            } finally {
                try {
                    if (response != null) {
                        response.close();
                    }

                    httpclient.close();
                } catch (IOException e) {
                    log.error("HttpClientUtil-doGet方法发生异常:", e);
                }

            }

            return resultString;
        }
    }
}
