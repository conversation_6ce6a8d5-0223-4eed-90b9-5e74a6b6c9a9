package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.partnercenter.entity.MFilePartnerEntity;
import org.apache.ibatis.annotations.Mapper;

/**
* <AUTHOR>
* @description 针对表【m_file_partner】的数据库操作Mapper
* @createDate 2025-01-08 16:45:07
* @Entity com.partner.entity.MFilePartner
*/
@Mapper
@DS("appfiledb")
public interface MFilePartnerMapper extends BaseMapper<MFilePartnerEntity> {


}




