package com.get.partnercenter.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.partnercenter.entity.UAgentCommissionTypeEntity;
import com.get.partnercenter.service.UAgentCommissionTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "后台管理-代理等级")
@RestController
@RequestMapping("partner/uAgentCommissionType")
public class UAgentCommissionTypeController {

    @Autowired
    private UAgentCommissionTypeService uAgentCommissionTypeService;

    @ApiOperation(value = "代理等级-列表数据")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/代理等级/列表查询")
    @GetMapping("searchList")
    public ResponseBo<UAgentCommissionTypeEntity> searchList(){
        List<UAgentCommissionTypeEntity> datas=uAgentCommissionTypeService.searchList();

        return new ResponseBo(datas);
    }


}
