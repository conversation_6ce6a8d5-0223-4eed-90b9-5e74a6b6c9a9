package com.get.partnercenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.partnercenter.dto.HighCommissionDto;
import com.get.partnercenter.dto.MEventParamsDto;
import com.get.partnercenter.entity.MEventEntity;
import com.get.partnercenter.vo.MEventRegistrationAgentVo;
import com.get.partnercenter.vo.MEventVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_event】的数据库操作Mapper
* @createDate 2025-04-22 09:57:14
* @Entity com.get.partnercenter.entity.MEvent
*/
@Mapper
@DS("saledb")
public interface MEventMapper extends BaseMapper<MEventEntity> {

    List<MEventVo> searchPage(IPage<MEventVo> page, @Param("query") MEventParamsDto params);

    MEventVo selectByDetail(MEventParamsDto params);


    int updateByIds(@Param("type")int type,@Param("ids")Long[] ids);


    List<MEventRegistrationAgentVo> searchRegistration(IPage<MEventRegistrationAgentVo> page, @Param("query") MEventParamsDto params);


}




