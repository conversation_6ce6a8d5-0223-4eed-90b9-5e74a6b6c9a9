<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.partnercenter.mapper.MStudentOfferItemAgentConfirmMapper">

    <resultMap id="BaseResultMap" type="com.get.partnercenter.entity.MStudentOfferItemAgentConfirmEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="fkTenantId" column="fk_tenant_id" jdbcType="BIGINT"/>
        <result property="fkAgentId" column="fk_agent_id" jdbcType="BIGINT"/>
        <result property="fkStudentOfferItemId" column="fk_student_offer_item_id" jdbcType="BIGINT"/>
        <result property="fkPartnerUserId" column="fk_partner_user_id" jdbcType="BIGINT"/>
        <result property="isSystemConfirmed" column="is_system_confirmed" jdbcType="BIT"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="searchAgentPage" resultType="com.get.partnercenter.vo.CommissionAgentInfoVo">
        SELECT
            rAgentCompany.id  AS id,
            mCompany.id AS companyId,
            mCompany.NUM AS companyNum,
            mCompany.name_chn AS companyName,
            mAgent.is_active AS status,
            mAgent.id AS agentId,
            mAgent.num AS agentNum,
            mAgent.name AS agentName,
            rStaffBdCode.bd_code,
            mStaff.name AS bdName,
            mStaff.name_en AS bdNameEn,
            b.num AS studentNum,
            uAreaCountry.name_chn AS countryName,
            uAreaState.name AS stateName
        FROM ais_sale_center.m_agent mAgent
                 INNER JOIN ais_sale_center.r_agent_company rAgentCompany ON rAgentCompany.fk_agent_id=mAgent.id
                 INNER JOIN ais_permission_center.m_company mCompany ON rAgentCompany.fk_company_id=mCompany.id
                 INNER JOIN ais_sale_center.r_agent_staff rAgentStaff ON rAgentStaff.fk_agent_id = mAgent.id and rAgentStaff.is_active = 1
                 INNER JOIN ais_permission_center.m_staff mStaff ON mStaff.id=rAgentStaff.fk_staff_id
                 INNER JOIN ais_sale_center.r_staff_bd_code rStaffBdCode ON rStaffBdCode.fk_staff_id = rAgentStaff.fk_staff_id
                 INNER JOIN
                 (
                     SELECT offerItem.fk_agent_id ,count(*) num FROM  ais_sale_center.m_student_offer_item offerItem
                     INNER JOIN (
                         SELECT  offerItem.id AS itemId,min(rStudentOfferItemStep.gmt_create) AS gmt_create
                             FROM ais_sale_center.m_student_offer_item offerItem
                             INNER JOIN  ais_sale_center.r_student_offer_item_step rStudentOfferItemStep ON offerItem.id=rStudentOfferItemStep.fk_student_offer_item_id
                             WHERE    rStudentOfferItemStep.fk_student_offer_item_step_id IN(6,7,8,10)
                             GROUP BY offerItem.id
                             HAVING  YEAR(min(rStudentOfferItemStep.gmt_create))=#{query.year} and MONTH(min(rStudentOfferItemStep.gmt_create))=#{query.month}
                     ) a ON  a.itemId=offerItem.id
                    LEFT JOIN  ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=offerItem.fk_institution_course_id
                    LEFT  JOIN ais_institution_center.m_institution mInstitution ON mInstitution.id = offerItem.fk_institution_id
                    WHERE 1=1
                    <if test="query.areaCountryId!=null  ">
                        AND offerItem.fk_area_country_id=#{query.areaCountryId}
                    </if>
                    <if test="query.institutionName!=null  and query.institutionName!=''">
                        AND
                        (
                            mInstitution.name like concat("%",#{query.institutionName},"%") OR mInstitution.name_chn like concat("%",#{query.institutionName},"%")
                        )
                    </if>
                    <if test="query.courseName!=null  and query.courseName!=''">
                        AND
                        (
                            institutionCourse.name like concat("%",#{query.courseName},"%") OR institutionCourse.name_chn like concat("%",#{query.courseName},"%")
                        )
                    </if>
                    <if test="query.courseLevelId!=null  ">
                        AND FIND_IN_SET(#{query.courseLevelId},offerItem.fk_institution_course_major_level_ids)
                    </if>


                        GROUP BY offerItem.fk_agent_id
                 ) b ON 	b.fk_agent_id=mAgent.id
                 LEFT  JOIN ais_institution_center.u_area_country uAreaCountry ON mAgent.fk_area_country_id = uAreaCountry.id
                 LEFT  JOIN ais_institution_center.u_area_state uAreaState ON mAgent.fk_area_state_id = uAreaState.id
        WHERE 1=1
            <if test="query.agentName!=null and query.agentName!='' ">
                AND mAgent.name like concat("%",#{query.agentName},"%")
            </if>

    </select>
    <select id="searchOfferInfo" resultType="com.get.partnercenter.vo.CommissionConfirmStudentsVo">
        SELECT
            offerItem.id,
            offerItem.fk_parent_student_offer_item_id,
            offerItem.fk_student_offer_id,
            offerItem.opening_time AS openingTime,
            agentConfirm.is_system_confirmed,
            offerItemStep.step_name AS stepName,
            student.name AS studentName,
            student.last_name AS lastName,
            student.first_name AS firstName,
            student.id AS studentId,
            student.birthday AS birthday,
            uAreaCountry.name_chn AS countryName,
            mInstitution.name_chn AS institutionName,
            institutionCourse.name_chn AS courseName,
            agentConfirm.gmt_create_user,
            agentConfirm.gmt_create,
            offerItem.is_follow,
            mInstitutionChanner.name AS channerName,
            mInstitutionProvider.name AS providerName
        FROM  ais_sale_center.m_student_offer_item offerItem
                  INNER JOIN (
            SELECT  offerItem.id AS itemId,min(rStudentOfferItemStep.gmt_create) AS gmt_create
            FROM ais_sale_center.m_student_offer_item offerItem
                     INNER JOIN  ais_sale_center.r_student_offer_item_step rStudentOfferItemStep ON offerItem.id=rStudentOfferItemStep.fk_student_offer_item_id
            WHERE    rStudentOfferItemStep.fk_student_offer_item_step_id IN(6,7,8,10)
            GROUP BY offerItem.id HAVING  YEAR(min(rStudentOfferItemStep.gmt_create))=#{year} and MONTH(min(rStudentOfferItemStep.gmt_create))=#{month}
        ) a ON  a.itemId=offerItem.id
                  INNER JOIN  ais_sale_center.m_student student ON student.id = offerItem.fk_student_id
                  INNER JOIN ais_sale_center.u_student_offer_item_step AS offerItemStep ON offerItemStep.id = offerItem.fk_student_offer_item_step_id
                  LEFT JOIN  app_partner_center.m_student_offer_item_agent_confirm agentConfirm ON  agentConfirm.fk_student_offer_item_id=offerItem.id
                  LEFT  JOIN ais_institution_center.u_area_country uAreaCountry ON offerItem.fk_area_country_id = uAreaCountry.id
                  LEFT JOIN  ais_institution_center.m_institution_course institutionCourse ON institutionCourse.id=offerItem.fk_institution_course_id
                  LEFT  JOIN ais_institution_center.m_institution mInstitution ON mInstitution.id = offerItem.fk_institution_id
                  LEFT JOIN ais_institution_center.m_institution_channel mInstitutionChanner ON offerItem.fk_institution_channel_id=mInstitutionChanner.id
                  LEFT JOIN ais_institution_center.m_institution_provider mInstitutionProvider ON offerItem.fk_institution_provider_id=mInstitutionProvider.id
        WHERE  offerItem.fk_agent_id=#{agentId}
        <if test="companyId!=null">
              AND student.fk_company_id=#{companyId}
          </if>


    </select>


</mapper>
