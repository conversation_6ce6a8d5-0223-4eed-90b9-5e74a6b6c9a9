#服务器端口
server:
  port: 1098

#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: partnerdb
      datasource:
        partnerdb:
          url: ${get.datasource.test.partnerdb.url}
          username: ${get.datasource.test.partnerdb.username}
          password: ${get.datasource.test.partnerdb.password}
        saledb:
          url: ${get.datasource.test.saledb.url}
          username: ${get.datasource.test.saledb.username}
          password: ${get.datasource.test.saledb.password}
        permissiondb:
          url: ${get.datasource.test.permissiondb.url}
          username: ${get.datasource.test.permissiondb.username}
          password: ${get.datasource.test.permissiondb.password}
        institutiondb:
          url: ${get.datasource.test.institutiondb.url}
          username: ${get.datasource.test.institutiondb.username}
          password: ${get.datasource.test.institutiondb.password}
        systemdb:
          url: ${get.datasource.test.systemdb.url}
          username: ${get.datasource.test.systemdb.username}
          password: ${get.datasource.test.systemdb.password}
        appfiledb:
          url: ${get.datasource.test.appfiledb.url}
          username: ${get.datasource.test.appfiledb.username}
          password: ${get.datasource.test.appfiledb.password}
        pmp2db:
          url: ${get.datasource.test.pmp2db.url}
          username: ${get.datasource.test.pmp2db.username}
          password: ${get.datasource.test.pmp2db.password}
        platformdb:
          url: ${get.datasource.test.platformdb.url}
          username: ${get.datasource.test.platformdb.username}
          password: ${get.datasource.test.platformdb.password}