package com.get.aismail.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.aismail.entity.MMailSyncQueueDoris;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@DS("mail-doris")
@Mapper
public interface MMailSyncQueueDorisMapper extends BaseMapper<MMailSyncQueueDoris> {
/*    void deleteById(@Param("id") Long id);
    void insertDoris(MMailSyncQueueDoris mailSyncQueueDoris);*/
}
