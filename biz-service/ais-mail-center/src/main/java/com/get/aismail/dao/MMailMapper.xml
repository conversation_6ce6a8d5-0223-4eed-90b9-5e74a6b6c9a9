<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aismail.dao.MMailMapper">
    <select id="selectMail" resultType="com.get.aismail.dto.MailDto">
        SELECT
        mail.fk_mail_account_id AS fkAccountId,
        mail.id AS id,
        mail.mail_id AS mailID,
        mail.to_mail AS toName,
        mail.from_mail AS fromName,
        mail.cc_mail AS ccName,
        mail.bcc_mail AS bccName,
        mail.subject AS subject,
        mail.body AS body,
        mail.fold_box AS foldBox,
        mail.is_read AS isRead,
        mail.is_star AS isStar,
        mail.llm_mail_type AS llmMailType,
        llm.llm_mail_analysis AS llmMailAnalysis,
        mail.date AS date,
        qu.operation_type AS operationType,
        qu.operation_value AS operationValue
        FROM m_mail AS mail
        LEFT JOIN (SELECT * FROM m_mail_sync_queue AS qu1
        WHERE is_sync = FALSE
        AND qu1.operation_type = 3
        AND qu1.gmt_create = (
        SELECT MAX(gmt_create)
        FROM m_mail_sync_queue AS qu2
        WHERE qu2.fk_mail_id = qu1.fk_mail_id
        AND qu2.is_sync = FALSE
        AND qu2.operation_type = 3
        )
        ORDER BY qu1.gmt_create DESC) AS qu ON mail.id = qu.fk_mail_id
        LEFT JOIN (SELECT * FROM m_mail_llm_analysis) AS llm ON mail.id = llm.fk_mail_id
        <where>
            mail.fk_mail_account_id = #{searchMailVo.mailAccountId}
            <if test="searchMailVo.boxName != null and searchMailVo.boxName != '' ">
                AND (
                (qu.operation_type IS NOT NULL AND qu.operation_type != ''
                AND qu.operation_value = #{searchMailVo.boxName})
                OR
                (qu.operation_type IS NULL AND mail.fold_box = #{searchMailVo.boxName})
                )
            </if>
            <if test="searchMailVo.isStart != null and searchMailVo.isStart != '' ">
                AND mail.is_star = #{searchMailVo.isStart}
            </if>
            <if test="searchMailVo.dateSort != null and searchMailVo.dateSort != '' and searchMailVo.dateSort == 1">
                AND DATE(mail.date) = #{searchMailVo.today}
            </if>
            <if test="searchMailVo.dateSort != null and searchMailVo.dateSort != '' and searchMailVo.dateSort == 2">
                AND DATE(mail.date) = #{searchMailVo.yesterday}
            </if>
            <if test="searchMailVo.dateSort != null and searchMailVo.dateSort != '' and searchMailVo.dateSort == 3">
                AND DATE(mail.date) &lt; #{searchMailVo.yesterday}
            </if>
            <if test="searchMailVo.searchContent != null and searchMailVo.searchContent != '' ">
                AND(
                mail.body LIKE concat ('%',#{searchMailVo.searchContent},'%')
                OR mail.subject LIKE concat ('%',#{searchMailVo.searchContent},'%')
                OR mail.from_mail LIKE concat ('%',#{searchMailVo.searchContent},'%')
                )
            </if>
            <if test="searchMailVo.llmMailType != null and searchMailVo.llmMailType != '' ">
                AND mail.llm_mail_type = #{searchMailVo.llmMailType}
            </if>
            ORDER BY mail.date Desc
        </where>
    </select>

    <select id="selectMailByIds" resultType="com.get.aismail.dto.MailDto">
        SELECT
        mail.fk_mail_account_id AS fkAccountId,
        mail.id AS id,
        mail.mail_id AS mailID,
        mail.to_mail AS toName,
        mail.from_mail AS fromName,
        mail.cc_mail AS ccName,
        mail.bcc_mail AS bccName,
        mail.subject AS subject,
        mail.body AS body,
        mail.fold_box AS foldBox,
        mail.is_read AS isRead,
        mail.is_star AS isStar,
        mail.llm_mail_type AS llmMailType,
        llm.llm_mail_analysis AS llmMailAnalysis,
        mail.date AS date,
        qu.operation_type AS operationType,
        qu.operation_value AS operationValue
        FROM m_mail AS mail
        LEFT JOIN (SELECT * FROM m_mail_sync_queue AS qu1
        WHERE is_sync = FALSE
        AND qu1.operation_type = 3
        AND qu1.gmt_create = (
        SELECT MAX(gmt_create)
        FROM m_mail_sync_queue AS qu2
        WHERE qu2.fk_mail_id = qu1.fk_mail_id
        AND qu2.is_sync = FALSE
        AND qu2.operation_type = 3
        )
        ORDER BY qu1.gmt_create DESC) AS qu ON mail.id = qu.fk_mail_id
        LEFT JOIN (SELECT * FROM m_mail_llm_analysis) AS llm ON mail.id = llm.fk_mail_id
        <where>
            mail.id IN
            <foreach item="id" collection="ids" open="(" separator="," close=")">
                #{id}
            </foreach>
        </where>
        ORDER BY mail.date Desc
    </select>

    <select id="selectNotRead" resultType="int">
        SELECT COUNT(*)
        FROM m_mail AS mail
        LEFT JOIN (SELECT * FROM m_mail_sync_queue AS qu1
        WHERE is_sync = FALSE
        AND qu1.operation_type = 3
        AND qu1.gmt_create = (
        SELECT MAX(gmt_create)
        FROM m_mail_sync_queue AS qu2
        WHERE qu2.fk_mail_id = qu1.fk_mail_id
        AND qu2.is_sync = FALSE
        AND qu2.operation_type = 3
        )
        ORDER BY qu1.gmt_create DESC) AS qu ON mail.id = qu.fk_mail_id
        <where>
            mail.fk_mail_account_id = #{searchMailVo.mailAccountId}
            AND mail.is_read = FALSE
            <if test="searchMailVo.boxName != null and searchMailVo.boxName != '' ">
                AND (
                (qu.operation_type IS NOT NULL AND qu.operation_type != ''
                AND qu.operation_value = #{searchMailVo.boxName})
                OR
                (qu.operation_type IS NULL AND mail.fold_box = #{searchMailVo.boxName})
                )
            </if>
            <if test="searchMailVo.dateSort != null and searchMailVo.dateSort != '' and searchMailVo.dateSort == 1">
                AND DATE(mail.date) = #{searchMailVo.today}
            </if>
            <if test="searchMailVo.dateSort != null and searchMailVo.dateSort != '' and searchMailVo.dateSort == 2">
                AND DATE(mail.date) = #{searchMailVo.yesterday}
            </if>
            <if test="searchMailVo.dateSort != null and searchMailVo.dateSort != '' and searchMailVo.dateSort == 3">
                AND DATE(mail.date) &lt; #{searchMailVo.yesterday}
            </if>
            <if test="searchMailVo.llmMailType != null and searchMailVo.llmMailType != '' ">
                AND mail.llm_mail_type = #{searchMailVo.llmMailType}
            </if>
        </where>
    </select>
</mapper>