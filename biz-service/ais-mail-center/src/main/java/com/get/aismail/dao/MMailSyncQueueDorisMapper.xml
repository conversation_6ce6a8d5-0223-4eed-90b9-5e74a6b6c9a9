<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aismail.dao.MMailSyncQueueDorisMapper">
    <delete id="deleteById">
        delete
            from m_mail_sync_queue
        where m_mail_sync_queue.id =  #{id}
    </delete>

    <insert id="insertDoris" parameterType="com.get.aismail.entity.MMailSyncQueueDoris">
        INSERT INTO m_mail_sync_queue
        (id, fk_mail_account_id, fk_mail_id, mail_id, operation_type, operation_value, is_sync, gmt_create, gmt_create_user)
        VALUES
            (#{id}, #{fkMailAccountId}, #{fkMailId}, #{mailId}, #{operationType}, #{operationValue}, #{isSync}, #{gmtCreate}, #{gmtCreateUser})
    </insert>
</mapper>