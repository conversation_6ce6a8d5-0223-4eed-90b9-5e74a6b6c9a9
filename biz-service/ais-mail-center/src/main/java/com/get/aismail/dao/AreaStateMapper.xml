<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aismail.dao.AreaStateMapper">
    <select id="getStateFullNameById" parameterType="java.lang.Long" resultType="java.lang.String">
        select
            CASE WHEN IFNULL(name_chn,'')='' THEN `name` ELSE CONCAT(`name`,'（',name_chn,'）') END fullName
        from
            u_area_state
        where
            id = #{id}
    </select>

</mapper>