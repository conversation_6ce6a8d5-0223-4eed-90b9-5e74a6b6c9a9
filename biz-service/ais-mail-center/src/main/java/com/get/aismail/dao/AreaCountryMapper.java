package com.get.aismail.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.aismail.dto.AreaCountryDto;
import com.get.aismail.entity.AreaCountry;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@DS("institution")
@Mapper
public interface AreaCountryMapper extends BaseMapper<AreaCountry> {

    List<AreaCountry> getAreaCode();

    List<AreaCountryDto> getAllCountryList();
}
