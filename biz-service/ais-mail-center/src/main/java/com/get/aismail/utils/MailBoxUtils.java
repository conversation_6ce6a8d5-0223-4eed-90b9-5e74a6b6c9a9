package com.get.aismail.utils;


import com.get.aismail.dao.MMailAttachedMapper;
import com.get.aismail.dao.MMailMapper;
import com.get.aismail.dto.MailBox;
import com.get.aismail.entity.MMailAccount;
import com.get.aismail.entity.MMailAttached;
import com.get.aismail.vo.DownloadAttachedVo;
import com.get.core.log.exception.GetServiceException;
import com.sun.mail.imap.IMAPFolder;
import com.sun.mail.imap.IMAPStore;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.mail.*;
import javax.mail.internet.*;
import java.io.*;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.get.core.tool.api.ResultCode.BAD_REQUEST;

@Component
public class MailBoxUtils {
    @Resource
    private MMailAttachedMapper attachedMapper;
    @Resource
    private MMailMapper mailMapper;

    // 定义用于匹配电子邮件地址的正则表达式
    private static final String EMAIL_PATTERN = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";

    /**
     * 验证提供的电子邮件地址是否有效。
     *
     * @param email 待验证的电子邮件地址。
     * @return 如果电子邮件地址有效，则返回true；否则返回false。
     */
    public static boolean isValidEmail(String email) {
        if (email == null) {
            return false;
        }

        // 创建Pattern对象
        Pattern pattern = Pattern.compile(EMAIL_PATTERN);
        // 创建Matcher对象
        Matcher matcher = pattern.matcher(email);
        // 使用matches()方法尝试匹配整个区域
        return matcher.matches();
    }

    /**
     * 登录邮箱方法
     *
     * @param username 用户名
     * @param password 密码
     * @return 返回邮箱登录实体类
     * @throws Exception 异常抛出
     */
    public static MailBox login(String username, String password, String host) throws Exception {
        // 设置属性
        Properties properties = new Properties();
        properties.put("mail.store.protocol", "imaps");
        properties.put("mail.imap.host", host);
        properties.put("mail.imap.port", "993");
        properties.put("mail.imap.ssl.enable", "true");
        properties.put("mail.imap.connectiontimeout", "1000*60*5");
        properties.setProperty("mail.imap.partialfetch", "false");
        properties.setProperty("mail.imap.fetchsize", "10485760");
/*        // 设置连接超时时间（毫秒）
        properties.put("mail.smtp.connectiontimeout", "5000");  // 5秒

        // 设置读取超时时间（毫秒）
        properties.put("mail.smtp.timeout", "5000");  // 5秒*/
        Session session = Session.getInstance(properties);
        Store store = session.getStore("imap");
//        session.setDebug(true); // 启用调试日志
        store.connect(username, password);

//        // 获取所有文件夹
//        Folder[] folders = store.getDefaultFolder().list();
        MailBox mailBox = new MailBox();
        mailBox.setSession(session);
        mailBox.setStore(store);
        return mailBox;
    }

    public static MailBox login163(String username, String password, String host, String port) throws Exception {
        Properties props = new Properties();
        props.setProperty("mail.store.protocol", "imap");
        props.setProperty("mail.imap.host", host);
        props.setProperty("mail.imap.port", port);
        props.setProperty("mail.imap.connectiontimeout", "3000000"); // 30秒
        props.setProperty("mail.imap.timeout", "30000000"); // 5分钟
        HashMap IAM = new HashMap();
        //带上IMAP ID信息，由key和value组成，例如name，version，vendor，support-email等。
        IAM.put("name", "myname");
        IAM.put("version", "1.0.0");
        IAM.put("vendor", "myclient");
        IAM.put("support-email", "<EMAIL>");
        Session session = Session.getInstance(props);

        IMAPStore store = (IMAPStore) session.getStore("imap");
        store.connect(username, password);
        store.id(IAM);
        MailBox mailBox = new MailBox();
        mailBox.setSession(session);
        mailBox.setStore(store);
        return mailBox;
    }

    public static void deleteMail(MailBox mailBox, String mailId, String foldType) throws Exception {
        Store store = mailBox.getStore();
        Folder targetFolder = store.getFolder(foldType);
        targetFolder.open(Folder.READ_WRITE);
        Message[] messages = targetFolder.getMessages();
        for (Message message : messages) {
            MimeMessage mimeMessage = (MimeMessage) message;
            String messageId = mimeMessage.getMessageID().replaceAll("<", "").replaceAll(">", "");
            if (mailId.equals(messageId)) {
                message.setFlag(Flags.Flag.DELETED, true);
                targetFolder.close(true);
                break;
            }
        }
    }


    public static String downAnnex(MailBox mailBox, String foldType, DownloadAttachedVo downloadAttachedVo, String savePath) throws Exception {
        String AbsolutePath = savePath;
        Store store = mailBox.getStore();
        Folder folder = store.getFolder(foldType);
        folder.open(Folder.READ_ONLY);
        IMAPFolder imapFolder = (IMAPFolder) folder;
        long uid = Long.parseLong(downloadAttachedVo.getMailId());
        Message message = imapFolder.getMessageByUID(uid);
        if (message != null) {
            MimeMessage mimeMessage = (MimeMessage) message;
            if (mimeMessage.isMimeType("multipart/*")) {
                MimeMultipart mimeMultipart = (MimeMultipart) mimeMessage.getContent();
                int partCount = mimeMultipart.getCount();
                for (int i = 0; i < partCount; i++) {
                    BodyPart bodyPart = mimeMultipart.getBodyPart(i);
                    if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
                        // 获取附件名称
                        String fileName = bodyPart.getFileName();
                        fileName = MimeUtility.decodeText(fileName);
                        if ((fileName.equals(downloadAttachedVo.getFileName()) || fileName.equals(downloadAttachedVo.getFileName().replaceAll(" ", " "))) && String.valueOf(i).equals(downloadAttachedVo.getAnnexId())) {
                            AbsolutePath = AbsolutePath + UUID.randomUUID() + fileName;
                            // 保存附件到本地
                            saveAttachment(bodyPart, AbsolutePath);
                        }
                    }
                }
            }
            return new File(AbsolutePath).getAbsolutePath();
        }else{
            return null;
        }
    }

    public static Session buildSendSession(String emailUsername, String emailPassword, String host, String port) throws Exception {
        // 设置邮件会话属性
        Properties props = new Properties();
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.port", port); // SMTP服务器端口号
        props.put("mail.smtp.starttls.enable", "true"); // 启用TLS加密
        props.put("mail.debug", "true");
        props.setProperty("mail.smtp.partialfetch", "false");
        props.setProperty("mail.smtp.fetchsize", "10485760");
//        props.setProperty("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory"); // 使用SSL加密

        // 创建Session实例对象
        return Session.getInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(emailUsername, emailPassword);
            }
        });
/*        // 创建Session对象
        return Session.getDefaultInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(emailUsername, emailPassword);
            }
        });*/
    }

    /*public static Message saveMail(MailBox mailBox, String foldName, SendMailVo sendMailVo) throws Exception {
        Store store = mailBox.getStore();
        // 创建MimeMessage对象
        Message message = new MimeMessage(mailBox.getSession());
        if (sendMailVo.getEmailAccount() != null) {
            // 发件人
            message.setFrom(new InternetAddress(sendMailVo.getEmailAccount()));
        }
        if (sendMailVo.getRecipient() != null) {
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(sendMailVo.getRecipient()));
        }
        // 抄送人，多个抄送人之间用，号隔开
        if (sendMailVo.getCcPeople() != null) {
            message.setRecipients(Message.RecipientType.CC, InternetAddress.parse(sendMailVo.getCcPeople()));
        }
        if (sendMailVo.getSubject() != null) {
            // 邮件标题
            message.setSubject(sendMailVo.getSubject());
        }
        BodyPart messageBodyPart = new MimeBodyPart();
        if (sendMailVo.getContent() != null) {
            // 邮件内容
            messageBodyPart.setText(sendMailVo.getContent());
        }
        // 创建多部件消息
        Multipart multipart = new MimeMultipart();
        multipart.addBodyPart(messageBodyPart);
        // 添加附件
        if (sendMailVo.getAttachedDtos() != null) {
            for (AttachedDto attachedDto : sendMailVo.getAttachedDtos()) {
                String annexPath = attachedDto.getPath();
                messageBodyPart = new MimeBodyPart();
                DataSource source = new FileDataSource(annexPath);
                messageBodyPart.setDataHandler(new DataHandler(source));
                messageBodyPart.setFileName(attachedDto.getName());
                multipart.addBodyPart(messageBodyPart);
            }
        }
        // 将多部件消息设置为邮件内容
        message.setContent(multipart);
        // 打开已发送邮件文件夹
        Folder sentFolder = store.getFolder(foldName);
        sentFolder.open(Folder.READ_WRITE);
        // 将邮件复制到草稿箱文件夹
        sentFolder.appendMessages(new Message[]{message});
        return message;
    }*/

/*    public static Message sendMail(Session session, MailBox mailBox, String foldName, SendMailVo sendMailVo) throws Exception {
        Store store = mailBox.getStore();
        // 创建MimeMessage对象
        Message message = new MimeMessage(session);
        // 发件人
        message.setFrom(new InternetAddress(sendMailVo.getEmailAccount()));
        message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(sendMailVo.getRecipient()));
        // 抄送人，多个抄送人之间用，号隔开
        if (sendMailVo.getCcPeople() != null && !sendMailVo.getCcPeople().isEmpty()) {
            message.setRecipients(Message.RecipientType.CC, InternetAddress.parse(sendMailVo.getCcPeople()));
        }
        // 邮件标题
        message.setSubject(sendMailVo.getSubject());
        BodyPart messageBodyPart = new MimeBodyPart();

        // 处理内嵌文件
        String bodyContent = sendMailVo.getContent();
        // 正则表达式匹配<img>标签中的src属性
        String imgRegex = "<img[^>]+src\\s*=\\s*['\"]([^'\"]+)['\"][^>]*>";
        Pattern pattern = Pattern.compile(imgRegex, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(bodyContent);
        // 创建一个列表来保存所有的src属性值
        List<String> srcList = new ArrayList<>();
        // 查找所有匹配的src属性值并添加到列表中
        while (matcher.find()) {
            srcList.add(matcher.group(1));
        }
        // 将body中的原本在桶里面的内容，替换成cid，形式。
        List<ContentReplace> contentIds = new ArrayList<>();
        String bucket = "https://hti-email-images-dev-1301376564.cos.ap-shanghai.myqcloud.com/";
        for (String str : srcList) {
            if (str.contains(bucket)) {
                String contentId = UUID.randomUUID().toString();
                String bodyContentId = "cid:" + contentId;
                bodyContent = bodyContent.replace("str", bodyContentId);
                ContentReplace contentReplace = new ContentReplace();
                contentReplace.setContentId(str.replace(bucket,""));
                contentReplace.setContentId(contentId);
                contentIds.add(contentReplace);
            }
        }

        // 邮件内容
        messageBodyPart.setContent(sendMailVo.getContent(), "text/html; charset=UTF-8");
        // 创建多部件消息
        Multipart multipart = new MimeMultipart();

        multipart.addBodyPart(messageBodyPart);
        // 添加附件
        if (sendMailVo.getAttachedDtos() != null) {
            for (AttachedDto attachedDto : sendMailVo.getAttachedDtos()) {
                String annexPath = attachedDto.getPath();
                messageBodyPart = new MimeBodyPart();
                DataSource source = new FileDataSource(annexPath);
                messageBodyPart.setDataHandler(new DataHandler(source));
                messageBodyPart.setFileName(attachedDto.getName());
                multipart.addBodyPart(messageBodyPart);
            }
        }
        // 将多部件消息设置为邮件内容
        message.setContent(multipart);
        // 发送邮件
        Transport.send(message);
        // 打开已发送邮件文件夹
        Folder sentFolder = store.getFolder(foldName);
        sentFolder.open(Folder.READ_WRITE);
        // 将邮件复制到已发送邮件文件夹
        sentFolder.appendMessages(new Message[]{message});
        sentFolder.close(true);
        return message;
    }*/


    private static void readFully(InputStream inputStream, FileOutputStream fos) throws Exception {
        byte[] buffer = new byte[327680];
        int bytesRead;
        long s = System.currentTimeMillis();
        int i = 0;
        System.out.println("开始读取附件内容");
        long e1 = System.currentTimeMillis();
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            long e2 = System.currentTimeMillis();
            System.out.println("第：" + i + "次，读取下载附件耗时：" + (e2 - e1));
            i = i + 1;
            fos.write(buffer, 0, bytesRead);
            e1 = System.currentTimeMillis();
        }
        long e = System.currentTimeMillis();
        System.out.println("读取下载附件总耗时：" + (e - s));
        inputStream.close();
    }

    public static void saveAttachment(BodyPart bodyPart, String fileName) throws Exception {
        // 创建输出文件
        File file = new File(fileName);
        try (FileOutputStream fos = new FileOutputStream(file)) {
            InputStream bodyInputStream = bodyPart.getInputStream();
            // 读取附件数据
            readFully(bodyInputStream, fos);
        }
    }

    public static void processAttachments(Multipart multipart, List<MMailAttached> mMailAttacheds, MMailAccount mMailAccount, String mailId) throws Exception {
        int partCount = multipart.getCount();
        for (int j = 0; j < partCount; j++) {
            BodyPart bodyPart = multipart.getBodyPart(j);
            // 如果这是一个嵌套的multipart部分，则递归处理
            if (bodyPart.isMimeType("multipart/*")) {
                processAttachments((Multipart) bodyPart.getContent(), mMailAttacheds, mMailAccount, mailId);
            } else {
                try {
                    // 尝试获取文件名
                    String fileName = MimeUtility.decodeText(bodyPart.getFileName());
                    // 检查是否有文件名或disposition为attachment/inline
                    String disposition = bodyPart.getDisposition();
                    if ((fileName != null && !fileName.trim().isEmpty()) ||
                            (disposition != null &&
                                    (disposition.equalsIgnoreCase(Part.ATTACHMENT) ||
                                            disposition.equalsIgnoreCase(Part.INLINE)))) {

                        if (fileName == null || fileName.trim().isEmpty()) {
                            // 如果没有文件名，创建一个默认名称
                            fileName = "unnamed_attachment_" + j;
                        }
                        // 创建并填充附件实体对象
                        MMailAttached mMailAttached = new MMailAttached();
                        mMailAttached.setFkMailAccountId(mMailAccount.getId());
                        mMailAttached.setMailId(mailId);
                        mMailAttached.setFileName(fileName);
                        mMailAttached.setFileId(String.valueOf(j));
                        // 获取文件扩展名
                        int lastDotIndex = fileName.lastIndexOf('.');
                        if (lastDotIndex != -1) {
                            if (fileName.substring(lastDotIndex + 1).length() > 4) {
                                mMailAttached.setFileExtension(fileName.substring(fileName.length() - 4));
                            } else {
                                mMailAttached.setFileExtension(fileName.substring(lastDotIndex + 1));
                            }
                        } else {
                            mMailAttached.setFileExtension(".");
                        }

                        mMailAttached.setGmtCreate(LocalDateTime.now());
                        mMailAttacheds.add(mMailAttached);
                    }
                } catch (Exception e) {
                    // 记录错误但继续处理其他部分
                    System.out.println("Error processing attachment: " + e.getMessage());
                }
            }
        }
    }

    /*public void fetchAllMail(MMailAccount mMailAccount, MMail mMail, MailBox mailBox, String foldName, String saveFoldName) throws Exception {
        Store store = mailBox.getStore();
        Folder folder = store.getFolder(foldName);
        folder.open(Folder.READ_ONLY);
        // 转换为 Date
        Date afterDate = Date.from(mMail.getDate().atZone(ZoneId.systemDefault()).toInstant());
        // 使用 ComparisonTerm.GT 表示"大于"，即晚于给定日期
        SearchTerm receivedAfter = new ReceivedDateTerm(ComparisonTerm.GT, afterDate);
        // 搜索邮件
        Message[] messages = folder.search(receivedAfter);
        for (Message message : messages) {
            MimeMessage mimeMessage = (MimeMessage) message;
            // 获取邮件日期
            Date date = mimeMessage.getReceivedDate();
            // 将 Date 转换为 LocalDateTime
            LocalDateTime mailDate = date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            LocalDateTime targetDateExclusive = mMail.getDate();
            if (!mailDate.isAfter(targetDateExclusive)) {
                continue;
            }
            // 获取邮件id
            String mailId = "";
            if (mimeMessage.getMessageID() != null && !mimeMessage.getMessageID().isEmpty()) {
                mailId = mimeMessage.getMessageID().replaceAll("<", "").replaceAll(">", "");
            }
            FetchMailDto fetchMailDto = new FetchMailDto();
            List<MMailAttached> mMailAttacheds = new ArrayList<>();
            fetchMailDto.setMailAttachList(mMailAttacheds);
            // 获取邮件的主题
            String subject = mimeMessage.getSubject();
            // 获取邮件内容
            Object mailContent = mimeMessage.getContent();
            // 获取邮件正文内容和附件
            String content = "";
            if (mailContent instanceof String) {
                content = (String) mailContent;
            } else if (mailContent instanceof Multipart) {
                MimeMultipart multipart = (MimeMultipart) mailContent;
                processAttachments(multipart, mMailAttacheds, mMailAccount, mailId);
                int partCount = multipart.getCount();
                for (int j = 0; j < partCount; j++) {
                    BodyPart bodyPart = multipart.getBodyPart(j);
                    if (bodyPart.isMimeType("text/html")) {
                        try {
                            content = java.net.URLDecoder.decode(String.valueOf(bodyPart.getContent()), "utf-8");
                        } catch (Exception e) {
                            System.out.println("解析附件中的内容出错" + e.getMessage());
                        }
                    } else if (bodyPart.isMimeType("multipart/*")) {
                        // 处理嵌套的多部分内容
                        content = processNestedMultipart((MimeMultipart) bodyPart.getContent());
                    }
                }
            }
            // 获取发件人
            String from = ((InternetAddress) message.getFrom()[0]).getAddress();
            // 获取收件人
            StringBuilder toName = new StringBuilder();
            // 获取收件人信息
            Address[] recipients = message.getAllRecipients();
            if (recipients != null) {
                for (Address recipient : recipients) {
                    toName.append(((InternetAddress) recipient).getAddress()).append(",");
                }
            }
            // 如果 toName 不为空，则删除最后一个逗号
            if (toName.length() > 0) {
                toName.setLength(toName.length() - 1); // 删除最后一个字符
            }
            // 获取抄送人
            StringBuilder ccName = new StringBuilder();
            Address[] ccAddresses = message.getRecipients(Message.RecipientType.CC);
            if (ccAddresses != null && ccAddresses.length > 0) {
                System.out.println("CC recipients for message with subject: " + message.getSubject());
                for (Address address : ccAddresses) {
                    ccName.append(((InternetAddress) address).getAddress()).append(",");
                }
            }
            // 如果 ccName 不为空，则删除最后一个逗号
            if (ccName.length() > 0) {
                ccName.setLength(ccName.length() - 1); // 删除最后一个字符
            }
            boolean isRead = mimeMessage.isSet(Flags.Flag.SEEN);
            boolean isStar = mimeMessage.isSet(Flags.Flag.FLAGGED);

            MMail mail = new MMail();
            mail.setFkPlatformCode(mMailAccount.getFkPlatformCode());
            mail.setFkPlatformUserId(mMailAccount.getFkPlatformUserId());
            mail.setFkMailAccountId(mMailAccount.getId());
            mail.setMailId(mailId);
            mail.setFoldBox(saveFoldName);
            mail.setSubject(subject);
            mail.setBody(content);
            mail.setFromMail(from);
            mail.setToMail(toName.toString());
            mail.setCcMail(ccName.toString());
            mail.setSeparately(false);

            // 将 Date 转换为 LocalDateTime
            LocalDateTime localDateTime = date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            mail.setDate(localDateTime);
            mail.setLlmMailType(0);
            mail.setIsRead(isRead);
            mail.setStar(isStar);
            mail.setGmtCreate(LocalDateTime.now());
            try {
                QueryWrapper<MMail> mMailQueryWrapper2 = new QueryWrapper<>();
                mMailQueryWrapper2.eq("mail_id", mail.getMailId());
                mMailQueryWrapper2.eq("fk_mail_account_id", mMailAccount.getId());
                List<MMail> mailList = mailMapper.selectList(mMailQueryWrapper2);
                if (!mailList.isEmpty()) {
                    continue;
                }
                mailMapper.insert(mail);
                for (MMailAttached mailAttached : mMailAttacheds) {
                    mailAttached.setFkMailId(mail.getId());
                    attachedMapper.insert(mailAttached);
                }
            } catch (Exception e) {
                System.out.println("手动获取邮件失败：" + e.getMessage());
            }
        }
    }*/

    private static String processNestedMultipart(MimeMultipart multipart) throws Exception {
        int partCount = multipart.getCount();
        String content = "";
        for (int i = 0; i < partCount; i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            if (bodyPart.isMimeType("text/html")) {
                try {
                    content = java.net.URLDecoder.decode(String.valueOf(bodyPart.getContent()), "utf-8");
                } catch (Exception e) {
                    System.out.println("解析html出现问题" + e.getMessage());
                }

            }
        }
        return content;
    }
}
