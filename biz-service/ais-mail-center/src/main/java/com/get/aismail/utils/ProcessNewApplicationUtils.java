package com.get.aismail.utils;

import com.get.aismail.dto.*;
import com.get.aismail.vo.AddNewStudentVo;
import lombok.experimental.UtilityClass;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.util.ArrayList;
import java.util.List;

@UtilityClass
public class ProcessNewApplicationUtils {
    // 计算两个字符串的最长公共子序列长度
    public static int lcsLength(String s1, String s2) {
        int m = s1.length();
        int n = s2.length();
        int[][] dp = new int[m + 1][n + 1];
        for (int i = 1; i <= m; i++) {
            for (int j = 1; j <= n; j++) {
                if (s1.toLowerCase().charAt(i - 1) == s2.toLowerCase().charAt(j - 1)) {
                    dp[i][j] = dp[i - 1][j - 1] + 1;
                } else {
                    dp[i][j] = Math.max(dp[i - 1][j], dp[i][j - 1]);
                }
            }
        }
        return dp[m][n];
    }

    public static List<XWPFTableCell> findBestMatchCells(String targetStr, XWPFTable table) {
        List<XWPFTableCell> bestMatchCells = new ArrayList<>();
        List<XWPFTableCell> bestMatchCells1 = new ArrayList<>();
        String bestStr = "";
        int maxLength = targetStr.length() / 2 - 1;
        for (int i = 0; i < table.getRows().size(); i++) {
            List<XWPFTableCell> row = table.getRow(i).getTableCells();
            for (int j = 0; j < row.size(); j++) {
                String str = row.get(j).getText();
                if (targetStr.equals(str)) {
                    bestStr = row.get(j).getText();
                    break;
                }
                // 判断最符合的单元格
                int currentLength = lcsLength(targetStr, str);
                if (currentLength > maxLength) {
                    maxLength = currentLength;
                    bestStr = row.get(j).getText();
                }

            }
        }
        for (int i = 0; i < table.getRows().size(); i++) {
            List<XWPFTableCell> row = table.getRow(i).getTableCells();
            for (int j = 0; j < row.size(); j++) {
                String str = row.get(j).getText();
                if (bestStr.equals(str)) {
                    if (j == row.size() - 1 && i + 1 < table.getRows().size()) {
                        bestMatchCells.add(table.getRow(i + 1).getCell(0));
                    } else {
                        bestMatchCells.add(row.get(j + 1));
                    }
                }
            }
        }
        // 如果是一行行填写的单独处理，根据关键词的出现次数，如果出现一次则是一行行填写的
        if (bestMatchCells.size() == 1) {
            for (int i = 0; i < table.getRows().size(); i++) {
                boolean check = false;
                List<XWPFTableCell> row = table.getRow(i).getTableCells();
                for (int j = 0; j < row.size(); j++) {
                    String str = row.get(j).getText();
                    if (bestStr.equals(str)) {
                        check = true;
                        for (int k = i + 1; k < table.getRows().size(); k++) {
                            if (table.getRow(k).getCell(j).getText().isEmpty()) {
                                break;
                            }
                            bestMatchCells1.add(table.getRow(k).getCell(j));
                        }
                    }
                }
                if (check) {
                    break;
                }
            }
            return bestMatchCells1;
        }
        return bestMatchCells;
    }

    // 找到目标字符串在表格中最匹配的项
    public static Element findBestMatchCellIssue(String targetStr, Elements tables) {
        // 创建一个新的Word文档
        XWPFDocument document = new XWPFDocument();
        // 创建一个空的表格
        XWPFTable bestTable = document.createTable();
        // 创建一个空的单元格
        Element bestCell = new Element("td");
        for (int i = 0; i < tables.size(); i++) {
            Elements tds = tables.get(i).select("td");
            boolean checkFind = false;
            for (int j = 0; j < tds.size(); j++) {
                String str = tds.get(j).text();
                if (str.replaceAll("\n", "").replaceAll(" ", "").equals(targetStr)) {
                    // 如果不是当前行的最后一列，则选择同一行的下一个单元格作为bestCell
                    if (j < tables.size() - 1) {
                        bestCell = tds.get(j + 1);
                    }
                    // 如果是当前行的最后一列但不是表格的最后一行，则选择下一行的第一个单元格作为bestCell
                    else if (i < tables.size() - 1) {
                        bestCell = tables.get(i + 1).select("td").get(0);
                    }
                    checkFind = true;
                    break;
                }
            }
            if (checkFind) {
                break;
            }
        }


        /*for (int i = 0; i < table.getRows().size(); i++) {
            List<XWPFTableCell> row = table.getRow(i).getTableCells();
            boolean checkFind = false;
            for (int j = 0; j < row.size(); j++) {
                String str = row.get(j).getText();
                if (str.replaceAll("\n", "").replaceAll(" ", "").equals(targetStr)) {
                    // 如果不是当前行的最后一列，则选择同一行的下一个单元格作为bestCell
                    if (j < row.size() - 1) {
                        bestCell = row.get(j + 1);
                    }
                    // 如果是当前行的最后一列但不是表格的最后一行，则选择下一行的第一个单元格作为bestCell
                    else if (i < table.getRows().size() - 1) {
                        bestCell = table.getRow(i + 1).getCell(0);
                    }
                    checkFind = true;
                    break;
                }
            }
            if (checkFind) {
                break;
            }
        }*/
        return bestCell;
    }

    // 找到目标字符串在表格中最匹配的项
    public static List<Element> findBestMatchCellIssueRow(String targetStr, Elements tables) {
        List<Element> bestMatchCells = new ArrayList<>();
        for (int i = 0; i < tables.size(); i++) {
            Elements row = tables.get(i).select("td");
            for (int j = 0; j < row.size(); j++) {
                String str = row.get(j).text();
                if (str.replaceAll("\n", "").replaceAll(" ", "").equals(targetStr)) {
                    bestMatchCells.add(tables.get(i + 1).select("td").get(j));
                }
            }
        }
        return bestMatchCells;
    }


    // 找到目标字符串在表格中最匹配的项
    public static XWPFTableCell findBestMatchCell(String targetStr, XWPFTable table) {
        // 创建一个新的Word文档
        XWPFDocument document = new XWPFDocument();
        // 创建一个空的表格
        XWPFTable bestTable = document.createTable();
        // 创建一个空的单元格
        XWPFTableCell bestCell = bestTable.createRow().createCell();

        double maxLength = 0;
        for (int i = 0; i < table.getRows().size(); i++) {
            List<XWPFTableCell> row = table.getRow(i).getTableCells();
            for (int j = 0; j < row.size(); j++) {
                String str = row.get(j).getText();
                if (targetStr.equals(str)) {
                    if (j == row.size() - 1 && i + 1 < table.getRows().size()) {
                        bestCell = table.getRow(i + 1).getCell(0);
                    } else {
                        bestCell = row.get(j + 1);
                    }
                    return bestCell;
                }
                // 判断最符合的单元格
                int currentLength = lcsLength(targetStr, str);
                double matchPercentage = currentLength * 1.0 / targetStr.length();
                if (matchPercentage > maxLength) {
                    maxLength = matchPercentage;
                    // 如果不是当前行的最后一列，则选择同一行的下一个单元格作为bestCell
                    if (j < row.size() - 1) {
                        bestCell = row.get(j + 1);
                    }
                    // 如果是当前行的最后一列但不是表格的最后一行，则选择下一行的第一个单元格作为bestCell
                    else if (i < table.getRows().size() - 1) {
                        bestCell = table.getRow(i + 1).getCell(0);
                    }
                }
            }
        }
        return bestCell;
    }

    // 找到目标字符串在 word 中最匹配的表格
    public static XWPFTable findBestMatchTable(String targetStr, List<XWPFTable> tables) {
        // 创建一个新的Word文档
        XWPFDocument document = new XWPFDocument();
        // 创建一个空的表格
        XWPFTable bestTable = document.createTable();

        double maxLength = 0.5;
        for (int i = 0; i < tables.size(); i++) {
            XWPFTable table = tables.get(i);
            String str = table.getRow(0).getCell(0).getText();
            if (targetStr.equals(str)) {
                bestTable = table;
                return bestTable;
            }
            // 判断最符合的单元格
            int currentLength = lcsLength(targetStr, str);
            double matchPercentage = currentLength * 1.0 / str.replaceAll("[A-Za-z]", "").length();
            if (matchPercentage > maxLength) {
                maxLength = matchPercentage;
                bestTable = table;
            }
        }
        return bestTable;
    }
}
