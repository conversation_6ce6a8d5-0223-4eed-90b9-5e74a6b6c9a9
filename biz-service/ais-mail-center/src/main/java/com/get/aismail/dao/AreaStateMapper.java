package com.get.aismail.dao;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.aismail.entity.AreaState;
import org.apache.ibatis.annotations.Mapper;
@DS("institution")
@Mapper
public interface AreaStateMapper extends BaseMapper<AreaState> {
    /**
     * @return java.lang.String
     * @Description :通过州省id 查找对应的州省全称
     * @Param [id]
     * <AUTHOR>
     */
    String getStateFullNameById(Long id);
}
