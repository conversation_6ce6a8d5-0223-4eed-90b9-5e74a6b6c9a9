package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.ConventionRegistrationVo;
import com.get.salecenter.entity.ConventionRegistration;
import com.get.salecenter.dto.ConventionRegistrationDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 峰会报名管理mapper
 */
@Mapper
public interface ConventionRegistrationMapper extends BaseMapper<ConventionRegistration>, GetMapper<ConventionRegistration> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insert(ConventionRegistration record);

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionRegistration record);

    /**
     * 根据报名id查找该报名对应的展位名称
     *
     * @param id
     * @return
     */
    String getBoothNameById(@Param("id") Long id);


    /**
     * 所属报名名册下拉框
     *
     * @param conventionId
     * @return
     */
    List<ConventionRegistrationVo> getConventionRegistrationDtoList(@Param("conventionId") Long conventionId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :根据展位名称 模糊查询对应的报名ids
     * @Param [boothName]
     * <AUTHOR>
     */
    List<Long> getRegistrationIdsByName(@Param("boothName") String boothName);

    /**
     * @return java.lang.Boolean
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    Boolean conventionRegistrationIsEmpty(Long id);

    /**
     * @return void
     * @Description: 修改状态
     * @Param [id, status]
     * <AUTHOR>
     */
    void updateStatus(@Param("id") Long id, @Param("status") Integer status);

    /**
     * @return java.lang.Integer
     * @Description: 校验是否重复
     * @Param [conventionId, boothNum]
     * <AUTHOR>
     */
    Integer validateAdd(@Param("conventionId") Long conventionId, @Param("boothNum") String boothNum);

    /**
     * @return
     * @Description : 查询回执码
     * @Param
     * <AUTHOR>
     */
    List<ConventionRegistrationVo> getReceiptCodes(@Param("conventionRegistrationDto") ConventionRegistrationDto conventionRegistrationDto);


    /**
     * @return
     * @Description : 根据回执码查展位信息
     * @Param
     * <AUTHOR>
     */
    List<ConventionRegistrationVo> getBoothsByReceiptCode(@Param("receiptCode") String receiptCode, @Param("conventionId") Long conventionId, @Param("status") Integer status);


    /**
     * @return
     * @Description : 验证提供商名是否被用
     * @Param
     * <AUTHOR>
     */
    List<ConventionRegistrationVo> providerNameVerify(@Param("conventionId") Long conventionId, @Param("providerName") String providerName);

    /**
     * @return
     * @Description : 根据回执码和状态查询报名信息
     * @Param
     * <AUTHOR>
     */
    List<ConventionRegistration> getSumRegistrationFreeByReceiptCode(@Param("receiptCode") String receiptCode, @Param("status") Integer status);

    /**
     * @return java.util.List<com.get.salecenter.vo.BoothVo>
     * @Description :根据回执码查找展位
     * @Param [receiptCode]
     * <AUTHOR>
     */
    List<ConventionRegistrationVo> getBoothListByReceiptCode(@Param("receiptCode") String receiptCode, @Param("conventionId") Long fkConventionId);

    /**
     * @return java.util.List<com.get.salecenter.vo.BoothVo>
     * @Description :根据回执码查找总费用
     * @Param [receiptCode]
     * <AUTHOR>
     */
    List<ConventionRegistration> getSumRegistrationFreeListByReceiptCode(@Param("receiptCode") String receiptCode);

    /**
     * @return java.lang.Long
     * @Description :根据回执码查找峰会id
     * @Param [receiptCode]
     * <AUTHOR>
     */
    Long getConventionIdByReceiptCode(@Param("receiptCode") String receiptCode);

    /**
     * @return java.lang.Long
     * @Description :根据回执码查找机构名称
     * @Param [receiptCode]
     * <AUTHOR>
     */
    List<ConventionRegistrationVo> getInstitutionNameSelect(@Param("receiptCode") String receiptCode, @Param("fkConventionId") Long fkConventionId);

    /**
     * @return java.lang.Long
     * @Description :根据回执码查找报名名册
     * @Param [receiptCode]
     * <AUTHOR>
     */
    List<ConventionRegistration> getConventionRegistrationByReceiptCode(@Param("receiptCode") String receiptCode);

    /**
     * @return java.lang.Long
     * @Description :根据conventionId查找回执码
     * @Param [conventionId]
     * <AUTHOR>
     */
    List<String> getReceiptCodesByConventionId(@Param("conventionId") Long conventionId);

    /**
     * 获取报名名册
     *
     * @param conventionId
     * @return
     */
    List<ConventionRegistrationVo> getConventionRegistrationsByConventionId(@Param("conventionId") Long conventionId);

    /**
     * 获取报名的人数
     * @param placeName
     * @return
     */
    int getPlaceCount(@Param("placeName")String placeName,@Param("conventionId") Long conventionId);

    /**
     * 根据提供商名返回报名名册id
     *
     * @param providerName
     * @param fkConventionId
     * @return
     */
    List<Long> getConventionRegistrationIdByProviderName(@Param("providerName") String providerName, @Param("fkConventionId") Long fkConventionId);

}