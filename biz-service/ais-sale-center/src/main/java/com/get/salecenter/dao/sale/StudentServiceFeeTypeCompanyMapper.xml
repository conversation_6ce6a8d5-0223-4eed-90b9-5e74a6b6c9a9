<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.salecenter.dao.sale.StudentServiceFeeTypeCompanyMapper">


    <select id="getServiceFeeTypeCompanyInfo" resultType="com.get.salecenter.vo.SelItem">
        SELECT
        r.fk_student_service_fee_type_id AS keyId,
        GROUP_CONCAT(m.short_name ORDER BY m.id) AS val
        FROM
        ais_permission_center.m_company m
        INNER JOIN r_student_service_fee_type_company r ON r.fk_company_id = m.id
        WHERE
        r.fk_student_service_fee_type_id in
        <foreach collection="ids" item="cid" open="(" separator="," close=")">
            #{cid}
        </foreach>
        and
        r.fk_company_id  in
        <foreach item="id" index="index" collection="companyIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY
        r.fk_student_service_fee_type_id
    </select>
    <select id="getServiceFeeTypeCompanyInfoById" resultType="java.lang.String">
        SELECT
            GROUP_CONCAT(m.short_name ORDER BY m.id)
        FROM
        ais_permission_center.m_company m
        INNER JOIN r_student_service_fee_type_company r ON r.fk_company_id = m.id
        WHERE
            1=1
        <if test="id!=null and id!=''">
            AND r.fk_student_service_fee_type_id = #{id}
        </if>
        GROUP BY
        r.fk_student_service_fee_type_id
    </select>
    <select id="getServiceFeeTypeCompanyId" resultType="java.lang.Long">
        SELECT
            m.id
        FROM
        ais_permission_center.m_company m
        INNER JOIN r_student_service_fee_type_company r ON r.fk_company_id = m.id
        WHERE
             r.fk_student_service_fee_type_id = #{id}
    </select>
</mapper>
