package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.EventSummaryListVo;
import com.get.salecenter.entity.EventSummary;
import com.get.salecenter.dto.EventSummaryListDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface EventSummaryMapper extends BaseMapper<EventSummary>, GetMapper<EventSummary> {
    @Override
    int insert(EventSummary record);

    int insertSelective(EventSummary record);

    /**
     * @Description: 获取最大排序值
     * @Author: Jerry
     * @Date:14:14 2021/9/3
     */
    Integer getMaxViewOrder();

    /**
     * 查询列表
     *
     * @param iPage
     * @param eventSummaryListDto
     * @return
     */
    List<EventSummaryListVo> getEventSummaries(IPage<EventSummaryListVo> iPage, @Param("eventSummaryListDto") EventSummaryListDto eventSummaryListDto);
}