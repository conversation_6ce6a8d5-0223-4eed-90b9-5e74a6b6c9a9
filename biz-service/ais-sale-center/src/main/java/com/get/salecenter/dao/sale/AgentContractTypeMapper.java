package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AgentContractType;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface AgentContractTypeMapper extends BaseMapper<AgentContractType> {

    int insertSelective(AgentContractType record);

    /**
     * @return java.lang.Integer
     * @Description: 查找最大排序序号
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

}