package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.AgentVo;
import com.get.salecenter.vo.ApprovalItemVo;
import com.get.salecenter.vo.ClientListInfoVo;
import com.get.salecenter.vo.SameStudentClientVo;
import com.get.salecenter.entity.Client;
import com.get.salecenter.dto.ClientDto;
import com.get.salecenter.dto.SameStudentClientDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author:Neil
 * Time: 12:57
 * Date: 2022/8/17
 * Description:
 */
@Mapper
public interface ClientMapper extends GetMapper<Client> {
    /**
     * 获取客户最大编号
     * @return
     */
    Long getMaxId();

    List<ClientListInfoVo> getResourceList(IPage<Client> page, @Param("clientDto") ClientDto clientDto, @Param("followerIds") List<Long> followerIds,
                                           @Param("export")Boolean export, @Param("hide") Boolean hide);

    List<AgentVo> getClientAgents(@Param("fkClientIds")List<Long> ids);

    ApprovalItemVo selectClientStudentItem(@Param("id")Long id);

    List<SameStudentClientVo> getSameStudentClients(@Param("sameStudentClientDto") SameStudentClientDto sameStudentClientDto);
}
