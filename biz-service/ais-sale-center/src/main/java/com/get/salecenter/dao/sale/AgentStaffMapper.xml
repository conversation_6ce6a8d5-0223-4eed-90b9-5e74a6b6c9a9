<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentStaffMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.AgentStaff">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_agent_id" jdbcType="BIGINT" property="fkAgentId"/>
        <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId"/>
        <result column="is_active" jdbcType="BIT" property="isActive"/>
        <result column="active_date" jdbcType="TIMESTAMP" property="activeDate"/>
        <result column="unactive_date" jdbcType="TIMESTAMP" property="unactiveDate"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <insert id="insert" parameterType="com.get.salecenter.entity.AgentStaff" useGeneratedKeys="true" keyProperty="id">
    insert into r_agent_staff (id, fk_agent_id, fk_staff_id,
      is_active, active_date,
      unactive_date, gmt_create, gmt_create_user,
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkAgentId,jdbcType=BIGINT}, #{fkStaffId,jdbcType=BIGINT},
      #{isActive,jdbcType=BIT}, #{activeDate,jdbcType=TIMESTAMP},
      #{unactiveDate,jdbcType=TIMESTAMP}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR},
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.get.salecenter.entity.AgentStaff" useGeneratedKeys="true"
            keyProperty="id">
        insert into r_agent_staff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkAgentId != null">
                fk_agent_id,
            </if>
            <if test="fkStaffId != null">
                fk_staff_id,
            </if>
            <if test="isActive != null">
                is_active,
            </if>
            <if test="activeDate != null">
                active_date,
            </if>
            <if test="unactiveDate != null">
                unactive_date,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkAgentId != null">
                #{fkAgentId,jdbcType=BIGINT},
            </if>
            <if test="fkStaffId != null">
                #{fkStaffId,jdbcType=BIGINT},
            </if>
            <if test="isActive != null">
                #{isActive,jdbcType=BIT},
            </if>
            <if test="activeDate != null">
                #{activeDate,jdbcType=TIMESTAMP},
            </if>
            <if test="unactiveDate != null">
                #{unactiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getAgentIdsByBdNum" parameterType="java.lang.String" resultType="java.lang.Long">
    SELECT
	 fk_agent_id
    FROM
	 r_agent_staff
    WHERE
	 fk_staff_id IN( SELECT fk_staff_id FROM r_staff_bd_code WHERE position(#{bdNum} in bd_code))
    and
     is_active = 1
    </select>
    <select id="getAgentIdsByBdAreaRegionId" resultType="java.lang.Long">
        SELECT
         fk_agent_id
        FROM
         r_agent_staff
        WHERE
         fk_staff_id IN( SELECT fk_staff_id FROM r_staff_bd_code WHERE FIND_IN_SET(#{fkAreaRegionId},fk_area_region_id))
        and
         is_active = 1
    </select>

    <select id="getStaffBdCodeList" resultType="com.get.salecenter.vo.StaffBdCodeVo">
        select
        rsbc.id, rsbc.fk_staff_id, rsbc.bd_code, mc.num AS companyNum
        from
        r_staff_bd_code AS rsbc
        LEFT JOIN ais_permission_center.m_company AS mc ON mc.id = rsbc.fk_company_id
        LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = rsbc.fk_staff_id
        where
        rsbc.fk_staff_id
        IN
        <foreach collection="staffIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="!testBdFlag">
            AND rsbc.bd_code NOT LIKE 'T%'
        </if>
        order by mc.view_order desc, ms.is_on_duty desc
    </select>

    <select id="getAgentSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT DISTINCT a.id id ,concat(if(a.is_active=0,'【无效】',''),
        a.name)  name,
        a.is_active status  FROM m_agent a
        LEFT join r_agent_staff r on r.fk_agent_id=a.id
        <where>
            fk_agent_id in
            <if test="agentIds!=null">
                <foreach collection="agentIds" item="agentId" index="index" open="(" separator="," close=")">
                    #{agentId,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        ORDER BY a.id
    </select>

    <select id="getBDSelect" resultType="com.get.salecenter.vo.StaffBdCodeVo">

        SELECT s.fk_staff_id,b.bd_code from r_agent_staff s
        left join r_staff_bd_code b on s.fk_staff_id=b.fk_staff_id
        where s.fk_agent_id=#{fkAgentId}
        and s.is_active=1
        ORDER BY b.bd_code
    </select>

    <select id="getAgentByStudentId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT s.fk_agent_id id,concat(if(a.is_active=0,'【无效】',''),
                                       a.name)  name,
               a.is_active status ,f.bd_code nameChn from r_student_agent s
        left join  r_agent_staff f on s.fk_agent_id=f.fk_agent_id
        left join m_agent a on s.fk_agent_id=a.id
        where  s.is_active=1
        and f.is_active=1
        and s.fk_student_id=#{studentId}
    </select>

    <select id="getAgentList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT DISTINCT a.id id ,concat(if(a.is_active=0,'【无效】',''),CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）"))))  name,
        a.is_active status
        FROM m_agent a
        LEFT JOIN r_agent_staff r ON r.fk_agent_id=a.id
        LEFT JOIN r_student_agent s ON s.fk_agent_id=a.id
        WHERE
        s.fk_student_id=#{studentId}
        <if test="visibleCountryIds != null and visibleCountryIds.size() > 0" >
            and a.fk_area_country_id IN
            <foreach collection="visibleCountryIds" item="visibleCountryId" open="(" separator="," close=")">
                #{visibleCountryId}
            </foreach>
        </if>
    </select>

    <select id="getBdCount" resultType="com.get.salecenter.entity.Agent">
        SELECT
        DISTINCT a.* FROM r_agent_staff as ags
        LEFT JOIN m_agent as a on a.id = ags.fk_agent_id
        where ags.fk_staff_id = #{staffId} and ags.is_active = 1
    </select>
    <select id="selectAgentStaffBdInfo" resultType="com.get.salecenter.vo.StaffBdCodeVo">
        SELECT
            s.*,sbc.bd_code AS bdCode
        FROM
            r_agent_staff AS s
        INNER JOIN r_staff_bd_code AS sbc ON sbc.fk_staff_id = s.fk_staff_id
        WHERE s.is_active = 1 AND s.fk_agent_id = #{agentId}
    </select>

    <select id="getStaffByAgent" resultType="java.lang.Long">
        SELECT fk_staff_id
        FROM r_agent_staff
        where fk_agent_id = #{id} and is_active = 1 limit 1
    </select>

    <select id="getStaffsByAgentsMap" resultType="java.util.Map">
        SELECT fk_agent_id,fk_staff_id
        FROM r_agent_staff
        where is_active = 1 and  fk_agent_id in
        <if test="ids!=null">
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>
    <select id="getStaffsByAgents" resultType="java.lang.Long">
        SELECT fk_staff_id
        FROM r_agent_staff
        where is_active = 1
        <if test="ids!=null">
            and  fk_agent_id in
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="getBdNameByAgentIds" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        select r.fk_agent_id id,group_concat(if(s.is_on_duty=1,'','【离职】'),s.name,IFNULL(concat('(',s.name_en,')'),'')) name
        from r_agent_staff r
        LEFT JOIN ais_permission_center.m_staff s on r.fk_staff_id = s.id
        where r.is_active = 1
        <if test="ids!=null">
            and r.fk_agent_id in
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
        group by r.fk_agent_id
    </select>

    <select id="getAreaRegionIdByAgentIds" resultType="com.get.salecenter.vo.StaffBdCodeVo">
        SELECT
            a.fk_agent_id AS fkAgentId, b.fk_area_region_id AS fkAreaRegionId
        FROM r_agent_staff AS a
        LEFT JOIN r_staff_bd_code AS b ON a.fk_staff_id = b.fk_staff_id
        WHERE a.is_active = 1
        <if test="agentIds != null">
            AND a.fk_agent_id in
            <foreach collection="agentIds" item="id" index="index" open="(" separator="," close=")">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

</mapper>