package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.StudentServiceFeeTypeVo;
import com.get.salecenter.entity.StudentServiceFeeType;
import com.get.salecenter.dto.StudentServiceFeeTypeDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * <AUTHOR>
 */
@Mapper
public interface ServiceTypeManagementMapper extends GetMapper<StudentServiceFeeType> {


    /**
     * 获取列表数据
     * @param iPage
     * @param studentServiceFeeTypeDto
     * @return
     */
    List<StudentServiceFeeTypeVo> datas(@Param("iPage") IPage<StudentServiceFeeTypeVo> iPage
            , @Param("studentServiceFeeTypeDto") StudentServiceFeeTypeDto studentServiceFeeTypeDto);


    /**
     * 获取最大orderview值
     * @return
     */
    Integer getMaxOrder();


    /**
     * 类型下拉
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getServiceTypeList(Long companyId);
}
