<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.PayablePlanContractFormulaMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.PayablePlanContractFormula">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_payable_plan_id" jdbcType="BIGINT" property="fkPayablePlanId" />
    <result column="fk_contract_formula_id" jdbcType="BIGINT" property="fkContractFormulaId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.salecenter.entity.PayablePlanContractFormula">
    insert into r_payable_plan_contract_formula (id, fk_payable_plan_id, fk_contract_formula_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkPayablePlanId,jdbcType=BIGINT}, #{fkContractFormulaId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.PayablePlanContractFormula">
    insert into r_payable_plan_contract_formula
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkPayablePlanId != null">
        fk_payable_plan_id,
      </if>
      <if test="fkContractFormulaId != null">
        fk_contract_formula_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkPayablePlanId != null">
        #{fkPayablePlanId,jdbcType=BIGINT},
      </if>
      <if test="fkContractFormulaId != null">
        #{fkContractFormulaId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="isExistByFormulaId" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id from r_payable_plan_contract_formula where fk_contract_formula_id=#{id}
  </select>
</mapper>