<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ClientOfferStepMapper">

    <select id="getMaxStepOrder" resultType="java.lang.Integer">
        select
            IFNULL(max(step_order)+1,0) step_order
        from
            u_client_offer_step
    </select>
</mapper>