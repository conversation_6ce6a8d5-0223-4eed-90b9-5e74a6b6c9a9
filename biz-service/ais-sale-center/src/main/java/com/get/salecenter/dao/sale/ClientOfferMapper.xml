<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ClientOfferMapper">

    <select id="isExistByClientId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_client_offer where fk_client_id=#{clientId}
    </select>

    <select id="getClientOfferList" resultType="com.get.salecenter.vo.ClientOfferVo">
        SELECT
        a.*,
        b.step_name
        FROM
        m_client_offer a

        INNER JOIN (
        SELECT DISTINCT a.id FROM (
        <!-- 项目成员的权限(咨询客户) -->
        SELECT a.id FROM ais_sale_center.m_client_offer a
        INNER JOIN ais_sale_center.s_student_project_role_staff c
        ON a.id=c.fk_table_id AND c.fk_table_name='m_client_offer' AND c.is_active=1
        <if test="staffFollowerIds != null and staffFollowerIds.size()>0">
            AND c.fk_staff_id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
        </if>
        UNION ALL
        -- #BD的权限
        SELECT a.id FROM ais_sale_center.m_client_offer a
        INNER JOIN ais_sale_center.r_client_agent b ON a.fk_client_id=b.fk_client_id AND b.is_active = 1
        INNER JOIN ais_sale_center.r_agent_staff c ON b.fk_agent_id=c.fk_agent_id AND c.is_active = 1 AND c.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        WHERE 1=1
        UNION ALL
        -- #项目成员的权限
        SELECT a.id FROM ais_sale_center.m_client_offer a
        INNER JOIN ais_sale_center.s_student_project_role_staff b ON a.id=b.fk_table_id AND b.fk_table_name='m_client_offer' AND b.is_active=1 AND b.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        UNION ALL
        -- #申请方案创建人的权限
        SELECT a.id FROM ais_sale_center.m_client_offer a
        INNER JOIN ais_permission_center.m_staff b ON a.gmt_create_user=b.login_id AND b.id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        WHERE 1=1
        ) a
        ) z ON a.id=z.id
        LEFT JOIN u_client_offer_step b on a.fk_client_offer_step_id = b.id
        WHERE 1=1
        <if test="clientOfferDto.fkClientId!=null and clientOfferDto.fkClientId !=''">
            and a.fk_client_id=#{clientOfferDto.fkClientId}
        </if>
    </select>

    <select id="getClientOfferSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
        id,
        num NAME
        FROM
        `m_client_offer`
        WHERE
        fk_client_id = #{fkClientId} AND status = 1
    </select>
    <select id="getClientAgentSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT DISTINCT
            a.id,
            a.`name`,
            CONCAT(a.`name`,'(',a.num,')') as fullName,
            r.is_active as status
        FROM
            m_agent a
        INNER JOIN r_client_agent r ON r.fk_agent_id = a.id
        WHERE
            r.fk_client_id = #{fkClientId}
            AND r.is_active = 1
    </select>
    <select id="getClientOfferInfoById" resultType="com.get.salecenter.vo.ClientOfferVo">
        SELECT
            f.*, c.`name`,
            c.first_name,
            c.last_name
        FROM
            m_client_offer f
        LEFT JOIN m_client c ON c.id = f.fk_client_id
        WHERE
            f.id = #{id}
    </select>
    <select id="isExistByStepId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_client_offer where fk_client_offer_step_id=#{id}
    </select>
</mapper>