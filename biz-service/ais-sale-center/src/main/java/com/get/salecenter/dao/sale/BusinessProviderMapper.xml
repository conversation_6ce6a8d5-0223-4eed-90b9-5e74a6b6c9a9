<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.BusinessProviderMapper">

  <select id="getDatas" resultType="com.get.salecenter.vo.BusinessProviderVo">
     select mbp.*,mbpt.type_name as fkBusinessProviderTypeName  from m_business_provider mbp
     left join u_business_provider_type mbpt on mbp.fk_business_provider_type_id = mbpt.id
      left join r_business_provider_company r on r.fk_business_provider_id = mbp.id
      where 1 = 1 and (fk_department_ids is null or fk_department_ids = ''
      <foreach collection="fkDepartIds" item="item">
          or FIND_IN_SET(#{item},fk_department_ids)
      </foreach>
      )
    <if test="businessProviderDto.fkCompanyId!=null ">
      and r.fk_company_id = #{businessProviderDto.fkCompanyId}
    </if>
    <if test="businessProviderDto.fkTypeKey!=null and businessProviderDto.fkTypeKey!=''">
      and find_in_set(#{businessProviderDto.fkTypeKey},mbp.fk_type_key) >0
    </if>
    <if test="businessProviderDto.isActive!=null">
      and mbp.is_active = #{businessProviderDto.isActive}
    </if>
      <if test="businessProviderDto.fkBusinessProviderTypeId!=null">
          and mbp.fk_business_provider_type_id = #{businessProviderDto.fkBusinessProviderTypeId}
      </if>
    <if test="businessProviderDto.keyWord!=null  and businessProviderDto.keyWord!=''">
      and (mbp.name LIKE CONCAT('%', #{businessProviderDto.keyWord}, '%') OR mbp.name_chn LIKE CONCAT('%', #{businessProviderDto.keyWord}, '%') OR mbp.num LIKE CONCAT('%',
      #{businessProviderDto.keyWord}, '%'))
    </if>
    ORDER BY mbp.gmt_create desc
  </select>

  <select id="getNamesByIds" resultType="com.get.salecenter.vo.BusinessProviderVo">
    select
    b.id,
      (CASE
          WHEN IFNULL(name_chn,'') = ''
            THEN name
          ELSE CONCAT(name, '（', name_chn, '）')
      END) AS name
    from
    m_business_provider b
    where
    b.id in
    <foreach collection="ids" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="providerSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT m.id,concat(if(is_active=0,'【无效】',''),
    CASE
    WHEN IFNULL(name_chn,'') = ''
    THEN
    name ELSE CONCAT(name, '（', name_chn, '）' )
    END)  name,
    is_active status from m_business_provider m
    inner join r_business_provider_company r  on r.fk_business_provider_id=m.id
    WHERE 1=1 and (fk_department_ids is null or fk_department_ids = ''
      <foreach collection="fkDepartIds" item="item" >
          or FIND_IN_SET(#{item},fk_department_ids)
      </foreach>
        )
    <if test="tableName!=null">
      and FIND_IN_SET(#{tableName}, fk_type_key) >0
    </if>
    <if test="companyId!=null">
        AND r.fk_company_id =#{companyId}
    </if>
  </select>
  <select id="getBusinessProviderIdByTargetName" resultType="java.lang.Long">
    SELECT
        id
    FROM
        m_business_provider
    WHERE
        (
           name LIKE CONCAT('%',#{targetName},'%') OR name_chn LIKE CONCAT('%',#{targetName},'%')
        ) AND is_active = 1
  </select>
  <select id="providerSelectByCompanyId" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT m.id,concat(if(is_active=0,'【无效】',''),
    CASE
    WHEN IFNULL(name_chn,'') = ''
    THEN
    name ELSE CONCAT(name, '（', name_chn, '）' )
    END)  name,
    is_active status from m_business_provider m
    inner join r_business_provider_company r on r.fk_business_provider_id=m.id
    where r.fk_company_id = #{companyId}
  </select>

  <select id="getInvoicePlanId" resultType="java.lang.Long">
        SELECT
            a.id
        FROM
            m_receivable_plan a
                INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = a.id
                INNER JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
                INNER JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id and d.fk_type_key = 'm_business_provider_acc'
        WHERE
            d.fk_type_target_id = #{targetId}
          AND d.id = #{receiptFormId}
  </select>
    <select id="getBusinessProviderInfoByPlanIds" resultType="com.get.salecenter.vo.BusinessProviderVo">
    SELECT
		p.id as plan_id,
		mbp.num,
		mbp. NAME,
		mbp.name_chn
	FROM
		m_business_provider mbp
	INNER JOIN m_student_accommodation msa ON msa.fk_business_provider_id = mbp.id
	INNER JOIN m_receivable_plan p ON msa.id = p.fk_type_target_id AND p.fk_type_key = 'm_student_accommodation'
	<if test="ids!=null and ids.size>0">
        where p.id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </if>
    </select>
    <select id="getProviderById" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            p.id,
            concat(

                IF (
                    is_active = 0,
                    '【无效】',
                    ''
                ),
                CASE
            WHEN IFNULL(p.name_chn, '') = '' THEN
                NAME
            ELSE
                CONCAT(
                    p. NAME,
                    '（',
                    p.name_chn,
                    '）'
                )
            END
            ) NAME,
            p.is_active STATUS
        FROM
            m_business_provider p
        INNER JOIN m_student_accommodation c ON c.fk_business_provider_id = p.id
        WHERE
        1 = 1 and (p.fk_department_ids is null or p.fk_department_ids = ''
        <foreach collection="fkDepartIds" item="item" >
            or FIND_IN_SET(#{item},fk_department_ids)
        </foreach>
        )
        AND c.id = #{id}
    </select>
    <select id="getBusinessProvider" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            m.id,
            `name`,
            name_chn
        FROM
            m_business_provider m
        inner join r_business_provider_company r on r.fk_business_provider_id=m.id
        WHERE
            1=1
            <if test="companyIds!=null and companyIds.size>0">
                AND r.fk_company_id IN
                <foreach collection="companyIds" item="cid" open="(" close=")" separator=",">
                    #{cid}
                </foreach>
            </if>
            <if test="keyWord!=null and keyWord!=''">
                AND (`name` LIKE CONCAT('%',#{keyWord},'%') OR name_chn LIKE CONCAT('%',#{keyWord},'%'))
            </if>

    </select>
    <select id="getBusinessProviderByTargetName" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            id,
            `name`,
            name_chn
        FROM
            m_business_provider
        WHERE
        is_active = 1
        AND  (
           name LIKE CONCAT('%',#{targetName},'%') OR name_chn LIKE CONCAT('%',#{targetName},'%')
        )
        LIMIT 20
    </select>
    <select id="getBusinessProviderCompanyInfoById" resultType="com.get.salecenter.vo.SelItem">
        SELECT
            c.id as keyId,
            c.short_name as val
        FROM
            m_business_provider b
            INNER JOIN r_business_provider_company rbpc ON b.id  = rbpc.fk_business_provider_id
            LEFT JOIN ais_permission_center.m_company c ON c.id = rbpc.fk_company_id
            WHERE b.id = #{id}
        GROUP BY b.id
    </select>
    <select id="getClientSourceProviderSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT m.id,concat(if(is_active=0,'【无效】',''),
        CASE
        WHEN IFNULL(name_chn,'') = ''
        THEN
        name ELSE CONCAT(name, '（', name_chn, '）' )
        END)  name,
        is_active status from m_business_provider m
        inner join r_business_provider_company r on r.fk_business_provider_id=m.id
        WHERE 1=1
        <if test="tableName!=null and tableName!=''">
            AND FIND_IN_SET(#{tableName}, fk_type_key) >0
        </if>
        <if test="tableName ==null or tableName == ''">
            AND FIND_IN_SET('client_source', fk_type_key) > 0
        </if>
        <if test="companyId!=null">
            AND r.fk_company_id = #{companyId}
        </if>
        <if test="fkDepartIds !=null and fkDepartIds.size()>0">
            AND (
            fk_department_ids is null
            OR fk_department_ids = ""
            <foreach collection="fkDepartIds" item="item" >
                or FIND_IN_SET(#{item},fk_department_ids)
            </foreach>
            )
        </if>

    </select>

    <select id="getBusinessProviderSelect" resultType="com.get.salecenter.vo.BusinessProviderSelectVo">
        SELECT
            m.id,
            (CASE
                WHEN IFNULL(name_chn,'') = ''
                    THEN name
                ELSE CONCAT(name, '（', name_chn, '）')
            END) AS fullName,
            product_info
        FROM m_business_provider m
        inner join r_business_provider_company r on r.fk_business_provider_id=m.id
        WHERE is_active = 1
          AND r.fk_company_id = #{fkCompanyId}
        <if test="fkTypeKey != null and fkTypeKey != ''">
            AND FIND_IN_SET(#{fkTypeKey}, fk_type_key) > 0
        </if>
        <if test="fkDepartIds != null and fkDepartIds.size() > 0">
            AND (
            fk_department_ids IS NULL OR fk_department_ids = ''
            <foreach collection="fkDepartIds" item="item">
                OR FIND_IN_SET(#{item}, fk_department_ids)
            </foreach>
            )
        </if>
    </select>

    <select id="getBusinessProviderSelectByTypeKey" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            m.id,
            CONCAT(
                IF(m.is_active = 0, '【无效】', ''),
                CASE
                    WHEN IFNULL(m.name_chn, '') = '' THEN m.name
                    ELSE CONCAT(m.name, '（', m.name_chn, '）')
                END
            ) name
        FROM m_business_provider m
        INNER JOIN r_business_provider_company r on r.fk_business_provider_id = m.id
        WHERE m.is_active = 1
        <if test="companyId != null">
            AND r.fk_company_id = #{companyId}
        </if>
        <if test="typeKey != null and typeKey != ''">
            AND FIND_IN_SET(#{typeKey}, m.fk_type_key) > 0
        </if>
        ORDER BY m.is_active DESC
    </select>
    <select id="getPlanIdsByBusinessProviderId" resultType="com.get.salecenter.entity.ReceivablePlan">
        SELECT i.*
        FROM
            ais_sale_center.m_receivable_plan i
                INNER JOIN (
                SELECT
                    a.id
                FROM
                    m_receivable_plan a
                        INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = a.id
                        INNER JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
                        INNER JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id and d.fk_type_key = 'm_business_provider_ins'
                WHERE
                    d.fk_type_target_id = #{targetId}
                  AND d.id = #{receiptFormId}
                  and a.status = 1
                  and d.status = 1
            ) a ON a.id = i.id
                INNER JOIN m_student_insurance b ON b.id = i.fk_type_target_id and i.fk_type_key = 'm_student_insurance'
        GROUP BY i.id
    </select>

</mapper>