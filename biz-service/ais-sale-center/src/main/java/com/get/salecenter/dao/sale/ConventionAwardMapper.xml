<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionAwardMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.ConventionAward"  keyProperty="id" useGeneratedKeys="true">
    insert into m_convention_award (id, fk_convention_id, award_name, 
      award_description, award_count, provider_name, 
      provider_name_sub, get_role, view_order, get_fk_convention_person_id,
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkConventionId,jdbcType=BIGINT}, #{awardName,jdbcType=VARCHAR}, 
      #{awardDescription,jdbcType=VARCHAR}, #{awardCount,jdbcType=INTEGER}, #{providerName,jdbcType=VARCHAR}, 
      #{providerNameSub,jdbcType=VARCHAR}, #{getRole,jdbcType=VARCHAR}, #{viewOrder,jdbcType=INTEGER},#{getFkConventionPersonId,jdbcType=VARCHAR},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionAward"  keyProperty="id" useGeneratedKeys="true">
    insert into m_convention_award
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionId != null">
        fk_convention_id,
      </if>
      <if test="awardName != null">
        award_name,
      </if>
      <if test="awardDescription != null">
        award_description,
      </if>
      <if test="awardCount != null">
        award_count,
      </if>
      <if test="providerName != null">
        provider_name,
      </if>
      <if test="providerNameSub != null">
        provider_name_sub,
      </if>
      <if test="getRole != null">
        get_role,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="getFkConventionPersonId != null">
        get_fk_convention_person_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionId != null">
        #{fkConventionId,jdbcType=BIGINT},
      </if>
      <if test="awardName != null">
        #{awardName,jdbcType=VARCHAR},
      </if>
      <if test="awardDescription != null">
        #{awardDescription,jdbcType=VARCHAR},
      </if>
      <if test="awardCount != null">
        #{awardCount,jdbcType=INTEGER},
      </if>
      <if test="providerName != null">
        #{providerName,jdbcType=VARCHAR},
      </if>
      <if test="providerNameSub != null">
        #{providerNameSub,jdbcType=VARCHAR},
      </if>
      <if test="getRole != null">
        #{getRole,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="getFkConventionPersonId != null">
        #{getFkConventionPersonId,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getConventionAwards" resultType="com.get.salecenter.vo.ConventionAwardVo">
      select * from m_convention_award where 1=1
    <if test="roles != null">
      <foreach collection="roles" item="role" open="AND (" close=")" separator="or" >
        get_role like concat("%",#{role},"%")
      </foreach>
    </if>
    <if test="awardName != null">
     and  (award_name like concat("%",#{awardName},"%") or provider_name like concat("%",#{awardName},"%") or provider_name_sub like concat("%",#{awardName},"%"))
    </if>
    <if test="fkConventionId!=null">
      and fk_convention_id = #{fkConventionId}
    </if>
     order by view_order desc
  </select>
  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select ifnull(max(view_order)+1,1) from m_convention_award
  </select>
  <select id="listAward" resultType="com.get.salecenter.vo.ConventionAwardVo">
    select la.*,la.award_count-IFNULL(las.usecount,0) restcount from m_convention_award la left join
      (select max(lw.fk_convention_award_id)lid, count(lw.fk_convention_award_id)usecount from m_convention_award_winner lw group by lw.fk_convention_award_id) las on la.id=las.lid where 1=1
      and la.fk_convention_id = #{fkConventionId} and instr(trim(la.award_name) ,trim(lower(#{award_name}))) > 0   order by la.view_order desc
  </select>
  <select id="getRole" resultType="java.lang.String">
    select get_role from m_convention_award where id = #{awardId}
  </select>
  <select id="getPersonId" resultType="java.lang.String">
    select get_fk_convention_person_id from m_convention_award where id = #{awardId}
  </select>
</mapper>