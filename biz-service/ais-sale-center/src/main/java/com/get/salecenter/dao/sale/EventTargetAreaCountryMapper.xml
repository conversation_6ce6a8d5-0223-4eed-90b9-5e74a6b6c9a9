<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EventTargetAreaCountryMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.EventTargetAreaCountry">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_event_id" jdbcType="BIGINT" property="fkEventId" />
    <result column="fk_area_country_id" jdbcType="BIGINT" property="fkAreaCountryId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

  <insert id="insertSelective" parameterType="com.get.salecenter.entity.EventTargetAreaCountry" keyProperty="id" useGeneratedKeys="true">
    insert into r_event_target_area_country
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkEventId != null">
        fk_event_id,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkEventId != null">
        #{fkEventId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getCountryIdsByEventIds" resultType="java.lang.Long">
    select
     fk_area_country_id
    from
     r_event_target_area_country
    where
     fk_event_id
    in
     <foreach item="item" index="index" collection="eventIds" open="(" separator="," close=")">
       #{item}
     </foreach>

  </select>

</mapper>