package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.entity.ClientStaff;
import com.get.salecenter.vo.ClientStaffVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ClientStaffMapper extends BaseMapper<ClientStaff> {

    /**
     * 新增学生资源负责人
     * @param clientStaff
     */
    void insert(ClientStaffVo clientStaff);

    /**
     * 根据学生资源id查询学生资源负责人
     *
     * @param pages
     * @param clientId
     * @return
     */
    List<ClientStaffVo> findAllByClientStaff(@Param("pages") IPage<ClientStaffVo> pages, @Param("clientId") Long clientId);

    void updateById(@Param("clientStaff") ClientStaffVo clientStaff);

    ClientStaffVo selectByStaffId(@Param("fkStaffId") Long fkStaffId,@Param("fkClientId") Long fkClientId);
}
