<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EventBillMapper">
    <select id="getEventBills" resultType="com.get.salecenter.vo.EventBillListVo">
        SELECT
<!--            a.*,-->
            ANY_VALUE(a.id) AS id,
            ANY_VALUE(a.fk_company_id) AS fk_company_id,
            ANY_VALUE(a.fk_institution_provider_id) AS fk_institution_provider_id,
            ANY_VALUE(a.event_year) AS event_year,
            ANY_VALUE(a.fk_currency_type_num_invoice) AS fk_currency_type_num_invoice,
            ANY_VALUE(a.invoice_amount) AS invoice_amount,
            ANY_VALUE(a.fk_currency_type_num_event) AS fk_currency_type_num_event,
            ANY_VALUE(a.event_amount) AS event_amount,
            ANY_VALUE(a.invoice_summary) AS invoice_summary,
            ANY_VALUE(a.invoice_contact_person) AS invoice_contact_person,
            ANY_VALUE(a.invoice_contact_email) AS invoice_contact_email,
            ANY_VALUE(a.remark) AS remark,
            ANY_VALUE(a.fk_receivable_plan_id) AS fk_receivable_plan_id,
            ANY_VALUE(a.fk_invoice_num) AS fk_invoice_num,
            ANY_VALUE(a.status) AS status,
            MAX(a.gmt_create) AS gmt_create,
            ANY_VALUE(a.gmt_create_user) AS gmt_create_user,
            MAX(a.gmt_modified) AS gmt_modified,
            ANY_VALUE(a.gmt_modified_user) AS gmt_modified_user,
            ANY_VALUE(l.short_name) AS fkCompanyName,
<!--            GROUP_CONCAT(DISTINCT CONCAT(d.name, IF(d.name_en IS NULL, '', CONCAT('（', d.name_en, '）')))) AS fkStaffNoticeName,-->
            GROUP_CONCAT(
            CONCAT_WS('', d.name,
            IF(d.name_en IS NULL, '', CONCAT('（', d.name_en, '）'))
            )
            ) AS fkStaffNoticeName,
<!--            CONCAT(e.name, IF(e.name_chn IS NULL, '', CONCAT('（', e.name_chn, '）'))) AS fkInstitutionProviderName,-->
            ANY_VALUE(CONCAT(e.name, IF(e.name_chn IS NULL, '', CONCAT('（', e.name_chn, '）')))) AS fkInstitutionProviderName,
            ANY_VALUE(ac.fkAreaCountryName) AS fkAreaCountryName,
            ANY_VALUE(es.fkEventSummaryName) AS fkEventSummaryName,
<!--            GROUP_CONCAT(DISTINCT CONCAT(g.name, IF(g.name_chn IS NULL, '', CONCAT('（', g.name_chn, '）')))) AS fkAreaCountryName,-->
<!--            TRIM(GROUP_CONCAT(DISTINCT CONCAT(' ', n.event_summary))) AS fkEventSummaryName,-->
            ANY_VALUE(nullif(a.invoice_amount,  0)) AS invoiceAmountCurrency,
            ANY_VALUE( CONCAT(h.type_name, '（', h.num, '）')) AS invoiceCurrencyTypeNum,
            ANY_VALUE(nullif(a.event_amount,  0)) AS eventAmountCurrency,
            ANY_VALUE(CONCAT(h2.type_name, '（', h2.num, '）')) AS eventCurrencyTypeNum,
            ANY_VALUE(ac.fkAreaCountryIds) AS fkAreaCountryIds,
            ANY_VALUE(es.fkEventSummaryIds) AS fkEventSummaryIds,
<!--            GROUP_CONCAT(DISTINCT g.id) AS fkAreaCountryIds,-->
<!--            GROUP_CONCAT(DISTINCT n.id) AS fkEventSummaryIds,-->
            ANY_VALUE(a.event_amount - IFNULL(z1.sumAmountReceivable, 0) - IFNULL(z2.sumAmountReceivable, 0)) AS differenceEventAmountCurrency,
            ANY_VALUE(
                CASE
                WHEN IFNULL(z1.sumAmountReceivable, 0) + IFNULL(z2.sumAmountReceivable, 0) = 0 THEN 0
                WHEN a.event_amount - IFNULL(z1.sumAmountReceivable, 0) - IFNULL(z2.sumAmountReceivable, 0) = 0 THEN 2
                WHEN a.event_amount - IFNULL(z1.sumAmountReceivable, 0) - IFNULL(z2.sumAmountReceivable, 0) > 0 THEN 1
                END
            ) AS allocationStatus,
            ANY_VALUE(IFNULL(z1.sumAmountReceivable, 0) + IFNULL(z2.sumAmountReceivable, 0)) AS assignedAmount,
            ANY_VALUE(a.event_amount - IFNULL(z1.sumAmountReceivable, 0) - IFNULL(z2.sumAmountReceivable, 0)) AS differenceEventAmount
        FROM
            m_event_bill a
            INNER JOIN
            (
                SELECT DISTINCT a.id FROM(

                SELECT
                a.id
                FROM
                m_event_bill a
                INNER JOIN ais_permission_center.m_staff b ON a.gmt_create_user = b.login_id
                WHERE
                b.id = #{staffId}

                UNION ALL

                SELECT
                a.id
                FROM
                m_event_bill a
                INNER JOIN r_event_bill_staff_notice c on a.id = c.fk_event_bill_id
                INNER JOIN ais_permission_center.m_staff b on b.id = c.fk_staff_id_notice
                WHERE
                b.id IN
                <if test="staffFollowerIds != null and staffFollowerIds.size() != 0">
                    <foreach collection="staffFollowerIds" item="staffFollowerId" open="(" separator="," close=")">
                        #{staffFollowerId}
                    </foreach>
                </if>

                OR a.gmt_create_user =#{loginId}
                ) a
            ) z on z.id = a.id
            LEFT JOIN (
                    SELECT fk_event_bill_id, fk_staff_id_notice
                    FROM (
                    SELECT DISTINCT fk_event_bill_id, fk_staff_id_notice
                    FROM r_event_bill_staff_notice
                    ) AS sub_c
            ) c ON a.id = c.fk_event_bill_id


<!--                LEFT JOIN r_event_bill_staff_notice c ON a.id = c.fk_event_bill_id-->
            LEFT JOIN ais_permission_center.m_staff d ON d.id = c.fk_staff_id_notice
            LEFT JOIN ais_institution_center.m_institution_provider e ON e.id = a.fk_institution_provider_id
            LEFT JOIN r_event_bill_area_country f ON a.id = f.fk_event_bill_id
            LEFT JOIN (
                SELECT
                    t.fk_event_bill_id,
                    GROUP_CONCAT(CAST(t.fk_area_country_id AS CHAR)) AS fkAreaCountryIds,
                    GROUP_CONCAT(t.name_chn_full) AS fkAreaCountryName
                FROM (
                    SELECT DISTINCT
                    f.fk_event_bill_id,
                    f.fk_area_country_id,
                    CONCAT(g.name, IF(g.name_chn IS NULL, '', CONCAT('（', g.name_chn, '）'))) AS name_chn_full
                    FROM r_event_bill_area_country f
                    JOIN ais_institution_center.u_area_country g ON g.id = f.fk_area_country_id
                ) t
                GROUP BY t.fk_event_bill_id
            ) ac ON ac.fk_event_bill_id = a.id
<!--                LEFT JOIN ais_institution_center.u_area_country g ON g.id = f.fk_area_country_id-->
            LEFT JOIN ais_finance_center.u_currency_type h ON h.num = a.fk_currency_type_num_invoice
            LEFT JOIN ais_finance_center.u_currency_type h2 ON h2.num = a.fk_currency_type_num_event
            LEFT JOIN ais_permission_center.m_company l ON l.id = a.fk_company_id
            LEFT JOIN r_event_bill_event_summary m ON m.fk_event_bill_id = a.id
            LEFT JOIN (
                SELECT
                t.fk_event_bill_id,
                GROUP_CONCAT(CAST(t.fk_event_summary_id AS CHAR)) AS fkEventSummaryIds,
                TRIM(GROUP_CONCAT(t.summary)) AS fkEventSummaryName
                FROM (
                    SELECT DISTINCT
                    m.fk_event_bill_id,
                    m.fk_event_summary_id,
                    CONCAT('', n.event_summary) AS summary
                    FROM r_event_bill_event_summary m
                    JOIN u_event_summary n ON n.id = m.fk_event_summary_id
                ) t
                GROUP BY t.fk_event_bill_id
            ) es ON es.fk_event_bill_id = a.id
<!--                LEFT JOIN u_event_summary n ON n.id = m.fk_event_summary_id-->

            LEFT JOIN (
                SELECT
                    a.fk_event_bill_id,
                    SUM(IFNULL(a.amount_receivable, 0)) AS sumAmountReceivable
                FROM m_event_cost a
                         LEFT JOIN m_event_bill b ON a.fk_event_bill_id = b.id
                WHERE b.status != 0
                GROUP BY a.fk_event_bill_id
            ) z1 ON a.id = z1.fk_event_bill_id

            LEFT JOIN (
                SELECT
                    a.fk_event_bill_id,
                    SUM(IFNULL(a.amount_receivable, 0)) AS sumAmountReceivable
                FROM m_event_incentive_cost a
                         LEFT JOIN m_event_bill b ON a.fk_event_bill_id = b.id
                WHERE b.status != 0
                GROUP BY a.fk_event_bill_id
            ) z2 ON a.id = z2.fk_event_bill_id

        where 1=1
        <if test="eventBillListDto.fkAreaCountryIdList.size() == 0">
            AND f.fk_area_country_id IN
            <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>
        <if test="eventBillListDto.fkCompanyId !=null and eventBillListDto.fkCompanyId != ''">
            and a.fk_company_id = #{eventBillListDto.fkCompanyId}
        </if>
        <if test="eventBillListDto.fkCompanyIdList != null and eventBillListDto.fkCompanyIdList.size()>0">
            and a.fk_company_id in
            <foreach collection="eventBillListDto.fkCompanyIdList" item="fkCompanyId" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="eventBillListDto.fkInvoiceNum !=null and eventBillListDto.fkInvoiceNum !=''">
            and a.fk_invoice_num = #{eventBillListDto.fkInvoiceNum}
        </if>
        <if test="eventBillListDto.isCreateInvoice != null">
            <if test="eventBillListDto.isCreateInvoice == 0">
                and a.fk_invoice_num is null
            </if>
            <if test="eventBillListDto.isCreateInvoice == 1">
                and a.fk_invoice_num is not null
            </if>
        </if>

        <if test="eventBillListDto.status ==0 or eventBillListDto.status !=null and eventBillListDto.status !=''">
            and a.status = #{eventBillListDto.status}
        </if>
        <if test="eventBillListDto.eventYear !=null">
            and a.event_year = #{eventBillListDto.eventYear}
        </if>
        <if test="eventBillListDto.remark !=null and eventBillListDto.remark !=''">
            and a.remark like concat("%",#{eventBillListDto.remark},"%")
        </if>
        <if test="eventBillListDto.gmtCreateUser !=null and eventBillListDto.gmtCreateUser !=''">
            and a.gmt_create_user like concat("%",#{eventBillListDto.gmtCreateUser},"%")
        </if>
        GROUP BY a.id,es.fkEventSummaryIds,es.fkEventSummaryName,ac.fkAreaCountryIds,ac.fkAreaCountryName
        having 1=1
        <if test="eventBillListDto.institutionProviderName !=null and eventBillListDto.institutionProviderName !=''">
            and fkInstitutionProviderName like concat("%",#{eventBillListDto.institutionProviderName},"%")
        </if>
        <if test="eventBillListDto.fkAreaCountryIdList !=null and eventBillListDto.fkAreaCountryIdList.size()>0">
            AND
            <foreach collection="eventBillListDto.fkAreaCountryIdList" item="fkAreaCountry" open="(" separator="or"
                     close=")">
                (FIND_IN_SET(#{fkAreaCountry},fkAreaCountryIds)>0)
            </foreach>
        </if>
        <if test="eventBillListDto.fkEventSummaryIdList !=null and eventBillListDto.fkEventSummaryIdList.size()>0">
            AND
            <foreach collection="eventBillListDto.fkEventSummaryIdList" item="fkEventSummary" open="(" separator="or"
                     close=")">
                (FIND_IN_SET(#{fkEventSummary},fkEventSummaryIds)> 0)
            </foreach>
        </if>
        <if test="eventBillListDto.allocationStatus !=null">
            and allocationStatus = #{eventBillListDto.allocationStatus}
        </if>
        ORDER BY ANY_VALUE(a.status) DESC, ANY_VALUE(a.gmt_create) DESC
    </select>


    <select id="getCountryNamesByPlanIds" resultType="com.get.salecenter.vo.ReceivablePlanVo">
        SELECT
            a.fk_receivable_plan_id as id,
            GROUP_CONCAT(CASE WHEN IFNULL(c.name_chn,'')='' THEN c.`name` ELSE CONCAT(c.`name`,'（',c.name_chn,'）') END) as fkAreaCountryName
        FROM
            `m_event_bill` a
                INNER JOIN r_event_bill_area_country b on a.id = b.fk_event_bill_id
                INNER JOIN ais_institution_center.u_area_country c on c.id = b.fk_area_country_id
        where 1=1
        and a.fk_receivable_plan_id in
        <foreach collection="fkPlanIds" item="fkPlanId" index="index" open="(" separator="," close=")">
            #{fkPlanId}
        </foreach>
        GROUP BY a.fk_receivable_plan_id
    </select>

    <select id="getSummarySelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
        a.id,
        a.event_summary AS `name`
        FROM
        `u_event_summary` a
        INNER JOIN r_event_bill_event_summary b ON a.id = b.fk_event_summary_id
        INNER JOIN (
        SELECT DISTINCT
        a.id
        FROM
        (
        SELECT
        a.id
        FROM
        m_event_bill a
        INNER JOIN ais_permission_center.m_staff b ON a.gmt_modified_user = b.login_id
        WHERE
        b.id = #{staffId} UNION ALL
        SELECT
        a.id
        FROM
        m_event_bill a
        INNER JOIN r_event_bill_area_country b ON a.id = b.fk_event_bill_id
        WHERE
        b.fk_area_country_id IN
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
        </foreach>
        ) a
        ) z ON z.id = b.fk_event_bill_id
        WHERE
        1 = 1

        AND a.fk_company_id = #{fkCompanyId}

        GROUP BY
        a.id
        ORDER BY
        a.view_order DESC
    </select>
    <select id="getMaxNumOrder" resultType="java.lang.Integer">
        select
            IFNULL(count(*)+1,1) order
        from
            m_event_bill
        where 1=1
        and fk_invoice_num like concat("%",#{num},"%")
    </select>
    <select id="getEventBillByInstitutionProviderId" resultType="com.get.salecenter.vo.EventBillVo">
        SELECT
            a.id,
            a.fk_company_id,
            a.fk_institution_provider_id,
            a.event_year,
            a.fk_currency_type_num_invoice,
            a.invoice_amount,
            a.fk_currency_type_num_event,
            a.event_amount,
            a.invoice_summary,
            a.invoice_contact_person,
            a.invoice_contact_email,
            a.remark,
            a.fk_receivable_plan_id,
            a.fk_invoice_num,
            a.`status`,
            CONCAT(a.event_amount,c.type_name,"(",c.num,")",IF(a.invoice_summary is null,"",CONCAT("，",a.invoice_summary))) selectName,
            c.type_name as fkCurrencyTypeNumEventName
        FROM
            m_event_bill a
            LEFT JOIN m_event_cost b on b.fk_event_bill_id = a.id
            INNER JOIN ais_finance_center.u_currency_type c on c.num = a.fk_currency_type_num_event
        WHERE
            a.`status` !=0
        <if test="institutionProviderId != null">
            and a.fk_institution_provider_id = #{institutionProviderId}
        </if>
        <if test="companyId != null">
            and a.fk_company_id = #{companyId}
        </if>
        <if test="eventCostId !=null">
            and (b.id is null or b.id = #{eventCostId})
        </if>
        <if test="eventYear != null">
            AND a.event_year = #{eventYear}
        </if>
        GROUP BY a.id
    </select>
    <select id="getEventBillByProviderIdAndEventIncentiveCostId"
            resultType="com.get.salecenter.vo.EventBillVo">
        SELECT
        a.id,
        a.fk_company_id,
        a.fk_institution_provider_id,
        a.event_year,
        a.fk_currency_type_num_invoice,
        a.invoice_amount,
        a.fk_currency_type_num_event,
        a.event_amount,
        a.invoice_summary,
        a.invoice_contact_person,
        a.invoice_contact_email,
        a.remark,
        a.fk_receivable_plan_id,
        a.fk_invoice_num,
        a.`status`,
        CONCAT(a.event_amount,c.type_name,"(",c.num,")",IF(a.invoice_summary is null,"",CONCAT("，",a.invoice_summary))) selectName,
        c.type_name as fkCurrencyTypeNumEventName
        FROM
        m_event_bill a
        LEFT JOIN m_event_incentive_cost b on b.fk_event_bill_id = a.id
        INNER JOIN ais_finance_center.u_currency_type c on c.num = a.fk_currency_type_num_event
        WHERE
        a.`status` !=0
        <if test="institutionProviderId != null">
            and a.fk_institution_provider_id = #{institutionProviderId}
        </if>
        <if test="companyId != null">
            and a.fk_company_id = #{companyId}
        </if>
        <if test="eventIncentiveCostId !=null">
            and (b.id is null or b.id = #{eventIncentiveCostId})
        </if>
        GROUP BY a.id
    </select>
    <select id="getEventBillAccount" resultType="com.get.salecenter.vo.EventBillAccountVo">
        SELECT
            a.id as fkEventBillId,
            c.amount_receivable,
            b.fk_currency_type_num,
            c.id as fkReceiptFormItemId,
            c.fk_receivable_plan_id as fkReceivablePlanId
        FROM
            `m_event_bill` a
                LEFT JOIN m_receivable_plan b on a.fk_receivable_plan_id = b.id
                LEFT JOIN ais_finance_center.m_receipt_form_item c on c.fk_receivable_plan_id = b.id
                LEFT JOIN ais_finance_center.m_receipt_form d on d.id = c.fk_receipt_form_id
        WHERE
            a.`status` = 1
          AND b.`status` = 1
          AND d.`status` = 1
        <if test="planIds !=null and planIds.size()>0">
            AND b.id in
            <foreach collection="planIds" item="planId" open="(" separator="," close=")">
                #{planId}
            </foreach>
        </if>
        ORDER BY c.gmt_create
    </select>
    <select id="getReceivablePlanByInvoiceNum" resultType="java.lang.Long">
        SELECT
            a.fk_receivable_plan_id
        FROM
            ais_finance_center.`r_invoice_receivable_plan` a
                LEFT JOIN ais_finance_center.m_invoice b on a.fk_invoice_id = b.id and b.`status` = 1
        where b.num = #{invoiceNum}
    </select>
    <select id="getReceivableInfo" resultType="com.get.salecenter.vo.EventBillListVo">
        SELECT
            ebr.fk_event_bill_id as id,
            CONCAT(k.type_name,"（",k.num,"）") receivableCurrencyTypeNum,
<!--            CONCAT(SUM(IFNULL(i.receivable_amount,0)),k.type_name,"（",k.num,"）") receivableAmountCurrency,-->
            CONCAT(SUM(IFNULL(i.receivable_amount,0))) receivableAmountCurrency,
<!--            CONCAT(SUM((IFNULL(j.sum_amount_receivable,0) + IFNULL(j.sum_amount_exchange_rate,0))),k.type_name,"（",k.num,"）") actualReceivableAmountCurrency,-->
            CONCAT(SUM((IFNULL(j.sum_amount_receivable,0) + IFNULL(j.sum_amount_exchange_rate,0)))) actualReceivableAmountCurrency,
            SUM(IFNULL(j.sum_amount_receivable,0) + IFNULL(j.sum_amount_exchange_rate,0)) actualReceivableAmount,
<!--            CONCAT(SUM((IFNULL(i.receivable_amount,0) -(IFNULL(j.sum_amount_receivable,0) - IFNULL(j.sum_amount_exchange_rate,0)) )),k.type_name,"（",k.num,"）") differenceAmountCurrency-->
            CONCAT(SUM((IFNULL(i.receivable_amount,0) -(IFNULL(j.sum_amount_receivable,0) - IFNULL(j.sum_amount_exchange_rate,0)) ))) differenceAmountCurrency
        FROM
            r_event_bill_receivable_plan ebr
                LEFT JOIN m_receivable_plan i on i.id = ebr.fk_receivable_plan_id
                LEFT JOIN (
                SELECT a.fk_receivable_plan_id,
                       SUM(IFNULL(a.amount_receivable,0)) sum_amount_receivable,
                       SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate,
                       SUM(IFNULL(a.amount_hkd,0)) sum_amount_hkd,
                       SUM(IFNULL(a.amount_rmb,0)) sum_amount_rmb
                FROM ais_finance_center.m_receipt_form_item a
                         LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id=b.id
                WHERE b.`status`!=0 AND b.settlement_status = 1
                GROUP BY a.fk_receivable_plan_id
            ) j ON j.fk_receivable_plan_id=ebr.fk_receivable_plan_id
                LEFT JOIN ais_finance_center.u_currency_type k on k.num = i.fk_currency_type_num
        where ebr.fk_event_bill_id in
        <foreach collection="eventBillIds" index="index" item="eventBillId" open="(" separator="," close=")">
            #{eventBillId}
        </foreach>
        GROUP BY ebr.fk_event_bill_id,i.fk_currency_type_num
    </select>
    <select id="getSummarySelectList" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
        a.id,
        a.event_summary AS `name`
        FROM
        `u_event_summary` a
        INNER JOIN r_event_bill_event_summary b ON a.id = b.fk_event_summary_id
        INNER JOIN (
        SELECT DISTINCT
        a.id
        FROM
        (
        SELECT
        a.id
        FROM
        m_event_bill a
        INNER JOIN ais_permission_center.m_staff b ON a.gmt_modified_user = b.login_id
        WHERE
        b.id = #{staffId} UNION ALL
        SELECT
        a.id
        FROM
        m_event_bill a
        INNER JOIN r_event_bill_area_country b ON a.id = b.fk_event_bill_id
        WHERE
        b.fk_area_country_id IN
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
        </foreach>
        ) a
        ) z ON z.id = b.fk_event_bill_id
        WHERE
        1 = 1

        AND a.fk_company_id IN
        <foreach collection="fkCompanyIdList" item="companyId" open="(" separator="," close=")">
            #{companyId}
        </foreach>
        GROUP BY
        a.id
        ORDER BY
        a.view_order DESC

    </select>

    <select id="getEventBillYearsByInstitutionProviderId" resultType="java.lang.String">
        SELECT DISTINCT a.event_year
        FROM m_event_bill a
                 LEFT JOIN m_event_cost b on b.fk_event_bill_id = a.id
                 INNER JOIN ais_finance_center.u_currency_type c on c.num = a.fk_currency_type_num_event
        WHERE a.`status` !=0
        <if test="institutionProviderId != null">
            and a.fk_institution_provider_id = #{institutionProviderId}
        </if>
        <if test="companyId != null">
            and a.fk_company_id = #{companyId}
        </if>
        <if test="eventCostId != null">
            and (b.id is null
             or b.id = #{eventCostId})
        </if>
        GROUP BY a.event_year
        ORDER BY a.event_year DESC
    </select>
</mapper>