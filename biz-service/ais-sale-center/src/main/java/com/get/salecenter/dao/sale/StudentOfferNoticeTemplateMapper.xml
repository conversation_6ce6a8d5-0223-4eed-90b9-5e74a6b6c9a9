<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentOfferNoticeTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.StudentOfferNoticeTemplate">
        <id column="id" property="id" />
        <result column="type_key" property="typeKey" />
        <result column="title" property="title" />
        <result column="email_template" property="emailTemplate" />
        <result column="remark" property="remark" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type_key, title, email_template, remark, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>

</mapper>
