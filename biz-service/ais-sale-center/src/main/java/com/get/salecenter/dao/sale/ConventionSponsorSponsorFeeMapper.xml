<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionSponsorSponsorFeeMapper">


  <select id="getSponsorFeeDtoList" resultType="com.get.salecenter.vo.ConventionSponsorFeeVo">
    SELECT
	  b.*, a.remark
    FROM
	  r_convention_sponsor_sponsor_fee a
	LEFT JOIN m_convention_sponsor_fee b ON a.fk_convention_sponsor_fee_id = b.id
    WHERE
	  a.fk_convention_sponsor_id = #{sponsorId}

  </select>
</mapper>