package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.entity.ReceivableReason;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface ReceivableReasonMapper extends BaseMapper<ReceivableReason> {
    int insert(ReceivableReason record);

    int insertSelective(ReceivableReason record);

    Integer getMaxViewOrder();

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param []
     * <AUTHOR>
     */
    List<BaseSelectEntity> getReasonSelect();

    /**
     * @return java.lang.String
     * @Description: 查询名称
     * @Param [id]
     * <AUTHOR>
     */
    String getReasonNameById(Integer id);
}