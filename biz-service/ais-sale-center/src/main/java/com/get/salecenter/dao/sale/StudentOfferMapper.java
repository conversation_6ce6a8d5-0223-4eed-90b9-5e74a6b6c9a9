package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.StudentStepHistoryVo;
import com.get.salecenter.bo.StudentOfferListQueryBo;
import com.get.salecenter.vo.*;
import com.get.salecenter.entity.RStudentOfferItemStep;
import com.get.salecenter.entity.StudentOffer;
import com.get.salecenter.dto.StudentInfoDto;
import com.get.salecenter.dto.StudentOfferDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface StudentOfferMapper extends BaseMapper<StudentOffer> {

    Long getMaxId();

    /**
     * @return java.util.List<com.get.core.mybatis.base.BaseSelectEntity>
     * @Description: 下拉
     * @Param [projectType]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getStudentOfferSelect(@Param("studentId") Long studentId);

    /**
     * 获取学习方案下拉
     *
     * @param studentId
     * @param staffFollowerIds
     * @param isStudentAdmin
     * @return
     */
    List<BaseSelectEntity> getStudentOfferSelectNew(@Param("studentId") Long studentId,
                                                    @Param("staffFollowerIds")List<Long> staffFollowerIds,
                                                    @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                    @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                    @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentItemAndStepVo>
     * @Description: 查询学习计划和步骤
     * @Param [offerId]
     * <AUTHOR>
     */
    List<StudentItemAndStepVo> getItemAndStepList(@Param("offerId") Long offerId);


    /**
     * @return java.lang.String
     * @Description: 查询代理名称
     * @Param [offerId]
     * <AUTHOR>
     */
    String getAgentNameByOfferId(Long offerId);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean isExistByStudentId(Long studentId);

    /**
     * 根据学习计划获取学生人头数
     *
     * @Date 18:04 2021/6/15
     * <AUTHOR>
     */
    int getStudentCountByOfferIds(@Param("studentOfferIdList") List<Long> studentOfferIdList);

    /**
     * 步骤列表
     *
     * @param ids
     * @return
     */
    List<StudentOfferItemListVo> getItemStepList(@Param("ids") Set<Long> ids);

    /**
     * 申请列表
     *
     * @param studentOfferDto
     * @return
     */
    List<StudentOfferVo> getStudentOfferWorkFolw(IPage<StudentOfferVo> iPage, @Param("studentOfferDto") StudentOfferDto studentOfferDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.StudentItemAndStepVo>
     * @Description: 查询学习计划和步骤
     * @Param [offerId]
     * <AUTHOR>
     */
    List<StudentItemAndStepVo> getItemAndSteps(@Param("offerId") Long offerId);

    /**
     * @param ids
     * @param isStudentOfferItemFinancialHiding
     * @return
     */
    List<StudentItemInfoVo> getItemAndStepsByIds(@Param("ids") Set<Long> ids, @Param("isStudentOfferItemFinancialHiding") boolean isStudentOfferItemFinancialHiding);

    /**
     * 获取代理
     *
     * @param offerIds
     * @return
     */
    List<AgentVo> getAgentByOfferIds(@Param("offerIds") String offerIds);


    /**
     * 通过学生id获取学生申请国家
     *
     * @param studentId
     * @return
     */
    Set<Long> getAreaCountryByStudentId(@Param("studentId") Long studentId);

    /**
     * 通过学生ids获取学生申请国家s
     *
     * @param studentIds
     * @return
     */
    Set<Long> getAreaCountrysByStudentIds(@Param("studentIds") Set<Long> studentIds);

    /**
     * @param studentOfferDto
     * @return
     */
    List<Long> getOfferIdByStepChangeDate(@Param("studentOfferDto") StudentOfferDto studentOfferDto);

    /**
     * 国家线ids
     *
     * @param companyId
     * @return
     */
    List<Long> getAreaCountryList(@Param("companyId") Long companyId);

    /**
     * 获取可以看的国家线
     *
     * @param staffIds
     * @return
     */
    List<Long> getVisibleCountryIdByStaffIds(@Param("staffIds") String staffIds);

    /**
     * 根据id查询学生id
     *
     * @param id
     * @return
     */
    Long getStudentIdById(Long id);

    /**
     * 根据学生id查询offer
     *
     * @param id
     * @return
     */
    List<StudentOffer> getOfferByStudentId(@Param("id") Long id);

    /**
     * 根据学生ids查询offer集合
     *
     * @param ids
     * @returns
     */
    List<StudentOffer> getOfferByStudentIds(@Param("ids")  List<Long> ids);

    /**
     * @param studentOfferDto
     * @param staffFollowerIds
     * @param userNames
     * @return
     */
    List<StudentOfferVo> getStudentOfferListNew(IPage<StudentOfferVo> pages, @Param("studentOfferDto") StudentOfferDto studentOfferDto, @Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("userNames") List<String> userNames);

    /**
     * @param
     * @param staffFollowerIds
     * @param countryIds
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @param permissionGroupInstitutionIds
     * @param staffBoundBdIds
     * @return
     */
    @DS("saledb-doris")
    List<StudentOfferVo> getStudentOfferListNew2(@Param("studentOfferDto") StudentOfferListQueryBo offerListQueryBo, @Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("countryIds") List<Long> countryIds,
                                                 @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding, @Param("isStudentAdmin") Boolean isStudentAdmin, @Param("institutionIds") List<Long> institutionIds, @Param("isBd")Boolean isBd,
                                                 @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                 @Param("staffBoundBdIds") List<Long> staffBoundBdIds);

    /**
     * 申请方案列表带权限
     *
     * @param studentOfferDto
     * @param staffFollowerIds
     * @param countryIds
     * @param isStudentOfferItemFinancialHiding
     * @param isStudentAdmin
     * @param staffId
     * @param permissionGroupInstitutionIds
     * @param staffBoundBdIds
     * @return
     */
    @DS("saledb-doris")
    List<StudentOffer> getStudentOfferNew(IPage<StudentOffer> pages, @Param("studentOfferDto") StudentOfferDto studentOfferDto,
                                          @Param("staffFollowerIds") List<Long> staffFollowerIds, @Param("countryIds")List<Long> countryIds,
                                          @Param("isStudentOfferItemFinancialHiding") Boolean isStudentOfferItemFinancialHiding,
                                          @Param("isStudentAdmin")  Boolean isStudentAdmin,
                                          @Param("staffId")  Long staffId,
                                          @Param("institutionIds")List<Long> institutionIds,
                                          @Param("isBd")Boolean isBd,
                                          @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                          @Param("staffBoundBdIds") List<Long> staffBoundBdIds);


    /**
     * @return java.lang.String
     * @Description: 查询代理名称
     * @Param [offerId]
     * <AUTHOR>
     */
    List<StudentOfferVo> getAgentNameByOfferIds(@Param("offerIds")Set<Long> offerIds);

    /**
     * 获取学生
     * @param fkStudentNum
     * @return
     */
    List<AgentsBindingVo> getStudentOfferAgentSelect(@Param("fkStudentNum")String fkStudentNum);

    List<COfferVo> test(@Param("ids") List<Long> ids);

    List<RStudentOfferItemStep> selectOfferAccepted(@Param("itemId")Long itemId);

    List<StudentOfferBindingVo> getOfferBindingList(IPage<StudentOfferBindingVo> iPage, @Param("studentInfoDto") StudentInfoDto studentInfoDto);

    /**
     * issue创建的学习方案
     * @param ids
     * @return
     */
    Set<Long> selectIssueCreatItem(@Param("ids")Set<Long> ids);

    @DS("saledb-doris")
    List<StudentOfferItemUploadVo> getStudentOffer();


    StudentOffer verifyStudentOffer(@Param("offerVo") StudentOfferDto offerVo);
    /**
     * 根据offerid， 角色key 获取对应角色员工
     *
     * @param offerIds
     * @param roleKeyStr
     * @return
     */
    List<BaseSelectEntity> getRoleStaffByOfferId(@Param("offerIds") List<Long> offerIds, @Param("roleKeyStr") String roleKeyStr);

    @DS("saledb-doris")
    List<StudentStepHistoryVo> getStatusChangeDetails(@Param("studentId") Long studentId,@Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                      @Param("isStudentOfferItemFinancialHiding")Boolean isStudentOfferItemFinancialHiding,
                                                      @Param("isStudentAdmin") Boolean isStudentAdmin,
                                                      @Param("permissionGroupInstitutionIds") List<Long> permissionGroupInstitutionIds,
                                                      @Param("staffBoundBdIds") List<Long> staffBoundBdIds);
}