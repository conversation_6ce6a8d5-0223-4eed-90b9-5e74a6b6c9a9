package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.AgentCompanyInfoVo;
import com.get.salecenter.entity.AgentCompany;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface AgentCompanyMapper extends GetMapper<AgentCompany>,BaseMapper<AgentCompany> {

    int insertSelective(AgentCompany record);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description: 查询合同对应代理所关联的公司
     * @Param [contractId]
     * <AUTHOR>
     */
    List<Long> getAgentCompanyIdByContractId(Long contractId);

    List<AgentCompanyInfoVo> getAgentCompanyName(@Param("list") Set<Long> id);

    Integer getAgentCompanyIdById(Long id);
}