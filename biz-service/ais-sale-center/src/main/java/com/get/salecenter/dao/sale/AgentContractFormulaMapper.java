package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AgentContractFormula;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/6 12:21
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface AgentContractFormulaMapper extends BaseMapper<AgentContractFormula> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(AgentContractFormula record);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param [agentId]
     * <AUTHOR>
     */
    Integer getMaxViewOrder(@Param("agentId") Long agentId);

    /**
     * @return boolean
     * @Description :校验
     * @Param [agentId]
     * <AUTHOR>
     */
    boolean agentContractFormulaIsEmpty(Long agentId);

    /**
     * @return com.get.salecenter.entity.AgentContractFormula
     * @Description: 合同公式获取代理合同公式
     * @Param [contractFormula]
     * <AUTHOR>
     **/
    List<AgentContractFormula> getAgentContractFormulasByFormula(@Param("agentId") Long agentId, @Param("currencyTypeNum") String currencyTypeNum);

}