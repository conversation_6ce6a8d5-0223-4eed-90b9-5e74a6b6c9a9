<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentLabelMapper">

    <insert id="insertBatchSomeColumn" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO r_agent_label
        (id, fk_agent_id, email, fk_label_id, gmt_create, gmt_create_user)
        VALUES
        <if test="list != null and list.size() > 0">
            <foreach collection="list" item="item" separator=",">
                (
                #{item.id},
                #{item.fkAgentId},
                #{item.email},
                #{item.fkLabelId},
                #{item.gmtCreate},
                #{item.gmtCreateUser}
                )
            </foreach>
        </if>
    </insert>

    <select id="getAgentLabelList" resultType="com.get.salecenter.vo.AgentLabelVo">
        SELECT a.id,
               a.fk_agent_id       AS fkAgentId,
               a.email             AS labelEmail,
               a.fk_label_id       AS fkLabelId,
               a.gmt_create        AS gmtCreate,
               a.gmt_create_user   AS gmtCreateUser,
               a.gmt_modified      AS gmtModified,
               a.gmt_modified_user AS gmtModifiedUser,
               b.id                AS labelId,
               b.label_name        AS labelName,
               b.label_key         AS labelKey,
               b.icon_name         AS iconName,
               b.color             AS color,
               b.remark            AS labelRemark,
               c.id                AS labelTypeId,
               c.type_name         AS labelTypeName
        FROM ais_sale_center.r_agent_label a
                 LEFT JOIN ais_platform_center.u_label b ON a.fk_label_id = b.id and b.is_active = 1
                 LEFT JOIN ais_platform_center.u_label_type c ON b.fk_label_type_id = c.id
        WHERE 1 = 1
        <if test="agentLabelVo.id != null">
            AND a.id = #{agentLabelVo.id}
        </if>
        <if test="agentLabelVo.fkAgentId != null">
            AND a.fk_agent_id = #{agentLabelVo.fkAgentId}
        </if>
        <if test="agentLabelVo.fkLabelId != null">
            AND a.fk_label_id = #{agentLabelVo.fkLabelId}
        </if>
        <if test="agentLabelVo.labelEmail != null and agentLabelVo.labelEmail != ''">
            AND a.email LIKE CONCAT('%', #{agentLabelVo.labelEmail}, '%')
        </if>
        <if test="agentLabelVo.labelEmails != null and agentLabelVo.labelEmails != ''">
            AND
            <foreach collection="agentLabelVo.labelEmails" item="labelEmail" open="(" separator="OR" close=")">
                a.email LIKE CONCAT('%', #{labelEmail}, '%')
            </foreach>
        </if>
        <if test="agentLabelVo.labelTypeId != null">
            AND c.id = #{agentLabelVo.labelTypeId}
        </if>
        <if test="agentLabelVo.labelRemark != null and agentLabelVo.labelRemark != ''">
            AND c.type_name LIKE CONCAT('%', #{agentLabelVo.labelRemark}, '%')
        </if>
        <if test="agentLabelVo.fkAgentIds != null and agentLabelVo.fkAgentIds.size() > 0">
            AND a.fk_agent_id IN
            <foreach collection="agentLabelVo.fkAgentIds" item="fkAgentId" open="(" separator="," close=")">
                #{fkAgentId}
            </foreach>
            AND a.email = ''
        </if>
        <if test="agentLabelVo.labelKeyWord != null and agentLabelVo.labelKeyWord != ''">
            AND b.label_name LIKE CONCAT('%', #{agentLabelVo.labelKeyWord}, '%')
            AND b.remark LIKE CONCAT('%', #{agentLabelVo.labelKeyWord}, '%')
        </if>
    </select>

</mapper>