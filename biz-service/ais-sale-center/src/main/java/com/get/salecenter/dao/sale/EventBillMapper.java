package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.dto.EventBillListDto;
import com.get.salecenter.entity.EventBill;
import com.get.salecenter.vo.EventBillAccountVo;
import com.get.salecenter.vo.EventBillListVo;
import com.get.salecenter.vo.EventBillVo;
import com.get.salecenter.vo.ReceivablePlanVo;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @author: Hardy
 * @create: 2022/5/9 11:32
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface EventBillMapper extends BaseMapper<EventBill>, GetMapper<EventBill> {

    /**
     * @param iPage
     * @param eventBillListDto
     * @param countryIds
     * @param staffId
     * @return
     */
    @DS("saledb-doris")
    List<EventBillListVo> getEventBills(IPage<EventBillListVo> iPage,
                                        @Param("eventBillListDto") EventBillListDto eventBillListDto,
                                        @Param("countryIds") List<Long> countryIds,
                                        @Param("staffId") Long staffId, @Param("loginId") String loginId,@Param("staffFollowerIds") List<Long> staffFollowerIds);


    List<ReceivablePlanVo> getCountryNamesByPlanIds(@Param("fkPlanIds") Set<Long> fkPlanIds);

    List<BaseSelectEntity> getSummarySelect(@Param("fkCompanyId") Long fkCompanyId,
                                            @Param("countryIds") List<Long> countryIds,
                                            @Param("staffId") Long staffId);

    List<BaseSelectEntity> getSummarySelectList(@Param("fkCompanyIdList") List<Long> fkCompanyIdList,
                                            @Param("countryIds") List<Long> countryIds,
                                            @Param("staffId") Long staffId);



    int getMaxNumOrder(@Param("num") String num);

    /**
     * 获取符合条件的eventBill
     *
     * @param institutionProviderId
     * @param companyId
     * @return
     */
    List<EventBillVo> getEventBillByInstitutionProviderId(@Param("institutionProviderId") Long institutionProviderId,
                                                          @Param("companyId") Long companyId,
                                                          @Param("eventCostId") Long eventCostId,
                                                          @Param("eventYear")String eventYear);

    /**
     * 获取符合条件的eventBill数据的年份
     *
     * @param institutionProviderId
     * @param companyId
     * @return
     */
    List<String> getEventBillYearsByInstitutionProviderId(@Param("institutionProviderId") Long institutionProviderId,
                                                           @Param("companyId") Long companyId,
                                                           @Param("eventCostId") Long eventCostId);


    /**
     * 获取符合条件的eventBill
     *
     * @param institutionProviderId
     * @param companyId
     * @return
     */
    List<EventBillVo> getEventBillByProviderIdAndEventIncentiveCostId(@Param("institutionProviderId") Long institutionProviderId,
                                                                      @Param("companyId") Long companyId,
                                                                      @Param("eventIncentiveCostId") Long eventIncentiveCostId);

    /**
     * 活动费用汇总到账金额
     *
     * @param planIds
     * @return
     */
    List<EventBillAccountVo> getEventBillAccount(@Param("planIds") Set<Long> planIds);

    List<Long> getReceivablePlanByInvoiceNum(@Param("invoiceNum") String invoiceNum);

    List<EventBillListVo> getReceivableInfo(@Param("eventBillIds") List<Long> eventBillIds);
}
