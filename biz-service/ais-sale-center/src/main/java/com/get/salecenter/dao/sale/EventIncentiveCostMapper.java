package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.EventIncentiveCostVo;
import com.get.salecenter.entity.EventIncentiveCost;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface EventIncentiveCostMapper extends BaseMapper<EventIncentiveCost>, GetMapper<EventIncentiveCost> {

    List<EventIncentiveCostVo> getReceiptFormInfo(@Param("eventIncentiveCostIds") Set<Long> eventIncentiveCostIds);
}