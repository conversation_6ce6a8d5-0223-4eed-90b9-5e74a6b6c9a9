package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.dto.ConventionTableDto;
import com.get.salecenter.vo.TableCharacterSubtotal;
import com.get.salecenter.vo.TableChartVo;
import com.get.salecenter.entity.ConventionTable;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/27 11:32
 * @verison: 1.0
 * @description: 峰会桌台mapper
 */
@Mapper
public interface ConventionTableMapper extends BaseMapper<ConventionTable> {

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionTable record);

    /**
     * 根据峰会id和桌台类型 查找该桌台类型最大桌台编号
     *
     * @param id
     * @param tableTypeKey
     * @return
     */
    ConventionTable getMaxTableNum(@Param("id") Long id, @Param("tableTypeKey") String tableTypeKey, @Param("preNum") String preNum);

    /**
     * @return java.util.List<com.get.salecenter.vo.TableChartVo>
     * @Description :该峰会和类型下所有桌台信息
     * @Param [conventionId, tableType]
     * <AUTHOR>
     */
    List<TableChartVo> getTableChartList(@Param("conventionId") Long conventionId, @Param("tableType") String tableType);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :获取桌台编号集合
     * @Param [conventionId]
     * <AUTHOR>
     */
    List<String> getTableNums(@Param("conventionId") Long conventionId, @Param("tableType") String tableType);

    /**
     * @return java.lang.String
     * @Description :培训桌根据桌台编号、峰会id 查找该桌台安排的展位名称
     * @Param [tableNum, conventionId, tableType]
     * <AUTHOR>
     */
    String getBootName(@Param("tableNum") String tableNum, @Param("conventionId") Long conventionId, @Param("tableType") String tableType);

    /**
     * @return java.lang.Boolean
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    Boolean conventionTableIsEmpty(Long id);

    /**
     * 获取桌子角色小计
     * @param fkTableTypeKey
     * @param fkConventionId
     * @return
     */
    List<TableCharacterSubtotal> getTableCharacterSubtotal(@Param("fkTableTypeKey") String fkTableTypeKey, @Param("conventionId") Long fkConventionId);

    List<TableCharacterSubtotal> getTableCharacterSubtotal(@Param("conventionTableDto") ConventionTableDto conventionTableVo);
}