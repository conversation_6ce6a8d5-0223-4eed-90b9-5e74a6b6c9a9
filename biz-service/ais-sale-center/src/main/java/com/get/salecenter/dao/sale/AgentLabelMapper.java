package com.get.salecenter.dao.sale;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.dto.AgentLabelDto;
import com.get.salecenter.entity.AgentLabel;
import com.get.salecenter.vo.AgentLabelVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AgentLabelMapper extends BaseMapper<AgentLabel> {
    Long insertBatchSomeColumn(@Param("list") List<AgentLabel> list);

    List<AgentLabelVo> getAgentLabelList(IPage<AgentLabelVo> iPage, @Param("agentLabelVo") AgentLabelDto agentLabelVo);
}
