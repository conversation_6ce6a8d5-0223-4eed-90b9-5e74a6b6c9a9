package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.dto.*;
import com.get.salecenter.vo.*;
import com.get.salecenter.vo.ConventionPersonVo;
import com.get.salecenter.entity.ConventionPerson;
import com.get.salecenter.entity.ConventionRegistration;
import com.get.salecenter.entity.StaffBdCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/27 12:21
 * @verison: 1.0
 * @description: 参展人员mapper
 */
@Mapper
@DS("saledb")
public interface ConventionPersonMapper extends GetMapper<ConventionPerson> {

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionPerson record);

    /**
     * 查找参会人员姓名和性别
     *
     * @param id
     * @return
     */
    ConventionPersonVo findConventionPersonNameAndSex(@Param("id") Long id);

    /**
     * 跟进BD下拉框
     *
     * @return
     */
    List<StaffBdCodeVo> getBdList(@Param("companyId") Long companyId, @Param("fkAreaRegionId")Long fkAreaRegionId);

    List<StaffBdCodeVo> getAllBdList(@Param("companyId") Long companyId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :根据姓名模糊查询对应ids
     * @Param [personName]
     * <AUTHOR>
     */
    List<Long> getPersonIdsByName(@Param("personName") String personName);

    /**
     * @return com.get.salecenter.vo.ConventionPersonVo
     * @Description :根据id查找姓名
     * @Param [id]
     * <AUTHOR>
     */
    ConventionPersonVo findPersonById(Long id);

    /**
     * @return java.util.List<com.get.salecenter.entity.ConventionPerson>
     * @Description :参会人员下拉框数据-桌台
     * @Param [id, type]
     * <AUTHOR>
     */
    List<ConventionPerson> getPersonForTableList(@Param("id") Long id, @Param("type") String type);

    /**
     * 根据峰会id和桌子类型 获取已安排桌子人员
     *
     * @Date 15:21 2021/6/29
     * <AUTHOR>
     */
    List<ConventionPerson> getPersonForTable(@Param("id") Long id, @Param("type") String type);

//    /**
//     * @return java.util.List<java.lang.String>
//     * @Description :根据bdName模糊查询bdNumList
//     * @Param [bdNameKey]
//     * <AUTHOR>
//     */
//    List<String> getBdCode(@Param("bdNameKey") String bdNameKey);
//
//    /**
//     * @return java.lang.String
//     * @Description :根据bdCode查找对应bd名称
//     * @Param [bdCode]
//     * <AUTHOR>
//     */
//    String getBdName(String bdCode);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionPersonVo>
     * @Description :筛选出桌台未被安排的人员信息
     * @Param [personIds, conventionPersonDto]
     * <AUTHOR>
     */
    List<ConventionPersonVo> getNotArrangedPersonList(IPage<ConventionPersonVo> iPage, @Param("personIds") List<Long> personIds, @Param("conventionDto") ConventionPersonDto conventionPersonDto);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionPersonVo>
     * @Description :筛选出房间未被安排的人员信息
     * @Param [allRoomIds, conventionPersonDto]
     * <AUTHOR>
     */
    List<ConventionPersonVo> getRoomNotArrangedPersonList(IPage<ConventionPersonVo> conventionPersonDtoIPage, @Param("roomIds") List<Long> roomIds, @Param("conventionDto") ConventionPersonDto conventionPersonDto);

    /**
     * @return java.lang.Boolean
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    Boolean conventionPersonIsEmpty(Long id);


    /**
     * @return java.util.List<java.lang.Long>
     * @Description :根据姓名模糊查询对应ids
     * @Param [personType]
     * <AUTHOR>
     */
    List<Long> getPersonIdsByType(@Param("personType") Integer personType);

    /**
     * @return ConventionRegistration
     * @Description :根据personId返回绑定报名名册的机构名
     * @Param [id]
     * <AUTHOR>
     */
    ConventionRegistration getInstitutionName(@Param("id") Long id);

    /**
     * @return Integer
     * @Description :获取
     * @Param [receiptCode]
     * <AUTHOR>
     */
    Integer getPersonCount(@Param("receiptCode") String receiptCode);

    /**
     * 获取参会人的展位名称
     *
     * @param personIds
     * @return
     */
    List<ConventionPersonVo> getConventionPersonBoothMap(@Param("personIds") List<Long> personIds);

    /**
     * 获取bdcode
     *
     * @param staffIds
     * @return
     */
    List<String> getBdCode(@Param("staffIds") List<Long> staffIds);

    /**
     * 获取staffIds
     *
     * @param bdCode
     * @return
     */
    Long getBdName(@Param("bdCode") String bdCode);

    /**
     * 获取ids by bdcode
     *
     * @param bdCode
     * @return
     */
    List<Long> getPersonIdsByBdCode(@Param("bdCode") String bdCode);

    /**
     * 参会人姓名和性别
     *
     * @param personIds
     * @return
     */
    List<ConventionPersonVo> findConventionPersonNameAndSexByIds(@Param("personIds") Set<Long> personIds);

    /**
     * 参会人姓名和性别
     *
     * @param conventionId
     * @return
     */
    List<ConventionPersonVo> findConventionPersonNameAndSexByConventionId(@Param("conventionId") Long conventionId, @Param("bdCodes")List<String> bdCodes);

    /**
     * 获取参会人参与晚宴的ids
     *
     * @param conventionPersonDto
     * @return
     */
    List<Long> getPersonIdsByDinnerProcedure(@Param("conventionPersonDto") ConventionPersonDto conventionPersonDto);

    /**
     * 获取参会人是否参与培训的ids
     *
     * @param conventionPersonDto
     * @return
     */
    List<Long> getPersonIdsByTrainingProcedure(@Param("conventionPersonDto") ConventionPersonDto conventionPersonDto);

    /**
     * 获取参会人没有参与培训的ids
     *
     * @param conventionPersonDto
     * @return
     */
    List<Long> getPersonIdsByNotTrainingProcedure(@Param("conventionPersonDto") ConventionPersonDto conventionPersonDto);

    /**
     * 获取参会人没有参与晚宴的ids
     *
     * @param conventionPersonDto
     * @return
     */
    List<Long> getPersonIdsByNotDinnerProcedure(@Param("conventionPersonDto") ConventionPersonDto conventionPersonDto);

    List<Long> getRepeatPerson(@Param("searchRepeatPerson") Integer searchRepeatPerson,
                               @Param("conventionId")Long conventionId,
                               @Param("tableTypeKey")String tableTypeKey);
    List<ConventionPersonVotingRankingVo>getRanking(@Param("conventionPersonVotingRankingDto") ConventionPersonVotingRankingDto conventionPersonVotingRankingDto);

    List<ConventionPersonVotingRankingVo> getRankingList();

    List<LikeCollectionActivityVo> getLikeCollectionActivityList(IPage<LikeCollectionActivityVo> iPage, @Param("data") LikeCollectionActivitySearchDto data);

    MediaAndAttachedVo getMediaAndAttached(@Param("fkTableId") Long fkTableId,@Param("typeKey") String typeKey ,@Param("fkTableName") String fkTableName);

    Set<ConventionSelectVo> getConventionSelect();

    void likeConventionApproval(@Param("id") Long id,@Param("status") Integer status);

    void likeConventionEdit(@Param("data") LikeConventionEditDto data);


    /**
     * 获取bd员工id
     * @param bdCodeList
     * @return
     */
    List<StaffBdCode> getBdNameList(@Param("bdCodeList") List<String> bdCodeList);

    List<ConventionPersonVotingRankingVo> getHelpRankingList(IPage<ConventionPersonVotingRankingVo> iPage, @Param("bdCodeList") List<String> bdCodeList, @Param("name")String name, @Param("tel") String tel);

    /**
     * 代理参会统计
     * @param agentAttendanceStatisticsDto
     * @return
     */
    List<AgentAttendanceStatisticsVo> getAgentAttendanceStatistics(@Param("agentAttendanceStatisticsDto") AgentAttendanceStatisticsDto agentAttendanceStatisticsDto);

    /**
     * 获取代理参会人绑定bd数量信息
     * @param agentAttendanceStatisticsDto
     * @return
     */
    AgentAttendanceBindingInfoVo getAgentAttendanceBindingInfo(@Param("agentAttendanceStatisticsDto") AgentAttendanceStatisticsDto agentAttendanceStatisticsDto);
}