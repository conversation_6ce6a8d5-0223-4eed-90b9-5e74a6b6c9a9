<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionAwardCodeMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.ConventionAwardCode">
    insert into m_convention_award_code (id, fk_convention_id, award_code, 
      fk_convention_person_id, pay_system_order_num, 
      pay_wechat_order_num, use_type, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkConventionId,jdbcType=BIGINT}, #{awardCode,jdbcType=VARCHAR}, 
      #{fkConventionPersonId,jdbcType=BIGINT}, #{paySystemOrderNum,jdbcType=VARCHAR}, 
      #{payWechatOrderNum,jdbcType=VARCHAR}, #{useType,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionAwardCode">
    insert into m_convention_award_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionId != null">
        fk_convention_id,
      </if>
      <if test="awardCode != null">
        award_code,
      </if>
      <if test="fkConventionPersonId != null">
        fk_convention_person_id,
      </if>
      <if test="paySystemOrderNum != null">
        pay_system_order_num,
      </if>
      <if test="payWechatOrderNum != null">
        pay_wechat_order_num,
      </if>
      <if test="useType != null">
        use_type,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionId != null">
        #{fkConventionId,jdbcType=BIGINT},
      </if>
      <if test="awardCode != null">
        #{awardCode,jdbcType=VARCHAR},
      </if>
      <if test="fkConventionPersonId != null">
        #{fkConventionPersonId,jdbcType=BIGINT},
      </if>
      <if test="paySystemOrderNum != null">
        #{paySystemOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="payWechatOrderNum != null">
        #{payWechatOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="useType != null">
        #{useType,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="deleteByStatus">
    delete from m_convention_award_code  where
      use_type is null  and fk_convention_id = #{id}
  </select>

  <select id="getConventionAwardCodes" resultType="com.get.salecenter.vo.ConventionAwardCodeVo">
       select cad.*,cp.type,cp.name_chn name from m_convention_award_code cad LEFT JOIN m_convention_person cp ON cad.fk_convention_person_id = cp.id WHERE 1=1
      <if test="conventionAwardCodeDto.awardCode!= null">
          and (cad.award_code like concat("%",#{conventionAwardCodeDto.awardCode},"%") or cp.name like concat("%",#{conventionAwardCodeDto.awardCode},"%") or cp.name_chn like concat("%",#{conventionAwardCodeDto.awardCode},"%"))
      </if>
    <if test="conventionAwardCodeDto.fkConventionId!= null">
      and cad.fk_convention_id = #{conventionAwardCodeDto.fkConventionId}
    </if>
  </select>
  <select id="getTicketsByGroleAndAwardIdNotUsed" resultType="com.get.salecenter.vo.ConventionAwardCodeVo">
    select lt.* from m_convention_award la,m_convention_award_code lt left join m_convention_person g on lt.fk_convention_person_id = g.id
    where 1=1 and g.type in(${agrole}) and la.id=#{award_id} and lt.id not in(select fk_convention_award_code_id from m_convention_award_winner) and lt.fk_convention_id = #{fkConventionId}
  </select>
  <select id="getTicketsByGroleAndAwardIdUsed" resultType="com.get.salecenter.vo.ConventionAwardCodeVo">
    select lt.* from m_convention_award la,m_convention_award_code lt left join m_convention_person g on lt.fk_convention_person_id = g.id where 1=1 and g.type in(${agrole}) and la.id=#{award_id}
          and EXISTS(select 1 from m_convention_award_winner l_w where g.id = l_w.fk_convention_person_id and l_w.fk_convention_award_code_id=lt.id
                              and l_w.fk_convention_award_id=la.id) and lt.fk_convention_id = #{fkConventionId}
  </select>
  <select id="getListTicketsNoUsedByAwardId" resultType="com.get.salecenter.vo.TicketsNoUsedVo">
    select lt.id id ,lt.award_code awardCode from m_convention_award_code lt left join m_convention_person g on lt.fk_convention_person_id=g.id
    where 1=1 and  g.type in(${roles})
      and lt.fk_convention_person_id is not null and lt.use_type = 1
      and lt.id not in (select fk_convention_award_code_id from  m_convention_award_winner )
      <if test="personId!=null and personId!=''">
        and lt.fk_convention_person_id not in (${personId})
      </if>
  </select>
  <select id="getById" resultType="com.get.salecenter.entity.ConventionAwardCode">
     select * from m_convention_award_code where id =#{id}
  </select>
  <select id="getBySystemCode" resultType="com.get.salecenter.entity.ConventionAwardCode">
      select award_code from m_convention_award_code where pay_system_order_num = #{num}
  </select>
  <select id="getCodeByAwardAndType" parameterType="java.lang.Long" resultType="com.get.salecenter.vo.TicketsNoUsedVo">
        select id id ,award_code awardCode from m_convention_award_code
where fk_convention_person_id =#{id} and use_type = 1

  </select>

  <select id="getLuckyNumber" resultType="com.get.salecenter.entity.ConventionAwardCode">
    SELECT
      mcac.*
    FROM
      m_convention_award_code AS mcac
        INNER JOIN m_convention_person AS mcp ON mcp.id = mcac.fk_convention_person_id
    where mcac.fk_convention_id = #{fkConventionId}
    <if test="getRole != null">
      AND mcp.type IN (${getRole})
    </if>
    <if test="awardCodeIds != null and awardCodeIds.size > 0">
      AND mcac.id NOT IN
      <foreach collection="awardCodeIds" item="awardCodeId" index="index" open="(" separator="," close=")">
        #{awardCodeId}
      </foreach>
    </if>
    AND use_type = 1
  </select>

  <select id="getCheatingNumber" resultType="com.get.salecenter.entity.ConventionAwardCode">
    SELECT
      mcac.*
    FROM
      m_convention_award_code AS mcac
        INNER JOIN m_convention_person AS mcp ON mcp.id = mcac.fk_convention_person_id
    where mcac.fk_convention_id = #{fkConventionId}
      AND mcp.id IN (${getFkConventionPersonId})
      AND use_type = 1
  </select>
</mapper>