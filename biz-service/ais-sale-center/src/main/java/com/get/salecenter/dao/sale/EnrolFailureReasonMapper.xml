<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EnrolFailureReasonMapper">
    <select id="getMaxViewOrder" resultType="java.lang.Integer">
        select ifnull(max(view_order)+1,1) from u_enrol_failure_reason
      </select>

    <select id="getStudentsFailureState" resultType="com.get.salecenter.vo.EnrolFailureReasonVo">
        SELECT
        COUNT( DISTINCT a.itemId ) AS itemNum,
        a.reason_name AS reasonName,
        a.reason_key AS reasonKey,
        a.reasonId AS id
        FROM
        (
        SELECT
        soi.id AS itemId,
        efr.id AS reasonId,
        efr.reason_name,
        efr.reason_key,
        usois.step_key
        FROM
        u_enrol_failure_reason AS efr
        LEFT JOIN m_student_offer_item AS soi ON efr.id = soi.fk_enrol_failure_reason_id AND soi.status = 1
        LEFT JOIN u_student_offer_item_step AS usois ON usois.id = soi.fk_student_offer_item_step_id
        LEFT JOIN m_student_offer AS so ON so.id = soi.fk_student_offer_id AND so.status = 1
        LEFT JOIN m_student AS s ON s.id = so.fk_student_id
        WHERE usois.step_key = 'STEP_FAILURE'
        AND soi.fk_area_country_id  IN
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
        </foreach>
        <if test="companyId != null">
            AND s.fk_company_id = #{companyId}
        </if>
        <if test="staffId != null">
            AND so.fk_staff_id = #{staffId}
        </if>
        <if test="areaCountryIds != null and areaCountryIds.size()>0">
            AND so.fk_area_country_id IN
            <foreach collection="areaCountryIds" item="areaCountryId" open="(" separator="," close=")">
                #{areaCountryId}
            </foreach>
        </if>
        <if test="beginTime != null">
            AND ( DATE_FORMAT( soi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginTime}, '%Y-%m-%d' ))
        </if>
        <if test="endTime != null">
            AND ( DATE_FORMAT( soi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endTime}, '%Y-%m-%d' ))
        </if>
        AND (
        1=1
        <if test="studentIds != null">
            AND s.id IN
            <foreach collection="studentIds" item="studentId" index="index" open="(" separator=","
                     close=")">
                #{studentId}
            </foreach>
            OR s.id IS NULL
        </if>
        <if test="userNames != null and userNames.size() > 0">
            OR s.gmt_create_user IN
            <foreach collection="userNames" item="userName" open="(" separator="," close=")">
                #{userName}
            </foreach>
        </if>
        )
        ) a
        GROUP BY
        a.reasonId
    </select>
</mapper>