<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.salecenter.dao.occ.OccMapper">


    <select id="getData" resultType="com.get.financecenter.vo.OccVo">
         select * from ROAD.FINANCIALDATE where PAYABLE_FIXED_AMOUNT>0 and type='应付'
    </select>

    <select id="getDataByInfo" resultType="com.get.financecenter.vo.OccVo">
         select * from ROAD.FINANCIALDATE where COURSEID=#{courseId} AND to_char(GMT_PAYABLE_CREATE_TIME,'yyyy-MM-dd')=#{createTime} and type='应付'
    </select>

    <select id="batchGetByCourseId" resultType="com.get.financecenter.vo.OccVo">
        select * from ROAD.FINANCIALDATE where COURSEID in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getDataById" resultType="com.get.financecenter.vo.OccVo" parameterType="String">
        select * from ROAD.FINANCIALDATE where COURSEID=#{courseId}
    </select>

    <select id="getOcByCourseId" resultType="com.get.financecenter.vo.OccVo" parameterType="String">
        select * from ROAD.FINANCIALDATE where COURSEID=#{courseId} and TYPE=#{type} ORDER BY CREATETIME DESC
    </select>

    <select id="getAllCourseId" resultType="java.lang.String">
        select distinct COURSEID from ROAD.FINANCIALDATE
    </select>

    <select id="getUptoRecord" resultType="com.get.financecenter.vo.OccVo">
        select * from  ROAD.FINANCIALDATE WHERE  TYPE=#{type} and COURSEID=#{courseId}  and rownum=1 order by GMT_RECEIVABLE_CREATE_TIME desc
    </select>

    <select id="getLastPayableRecord" resultType="com.get.financecenter.vo.OccVo">
        select * from  ROAD.FINANCIALDATE WHERE  TYPE=#{type} and COURSEID=#{courseId}  and rownum=1 order by GMT_PAYABLE_CREATE_TIME desc
    </select>

    <select id="getErrorCourseId" resultType="java.lang.String" parameterType="int">
        select distinct COURSEID from ROAD.FINANCIALDATE where STATE=#{state}
    </select>

    <update id="updateRecord">
        update ROAD.FINANCIALDATE set STATE=#{state},BACK_UP=#{remarks} where ID=#{id}
    </update>


    <update id="updateState">
        update ROAD.FINANCIALDATE set STATE=#{state} where ID=#{id}
    </update>

    <select id="getAppdixList" resultType="com.get.salecenter.vo.AgencyAppdixVo">
         --select * from AGENCYAGREEMENT where 1=1 order by agencyid asc
        select * from (
        select * from AGENCYAGREEMENT where 1=1 order by agencyid asc
        )where rownum &lt; 100
    </select>
    <select id="getAllNewReceivedPay" resultType="com.get.financecenter.vo.OccVo">
        select * from ROAD.FINANCIALDATE where BACK_UP IS NOT NULL and type='实付'
    </select>

    <select id="getListAttactment" resultType="com.get.salecenter.vo.AgencyAppdixVo">
        select * from (select * from agencyagreement where agencyid in(
        select agencyid from agencyagreement where agencyid not in(
        select agencyid from agencyagreement group by agencyid,endtm
        having count(endtm)>1
        )group by agencyid
        )order by agencyid asc )where rownum &lt; 10086
    </select>


    <select id="getDataByIds" resultType="com.get.financecenter.vo.OccVo">
                select * from ROAD.FINANCIALDATE where COURSEID in
                <foreach collection="courseIds" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
    </select>
</mapper>
