package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.AgentRoleStaffVo;
import com.get.salecenter.vo.AgentVo;
import com.get.salecenter.entity.AgentRoleStaff;
import com.get.salecenter.dto.AgentRoleStaffDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AgentRoleStaffMapper extends GetMapper<AgentRoleStaff> {
    int insert(AgentRoleStaff record);

    int insertSelective(AgentRoleStaff record);

    int updateByPrimaryKey(AgentRoleStaff record);

    /**
     * 代理项目成员配置管理列表
     *
     * @Date 12:43 2021/8/2
     * <AUTHOR>
     */
    List<AgentVo> getAgentRoleStaffs(IPage<AgentVo> iPage, @Param("agentRoleStaffDto") AgentRoleStaffDto agentRoleStaffDto);

    List<Long> getCountryIds();

    /**
     * 代理项目成员配置详情
     *
     * @Date 12:13 2021/8/26
     * <AUTHOR>
     */
    List<AgentRoleStaff> selectAgentRoleStaffInfo(@Param("agentId") Long agentId,
                                                  @Param("countryId") Long countryId,
                                                  @Param("companyId") Long companyId,
                                                  @Param("fkTypeKey") String fkTypeKey);

    /**
     * 通用项目成员配置列表
     *
     * @param agentRoleStaffDto
     * @return
     */
    List<AgentRoleStaffVo> getCommonAgentRoleStaffs(IPage<AgentRoleStaffVo> iPage, AgentRoleStaffDto agentRoleStaffDto);

    /**
     * 代理项目成员配置详情
     *
     * @param agentId
     * @param countryId
     * @param companyId
     * @param fkTypeKey
     * @param agentCountryId
     * @param agentStateId
     * @return
     */
    List<AgentRoleStaff> selectAgentRoleStaff(@Param("agentId") Long agentId,
                                              @Param("countryId") Long countryId,
                                              @Param("companyId") Long companyId,
                                              @Param("fkTypeKey") String fkTypeKey,
                                              @Param("agentCountryId") Long agentCountryId,
                                              @Param("agentStateId") Long agentStateId,
                                              @Param("roleId") Long roleId);
}