<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentProjectRoleStaffMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.StudentProjectRoleStaff">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_table_name" jdbcType="VARCHAR" property="fkTableName"/>
        <result column="fk_table_id" jdbcType="BIGINT" property="fkTableId"/>
        <result column="fk_student_project_role_id" jdbcType="BIGINT" property="fkStudentProjectRoleId"/>
        <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId"/>
        <result column="is_active" jdbcType="BIT" property="isActive"/>
        <result column="active_date" jdbcType="TIMESTAMP" property="activeDate"/>
        <result column="unactive_date" jdbcType="TIMESTAMP" property="unactiveDate"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , fk_table_name, fk_table_id, fk_student_project_role_id, fk_staff_id, is_active,
        active_date, unactive_date, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>

    <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentProjectRoleStaff" keyProperty="id"
            useGeneratedKeys="true">
        insert into s_student_project_role_staff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkTableName != null">
                fk_table_name,
            </if>
            <if test="fkTableId != null">
                fk_table_id,
            </if>
            <if test="fkStudentProjectRoleId != null">
                fk_student_project_role_id,
            </if>
            <if test="fkStaffId != null">
                fk_staff_id,
            </if>
            <if test="isActive != null">
                is_active,
            </if>
            <if test="activeDate != null">
                active_date,
            </if>
            <if test="unactiveDate != null">
                unactive_date,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkTableName != null">
                #{fkTableName,jdbcType=VARCHAR},
            </if>
            <if test="fkTableId != null">
                #{fkTableId,jdbcType=BIGINT},
            </if>
            <if test="fkStudentProjectRoleId != null">
                #{fkStudentProjectRoleId,jdbcType=BIGINT},
            </if>
            <if test="fkStaffId != null">
                #{fkStaffId,jdbcType=BIGINT},
            </if>
            <if test="isActive != null">
                #{isActive,jdbcType=BIT},
            </if>
            <if test="activeDate != null">
                #{activeDate,jdbcType=TIMESTAMP},
            </if>
            <if test="unactiveDate != null">
                #{unactiveDate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <insert id="batchInsertSelective" parameterType="java.util.List" keyProperty="id" useGeneratedKeys="true">
        insert into s_student_project_role_staff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="list[0].id != null">
                id,
            </if>
            <if test="list[0].fkTableName != null">
                fk_table_name,
            </if>
            <if test="list[0].fkTableId != null">
                fk_table_id,
            </if>
            <if test="list[0].fkStudentProjectRoleId != null">
                fk_student_project_role_id,
            </if>
            <if test="list[0].fkStaffId != null">
                fk_staff_id,
            </if>
            <if test="list[0].isActive != null">
                is_active,
            </if>
            <if test="list[0].activeDate != null">
                active_date,
            </if>
            <if test="list[0].unactiveDate != null">
                unactive_date,
            </if>
            <if test="list[0].gmtCreate != null">
                gmt_create,
            </if>
            <if test="list[0].gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="list[0].gmtModified != null">
                gmt_modified,
            </if>
            <if test="list[0].gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        values
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="item.id != null">
                    #{item.id,jdbcType=BIGINT},
                </if>
                <if test="item.fkTableName != null">
                    #{item.fkTableName,jdbcType=VARCHAR},
                </if>
                <if test="item.fkTableId != null">
                    #{item.fkTableId,jdbcType=BIGINT},
                </if>
                <if test="item.fkStudentProjectRoleId != null">
                    #{item.fkStudentProjectRoleId,jdbcType=BIGINT},
                </if>
                <if test="item.fkStaffId != null">
                    #{item.fkStaffId,jdbcType=BIGINT},
                </if>
                <if test="item.isActive != null">
                    #{item.isActive,jdbcType=BIT},
                </if>
                <if test="item.activeDate != null">
                    #{item.activeDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.unactiveDate != null">
                    #{item.unactiveDate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.gmtCreate != null">
                    #{item.gmtCreate,jdbcType=TIMESTAMP},
                </if>
                <if test="item.gmtCreateUser != null">
                    #{item.gmtCreateUser,jdbcType=VARCHAR},
                </if>
                <if test="item.gmtModified != null">
                    #{item.gmtModified,jdbcType=TIMESTAMP},
                </if>
                <if test="item.gmtModifiedUser != null">
                    #{item.gmtModifiedUser,jdbcType=VARCHAR},
                </if>
            </trim>
        </foreach>
    </insert>



    <select id="validateEdit" resultType="java.lang.Integer">
        SELECT count(*) record
        from s_student_project_role_staff
        where fk_table_name = #{fkTableName}
          and fk_table_id = #{fkTableId}
          and fk_student_project_role_id = #{fkStudentProjectRoleId}
          and fk_staff_id = #{fkStaffId}
          and is_active = 1 LIMIT 1
    </select>

    <select id="getStudetnIdsByStaffId" resultType="java.lang.Long">
        SELECT so.fk_student_id FROM s_student_project_role_staff AS sprs
        INNER JOIN m_student_offer AS so ON so.id = sprs.fk_table_id
        WHERE sprs.is_active = 1
        AND sprs.fk_table_name='m_student_offer'
        <if test="staffIds">
            AND sprs.fk_staff_id IN
            <foreach collection="staffIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="getStudentAndStaffByProjectRoleId" resultType="java.util.Map">
        select mso.fk_student_id AS fkStudentId
        from m_student_offer mso
        join s_student_project_role_staff ssprs on mso.id = ssprs.fk_table_id
        where mso.status = 1 and ssprs.is_active = 1
        <if test="projectRoleId != null">
            and ssprs.fk_student_project_role_id = #{projectRoleId}
        </if>
        <if test="fkStaffIds != null">
            and ssprs.fk_staff_id in
            <foreach collection="fkStaffIds" item="fkStaffId" index="index" open="(" separator="," close=")">
                #{fkStaffId}
            </foreach>
        </if>
    </select>
    <select id="selectProjectStaff" resultType="com.get.salecenter.vo.StudentProjectRoleStaffVo">
        SELECT
        spr.id as fkRoleId,
        GROUP_CONCAT( DISTINCT sprs.fk_staff_id ORDER BY ms.id) AS staffIdStr,
        GROUP_CONCAT( DISTINCT CASE WHEN IFNULL(ms.name_en,'')='' THEN ms.`name` ELSE CONCAT(ms.`name`,'（',ms.name_en,'）') END ORDER BY ms.id) AS staffName,
        spr.role_name,sprs.fk_table_id as fkTableId
        FROM
        s_student_project_role_staff AS sprs
        INNER JOIN u_student_project_role AS spr ON spr.id = sprs.fk_student_project_role_id
        LEFT JOIN ais_permission_center.m_staff ms on ms.id = sprs.fk_staff_id
        WHERE
        sprs.is_active = 1
        <if test="key!=null and key!=''">
            AND sprs.fk_table_name = #{key}
        </if>
        <if test="itemIdList!=null and itemIdList.size>0">
            AND sprs.fk_table_id IN
            <foreach collection="itemIdList" item="itemId" index="index" open="(" separator="," close=")">
                #{itemId,jdbcType=BIGINT}
            </foreach>
        </if>
        GROUP BY
        spr.id,sprs.fk_table_id
    </select>
    <select id="getStudentProjectRoleByIdSet" resultType="com.get.salecenter.entity.StudentProjectRole">
        select uspr.id, uspr.fk_company_id fkCompanyId, CONCAT("【",mc.name,"】"," ", uspr.role_name, "（", uspr.role_key ,"）") roleName,
        uspr.role_key roleKey, uspr.department_num departmentNum, uspr.remark,
        uspr.view_order viewOrder, uspr.gmt_create gmtCreate, uspr.gmt_create_user gmtCreateUser,
        uspr.gmt_modified gmtModified, uspr.gmt_modified_user gmtModifiedUser
        from u_student_project_role AS uspr
        LEFT JOIN ais_permission_center.m_company AS mc ON mc.id = uspr.fk_company_id
        where uspr.id in
        <foreach collection="projectRoleIdSet" item="itemId" index="index" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>
    <select id="getStudentProjectConsultantRoleByIdSet" resultType="com.get.salecenter.entity.StudentProjectRole">
        select id, fk_company_id fkCompanyId, role_name roleName,
        role_key roleKey, department_num departmentNum, remark,
        view_order viewOrder, gmt_create gmtCreate, gmt_create_user gmtCreateUser,
        gmt_modified gmtModified, gmt_modified_user gmtModifiedUser from u_student_project_role where 1=1 and (role_key like '%_RC%' or role_key like
        '%_ARC%') and
        id in
        <foreach collection="projectRoleIdSet" item="itemId" index="index" open="(" separator="," close=")">
            #{itemId}
        </foreach>
    </select>
    <select id="getProjectRole" resultType="com.get.salecenter.vo.StudentProjectRoleStaffVo">
        select distinct t1.*,t2.role_name as roleName,t2.role_key as roleKey from s_student_project_role_staff t1
        left join u_student_project_role t2
        on t1.fk_student_project_role_id = t2.id
        where fk_table_name='m_student_offer' and fk_table_id=#{fkStudentOfferId}
        and is_active=1
        <if test="staffIds!=null">
            and  fk_staff_id in
            <foreach collection="staffIds" item="staffId" open="(" separator="," close=")">
                #{staffId}
            </foreach>
        </if>
    </select>
    <select id="getProjectRoleStaff" resultType="com.get.salecenter.entity.StudentProjectRoleStaff">
        SELECT
        a.id,
        a.gmt_create,
        a.gmt_modified,
        a.gmt_create_user,
        a.gmt_modified_user,
        a.fk_table_name,
        a.fk_table_id,
        a.fk_student_project_role_id,
        a.fk_staff_id,
        a.is_active,
        a.active_date,
        a.unactive_date
        FROM
        s_student_project_role_staff a

        <if test="!isStudentAdmin">
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.offerPermissionSql"/>
            ) z ON a.fk_table_id=z.id
        </if>
        INNER JOIN u_student_project_role upr ON upr.id = a.fk_student_project_role_id
        WHERE 1=1
        <if test="projectRoleStaffDto.fkStudentId !=null and projectRoleStaffDto.fkStudentId !=''">
            AND (( a.fk_table_id in
            <foreach collection="projectRoleStaffDto.offerIds" item="offerId" index="index" open="(" separator="," close=")">
                #{offerId}
            </foreach>
            AND a.fk_table_name = "m_student_offer")
            OR ( a.fk_table_id IN
            <foreach collection="projectRoleStaffDto.accommodationIds" item="accommodationId" index="index" open="(" separator="," close=")">
                #{accommodationId}
            </foreach>
            AND a.fk_table_name = "m_student_accommodation" )
            OR ( a.fk_table_id IN
            <foreach collection="projectRoleStaffDto.insuranceIds" item="insuranceId" index="index" open="(" separator="," close=")">
                #{insuranceId}
            </foreach>
            AND a.fk_table_name = "m_student_insurance" ))
        </if>
        <if test="projectRoleStaffDto.fkStaffId !=null and projectRoleStaffDto.fkStaffId !=''">
            AND a.fk_staff_id = #{projectRoleStaffDto.fkStaffId}
        </if>
        <if test="projectRoleStaffDto.fkStudentProjectRoleId !=null and projectRoleStaffDto.fkStudentProjectRoleId !=''">
            AND a.fk_student_project_role_id = #{projectRoleStaffDto.fkStudentProjectRoleId}
        </if>
        <if test="projectRoleStaffDto.fkTableName !=null and projectRoleStaffDto.fkTableName !=''">
            AND a.fk_table_name = #{projectRoleStaffDto.fkTableName}
        </if>
        ORDER BY upr.view_order DESC
    </select>

    <select id="getClientProjectRoleStaff" resultType="com.get.salecenter.entity.StudentProjectRoleStaff">
        SELECT
        a.id,
        a.gmt_create,
        a.gmt_modified,
        a.gmt_create_user,
        a.gmt_modified_user,
        a.fk_table_name,
        a.fk_table_id,
        a.fk_student_project_role_id,
        a.fk_staff_id,
        a.is_active,
        a.active_date,
        a.unactive_date
        FROM
        s_student_project_role_staff a

        INNER JOIN (
        SELECT DISTINCT a.id FROM (
        <!-- 项目成员的权限(咨询客户) -->
        SELECT a.id FROM ais_sale_center.m_client_offer a
        INNER JOIN ais_sale_center.s_student_project_role_staff c
        ON a.id=c.fk_table_id AND c.fk_table_name='m_client_offer' AND c.is_active=1 AND a.status=1
        <if test="staffFollowerIds != null and staffFollowerIds.size()>0">
            AND c.fk_staff_id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
        </if>
        UNION ALL
        -- #BD的权限
        SELECT a.id FROM ais_sale_center.m_client_offer a
        INNER JOIN ais_sale_center.r_client_agent b ON a.fk_client_id=b.fk_client_id AND b.is_active = 1 AND a.status=1
        INNER JOIN ais_sale_center.r_agent_staff c ON b.fk_agent_id=c.fk_agent_id AND c.is_active = 1  AND c.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        WHERE 1=1
        UNION ALL
        -- #项目成员的权限
        SELECT a.id FROM ais_sale_center.m_client_offer a
        INNER JOIN ais_sale_center.s_student_project_role_staff b ON a.id=b.fk_table_id AND b.fk_table_name='m_client_offer' AND b.is_active=1 AND b.fk_staff_id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        WHERE 1=1
        UNION ALL
        -- #申请方案创建人的权限
        SELECT a.id FROM ais_sale_center.m_client_offer a
        INNER JOIN ais_permission_center.m_staff b ON a.gmt_create_user=b.login_id AND a.status=1 AND b.id IN
        <foreach collection="staffFollowerIds" item="staffFollowerId" index="index" open="(" separator="," close=")">
            #{staffFollowerId}
        </foreach>
        ) a
        ) z ON a.fk_table_id=z.id
        WHERE 1=1
        <if test="projectRoleStaffDto.fkStudentId !=null and projectRoleStaffDto.fkStudentId !=''">
            AND (( a.fk_table_id in
            <foreach collection="projectRoleStaffDto.clientOfferIds" item="offerId" index="index" open="(" separator="," close=")">
                #{offerId}
            </foreach>
            AND a.fk_table_name = "m_client_offer"))
        </if>
        <if test="projectRoleStaffDto.fkStaffId !=null and projectRoleStaffDto.fkStaffId !=''">
            AND a.fk_staff_id = #{projectRoleStaffDto.fkStaffId}
        </if>
        <if test="projectRoleStaffDto.fkStudentProjectRoleId !=null and projectRoleStaffDto.fkStudentProjectRoleId !=''">
            AND a.fk_student_project_role_id = #{projectRoleStaffDto.fkStudentProjectRoleId}
        </if>
        <if test="projectRoleStaffDto.fkTableName !=null and projectRoleStaffDto.fkTableName !=''">
            AND a.fk_table_name = #{projectRoleStaffDto.fkTableName}
        </if>
    </select>

    <select id="getProjectRoleStaffNoPage" resultType="java.util.Map">
        select mso.id,GROUP_CONCAT(distinct CASE
        WHEN IFNULL(ms.name, '') = '' THEN
        ms.name_en
        ELSE
        CONCAT(
        ms.name,
        '(',ms.name_en,')'
        )
        END ORDER BY uspr.view_order DESC)name from s_student_project_role_staff ssprs
        left join u_student_project_role uspr on ssprs.fk_student_project_role_id = uspr.id
        left join ais_permission_center.m_staff ms on ms.id = ssprs.fk_staff_id
        left join m_student_offer mso on mso.id = ssprs.fk_table_id
        where 1=1
        <if test="studentIds != null and studentIds.size()>0 and studentIds.size() &lt; 9000">
            AND mso.fk_student_id IN
            <foreach collection="studentIds" item="studentId" index="index" open="(" separator="," close=")">
                #{studentId,jdbcType=BIGINT}
            </foreach>
        </if>
        <if test="fkTableName !=null and fkTableName !=''">
            AND ssprs.fk_table_name = #{fkTableName}
        </if>
        and ssprs.is_active = 1
        group by mso.id
    </select>

    <select id="getAllProjectRoleStaff" resultType="com.get.salecenter.vo.StudentProjectRoleStaffVo">
        SELECT
        id,
        gmt_create,
        gmt_modified,
        gmt_create_user,
        gmt_modified_user,
        fk_table_name,
        fk_table_id,
        fk_student_project_role_id,
        fk_staff_id,
        is_active,
        active_date,
        unactive_date
        FROM
        s_student_project_role_staff
        <where>
            <if test="fkStudentOfferId != null">
                fk_table_id=#{fkStudentOfferId}
            </if>
            AND fk_table_name = 'm_student_offer'
            AND is_active = 1
        </where>
    </select>
    <select id="selectProjectStaffByStudentId" resultType="com.get.salecenter.vo.StudentProjectRoleStaffVo">
        SELECT
            x.*
        FROM
            (
                SELECT
                    sprs.fk_staff_id,
                    spr.role_name,
                    spr.view_order
                FROM
                    s_student_project_role_staff AS sprs
                        INNER JOIN u_student_project_role AS spr ON spr.id = sprs.fk_student_project_role_id
                        AND sprs.fk_table_name = 'm_student_offer'
                        AND sprs.is_active = 1
                        INNER JOIN m_student_offer AS mso ON mso.id = sprs.fk_table_id
                WHERE
                    mso.fk_student_id = #{studentId} and mso.`status` != 0
                UNION
                SELECT
                    sprs.fk_staff_id,
                    spr.role_name,
                    spr.view_order
                FROM
                    s_student_project_role_staff AS sprs
                        INNER JOIN u_student_project_role AS spr ON spr.id = sprs.fk_student_project_role_id
                        AND sprs.fk_table_name = 'm_student_accommodation'
                        AND sprs.is_active = 1
                        INNER JOIN m_student_accommodation AS msa ON msa.id = sprs.fk_table_id
                WHERE
                    msa.fk_student_id = #{studentId} and msa.`status` != 0
                UNION
                SELECT
                    sprs.fk_staff_id,
                    spr.role_name,
                    spr.view_order
                FROM
                    s_student_project_role_staff AS sprs
                        INNER JOIN u_student_project_role AS spr ON spr.id = sprs.fk_student_project_role_id
                        AND sprs.fk_table_name = 'm_student_insurance'
                        AND sprs.is_active = 1
                        INNER JOIN m_student_insurance AS msi ON msi.id = sprs.fk_table_id
                WHERE
                    msi.fk_student_id = #{studentId} and msi.`status` != 0
            ) x
        GROUP BY
            x.fk_staff_id,
            x.role_name
        ORDER BY x.view_order DESC
    </select>
    <select id="getItemIdByRoleKey" resultType="com.get.salecenter.vo.StudentProjectRoleStaffVo">
        SELECT
            *
        FROM
            s_student_project_role_staff AS sprs
                INNER JOIN
                u_student_project_role AS spr
                ON
                        sprs.fk_student_project_role_id = spr.id AND

                        spr.role_key like concat(#{roleKey},"%")

                        and sprs.fk_table_name = 'm_student_offer'
            WHERE sprs.fk_table_id in
            <foreach collection="tableIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
    </select>
    <select id="getStaffIdByRoleKey" resultType="com.get.salecenter.vo.StudentProjectRoleStaffVo">
        SELECT
        a.fk_table_id,
        a.fk_staff_id
        FROM
        s_student_project_role_staff a
        INNER JOIN u_student_project_role AS b ON a.fk_student_project_role_id = b.id AND a.fk_table_name = 'm_student_offer' AND a.is_active = 1
        <if test="roleKey == 'RC'">
            and b.role_key in ("GEA_RC","GEA_RC_UK","GEA_RC_AU","GEA_RC_US","GEA_RC_UK_2")
        </if>
        <if test="roleKey == 'ARC'">
            and b.role_key in ("GEA_ARC","GEA_ARC_UK","GEA_ARC_AU","GEA_ARC_US","UK2_ARC")
        </if>
        <if test="roleKey == 'LI'">
            and b.role_key in ("GEA_LI","GEA_LI_UK","GEA_LI_AU","GEA_LI_US")
        </if>
        <if test="roleKey == 'AD'">
            and b.role_key in ("GEA_AD","GEA_AD_UK","GEA_AD_AU","GEA_AD_US","UK2_AD")
        </if>
        <if test="roleKey == 'CM'">
            and b.role_key in ("GEA_CM","GEA_CM_UK","GEA_CM_AU","GEA_CM_US")
        </if>
        <!--iae外联-->
        <if test="roleKey == 'IAELI'">
            and b.role_key in ("HTI_COORDINATOR")
        </if>
        <!--iae咨询支持-->
        <if test="roleKey == 'IAESUP'">
            and b.role_key in ("HTI_COUNSELLING_SUPPORT")
        </if>
        <!--iae文案-->
        <if test="roleKey == 'IAEAD'">
            and b.role_key in ("HTI_ASSISTANT")
        </if>
        <!--INDEX咨询主管-->
        <if test="roleKey == 'INDEXCM'">
            and b.role_key in ("INDEX_CM")
        </if>
        <!--INDEX跟进顾问-->
        <if test="roleKey == 'INDEXARC'">
            and b.role_key in ("INDEX_ARC")
        </if>
        <!--sea Consultant-->
        <if test="roleKey == 'SEAARC'">
            and b.role_key in ("SEA_ARC","SEAMYS_ARC","SEAVNM_ARC")
        </if>
        <!--sea Consultant Manager-->
        <if test="roleKey == 'SEACM'">
            and b.role_key in ("SEA_CM","SEAMYS_CM","SEAVNM_CM")
        </if>
        where a.fk_table_id in
        <foreach collection="tableIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>

    </select>

    <select id="selectListRoleByIds" resultType="com.get.salecenter.vo.StudentOfferRoleAndStaffVo">
        SELECT
        s.*,r.role_name,r.role_key
        FROM
        s_student_project_role_staff s
        left join u_student_project_role r on s.fk_student_project_role_id = r.id
        WHERE
        s.fk_table_name = 'm_student_offer'
        AND is_active = 1 and (r.role_key like '%_RC%' or r.role_key like '%_ARC%' or r.role_key like '%_AD%'
        or r.role_key like '%_COUNSELLING_SUPPORT%'
        or r.role_key like '%_COORDINATOR%')
        AND s.fk_table_id in
        <foreach collection="offerIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectListRoleByClientIds" resultType="com.get.salecenter.vo.StudentOfferRoleAndStaffVo">
        SELECT
        s.*,r.role_name,r.role_key
        FROM
        s_student_project_role_staff s
        left join u_student_project_role r on s.fk_student_project_role_id = r.id
        WHERE
        s.fk_table_name = 'm_client_offer'
        AND is_active = 1 and (r.role_key like '%_RC%' or r.role_key like '%_ARC%')
        AND s.fk_table_id in
        <foreach collection="offerIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectProjectStaffByClientId" resultType="com.get.salecenter.vo.ClientProjectRoleStaffVo">
        SELECT
        x.*
        FROM
        (
        SELECT
        sprs.fk_staff_id,
        spr.role_name,
        spr.view_order
        FROM
        s_student_project_role_staff AS sprs
        INNER JOIN u_student_project_role AS spr ON spr.id = sprs.fk_student_project_role_id
        AND sprs.fk_table_name = 'm_client_offer'
        AND sprs.is_active = 1
        INNER JOIN m_client_offer AS mso ON mso.id = sprs.fk_table_id
        WHERE
        mso.fk_client_id = #{clientId}
        ) x
        GROUP BY
        x.fk_staff_id,
        x.role_name
        ORDER BY x.view_order DESC
    </select>
    <select id="getAFollowUpConsultant" resultType="com.get.salecenter.vo.SelItem">
        select mso.id as keyId,GROUP_CONCAT(distinct CASE
        WHEN IFNULL(ms.name, '') = '' THEN
        ms.name_en
        ELSE
        CONCAT(
        ms.name,
        '(',ms.name_en,')'
        )
        END ) as val from s_student_project_role_staff ssprs
        left join u_student_project_role uspr on ssprs.fk_student_project_role_id = uspr.id
        left join ais_permission_center.m_staff ms on ms.id = ssprs.fk_staff_id
        left join m_client_offer mso on mso.id = ssprs.fk_table_id
        where 1=1
        <if test="clientIds!=null and clientIds.size()>0">
            AND mso.fk_client_id IN
            <foreach collection="clientIds" item="cid" index="index" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
        <if test="key!=null and key!=''">
            AND uspr.role_key = #{key}
        </if>
        <if test="fkTableName !=null and fkTableName !=''">
            AND ssprs.fk_table_name = #{fkTableName}
        </if>
        and ssprs.is_active = 1
        group by mso.id
    </select>
    <select id="selectProjectStaffById" resultType="com.get.salecenter.vo.StudentProjectRoleStaffVo">
        SELECT
        sprs.fk_student_project_role_id,
        sprs.fk_staff_id AS fkStaffId,
        CASE
        WHEN IFNULL(ms.name_en, '') = '' THEN
        ms.`name`
        ELSE
        CONCAT(
        ms.`name`,
        '（',
        ms.name_en,
        '）'
        )
        END staffName,
        spr.role_name,
        sprs.fk_table_id AS fkTableId
        FROM
        s_student_project_role_staff AS sprs
        INNER JOIN u_student_project_role AS spr ON spr.id = sprs.fk_student_project_role_id
        INNER JOIN ais_permission_center.m_staff ms on ms.id = sprs.fk_staff_id
        WHERE
        sprs.is_active = 1
        <if test="key!=null and key!=''">
            AND sprs.fk_table_name = #{key}
        </if>
        <if test="fkTableId!=null and fkTableId!=''">
            AND sprs.fk_table_id = #{fkTableId}
        </if>
        GROUP BY
        sprs.id
    </select>
    <select id="selectProjectStaffByIds" resultType="com.get.salecenter.vo.StudentProjectRoleStaffVo">
        SELECT
        sprs.fk_student_project_role_id,
        sprs.fk_staff_id AS fkStaffId,
        CASE
        WHEN IFNULL(ms.name_en, '') = '' THEN
        ms.`name`
        ELSE
        CONCAT(
        ms.`name`,
        '（',
        ms.name_en,
        '）'
        )
        END staffName,
        spr.role_name,
        sprs.fk_table_id AS fkTableId
        FROM
        s_student_project_role_staff AS sprs
        INNER JOIN u_student_project_role AS spr ON spr.id = sprs.fk_student_project_role_id
        INNER JOIN ais_permission_center.m_staff ms on ms.id = sprs.fk_staff_id
        WHERE
        sprs.is_active = 1
        <if test="key!=null and key!=''">
            AND sprs.fk_table_name = #{key}
        </if>
        <if test="itemIdList!=null and itemIdList.size>0">
            AND sprs.fk_table_id IN
            <foreach collection="itemIdList" item="itemId" index="index" open="(" separator="," close=")">
                #{itemId,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>
    <select id="selectProjectStaffByClientIds" resultType="com.get.salecenter.vo.ClientProjectRoleStaffVo">
        SELECT
            x.*
        FROM
            (
                SELECT
                    sprs.fk_table_id,
                    sprs.fk_staff_id,
                    spr.role_name,
                    spr.view_order
                FROM
                    s_student_project_role_staff AS sprs
                        INNER JOIN u_student_project_role AS spr ON spr.id = sprs.fk_student_project_role_id
                        AND sprs.fk_table_name = 'm_client_offer'
                        AND sprs.is_active = 1
                        INNER JOIN m_client_offer AS mso ON mso.id = sprs.fk_table_id
                WHERE 1=1
                <if test="clientIds!=null and clientIds.size>0">
                    AND mso.fk_client_id IN
                    <foreach collection="clientIds" item="clientId" index="index" open="(" separator="," close=")">
                        #{clientId,jdbcType=BIGINT}
                    </foreach>
                </if>
            ) x

        ORDER BY x.view_order DESC
    </select>
</mapper>