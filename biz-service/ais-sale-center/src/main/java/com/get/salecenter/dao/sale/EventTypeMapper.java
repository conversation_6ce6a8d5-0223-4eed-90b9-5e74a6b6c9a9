package com.get.salecenter.dao.sale;

import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.EventType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/12/7 11:26
 * @verison: 1.0
 * @description:
 */
@Mapper
public interface EventTypeMapper extends GetMapper<EventType> {

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * @return java.lang.String
     * @Description :
     * @Param [eventTypeId]
     * <AUTHOR>
     */
    String getEventTypeNameById(@Param("eventTypeId") Long eventTypeId);

    List<EventType> getEventTypeList(@Param("fkDepartIds")Set<Long> fkDepartIds);
}