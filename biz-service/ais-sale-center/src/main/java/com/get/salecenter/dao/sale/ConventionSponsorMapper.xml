<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionSponsorMapper">
    <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionSponsor" keyProperty="id" useGeneratedKeys="true">
        insert into m_convention_sponsor
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkConventionId != null">
                fk_convention_id,
            </if>
            <if test="fkInstitutionProviderId != null">
                fk_institution_provider_id,
            </if>
            <if test="fkEventCostId != null">
                fk_event_cost_id,
            </if>
            <if test="sponsorName != null">
                sponsor_name,
            </if>
            <if test="receiptCode != null">
                receipt_code,
            </if>
            <if test="fkCurrencyTypeNum != null">
                fk_currency_type_num,
            </if>
            <if test="feeOther != null">
                fee_other,
            </if>
            <if test="feeOtherCny != null">
                fee_other_cny,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkConventionId != null">
                #{fkConventionId,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionProviderId != null">
                #{fkInstitutionProviderId,jdbcType=BIGINT},
            </if>
            <if test="fkEventCostId != null">
                #{fkEventCostId,jdbcType=BIGINT},
            </if>
            <if test="sponsorName != null">
                #{sponsorName,jdbcType=VARCHAR},
            </if>
            <if test="receiptCode != null">
                #{receiptCode,jdbcType=VARCHAR},
            </if>
            <if test="fkCurrencyTypeNum != null">
                #{fkCurrencyTypeNum,jdbcType=VARCHAR},
            </if>
            <if test="feeOther != null">
                #{feeOther,jdbcType=DECIMAL},
            </if>
            <if test="feeOtherCny != null">
                #{feeOtherCny,jdbcType=DECIMAL},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getSponsorsByReceiptCode" resultType="com.get.salecenter.vo.ConventionSponsorVo">
        SELECT *
        FROM m_convention_sponsor
        where receipt_code = #{receiptCode}
          and fk_convention_id = #{fkConventionId}
          and status = #{status}
    </select>
    <select id="getSponsorByReceiptCodeAndStatus" resultType="com.get.salecenter.vo.ConventionSponsorVo">
        SELECT
            b.*
        FROM
            m_convention_sponsor_fee a
            LEFT JOIN m_convention_sponsor b ON a.fk_convention_id = b.fk_convention_id
        WHERE
            b.receipt_code = #{receiptCode} and b.status = #{status} and b.fk_convention_id = #{fkConventionId} and b.receipt_code is not null
        GROUP BY b.id
    </select>
    <select id="getSponsorListByReceiptCode" resultType="com.get.salecenter.vo.ConventionSponsorVo">
        SELECT
            b.*
        FROM
            m_convention_sponsor_fee a
                LEFT JOIN m_convention_sponsor b ON a.fk_convention_id = b.fk_convention_id
        WHERE
            b.receipt_code = #{receiptCode}  and b.fk_convention_id = #{fkConventionId} and b.receipt_code is not null
        GROUP BY b.id
    </select>
</mapper>