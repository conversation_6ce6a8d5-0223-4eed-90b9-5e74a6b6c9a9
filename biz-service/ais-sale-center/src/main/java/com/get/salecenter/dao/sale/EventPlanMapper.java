package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.vo.EventPlanVo;
import com.get.salecenter.entity.EventPlan;
import com.get.salecenter.dto.EventPlanSearchDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Mapper
public interface EventPlanMapper extends BaseMapper<EventPlan> {

    /**
     * 列表数据
     * <AUTHOR>
     * @DateTime 2023/12/13 16:31
     */
   List<EventPlanVo> getEventPlans(IPage<EventPlanVo> iPage, @Param("eventPlanSearchDto") EventPlanSearchDto eventPlanSearchDto);

   /**
    * 所属公司下拉
    * <AUTHOR>
    * @DateTime 2023/12/13 18:13
    */
   List<Long> getCompanyList();

   /**
    * 年度下拉
    * <AUTHOR>
    * @DateTime 2023/12/13 18:13
    */
   List<Integer> getYearList();

}
