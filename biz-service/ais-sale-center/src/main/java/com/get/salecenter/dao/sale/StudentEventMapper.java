package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.StudentEvent;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface StudentEventMapper extends BaseMapper<StudentEvent> {

    int insertSelective(StudentEvent record);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [agentId]
     * <AUTHOR>
     */
    <PERSON><PERSON><PERSON> isExistByStudentId(Long studentId);

}