<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionHotelRoomPersonMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ConventionHotelRoomPerson">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_convention_hotel_room_id" jdbcType="BIGINT" property="fkConventionHotelRoomId"/>
        <result column="fk_convention_person_id" jdbcType="BIGINT" property="fkConventionPersonId"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>

    <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionHotelRoomPerson">
        insert into r_convention_hotel_room_person
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkConventionHotelRoomId != null">
                fk_convention_hotel_room_id,
            </if>
            <if test="fkConventionPersonId != null">
                fk_convention_person_id,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkConventionHotelRoomId != null">
                #{fkConventionHotelRoomId,jdbcType=BIGINT},
            </if>
            <if test="fkConventionPersonId != null">
                #{fkConventionPersonId,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getConventionHotelRoomPersonDtoList" resultType="com.get.salecenter.vo.ConventionHotelRoomPersonVo">
        SELECT a.id, a.fk_convention_hotel_room_id, a.fk_convention_person_id, c.bed_count
        FROM r_convention_hotel_room_person a
                 LEFT JOIN m_convention_hotel_room b ON a.fk_convention_hotel_room_id = b.id
                 LEFT JOIN m_convention_hotel c ON b.fk_convention_hotel_id = c.id
        WHERE a.fk_convention_hotel_room_id = #{roomId}

    </select>

    <select id="getRommId" resultType="java.lang.Long">
        SELECT
        chr.id
        FROM
        m_convention_hotel_room AS chr
        INNER JOIN
        m_convention_hotel AS ch
        ON
        chr.fk_convention_hotel_id = ch.id
        WHERE
        chr.stay_date = #{date} AND
        chr.system_room_num = #{systemRoomNum}
        <if test="fkConventionId != null and fkConventionId != '' ">
        AND  ch.fk_convention_id = #{fkConventionId}
        </if>
        order by ch.gmt_create desc limit 1
    </select>

    <select id="getArrangedBedCount" resultType="java.lang.Integer">
        select
        count(*)
        from
        r_convention_hotel_room_person a
        <if test="types != null and types.size()>0">
        LEFT JOIN m_convention_person b on a.fk_convention_person_id = b.id
        </if>
        where
        a.fk_convention_hotel_room_id
        in
        <foreach item="item" index="index" collection="roomIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="types != null and types.size()>0">
        and  b.type in
            <foreach item="type" index="index" collection="types" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
    </select>

    <select id="conventionHotelRoomPersonIsEmpty" resultType="java.lang.Boolean">
        select IFNULL(max(id), 0) id
        from r_convention_hotel_room_person
        where ${fieldName} = #{id} LIMIT 1;

    </select>

    <select id="getUsedRoomCount" resultType="java.lang.Integer">
        select
        count(DISTINCT a.fk_convention_hotel_room_id)
        from
        r_convention_hotel_room_person a
        <if test="types != null and types.size()>0">
            LEFT JOIN m_convention_person b on a.fk_convention_person_id = b.id
        </if>
        where
        a.fk_convention_hotel_room_id
        in
        <foreach item="item" index="index" collection="roomIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="types != null and types.size()>0">
            and  b.type in
            <foreach item="type" index="index" collection="types" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
    </select>
    <select id="getconventionPersonIds" resultType="java.lang.Long">
        SELECT m_convention_person.id
        FROM m_convention_person
                 LEFT JOIN
             m_convention_registration
             ON
                     m_convention_registration.fk_convention_id = m_convention_person.fk_convention_id
                 LEFT JOIN
             r_convention_person_registration
             ON
                     m_convention_person.id = r_convention_person_registration.fk_convention_person_id
                 LEFT JOIN
             r_convention_hotel_room_person
             ON
                     m_convention_person.id = r_convention_hotel_room_person.fk_convention_person_id
                 LEFT JOIN
             m_convention_hotel_room
             ON
                     r_convention_hotel_room_person.fk_convention_hotel_room_id = m_convention_hotel_room.id
                 LEFT JOIN
             m_convention_hotel
             ON
                 m_convention_hotel_room.fk_convention_hotel_id = m_convention_hotel.id
        WHERE m_convention_registration.fk_convention_id = #{conventionId}
        <if test="conventionRegistrationIds != null and conventionRegistrationIds.size()>0">
            AND r_convention_person_registration.fk_convention_registration_id IN
            <foreach collection="conventionRegistrationIds" item="id" index="index" open="(" separator="," close=")">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
          AND m_convention_person.type = 0
        GROUP BY m_convention_person.id
    </select>
    <select id="getStayDateStart" resultType="com.get.salecenter.entity.ConventionHotelRoom">
        SELECT chr.*
        FROM m_convention_hotel_room AS chr
                 LEFT JOIN r_convention_hotel_room_person AS chrp ON chrp.fk_convention_hotel_room_id = chr.id
        WHERE chrp.fk_convention_person_id = #{id}
        ORDER BY stay_date LIMIT 1
    </select>

    <select id="getStayDateEnd" resultType="com.get.salecenter.entity.ConventionHotelRoom">
        SELECT chr.*
        FROM m_convention_hotel_room AS chr
                 LEFT JOIN r_convention_hotel_room_person AS chrp ON chrp.fk_convention_hotel_room_id = chr.id
        WHERE chrp.fk_convention_person_id = #{id}
        ORDER BY stay_date desc LIMIT 1
    </select>
    <select id="getPersonIds" resultType="java.lang.Long">
        SELECT
        m_convention_person.*
        FROM
        m_convention_person
        INNER JOIN
        m_convention_registration
        ON
        m_convention_person.remark = m_convention_registration.provider_name
        WHERE
        m_convention_person.fk_convention_id = #{conventionId} AND
        m_convention_person.remark != "" AND
        m_convention_person.remark is not null
        <if test="conventionRegistrationIds != null and conventionRegistrationIds.size()>0">
            AND m_convention_registration.id IN
            <foreach collection="conventionRegistrationIds" item="id" index="index" open="(" separator="," close=")">
                #{id,jdbcType=BIGINT}
            </foreach>
        </if>
    </select>

    <select id="getPersonCount" resultType="java.lang.Integer">
        SELECT COUNT( id ) FROM r_convention_hotel_room_person WHERE fk_convention_hotel_room_id = #{fkConventionHotelRoomId};
    </select>

    <select id="getConventionHotelRoomPersonInfo"
            resultType="com.get.salecenter.vo.ConventionHotelRoomPersonVo">
        SELECT
            *
        FROM
            r_convention_hotel_room_person AS chrp
                INNER JOIN
            m_convention_hotel_room AS chr
            ON
                chr.id = chrp.fk_convention_hotel_room_id
                INNER JOIN
            m_convention_hotel AS ch
            ON
                chr.fk_convention_hotel_id = ch.id
        WHERE
            chrp.fk_convention_person_id = #{conventionPersonId}
    </select>
    <select id="getOtherPersons" resultType="com.get.salecenter.vo.ConventionHotelRoomPersonVo">
        SELECT
            *
        FROM
            r_convention_hotel_room_person AS chrp
                INNER JOIN
            m_convention_hotel_room AS chr
            ON
                chr.id = chrp.fk_convention_hotel_room_id
                INNER JOIN
            m_convention_hotel AS ch
            ON
                chr.fk_convention_hotel_id = ch.id
        WHERE
            chrp.fk_convention_person_id !=#{fkConventionPersonId}
            and
            chrp.fk_convention_hotel_room_id = #{fkConventionHotelRoomId}
    </select>
    <select id="getRoomIdAndDate" resultType="com.get.salecenter.vo.ConventionHotelRoomVo">
        SELECT
            chr.id,chr.stay_date,chr.system_room_num
        FROM
            m_convention_hotel_room AS chr
                INNER JOIN
            m_convention_hotel AS ch
            ON
                chr.fk_convention_hotel_id = ch.id
        WHERE
            ch.fk_convention_id = #{conventionId}
        order by ch.gmt_create desc
    </select>
    <select id="getConventionHotelRoomPersonDtos"
            resultType="com.get.salecenter.vo.ConventionHotelRoomPersonVo">
        SELECT a.id, a.fk_convention_hotel_room_id, a.fk_convention_person_id, c.bed_count
        FROM r_convention_hotel_room_person a
                 LEFT JOIN m_convention_hotel_room b ON a.fk_convention_hotel_room_id = b.id
                 LEFT JOIN m_convention_hotel c ON b.fk_convention_hotel_id = c.id
        WHERE c.fk_convention_id = #{conventionId}
        order by
            a.fk_convention_hotel_room_id
    </select>
</mapper>