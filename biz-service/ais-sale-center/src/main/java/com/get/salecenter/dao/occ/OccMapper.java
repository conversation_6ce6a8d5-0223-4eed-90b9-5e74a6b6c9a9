package com.get.salecenter.dao.occ;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.financecenter.vo.OccVo;
import com.get.salecenter.vo.AgencyAppdixVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@DS("occdb")
@Mapper
public interface OccMapper {


    List<OccVo> getData();

    OccVo getDataByInfo(@Param("courseId")String courseId, @Param("createTime")String createTime);

    List<OccVo> batchGetByCourseId(@Param("list")List<String> list);

    List<String> getAllCourseId();

    List<String> getErrorCourseId(int state);

    OccVo getUptoRecord(@Param("courseId")String courseId, @Param("type") String type);

    OccVo getLastPayableRecord(@Param("courseId")String courseId, @Param("type") String type);

    List<OccVo> getOcByCourseId(@Param("courseId") String courseId, @Param("type") String type);

    void updateRecord(@Param("state") String state,@Param("remarks") String remarks,@Param("id") Long id);


    void updateState(@Param("state") String state,@Param("id") Long id);

    List<OccVo> getDataById(@Param("courseId")String courseId);

    List<OccVo> getDataByIds(@Param("courseIds")Set<String> courseId);

    List<AgencyAppdixVo> getAppdixList();

    List<OccVo> getAllNewReceivedPay();

    List<AgencyAppdixVo> getListAttactment();


}
