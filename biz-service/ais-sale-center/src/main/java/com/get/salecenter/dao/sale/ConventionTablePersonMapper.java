package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.ConventionTablePersonNumVo;
import com.get.salecenter.entity.ConventionTable;
import com.get.salecenter.entity.ConventionTablePerson;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/28 12:00
 * @verison: 1.0
 * @description: 配置桌台-晚宴桌mapper
 */
@Mapper
public interface ConventionTablePersonMapper extends BaseMapper<ConventionTablePerson>, GetMapper<ConventionTablePerson> {

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionTablePerson record);

    /**
     * 根据桌台ids查找所有人员id
     *
     * @param tableIds
     * @return
     */
    List<Long> getPersonIds(@Param("tableIds") List<Long> tableIds);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :通过参会人员id 获取对应桌台信息
     * @Param [id]
     * <AUTHOR>
     */
    List<ConventionTable> getTableByPersonId(@Param("id") Long id);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :通过参会人员id 获取对应桌台信息
     * @Param [id]
     * <AUTHOR>
     */
    List<ConventionTablePersonNumVo> getTableByPersonIds(@Param("ids") List<Long> ids);

    /**
     * @return java.lang.Boolean
     * @Description :
     * @Param [fieldName, id]
     * <AUTHOR>
     */
    Boolean conventionTablePersonIsEmpty(@Param("fieldName") String fieldName, @Param("id") Long id);


    List<ConventionTablePerson> getConventionTablePersonByPersonId(@Param("conventionPersonId") Long conventionPersonId, @Param("conventionId")Long conventionId, @Param("key")String key);
}