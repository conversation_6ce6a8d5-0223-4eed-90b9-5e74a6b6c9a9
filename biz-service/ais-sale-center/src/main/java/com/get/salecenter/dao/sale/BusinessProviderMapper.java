package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.vo.BusinessProviderSelectVo;
import com.get.salecenter.vo.BusinessProviderVo;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.entity.BusinessProvider;
import com.get.salecenter.dto.query.BusinessProviderQueryDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * author:Neil
 * Time: 17:13
 * Date: 2022/6/20
 * Description:
 */
@Mapper
public interface BusinessProviderMapper extends BaseMapper<BusinessProvider>, GetMapper<BusinessProvider> {
//    void insertSelective(BusinessProvider businessProvider);

    List<BusinessProviderVo> getDatas(IPage<BusinessProviderQueryDto> iPage , @Param("businessProviderDto") BusinessProviderQueryDto businessProviderDto, @Param("fkDepartIds")Set<Long> fkDepartIds);

    List<BusinessProviderVo> getNamesByIds(@Param("ids") Set<Long> ids);

    List<BaseSelectEntity> providerSelect(@Param("tableName")String tableName,@Param("companyId") Long companyId,@Param("fkDepartIds")Set<Long> fkDepartIds);

    List<BaseSelectEntity> getProviderById(@Param("id") Long id,@Param("fkDepartIds")Set<Long> fkDepartIds);

    List<BaseSelectEntity> providerSelectByCompanyId(@Param("companyId")Long companyId);

    Set<Long> getInvoicePlanId(@Param("targetId")Long targetId,@Param("receiptFormId")Long receiptFormId);

    List<BusinessProviderVo> getBusinessProviderInfoByPlanIds(@Param("ids")Set<Long> ids);

    List<Long> getBusinessProviderIdByTargetName(String targetName);

    List<BaseSelectEntity> getBusinessProvider(@Param("companyIds") List<Long> companyIds,@Param("keyWord") String keyWord);

    List<BaseSelectEntity> getBusinessProviderByTargetName(String targetName);

    SelItem getBusinessProviderCompanyInfoById(Long id);

    List<BaseSelectEntity> getClientSourceProviderSelect(@Param("tableName")String tableName, @Param("companyId")Long companyId, @Param("fkDepartIds")Set<Long> fkDepartIds);

    /**
     * 根据fk_type_key（业务类型关键字）、公司、当前员工及下属员工所有部门ids 获取业务提供商下拉
     *
     * @param fkDepartIds 当前员工及下属员工所有部门ids
     * @param fkTypeKey   业务类型关键字
     * @param fkCompanyId 公司Id
     * @return
     */
    List<BusinessProviderSelectVo> getBusinessProviderSelect(@Param("fkDepartIds") Set<Long> fkDepartIds, @Param("fkTypeKey") String fkTypeKey, @Param("fkCompanyId") Long fkCompanyId);
    List<BaseSelectEntity> getBusinessProviderSelectByTypeKey(@Param("companyId") Long companyId, @Param("typeKey") String typeKey);

    /**
     * 获取留学保险提供商可绑定的应收计划
     * @param targetId
     * @param receiptFormId
     * @return
     */
    List<ReceivablePlan> getPlanIdsByBusinessProviderId(@Param("targetId") Long targetId, @Param("receiptFormId") Long receiptFormId);

}
