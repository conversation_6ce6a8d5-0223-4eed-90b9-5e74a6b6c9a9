<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentContractAccountMapper">

    <select id="isExistByContractId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_agent_contract_account where fk_agent_id=#{contractId}
    </select>

    <select id="validateCurrency" resultType="java.lang.Long">
        SELECT max(id) sum  from m_agent_contract_account
        where fk_agent_id=#{fkAgentId} and fk_currency_type_num=#{currency} AND is_active = 1
        <if test="agentContractAccountId != null">
            AND id != #{agentContractAccountId}
        </if>
    </select>
    <select id="selectagentContractAccountByAgentIds"
            resultType="com.get.salecenter.entity.AgentContractAccount">
        SELECT
            *
        FROM
            ais_sale_center.m_agent_contract_account AS maca
                INNER JOIN ais_finance_center.u_currency_type AS uct ON uct.num = maca.fk_currency_type_num
        WHERE maca.is_active = 1
          AND maca.fk_agent_id IN
        <foreach collection="agentIds" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY
            uct.view_order DESC
</select>

    <select id="getAgentContractAccountSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            ba.id,
            CONCAT(
                    '【',
                    CASE
                        WHEN IFNULL(ct.type_name, '') = '' THEN
                            ct.num
                        ELSE
                            CONCAT(
                                    ct.num,
                                    '（',
                                    ct.type_name,
                                    '）'
                                )
                        END,
                    '】',
                    ba.bank_account,
                    ',',
                    ba.bank_name
                ) as name
        FROM
            m_agent_contract_account ba,
            ais_finance_center.u_currency_type ct
        WHERE
            ba.fk_currency_type_num = ct.num
          and  ba.is_active = 1
          AND ba.fk_agent_id = #{fkTargetId}
    </select>
    <select id="selectAgentContractAccountByAccountIds"
            resultType="com.get.salecenter.vo.AgentContractAccountVo">
        SELECT maca.*, mac.contract_num FROM  m_agent_contract_account AS maca
        LEFT JOIN r_agent_contract_agent_account as acaa ON acaa.fk_agent_contract_account_id = maca.id
        LEFT JOIN m_agent_contract as mac ON mac.id = acaa.fk_agent_contract_id
        WHERE maca.id IN
        <foreach collection="accountIds" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getAgentContractBankAccountNameById" resultType="java.lang.String">
        SELECT
            CONCAT(
                    '【',
                    CASE
                        WHEN IFNULL(ct.type_name, '') = '' THEN
                            ct.num
                        ELSE
                            CONCAT(
                                    ct.num,
                                    '（',
                                    ct.type_name,
                                    '）'
                                )
                        END,
                    '】',
                    ba.bank_account,
                    ',',
                    ba.bank_name
                ) bankName
        FROM
            m_agent_contract_account ba,
            ais_finance_center.u_currency_type ct
        WHERE
            ba.fk_currency_type_num = ct.num
          and  ba.id = #{id}
    </select>

    <select id="getIsDefaultContractAccount" resultType="java.lang.Integer">
        SELECT count(0) FROM m_agent_contract_account WHERE fk_agent_id=#{fkAgentId} AND is_default=1
        <if test="id!=null">
            and id !=#{id}
        </if>
    </select>

    <select id="getAgentContractAccountExist" resultType="com.get.salecenter.vo.AgentContractAccountVo">
        select sc.id,sc.bank_account,sc.bank_account_num,IFNULL((select `name` from m_agent where id
        =sc.fk_agent_id),null)as fkAgentName from m_agent ma
        join m_agent_contract_account sc on ma.id =sc.fk_agent_id
        join r_agent_company rac on rac.fk_agent_id = ma.id
        where 1=1  and rac.fk_company_id=#{companyId}
        <if test="bankAccount!=null and bankAccount!=''">
            and sc.bank_account=#{bankAccount}
        </if>
        <if test="bankAccountNum!=null and bankAccountNum!=''">
            and sc.bank_account_num=#{bankAccountNum}
        </if>
    </select>
    <select id="findBranchAccountInfoById" resultType="com.get.salecenter.vo.AgentContractAccountVo">
            SELECT
            CONCAT(uc.name_chn,'(',uc.name,')') as fk_area_country_name,
            CONCAT(ac.name_chn,'(',ac.name,')') as fk_area_city_name,
            CONCAT(s.name_chn,'(',s.name,')') as fk_area_state_name,
            d.name as fk_area_city_division_name,
            CASE WHEN IFNULL(t.type_name,'')='' THEN t.num ELSE CONCAT(t.type_name,'（',t.num,'）') END currency_type_name,
            c.* from m_agent_contract_account c
            LEFT JOIN ais_institution_center.u_area_country uc on uc.id = c.fk_area_country_id
            LEFT JOIN ais_institution_center.u_area_city ac on ac.id = c.fk_area_city_id
            LEFT JOIN ais_institution_center.u_area_state s on  s.id = c.fk_area_state_id
            LEFT JOIN ais_institution_center.u_area_city_division d on d.id = c.fk_area_city_division_id
            LEFT JOIN ais_finance_center.u_currency_type t on t.num = c.fk_currency_type_num
            where
	            c.id = #{id}
    </select>
    <select id="getAgentContractAccountSelectById" resultType="com.get.salecenter.vo.AgentContractAccountVo">
        SELECT
            ba.id id,
            CONCAT(
                    '【',
                    CASE
                        WHEN IFNULL(ct.type_name, '') = '' THEN
                            ct.num
                        ELSE
                            CONCAT(
                                    ct.num,
                                    '（',
                                    ct.type_name,
                                    '）'
                                )
                        END,
                    '】',
                    ba.bank_account,
                    ',',
                    ba.bank_name
                ) bankName
        FROM
            m_agent_contract_account ba,
            ais_finance_center.u_currency_type ct
        WHERE
            ba.fk_currency_type_num = ct.num
          and  ba.is_active = 1
          AND ba.fk_agent_id=#{agentId}
    </select>
    <select id="getFirstAgentContractAccountByAgentId"
            resultType="com.get.salecenter.entity.AgentContractAccount">
        SELECT
            bank_account,
            bank_account_num,
            bank_name,
            bank_address,
            bank_code_type,
            bank_code,
            is_default
        FROM
            m_agent_contract_account
        WHERE
            fk_agent_id = #{agentId}
            AND is_active = 1
            AND is_default = 1
        LIMIT 1
    </select>
    <select id="getAgentContractPersonMobile" resultType="com.get.salecenter.vo.SelItem">
        SELECT
            fk_table_id as keyId,
            mobile as val
        FROM
            s_contact_person
        WHERE
            fk_table_name = 'm_agent'
        AND fk_contact_person_type_key = 'CONTACT_AGENT_CONTRACT'
        AND fk_table_id IN
        <foreach collection="agentIds" separator="," open="(" close=")" item="cid">
            #{cid}
        </foreach>
        AND mobile is not null
        AND mobile !=''
        GROUP BY
            fk_table_id
    </select>
    <select id="getAgentContractAccountPaymentFrom" resultType="java.lang.Long">
        SELECT
            DISTINCT b1.fk_agent_contract_account_id
        FROM
            ais_finance_center.m_payment_form a
        LEFT JOIN ais_finance_center.m_payment_form_item b ON a.id = b.fk_payment_form_id
        LEFT JOIN ais_finance_center.r_payable_plan_settlement_installment b1 ON b.id = b1.fk_payment_form_item_id
        LEFT JOIN ais_sale_center.m_payable_plan c ON b.fk_payable_plan_id = c.id
        LEFT JOIN ais_sale_center.m_student_offer_item d ON c.fk_type_key = 'm_student_offer_item'
        AND c.fk_type_target_id = d.id
        WHERE
            a.fk_type_key = 'm_agent'
            <if test="accountIds!=null and accountIds.size>0">
                AND a.fk_bank_account_id IN
                <foreach collection="accountIds" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
        ORDER BY
            a.fk_type_target_id,
            a.fk_bank_account_id
    </select>
</mapper>