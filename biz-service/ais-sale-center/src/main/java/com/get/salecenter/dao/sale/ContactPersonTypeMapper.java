package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.ContactPersonType;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ContactPersonTypeMapper extends BaseMapper<ContactPersonType>, GetMapper<ContactPersonType> {


    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    @Override
    int insert(ContactPersonType record);

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ContactPersonType record);

    /**
     * @return java.lang.Integer
     * @Description :
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

}