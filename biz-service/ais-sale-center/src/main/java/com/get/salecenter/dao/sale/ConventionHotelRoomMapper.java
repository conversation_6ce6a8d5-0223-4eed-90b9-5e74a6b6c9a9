package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.ConventionHotelRoomVo;
import com.get.salecenter.entity.ConventionHotelRoom;
import com.get.salecenter.dto.ConventionHotelRoomDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/18 11:35
 * @verison: 1.0
 * @description: 酒店房间配置管理mapper
 */
@Mapper
public interface ConventionHotelRoomMapper extends BaseMapper<ConventionHotelRoom> {

    /**
     * 根据酒店房型id  获取已存在的最大系统房号
     *
     * @param hotelId
     * @return
     */
    String getMaxSystemRoomNum(@Param("hotelId") Long hotelId);

    /**
     * 根据酒店房型id 查询对应房间ids
     *
     * @param hotelId
     * @param systemRoomNum
     * @return
     */
    List<Long> getRoomIdsByHotelId(@Param("hotelId") Long hotelId, @Param("systemRoomNum") String systemRoomNum);

    /**
     * @return java.lang.Boolean
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    Boolean conventionHoteRoomIsEmpty(@Param("id") Long id);

    /**
     * 根据酒店房型id 查出所开房间的入住日期集合
     *
     * @param conventionHotelIds
     * @return
     */
    List<Date> getDates(@Param("conventionHotelIds") List<Long> conventionHotelIds);

    /**
     * 根据房型id 查出分类后所有该房型id开得房间信息(可按条件查找)
     *
     * @param conventionHotelIds
     * @param conventionHotelRoomDto
     * @return
     */
    List<ConventionHotelRoomVo> getConventionHotelRoomDtoList(@Param("conventionHotelIds") List<Long> conventionHotelIds,
                                                              @Param("conventionHotelRoomDto") ConventionHotelRoomDto conventionHotelRoomDto,
                                                              @Param("bdCodes")List<String> bdCodes,
                                                              @Param("companyId")Long companyId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :通过日期和酒店id查找房间ids
     * @Param [id, date]
     * <AUTHOR>
     */
    List<Long> getRoomIds(@Param("id") Long id, @Param("date") String date);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :根据房型ids（可选条件-日期） 查找所有房型所开得房间id
     * @Param [conventionHotelIds, roomDate]
     * <AUTHOR>
     */
    List<Long> getAllRoomIds(@Param("conventionHotelIds") List<Long> conventionHotelIds, @Param("roomDate") String roomDate);


    /**
     * 根据ConventionPersonId查找ConventionHotelRoom
     *
     * @param id
     */
    List<ConventionHotelRoom> getConventionHotelRoom(@Param("id") Long id);

    /**
     * 根据ids查询，按系统房号分组
     *
     * @param ids
     * @return
     */
    List<ConventionHotelRoom> getConventionHotelRoomsByIds(@Param("ids") List<Long> ids);

    /**
     * 获取ids
     *
     * @param roomIds
     * @return
     */
    List<ConventionHotelRoom> getRoomByIds(@Param("roomIds") List<Long> roomIds);

    /**
     * 根据房间类型id获取已开房间数
     *
     * @Date 17:28 2024/1/2
     * <AUTHOR>
     */
    Long getHotelRoomCountByRoomTypeId(@Param("roomTypeId") Long roomTypeId);

}