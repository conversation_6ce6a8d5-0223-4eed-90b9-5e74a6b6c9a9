package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.SelItem;
import com.get.salecenter.entity.RStudentOfferItemStep;
import com.get.salecenter.dto.RStudentOfferItemStepDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface RStudentOfferItemStepMapper extends BaseMapper<RStudentOfferItemStep>, GetMapper<RStudentOfferItemStep> {

    int insertSelective(RStudentOfferItemStep record);

//    /**
//     * @Description: 获取当前步骤
//     * @Param [itemId]
//     * @return java.lang.Integer
//     * <AUTHOR>
//     */
//    Integer getMaxStepOrder(Long itemId);


    /**
     * @return java.lang.Boolean
     * @Description: 删除校验
     * @Param [stepId]
     * <AUTHOR>
     */
    Boolean isExistByItemStepId(Long stepId);

    List<Long> selectItemIdByStepIdAndGmtCreate(RStudentOfferItemStepDto RStudentOfferItemStepDto);

    /**
     * 批量插入
     * @param rStudentOfferItemSteps
     * @return
     */
    int insertRSteps(@Param("rStudentOfferItemSteps")List<RStudentOfferItemStep> rStudentOfferItemSteps);

    Set<Long> selectFailureOfferItemSteps(@Param("ids")Set<Long> ids);

    List<SelItem> selectOfferStepCoe(@Param("ids")Set<Long> ids);

    List<RStudentOfferItemStep> failureIds(@Param("ids")Set<Long> itemIds);

    /**
     * Author Cream
     * Description : //获取最新备注
     * Date 2023/8/17 15:23
     * Params:
     * Return
     */
    List<SelItem> getLastRemarkByIds(@Param("ids") Set<Long> ids);
}