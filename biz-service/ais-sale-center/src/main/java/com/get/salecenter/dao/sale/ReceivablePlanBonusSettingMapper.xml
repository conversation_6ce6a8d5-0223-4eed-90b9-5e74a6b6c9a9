<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.salecenter.dao.sale.ReceivablePlanBonusSettingMapper">

    <insert id="insert">
        INSERT INTO r_receivable_plan_bonus_setting
        (fk_receivable_plan_id,type,bonus_commission_rate,bonus_fixed_amount,gmt_create,gmt_modified,gmt_create_user,gmt_modified_user)
        VALUES
        (#{fkReceivablePlanId},#{type},#{bonusCommissionRate},#{bonusFixedAmount},#{gmtCreate},#{gmtModified},#{gmtCreateUser},#{gmtModifiedUser})
    </insert>

    <select id="selectBatchReceivableIds" resultType="com.get.salecenter.entity.ReceivablePlanBonusSetting">
        SELECT id,fk_receivable_plan_id,type,bonus_commission_rate,bonus_fixed_amount,gmt_create,gmt_modified,gmt_create_user,gmt_modified_user
        FROM r_receivable_plan_bonus_setting
        WHERE fk_receivable_plan_id IN
        <foreach item="receivableId" collection="receivableIds" separator="," open="(" close=")" index="index">
            #{receivableId}
        </foreach>
    </select>
</mapper>
