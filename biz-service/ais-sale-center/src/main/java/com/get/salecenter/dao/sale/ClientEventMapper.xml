<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ClientEventMapper">


    <select id="getLastVisitTime" resultType="com.get.salecenter.vo.SelItem">
            SELECT
                fk_client_id as keyId,
                max( gmt_create ) as val
                <!--CASE WHEN IFNULL(max( gmt_create ),0) > IFNULL(max( follow_up_time ),0) THEN max(gmt_create) ELSE max(follow_up_time) END val-->
            FROM
                m_client_event
            WHERE 1=1
                and fk_student_event_type_id != 5
                <if test="list!=null and list.size>0">
                 and fk_client_id in
                    <foreach collection="list" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                </if>
            GROUP BY
                fk_client_id
    </select>

    <select id="isExistByClientId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_client_event where fk_client_id=#{clientId}
    </select>
    <select id="getVisitTime" resultType="com.get.salecenter.vo.SelItem">
        SELECT
            fk_client_id AS keyId,
        max(follow_up_time) AS val
        FROM
            m_client_event
        WHERE
            fk_client_id in
            <foreach collection="list" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
<!--        <AND (DATE_FORMAT(follow_up_time,'%Y-%m-%d') > CURDATE())>-->
        GROUP BY fk_client_id
        having 1 = 1
        <if test="beginFollowUpTime!=null and endFollowUpTime!=null">
            AND DATE_FORMAT(val, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{beginFollowUpTime}, '%Y-%m-%d' )
            AND DATE_FORMAT(val, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{endFollowUpTime}, '%Y-%m-%d' )
        </if>
        ORDER BY
            follow_up_time
    </select>
</mapper>