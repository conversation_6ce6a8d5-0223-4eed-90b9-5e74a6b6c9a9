package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.BusinessProviderAccountVo;
import com.get.salecenter.entity.BusinessProviderAccount;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface BusinessProviderAccountMapper extends GetMapper<BusinessProviderAccount> {


    /**
     * 通过id获取业务提供商账户列表
     * @param businessProviderId
     * @param iPage
     * @return
     */
    List<BusinessProviderAccountVo> getBusinessProviderAccountsById(@Param("iPage") IPage<BusinessProviderAccountVo> iPage, @Param("businessProviderId") Long businessProviderId);


    /**
     * 业务提供商合同账户列表账户重复提示
     * @param businessProviderId
     * @param bankAccount
     * @param bankAccountNum
     * @return
     */
    List<BusinessProviderAccountVo> getContractAccountExist(@Param("businessProviderId") Long businessProviderId, @Param("bankAccount") String bankAccount, @Param("bankAccountNum") String bankAccountNum);

    /**
     * 获取业务提供商账户列表
     * @param fkTargetId
     * @return
     */
    List<BaseSelectEntity> getBusinessProviderAccountListByFkTargetId(Long fkTargetId);

    /**
     * 获取业务提供商账
     * @param fkBankAccountId
     * @return
     */
    String getBusinessProviderBankAccountNameById(Long fkBankAccountId);
}
