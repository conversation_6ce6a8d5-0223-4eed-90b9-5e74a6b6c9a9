package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.EventPlanThemeOfflineVo;
import com.get.salecenter.entity.EventPlanThemeOffline;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Mapper
public interface EventPlanThemeOfflineMapper extends BaseMapper<EventPlanThemeOffline> {

    List<EventPlanThemeOfflineVo> getEventPlanThemeOfflines(@Param("fkEventPlanId") Long fkEventPlanId, @Param("displayType") Integer displayType);

    /**
     * 获取最大排序
     * <AUTHOR>
     * @DateTime 2023/12/15 16:29
     */
    Integer getMaxViewOrder(Long fkEventPlanThemeId);

}
