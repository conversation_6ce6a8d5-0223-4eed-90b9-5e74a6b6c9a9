<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentOfferItemFailureMapper">
    <insert id="insert" parameterType="com.get.salecenter.entity.StudentOfferItemFailure">
        insert into m_student_offer_item_failure (id, fk_company_id, fk_student_id,
                                                  fk_student_offer_item_id, status, gmt_create,
                                                  gmt_create_user, gmt_modified, gmt_modified_user,
                                                  remark)
        values (#{id,jdbcType=BIGINT}, #{fkCompanyId,jdbcType=BIGINT}, #{fkStudentId,jdbcType=BIGINT},
                #{fkStudentOfferItemId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
                #{gmtModifiedUser,jdbcType=VARCHAR},
                #{remark,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentOfferItemFailure">
        insert into m_student_offer_item_failure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkCompanyId != null">
                fk_company_id,
            </if>
            <if test="fkStudentId != null">
                fk_student_id,
            </if>
            <if test="fkStudentOfferItemId != null">
                fk_student_offer_item_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
            <if test="remark != null">
                remark,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkCompanyId != null">
                #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="fkStudentId != null">
                #{fkStudentId,jdbcType=BIGINT},
            </if>
            <if test="fkStudentOfferItemId != null">
                #{fkStudentOfferItemId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateById" parameterType="com.get.salecenter.entity.StudentOfferItemFailure">
        update m_student_offer_item_failure
        <set>
            <if test="fkCompanyId != null">
                fk_company_id = #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="fkStudentId != null">
                fk_student_id = #{fkStudentId,jdbcType=BIGINT},
            </if>
            <if test="fkStudentOfferItemId != null">
                fk_student_offer_item_id = #{fkStudentOfferItemId,jdbcType=BIGINT},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.get.salecenter.entity.StudentOfferItemFailure">
        update m_student_offer_item_failure
        set fk_company_id            = #{fkCompanyId,jdbcType=BIGINT},
            fk_student_id            = #{fkStudentId,jdbcType=BIGINT},
            fk_student_offer_item_id = #{fkStudentOfferItemId,jdbcType=BIGINT},
            status                   = #{status,jdbcType=INTEGER},
            gmt_create               = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user          = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified             = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user        = #{gmtModifiedUser,jdbcType=VARCHAR},
            remark                   = #{remark,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.salecenter.entity.StudentOfferItemFailure">
        update m_student_offer_item_failure
        set fk_company_id            = #{fkCompanyId,jdbcType=BIGINT},
            fk_student_id            = #{fkStudentId,jdbcType=BIGINT},
            fk_student_offer_item_id = #{fkStudentOfferItemId,jdbcType=BIGINT},
            status                   = #{status,jdbcType=INTEGER},
            gmt_create               = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user          = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified             = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user        = #{gmtModifiedUser,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="getStudentOfferItemFailureList" resultType="com.get.salecenter.vo.StudentOfferItemFailureVo">
        SELECT
        soi.num,
        soi.fk_area_country_id AS areaCountryId,
        soi.fk_institution_id AS institutionId,
        soi.fk_institution_course_id AS institutionCourseId,
        soi.opening_time AS openingTime,
        soi.defer_opening_time AS deferOpeningTime,
        soi.is_defer_entrance AS isDeferEntrance,
        s.name AS studentName,
        CONCAT(s.last_name,s.first_name) AS studentNameEng,
        a.name AS agentName,
        soi.fk_institution_provider_id AS institutionProviderId,
        soi.fk_institution_course_id AS institutionCourseId,
        soif.*
        FROM
        m_student_offer_item_failure AS soif
        INNER JOIN m_student_offer_item soi ON soi.id = soif.fk_student_offer_item_id
        INNER JOIN m_student_offer AS so ON so.id = soi.fk_student_offer_id
        LEFT JOIN m_agent AS a ON a.id = so.fk_agent_id
        INNER JOIN m_student AS s ON s.id = soif.fk_student_id
        WHERE 1=1
        <if test="studentOfferItemFailureDto.createBeginDate != null">
            and DATE_FORMAT(soif.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]>
            DATE_FORMAT(#{studentOfferItemFailureDto.createBeginDate},'%Y-%m-%d')
        </if>
        <if test="studentOfferItemFailureDto.createEndDate != null">
            and DATE_FORMAT(soif.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]>
            DATE_FORMAT(#{studentOfferItemFailureDto.createEndDate},'%Y-%m-%d')
        </if>
        <if test="studentOfferItemFailureDto.offerItemNum != null and studentOfferItemFailureDto.offerItemNum !=''">
            AND soi.num = #{studentOfferItemFailureDto.offerItemNum}
        </if>
        <if test="studentOfferItemFailureDto.status != null and studentOfferItemFailureDto.status !='' or studentOfferItemFailureDto.status == 0">
            AND soif.status = #{studentOfferItemFailureDto.status}
        </if>
        <if test="studentOfferItemFailureDto.agentName != null and studentOfferItemFailureDto.agentName !=''">
            AND a.name like concat("%",#{studentOfferItemFailureDto.agentName},"%")
        </if>
        <if test="studentOfferItemFailureDto.agentId != null and studentOfferItemFailureDto.agentId !=''">
            AND a.id = #{studentOfferItemFailureDto.agentId}
        </if>
        <if test="studentOfferItemFailureDto.studentName != null and studentOfferItemFailureDto.studentName !=''">
            AND s.name like concat("%",#{studentOfferItemFailureDto.studentName},"%")
        </if>
        <if test="studentOfferItemFailureDto.studentNameEng != null and studentOfferItemFailureDto.studentNameEng !=''">
            AND CONCAT(s.last_name,s.first_name) like concat("%",#{studentOfferItemFailureDto.studentNameEng},"%")
        </if>
        <if test="studentOfferItemFailureDto.companyId != null and studentOfferItemFailureDto.companyId !=''">
            AND s.fk_company_id = #{studentOfferItemFailureDto.companyId}
        </if>
        <if test="studentOfferItemFailureDto.areaCountryId != null and studentOfferItemFailureDto.areaCountryId !=''">
            AND soi.fk_area_country_id = #{studentOfferItemFailureDto.areaCountryId}
        </if>
        <if test="studentOfferItemFailureDto.institutionProviderId != null and studentOfferItemFailureDto.institutionProviderId !=''">
            AND soi.fk_institution_provider_id = #{studentOfferItemFailureDto.institutionProviderId}
        </if>
        <if test="studentOfferItemFailureDto.institutionId != null and studentOfferItemFailureDto.institutionId !=''">
            AND soi.fk_institution_id = #{studentOfferItemFailureDto.institutionId}
        </if>
        <if test="studentOfferItemFailureDto.institutionCourseId != null and studentOfferItemFailureDto.institutionCourseId !=''">
            AND soi.fk_institution_course_id = #{studentOfferItemFailureDto.institutionCourseId}
        </if>
        order by status ASC, id desc
    </select>

    <select id="agentSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            a.id,
            concat(if(a.is_active=0,'【无效】',''),
                   a.name)  name,
            a.is_active status
        FROM
            m_student_offer_item_failure AS soif
                INNER JOIN m_student_offer_item soi ON soi.id = soif.fk_student_offer_item_id
                INNER JOIN m_student_offer AS so ON so.id = soi.fk_student_offer_id
                INNER JOIN m_agent AS a ON a.id = so.fk_agent_id
                INNER JOIN m_student AS s ON s.id = soif.fk_student_id
        WHERE
            s.fk_company_id = 1
        GROUP BY a.id
        order by CONVERT(a.name USING gbk)

    </select>
</mapper>