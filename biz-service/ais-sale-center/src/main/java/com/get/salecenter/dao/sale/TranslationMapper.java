package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.Translation;
import com.get.salecenter.dto.TranslationDto;
import org.apache.ibatis.annotations.Mapper;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-04
 */
@Mapper
public interface TranslationMapper extends BaseMapper<Translation> {

    String getTranslation(TranslationDto translationDto);
}
