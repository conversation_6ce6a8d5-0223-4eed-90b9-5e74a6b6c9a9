<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.EventTypeMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.EventType">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="type_name" jdbcType="VARCHAR" property="typeName" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0)
    from
     u_event_type
  </select>

  <select id="getEventTypeNameById" resultType="java.lang.String">
    select type_name from u_event_type where id = #{eventTypeId}

  </select>
    <select id="getEventTypeList" resultType="com.get.salecenter.entity.EventType">
      SELECT
      a.*
      FROM
      u_event_type a
      where 1=1
        <if test="fkDepartIds !=null">
          and (
          a.fk_department_ids is null
          <foreach collection="fkDepartIds" item="item" >
            or FIND_IN_SET(#{item},a.fk_department_ids)
          </foreach>
          )
        </if>
      ORDER BY a.view_order DESC
    </select>
</mapper>