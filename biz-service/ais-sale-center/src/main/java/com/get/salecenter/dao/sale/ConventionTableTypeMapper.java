package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.ConventionTableTypeVo;
import com.get.salecenter.entity.ConventionTableType;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/25 15:00
 * @verison: 1.0
 * @description: 桌台类型mapper
 */
@Mapper
public interface ConventionTableTypeMapper extends BaseMapper<ConventionTableType> {

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionTableType record);

    /**
     * 桌台类型下拉框数据
     *
     * @return
     */
    List<ConventionTableTypeVo> getTableTypeList();

    /**
     * 查找排序最大值
     *
     * @return
     */
    Integer getMaxViewOrder();


}