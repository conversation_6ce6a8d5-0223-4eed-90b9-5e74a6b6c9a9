<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.TranslationMappingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.TranslationMapping">
        <id column="id" property="id" />
        <result column="fk_table_name" property="fkTableName" />
        <result column="fk_column_name" property="fkColumnName" />
        <result column="input_title" property="inputTitle" />
        <result column="input_type" property="inputType" />
        <result column="max_length" property="maxLength" />
        <result column="view_order" property="viewOrder" />
        <result column="is_active" property="isActive" />
        <result column="gmt_create" property="gmtCreate" />
        <result column="gmt_create_user" property="gmtCreateUser" />
        <result column="gmt_modified" property="gmtModified" />
        <result column="gmt_modified_user" property="gmtModifiedUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fk_table_name, fk_column_name, input_title, input_type, max_length, view_order, is_active, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>

</mapper>
