<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionPersonRegistrationMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ConventionPersonRegistration">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_convention_person_id" jdbcType="BIGINT" property="fkConventionPersonId" />
    <result column="fk_convention_registration_id" jdbcType="BIGINT" property="fkConventionRegistrationId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

  <insert id="insert" parameterType="com.get.salecenter.entity.ConventionPersonRegistration">
    insert into r_convention_person_registration (id, fk_convention_person_id, fk_convention_registration_id,
      gmt_create, gmt_create_user, gmt_modified,
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkConventionPersonId,jdbcType=BIGINT}, #{fkConventionRegistrationId,jdbcType=BIGINT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionPersonRegistration">
    insert into r_convention_person_registration
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionPersonId != null">
        fk_convention_person_id,
      </if>
      <if test="fkConventionRegistrationId != null">
        fk_convention_registration_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionPersonId != null">
        #{fkConventionPersonId,jdbcType=BIGINT},
      </if>
      <if test="fkConventionRegistrationId != null">
        #{fkConventionRegistrationId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getConventionRegistrationId" parameterType="java.lang.Long" resultType="long">
    select
     fk_convention_registration_id
    from
     r_convention_person_registration
    where
     fk_convention_person_id = #{conventionPersonId}

  </select>
    <select id="getConventionRegistrationIds" resultType="java.lang.Long">
      SELECT
        DISTINCT cpr.fk_convention_registration_id
      FROM
        r_convention_person_registration as cpr
          INNER JOIN
        m_convention_person as cp
        ON
          cpr.fk_convention_person_id = cp.id and cp.fk_convention_id=#{fkConventionId}
      ORDER BY cpr.fk_convention_registration_id
    </select>


  <select id="getConventionPersonByRegistrationIds" resultType="com.get.salecenter.vo.ConventionPersonVo">
    SELECT
    m_convention_person.*,
    r_convention_person_registration.fk_convention_registration_id
    FROM
    r_convention_person_registration
    INNER JOIN
    m_convention_person
    ON
    r_convention_person_registration.fk_convention_person_id = m_convention_person.id
    INNER JOIN
    m_convention_registration
    ON
    r_convention_person_registration.fk_convention_registration_id = m_convention_registration.id
    where
    1=1
    <if test="ids != null and ids.size()>0">
      and r_convention_person_registration.fk_convention_registration_id in
      <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </select>
    <select id="getConventionPersonProvideNameMapByIds"
            resultType="com.get.salecenter.vo.ConventionPersonVo">
      SELECT
        b.id,
        c.provider_name as company
      FROM
        r_convention_person_registration a
          LEFT JOIN m_convention_person b on a.fk_convention_person_id = b.id
          LEFT JOIN m_convention_registration c on a.fk_convention_registration_id = c.id
      where b.id in
      <foreach collection="institutionAgentIds" item="institutionAgentId" index="index" open="(" separator="," close=")">
        #{institutionAgentId}
      </foreach>
    </select>
    <select id="checkConventionPersonRegistration" resultType="java.lang.Boolean">
        SELECT
          count(1)>0
        FROM
          m_convention_registration AS mcr
          INNER JOIN r_convention_person_registration AS rcpr ON mcr.id = rcpr.fk_convention_registration_id
        WHERE
          rcpr.fk_convention_person_id = #{conventionPersonId}
          and mcr.receipt_code = #{receiptCode}
    </select>
</mapper>
