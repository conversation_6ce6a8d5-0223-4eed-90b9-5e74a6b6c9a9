package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.EventBillEventSummary;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface EventBillEventSummaryMapper extends BaseMapper<EventBillEventSummary>, GetMapper<EventBillEventSummary> {
    @Override
    int insert(EventBillEventSummary record);

    int insertSelective(EventBillEventSummary record);
}