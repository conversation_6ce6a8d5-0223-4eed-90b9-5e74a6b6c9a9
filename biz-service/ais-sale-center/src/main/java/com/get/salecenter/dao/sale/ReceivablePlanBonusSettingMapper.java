package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.ReceivablePlanBonusSetting;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ReceivablePlanBonusSettingMapper extends BaseMapper<ReceivablePlanBonusSetting> {
    int insert(ReceivablePlanBonusSetting receivablePlanBonus);

    List<ReceivablePlanBonusSetting> selectBatchReceivableIds(@Param("receivableIds") List<Long> receivableIds);
}
