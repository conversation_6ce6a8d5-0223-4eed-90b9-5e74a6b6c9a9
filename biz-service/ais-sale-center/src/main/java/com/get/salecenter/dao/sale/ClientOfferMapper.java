package com.get.salecenter.dao.sale;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.ClientOfferVo;
import com.get.salecenter.entity.ClientOffer;
import com.get.salecenter.dto.ClientOfferDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * author:Neil
 * Time: 16:01
 * Date: 2022/8/17
 * Description:
 */
@Mapper
public interface ClientOfferMapper extends GetMapper<ClientOffer> {
    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [clientId]
     * <AUTHOR>
     */
    Boolean isExistByClientId(@Param("clientId")Long clientId);

    List<ClientOfferVo> getClientOfferList(@Param("clientOfferDto") ClientOfferDto clientOfferDto, @Param("staffFollowerIds")List<Long> staffFollowerIds);

    List<BaseSelectEntity> getClientOfferSelect(@Param("fkClientId")Long fkClientId);

    List<BaseSelectEntity> getClientAgentSelect(Long fkClientId);

    ClientOfferVo getClientOfferInfoById(Long id);

    Boolean isExistByStepId(Long id);
}
