<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionPersonStaffMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ConventionPersonStaff">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_convention_person_id" jdbcType="BIGINT" property="fkConventionPersonId" />
    <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>


  <insert id="insert" parameterType="com.get.salecenter.entity.ConventionPersonStaff">
    insert into r_convention_person_staff (id, fk_convention_person_id, fk_staff_id, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkConventionPersonId,jdbcType=BIGINT}, #{fkStaffId,jdbcType=BIGINT}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionPersonStaff">
    insert into r_convention_person_staff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionPersonId != null">
        fk_convention_person_id,
      </if>
      <if test="fkStaffId != null">
        fk_staff_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionPersonId != null">
        #{fkConventionPersonId,jdbcType=BIGINT},
      </if>
      <if test="fkStaffId != null">
        #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getStaffId" parameterType="java.lang.Long" resultType="long">
    select
     fk_staff_id
    from
     r_convention_person_staff
    where
     fk_convention_person_id = #{id}
  </select>
</mapper>