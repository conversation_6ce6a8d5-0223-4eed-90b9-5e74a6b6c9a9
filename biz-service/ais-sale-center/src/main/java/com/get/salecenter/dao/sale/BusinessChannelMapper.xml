<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.BusinessChannelMapper">
  <select id="getDatas" resultType="com.get.salecenter.vo.BusinessChannelVo">
     select m_business_channel.* from m_business_channel
      inner join r_business_channel_company r on r.fk_business_channel_id= m_business_channel.id
     where 1 = 1
    <if test="businessChannelDto.fkCompanyId!=null ">
      and r.fk_company_id = #{businessChannelDto.fkCompanyId}
    </if>
    <if test="businessChannelDto.fkTypeKey!=null and businessChannelDto.fkTypeKey!=''">
      and fk_type_key = #{businessChannelDto.fkTypeKey}
    </if>
    <if test="businessChannelDto.isActive!=null">
      and is_active = #{businessChannelDto.isActive}
    </if>
    <if test="businessChannelDto.keyWord!=null  and businessChannelDto.keyWord!=''">
      and (name LIKE CONCAT('%', #{businessChannelDto.keyWord}, '%') OR name_chn LIKE CONCAT('%', #{businessChannelDto.keyWord}, '%') OR num LIKE CONCAT('%', #{businessChannelDto.keyWord}, '%'))
    </if>
    ORDER BY CONVERT(name USING gbk),is_active desc
  </select>

  <select id="getNameById" parameterType="java.lang.Long" resultType="string">
    select
      name
    from
      m_business_channel b
    where
      b.id = #{id}
  </select>
  <select id="channelSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT m.id,concat(if(is_active=0,'【无效】',''),
                     CASE
                       WHEN IFNULL(name_chn,'') = ''
                         THEN
                         name ELSE CONCAT(name, '（', name_chn, '）' )
                       END)  name,
                 is_active status from m_business_channel m
    inner join r_business_channel_company r on r.fk_business_channel_id= m.id
    WHERE 1=1
    <if test="tableName!=null">
      and  fk_type_key = #{tableName}
    </if>
    <if test="companyId!=null">
      and  r.fk_company_id = #{companyId}
    </if>
  </select>
  <select id="getFullNameById" parameterType="java.lang.Long" resultType="string">
    select
      concat(if(is_active=0,'【无效】',''),
             CASE
               WHEN IFNULL(name_chn,'') = ''
                 THEN
                 name ELSE CONCAT(name, '（', name_chn, '）' )
               END)   fullName
    from
      m_business_channel
    where
      id = #{id}
  </select>

  <select id="getTargetName" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    select
    m.id, concat(if(is_active=0,'【无效】',''),
    CASE
    WHEN IFNULL(name_chn,'') = ''
    THEN
    name ELSE CONCAT(name, '（', name_chn, '）' )
    END)   name
    from
    m_business_channel m
    inner join r_business_channel_company r on r.fk_business_channel_id=m.id
    WHERE is_active = 1
    <if test="companyId!=null">
      and  r.fk_company_id = #{companyId}
    </if>
    <if test="tableName!=null">
      and  fk_type_key = #{tableName}
    </if>
    ORDER BY is_active DESC
  </select>
  <select id="getChannelIds" resultType="java.lang.Long">
    SELECT id from m_business_channel where  is_active = 1
    <if test="tableName!=null">
      and  fk_type_key = #{tableName}
    </if>
    and (name LIKE CONCAT('%', #{channelName}, '%') OR name_chn LIKE CONCAT('%', #{channelName}, '%') OR num LIKE CONCAT('%', #{channelName}, '%')
    or concat(name,"（",name_chn,"）") LIKE CONCAT('%', #{channelName}, '%')
    )
  </select>
  <select id="getPlanIdsByTableNameAndChannelId" resultType="com.get.salecenter.vo.BusinessChannelVo">
      SELECT
        p.id planId,
        p.fk_type_key,
        m.*
      FROM
        m_receivable_plan p
          INNER JOIN ${tableName} m ON p.fk_type_target_id = m.id  AND fk_type_key = #{tableName}
          INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = p.id
          INNER JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
          INNER JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id AND d.fk_type_key = #{fkTypeKey}
      WHERE
        fk_business_channel_id = #{channelId}
        AND d.id = #{receiptFormId}
        AND m.STATUS != 0
        AND p.STATUS = 1
      GROUP BY p.id
    </select>
    <select id="getBusinessId" resultType="java.lang.Long">
      SELECT
          b.id
      FROM
          m_business_channel b
          <if test="fkTypeKey == 'm_student_insurance'">
            INNER JOIN m_student_insurance msi ON b.id = msi.fk_business_channel_id
          </if>
          <if test="fkTypeKey == 'm_student_accommodation'">
            INNER JOIN m_student_accommodation msi ON b.id = msi.fk_business_channel_id
          </if>
      WHERE
          msi.id in
          <foreach collection="targetIds" separator="," item="item" open="(" close=")">
            #{item}
          </foreach>
    </select>
  <select id="getNamesByIds" resultType="com.get.salecenter.vo.BusinessChannelVo">
    select
      b.id,
      b.name
    from
      m_business_channel b
    where
      b.id in
      <foreach collection="ids" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
  </select>
    <select id="getBusinessProviderIdByAccIds" resultType="java.lang.Long">
      SELECT
        fk_business_provider_id
      FROM
        m_student_accommodation
      <if test="ids!=null and ids.size>0">
        WHERE
            id IN
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
      </if>
    </select>
    <select id="getChannelByWork" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      SELECT
      m.id,
      m.NAME
      FROM
      m_business_channel m
      inner join r_business_channel_company r on r.fk_business_channel_id =m.id
      WHERE
      fk_type_key = #{fkType}
      AND (REPLACE (NAME, ' ', '') LIKE CONCAT('%', #{keyWord}, '%')
      OR REPLACE (name_chn, ' ', '') LIKE CONCAT('%', #{keyWord}, '%'))
      <if test="companyIds!=null and companyIds.size>0">
        AND r.fk_company_id IN
        <foreach collection="companyIds" item="cid" separator="," open="(" close=")">
          #{cid}
        </foreach>
      </if>
    </select>
  <select id="getFullNameByIds" resultType="com.get.salecenter.vo.BusinessChannelVo">
    select
      id,
      concat(if(is_active=0,'【无效】',''),
             CASE
               WHEN IFNULL(name_chn,'') = ''
                 THEN
                 name ELSE CONCAT(name, '（', name_chn, '）' )
               END)   fullName
    from
      m_business_channel
    where 1=1
    <if test="ids !=null and ids.size()>0">
      and id in
      <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
        #{id}
      </foreach>
    </if>
  </select>
  <select id="getPlanIdsByChannel" resultType="com.get.salecenter.vo.BusinessChannelVo">
        SELECT
          p.id planId,
          p.fk_type_key,
          m.`name` as fullName
        FROM
        m_receivable_plan p
          INNER JOIN m_business_channel m ON  p.fk_type_target_id= m.id AND p.fk_type_key = #{fkTypeKey}
          INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = p.id
          INNER JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
          INNER JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id AND d.fk_type_key = #{fkTypeKey}
      WHERE
            m.id = #{channelId}
        AND d.id = #{receiptFormId}
        AND p.STATUS = 1
      GROUP BY p.id
  </select>
</mapper>