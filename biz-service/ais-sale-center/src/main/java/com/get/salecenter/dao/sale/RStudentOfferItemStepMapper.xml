<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.RStudentOfferItemStepMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.RStudentOfferItemStep">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_student_offer_item_id" jdbcType="BIGINT" property="fkStudentOfferItemId"/>
        <result column="fk_student_offer_item_step_id" jdbcType="BIGINT" property="fkStudentOfferItemStepId"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_student_offer_item_id, fk_student_offer_item_step_id, remark, gmt_create, 
    gmt_create_user, gmt_modified, gmt_modified_user
    </sql>

    <insert id="insertSelective" parameterType="com.get.salecenter.entity.RStudentOfferItemStep" useGeneratedKeys="true"
            keyProperty="id">
        insert into r_student_offer_item_step
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkStudentOfferItemId != null">
                fk_student_offer_item_id,
            </if>
            <if test="fkStudentOfferItemStepId != null">
                fk_student_offer_item_step_id,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkStudentOfferItemId != null">
                #{fkStudentOfferItemId,jdbcType=BIGINT},
            </if>
            <if test="fkStudentOfferItemStepId != null">
                #{fkStudentOfferItemStepId,jdbcType=BIGINT},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!--<select id="getMaxStepOrder" resultType="java.lang.Integer">-->
    <!--SELECT min(p.step_order) step_order from r_student_offer_item_step s-->
    <!--left join u_student_offer_item_step p on s.fk_student_offer_item_step_id=p.id-->
    <!--where s.fk_student_offer_item_id=#{itemId}-->
    <!--</select>-->

    <select id="isExistByItemStepId" resultType="java.lang.Boolean">
      SELECT IFNULL(max(id),0) id from r_student_offer_item_step where fk_student_offer_item_step_id=#{stepId}
    </select>

    <select id="selectItemIdByStepIdAndGmtCreate" resultType="java.lang.Long">
        select fk_student_offer_item_id from  r_student_offer_item_step where 1=1
        <if test="fkStudentOfferItemStepId !=null">
            and fk_student_offer_item_step_id = #{fkStudentOfferItemStepId}
        </if>
        --  状态变更时间
        <if test="endGmtCreate != null">
            and DATE_FORMAT(gmt_create,'%Y-%m-%d') &gt;= DATE_FORMAT(#{endGmtCreate},'%Y-%m-%d')
        </if>
        <if test="startGmtCreate != null">
            and DATE_FORMAT(gmt_create,'%Y-%m-%d') &lt;= DATE_FORMAT(#{startGmtCreate},'%Y-%m-%d')
        </if>
    </select>
    <insert id="insertRSteps">
        insert into r_student_offer_item_step
         (fk_student_offer_item_id,fk_student_offer_item_step_id, gmt_create,gmt_create_user)
        values
         <foreach collection="rStudentOfferItemSteps" item="step" index="index"  separator=",">
             (#{step.fkStudentOfferItemId,jdbcType=BIGINT},
             #{step.fkStudentOfferItemStepId,jdbcType=BIGINT},
             #{step.gmtCreate,jdbcType=TIMESTAMP},
             #{step.gmtCreateUser,jdbcType=VARCHAR})
         </foreach>
    </insert>

    <select id="selectFailureOfferItemSteps" resultType="java.lang.Long">
        select fk_student_offer_item_id from r_student_offer_item_step
        where fk_student_offer_item_step_id = 9
        <if test="ids!=null and ids.size>0">
           and fk_student_offer_item_id IN
            <foreach collection="ids" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectOfferStepCoe" resultType="com.get.salecenter.vo.SelItem">
        select fk_student_offer_item_id as key_id,gmt_create as val from r_student_offer_item_step
        where fk_student_offer_item_step_id = 6
        <if test="ids!=null and ids.size>0">
            and fk_student_offer_item_id IN
            <foreach collection="ids" open="(" close=")" separator="," item="item">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="failureIds" resultType="com.get.salecenter.entity.RStudentOfferItemStep">
        SELECT fk_student_offer_item_id,max(gmt_create) as gmt_create FROM r_student_offer_item_step
        where fk_student_offer_item_step_id = 9
        and fk_student_offer_item_id IN
        <foreach collection="ids" open="(" close=")" separator="," item="item">
            #{item}
        </foreach>
        GROUP BY id
    </select>
    <select id="getLastRemarkByIds" resultType="com.get.salecenter.vo.SelItem">
        SELECT
            s.fk_student_offer_item_id as keyId,
            s.remark as val
        FROM
            r_student_offer_item_step AS s
        INNER JOIN (
            SELECT
                fk_student_offer_item_id,
                MAX(id) AS id
            FROM
                r_student_offer_item_step
            WHERE
                remark != ''
            AND remark IS NOT NULL
            <if test="ids!=null and ids.size>0">
                AND fk_student_offer_item_id IN
                <foreach collection="ids" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
            GROUP BY
                fk_student_offer_item_id
        ) AS t ON s.fk_student_offer_item_id = t.fk_student_offer_item_id
        AND s.id = t.id;
    </select>
</mapper>