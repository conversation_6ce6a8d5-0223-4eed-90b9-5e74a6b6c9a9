<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentRoleStaffMapper">
    <insert id="insert" parameterType="com.get.salecenter.entity.AgentRoleStaff" keyProperty="id" useGeneratedKeys="true">
    insert into r_agent_role_staff (id, fk_company_id, fk_type_key,
      fk_agent_id, fk_area_country_id_agent, fk_area_state_id_agent,
      fk_student_project_role_id, fk_area_country_id,
      fk_staff_id, gmt_create, gmt_create_user,
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkCompanyId,jdbcType=BIGINT}, #{fkTypeKey,jdbcType=VARCHAR},
      #{fkAgentId,jdbcType=BIGINT}, #{fkAreaCountryIdAgent,jdbcType=BIGINT}, #{fkAreaStateIdAgent,jdbcType=BIGINT},
      #{fkStudentProjectRoleId,jdbcType=BIGINT}, #{fkAreaCountryId,jdbcType=BIGINT},
      #{fkStaffId,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR},
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
    <insert id="insertSelective" parameterType="com.get.salecenter.entity.AgentRoleStaff" keyProperty="id" useGeneratedKeys="true">
        insert into r_agent_role_staff
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkCompanyId != null">
                fk_company_id,
            </if>
            <if test="fkTypeKey != null">
                fk_type_key,
            </if>
            <if test="fkAgentId != null">
                fk_agent_id,
            </if>
            <if test="fkAreaCountryIdAgent != null">
                fk_area_country_id_agent,
            </if>
            <if test="fkAreaStateIdAgent != null">
                fk_area_state_id_agent,
            </if>
            <if test="fkStudentProjectRoleId != null">
                fk_student_project_role_id,
            </if>
            <if test="fkAreaCountryId != null">
                fk_area_country_id,
            </if>
            <if test="fkStaffId != null">
                fk_staff_id,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkCompanyId != null">
                #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="fkTypeKey != null">
                #{fkTypeKey,jdbcType=VARCHAR},
            </if>
            <if test="fkAgentId != null">
                #{fkAgentId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCountryIdAgent != null">
                #{fkAreaCountryIdAgent,jdbcType=BIGINT},
            </if>
            <if test="fkAreaStateIdAgent != null">
                #{fkAreaStateIdAgent,jdbcType=BIGINT},
            </if>
            <if test="fkStudentProjectRoleId != null">
                #{fkStudentProjectRoleId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCountryId != null">
                #{fkAreaCountryId,jdbcType=BIGINT},
            </if>
            <if test="fkStaffId != null">
                #{fkStaffId,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.get.salecenter.entity.AgentRoleStaff">
        update r_agent_role_staff
        <set>
            <if test="fkCompanyId != null">
                fk_company_id = #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="fkTypeKey != null">
                fk_type_key = #{fkTypeKey,jdbcType=VARCHAR},
            </if>
            <if test="fkAgentId != null">
                fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCountryIdAgent != null">
                fk_area_country_id_agent = #{fkAreaCountryIdAgent,jdbcType=BIGINT},
            </if>
            <if test="fkAreaStateIdAgent != null">
                fk_area_state_id_agent = #{fkAreaStateIdAgent,jdbcType=BIGINT},
            </if>
            <if test="fkStudentProjectRoleId != null">
                fk_student_project_role_id = #{fkStudentProjectRoleId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCountryId != null">
                fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
            </if>
            <if test="fkStaffId != null">
                fk_staff_id = #{fkStaffId,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.salecenter.entity.AgentRoleStaff">
    update r_agent_role_staff
    set fk_company_id = #{fkCompanyId,jdbcType=BIGINT},
      fk_type_key = #{fkTypeKey,jdbcType=VARCHAR},
      fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
      fk_area_country_id_agent = #{fkAreaCountryIdAgent,jdbcType=BIGINT},
      fk_area_state_id_agent = #{fkAreaStateIdAgent,jdbcType=BIGINT},
      fk_student_project_role_id = #{fkStudentProjectRoleId,jdbcType=BIGINT},
      fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
      fk_staff_id = #{fkStaffId,jdbcType=BIGINT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <select id="getAgentRoleStaffs" parameterType="com.get.salecenter.vo.AgentRoleStaffVo"
            resultType="com.get.salecenter.vo.AgentVo">
        SELECT
        group_concat( DISTINCT IFNULL(ars.fk_student_project_role_id,"0"),IF(ars.fk_staff_id is null,"","-"),IFNULL(ars.fk_staff_id,"-0"),IF(spr.view_order is null,"","-"),IFNULL(spr.view_order,"-0"),IF(ars.fk_area_country_id is null,"","-"),IFNULL(ars.fk_area_country_id,"-0"),IF(ars.fk_type_key is null or ars.fk_type_key = '',"","-"),IF(ars.fk_type_key is null or ars.fk_type_key = '',"-0",ars.fk_type_key)) AS roleStaffStr,
        group_concat( DISTINCT ac.fk_company_id ) companyIdStr,
        group_concat( DISTINCT ars.fk_student_project_role_id) AS roleStr,
        a.id,
        a.NAME AS name,
        a.num,
        la.NAME AS parentName,
        la.num AS parentNum,
        a.fk_area_country_id AS fkAreaCountryId,
        a.fk_area_state_id AS fkAreaStateId,
        a.fk_area_city_id AS fkAreaCityId,
        a.is_active AS isActive,
        group_concat(DISTINCT ars.fk_type_key) AS typeKeyStr
        FROM
        m_agent AS a
        LEFT JOIN r_agent_role_staff AS ars ON a.id = ars.fk_agent_id
        <if test="agentRoleStaffDto.fkTypeKey != null and agentRoleStaffDto.fkTypeKey != ''">
            AND  ars.fk_type_key = #{agentRoleStaffDto.fkTypeKey}
        </if>
<!--        <if test="agentRoleStaffDto.fkTypeKey == null or agentRoleStaffDto.fkTypeKey == ''">-->
<!--            AND  (ars.fk_type_key is null or ars.fk_type_key = '')-->
<!--        </if>-->
        LEFT JOIN m_agent AS la ON la.id = a.fk_parent_agent_id
        LEFT JOIN r_agent_company AS ac ON ac.fk_agent_id = a.id
        LEFT JOIN r_agent_staff AS s ON s.fk_agent_id = a.id AND s.is_active = 1
        LEFT JOIN r_staff_bd_code AS sbc ON sbc.fk_staff_id = s.fk_staff_id
        LEFT JOIN u_student_project_role AS spr ON spr.id = ars.fk_student_project_role_id
        <where>
            <if test="agentRoleStaffDto.fkCompanyId != '' and agentRoleStaffDto.fkCompanyId != null">
                AND ac.fk_company_id = #{agentRoleStaffDto.fkCompanyId}
            </if>
            <if test="agentRoleStaffDto.fkAreaCountryId != '' and agentRoleStaffDto.fkAreaCountryId != null">
                AND a.fk_area_country_id = #{agentRoleStaffDto.fkAreaCountryId}
            </if>
            <if test="agentRoleStaffDto.fkAreaStateId != '' and agentRoleStaffDto.fkAreaStateId != null">
                AND a.fk_area_state_id = #{agentRoleStaffDto.fkAreaStateId}
            </if>
            <if test="agentRoleStaffDto.fkAreaCityId != '' and agentRoleStaffDto.fkAreaCityId != null">
                AND a.fk_area_city_id = #{agentRoleStaffDto.fkAreaCityId}
            </if>
            <if test="agentRoleStaffDto.isActive != null">
                AND a.is_active = #{agentRoleStaffDto.isActive}
            </if>
            <if test="agentRoleStaffDto.bdCode != '' and agentRoleStaffDto.bdCode != null">
                AND sbc.bd_code = #{agentRoleStaffDto.bdCode}
            </if>
            <if test="agentRoleStaffDto.keyWord != '' and agentRoleStaffDto.keyWord != null">
                AND (a.num LIKE CONCAT('%', #{agentRoleStaffDto.keyWord}, '%') OR a.name LIKE CONCAT('%', #{agentRoleStaffDto.keyWord}, '%'))
            </if>
            <if test="agentRoleStaffDto.agentCountryId != '' and agentRoleStaffDto.agentCountryId != null">
                AND ars.fk_area_country_id = #{agentRoleStaffDto.agentCountryId}
            </if>
            <if test="agentRoleStaffDto.fkTypeKey != '' and agentRoleStaffDto.fkTypeKey != null">
                AND ars.fk_type_key = #{agentRoleStaffDto.fkTypeKey}
            </if>
<!--            <if test="agentRoleStaffDto.fkTypeKey == '' or agentRoleStaffDto.fkTypeKey == null">-->
<!--                AND ars.fk_type_key is null or ars.fk_type_key = ''-->
<!--            </if>-->
            <if test="agentRoleStaffDto.studentProjectRoleId != '' and agentRoleStaffDto.studentProjectRoleId != null">
                AND ars.fk_student_project_role_id = #{agentRoleStaffDto.studentProjectRoleId}
            </if>
            <if test="agentRoleStaffDto.fkStaffId != '' and agentRoleStaffDto.fkStaffId != null">
                AND ars.fk_staff_id = #{agentRoleStaffDto.fkStaffId}
            </if>
            <!--            <if test="fkTypeKey == 'm_student_offer' and fkTypeKey == null">-->
            <!--                AND ars.fk_type_key = "m_student_offer" and ars.fk_type_key is null-->
            <!--            </if>-->
            <!--            <if test="fkCompanyId != '' and fkCompanyId != null">-->
            <!--                AND (spr.fk_company_id = #{fkCompanyId} OR spr.fk_company_id IS NULL)-->
            <!--            </if>-->
            <!--            <if test="fkTypeKey != '' and fkTypeKey != null">-->
            <!--                AND ars.fk_type_key = #{fkTypeKey}-->
            <!--            </if>-->
        </where>
        GROUP BY
        a.id
        <if test="agentRoleStaffDto.fkStudentProjectRoleId != '' and agentRoleStaffDto.fkStudentProjectRoleId != null">
            HAVING !FIND_IN_SET(#{agentRoleStaffDto.fkStudentProjectRoleId},roleStr) OR roleStr IS NULL
        </if>
        order by isActive desc, CONVERT(a.NAME USING gbk)
    </select>
    <select id="getCountryIds" resultType="java.lang.Long">
        select fk_area_country_id
        from r_agent_role_staff
        GROUP BY fk_area_country_id
    </select>
    <select id="selectAgentRoleStaffInfo" resultType="com.get.salecenter.entity.AgentRoleStaff">
        SELECT ars.*
        FROM r_agent_role_staff AS ars
        INNER JOIN u_student_project_role AS spr ON spr.id = ars.fk_student_project_role_id
        <where>
            <if test="agentId != null and agentId != ''">
                AND ars.fk_agent_id = #{agentId}
            </if>
            <if test="agentId == null and agentId == ''">
                AND ars.fk_agent_id is null
            </if>
            <if test="countryId != null and countryId != ''">
                AND  ars.fk_area_country_id = #{countryId}
            </if>
            <if test="companyId != null">
                AND  spr.fk_company_id = #{companyId}
            </if>
            <if test="fkTypeKey != null and fkTypeKey != ''">
                AND  ars.fk_type_key = #{fkTypeKey}
            </if>
            <if test="fkTypeKey == null and fkTypeKey == ''">
                AND  ars.fk_type_key is null
            </if>
        </where>
        order by spr.view_order desc
    </select>
    <select id="getCommonAgentRoleStaffs" resultType="com.get.salecenter.vo.AgentRoleStaffVo">
        SELECT
        rars.id,
        rars.fk_company_id,
        rars.fk_type_key,
        rars.fk_agent_id,
        rars.fk_student_project_role_id,
        rars.fk_area_country_id,
        rars.gmt_create,
        rars.fk_staff_id,
        rars.fk_area_country_id_agent,
        rars.fk_area_state_id_agent,
        rars.gmt_create_user,
        rars.gmt_modified,
        rars.gmt_modified_user
        FROM
        r_agent_role_staff AS rars  where rars.fk_agent_id is null
        <if test="agentRoleStaffDto.fkCompanyId != null and agentRoleStaffDto.fkCompanyId != ''">
            AND  rars.fk_company_id = #{agentRoleStaffDto.fkCompanyId}
        </if>
        <if test="agentRoleStaffDto.fkTypeKey != null and agentRoleStaffDto.fkTypeKey != ''">
            AND rars.fk_type_key = #{agentRoleStaffDto.fkTypeKey}
        </if>
        <if test="agentRoleStaffDto.fkAreaCountryId != null and agentRoleStaffDto.fkAreaCountryId != ''">
            AND  rars.fk_area_country_id = #{agentRoleStaffDto.fkAreaCountryId}
        </if>
        <if test="agentRoleStaffDto.fkStudentProjectRoleId != null and agentRoleStaffDto.fkStudentProjectRoleId != ''">
            AND  rars.fk_student_project_role_id = #{agentRoleStaffDto.fkStudentProjectRoleId}
        </if>
        <if test="agentRoleStaffDto.fkStaffId != null and agentRoleStaffDto.fkStaffId != ''">
            AND  rars.fk_staff_id = #{agentRoleStaffDto.fkStaffId}
        </if>
        <if test="agentRoleStaffDto.fkAreaStateIdAgent != null and agentRoleStaffDto.fkAreaStateIdAgent != ''">
            AND  rars.fk_area_state_id_agent = #{agentRoleStaffDto.fkAreaStateIdAgent}
        </if>
        <if test="agentRoleStaffDto.fkAreaCountryIdAgent != null and agentRoleStaffDto.fkAreaCountryIdAgent != ''">
            AND  rars.fk_area_country_id_agent = #{agentRoleStaffDto.fkAreaCountryIdAgent}
        </if>
    </select>
    <select id="selectAgentRoleStaff" resultType="com.get.salecenter.entity.AgentRoleStaff">
        SELECT ars.*
        FROM r_agent_role_staff AS ars
        INNER JOIN u_student_project_role AS spr ON spr.id = ars.fk_student_project_role_id
        <where>
            <if test="agentId != null and agentId != ''">
                AND ars.fk_agent_id = #{agentId}
            </if>
            <if test="countryId != null and countryId != ''">
                AND  ars.fk_area_country_id = #{countryId}
            </if>
            <if test="companyId != null">
                AND  spr.fk_company_id = #{companyId}
            </if>
            <if test="fkTypeKey != null and fkTypeKey != ''">
                AND  ars.fk_type_key = #{fkTypeKey}
            </if>
            <if test="agentCountryId != null and agentCountryId != ''">
                AND  ars.fk_area_country_id_agent = #{agentCountryId}
            </if>
            <if test="agentStateId != null and agentStateId != ''">
                AND  ars.fk_area_state_id_agent = #{agentStateId}
            </if>
            <if test="roleId != null and roleId != ''">
                AND  ars.fk_student_project_role_id = #{roleId}
            </if>
        </where>
        order by spr.view_order desc
    </select>
</mapper>