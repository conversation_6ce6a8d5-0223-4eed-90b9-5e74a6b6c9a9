package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.StaffCommissionInstitutionVo;
import com.get.salecenter.entity.StaffCommissionInstitution;
import com.get.salecenter.dto.StaffCommissionInstitutionDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StaffCommissionInstitutionMapper extends GetMapper<StaffCommissionInstitution> {

    /**
     *
     * @param iPage
     * @param staffCommissionInstitutionDto
     * @return
     */
    List<StaffCommissionInstitutionVo> getStaffCommissionInstitutionDtos(IPage<StaffCommissionInstitutionVo> iPage, @Param("staffCommissionInstitutionDto") StaffCommissionInstitutionDto staffCommissionInstitutionDto);
}