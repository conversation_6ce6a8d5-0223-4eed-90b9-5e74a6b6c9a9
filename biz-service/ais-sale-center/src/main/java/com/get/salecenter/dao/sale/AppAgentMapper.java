package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.AppAgentListVo;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.AppAgent;
import com.get.salecenter.dto.AgentAnnualSummaryDto;
import com.get.salecenter.dto.AppAgentListDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AppAgentMapper extends GetMapper<AppAgent> {

    /**
     * 根据条件查询
     *
     * @param iPage
     * @param appAgentListDto
     * @param agentAnnualSummaryDto
     * @return
     */
    List<AppAgentListVo> getAppAgents(IPage<Agent> iPage,
                                      @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                      @Param("appAgentListDto") AppAgentListDto appAgentListDto,
                                      @Param("agentAnnualSummaryDto") AgentAnnualSummaryDto agentAnnualSummaryDto);

    /**
     * bd下拉
     * @param companyId
     * @return
     */
    List<BaseSelectEntity> getBdStaffSelect(@Param("companyId") Long companyId);
}