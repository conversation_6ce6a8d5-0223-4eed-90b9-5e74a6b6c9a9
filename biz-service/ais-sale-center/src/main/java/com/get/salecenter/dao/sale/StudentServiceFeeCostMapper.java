package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.vo.StudentServiceFeeCostListVo;
import com.get.salecenter.entity.StudentServiceFeeCost;
import com.get.salecenter.dto.StudentServiceFeeCostListDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-02-05
 */
@Mapper
public interface StudentServiceFeeCostMapper extends BaseMapper<StudentServiceFeeCost> {

    List<StudentServiceFeeCostListVo> getStudentServiceFeeCostListDtos(IPage<StudentServiceFeeCostListVo> iPage, @Param("studentServiceFeeCostListDto") StudentServiceFeeCostListDto studentServiceFeeCostListDto);
}
