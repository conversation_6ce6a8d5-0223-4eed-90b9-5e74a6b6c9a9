package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.EventPlanThemeVo;
import com.get.salecenter.entity.EventPlanTheme;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-12
 */
@Mapper
public interface EventPlanThemeMapper extends BaseMapper<EventPlanTheme> {

    /**
     * 列表数据
     * <AUTHOR>
     * @DateTime 2023/12/14 11:10
     */
    List<EventPlanThemeVo> getEventPlanThemes(@Param("fkEventPlanId") Long fkEventPlanId);

    /**
     * 获取最大排序
     * <AUTHOR>
     * @DateTime 2023/12/15 15:53
     */
    Integer getMaxViewOrder(@Param("fkEventPlanId") Long fkEventPlanId);

}
