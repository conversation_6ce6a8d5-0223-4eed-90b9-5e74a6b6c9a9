package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.salecenter.vo.ContactPersonVo;
import com.get.salecenter.vo.ContactPersonMobileSelectVo;
import com.get.salecenter.entity.SaleContactPerson;
import com.get.salecenter.dto.ContactPersonDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface ContactPersonMapper extends BaseMapper<SaleContactPerson> {


    List<ContactPersonVo> datas(ContactPersonDto contactPersonVo);

    List<BaseSelectEntity> getContactPersonEmailSelect(@Param("fkTableName") String fkTableName, @Param("fkTableId") Long fkTableId);

    Integer getContactPersonByType(@Param("fkContactPersonType") Long fkContactPersonType);

    List<ContactPersonVo> getExistContactPersonPM(@Param("email") String email, @Param("tel") String tel,
                                                  @Param("mobile") String mobile, @Param("companyId") Long companyId);

    /**
     * 获取代理联系人列表
     *
     * @param contactPersonVo
     * @return
     */
    List<ContactPersonVo> getContactPersons(IPage<SaleContactPerson> pages,
                                            @Param("contactPersonDto") ContactPersonDto contactPersonVo,
                                            @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                            @Param("companyIds") List<Long> companyIds);

    /**
     * 获取联系人信息
     *
     * @param agentIds
     * @return
     */
    List<ContactPersonVo> getContactPersonInfo(@Param("agentIds")Set<Long> agentIds);

    List<BaseSelectEntity> getContactPersonMobileAreaCodeSelect(@Param("fkTableName") String fkTableName, @Param("fkTableId") Long fkTableId);

    List<ContactPersonMobileSelectVo> getContactPersonMobileSelect(@Param("fkTableName") String fkTableName, @Param("fkTableId") Long fkTableId);
}