<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StaffCommissionActionMapper">
    <select id="getSuitablePolicies" resultType="com.get.salecenter.vo.StaffCommissionConfirmVo">
        SELECT
            a.id as fkStudentOfferItemId,
            a.fk_area_country_id,
            a.fk_institution_id,
            a.fk_institution_course_major_level_ids,
            a.fk_student_offer_id,
            g.fk_company_id,
            GROUP_CONCAT(DISTINCT c.role_key) roleKeys,
            GROUP_CONCAT(DISTINCT e.id) stepIds
        FROM
            m_student_offer_item a
                INNER JOIN s_student_project_role_staff b on a.fk_student_offer_id = b.fk_table_id and b.fk_table_name = 'm_student_offer'
                LEFT JOIN u_student_project_role c on c.id = b.fk_student_project_role_id
                LEFT JOIN r_student_offer_item_step d on d.fk_student_offer_item_id = a.id
                LEFT JOIN u_student_offer_item_step e on e.id = d.fk_student_offer_item_step_id
                LEFT JOIN m_student_offer f on f.id = a.fk_student_offer_id and f.`status`=1
                LEFT JOIN m_student g on a.fk_student_id = g.id


        where a.`status` = 1
        <if test="staffCommissionPolicyDto.fkStudentId !=null">
          and a.fk_student_id = #{staffCommissionPolicyDto.fkStudentId}
        </if>

        GROUP BY a.id
    </select>
    <select id="getStudentOfferItemsByCommissionStepAndStudentKey"
            resultType="com.get.salecenter.vo.StudentOfferItemCommissionVo">
        SELECT
            a.id,
            a.fk_student_id,
            a.fk_student_offer_id,
            a.fk_area_country_id,
            a.fk_institution_id,
            a.fk_institution_course_major_level_ids,
            a.fk_student_offer_item_step_id,
            a.fk_institution_course_id,
            a.old_course_custom_name,
            b.fk_student_project_role_id,
            b.fk_staff_id,
            d.fk_company_id
        FROM
            m_student_offer_item AS a
                LEFT JOIN
            s_student_project_role_staff AS b
            ON
                a.fk_student_offer_id = b.fk_table_id and b.is_active = 1
                INNER JOIN
            (
                SELECT
                    a.id
                FROM
                    m_student_offer_item AS a
                        LEFT JOIN
                    r_student_offer_item_step AS b
                    ON
                        a.id = b.fk_student_offer_item_id
                        LEFT JOIN
                    u_staff_commission_step AS c
                    ON
                        FIND_IN_SET( b.fk_student_offer_item_step_id, c.fk_student_offer_item_step_ids )
                        LEFT JOIN
                    m_student_offer AS d
                    ON
                        a.fk_student_offer_id = d.id AND d.`status` = 1
                WHERE
                    a.`status` = 1
                  AND
                    a.fk_student_id = #{fkStudentId}
                  AND
                    c.id = #{fkStaffCommissionStepKeyId}
                GROUP BY
                    a.id
            ) AS c
            ON
                a.id = c.id
                LEFT JOIN
            m_student AS d
            ON
                a.fk_student_id = d.id
        WHERE
            a.`status` = 1
          AND a.fk_student_id = #{fkStudentId}
          AND a.is_follow != 1
    </select>
    <select id="getSettlementStaffByRoleKeysAndItemIds"
            resultType="com.get.salecenter.vo.StaffCommissionActionVo">
        SELECT
            a.id as fkStudentOfferItemId,
            b.fk_staff_id,
            c.role_key fk_student_project_role_key
        FROM
            m_student_offer_item a
                LEFT JOIN s_student_project_role_staff b on a.fk_student_offer_id = b.fk_table_id AND b.fk_table_name='m_student_offer' and b.is_active = 1
                LEFT JOIN u_student_project_role c on c.id = b.fk_student_project_role_id
        where 1=1
        <if test="roleKeys !=null and roleKeys.size()>0">
            and c.role_key in
            <foreach collection="roleKeys" item="rokeKey" index="index" open="(" separator="," close=")">
                #{rokeKey}
            </foreach>
        </if>
        <if test="itemIds !=null and itemIds.size()>0">
            and a.id in
            <foreach collection="itemIds" item="itemId" index="index" open="(" separator="," close=")">
                #{itemId}
            </foreach>
        </if>
    </select>
    <select id="getStaffCommissionSummary" resultType="com.get.salecenter.vo.StaffCommissionSummaryVo">
        SELECT
            distinct
            a.fk_student_id,
            b.fk_company_id,
            a.`status` as commissionStatus,
            b.`name`,
            b.last_name,
            b.first_name,
            CASE WHEN IFNULL(max(e.receiveStatus),0)=IFNULL(max(e.receiveStatus),0) THEN IFNULL(max(e.receiveStatus),0)
            ELSE 1
            END receiveStatus
        FROM
            `r_staff_commission_student` a
                INNER JOIN (
                SELECT a.id FROM ais_sale_center.m_student a
                INNER JOIN ais_sale_center.r_staff_commission_student c on a.id = c.fk_student_id
                INNER JOIN ais_permission_center.m_staff b ON a.gmt_create_user=b.login_id
                <if test="staffFollowerIds!=null and staffFollowerIds.size()>0">
                    AND b.id IN
                    <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                UNION ALL
                SELECT a.id FROM ais_sale_center.m_student a
                INNER JOIN ais_sale_center.r_staff_commission_student c on a.id = c.fk_student_id
                INNER JOIN ais_sale_center.m_student_offer b on b.fk_student_id = a.id and b.`status` = 1
                INNER JOIN ais_sale_center.s_student_project_role_staff d on d.fk_table_name = 'm_student_offer' and d.fk_table_id = b.id and d.is_active = 1
                <if test="staffFollowerIds!=null and staffFollowerIds.size()>0">
                    and d.fk_staff_id in
                    <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                </if>
                ) z on z.id = a.fk_student_id
                LEFT JOIN m_student b ON a.fk_student_id = b.id
                LEFT JOIN m_student_service_fee c on c.fk_student_id = b.id
<!--                <LEFT JOIN u_student_service_fee_type d on d.id = c.fk_student_service_fee_type_id and d.type_key = 'APPLICATION_SERVICE_FEE'>-->
                LEFT JOIN (
                SELECT
                    a.fk_type_target_id,
                    CASE WHEN SUM(IFNULL(b.sum_amount_receivable, 0)) + SUM(IFNULL(b.sum_amount_exchange_rate, 0)) - SUM(IFNULL(a.receivable_amount, 0)) = 0 THEN 2 ELSE
                        CASE WHEN SUM(IFNULL(b.sum_amount_receivable, 0)) + SUM(IFNULL(b.sum_amount_exchange_rate, 0)) > 0 THEN 1 ELSE 0 END
                        END AS receiveStatus
                FROM ais_sale_center.m_receivable_plan a
                         LEFT JOIN (
                    SELECT
                        a.fk_receivable_plan_id,
                        SUM(IFNULL(a.amount_receivable,0)) sum_amount_receivable,
                        SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate,
                        SUM(IFNULL(a.amount_hkd,0)) sum_amount_hkd,
                        SUM(IFNULL(a.amount_rmb,0)) sum_amount_rmb
                    FROM ais_finance_center.m_receipt_form_item a
                             LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id=b.id
                    WHERE b.`status`!=0
                    GROUP BY a.fk_receivable_plan_id
                ) b ON a.id=b.fk_receivable_plan_id
                         LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
                WHERE a.fk_type_key='m_student_service_fee' AND a.`status`!=0
                GROUP BY a.fk_type_target_id, a.fk_currency_type_num, c.type_name
            ) e on e.fk_type_target_id = c.id
            LEFT JOIN r_student_agent f on f.fk_student_id = a.fk_student_id and f.is_active = 1
            LEFT JOIN m_staff_commission_action g on g.fk_student_id = a.fk_student_id
            LEFT JOIN m_student_offer h on h.fk_student_id = a.fk_student_id and h.`status` = 1
            where 1=1
            <if test="fkAreaCountryIds !=null and fkAreaCountryIds.size()>0">
                and h.fk_area_country_id in
                <foreach collection="fkAreaCountryIds" item="fkAreaCountryId" index="index" open="(" separator="," close=")">
                    #{fkAreaCountryId}
                </foreach>
            </if>
            <if test="staffCommissionSummaryDto.fkCompanyId !=null">
                and b.fk_company_id = #{staffCommissionSummaryDto.fkCompanyId}
            </if>
            <if test="staffCommissionSummaryDto.agentId !=null">
                and f.fk_agent_id = #{staffCommissionSummaryDto.agentId}
            </if>
            <if test="staffCommissionSummaryDto.fkStaffId !=null">
                and g.fk_staff_id = #{staffCommissionSummaryDto.fkStaffId}
            </if>
            <if test="staffCommissionSummaryDto.studentName != null and staffCommissionSummaryDto.studentName != ''">
                and (b.name like CONCAT('%',#{staffCommissionSummaryDto.studentName},'%') or b.first_name like CONCAT('%',#{staffCommissionSummaryDto.studentName},'%')
                or b.last_name like CONCAT('%',#{staffCommissionSummaryDto.studentName},'%') or REPLACE(CONCAT(b.first_name,b.last_name),' ','') like concat('%',#{staffCommissionSummaryDto.studentName},'%')
                OR REPLACE(CONCAT(b.last_name,b.first_name),' ','') like concat('%',#{staffCommissionSummaryDto.studentName},'%') )
            </if>
            GROUP BY a.fk_student_id
    </select>
    <select id="getCourseTypeGroupAndSettlementStatus"
            resultType="com.get.salecenter.vo.StaffCommissionConfirmVo">
        SELECT
            a.id as fkStudentOfferItemId,
            GROUP_CONCAT(DISTINCT c.fk_course_type_group_id) as courseTypeGroupIds,
            GROUP_CONCAT(DISTINCT CONCAT(d.type_group_name,"（",d.type_group_name_chn,"）") SEPARATOR '/') as courseTypeGroupNames,
            CASE
                WHEN e.`status` is null THEN 0
                WHEN e.`status`= 0 THEN 1
                ELSE 2
                END settlementStatus
        FROM
            m_student_offer_item a
                LEFT JOIN ais_institution_center.r_institution_course_type b on a.fk_institution_course_id = b.fk_institution_course_id
                LEFT JOIN ais_institution_center.r_course_type_group_course_type c on b.fk_course_type_id = c.fk_course_type_id
                LEFT JOIN ais_institution_center.u_course_type_group d on d.id = c.fk_course_type_group_id
                LEFT JOIN m_staff_commission_action e on a.id = e.fk_student_offer_item_id
        where 1=1
        <if test="offerItemIds !=null and offerItemIds.size()>0">
            and a.id in
            <foreach collection="offerItemIds" item="offerItemId" index="index" open="(" separator="," close=")">
                #{offerItemId}
            </foreach>
        </if>
        GROUP BY a.id
    </select>
    <select id="getStaffCommissionDynamicSummary"
            resultType="com.get.salecenter.vo.StaffCommissionSummaryVo">
<!--       < SELECT-->
<!--            a.fk_student_id,-->
<!--            g.step_key fk_staff_commission_step_key,-->
<!--            CASE-->
<!--                WHEN MAX(h.`status`) is null THEN 0-->

<!--                WHEN MAX(h.`status`)=MIN(h.`status`) and MAX(h.`status`)=0 THEN 1-->
<!--                WHEN MAX(h.`status`)=MIN(h.`status`) and MAX(h.`status`)=1 THEN 2-->
<!--                ELSE 3-->
<!--                END settlementStatus,-->
<!--            CASE-->
<!--                WHEN MAX(c.is_add_app)=0 THEN 0-->
<!--                ELSE 1-->
<!--                END addAppStatus,-->
<!--            CASE-->
<!--                WHEN d.id is null THEN 0-->
<!--                ELSE 1-->
<!--                END institutionStatus,-->
<!--            CASE-->
<!--                WHEN c.id is null THEN 0-->
<!--                ELSE 1-->
<!--                END offerItemStatus-->
<!--        FROM-->
<!--            r_staff_commission_student a-->
<!--                INNER JOIN (-->
<!--                SELECT a.id FROM ais_sale_center.m_student a-->
<!--                INNER JOIN ais_sale_center.r_staff_commission_student c on a.id = c.fk_student_id-->
<!--                INNER JOIN ais_permission_center.m_staff b ON a.gmt_create_user=b.login_id-->
<!--                <if test="staffFollowerIds!=null and staffFollowerIds.size()>0">-->
<!--                    AND b.id IN-->
<!--                    <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">-->
<!--                        #{id}-->
<!--                    </foreach>-->
<!--                </if>-->
<!--                UNION ALL-->
<!--                SELECT a.id FROM ais_sale_center.m_student a-->
<!--                INNER JOIN ais_sale_center.r_staff_commission_student c on a.id = c.fk_student_id-->
<!--                INNER JOIN ais_sale_center.m_student_offer b on b.fk_student_id = a.id and b.`status` = 1-->
<!--                INNER JOIN ais_sale_center.s_student_project_role_staff d on d.fk_table_name = 'm_student_offer' and d.fk_table_id = b.id and d.is_active = 1-->
<!--                <if test="staffFollowerIds!=null and staffFollowerIds.size()>0">-->
<!--                    and d.fk_staff_id in-->
<!--                    <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">-->
<!--                        #{id}-->
<!--                    </foreach>-->
<!--                </if>-->
<!--                ) z on z.id = a.fk_student_id-->
<!--                LEFT JOIN `m_staff_commission_action` b on a.fk_student_id = b.fk_student_id-->
<!--                LEFT JOIN m_student_offer_item c on c.fk_student_id = a.fk_student_id and c.status = 1-->
<!--                LEFT JOIN r_staff_commission_institution d on c.fk_institution_id = d.fk_institution_id-->
<!--                LEFT JOIN m_student e on e.id = a.fk_student_id-->
<!--                LEFT JOIN  r_student_offer_item_step f on f.fk_student_offer_item_id = c.id-->
<!--                LEFT JOIN  u_staff_commission_step g on FIND_IN_SET(f.fk_student_offer_item_step_id,g.fk_student_offer_item_step_ids)-->
<!--                LEFT JOIN (-->
<!--                SELECT id,fk_student_id,fk_staff_commission_step_key,`status` FROM m_staff_commission_action-->
<!--                ) h on h.fk_student_id = a.fk_student_id and h.fk_staff_commission_step_key = g.step_key-->
<!--                LEFT JOIN m_student_offer i on i.fk_student_id = a.fk_student_id and i.`status` = 1-->
<!--&lt;!&ndash;                <LEFT JOIN (&ndash;&gt;-->
<!--&lt;!&ndash;                SELECT&ndash;&gt;-->
<!--&lt;!&ndash;                a.fk_student_id,&ndash;&gt;-->
<!--&lt;!&ndash;                g.step_key,&ndash;&gt;-->
<!--&lt;!&ndash;                CASE&ndash;&gt;-->
<!--&lt;!&ndash;                WHEN j.id is null THEN 0 ELSE 1&ndash;&gt;-->
<!--&lt;!&ndash;                END policyStatus&ndash;&gt;-->
<!--&lt;!&ndash;                FROM&ndash;&gt;-->
<!--&lt;!&ndash;                r_staff_commission_student a&ndash;&gt;-->
<!--&lt;!&ndash;                LEFT JOIN m_student_offer_item b ON a.fk_student_id = b.fk_student_id&ndash;&gt;-->
<!--&lt;!&ndash;                AND b.`status` = 1&ndash;&gt;-->
<!--&lt;!&ndash;                LEFT JOIN r_student_offer_item_step f ON f.fk_student_offer_item_id = b.id&ndash;&gt;-->
<!--&lt;!&ndash;                LEFT JOIN u_staff_commission_step g ON FIND_IN_SET( f.fk_student_offer_item_step_id, g.fk_student_offer_item_step_ids )&ndash;&gt;-->
<!--&lt;!&ndash;                LEFT JOIN s_student_project_role_staff k on k.fk_table_id = b.fk_student_offer_id and k.fk_table_name = 'm_student_offer' and k.is_active = 1&ndash;&gt;-->
<!--&lt;!&ndash;                LEFT JOIN u_student_project_role l on l.id = k.fk_student_project_role_id&ndash;&gt;-->
<!--&lt;!&ndash;                LEFT JOIN m_staff_commission_policy j ON g.step_key = j.fk_staff_commission_step_key&ndash;&gt;-->
<!--&lt;!&ndash;                AND ( FIND_IN_SET( j.fk_major_level_id,b.fk_institution_course_major_level_ids ) and  b.fk_institution_course_major_level_ids is not null)&ndash;&gt;-->
<!--&lt;!&ndash;                AND j.fk_area_country_id = b.fk_area_country_id and (j.fk_area_country_id = b.fk_area_country_id or j.fk_area_country_id is null)&ndash;&gt;-->
<!--&lt;!&ndash;                AND l.role_key = j.fk_student_project_role_key&ndash;&gt;-->
<!--&lt;!&ndash;                GROUP BY&ndash;&gt;-->
<!--&lt;!&ndash;                a.fk_student_id,g.step_key&ndash;&gt;-->
<!--&lt;!&ndash;                )	z1 on z1.fk_student_id = a.fk_student_id and z1.step_key = g.step_key>&ndash;&gt;-->
<!--        where 1=1-->
<!--          <if test="fkAreaCountryIds !=null and fkAreaCountryIds.size()>0">-->
<!--              and i.fk_area_country_id in-->
<!--              <foreach collection="fkAreaCountryIds" item="fkAreaCountryId" index="index" open="(" separator="," close=")">-->
<!--                  #{fkAreaCountryId}-->
<!--              </foreach>-->
<!--          </if>-->
<!--          <if test="studentIdList !=null and studentIdList.size()>0">-->
<!--              and a.fk_student_id in-->
<!--              <foreach collection="studentIdList" item="studentId" index="index" open="(" separator="," close=")">-->
<!--                  #{studentId}-->
<!--              </foreach>-->
<!--          </if>-->
<!--          <if test="commissionStepKeys !=null and commissionStepKeys.size()>0">-->
<!--              and (-->
<!--              b.fk_staff_commission_step_key in-->
<!--              <foreach collection="commissionStepKeys" item="commissionStepKey" index="index" open="(" separator="," close=")">-->
<!--                  #{commissionStepKey}-->
<!--              </foreach>-->
<!--              or b.id is null-->
<!--            )-->
<!--          </if>-->
<!--        <if test="staffCommissionSummaryDto.fkCompanyId !=null">-->
<!--          and e.fk_company_id = #{staffCommissionSummaryDto.fkCompanyId}-->
<!--        </if>-->
<!--        GROUP BY a.fk_student_id,g.step_key>-->

        SELECT
        a.fk_student_id,
        j.fk_staff_commission_step_key,
        CASE
        WHEN b.id is null THEN 4
        WHEN f.`status` is null and j.fk_staff_commission_step_key = 'COLLEGE_COMMISSION' and g.id is null THEN 4
        WHEN f.`status` is null and j.fk_staff_commission_step_key = 'ADD_APPLICATION' and MAX(b.is_add_app)=0 THEN 4
        WHEN f.`status` is not null and MAX(f.`status`)=MIN(f.`status`) and MAX(f.`status`)= 0 THEN 1
        WHEN f.`status` is not null and MAX(f.`status`)=MIN(f.`status`) and MAX(f.`status`)=1 THEN 2
        WHEN f.`status` is not null and MAX(f.`status`)=1 and MIN(f.`status`)=0 THEN 3
        WHEN f.`status` is null and (j.id is null or GROUP_CONCAT(DISTINCT z.role_key) is null)THEN 4
        WHEN f.`status` is null and j.id is not null and GROUP_CONCAT(DISTINCT z.role_key) is not null and GROUP_CONCAT(DISTINCT d.fk_student_offer_item_id) is not null THEN 0
        WHEN f.`status` is null and j.id is not null and GROUP_CONCAT(DISTINCT z.role_key) is not null and GROUP_CONCAT(DISTINCT d.fk_student_offer_item_id) is null THEN 5
        ELSE 4
        END settlementStatus
        FROM
        r_staff_commission_student a
        INNER JOIN (
        SELECT a.id FROM ais_sale_center.m_student a
        INNER JOIN ais_sale_center.r_staff_commission_student c on a.id = c.fk_student_id
        INNER JOIN ais_permission_center.m_staff b ON a.gmt_create_user=b.login_id
        <if test="staffFollowerIds!=null and staffFollowerIds.size()>0">
            AND b.id IN
            <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        UNION ALL
        SELECT a.id FROM ais_sale_center.m_student a
        INNER JOIN ais_sale_center.r_staff_commission_student c on a.id = c.fk_student_id
        INNER JOIN ais_sale_center.m_student_offer b on b.fk_student_id = a.id and b.`status` = 1
        INNER JOIN ais_sale_center.s_student_project_role_staff d on d.fk_table_name = 'm_student_offer' and d.fk_table_id = b.id and d.is_active = 1
        <if test="staffFollowerIds!=null and staffFollowerIds.size()>0">
            and d.fk_staff_id in
            <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ) z1 on z1.id = a.fk_student_id
        LEFT JOIN m_student_offer_item b ON a.fk_student_id = b.fk_student_id
        AND b.`status` = 1
        LEFT JOIN m_staff_commission_policy j ON  ( FIND_IN_SET( j.fk_major_level_id, b.fk_institution_course_major_level_ids ) AND b.fk_institution_course_major_level_ids IS NOT NULL )
        AND j.fk_area_country_id = b.fk_area_country_id
        AND ( j.fk_area_country_id = b.fk_area_country_id OR j.fk_area_country_id IS NULL )
        LEFT JOIN (
        select DISTINCT a.fk_student_id,d.role_key,b.id fk_student_offer_item_id from
        r_staff_commission_student a
        LEFT JOIN m_student_offer_item b ON a.fk_student_id = b.fk_student_id
        AND b.`status` = 1
        LEFT JOIN s_student_project_role_staff c on c.fk_table_id = b.fk_student_offer_id and c.fk_table_name='m_student_offer' and c.is_active =1
        LEFT JOIN u_student_project_role d on c.fk_student_project_role_id = d.id
        ) z on z.role_key = j.fk_student_project_role_key and z.fk_student_id = a.fk_student_id and z.fk_student_offer_item_id = b.id
        LEFT JOIN m_student c on c.id = a.fk_student_id

        LEFT JOIN u_staff_commission_step e on e.step_key = j.fk_staff_commission_step_key

        LEFT JOIN r_student_offer_item_step d on d.fk_student_offer_item_id = b.id and FIND_IN_SET(d.fk_student_offer_item_step_id,e.fk_student_offer_item_step_ids)

        LEFT JOIN m_staff_commission_action f on f.fk_student_id = a.fk_student_id and f.fk_staff_commission_step_key = j.fk_staff_commission_step_key

        LEFT JOIN r_staff_commission_institution g on b.fk_institution_id = g.fk_institution_id

        LEFT JOIN m_student_offer i on i.fk_student_id = a.fk_student_id and i.`status` = 1

        LEFT JOIN m_student st on st.id = a.fk_student_id
        where 1=1
        <if test="fkAreaCountryIds !=null and fkAreaCountryIds.size()>0">
            and i.fk_area_country_id in
            <foreach collection="fkAreaCountryIds" item="fkAreaCountryId" index="index" open="(" separator="," close=")">
                #{fkAreaCountryId}
            </foreach>
        </if>
        <if test="studentIdList !=null and studentIdList.size()>0">
            and a.fk_student_id in
            <foreach collection="studentIdList" item="studentId" index="index" open="(" separator="," close=")">
                #{studentId}
            </foreach>
        </if>
        <if test="staffCommissionSummaryDto.fkCompanyId !=null">
            and st.fk_company_id = #{staffCommissionSummaryDto.fkCompanyId}
        </if>
        <if test="commissionStepKeys !=null and commissionStepKeys.size()>0">
            and j.fk_staff_commission_step_key in
            <foreach collection="commissionStepKeys" item="commissionStepKey" index="index" open="(" separator="," close=")">
                #{commissionStepKey}
            </foreach>
        </if>
        GROUP BY
        a.fk_student_id,
        j.fk_staff_commission_step_key

    </select>
    <select id="getCommissionStaffDatas" resultType="com.get.salecenter.vo.CommissionStaffDatasItemVo">
        SELECT
        a.fk_company_id,
        e.fk_staff_id,
        e.fk_staff_commission_step_key,
        e.`status` settlementStatus,
        e.commission_amount,
        b.`status` commissionStatus,
        b.fk_student_id,
        a.NAME,
        a.last_name,
        a.first_name,
        GROUP_CONCAT( DISTINCT d.fk_student_project_role_id ) fk_student_project_role_ids
        FROM
        m_staff_commission_action e
        LEFT JOIN m_student a on e.fk_student_id = a.id
        LEFT JOIN r_staff_commission_student AS b ON a.id = b.fk_student_id
        LEFT JOIN m_student_offer_item AS c ON b.fk_student_id = c.fk_student_id
        AND c.STATUS = 1
        LEFT JOIN s_student_project_role_staff AS d ON c.fk_student_offer_id = d.fk_table_id
        AND d.fk_table_name = 'm_student_offer'
        AND d.is_active = 1
        AND d.fk_staff_id = e.fk_staff_id
        WHERE
        1 = 1
        <if test="commissionStaffDatasDto.fkCompanyId !=null">
            and a.fk_company_id = #{commissionStaffDatasDto.fkCompanyId}
        </if>
        <if test="commissionStaffDatasDto.fkStudentIds !=null and commissionStaffDatasDto.fkStudentIds.size()>0">
            and a.id in
            <foreach collection="commissionStaffDatasDto.fkStudentIds" item="fkStudentId" index="index" open="(" separator="," close=")">
                #{fkStudentId}
            </foreach>
        </if>
        GROUP BY
        e.id

    </select>
    <select id="getStaffCommissionAgentList" resultType="com.get.salecenter.entity.Agent">
        SELECT
        a.*
        FROM
        m_agent a
        LEFT JOIN r_student_agent b ON b.fk_agent_id = a.id
        AND b.is_active = 1 AND a.is_active = 1
        INNER JOIN r_staff_commission_student c ON c.fk_student_id = b.fk_student_id
        LEFT JOIN m_student d on d.id = c.fk_student_id
        WHERE 1=1
        <if test="companyId != null">
        and d.fk_company_id  = #{companyId}
        </if>
    </select>
    <select id="getStaffCommissionDynamicSummaryPolicyStatus"
            resultType="com.get.salecenter.vo.StaffCommissionSummaryVo">
        SELECT
            a.fk_student_id,
            j.fk_staff_commission_step_key,
            z.role_key,
            CASE
                WHEN j.id IS NULL THEN 0
                WHEN z.role_key is null THEN 0
                ELSE 1
                END policyStatus
        FROM
            r_staff_commission_student a
                LEFT JOIN m_student_offer_item b ON a.fk_student_id = b.fk_student_id
                AND b.`status` = 1
                LEFT JOIN m_staff_commission_policy j ON  ( FIND_IN_SET( j.fk_major_level_id, b.fk_institution_course_major_level_ids ) AND b.fk_institution_course_major_level_ids IS NOT NULL )
                AND j.fk_area_country_id = b.fk_area_country_id
                AND ( j.fk_area_country_id = b.fk_area_country_id OR j.fk_area_country_id IS NULL )
                LEFT JOIN (
                select DISTINCT a.fk_student_id,d.role_key from
                    r_staff_commission_student a
                        LEFT JOIN m_student_offer_item b ON a.fk_student_id = b.fk_student_id
                        AND b.`status` = 1
                        LEFT JOIN s_student_project_role_staff c on c.fk_table_id = b.fk_student_offer_id and c.fk_table_name='m_student_offer' and c.is_active =1
                        LEFT JOIN u_student_project_role d on c.fk_student_project_role_id = d.id
            ) z on z.role_key = j.fk_student_project_role_key and z.fk_student_id = a.fk_student_id
            LEFT JOIN m_student c on c.id = a.fk_student_id
        where 1=1
        <if test="staffCommissionSummaryDto.fkCompanyId !=null">
            and c.fk_company_id = #{staffCommissionSummaryDto.fkCompanyId}
        </if>
        <if test="studentIds !=null and studentIds.size()>0">
            and a.fk_student_id in
            <foreach collection="studentIds" item="studentId" index="index" open="(" separator="," close=")">
                #{studentId}
            </foreach>
        </if>
        GROUP BY
            a.fk_student_id,
            j.fk_staff_commission_step_key
    </select>
    <select id="getStaffSettlementStatisticsFixedDatas"
            resultType="com.get.salecenter.vo.StaffSettlementStatisticsItemVo">
        SELECT
            h.fk_company_id,
            a.fk_student_id,
            h.`name` as studentName,
            h.last_name,
            h.first_name,
            i.fk_staff_id,
            GROUP_CONCAT(DISTINCT DATE_FORMAT(b.defer_opening_time,'%Y%m')) as openingTimes,
            GROUP_CONCAT(DISTINCT c.fk_agent_id) as agentIds,
            GROUP_CONCAT(DISTINCT c.fk_area_country_id) as fkAreaCountryIds,
            GROUP_CONCAT(DISTINCT b.fk_institution_course_major_level_ids) as fk_institution_course_major_level_ids,
            GROUP_CONCAT(DISTINCT b.fk_institution_course_type_group_ids) as fk_institution_course_type_group_ids,
            d.amount as receiptAmount,
            GROUP_CONCAT(DISTINCT DATE_FORMAT(f.gmt_create,'%Y年%m月')) as receiptDate,
            GROUP_CONCAT(DISTINCT j.settlement_date) as settlementDates,
            a.`status`
        FROM
            r_staff_commission_student AS a
                LEFT JOIN m_student_offer_item b on a.fk_student_id = b.fk_student_id and b.`status` = 1
                LEFT JOIN m_student_offer c on c.id = b.fk_student_offer_id and c.`status` = 1
                LEFT JOIN m_student_service_fee d on d.fk_student_id = a.fk_student_id and d.`status` = 1
                LEFT JOIN u_student_service_fee_type e on d.fk_student_service_fee_type_id = e.id and e.type_key = 'APPLICATION+VISA_SERVICE_FEE'
                LEFT JOIN m_receivable_plan f on f.fk_type_key = "m_student_service_fee" and f.fk_type_target_id = d.id and f.`status` = 1
                LEFT JOIN ais_finance_center.m_receipt_form_item g on g.fk_receivable_plan_id = f.id and f.`status` = 1
                INNER JOIN m_student h on h.id = a.fk_student_id
                LEFT JOIN s_student_project_role_staff i on i.fk_table_id = c.id and i.fk_table_name='m_student_offer' and i.is_active = 1
                LEFT JOIN m_staff_commission_action j on j.fk_student_id = a.fk_student_id
        WHERE 1=1
        <if test="staffSettlementStatisticsDto.fkStaffId !=null">
            and i.fk_staff_id = #{staffSettlementStatisticsDto.fkStaffId}
        </if>
        <if test="staffSettlementStatisticsDto.fkCompanyId !=null">
            and h.fk_company_id = #{staffSettlementStatisticsDto.fkCompanyId}
        </if>
        GROUP BY a.fk_student_id
        ORDER BY h.gmt_create desc
    </select>
    <select id="getStaffSettlementStatisticsTotalDynamicDatas"
            resultType="com.get.salecenter.vo.StaffCommissionActionVo">
        SELECT
            a.fk_student_id,
            a.fk_staff_commission_step_key,
            SUM(a.commission_amount) commission_amount,
            GROUP_CONCAT(DISTINCT a.settlement_date) as settlement_date,
            a.`status`,
            b.step_order
        FROM
            m_staff_commission_action AS a
                LEFT JOIN u_staff_commission_step b on b.step_key = a.fk_staff_commission_step_key
        WHERE 1=1
        <if test="staffSettlementStatisticsDto.fkStaffId !=null">
            and a.fk_staff_id = #{staffSettlementStatisticsDto.fkStaffId}
        </if>
        <if test="staffSettlementStatisticsDto.fkCompanyId !=null">
            and a.fk_company_id = #{staffSettlementStatisticsDto.fkCompanyId}
        </if>
        GROUP BY a.fk_student_id,a.fk_staff_commission_step_key,a.`status`
    </select>
    <select id="getSumSettlementAmounts" resultType="com.get.salecenter.vo.StaffCommissionActionVo">
        SELECT
            a.fk_staff_commission_step_key,
            SUM(a.commission_amount) commission_amount,
            b.step_order
        FROM
            m_staff_commission_action AS a
                LEFT JOIN u_staff_commission_step b on b.step_key = a.fk_staff_commission_step_key
        WHERE 1=1
        <if test="staffSettlementStatisticsDto.fkStaffId !=null">
            and a.fk_staff_id = #{staffSettlementStatisticsDto.fkStaffId}
        </if>
        <if test="staffSettlementStatisticsDto.fkStaffId !=null">
            and a.settlement_date = DATE_FORMAT(#{staffSettlementStatisticsDto.settlementDate},"%Y%m")
        </if>
        GROUP BY a.fk_staff_commission_step_key
    </select>
    <select id="getStaffCommissionStaffIds" resultType="java.lang.String">
        SELECT
            DISTINCT a.department_num
        FROM
            u_student_project_role a
                INNER JOIN (
                SELECT DISTINCT fk_student_project_role_key FROM m_staff_commission_policy
            ) b on b.fk_student_project_role_key = a.role_key
        where 1=1
        <if test="companyId !=null">
            and a.fk_company_id = #{companyId}
        </if>
    </select>
    <select id="getStaffSettlementRefundList" resultType="com.get.salecenter.vo.StaffCommissionRefundVo">
        SELECT a.*,
        CONCAT("【",
        CASE WHEN a.fk_student_project_role_key = "%_COORDINATOR" THEN "外联"
            WHEN a.fk_student_project_role_key = "%_COUNSELLING_SUPPORT" THEN "咨询支持" END
        ,"】",ms.name) as roleAndStaff,
        rr.reason_name as reason_name,
        ma.name as agentName,
        CASE WHEN IFNULL(CONCAT(c.first_name,c.last_name), '') = '' THEN c.name ELSE CONCAT(c.name, '（',
        CONCAT(c.first_name," ",c.last_name), '）') END studentName,
        CONCAT(i.`name`,IF(i.name_chn is null or i.name_chn = '','',CONCAT("（",i.name_chn,"）"))) AS institutionFullName,
        oi.defer_opening_time AS deferOpeningTime,
        oi.status as itemStatus,
        oi.fk_area_country_id,
        oi.fk_institution_course_id,
        oi.fk_student_offer_item_step_id,
        oi.old_course_custom_name,
        oi.fk_institution_provider_id,
        oi.fk_institution_channel_id,
        oi.fk_student_offer_id,
        oi.fk_student_id
        FROM m_staff_commission_action a
        <!--主要过滤sql结束-->
        <if test="!isStudentAdmin">
        INNER JOIN (
        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.studentAuthority"/>
        ) z ON a.fk_student_id=z.id
        </if>
        INNER JOIN m_student_offer_item oi ON a.fk_student_offer_item_id = oi.id
        INNER JOIN u_staff_commission_step uscs on uscs.step_key = a.fk_staff_commission_step_key  AND uscs.fk_company_id = 30
        LEFT JOIN m_student c ON oi.fk_student_id = c.id
        LEFT JOIN m_agent ma on ma.id = oi.fk_agent_id
        LEFT JOIN u_settlement_refund_reason rr on rr.id = a.fk_settlement_refund_reason_id
        LEFT JOIN ais_institution_center.m_institution_provider mp ON oi.fk_institution_provider_id=mp.id
        LEFT JOIN u_student_project_role p on p.role_key = a.fk_student_project_role_key
        LEFT JOIN ais_permission_center.m_staff ms on ms.id = a.fk_staff_id
        LEFT JOIN ais_institution_center.m_institution i ON oi.fk_institution_id = i.id
        <if test="staffCommissionRefundDto.stepFailureBeginTime != null or staffCommissionRefundDto.stepFailureEndTime != null">
            INNER JOIN (
            select a.fk_student_offer_item_id as id
            FROM r_student_offer_item_step a
            where a.fk_student_offer_item_step_id = 9
            <!--入学失败操作时间审核时间-->
            <if test="staffCommissionRefundDto.stepFailureBeginTime != null">
                AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{staffCommissionRefundDto.stepFailureBeginTime},'%Y-%m-%d')
            </if>
            <if test="staffCommissionRefundDto.stepFailureEndTime != null">
                AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{staffCommissionRefundDto.stepFailureEndTime},'%Y-%m-%d')
            </if>
            ) r on r.id = oi.id
        </if>
        WHERE 1=1 and (oi.status = 0 OR oi.fk_student_offer_item_step_id =9) and (a.fk_staff_commission_step_key =
        'STEP_OFFER_SELECTION' OR a.fk_staff_commission_step_key = 'STEP_ENROLLED' OR a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED_BACK' )
        AND a.commission_amount != 0
        AND a.id NOT IN ( SELECT
        a.id
        FROM
        m_staff_commission_action a
        INNER JOIN
        m_student_offer_item oi
        ON a.fk_student_offer_item_id = oi.id
        INNER JOIN
        r_student_offer_item_step r
        ON oi.id = r.fk_student_offer_item_id
        WHERE
        a.fk_staff_commission_step_key = 'STEP_OFFER_SELECTION'
        AND r.fk_student_offer_item_step_id = 10)
        <if test="staffCommissionRefundDto.fkCompanyIds != null and staffCommissionRefundDto.fkCompanyIds.size()>0">
            AND a.fk_company_id IN
            <foreach collection="staffCommissionRefundDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="staffCommissionRefundDto.refundReviewStatus!=null">
            <choose>
                <when test="staffCommissionRefundDto.refundReviewStatus!= 0">
                    AND a.refund_review_status = #{staffCommissionRefundDto.refundReviewStatus}
                </when>
                <otherwise>
                    AND (a.refund_review_status = 0 or  a.refund_review_status is null)
                </otherwise>
            </choose>
        </if>
        AND a.fk_company_id IN
        <foreach collection="staffCommissionRefundDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
            #{fkCompanyId}
        </foreach>
        <if test="staffCommissionRefundDto.refundSettlementStatus!=null">
            <choose>
                <when test="staffCommissionRefundDto.refundSettlementStatus!= 0">
                    AND a.refund_settlement_status = #{staffCommissionRefundDto.refundSettlementStatus}
                </when>
                <otherwise>
                    AND (a.refund_settlement_status = 0 or  a.refund_settlement_status is null)
                </otherwise>
            </choose>
        </if>
        <!--退款结算时间-->
        <if test="staffCommissionRefundDto.createBeginTime != null">
            AND DATE_FORMAT(a.refund_settlement_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{staffCommissionRefundDto.createBeginTime},'%Y-%m-%d')
        </if>
        <if test="staffCommissionRefundDto.createEndTime != null">
            AND DATE_FORMAT(a.refund_settlement_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{staffCommissionRefundDto.createEndTime},'%Y-%m-%d')
        </if>
        <!--退款审核时间-->
        <if test="staffCommissionRefundDto.refundReviewBeginTime != null">
            AND DATE_FORMAT(a.refund_review_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{staffCommissionRefundDto.refundReviewBeginTime},'%Y-%m-%d')
        </if>
        <if test="staffCommissionRefundDto.refundReviewEndTime != null">
            AND DATE_FORMAT(a.refund_review_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{staffCommissionRefundDto.refundReviewEndTime},'%Y-%m-%d')
        </if>
        <if test="staffCommissionRefundDto.settlementDate != null and staffCommissionRefundDto.settlementDate !='' ">
            AND a.settlement_date= #{staffCommissionRefundDto.settlementDate}
        </if>
        <if test="staffCommissionRefundDto.staffName != null and staffCommissionRefundDto.staffName !=''">
            AND (
            <!-- LOWER(p.role_name) like concat('%',#{staffCommissionRefundDto.staffName},'%') OR LOWER(p.role_key) like concat('%',#{staffCommissionRefundDto.staffName},'%')
                OR  -->
            LOWER(ms.name) like concat('%',#{staffCommissionRefundDto.staffName},'%') OR LOWER(ms.name_en) like concat('%',#{staffCommissionRefundDto.staffName},'%')
            )
        </if>
        <if test="staffCommissionRefundDto.studentName != null and staffCommissionRefundDto.studentName != ''">
            AND
            (REPLACE(CONCAT(LOWER(c.first_name),LOWER(c.last_name)),' ','') like concat('%',#{staffCommissionRefundDto.studentName},'%')
            OR REPLACE(CONCAT(LOWER(c.last_name),LOWER(c.first_name)),' ','') like concat('%',#{staffCommissionRefundDto.studentName},'%')
            OR LOWER(c.`name`) like concat('%',#{staffCommissionRefundDto.studentName},'%')
            OR LOWER(c.last_name) like concat('%',#{staffCommissionRefundDto.studentName},'%')
            OR LOWER(c.first_name) like concat('%',#{staffCommissionRefundDto.studentName},'%'))
        </if>
        <!--过滤我的国家权限-->
        <if test="areaCountryIds != null and areaCountryIds.size() > 0">
            AND oi.fk_area_country_id IN
            <foreach collection="areaCountryIds" item="areaCountryId" open="(" separator="," close=")">
                #{areaCountryId}
            </foreach>
        </if>
        <if test="staffCommissionRefundDto.agentName != null and staffCommissionRefundDto.agentName != ''">
            AND LOWER(ma.name) LIKE CONCAT('%', #{staffCommissionRefundDto.agentName}, '%')
        </if>
        <if test="staffCommissionRefundDto.fkSettlementRefundReasonIds != null and staffCommissionRefundDto.fkSettlementRefundReasonIds.size() > 0">
            AND a.fk_settlement_refund_reason_id IN
            <foreach collection="staffCommissionRefundDto.fkSettlementRefundReasonIds" item="fkSettlementRefundReasonId" open="(" separator="," close=")">
                #{fkSettlementRefundReasonId}
            </foreach>
        </if>
        <if test="staffCommissionRefundDto.institutionName != null and staffCommissionRefundDto.institutionName != ''">
            AND (
            LOWER(i.name) like concat('%',#{staffCommissionRefundDto.institutionName},'%')
                or LOWER(i.name_chn) like concat('%',#{staffCommissionRefundDto.institutionName},'%')
            )
        </if>
        order by a.settlement_date desc,c.name asc,uscs.step_order desc,a.id desc
    </select>
    <select id="getSettlementDateSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT settlement_date as name FROM m_staff_commission_action group by settlement_date order by settlement_date desc
    </select>
    <select id="getStaffSettlementRefundSummaryList" resultType="com.get.salecenter.vo.StaffCommissionRefundDetailVo">
        SELECT a.fk_staff_id,
        max(p.role_name) as position,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_OFFER_SELECTION' AND a.refund_settlement_status = 2
        THEN 1
        ELSE 0 END) AS osSettlementQuantity,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_OFFER_SELECTION' AND
        (a.refund_settlement_status IS NULL OR a.refund_settlement_status = 0) THEN 1
        ELSE 0 END) AS osUnSettlementQuantity,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED' AND a.refund_settlement_status = 2 THEN 1
        ELSE 0 END) AS vgSettlementQuantity,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED' AND
        (a.refund_settlement_status IS NULL OR a.refund_settlement_status = 0) THEN 1
        ELSE 0 END) AS vgUnSettlementQuantity,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_ENROLLED' AND a.refund_settlement_status = 2 THEN 1
        ELSE 0 END) AS seSettlementQuantity,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_ENROLLED' AND
        (a.refund_settlement_status IS NULL OR a.refund_settlement_status = 0) THEN 1
        ELSE 0 END) AS seUnSettlementQuantity,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED_BACK' AND a.refund_settlement_status = 2
        THEN 1
        ELSE 0 END) AS vgbSettlementQuantity,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED_BACK' AND
        (a.refund_settlement_status IS NULL OR a.refund_settlement_status = 0) THEN 1
        ELSE 0 END) AS vgbUnSettlementQuantity,
        SUM(CASE WHEN a.refund_settlement_status = 2 THEN a.commission_amount ELSE 0 END) AS commissionAmount,
        SUM(CASE
        WHEN a.refund_settlement_status IS NULL OR a.refund_settlement_status = 0 THEN a.commission_amount
        ELSE 0 END) AS unCommissionAmount,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_OFFER_SELECTION' AND a.refund_settlement_status = 2
        THEN a.commission_amount
        ELSE 0 END) AS osSettlementAmount,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_OFFER_SELECTION' AND
        (a.refund_settlement_status IS NULL OR a.refund_settlement_status = 0) THEN a.commission_amount
        ELSE 0 END) AS osUnSettlementAmount,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED' AND a.refund_settlement_status = 2 THEN
        a.commission_amount
        ELSE 0 END) AS vgSettlementAmount,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED' AND
        (a.refund_settlement_status IS NULL OR a.refund_settlement_status = 0) THEN a.commission_amount
        ELSE 0 END) AS vgUnSettlementAmount,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_ENROLLED' AND a.refund_settlement_status = 2 THEN a.commission_amount
        ELSE 0 END) AS seSettlementAmount,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_ENROLLED' AND
        (a.refund_settlement_status IS NULL OR a.refund_settlement_status = 0) THEN a.commission_amount
        ELSE 0 END) AS seUnSettlementAmount,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED_BACK' AND a.refund_settlement_status = 2
        THEN a.commission_amount
        ELSE 0 END) AS vgbSettlementAmount,
        SUM(CASE
        WHEN a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED_BACK' AND
        (a.refund_settlement_status IS NULL OR a.refund_settlement_status = 0) THEN a.commission_amount
        ELSE 0 END) AS vgbUnSettlementAmount
        FROM m_staff_commission_action a
        <!--主要过滤sql结束-->
        <if test="!isStudentAdmin">
            INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.studentAuthority"/>
            ) z ON a.fk_student_id=z.id
        </if>
        INNER JOIN m_student_offer_item oi ON a.fk_student_offer_item_id = oi.id
        LEFT JOIN m_student c ON oi.fk_student_id = c.id
        LEFT JOIN m_agent ma on ma.id = oi.fk_agent_id
        LEFT JOIN ais_institution_center.m_institution_provider mp ON oi.fk_institution_provider_id=mp.id
        LEFT JOIN u_student_project_role p on p.role_key = a.fk_student_project_role_key
        LEFT JOIN ais_permission_center.m_staff ms on ms.id = a.fk_staff_id
        LEFT JOIN ais_institution_center.m_institution i ON oi.fk_institution_id = i.id
        <if test="staffCommissionRefundDto.stepFailureBeginTime != null or staffCommissionRefundDto.stepFailureEndTime != null">
            INNER JOIN (
            select a.fk_student_offer_item_id as id
            FROM r_student_offer_item_step a
            where a.fk_student_offer_item_step_id = 9
            <!--入学失败操作时间审核时间-->
            <if test="staffCommissionRefundDto.stepFailureBeginTime != null">
                AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{staffCommissionRefundDto.stepFailureBeginTime},'%Y-%m-%d')
            </if>
            <if test="staffCommissionRefundDto.stepFailureEndTime != null">
                AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{staffCommissionRefundDto.stepFailureEndTime},'%Y-%m-%d')
            </if>
            ) r on r.id = oi.id
        </if>
        WHERE 1=1 and a.refund_review_status = 2 and (oi.status = 0 OR oi.fk_student_offer_item_step_id =9) and (a.fk_staff_commission_step_key =
        'STEP_OFFER_SELECTION' OR a.fk_staff_commission_step_key =
        'STEP_ENROLLED' OR a.fk_staff_commission_step_key = 'STEP_VISA_GRANTED_BACK' )
        AND a.id NOT IN ( SELECT
        a.id
        FROM
        m_staff_commission_action a
        INNER JOIN
        m_student_offer_item oi
        ON a.fk_student_offer_item_id = oi.id
        INNER JOIN
        r_student_offer_item_step r
        ON oi.id = r.fk_student_offer_item_id
        WHERE
        a.fk_staff_commission_step_key = 'STEP_OFFER_SELECTION'
        AND r.fk_student_offer_item_step_id = 10)
        <if test="staffCommissionRefundDto.fkCompanyIds != null and staffCommissionRefundDto.fkCompanyIds.size()>0">
            AND a.fk_company_id  IN
            <foreach collection="staffCommissionRefundDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="staffCommissionRefundDto.refundReviewStatus!=null">
            <choose>
                <when test="staffCommissionRefundDto.refundReviewStatus!= 0">
                    AND a.refund_review_status = #{staffCommissionRefundDto.refundReviewStatus}
                </when>
                <otherwise>
                    AND (a.refund_review_status = 0 or  a.refund_review_status is null)
                </otherwise>
            </choose>
        </if>
        <if test="staffCommissionRefundDto.refundSettlementStatus!=null">
            <choose>
                <when test="staffCommissionRefundDto.refundSettlementStatus!= 0">
                    AND a.refund_settlement_status = #{staffCommissionRefundDto.refundSettlementStatus}
                </when>
                <otherwise>
                    AND (a.refund_settlement_status = 0 or  a.refund_settlement_status is null)
                </otherwise>
            </choose>
        </if>
        <!--退款结算时间-->
        <if test="staffCommissionRefundDto.createBeginTime != null">
            AND DATE_FORMAT(a.refund_settlement_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{staffCommissionRefundDto.createBeginTime},'%Y-%m-%d')
        </if>
        <if test="staffCommissionRefundDto.createEndTime != null">
            AND DATE_FORMAT(a.refund_settlement_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{staffCommissionRefundDto.createEndTime},'%Y-%m-%d')
        </if>
        <!--退款审核时间-->
        <if test="staffCommissionRefundDto.refundReviewBeginTime != null">
            AND DATE_FORMAT(a.refund_review_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{staffCommissionRefundDto.refundReviewBeginTime},'%Y-%m-%d')
        </if>
        <if test="staffCommissionRefundDto.refundReviewEndTime != null">
            AND DATE_FORMAT(a.refund_review_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{staffCommissionRefundDto.refundReviewEndTime},'%Y-%m-%d')
        </if>
        <if test="staffCommissionRefundDto.settlementDate != null and staffCommissionRefundDto.settlementDate !='' ">
            AND a.settlement_date= #{staffCommissionRefundDto.settlementDate}
        </if>
        <if test="staffCommissionRefundDto.staffName != null and staffCommissionRefundDto.staffName !=''">
            AND (
            LOWER(p.role_name) like concat('%',#{staffCommissionRefundDto.staffName},'%') OR LOWER(p.role_key) like concat('%',#{staffCommissionRefundDto.staffName},'%')
            OR LOWER(ms.name) like concat('%',#{staffCommissionRefundDto.staffName},'%') OR LOWER(ms.name_en) like concat('%',#{staffCommissionRefundDto.staffName},'%')
            )
        </if>
        <if test="staffCommissionRefundDto.studentName != null and staffCommissionRefundDto.studentName != ''">
            AND
            (REPLACE(CONCAT(LOWER(c.first_name),LOWER(c.last_name)),' ','') like concat('%',#{staffCommissionRefundDto.studentName},'%')
            OR REPLACE(CONCAT(LOWER(c.last_name),LOWER(c.first_name)),' ','') like concat('%',#{staffCommissionRefundDto.studentName},'%')
            OR LOWER(c.`name`) like concat('%',#{staffCommissionRefundDto.studentName},'%')
            OR LOWER(c.last_name) like concat('%',#{staffCommissionRefundDto.studentName},'%')
            OR LOWER(c.first_name) like concat('%',#{staffCommissionRefundDto.studentName},'%'))
        </if>
        <!--过滤我的国家权限-->
        <if test="areaCountryIds != null and areaCountryIds.size() > 0">
            AND oi.fk_area_country_id IN
            <foreach collection="areaCountryIds" item="areaCountryId" open="(" separator="," close=")">
                #{areaCountryId}
            </foreach>
        </if>
        <if test="staffCommissionRefundDto.agentName != null and staffCommissionRefundDto.agentName != ''">
            AND LOWER(ma.name) LIKE CONCAT('%', #{staffCommissionRefundDto.agentName}, '%')
        </if>
        <if test="staffCommissionRefundDto.institutionName != null and staffCommissionRefundDto.institutionName != ''">
            AND (
            LOWER(i.name) like concat('%',#{staffCommissionRefundDto.institutionName},'%')
            or LOWER(i.name_chn) like concat('%',#{staffCommissionRefundDto.institutionName},'%')
            )
        </if>
        GROUP BY a.fk_staff_id
    </select>
</mapper>