package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.ConventionTableRegistration;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ConventionTableRegistrationMapper extends BaseMapper<ConventionTableRegistration> {

    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionTableRegistration record);

    /**
     * @return java.lang.Boolean
     * @Description :
     * @Param [fieldName, id]
     * <AUTHOR>
     */
    Boolean conventionTableRegistrationIsEmpty(@Param("fieldName") String fieldName, @Param("id") Long id);


}