<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionTableRegistrationMapper">
  <resultMap id="BaseResultMap" type="com.get.salecenter.entity.ConventionTableRegistration">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_convention_table_id" jdbcType="BIGINT" property="fkConventionTableId" />
    <result column="fk_convention_registration_id" jdbcType="BIGINT" property="fkConventionRegistrationId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>

  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionTableRegistration" keyProperty="id" useGeneratedKeys="true">
    insert into r_convention_table_registration
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionTableId != null">
        fk_convention_table_id,
      </if>
      <if test="fkConventionRegistrationId != null">
        fk_convention_registration_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionTableId != null">
        #{fkConventionTableId,jdbcType=BIGINT},
      </if>
      <if test="fkConventionRegistrationId != null">
        #{fkConventionRegistrationId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="conventionTableRegistrationIsEmpty"  resultType="java.lang.Boolean">
      select IFNULL(max(id),0) id from r_convention_table_registration where ${fieldName} = #{id} LIMIT 1
    </select>
</mapper>