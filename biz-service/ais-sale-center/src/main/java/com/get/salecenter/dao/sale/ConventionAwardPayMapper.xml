<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionAwardPayMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.ConventionAwardPay">
    insert into m_convention_award_pay (id, fk_convention_id, fk_convention_person_id, 
      pay_count, pay_amount, pay_system_order_num, 
      pay_type, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkConventionId,jdbcType=BIGINT}, #{fkConventionPersonId,jdbcType=BIGINT}, 
      #{payCount,jdbcType=INTEGER}, #{payAmount,jdbcType=DECIMAL}, #{paySystemOrderNum,jdbcType=VARCHAR}, 
      #{payType,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionAwardPay">
    insert into m_convention_award_pay
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionId != null">
        fk_convention_id,
      </if>
      <if test="fkConventionPersonId != null">
        fk_convention_person_id,
      </if>
      <if test="payCount != null">
        pay_count,
      </if>
      <if test="payAmount != null">
        pay_amount,
      </if>
      <if test="paySystemOrderNum != null">
        pay_system_order_num,
      </if>
      <if test="payType != null">
        pay_type,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionId != null">
        #{fkConventionId,jdbcType=BIGINT},
      </if>
      <if test="fkConventionPersonId != null">
        #{fkConventionPersonId,jdbcType=BIGINT},
      </if>
      <if test="payCount != null">
        #{payCount,jdbcType=INTEGER},
      </if>
      <if test="payAmount != null">
        #{payAmount,jdbcType=DECIMAL},
      </if>
      <if test="paySystemOrderNum != null">
        #{paySystemOrderNum,jdbcType=VARCHAR},
      </if>
      <if test="payType != null">
        #{payType,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="datas" resultType="com.get.salecenter.vo.ConventionAwardPayVo">
    select cap.*,cp.type type,cp.name_chn name from m_convention_award_pay cap
      left join m_convention_award_code cac on cac.pay_system_order_num = cap.pay_system_order_num
      left join m_convention_person cp on cp.id = cap.fk_convention_person_id
      where 1=1
      <if test="conventionAwardPayDto.keyWord!=null">
       and (cap.pay_system_order_num like concat("%",#{conventionAwardPayDto.keyWord},"%")
         or cac.award_code like concat("%",#{conventionAwardPayDto.keyWord},"%")
        or  (cp.name like concat("%",#{conventionAwardPayDto.keyWord},"%") or cp.name_chn like concat("%",#{conventionAwardPayDto.keyWord},"%")))
      </if>
    <if test="conventionAwardPayDto.fkConventionId!=null">
      and cap.fk_convention_id = #{conventionAwardPayDto.fkConventionId}
    </if>
    group by cap.id
  </select>
</mapper>