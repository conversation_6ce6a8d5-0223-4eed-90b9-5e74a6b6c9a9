package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.salecenter.dto.StudentAgentDto;
import com.get.salecenter.entity.StudentAgent;
import com.get.salecenter.vo.AgentAndAgentLabelVo;
import com.get.salecenter.vo.StudentAgentVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
//@DS("saledb")
public interface StudentAgentMapper extends BaseMapper<StudentAgent> {


    int insertSelective(StudentAgent record);


    /**
     * @return java.util.List<com.get.salecenter.vo.StudentAgentVo>
     * @Description: 列表
     * @Param [studentAgentDto]
     * <AUTHOR>
     */
    List<StudentAgentVo> datas(IPage<StudentAgentVo> iPage, StudentAgentDto studentAgentDto);


    /**
     * @return java.lang.Integer
     * @Description: 校验是否已经存在绑定
     * @Param [studentId, agentId]
     * <AUTHOR>
     */
    Integer validateAdd(@Param(value = "studentId") Long studentId, @Param(value = "agentId") Long agentId);

    /**
     * @return java.lang.Integer
     * @Description: 校验是否已经存在绑定
     * @Param [studentId, agentId]
     * <AUTHOR>
     */
    Integer validateUpdate(@Param(value = "studentId") Long studentId);


    /**
     * @return java.util.List<java.lang.String>
     * @Description: 根据学生id查询代理名称
     * @Param [studentId]
     * <AUTHOR>
     */
    List<String> getAgentNameByStudentId(Long studentId);


    /**
     * 根据学生ids查询代理名称
     *
     * @param studentIds
     * @return
     */
    List<StudentAgentVo> getAgentNameByStudentIds(@Param(value = "studentIds") Set<Long> studentIds);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [agentId]
     * <AUTHOR>
     */
    Boolean isExistByAgentId(Long agentId);

    /**
     * @return java.util.List<java.lang.String>
     * @Description:
     * @Param [studentId]
     * <AUTHOR>
     */
    List<String> getBdCodeByStudentId(Long studentId);

    /**
     * 根据学生ids查询bd编号
     *
     * @param studentIds
     * @return
     */
    List<StudentAgentVo> getBdCodeByStudentIds(@Param(value = "studentIds") Set<Long> studentIds);

    /**
     * @return
     * @Description：获取学生绑定的代理列表
     * @Param
     * @Date 12:50 2021/4/25
     * <AUTHOR>
     */
    List<AgentAndAgentLabelVo> getAgentListByStudentId(@Param(value = "studentId")Long studentId);

    /**
     * @return
     * @Description：获取学生ids绑定的代理列表
     * @Param
     * @Date 12:50 2021/4/25
     * <AUTHOR>
     */
    List<Map<Long, String>> getAgentListByStudentIds(@Param(value = "studentIds") List<Long> studentIds);

    /**
     * 根据代理名称查询学生
     *
     * @param agentName
     * @return
     */
    List<Long> getRelationByAgentName(String agentName);

    /**
     * 获取绑定的代理
     *
     * @Date 14:10 2021/7/30
     * <AUTHOR>
     */
    List<StudentAgentVo> getAgentStaffNameByStudentId(@Param(value = "id") Long id);

    /**
     * 根据学生ids获取绑定的代理
     * @param studentIds
     * @return
     */
    List<StudentAgentVo> getAgentStaffNameByStudentIds(@Param(value = "studentIds") Set<Long> studentIds);

    List<StudentAgent> getAgentStudentNum(@Param(value = "fkAgentIds")  Set<Long> fkAgentIds);
}