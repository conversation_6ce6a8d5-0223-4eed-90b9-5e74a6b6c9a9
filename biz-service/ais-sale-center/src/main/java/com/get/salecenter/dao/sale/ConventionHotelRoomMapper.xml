<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionHotelRoomMapper">

    <select id="getMaxSystemRoomNum" parameterType="java.lang.Long" resultType="string">
    select
     system_room_num
    from
     m_convention_hotel_room
    where
     fk_convention_hotel_id = #{hotelId}
    ORDER BY
     system_room_num
    desc
    LIMIT 1
  </select>

    <select id="getRoomIdsByHotelId"  resultType="java.lang.Long">
    select
     id
    from
     m_convention_hotel_room
    where
     fk_convention_hotel_id = #{hotelId}
    and
     system_room_num = #{systemRoomNum}
  </select>

    <select id="getDates" parameterType="java.lang.Long" resultType="java.util.Date">
        SELECT
        stay_date
        FROM
        m_convention_hotel_room
        WHERE
        fk_convention_hotel_id
        IN
        <foreach item="item" index="index" collection="conventionHotelIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        stay_date
        ORDER BY
        stay_date;
    </select>

    <select id="getHotelRoomsInIds" parameterType="java.util.List" resultType="java.lang.String">
        select
        system_room_num
        from
        m_convention_hotel_room
        where
        fk_convention_hotel_id
        in
        <foreach item="item" index="index" collection="conventionHotelIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        GROUP BY
        system_room_num

    </select>

    <select id="conventionHoteRoomIsEmpty" parameterType="java.lang.Long" resultType="java.lang.Boolean">
     select IFNULL(max(id),0) id from m_convention_hotel_room where fk_convention_hotel_id = #{id}  LIMIT 1;

  </select>

    <select id="getConventionHotelRoomDtoList" resultType="com.get.salecenter.vo.ConventionHotelRoomVo">
        select DISTINCT a.* from (
            SELECT  b.view_order,a.system_room_num,a.hotel_room_num,b.room_type,b.hotel,b.bed_count,b.id FROM
             (
            SELECT fk_convention_hotel_id, system_room_num, hotel_room_num
            FROM m_convention_hotel_room where fk_convention_hotel_id
            in
            <foreach item="item" index="index" collection="conventionHotelIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            GROUP BY fk_convention_hotel_id, system_room_num, hotel_room_num
            ) a
            LEFT JOIN m_convention_hotel b ON a.fk_convention_hotel_id=b.id
            WHERE 1=1
            <if test=" conventionHotelRoomDto.hotel != null and conventionHotelRoomDto.hotel != ''">
                and hotel = #{conventionHotelRoomDto.hotel}
            </if>
            <if test=" conventionHotelRoomDto.roomType != null and conventionHotelRoomDto.roomType != ''">
                and room_type = #{conventionHotelRoomDto.roomType}
            </if>
            <if test=" conventionHotelRoomDto.roomNum != null and conventionHotelRoomDto.roomNum != ''">
                and (system_room_num like #{conventionHotelRoomDto.roomNum} or hotel_room_num like
                #{conventionHotelRoomDto.roomNum})
            </if>
            <if test="bdCodes !=null and bdCodes.size()>0">
                and EXISTS (
                SELECT
                1
                FROM
                m_convention_hotel_room a1
                LEFT JOIN r_convention_hotel_room_person b1 on a1.id = b1.fk_convention_hotel_room_id
                LEFT JOIN m_convention_person c1 on b1.fk_convention_person_id = c1.id
                where a1.system_room_num = a.system_room_num
                and	a1.fk_convention_hotel_id in
                <foreach item="item" index="index" collection="conventionHotelIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
                and c1.bd_code in
                <foreach collection="bdCodes" item="bdCode" index="index" open="(" separator="," close=")">
                    #{bdCode}
                </foreach>
                GROUP BY a1.system_room_num
                )
            </if>
            <if test="companyId != null and companyId != 3">
                UNION ALL
                SELECT
                b.view_order,
                a.system_room_num,
                a.hotel_room_num,
                b.room_type,
                b.hotel,
                b.bed_count,
                b.id
                FROM
                ( SELECT
                id,
                fk_convention_hotel_id,
                system_room_num,
                hotel_room_num
                FROM
                m_convention_hotel_room
                where
                fk_convention_hotel_id in
                <foreach item="item" index="index" collection="conventionHotelIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
                GROUP BY
                fk_convention_hotel_id,
                system_room_num,
                hotel_room_num ) a
                LEFT JOIN
                m_convention_hotel b
                ON a.fk_convention_hotel_id=b.id
                LEFT JOIN r_convention_hotel_room_person c on c.fk_convention_hotel_room_id = a.id
                where c.id is null
                <if test=" conventionHotelRoomDto.hotel != null and conventionHotelRoomDto.hotel != ''">
                    and hotel = #{conventionHotelRoomDto.hotel}
                </if>
                <if test=" conventionHotelRoomDto.roomType != null and conventionHotelRoomDto.roomType != ''">
                    and room_type = #{conventionHotelRoomDto.roomType}
                </if>
                <if test=" conventionHotelRoomDto.roomNum != null and conventionHotelRoomDto.roomNum != ''">
                    and (system_room_num like #{conventionHotelRoomDto.roomNum} or hotel_room_num like
                    #{conventionHotelRoomDto.roomNum})
                </if>
            </if>


        ) a

        ORDER BY a.view_order DESC, a.system_room_num;

    </select>

    <select id="getRoomIds" resultType="java.lang.Long">
        select
         id
        from
         m_convention_hotel_room
        where
         stay_date = #{date}
        and
         fk_convention_hotel_id = #{id}

    </select>

    <select id="getAllRoomIds" resultType="java.lang.Long">
       select
         id
        from
         m_convention_hotel_room
        where
         fk_convention_hotel_id
        in
        <foreach item="item" index="index" collection="conventionHotelIds" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="roomDate != null and roomDate != ''">
            and stay_date = #{roomDate}
        </if>

    </select>
    <select id="getConventionHotelRoom" resultType="com.get.salecenter.entity.ConventionHotelRoom">
        SELECT
            *
        FROM
            m_convention_hotel_room
                left JOIN
            r_convention_hotel_room_person
            ON
            r_convention_hotel_room_person.fk_convention_hotel_room_id = m_convention_hotel_room.id
        WHERE
            r_convention_hotel_room_person.fk_convention_person_id = #{id}
    </select>
    <select id="getConventionHotelRoomsByIds" resultType="com.get.salecenter.entity.ConventionHotelRoom">
        SELECT
            *
        FROM
            `m_convention_hotel_room`
        WHERE
            id IN
            <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
               #{item}
             </foreach>
    </select>
    <select id="getRoomByIds" resultType="com.get.salecenter.entity.ConventionHotelRoom">
        SELECT
            fk_convention_hotel_id,
            system_room_num
        FROM
            `m_convention_hotel_room`
        <where>
            <if test="roomIds != null and roomIds.size()>0">
                AND id IN
                <foreach collection="roomIds" item="id" index="index" open="(" separator="," close=")">
                    #{id,jdbcType=BIGINT}
                </foreach>
            </if>
        </where>
        GROUP BY
            fk_convention_hotel_id,
            system_room_num
    </select>

    <select id="getHotelRoomCountByRoomTypeId" resultType="java.lang.Long">
        SELECT IFNULL(COUNT(*), 0) AS result_count
        FROM (
                 SELECT *
                 FROM ais_sale_center.m_convention_hotel_room
                 WHERE fk_convention_hotel_id = #{roomTypeId}
                 GROUP BY fk_convention_hotel_id, system_room_num
             ) AS subquery;
    </select>

</mapper>