package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.ConventionSponsorFee;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface ConventionSponsorFeeMapper extends BaseMapper<ConventionSponsorFee> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ConventionSponsorFee record);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param [conventionId]
     * <AUTHOR>
     */
    Integer getMaxViewOrder(Long conventionId);
}