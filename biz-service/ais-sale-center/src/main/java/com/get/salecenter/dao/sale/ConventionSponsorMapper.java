package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.ConventionSponsorVo;
import com.get.salecenter.entity.ConventionSponsor;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ConventionSponsorMapper extends BaseMapper<ConventionSponsor>, GetMapper<ConventionSponsor> {
    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(ConventionSponsor record);


    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionSponsorVo>
     * @Description :新增
     * @Param [receiptCode, fkConventionId, status]
     * <AUTHOR>
     */
    List<ConventionSponsorVo> getSponsorsByReceiptCode(@Param("receiptCode") String receiptCode, @Param("fkConventionId") Long fkConventionId, @Param("status") Integer status);


    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionSponsorVo>
     * @Description :根据回执码，状态，峰会id查询赞助商
     * @Param [receiptCode, fkConventionId, status]
     * <AUTHOR>
     */
    List<ConventionSponsorVo> getSponsorByReceiptCodeAndStatus(@Param("receiptCode") String receiptCode, @Param("fkConventionId") Long fkConventionId, @Param("status") Integer status);

    /**
     * @return java.util.List<com.get.salecenter.vo.ConventionSponsorVo>
     * @Description :根据回执码，峰会id查询赞助商
     * @Param [receiptCode, fkConventionId, status]
     * <AUTHOR>
     */
    List<ConventionSponsorVo> getSponsorListByReceiptCode(@Param("receiptCode") String receiptCode, @Param("fkConventionId") Long fkConventionId);
}