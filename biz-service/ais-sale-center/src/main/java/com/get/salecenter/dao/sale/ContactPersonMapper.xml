<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ContactPersonMapper">

  <select id="datas" resultType="com.get.salecenter.vo.ContactPersonVo">
       select cp.* from  s_contact_person cp right join r_contact_person_company cpc
    <if test="fkTableName!=null and fkTableName !=''" >
      left join ${fkTableName} ip on cp.fk_table_id = ip.id
    </if>
    <where>
      <if test="fkTableName!=null and fkTableName !=''" >
        and  cp.fk_table_name = #{fkTableName}
      </if>
      <if test="targetName != null and  targetName !=''">
        and position(#{targetName,jdbcType=VARCHAR} in ip.name)
      </if>
      <if test="fkContactPersonTypeKey!=null and fkContactPersonTypeKey !=''" >
        and  cp.fk_contact_person_type_key like concat("%",#{fkContactPersonTypeKey},"%")
      </if>
      <if test="keyWord != null and  keyWord !=''">
        and position(#{keyWord,jdbcType=VARCHAR} in cp.name)
      </if>
      <if test="companyIds != null and companyIds.size()>0">
        AND cpc.fk_company_id IN
        <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
          #{companyId}
        </foreach>
      </if>
    </where>
  </select>
  <select id="getContactPersonEmailSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT id,email AS name FROM s_contact_person WHERE fk_table_name=#{fkTableName} AND fk_table_id=#{fkTableId}
        AND fk_contact_person_type_key!='CONTACT_AGENT_CONTRACT'
  </select>
  <select id="getContactPersonByType" resultType="java.lang.Integer">
    SELECT COUNT(0) FROM s_contact_person WHERE fk_contact_person_type_key = #{fkContactPersonType}
  </select>

  <select id="getExistContactPersonPM" resultType="com.get.salecenter.vo.ContactPersonVo">
    select sc.mobile,sc.id,sc.email,sc.tel,sc.fk_table_id,
    mc.num AS companyName,
    ma.name
    from m_agent ma
    join s_contact_person sc on ma.id =sc.fk_table_id
    join r_agent_company rac on rac.fk_agent_id = ma.id
    join ais_permission_center.m_company mc on mc.id = rac.fk_company_id
    where sc.fk_table_name='m_agent'
<!--    and rac.fk_company_id=#{companyId}-->
    <if test="email!=null and email !=''">
      and  sc.email=#{email}
    </if>
    <if test="mobile!=null and mobile !=''">
      and  sc.mobile=#{mobile}
    </if>
    <if test="tel!=null and tel !=''">
      and  sc.tel=#{tel}
    </if>
    GROUP BY sc.fk_table_id
  </select>

  <select id="getContactPersons" resultType="com.get.salecenter.vo.ContactPersonVo">
    SELECT
    a.id,
    a.fk_table_name,
    a.fk_table_id,
    a.fk_contact_person_type_key,
    a.`name`,
    a.gender,
    a.birthday,
    a.company,
    a.department,
    a.title,
    a.mobile_area_code,
    a.mobile,
    a.tel_area_code,
    a.tel,
    a.email,
    IFNULL(a.is_commission_email,0) is_commission_email,
    a.is_news_email,
    a.fk_area_country_ids_news,
    a.qq,
    a.wechat,
    a.whatsapp,
    a.contact_address,
    a.remark,
    a.gmt_create,
    a.gmt_create_user,
    a.gmt_modified,
    a.gmt_modified_user
    FROM ais_sale_center.s_contact_person a
    <if test="staffFollowerIds != null and staffFollowerIds.size() != 0">
       INNER JOIN (
       <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.contactPersonPermissionSql"/>
       ) z ON a.id = z.id
    </if>
    INNER JOIN ais_sale_center.m_agent b on a.fk_table_id = b.id AND a.fk_table_name = 'm_agent'
    <if test="contactPersonDto.fkAreaRegionId != null and contactPersonDto.fkAreaRegionId !=''">
        LEFT JOIN ais_institution_center.r_area_region_state c on b.fk_area_state_id = c.fk_area_state_id
    </if>
    INNER JOIN ais_sale_center.r_contact_person_company g ON g.fk_contact_person_id = a.id
    <if test="contactPersonDto.bdName != null and contactPersonDto.bdName !=''">
      INNER JOIN ais_sale_center.r_agent_staff AS agentStaff ON b.id = agentStaff.fk_agent_id WHERE agentStaff.is_active =1 AND agentStaff.fk_staff_id in (
        (SELECT m.id FROM ais_permission_center.m_staff m  INNER JOIN ais_sale_center.r_staff_bd_code AS bdCode ON m.id = bdCode.fk_staff_id
      WHERE  m.name LIKE CONCAT('%',#{contactPersonDto.bdName},'%') OR m.name_en like CONCAT('%',#{contactPersonDto.bdName},'%')  OR bdCode.bd_code like CONCAT('%',#{contactPersonDto.bdName},'%')))
    </if>
    <if test="contactPersonDto.bdName == null and contactPersonDto.bdName ==''">
      where 1=1
    </if>

    <choose>
      <when test="contactPersonDto.isContractContact">
<!--        and a.fk_contact_person_type_key = 'CONTACT_AGENT_CONTRACT'-->
<!--        修复类型多选的情况-->
        and FIND_IN_SET('CONTACT_AGENT_CONTRACT', a.fk_contact_person_type_key) > 0
      </when>
      <otherwise>
<!--        and a.fk_contact_person_type_key != 'CONTACT_AGENT_CONTRACT'-->
        and FIND_IN_SET('CONTACT_AGENT_CONTRACT', a.fk_contact_person_type_key) = 0
      </otherwise>
    </choose>
      <if test="companyIds != null and companyIds.size() != 0">
          AND g.fk_company_id in
          <foreach collection="companyIds" item="companyIditem" index="index" open="(" separator="," close=")">
              #{companyIditem}
          </foreach>
      </if>
<!--    <if test="contactPersonDto.bdName != null and contactPersonDto.bdName !=''">-->
<!--      AND (e.`name` LIKE CONCAT("%",#{contactPersonDto.bdName},"%") OR e.name_en LIKE CONCAT("%",#{contactPersonDto.bdName},"%")-->
<!--      OR EXISTS (SELECT id FROM ais_sale_center.r_staff_bd_code WHERE fk_staff_id=d.fk_staff_id-->
<!--      AND bd_code LIKE CONCAT("%",#{contactPersonDto.bdName},"%"))) &#45;&#45; bd名称或code过滤-->
<!--    </if>-->
    <if test="contactPersonDto.fkAreaCountryId != null and contactPersonDto.fkAreaCountryId !=''">
      AND b.fk_area_country_id = #{contactPersonDto.fkAreaCountryId} -- 代理国家查询
    </if>
    <if test="contactPersonDto.fkAreaStateId != null and contactPersonDto.fkAreaStateId !=''">
      AND b.fk_area_state_id = #{contactPersonDto.fkAreaStateId} -- 代理州省查询
    </if>
    <if test="contactPersonDto.fkAreaCityId != null and contactPersonDto.fkAreaCityId !=''">
      AND b.fk_area_city_id = #{contactPersonDto.fkAreaCityId} -- 代理城市查询
    </if>
    <if test="contactPersonDto.fkAreaRegionId != null and contactPersonDto.fkAreaRegionId !=''">
      AND c.fk_area_region_id = #{contactPersonDto.fkAreaRegionId} -- 大区查询
    </if>
    <if test="contactPersonDto.fkCompanyId != null and contactPersonDto.fkCompanyId !=''">
      AND g.fk_company_id  = #{contactPersonDto.fkCompanyId}
    </if>
    <if test="contactPersonDto.agentIsActive != null">
      AND b.is_active =#{contactPersonDto.agentIsActive}
    </if>
    <if test="contactPersonDto.fkTableName != null and contactPersonDto.fkTableName !=''">
      AND a.fk_table_name =#{contactPersonDto.fkTableName}  -- 类型查询
    </if>
    <if test="contactPersonDto.fkTableId != null and contactPersonDto.fkTableId !=''">
      AND a.fk_table_id =#{contactPersonDto.fkTableId}  -- tableId查询
    </if>
    <if test="contactPersonDto.fkContactPersonTypeKey != null and contactPersonDto.fkContactPersonTypeKey !=''">
      AND FIND_IN_SET(#{contactPersonDto.fkContactPersonTypeKey},a.fk_contact_person_type_key)  -- 联系人类型查询
    </if>
    <if test="contactPersonDto.keyWord != null and contactPersonDto.keyWord !=''">
      AND (a.name like CONCAT("%",#{contactPersonDto.keyWord},"%")
      OR
      a.remark like CONCAT("%",#{contactPersonDto.keyWord},"%")
      OR
      a.company like CONCAT("%",#{contactPersonDto.keyWord},"%")
      ) -- 关键词查询
    </if>
    <if test="contactPersonDto.isKeyAgent != null">
      AND b.is_key_agent = #{contactPersonDto.isKeyAgent} -- 是否关键代理
    </if>
    <if test="contactPersonDto.isCommissionEmail != null">
      AND IFNULL(a.is_commission_email,0) = #{contactPersonDto.isCommissionEmail} -- 是否合同佣金联系邮箱
    </if>
    <if test="contactPersonDto.queryWord != null and contactPersonDto.queryWord !=''">
      AND (
      REPLACE(LOWER(a.name)," ","") like concat("%",REPLACE(LOWER(#{contactPersonDto.queryWord})," ",""),"%")
      or
      a.wechat LIKE CONCAT("%",#{contactPersonDto.queryWord},"%")
      or
      a.email LIKE CONCAT("%",#{contactPersonDto.queryWord},"%")
      )
    </if> -- 代理编号查询/代理名称查询/代理名称查询
    group by a.id
    order by a.gmt_create desc
  </select>
    <select id="getContactPersonInfo" resultType="com.get.salecenter.vo.ContactPersonVo">
      SELECT
      a.id,
      a.fk_table_name,
      a.fk_table_id,
      a.fk_contact_person_type_key,
      a.name,
      a.gender,
      a.birthday,
      a.company,
      a.department,
      a.title,
      a.mobile_area_code,
      a.mobile,
      a.tel_area_code,
      a.tel,
      a.email,
      a.qq,
      a.wechat,
      a.whatsapp,
      a.contact_address,
      a.remark,
      a.gmt_create_user,
      a.gmt_create,
      a.gmt_modified_user,
      a.gmt_modified
--       b.fk_user_id,
--       m.login_id,
--       m.fk_platform_type
      FROM
      s_contact_person a
--       left join ais_app_issue_center.r_user_contact_person b on b.fk_contact_person_id = a.id
--       left join ais_app_registration_center.m_user m on b.fk_user_id = m.id
      WHERE 1=1
      <if test="agentIds != null and agentIds.size() > 0">
      AND a.fk_table_id in
      <foreach collection="agentIds" item="agentId" open="(" separator="," close=")">
        #{agentId}
      </foreach>
      </if>
      AND a.fk_table_name = 'm_agent'
      AND (a.fk_contact_person_type_key != 'CONTACT_AGENT_CONTRACT'  or  a.fk_contact_person_type_key is null)
    </select>
    <select id="getContactPersonMobileAreaCodeSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
      SELECT id,mobile_area_code AS name FROM s_contact_person WHERE fk_table_name=#{fkTableName} AND fk_table_id=#{fkTableId}
    </select>
  <select id="getContactPersonMobileSelect" resultType="com.get.salecenter.vo.ContactPersonMobileSelectVo">
    SELECT id,mobile AS name,mobile_area_code as agentContactTelAreaCode FROM s_contact_person WHERE fk_table_name=#{fkTableName} AND fk_table_id=#{fkTableId}
  </select>
</mapper>