<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentMapper">

    <select id="getAgentNameById" parameterType="java.lang.Long" resultType="string">
        select a.name
        from m_agent a
        where a.id = #{id}
    </select>

    <select id="isExistByAgentId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id), 0) id
        from m_agent
        where fk_parent_agent_id = #{agentId}
    </select>

    <select id="getAgentFollowerIds" resultType="java.lang.Long">
        SELECT id
        FROM (
                 SELECT @ids AS _ids,
                     (SELECT @ids := GROUP_CONCAT(id)
                        FROM m_agent
                        WHERE FIND_IN_SET(fk_parent_agent_id, @ids)
                     ) AS cids,
                        @l := @l+1 AS LEVEL
                 FROM m_agent,
                     (SELECT @ids := #{agentId}, @l := 0 ) b
                 WHERE @ids IS NOT NULL
             ) id,
             m_agent DATA
        WHERE FIND_IN_SET(DATA.fk_parent_agent_id, id._ids)
        GROUP BY id, fk_parent_agent_id
        ORDER BY id
    </select>

    <select id="getAgentFollowerIdsByIds" resultType="java.lang.Long">
        SELECT id
        FROM (
                 SELECT @ids AS _ids,
                     (SELECT @ids := GROUP_CONCAT(id)
                        FROM m_agent
                        WHERE FIND_IN_SET(fk_parent_agent_id, @ids)
                     ) AS cids,
                        @l := @l+1 AS LEVEL
                 FROM m_agent,
                     (SELECT @ids := #{agentIds}, @l := 0 ) b
                 WHERE @ids IS NOT NULL
             ) id,
             m_agent DATA
        WHERE FIND_IN_SET(DATA.fk_parent_agent_id, id._ids)
        GROUP BY id, fk_parent_agent_id
        ORDER BY id
    </select>

    <select id="getSubordinateNotPortAgentIds" resultType="java.lang.Long">
        SELECT id FROM m_agent WHERE fk_parent_agent_id IN
        <foreach collection="agentIds" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND is_settlement_port = 0
    </select>

    <select id="isActiveByAgentId" resultType="java.lang.Boolean">
        SELECT is_active
        FROM m_agent
        WHERE id = #{agentId}

    </select>


    <select id="commissionSummary" resultType="com.get.salecenter.vo.CommissionSummaryVo">
        SELECT
        x.*
        FROM
        (
        <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_offer_item'">
            SELECT
            a.id,
            CASE
            WHEN (a.name_note IS NOT NULL AND a.name_note!='')
            THEN
            CONCAT(
            a.`name`,
            '（',
            IFNULL(a.name_note, ''),
            '）'
            )
            ELSE
            a.NAME
            END AS agentName,
            CASE WHEN a.nature = 1 THEN a.tax_code ELSE a.id_card_num END agentIdCard,
            CASE WHEN IFNULL(s.name_en,'')='' THEN s.`name` ELSE CONCAT(s.`name`,'（',s.name_en,'）') END bdName,
            mpp.fk_currency_type_num AS planCurrencyNum,
            mpp.fk_type_key,
            aca.fk_currency_type_num AS accountCurrencyNum,
            SUM( mpp.payable_amount ) AS payableAmount,
            GROUP_CONCAT( DISTINCT stu.NAME ) AS studentName,
            IFNULL(SUM(c.paidAmount), 0) AS paidAmount,
            IFNULL(SUM(c.differenceAmount), 0) AS differenceAmount,
            aca.id AS agentContractAccountId,
            aca.bank_account,
            aca.bank_account_num,
            aca.bank_name,
            aca.bank_branch_name,
            aca.bank_address,
            aca.area_country_code,
            CASE WHEN aca.bank_code_type = 'SwiftCode' THEN aca.bank_code ELSE CONCAT(aca.bank_code_type,':',aca.bank_code) END swift_code,
            SUM(rppsi.amount_actual) AS amount_actual,
            SUM(rppsi.serviceFeeActual) AS serviceFeeActual,
            GROUP_CONCAT(rppsi.settlementIds) AS settlementIds,
            GROUP_CONCAT( DISTINCT com.short_name ) AS fkCompanyName,
            IFNULL(rppsf.id, 0) AS lockFlag
            FROM
            ais_sale_center.m_agent AS a
            INNER JOIN m_student_offer_item AS msoi ON msoi.fk_agent_id = a.id
            INNER JOIN m_payable_plan AS mpp ON mpp.fk_type_target_id = msoi.id
            AND mpp.fk_type_key = 'm_student_offer_item'
            AND mpp.STATUS = 1
            INNER JOIN ais_sale_center.m_student AS stu ON stu.id = msoi.fk_student_id
            <if test="commissionSummaryDto.fkCompanyId != null and commissionSummaryDto.fkCompanyId !=''">
                AND stu.fk_company_id = #{commissionSummaryDto.fkCompanyId}
            </if>
            <if test="commissionSummaryDto.fkCompanyIds != null and commissionSummaryDto.fkCompanyIds.size()>0">
                AND stu.fk_company_id in
                <foreach collection="commissionSummaryDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
            INNER JOIN ais_permission_center.m_company AS com ON stu.fk_company_id = com.id
            LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id
            AND ras.is_active = 1
            LEFT JOIN ais_permission_center.m_staff AS s ON s.id = ras.fk_staff_id
            AND s.is_active = 1
            -- 实际支付金额
            INNER JOIN (
                SELECT rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual,
                GROUP_CONCAT(rppsi1.id) AS settlementIds
                FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
                WHERE rppsi1.status_settlement = 3
                GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
            ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            INNER JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.id = rppsi.fk_agent_contract_account_id
            --      显示已付金额、差额
            LEFT JOIN (
            SELECT
            mpp2.id,
            IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
            mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
            FROM
            ais_sale_center.m_payable_plan AS mpp2
            LEFT JOIN (
            SELECT
            mpp3.id,
            IFNULL( SUM( mpfi2.amount_payable ), 0 ) + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
            FROM
            ais_sale_center.m_payable_plan AS mpp3
            LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
            AND mpp3.fk_type_key = 'm_student_offer_item'
            LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
            WHERE
            mpp3.STATUS = 1 AND mpf2.status = 1
            GROUP BY
            mpp3.id
            ) mpp4 ON mpp4.id = mpp2.id
            ) c ON c.id = mpp.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_flag AS rppsf ON rppsf.fk_agent_id = a.id
            AND rppsf.fk_currency_type_num = mpp.fk_currency_type_num
            AND rppsf.fk_currency_type_num_account = aca.fk_currency_type_num
            AND rppsf.fk_type_key = 'm_student_offer_item'
            <where>
                <if test="commissionSummaryDto.agentNameOrNum != null and commissionSummaryDto.agentNameOrNum != ''">
                    AND (a.num LIKE CONCAT('%', #{commissionSummaryDto.agentNameOrNum}, '%') OR a.name LIKE CONCAT('%',
                    #{commissionSummaryDto.agentNameOrNum}, '%'))
                </if>
                <if test="commissionSummaryDto.fkCurrencyTypeNum != null and commissionSummaryDto.fkCurrencyTypeNum != ''">
                    AND aca.fk_currency_type_num = #{commissionSummaryDto.fkCurrencyTypeNum}
                </if>
                <if test="commissionSummaryDto.accountCurrencyNum != null and commissionSummaryDto.accountCurrencyNum != ''">
                    AND rppsaa.fk_currency_type_num = #{commissionSummaryDto.accountCurrencyNum}
                </if>

            </where>
            GROUP BY
            a.id,
            mpp.fk_currency_type_num,
            aca.id

        </if>


        <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_insurance'">
            <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_offer_item'">
                UNION ALL
            </if>
            SELECT
            a.id,
            a.NAME AS agentName,
            CASE WHEN a.nature = 1 THEN a.tax_code ELSE a.id_card_num END agentIdCard,
            CASE WHEN IFNULL(ms.name_en,'')='' THEN ms.`name` ELSE CONCAT(ms.`name`,'（',ms.name_en,'）') END bdName,
            mpp.fk_currency_type_num AS planCurrencyNum,
            mpp.fk_type_key,
            aca.fk_currency_type_num AS accountCurrencyNum,
            SUM( mpp.payable_amount ) AS payableAmount,
            CASE
            WHEN msi.type = 1
            THEN msi.insurant_name
            ELSE GROUP_CONCAT( DISTINCT s.NAME ) END AS studentName,
            IFNULL(SUM(c.paidAmount), 0) AS paidAmount,
            IFNULL(SUM(c.differenceAmount), 0) AS differenceAmount,
            aca.id AS agentContractAccountId,
            aca.bank_account,
            aca.bank_account_num,
            aca.bank_name,
            aca.bank_branch_name,
            aca.bank_address,
            aca.area_country_code,
            CASE WHEN aca.bank_code_type = 'SwiftCode' THEN aca.bank_code ELSE CONCAT(aca.bank_code_type,':',aca.bank_code) END swift_code,
            SUM(rppsi.amount_actual) AS amount_actual,
            SUM(rppsi.serviceFeeActual) AS serviceFeeActual,
            GROUP_CONCAT(rppsi.settlementIds) AS settlementIds,
            GROUP_CONCAT( DISTINCT com.short_name ) AS fkCompanyName,
            IFNULL(rppsf.id, 0) AS lockFlag
            FROM
            ais_sale_center.m_agent AS a
            INNER JOIN m_student_insurance AS msi ON msi.fk_agent_id = a.id
            INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = msi.id
            AND mpp.fk_type_key = 'm_student_insurance'
            AND mpp.STATUS = 1
            INNER JOIN ais_sale_center.m_student AS stu ON stu.id = msi.fk_student_id
            <if test="commissionSummaryDto.fkCompanyId != null and commissionSummaryDto.fkCompanyId !=''">
                AND stu.fk_company_id = #{commissionSummaryDto.fkCompanyId}
            </if>
            <if test="commissionSummaryDto.fkCompanyIds != null and commissionSummaryDto.fkCompanyIds.size()>0">
                AND stu.fk_company_id in
                <foreach collection="commissionSummaryDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
            INNER JOIN ais_permission_center.m_company AS com ON stu.fk_company_id = com.id
            INNER JOIN m_student AS s ON s.id = msi.fk_student_id
            <!--AND s.fk_company_id = #{commissionSummaryDto.fkCompanyId}-->
            LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id
            AND ras.is_active = 1
            LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = ras.fk_staff_id
            AND ms.is_active = 1
            -- 实际支付金额
            INNER JOIN (
                SELECT rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual,
                GROUP_CONCAT(rppsi1.id) AS settlementIds
                FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
                WHERE rppsi1.status_settlement = 3
                GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
            ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            INNER JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.id = rppsi.fk_agent_contract_account_id
            --      显示已付金额、差额
            LEFT JOIN (
            SELECT
            mpp2.id,
            IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
            mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
            FROM
            ais_sale_center.m_payable_plan AS mpp2
            LEFT JOIN (
            SELECT
            mpp3.id,
            IFNULL( SUM( mpfi2.amount_payable ), 0 )  + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
            FROM
            ais_sale_center.m_payable_plan AS mpp3
            LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
            AND mpp3.fk_type_key = 'm_student_insurance'
            LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
            WHERE
            mpp3.STATUS = 1 AND mpf2.status = 1
            GROUP BY
            mpp3.id
            ) mpp4 ON mpp4.id = mpp2.id
            ) c ON c.id = mpp.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_flag AS rppsf ON rppsf.fk_agent_id = a.id
            AND rppsf.fk_currency_type_num = mpp.fk_currency_type_num
            AND rppsf.fk_currency_type_num_account = aca.fk_currency_type_num
            AND rppsf.fk_type_key = 'm_student_insurance'
            <where>
                <if test="commissionSummaryDto.agentNameOrNum != null and commissionSummaryDto.agentNameOrNum != ''">
                    AND (a.num LIKE CONCAT('%', #{commissionSummaryDto.agentNameOrNum}, '%') OR a.name LIKE CONCAT('%',
                    #{commissionSummaryDto.agentNameOrNum}, '%'))
                </if>
                <if test="commissionSummaryDto.fkCurrencyTypeNum != null and commissionSummaryDto.fkCurrencyTypeNum != ''">
                    AND aca.fk_currency_type_num = #{commissionSummaryDto.fkCurrencyTypeNum}
                </if>
                <if test="commissionSummaryDto.accountCurrencyNum != null and commissionSummaryDto.accountCurrencyNum != ''">
                    AND rppsaa.fk_currency_type_num = #{commissionSummaryDto.accountCurrencyNum}
                </if>
            </where>
            GROUP BY
            a.id,
            mpp.fk_currency_type_num,
            aca.id

        </if>

        <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_accommodation'">
            <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_offer_item'">
                UNION ALL
            </if>
            SELECT
            a.id,
            a.NAME AS agentName,
            CASE WHEN a.nature = 1 THEN a.tax_code ELSE a.id_card_num END agentIdCard,
            CASE WHEN IFNULL(ms.name_en,'')='' THEN ms.`name` ELSE CONCAT(ms.`name`,'（',ms.name_en,'）') END bdName,
            mpp.fk_currency_type_num AS planCurrencyNum,
            mpp.fk_type_key,
            aca.fk_currency_type_num AS accountCurrencyNum,
            SUM( mpp.payable_amount ) AS payableAmount,
            GROUP_CONCAT( DISTINCT stu.NAME ) AS studentName,
            IFNULL(SUM(c.paidAmount), 0) AS paidAmount,
            IFNULL(SUM(c.differenceAmount), 0) AS differenceAmount,
            aca.id AS agentContractAccountId,
            aca.bank_account,
            aca.bank_account_num,
            aca.bank_name,
            aca.bank_branch_name,
            aca.bank_address,
            aca.area_country_code,
            CASE WHEN aca.bank_code_type = 'SwiftCode' THEN aca.bank_code ELSE CONCAT(aca.bank_code_type,':',aca.bank_code) END swift_code,
            SUM(rppsi.amount_actual) AS amount_actual,
            SUM(rppsi.serviceFeeActual) AS serviceFeeActual,
            GROUP_CONCAT(rppsi.settlementIds) AS settlementIds,
            GROUP_CONCAT( DISTINCT com.short_name ) AS fkCompanyName,
            IFNULL(rppsf.id, 0) AS lockFlag
            FROM
            ais_sale_center.m_agent AS a
            INNER JOIN m_student_accommodation AS msa ON msa.fk_agent_id = a.id
            INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = msa.id
            AND mpp.fk_type_key = 'm_student_accommodation'
            AND mpp.STATUS = 1
            INNER JOIN ais_sale_center.m_student AS stu ON stu.id = msa.fk_student_id
            <if test="commissionSummaryDto.fkCompanyId != null and commissionSummaryDto.fkCompanyId !=''">
                AND stu.fk_company_id = #{commissionSummaryDto.fkCompanyId}
            </if>
            <if test="commissionSummaryDto.fkCompanyIds != null and commissionSummaryDto.fkCompanyIds.size()>0">
                AND stu.fk_company_id in
                <foreach collection="commissionSummaryDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
            INNER JOIN ais_permission_center.m_company AS com ON stu.fk_company_id = com.id
            LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id
            AND ras.is_active = 1
            LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = ras.fk_staff_id
            AND ms.is_active = 1
            -- 实际支付金额
            INNER JOIN (
                SELECT rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual,
                GROUP_CONCAT(rppsi1.id) AS settlementIds
                FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
                WHERE rppsi1.status_settlement = 3
                GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
            ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            INNER JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.id = rppsi.fk_agent_contract_account_id
            --      显示已付金额、差额
            LEFT JOIN (
            SELECT
            mpp2.id,
            IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
            mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
            FROM
            ais_sale_center.m_payable_plan AS mpp2
            LEFT JOIN (
            SELECT
            mpp3.id,
            IFNULL( SUM( mpfi2.amount_payable ), 0 )  + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
            FROM
            ais_sale_center.m_payable_plan AS mpp3
            LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
            AND mpp3.fk_type_key = 'm_student_accommodation'
            LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
            WHERE
            mpp3.STATUS = 1 AND mpf2.status = 1
            GROUP BY
            mpp3.id
            ) mpp4 ON mpp4.id = mpp2.id
            ) c ON c.id = mpp.id
            <!--            <if test="commissionSummaryDto.studentName != null and commissionSummaryDto.studentName != ''">-->
            <!--                AND s.name LIKE CONCAT('%', #{commissionSummaryDto.studentName}, '%')-->
            <!--            </if>-->
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_flag AS rppsf ON rppsf.fk_agent_id = a.id
            AND rppsf.fk_currency_type_num = mpp.fk_currency_type_num
            AND rppsf.fk_currency_type_num_account = aca.fk_currency_type_num
            AND rppsf.fk_type_key = 'm_student_accommodation'
            <where>
                <if test="commissionSummaryDto.agentNameOrNum != null and commissionSummaryDto.agentNameOrNum != ''">
                    AND (a.num LIKE CONCAT('%', #{commissionSummaryDto.agentNameOrNum}, '%') OR a.name LIKE CONCAT('%',
                    #{commissionSummaryDto.agentNameOrNum}, '%'))
                </if>
                <if test="commissionSummaryDto.fkCurrencyTypeNum != null and commissionSummaryDto.fkCurrencyTypeNum != ''">
                    AND aca.fk_currency_type_num = #{commissionSummaryDto.fkCurrencyTypeNum}
                </if>
                <if test="commissionSummaryDto.accountCurrencyNum != null and commissionSummaryDto.accountCurrencyNum != ''">
                    AND rppsaa.fk_currency_type_num = #{commissionSummaryDto.accountCurrencyNum}
                </if>
            </where>
            GROUP BY
            a.id,
            mpp.fk_currency_type_num,
            aca.id
        </if>
        <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_service_fee'">
            <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_offer_item'">
                UNION ALL
            </if>
            SELECT
            a.id,
            a.NAME AS agentName,
            CASE WHEN a.nature = 1 THEN a.tax_code ELSE a.id_card_num END agentIdCard,
            CASE WHEN IFNULL(ms.name_en,'')='' THEN ms.`name` ELSE CONCAT(ms.`name`,'（',ms.name_en,'）') END bdName,
            mpp.fk_currency_type_num AS planCurrencyNum,
            mpp.fk_type_key,
            aca.fk_currency_type_num AS accountCurrencyNum,
            SUM( mpp.payable_amount ) AS payableAmount,
            GROUP_CONCAT( DISTINCT stu.NAME ) AS studentName,
            IFNULL(SUM(c.paidAmount), 0) AS paidAmount,
            IFNULL(SUM(c.differenceAmount), 0) AS differenceAmount,
            aca.id AS agentContractAccountId,
            aca.bank_account,
            aca.bank_account_num,
            aca.bank_name,
            aca.bank_branch_name,
            aca.bank_address,
            aca.area_country_code,
            CASE WHEN aca.bank_code_type = 'SwiftCode' THEN aca.bank_code ELSE CONCAT(aca.bank_code_type,':',aca.bank_code) END swift_code,
            SUM(rppsi.amount_actual) AS amount_actual,
            SUM(rppsi.serviceFeeActual) AS serviceFeeActual,
            GROUP_CONCAT(rppsi.settlementIds) AS settlementIds,
            GROUP_CONCAT( DISTINCT com.short_name ) AS fkCompanyName,
            IFNULL(rppsf.id, 0) AS lockFlag
            FROM
            ais_sale_center.m_agent AS a
            INNER JOIN m_student_service_fee AS mssf ON mssf.fk_agent_id = a.id
            INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = mssf.id
            AND mpp.fk_type_key = 'm_student_service_fee'
            AND mpp.STATUS = 1
            INNER JOIN ais_sale_center.m_student AS stu ON stu.id = mssf.fk_student_id
            <if test="commissionSummaryDto.fkCompanyId != null and commissionSummaryDto.fkCompanyId !=''">
                AND stu.fk_company_id = #{commissionSummaryDto.fkCompanyId}
            </if>
            <if test="commissionSummaryDto.fkCompanyIds != null and commissionSummaryDto.fkCompanyIds.size()>0">
                AND stu.fk_company_id in
                <foreach collection="commissionSummaryDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
            INNER JOIN ais_permission_center.m_company AS com ON stu.fk_company_id = com.id
            LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id
            AND ras.is_active = 1
            LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = ras.fk_staff_id
            AND ms.is_active = 1
            -- 实际支付金额
            INNER JOIN (
                SELECT rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual,
                GROUP_CONCAT(rppsi1.id) AS settlementIds
                FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
                WHERE rppsi1.status_settlement = 3
                GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
            ) AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            INNER JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.id = rppsi.fk_agent_contract_account_id
            --      显示已付金额、差额
            LEFT JOIN (
            SELECT
            mpp2.id,
            IFNULL( mpp4.paidAmount, 0 ) AS paidAmount,
            mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
            FROM
            ais_sale_center.m_payable_plan AS mpp2
            LEFT JOIN (
            SELECT
            mpp3.id,
            IFNULL( SUM( mpfi2.amount_payable ), 0 )  + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
            FROM
            ais_sale_center.m_payable_plan AS mpp3
            LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
            AND mpp3.fk_type_key = 'm_student_service_fee'
            LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
            WHERE
            mpp3.STATUS = 1 AND mpf2.status = 1
            GROUP BY
            mpp3.id
            ) mpp4 ON mpp4.id = mpp2.id
            ) c ON c.id = mpp.id
            <!--            <if test="commissionSummaryDto.studentName != null and commissionSummaryDto.studentName != ''">-->
            <!--                AND s.name LIKE CONCAT('%', #{commissionSummaryDto.studentName}, '%')-->
            <!--            </if>-->
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_flag AS rppsf ON rppsf.fk_agent_id = a.id
            AND rppsf.fk_currency_type_num = mpp.fk_currency_type_num
            AND rppsf.fk_currency_type_num_account = aca.fk_currency_type_num
            AND rppsf.fk_type_key = 'm_student_service_fee'
            <where>
                <if test="commissionSummaryDto.agentNameOrNum != null and commissionSummaryDto.agentNameOrNum != ''">
                    AND (a.num LIKE CONCAT('%', #{commissionSummaryDto.agentNameOrNum}, '%') OR a.name LIKE CONCAT('%',
                    #{commissionSummaryDto.agentNameOrNum}, '%'))
                </if>
                <if test="commissionSummaryDto.fkCurrencyTypeNum != null and commissionSummaryDto.fkCurrencyTypeNum != ''">
                    AND aca.fk_currency_type_num = #{commissionSummaryDto.fkCurrencyTypeNum}
                </if>
                <if test="commissionSummaryDto.accountCurrencyNum != null and commissionSummaryDto.accountCurrencyNum != ''">
                    AND rppsaa.fk_currency_type_num = #{commissionSummaryDto.accountCurrencyNum}
                </if>
            </where>
            GROUP BY
            a.id,
            mpp.fk_currency_type_num,
            aca.id
        </if>
        ) x
        <if test="commissionSummarySecondaryScreeningDtoList != null and commissionSummarySecondaryScreeningDtoList.size != 0">
            where 1=2
            <foreach collection="commissionSummarySecondaryScreeningDtoList" item="commissionSummarySecondaryScreeningVo"
                     index="index">
                OR (x.planCurrencyNum = #{commissionSummarySecondaryScreeningVo.payPlanCurrencyTypeNum}
                AND x.accountCurrencyNum = #{commissionSummarySecondaryScreeningVo.accountCurrencyTypeNum}
                AND x.fk_type_key = #{commissionSummarySecondaryScreeningVo.fkTypeKey}
                AND x.id = #{commissionSummarySecondaryScreeningVo.agentId}
                )
            </foreach>
        </if>
        ORDER BY
        CONVERT(x.agentName USING gbk)




        <!--        SELECT-->
        <!--        x.*-->
        <!--        FROM-->
        <!--        (-->
        <!--        <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_offer_item'">-->
        <!--            SELECT-->
        <!--            b.id,-->
        <!--            CASE WHEN IFNULL(s.name_en,'')='' THEN s.`name` ELSE CONCAT(s.`name`,'（',s.name_en,'）') END bdName,-->
        <!--            b.agentName,-->
        <!--            b.planCurrencyNum,-->
        <!--            b.accountCurrencyNum,-->
        <!--            b.payableAmount,-->
        <!--            b.studentName,-->
        <!--            b.fk_type_key,-->
        <!--            aca.bank_account,-->
        <!--            aca.bank_account_num,-->
        <!--            aca.bank_name,-->
        <!--            aca.bank_branch_name,-->
        <!--            aca.bank_address,-->
        <!--            aca.swift_code,-->
        <!--            aca.other_code-->
        <!--            FROM-->
        <!--            ais_sale_center.m_agent AS a-->
        <!--            LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id-->
        <!--            AND ras.is_active = 1-->
        <!--            LEFT JOIN ais_permission_center.m_staff AS s ON s.id = ras.fk_staff_id-->
        <!--            AND s.is_active = 1-->
        <!--            INNER JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.fk_agent_id = a.id-->
        <!--            INNER JOIN (-->
        <!--            SELECT-->
        <!--            a.id,-->
        <!--            a.NAME AS agentName,-->
        <!--            mpp.fk_currency_type_num AS planCurrencyNum,-->
        <!--            mpp.fk_type_key,-->
        <!--            aca.fk_currency_type_num AS accountCurrencyNum,-->
        <!--            SUM( mpp.payable_amount ) AS payableAmount,-->
        <!--            GROUP_CONCAT( DISTINCT stu.NAME ) AS studentName-->
        <!--            FROM-->
        <!--            ais_sale_center.m_agent AS a-->
        <!--            INNER JOIN m_student_offer_item AS msoi ON msoi.fk_agent_id = a.id AND msoi.status = 1 AND a.is_active = 1-->
        <!--            INNER JOIN m_payable_plan AS mpp ON mpp.fk_type_target_id = msoi.id-->
        <!--            AND mpp.fk_type_key = 'm_student_offer_item'-->
        <!--            AND mpp.status_settlement = 3-->
        <!--            AND mpp.STATUS = 1-->
        <!--            INNER JOIN ais_sale_center.m_student AS stu ON stu.id = msoi.fk_student_id AND stu.fk_company_id = #{commissionSummaryDto.fkCompanyId}-->
        <!--            &lt;!&ndash;            <if test="commissionSummaryDto.studentName != null and commissionSummaryDto.studentName != ''">&ndash;&gt;-->
        <!--            &lt;!&ndash;                AND stu.name LIKE CONCAT('%', #{commissionSummaryDto.studentName}, '%')&ndash;&gt;-->
        <!--            &lt;!&ndash;            </if>&ndash;&gt;-->
        <!--            INNER JOIN ais_sale_center.r_payable_plan_settlement_agent_account AS rppsaa ON rppsaa.fk_payable_plan_id =-->
        <!--            mpp.id-->
        <!--            INNER JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.id = rppsaa.fk_agent_contract_account_id-->
        <!--&#45;&#45;             WHERE  a.is_settlement_port =1-->
        <!--            GROUP BY-->
        <!--            a.id,-->
        <!--            mpp.fk_currency_type_num,-->
        <!--            aca.fk_currency_type_num-->
        <!--            ) b ON b.id = a.id-->
        <!--            AND b.accountCurrencyNum = aca.fk_currency_type_num-->
        <!--            <where>-->
        <!--                <if test="commissionSummaryDto.agentNameOrNum != null and commissionSummaryDto.agentNameOrNum != ''">-->
        <!--                    AND (a.num LIKE CONCAT('%', #{commissionSummaryDto.agentNameOrNum}, '%') OR a.name LIKE CONCAT('%',-->
        <!--                    #{commissionSummaryDto.agentNameOrNum}, '%'))-->
        <!--                </if>-->
        <!--                <if test="commissionSummaryDto.fkCurrencyTypeNum != null and commissionSummaryDto.fkCurrencyTypeNum != ''">-->
        <!--                    AND aca.fk_currency_type_num = #{commissionSummaryDto.fkCurrencyTypeNum}-->
        <!--                </if>-->
        <!--            </where>-->
        <!--        </if>-->


        <!--        <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_insurance'">-->
        <!--            <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_offer_item'">-->
        <!--                UNION ALL-->
        <!--            </if>-->
        <!--            SELECT-->
        <!--            b.id,-->
        <!--            s.NAME AS bdName,-->
        <!--            b.agentName,-->
        <!--            b.planCurrencyNum,-->
        <!--            b.accountCurrencyNum,-->
        <!--            b.payableAmount,-->
        <!--            b.studentName,-->
        <!--            b.fk_type_key,-->
        <!--            aca.bank_account,-->
        <!--            aca.bank_account_num,-->
        <!--            aca.bank_name,-->
        <!--            aca.bank_branch_name,-->
        <!--            aca.bank_address,-->
        <!--            aca.swift_code,-->
        <!--            aca.other_code-->
        <!--            FROM-->
        <!--            ais_sale_center.m_agent AS a-->
        <!--            LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id-->
        <!--            AND ras.is_active = 1-->
        <!--            LEFT JOIN ais_permission_center.m_staff AS s ON s.id = ras.fk_staff_id-->
        <!--            AND s.is_active = 1-->
        <!--            INNER JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.fk_agent_id = a.id-->
        <!--            INNER JOIN (-->
        <!--            SELECT-->
        <!--            a.id,-->
        <!--            a.NAME AS agentName,-->
        <!--            mpp.fk_currency_type_num AS planCurrencyNum,-->
        <!--            mpp.fk_type_key,-->
        <!--            aca.fk_currency_type_num AS accountCurrencyNum,-->
        <!--            SUM( mpp.payable_amount ) AS payableAmount,-->
        <!--            GROUP_CONCAT( DISTINCT s.NAME ) AS studentName-->
        <!--            FROM-->
        <!--            ais_sale_center.m_agent AS a-->
        <!--            INNER JOIN m_student_insurance AS msi ON msi.fk_agent_id = a.id AND msi.status = 1-->
        <!--            INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = msi.id-->
        <!--            AND mpp.fk_type_key = 'm_student_insurance'-->
        <!--            AND mpp.STATUS = 1-->
        <!--            AND mpp.status_settlement = 3-->
        <!--            INNER JOIN ais_sale_center.r_payable_plan_settlement_agent_account AS rppsaa ON rppsaa.fk_payable_plan_id =-->
        <!--            mpp.id-->
        <!--            INNER JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.id = rppsaa.fk_agent_contract_account_id-->
        <!--            INNER JOIN m_student AS s ON s.id = msi.fk_student_id AND s.fk_company_id = #{commissionSummaryDto.fkCompanyId}-->
        <!--            &lt;!&ndash;            <if test="commissionSummaryDto.studentName != null and commissionSummaryDto.studentName != ''">&ndash;&gt;-->
        <!--            &lt;!&ndash;                AND s.name LIKE CONCAT('%', #{commissionSummaryDto.studentName}, '%')&ndash;&gt;-->
        <!--            &lt;!&ndash;            </if>&ndash;&gt;-->
        <!--&#45;&#45;             WHERE  a.is_settlement_port =1-->
        <!--            GROUP BY-->
        <!--            a.id,-->
        <!--            mpp.fk_currency_type_num,-->
        <!--            aca.fk_currency_type_num-->
        <!--            ) b ON b.id = a.id-->
        <!--            AND b.accountCurrencyNum = aca.fk_currency_type_num-->
        <!--            <where>-->
        <!--                <if test="commissionSummaryDto.agentNameOrNum != null and commissionSummaryDto.agentNameOrNum != ''">-->
        <!--                    AND (a.num LIKE CONCAT('%', #{commissionSummaryDto.agentNameOrNum}, '%') OR a.name LIKE CONCAT('%',-->
        <!--                    #{commissionSummaryDto.agentNameOrNum}, '%'))-->
        <!--                </if>-->
        <!--                <if test="commissionSummaryDto.fkCurrencyTypeNum != null and commissionSummaryDto.fkCurrencyTypeNum != ''">-->
        <!--                    AND aca.fk_currency_type_num = #{commissionSummaryDto.fkCurrencyTypeNum}-->
        <!--                </if>-->
        <!--            </where>-->
        <!--        </if>-->

        <!--        <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_accommodation'">-->
        <!--            <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_offer_item'">-->
        <!--                UNION ALL-->
        <!--            </if>-->
        <!--            SELECT-->
        <!--            b.id,-->
        <!--            s.NAME AS bdName,-->
        <!--            b.agentName,-->
        <!--            b.planCurrencyNum,-->
        <!--            b.accountCurrencyNum,-->
        <!--            b.payableAmount,-->
        <!--            b.studentName,-->
        <!--            b.fk_type_key,-->
        <!--            aca.bank_account,-->
        <!--            aca.bank_account_num,-->
        <!--            aca.bank_name,-->
        <!--            aca.bank_branch_name,-->
        <!--            aca.bank_address,-->
        <!--            aca.swift_code,-->
        <!--            aca.other_code-->
        <!--            FROM-->
        <!--            ais_sale_center.m_agent AS a-->
        <!--            LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id-->
        <!--            AND ras.is_active = 1-->
        <!--            LEFT JOIN ais_permission_center.m_staff AS s ON s.id = ras.fk_staff_id-->
        <!--            AND s.is_active = 1-->
        <!--            INNER JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.fk_agent_id = a.id-->
        <!--            INNER JOIN (-->
        <!--            SELECT-->
        <!--            a.id,-->
        <!--            a.NAME AS agentName,-->
        <!--            mpp.fk_currency_type_num AS planCurrencyNum,-->
        <!--            mpp.fk_type_key,-->
        <!--            aca.fk_currency_type_num AS accountCurrencyNum,-->
        <!--            SUM( mpp.payable_amount ) AS payableAmount,-->
        <!--            GROUP_CONCAT( DISTINCT s.NAME ) AS studentName-->
        <!--            FROM-->
        <!--            ais_sale_center.m_agent AS a-->
        <!--            INNER JOIN m_student_accommodation AS msa ON msa.fk_agent_id = a.id AND msa.status = 1-->
        <!--            INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = msa.id-->
        <!--            AND mpp.fk_type_key = 'm_student_accommodation'-->
        <!--            AND mpp.STATUS = 1-->
        <!--            AND mpp.status_settlement = 3-->
        <!--            INNER JOIN ais_sale_center.r_payable_plan_settlement_agent_account AS rppsaa ON rppsaa.fk_payable_plan_id =-->
        <!--            mpp.id-->
        <!--            INNER JOIN ais_sale_center.m_agent_contract_account AS aca ON aca.id = rppsaa.fk_agent_contract_account_id-->
        <!--            INNER JOIN m_student AS s ON s.id = msa.fk_student_id AND s.fk_company_id = #{commissionSummaryDto.fkCompanyId}-->
        <!--&#45;&#45;             WHERE a.is_settlement_port =1-->
        <!--            &lt;!&ndash;            <if test="commissionSummaryDto.studentName != null and commissionSummaryDto.studentName != ''">&ndash;&gt;-->
        <!--            &lt;!&ndash;                AND s.name LIKE CONCAT('%', #{commissionSummaryDto.studentName}, '%')&ndash;&gt;-->
        <!--            &lt;!&ndash;            </if>&ndash;&gt;-->
        <!--            GROUP BY-->
        <!--            a.id,-->
        <!--            mpp.fk_currency_type_num,-->
        <!--            aca.fk_currency_type_num-->
        <!--            ) b ON b.id = a.id-->
        <!--            AND b.accountCurrencyNum = aca.fk_currency_type_num-->
        <!--            <where>-->
        <!--                <if test="commissionSummaryDto.agentNameOrNum != null and commissionSummaryDto.agentNameOrNum != ''">-->
        <!--                    AND (a.num LIKE CONCAT('%', #{commissionSummaryDto.agentNameOrNum}, '%') OR a.name LIKE CONCAT('%',-->
        <!--                    #{commissionSummaryDto.agentNameOrNum}, '%'))-->
        <!--                </if>-->
        <!--                <if test="commissionSummaryDto.fkCurrencyTypeNum != null and commissionSummaryDto.fkCurrencyTypeNum != ''">-->
        <!--                    AND aca.fk_currency_type_num = #{commissionSummaryDto.fkCurrencyTypeNum}-->
        <!--                </if>-->
        <!--            </where>-->
        <!--        </if>-->
        <!--        ) x-->
        <!--        <if test="commissionSummarySecondaryScreeningDtoList != null and commissionSummarySecondaryScreeningDtoList.size != 0">-->
        <!--            where 1=2-->
        <!--            <foreach collection="commissionSummarySecondaryScreeningDtoList" item="commissionSummarySecondaryScreeningVo"-->
        <!--                     index="index">-->
        <!--               OR (x.planCurrencyNum = #{commissionSummarySecondaryScreeningVo.payPlanCurrencyTypeNum}-->
        <!--                AND x.accountCurrencyNum = #{commissionSummarySecondaryScreeningVo.accountCurrencyTypeNum}-->
        <!--                AND x.fk_type_key = #{commissionSummarySecondaryScreeningVo.fkTypeKey} )-->
        <!--            </foreach>-->
        <!--        </if>-->
        <!--        ORDER BY-->
        <!--        x.agentName-->
    </select>

    <select id="getAgentPayablePlanByNumSettlementBatch" resultType="com.get.salecenter.vo.PayablePlanVo">
        SELECT a.id AS fkAgentId,
               rppsi.fk_agent_contract_account_id,
               rppsi.fk_currency_type_num AS accountCurrencyTypeNum,
               mpp.*,
               rppsi.amount_actual,
               rppsi.serviceFeeActual,
               rppsi.fk_receipt_form_item_id
        FROM m_agent AS a
        -- 实际支付金额
        INNER JOIN (
            SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual, MAX(rppsi1.fk_receipt_form_item_id) AS fk_receipt_form_item_id,
                MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num
            FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            WHERE  rppsi1.num_settlement_batch = #{numSettlementBatch}
            GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num
        ) AS rppsi ON rppsi.fk_agent_id_settlement = a.id
        INNER JOIN m_payable_plan AS mpp ON mpp.id = rppsi.fk_payable_plan_id
            AND mpp.fk_type_key = 'm_student_offer_item'

        UNION ALL

        SELECT
            a.id AS fkAgentId,
            rppsi.fk_agent_contract_account_id,
            rppsi.fk_currency_type_num AS accountCurrencyTypeNum,
            mpp.*,
            rppsi.amount_actual,
            rppsi.serviceFeeActual,
            rppsi.fk_receipt_form_item_id
        FROM
            m_agent AS a
        -- 实际支付金额
        INNER JOIN (
            SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual, MAX(rppsi1.fk_receipt_form_item_id) AS fk_receipt_form_item_id,
                MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num
            FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            WHERE  rppsi1.num_settlement_batch = #{numSettlementBatch}
            GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num
        ) AS rppsi ON rppsi.fk_agent_id_settlement = a.id
        INNER JOIN m_payable_plan AS mpp ON mpp.id = rppsi.fk_payable_plan_id
        AND mpp.fk_type_key = 'm_student_insurance'

        UNION ALL

        SELECT
            a.id AS fkAgentId,
            rppsi.fk_agent_contract_account_id,
            rppsi.fk_currency_type_num AS accountCurrencyTypeNum,
            mpp.*,
            rppsi.amount_actual,
            rppsi.serviceFeeActual,
            rppsi.fk_receipt_form_item_id
        FROM
            m_agent AS a
        -- 实际支付金额
        INNER JOIN (
            SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual, MAX(rppsi1.fk_receipt_form_item_id) AS fk_receipt_form_item_id,
                MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num
            FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            WHERE  rppsi1.num_settlement_batch = #{numSettlementBatch}
            GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num
        ) AS rppsi ON rppsi.fk_agent_id_settlement = a.id
        INNER JOIN m_payable_plan AS mpp ON mpp.id = rppsi.fk_payable_plan_id
        AND mpp.fk_type_key = 'm_student_accommodation'


        UNION ALL

        SELECT
            a.id AS fkAgentId,
            rppsi.fk_agent_contract_account_id,
            rppsi.fk_currency_type_num AS accountCurrencyTypeNum,
            mpp.*,
            rppsi.amount_actual,
            rppsi.serviceFeeActual,
            rppsi.fk_receipt_form_item_id
        FROM
            m_agent AS a
        -- 实际支付金额
        INNER JOIN (
            SELECT rppsi1.fk_payable_plan_id, MAX(rppsi1.amount_actual) AS amount_actual, MAX(rppsi1.service_fee_actual) AS serviceFeeActual, MAX(rppsi1.fk_receipt_form_item_id) AS fk_receipt_form_item_id,
                MAX(rppsi1.fk_agent_id_settlement) AS fk_agent_id_settlement, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num
            FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            WHERE  rppsi1.num_settlement_batch = #{numSettlementBatch}
            GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num
        ) AS rppsi ON rppsi.fk_agent_id_settlement = a.id
        INNER JOIN m_payable_plan AS mpp ON mpp.id = rppsi.fk_payable_plan_id
        AND mpp.fk_type_key = 'm_student_service_fee'

    </select>

    <select id="agentSettlementList" resultType="com.get.salecenter.entity.Agent">
        SELECT
        a.*,b.weights
        FROM
        m_agent AS a
        INNER JOIN
        (
        SELECT DISTINCT x.id, x.weights, MAX(x.maxTime) maxTime FROM (
        <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_offer_item'">
            SELECT
            a.id, LENGTH(CONCAT(s.first_name,s.last_name)) as weights, MAX(rppss.gmt_create) AS maxTime
            FROM
            m_agent AS a
            INNER JOIN m_student_offer_item AS msoi ON msoi.fk_agent_id = a.id
            INNER JOIN m_payable_plan AS mpp ON mpp.fk_type_target_id = msoi.id
            AND mpp.fk_type_key = 'm_student_offer_item'
            INNER JOIN m_student AS s ON s.id = msoi.fk_student_id
            <if test="agentSettlementDto.statusSettlement == 2">
                <if test="agentSettlementDto.fkCompanyId != null and agentSettlementDto.fkCompanyId !=''">
                    AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
                </if>
                <if test="agentSettlementDto.fkCompanyIds != null and agentSettlementDto.fkCompanyIds.size()>0">
                    AND s.fk_company_id in
                    <foreach collection="agentSettlementDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                        #{fkCompanyId}
                    </foreach>
                </if>
            </if>

            LEFT JOIN ais_sale_center.m_client AS mc ON mc.id = s.fk_client_id
            LEFT JOIN ais_permission_center.m_company AS cliCompany ON cliCompany.id = mc.fk_company_id
            LEFT JOIN (SELECT fk_client_id,MAX(fk_table_name) AS fk_table_name FROM ais_sale_center.s_client_source GROUP BY fk_client_id) AS scs ON scs.fk_client_id = mc.id
            LEFT JOIN ais_sale_center.u_client_source_type AS ucsot ON ucsot.type_key = scs.fk_table_name

            INNER JOIN (
            SELECT rppsi1.fk_payable_plan_id,
            CASE WHEN COUNT(rppsi1.fk_receipt_form_item_id) = COUNT(*) THEN 0 ELSE 1 END AS prepaidMark
            FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.id = rppsi1.fk_receipt_form_item_id
            LEFT JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
            LEFT JOIN (
                SELECT mi.id, MIN(mrf2.receipt_date) AS receipt_date FROM ais_finance_center.m_invoice AS mi
                INNER JOIN ais_finance_center.r_receipt_form_invoice AS rrfi ON rrfi.fk_invoice_id = mi.id
                INNER JOIN ais_finance_center.m_receipt_form AS mrf2 ON mrf2.id = rrfi.fk_receipt_form_id
                GROUP BY mi.id
            ) AS invoice ON invoice.id = rppsi1.fk_invoice_id
            <where>
                AND rppsi1.status_settlement = #{agentSettlementDto.statusSettlement}
                <if test="agentSettlementDto.receiptStartTime!=null">
                    AND (
                    DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                    OR
                    DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                    OR
                    rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                    )
                </if>
                <if test="agentSettlementDto.receiptEndTime!=null">
                    AND (
                    DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                    OR
                    DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                    OR
                    rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                    )
                </if>
            </where>
            GROUP BY rppsi1.fk_payable_plan_id
            <if test="agentSettlementDto.prepaidMark != null">
                HAVING
                <if test="agentSettlementDto.prepaidMark">
                    prepaidMark = 1
                </if>
                <if test="!agentSettlementDto.prepaidMark">
                    prepaidMark = 0
                </if>
            </if>
            ) AS installmentCreateTime ON installmentCreateTime.fk_payable_plan_id = mpp.id

            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}
            LEFT JOIN ais_finance_center.r_invoice_receivable_plan AS rirp ON rirp.id = rppsi.fk_invoice_receivable_plan_id

            WHERE rppsi.status_settlement = #{agentSettlementDto.statusSettlement}
            AND msoi.is_follow_hidden = 0
            --  佣金结算第一步，只显示enrolled状态的申请计划
        <if test="agentSettlementDto.statusSettlement == 0">
            AND (
            ( msoi.fk_student_offer_item_step_id IN (SELECT id FROM u_student_offer_item_step where step_key = #{enrolledKey} or step_key = #{stepEnrolledTbc}) )
                OR
             rppsi.amount_actual <![CDATA[< ]]> 0
                OR
             rppsi.fk_receipt_form_item_id = 0
            <if test="payInAdvanceFlag">
                OR ( mpp.is_pay_in_advance = 1 or rirp.is_pay_in_advance = 1 )
            </if>
            )
        </if>
            AND mpp.STATUS = 1 AND a.is_active = 1
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
            <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName != ''">
                AND (
                REPLACE(CONCAT(s.first_name,s.last_name),' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                OR REPLACE(s.`name`,' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                OR REPLACE(s.last_name,' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                OR REPLACE(s.first_name,' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                ) -- 过滤学生中英文名字
            </if>
            <if test="agentSettlementDto.commissionMark != null and agentSettlementDto.commissionMark != ''">
                AND LOWER(IF(ucsot.type_mark is not null, CONCAT(cliCompany.short_name, '.', ucsot.type_mark), '')) LIKE CONCAT("%",#{agentSettlementDto.commissionMark},"%") -- 过滤佣金结算标记
            </if>
            <if test="agentSettlementDto.applyStartTime!=null">
                and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.applyStartTime},
                '%Y-%m-%d' )
            </if>
            <if test="agentSettlementDto.applyEndTime!=null">
                and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.applyEndTime},
                '%Y-%m-%d' )
            </if>

            -- 开学时间
            <if test="agentSettlementDto.openingStartTime != null">
                AND  (
                DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{agentSettlementDto.openingStartTime},'%Y-%m-%d')
                )
            </if>
            <if test="agentSettlementDto.openingEndTime != null">
                AND (
                DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{agentSettlementDto.openingEndTime},'%Y-%m-%d')
                )
            </if>

        GROUP BY a.id
        </if>




<!--        <if test="agentSettlementDto.openingStartTime == null and agentSettlementDto.openingEndTime == null">-->





        <if test="(agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_insurance') and (agentSettlementDto.openingStartTime == null and agentSettlementDto.openingEndTime == null)">
            <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_offer_item'">
                UNION ALL
            </if>
            SELECT
            a.id, LENGTH(CONCAT(s.first_name,s.last_name)) as weights, MAX(rppss.gmt_create) AS maxTime
            FROM
            m_agent AS a
            INNER JOIN m_student_insurance AS msi ON msi.fk_agent_id = a.id
            INNER JOIN m_payable_plan AS mpp ON mpp.fk_type_target_id = msi.id
            AND mpp.fk_type_key = 'm_student_insurance'
            INNER JOIN m_student AS s ON s.id = msi.fk_student_id
            <if test="agentSettlementDto.statusSettlement == 2">
                <if test="agentSettlementDto.fkCompanyId != null and agentSettlementDto.fkCompanyId !=''">
                    AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
                </if>
                <if test="agentSettlementDto.fkCompanyIds != null and agentSettlementDto.fkCompanyIds.size()>0">
                    AND s.fk_company_id in
                    <foreach collection="agentSettlementDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                        #{fkCompanyId}
                    </foreach>
                </if>
            </if>
            INNER JOIN (
            SELECT rppsi1.fk_payable_plan_id,
            CASE WHEN COUNT(rppsi1.fk_receipt_form_item_id) = COUNT(*) THEN 0 ELSE 1 END AS prepaidMark
            FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.id = rppsi1.fk_receipt_form_item_id
            LEFT JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
            LEFT JOIN (
                SELECT mi.id, MIN(mrf2.receipt_date) AS receipt_date FROM ais_finance_center.m_invoice AS mi
                INNER JOIN ais_finance_center.r_receipt_form_invoice AS rrfi ON rrfi.fk_invoice_id = mi.id
                INNER JOIN ais_finance_center.m_receipt_form AS mrf2 ON mrf2.id = rrfi.fk_receipt_form_id
                GROUP BY mi.id
            ) AS invoice ON invoice.id = rppsi1.fk_invoice_id
            <where>
                AND rppsi1.status_settlement = #{agentSettlementDto.statusSettlement}
                <if test="agentSettlementDto.receiptStartTime!=null">
                    AND (
                    DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                    OR
                    DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                    OR
                    rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                    )
                </if>
                <if test="agentSettlementDto.receiptEndTime!=null">
                    AND (
                    DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                    OR
                    DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                    OR
                    rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                    )
                </if>
            </where>
            GROUP BY rppsi1.fk_payable_plan_id
            <if test="agentSettlementDto.prepaidMark != null">
                HAVING
                <if test="agentSettlementDto.prepaidMark">
                    prepaidMark = 1
                </if>
                <if test="!agentSettlementDto.prepaidMark">
                    prepaidMark = 0
                </if>
            </if>
            ) AS installmentCreateTime ON installmentCreateTime.fk_payable_plan_id = mpp.id

            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}

            WHERE rppsi.status_settlement = #{agentSettlementDto.statusSettlement}
            AND mpp.STATUS = 1
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
            <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName !=''">
                AND (
                REPLACE(CONCAT(s.first_name,s.last_name),' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                OR s.`name` like concat('%',#{agentSettlementDto.studentName},'%')
                OR s.last_name like concat('%',#{agentSettlementDto.studentName},'%')
                OR s.first_name like concat('%',#{agentSettlementDto.studentName},'%')
                OR msi.`insurant_name` like concat('%',#{agentSettlementDto.studentName},'%')
                OR msi.insurant_last_name like concat('%',#{agentSettlementDto.studentName},'%')
                OR msi.insurant_first_name like concat('%',#{agentSettlementDto.studentName},'%')
                )
            </if>
            GROUP BY a.id

        </if>
        <if test="(agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_accommodation') and (agentSettlementDto.openingStartTime == null and agentSettlementDto.openingEndTime == null)">
            <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_insurance'">
                UNION ALL
            </if>
            SELECT
            a.id, LENGTH(CONCAT(s.first_name,s.last_name)) as weights, MAX(rppss.gmt_create) AS maxTime
            FROM
            m_agent AS a
            INNER JOIN m_student_accommodation AS msa ON msa.fk_agent_id = a.id
            INNER JOIN m_payable_plan AS mpp ON mpp.fk_type_target_id = msa.id
            AND mpp.fk_type_key = 'm_student_accommodation'
            INNER JOIN m_student AS s ON s.id = msa.fk_student_id
            <if test="agentSettlementDto.statusSettlement == 2">
                <if test="agentSettlementDto.fkCompanyId != null and agentSettlementDto.fkCompanyId !=''">
                    AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
                </if>
                <if test="agentSettlementDto.fkCompanyIds != null and agentSettlementDto.fkCompanyIds.size()>0">
                    AND s.fk_company_id in
                    <foreach collection="agentSettlementDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                        #{fkCompanyId}
                    </foreach>
                </if>
            </if>
            INNER JOIN (
            SELECT rppsi1.fk_payable_plan_id,
            CASE WHEN COUNT(rppsi1.fk_receipt_form_item_id) = COUNT(*) THEN 0 ELSE 1 END AS prepaidMark
            FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.id = rppsi1.fk_receipt_form_item_id
            LEFT JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
            LEFT JOIN (
                SELECT mi.id, MIN(mrf2.receipt_date) AS receipt_date FROM ais_finance_center.m_invoice AS mi
                INNER JOIN ais_finance_center.r_receipt_form_invoice AS rrfi ON rrfi.fk_invoice_id = mi.id
                INNER JOIN ais_finance_center.m_receipt_form AS mrf2 ON mrf2.id = rrfi.fk_receipt_form_id
                GROUP BY mi.id
            ) AS invoice ON invoice.id = rppsi1.fk_invoice_id
            <where>
                AND rppsi1.status_settlement = #{agentSettlementDto.statusSettlement}
                <if test="agentSettlementDto.receiptStartTime!=null">
                    AND (
                    DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                    OR
                    DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                    OR
                    rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                    )
                </if>
                <if test="agentSettlementDto.receiptEndTime!=null">
                    AND (
                    DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                    OR
                    DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                    OR
                    rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                    )
                </if>
            </where>
            GROUP BY rppsi1.fk_payable_plan_id
            <if test="agentSettlementDto.prepaidMark != null">
                HAVING
                <if test="agentSettlementDto.prepaidMark">
                    prepaidMark = 1
                </if>
                <if test="!agentSettlementDto.prepaidMark">
                    prepaidMark = 0
                </if>
            </if>
            ) AS installmentCreateTime ON installmentCreateTime.fk_payable_plan_id = mpp.id

            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}

            WHERE rppsi.status_settlement = #{agentSettlementDto.statusSettlement}
            AND mpp.STATUS = 1
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
            <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName !=''">
                AND
                (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                OR s.`name` like concat('%',#{agentSettlementDto.studentName},'%')
                OR s.last_name like concat('%',#{agentSettlementDto.studentName},'%')
                OR s.first_name like concat('%',#{agentSettlementDto.studentName},'%'))
            </if>
            GROUP BY a.id
        </if>
        <if test="(agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_service_fee') and (agentSettlementDto.openingStartTime == null and agentSettlementDto.openingEndTime == null)">
            <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_insurance'">
                UNION ALL
            </if>
            SELECT
            a.id, LENGTH(CONCAT(s.first_name,s.last_name)) as weights, MAX(rppss.gmt_create) AS maxTime
            FROM
            m_agent AS a
            INNER JOIN m_student_service_fee AS mssf ON mssf.fk_agent_id = a.id
            INNER JOIN m_payable_plan AS mpp ON mpp.fk_type_target_id = mssf.id
            AND mpp.fk_type_key = 'm_student_service_fee'
            INNER JOIN m_student AS s ON s.id = mssf.fk_student_id
            <if test="agentSettlementDto.statusSettlement == 2">
                <if test="agentSettlementDto.fkCompanyId != null and agentSettlementDto.fkCompanyId !=''">
                    AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
                </if>
                <if test="agentSettlementDto.fkCompanyIds != null and agentSettlementDto.fkCompanyIds.size()>0">
                    AND s.fk_company_id in
                    <foreach collection="agentSettlementDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                        #{fkCompanyId}
                    </foreach>
                </if>
            </if>
            INNER JOIN (
            SELECT rppsi1.fk_payable_plan_id,
            CASE WHEN COUNT(rppsi1.fk_receipt_form_item_id) = COUNT(*) THEN 0 ELSE 1 END AS prepaidMark
            FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.id = rppsi1.fk_receipt_form_item_id
            LEFT JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
            LEFT JOIN (
                SELECT mi.id, MIN(mrf2.receipt_date) AS receipt_date FROM ais_finance_center.m_invoice AS mi
                INNER JOIN ais_finance_center.r_receipt_form_invoice AS rrfi ON rrfi.fk_invoice_id = mi.id
                INNER JOIN ais_finance_center.m_receipt_form AS mrf2 ON mrf2.id = rrfi.fk_receipt_form_id
                GROUP BY mi.id
            ) AS invoice ON invoice.id = rppsi1.fk_invoice_id
            <where>
                AND rppsi1.status_settlement = #{agentSettlementDto.statusSettlement}
                <if test="agentSettlementDto.receiptStartTime!=null">
                    AND (
                    DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                    OR
                    DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                    OR
                    rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                    )
                </if>
                <if test="agentSettlementDto.receiptEndTime!=null">
                    AND (
                    DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                    OR
                    DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                    OR
                    rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                    )
                </if>
            </where>
            GROUP BY rppsi1.fk_payable_plan_id
            <if test="agentSettlementDto.prepaidMark != null">
                HAVING
                <if test="agentSettlementDto.prepaidMark">
                    prepaidMark = 1
                </if>
                <if test="!agentSettlementDto.prepaidMark">
                    prepaidMark = 0
                </if>
            </if>
            ) AS installmentCreateTime ON installmentCreateTime.fk_payable_plan_id = mpp.id

            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}

            WHERE rppsi.status_settlement = #{agentSettlementDto.statusSettlement}
            AND mpp.STATUS = 1
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
            <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName !=''">
                AND
                (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like concat('%',#{agentSettlementDto.studentName},'%')
                OR s.`name` like concat('%',#{agentSettlementDto.studentName},'%')
                OR s.last_name like concat('%',#{agentSettlementDto.studentName},'%')
                OR s.first_name like concat('%',#{agentSettlementDto.studentName},'%'))
            </if>
            GROUP BY a.id

        </if>

        )x
        GROUP BY x.id
        )b ON b.id = a.id
        LEFT JOIN ais_sale_center.r_agent_staff AS ras ON ras.fk_agent_id = a.id AND ras.is_active = 1
        LEFT JOIN ais_permission_center.m_staff AS ms ON ms.id = ras.fk_staff_id
        LEFT JOIN ais_sale_center.r_staff_bd_code AS rsbc ON rsbc.fk_staff_id = ms.id
        <if test="agentSettlementDto.statusSettlement != 2">
        -- 代理权限
        INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.agentPermissionSql"/>
        ) z ON a.id=z.id
        </if>
        <where>
            <if test="agentSettlementDto.agentNameOrNum != null and  agentSettlementDto.agentNameOrNum !=''">
                and (a.num =#{agentSettlementDto.agentNameOrNum} OR a.name LIKE CONCAT('%', #{agentSettlementDto.agentNameOrNum}, '%'))
            </if>
            <if test="agentSettlementDto.agentAreaStateId != null and  agentSettlementDto.agentAreaStateId !=''">
                and a.fk_area_state_id =#{agentSettlementDto.agentAreaStateId}
            </if>
            <if test="agentSettlementDto.bdNameOrCode != null and  agentSettlementDto.bdNameOrCode !=''">
                and (rsbc.bd_code LIKE CONCAT('%', #{agentSettlementDto.bdNameOrCode}, '%') OR ms.name LIKE CONCAT('%', #{agentSettlementDto.bdNameOrCode}, '%') OR ms.name_en LIKE CONCAT('%', #{agentSettlementDto.bdNameOrCode}, '%') )
            </if>
            <if test="agentSettlementDto.fkAreaRegionId != null and  agentSettlementDto.fkAreaRegionId !=''">
                and FIND_IN_SET(#{agentSettlementDto.fkAreaRegionId}, rsbc.fk_area_region_id)
            </if>
            <if test="agentSettlementDto.stepSubmissionStartTime!=null">
                and DATE_FORMAT( b.maxTime, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionStartTime}, '%Y-%m-%d' )
            </if>
            <if test="agentSettlementDto.stepSubmissionEndTime!=null">
                and DATE_FORMAT( b.maxTime, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionEndTime},
                '%Y-%m-%d' )
            </if>
            <if test="agentSettlementDto.settlementFlag != null">
                <if test="agentSettlementDto.settlementFlag">
                    AND EXISTS
                </if>
                <if test="!agentSettlementDto.settlementFlag">
                    AND NOT EXISTS
                </if>
                ( SELECT 1 FROM ais_finance_center.r_payable_plan_settlement_flag AS rppsf WHERE rppsf.status_settlement = 1 and rppsf.fk_agent_id = a.id )
            </if>
        </where>
        GROUP BY b.id
        order by b.maxTime desc
    </select>

    <select id="agentConfirmationList" resultType="com.get.salecenter.entity.Agent">
        <!--
                SELECT
                a.*
                FROM
                m_agent AS a
                INNER JOIN
                (
                SELECT DISTINCT x.id FROM (
                <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_offer_item'">
                    SELECT
                    a.id
                    FROM
                    m_agent AS a
                    - 代理结算关系表为基础数据源
                    INNER JOIN r_payable_plan_settlement_agent AS rppsa ON rppsa.fk_agent_id = a.id
                    INNER JOIN m_payable_plan AS mpp ON mpp.id = rppsa.fk_payable_plan_id AND mpp.fk_type_key = 'm_student_offer_item'
                    INNER JOIN m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id
                    INNER JOIN m_student_offer AS mso ON mso.id = msoi.fk_student_offer_id
                    INNER JOIN m_student AS s ON s.id = mso.fk_student_id AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
                    - 只有有收款记录的学习计划才能显示
                    INNER JOIN (
                    SELECT DISTINCT msoi2.id FROM m_student_offer_item AS msoi2
                    INNER JOIN m_receivable_plan AS mrp ON mrp.fk_type_target_id = msoi2.id AND mrp.fk_type_key =
                    'm_student_offer_item'
                    INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
                    )z ON z.id = msoi.id
                    WHERE
                    a.is_active = 1
                    AND a.is_settlement_port =1
                    AND mso.STATUS = 1
                    AND mso.status_workflow IN ( 0, 3, 4 )
                    AND msoi.STATUS = 1
                    AND mpp.STATUS = 1
                    AND mpp.status_settlement IN
                    <foreach collection="statusSettlementList" item="statusSettlement" index="index" open="(" separator="," close=")">
                        #{statusSettlement}
                    </foreach>
                    <if test="subordinateAgentIds != null and subordinateAgentIds.size()>0">
                        AND a.id IN
                        <foreach collection="subordinateAgentIds" item="agentId" index="index" open="(" separator="," close=")">
                            #{agentId}
                        </foreach>
                    </if>
                    <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName != ''">
                        AND s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')
                    </if>
                    <if test="agentSettlementDto.applyStartTime!=null">
                        and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.applyStartTime},
                        '%Y-%m-%d' )
                    </if>
                    <if test="agentSettlementDto.applyEndTime!=null">
                        and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.applyEndTime},
                        '%Y-%m-%d' )
                    </if>
                    <if test="agentSettlementDto.openingStartTime!=null">
                        and DATE_FORMAT( msoi.opening_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.openingStartTime},
                        '%Y-%m-%d' )
                    </if>
                    <if test="agentSettlementDto.openingEndTime!=null">
                        and DATE_FORMAT( msoi.opening_time, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.openingEndTime},
                        '%Y-%m-%d' )
                    </if>
                    <if test="agentSettlementDto.negativeFlag">
                        AND mpp.payable_amount <![CDATA[< ]]> 0
                    </if>
                    GROUP BY a.id
                </if>
                <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_insurance'">
                    <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_offer_item'">
                        UNION ALL
                    </if>
                    SELECT
                    a.id
                    FROM
                    m_agent AS a
                    - 代理结算关系表为基础数据源
                    INNER JOIN r_payable_plan_settlement_agent AS rppsa ON rppsa.fk_agent_id = a.id
                    INNER JOIN m_payable_plan AS mpp ON mpp.id = rppsa.fk_payable_plan_id AND mpp.fk_type_key = 'm_student_insurance'
                    INNER JOIN m_student_insurance AS msi ON msi.id = mpp.fk_type_target_id
                    INNER JOIN m_student AS s ON s.id = msi.fk_student_id AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
                    - 只有有收款记录的才能显示
                    INNER JOIN (
                    SELECT DISTINCT msi2.id FROM m_student_insurance AS msi2
                    INNER JOIN m_receivable_plan AS mrp ON mrp.fk_type_target_id = msi2.id AND mrp.fk_type_key =
                    'm_student_insurance'
                    INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
                    )z ON z.id = msi.id
                    WHERE
                    a.is_active = 1
                    AND a.is_settlement_port =1
                    AND mpp.STATUS = 1
                    AND msi.STATUS = 1
                    AND mpp.status_settlement IN
                    <foreach collection="statusSettlementList" item="statusSettlement" index="index" open="(" separator="," close=")">
                        #{statusSettlement}
                    </foreach>
                    <if test="subordinateAgentIds != null and subordinateAgentIds.size()>0">
                        AND a.id IN
                        <foreach collection="subordinateAgentIds" item="agentId" index="index" open="(" separator="," close=")">
                            #{agentId}
                        </foreach>
                    </if>
                    <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName !=''">
                        AND s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')
                    </if>
                    <if test="agentSettlementDto.negativeFlag">
                        AND mpp.payable_amount <![CDATA[< ]]> 0
                    </if>
                    GROUP BY a.id

                </if>
                <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_accommodation'">
                    <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_insurance'">
                        UNION ALL
                    </if>
                    SELECT
                    a.id
                    FROM
                    m_agent AS a
                    - 代理结算关系表为基础数据源
                    INNER JOIN r_payable_plan_settlement_agent AS rppsa ON rppsa.fk_agent_id = a.id
                    INNER JOIN m_payable_plan AS mpp ON mpp.id = rppsa.fk_payable_plan_id AND mpp.fk_type_key = 'm_student_accommodation'
                    INNER JOIN m_student_accommodation AS msa ON msa.id = mpp.fk_type_target_id
                    INNER JOIN m_student AS s ON s.id = msa.fk_student_id AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
                    - 只有有收款记录的才能显示
                    INNER JOIN (
                    SELECT DISTINCT msa2.id FROM m_student_accommodation AS msa2
                    INNER JOIN m_receivable_plan AS mrp ON mrp.fk_type_target_id = msa2.id AND mrp.fk_type_key =
                    'm_student_accommodation'
                    INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
                    )z ON z.id = msa.id
                    WHERE
                    a.is_active = 1
                    AND a.is_settlement_port =1
                    AND mpp.STATUS = 1
                    AND msa.STATUS = 1
                    AND mpp.status_settlement IN
                    <foreach collection="statusSettlementList" item="statusSettlement" index="index" open="(" separator="," close=")">
                        #{statusSettlement}
                    </foreach>
                    <if test="subordinateAgentIds != null and subordinateAgentIds.size()>0">
                        AND a.id IN
                        <foreach collection="subordinateAgentIds" item="agentId" index="index" open="(" separator="," close=")">
                            #{agentId}
                        </foreach>
                    </if>
                    <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName !=''">
                        AND s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')
                    </if>
                    <if test="agentSettlementDto.negativeFlag">
                        AND mpp.payable_amount <![CDATA[< ]]> 0
                    </if>
                    GROUP BY a.id
                </if>
                )x
                )b ON b.id = a.id
                <where>
                    <if test="agentSettlementDto.agentNameOrNum != null and  agentSettlementDto.agentNameOrNum !=''">
                        and (a.num =#{agentSettlementDto.agentNameOrNum} OR a.name LIKE CONCAT('%', #{agentSettlementDto.agentNameOrNum}, '%'))
                    </if>
                </where>
                GROUP BY b.id
                ORDER BY a.name
                -->
    </select>


    <select id="getAgentPayablePlanCountByAgentIds" resultType="java.lang.Integer">
        SELECT
        SUM(IFNULL(b.planCount,0))
        FROM
        (
        <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_offer_item'">
        SELECT COUNT(DISTINCT c.id) AS planCount FROM (
            SELECT
            mpp.id, MAX(rppss.gmt_create) AS maxTime
            FROM
            m_agent AS a
            INNER JOIN m_student_offer_item AS msoi ON msoi.fk_agent_id = a.id
            INNER JOIN m_payable_plan AS mpp ON mpp.fk_type_target_id = msoi.id
            AND mpp.fk_type_key = 'm_student_offer_item'
            INNER JOIN m_student AS s ON s.id = msoi.fk_student_id
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}
            LEFT JOIN ais_finance_center.r_invoice_receivable_plan AS rirp ON rirp.id = rppsi.fk_invoice_receivable_plan_id

            LEFT JOIN ais_sale_center.m_client AS mc ON mc.id = s.fk_client_id
            LEFT JOIN ais_permission_center.m_company AS cliCompany ON cliCompany.id = mc.fk_company_id
            LEFT JOIN (SELECT fk_client_id,MAX(fk_table_name) AS fk_table_name FROM ais_sale_center.s_client_source GROUP BY fk_client_id) AS scs ON scs.fk_client_id = mc.id
            LEFT JOIN ais_sale_center.u_client_source_type AS ucsot ON ucsot.type_key = scs.fk_table_name

            -- 实际支付金额
            INNER JOIN (
            SELECT rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id, rppsi1.fk_currency_type_num, CASE WHEN rppsi1.status = 0 THEN SUM(rppsi1.amount_actual) ELSE MAX(rppsi1.amount_actual) END amount_actual,
            CASE WHEN rppsi1.status = 0 THEN SUM(rppsi1.service_fee_actual) ELSE MAX(rppsi1.service_fee_actual) END serviceFeeActual, MAX( CASE WHEN rppsi1.fk_receipt_form_item_id IS NULL OR  rppsi1.fk_receipt_form_item_id = 0 THEN rppsi1.gmt_create ELSE NULL END ) AS settlementCreateTime,
            CASE WHEN COUNT(fk_receipt_form_item_id) = COUNT(*) THEN 0 ELSE 1 END AS prepaidMark,
            MAX(rppsi1.account_export_time) AS account_export_time, MIN(rppsi1.is_roll_back) AS is_roll_back,
            COALESCE(
            CASE
            WHEN IFNULL(mrf1.receipt_date, CAST('9999-01-01 00:00:00' AS DATETIME)) <![CDATA[ <  ]]> IFNULL(invoice.receipt_date, CAST('9999-01-01 00:00:00' AS DATETIME)) THEN mrf1.receipt_date
            ELSE invoice.receipt_date
            END
            ) AS installmentCreateTime,
            SUM( IFNULL(mrfi1.amount_receivable, 0) ) AS amount_receivable, MAX(rppsi1.fk_receipt_form_item_id) AS maxFkReceiptFormItemId, GROUP_CONCAT(rppsi1.id) AS settlementIds, IFNULL(MAX(rirp.is_pay_in_advance), 0) AS is_pay_in_advance
            FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi1
            LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.id = rppsi1.fk_receipt_form_item_id
            LEFT JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
            LEFT JOIN ais_finance_center.r_invoice_receivable_plan AS rirp ON rirp.id = rppsi1.fk_invoice_receivable_plan_id
            LEFT JOIN (
            SELECT mi.id, MIN(mrf2.receipt_date) AS receipt_date FROM ais_finance_center.m_invoice AS mi
            INNER JOIN ais_finance_center.r_receipt_form_invoice AS rrfi ON rrfi.fk_invoice_id = mi.id
            INNER JOIN ais_finance_center.m_receipt_form AS mrf2 ON mrf2.id = rrfi.fk_receipt_form_id
            GROUP BY mi.id
            ) AS invoice ON invoice.id = rppsi1.fk_invoice_id
            <where>
                AND rppsi1.status_settlement = #{agentSettlementDto.statusSettlement}
                <if test="agentSettlementDto.receiptStartTime!=null">
                    AND (
                    DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                    OR
                    DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                    OR
                    rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                    )
                </if>
                <if test="agentSettlementDto.receiptEndTime!=null">
                    AND (
                    DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                    OR
                    DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                    OR
                    rppsi1.fk_receipt_form_item_id = 0 OR ( rppsi1.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                    )
                </if>
            </where>
            GROUP BY rppsi1.fk_payable_plan_id, rppsi1.fk_agent_contract_account_id
            <if test="agentSettlementDto.prepaidMark != null">
                HAVING
                <if test="agentSettlementDto.prepaidMark">
                    prepaidMark = 1
                </if>
                <if test="!agentSettlementDto.prepaidMark">
                    prepaidMark = 0
                </if>
            </if>
            ) AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp.id

            WHERE mpp.STATUS = 1
            AND msoi.is_follow_hidden = 0
            <if test="agentSettlementDto.studentName != null and  agentSettlementDto.studentName !=''">
                AND
                (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.`name`,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.last_name,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ','')
                OR REPLACE(s.first_name,' ','') like REPLACE(concat('%',#{agentSettlementDto.studentName},'%'),' ',''))
            </if>
            <if test="agentSettlementDto.statusSettlement == 2">
            <if test="agentSettlementDto.fkCompanyId != null and agentSettlementDto.fkCompanyId !=''">
                AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
            </if>
            <if test="agentSettlementDto.fkCompanyIds != null and agentSettlementDto.fkCompanyIds.size()>0">
                AND s.fk_company_id in
                <foreach collection="agentSettlementDto.fkCompanyIds" item="fkCompanyId" index="index" open="("
                         separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
            </if>

            <if test="agentSettlementDto.commissionMark != null and agentSettlementDto.commissionMark != ''">
                AND LOWER(IF(ucsot.type_mark is not null, CONCAT(cliCompany.short_name, '.', ucsot.type_mark), '')) LIKE CONCAT("%",#{agentSettlementDto.commissionMark},"%") -- 过滤佣金结算标记
            </if>

            <if test="agentSettlementDto.openingStartTime != null">
                AND  (
                DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{agentSettlementDto.openingStartTime},'%Y-%m-%d')
                )
            </if>
            <if test="agentSettlementDto.openingEndTime != null">
                AND (
                DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{agentSettlementDto.openingEndTime},'%Y-%m-%d')
                )
            </if>
            AND a.id = #{subordinateAgentId}
            AND rppsi.status_settlement = #{agentSettlementDto.statusSettlement}
            --  佣金结算第一步，只显示enrolled状态的申请计划
            <if test="agentSettlementDto.statusSettlement == 0">
                AND (
                ( msoi.fk_student_offer_item_step_id IN (SELECT id FROM u_student_offer_item_step where step_key = #{enrolledKey} or step_key = #{stepEnrolledTbc}) )
                    OR
                rppsi.amount_actual <![CDATA[< ]]> 0
                    OR
                rppsi.fk_receipt_form_item_id = 0
                <if test="payInAdvanceFlag">
                    OR ( mpp.is_pay_in_advance = 1 or rirp.is_pay_in_advance = 1 )
                </if>
                )
            </if>
            <if test="agentSettlementDto.settlementFlag != null">
                <if test="agentSettlementDto.settlementFlag">
                    AND EXISTS
                </if>
                <if test="!agentSettlementDto.settlementFlag">
                    AND NOT EXISTS
                </if>
                ( SELECT 1 FROM ais_finance_center.r_payable_plan_settlement_flag AS rppsf WHERE rppsf.status_settlement = 1 and rppsf.fk_agent_id = a.id )
            </if>
            <!--            <if test="agentSettlementDto.statusSettlement != null">-->
            <!--                <if test="agentSettlementDto.statusSettlement != 2">-->
            <!--                    AND mpp.status_settlement = #{agentSettlementDto.statusSettlement}-->
            <!--                </if>-->
            <!--                <if test="agentSettlementDto.statusSettlement == 2">-->
            <!--                    AND (mpp.status_settlement = #{agentSettlementDto.statusSettlement} OR (mpp.status_settlement IN (0,1) AND mpp.payable_amount <![CDATA[< ]]> 0))-->
            <!--                </if>-->
            <!--            </if>-->
            <!--            <if test="subordinateAgentIds != null and subordinateAgentIds.size()>0">-->
            <!--                AND a.id IN-->
            <!--                <foreach collection="subordinateAgentIds" item="agentId" index="index" open="(" separator="," close=")">-->
            <!--                    #{agentId}-->
            <!--                </foreach>-->
            <!--            </if>-->
            <if test="agentSettlementDto.applyStartTime!=null">
                and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.applyStartTime},
                '%Y-%m-%d' )
            </if>
            <if test="agentSettlementDto.applyEndTime!=null">
                and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.applyEndTime},
                '%Y-%m-%d' )
            </if>
<!--            <if test="agentSettlementDto.openingStartTime!=null">-->
<!--                and DATE_FORMAT( msoi.opening_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.openingStartTime},-->
<!--                '%Y-%m-%d' )-->
<!--            </if>-->
<!--            <if test="agentSettlementDto.openingEndTime!=null">-->
<!--                and DATE_FORMAT( msoi.opening_time, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.openingEndTime},-->
<!--                '%Y-%m-%d' )-->
<!--            </if>-->
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
            GROUP BY mpp.id
        <if test="agentSettlementDto.stepSubmissionStartTime!=null or agentSettlementDto.stepSubmissionEndTime!=null">
            HAVING 1=1
            <if test="agentSettlementDto.stepSubmissionStartTime!=null">
                AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionStartTime}, '%Y-%m-%d' )
            </if>
            <if test="agentSettlementDto.stepSubmissionEndTime!=null">
                AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionEndTime},
                '%Y-%m-%d' )
            </if>
        </if>
        )c
        </if>
        <if test="(agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_insurance') and (agentSettlementDto.openingStartTime == null and agentSettlementDto.openingEndTime == null)">
            <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_offer_item'">
                UNION ALL
            </if>
        SELECT COUNT(DISTINCT c.id) AS planCount FROM (
            SELECT
            mpp.id, MAX(rppss.gmt_create) AS maxTime
            FROM
            m_agent AS a
            INNER JOIN m_student_insurance AS msi ON msi.fk_agent_id = a.id
            INNER JOIN m_payable_plan AS mpp ON mpp.fk_type_target_id = msi.id
            AND mpp.fk_type_key = 'm_student_insurance'
            INNER JOIN m_student AS s ON s.id = msi.fk_student_id
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}
            LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.id = rppsi.fk_receipt_form_item_id
            LEFT JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
            LEFT JOIN ais_finance_center.r_invoice_receivable_plan AS rirp ON rirp.id = rppsi.fk_invoice_receivable_plan_id
            LEFT JOIN (
            SELECT mi.id, MIN(mrf2.receipt_date) AS receipt_date FROM ais_finance_center.m_invoice AS mi
            INNER JOIN ais_finance_center.r_receipt_form_invoice AS rrfi ON rrfi.fk_invoice_id = mi.id
            INNER JOIN ais_finance_center.m_receipt_form AS mrf2 ON mrf2.id = rrfi.fk_receipt_form_id
            GROUP BY mi.id
            ) AS invoice ON invoice.id = rppsi.fk_invoice_id
            WHERE mpp.STATUS = 1
            AND a.id = #{subordinateAgentId}
            AND rppsi.status_settlement = #{agentSettlementDto.statusSettlement}
            <!--            <if test="agentSettlementDto.statusSettlement != null">-->
            <!--                <if test="agentSettlementDto.statusSettlement != 2">-->
            <!--                    AND mpp.status_settlement = #{agentSettlementDto.statusSettlement}-->
            <!--                </if>-->
            <!--                <if test="agentSettlementDto.statusSettlement == 2">-->
            <!--                    AND (mpp.status_settlement = #{agentSettlementDto.statusSettlement} OR (mpp.status_settlement IN (0,1) AND mpp.payable_amount <![CDATA[< ]]> 0))-->
            <!--                </if>-->
            <!--            </if>-->

            <!--            <if test="subordinateAgentIds != null and subordinateAgentIds.size()>0">-->
            <!--                AND a.id IN-->
            <!--                <foreach collection="subordinateAgentIds" item="agentId" index="index" open="(" separator="," close=")">-->
            <!--                    #{agentId}-->
            <!--                </foreach>-->
            <!--            </if>-->
            <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName !=''">
                AND (s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')
                OR msi.`insurant_name` like concat('%',#{agentSettlementDto.studentName},'%')
                OR msi.insurant_last_name like concat('%',#{agentSettlementDto.studentName},'%')
                OR msi.insurant_first_name like concat('%',#{agentSettlementDto.studentName},'%'))
            </if>
            <if test="agentSettlementDto.receiptStartTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                rppsi.fk_receipt_form_item_id = 0 OR ( rppsi.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
            <if test="agentSettlementDto.receiptEndTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                rppsi.fk_receipt_form_item_id = 0 OR ( rppsi.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
            GROUP BY mpp.id
            <if test="agentSettlementDto.stepSubmissionStartTime!=null or agentSettlementDto.stepSubmissionEndTime!=null">
                HAVING 1=1
                <if test="agentSettlementDto.stepSubmissionStartTime!=null">
                    AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionStartTime}, '%Y-%m-%d' )
                </if>
                <if test="agentSettlementDto.stepSubmissionEndTime!=null">
                    AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionEndTime},
                    '%Y-%m-%d' )
                </if>
            </if>
        )c
        </if>
        <if test="(agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_accommodation') and (agentSettlementDto.openingStartTime == null and agentSettlementDto.openingEndTime == null)">
            <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_insurance'">
                UNION ALL
            </if>
        SELECT COUNT(DISTINCT c.id) AS planCount FROM (
            SELECT
            mpp.id, MAX(rppss.gmt_create) AS maxTime
            FROM
            m_agent AS a
            INNER JOIN m_student_accommodation AS msa ON msa.fk_agent_id = a.id
            INNER JOIN m_payable_plan AS mpp ON mpp.fk_type_target_id = msa.id
            AND mpp.fk_type_key = 'm_student_accommodation'
            INNER JOIN m_student AS s ON s.id = msa.fk_student_id
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}
            LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.id = rppsi.fk_receipt_form_item_id
            LEFT JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
            LEFT JOIN ais_finance_center.r_invoice_receivable_plan AS rirp ON rirp.id = rppsi.fk_invoice_receivable_plan_id
            LEFT JOIN (
            SELECT mi.id, MIN(mrf2.receipt_date) AS receipt_date FROM ais_finance_center.m_invoice AS mi
            INNER JOIN ais_finance_center.r_receipt_form_invoice AS rrfi ON rrfi.fk_invoice_id = mi.id
            INNER JOIN ais_finance_center.m_receipt_form AS mrf2 ON mrf2.id = rrfi.fk_receipt_form_id
            GROUP BY mi.id
            ) AS invoice ON invoice.id = rppsi.fk_invoice_id
            WHERE mpp.STATUS = 1
            AND a.id = #{subordinateAgentId}
            AND rppsi.status_settlement = #{agentSettlementDto.statusSettlement}
            <!--            <if test="agentSettlementDto.statusSettlement != null">-->
            <!--                <if test="agentSettlementDto.statusSettlement != 2">-->
            <!--                    AND mpp.status_settlement = #{agentSettlementDto.statusSettlement}-->
            <!--                </if>-->
            <!--                <if test="agentSettlementDto.statusSettlement == 2">-->
            <!--                    AND (mpp.status_settlement = #{agentSettlementDto.statusSettlement} OR (mpp.status_settlement IN (0,1) AND mpp.payable_amount <![CDATA[< ]]> 0))-->
            <!--                </if>-->
            <!--            </if>-->

            <!--            <if test="subordinateAgentIds != null and subordinateAgentIds.size()>0">-->
            <!--                AND a.id IN-->
            <!--                <foreach collection="subordinateAgentIds" item="agentId" index="index" open="(" separator="," close=")">-->
            <!--                    #{agentId}-->
            <!--                </foreach>-->
            <!--            </if>-->
            <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName !=''">
                AND s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')
            </if>
            <if test="agentSettlementDto.receiptStartTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                rppsi.fk_receipt_form_item_id = 0 OR ( rppsi.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
            <if test="agentSettlementDto.receiptEndTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                rppsi.fk_receipt_form_item_id = 0 OR ( rppsi.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
            GROUP BY mpp.id
            <if test="agentSettlementDto.stepSubmissionStartTime!=null or agentSettlementDto.stepSubmissionEndTime!=null">
                HAVING 1=1
                <if test="agentSettlementDto.stepSubmissionStartTime!=null">
                    AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionStartTime}, '%Y-%m-%d' )
                </if>
                <if test="agentSettlementDto.stepSubmissionEndTime!=null">
                    AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionEndTime},
                    '%Y-%m-%d' )
                </if>
            </if>
        )c
        </if>
        <if test="(agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_service_fee') and (agentSettlementDto.openingStartTime == null and agentSettlementDto.openingEndTime == null)">
            <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_accommodation'">
                UNION ALL
            </if>
        SELECT COUNT(DISTINCT c.id) AS planCount FROM (
            SELECT
            mpp.id, MAX(rppss.gmt_create) AS maxTime
            FROM
            m_agent AS a
            INNER JOIN m_student_service_fee AS mssf ON mssf.fk_agent_id = a.id
            INNER JOIN m_payable_plan AS mpp ON mpp.fk_type_target_id = mssf.id
            AND mpp.fk_type_key = 'm_student_service_fee'
            INNER JOIN m_student AS s ON s.id = mssf.fk_student_id
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_status AS rppss ON rppss.fk_payable_plan_id = rppsi.fk_payable_plan_id AND rppss.status_settlement = #{agentSettlementDto.statusSettlement}
            LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi1 ON mrfi1.id = rppsi.fk_receipt_form_item_id
            LEFT JOIN ais_finance_center.m_receipt_form AS mrf1 ON mrf1.id = mrfi1.fk_receipt_form_id
            LEFT JOIN ais_finance_center.r_invoice_receivable_plan AS rirp ON rirp.id = rppsi.fk_invoice_receivable_plan_id
            LEFT JOIN (
            SELECT mi.id, MIN(mrf2.receipt_date) AS receipt_date FROM ais_finance_center.m_invoice AS mi
            INNER JOIN ais_finance_center.r_receipt_form_invoice AS rrfi ON rrfi.fk_invoice_id = mi.id
            INNER JOIN ais_finance_center.m_receipt_form AS mrf2 ON mrf2.id = rrfi.fk_receipt_form_id
            GROUP BY mi.id
            ) AS invoice ON invoice.id = rppsi.fk_invoice_id
            WHERE mpp.STATUS = 1
            AND a.id = #{subordinateAgentId}
            AND rppsi.status_settlement = #{agentSettlementDto.statusSettlement}
            <if test="agentSettlementDto.receiptStartTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.receiptStartTime}, '%Y-%m-%d' )
                OR
                rppsi.fk_receipt_form_item_id = 0 OR ( rppsi.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
            <if test="agentSettlementDto.receiptEndTime!=null">
                AND (
                DATE_FORMAT( mrf1.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                DATE_FORMAT( invoice.receipt_date, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.receiptEndTime}, '%Y-%m-%d' )
                OR
                rppsi.fk_receipt_form_item_id = 0 OR ( rppsi.fk_receipt_form_item_id IS NULL AND invoice.receipt_date IS NULL)
                )
            </if>
            <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName !=''">
                AND s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')
            </if>
            <if test="agentSettlementDto.negativeFlag != null">
                <if test="agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[< ]]> 0
                </if>
                <if test="!agentSettlementDto.negativeFlag">
                    AND mpp.payable_amount <![CDATA[>= ]]> 0
                </if>
            </if>
            GROUP BY mpp.id
            <if test="agentSettlementDto.stepSubmissionStartTime!=null or agentSettlementDto.stepSubmissionEndTime!=null">
                HAVING 1=1
                <if test="agentSettlementDto.stepSubmissionStartTime!=null">
                    AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionStartTime}, '%Y-%m-%d' )
                </if>
                <if test="agentSettlementDto.stepSubmissionEndTime!=null">
                    AND DATE_FORMAT( maxTime, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.stepSubmissionEndTime},
                    '%Y-%m-%d' )
                </if>
            </if>
        )c
        </if>
        )b
    </select>

    <select id="getAgentConfirmationPayablePlanCountByAgentIds" resultType="java.lang.Integer">
        <!--
                SELECT
                SUM(b.planCount)
                FROM
                (
                <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_offer_item'">
                    SELECT
                    COUNT(DISTINCT mpp.id) AS planCount
                    FROM
                    m_agent AS a
                    - 代理结算关系表为基础数据源
                    INNER JOIN r_payable_plan_settlement_agent AS rppsa ON rppsa.fk_agent_id = a.id
                    INNER JOIN m_payable_plan AS mpp ON mpp.id = rppsa.fk_payable_plan_id AND mpp.fk_type_key = 'm_student_offer_item'
                    INNER JOIN m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id
                    INNER JOIN m_student_offer AS mso ON mso.id = msoi.fk_student_offer_id
                    INNER JOIN m_student AS s ON s.id = mso.fk_student_id AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
                    - 只有有收款记录的学习计划才能显示
                    INNER JOIN (
                    SELECT DISTINCT msoi2.id FROM ais_sale_center.m_student_offer_item AS msoi2
                    INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msoi2.id AND mrp.fk_type_key =
                    'm_student_offer_item'
                    INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
                    )z ON z.id = msoi.id
                    WHERE
                    a.is_active = 1
                    AND mso.STATUS = 1
                    AND mso.status_workflow IN ( 0, 3, 4 )
                    AND msoi.STATUS = 1
                    AND mpp.STATUS = 1
                    AND mpp.status_settlement IN
                    <foreach collection="statusSettlementList" item="statusSettlement" index="index" open="(" separator="," close=")">
                        #{statusSettlement}
                    </foreach>
                    <if test="subordinateAgentIds != null and subordinateAgentIds.size()>0">
                        AND a.id IN
                        <foreach collection="subordinateAgentIds" item="agentId" index="index" open="(" separator="," close=")">
                            #{agentId}
                        </foreach>
                    </if>
                    <if test="agentSettlementDto.applyStartTime!=null">
                        and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.applyStartTime},
                        '%Y-%m-%d' )
                    </if>
                    <if test="agentSettlementDto.negativeFlag">
                        AND mpp.payable_amount <![CDATA[< ]]> 0
                    </if>
                    <if test="agentSettlementDto.applyEndTime!=null">
                        and DATE_FORMAT( msoi.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.applyEndTime},
                        '%Y-%m-%d' )
                    </if>
                    <if test="agentSettlementDto.openingStartTime!=null">
                        and DATE_FORMAT( msoi.opening_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{agentSettlementDto.openingStartTime},
                        '%Y-%m-%d' )
                    </if>
                    <if test="agentSettlementDto.openingEndTime!=null">
                        and DATE_FORMAT( msoi.opening_time, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{agentSettlementDto.openingEndTime},
                        '%Y-%m-%d' )
                    </if>
                    <if test="agentSettlementDto.negativeFlag">
                        AND mpp.payable_amount <![CDATA[< ]]> 0
                    </if>
                    GROUP BY a.id
                </if>
                <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_insurance'">
                    <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_offer_item'">
                        UNION ALL
                    </if>
                    SELECT
                    COUNT(DISTINCT mpp.id) AS planCount
                    FROM
                    m_agent AS a
                    - 代理结算关系表为基础数据源
                    INNER JOIN r_payable_plan_settlement_agent AS rppsa ON rppsa.fk_agent_id = a.id
                    INNER JOIN m_payable_plan AS mpp ON mpp.id = rppsa.fk_payable_plan_id AND mpp.fk_type_key = 'm_student_insurance'
                    INNER JOIN m_student_insurance AS msi ON msi.id = mpp.fk_type_target_id
                    INNER JOIN m_student AS s ON s.id = msi.fk_student_id AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
                    - 只有有收款记录的才能显示
                    INNER JOIN (
                    SELECT DISTINCT msi2.id FROM ais_sale_center.m_student_insurance AS msi2
                    INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msi2.id AND mrp.fk_type_key =
                    'm_student_insurance'
                    INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
                    )z ON z.id = msi.id
                    WHERE
                    a.is_active = 1
                    AND mpp.STATUS = 1
                    AND msi.STATUS = 1
                    AND mpp.status_settlement IN
                    <foreach collection="statusSettlementList" item="statusSettlement" index="index" open="(" separator="," close=")">
                        #{statusSettlement}
                    </foreach>
                    <if test="subordinateAgentIds != null and subordinateAgentIds.size()>0">
                        AND a.id IN
                        <foreach collection="subordinateAgentIds" item="agentId" index="index" open="(" separator="," close=")">
                            #{agentId}
                        </foreach>
                    </if>
                    <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName !=''">
                        AND s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')
                    </if>
                    <if test="agentSettlementDto.negativeFlag">
                        AND mpp.payable_amount <![CDATA[< ]]> 0
                    </if>
                    GROUP BY a.id
                </if>
                <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_accommodation'">
                    <if test="agentSettlementDto.businessType == null or agentSettlementDto.businessType == '' or agentSettlementDto.businessType == 'm_student_insurance'">
                        UNION ALL
                    </if>
                    SELECT
                    COUNT(DISTINCT mpp.id) AS planCount
                    FROM
                    m_agent AS a
                    - 代理结算关系表为基础数据源
                    INNER JOIN r_payable_plan_settlement_agent AS rppsa ON rppsa.fk_agent_id = a.id
                    INNER JOIN m_payable_plan AS mpp ON mpp.id = rppsa.fk_payable_plan_id AND mpp.fk_type_key = 'm_student_accommodation'
                    INNER JOIN m_student_accommodation AS msa ON msa.id = mpp.fk_type_target_id
                    INNER JOIN m_student AS s ON s.id = msa.fk_student_id AND s.fk_company_id = #{agentSettlementDto.fkCompanyId}
                    - 只有有收款记录的才能显示
                    INNER JOIN (
                    SELECT DISTINCT msa2.id FROM ais_sale_center.m_student_accommodation AS msa2
                    INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.fk_type_target_id = msa2.id AND mrp.fk_type_key =
                    'm_student_accommodation'
                    INNER JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
                    )z ON z.id = msa.id
                    WHERE
                    a.is_active = 1
                    AND mpp.STATUS = 1
                    AND msa.STATUS = 1
                    AND mpp.status_settlement IN
                    <foreach collection="statusSettlementList" item="statusSettlement" index="index" open="(" separator="," close=")">
                        #{statusSettlement}
                    </foreach>
                    <if test="subordinateAgentIds != null and subordinateAgentIds.size()>0">
                        AND a.id IN
                        <foreach collection="subordinateAgentIds" item="agentId" index="index" open="(" separator="," close=")">
                            #{agentId}
                        </foreach>
                    </if>
                    <if test="agentSettlementDto.studentName != null and agentSettlementDto.studentName !=''">
                        AND s.name LIKE CONCAT('%', #{agentSettlementDto.studentName}, '%')
                    </if>
                    <if test="agentSettlementDto.negativeFlag">
                        AND mpp.payable_amount <![CDATA[< ]]> 0
                    </if>
                    GROUP BY a.id
                </if>
                )b
                -->
    </select>
    <select id="getAgentBycommissionSummaryVo"
            resultType="com.get.salecenter.dto.CommissionSummarySecondaryScreeningDto">

        <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_offer_item'">
            SELECT
            a.id AS agentId,
            mpp.fk_currency_type_num AS payPlanCurrencyTypeNum,
            rppsi.fk_currency_type_num AS accountCurrencyTypeNum,
            mpp.fk_type_key
            FROM
            m_agent AS a
            INNER JOIN m_student_offer_item AS msoi ON msoi.fk_agent_id = a.id
            INNER JOIN m_payable_plan AS mpp ON mpp.fk_type_target_id = msoi.id
            AND mpp.fk_type_key = 'm_student_offer_item'
            AND mpp.STATUS = 1
            INNER JOIN m_student AS s ON s.id = msoi.fk_student_id
            <if test="commissionSummaryDto.fkCompanyId != null and commissionSummaryDto.fkCompanyId !=''">
                AND s.fk_company_id = #{commissionSummaryDto.fkCompanyId}
            </if>
            <if test="commissionSummaryDto.fkCompanyIds != null and commissionSummaryDto.fkCompanyIds.size()>0">
                AND s.fk_company_id in
                <foreach collection="commissionSummaryDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id AND rppsi.status_settlement = 3
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_flag AS rppsf ON rppsf.fk_agent_id = a.id
            AND rppsf.fk_currency_type_num = mpp.fk_currency_type_num
            AND rppsf.fk_currency_type_num_account = rppsi.fk_currency_type_num
            AND rppsf.fk_type_key = 'm_student_offer_item'
            <where>
                <if test="commissionSummaryDto.studentName != null and commissionSummaryDto.studentName != ''">
                    AND
                    (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR s.`name` like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR s.last_name like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR s.first_name like concat('%',#{commissionSummaryDto.studentName},'%'))
                </if>
                <if test="commissionSummaryDto.agentAreaStateId != null and  commissionSummaryDto.agentAreaStateId !=''">
                    and a.fk_area_state_id =#{commissionSummaryDto.agentAreaStateId}
                </if>
                <if test="commissionSummaryDto.lockFlag != null">
                    <if test="commissionSummaryDto.lockFlag">
                        AND rppsf.id IS NOT NULL
                    </if>
                    <if test="!commissionSummaryDto.lockFlag">
                        AND rppsf.id IS NULL
                    </if>
                </if>
                <if test="commissionSummaryDto.agentNameOrNum != null and commissionSummaryDto.agentNameOrNum != ''">
                    AND (a.num LIKE CONCAT('%', #{commissionSummaryDto.agentNameOrNum}, '%') OR a.name LIKE CONCAT('%',
                    #{commissionSummaryDto.agentNameOrNum}, '%'))
                </if>
            </where>
            GROUP BY
            a.id,
            mpp.fk_currency_type_num,
            rppsi.fk_currency_type_num
        </if>
        <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_insurance'">
            <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_offer_item'">
                UNION ALL
            </if>
            SELECT
            a.id AS agentId,
            mpp.fk_currency_type_num AS payPlanCurrencyTypeNum,
            rppsi.fk_currency_type_num AS accountCurrencyTypeNum,
            mpp.fk_type_key
            FROM
            ais_sale_center.m_agent AS a
            INNER JOIN m_student_insurance AS msi ON msi.fk_agent_id = a.id
            INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = msi.id
            AND mpp.fk_type_key = 'm_student_insurance'
            AND mpp.STATUS = 1
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id AND rppsi.status_settlement = 3
            INNER JOIN m_student AS s ON s.id = msi.fk_student_id
            <if test="commissionSummaryDto.fkCompanyId != null and commissionSummaryDto.fkCompanyId !=''">
                AND s.fk_company_id = #{commissionSummaryDto.fkCompanyId}
            </if>
            <if test="commissionSummaryDto.fkCompanyIds != null and commissionSummaryDto.fkCompanyIds.size()>0">
                AND s.fk_company_id in
                <foreach collection="commissionSummaryDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_flag AS rppsf ON rppsf.fk_agent_id = a.id
            AND rppsf.fk_currency_type_num = mpp.fk_currency_type_num
            AND rppsf.fk_currency_type_num_account = rppsi.fk_currency_type_num
            AND rppsf.fk_type_key = 'm_student_insurance'
            <where>
                <if test="commissionSummaryDto.studentName != null and commissionSummaryDto.studentName != ''">
                    AND
                    (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR s.`name` like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR s.last_name like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR s.first_name like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR msi.`insurant_name` like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR msi.insurant_last_name like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR msi.insurant_first_name like concat('%',#{commissionSummaryDto.studentName},'%'))
                </if>
                <if test="commissionSummaryDto.agentAreaStateId != null and  commissionSummaryDto.agentAreaStateId !=''">
                    and a.fk_area_state_id =#{commissionSummaryDto.agentAreaStateId}
                </if>
                <if test="commissionSummaryDto.lockFlag != null">
                    <if test="commissionSummaryDto.lockFlag">
                        AND rppsf.id IS NOT NULL
                    </if>
                    <if test="!commissionSummaryDto.lockFlag">
                        AND rppsf.id IS NULL
                    </if>
                </if>
                <if test="commissionSummaryDto.agentNameOrNum != null and commissionSummaryDto.agentNameOrNum != ''">
                    AND (a.num LIKE CONCAT('%', #{commissionSummaryDto.agentNameOrNum}, '%') OR a.name LIKE CONCAT('%',
                    #{commissionSummaryDto.agentNameOrNum}, '%'))
                </if>
            </where>
            GROUP BY
            a.id,
            mpp.fk_currency_type_num,
            rppsi.fk_currency_type_num
        </if>
        <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_accommodation'">
            <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_offer_item'">
                UNION ALL
            </if>

            SELECT
            a.id AS agentId,
            mpp.fk_currency_type_num AS payPlanCurrencyTypeNum,
            rppsi.fk_currency_type_num AS accountCurrencyTypeNum,
            mpp.fk_type_key
            FROM
            ais_sale_center.m_agent AS a
            INNER JOIN m_student_accommodation AS msa ON msa.fk_agent_id = a.id
            INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = msa.id
            AND mpp.fk_type_key = 'm_student_accommodation'
            AND mpp.STATUS = 1
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id AND rppsi.status_settlement = 3
            INNER JOIN m_student AS s ON s.id = msa.fk_student_id
            <if test="commissionSummaryDto.fkCompanyId != null and commissionSummaryDto.fkCompanyId !=''">
                AND s.fk_company_id = #{commissionSummaryDto.fkCompanyId}
            </if>
            <if test="commissionSummaryDto.fkCompanyIds != null and commissionSummaryDto.fkCompanyIds.size()>0">
                AND s.fk_company_id in
                <foreach collection="commissionSummaryDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_flag AS rppsf ON rppsf.fk_agent_id = a.id
            AND rppsf.fk_currency_type_num = mpp.fk_currency_type_num
            AND rppsf.fk_currency_type_num_account = rppsi.fk_currency_type_num
            AND rppsf.fk_type_key = 'm_student_accommodation'
            <where>
                <if test="commissionSummaryDto.studentName != null and commissionSummaryDto.studentName != ''">
                    AND
                    (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR s.`name` like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR s.last_name like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR s.first_name like concat('%',#{commissionSummaryDto.studentName},'%'))
                </if>
                <if test="commissionSummaryDto.agentAreaStateId != null and  commissionSummaryDto.agentAreaStateId !=''">
                    and a.fk_area_state_id =#{commissionSummaryDto.agentAreaStateId}
                </if>
                <if test="commissionSummaryDto.lockFlag != null">
                    <if test="commissionSummaryDto.lockFlag">
                        AND rppsf.id IS NOT NULL
                    </if>
                    <if test="!commissionSummaryDto.lockFlag">
                        AND rppsf.id IS NULL
                    </if>
                </if>
                <if test="commissionSummaryDto.agentNameOrNum != null and commissionSummaryDto.agentNameOrNum != ''">
                    AND (a.num LIKE CONCAT('%', #{commissionSummaryDto.agentNameOrNum}, '%') OR a.name LIKE CONCAT('%',
                    #{commissionSummaryDto.agentNameOrNum}, '%'))
                </if>
            </where>
            GROUP BY
            a.id,
            mpp.fk_currency_type_num,
            rppsi.fk_currency_type_num
        </if>
        <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_service_fee'">
            <if test="commissionSummaryDto.businessType == null or commissionSummaryDto.businessType == '' or commissionSummaryDto.businessType == 'm_student_offer_item'">
                UNION ALL
            </if>

            SELECT
            a.id AS agentId,
            mpp.fk_currency_type_num AS payPlanCurrencyTypeNum,
            rppsi.fk_currency_type_num AS accountCurrencyTypeNum,
            mpp.fk_type_key
            FROM
            ais_sale_center.m_agent AS a
            INNER JOIN m_student_service_fee AS mssf ON mssf.fk_agent_id = a.id
            INNER JOIN ais_sale_center.m_payable_plan AS mpp ON mpp.fk_type_target_id = mssf.id
            AND mpp.fk_type_key = 'm_student_service_fee'
            AND mpp.STATUS = 1
            INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id AND rppsi.status_settlement = 3
            INNER JOIN m_student AS s ON s.id = mssf.fk_student_id
            <if test="commissionSummaryDto.fkCompanyId != null and commissionSummaryDto.fkCompanyId !=''">
                AND s.fk_company_id = #{commissionSummaryDto.fkCompanyId}
            </if>
            <if test="commissionSummaryDto.fkCompanyIds != null and commissionSummaryDto.fkCompanyIds.size()>0">
                AND s.fk_company_id in
                <foreach collection="commissionSummaryDto.fkCompanyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                    #{fkCompanyId}
                </foreach>
            </if>
            LEFT JOIN ais_finance_center.r_payable_plan_settlement_flag AS rppsf ON rppsf.fk_agent_id = a.id
            AND rppsf.fk_currency_type_num = mpp.fk_currency_type_num
            AND rppsf.fk_currency_type_num_account = rppsi.fk_currency_type_num
            AND rppsf.fk_type_key = 'm_student_service_fee'
            <where>
                <if test="commissionSummaryDto.studentName != null and commissionSummaryDto.studentName != ''">
                    AND
                    (REPLACE(CONCAT(s.first_name,s.last_name),' ','') like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR REPLACE(CONCAT(s.last_name,s.first_name),' ','') like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR s.`name` like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR s.last_name like concat('%',#{commissionSummaryDto.studentName},'%')
                    OR s.first_name like concat('%',#{commissionSummaryDto.studentName},'%'))
                </if>
                <if test="commissionSummaryDto.agentAreaStateId != null and  commissionSummaryDto.agentAreaStateId !=''">
                    and a.fk_area_state_id =#{commissionSummaryDto.agentAreaStateId}
                </if>
                <if test="commissionSummaryDto.lockFlag != null">
                    <if test="commissionSummaryDto.lockFlag">
                        AND rppsf.id IS NOT NULL
                    </if>
                    <if test="!commissionSummaryDto.lockFlag">
                        AND rppsf.id IS NULL
                    </if>
                </if>
                <if test="commissionSummaryDto.agentNameOrNum != null and commissionSummaryDto.agentNameOrNum != ''">
                    AND (a.num LIKE CONCAT('%', #{commissionSummaryDto.agentNameOrNum}, '%') OR a.name LIKE CONCAT('%',
                    #{commissionSummaryDto.agentNameOrNum}, '%'))
                </if>
            </where>
            GROUP BY
            a.id,
            mpp.fk_currency_type_num,
            rppsi.fk_currency_type_num
        </if>
    </select>

    <update id="agentIsKeyExpired">
        UPDATE m_agent
        SET is_key_agent=0
        WHERE DATE_FORMAT(key_agent_failure_time, '%Y-%m-%d') &lt; DATE_FORMAT(now(), '%Y-%m-%d')
          AND is_key_agent = 1
    </update>

    <select id="getIsExistAgent" resultType="com.get.salecenter.vo.AgentVo">
        SELECT mg.`name`,mg.id,mg.id_card_num,mg.tax_code,mg.nature,mg.legal_person,ras.fk_staff_id as fkStaffId,
        GROUP_CONCAT(mc.num ORDER BY mc.view_order DESC) AS companyName FROM  m_agent mg
        LEFT JOIN r_agent_staff ras on ras.fk_agent_id = mg.id AND ras.is_active = 1
        join r_agent_company rac on rac.fk_agent_id = mg.id
        join ais_permission_center.m_company mc on mc.id = rac.fk_company_id
        where 1=1
<!--        ,rac.fk_company_id=#{companyId}-->
        <if test="idCard !=null and idCard!=''">
            and  mg.id_card_num=#{idCard}
        </if>
        <if test="taxCode!=null and taxCode !=''">
            and mg.tax_code=#{taxCode}
        </if>
        <if test="nature!=null and nature !=''">
            and mg.nature=#{nature}
        </if>
        <if test="legalPerson!=null and legalPerson !=''">
            and mg.legal_person=#{legalPerson}
        </if>
        <if test="name!=null and name!=''">
            and mg.name=#{name}
        </if>
        GROUP BY mg.id
        limit 0,1
    </select>

    <select id="getSource" resultType="com.get.salecenter.vo.AgentSourceVo">
        select * from(
        SELECT a.id as fkAgentIds, a.num,a.`name` agName,
        CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）"))) as agentName,
        a.name_note,
        IFNULL(d.kpiScoreThree,0) kpiScoreThree,
        IFNULL(d.kpiScoreTwo,0) kpiScoreTwo,
        IFNULL(d.kpiScoreOne,0) kpiScoreOne,
        IFNULL(d.apiSum,0) as ttlScore,
        IFNULL(f.no_dp_count,0) noadmittedcount,g.bdnames
        FROM (SELECT a.* FROM m_agent a LEFT JOIN r_agent_company b ON a.id=b.fk_agent_id WHERE b.fk_company_id=2) a
        INNER JOIN (
        SELECT DISTINCT fk_agent_id FROM r_agent_staff WHERE is_active=1
        <if test="staffFollowerIds != null and staffFollowerIds.size()>0">
            AND fk_staff_id IN
            <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ) b ON a.id=b.fk_agent_id
        LEFT JOIN (
        SELECT 	a.fk_agent_id,
        SUM( CASE WHEN a.kpiScore = 3 THEN a.kpiScore ELSE 0 END ) AS kpiScoreThree,
        SUM( CASE WHEN a.kpiScore = 2 THEN a.kpiScore ELSE 0 END ) AS kpiScoreTwo,
        SUM( CASE WHEN a.kpiScore = 1 THEN a.kpiScore ELSE 0 END ) AS kpiScoreOne,
        SUM( a.kpiScore ) AS apiSum
        FROM (
        SELECT a.fk_agent_id,
        CASE
        WHEN MAX( CASE ukip.score WHEN 3 THEN 3 WHEN 2 THEN 2 ELSE 1 END ) >= 3 THEN
        3
        WHEN MAX( CASE ukip.score WHEN 3 THEN 3 WHEN 2 THEN 2 ELSE 1 END ) >= 2 THEN
        2 ELSE 1
        END AS kpiScore
        FROM m_student_offer_item a
        LEFT JOIN (
        SELECT a.fk_student_offer_item_id, MIN(a.gmt_create) dp_time FROM r_student_offer_item_step a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
        WHERE b.step_key ='STEP_DEPOSIT_PAID'
        GROUP BY a.fk_student_offer_item_id
        ) c ON a.id=c.fk_student_offer_item_id
        LEFT JOIN u_kpi_institution_provider ukip ON ukip.fk_institution_provider_id = a.fk_institution_provider_id
        <if test="institutionIdsExcludingString != null and institutionIdsExcludingString != ''">
            and not FIND_IN_SET(a.fk_institution_id,#{institutionIdsExcludingString})
        </if>
        WHERE a.`status`=1  AND a.is_follow=0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0)!=1
        </if>
        AND c.dp_time>=#{beginTime} AND c.dp_time &lt;=#{endTime}
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) d ON a.id=d.fk_agent_id
<!--        LEFT JOIN (-->
<!--        SELECT a.fk_agent_id, COUNT(a.fk_student_id)*3 kpi_score FROM (-->
<!--        SELECT a.fk_agent_id, a.fk_student_id-->
<!--        FROM m_student_offer_item a-->
<!--        LEFT JOIN (-->
<!--        SELECT a.fk_student_offer_item_id, MIN(a.gmt_create) dp_time FROM r_student_offer_item_step a-->
<!--        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id-->
<!--        WHERE b.step_key IN('STEP_DEPOSIT_PAID','STEP_OFFER_SELECTION')-->
<!--        GROUP BY a.fk_student_offer_item_id-->
<!--        ) c ON a.id=c.fk_student_offer_item_id-->
<!--        LEFT JOIN ais_institution_center.m_institution d ON a.fk_institution_id=d.id-->
<!--        WHERE a.`status`=1 AND a.is_follow=0-->
<!--        <if test="!isStudentOfferItemFinancialHiding">-->
<!--            AND IFNULL(a.is_follow_hidden, 0)!=1-->
<!--        </if>-->
<!--        AND c.dp_time>=#{beginTime} AND c.dp_time &lt;=#{endTime}-->
<!--        AND d.is_kpi=1-->
<!--        GROUP BY a.fk_agent_id, a.fk_student_id-->
<!--        ) a-->
<!--        GROUP BY a.fk_agent_id-->
<!--        ) e ON a.id=e.fk_agent_id-->
        LEFT JOIN (
        SELECT a.fk_agent_id, COUNT(a.fk_student_id) no_dp_count FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM m_student_offer_item a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
        LEFT JOIN (
        SELECT a.fk_student_offer_item_id, b.defer_entrance_time
        FROM (SELECT a.fk_student_offer_item_id, MAX( a.id ) as id FROM m_student_offer_item_defer_entrance_time a GROUP BY a.fk_student_offer_item_id) a
        INNER JOIN m_student_offer_item_defer_entrance_time b on a.id = b.id
        ) c ON a.id=c.fk_student_offer_item_id
        WHERE a.`status`=1  AND a.is_follow=0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0)!=1
        </if>
        AND b.step_key IN('STEP_NEW_APP','STEP_SUBMITTED','STEP_NOTIFIED','STEP_REGISTERED','STEP_APP_RECEIVED','STEP_ADMITTED')
        AND a.defer_opening_time &gt;= #{opening_start_time}
        AND a.defer_opening_time &lt;= #{opening_end_time}
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) f ON a.id=f.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, GROUP_CONCAT(b.`name`, '（', b.name_en, '）') bdnames
        FROM ais_sale_center.r_agent_staff a
        LEFT JOIN ais_permission_center.m_staff b ON a.fk_staff_id=b.id
        WHERE a.is_active=1
        GROUP BY a.fk_agent_id
        ) g ON a.id=g.fk_agent_id
        WHERE 1=1
        and b.fk_agent_id IS NOT NULL
        <if test='state =="1"'>
            and a.id=#{bms_id}
        </if>
        <if test='state =="2"'>
            and a.id_gea=#{cpp_id}
        </if>
        <if test="agentName !=null and agentName!=''">
<!--            <and  a.name like concat('%',#{agentName},'%')>-->
            HAVING agentName like concat('%',#{agentName},'%')
        </if>
        ORDER BY ttlScore DESC
        )aa where 1=1 and aa.ttlScore!=0
    </select>

    <select id="getSourceBackUp" resultType="com.get.salecenter.vo.AgentSourceVo">
        select * from(
        SELECT a.id as fkAgentIds, a.num,a.`name` agName,
        CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）"))) as agentName,
        a.name_note,
        IFNULL(c.crt_score,0) crtscore,
        IFNULL(d.dp_score,0) admittedscore,
        IFNULL(e.kpi_score,0) kpiscore,
        (IFNULL(c.crt_score,0)+IFNULL(d.dp_score,0)+IFNULL(e.kpi_score,0)) ttlscore,
        IFNULL(f.no_dp_count,0) noadmittedcount,g.bdnames
        FROM (SELECT a.* FROM m_agent a LEFT JOIN r_agent_company b ON a.id=b.fk_agent_id WHERE b.fk_company_id=2) a
        LEFT JOIN (SELECT DISTINCT fk_agent_id FROM r_agent_staff WHERE is_active=1 AND fk_staff_id IN(${splitSetString})) b ON a.id=b.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, COUNT(a.fk_student_id) crt_score FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM r_student_agent a
        LEFT JOIN m_student b ON a.fk_student_id=b.id
        WHERE a.is_active=1
        AND b.gmt_create>=#{beginTime} AND b.gmt_create &lt;=#{endTime}
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) c ON a.id=c.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, COUNT(a.fk_student_id)*3 dp_score FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM m_student_offer_item a
        LEFT JOIN (
        SELECT a.fk_student_offer_item_id, MIN(a.gmt_create) dp_time FROM r_student_offer_item_step a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
        WHERE b.step_key IN('STEP_DEPOSIT_PAID','STEP_OFFER_SELECTION')
        GROUP BY a.fk_student_offer_item_id
        ) c ON a.id=c.fk_student_offer_item_id
        WHERE a.`status`=1  AND a.is_follow=0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0)!=1
        </if>
        AND c.dp_time>=#{beginTime} AND c.dp_time &lt;=#{endTime}
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) d ON a.id=d.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, COUNT(a.fk_student_id)*3 kpi_score FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM m_student_offer_item a
        LEFT JOIN (
        SELECT a.fk_student_offer_item_id, MIN(a.gmt_create) dp_time FROM r_student_offer_item_step a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
        WHERE b.step_key IN('STEP_DEPOSIT_PAID','STEP_OFFER_SELECTION')
        GROUP BY a.fk_student_offer_item_id
        ) c ON a.id=c.fk_student_offer_item_id
        LEFT JOIN ais_institution_center.m_institution d ON a.fk_institution_id=d.id
        WHERE a.`status`=1 AND a.is_follow=0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0)!=1
        </if>
        AND c.dp_time>=#{beginTime} AND c.dp_time &lt;=#{endTime}
        AND d.is_kpi=1
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) e ON a.id=e.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, COUNT(a.fk_student_id) no_dp_count FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM m_student_offer_item a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
        LEFT JOIN (
        SELECT a.fk_student_offer_item_id, b.defer_entrance_time
        FROM (SELECT a.fk_student_offer_item_id, MAX( a.id ) as id FROM m_student_offer_item_defer_entrance_time a GROUP BY a.fk_student_offer_item_id) a
        INNER JOIN m_student_offer_item_defer_entrance_time b on a.id = b.id
        ) c ON a.id=c.fk_student_offer_item_id
        WHERE a.`status`=1  AND a.is_follow=0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0)!=1
        </if>
        AND b.step_key IN('STEP_NEW_APP','STEP_SUBMITTED','STEP_NOTIFIED','STEP_REGISTERED','STEP_APP_RECEIVED','STEP_ADMITTED')
        AND a.defer_opening_time &gt;= #{opening_start_time}
        AND a.defer_opening_time &lt;= #{opening_end_time}
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) f ON a.id=f.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, GROUP_CONCAT(b.`name`, '（', b.name_en, '）') bdnames
        FROM ais_sale_center.r_agent_staff a
        LEFT JOIN ais_permission_center.m_staff b ON a.fk_staff_id=b.id
        WHERE a.is_active=1
        GROUP BY a.fk_agent_id
        ) g ON a.id=g.fk_agent_id
        WHERE 1=1
        and b.fk_agent_id IS NOT NULL
        <if test="agentName !=null and agentName!=''">
            <!--            <and  a.name like concat('%',#{agentName},'%')>-->
            HAVING agentName like concat('%',#{agentName},'%')
        </if>
        ORDER BY (IFNULL(c.crt_score,0)+IFNULL(d.dp_score,0)+IFNULL(e.kpi_score,0)) DESC
        )aa where 1=1 and aa.ttlscore!=0
    </select>

    <select id="getSumSourceByCppIdOrBmsId" resultType="com.get.salecenter.vo.AgentSourceVo">
        select (IFNULL(aa.admittedscore,0)+IFNULL(aa.kpiscore,0)) ttlscore from(
        SELECT a.id, a.num, a.name as agentName, a.name_note,
        IFNULL(c.crt_score,0) crtscore,
        IFNULL(d.dp_score,0) admittedscore,
        IFNULL(e.kpi_score,0) kpiscore,
        (IFNULL(c.crt_score,0)+IFNULL(d.dp_score,0)+IFNULL(e.kpi_score,0)) ttlscore,
        IFNULL(f.no_dp_count,0) noadmittedcount
        FROM (SELECT a.* FROM m_agent a LEFT JOIN r_agent_company b ON a.id=b.fk_agent_id WHERE b.fk_company_id=2) a
        LEFT JOIN (SELECT DISTINCT fk_agent_id FROM r_agent_staff WHERE is_active=1 AND fk_staff_id IN(822)) b ON a.id=b.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, COUNT(a.fk_student_id) crt_score FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM r_student_agent a
        LEFT JOIN m_student b ON a.fk_student_id=b.id
        WHERE a.is_active=1
        AND b.gmt_create>=#{beginTime} AND b.gmt_create &lt;=#{endTime}
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) c ON a.id=c.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, COUNT(a.fk_student_id)*3 dp_score FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM m_student_offer_item a
        LEFT JOIN (
        SELECT a.fk_student_offer_item_id, MIN(a.gmt_create) dp_time FROM r_student_offer_item_step a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
        WHERE b.step_key IN('STEP_DEPOSIT_PAID','STEP_OFFER_SELECTION','STEP_VISA_SUBMITTED','STEP_VISA_GRANTED','STEP_ENROLLED')
        GROUP BY a.fk_student_offer_item_id
        ) c ON a.id=c.fk_student_offer_item_id
        WHERE a.`status`=1 AND a.is_follow=0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0)!=1
        </if>
        AND c.dp_time>=#{beginTime} AND c.dp_time &lt;=#{endTime}
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) d ON a.id=d.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, COUNT(a.fk_student_id)*3 kpi_score FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM m_student_offer_item a
        LEFT JOIN (
        SELECT a.fk_student_offer_item_id, MIN(a.gmt_create) dp_time FROM r_student_offer_item_step a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
        WHERE b.step_key IN('STEP_DEPOSIT_PAID','STEP_OFFER_SELECTION','STEP_VISA_SUBMITTED','STEP_VISA_GRANTED','STEP_ENROLLED')
        GROUP BY a.fk_student_offer_item_id
        ) c ON a.id=c.fk_student_offer_item_id
        LEFT JOIN ais_institution_center.m_institution d ON a.fk_institution_id=d.id
        WHERE a.`status`=1 AND a.is_follow=0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0)!=1
        </if>
        AND c.dp_time>=#{beginTime} AND c.dp_time &lt;=#{endTime}
        AND d.is_kpi=1
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) e ON a.id=e.fk_agent_id
        LEFT JOIN (SELECT a.fk_agent_id, COUNT(a.fk_student_id) no_dp_count FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM m_student_offer_item a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
        WHERE a.`status`=1 AND a.is_follow=0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0)!=1
        </if>
        AND b.step_key IN('STEP_NEW_APP','STEP_SUBMITTED','STEP_NOTIFIED','STEP_REGISTERED','STEP_APP_RECEIVED','STEP_ADMITTED')
        AND DATE_FORMAT(a.defer_opening_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{opening_start_time},'%Y-%m-%d')
        AND DATE_FORMAT(a.defer_opening_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{opening_end_time},'%Y-%m-%d')
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) f ON a.id=f.fk_agent_id
        WHERE 1=1
        <if test='state =="1"'>
            and a.id=#{bms_id}
        </if>
        <if test='state =="2"'>
            and a.id_gea=#{cpp_id}
        </if>
        ORDER BY (IFNULL(d.dp_score,0)+IFNULL(e.kpi_score,0)) DESC
        )aa
    </select>

    <select id="getContactPersonAgentList" resultType="com.get.salecenter.entity.Agent">
        SELECT DISTINCT b.id,b.name
        FROM ais_sale_center.s_contact_person a
        INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.contactPersonPermissionSql"/>
        ) z ON a.id=z.id


        LEFT JOIN ais_sale_center.m_agent_contract f on f.id = a.fk_table_id and a.fk_table_name = 'm_agent_contract'
        INNER JOIN ais_sale_center.m_agent b on (a.fk_table_id = b.id AND a.fk_table_name = 'm_agent') OR (f.fk_agent_id = b.id)
        LEFT JOIN ais_sale_center.r_contact_person_company g on g.fk_contact_person_id = a.id

        where g.fk_company_id =#{companyId}  -- 公司查询
        order by a.gmt_create desc
    </select>

    <select id="verifyAgentPermissions" resultType="java.lang.Long">
        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.agentPermissionSql"/>
        WHERE a.id = #{agentId}
    </select>

    <select id="getAgents" resultType="com.get.salecenter.vo.AgentVo">
        SELECT a.*,
        MIN(mac.gmt_create) as firstContractTime

        FROM ais_sale_center.m_agent a

        INNER JOIN (
        SELECT DISTINCT b.id
        FROM (

        SELECT b.id
        -- #第一层####################
        FROM ais_sale_center.m_agent b

        <!-- 代理统计报表跳转专用sql -->
        <if test="agentAnnualSummaryDto != null">
        INNER JOIN (
            SELECT
            ma.id
            <if test="agentAnnualSummaryDto.jumpType != 4">
                <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.agentStatistical"/>
            </if>
            <if test="agentAnnualSummaryDto.jumpType == 4">
                <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.unsignedNum"/>
                AND contractFlag.id IS NULL
            </if>
            GROUP BY ma.id

        )jumpAgent ON jumpAgent.id = b.id
        </if>


        INNER JOIN (
        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.agentPermissionSql"/>
        ) z ON b.id=z.id
        ) b
        ) c on a.id = c.id
--         INNER JOIN r_agent_company d on a.id = d.fk_agent_id

        -- bd编号
        <if test="(agentDto.bdCode !=null and agentDto.bdCode !='') or
        (agentDto.fkAreaRegionId !=null and agentDto.fkAreaRegionId !='') or
        (agentDto.bdName !=null and agentDto.bdName !='')">
        INNER JOIN r_agent_staff e on e.fk_agent_id = a.id and e.is_active = 1
        INNER JOIN ais_permission_center.m_staff m ON e.fk_staff_id=m.id
        INNER JOIN r_staff_bd_code f on f.fk_staff_id = e.fk_staff_id
        </if>
        LEFT JOIN m_agent_contract mac on mac.fk_agent_id = a.id and mac.is_active = 1

        <if test="(agentDto.labelTypeId != null and agentDto.labelTypeId != '' ) or
        (agentDto.labelRemark != null and agentDto.labelRemark != '' )or
        (agentDto.labelEmail != null and agentDto.labelEmail != '' )">
            INNER JOIN ais_sale_center.r_agent_label b3 ON b3.fk_agent_id = a.id
            INNER JOIN ais_platform_center.u_label b1 ON b1.id = b3.fk_label_id
            INNER JOIN ais_platform_center.u_label_type b2 ON b2.id = b1.fk_label_type_id
        </if>

        where 1=1
        <!-- 公司权限 -->
        AND EXISTS (
            SELECT 1 FROM ais_sale_center.r_agent_company d
            where a.id = d.fk_agent_id
            AND d.fk_company_id in
            <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
            #{companyId}
            </foreach>
            -- 公司
            <if test="agentDto.fkCompanyId !=null and agentDto.fkCompanyId !=''">
                and d.fk_company_id =#{agentDto.fkCompanyId}
            </if>
        )
        <!--
        and d.fk_company_id in
        <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
        #{companyId}
        </foreach>
        <if test="agentDto.fkCompanyId !=null and agentDto.fkCompanyId !=''">
        and d.fk_company_id =#{agentDto.fkCompanyId}
        </if>
        -->
        -- bd编号
        <if test="agentDto.bdCode !=null and agentDto.bdCode !=''">
        and f.bd_code =#{agentDto.bdCode}
        </if>
        <!--bd名称-->
        <if test="agentDto.bdName != null and agentDto.bdName != ''">
            AND (LOWER(m.`name`) like concat("%",#{agentDto.bdName},"%")
            OR LOWER(m.name_en) like concat("%",#{agentDto.bdName},"%"))
        </if>

        -- 大区
        <if test="agentDto.fkAreaRegionId !=null and agentDto.fkAreaRegionId !=''">
        and FIND_IN_SET(#{agentDto.fkAreaRegionId},f.fk_area_region_id)
        </if>

        -- 父代理
        <if test="agentDto.fkParentAgentId !=null and agentDto.fkParentAgentId !=''">
        and a.fk_parent_agent_id =#{agentDto.fkParentAgentId}
        </if>
        -- 国家查询
        <if test="agentDto.fkAreaCountryId !=null and agentDto.fkAreaCountryId !=''">
        and a.fk_area_country_id =#{agentDto.fkAreaCountryId}
        </if>
        -- 州省查询
        <if test="agentDto.fkAreaStateId !=null and agentDto.fkAreaStateId !=''">
        and a.fk_area_state_id =#{agentDto.fkAreaStateId}
        </if>
        -- 城市查询
        <if test="agentDto.fkAreaCityId !=null and agentDto.fkAreaCityId !=''">
        and a.fk_area_city_id =#{agentDto.fkAreaCityId}
        </if>
        -- 是否激活查询
        <if test="agentDto.isActive !=null">
        and a.is_active =#{agentDto.isActive}
        </if>
        -- 是否渠道代理查询
        <if test="agentDto.isCustomerChannel !=null">
        and a.is_customer_channel =#{agentDto.isCustomerChannel}
        </if>
        -- 是否关键代理查询
        <if test="agentDto.isKeyAgent !=null">
        and a.is_key_agent =#{agentDto.isKeyAgent}
        </if>
        -- 性质
        <if test="agentDto.nature !=null  and agentDto.nature !=''">
            and a.nature = #{agentDto.nature}
        </if>
        <if test="agentDto.createBeginTime != null">
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{agentDto.createBeginTime},'%Y-%m-%d')
        </if>
        <if test="agentDto.createEndTime != null">
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{agentDto.createEndTime},'%Y-%m-%d')
        </if>

        <if test="agentDto.natureList != null and agentDto.natureList.size()>0">
            and a.nature in
            <foreach collection="agentDto.natureList" item="nature" open="(" separator="," close=")">
                #{nature}
            </foreach>
        </if>
        -- 用于KPI统计中代理数的跳转
        <if test="agentDto.agentIds != null and agentDto.agentIds.size() > 0">
            and a.id in
            <foreach collection="agentDto.agentIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        -- 性质备注
        <if test="agentDto.natureNote !=null  and agentDto.natureNote !=''">
            and a.nature_note LIKE concat('%',#{agentDto.natureNote},'%')
        </if>
        -- 查询条件-编号/名称关键字/名字备注/原公司
        <if test="agentDto.keyWord !=null and agentDto.keyWord !=''">
        and (a.num like concat('%',#{agentDto.keyWord},'%')
        or a.name like concat('%',#{agentDto.keyWord},'%')
        or a.name_note like concat('%',#{agentDto.keyWord},'%')
        or a.remark like concat('%',#{agentDto.keyWord},'%')
        or a.nature_note like concat('%',#{agentDto.keyWord},'%')
            )
        </if>
        -- 是否有备注
        <if test="agentDto.existsRemark != null">
            <if test="!agentDto.existsRemark">
                and ( a.remark IS NULL OR a.remark = '' )
            </if>
            <if test="agentDto.existsRemark">
                and ( a.remark IS NOT NULL AND a.remark != '' )
            </if>
        </if>
        <if test="agentDto.isHasContractAttachment !=null">
            <if test="agentDto.isHasContractAttachment">
                AND EXISTS
            </if>
            <if test="!agentDto.isHasContractAttachment">
                AND NOT EXISTS
            </if>
            (
                SELECT 1 FROM m_agent_contract ac
                INNER JOIN s_media_and_attached maa ON maa.fk_table_id = ac.id AND maa.fk_table_name = "m_agent_contract"
                WHERE
                ac.fk_agent_id = a.id and ac.is_active = 1
            )
        </if>
        <if test="agentDto.labelTypeId != null and agentDto.labelTypeId != ''">
            AND b2.id = #{agentDto.labelTypeId}
        </if>

        <if test="agentDto.labelRemark != null and agentDto.labelRemark != ''">
            AND (b1.label_name LIKE CONCAT('%', #{agentDto.labelRemark}, '%')
            OR b1.remark LIKE CONCAT('%', #{agentDto.labelRemark}, '%'))
        </if>

        <if test="agentDto.labelEmail != null and agentDto.labelEmail != ''">
            AND b3.email LIKE CONCAT('%', #{agentDto.labelEmail}, '%')
        </if>

        <!-- 合同状态过滤（仅处理数据库原始状态） -->
        <if test="agentDto.contractApprovalStatus != null and agentDto.contractApprovalStatus &lt;= 5 and agentDto.contractApprovalStatus >= -4 and agentDto.contractApprovalStatus != 0">
            AND EXISTS (
                SELECT 1 FROM m_agent_contract mac2 
                WHERE mac2.fk_agent_id = a.id 
                AND mac2.is_active = 1
                AND mac2.contract_approval_status = #{agentDto.contractApprovalStatus}
            )
        </if>

        GROUP BY a.id
        order by a.gmt_create desc,a.is_active desc
    </select>
    <select id="getAgentList" resultType="com.get.salecenter.vo.AgentVo">
        SELECT a.*
<!--        ,b.email,b1.remark AS labelRemark,b2.id AS labelTypeId-->
        FROM ais_sale_center.m_agent a -- #FORCE index(index_gmt_create)
--         INNER JOIN r_agent_company d on a.id = d.fk_agent_id
        -- bd编号
        <if test="(agentDto.bdCode !=null and agentDto.bdCode !='') or
        (agentDto.fkAreaRegionId !=null and agentDto.fkAreaRegionId !='') or
        (agentDto.bdName !=null and agentDto.bdName !='')">
            INNER JOIN r_agent_staff e on e.fk_agent_id = a.id and e.is_active = 1
            INNER JOIN ais_permission_center.m_staff m ON e.fk_staff_id=m.id
            INNER JOIN r_staff_bd_code f on f.fk_staff_id = e.fk_staff_id
        </if>
<!--        <if test="agentDto.labelTypeId != null or agentDto.labelRemark != null or agentDto.labelEmail != null">-->
<!--            INNER JOIN ais_sale_center.r_agent_label b ON b.fk_agent_id = a.id-->
<!--            INNER JOIN ais_platform_center.u_label b1 ON b1.id = b.fk_label_id-->
<!--            INNER JOIN ais_platform_center.u_label_type b2 ON b2.id = b1.fk_label_type_id-->
<!--        </if>-->

        where 1=1
        <if test="agentDto.fkCompanyId !=null and agentDto.fkCompanyId !=''">
        AND EXISTS (
            SELECT 1 FROM ais_sale_center.r_agent_company d
            where a.id = d.fk_agent_id
            AND d.fk_company_id =#{agentDto.fkCompanyId}
        )
        </if>
<!--        -->
<!--        &#45;&#45; 公司-->
<!--        <if test="agentDto.fkCompanyId !=null and agentDto.fkCompanyId !=''">-->
<!--            and d.fk_company_id =#{agentDto.fkCompanyId}-->
<!--        </if>-->
        -- bd编号
        <if test="agentDto.bdCode !=null and agentDto.bdCode !=''">
            and f.bd_code =#{agentDto.bdCode}
        </if>
        <!--bd名称-->
        <if test="agentDto.bdName != null and agentDto.bdName != ''">
            AND (LOWER(m.`name`) like concat("%",#{agentDto.bdName},"%")
            OR LOWER(m.name_en) like concat("%",#{agentDto.bdName},"%"))
        </if>
        -- 大区
        <if test="agentDto.fkAreaRegionId !=null and agentDto.fkAreaRegionId !=''">
            and FIND_IN_SET(#{agentDto.fkAreaRegionId},f.fk_area_region_id)
        </if>

        <!--        &#45;&#45;  #过滤业务国家权限-->
        <!--        <if test="areaCountryIds != null and areaCountryIds.size() > 0">-->
        <!--            AND a.fk_area_country_id IN-->
        <!--            <foreach collection="areaCountryIds" item="areaCountryId" index="index" open="(" separator="," close=")">-->
        <!--                #{areaCountryId}-->
        <!--            </foreach>-->
        <!--        </if>-->
        -- 父代理
        <if test="agentDto.fkParentAgentId !=null and agentDto.fkParentAgentId !=''">
            and a.fk_parent_agent_id =#{agentDto.fkParentAgentId}
        </if>
        -- 国家查询
        <if test="agentDto.fkAreaCountryId !=null and agentDto.fkAreaCountryId !=''">
            and a.fk_area_country_id =#{agentDto.fkAreaCountryId}
        </if>
        -- 州省查询
        <if test="agentDto.fkAreaStateId !=null and agentDto.fkAreaStateId !=''">
            and a.fk_area_state_id =#{agentDto.fkAreaStateId}
        </if>
        -- 城市查询
        <if test="agentDto.fkAreaCityId !=null and agentDto.fkAreaCityId !=''">
            and a.fk_area_city_id =#{agentDto.fkAreaCityId}
        </if>
        -- 是否激活查询
        <if test="agentDto.isActive !=null">
            and a.is_active =#{agentDto.isActive}
        </if>
        -- 是否关键代理查询
        <if test="agentDto.isKeyAgent !=null">
            and a.is_key_agent =#{agentDto.isKeyAgent}
        </if>
        <if test="agentDto.natureList != null and agentDto.natureList.size()>0">
            and a.nature in
            <foreach collection="agentDto.natureList" item="nature" open="(" separator="," close=")">
                #{nature}
            </foreach>
        </if>
        -- 查询条件-编号/名称关键字/名字备注/原公司
        <if test="agentDto.keyWord !=null and agentDto.keyWord !=''">
            and (a.num like concat('%',#{agentDto.keyWord},'%')
            or a.name like concat('%',#{agentDto.keyWord},'%')
            or a.name_note like concat('%',#{agentDto.keyWord},'%'))
        </if>
<!--        <if test="agentDto.labelTypeId != null">-->
<!--            AND b2.id = #{agentDto.labelTypeId}-->
<!--        </if>-->

<!--        <if test="agentDto.labelRemark != null">-->
<!--            AND b1.remark LIKE CONCAT('%', #{agentDto.labelRemark}, '%')-->
<!--        </if>-->

<!--        <if test="agentDto.labelEmail != null">-->
<!--            AND a.email LIKE CONCAT('%', #{agentDto.labelEmail}, '%')-->
<!--        </if>-->
        order by a.gmt_create desc,a.is_active desc
    </select>

    <select id="getAgentByIds" resultType="com.get.salecenter.entity.Agent">
        select *
        from ais_sale_center.m_agent
        <where>
            <if test="ids!=null and ids.size()>0">
                id IN
                <foreach collection="ids" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>

    </select>
    <select id="getAgentIdByPayablePlanId" resultType="java.lang.Long">
        SELECT
	        m.id
        FROM
	        m_agent m
        INNER JOIN m_student_offer_item f ON f.fk_agent_id = m.id
        WHERE
	        f.id = #{id}
    </select>
    <select id="getAgentListByName" resultType="com.get.salecenter.vo.AgentSubVo">
        SELECT DISTINCT concat(if(a.name_note='',a.name,concat_ws(',',a.name,a.name_note)),'（',a.num,'）') as fullName,a.name,a.id
        FROM ais_sale_center.m_agent a
        INNER JOIN r_agent_company d on a.id = d.fk_agent_id
        LEFT JOIN ais_permission_center.m_company e ON d.fk_company_id=e.id
        <if test="value == 1">
            INNER JOIN (
                <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.agentPermissionSql"/>
            ) z ON a.id=z.id
        </if>
        where 1=1
        -- 公司
        <if test="companyIds !=null and companyIds.size()>0 ">
            and d.fk_company_id in
            <foreach collection="companyIds" item="fkCompanyId" index ="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="agentName !=null and agentName !=''">
            and (a.name like concat('%',#{agentName},'%') or a.num like concat('%',#{agentName},'%') or a.name_note like concat('%',#{agentName},'%') or e.short_name like concat('%',#{agentName},'%') or a.num = #{agentName})
        </if>
        GROUP BY a.id
        limit 20
    </select>
    <select id="getIaeAgentById" resultType="com.get.salecenter.entity.Agent">
        SELECT
            a.*
        FROM
            m_agent a
--         INNER JOIN r_agent_company c ON a.id = c.fk_agent_id
        WHERE
--             c.fk_company_id = 3
--         AND
            a.id = #{agentId}
    </select>
    <select id="getAgentByTargetName" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            id,
            `name`,
            num
        FROM
            m_agent
        WHERE
            is_active = 1
            <if test="targetName!=null and targetName!=''">
               AND name LIKE CONCAT('%',#{targetName},'%')
            </if>
        LIMIT 20
    </select>
    <select id="getAgentListNew" resultType="com.get.salecenter.entity.Agent">
        select a.* from m_agent a

        <if test="value == 1">
        INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.agentPermissionSql"/>
        ) z ON a.id=z.id
        </if>
        where a.is_active = 1
        <if test="agentIdList !=null and agentIdList.size()>0 ">
            and a.id in
            <foreach collection="agentIdList" item="id" index ="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by a.name
    </select>
    <select id="getSourceListAll" resultType="com.get.salecenter.vo.AgentSourceVo">
        select * from(
        SELECT a.id as fkAgentIds, a.num,a.`name` agName,
        CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）"))) as agentName,
        a.name_note,
        IFNULL(d.dp_score,0) admittedscore,
        IFNULL(e.kpi_score,0) kpiscore,
        (IFNULL(d.dp_score,0)+IFNULL(e.kpi_score,0)) ttlscore,
        IFNULL(f.no_dp_count,0) noadmittedcount,g.bdnames
        FROM (SELECT a.* FROM m_agent a LEFT JOIN r_agent_company b ON a.id=b.fk_agent_id WHERE b.fk_company_id=2) a
        LEFT JOIN (
        SELECT a.fk_agent_id, COUNT(a.fk_student_id)*3 dp_score FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM m_student_offer_item a
        LEFT JOIN (
        SELECT a.fk_student_offer_item_id, MIN(a.gmt_create) dp_time FROM r_student_offer_item_step a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
        WHERE b.step_key IN('STEP_DEPOSIT_PAID','STEP_OFFER_SELECTION')
        GROUP BY a.fk_student_offer_item_id
        ) c ON a.id=c.fk_student_offer_item_id
        WHERE a.`status`=1  AND a.is_follow=0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0)!=1
        </if>
        AND c.dp_time>=#{beginTime} AND c.dp_time &lt;=#{endTime}
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) d ON a.id=d.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, COUNT(a.fk_student_id)*3 kpi_score FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM m_student_offer_item a
        LEFT JOIN (
        SELECT a.fk_student_offer_item_id, MIN(a.gmt_create) dp_time FROM r_student_offer_item_step a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
        WHERE b.step_key IN('STEP_DEPOSIT_PAID','STEP_OFFER_SELECTION')
        GROUP BY a.fk_student_offer_item_id
        ) c ON a.id=c.fk_student_offer_item_id
        LEFT JOIN ais_institution_center.m_institution d ON a.fk_institution_id=d.id
        WHERE a.`status`=1 AND a.is_follow=0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0)!=1
        </if>
        AND c.dp_time>=#{beginTime} AND c.dp_time &lt;=#{endTime}
        AND d.is_kpi=1
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) e ON a.id=e.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, COUNT(a.fk_student_id) no_dp_count FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM m_student_offer_item a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
        LEFT JOIN (
        SELECT a.fk_student_offer_item_id, b.defer_entrance_time
        FROM (SELECT a.fk_student_offer_item_id, MAX( a.id ) as id FROM m_student_offer_item_defer_entrance_time a GROUP BY a.fk_student_offer_item_id) a
        INNER JOIN m_student_offer_item_defer_entrance_time b on a.id = b.id
        ) c ON a.id=c.fk_student_offer_item_id
        WHERE a.`status`=1  AND a.is_follow=0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0)!=1
        </if>
        AND b.step_key IN('STEP_NEW_APP','STEP_SUBMITTED','STEP_NOTIFIED','STEP_REGISTERED','STEP_APP_RECEIVED','STEP_ADMITTED')
        AND a.defer_opening_time &gt;= #{opening_start_time}
        AND a.defer_opening_time &lt;= #{opening_end_time}
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) f ON a.id=f.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, GROUP_CONCAT(b.`name`, '（', b.name_en, '）') bdnames
        FROM ais_sale_center.r_agent_staff a
        LEFT JOIN ais_permission_center.m_staff b ON a.fk_staff_id=b.id
        WHERE a.is_active=1
        GROUP BY a.fk_agent_id
        ) g ON a.id=g.fk_agent_id
        WHERE 1=1
        --     and b.fk_agent_id IS NOT NULL

        ORDER BY (IFNULL(d.dp_score,0)+IFNULL(e.kpi_score,0)) DESC
        )aa where 1=1 and aa.ttlscore!=0
    </select>

    <select id="geUnsignedNumByBdIds" resultType="com.get.salecenter.vo.UnsignedVo">
        SELECT
        ms.id AS bdId,
        COUNT(DISTINCT CASE WHEN contractFlag.id IS NULL THEN ma.id END ) AS unsignedNum
        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.unsignedNum"/>
        GROUP BY ms.id
    </select>
<!--    <select id="getIssueAgenInfo" resultType="java.lang.Long" parameterType="java.lang.Long">
        SELECT
            id
        FROM
            ais_app_issue_center.r_user_agent
        WHERE
            fk_agent_id = #{id}
    </select>-->
    <select id="getAgentListAll" resultType="com.get.salecenter.vo.AgentVo">
        SELECT concat(a.num,'-',a.name) as fkAgentNameNumStr,a.name,a.id,a.num
        FROM ais_sale_center.m_agent a -- #FORCE index(index_gmt_create)
--         INNER JOIN r_agent_company d on a.id = d.fk_agent_id
--         LEFT JOIN ais_permission_center.m_company e ON d.fk_company_id=e.id
        INNER JOIN (
            <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.agentPermissionSql"/>
        ) z ON a.id=z.id
        where 1=1
        <!-- 公司 -->
        <if test="companyIds !=null and companyIds.size()>0 ">
            AND EXISTS (
            SELECT 1 FROM ais_sale_center.r_agent_company d
            where a.id = d.fk_agent_id
            AND d.fk_company_id in
            <foreach collection="companyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
            )
        </if>
<!--        &#45;&#45; 公司-->
<!--        <if test="companyIds !=null and companyIds.size()>0 ">-->
<!--            and d.fk_company_id in-->
<!--            <foreach collection="companyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">-->
<!--                #{fkCompanyId}-->
<!--            </foreach>-->
<!--        </if>-->
        order by a.name
    </select>
    <select id="getSource2025" resultType="com.get.salecenter.vo.AgentSourceVo">

        select * from(
        SELECT a.id as fkAgentIds, a.num,a.`name` agName,
        CONCAT(a.`name`,IF(a.name_note is null or a.name_note = "","",CONCAT("（",a.name_note,"）"))) as agentName,
        a.name_note,
        IFNULL(d.kpiScoreThree,0) kpiScoreThree,
        IFNULL(d.kpiScoreTwo,0) kpiScoreTwo,
        IFNULL(d.kpiScoreOne,0) kpiScoreOne,
        IFNULL(d.apiSum,0) as ttlScore,
        IFNULL(f.no_dp_count,0) noadmittedcount,g.bdnames
        FROM (SELECT a.* FROM m_agent a LEFT JOIN r_agent_company b ON a.id=b.fk_agent_id WHERE b.fk_company_id=2) a
        INNER JOIN (
        SELECT DISTINCT fk_agent_id FROM r_agent_staff WHERE is_active=1
        <if test="staffFollowerIds != null and staffFollowerIds.size()>0">
            AND fk_staff_id IN
            <foreach collection="staffFollowerIds" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        ) b ON a.id=b.fk_agent_id
        LEFT JOIN (
        SELECT
        a2.fk_agent_id,
        SUM( CASE WHEN a2.score = 4 THEN 4 ELSE 0 END ) AS kpiScorefour,
        SUM( CASE WHEN a2.score = 3 THEN 3 ELSE 0 END ) AS kpiScoreThree,
        SUM( CASE WHEN a2.score = 2 THEN 2 ELSE 0 END ) AS kpiScoreTwo,
        SUM( CASE WHEN a2.score = 1 THEN 1 ELSE 0 END ) AS kpiScoreOne,
        SUM( a2.score ) AS apiSum
        FROM
        (
        SELECT
        a1.*,
        b1.type,
        CASE

        WHEN b1.type = 1
        AND ukip.score IS NOT NULL
        AND a1.fk_parent_student_offer_item_id IS NULL THEN
        ukip.score
        WHEN b1.type = 1
        AND ukip.score IS NULL THEN
        2 ELSE 1
        END AS score
        FROM
        m_student_offer_item a1
        INNER JOIN (-- 获取符合条件的所有方案并分类积分条件，如果积分是2的默认积分是1分，否则是2
        SELECT
        a.fk_student_offer_item_id,
        MIN( a.gmt_create ) dp_time,
        1 AS type
        FROM
        r_student_offer_item_step a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id = b.id
        WHERE
        b.step_key = 'STEP_DEPOSIT_PAID'
        AND a.gmt_create &gt;= #{opening_start_time}
        AND a.gmt_create &lt;= #{opening_start_time}
        GROUP BY
        a.fk_student_offer_item_id UNION ALL-- 获取缴费积分是1分的kpi
        SELECT
        a.fk_student_offer_item_id,
        MIN( a.gmt_create ) AS dp_time,
        2 AS type
        FROM
        r_student_offer_item_step a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id = b.id
        WHERE
        b.step_key = 'STEP_NEW_APP'
        AND NOT EXISTS ( SELECT 1 FROM u_student_offer_item_step c WHERE c.id = a.fk_student_offer_item_id AND c.step_key = 'STEP_DEPOSIT_PAID' )
        AND a.gmt_create &gt;= #{opening_start_time}
        AND a.gmt_create &lt;= #{opening_start_time}
        GROUP BY
        a.fk_student_offer_item_id
        ) b1 ON a1.id = b1.fk_student_offer_item_id
        LEFT JOIN u_kpi_institution_provider ukip ON ukip.fk_institution_provider_id = a1.fk_institution_provider_id
        ) a2
        GROUP BY
        a2.fk_agent_id
        ) d ON a.id=d.fk_agent_id

        LEFT JOIN (
        SELECT a.fk_agent_id, COUNT(a.fk_student_id) no_dp_count FROM (
        SELECT a.fk_agent_id, a.fk_student_id
        FROM m_student_offer_item a
        LEFT JOIN u_student_offer_item_step b ON a.fk_student_offer_item_step_id=b.id
        LEFT JOIN (
        SELECT a.fk_student_offer_item_id, b.defer_entrance_time
        FROM (SELECT a.fk_student_offer_item_id, MAX( a.id ) as id FROM m_student_offer_item_defer_entrance_time a GROUP BY a.fk_student_offer_item_id) a
        INNER JOIN m_student_offer_item_defer_entrance_time b on a.id = b.id
        ) c ON a.id=c.fk_student_offer_item_id
        WHERE a.`status`=1  AND a.is_follow=0
        <if test="!isStudentOfferItemFinancialHiding">
            AND IFNULL(a.is_follow_hidden, 0)!=1
        </if>
        AND b.step_key IN('STEP_NEW_APP','STEP_SUBMITTED','STEP_NOTIFIED','STEP_REGISTERED','STEP_APP_RECEIVED','STEP_ADMITTED')
        AND a.defer_opening_time &gt;= #{opening_start_time}
        AND a.defer_opening_time &lt;= #{opening_end_time}
        GROUP BY a.fk_agent_id, a.fk_student_id
        ) a
        GROUP BY a.fk_agent_id
        ) f ON a.id=f.fk_agent_id
        LEFT JOIN (
        SELECT a.fk_agent_id, GROUP_CONCAT(b.`name`, '（', b.name_en, '）') bdnames
        FROM ais_sale_center.r_agent_staff a
        LEFT JOIN ais_permission_center.m_staff b ON a.fk_staff_id=b.id
        WHERE a.is_active=1
        GROUP BY a.fk_agent_id
        ) g ON a.id=g.fk_agent_id
        WHERE 1=1
        and b.fk_agent_id IS NOT NULL
        <if test='state =="1"'>
            and a.id=#{bms_id}
        </if>
        <if test='state =="2"'>
            and a.id_gea=#{cpp_id}
        </if>
        <if test="agentName !=null and agentName!=''">
            <!--            <and  a.name like concat('%',#{agentName},'%')>-->
            HAVING agentName like concat('%',#{agentName},'%')
        </if>
        ORDER BY ttlScore DESC
        )aa where 1=1 and aa.ttlScore!=0


    </select>

    <select id="getAgentIdByEmail" resultType="java.lang.Long">
        select a.id from m_agent a left join s_contact_person p on a.id = p.fk_table_id where p.email like concat("%",#{email},"%")
    </select>


    <select id="getAgentCommissionTypeAndAgentIsBind" resultType="com.get.salecenter.vo.AgentVo">
        SELECT
        a.id AS fkAgentId,
        a.fk_parent_agent_id,
        a.fk_area_country_id,
        a.fk_area_state_id,
        a.fk_area_city_id,
        a.num,
        a.name,
        a.name_note,
        a.personal_name,
        a.nick_name,
        a.nature,
        a.nature_note,
        a.legal_person,
        a.tax_code,
        a.id_card_num,
        a.address,
        a.remark,
        a.invitation_code,
        a.is_settlement_port,
        a.is_key_agent,
        a.key_agent_failure_time,
        a.is_reject_email,
        a.is_customer_channel,
        a.is_active,
        a.id_gea,
        a.id_iae,
        a.gmt_create,
        a.gmt_create_user,
        a.gmt_modified,
        a.gmt_modified_user,
        racta.id
<!--        m.id AS bdCode,-->
<!--        m.name AS bdName,-->
<!--        m.fk_company_id-->
        FROM ais_sale_center.m_agent a
        INNER JOIN (
        SELECT DISTINCT b.id
        FROM (
        SELECT b.id
        FROM ais_sale_center.m_agent b
        INNER JOIN (
        <include refid="com.get.salecenter.dao.sale.SaleCommonSqlTemplate.agentPermissionSql"/>
        ) z ON b.id=z.id
        ) b
        ) c on a.id = c.id

--         INNER JOIN r_agent_company d on a.id = d.fk_agent_id

        -- bd编号
        INNER JOIN r_agent_staff e on e.fk_agent_id = a.id and e.is_active = 1
        INNER JOIN ais_permission_center.m_staff m ON e.fk_staff_id=m.id
        INNER JOIN r_staff_bd_code f on f.fk_staff_id = e.fk_staff_id

        LEFT JOIN ais_pmp2_center.r_agent_commission_type_agent AS racta ON racta.fk_agent_id = a.id

        where 1=1
       <if test="agentCommissionTypeAgentDto.fkAgentCommissionTypeId !=null and agentCommissionTypeAgentDto.fkAgentCommissionTypeId !=''">
           <if test="agentCommissionTypeAgentDto.isBind == 0">
               AND NOT EXISTS (SELECT 1 FROM ais_pmp2_center.r_agent_commission_type_agent AS racta WHERE racta.fk_agent_id = a.id )
           </if>
           <if test="agentCommissionTypeAgentDto.isBind == 1">
               AND EXISTS (SELECT 1 FROM ais_pmp2_center.r_agent_commission_type_agent AS racta WHERE racta.fk_agent_id = a.id AND racta.fk_agent_commission_type_id = #{agentCommissionTypeAgentDto.fkAgentCommissionTypeId})
           </if>
       </if>
        <!-- 公司权限 -->
        AND EXISTS (
            SELECT 1 FROM ais_sale_center.r_agent_company d
            where a.id = d.fk_agent_id
            AND d.fk_company_id in
        <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">
            #{companyId}
        </foreach>
        -- 公司
        <if test="agentCommissionTypeAgentDto.fkCompanyId !=null and agentCommissionTypeAgentDto.fkCompanyId !=''">
            and d.fk_company_id =#{agentCommissionTypeAgentDto.fkCompanyId}
        </if>
        )
<!--        &lt;!&ndash; 公司权限 &ndash;&gt;-->
<!--        and d.fk_company_id in-->
<!--        <foreach collection="companyIds" item="companyId" index="index" open="(" separator="," close=")">-->
<!--            #{companyId}-->
<!--        </foreach>-->
<!--        &#45;&#45; 公司-->
<!--        <if test="agentCommissionTypeAgentDto.fkCompanyId !=null and agentCommissionTypeAgentDto.fkCompanyId !=''">-->
<!--            and d.fk_company_id =#{agentCommissionTypeAgentDto.fkCompanyId}-->
<!--        </if>-->

        -- 大区
        <if test="agentCommissionTypeAgentDto.fkAreaRegionId !=null and agentCommissionTypeAgentDto.fkAreaRegionId !=''">
            and FIND_IN_SET(#{agentCommissionTypeAgentDto.fkAreaRegionId},f.fk_area_region_id)
        </if>
        -- 国家查询
        <if test="agentCommissionTypeAgentDto.fkAreaCountryId !=null and agentCommissionTypeAgentDto.fkAreaCountryId !=''">
            and a.fk_area_country_id =#{agentCommissionTypeAgentDto.fkAreaCountryId}
        </if>
        -- 州省查询
        <if test="agentCommissionTypeAgentDto.fkAreaStateId !=null and agentCommissionTypeAgentDto.fkAreaStateId !=''">
            and a.fk_area_state_id =#{agentCommissionTypeAgentDto.fkAreaStateId}
        </if>
        -- 城市查询
        <if test="agentCommissionTypeAgentDto.fkAreaCityId !=null and agentCommissionTypeAgentDto.fkAreaCityId !=''">
            and a.fk_area_city_id =#{agentCommissionTypeAgentDto.fkAreaCityId}
        </if>

        -- 查询条件-编号/名称关键字/名字备注/原公司
        <if test="agentCommissionTypeAgentDto.keyWord !=null and agentCommissionTypeAgentDto.keyWord !=''">
            and (a.num like concat('%',#{agentCommissionTypeAgentDto.keyWord},'%')
            or a.name like concat('%',#{agentCommissionTypeAgentDto.keyWord},'%')
            or a.name_note like concat('%',#{agentCommissionTypeAgentDto.keyWord},'%')
            or a.remark like concat('%',#{agentCommissionTypeAgentDto.keyWord},'%')
            or a.nature_note like concat('%',#{agentCommissionTypeAgentDto.keyWord},'%')
            )
        </if>

        GROUP BY  a.id
<!--        m.id,-->
<!--        m.name,-->
<!--        m.fk_company_id-->
        order by a.gmt_create desc,a.is_active desc
    </select>
</mapper>