<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.PayablePlanMapper">
    <select id="getCompanyIdByPlanId" resultType="java.lang.Long">
        SELECT fk_company_id from m_student where id in(
            SELECT o.fk_student_id FROM  m_student_offer_item i
                                             left join m_student_offer o on i.fk_student_offer_id=o.id
            where i.id=#{planId})
    </select>

    <select id="getCompanyIdByInsuranceTargetId" resultType="java.lang.Long">
        select s.fk_company_id from  m_student_insurance i
                                         left join m_student s on i.fk_student_id = s.id
        where i.id = #{fkTypeTargetId}
    </select>

    <select id="getCompanyIdByAccommodationTargetId" resultType="java.lang.Long">
        select s.fk_company_id from m_student_accommodation a
                                        left join m_student s on a.fk_student_id = s.id
        where a.id = #{fkTypeTargetId}
    </select>

    <select id="getPlanIdByCompanyId" resultType="java.lang.Long">
        SELECT r.id from m_payable_plan r
                             left join m_student_offer_item i
                                       on r.fk_type_target_id=i.id
        where i.fk_student_offer_id in(
            SELECT o.id FROM  m_student_offer o
                                  left join m_student s on o.fk_student_id =s.id
            where s.fk_company_id=#{companyId}
            <if test="studentId !=null">
                and i.fk_student_id =#{studentId}
            </if>
        )
          and r.fk_type_key='m_student_offer_item'
    </select>
    <select id="getStudentInsurancePlanIds" resultType="java.lang.Long">
        SELECT r.id from m_payable_plan r
                             left join m_student_insurance i on r.fk_type_target_id=i.id
                             left join m_student s on i.fk_student_id = s.id
        where  r.fk_type_key='m_student_insurance'
          and s.fk_company_id=#{companyId}
        <if test="studentId !=null">
            and i.fk_student_id =#{studentId}
        </if>
    </select>
    <select id="getStudentAccommodationIds" resultType="java.lang.Long">
        SELECT r.id from m_payable_plan r
                             left join m_student_accommodation a on r.fk_type_target_id=a.id
                             left join m_student s on a.fk_student_id =s.id
        where r.fk_type_key='m_student_accommodation'
          and s.fk_company_id=#{companyId}
        <if test="studentId !=null">
            and a.fk_student_id =#{studentId}
        </if>
    </select>

    <select id="getPlanIdByStudentName" resultType="java.lang.Long">
        SELECT r.id from m_payable_plan r
                             left join m_student_offer_item i on r.fk_type_target_id=i.id
        where i.fk_student_offer_id in(
            SELECT o.id FROM  m_student_offer o
                                  left join m_student s on o.fk_student_id =s.id
            where position(#{studentName} in s.name)
        )
          and r.fk_type_key='m_student_offer_item'
    </select>

    <select id="getPlanIdByAgentId" resultType="java.lang.Long">
        SELECT r.id from m_payable_plan r
        left join m_student_offer_item i on r.fk_type_target_id=i.id
        where i.fk_student_offer_id in(
        SELECT o.id FROM m_student_offer o
        where o.fk_agent_id in
        <foreach collection="agentIds" item="agentId" index="index" open="(" separator="," close=")">
            #{agentId}
        </foreach>
        )
        and r.fk_type_key='m_student_offer_item'
    </select>

    <select id="getPlanIdByInstitutionId" resultType="java.lang.Long">
        SELECT r.id from m_payable_plan r
        left join m_student_offer_item i on r.fk_type_target_id=i.id
        where i.fk_institution_id in
        <foreach collection="institutionIds" item="institutionId" index="index" open="(" separator="," close=")">
            #{institutionId}
        </foreach>
        and r.fk_type_key='m_student_offer_item'
    </select>

    <select id="getPlanIdByCourseId" resultType="java.lang.Long">
        SELECT r.id from m_payable_plan r
        left join m_student_offer_item i on r.fk_type_target_id=i.id
        where i.fk_institution_course_id in
        <foreach collection="courseIds" item="courseId" index="index" open="(" separator="," close=")">
            #{courseId}
        </foreach>
        and r.fk_type_key='m_student_offer_item'
    </select>

    <select id="getAgentPaySumDatas" resultType="com.get.salecenter.vo.AgentPaySumVo">
        SELECT a.fk_company_id AS fkCompanyId, a.company_short_name AS fkCompanyName,
        a.fk_agent_id AS agentId, a.agent_name AS agentName,a.name_note AS nameNote,
        a.fk_currency_type_num AS fkCurrencyTypeNum, a.currency_type_name AS fkCurrencyTypeName,
        COUNT(a.fk_payable_plan_id) AS compleApplyCount,
        SUM(a.payable_amount) AS payableAmount, SUM(a.actual_payable_amount) AS amountPayable, SUM(a.diff_payable_amount) differPay,
        SUM(a.settlement_actual_payable_amount) AS settlementAmountPayable,
        CASE
        WHEN SUM(a.payable_amount)+ SUM(a.diff_payable_amount) =0.00 THEN 0
        WHEN  SUM(a.diff_payable_amount) =0.00 THEN 2
        ELSE 1 END as payableStatus
        FROM (
        SELECT
        a.id fk_payable_plan_id,
        g.id fk_company_id, g.short_name company_short_name,
        h.id fk_agent_id, h.`name` agent_name,h.name_note,
        a.fk_type_target_id fk_student_offer_item_id, d.gmt_create student_offer_item_create_time,
        a.fk_currency_type_num, c.type_name currency_type_name,
        IFNULL(a.payable_amount,0) payable_amount,
        IFNULL(b.sum_amount_payable,0) sum_amount_payable,
        IFNULL(b.sum_amount_exchange_rate,0) sum_amount_exchange_rate,
        IFNULL(b.sum_amount_hkd,0) sum_amount_hkd,
        IFNULL(b.sum_amount_rmb,0) sum_amount_rmb,
        (IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0)) actual_payable_amount,
        (IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0) - IFNULL(a.payable_amount,0)) diff_payable_amount,
        (IFNULL(i.sum_amount_payable,0) + IFNULL(i.sum_amount_exchange_rate,0)) settlement_actual_payable_amount
        FROM ais_sale_center.m_payable_plan a
        LEFT JOIN (
        SELECT a.fk_payable_plan_id,
        SUM(IFNULL(a.amount_payable,0)) sum_amount_payable,
        SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd,0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb,0)) sum_amount_rmb
        FROM ais_finance_center.m_payment_form_item a
        LEFT JOIN ais_finance_center.m_payment_form b ON a.fk_payment_form_id=b.id
        WHERE b.`status`!=0
        GROUP BY a.fk_payable_plan_id
        ) b ON a.id=b.fk_payable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
        LEFT JOIN ais_sale_center.m_student_offer_item d ON a.fk_type_target_id=d.id
        LEFT JOIN ais_sale_center.m_student_offer e ON d.fk_student_offer_id=e.id
        LEFT JOIN ais_sale_center.m_student f ON e.fk_student_id=f.id
        LEFT JOIN ais_permission_center.m_company g ON f.fk_company_id=g.id
        LEFT JOIN ais_sale_center.m_agent h ON e.fk_agent_id=h.id
        LEFT JOIN (
        SELECT a.fk_payable_plan_id,
        SUM(IFNULL(a.amount_payable,0)) sum_amount_payable,
        SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate
        FROM ais_finance_center.m_payment_form_item a
        LEFT JOIN ais_finance_center.m_payment_form b ON a.fk_payment_form_id=b.id
        WHERE b.`status`!=0
         <!--结算时间-->
        <if test="agentPaySumDto.formItemStartTime != null">
            AND  (
            DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{agentPaySumDto.formItemStartTime},'%Y-%m-%d')
            )
        </if>
        <if test="agentPaySumDto.formItemEndTime != null">
            AND (
            DATE_FORMAT(a.gmt_create,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{agentPaySumDto.formItemEndTime},'%Y-%m-%d')
            )
        </if>
        GROUP BY a.fk_payable_plan_id
        ) i ON a.id=i.fk_payable_plan_id
        <!-- <if test="agentPaySumDto.openingTimeStart != null or agentPaySumDto.openingTimeEnd != null">
            LEFT JOIN (
            SELECT
            b.fk_student_offer_item_id
            FROM
            (SELECT
            a.fk_student_offer_item_id,
            MAX( a.id ) as id
            FROM
            m_student_offer_item_defer_entrance_time a
            GROUP BY
            a.fk_student_offer_item_id) a
            INNER JOIN m_student_offer_item_defer_entrance_time b on a.id = b.id
            where 1=1
            <if test="agentPaySumDto.openingTimeStart != null">
                AND  DATE_FORMAT(b.defer_entrance_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{agentPaySumDto.openingTimeStart},'%Y-%m-%d')
            </if>
            <if test="agentPaySumDto.openingTimeEnd != null">
                AND DATE_FORMAT(b.defer_entrance_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{agentPaySumDto.openingTimeEnd},'%Y-%m-%d')
            </if>
            ) z1 on d.id = z1.fk_student_offer_item_id
        </if> -->
        WHERE a.fk_type_key='m_student_offer_item'
        AND a.`status`!=0
        AND d.`status`!=0
        <!--查询-学生姓名-->
        <if test="agentPaySumDto.studentName != null and agentPaySumDto.studentName !=''">
            and (f.name LIKE CONCAT('%',#{agentPaySumDto.studentName},'%') OR f.last_name LIKE CONCAT('%', #{agentPaySumDto.studentName}, '%') OR f.first_name LIKE CONCAT('%', #{agentPaySumDto.studentName}, '%'))
        </if>
        <!--查询-学校名称-->
        <if test="agentPaySumDto.fkInstitutionId != null and agentPaySumDto.fkInstitutionId !=''">
            and d.fk_institution_id = #{agentPaySumDto.fkInstitutionId}
        </if>
        <!--查询-学校提供商-->
        <if test="agentPaySumDto.fkProviderId != null and agentPaySumDto.fkProviderId !=''">
            and d.fk_institution_provider_id = #{agentPaySumDto.fkProviderId}
        </if>

        -- 开学时间
        <if test="agentPaySumDto.openingTimeStart != null">
            AND  (
            DATE_FORMAT(d.defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{agentPaySumDto.openingTimeStart},'%Y-%m-%d')
            )
        </if>
        <if test="agentPaySumDto.openingTimeEnd != null">
            AND (
            DATE_FORMAT(d.defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{agentPaySumDto.openingTimeEnd},'%Y-%m-%d')
            )
        </if>
        <if test="agentPaySumDto.formItemStartTime != null or agentPaySumDto.formItemEndTime != null">
            and i.fk_payable_plan_id is not null
        </if>
        ) a
        WHERE 1=1
        <if test="agentPaySumDto.fkCompanyId != null">
            and a.fk_company_id = #{agentPaySumDto.fkCompanyId}
        </if>
        -- 改为公司多选
        <if test="agentPaySumDto.fkCompanyIds != null and agentPaySumDto.fkCompanyIds.size() > 0">
            and a.fk_company_id in
            <foreach collection="agentPaySumDto.fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="agentPaySumDto.agentName != null and agentPaySumDto.agentName != ''">
            and (a.agent_name LIKE CONCAT('%',#{agentPaySumDto.agentName},'%') OR a.name_note LIKE CONCAT('%',#{agentPaySumDto.agentName},'%'))
        </if>
        <if test="agentPaySumDto.startTime != null">
            and DATE_FORMAT(a.student_offer_item_create_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{agentPaySumDto.startTime},'%Y-%m-%d')
        </if>
        <if test="agentPaySumDto.endTime != null">
            and DATE_FORMAT(a.student_offer_item_create_time,'%Y-%m-%d') <![CDATA[< ]]> DATE_FORMAT(#{agentPaySumDto.endTime},'%Y-%m-%d')
        </if>
        GROUP BY a.fk_company_id, a.company_short_name,
        a.fk_agent_id, a.agent_name,
        a.fk_currency_type_num, a.currency_type_name
        <if test="agentPaySumDto.payableStatus != null and agentPaySumDto.payableStatus != ''">
            HAVING  payableStatus = #{agentPaySumDto.payableStatus}
        </if>
        order by CONVERT(a.agent_name USING gbk)
    </select>
    <select id="agentPaySumDetail" resultType="com.get.salecenter.vo.PayablePlanVo">
        SELECT
        mpp.id,
        mpp.fk_type_key,
        mpp.fk_type_target_id,
        mpp.summary,
        mpp.fk_currency_type_num,
        mpp.tuition_amount,
        mpp.commission_rate,
        mpp.fixed_amount,
        mpp.bonus_amount,
        mpp.payable_amount,
        mpp.payable_plan_date,
        mpp.`status`,
        mpp.gmt_create,
        mpp.gmt_create_user,
        mpp.gmt_modified,
        mpp.gmt_modified_user
        FROM m_payable_plan AS mpp
        INNER JOIN m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id AND mpp.fk_type_key = 'm_student_offer_item' AND mpp.status != 0 AND msoi.status != 0
        INNER JOIN m_student_offer AS mso ON mso.id = msoi.fk_student_offer_id AND mso.status !=0
        INNER JOIN m_agent AS a ON a.id = mso.fk_agent_id
        <where>
            <if test="agentPaySumDetailDto.fkCurrencyTypeNum != null and agentPaySumDetailDto.fkCurrencyTypeNum != ''">
                AND mpp.fk_currency_type_num = #{agentPaySumDetailDto.fkCurrencyTypeNum}
            </if>
            <if test="agentPaySumDetailDto.fkAgentId != null">
                AND a.id = #{agentPaySumDetailDto.fkAgentId}
            </if>
            <if test="agentPaySumDetailDto.startTime != null">
                AND DATE_FORMAT(msoi.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{agentPaySumDetailDto.startTime},'%Y-%m-%d')
            </if>
            <if test="agentPaySumDetailDto.endTime != null">
                AND DATE_FORMAT(msoi.gmt_create,'%Y-%m-%d') <![CDATA[< ]]> DATE_FORMAT(#{agentPaySumDetailDto.endTime},'%Y-%m-%d')
            </if>
        </where>
    </select>
    <select id="studentPaySumDetail" resultType="com.get.salecenter.vo.PayablePlanVo">
        SELECT *,msoi.student_id FROM ais_sale_center.m_payable_plan AS mpp
        INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id AND mpp.fk_type_key = 'm_student_offer_item' AND mpp.status != 0 AND msoi.status != 0
        INNER JOIN ais_sale_center.m_student_offer AS mso ON mso.id = msoi.fk_student_offer_id AND mso.status !=0
        <where>
            <if test="studentPaySumDetailDto.fkStudentOfferId != null and studentPaySumDetailDto.fkStudentOfferId !=''">
                and msoi.id =#{studentPaySumDetailDto.fkStudentOfferId}
            </if>
            <if test="studentPaySumDetailDto.fkCurrencyTypeNum != null and studentPaySumDetailDto.fkCurrencyTypeNum !=''">
                and mpp.fk_currency_type_num =#{studentPaySumDetailDto.fkCurrencyTypeNum}
            </if>
            <if test="studentPaySumDetailDto.startTime != null">
                AND DATE_FORMAT(msoi.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentPaySumDetailDto.startTime},'%Y-%m-%d')
            </if>
            <if test="studentPaySumDetailDto.endTime != null">
                AND DATE_FORMAT(msoi.gmt_create,'%Y-%m-%d') <![CDATA[< ]]> DATE_FORMAT(#{studentPaySumDetailDto.endTime},'%Y-%m-%d')
            </if>
        </where>
    </select>
    <select id="getPayablePlansInfo" resultType="com.get.salecenter.vo.PayablePlanVo">
        SELECT a.id,a.fk_type_target_id,a.fk_type_key,
        a.fk_currency_type_num, c.type_name,
        SUM(IFNULL(a.payable_amount,0)) payable_amount, <!--#应付金额-->
        SUM(IFNULL(b.sum_amount_payable,0)) sum_amount_payable, <!--#实付折合金额（这个金额币种和应收一致，因为已经是折合了）-->
        SUM(IFNULL(b.sum_amount_exchange_rate,0)) sum_amount_exchange_rate, <!--#汇率调整-->
        SUM(IFNULL(b.sum_amount_hkd,0)) sum_amount_hkd, <!--#港币金额-->
        SUM(IFNULL(b.sum_amount_rmb,0)) sum_amount_rmb, <!--#人民币金额-->
        (SUM(IFNULL(b.sum_amount_payable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0))) actual_payable_amount, <!--#实付（折合金额+汇率调整）-->
        (SUM(IFNULL(b.sum_amount_payable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0)) - SUM(IFNULL(a.payable_amount,0))) diff_payable_amount, <!--#差额-->
        CASE
        WHEN (SUM(IFNULL(b.sum_amount_payable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0)) - SUM(IFNULL(a.payable_amount,0)))=0.00 THEN 2
        WHEN (SUM(IFNULL(b.sum_amount_payable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0)) - SUM(IFNULL(a.payable_amount,0)))>(-SUM(IFNULL(a.payable_amount,0))) THEN 1
        ELSE 0 END as payableStatus
        FROM ais_sale_center.m_payable_plan a
        LEFT JOIN (
        <!--#计算每条应付计划里累计的实付金额-->
        SELECT a.fk_payable_plan_id,
        SUM(IFNULL(a.amount_payable,0)) sum_amount_payable,
        SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd,0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb,0)) sum_amount_rmb
        FROM ais_finance_center.m_payment_form_item a
        LEFT JOIN ais_finance_center.m_payment_form b ON a.fk_payment_form_id=b.id
        WHERE b.`status`!=0 <!--#关闭的付款单不作计算-->
        GROUP BY a.fk_payable_plan_id
        ) b ON a.id=b.fk_payable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
        WHERE 1=1
        <if test="payablePlanDto.ids != null and payablePlanDto.ids.size()>0">
            and a.id in
            <foreach collection="payablePlanDto.ids" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        <if test="payablePlanDto.fkTypeKey != null and payablePlanDto.fkTypeKey !=''">
        and a.fk_type_key = #{payablePlanDto.fkTypeKey}
        </if>
        <if test="payablePlanDto.fkTypeTargetId != null and payablePlanDto.fkTypeTargetId !=''">
        and a.fk_type_target_id = #{payablePlanDto.fkTypeTargetId}
        </if>
        GROUP BY a.id,a.fk_type_target_id, a.fk_currency_type_num, c.type_name
        <if test="payablePlanDto.payableStatus != null and payablePlanDto.payableStatus !='' or payablePlanDto.payableStatus == 0">
            HAVING  payableStatus = #{payablePlanDto.payableStatus}
        </if>

    </select>





    <select id="iFileGroupByAgidAndCurrencyInfo" resultType="com.get.salecenter.vo.IFileInfoVo">
        SELECT
            max(id) id,
            max(agentid) agentid,
            max(agentName) agentName,
            max(agentAreaCountryId) agentAreaCountryId,
            max(address) address,
            max(fk_staff_id) fk_staff_id,
            max(bank_branch_name) bank_branch_name,
            max(bank_account) bank_account,
            max(bank_name) bank_name,
            max(bank_address) bank_address,
            max(accountCurrencyTypeNum) accountCurrencyTypeNum,
            max(other_code) other_code,
            max(bank_account_num) bank_account_num,
            max(swift_code) swift_code,
            max(nature) nature,
            group_concat(NAME SEPARATOR ',') NAME,
            group_concat(payable_amount SEPARATOR ';') payamount
        FROM
            (
                SELECT
                    max(id) id,
                    max(agentid) agentid,
                    max(agentName) agentName,
                    max(agentAreaCountryId) agentAreaCountryId,
                    max(address) address,
                    max(fk_staff_id) fk_staff_id,
                    max(bank_branch_name) bank_branch_name,
                    max(bank_account) bank_account,
                    max(bank_name) bank_name,
                    max(bank_address) bank_address,
                    max(accountCurrencyTypeNum) accountCurrencyTypeNum,
                    max(other_code) other_code,
                    max(bank_account_num) bank_account_num,
                    max(swift_code) swift_code,
                    max(nature) nature,
                    CONCAT_WS(
                            ',',
                            max(fk_currency_type_num),
                            SUM(payable_amount)
                        ) payable_amount,
                    group_concat(NAME SEPARATOR ',') NAME
                FROM
                    (
                        SELECT
                            mpp.id,
                            a.id AS agentid,
                            a. NAME AS agentName,
                            a.fk_area_country_id AS agentAreaCountryId,
                            a.fk_area_city_id AS agentAreaCityId,
                            a.address,
                            msoi.fk_staff_id,
                            maca.bank_branch_name,
                            maca.bank_account,
                            maca.bank_name,
                            maca.bank_address,
                            maca.fk_currency_type_num AS accountCurrencyTypeNum,
                            maca.other_code,
                            mpp.payable_amount,
                            maca.bank_account_num,
                            mpp.fk_currency_type_num,
                            s. NAME,
                            maca.swift_code,
                            a.nature
                        FROM
                            m_payable_plan AS mpp
                                LEFT JOIN m_student_offer_item AS msoi ON msoi.id = mpp.fk_type_target_id
                                AND mpp.fk_type_key = 'm_student_offer_item'
                                LEFT JOIN m_agent AS a ON a.id = msoi.fk_agent_id
                                LEFT JOIN ais_finance_center.r_payable_plan_settlement_agent_account AS rppsaa ON rppsaa.fk_payable_plan_id = mpp.id
                                LEFT JOIN m_agent_contract_account AS maca ON maca.id = rppsaa.fk_agent_contract_account_id
                                LEFT JOIN m_student AS s ON s.id = msoi.fk_student_id
                        WHERE
                            mpp.num_settlement_batch = #{numSettlementBatch})a
                GROUP BY
                    a.agentid,
                    fk_currency_type_num
            ) b
        GROUP BY
            b.agentid
    </select>

    <select id="getFkTypeTargetIdByIds" resultType="java.lang.Long">
        SELECT fk_type_target_id FROM m_payable_plan
        WHERE 1=1
        <if test="ids != null and ids.size()>0">
            and id in
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="getPayablePlanByIds" resultType="com.get.salecenter.vo.PayablePlanVo">
        SELECT * FROM m_payable_plan
        <where>
            <if test="ids != null and ids.size()>0">
                id IN
                <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>

    <select id="isExistByItemId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_payable_plan where fk_type_target_id=#{id} and fk_type_key = 'm_student_offer_item' and status!=0
    </select>

    <select id="getPlanIdByCompanyIds" resultType="java.lang.Long">
        SELECT r.id from m_payable_plan r
        left join m_student_offer_item i
        on r.fk_type_target_id=i.id
        where i.fk_student_offer_id in(
        SELECT o.id FROM  m_student_offer o
        left join m_student s on o.fk_student_id =s.id
        where 1=1
        <if test="fkCompanyIds != null and fkCompanyIds.size() > 0">
            and s.fk_company_id IN
            <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="studentId !=null">
            and i.fk_student_id =#{studentId}
        </if>
        )
        and r.fk_type_key='m_student_offer_item'

    </select>
    <select id="getStudentInsurancePlanIdsByCompanyIds" resultType="java.lang.Long">
        SELECT r.id from m_payable_plan r
        left join m_student_insurance i on r.fk_type_target_id=i.id
        left join m_student s on i.fk_student_id = s.id
        where  r.fk_type_key='m_student_insurance'
        <if test="fkCompanyIds != null and fkCompanyIds.size() > 0">
            and s.fk_company_id in
            <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="studentId !=null">
            and i.fk_student_id =#{studentId}
        </if>
    </select>
    <select id="getStudentAccommodationIdsByCompanyIds" resultType="java.lang.Long">
        SELECT r.id from m_payable_plan r
        left join m_student_accommodation a on r.fk_type_target_id=a.id
        left join m_student s on a.fk_student_id =s.id
        where r.fk_type_key='m_student_accommodation'
        <if test="fkCompanyIds != null and fkCompanyIds.size() > 0">
            and s.fk_company_id in
            <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="studentId !=null">
            and a.fk_student_id =#{studentId}
        </if>
    </select>
    <!-- <select id="getPayablePlanByNumSettlementBatch" resultType="com.get.salecenter.vo.CommissionSummaryBatchPayablePlanVo">
        SELECT mpp.*, rppsi.fk_agent_contract_account_id, rppsi.fk_currency_type_num AS agentCurrencyTypeNum
        FROM m_payable_plan AS mpp
        INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id AND rppsi.num_settlement_batch = #{numSettlementBatch}
        GROUP BY
            mpp.id
    </select> -->



    <select id="payablePlanDatas" resultType="com.get.salecenter.vo.PayablePlanNewVo">
        SELECT a.id as ids,f.duration,f.duration_type,f.app_remark,
        IFNULL(f.is_defer_entrance,false) as is_defer_entrance,
        a.fk_receivable_plan_id,
        a.fk_type_target_id,
        a.fk_company_id as fk_company_ids,
        e.short_name AS company_name,
        a.fk_type_key,
        a.tuition_amount,
        a.commission_rate,
        a.split_rate,
        a.commission_amount,
        a.fixed_amount,
        a.bonus_amount,
        a.payable_plan_date,
        IFNULL(a.is_pay_in_advance,0) is_pay_in_advance,
        a.`status`,
        a.summary,
        a.gmt_create_user,
        a.gmt_create,
        a.fk_currency_type_num, c.type_name as fk_currency_type_name,
        IFNULL(a.payable_amount,0) payable_amount,
        IFNULL(b.sum_amount_payable,0) sum_amount_payable,
        IFNULL(b.sum_amount_exchange_rate,0) sum_amount_exchange_rate,
        IFNULL(b.sum_amount_hkd,0) sum_amount_hkd,
        IFNULL(b.sum_amount_rmb,0) sum_amount_rmb,
        (IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0)) actual_payable_amount,
        (IFNULL(a.payable_amount,0) - IFNULL(b.sum_amount_payable,0) - IFNULL(b.sum_amount_exchange_rate,0)) diff_payable_amount,
        z.*,
        case when (IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0))=0 then 0
        when  ((IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0)) >0) or ((IFNULL(a.payable_amount,0) - IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0))>0) THEN 1
        when  ((IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0)) - IFNULL(a.payable_amount,0))=0 THEN 2 end payableStatus,
        LENGTH(CONCAT(z.student_first_name,z.student_last_name)) as weights,
        IF
        ( tradeStatus.tradeStatus, tradeStatus.tradeStatus, 0 ) AS tradeStatus
        FROM ais_sale_center.m_payable_plan a
        LEFT JOIN (
        <!--计算每条应付计划里累计的实付金额-->
        SELECT a.fk_payable_plan_id,
        SUM(IFNULL(a.amount_payable,0)) sum_amount_payable,
        SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd,0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb,0)) sum_amount_rmb
        FROM ais_finance_center.m_payment_form_item a
        LEFT JOIN ais_finance_center.m_payment_form b ON a.fk_payment_form_id=b.id
        WHERE b.`status`!=0
        <!--关闭的付款单不作计算-->
        GROUP BY a.fk_payable_plan_id
        ) b ON a.id=b.fk_payable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
        LEFT JOIN ais_permission_center.m_company e ON a.fk_company_id=e.id
        <!-- 计算收款状态 -->
        LEFT JOIN (
        SELECT
        mpp1.id,
        CASE
        MAX( mrp.receivable_amount ) - IFNULL(SUM( mrfi.amount_receivable ),0) - IFNULL(SUM( mrfi.amount_exchange_rate ),0)
        WHEN SUM( mrp.receivable_amount ) THEN
        0
        WHEN 0 THEN
        2 ELSE 1
        END AS tradeStatus
        FROM
        ais_sale_center.m_payable_plan AS mpp1
        INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.id = mpp1.fk_receivable_plan_id
        LEFT JOIN ais_finance_center.m_receipt_form_item AS mrfi ON mrfi.fk_receivable_plan_id = mrp.id
        LEFT JOIN ais_finance_center.m_receipt_form AS mrf ON mrf.id = mrfi.fk_receipt_form_id AND mrf.status = 1
        WHERE
        mrp.STATUS = 1
        GROUP BY
        mpp1.id
        ) tradeStatus ON tradeStatus.id = a.id
        <if test="payablePlanNewDto.fkInvoiceNums != null and payablePlanNewDto.fkInvoiceNums != ''">
            LEFT JOIN (
            SELECT a.fk_receivable_plan_id, GROUP_CONCAT(cast(a.fk_invoice_id as char)) as
            fk_invoice_ids,GROUP_CONCAT(b.num) as fk_invoice_num
            FROM ais_finance_center.r_invoice_receivable_plan a
            LEFT JOIN ais_finance_center.m_invoice b ON a.fk_invoice_id=b.id
            GROUP BY a.fk_receivable_plan_id
            ) d ON a.fk_receivable_plan_id=d.fk_receivable_plan_id
        </if>

            LEFT JOIN (
            <!--留学申请计划-->
            SELECT a.id, a.fk_type_key,
            b.fk_area_country_id AS fk_area_country_ids, concat(c.`name`,'(',c.name_chn,')') AS fk_area_country_name,
            b.fk_student_id, d.`name` AS student_name, d.first_name AS student_first_name, d.last_name AS student_last_name, DATE_FORMAT(d.birthday, '%Y-%m-%d') AS student_birthday,
            b.fk_agent_id, e.`name` AS fk_agent_name, e.name_note AS agent_name_note,
            b.fk_institution_id AS institution_id, g.`name` AS ist_or_apm_name, g.short_name AS short_name_or_apa_start_time, g.name_chn AS stu_name_passport_apa_end, g.short_name_chn AS stu_cn_apa_day,
            b.fk_institution_course_id AS fk_institution_course_id, h.`name` AS fk_course_name, h.name_chn AS fk_course_name_cn, b.opening_time AS opening_time,b.defer_opening_time,
            NULL AS bzi_name24,b.old_institution_name,b.old_institution_full_name,b.old_course_custom_name,b.is_follow,b.fk_parent_student_offer_item_id,b.fk_student_offer_id as fk_student_offer_id,b.student_id,
            g.name AS fk_institution_name,case d.gender when 0 then '女' when 1 then '男' ELSE '其他' end genderName,b.fk_staff_id,e.num AS agentNum,b.num as offerItemNum,
            IF(ucsot.type_mark is not null, CONCAT(cliCompany.short_name, '.', ucsot.type_mark), '') AS commissionMark,
            NULL AS insurantPassportNum,
            NULL AS insuranceInfo
            FROM ais_sale_center.m_payable_plan a
            INNER JOIN ais_sale_center.m_student_offer_item b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_student_offer_item'
            <!-- <if test="payablePlanNewDto.beginTime != null or payablePlanNewDto.endTime != null">
                LEFT JOIN (
                SELECT
                b.fk_student_offer_item_id
                FROM
                (SELECT
                a.fk_student_offer_item_id,
                MAX( a.id ) as id
                FROM
                m_student_offer_item_defer_entrance_time a
                GROUP BY
                a.fk_student_offer_item_id) a
                INNER JOIN m_student_offer_item_defer_entrance_time b on a.id = b.id
                where 1=1
                <if test="payablePlanNewDto.beginTime != null">
                    AND  DATE_FORMAT(b.defer_entrance_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{payablePlanNewDto.beginTime},'%Y-%m-%d')
                </if>
                <if test="payablePlanNewDto.endTime != null">
                    AND DATE_FORMAT(b.defer_entrance_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{payablePlanNewDto.endTime},'%Y-%m-%d')
                </if>
                ) z1 on b.id = z1.fk_student_offer_item_id
            </if> -->

            LEFT JOIN ais_institution_center.u_area_country c ON b.fk_area_country_id=c.id
            LEFT JOIN ais_sale_center.m_student d ON b.fk_student_id=d.id
            LEFT JOIN ais_sale_center.m_agent e ON b.fk_agent_id=e.id
            LEFT JOIN ais_institution_center.m_institution g ON b.fk_institution_id=g.id
            LEFT JOIN ais_institution_center.m_institution_course h ON b.fk_institution_course_id=h.id

            LEFT JOIN ais_sale_center.m_client AS mc ON mc.id = d.fk_client_id
            LEFT JOIN ais_permission_center.m_company AS cliCompany ON cliCompany.id = mc.fk_company_id
            LEFT JOIN (SELECT fk_client_id,MAX(fk_table_name) AS fk_table_name FROM ais_sale_center.s_client_source GROUP BY fk_client_id) AS scs ON scs.fk_client_id = mc.id
            LEFT JOIN ais_sale_center.u_client_source_type AS ucsot ON ucsot.type_key = scs.fk_table_name

            where 1=1
            -- 开学时间
            <if test="payablePlanNewDto.beginTime != null">
                AND  (
                DATE_FORMAT(b.defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{payablePlanNewDto.beginTime},'%Y-%m-%d')
                )
            </if>
            <if test="payablePlanNewDto.endTime != null">
                AND (
                DATE_FORMAT(b.defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{payablePlanNewDto.endTime},'%Y-%m-%d')
                )
            </if>
        <if test="payablePlanNewDto.beginTime == null or payablePlanNewDto.endTime == null">
            UNION ALL

        <!--留学保险-->
            SELECT a.id, a.fk_type_key,
            b.fk_area_country_id AS fk_area_country_ids, concat(c.`name`,'(',c.name_chn,')') AS fk_area_country_name,
            b.fk_student_id,
            CASE
            WHEN b.type = 1
            THEN b.insurant_name
            ELSE d.`name` END AS student_name,
            CASE
            WHEN b.type = 1
            THEN b.insurant_first_name
            ELSE d.first_name END AS student_first_name,
            CASE
            WHEN b.type = 1
            THEN b.insurant_last_name
            ELSE d.last_name END AS student_last_name, DATE_FORMAT(d.birthday, '%Y-%m-%d') AS student_birthday,
            b.fk_agent_id, e.`name` AS agent_name, e.name_note AS agent_name_note,
            NULL AS institution_id, DATE_FORMAT(b.insurance_start_time, '%Y-%m-%d') AS ist_or_apm_name, DATE_FORMAT(b.insurance_end_time, '%Y-%m-%d') AS short_name_or_apa_start_time,
            CASE
            WHEN b.type = 1
            THEN b.insurant_passport_num
            ELSE d.passport_num END AS stu_name_passport_apa_end,
            NULL AS stu_cn_apa_day,
            NULL AS fk_institution_course_id, NULL AS fk_course_name, NULL AS fk_course_name_cn, NULL AS opening_time, NULL AS defer_opening_time, NULL AS bzi_name24,NULL AS
            old_institution_name,NULL AS old_institution_full_name,NULL AS old_course_custom_name,NULL AS is_follow,NULL AS fk_parent_student_offer_item_id,NULL AS fk_student_offerId,
            NULL AS student_id,NULL AS fk_institution_name,case d.gender when 0 then '女' when 1 then '男' ELSE '其他' end genderName,b.fk_staff_id,e.num AS agentNum,NULL as offerItemNum,
            NULL AS commissionMark,
            NULL AS insurantPassportNum,
            NULL AS insuranceInfo
            FROM ais_sale_center.m_payable_plan a
            INNER JOIN ais_sale_center.m_student_insurance b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_student_insurance'
            LEFT JOIN ais_institution_center.u_area_country c ON b.fk_area_country_id=c.id
            LEFT JOIN ais_sale_center.m_student d ON b.fk_student_id=d.id
            LEFT JOIN ais_sale_center.m_agent e ON b.fk_agent_id=e.id
            UNION ALL
        <!-- 服务费 -->
            SELECT a.id, a.fk_type_key,
            b.fk_area_country_ids AS fk_area_country_ids, NULL AS fk_area_country_name,
            b.fk_student_id, d.`name` AS student_name, d.first_name AS student_first_name, d.last_name AS student_last_name, DATE_FORMAT(d.birthday, '%Y-%m-%d') AS student_birthday,
            b.fk_agent_id, g.`name` AS agent_name, g.name_note AS agent_name_note,
            NULL AS institution_id, NULL AS ist_or_apm_name, NULL AS short_name_or_apa_start_time, d.passport_num AS stu_name_passport_apa_end, NULL AS stu_cn_apa_day,
            NULL AS fk_institution_course_id, NULL AS fk_course_name, NULL AS fk_course_name_cn, NULL AS opening_time, NULL AS defer_opening_time, NULL AS bzi_name24,NULL AS
            old_institution_name,e.type_name AS old_institution_full_name,NULL AS old_course_custom_name,NULL AS is_follow,NULL AS fk_parent_student_offer_item_id,NULL AS fk_student_offerId,
            NULL AS student_id,NULL AS fk_institution_name,case d.gender when 0 then '女' when 1 then '男' ELSE '其他' end genderName,b.fk_staff_id,b.num AS agentNum,NULL as offerItemNum,
            NULL AS commissionMark,
            NULL AS insurantPassportNum,
            NULL AS insuranceInfo
            FROM ais_sale_center.m_payable_plan a
            INNER JOIN ais_sale_center.m_student_service_fee b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_student_service_fee'
            <!-- LEFT JOIN ais_institution_center.u_area_country c ON FIND_IN_SET(c.id,b.fk_area_country_ids) -->
            LEFT JOIN ais_sale_center.m_student d ON b.fk_student_id=d.id
            LEFT JOIN ais_sale_center.m_agent g ON b.fk_agent_id=g.id
            LEFT JOIN ais_sale_center.u_student_service_fee_type e ON b.fk_student_service_fee_type_id=e.id
            UNION ALL
            <!--留学住宿-->
            SELECT a.id, a.fk_type_key,
            b.fk_area_country_id AS fk_area_country_ids, concat(c.`name`,'(',c.name_chn,')') AS fk_area_country_name,
            b.fk_student_id, d.`name` AS student_name, d.first_name AS student_first_name, d.last_name AS student_last_name, DATE_FORMAT(d.birthday, '%Y-%m-%d') AS student_birthday,
            b.fk_agent_id, e.`name` AS fk_agent_name, e.name_note AS agent_name_note,
            NULL AS institution_id, b.apartment_name AS ist_or_apm_name, DATE_FORMAT(b.check_in_date, '%Y-%m-%d') AS short_name_or_apa_start_time, DATE_FORMAT(b.check_out_date, '%Y-%m-%d') AS stu_name_passport_apa_end, b.duration AS stu_cn_apa_day,
            NULL AS fk_institution_course_id, NULL AS fk_course_name, NULL AS fk_course_name_cn, NULL AS opening_time, NULL AS defer_opening_time, NULL AS bzi_name24,
            NULL AS old_institution_name,NULL AS old_institution_full_name,NULL AS old_course_custom_name,NULL AS is_follow,NULL AS fk_parent_student_offer_item_id,
            NULL AS fk_student_offerId,NULL AS student_id,NULL AS fk_institution_name,case d.gender when 0 then '女' when 1 then '男' ELSE '其他' end genderName,b.fk_staff_id,e.num AS agentNum,NULL as offerItemNum,
            NULL AS commissionMark,
            NULL AS insurantPassportNum,
            NULL AS insuranceInfo
            FROM ais_sale_center.m_payable_plan a
            INNER JOIN ais_sale_center.m_student_accommodation b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_student_accommodation'
            LEFT JOIN ais_institution_center.u_area_country c ON b.fk_area_country_id=c.id
            LEFT JOIN ais_sale_center.m_student d ON b.fk_student_id=d.id
            LEFT JOIN ais_sale_center.m_agent e ON b.fk_agent_id=e.id
            <!--学校提供商-->
            UNION ALL
            SELECT
            a.id,
            a.fk_type_key,
            NULL AS fk_area_country_ids,
            NULL AS fk_area_country_name,
            NULL AS fk_student_id,
            NULL AS student_name,
            NULL AS student_first_name,
            NULL AS student_last_name,
            NULL AS student_birthday,
            NULL AS fk_agent_id,
            NULL AS agent_name,
            NULL AS agent_name_note,
            NULL AS institution_id,
            NULL AS ist_or_apm_name,
            NULL AS short_name_or_apa_start_time,
            NULL AS stu_name_passport_apa_end,
            NULL AS stu_cn_apa_day,
            NULL AS fk_institution_course_id,
            NULL AS fk_course_name,
            NULL AS fk_course_name_cn,
            NULL AS opening_time,
            NULL AS defer_opening_time,
            NULL AS bzi_name24,
            b.`name` AS old_institution_name,
            CONCAT(
            b.name_chn,
            '（',
            b.`name`,
            '）'
            ) AS old_institution_full_name,
            NULL AS old_course_custom_name,
            NULL AS is_follow,
            NULL AS fk_parent_student_offer_item_id,
            NULL AS fk_student_offerId,
            NULL AS student_id,NULL AS fk_institution_name,
            NULL AS gender_name,NULL AS fkStaffId,NULL AS agentNum,NULL as offerItemNum,
            NULL AS commissionMark,
            NULL AS insurantPassportNum,
            NULL AS insuranceInfo
            FROM
            ais_sale_center.m_payable_plan a
            INNER JOIN ais_institution_center.m_institution_provider b ON a.fk_type_target_id = b.id
            AND a.fk_type_key = 'm_institution_provider'
            <!--业务提供商-->
            UNION ALL
            SELECT
            a.id,
            a.fk_type_key,
            NULL AS fk_area_country_ids,
            NULL AS fk_area_country_name,
            NULL AS fk_student_id,
            NULL AS student_name,
            NULL AS student_first_name,
            NULL AS student_last_name,
            NULL AS student_birthday,
            NULL AS fk_agent_id,
            NULL AS agent_name,
            NULL AS agent_name_note,
            NULL AS institution_id,
            NULL AS ist_or_apm_name,
            NULL AS short_name_or_apa_start_time,
            NULL AS stu_name_passport_apa_end,
            NULL AS stu_cn_apa_day,
            NULL AS fk_institution_course_id,
            NULL AS fk_course_name,
            NULL AS fk_course_name_cn,
            NULL AS opening_time,
            NULL AS defer_opening_time,
            NULL AS bzi_name24,
            b.`name` AS old_institution_name,
            b.name_chn AS old_institution_full_name,
            NULL AS old_course_custom_name,
            NULL AS is_follow,
            NULL AS fk_parent_student_offer_item_id,
            NULL AS fk_student_offerId,
            NULL AS student_id,NULL AS fk_institution_name,
            NULL AS gender_name,NULL AS fkStaffId,NULL AS agentNum,NULL as offerItemNum,
            NULL AS commissionMark,
            NULL AS insurantPassportNum,
            NULL AS insuranceInfo
            FROM
            ais_sale_center.m_payable_plan a
            INNER JOIN ais_sale_center.m_business_provider b ON a.fk_type_target_id = b.id
            AND a.fk_type_key = 'm_business_provider'
            <!--澳小保-->
            UNION ALL
            SELECT
            a.id,
            a.fk_type_key,
            NULL AS fk_area_country_ids,
            NULL AS fk_area_country_name,
            NULL AS fk_student_id,
            CONCAT(b.insurant_name, " (",b.insurant_first_name, " ", b.insurant_last_name, ")") AS student_name,
            NULL AS student_first_name,
            NULL AS student_last_name,
            NULL AS student_birthday,
            NULL AS fk_agent_id,
            NULL AS agent_name,
            NULL AS agent_name_note,
            NULL AS institution_id,
            NULL AS ist_or_apm_name,
            NULL AS short_name_or_apa_start_time,
            NULL AS stu_name_passport_apa_end,
            NULL AS stu_cn_apa_day,
            NULL AS fk_institution_course_id,
            NULL AS fk_course_name,
            NULL AS fk_course_name_cn,
            NULL AS opening_time,
            NULL AS defer_opening_time,
            NULL AS bzi_name24,
            NULL AS old_institution_name,
            NULL AS old_institution_full_name,
            NULL AS old_course_custom_name,
            NULL AS is_follow,
            NULL AS fk_parent_student_offer_item_id,
            NULL AS fk_student_offerId,
            NULL AS student_id,NULL AS fk_institution_name,
            NULL AS gender_name,NULL AS fkStaffId,NULL AS agentNum,NULL as offerItemNum,
            NULL AS commissionMark,
            b.insurant_passport_num AS insurantPassportNum,
            CONCAT(upt.type_name,"(",upt.type_key,")： ",b.insurance_num,", ",b.insurance_type,", ",DATE_FORMAT( b.insurance_start_time, '%Y-%m-%d' ),"至", DATE_FORMAT( b.insurance_end_time, '%Y-%m-%d' )) AS insuranceInfo
            FROM
            ais_sale_center.m_payable_plan a
            INNER JOIN app_insurance_center.m_insurance_order b ON a.fk_type_target_id = b.id AND a.fk_type_key = 'm_insurance_order'
            LEFT JOIN app_insurance_center.u_product_type AS upt ON upt.id = b.fk_product_type_id
        </if>
        ) z ON a.id=z.id
        LEFT JOIN m_student_offer_item f on f.id = a.fk_type_target_id
        WHERE 1=1
        <if test="payablePlanNewDto.tradeStatus != null">
            AND tradeStatus.tradeStatus = #{payablePlanNewDto.tradeStatus}
        </if>
        <if test="payablePlanNewDto.beginTime != null or payablePlanNewDto.endTime != null">
        AND a.fk_type_key='m_student_offer_item'
        AND z.id is not null
        AND z.defer_opening_time is not null
        </if>
        <!--导出，支持勾选导出-->
        <if test="payablePlanNewDto.exportIds != null and payablePlanNewDto.exportIds.size > 0">
            AND a.id IN
            <foreach collection="payablePlanNewDto.exportIds" item="exportId" open="(" separator="," close=")">
                #{exportId}
            </foreach>
        </if>
        AND a.`status`!=0
        <if test="payablePlanNewDto.fkTypeKey != null and payablePlanNewDto.fkTypeKey != '' ">
            AND a.fk_type_key = #{payablePlanNewDto.fkTypeKey}
        </if>
        <if test="payablePlanNewDto.fkAreaCountryIds != null and payablePlanNewDto.fkAreaCountryIds != ''">
            AND FIND_IN_SET(#{payablePlanNewDto.fkAreaCountryIds}, z.fk_area_country_ids)>0
        </if>
        <if test="payablePlanNewDto.fkStudentId != null and payablePlanNewDto.fkStudentId != ''">
            AND z.fk_student_id = #{payablePlanNewDto.fkStudentId}
        </if>
        <if test="payablePlanNewDto.studentName != null and payablePlanNewDto.studentName != ''">
            AND (
            REPLACE(CONCAT(LOWER(z.student_first_name),LOWER(z.student_last_name)),' ','') like concat('%',#{payablePlanNewDto.studentName},'%')
            OR REPLACE(CONCAT(LOWER(z.student_last_name),LOWER(z.student_first_name)),' ','') like concat('%',#{payablePlanNewDto.studentName},'%')
            OR REPLACE(LOWER(z.student_name),' ','') like concat('%',#{payablePlanNewDto.studentName},'%')
            OR REPLACE(LOWER(z.student_last_name),' ','') like concat('%',#{payablePlanNewDto.studentName},'%')
            OR REPLACE(LOWER(z.student_first_name),' ','') like concat('%',#{payablePlanNewDto.studentName},'%')
            OR z.student_birthday = #{payablePlanNewDto.studentName}
            OR REPLACE(LOWER(z.student_id),' ','') like concat('%',#{payablePlanNewDto.studentName},'%')
            ) -- 过滤学生中英文名字
        </if>
        <if test="payablePlanNewDto.agentName != null and payablePlanNewDto.agentName != ''">
            AND ( LOWER(z.fk_agent_name) LIKE CONCAT('%',#{payablePlanNewDto.agentName},'%') OR LOWER(z.agent_name_note) LIKE CONCAT('%',#{payablePlanNewDto.agentName},'%') )
        </if>
        <if test="payablePlanNewDto.bziName != null and payablePlanNewDto.bziName != ''">
            AND (LOWER(z.ist_or_apm_name) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.short_name_or_apa_start_time) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.stu_name_passport_apa_end) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.stu_cn_apa_day) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.fk_course_name) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.fk_course_name_cn) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.old_course_custom_name) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.defer_opening_time) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.bzi_name24) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%'))
        </if>
        <if test="payablePlanNewDto.bziNameSupplement != null and payablePlanNewDto.bziNameSupplement != ''">
            AND (LOWER(z.ist_or_apm_name) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.stu_name_passport_apa_end) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.stu_cn_apa_day) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.fk_course_name) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.fk_course_name_cn) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%')
            OR LOWER(z.old_course_custom_name) LIKE CONCAT('%',#{payablePlanNewDto.bziName},'%'))
        </if>
        <!-- 过滤学生佣金结算标记关键字 -->
        <if test="payablePlanNewDto.commissionMark != null and payablePlanNewDto.commissionMark != ''">
            AND LOWER(z.commissionMark) LIKE CONCAT('%',#{payablePlanNewDto.commissionMark},'%')
        </if>
        <!-- 过滤 应付金额不为0 -->
        <if test="payablePlanNewDto.payableAmountUnequalZeroFlag != null">
            <choose>
                <when test="payablePlanNewDto.payableAmountUnequalZeroFlag">
                    AND IFNULL(a.payable_amount, 0) <![CDATA[ != ]]> 0
                </when>
                <otherwise>
                    AND IFNULL(a.payable_amount, 0) = 0
                </otherwise>
            </choose>
        </if>
        <!-- IFNULL(a.is_pay_in_advance,0)=0 是否预付款-->
        <if test="payablePlanNewDto.isPayInAdvance != null">
            AND IFNULL(a.is_pay_in_advance,0)=#{payablePlanNewDto.isPayInAdvance}
        </if>
        <if test="payablePlanNewDto.fkInvoiceNums != null and payablePlanNewDto.fkInvoiceNums != ''">
            AND REPLACE(LOWER(d.fk_invoice_num),"'","_") LIKE CONCAT('%',#{payablePlanNewDto.fkInvoiceNums},'%')
        </if>
        <if test="payablePlanNewDto.payableStatus==0">
            AND (IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0))=0
        </if>
        <if test="payablePlanNewDto.payableStatus==1">
            AND (IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0)) >0
            AND (IFNULL(a.payable_amount,0) - IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0))>0
        </if>
        <if test="payablePlanNewDto.payableStatus==2">
            AND ((IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0)) - IFNULL(a.payable_amount,0))=0
        </if>
        <if test="payablePlanNewDto.payableStatus==3">
             AND ((IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0))=0 OR
             ((IFNULL(b.sum_amount_payable,0) + IFNULL(b.sum_amount_exchange_rate,0)) >0
            AND (IFNULL(a.payable_amount,0) - IFNULL(b.sum_amount_payable,0) - IFNULL(b.sum_amount_exchange_rate,0))>0)
            OR  (IFNULL(a.payable_amount,0) - IFNULL(b.sum_amount_payable,0) - IFNULL(b.sum_amount_exchange_rate,0))<![CDATA[<]]> 0
            )
        </if>
        <if test="payablePlanNewDto.fkCompanyIds != null and payablePlanNewDto.fkCompanyIds.size() > 0">
        AND a.fk_company_id in
        <foreach collection="payablePlanNewDto.fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
            #{fkCompanyId}
        </foreach>
    </if>
        <if test="payablePlanNewDto.summary != null and payablePlanNewDto.summary != ''">
            AND REPLACE(LOWER(a.summary),' ','') LIKE CONCAT('%',#{payablePlanNewDto.summary},'%')
        </if>
        <choose>
            <when test="payablePlanNewDto.studentName != null and payablePlanNewDto.studentName != ''">
                ORDER BY weights ASC
            </when>
            <otherwise>
                order by a.gmt_create desc
            </otherwise>
        </choose>
    </select>
    <select id="getPayablePlanLastId" resultType="java.lang.Long">
        select id from ais_sale_center.m_payable_plan order by id desc limit 1
    </select>
    <select id="getPaidAmountByPayablePlanId" resultType="java.math.BigDecimal">
        SELECT
            IFNULL( SUM( mpfi.amount_payable ), 0 )
        FROM
            ais_sale_center.m_payable_plan AS mpp
                LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi ON mpfi.fk_payable_plan_id = mpp.id
        WHERE mpp.id = #{id}
    </select>
    <select id="getPayablePlanByTargetsAndType" resultType="com.get.salecenter.vo.PayablePlanVo">
        SELECT 	a.id,
        a.fk_type_target_id, a.fk_currency_type_num,c.type_name fkCurrencyTypeName,
        --  #应付金额-->
        SUM(IFNULL(a.payable_amount,0)) payable_amount,
        --  #实付（折合金额+汇率调整）
        (SUM(IFNULL(b.sum_amount_payable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0))) actual_payable_amount,
        --  #差额
        (SUM(IFNULL(a.payable_amount,0)) - SUM(IFNULL(b.sum_amount_payable,0)) - SUM(IFNULL(b.sum_amount_exchange_rate,0)) ) diff_payable_amount
        FROM ais_sale_center.m_payable_plan AS a
        LEFT JOIN (
        -- #计算每条应付计划里累计的实付金额
        SELECT a.fk_payable_plan_id,
        SUM(IFNULL(a.amount_payment,0)) sum_amount_payable,
        SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd,0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb,0)) sum_amount_rmb
        FROM ais_finance_center.m_payment_form_item a
        LEFT JOIN ais_finance_center.m_payment_form b ON a.fk_payment_form_id=b.id
        -- #关闭的付款单不作计算-->
        WHERE b.`status`!=0
        GROUP BY a.fk_payable_plan_id
        ) b ON a.id=b.fk_payable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
        WHERE a.fk_type_key = #{fkTypeKey}
        AND a.fk_type_target_id IN
        <foreach collection="fkTypeTargetIds" item="fkTypeTargetId" open="(" separator="," close=")">
            #{fkTypeTargetId}
        </foreach>
        AND a.`status`!=0
        GROUP BY a.id,a.fk_type_target_id, a.fk_currency_type_num, c.type_name
    </select>
    <select id="getPayablePlanByCourseId" resultType="com.get.salecenter.entity.PayablePlan">
        SELECT
            p.*
        FROM
            m_payable_plan p
        INNER JOIN m_student_offer_item o ON o.id = p.fk_type_target_id
        WHERE
            o.id_gea = #{courseId}
        AND DATE_FORMAT(p.gmt_create, '%Y-%m-%d') = DATE_FORMAT(#{createTime}, '%Y-%m-%d')  and p.gmt_create_user='admin-1'
    </select>
    <select id="getOtherPlan" resultType="com.get.salecenter.vo.SaleDataTempVo">
        SELECT p.id,o.id_gea as course_id,p.payable_amount as amount,p.gmt_create as createTime from m_payable_plan p
        INNER JOIN m_student_offer_item o on o.id=p.fk_type_target_id where   p.gmt_create_user='admin-x'
    </select>

    <select id="queryPayablePlan" resultType="com.get.salecenter.entity.PayablePlan">
        SELECT p.* from m_payable_plan p INNER JOIN m_student_offer_item f on f.id=p.fk_type_target_id where f.id_gea = #{courseId}
<!--        <foreach collection="list" open="(" close=")" item="item" separator=",">-->
<!--            #{item}-->
<!--        </foreach>-->
    </select>
    <select id="queryTsPayablePlan" resultType="com.get.salecenter.entity.PayablePlan">
        SELECT p.* from m_payable_plan p INNER JOIN m_student_offer_item f on f.id=p.fk_type_target_id where f.id = #{offerId}
    </select>
    <select id="getPayablePlanByReId" resultType="com.get.salecenter.entity.PayablePlan">
        SELECT *
        FROM
        m_payable_plan
        WHERE
        fk_receivable_plan_id = #{rePlanId}
    </select>

    <select id="getPayablePlanByOfferItemId" resultType="com.get.salecenter.entity.PayablePlan">
        SELECT
            fk_company_id,
            fk_currency_type_num,
            tuition_amount,
            split_rate,
            fixed_amount,
            payable_amount,
            is_pay_in_advance,
            commission_rate,
            commission_amount,
            bonus_amount,
            summary,
            payable_plan_date
        FROM
            m_payable_plan
        WHERE
            fk_type_key = "m_student_offer_item"
        AND fk_type_target_id = #{offerItemId}
        AND status = 1
    </select>
    <select id="getDeleteSettlementPayablePlans" resultType="com.get.salecenter.entity.PayablePlan">
        SELECT mpp.* FROM m_payable_plan AS mpp
        INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi ON rppsi.fk_payable_plan_id = mpp.id
        WHERE mpp.id IN
        <foreach collection="deleteSettlementDeleteDto.fkPayablePlanIds" item="fkPayablePlanId" index="index" open="(" separator="," close=")">
            #{fkPayablePlanId}
        </foreach>

        AND (
        <choose>
            <when test="deleteSettlementDeleteDto.statusSettlement == 0">
                ( mpp.status_settlement = #{deleteSettlementDeleteDto.statusSettlement} ) OR ( mpp.status_settlement = 4
                AND rppsi.num_settlement_batch IS NOT NULL )
            </when>
            <otherwise>
                mpp.status_settlement = #{deleteSettlementDeleteDto.statusSettlement}
            </otherwise>
        </choose>
        )
        GROUP BY mpp.id
    </select>
    <select id="getCompanyIdByInsuranceTargetIds" resultType="com.get.salecenter.vo.StudentInsuranceVo">
        select i.id,s.fk_company_id from  m_student_insurance i
                                         left join m_student s on i.fk_student_id = s.id
        where 1=1
        <if test="ids !=null and ids.size()>0">
            and i.id in
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="getCompanyIdByAccommodationTargetIds"
            resultType="com.get.salecenter.vo.StudentAccommodationVo">
        select a.id,s.fk_company_id from m_student_accommodation a
                                        left join m_student s on a.fk_student_id = s.id
        where 1=1
        <if test="ids !=null and ids.size()>0">
            and a.id in
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="getCompanyIdByPlanIds" resultType="com.get.salecenter.vo.StudentOfferItemVo">
        SELECT
            a.id,b.fk_company_id
        FROM
            `m_student_offer_item` a LEFT JOIN m_student b on a.fk_student_id = b.id
        where 1=1
        <if test="ids !=null and ids.size()>0">
            and a.id in
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="getPayablePlanRemarkDetails" resultType="com.get.salecenter.vo.PlanRemarkDetailResultVo">
        SELECT
            *
        FROM
            (
                SELECT
                    a1.id,
                    c.remark scoreRemark,
                    c.education_project,
                    c.education_degree,
                    d.remark studentOfferRemark,
                    GROUP_CONCAT(IF(e.remark !='',CONCAT(f.step_name,":",e.remark),null)) stepRemarks,
                    c.id fkStudentId,
                    b.id fkStudentOfferItemId,
                    NULL as insuranceRemark,
                    NULL as accommodationRemark
                FROM
                    m_payable_plan a1
                        INNER JOIN m_student_offer_item b on a1.fk_type_key = 'm_student_offer_item' and a1.fk_type_target_id = b.id
                        LEFT JOIN m_student c on b.fk_student_id = c.id
                        LEFT JOIN m_student_offer d on d.id = b.fk_student_offer_id
                        LEFT JOIN r_student_offer_item_step e on e.fk_student_offer_item_id = b.id
                        LEFT JOIN u_student_offer_item_step f on e.fk_student_offer_item_step_id = f.id
                where 1=1
                <if test="planIds !=null and planIds.size() > 0">
                    and a1.id in
                    <foreach collection="planIds" item="planId" index="index" open="(" separator="," close=")">
                        #{planId}
                    </foreach>
                </if>
                GROUP BY a1.id
                UNION All
                SELECT
                    a2.id,
                    c.remark scoreRemark,
                    c.education_project,
                    c.education_degree,
                    NULL as studentOfferRemark,
                    NULL as stepRemarks,
                    c.id as fkStudentId,
                    NULL as fkStudentOfferItemId,
                    b.remark insuranceRemark,
                    NULL as accommodationRemark
                FROM
                    m_payable_plan a2
                        INNER JOIN m_student_insurance b on a2.fk_type_key = 'm_student_insurance' and a2.fk_type_target_id = b.id
                        LEFT JOIN m_student c on b.fk_student_id = c.id
                where 1=1
                <if test="planIds !=null and planIds.size() > 0">
                    and a2.id in
                    <foreach collection="planIds" item="planId" index="index" open="(" separator="," close=")">
                        #{planId}
                    </foreach>
                </if>
                GROUP BY a2.id
                UNION All
                SELECT
                    a3.id,
                    c.remark scoreRemark,
                    c.education_project,
                    c.education_degree,
                    NULL as studentOfferRemark,
                    NULL as stepRemarks,
                    c.id as fkStudentId,
                    NULL as fkStudentOfferItemId,
                    NULL as insuranceRemark,
                    b.remark accommodationRemark
                FROM
                    m_payable_plan a3
                        INNER JOIN m_student_accommodation b on a3.fk_type_key = 'm_student_accommodation' and a3.fk_type_target_id = b.id
                        LEFT JOIN m_student c on b.fk_student_id = c.id
                where 1=1
                <if test="planIds !=null and planIds.size() > 0">
                    and a3.id in
                    <foreach collection="planIds" item="planId" index="index" open="(" separator="," close=")">
                        #{planId}
                    </foreach>
                </if>
                GROUP BY a3.id
            ) z
    </select>
    <select id="getPayPlanTheLatestThreeTuitionFees" resultType="java.lang.String">
         SELECT tuition_amount FROM m_payable_plan WHERE fk_company_id = #{fkCompanyId} ORDER BY id DESC LIMIT 3
    </select>
    <select id="getPaidAmountInfoByIds" resultType="com.get.salecenter.vo.PublicPayFormDetailVo">
        SELECT
        <!--币种-->
        CONCAT( c.type_name, "(", c.num, "）" ) AS payableCurrencyTypeName,
        <!--实收（折合金额+汇率调整）-->
        IFNULL( i.amount_payable, 0 ) + IFNULL( i.amount_exchange_rate, 0 ) AS actualPayableAmount,
        <!--付款时间-->
        i.gmt_create as actualPayTime,
        p.id
        FROM
        ais_finance_center.m_payment_form_item i
        INNER JOIN m_payable_plan p ON p.id = i.fk_payable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON p.fk_currency_type_num=c.num
        <if test="ids!=null and ids.size>0">
            where p.id in
            <foreach collection="ids" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
    </select>
    <select id="getPayableListByAgent" resultType="com.get.salecenter.vo.PayablePlanVo">
        SELECT
            *
        FROM
            (
                SELECT
                    p.*
                FROM
                    m_payable_plan p
                INNER JOIN m_student_offer_item b ON b.id = p.fk_type_target_id
                AND p.fk_type_key = 'm_student_offer_item'
                AND b.fk_agent_id = #{targetId}
                UNION ALL
                    SELECT
                        p.*
                    FROM
                        m_payable_plan p
                    INNER JOIN m_student_insurance b ON b.id = p.fk_type_target_id
                    AND p.fk_type_key = 'm_student_insurance'
                    AND b.fk_agent_id = #{targetId}
                    UNION ALL
                        SELECT
                            p.*
                        FROM
                            m_payable_plan p
                        INNER JOIN m_student_accommodation b ON b.id = p.fk_type_target_id
                        AND p.fk_type_key = 'm_student_accommodation'
                        AND b.fk_agent_id = #{targetId}
                        UNION ALL
                            SELECT
                                p.*
                            FROM
                                m_payable_plan p
                            INNER JOIN m_student_service_fee b ON b.id = p.fk_type_target_id
                            AND p.fk_type_key = 'm_student_service_fee'
                            AND b.fk_agent_id = #{targetId}
            ) c WHERE c.status != 0 AND (c.payable_amount <![CDATA[>]]> 0 OR c.payable_amount <![CDATA[<]]> 0)
    </select>
    <select id="getAgentPayInfo" resultType="com.get.salecenter.vo.SelItem">
        SELECT
            count(m.fk_payment_form_id) as val,g.id as keyId
        FROM
            ais_finance_center.m_payment_form_item m
        INNER JOIN m_payable_plan p ON p.id = m.fk_payable_plan_id
        INNER JOIN m_student_offer_item f ON f.id = p.fk_type_target_id
        INNER JOIN m_agent g ON g.id = f.fk_agent_id
        WHERE
            p.fk_type_key = 'm_student_offer_item'
        AND g.id IN
        <foreach collection="ids" open="(" separator="," close=")" item="gid">
            #{gid}
        </foreach>
        GROUP BY g.id
        UNION ALL

        SELECT
            count(m.fk_payment_form_id) as val,g.id as keyId
        FROM
            ais_finance_center.m_payment_form_item m
        INNER JOIN m_payable_plan p ON p.id = m.fk_payable_plan_id
        INNER JOIN m_student_insurance f ON f.id = p.fk_type_target_id
        INNER JOIN m_agent g ON g.id = f.fk_agent_id
        WHERE
            p.fk_type_key = 'm_student_insurance'
        AND g.id IN
        <foreach collection="ids" open="(" separator="," close=")" item="gid">
            #{gid}
        </foreach>
        GROUP BY g.id
        UNION ALL

        SELECT
            count(m.fk_payment_form_id) as val,g.id as keyId
        FROM
            ais_finance_center.m_payment_form_item m
        INNER JOIN m_payable_plan p ON p.id = m.fk_payable_plan_id
        INNER JOIN m_student_accommodation f ON f.id = p.fk_type_target_id
        INNER JOIN m_agent g ON g.id = f.fk_agent_id
        WHERE
            p.fk_type_key = 'm_student_accommodation'
        AND g.id IN
        <foreach collection="ids" open="(" separator="," close=")" item="gid">
            #{gid}
        </foreach>
        GROUP BY g.id
        UNION ALL

        SELECT
            count(m.fk_payment_form_id) as val,g.id as keyId
        FROM
            ais_finance_center.m_payment_form_item m
        INNER JOIN m_payable_plan p ON p.id = m.fk_payable_plan_id
        INNER JOIN m_student_service_fee f ON f.id = p.fk_type_target_id
        INNER JOIN m_agent g ON g.id = f.fk_agent_id
        WHERE
            p.fk_type_key = 'm_student_service_fee'
        AND g.id IN
        <foreach collection="ids" open="(" separator="," close=")" item="gid">
            #{gid}
        </foreach>
        GROUP BY g.id
    </select>

    <select id="getReceivablePlansBySort" resultType="com.get.salecenter.vo.ReceivablePlanVo">
        SELECT
            p.*
        FROM
            m_receivable_plan p
        <if test="receiptAmountSort">
            LEFT JOIN (
            SELECT
            SUM(service_fee) + SUM(amount_receivable) + SUM(amount_exchange_rate) AS amount,
            fk_receivable_plan_id
            FROM
            ais_finance_center.m_receipt_form_item rmi
            WHERE
            rmi.fk_receipt_form_id = #{receiptFormId}
            AND fk_receivable_plan_id IN
            <foreach collection="planIds" item="pid" open="(" separator="," close=")">
                #{pid}
            </foreach>
            GROUP BY
            fk_receivable_plan_id
            ) mri ON mri.fk_receivable_plan_id = p.id
        </if>
        <if test="invoiceAmountSort">
            LEFT JOIN (
            SELECT
            SUM(IFNULL(rp.amount, 0)) AS amount,
            rp.fk_receivable_plan_id
            FROM
            ais_finance_center.r_invoice_receivable_plan rp
            INNER JOIN ais_finance_center.r_receipt_form_invoice ri ON ri.fk_invoice_id = rp.fk_invoice_id AND ri.fk_receipt_form_id = #{receiptFormId}
            WHERE
            rp.fk_receivable_plan_id IN
            <foreach collection="planIds" item="pid" open="(" separator="," close=")">
                #{pid}
            </foreach>
            GROUP BY
            rp.fk_receivable_plan_id
            ) rp ON rp.fk_receivable_plan_id = p.id
        </if>
        <if test="studentName!=null and studentName!=''">
            INNER JOIN (
            SELECT
            a.id,p.id as fk_receivable_plan_id
            FROM
            m_student a
            INNER JOIN m_student_offer_item f ON f.fk_student_id = a.id
            INNER JOIN m_receivable_plan p ON p.fk_type_target_id = f.id
            WHERE
            p.id IN
            <foreach collection="planIds" item="pid" open="(" separator="," close=")">
                #{pid}
            </foreach>
            AND (
            REPLACE(CONCAT(a.first_name,a.last_name),' ','') like concat('%',#{studentName},'%')
            OR REPLACE(CONCAT(a.last_name,a.first_name),' ','') like concat('%',#{studentName},'%')
            OR REPLACE(a.`name`,' ','') like concat('%',#{studentName},'%')
            OR REPLACE(a.last_name,' ','') like concat('%',#{studentName},'%')
            OR REPLACE(a.first_name,' ','') like concat('%',#{studentName},'%'))
            UNION
            SELECT
            a.id,p.id as fk_receivable_plan_id
            FROM
            m_student a
            INNER JOIN m_student_insurance f ON f.fk_student_id = a.id
            INNER JOIN m_receivable_plan p ON p.fk_type_target_id = f.id
            WHERE
            p.id IN
            <foreach collection="planIds" item="pid" open="(" separator="," close=")">
                #{pid}
            </foreach>
            AND (
            REPLACE(CONCAT(a.first_name,a.last_name),' ','') like concat('%',#{studentName},'%')
            OR REPLACE(CONCAT(a.last_name,a.first_name),' ','') like concat('%',#{studentName},'%')
            OR REPLACE(a.`name`,' ','') like concat('%',#{studentName},'%')
            OR REPLACE(a.last_name,' ','') like concat('%',#{studentName},'%')
            OR REPLACE(a.first_name,' ','') like concat('%',#{studentName},'%'))
            UNION
            SELECT
            a.id,p.id as fk_receivable_plan_id
            FROM
            m_student a
            INNER JOIN m_student_accommodation f ON f.fk_student_id = a.id
            INNER JOIN m_receivable_plan p ON p.fk_type_target_id = f.id
            WHERE
            p.id IN
            <foreach collection="planIds" item="pid" open="(" separator="," close=")">
                #{pid}
            </foreach>
            AND (
            REPLACE(CONCAT(a.first_name,a.last_name),' ','') like concat('%',#{studentName},'%')
            OR REPLACE(CONCAT(a.last_name,a.first_name),' ','') like concat('%',#{studentName},'%')
            OR REPLACE(a.`name`,' ','') like concat('%',#{studentName},'%')
            OR REPLACE(a.last_name,' ','') like concat('%',#{studentName},'%')
            OR REPLACE(a.first_name,' ','') like concat('%',#{studentName},'%'))
            UNION
            SELECT
            a.id,p.id as fk_receivable_plan_id
            FROM
            m_student a
            INNER JOIN m_student_service_fee f ON f.fk_student_id = a.id
            INNER JOIN m_receivable_plan p ON p.fk_type_target_id = f.id
            WHERE
            p.id IN
            <foreach collection="planIds" item="pid" open="(" separator="," close=")">
                #{pid}
            </foreach>
            AND (
            REPLACE(CONCAT(a.first_name,a.last_name),' ','') like concat('%',#{studentName},'%')
            OR REPLACE(CONCAT(a.last_name,a.first_name),' ','') like concat('%',#{studentName},'%')
            OR REPLACE(a.`name`,' ','') like concat('%',#{studentName},'%')
            OR REPLACE(a.last_name,' ','') like concat('%',#{studentName},'%')
            OR REPLACE(a.first_name,' ','') like concat('%',#{studentName},'%'))
            ) a ON a.fk_receivable_plan_id = p.id
        </if>
        WHERE
            p.id IN
            <foreach collection="planIds" item="pid" open="(" separator="," close=")">
                #{pid}
            </foreach>
        <if test="receiptAmountSort">
            ORDER BY mri.amount
            <if test="invoiceAmountSort">
                ,rp.amount
            </if>
            DESC
        </if>
        <if test="invoiceAmountSort and !receiptAmountSort">
            ORDER BY rp.amount DESC
        </if>
    </select>


</mapper>