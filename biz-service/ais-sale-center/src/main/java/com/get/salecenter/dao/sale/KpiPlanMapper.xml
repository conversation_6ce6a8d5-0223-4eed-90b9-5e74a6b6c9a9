<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.KpiPlanMapper">

    <select id="getSumTimeKpiData" resultType="java.lang.String">
        SELECT
            CONCAT(SUM(sumTime), '') AS sumTimeStr
        FROM (
            SELECT SUM(IFNULL(UNIX_TIMESTAMP(gmt_create), 0) + IFNULL(UNIX_TIMESTAMP(gmt_modified), 0)) AS sumTime
            FROM m_kpi_plan
            WHERE id = #{fkKpiPlanId}
            UNION ALL
            SELECT SUM(IFNULL(UNIX_TIMESTAMP(gmt_create), 0) + IFNULL(UNIX_TIMESTAMP(gmt_modified), 0)) AS sumTime
            FROM m_kpi_plan_group
            WHERE fk_kpi_plan_id = #{fkKpiPlanId}
            UNION ALL
            SELECT SUM(IFNULL(UNIX_TIMESTAMP(gmt_create), 0) + IFNULL(UNIX_TIMESTAMP(gmt_modified), 0)) AS sumTime
            FROM m_kpi_plan_group_item
            WHERE fk_kpi_plan_id = #{fkKpiPlanId}
            UNION ALL
            SELECT SUM(IFNULL(UNIX_TIMESTAMP(gmt_create), 0) + IFNULL(UNIX_TIMESTAMP(gmt_modified), 0)) AS sumTime
            FROM m_kpi_plan_staff
            WHERE fk_kpi_plan_id = #{fkKpiPlanId}
            UNION ALL
            SELECT SUM(IFNULL(UNIX_TIMESTAMP(b.gmt_create), 0) + IFNULL(UNIX_TIMESTAMP(b.gmt_modified), 0)) AS sumTime
            FROM m_kpi_plan_staff a
            INNER JOIN m_kpi_plan_staff_label b ON a.id = b.fk_kpi_plan_staff_id
            WHERE a.fk_kpi_plan_id = #{fkKpiPlanId}
            UNION ALL
            SELECT SUM(IFNULL(UNIX_TIMESTAMP(b.gmt_create), 0) + IFNULL(UNIX_TIMESTAMP(b.gmt_modified), 0)) AS sumTime
            FROM m_kpi_plan_group_item a
            INNER JOIN m_kpi_plan_target b ON a.id = b.fk_kpi_plan_group_item_id
            WHERE a.fk_kpi_plan_id = #{fkKpiPlanId}
        ) AS res
    </select>

</mapper>
