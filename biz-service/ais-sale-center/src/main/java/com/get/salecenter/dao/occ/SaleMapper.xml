<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.salecenter.dao.occ.SaleMapper">


    <select id="getOfferByCourseId" parameterType="String" resultType="com.get.salecenter.vo.StudentOfferItemVo">
        select * from ais_sale_center.m_student_offer_item where id_gea=#{courseId}
    </select>


    <select id="getOfferByErrorState" resultType="com.get.salecenter.vo.StudentOfferItemVo">
        select * from ais_sale_center.m_student_offer_item where id_gea in
        <foreach collection="list" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>
</mapper>
