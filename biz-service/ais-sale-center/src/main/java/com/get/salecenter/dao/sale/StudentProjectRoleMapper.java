package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.StudentProjectRoleVo;
import com.get.salecenter.entity.StudentProjectRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@DS("saledb")
@Mapper
public interface StudentProjectRoleMapper extends GetMapper<StudentProjectRole> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    @Override
    int insert(StudentProjectRole record);

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(StudentProjectRole record);

    /**
     * @return java.lang.Integer
     * @Description :该公司中最大排序值
     * @Param [fkCompanyId]
     * <AUTHOR>
     */
    Integer getMaxViewOrder(@Param("fkCompanyId") Long fkCompanyId);


    List<BaseSelectEntity> getRoleSelect(@Param("fkCompanyId") Long fkCompanyId);

    /**
     * 角色下拉
     *
     * @return
     */
    List<StudentProjectRole> getRoleCollection();

    /**
     * 部门角色下拉
     *
     * @return
     */
    List<StudentProjectRole> getDepartmentRole(@Param("fkDepartmentId") Long fkDepartmentId, @Param("fkCompanyId") Long fkCompanyId);

    /**
     * 根据key获取角色
     *
     * @param roleKey
     * @return
     */
    StudentProjectRole getRoleByKey(@Param("roleKey") String roleKey);


    /**
     * 根据key获取角色
     * @param roleKeys
     * @return
     */
    List<StudentProjectRoleVo> getRoleByKeys(@Param("roleKeys") Set<String> roleKeys);

    /**
     * 获取对应公司下有申请计划的 角色下拉
     *
     * @Date 17:39 2023/1/11
     * <AUTHOR>
     */
    List<BaseSelectEntity> getExistsOfferItemAgentList(@Param("companyId") Long companyId,
                                                       @Param("countryIds") List<Long> countryIds);

    List<BaseSelectEntity> getRoleStaffByRoleId(@Param("projectRoleKey") String projectRoleKey,
                                                @Param("departmentIdStr") String departmentIdStr,
                                                @Param("staffFollowerIds") List<Long> staffFollowerIds,
                                                @Param("companyIdStr") String companyIdStr);

    /**
     * 获取存在学习方案角色下的所属部门
     *
     * @Date 11:01 2023/4/6
     * <AUTHOR>
     */
    List<BaseSelectEntity> getRoleStaffDepartmentByRoleId(@Param("projectRoleKey") String projectRoleKey,
                                                          @Param("companyIds") List<Long> companyIds,
                                                          @Param("staffFollowerIds") List<Long> staffFollowerIds);

    List<StudentProjectRole> getRoleByKeysLike(@Param("roleList")List<String> roleList);
}