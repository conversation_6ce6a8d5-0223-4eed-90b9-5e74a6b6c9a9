package com.get.salecenter.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.ConventionExamPrizes;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@DS("conventiondb")
@Mapper
public interface ConventionExamPrizesMapper extends GetMapper<ConventionExamPrizes> {
//    List<ConventionExamPrizesVo> getConventionExamPrizesList(IPage<ConventionExamPrizesVo> iPage, ConventionExamPrizesDto conventionExamPrizesVo);

//    List<BaseSelectEntity> examinationSelectByConventionId(@Param("fkConventionId")Long fkConventionId);

//    List<ConventionExamPrizesNewVo> getConventionExamExportList(@Param("fkConventionId")Long fkConventionId);

//    Long getConventionExamPrizesLog(@Param("fkConventionId")Long fkConventionId, @Param("fkExaminationId")Long fkExaminationId, @Param("fkUserId")Long fkUserId);

    int getConventionExamScore(@Param("fkUserId")Long fkUserId, @Param("fkExaminationId")Long fkExaminationId);

    int getConventionExamScoreSum(@Param("fkUserId")Long fkUserId);

//    Long getConventionPrizesWinning(@Param("fkConventionId")Long fkConventionId, @Param("fkExaminationId")Long fkExaminationId, @Param("fkUserId")Long fkUserId);
}
