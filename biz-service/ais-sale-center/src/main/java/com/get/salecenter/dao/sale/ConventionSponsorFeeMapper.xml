<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionSponsorFeeMapper">

  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ConventionSponsorFee" keyProperty="id" useGeneratedKeys="true">
    insert into m_convention_sponsor_fee
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkConventionId != null">
        fk_convention_id,
      </if>
      <if test="typeName != null">
        type_name,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="fkCurrencyTypeNum != null">
        fk_currency_type_num,
      </if>
      <if test="fee != null">
        fee,
      </if>
      <if test="dateNote != null">
        date_note,
      </if>
      <if test="timeNote != null">
        time_note,
      </if>
      <if test="imgUrl != null">
        img_url,
      </if>
      <if test="summary != null">
        summary,
      </if>
      <if test="countLimit != null">
        count_limit,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="feeCny != null">
        fee_cny,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkConventionId != null">
        #{fkConventionId,jdbcType=BIGINT},
      </if>
      <if test="typeName != null">
        #{typeName,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="fkCurrencyTypeNum != null">
        #{fkCurrencyTypeNum,jdbcType=VARCHAR},
      </if>
      <if test="fee != null">
        #{fee,jdbcType=DECIMAL},
      </if>
      <if test="dateNote != null">
        #{dateNote,jdbcType=VARCHAR},
      </if>
      <if test="timeNote != null">
        #{timeNote,jdbcType=VARCHAR},
      </if>
      <if test="imgUrl != null">
        #{imgUrl,jdbcType=VARCHAR},
      </if>
      <if test="summary != null">
        #{summary,jdbcType=VARCHAR},
      </if>
      <if test="countLimit != null">
        #{countLimit,jdbcType=INTEGER},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="feeCny != null">
        #{feeCny,jdbcType=DECIMAL},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select
     IFNULL(max(view_order)+1,0) view_order
    from
     m_convention_sponsor_fee
    where
      fk_convention_id = #{conventionId}
  </select>
</mapper>