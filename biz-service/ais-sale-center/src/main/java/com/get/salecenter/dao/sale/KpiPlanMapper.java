package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.KpiPlan;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@Mapper
public interface KpiPlanMapper extends BaseMapper<KpiPlan> {

    /**
     * 获取KPI方案数据时间戳之和（所有涉及到KPI统计所需要的数据表）
     *
     * @param fkKpiPlanId kpi方案id
     * @return
     */
    String getSumTimeKpiData(@Param("fkKpiPlanId") Long fkKpiPlanId);
}
