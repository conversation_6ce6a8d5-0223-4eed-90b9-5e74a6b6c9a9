package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.ConventionAwardVo;
import com.get.salecenter.entity.ConventionAward;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ConventionAwardMapper extends BaseMapper<ConventionAward>, GetMapper<ConventionAward> {
    int insert(ConventionAward record);

    int insertSelective(ConventionAward record);

    /**
     * @return List
     * @Description :獎品列表
     * @Param [awardName, roles]
     * <AUTHOR>
     */
    List<ConventionAwardVo> getConventionAwards(@Param("awardName") String awardName, @Param("roles") List<String> roles, @Param("fkConventionId") Long fkConventionId);

    Integer getMaxViewOrder();

    /**
     * @return List
     * @Description :全部獎品，抽奖用的
     * @Param [awardName, roles]
     * <AUTHOR>
     */
    List<ConventionAwardVo> listAward(@Param("award_name") String award_name, @Param("fkConventionId") Long fkConventionId);

    String getRole(@Param("awardId") Long awardId);

    String getPersonId(@Param("awardId") Long awardId);
}