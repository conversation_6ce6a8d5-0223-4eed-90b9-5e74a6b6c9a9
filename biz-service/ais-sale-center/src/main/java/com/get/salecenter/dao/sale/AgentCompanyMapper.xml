<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.AgentCompanyMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.AgentCompany">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_agent_id" jdbcType="BIGINT" property="fkAgentId"/>
        <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_agent_id, fk_company_id, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
  </sql>
    <insert id="insertSelective" parameterType="com.get.salecenter.entity.AgentCompany" keyProperty="id"
            useGeneratedKeys="true">
        insert into r_agent_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkAgentId != null">
                fk_agent_id,
            </if>
            <if test="fkCompanyId != null">
                fk_company_id,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkAgentId != null">
                #{fkAgentId,jdbcType=BIGINT},
            </if>
            <if test="fkCompanyId != null">
                #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getAgentCompanyIdByContractId" resultType="java.lang.Long">
        SELECT MIN(a.fk_company_id) from r_agent_company a
        left join m_agent_contract m on m.fk_agent_id=a.fk_agent_id
        where m.id=#{contractId}
    </select>

    <select id="getAgentCompanyIdById" resultType="java.lang.Integer">
        SELECT MIN(a.fk_company_id) from r_agent_company a
        INNER join m_agent m on m.id=a.fk_agent_id
        where m.id=#{id}
    </select>

    <select id="getAgentCompanyName" resultType="com.get.salecenter.vo.AgentCompanyInfoVo">
        SELECT fk_agent_id as agentId,GROUP_CONCAT(e.short_name  order by e.view_order desc) as companyName,MIN(e.id) as companyId FROM r_agent_company a
        LEFT JOIN ais_permission_center.m_company e ON a.fk_company_id=e.id
        where fk_agent_id in
        <foreach collection="list" open="(" close=")" item="item" separator=",">
            #{item}
        </foreach>
         GROUP BY a.fk_agent_id;
    </select>

</mapper>