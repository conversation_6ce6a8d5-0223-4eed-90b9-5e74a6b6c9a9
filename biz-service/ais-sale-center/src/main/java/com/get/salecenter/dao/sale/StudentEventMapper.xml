<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentEventMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.StudentEvent">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_student_id" jdbcType="BIGINT" property="fkStudentId"/>
        <result column="fk_student_event_type_id" jdbcType="BIGINT" property="fkStudentEventTypeId"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_student_id, fk_student_event_type_id, description, gmt_create, gmt_create_user, 
    gmt_modified, gmt_modified_user
  </sql>

    <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentEvent" useGeneratedKeys="true"
            keyProperty="id">
        insert into m_student_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkStudentId != null">
                fk_student_id,
            </if>
            <if test="fkStudentEventTypeId != null">
                fk_student_event_type_id,
            </if>
            <if test="description != null">
                description,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkStudentId != null">
                #{fkStudentId,jdbcType=BIGINT},
            </if>
            <if test="fkStudentEventTypeId != null">
                #{fkStudentEventTypeId,jdbcType=BIGINT},
            </if>
            <if test="description != null">
                #{description,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="isExistByStudentId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_student_event where fk_student_id=#{studentId}
    </select>
</mapper>