<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StaffCommissionPolicyMapper">
    <select id="getStaffCommissionPolicies" resultType="com.get.salecenter.vo.StaffCommissionPolicyVo">
        SELECT
        a.id,
        a.fk_company_id,
        a.fk_student_project_role_key,
        a.fk_staff_commission_step_key,
        a.fk_area_country_id,
        a.fk_institution_id,
        a.fk_major_level_id,
        a.fixed_amount,
        a.commission_rate,
        a.priority,
        a.gmt_create,
        a.gmt_create_user,
        a.gmt_modified,
        a.gmt_modified_user,
        b.role_name as fk_student_project_role_key_name,
        c.step_name as fk_staff_commission_step_key_name
        FROM
        m_staff_commission_policy a
        LEFT JOIN u_student_project_role b on a.fk_student_project_role_key = b.role_key
        LEFT JOIN u_staff_commission_step c on a.fk_staff_commission_step_key = c.step_key
        where 1=1
        <if test="staffCommissionPolicyDto.fkCompanyId !=null">
            and a.fk_company_id = #{staffCommissionPolicyDto.fkCompanyId}
        </if>
        <if test="staffCommissionPolicyDto.fkStudentProjectRoleId !=null">
            and  b.id = #{staffCommissionPolicyDto.fkStudentProjectRoleId}
        </if>
        <if test="staffCommissionPolicyDto.fkStaffCommissionStepKeyId !=null">
            and c.id = #{staffCommissionPolicyDto.fkStaffCommissionStepKeyId}
        </if>
        <if test="staffCommissionPolicyDto.fkInstitutionId !=null">
            and a.fk_institution_id = #{staffCommissionPolicyDto.fkInstitutionId}
        </if>
        <if test="staffCommissionPolicyDto.fkMajorLevelId !=null">
            and a.fk_major_level_id = #{staffCommissionPolicyDto.fkMajorLevelId}
        </if>
        <if test="staffCommissionPolicyDto.fkAreaCountryId !=null">
            and a.fk_area_country_id = #{staffCommissionPolicyDto.fkAreaCountryId}
        </if>
        ORDER BY a.priority desc
    </select>
    <select id="getMaxPriority" resultType="java.lang.Integer">
        select
            IFNULL(max(priority)+1,0) priority
        from
            m_staff_commission_policy
    </select>
    <select id="getBdStaffCommissionPolicy" resultType="com.get.salecenter.vo.StaffCommissionPolicyVo">
        SELECT
            mscp.*
        FROM
            ais_sale_center.m_staff_commission_policy AS mscp
        WHERE
            mscp.fk_company_id = #{fkCompanyId}
          AND mscp.fk_student_project_role_key = #{projectRoleKey}
    </select>
</mapper>