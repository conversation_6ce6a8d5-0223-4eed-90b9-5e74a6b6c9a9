package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.entity.RStudentIssueStudent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2022/5/25
 * @TIME: 11:06
 * @Description:
 **/
@Mapper
public interface RStudentIssueStudentMapper extends GetMapper<RStudentIssueStudent> {

    RStudentIssueStudent getRStudentIssueStudentByStudentId(@Param("fkStudentId") Long fkStudentId);

    List<RStudentIssueStudent> getRStudentIssueStudent(@Param("fkStudentIds") Set<Long> fkStudentIds);

}
