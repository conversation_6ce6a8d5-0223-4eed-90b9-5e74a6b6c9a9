<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.StudentServiceFeeCostMapper">

    <select id="getStudentServiceFeeCostListDtos"
            resultType="com.get.salecenter.vo.StudentServiceFeeCostListVo">
        SELECT
            a.*,
            concat(
                    IF(is_active = 0,'【无效】', '' ),
                    CASE WHEN IFNULL( b.name_chn, '' ) = '' THEN NAME ELSE CONCAT( b.NAME, '（', b.name_chn, '）' ) END
                ) fkBusinessProviderName
        FROM
            m_student_service_fee_cost a
                LEFT JOIN m_business_provider b ON a.fk_business_provider_id = b.id
        where 1=1
        <if test="studentServiceFeeCostListDto.fkStudentServiceFeeId!=null">
            and a.fk_student_service_fee_id = #{studentServiceFeeCostListDto.fkStudentServiceFeeId}
        </if>
        ORDER BY a.`status` desc,a.gmt_create desc
    </select>

</mapper>
