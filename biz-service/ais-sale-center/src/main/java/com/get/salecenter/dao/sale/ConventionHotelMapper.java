package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.ConventionHotel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/6 11:25
 * @verison: 1.0
 * @description: 酒店房型管理mapper
 */
@Mapper
public interface ConventionHotelMapper extends BaseMapper<ConventionHotel> {

    /**
     * 酒店下拉框数据
     *
     * @param conventionId
     * @return
     */
    List<String> getHotelList(@Param("conventionId") Long conventionId);

    /**
     * 房型下拉框数据
     *
     * @param hotel
     * @return
     */
    List<String> getRoomTypeList(@Param("hotel") String hotel);

    /**
     * 根据峰会id和酒店名称查询对应ids
     *
     * @param hotel
     * @param conventionId
     * @return
     */
    List<Long> getConventionHotelIdsByHotel(@Param("hotel") String hotel, @Param("conventionId") Long conventionId);

    /**
     * 根据峰会id,酒店名称和房型查询对应id
     *
     * @param roomType
     * @param hotel
     * @param conventionId
     * @return
     */
    Long getConventionHotelId(@Param("roomType") String roomType, @Param("hotel") String hotel, @Param("conventionId") Long conventionId);

    /**
     * 查找排序最大值
     *
     * @param conventionId
     * @return
     */
    Integer getMaxViewOrder(@Param("conventionId") Long conventionId);

    /**
     * 根据峰会id 查找该峰会下所有房型ids
     *
     * @param conventionId
     * @return
     */
    List<Long> getConventionHotelIds(@Param("conventionId") Long conventionId);

    /**
     * @return java.lang.Boolean
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    Boolean conventionHotelIsEmpty(Long id);

    /**
     * 通过峰会id和personId获取酒店房间类型信息
     *
     * @param conventionPersonId
     * @return
     */
    ConventionHotel getConventionHotelByConventionPerson(@Param("conventionPersonId") Long conventionPersonId);
}