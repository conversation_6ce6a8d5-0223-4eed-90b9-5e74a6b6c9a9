<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ClientMapper">

    <select id="getResourceList" resultType="com.get.salecenter.vo.ClientListInfoVo">
        SELECT
            a.*,f.id as offer_id, f.fk_agent_id,
            <if test="hide">
                f.agent_name,
            </if>
            f.fk_area_country_id as offerCountryId,
            f.fk_staff_id,f.status,f.remark as offerRemark,
            MAX(scs.fk_staff_id) sourceStaffId,
            MAX(scs.fk_agent_id) sourceAgentId,
            dep.name as departmentName,
            GROUP_CONCAT(DISTINCT scs.fk_table_value) studentSource,
            MAX(scs.fk_table_id) fk_table_id,
            MAX(scs.fk_table_name) fk_table_name,
            t.maxOrder as maxStepOrder,
            GROUP_CONCAT(DISTINCT rcs.fk_staff_id ORDER BY rcs.fk_staff_id SEPARATOR ',') AS fkStudentStaffIds,
            GROUP_CONCAT(DISTINCT CONCAT(m.`name`,IF(m.`name_en` IS NULL OR TRIM(m.`name_en`) = '','',CONCAT('（', m.`name_en`, '）')))
            ORDER BY m.`name` SEPARATOR ',') AS fkStudentStaffNames
        FROM
            m_client a
        INNER JOIN (
            SELECT DISTINCT
                a.id
            FROM
                (
                    -- #父子代理的BD权限，儿子下面的学生，父亲的BD，第一层的权限
                    SELECT
                        a.id
                    FROM
                        ais_sale_center.m_client a
                    INNER JOIN ais_sale_center.r_client_agent b ON a.id = b.fk_client_id AND b.is_active=1
                    INNER JOIN ais_sale_center.m_agent c ON b.fk_agent_id = c.id
                    AND c.fk_parent_agent_id IS NOT NULL -- #第一层，儿子下面的学生
                    INNER JOIN ais_sale_center.r_agent_staff d ON c.fk_parent_agent_id = d.fk_agent_id
                    AND d.is_active = 1
                    AND d.fk_staff_id IN
                    <foreach collection="followerIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                    UNION ALL
                        -- #父子代理的BD权限，儿子下面的学生，父亲的BD，第二层的权限
                        SELECT
                            a.id
                        FROM
                            ais_sale_center.m_client a
                        INNER JOIN ais_sale_center.r_client_agent b ON a.id = b.fk_client_id AND b.is_active=1
                        INNER JOIN ais_sale_center.m_agent c ON b.fk_agent_id = c.id
                        AND c.fk_parent_agent_id IS NOT NULL -- #第二层，儿子下面的学生
                        INNER JOIN ais_sale_center.m_agent d ON c.fk_parent_agent_id = d.id
                        AND d.fk_parent_agent_id IS NOT NULL -- #第二层的父亲，对应第一层的儿子
                        INNER JOIN ais_sale_center.r_agent_staff e ON d.fk_parent_agent_id = e.fk_agent_id
                        AND e.is_active = 1
                        AND e.fk_staff_id IN
                    <foreach collection="followerIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                        <!--UNION ALL-->
                            <!--&#45;&#45; #BD的权限-->
                            <!--SELECT-->
                                <!--a.id-->
                            <!--FROM-->
                                <!--ais_sale_center.m_client a-->
                            <!--INNER JOIN ais_sale_center.r_client_agent b ON a.id = b.fk_client_id AND b.is_active=1-->
                            <!--INNER JOIN ais_sale_center.r_agent_staff c ON b.fk_agent_id = c.fk_agent_id-->
                            <!--AND c.is_active = 1-->
                            <!--AND c.fk_staff_id IN-->
                    <!--<foreach collection="followerIds" item="id" open="(" separator="," close=")">-->
                        <!--#{id}-->
                    <!--</foreach>-->
                            UNION ALL
                                -- #项目成员的权限
                                SELECT
                                    a.id
                                FROM
                                    ais_sale_center.m_client a
                                INNER JOIN m_client_offer f on f.fk_client_id = a.id
                                INNER JOIN ais_sale_center.s_student_project_role_staff b ON f.id = b.fk_table_id
                                AND b.is_active = 1
                                AND b.fk_table_name = 'm_client_offer'
                                AND b.fk_staff_id IN
                    <foreach collection="followerIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
                            UNION ALL
                                SELECT
                                a.id
                                FROM ais_sale_center.m_client a
                                INNER JOIN ais_sale_center.r_client_staff b ON a.id = b.fk_client_id AND b.is_active = 1
                                AND b.fk_staff_id IN
                                <foreach collection="followerIds" item="id" open="(" separator="," close=")">
                                    #{id}
                                </foreach>
                ) a
        ) z ON a.id = z.id
        LEFT JOIN (
        SELECT
        t1.*,
        t2.id AS offerId
        FROM
        (
        SELECT
        a1.id,
        MAX( c1.step_order ) AS maxOrder
        FROM
        m_client a1
        LEFT JOIN m_client_offer b1 ON b1.fk_client_id = a1.id and b1.status = 1
        LEFT JOIN u_client_offer_step c1 ON b1.fk_client_offer_step_id = c1.id
        GROUP BY
        a1.id
        ) t1
        LEFT JOIN (
        SELECT
        mco.id,
        mco.fk_client_id,
        ucos.step_order
        FROM
        m_client_offer mco
        LEFT JOIN u_client_offer_step ucos ON mco.fk_client_offer_step_id = ucos.id and mco.status = 1
        ) t2 ON t1.id = t2.fk_client_id
        AND t1.maxOrder = t2.step_order
        ) t ON t.id = a.id

        LEFT JOIN m_client_offer f ON f.id = t.offerId

        <!--<if test="clientDto.agentName!=null and clientDto.agentName!=''">-->
            <!--INNER JOIN r_client_agent r ON r.fk_client_id = a.id-->
            <!--AND r.is_active=1 AND r.fk_agent_id = #{clientDto.fkAgentId}-->
        <!--</if>-->
        <if test="clientDto.beginFollowUpTime!=null and clientDto.endFollowUpTime!=null">
            INNER JOIN(
            SELECT
            fk_client_id,
            max(follow_up_time) AS val
            FROM
            m_client_event
            GROUP BY fk_client_id
            having 1 = 1
            AND DATE_FORMAT(val, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{clientDto.beginFollowUpTime}, '%Y-%m-%d' )
            AND DATE_FORMAT(val, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{clientDto.endFollowUpTime}, '%Y-%m-%d' )
            ORDER BY
            follow_up_time
<!--            <-->
<!--                SELECT DISTINCT fk_client_id FROM m_client_event WHERE-->
<!--                DATE_FORMAT(follow_up_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{clientDto.beginFollowUpTime}, '%Y-%m-%d' )-->
<!--             AND DATE_FORMAT(follow_up_time, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{clientDto.endFollowUpTime}, '%Y-%m-%d' )-->
<!--            >-->
            ) e on e.fk_client_id = a.id
        </if>
        <if test="clientDto.projectRoleStaffId!=null and clientDto.projectRoleStaffId!=''">
            INNER JOIN (
            SELECT fk_table_id FROM ais_sale_center.s_student_project_role_staff
            WHERE fk_table_name='m_client_offer' AND is_active=1 AND fk_staff_id = #{clientDto.projectRoleStaffId}
            GROUP BY fk_table_id
            ) b1 ON f.id = b1.fk_table_id
        </if>
        LEFT JOIN ais_permission_center.m_staff sta on sta.login_id = a.gmt_create_user
        LEFT JOIN ais_permission_center.m_department dep on sta.fk_department_id = dep.id
        LEFT JOIN s_client_source scs on scs.fk_client_id = a.id
        LEFT JOIN m_agent AS ma ON ma.id = scs.fk_agent_id

        LEFT JOIN ais_sale_center.r_client_staff rcs ON rcs.fk_client_id = a.id AND rcs.is_active = 1
        LEFT JOIN ais_permission_center.m_staff m ON m.id = rcs.fk_staff_id

<!--        <LEFT JOIN u_client_offer_step ucos on ucos.id = f.fk_client_offer_step_id>       -->

        WHERE 1=1
        <if test="clientDto.agentName!=null and clientDto.agentName!=''">
            AND f.agent_name like CONCAT('%',#{clientDto.agentName},'%')
        </if>

        <if test="clientDto.fkCompanyId!=null and clientDto.fkCompanyId!=''">
            AND a.fk_company_id = #{clientDto.fkCompanyId}
        </if>

        <if test="clientDto.companyIds!=null and clientDto.companyIds.size()>0">
            AND a.fk_company_id IN
            <foreach collection="clientDto.companyIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>

        <if test="clientDto.name!=null and clientDto.name!=''">
            AND (
            REPLACE(CONCAT(LOWER(a.first_name),LOWER(a.last_name)),' ','') like concat('%',#{clientDto.name},'%')
            OR REPLACE(CONCAT(LOWER(a.last_name),LOWER(a.first_name)),' ','') like concat('%',#{clientDto.name},'%')
            OR REPLACE(LOWER(a.`name`),' ','') like concat('%',#{clientDto.name},'%')
            OR REPLACE(LOWER(a.last_name),' ','') like concat('%',#{clientDto.name},'%')
            OR REPLACE(LOWER(a.first_name),' ','') like concat('%',#{clientDto.name},'%')) -- 过滤学生中英文名字
        </if>

        <if test="clientDto.phone!=null and clientDto.phone!=''">
            AND a.mobile = #{clientDto.phone}
        </if>
        <if test="clientDto.weChatNumber!=null and clientDto.weChatNumber!=''">
            AND a.wechat like CONCAT('%',#{clientDto.weChatNumber},'%')
        </if>
        <if test="clientDto.email!=null and clientDto.email!=''">
            AND a.email = #{clientDto.email}
        </if>
        <if test="clientDto.fkAreaCountryId!=null and clientDto.fkAreaCountryId.size>0">
            AND f.fk_area_country_id IN
            <foreach collection="clientDto.fkAreaCountryId" item="countryId" open="(" close=")" separator=",">
                #{countryId}
            </foreach>
        </if>
        <if test="clientDto.fkTableName!=null and clientDto.fkTableName!=''">
            AND scs.fk_table_name = #{clientDto.fkTableName}
        </if>
        <if test="clientDto.fkTableValue!=null and clientDto.fkTableValue!=''">
            AND ( scs.fk_table_value LIKE CONCAT('%',#{clientDto.fkTableValue},'%')  OR  ma.name LIKE CONCAT('%',#{clientDto.fkTableValue},'%') OR ma.name_note LIKE CONCAT('%',#{clientDto.fkTableValue},'%') )
        </if>
        <if test="clientDto.remark!=null and clientDto.remark!=''">
            AND f.remark like concat('%',#{clientDto.remark},'%')
        </if>
        <if test="clientDto.beginTime!=null">
            AND DATE_FORMAT( a.expect_signing_time, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{clientDto.beginTime}, '%Y-%m-%d' )
        </if>
        <if test="clientDto.endTime!=null">
            AND DATE_FORMAT( a.expect_signing_time, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{clientDto.endTime}, '%Y-%m-%d' )
        </if>
        <if test="clientDto.reBeginCreateTime!=null">
            AND DATE_FORMAT( a.gmt_create, '%Y-%m-%d' ) <![CDATA[>= ]]> DATE_FORMAT(#{clientDto.reBeginCreateTime}, '%Y-%m-%d' )
        </if>
        <if test="clientDto.reEndCreateTime!=null">
            AND DATE_FORMAT( a.gmt_create, '%Y-%m-%d' ) <![CDATA[<= ]]> DATE_FORMAT(#{clientDto.reEndCreateTime}, '%Y-%m-%d' )
        </if>
        <if test="clientDto.star!=null and clientDto.star!=''">
            AND a.star_level = #{clientDto.star}
        </if>
        <if test="clientDto.status!=null">
            AND f.status = #{clientDto.status}
        </if>
        <if test="clientDto.fkDepartmentId!=null">
            AND dep.id = #{clientDto.fkDepartmentId}
        </if>
        <if test="clientDto.isActive!=null">
            AND a.is_active = #{clientDto.isActive}
        </if>
        <if test="clientDto.id!=null and clientDto.id!=''">
            AND a.id = #{clientDto.id}
        </if>

        <if test="clientDto.fkStudentStaffIds !=null and clientDto.fkStudentStaffIds.size()>0">
            AND EXISTS (
            SELECT 1
            FROM ais_sale_center.r_client_staff rcs2
            WHERE rcs2.fk_client_id = a.id
            AND rcs2.fk_staff_id IN
                <foreach collection="clientDto.fkStudentStaffIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            AND rcs2.is_active = 1
            )

        </if>

        <if test="clientDto.createUser != null and clientDto.createUser !=''">
            AND a.gmt_create_user LIKE CONCAT('%',#{clientDto.createUser},'%')
        </if>
        GROUP BY a.id
        ORDER BY a.id DESC
        <if test="export">
            LIMIT 0,50000
        </if>
    </select>

    <select id="getClientAgents" resultType="com.get.salecenter.vo.AgentVo">
        select rsa.fk_client_id as fkStudentId, ras.fk_agent_id,ras.fk_staff_id as fkStaffId,ma.`name` from r_agent_staff ras
        join r_client_agent rsa on ras.fk_agent_id=rsa.fk_agent_id
        join m_agent ma on ma.id=rsa.fk_agent_id
        where rsa.fk_client_id in
        <foreach collection="fkClientIds" item="ids" index="index" open="(" separator="," close=")">
            #{ids}
        </foreach>
        and ras.is_active=1 and rsa.is_active=1
    </select>
    <select id="selectClientStudentItem" resultType="com.get.salecenter.vo.ApprovalItemVo">
        select GROUP_CONCAT(a.name) as agentName,i.step_name as maxStepName,s.id
        from m_student s
                 left join r_student_agent sa on sa.fk_student_id = s.id
                 left join m_agent a on a.id = sa.fk_agent_id
                 LEFT JOIN (
            SELECT a.step_name,i.fk_student_id as id FROM
                m_student_offer_item i
                    LEFT JOIN u_student_offer_item_step a on a.id = i.fk_student_offer_item_step_id
            where i.fk_student_id = #{id}
            ORDER BY a.step_order desc limit 1
        ) i on i.id = s.id
        where s.id = #{id}
        GROUP BY s.id
    </select>
    <select id="getSameStudentClients" resultType="com.get.salecenter.vo.SameStudentClientVo">
        SELECT
        a.*,
        b.fk_agent_id
        FROM
        (
        SELECT
        *
        FROM
        m_student a
        where 1=1
        <if test="sameStudentClientDto.name !=null and sameStudentClientDto.name !=''">
            and a.`name` = #{sameStudentClientDto.name}
        </if>
        <if test="sameStudentClientDto.birthday !=null">
            and DATE_FORMAT(a.birthday,'%Y-%m-%d') = DATE_FORMAT(#{sameStudentClientDto.birthday},'%Y-%m-%d')
        </if>
        ) a
        INNER JOIN m_student_offer_item b on a.id = b.fk_student_id and b.status = 1
        where 1=1
        <if test="sameStudentClientDto.fkAreaCountryId !=null">
        and b.fk_area_country_id = #{sameStudentClientDto.fkAreaCountryId}
        </if>
        <if test="sameStudentClientDto.stepIds !=null and sameStudentClientDto.stepIds.size()>0">
        and b.fk_student_offer_item_step_id in
        <foreach collection="sameStudentClientDto.stepIds" item="stepId" index="index" open="(" separator="," close=")">
            #{stepId}
        </foreach>
        </if>
        GROUP BY a.id
    </select>
</mapper>