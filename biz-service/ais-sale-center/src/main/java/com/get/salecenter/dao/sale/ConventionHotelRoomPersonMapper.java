package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.ConventionHotelRoomPersonVo;
import com.get.salecenter.vo.ConventionHotelRoomVo;
import com.get.salecenter.entity.ConventionHotelRoom;
import com.get.salecenter.entity.ConventionHotelRoomPerson;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/8/21 16:04
 * @verison: 1.0
 * @description: 房间和参会人员中间表mapper
 */
@Mapper
public interface ConventionHotelRoomPersonMapper extends BaseMapper<ConventionHotelRoomPerson> {
    /**
     * 添加
     *
     * @param record
     * @return
     */
    int insertSelective(ConventionHotelRoomPerson record);

    /**
     * 查找中间表  该房间是否被被安排过
     *
     * @param roomId
     * @return
     */
    List<ConventionHotelRoomPersonVo> getConventionHotelRoomPersonDtoList(@Param("roomId") Long roomId);

    /**
     * 系统编号和日期确定房间id
     *
     * @param systemRoomNum
     * @param date
     * @return
     */
    Long getRommId(@Param("systemRoomNum") String systemRoomNum, @Param("date") String date, @Param("fkConventionId") Long fkConventionId);

    /**
     * @return java.lang.Integer
     * @Description :查找该日期和房型下，所有房间已经被安排得床位数
     * @Param [roomIds]
     * <AUTHOR>
     */
    Integer getArrangedBedCount(@Param("roomIds") List<Long> roomIds,@Param("types")List<Integer> types);

    /**
     * @return java.lang.Boolean
     * @Description :
     * @Param [fieldName, id]
     * <AUTHOR>
     */
    Boolean conventionHotelRoomPersonIsEmpty(@Param("fieldName") String fieldName, @Param("id") Long id);

    /**
     * @return java.lang.Integer
     * @Description :查找该日期和房型下，所有房间已经被安排得床位数
     * @Param [roomIds]
     * <AUTHOR>
     */
    Integer getUsedRoomCount(@Param("roomIds") List<Long> roomIds,@Param("types") List<Integer> types);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :
     * @Param [conventionId, roomType]
     * <AUTHOR>
     */
    List<Long> getconventionPersonIds(@Param("conventionId") Long conventionId, @Param("conventionRegistrationIds") List<Long> conventionRegistrationIds);


    /**
     * @return ConventionHotelRoomPerson
     * @Description : 获取住店日期
     * @Param [id]
     * <AUTHOR>
     */
    ConventionHotelRoom getStayDateStart(@Param("id") Long id);

    /**
     * @return ConventionHotelRoomPerson
     * @Description : 获取离店日期
     * @Param [id]
     * <AUTHOR>
     */
    ConventionHotelRoom getStayDateEnd(@Param("id") Long id);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取参会人员，预订表单选择纯机构名选项
     * @Param [conventionId, conventionRegistrationIds]
     * <AUTHOR>
     */
    List<Long> getPersonIds(@Param("conventionId") Long conventionId, @Param("conventionRegistrationIds") List<Long> conventionRegistrationIds);

    /**
     * 获取房间中人数
     *
     * @param fkConventionHotelRoomId
     * @return
     */
    Integer getPersonCount(@Param("fkConventionHotelRoomId") Long fkConventionHotelRoomId);

    /**
     * 获取参会人房间信息
     *
     * @param conventionPersonId
     * @return
     */
    List<ConventionHotelRoomPersonVo> getConventionHotelRoomPersonInfo(@Param("conventionPersonId") Long conventionPersonId);

    /**
     * 获取其他房客信息
     *
     * @param fkConventionHotelRoomId
     * @param fkConventionPersonId
     * @return
     */
    List<ConventionHotelRoomPersonVo> getOtherPersons(@Param("fkConventionHotelRoomId") Long fkConventionHotelRoomId, @Param("fkConventionPersonId") Long fkConventionPersonId);

    /**
     * 获取roomids
     *
     * @param conventionId
     * @return
     */
    List<ConventionHotelRoomVo> getRoomIdAndDate(@Param("conventionId") Long conventionId);

    /**
     * 峰会下所有房间
     *
     * @param conventionId
     * @return
     */
    List<ConventionHotelRoomPersonVo> getConventionHotelRoomPersonDtos(@Param("conventionId") Long conventionId);
}


