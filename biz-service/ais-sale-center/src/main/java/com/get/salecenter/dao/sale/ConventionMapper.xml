<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ConventionMapper">

  <select id="getProcedureCount" parameterType="java.lang.Long" resultType="java.lang.Long">
    select
	 count(p.id) procedureCount
	from
	 m_convention c
	left
	 join m_convention_procedure p
	on
	 p.fk_convention_id = c.id
	where
	 c.id = #{id}
  </select>

  <select id="getRegistrationCount" parameterType="java.lang.Long" resultType="java.lang.Long">
    select
	 count(r.id) registrationCount
	from
	 m_convention c
	left join
	 m_convention_registration r
	on
	 r.fk_convention_id = c.id
	where
	 c.id = #{id}
  </select>

  <select id="getPersonCount" parameterType="java.lang.Long" resultType="java.lang.Long">
    select
	 count(p.id) personCount
	from
	 m_convention c
	left join
	 m_convention_person p
	on
	 p.fk_convention_id = c.id
	where
	 c.id = #{id}
  </select>

  <select id="getConventionNameById" parameterType="java.lang.Long" resultType="string">
    select
     theme_name
    from
     m_convention
    where
     id = #{id}

  </select>

</mapper>