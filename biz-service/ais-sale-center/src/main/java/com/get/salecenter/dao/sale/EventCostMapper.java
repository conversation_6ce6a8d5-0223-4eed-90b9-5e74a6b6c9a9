package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.EventCostVo;
import com.get.salecenter.vo.ReceiptFormVo;
import com.get.salecenter.entity.EventCost;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@Mapper
public interface EventCostMapper extends BaseMapper<EventCost>, GetMapper<EventCost> {

    /**
     * 获取活动类型的收款单可绑定余额
     *
     * @Date 17:31 2021/12/3
     * <AUTHOR>
     */
    BigDecimal getReceiptFormBalance(@Param("fkReceiptFormId") Long fkReceiptFormId, @Param("eventCostId") Long eventCostId);

    /**
     * 获取活动类型的收款单可绑定余额
     *
     * @Date 17:31 2021/12/3
     * <AUTHOR>
     */
    List<ReceiptFormVo> getReceiptFormBalances(@Param("fkReceiptFormIds") Set<Long> fkReceiptFormIds, @Param("eventCostId") Long eventCostId);

    /**
     * 收款单信息
     *
     * @param eventCostIds
     * @return
     */
    List<EventCostVo> getReceiptFormInfo(@Param("eventCostIds") Set<Long> eventCostIds);
    
}