<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.salecenter.dao.sale.ReceivablePlanMapper">
    <insert id="insert" parameterType="com.get.salecenter.entity.ReceivablePlan" keyProperty="id" useGeneratedKeys="true">
        insert into m_receivable_plan (id, fk_company_id, fk_type_key,
                                       fk_type_target_id, summary, fk_currency_type_num,
                                       tuition_amount, commission_rate, net_rate,
                                       commission_amount, fixed_amount, bonus_type,
                                       bonus_amount, receivable_amount, receivable_plan_date,
                                       fk_receivable_reason_id, status, id_gea_finance,
                                       gmt_create, gmt_create_user, gmt_modified,
                                       gmt_modified_user)
        values (#{id,jdbcType=BIGINT}, #{fkCompanyId,jdbcType=BIGINT}, #{fkTypeKey,jdbcType=VARCHAR},
                #{fkTypeTargetId,jdbcType=BIGINT}, #{summary,jdbcType=VARCHAR}, #{fkCurrencyTypeNum,jdbcType=VARCHAR},
                #{tuitionAmount,jdbcType=DECIMAL}, #{commissionRate,jdbcType=DECIMAL}, #{netRate,jdbcType=DECIMAL},
                #{commissionAmount,jdbcType=DECIMAL}, #{fixedAmount,jdbcType=DECIMAL}, #{bonusType,jdbcType=INTEGER},
                #{bonusAmount,jdbcType=DECIMAL}, #{receivableAmount,jdbcType=DECIMAL}, #{receivablePlanDate,jdbcType=TIMESTAMP},
                #{fkReceivableReasonId,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{idGeaFinance,jdbcType=VARCHAR},
                #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
                #{gmtModifiedUser,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.salecenter.entity.ReceivablePlan" keyProperty="id" useGeneratedKeys="true">
        insert into m_receivable_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkCompanyId != null">
                fk_company_id,
            </if>
            <if test="fkTypeKey != null">
                fk_type_key,
            </if>
            <if test="fkTypeTargetId != null">
                fk_type_target_id,
            </if>
            <if test="summary != null">
                summary,
            </if>
            <if test="fkCurrencyTypeNum != null">
                fk_currency_type_num,
            </if>
            <if test="tuitionAmount != null">
                tuition_amount,
            </if>
            <if test="commissionRate != null">
                commission_rate,
            </if>
            <if test="netRate != null">
                net_rate,
            </if>
            <if test="commissionAmount != null">
                commission_amount,
            </if>
            <if test="fixedAmount != null">
                fixed_amount,
            </if>
            <if test="bonusType != null">
                bonus_type,
            </if>
            <if test="bonusAmount != null">
                bonus_amount,
            </if>
            <if test="receivableAmount != null">
                receivable_amount,
            </if>
            <if test="receivablePlanDate != null">
                receivable_plan_date,
            </if>
            <if test="fkReceivableReasonId != null">
                fk_receivable_reason_id,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="idGeaFinance != null">
                id_gea_finance,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkCompanyId != null">
                #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="fkTypeKey != null">
                #{fkTypeKey,jdbcType=VARCHAR},
            </if>
            <if test="fkTypeTargetId != null">
                #{fkTypeTargetId,jdbcType=BIGINT},
            </if>
            <if test="summary != null">
                #{summary,jdbcType=VARCHAR},
            </if>
            <if test="fkCurrencyTypeNum != null">
                #{fkCurrencyTypeNum,jdbcType=VARCHAR},
            </if>
            <if test="tuitionAmount != null">
                #{tuitionAmount,jdbcType=DECIMAL},
            </if>
            <if test="commissionRate != null">
                #{commissionRate,jdbcType=DECIMAL},
            </if>
            <if test="netRate != null">
                #{netRate,jdbcType=DECIMAL},
            </if>
            <if test="commissionAmount != null">
                #{commissionAmount,jdbcType=DECIMAL},
            </if>
            <if test="fixedAmount != null">
                #{fixedAmount,jdbcType=DECIMAL},
            </if>
            <if test="bonusType != null">
                #{bonusType,jdbcType=INTEGER},
            </if>
            <if test="bonusAmount != null">
                #{bonusAmount,jdbcType=DECIMAL},
            </if>
            <if test="receivableAmount != null">
                #{receivableAmount,jdbcType=DECIMAL},
            </if>
            <if test="receivablePlanDate != null">
                #{receivablePlanDate,jdbcType=TIMESTAMP},
            </if>
            <if test="fkReceivableReasonId != null">
                #{fkReceivableReasonId,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="idGeaFinance != null">
                #{idGeaFinance,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="getCompanyIdByPlanId" resultType="java.lang.Long">
        SELECT
            s.fk_company_id
        FROM
            m_receivable_plan AS mrp
                INNER JOIN m_student_offer_item AS msoi ON mrp.fk_type_target_id = msoi.id
                AND mrp.fk_type_key = 'm_student_offer_item'
                INNER JOIN m_student AS s ON s.id = msoi.fk_student_id
        where mrp.id=#{planId}
        UNION
        SELECT
            s.fk_company_id
        FROM
            m_receivable_plan AS mrp
                INNER JOIN m_student_insurance AS msi ON mrp.fk_type_target_id = msi.id
                AND mrp.fk_type_key = 'm_student_insurance'
                INNER JOIN m_student AS s ON s.id = msi.fk_student_id
        where mrp.id=#{planId}
        UNION
        SELECT
            s.fk_company_id
        FROM
            m_receivable_plan AS mrp
                INNER JOIN m_student_accommodation AS msa ON mrp.fk_type_target_id = msa.id
                AND mrp.fk_type_key = 'm_student_accommodation'
                INNER JOIN m_student AS s ON s.id = msa.fk_student_id
        where mrp.id=#{planId}
    </select>

    <select id="getPlanIdByCompanyId" resultType="java.lang.Long">
        SELECT r.id from m_receivable_plan r
        left join m_student_offer_item i on r.fk_type_target_id=i.id
        where i.fk_student_offer_id in(
        SELECT o.id FROM  m_student_offer o
        left join m_student s on o.fk_student_id =s.id
        where s.fk_company_id=#{companyId}
        <if test="studentId !=null">
            and i.fk_student_id =#{studentId}
        </if>
        )
    </select>

    <select id="getPlanIdByStudentName" resultType="java.lang.Long">
        SELECT r.id from m_receivable_plan r
        left join m_student_offer_item i on r.fk_type_target_id=i.id
        where i.fk_student_offer_id in(
        SELECT o.id FROM  m_student_offer o
        left join m_student s on o.fk_student_id =s.id
        where position(#{studentName} in s.name)
        )
    </select>

    <select id="getPlanIdByProviderId" resultType="java.lang.Long">
        SELECT r.id from m_receivable_plan r
        left join m_student_offer_item i on r.fk_type_target_id=i.id
        where i.fk_institution_provider_id in
        <foreach collection="providerIds" item="providerId" index="index" open="(" separator="," close=")">
            #{providerId}
        </foreach>
        and r.fk_type_key='m_student_offer_item'
    </select>

    <select id="getPlanIdByInstitutionId" resultType="java.lang.Long">
        SELECT r.id from m_receivable_plan r
        left join m_student_offer_item i on r.fk_type_target_id=i.id
        where i.fk_institution_id in
        <foreach collection="institutionIds" item="institutionId" index="index" open="(" separator="," close=")">
            #{institutionId}
        </foreach>
        and r.fk_type_key='m_student_offer_item'
    </select>

    <select id="getPlanIdByCourseId" resultType="java.lang.Long">
        SELECT r.id from m_receivable_plan r
        left join m_student_offer_item i on r.fk_type_target_id=i.id
        where i.fk_institution_course_id in
        <foreach collection="courseIds" item="courseId" index="index" open="(" separator="," close=")">
            #{courseId}
        </foreach>
        and r.fk_type_key='m_student_offer_item'
    </select>

    <select id="getInstitutionProviderReceivableSumDatas" resultType="com.get.salecenter.vo.InstitutionProviderReceivableSumVo">
        SELECT a.fk_company_id AS fkCompanyId, a.company_short_name AS fkCompanyName,
        a.fk_institution_provider_id AS institutionProviderId, a.institution_provider_name AS institutionProviderName,
        a.institution_provider_name_chn AS institutionProviderNameChn,
        a.fk_currency_type_num AS fkCurrencyTypeNum, a.currency_type_name AS fkCurrencyTypeName,
        COUNT(a.fk_receivable_plan_id) AS compleApplyCount,
        SUM(a.receivable_amount) AS payableAmount, SUM(a.actual_receivable_amount) AS amountPayable, SUM(a.diff_receivable_amount) AS differPay,
        CASE
        WHEN  SUM(a.diff_receivable_amount)=0.00 THEN 2
        WHEN SUM(a.receivable_amount)+SUM(a.diff_receivable_amount)=0.00 	THEN 0
        ELSE 1 END as receiveStatus
        FROM (
        SELECT
        a.id fk_receivable_plan_id,
        g.id fk_company_id, g.short_name company_short_name,
        h.id fk_institution_provider_id, h.`name` institution_provider_name, h.name_chn institution_provider_name_chn,
        a.fk_type_target_id fk_student_offer_item_id, d.gmt_create student_offer_item_create_time,
        a.fk_currency_type_num, c.type_name currency_type_name,
        IFNULL(a.receivable_amount,0) receivable_amount,
        IFNULL(b.sum_amount_receivable,0) sum_amount_receivable,
        IFNULL(b.sum_amount_exchange_rate,0) sum_amount_exchange_rate,
        IFNULL(b.sum_amount_hkd,0) sum_amount_hkd,
        IFNULL(b.sum_amount_rmb,0) sum_amount_rmb,
        (IFNULL(b.sum_amount_receivable,0) + IFNULL(b.sum_amount_exchange_rate,0)) actual_receivable_amount,
        ( IFNULL(a.receivable_amount,0) - IFNULL(b.sum_amount_receivable,0) - IFNULL(b.sum_amount_exchange_rate,0) ) diff_receivable_amount
        FROM ais_sale_center.m_receivable_plan a
        LEFT JOIN (
        SELECT a.fk_receivable_plan_id,
        SUM(IFNULL(a.amount_receivable,0)) sum_amount_receivable,
        SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd,0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb,0)) sum_amount_rmb
        FROM ais_finance_center.m_receipt_form_item a
        LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id=b.id
        WHERE b.`status`!=0
        GROUP BY a.fk_receivable_plan_id
        ) b ON a.id=b.fk_receivable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
        LEFT JOIN ais_sale_center.m_student_offer_item d ON a.fk_type_target_id=d.id
        LEFT JOIN ais_sale_center.m_student_offer e ON d.fk_student_offer_id=e.id
        LEFT JOIN ais_sale_center.m_student f ON e.fk_student_id=f.id
        LEFT JOIN ais_permission_center.m_company g ON f.fk_company_id=g.id
        LEFT JOIN ais_institution_center.m_institution_provider h ON d.fk_institution_provider_id=h.id
        WHERE a.fk_type_key='m_student_offer_item'
        <!--查询-学生姓名-->
        <if test="institutionProviderReceivableSumDto.studentName != null and institutionProviderReceivableSumDto.studentName !=''">
            and (f.name LIKE CONCAT('%',#{institutionProviderReceivableSumDto.studentName},'%') OR f.last_name LIKE CONCAT('%', #{institutionProviderReceivableSumDto.studentName}, '%') OR f.first_name LIKE CONCAT('%', #{institutionProviderReceivableSumDto.studentName}, '%'))
        </if>
        <!--查询-学校名称-->
        <if test="institutionProviderReceivableSumDto.fkInstitutionId != null and institutionProviderReceivableSumDto.fkInstitutionId !=''">
            and d.fk_institution_id = #{institutionProviderReceivableSumDto.fkInstitutionId}
        </if>

        -- 开学时间
        <if test="institutionProviderReceivableSumDto.openingTimeStart != null">
            AND DATE_FORMAT(d.defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{institutionProviderReceivableSumDto.openingTimeStart},'%Y-%m-%d')
        </if>
        <if test="institutionProviderReceivableSumDto.openingTimeEnd != null">
            AND DATE_FORMAT(d.defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{institutionProviderReceivableSumDto.openingTimeEnd},'%Y-%m-%d')
        </if>

        AND a.`status`!=0
        AND d.`status`!=0
        ) a
        WHERE 1=1
        <if test="institutionProviderReceivableSumDto.fkCompanyId != null">
            and a.fk_company_id = #{institutionProviderReceivableSumDto.fkCompanyId}
        </if>
        -- 改为公司多选
        <if test="institutionProviderReceivableSumDto.fkCompanyIds != null and institutionProviderReceivableSumDto.fkCompanyIds.size() > 0">
            and a.fk_company_id in
            <foreach collection="institutionProviderReceivableSumDto.fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="institutionProviderReceivableSumDto.institutionProvider != null and institutionProviderReceivableSumDto.institutionProvider != ''">
            AND (a.institution_provider_name LIKE CONCAT('%',#{institutionProviderReceivableSumDto.institutionProvider},'%') OR a.institution_provider_name_chn LIKE CONCAT('%',#{institutionProviderReceivableSumDto.institutionProvider},'%'))
        </if>
        <if test="institutionProviderReceivableSumDto.startTime != null">
            and DATE_FORMAT(a.student_offer_item_create_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{institutionProviderReceivableSumDto.startTime},'%Y-%m-%d')
        </if>
        <if test="institutionProviderReceivableSumDto.endTime != null">
            and DATE_FORMAT(a.student_offer_item_create_time,'%Y-%m-%d') <![CDATA[< ]]> DATE_FORMAT(#{institutionProviderReceivableSumDto.endTime},'%Y-%m-%d')
        </if>
        GROUP BY a.fk_company_id, a.company_short_name,
        a.fk_institution_provider_id, a.institution_provider_name, a.institution_provider_name_chn,
        a.fk_currency_type_num, a.currency_type_name
        <if test="institutionProviderReceivableSumDto.receiveStatus != null and institutionProviderReceivableSumDto.receiveStatus != ''">
            HAVING  receiveStatus = #{institutionProviderReceivableSumDto.receiveStatus}
        </if>
        order by CONVERT(a.institution_provider_name USING gbk)
    </select>
    <select id="institutionProviderReceivableSumDetail" resultType="com.get.salecenter.vo.ReceivablePlanVo">
        SELECT
        mrp.id,
        mrp.fk_type_key,
        mrp.fk_type_target_id,
        mrp.summary,
        mrp.fk_currency_type_num,
        mrp.tuition_amount,
        mrp.commission_rate,
        mrp.fixed_amount,
        mrp.bonus_amount,
        mrp.receivable_amount,
        mrp.receivable_plan_date,
        mrp.fk_receivable_reason_id,
        mrp.`status`,
        mrp.gmt_create,
        mrp.gmt_create_user,
        mrp.gmt_modified,
        mrp.gmt_modified_user,
        msoi.old_course_custom_name,
        msoi.old_institution_name,
        msoi.old_institution_full_name,
        msoi.defer_opening_time,
        f.num AS student_num,
        case f.gender when 0 then '女' when 1 then '男' ELSE '其他' end genderName,
        f.name as studentName,f.last_name as studentLastName,f.first_name as studentFirstName,f.birthday as studentBirthday
        FROM ais_sale_center.m_receivable_plan AS mrp
        INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = mrp.fk_type_target_id AND mrp.fk_type_key = 'm_student_offer_item' AND mrp.status != 0 AND msoi.status != 0
        INNER JOIN ais_sale_center.m_student_offer AS mso ON mso.id = msoi.fk_student_offer_id AND mso.status !=0
        INNER JOIN ais_institution_center.m_institution_provider AS mip ON mip.id = msoi.fk_institution_provider_id
        INNER JOIN ais_sale_center.m_student f ON msoi.fk_student_id = f.id
        <where>
            <if test="institutionProviderReceivableSumDetailDto.fkCurrencyTypeNum != null and institutionProviderReceivableSumDetailDto.fkCurrencyTypeNum != ''">
                AND mrp.fk_currency_type_num = #{institutionProviderReceivableSumDetailDto.fkCurrencyTypeNum}
            </if>
            <if test="institutionProviderReceivableSumDetailDto.fkInstitutionProviderId != null">
                AND mip.id = #{institutionProviderReceivableSumDetailDto.fkInstitutionProviderId}
            </if>
            <if test="institutionProviderReceivableSumDetailDto.startTime != null">
                AND DATE_FORMAT(msoi.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{institutionProviderReceivableSumDetailDto.startTime},'%Y-%m-%d')
            </if>
            <if test="institutionProviderReceivableSumDetailDto.endTime != null">
                AND DATE_FORMAT(msoi.gmt_create,'%Y-%m-%d') <![CDATA[< ]]> DATE_FORMAT(#{institutionProviderReceivableSumDetailDto.endTime},'%Y-%m-%d')
            </if>
            <if test="institutionProviderReceivableSumDetailDto.fkCompanyId != null">
                AND f.fk_company_id = #{institutionProviderReceivableSumDetailDto.fkCompanyId}
            </if>
            <!--查询-学生姓名-->
            <if test="institutionProviderReceivableSumDetailDto.studentName != null and institutionProviderReceivableSumDetailDto.studentName !=''">
                and (f.name LIKE CONCAT('%',#{institutionProviderReceivableSumDetailDto.studentName},'%') OR f.last_name LIKE CONCAT('%', #{institutionProviderReceivableSumDetailDto.studentName}, '%') OR f.first_name LIKE CONCAT('%', #{institutionProviderReceivableSumDetailDto.studentName}, '%'))
            </if>
            <!--查询-学校名称-->
            <if test="institutionProviderReceivableSumDetailDto.fkInstitutionId != null">
                and msoi.fk_institution_id = #{institutionProviderReceivableSumDetailDto.fkInstitutionId}
            </if>
            <if test="institutionProviderReceivableSumDetailDto.fkCompanyId != null">
                and mrp.fk_company_id = #{institutionProviderReceivableSumDetailDto.fkCompanyId}
            </if>
            <if test="institutionProviderReceivableSumDetailDto.openingTimeStart != null">
                AND DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{institutionProviderReceivableSumDetailDto.openingTimeStart},'%Y-%m-%d')
            </if>
            <if test="institutionProviderReceivableSumDetailDto.openingTimeEnd != null">
                AND DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{institutionProviderReceivableSumDetailDto.openingTimeEnd},'%Y-%m-%d')
            </if>
        </where>
    </select>

    <select id="studentReceivableSumDetail" resultType="com.get.salecenter.vo.ReceivablePlanVo">
        SELECT *,msoi.student_id,v.short_name as fkCompanyName,v.id as fkCompanyId,
        s.num AS student_num,case s.gender when 0 then '女' when 1 then '男' ELSE '其他' end genderName,
        s.name as studentName,s.last_name as studentLastName,s.first_name as studentFirstName,s.birthday as studentBirthday
        FROM ais_sale_center.m_receivable_plan AS mrp
        INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = mrp.fk_type_target_id AND mrp.fk_type_key = 'm_student_offer_item' AND mrp.status != 0 AND msoi.status != 0
        INNER JOIN ais_sale_center.m_student_offer AS mso ON mso.id = msoi.fk_student_offer_id AND mso.status !=0
        INNER JOIN ais_sale_center.m_student AS s ON s.id = msoi.fk_student_id
        LEFT JOIN ais_permission_center.m_company v ON v.id = mrp.fk_company_id
        <where>
            <if test="studentReceivableSumDetailDto.fkStudentOfferId != null and studentReceivableSumDetailDto.fkStudentOfferId !=''">
                and msoi.id =#{studentReceivableSumDetailDto.fkStudentOfferId}
            </if>
            <if test="studentReceivableSumDetailDto.fkCurrencyTypeNum != null and studentReceivableSumDetailDto.fkCurrencyTypeNum !=''">
                and mrp.fk_currency_type_num =#{studentReceivableSumDetailDto.fkCurrencyTypeNum}
            </if>
            <if test="studentReceivableSumDetailDto.startTime != null">
                AND DATE_FORMAT(msoi.gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{studentReceivableSumDetailDto.startTime},'%Y-%m-%d')
            </if>
            <if test="studentReceivableSumDetailDto.endTime != null">
                AND DATE_FORMAT(msoi.gmt_create,'%Y-%m-%d') <![CDATA[< ]]> DATE_FORMAT(#{studentReceivableSumDetailDto.endTime},'%Y-%m-%d')
            </if>
        </where>
    </select>
    <select id="getReceivablePlansInfo" resultType="com.get.salecenter.vo.ReceivablePlanVo">
        <!--#拆分应收币种，并累计对应币种的应收计划-->
        SELECT
        a.id,
        a.fk_type_key,
        a.fk_type_target_id,
        a.fk_currency_type_num,
        a.fk_company_id,
        a.tuition_amount,
        a.commission_rate,
        a.net_rate,
        a.commission_amount,
        a.fixed_amount,
        a.bonus_type,
        a.bonus_amount,
        a.receivable_plan_date,
        a.id_gea_finance,
        a.gmt_modified,
        a.gmt_modified_user,
        a.summary,
        a.gmt_create_user,
        a.gmt_create,
        c.type_name,
        SUM(IFNULL(a.receivable_amount,0)) receivable_amount, <!--#应收金额-->
        SUM(IFNULL(b.sum_amount_receivable,0)) sum_amount_receivable, <!--#实收折合金额（这个金额币种和应收一致，因为已经是折合了）-->
        SUM(IFNULL(b.sum_amount_exchange_rate,0)) sum_amount_exchange_rate, <!--#汇率调整-->
        SUM(IFNULL(b.sum_amount_hkd,0)) sum_amount_hkd, <!--#港币金额-->
        SUM(IFNULL(b.sum_amount_rmb,0)) sum_amount_rmb, <!--#人民币金额-->
        (SUM(IFNULL(b.sum_amount_receivable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0))) actual_receivable_amount, <!--#实收（折合金额+汇率调整）-->
        (IFNULL(a.receivable_amount,0) -(IFNULL(b.sum_amount_receivable,0) - IFNULL(b.sum_amount_exchange_rate,0)) ) diff_receivable_amount,<!--#差额-->
        CASE
        WHEN  (SUM(IFNULL(b.sum_amount_receivable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0)) - SUM(IFNULL(a.receivable_amount,0)))=0.00 THEN 2
        WHEN (SUM(IFNULL(b.sum_amount_receivable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0)) - SUM(IFNULL(a.receivable_amount,0))) > (-SUM(IFNULL(a.receivable_amount,0))) 	THEN 1
        ELSE 0 END as receiveStatus
        FROM ais_sale_center.m_receivable_plan a
        LEFT JOIN (
        <!--#计算每条应收计划里累计的实收金额-->
        SELECT a.fk_receivable_plan_id,
        SUM(IFNULL(a.amount_receivable,0)) sum_amount_receivable,
        SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd,0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb,0)) sum_amount_rmb
        FROM ais_finance_center.m_receipt_form_item a
        LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id=b.id
        WHERE b.`status`!=0 <!--关闭的收款单不作计算-->
        GROUP BY a.fk_receivable_plan_id
        ) b ON a.id=b.fk_receivable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
        WHERE 1=1
        <!--a.fk_type_key='m_student_offer_item'-->
        <if test="receivablePlanDto.ids != null and receivablePlanDto.ids.size()>0">
            and a.id in
            <foreach collection="receivablePlanDto.ids" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY a.id,a.fk_type_target_id, a.fk_currency_type_num, c.type_name
        <if test="receivablePlanDto.receiveStatus != null and receivablePlanDto.receiveStatus !='' or receivablePlanDto.receiveStatus == 0">
            HAVING  receiveStatus = #{receivablePlanDto.receiveStatus}
        </if>
    </select>
    <select id="getPlanIdsByStudentOfferItemIds" resultType="java.lang.Long">
        SELECT r.id from m_receivable_plan r
        where 1=1
        <if test="studentOfferItemIds != null and studentOfferItemIds !=''">
            AND r.fk_type_target_id in ${studentOfferItemIds}
        </if>
        and r.fk_type_key='m_student_offer_item'
    </select>
    <select id="getReceivablePlanDtosByProviderId" resultType="com.get.salecenter.vo.ReceivablePlanVo">
        SELECT
            mrp.id,
            mrp.fk_type_key,
            mrp.fk_type_target_id,
            mrp.summary,
            mrp.fk_currency_type_num,
            mrp.tuition_amount,
            mrp.commission_rate,
            mrp.net_rate,
            mrp.commission_amount,
            mrp.fixed_amount,
            mrp.bonus_amount,
            mrp.receivable_amount,
            mrp.receivable_plan_date,
            mrp.fk_receivable_reason_id,
            mrp.`status`,
            mrp.id_gea_finance,
            mrp.gmt_create,
            mrp.gmt_create_user,
            mrp.gmt_modified,
            mrp.gmt_modified_user
        FROM
            m_receivable_plan AS mrp
                LEFT JOIN
            m_student_offer_item AS msoi
            ON
            mrp.fk_type_target_id = msoi.id AND
            mrp.fk_type_key = "m_student_offer_item"
        WHERE
            msoi.fk_institution_provider_id = #{fkProviderId}
    </select>
    <select id="getPlanIdByCompanyIdAndAccommodation" resultType="java.lang.Long">
        SELECT
        mrp.id
        FROM
        m_student_accommodation AS msa
        INNER JOIN
        m_student AS ms
        ON
        msa.fk_student_id = ms.id
        INNER JOIN
        m_receivable_plan AS mrp
        ON
        msa.id = mrp.fk_type_target_id AND
        mrp.fk_type_key = 'm_student_accommodation'
        WHERE
        ms.fk_company_id = #{fkCompanyId}
        <if test="studentId !=null">
            and msa.fk_student_id =#{studentId}
        </if>
    </select>
    <select id="getPlanIdByCompanyIdAndInsurance" resultType="java.lang.Long">
        SELECT
        mrp.id
        FROM
        m_student_insurance AS msi
        INNER JOIN
        m_student AS ms
        ON
        msi.fk_student_id = ms.id
        INNER JOIN
        m_receivable_plan AS mrp
        ON
        msi.id = mrp.fk_type_target_id AND
        mrp.fk_type_key = 'm_student_insurance'
        WHERE
        ms.fk_company_id = #{fkCompanyId}
        <if test="studentId !=null">
            and msi.fk_student_id =#{studentId}
        </if>
    </select>

    <select id="getFkTypeTargetIdByIds" resultType="java.lang.Long">
        SELECT fk_type_target_id FROM m_receivable_plan
        WHERE 1=1
        <if test="ids != null and ids.size()>0">
            and id in
            <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>

    <select id="isExistByItemId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id from m_receivable_plan where fk_type_target_id=#{id} and fk_type_key = 'm_student_offer_item' and status!=0
    </select>
    <select id="getAllPlanIdByCompanyId" resultType="java.lang.Long">
        SELECT
        r.id
        FROM
        m_receivable_plan r
        INNER JOIN m_student_offer_item i ON r.fk_type_target_id = i.id
        AND r.fk_type_key = "m_student_offer_item"
        INNER JOIN m_student s ON s.id = i.fk_student_id
        AND s.fk_company_id = #{fkCompanyId}
        <if test="studentId !=null">
            and i.fk_student_id =#{studentId}
        </if>
        UNION ALL
        SELECT
        mrp.id
        FROM
        m_student_accommodation AS msa
        INNER JOIN m_student AS ms ON msa.fk_student_id = ms.id
        INNER JOIN m_receivable_plan AS mrp ON msa.id = mrp.fk_type_target_id
        AND mrp.fk_type_key = "m_student_accommodation"
        WHERE
        ms.fk_company_id = #{fkCompanyId}
        <if test="studentId !=null">
            and msa.fk_student_id =#{studentId}
        </if>
        UNION ALL
        SELECT
        mrp.id
        FROM
        m_student_insurance AS msi
        INNER JOIN m_student AS ms ON msi.fk_student_id = ms.id
        INNER JOIN m_receivable_plan AS mrp ON msi.id = mrp.fk_type_target_id
        AND mrp.fk_type_key = "m_student_insurance"
        WHERE
        ms.fk_company_id = #{fkCompanyId}
        <if test="studentId !=null">
            and msi.fk_student_id =#{studentId}
        </if>
        UNION ALL
        SELECT
        r.id
        FROM
        m_receivable_plan r
        where r.fk_type_key = "m_institution_provider"
        <if test="institutionProviderIds !=null and institutionProviderIds.size()>0">
            AND r.fk_type_target_id in
            <foreach collection="institutionProviderIds" item="institutionProviderId" open="(" separator="," close=")">
                #{institutionProviderId}
            </foreach>
        </if>
        UNION ALL
        SELECT
        r.id
        FROM
        m_receivable_plan r
        where r.fk_type_key = "other"
        <if test="institutionProviderIds !=null and institutionProviderIds.size()>0">
            AND r.fk_type_target_id in
            <foreach collection="institutionProviderIds" item="institutionProviderId" open="(" separator="," close=")">
                #{institutionProviderId}
            </foreach>
        </if>
<!--        UNION ALL-->
<!--        SELECT-->
<!--        r.id-->
<!--        FROM-->
<!--        m_receivable_plan r-->
<!--        where r.fk_type_key = "m_institution_provider"-->
<!--        <if test="institutionProviderIds !=null and institutionProviderIds.size()>0">-->
<!--            AND r.fk_type_target_id in-->
<!--            <foreach collection="institutionProviderIds" item="institutionProviderId" open="(" separator="," close=")">-->
<!--                #{institutionProviderId}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        UNION ALL-->
<!--        SELECT-->
<!--        r.id-->
<!--        FROM-->
<!--        m_receivable_plan r-->
<!--        where r.fk_type_key = "other"-->
<!--        <if test="institutionProviderIds !=null and institutionProviderIds.size()>0">-->
<!--            AND r.fk_type_target_id in-->
<!--            <foreach collection="institutionProviderIds" item="institutionProviderId" open="(" separator="," close=")">-->
<!--                #{institutionProviderId}-->
<!--            </foreach>-->
<!--        </if>-->
    </select>

    <select id="getReceivablePlanByTargetsAndType" resultType="com.get.salecenter.vo.ReceivablePlanVo">
        SELECT 	a.id,
        a.fk_type_target_id, a.fk_currency_type_num,c.type_name fkCurrencyTypeName,
        --  应收金额-->
        SUM(IFNULL(a.receivable_amount,0)) receivable_amount,
        -- 实收（折合金额+汇率调整）
        (SUM(IFNULL(b.sum_amount_receivable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0))) actual_receivable_amount,
        --  差额
        (SUM(IFNULL(a.receivable_amount,0)) - (SUM(IFNULL(b.sum_amount_receivable,0)) + SUM(IFNULL(b.sum_amount_exchange_rate,0)))) diff_receivable_amount
        FROM ais_sale_center.m_receivable_plan AS a
        LEFT JOIN (
        -- 计算每条应收计划里累计的实收金额
        SELECT a.fk_receivable_plan_id,
        SUM(IFNULL(a.amount_receivable,0)) sum_amount_receivable,
        SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd,0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb,0)) sum_amount_rmb
        FROM ais_finance_center.m_receipt_form_item a
        LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id=b.id
        -- 关闭的收款单不作计算
        WHERE b.`status`!=0
        GROUP BY a.fk_receivable_plan_id
        ) b ON a.id=b.fk_receivable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
        WHERE a.fk_type_key = #{fkTypeKey}
        AND a.fk_type_target_id IN
        <foreach collection="fkTypeTargetIds" item="fkTypeTargetId" open="(" separator="," close=")">
            #{fkTypeTargetId}
        </foreach>
        AND a.`status`!=0
        GROUP BY a.id,a.fk_type_target_id, a.fk_currency_type_num, c.type_name
    </select>


    <select id="getPlanIdByInstitutionProviderIds" resultType="java.lang.Long">
        SELECT
        r.id
        FROM
        m_receivable_plan r
        where r.fk_type_key = #{fkTypeKey}
        <if test="institutionProviderIds !=null and institutionProviderIds.size()>0">
            AND r.fk_type_target_id in
            <foreach collection="institutionProviderIds" item="institutionProviderId" open="(" separator="," close=")">
                #{institutionProviderId}
            </foreach>
        </if>
    </select>
    <select id="getPlanIdByCompanyIds" resultType="java.lang.Long">
        SELECT r.id from m_receivable_plan r
        left join m_student_offer_item i on r.fk_type_target_id=i.id
        where i.fk_student_offer_id in(
        SELECT o.id FROM  m_student_offer o
        left join m_student s on o.fk_student_id =s.id
        where 1=1
        <if test="fkCompanyIds != null and fkCompanyIds.size() > 0">
            and s.fk_company_id in
            <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="studentId !=null">
            and i.fk_student_id =#{studentId}
        </if>
        )
    </select>
    <select id="getPlanIdByCompanyIdsAndAccommodation" resultType="java.lang.Long">
        SELECT
        mrp.id
        FROM
        m_student_accommodation AS msa
        INNER JOIN
        m_student AS ms
        ON
        msa.fk_student_id = ms.id
        INNER JOIN
        m_receivable_plan AS mrp
        ON
        msa.id = mrp.fk_type_target_id AND
        mrp.fk_type_key = 'm_student_accommodation'
        WHERE 1=1
        <if test="fkCompanyIds != null and fkCompanyIds.size() > 0">
            and ms.fk_company_id in
            <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="studentId !=null">
            and msa.fk_student_id =#{studentId}
        </if>
    </select>
    <select id="getPlanIdByCompanyIdsAndInsurance" resultType="java.lang.Long">
        SELECT
        mrp.id
        FROM
        m_student_insurance AS msi
        INNER JOIN
        m_student AS ms
        ON
        msi.fk_student_id = ms.id
        INNER JOIN
        m_receivable_plan AS mrp
        ON
        msi.id = mrp.fk_type_target_id AND
        mrp.fk_type_key = 'm_student_insurance'
        WHERE 1=1
        <if test="fkCompanyIds != null and fkCompanyIds.size() > 0">
            and ms.fk_company_id in
            <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="studentId !=null">
            and msi.fk_student_id =#{studentId}
        </if>

    </select>
    <select id="getAllPlanIdByCompanyIds" resultType="java.lang.Long">
        SELECT
        r.id
        FROM
        m_receivable_plan r
        INNER JOIN m_student_offer_item i ON r.fk_type_target_id = i.id
        AND r.fk_type_key = "m_student_offer_item"
        INNER JOIN m_student s ON s.id = i.fk_student_id
        <if test="fkCompanyIds != null and fkCompanyIds.size() > 0">
            and s.fk_company_id in
            <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="studentId !=null">
            and i.fk_student_id =#{studentId}
        </if>
        UNION ALL
        SELECT
        mrp.id
        FROM
        m_student_accommodation AS msa
        INNER JOIN m_student AS ms ON msa.fk_student_id = ms.id
        INNER JOIN m_receivable_plan AS mrp ON msa.id = mrp.fk_type_target_id
        AND mrp.fk_type_key = "m_student_accommodation"
        WHERE 1=1
        <if test="fkCompanyIds != null and fkCompanyIds.size() > 0">
            and ms.fk_company_id in
            <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="studentId !=null">
            and msa.fk_student_id =#{studentId}
        </if>
        UNION ALL
        SELECT
        mrp.id
        FROM
        m_student_insurance AS msi
        INNER JOIN m_student AS ms ON msi.fk_student_id = ms.id
        INNER JOIN m_receivable_plan AS mrp ON msi.id = mrp.fk_type_target_id
        AND mrp.fk_type_key = "m_student_insurance"
        WHERE 1=1
        <if test="fkCompanyIds != null and fkCompanyIds.size() > 0">
            and ms.fk_company_id in
            <foreach collection="fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="studentId !=null">
            and msi.fk_student_id =#{studentId}
        </if>
        UNION ALL
        SELECT
        r.id
        FROM
        m_receivable_plan r
        where r.fk_type_key = "m_institution_provider"
        <if test="institutionProviderIds !=null and institutionProviderIds.size()>0">
            AND r.fk_type_target_id in
            <foreach collection="institutionProviderIds" item="institutionProviderId" open="(" separator="," close=")">
                #{institutionProviderId}
            </foreach>
        </if>
        UNION ALL
        SELECT
        r.id
        FROM
        m_receivable_plan r
        where r.fk_type_key = "other"
        <if test="institutionProviderIds !=null and institutionProviderIds.size()>0">
            AND r.fk_type_target_id in
            <foreach collection="institutionProviderIds" item="institutionProviderId" open="(" separator="," close=")">
                #{institutionProviderId}
            </foreach>
        </if>
    </select>
    <select id="getReceivablePlanByPayablePlanInfo" resultType="com.get.salecenter.entity.ReceivablePlan">
        SELECT * FROM m_receivable_plan AS mrp
        WHERE mrp.fk_type_key = #{fkTypeKey} AND mrp.fk_type_target_id = #{fkTypeTargetId} AND mrp.status = 1
    </select>

    <select id="getReceivablePlanNew" resultType="com.get.salecenter.vo.ReceivablePlanNewVo">
    SELECT p.* FROM (
        SELECT a.id as ids,
        a.fk_type_target_id,
        a.fk_company_id,
        b.duration,
        b.duration_type,
        IFNULL(b.is_defer_entrance,false) as is_defer_entrance,
        b.app_remark,
        e.short_name AS fk_company_name,
        a.tuition_amount,
        a.commission_rate,
        a.net_rate,
        a.commission_amount,
        a.fixed_amount,
        a.fk_type_key as fk_type_keys,
        a.receivable_plan_date,
        a.summary,
        a.`status`,
        a.gmt_create_user,
        a.gmt_create,
        a.fk_currency_type_num,
        <if test="receivablePlanNewDto.fkInvoiceId !=null and receivablePlanNewDto.fkInvoiceId != ''">
        r.id as invoiceReceivablePlanRelationId,
        r.amount as amount,
        r.is_pay_in_advance,
        r.pay_in_advance_amount,
        r.gmt_create as rgmt_create,
        </if>
        c.type_name as fkCurrencyTypeName,
        c.type_name as fk_currency_type_name,
        IFNULL(a.receivable_amount,0) receivable_amount,
        IFNULL(b2.sum_amount_receivable,0) sum_amount_receivable,
        IFNULL(b2.sum_amount_exchange_rate,0) sum_amount_exchange_rate,
        IFNULL(b2.sum_amount_hkd,0) sum_amount_hkd,
        IFNULL(b2.sum_amount_rmb,0) sum_amount_rmb,
        (IFNULL(b2.sum_amount_receivable,0) + IFNULL(b2.sum_amount_exchange_rate,0)) actual_receivable_amount,
        b2.receipt_currency_type_num as receiptCurrencyTypeNum,b2.paymentDate,
        (IFNULL(a.receivable_amount,0) -(IFNULL(b2.sum_amount_receivable,0) + IFNULL(b2.sum_amount_exchange_rate,0)) ) diff_receivable_amount,
        d.fk_invoice_ids, d.fk_invoice_num,
        z.*,
        a.bonus_type,
        a.bonus_amount,
        LENGTH(CONCAT(z.student_first_name,z.student_last_name)) as weights,
        CASE
        WHEN b1.formSize = 0  and b.is_no_commission = 0 and b.gmt_create >  #{receivablePlanNewDto.configRemindDto.reminderTime} THEN
        CASE
        WHEN b1.gmt_create > DATE_ADD(b.defer_opening_time,INTERVAL #{receivablePlanNewDto.configRemindDto.reminderDay3} DAY) THEN '2'
        WHEN b1.gmt_create > DATE_ADD(b.defer_opening_time,INTERVAL #{receivablePlanNewDto.configRemindDto.reminderDay2} DAY) THEN '1'
        ELSE '0' END ELSE '0'
        END AS reminderOpentimeStatus
        FROM ais_sale_center.m_receivable_plan a
        LEFT JOIN (
        SELECT a.fk_receivable_plan_id,
        GROUP_CONCAT(b.fk_currency_type_num) as receipt_currency_type_num,
        SUM(IFNULL(a.amount_receivable,0)) sum_amount_receivable,
        SUM(IFNULL(a.amount_exchange_rate,0)) sum_amount_exchange_rate,
        SUM(IFNULL(a.amount_hkd,0)) sum_amount_hkd,
        SUM(IFNULL(a.amount_rmb,0)) sum_amount_rmb,
        min(b.receipt_date) paymentDate
        FROM ais_finance_center.m_receipt_form_item a
        LEFT JOIN ais_finance_center.m_receipt_form b ON a.fk_receipt_form_id=b.id
        WHERE b.`status`!=0
        GROUP BY a.fk_receivable_plan_id
        ) b2 ON a.id=b2.fk_receivable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON a.fk_currency_type_num=c.num
        LEFT JOIN (
        SELECT a.fk_receivable_plan_id, GROUP_CONCAT(cast(a.fk_invoice_id as char)) as fk_invoice_ids,GROUP_CONCAT(b.num) as fk_invoice_num
        FROM ais_finance_center.r_invoice_receivable_plan a
        LEFT JOIN ais_finance_center.m_invoice b ON a.fk_invoice_id=b.id
        GROUP BY a.fk_receivable_plan_id
        ) d ON a.id=d.fk_receivable_plan_id
        LEFT JOIN ais_permission_center.m_company e ON a.fk_company_id=e.id

        LEFT JOIN
        (
        SELECT a.id, a.fk_type_key,b.fk_agent_id,x.majorLevelIds as majorLevelId,
        b.fk_area_country_id AS fk_area_country_ids, concat(c.`name`,'(',c.name_chn,')') AS fk_area_country_name,b.fk_staff_id AS fkStaffId,
        b.fk_student_id, d.`name` AS student_name, d.first_name AS student_first_name, d.last_name AS student_last_name, DATE_FORMAT(d.birthday, '%Y-%m-%d') AS
        student_birthday,d.num AS student_num,case d.gender when 0 then '女' when 1 then '男' ELSE '其他' end genderName,
        b.fk_institution_channel_id AS fk_institution_channel_id, e.`name` AS fk_institution_channel_name, NULL fk_institution_name, b.fk_institution_provider_id AS fk_institution_provider_id, f.`name` AS fk_institution_provider_name,
        f.name_chn AS fk_institution_provider_name_cn,
        b.fk_institution_id AS fk_institution_id, g.`name` AS ist_or_apm_name, g.short_name AS short_name_or_apa_start_time, g.name_chn AS stu_name_passport_apa_end, g.short_name_chn AS stu_cn_apa_day,
        b.fk_institution_course_id AS fk_institution_course_id, h.`name` AS fk_course_name, h.name_chn AS fk_course_name_cn, b.opening_time AS opening_time, b.defer_opening_time,
        NULL AS
        bzi_name24,b.old_institution_name,b.old_institution_full_name,b.old_course_custom_name,b.is_follow,b.fk_parent_student_offer_item_id,b.fk_student_offer_id as fk_student_offer_id,b.student_id,b.num as offer_item_num,
        IF(ucsot.type_mark is not null, CONCAT(cliCompany.short_name, '.', ucsot.type_mark), '') AS commissionMark,
        NULL AS insurantPassportNum,
        NULL AS insuranceInfo
        FROM ais_sale_center.m_receivable_plan a
        INNER JOIN ais_sale_center.m_student_offer_item b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_student_offer_item'
        LEFT JOIN ais_institution_center.u_area_country c ON b.fk_area_country_id=c.id
        LEFT JOIN ais_sale_center.m_student d ON b.fk_student_id=d.id
        LEFT JOIN ais_institution_center.m_institution_channel e ON b.fk_institution_channel_id=e.id
        LEFT JOIN ais_institution_center.m_institution_provider f ON b.fk_institution_provider_id=f.id
        LEFT JOIN ais_institution_center.m_institution g ON b.fk_institution_id=g.id
        LEFT JOIN ais_institution_center.m_institution_course h ON b.fk_institution_course_id=h.id

        LEFT JOIN ais_sale_center.m_client AS mc ON mc.id = d.fk_client_id
        LEFT JOIN ais_permission_center.m_company AS cliCompany ON cliCompany.id = mc.fk_company_id
        LEFT JOIN (SELECT fk_client_id,MAX(fk_table_name) AS fk_table_name FROM ais_sale_center.s_client_source GROUP BY fk_client_id) AS scs ON scs.fk_client_id = mc.id
        LEFT JOIN ais_sale_center.u_client_source_type AS ucsot ON ucsot.type_key = scs.fk_table_name

        LEFT join (
        SELECT
        l.fk_institution_course_id,
        GROUP_CONCAT(CAST(ml.id AS CHAR)) AS majorLevelIds
        FROM
        ais_institution_center.r_institution_course_major_level l
        LEFT JOIN ais_institution_center.u_major_level ml ON ml.id = l.fk_major_level_id
        GROUP BY
        l.fk_institution_course_id
        ) as x ON x.fk_institution_course_id = h.id
        where 1=1

        -- 开学时间
        <if test="receivablePlanNewDto.beginTime != null">
            AND  (
            DATE_FORMAT(b.defer_opening_time,'%Y-%m-%d') <![CDATA[>=]]> DATE_FORMAT(#{receivablePlanNewDto.beginTime},'%Y-%m-%d')
            )
        </if>
        <if test="receivablePlanNewDto.endTime != null">
            AND (
            DATE_FORMAT(b.defer_opening_time,'%Y-%m-%d') <![CDATA[<=]]> DATE_FORMAT(#{receivablePlanNewDto.endTime},'%Y-%m-%d')
            )
        </if>

        -- 课程长度
        <if test="receivablePlanNewDto.durationType !=null and receivablePlanNewDto.duration !=null">
            AND b.duration_type = #{receivablePlanNewDto.durationType}
            AND b.duration = #{receivablePlanNewDto.duration}
        </if>


        <if test="receivablePlanNewDto.beginTime == null or receivablePlanNewDto.endTime == null">
            UNION ALL
            SELECT a.id, a.fk_type_key,b.fk_agent_id,NULL as majorLevelId,
            b.fk_area_country_id AS fk_area_country_ids, concat(c.`name`,'(',c.name_chn,')') AS
            fk_area_country_name,b.fk_staff_id AS fkStaffId,
            b.fk_student_id,
            CASE
            WHEN b.type = 1
            THEN b.insurant_name
            ELSE d.`name` END AS student_name,
            CASE
            WHEN b.type = 1
            THEN b.insurant_first_name
            ELSE d.first_name END AS student_first_name,
            CASE
            WHEN b.type = 1
            THEN b.insurant_last_name
            ELSE d.last_name END AS student_last_name,
            DATE_FORMAT(d.birthday, '%Y-%m-%d') AS
            student_birthday,d.num AS student_num,case d.gender when 0 then '女' when 1 then '男' ELSE '其他' end
            genderName,
            b.fk_business_channel_id AS fk_institution_channel_id, e.`name` AS fk_institution_channel_name, e.name_chn
            AS fk_institution_name, NULL AS fk_institution_provider_id, NULL AS fk_institution_provider_name, NULL AS
            fk_institution_provider_name_cn,
            NULL AS fk_institution_id, DATE_FORMAT(b.insurance_start_time, '%Y-%m-%d') AS ist_or_apm_name,
            DATE_FORMAT(b.insurance_end_time, '%Y-%m-%d') AS short_name_or_apa_start_time,
            CASE
            WHEN b.type = 1
            THEN b.insurant_passport_num
            ELSE d.passport_num END AS stu_name_passport_apa_end,
            NULL AS stu_cn_apa_day,
            NULL AS fk_institution_course_id, NULL AS fk_course_name, NULL AS fk_course_name_cn, NULL AS opening_time,
            NULL AS defer_opening_time, NULL AS bzi_name24,NULL AS
            old_institution_name,NULL AS old_institution_full_name,NULL AS old_course_custom_name,NULL AS is_follow,NULL
            AS fk_parent_student_offer_item_id,NULL AS fk_student_offerId,NULL AS student_id,NULL as offer_item_num,
            NULL AS commissionMark,
            NULL AS insurantPassportNum,
            NULL AS insuranceInfo
        FROM ais_sale_center.m_receivable_plan a
        INNER JOIN ais_sale_center.m_student_insurance b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_student_insurance'
        LEFT JOIN ais_institution_center.u_area_country c ON b.fk_area_country_id=c.id
        LEFT JOIN ais_sale_center.m_student d ON b.fk_student_id=d.id
        LEFT JOIN ais_sale_center.m_business_channel e ON b.fk_business_channel_id=e.id
        UNION ALL
        SELECT a.id, a.fk_type_key,b.fk_agent_id,NULL as majorLevelId,
        b.fk_area_country_id AS fk_area_country_ids, concat(c.`name`,'(',c.name_chn,')') AS fk_area_country_name,b.fk_staff_id AS fkStaffId,
        b.fk_student_id, d.`name` AS student_name, d.first_name AS student_first_name, d.last_name AS student_last_name, DATE_FORMAT(d.birthday, '%Y-%m-%d') AS
        student_birthday,d.num AS student_num,case d.gender when 0 then '女' when 1 then '男' ELSE '其他' end genderName,
        b.fk_business_channel_id AS fk_institution_channel_id, e.`name` AS fk_institution_channel_name, e.name_chn AS fk_institution_name, b.fk_business_provider_id AS fk_institution_provider_id, f.NAME AS fk_institution_provider_name, NULL AS fk_institution_provider_name_cn,
        NULL AS fk_institution_id, b.apartment_name AS ist_or_apm_name, DATE_FORMAT(b.check_in_date, '%Y-%m-%d') AS short_name_or_apa_start_time, DATE_FORMAT(b.check_out_date, '%Y-%m-%d') AS
        stu_name_passport_apa_end, b.duration AS stu_cn_apa_day,
        NULL AS fk_institution_course_id, NULL AS fk_course_name, NULL AS fk_course_name_cn, NULL AS opening_time, NULL AS defer_opening_time, NULL AS bzi_name24,NULL AS
            old_institution_name,NULL AS old_institution_full_name,NULL AS old_course_custom_name,NULL AS is_follow,NULL AS
            fk_parent_student_offer_item_id,NULL AS fk_student_offerId,NULL AS student_id,NULL as offer_item_num,
        NULL AS commissionMark,
        NULL AS insurantPassportNum,
        NULL AS insuranceInfo
        FROM ais_sale_center.m_receivable_plan a
        INNER JOIN ais_sale_center.m_student_accommodation b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_student_accommodation'
        LEFT JOIN ais_institution_center.u_area_country c ON b.fk_area_country_id=c.id
        LEFT JOIN ais_sale_center.m_student d ON b.fk_student_id=d.id
        LEFT JOIN ais_sale_center.m_business_channel e ON b.fk_business_channel_id=e.id
        LEFT JOIN ais_sale_center.m_business_provider f ON b.fk_business_provider_id = f.id
        UNION ALL
            SELECT a.id,a.fk_type_key,NULL AS fkAgentId,NULL as majorLevelId,
            NULL AS fk_area_country_ids, NULL AS fk_area_country_name,NULL AS fkStaffId,
            NULL AS fk_student_id, NULL AS student_name, NULL AS student_first_name, NULL AS student_last_name, NULL AS student_birthday,NULL AS student_num,NULL AS genderName,
            b.id AS fk_institution_channel_id,  b.name AS fk_institution_channel_name, NULL AS fk_institution_name, NULL AS fk_institution_provider_id,NULL AS fk_institution_provider_name, b.name_chn AS fk_institution_provider_name_cn,
            NULL AS fk_institution_id, a.summary AS ist_or_apm_name, NULL AS short_name_or_apa_start_time, NULL AS stu_name_passport_apa_end, NULL AS stu_cn_apa_day,
            NULL AS fk_institution_course_id, NULL AS fk_course_name, NULL AS fk_course_name_cn, NULL AS opening_time, NULL AS defer_opening_time, NULL AS bzi_name24,NULL AS old_institution_name,NULL AS old_institution_full_name,
            NULL AS old_course_custom_name,NULL AS is_follow,NULL AS fk_parent_student_offer_item_id,NULL AS fk_student_offerId,NULL AS student_id,NULL as offer_item_num,
            NULL AS commissionMark,
            NULL AS insurantPassportNum,
            NULL AS insuranceInfo
            FROM ais_sale_center.m_receivable_plan a
            INNER JOIN ais_sale_center.m_business_channel b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_business_channel_ins'
        UNION ALL
        <!-- 服务费 -->
            SELECT a.id, a.fk_type_key,b.fk_agent_id,NULL as majorLevelId,
            b.fk_area_country_ids AS fk_area_country_ids, NULL AS fk_area_country_name,b.fk_staff_id AS fkStaffId,
            b.fk_student_id, d.`name` AS student_name, d.first_name AS student_first_name, d.last_name AS student_last_name, DATE_FORMAT(d.birthday, '%Y-%m-%d') AS
            student_birthday,d.num AS student_num,case d.gender when 0 then '女' when 1 then '男' ELSE '其他' end genderName,
            e.id AS fk_institution_channel_id, e.type_name AS fk_institution_channel_name, e.type_name AS fk_institution_name, NULL AS fk_institution_provider_id, NULL AS fk_institution_provider_name, NULL AS fk_institution_provider_name_cn,
            NULL AS fk_institution_id, NULL AS ist_or_apm_name, NULL AS short_name_or_apa_start_time, d.passport_num AS
            stu_name_passport_apa_end, NULL AS stu_cn_apa_day,
            NULL AS fk_institution_course_id, NULL AS fk_course_name, NULL AS fk_course_name_cn, NULL AS opening_time, NULL AS defer_opening_time, NULL AS bzi_name24,NULL AS
            old_institution_name,NULL AS old_institution_full_name,NULL AS old_course_custom_name,NULL AS is_follow,NULL AS fk_parent_student_offer_item_id,NULL AS fk_student_offerId,NULL AS student_id,
            NULL as offer_item_num,NULL AS commissionMark,
            NULL AS insurantPassportNum,
            NULL AS insuranceInfo
            FROM ais_sale_center.m_receivable_plan a
            INNER JOIN ais_sale_center.m_student_service_fee b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_student_service_fee'
            <!-- LEFT JOIN ais_institution_center.u_area_country c ON FIND_IN_SET(c.id,b.fk_area_country_ids) -->
            LEFT JOIN ais_sale_center.m_student d ON b.fk_student_id=d.id
            LEFT JOIN ais_sale_center.u_student_service_fee_type e ON b.fk_student_service_fee_type_id=e.id
        UNION ALL
            SELECT a.id,a.fk_type_key,NULL AS fkAgentId,NULL as majorLevelId,
            NULL AS fk_area_country_ids, NULL AS fk_area_country_name,NULL AS fkStaffId,
            NULL AS fk_student_id,NULL AS student_name,NULL AS student_first_name, NULL AS student_last_name,NULL AS student_birthday,
            NULL AS student_num,NULL AS genderName,
            b.id AS fk_institution_channel_id,  b.name AS fk_institution_channel_name, NULL AS fk_institution_name, NULL AS fk_institution_provider_id,NULL AS fk_institution_provider_name, b.name_chn AS fk_institution_provider_name_cn,
            NULL AS fk_institution_id, a.summary AS ist_or_apm_name, NULL AS short_name_or_apa_start_time, NULL AS stu_name_passport_apa_end, NULL AS stu_cn_apa_day,
            NULL AS fk_institution_course_id, NULL AS fk_course_name, NULL AS fk_course_name_cn, NULL AS opening_time, NULL AS defer_opening_time, NULL AS bzi_name24,
            NULL AS old_institution_name,NULL AS old_institution_full_name,NULL AS old_course_custom_name,NULL AS is_follow,NULL AS fk_parent_student_offer_item_id,
            NULL AS fk_student_offerId,NULL AS student_id,NULL as offer_item_num,NULL AS commissionMark,
            NULL AS insurantPassportNum,
            NULL AS insuranceInfo
            FROM ais_sale_center.m_receivable_plan a
            INNER JOIN ais_sale_center.m_business_channel b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_business_channel_acc'
        UNION ALL
        SELECT a.id, a.fk_type_key,NULL AS fkAgentId,NULL as majorLevelId,
        NULL AS fk_area_country_ids, NULL AS fk_area_country_name,NULL AS fkStaffId,
        NULL AS fk_student_id,NULL AS student_name,NULL AS student_first_name, NULL AS student_last_name,NULL AS student_birthday,
        NULL AS student_num,NULL AS genderName,
        NULL AS fk_institution_channel_id, NULL AS fk_institution_channel_name, NULL AS fk_institution_name, b.id AS fk_institution_provider_id, b.`name` AS fk_institution_provider_name, b.name_chn AS fk_institution_provider_name_cn,
        NULL AS fk_institution_id, a.summary AS ist_or_apm_name, NULL AS short_name_or_apa_start_time, NULL AS stu_name_passport_apa_end, NULL AS stu_cn_apa_day,
        NULL AS fk_institution_course_id, NULL AS fk_course_name, NULL AS fk_course_name_cn, NULL AS opening_time, NULL AS defer_opening_time, NULL AS bzi_name24,
        NULL AS old_institution_name,NULL AS old_institution_full_name,NULL AS old_course_custom_name,NULL AS is_follow,NULL AS fk_parent_student_offer_item_id,
        NULL AS fk_student_offerId,NULL AS student_id,NULL as offer_item_num,NULL AS commissionMark,
        NULL AS insurantPassportNum,
        NULL AS insuranceInfo
        FROM ais_sale_center.m_receivable_plan a
        INNER JOIN ais_institution_center.m_institution_provider b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_institution_provider'
        UNION  ALL
        SELECT a.id,a.fk_type_key,NULL AS fkAgentId,NULL as majorLevelId,
        NULL AS fk_area_country_ids, NULL AS fk_area_country_name,NULL AS fkStaffId,
        NULL AS fk_student_id,NULL AS student_name,NULL AS student_first_name, NULL AS student_last_name,NULL AS student_birthday,
        NULL AS student_num,NULL AS genderName,
        b.id AS fk_institution_channel_id,  b.name AS fk_institution_channel_name, NULL AS fk_institution_name, NULL AS fk_institution_provider_id,NULL AS fk_institution_provider_name, b.name_chn AS fk_institution_provider_name_cn,
        NULL AS fk_institution_id, a.summary AS ist_or_apm_name, NULL AS short_name_or_apa_start_time, NULL AS stu_name_passport_apa_end, NULL AS stu_cn_apa_day,
        NULL AS fk_institution_course_id, NULL AS fk_course_name, NULL AS fk_course_name_cn, NULL AS opening_time, NULL AS defer_opening_time, NULL AS bzi_name24,
        NULL AS old_institution_name,NULL AS old_institution_full_name,NULL AS old_course_custom_name,NULL AS is_follow,NULL AS fk_parent_student_offer_item_id,
        NULL AS fk_student_offerId,NULL AS student_id,NULL as offer_item_num,NULL AS commissionMark,
        NULL AS insurantPassportNum,
        NULL AS insuranceInfo
        FROM ais_sale_center.m_receivable_plan a
        INNER JOIN ais_institution_center.m_institution_channel b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_institution_channel'

        UNION ALL
        SELECT a.id,a.fk_type_key,NULL AS fkAgentId,NULL as majorLevelId,
        b.fk_area_country_id_to AS fk_area_country_ids, concat(c.`name`,'(',c.name_chn,')') AS fk_area_country_name,NULL AS fkStaffId,
        NULL AS fk_student_id, CONCAT(b.insurant_name, " (",b.insurant_first_name, " ", b.insurant_last_name, ")") AS student_name,NULL AS student_first_name, NULL AS student_last_name,NULL AS student_birthday,
        NULL AS student_num,NULL AS genderName,
        NULL AS fk_institution_channel_id,  NULL AS fk_institution_channel_name, NULL AS fk_institution_name, NULL AS fk_institution_provider_id,NULL AS fk_institution_provider_name, NULL AS fk_institution_provider_name_cn,
        NULL AS fk_institution_id, a.summary AS ist_or_apm_name, NULL AS short_name_or_apa_start_time, NULL AS stu_name_passport_apa_end, NULL AS stu_cn_apa_day,
        NULL AS fk_institution_course_id, NULL AS fk_course_name, NULL AS fk_course_name_cn, NULL AS opening_time, NULL AS defer_opening_time, NULL AS bzi_name24,
        NULL AS old_institution_name,NULL AS old_institution_full_name,NULL AS old_course_custom_name,NULL AS is_follow,NULL AS fk_parent_student_offer_item_id,
        NULL AS fk_student_offerId,NULL AS student_id,NULL as offer_item_num,NULL AS commissionMark,
        b.insurant_passport_num AS insurantPassportNum,
        CONCAT(upt.type_name,"(",upt.type_key,")： ",b.insurance_num,", ",b.insurance_type,", ",DATE_FORMAT( b.insurance_start_time, '%Y-%m-%d' ),"至", DATE_FORMAT( b.insurance_end_time, '%Y-%m-%d' )) AS insuranceInfo
        FROM ais_sale_center.m_receivable_plan a
        INNER JOIN app_insurance_center.m_insurance_order b ON a.fk_type_target_id=b.id AND a.fk_type_key='m_insurance_order'
        LEFT JOIN ais_institution_center.u_area_country c ON b.fk_area_country_id_to=c.id
        LEFT JOIN app_insurance_center.u_product_type AS upt ON upt.id = b.fk_product_type_id
        <!--UNION ALL
        SELECT a.id, a.fk_type_key,
        NULL AS fk_area_country_ids, NULL AS fk_area_country_name,
        NULL AS fk_student_id, NULL AS student_name, NULL AS student_first_name, NULL AS student_last_name, NULL AS student_birthday,
        NULL AS fk_institution_channel_id, NULL AS fk_institution_channel_name, NULL AS fk_institution_name, b.id AS fk_institution_provider_id, b.`name` AS fk_institution_provider_name, b.name_chn AS fk_institution_provider_name_cn,
        NULL AS fk_institution_id, a.summary AS ist_or_apm_name, NULL AS short_name_or_apa_start_time, NULL AS stu_name_passport_apa_end, NULL AS stu_cn_apa_day,
        NULL AS fk_institution_course_id, NULL AS fk_course_name, NULL AS fk_course_name_cn, NULL AS opening_time, NULL AS bzi_name24,NULL AS old_institution_name,NULL AS old_institution_full_name,NULL AS old_course_custom_name,NULL AS is_follow,NULL AS fk_parent_student_offer_item_id,NULL AS fk_student_offerId
        FROM ais_sale_center.m_receivable_plan a
        INNER JOIN ais_sale_center.m_event_bill c ON a.fk_type_target_id=c.id AND a.fk_type_key='m_event_bill'
        INNER JOIN ais_institution_center.m_institution_provider b ON c.fk_institution_provider_id = b.id-->
        </if>
        ) z ON a.id=z.id
        LEFT JOIN ais_sale_center.m_student_offer_item b on a.fk_type_target_id = b.id and a.fk_type_key='m_student_offer_item'
        <if test="receivablePlanNewDto.fkInvoiceId !=null and receivablePlanNewDto.fkInvoiceId != ''">
            LEFT JOIN ais_finance_center.r_invoice_receivable_plan  r on a.id = r.fk_receivable_plan_id
        </if>
        LEFT JOIN (
        SELECT r.fk_student_offer_item_id,max(r.gmt_create) as gmt_create,IF(COUNT(i.id) >0,1,0) as formSize
        FROM ais_sale_center.r_student_offer_item_step r
        LEFT JOIN ais_sale_center.m_receivable_plan a ON r.fk_student_offer_item_id = a.fk_type_target_id
        AND a.fk_type_key = 'm_student_offer_item'
        AND a.STATUS != 0
        LEFT JOIN ais_finance_center.m_receipt_form_item i on i.fk_receivable_plan_id = a.id
        where r.fk_student_offer_item_step_id = 6 GROUP BY r.fk_student_offer_item_id
        ) b1 on b1.fk_student_offer_item_id = b.id
        <if test="receivablePlanNewDto.isInvoiceFlag and receivablePlanNewDto.aprpTuitionFlag!=null">
            <choose>
                <when test="receivablePlanNewDto.aprpTuitionFlag">
                    INNER JOIN m_payable_plan mp ON mp.fk_receivable_plan_id = a.id AND mp.tuition_amount = a.tuition_amount
                </when>
                <otherwise>
                    INNER JOIN m_payable_plan mp ON mp.fk_receivable_plan_id = a.id AND mp.tuition_amount != a.tuition_amount
                </otherwise>
            </choose>
        </if>
        WHERE 1=1
        <if test="receivablePlanNewDto.fkInvoiceId !=null and receivablePlanNewDto.fkInvoiceId != ''">
            and r.fk_invoice_id=#{receivablePlanNewDto.fkInvoiceId}
        </if>
        <if test="receivablePlanNewDto.majorLevelId!=null">
            and FIND_IN_SET(#{receivablePlanNewDto.majorLevelId},z.majorLevelId) > 0
        </if>
        <if test="receivablePlanNewDto.beginTime != null or receivablePlanNewDto.endTime != null">
            AND z.defer_opening_time is not null
        </if>

        -- m_student_offer_item条件添加
        <if test="(receivablePlanNewDto.durationType !=null and receivablePlanNewDto.duration !=null) or receivablePlanNewDto.beginTime != null or receivablePlanNewDto.endTime != null">
            AND z.id is not null
            AND a.fk_type_key='m_student_offer_item'
        </if>

        <!--导出，支持勾选导出-->
        <if test="receivablePlanNewDto.exportIds != null and receivablePlanNewDto.exportIds.size > 0">
            AND a.id IN
            <foreach collection="receivablePlanNewDto.exportIds" item="exportId" open="(" separator="," close=")">
                #{exportId}
            </foreach>
        </if>

        AND a.`status`!=0
        <if test="receivablePlanNewDto.fkTypeKey != null and receivablePlanNewDto.fkTypeKey != '' ">
            AND a.fk_type_key = #{receivablePlanNewDto.fkTypeKey}
        </if>
        <if test="receivablePlanNewDto.fkAreaCountryIds != null and receivablePlanNewDto.fkAreaCountryIds != ''">
            AND FIND_IN_SET(#{receivablePlanNewDto.fkAreaCountryIds}, z.fk_area_country_ids)>0
        </if>
        <if test="receivablePlanNewDto.studentName != null and receivablePlanNewDto.studentName != ''">
            AND (
            REPLACE(CONCAT(LOWER(z.student_first_name),LOWER(z.student_last_name)),' ','') like concat('%',#{receivablePlanNewDto.studentName},'%')
            OR REPLACE(CONCAT(LOWER(z.student_last_name),LOWER(z.student_first_name)),' ','') like concat('%',#{receivablePlanNewDto.studentName},'%')
            OR REPLACE(LOWER(z.student_name),' ','') like concat('%',#{receivablePlanNewDto.studentName},'%')
            OR REPLACE(LOWER(z.student_last_name),' ','') like concat('%',#{receivablePlanNewDto.studentName},'%')
            OR REPLACE(LOWER(z.student_first_name),' ','') like concat('%',#{receivablePlanNewDto.studentName},'%')
            OR z.student_birthday = #{receivablePlanNewDto.studentName}
            OR REPLACE(LOWER(z.student_id),' ','') like concat('%',#{receivablePlanNewDto.studentName},'%')
            ) -- 过滤学生中英文名字
        </if>
        <if test="receivablePlanNewDto.bzoName != null and receivablePlanNewDto.bzoName != ''">
            AND (LOWER(z.fk_institution_channel_name) LIKE CONCAT('%',#{receivablePlanNewDto.bzoName},'%') OR LOWER(z.fk_institution_name) LIKE CONCAT('%',#{receivablePlanNewDto.bzoName},'%') OR
            LOWER(z.fk_institution_provider_name) LIKE CONCAT('%',#{receivablePlanNewDto.bzoName},'%') OR LOWER(z.fk_institution_provider_name_cn) LIKE CONCAT('%',#{receivablePlanNewDto.bzoName},'%'))
        </if>
        <if test="receivablePlanNewDto.fkStudentId != null and receivablePlanNewDto.fkStudentId != ''">
            AND z.fk_student_id = #{receivablePlanNewDto.fkStudentId}
        </if>
        <if test="receivablePlanNewDto.bziName != null and receivablePlanNewDto.bziName != ''">
            AND (LOWER(z.ist_or_apm_name) LIKE CONCAT('%',#{receivablePlanNewDto.bziName},'%')
            OR LOWER(z.short_name_or_apa_start_time) LIKE CONCAT('%',#{receivablePlanNewDto.bziName},'%')
            OR LOWER(z.stu_name_passport_apa_end) LIKE CONCAT('%',#{receivablePlanNewDto.bziName},'%')
            OR LOWER(z.stu_cn_apa_day) LIKE CONCAT('%',#{receivablePlanNewDto.bziName},'%')
            OR LOWER(z.fk_course_name) LIKE CONCAT('%',#{receivablePlanNewDto.bziName},'%')
            OR LOWER(z.fk_course_name_cn) LIKE CONCAT('%',#{receivablePlanNewDto.bziName},'%')
            OR LOWER(z.old_course_custom_name) LIKE CONCAT('%',#{receivablePlanNewDto.bziName},'%')
            OR LOWER(z.defer_opening_time) LIKE CONCAT('%',#{receivablePlanNewDto.bziName},'%')
            OR LOWER(z.bzi_name24) LIKE CONCAT('%',#{receivablePlanNewDto.bziName},'%'))
        </if>
        <if test="receivablePlanNewDto.bziNameSupplement != null and receivablePlanNewDto.bziNameSupplement != ''">
            AND (LOWER(z.ist_or_apm_name) LIKE CONCAT('%',#{receivablePlanNewDto.bziNameSupplement},'%')
            OR LOWER(z.stu_name_passport_apa_end) LIKE CONCAT('%',#{receivablePlanNewDto.bziNameSupplement},'%')
            OR LOWER(z.fk_course_name) LIKE CONCAT('%',#{receivablePlanNewDto.bziNameSupplement},'%')
            OR LOWER(z.fk_course_name_cn) LIKE CONCAT('%',#{receivablePlanNewDto.bziNameSupplement},'%')
            OR LOWER(z.old_course_custom_name) LIKE CONCAT('%',#{receivablePlanNewDto.bziNameSupplement},'%'))
        </if>
        <!-- 过滤学生佣金结算标记关键字 -->
        <if test="receivablePlanNewDto.commissionMark != null and receivablePlanNewDto.commissionMark != ''">
            AND LOWER(z.commissionMark) LIKE CONCAT('%',#{receivablePlanNewDto.commissionMark},'%')
        </if>

        <if test="receivablePlanNewDto.fkInvoiceNums != null and receivablePlanNewDto.fkInvoiceNums != ''">
<!--          AND FIND_IN_SET(REPLACE(#{receivablePlanNewDto.fkInvoiceNums}, ' ', ''), REPLACE(REPLACE(LOWER(d.fk_invoice_num), "'", "_"), ' ','')) > 0-->
            AND REPLACE(REPLACE(LOWER(d.fk_invoice_num), "'", "_"), ' ','') Like CONCAT('%',REPLACE(#{receivablePlanNewDto.fkInvoiceNums}, ' ', ''),'%' )
        </if>
        <if test="receivablePlanNewDto.receiveStatus == 0">
            AND ( (IFNULL(b2.sum_amount_receivable,0) + IFNULL(b2.sum_amount_exchange_rate,0))=0 AND IFNULL(a.receivable_amount,0) != 0)
        </if>
        <if test="receivablePlanNewDto.receiveStatus == 1">
            AND (IFNULL(b2.sum_amount_receivable,0) + IFNULL(b2.sum_amount_exchange_rate,0))>0
            AND (IFNULL(a.receivable_amount,0) - IFNULL(b2.sum_amount_receivable,0) + IFNULL(b2.sum_amount_exchange_rate,0)) > 0
        </if>
        <if test="receivablePlanNewDto.receiveStatus == 2">
            AND ((IFNULL(b2.sum_amount_receivable,0) + IFNULL(b2.sum_amount_exchange_rate,0)) - IFNULL(a.receivable_amount,0))=0
        </if>
        <if test="receivablePlanNewDto.receiveStatus == 3">
            AND (( (IFNULL(b2.sum_amount_receivable,0) + IFNULL(b2.sum_amount_exchange_rate,0))=0 AND IFNULL(a.receivable_amount,0) != 0) OR
            ((IFNULL(b2.sum_amount_receivable,0) + IFNULL(b2.sum_amount_exchange_rate,0))>0
            AND (IFNULL(a.receivable_amount,0) - IFNULL(b2.sum_amount_receivable,0) - IFNULL(b2.sum_amount_exchange_rate,0)) > 0)
            OR ((IFNULL(a.receivable_amount,0) - IFNULL(b2.sum_amount_receivable,0) - IFNULL(b2.sum_amount_exchange_rate,0)) <![CDATA[< ]]> 0)
            )
        </if>
        <if test="receivablePlanNewDto.uncollectedType != null and receivablePlanNewDto.uncollectedType == 1">
            AND (IFNULL(b2.sum_amount_receivable,0) + IFNULL(b2.sum_amount_exchange_rate,0))=0
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d')  &lt;= DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 90 DAY),'%Y-%m-%d ')
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') &gt;= DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 120 DAY),'%Y-%m-%d ')
        </if>

        <if test="receivablePlanNewDto.uncollectedType != null and receivablePlanNewDto.uncollectedType == 2">
            AND (IFNULL(b2.sum_amount_receivable,0) + IFNULL(b2.sum_amount_exchange_rate,0))=0
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d')  &lt;= DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 120 DAY),'%Y-%m-%d ')
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d') &gt;= DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 180 DAY),'%Y-%m-%d ')
        </if>
        <if test="receivablePlanNewDto.receivablePlanBeginDate!=null">
            AND DATE_FORMAT(a.receivable_plan_date,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{receivablePlanNewDto.receivablePlanBeginDate},'%Y-%m-%d')
        </if>
        <if test="receivablePlanNewDto.receivablePlanEndDate!=null">
            AND DATE_FORMAT(a.receivable_plan_date,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{receivablePlanNewDto.receivablePlanEndDate},'%Y-%m-%d')
        </if>
        <if test="receivablePlanNewDto.uncollectedType != null and receivablePlanNewDto.uncollectedType == 3">
            AND (IFNULL(b2.sum_amount_receivable,0) + IFNULL(b2.sum_amount_exchange_rate,0))=0
            AND DATE_FORMAT(a.gmt_create,'%Y-%m-%d')  &lt;= DATE_FORMAT(DATE_SUB(NOW(),INTERVAL 180 DAY),'%Y-%m-%d ')
        </if>
        <if test="receivablePlanNewDto.fkCompanyIds != null and receivablePlanNewDto.fkCompanyIds.size() > 0">
            AND a.fk_company_id in
            <foreach collection="receivablePlanNewDto.fkCompanyIds" item="fkCompanyId" index="index" open="(" separator="," close=")">
                #{fkCompanyId}
            </foreach>
        </if>
        <if test="receivablePlanNewDto.summary != null and receivablePlanNewDto.summary != ''">
            AND REPLACE(LOWER(a.summary),' ','') LIKE CONCAT('%',#{receivablePlanNewDto.summary},'%')
        </if>
        <if test="receivablePlanNewDto.bonusType != null and receivablePlanNewDto.bonusType != '' or receivablePlanNewDto.bonusType == 0">
            AND a.bonus_type = #{receivablePlanNewDto.bonusType}
        </if>
        <if test="receivablePlanNewDto.isBindingInvoice != null">
            AND
            <if test="receivablePlanNewDto.isBindingInvoice">
                EXISTS
            </if>
            <if test="!receivablePlanNewDto.isBindingInvoice">
                NOT EXISTS
            </if>
            (
            SELECT 1 FROM ais_finance_center.r_invoice_receivable_plan rirp WHERE a.id = rirp.fk_receivable_plan_id
            )
        </if>
        HAVING 1=1
        <if test="receivablePlanNewDto.receiptAmount != null and receivablePlanNewDto.receiptAmount != '' ">
            and ( receivable_amount = #{receivablePlanNewDto.receiptAmount} OR diff_receivable_amount = #{receivablePlanNewDto.receiptAmount} )
        </if>
    )p
        <choose>
            <when test="receivablePlanNewDto.studentName != null and receivablePlanNewDto.studentName != ''">
                ORDER BY p.weights ASC
            </when>
            <otherwise>
                <choose>
                    <when test="receivablePlanNewDto.isInvoiceFlag">
                        order by p.rgmt_create desc,p.invoiceReceivablePlanRelationId desc
                    </when>
                    <otherwise>
                        order by reminderOpentimeStatus desc,p.gmt_create desc
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>
    <select id="getReceivablePlanInfoByKeyATargetId" resultType="com.get.salecenter.vo.ReceivablePlanNewVo" parameterType="com.get.salecenter.dto.ReceivablePlanBatchDto">
        SELECT
        <if test="receivablePlanBatchDto.pageNumber!=null">
            DISTINCT
             p.id,p.`status`,p.fk_type_target_id,p.receivable_amount,p.bonus_type
        </if>
        <if test="receivablePlanBatchDto.pageNumber==null">
            count(DISTINCT(id)) as totalCount
        </if>

        FROM (
        SELECT p.id,p.`status`,p.fk_type_target_id,p.receivable_amount,p.bonus_type,p.fk_type_key FROM ais_sale_center.m_receivable_plan p
        <if test="receivablePlanBatchDto.typeKey == 'm_institution_channel' and receivablePlanBatchDto.typeKey!=''">
            INNER JOIN ais_sale_center.m_student_offer_item msoi
            on msoi.fk_institution_channel_id in
            <foreach collection="receivablePlanBatchDto.typeTargetIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND p.fk_type_target_id = msoi.id
            AND p.fk_type_key = 'm_student_offer_item'
            <if test="receivablePlanBatchDto.openingTime != null">
                AND DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{receivablePlanBatchDto.openingTime},'%Y-%m-%d')
                <if test="receivablePlanBatchDto.closingTime != null">
                    AND DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{receivablePlanBatchDto.closingTime},'%Y-%m-%d')
                </if>
            </if>
            <if test="receivablePlanBatchDto.studentName!=null and receivablePlanBatchDto.studentName!=''">AND
                msoi.fk_student_id in(
                SELECT
                s.id
                FROM
                m_student s
                WHERE
                REPLACE(
                CONCAT(s.first_name,s.last_name),' ','')
                like concat('%',#{receivablePlanBatchDto.studentName},'%')
                OR REPLACE(CONCAT(s.last_name,s.first_name),' ','')
                like concat('%',#{receivablePlanBatchDto.studentName},'%')
                OR REPLACE(s.`name`,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                OR REPLACE(s.last_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                OR REPLACE(s.first_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%'))
            </if>
        </if>
        <if test="receivablePlanBatchDto.typeKey == 'm_institution_provider' and receivablePlanBatchDto.typeKey!=''">
            INNER JOIN ais_sale_center.m_student_offer_item msoi
            on msoi.fk_institution_provider_id in
            <foreach collection="receivablePlanBatchDto.typeTargetIds" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
            AND p.fk_type_target_id = msoi.id
            AND p.fk_type_key = 'm_student_offer_item'
            <if test="receivablePlanBatchDto.openingTime != null">
                AND DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{receivablePlanBatchDto.openingTime},'%Y-%m-%d')
            </if>
            <if test="receivablePlanBatchDto.closingTime != null ">
                AND DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{receivablePlanBatchDto.closingTime},'%Y-%m-%d')
            </if>
        <if test="receivablePlanBatchDto.studentName!=null and receivablePlanBatchDto.studentName!=''">AND
            msoi.fk_student_id in(
            SELECT
            s.id
            FROM
            m_student s
            WHERE
            REPLACE(
            CONCAT(s.first_name,s.last_name),' ','')
            like concat('%',#{receivablePlanBatchDto.studentName},'%')
            OR REPLACE(CONCAT(s.last_name,s.first_name),' ','')
            like concat('%',#{receivablePlanBatchDto.studentName},'%')
            OR REPLACE(s.`name`,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
            OR REPLACE(s.last_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
            OR REPLACE(s.first_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%'))
        </if>
            <if test="receivablePlanBatchDto.openingTime == null">
                UNION ALL
                SELECT mrp.id,mrp.`status`,mrp.fk_type_target_id,mrp.receivable_amount,mrp.bonus_type,mrp.fk_type_key FROM ais_sale_center.m_receivable_plan mrp
                INNER JOIN ais_institution_center.m_institution_provider AS mip ON mip.id = mrp.fk_type_target_id AND mrp.fk_type_key = 'm_institution_provider' AND mrp.status = 1
                INNER JOIN ais_institution_center.r_institution_provider_institution_channel AS ripic ON ripic.fk_institution_provider_id = mip.id
                WHERE
                ripic.fk_institution_channel_id in
                <foreach collection="receivablePlanBatchDto.typeTargetIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                and mrp.status = 1
                GROUP BY mrp.id

                UNION ALL
                SELECT mrp.id,mrp.`status`,mrp.fk_type_target_id,mrp.receivable_amount,mrp.bonus_type,mrp.fk_type_key FROM ais_sale_center.m_receivable_plan mrp
                INNER JOIN ais_sale_center.m_student_service_fee AS mssf ON mssf.id = mrp.fk_type_target_id AND mrp.fk_type_key = 'm_student_service_fee' AND mrp.status = 1
                WHERE mssf.fk_type_key_receivable = 'm_institution_provider' AND mssf.fk_type_target_id_receivable in
                <foreach collection="receivablePlanBatchDto.typeTargetIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </if>
        <if test="receivablePlanBatchDto.openingTime == null">
            <if test="receivablePlanBatchDto.typeKey == 'm_business_channel_ins' and receivablePlanBatchDto.typeKey!=''">
                INNER JOIN m_student_insurance AS msi ON (msi.id = p.fk_type_target_id OR msi.fk_business_channel_id = p.fk_type_target_id) AND (p.
                fk_type_key = 'm_student_insurance' OR fk_type_key = 'm_business_channel_ins')
                <if test="receivablePlanBatchDto.studentName!=null and receivablePlanBatchDto.studentName!=''">
                    and msi.fk_student_id in(
                    SELECT
                    s.id
                    FROM
                    m_student_insurance  msi
                    LEFT JOIN m_student s ON msi.fk_student_id = s.id
                    WHERE
                    REPLACE(CONCAT(s.first_name,s.last_name),' ','')
                    like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(CONCAT(s.last_name,s.first_name),' ','')
                    like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.`name`,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.last_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.first_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(msi.`insurant_name`,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(msi.insurant_last_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(msi.insurant_first_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%'))
                </if>
                INNER JOIN m_business_channel AS mbc ON mbc.id=msi.fk_business_channel_id
                AND mbc.id in
                <foreach collection="receivablePlanBatchDto.typeTargetIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                 AND mbc.fk_type_key = 'm_student_insurance'
            </if>

            <if test="receivablePlanBatchDto.typeKey == 'm_business_provider_ins' and receivablePlanBatchDto.typeKey!=''">
                INNER JOIN m_student_insurance AS msi ON msi.id = p.fk_type_target_id AND p.fk_type_key = 'm_student_insurance'
                <if test="receivablePlanBatchDto.studentName!=null and receivablePlanBatchDto.studentName!=''">
                    AND msi.fk_student_id IN(
                        SELECT
                            s.id
                        FROM m_student_insurance msi
                        LEFT JOIN m_student s ON msi.fk_student_id = s.id
                        WHERE
                        REPLACE(CONCAT(s.first_name,s.last_name),' ','')
                        like concat('%',#{receivablePlanBatchDto.studentName},'%')
                        OR REPLACE(CONCAT(s.last_name,s.first_name),' ','')
                        like concat('%',#{receivablePlanBatchDto.studentName},'%')
                        OR REPLACE(s.`name`,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                        OR REPLACE(s.last_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                        OR REPLACE(s.first_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                        OR REPLACE(msi.`insurant_name`,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                        OR REPLACE(msi.insurant_last_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                        OR REPLACE(msi.insurant_first_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%'))
                </if>
                INNER JOIN m_business_provider mbp ON mbp.id = msi.fk_business_provider_id
                AND mbp.id IN
                <foreach collection="receivablePlanBatchDto.typeTargetIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                AND mbp.fk_type_key = 'm_student_insurance'
                UNION ALL
                    SELECT mrp.id,mrp.`status`,mrp.fk_type_target_id,mrp.receivable_amount,mrp.bonus_type,mrp.fk_type_key FROM ais_sale_center.m_receivable_plan mrp
                    INNER JOIN app_insurance_center.m_insurance_order AS mio ON mio.id = mrp.fk_type_target_id AND mrp.fk_type_key = 'm_insurance_order'
                    LEFT JOIN ais_sale_center.m_agent AS ma ON ma.id = mio.fk_agent_id
                    LEFT JOIN app_insurance_center.u_product_type AS upt ON upt.id = mio.fk_product_type_id
                <where>
                    <if test="receivablePlanBatchDto.studentName!=null and receivablePlanBatchDto.studentName!=''">
                        (
                        mio.insurance_num = CONCAT('%',#{receivablePlanBatchDto.studentName},'%')
                        OR mio.insurance_type LIKE CONCAT('%',#{receivablePlanBatchDto.studentName},'%')
                        OR mio.insurant_name LIKE CONCAT('%',#{receivablePlanBatchDto.studentName},'%')
                        OR ma.name LIKE CONCAT('%',#{receivablePlanBatchDto.studentName},'%')
                        OR upt.type_name LIKE CONCAT('%',#{receivablePlanBatchDto.studentName},'%')
                        OR upt.type_key LIKE CONCAT('%',#{receivablePlanBatchDto.studentName},'%')
                        )
                    </if>
                </where>
            </if>

            <if test="receivablePlanBatchDto.typeKey == 'm_business_channel_acc' and receivablePlanBatchDto.typeKey!=''">
                INNER JOIN m_student_accommodation AS msc ON (msc.id = p.fk_type_target_id OR msc.fk_business_channel_id = p.fk_type_target_id) AND (p.
                fk_type_key = 'm_student_accommodation' OR fk_type_key = 'm_business_channel_acc')
                <if test="receivablePlanBatchDto.studentName!=null and receivablePlanBatchDto.studentName!=''">
                    and msc.fk_student_id in(
                    SELECT
                    s.id
                    FROM
                    m_student s
                    WHERE
                    REPLACE(CONCAT(s.first_name,s.last_name),' ','')
                    like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(CONCAT(s.last_name,s.first_name),' ','')
                    like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.`name`,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.last_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.first_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%'))
                </if>
                INNER JOIN m_business_channel AS mbc ON mbc.id = msc.
                fk_business_channel_id AND mbc.id in
                <foreach collection="receivablePlanBatchDto.typeTargetIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                 AND mbc.fk_type_key ='m_student_accommodation'
            </if>

            <if test="receivablePlanBatchDto.typeKey == 'm_business_provider_acc' and receivablePlanBatchDto.typeKey!=''">
                INNER JOIN m_student_accommodation AS msc ON msc.id = p.fk_type_target_id AND p.
                fk_type_key = 'm_student_accommodation'
                INNER JOIN m_business_provider mbp ON mbp.id = msc.fk_business_provider_id
                <if test="receivablePlanBatchDto.studentName!=null and receivablePlanBatchDto.studentName!=''">
                    and msc.fk_student_id in(
                    SELECT
                    s.id
                    FROM
                    m_student s
                    WHERE
                    REPLACE(CONCAT(s.first_name,s.last_name),' ','')
                    like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(CONCAT(s.last_name,s.first_name),' ','')
                    like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.`name`,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.last_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.first_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%'))
                </if>
                INNER JOIN m_business_channel AS mbc ON mbc.id = msc.fk_business_channel_id
                AND mbp.id in
                <foreach collection="receivablePlanBatchDto.typeTargetIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
                AND mbc.fk_type_key ='m_student_accommodation'
            </if>

            <if test="receivablePlanBatchDto.typeKey == 'm_institution_provider'">
                UNION ALL
                SELECT mrp.id,mrp.`status`,mrp.fk_type_target_id,mrp.receivable_amount,mrp.bonus_type,mrp.fk_type_key FROM ais_sale_center.m_receivable_plan mrp
                <if test="receivablePlanBatchDto.studentName!=null and receivablePlanBatchDto.studentName!=''">
                    INNER JOIN m_student_offer_item m ON mrp.fk_type_target_id = m.id
                    INNER JOIN m_student s ON m.fk_student_id = s.id
                </if>
                WHERE
                1=1
                <if test="receivablePlanBatchDto.typeTargetIds!=null and receivablePlanBatchDto.typeTargetIds.size>0">
                    AND mrp.fk_type_target_id in
                    <foreach collection="receivablePlanBatchDto.typeTargetIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="receivablePlanBatchDto.studentName!=null and receivablePlanBatchDto.studentName!=''">
                    AND (REPLACE(CONCAT(s.first_name,s.last_name),' ','')
                    like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(CONCAT(s.last_name,s.first_name),' ','')
                    like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.`name`,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.last_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.first_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%'))
                </if>
                AND mrp.fk_type_key = 'm_institution_provider'
                and mrp.status = 1
            </if>
            <if test="receivablePlanBatchDto.typeKey == 'm_institution_channel' and receivablePlanBatchDto.typeKey!=''">
                UNION ALL
                SELECT mrp.id,mrp.`status`,mrp.fk_type_target_id,mrp.receivable_amount,mrp.bonus_type,mrp.fk_type_key FROM ais_sale_center.m_receivable_plan mrp
                <if test="receivablePlanBatchDto.studentName!=null and receivablePlanBatchDto.studentName!=''">
                    INNER JOIN m_student_offer_item m ON mrp.fk_type_target_id = m.id
                    INNER JOIN m_student s ON m.fk_student_id = s.id
                </if>
                WHERE
                1=1
                <if test="receivablePlanBatchDto.typeTargetIds!=null and receivablePlanBatchDto.typeTargetIds.size>0">
                    AND mrp.fk_type_target_id in
                    <foreach collection="receivablePlanBatchDto.typeTargetIds" item="item" separator="," open="(" close=")">
                        #{item}
                    </foreach>
                </if>
                <if test="receivablePlanBatchDto.studentName!=null and receivablePlanBatchDto.studentName!=''">
                    AND (REPLACE(CONCAT(s.first_name,s.last_name),' ','')
                    like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(CONCAT(s.last_name,s.first_name),' ','')
                    like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.`name`,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.last_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                    OR REPLACE(s.first_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%'))
                </if>
                AND mrp.fk_type_key = 'm_institution_channel'
                and mrp.status = 1
            </if>
        </if>
        <if test="receivablePlanBatchDto.typeKey == 'm_student'">
            INNER JOIN m_student_service_fee f ON f.id = p.fk_type_target_id
            INNER JOIN m_student s ON f.fk_student_id = s.id
            <if test="receivablePlanBatchDto.openingTime != null">
                INNER JOIN ais_sale_center.m_student_offer_item msoi
                AND DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{receivablePlanBatchDto.openingTime},'%Y-%m-%d')
                <if test="receivablePlanBatchDto.closingTime != null">
                    AND DATE_FORMAT(msoi.defer_opening_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{receivablePlanBatchDto.closingTime},'%Y-%m-%d')
                </if>
            </if>
            WHERE
            p.fk_type_key = 'm_student_service_fee'
            AND p.status = 1
            <if test="receivablePlanBatchDto.typeTargetIds!=null and receivablePlanBatchDto.typeTargetIds.size>0">
                AND f.fk_student_id in
                <foreach collection="receivablePlanBatchDto.typeTargetIds" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="receivablePlanBatchDto.studentName!=null and receivablePlanBatchDto.studentName!=''">
                AND (REPLACE(CONCAT(s.first_name,s.last_name),' ','')
                like concat('%',#{receivablePlanBatchDto.studentName},'%')
                OR REPLACE(CONCAT(s.last_name,s.first_name),' ','')
                like concat('%',#{receivablePlanBatchDto.studentName},'%')
                OR REPLACE(s.`name`,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                OR REPLACE(s.last_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%')
                OR REPLACE(s.first_name,' ','') like concat('%',#{receivablePlanBatchDto.studentName},'%'))
            </if>
        </if>
        ) p
        WHERE
            p.`status`= 1
            <if test="receivablePlanBatchDto.bonusType!=null">
                AND p.bonus_type = #{receivablePlanBatchDto.bonusType}
            </if>
        <if test="receivablePlanBatchDto.fkTypeKey != null and receivablePlanBatchDto.fkTypeKey != '' ">
            AND p.fk_type_key = #{receivablePlanBatchDto.fkTypeKey}
        </if>

        <if test="receivablePlanBatchDto.maxReceivableAmount != null and receivablePlanBatchDto.maxReceivableAmount != '' ">
            AND p.receivable_amount <![CDATA[<=]]> #{receivablePlanBatchDto.maxReceivableAmount}
        </if>

        <if test="receivablePlanBatchDto.minReceivableAmount != null and receivablePlanBatchDto.minReceivableAmount != '' ">
            AND p.receivable_amount <![CDATA[>=]]> #{receivablePlanBatchDto.minReceivableAmount}
        </if>

        <if test="receivablePlanBatchDto.pageNumber!=null and receivablePlanBatchDto.pageSize!=null">
            limit #{receivablePlanBatchDto.offset},#{receivablePlanBatchDto.pageSize}
        </if>
    </select>
    <select id="getReIdsAndCompanyName" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT
            p.id,
            c.short_name as name
        FROM
            m_receivable_plan p
        INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = p.id
        AND p.fk_type_key = #{typeKey}
        INNER JOIN ais_finance_center.r_receipt_form_invoice f ON f.fk_invoice_id = b.fk_invoice_id
        AND f.fk_receipt_form_id = #{receiptFormId}
        INNER JOIN ais_finance_center.m_receipt_form m ON m.id = f.fk_receipt_form_id
        AND m.fk_type_target_id = #{targetId}
        LEFT JOIN ais_permission_center.m_company c ON c.id = p.fk_type_target_id
        GROUP BY p.id
    </select>
    <select id="getReceiptFormReCount" resultType="java.lang.Integer">
        SELECT
            count(p.id)
        FROM
            m_receivable_plan p
        <if test="typeKey == 'm_business_channel_ins'">
            LEFT JOIN m_student_insurance m ON p.fk_type_target_id = m.id AND p.fk_type_key = 'm_student_insurance'
        </if>
        <if test="typeKey == 'm_business_channel_acc'">
            LEFT JOIN m_student_accommodation m ON p.fk_type_target_id = m.id AND p.fk_type_key = 'm_student_accommodation'
        </if>
        <if test="typeKey == 'm_business_channel_ins' or typeKey == 'm_business_channel_acc'">
            LEFT JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = p.id
            LEFT JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
            LEFT JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id AND d.fk_type_key = #{typeKey}
            WHERE
            m.fk_business_channel_id = #{targetId}
            AND d.id = #{receiptFormId}
            AND m.STATUS = 1
            AND p.STATUS = 1
            GROUP BY p.id
        </if>
        <if test="typeKey == 'm_institution_provider'">
            INNER JOIN (
            SELECT
            DISTINCT
            a.id
            FROM
            m_receivable_plan a
            INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = a.id
            INNER JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
            INNER JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id and d.fk_type_key = 'm_institution_provider'
            WHERE
            d.fk_type_target_id = #{targetId}
            AND d.id = #{receiptFormId}
            ) a ON a.id = p.id
            LEFT JOIN m_student_offer_item b ON b.id = p.fk_type_target_id and p.fk_type_key = 'm_student_offer_item'
            LEFT JOIN ais_institution_center.u_area_country c ON c.id = b.fk_area_country_id
            LEFT JOIN ais_institution_center.m_institution d ON d.id = b.fk_institution_id
            LEFT JOIN ais_institution_center.m_institution_provider e ON e.id = b.fk_institution_provider_id
            LEFT JOIN ais_institution_center.m_institution_course f ON f.id = b.fk_institution_course_id
            LEFT JOIN m_student g ON g.id = b.fk_student_id
            LEFT JOIN m_agent h ON h.id = b.fk_agent_id
        </if>

    </select>
    <select id="getReceiptFormReCountByChannel" resultType="java.lang.Integer">
        SELECT
	        COUNT(*)
        FROM
	    (
		SELECT DISTINCT
			i.id AS planId
		FROM
			m_receivable_plan i
		INNER JOIN (
			SELECT
				a.id
			FROM
				m_receivable_plan a
			INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = a.id
			INNER JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
			INNER JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id
			AND d.fk_type_key = 'm_institution_channel'
			WHERE
				d.fk_type_target_id = #{targetId}
			AND d.id = #{receiptFormId}
		) a ON a.id = i.id
		INNER JOIN m_student_offer_item b ON b.id = i.fk_type_target_id
		AND i.fk_type_key = 'm_student_offer_item'
		LEFT JOIN ais_institution_center.u_area_country c ON c.id = b.fk_area_country_id
		LEFT JOIN ais_institution_center.m_institution d ON d.id = b.fk_institution_id
		LEFT JOIN ais_institution_center.m_institution_provider e ON e.id = b.fk_institution_provider_id
		LEFT JOIN ais_institution_center.m_institution_course f ON f.id = b.fk_institution_course_id
		LEFT JOIN m_student g ON g.id = b.fk_student_id
		LEFT JOIN m_agent h ON h.id = b.fk_agent_id
		UNION ALL
			SELECT DISTINCT
				i.id AS planId
			FROM
				m_receivable_plan i
			INNER JOIN (
				SELECT
					a.id
				FROM
					m_receivable_plan a
				INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = a.id
				INNER JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
				INNER JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id
				AND d.fk_type_key = 'm_institution_channel'
				WHERE
					d.fk_type_target_id = #{targetId}
				AND d.id = #{receiptFormId}
			) a ON a.id = i.id
			INNER JOIN ais_institution_center.m_institution_channel b ON b.id = i.fk_type_target_id
			AND i.fk_type_key = 'm_institution_channel'
	) TOTAL
    </select>
    <select id="getReceivablePlanRemarkDetails" resultType="com.get.salecenter.vo.PlanRemarkDetailResultVo">
        SELECT
            *
        FROM
            (
                SELECT
                    a1.id,
                    c.remark scoreRemark,
                    c.education_project,
                    c.education_degree,
                    d.remark studentOfferRemark,
                    GROUP_CONCAT(IF(e.remark !='',CONCAT(f.step_name,":",e.remark),null)) stepRemarks,
                    c.id fkStudentId,
                    b.id fkStudentOfferItemId,
                    NULL as insuranceRemark,
                    NULL as accommodationRemark
                FROM
                    m_receivable_plan a1
                        INNER JOIN m_student_offer_item b on a1.fk_type_key = 'm_student_offer_item' and a1.fk_type_target_id = b.id
                        LEFT JOIN m_student c on b.fk_student_id = c.id
                        LEFT JOIN m_student_offer d on d.id = b.fk_student_offer_id
                        LEFT JOIN r_student_offer_item_step e on e.fk_student_offer_item_id = b.id
                        LEFT JOIN u_student_offer_item_step f on e.fk_student_offer_item_step_id = f.id
                where 1=1
                <if test="planIds !=null and planIds.size() > 0">
                      and a1.id in
                    <foreach collection="planIds" item="planId" index="index" open="(" separator="," close=")">
                        #{planId}
                    </foreach>
                </if>
                GROUP BY a1.id
                UNION All
                SELECT
                    a2.id,
                    c.remark scoreRemark,
                    c.education_project,
                    c.education_degree,
                    NULL as studentOfferRemark,
                    NULL as stepRemarks,
                    c.id as fkStudentId,
                    NULL as fkStudentOfferItemId,
                    b.remark insuranceRemark,
                    NULL as accommodationRemark
                FROM
                    m_receivable_plan a2
                        INNER JOIN m_student_insurance b on a2.fk_type_key = 'm_student_insurance' and a2.fk_type_target_id = b.id
                        LEFT JOIN m_student c on b.fk_student_id = c.id
                where 1=1
                <if test="planIds !=null and planIds.size() > 0">
                    and a2.id in
                    <foreach collection="planIds" item="planId" index="index" open="(" separator="," close=")">
                        #{planId}
                    </foreach>
                </if>
                GROUP BY a2.id
                UNION All
                SELECT
                    a3.id,
                    c.remark scoreRemark,
                    c.education_project,
                    c.education_degree,
                    NULL as studentOfferRemark,
                    NULL as stepRemarks,
                    c.id as fkStudentId,
                    NULL as fkStudentOfferItemId,
                    NULL as insuranceRemark,
                    b.remark accommodationRemark
                FROM
                    m_receivable_plan a3
                        INNER JOIN m_student_accommodation b on a3.fk_type_key = 'm_student_accommodation' and a3.fk_type_target_id = b.id
                        LEFT JOIN m_student c on b.fk_student_id = c.id
                where 1=1
                <if test="planIds !=null and planIds.size() > 0">
                    and a3.id in
                    <foreach collection="planIds" item="planId" index="index" open="(" separator="," close=")">
                        #{planId}
                    </foreach>
                </if>
                GROUP BY a3.id
            ) z
    </select>
    <select id="getRePlanTheLatestThreeTuitionFees" resultType="java.lang.String">
        SELECT tuition_amount FROM m_receivable_plan WHERE fk_company_id = #{fkCompanyId}  ORDER BY id DESC LIMIT 3
    </select>
    <select id="getReceivablePlansInfoByType" resultType="com.get.salecenter.vo.ReceivablePlanVo">
        <!--#拆分应收币种，并累计对应币种的应收计划-->
        SELECT
            a.*
        FROM ais_sale_center.m_receivable_plan a

        WHERE 1=1
        <if test="itemId!=null and itemId!=''">
            and a.fk_type_target_id = #{itemId}
        </if>
        <if test="typeKey!=null and typeKey!=''">
            and a.fk_type_key = #{typeKey}
        </if>
    </select>
    <select id="getReceiptAmountByIds" resultType="com.get.salecenter.vo.PublicReceiptFormDetailVo">
        SELECT
        <!--币种-->
        CONCAT( c.type_name, "(", c.num, "）" ) AS receiptCurrencyTypeName,
        <!--实收（折合金额+汇率调整）-->
        IFNULL( i.amount_receivable, 0 ) + IFNULL( i.amount_exchange_rate, 0 ) AS actualReceiptAmount,
        <!--付款时间-->
        i.gmt_create as actualReceiptTime,
        p.id
        FROM
        ais_finance_center.m_receipt_form_item i
        INNER JOIN m_receivable_plan p ON p.id = i.fk_receivable_plan_id
        LEFT JOIN ais_finance_center.u_currency_type c ON p.fk_currency_type_num=c.num
        <if test="ids!=null and ids.size>0">
            where p.id in
            <foreach collection="ids" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
    </select>
    <select id="getReceivableAmountInfo" resultType="com.get.salecenter.vo.ReceivablePlanVo">
        SELECT
            p.id,
            p.receivable_amount,
            p.tuition_amount,
            p.commission_amount,
            p.commission_rate,
            p.net_rate,
            p.fk_currency_type_num,
            b.amount_receivable
        FROM
            m_receivable_plan p
        LEFT JOIN (
            SELECT
                i.fk_receivable_plan_id as id,
                sum(
                    IFNULL(i.amount_receivable, 0)
                ) + sum(IFNULL(i.amount_exchange_rate, 0)) AS amount_receivable
            FROM
                ais_finance_center.m_receipt_form_item i
            INNER JOIN ais_finance_center.m_receipt_form f ON f.id = i.fk_receipt_form_id
            GROUP BY
                i.fk_receivable_plan_id
        ) AS b ON b.id = p.id
        WHERE
            1=1
            <if test="receivablePlanIds!=null and receivablePlanIds.size>0">
                AND p.id IN
                <foreach collection="receivablePlanIds" item="cid" open="(" separator="," close=")">
                    #{cid}
                </foreach>
            </if>
    </select>
    <select id="getIninvoiceAmounts" resultType="com.get.salecenter.vo.IninvoiceAmountVo">
        select p.fk_type_target_id as id,COALESCE(SUM(CAST(rirp.amount AS DECIMAL)), 0) as ininvoiceAmount
        from ais_sale_center.m_receivable_plan p
        left join ais_finance_center.r_invoice_receivable_plan rirp on p.id = rirp.fk_receivable_plan_id
        where p.status != 0 and p.fk_type_key = 'm_student_offer_item'
        <if test="ids!=null and ids.size>0">
            and p.fk_type_target_id in
            <foreach collection="ids" item="cid" open="(" separator="," close=")">
                #{cid}
            </foreach>
        </if>
        GROUP BY p.fk_type_target_id
        ORDER BY ininvoiceAmount
    </select>
    <select id="getReceivablePlanSelectByProvider" resultType="com.get.core.mybatis.base.BaseSelectEntity">
        SELECT i.id, CASE WHEN IFNULL(mip.name_chn,'') = '' THEN  mip.name ELSE CONCAT(mip.name, '（', mip.name_chn, '）' )  END AS name
        FROM
            m_receivable_plan i
        INNER JOIN (
                SELECT
                    a.id
                FROM
                    m_receivable_plan a
                        INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = a.id
                        INNER JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
                        INNER JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id and d.fk_type_key = 'm_institution_provider'
                WHERE
                    d.fk_type_target_id = #{providerId}
                  AND d.id = #{receiptFormId}
                  and a.status = 1
                  and d.status = 1
            ) a ON a.id = i.id
            INNER JOIN ais_institution_center.m_institution_provider AS mip ON mip.id = i.fk_type_target_id AND  i.fk_type_key = 'm_institution_provider'
        WHERE
            i.fk_type_target_id = #{providerId}
                AND i.status = 1

        UNION ALL

        SELECT
            i.id,
            GROUP_CONCAT(
                    "【",
                    b.num,
                    "】",
                    "/",
                    CONCAT( g.`name`, '（', IF(g.last_name IS NULL, '', g.last_name), ' ', g.first_name, '）' ),
                    "/",
                    c.`name`,
                    "/",
                    CONCAT( e.`name`, '（', e.`name_chn`, '）' ),
                    "/",
                    COALESCE ( CONCAT( d.`name`, IF (d.name_chn IS NULL OR d.name_chn = '', '', CONCAT("（", d.name_chn, "）")) ),
                               b.old_institution_name ),
                    "/",
                    COALESCE ( CONCAT( f.`name`, IF (f.name_chn IS NULL OR f.name_chn = '', '', CONCAT("（", f.name_chn, "）")) ),
                               b.old_course_custom_name ),
                    "/",
                    h.`name`
            ) AS NAME
        FROM
            m_receivable_plan i
                INNER JOIN (
                SELECT
                    a.id
                FROM
                    m_receivable_plan a
                        INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = a.id
                        INNER JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
                        INNER JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id and d.fk_type_key = 'm_institution_provider'
                WHERE
                    d.fk_type_target_id = #{providerId}
                  AND d.id = #{receiptFormId}
                  and a.status = 1
                  and d.status = 1
            ) a ON a.id = i.id
                INNER JOIN m_student_offer_item b ON b.id = i.fk_type_target_id and i.fk_type_key = 'm_student_offer_item'
                LEFT JOIN ais_institution_center.u_area_country c ON c.id = b.fk_area_country_id
                LEFT JOIN ais_institution_center.m_institution d ON d.id = b.fk_institution_id
                LEFT JOIN ais_institution_center.m_institution_provider e ON e.id = b.fk_institution_provider_id
                LEFT JOIN ais_institution_center.m_institution_course f ON f.id = b.fk_institution_course_id
                LEFT JOIN m_student g ON g.id = b.fk_student_id
                LEFT JOIN m_agent h ON h.id = b.fk_agent_id
        GROUP BY i.id

        UNION ALL

        SELECT i.id,
               CONCAT(CASE WHEN IFNULL(CONCAT(ms.first_name,ms.last_name), '') = '' THEN ms.name ELSE CONCAT(ms.name, '（', CONCAT(ms.first_name," ",ms.last_name), '）') END,
                                       "/",
                      (SELECT GROUP_CONCAT(name) FROM ais_institution_center.u_area_country WHERE FIND_IN_SET(id,mssf.fk_area_country_ids)),
                                    "/",
                      "【学校提供商】",
                                   "/",
                          CASE WHEN IFNULL(mip.name_chn,'') = '' THEN  mip.name ELSE CONCAT(mip.name, '（', mip.name_chn, '）' )  END,
                      "/",
                      mssft.type_name
               ) AS name
        FROM
            ais_sale_center.m_receivable_plan i
                INNER JOIN (
                SELECT
                    a.id
                FROM
                    m_receivable_plan a
                        INNER JOIN ais_finance_center.r_invoice_receivable_plan b ON b.fk_receivable_plan_id = a.id
                        INNER JOIN ais_finance_center.r_receipt_form_invoice c ON c.fk_invoice_id = b.fk_invoice_id
                        INNER JOIN ais_finance_center.m_receipt_form d ON d.id = c.fk_receipt_form_id and d.fk_type_key = 'm_institution_provider'
                WHERE
                    d.fk_type_target_id = #{providerId}
                  AND d.id = #{receiptFormId}
                  and a.status = 1
                  and d.status = 1
            ) a ON a.id = i.id
                INNER JOIN ais_sale_center.m_student_service_fee AS mssf ON mssf.id = i.fk_type_target_id AND i.fk_type_key = 'm_student_service_fee'
                INNER JOIN ais_institution_center.m_institution_provider AS mip ON mip.id = mssf.fk_type_target_id_receivable
                INNER JOIN ais_sale_center.m_student AS ms ON ms.id = mssf.fk_student_id
                LEFT JOIN ais_sale_center.u_student_service_fee_type AS mssft ON mssft.id = mssf.fk_student_service_fee_type_id
            WHERE mssf.fk_type_key_receivable = 'm_institution_provider'
            GROUP BY i.id
    </select>

</mapper>