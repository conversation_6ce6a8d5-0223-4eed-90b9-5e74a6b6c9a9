package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.StudentOfferItemPreMajorLevel;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface StudentOfferItemPreMajorLevelMapper extends BaseMapper<StudentOfferItemPreMajorLevel> {
    int insert(StudentOfferItemPreMajorLevel record);

    int insertSelective(StudentOfferItemPreMajorLevel record);

    int updateById(StudentOfferItemPreMajorLevel record);

    int updateByPrimaryKey(StudentOfferItemPreMajorLevel record);
}