package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.vo.EnrolFailureReasonVo;
import com.get.salecenter.entity.EnrolFailureReason;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/6/16
 * @TIME: 16:03
 * @Description:
 **/
@Mapper
public interface EnrolFailureReasonMapper extends BaseMapper<EnrolFailureReason> {


    /**
     * @return java.lang.Integer
     * @Description:查找最大排序序号
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();

    /**
     * 失败步骤状态栏
     *
     * @Date 10:24 2021/7/22
     * <AUTHOR>
     */
    List<EnrolFailureReasonVo> getStudentsFailureState(@Param("companyId") Long companyId,
                                                       @Param("studentIds") List<Long> studentIdList,
                                                       @Param("userNames") List<String> userNames,
                                                       @Param("beginTime") Date beginTime,
                                                       @Param("endTime") Date endTime,
                                                       @Param("staffId") Long staffId,
                                                       @Param("areaCountryIds") List<Long> areaCountryIds,
                                                       @Param("countryIds") List<Long> countryIds);

}
