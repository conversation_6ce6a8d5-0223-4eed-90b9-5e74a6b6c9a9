package com.get.salecenter.dao.sale;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.salecenter.vo.ClientAgentVo;
import com.get.salecenter.entity.ClientAgent;
import com.get.salecenter.dto.ClientAgentDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * author:<PERSON>
 * Time: 15:49
 * Date: 2022/8/17
 * Description:
 */
@Mapper
public interface ClientAgentMapper extends GetMapper<ClientAgent> {

    /**
     * 获取绑定的代理
     *
     * @Date 14:10 2022/8/17
     * <AUTHOR>
     */
    List<ClientAgentVo> getAgentStaffNameByClientId(@Param(value = "id") Long id);

    /**
     *
     * @param clientIds
     * @return
     */
    List<ClientAgentVo> getAgentNameByClientIds(@Param(value = "clientIds")Set<Long> clientIds);

    /**
     *
     * @param clientIds
     * @return
     */
    List<ClientAgentVo> getBdCodeByClientIds(@Param(value = "clientIds")Set<Long> clientIds);


    /**
     * 获取客户代理
     * @param fkAgentId
     * @param fkClientId
     * @return
     */
    Integer exitsClientAgent(@Param("fkAgentId") Long fkAgentId,@Param("fkClientId") Long fkClientId,@Param("isActive") Boolean isActive);

    /**
     * 获取客户代理列表
     * @param iPage
     * @param agentVo
     * @return
     */
    List<ClientAgentVo> datas(IPage<ClientAgent> iPage, @Param("agentDto") ClientAgentDto agentVo);
}
