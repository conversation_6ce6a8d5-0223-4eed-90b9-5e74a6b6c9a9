package get.middlecenter.service;

import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoAndItemDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoSearchDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.vo.MenuTreeVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.permissioncenter.vo.ResourceVo;
import java.util.List;


/**
 * 发版信息
 */
public interface ReleaseInfoAndItemService {

    ResponseBo getReleaseInfoAndItem(SearchBean<ReleaseInfoSearchDto> page);

    void addReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    void deleteReleaseInfoAndItem(Long id);

    ReleaseInfoAndItemVo getDetailedInformationById(Long id);

    void editReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    List<ResourceVo> getAisPermissionMenu();

    List<MenuTreeVo> getPartnerPermissionMenu(GetPermissionMenuDto getPermissionMenuDto);

    void updateReleaseInfoStatus(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    ResponseBo getUserListByResourceKeys(SearchBean<UserScopedDataDto> searchBean);

    ReleaseInfoAndItemVo getUserOneByResourceKeys(UserScopedDataDto userScopedDataDto);
}