package com.get.aisplatformcenter.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.get.aisplatformcenter.mapper.MFilePlatformMapper;
import com.get.aisplatformcenter.service.ITencentCloudService;
import com.get.aisplatformcenter.service.PlatformFileService;
import com.get.aisplatformcenter.util.AppendixUtils;
import com.get.aisplatformcenterap.entity.MFilePlatformEntity;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Service
@Slf4j
public class PlatformFileServiceImpl implements PlatformFileService {

    //图片存储地址-公开桶
    @Value("${file.tencentcloudimage.bucketname}")
    private String imageBucketName;

    //文件存储
    @Value("${file.tencentcloudfile.bucketname}")
    private String fileBucketName;

    @Resource
    private UtilService utilService;


    @Resource
    private ITencentCloudService tencentCloudService;
    @Resource
    private MFilePlatformMapper platFormMapper;


    @Override
    public List<MFilePlatformEntity> uploadAppendix(MultipartFile[] files, Boolean isPub) {
        List<MFilePlatformEntity> datas = new ArrayList<>();
        for (MultipartFile file : files) {
            String fileurl = AppendixUtils.getFilePath(file);
            String bucketName = fileBucketName;//私有桶
            if(isPub){
                fileurl = AppendixUtils.getFileHtiPath(file);//公开桶
                bucketName=imageBucketName;//公开桶
            }
            MFilePlatformEntity fileVo = saveFileInfo(bucketName, file, fileurl, getAppendixPerfix(),isPub);
            datas.add(fileVo);

        }
        return datas;
    }

    @DSTransactional
    public MFilePlatformEntity saveFileInfo(String bucketName, MultipartFile file, String fileurl,String perfix, boolean isPub) {
        MFilePlatformEntity filePlatformEntity = null;
        if (ObjectUtil.isNotEmpty(perfix)) {
            fileurl = perfix + fileurl;
        }
        String filename = file.getOriginalFilename();
        int i = filename.lastIndexOf(".");
        int j = fileurl.lastIndexOf("/");
        //获取后缀名
        String substring = filename.substring(i, filename.length()).toLowerCase();
        //获取目标文件名称
        String targetFileName = fileurl.substring(j + 1, fileurl.length());
        String ossPath = subString(fileurl);//如：/m_file_finance/files/2021/09/07/67db88c4-e052-42be-b13f-de487fd7aa98.jpg


        filePlatformEntity = new MFilePlatformEntity();
        filePlatformEntity.setFilePath(fileurl);
        filePlatformEntity.setFileNameOrc(filename);
        filePlatformEntity.setFileTypeOrc(substring);
        filePlatformEntity.setFileName(targetFileName);
        filePlatformEntity.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));

        tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
        filePlatformEntity.setFileKey(ossPath);
        filePlatformEntity.setGmtCreate(new Date());
        filePlatformEntity.setGmtCreateUser(SecureUtil.getLoginId());
        platFormMapper.insert(filePlatformEntity);
        return filePlatformEntity;
    }

    static String subString(String url) {
        if (url.contains("target")) {
            url = url.replace("/data/project/get/app-platform-center/target", "");
            url = url.replace("/data/project/get/app-file-center/target", "");
            return url;
        } else {
            return url;
        }

    }

    private String getAppendixPerfix() {
        return "/data/project/get/app-platform-center/target";
    }
}
