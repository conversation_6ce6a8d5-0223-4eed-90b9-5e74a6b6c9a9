package com.get.aisplatformcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.aisplatformcenterap.dto.ULabelTypeDto;
import com.get.aisplatformcenterap.entity.ULabelTypeEntity;
import com.get.aisplatformcenterap.vo.ULabelTypeVo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
* <AUTHOR>
* @description 针对表【u_label_type】的数据库操作Service
* @createDate 2024-12-19 11:15:30
*/
public interface ULabelTypeService extends IService<ULabelTypeEntity> {

    List<ULabelTypeVo> searchPage(ULabelTypeDto data, Page page);

    Long addULabelType(ULabelTypeDto uLabelTypeDto);

    ULabelTypeVo updateULabelType(ULabelTypeDto uLabelTypeDto);

    ResponseBo delete(Long id);

    ResponseBo batchDelete(Set<Long> uLabelTypes);

    ULabelTypeVo findULabelTypeById(Long id);

    void export(ULabelTypeDto uLabelTypeDto,HttpServletResponse response);

    //Boolean check(String typeKey);

    List<ULabelTypeVo> selectLabelType();

    void sortULabelType(List<ULabelTypeDto> uLabelTypeDtoList);
}
