package com.get.aisplatformcenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aisplatformcenter.mapper.ReleaseInfoItemMapper;
import com.get.aisplatformcenter.service.MReleaseInfoItemService;
import com.get.aisplatformcenterap.dto.ReleaseInfoItemDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.entity.MReleaseInfoItem;
import com.get.aisplatformcenterap.vo.ReleaseInfoItemVo;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 发版信息子项 ServiceImpl
 */
@Service
public class MReleaseInfoItemServiceImpl extends ServiceImpl<ReleaseInfoItemMapper, MReleaseInfoItem> implements MReleaseInfoItemService {
    @Resource
    private ReleaseInfoItemMapper releaseInfoItemMapper;

    @Resource
    private UtilService utilService;

    /**
     * 新增发版子项
     *
     * @param mReleaseInfoItemDto
     */
    @Override
    public void insert(ReleaseInfoItemDto mReleaseInfoItemDto) {
        //校验参数
        verifyParameters(mReleaseInfoItemDto);
        //处理参数
        MReleaseInfoItem mReleaseInfoItem = BeanCopyUtils.objClone(mReleaseInfoItemDto, MReleaseInfoItem::new);
        utilService.setCreateInfo(mReleaseInfoItem);
        releaseInfoItemMapper.insert(mReleaseInfoItem);
    }

    /**
     * 根据发版信息Id查询发版信息子项
     *
     * @param fkReleaseInfoId
     * @return
     */
    @Override
    public List<ReleaseInfoItemVo> getReleaseInfoItemByFkReleaseInfoId(Long fkReleaseInfoId) {
        List<ReleaseInfoItemVo> releaseInfoItemVos = new ArrayList<>();
        if (GeneralTool.isEmpty(fkReleaseInfoId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<MReleaseInfoItem> mReleaseInfoItems = releaseInfoItemMapper.selectList(new LambdaQueryWrapper<MReleaseInfoItem>().eq(MReleaseInfoItem::getFkReleaseInfoId, fkReleaseInfoId));
        mReleaseInfoItems.forEach(mReleaseInfoItem -> {
            ReleaseInfoItemVo releaseInfoItemVo = BeanCopyUtils.objClone(mReleaseInfoItem, ReleaseInfoItemVo::new);
            releaseInfoItemVos.add(releaseInfoItemVo);
        });
        return releaseInfoItemVos;
    }

    /**
     * 批量删除
     *
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBatch(List<Long> ids) {
        if (GeneralTool.isEmpty(ids)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<MReleaseInfoItem> mReleaseInfoItems = releaseInfoItemMapper.selectBatchIds(ids);
        if (GeneralTool.isEmpty(mReleaseInfoItems)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        Set<Long> itemIds = mReleaseInfoItems.stream().map(MReleaseInfoItem::getId).collect(Collectors.toSet());
        releaseInfoItemMapper.deleteBatchIds(itemIds);
    }

    @Override
    public List<ReleaseInfoItemVo> getAllReleaseInfoItemByReleaseInfoId(Long releaseInfoId) {
        List<ReleaseInfoItemVo> releaseInfoItemVos = new ArrayList<>();
        if (GeneralTool.isEmpty(releaseInfoId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LambdaQueryWrapper<MReleaseInfoItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MReleaseInfoItem::getFkReleaseInfoId, releaseInfoId);
        wrapper.orderByDesc(MReleaseInfoItem::getGmtCreate);
        List<MReleaseInfoItem> mReleaseInfoItems = releaseInfoItemMapper.selectList(wrapper);
        if (GeneralTool.isNotEmpty(mReleaseInfoItems)) {
            mReleaseInfoItems.forEach(mReleaseInfoItem -> {
                ReleaseInfoItemVo releaseInfoItemVo = BeanCopyUtils.objClone(mReleaseInfoItem, ReleaseInfoItemVo::new);
                releaseInfoItemVos.add(releaseInfoItemVo);
            });
            return releaseInfoItemVos;
        }
        return Collections.emptyList();
    }

    @Override
    public void editReleaseInfoItem(ReleaseInfoItemDto releaseInfoItemDto) {
        if (GeneralTool.isEmpty(releaseInfoItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(releaseInfoItemDto.getTitle())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("title_cannot_be_empty"));
        }
        if (releaseInfoItemDto.getTitle().length() > 100) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("character_limit_exceeded") + "100" + " (" + releaseInfoItemDto.getTitle() + ")");
        }
        MReleaseInfoItem mReleaseInfoItem = BeanCopyUtils.objClone(releaseInfoItemDto, MReleaseInfoItem::new);
        if (GeneralTool.isEmpty(releaseInfoItemDto.getId())) {

            utilService.setCreateInfo(mReleaseInfoItem);
            releaseInfoItemMapper.insert(mReleaseInfoItem);

        }else {

            utilService.updateUserInfoToEntity(mReleaseInfoItem);
            releaseInfoItemMapper.updateById(mReleaseInfoItem);
        }

    }

    @Override
    public List<String> getResourceKeysById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MReleaseInfoItem mReleaseInfoItem = releaseInfoItemMapper.selectById(id);
        if (GeneralTool.isEmpty(mReleaseInfoItem.getFkResourceKeys())) {
            return Collections.emptyList();
        }
        return Arrays.stream(mReleaseInfoItem.getFkResourceKeys().split(","))
                .collect(Collectors.toList());
    }

    @Override
    public List<ReleaseInfoItemVo> getReleaseInfoItemByReleaseInfoIdAndResourceKeys(UserScopedDataDto userScopedDataDto) {
        if (GeneralTool.isEmpty(userScopedDataDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(userScopedDataDto.getReleaseInfoId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null") + "(" + userScopedDataDto.getReleaseInfoId() + ")");
        }
        if (GeneralTool.isEmpty(userScopedDataDto.getFkResourceKeys())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resource_keys_cannot_be_empty"));
        }
        List<ReleaseInfoItemVo> releaseInfoItemByFkReleaseInfoId = getReleaseInfoItemByFkReleaseInfoId(userScopedDataDto.getReleaseInfoId());
        if (GeneralTool.isEmpty(releaseInfoItemByFkReleaseInfoId)) {
            return Collections.emptyList();
        }
        List<String> resourceKeys = userScopedDataDto.getFkResourceKeys();
        List<ReleaseInfoItemVo> collect = releaseInfoItemByFkReleaseInfoId.stream()
                .filter(item -> item.getFkResourceKeys() != null)
                .filter(item ->
                        Arrays.stream(item.getFkResourceKeys().split(","))
                                .map(String::trim)
                                .filter(s -> !s.isEmpty())
                                .allMatch(resourceKeys::contains)
                )
                .collect(Collectors.toList());
        return collect;
    }

    @Override
    public void deleteReleaseInfoItemById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        releaseInfoItemMapper.deleteById(id);
    }

    /**
     * 校验参数
     *
     * @param mReleaseInfoItemDto
     */
    private static void verifyParameters(ReleaseInfoItemDto mReleaseInfoItemDto) {
        if (GeneralTool.isEmpty(mReleaseInfoItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(mReleaseInfoItemDto.getTitle())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("title_cannot_be_empty"));
        }
        if (mReleaseInfoItemDto.getTitle().length() > 100) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("character_limit_exceeded") + "100" + " (" + mReleaseInfoItemDto.getTitle() + ")");
        }
        if (GeneralTool.isEmpty(mReleaseInfoItemDto.getPermissionType())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("permission_type_cannot_be_empty"));
        }
        if (mReleaseInfoItemDto.getPermissionType() == 1) {
            if (GeneralTool.isEmpty(mReleaseInfoItemDto.getFkResourceKeys())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("resource_keys_cannot_be_empty"));
            }
        }

    }
}

