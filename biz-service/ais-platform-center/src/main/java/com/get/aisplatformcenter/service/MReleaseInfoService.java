package com.get.aisplatformcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoAndItemDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoSearchDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.entity.MReleaseInfo;
import com.get.aisplatformcenterap.vo.MenuTreeVo;
import com.get.aisplatformcenterap.vo.PlatFormTypeVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVos;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.permissioncenter.vo.ResourceVo;
import java.util.List;
import javax.validation.Valid;


/**
 * 发版信息
 */
public interface MReleaseInfoService extends IService<MReleaseInfo> {

    List<PlatFormTypeVo> getPlatformTypeDropDown();

    List<ReleaseInfoAndItemVo> getReleaseInfoAndItem(@Valid ReleaseInfoSearchDto releaseInfoSearchDto, Page page);

    void addReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    void deleteReleaseInfoAndItem(Long id);

    ReleaseInfoAndItemVo getDetailedInformationById(Long id);

    void editReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    void updateReleaseInfoStatus(ReleaseInfoAndItemDto releaseInfoAndItemDto);

    List<ReleaseInfoAndItemVo> getUserListByResourceKeys(@Valid UserScopedDataDto userScopedDataDto, Page page);

    ReleaseInfoAndItemVo getUserOneByResourceKeys(UserScopedDataDto userScopedDataDto);

    ReleaseInfoAndItemVos getReleaseInfoAndItemAndPage(@Valid ReleaseInfoSearchDto re, Page page);

    ReleaseInfoAndItemVos getUserListByResourceKeysAndPage(SearchBean<UserScopedDataDto> page);

    List<ResourceVo> getAisPermissionMenu();

    List<MenuTreeVo> getPartnerPermissionMenu(GetPermissionMenuDto getPermissionMenuDto);
}

