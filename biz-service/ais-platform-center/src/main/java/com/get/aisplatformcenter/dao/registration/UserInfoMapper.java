
package com.get.aisplatformcenter.dao.registration;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import com.get.aisplatformcenterap.entity.UserInfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("registrationdb")
public interface UserInfoMapper extends BaseMapper<UserInfoEntity> {

}