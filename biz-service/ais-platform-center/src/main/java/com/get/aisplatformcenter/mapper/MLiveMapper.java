package com.get.aisplatformcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.aisplatformcenterap.dto.MLiveDto;
import com.get.aisplatformcenterap.entity.MLiveEntity;
import com.get.aisplatformcenterap.vo.MLiveVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_live】的数据库操作Mapper
* @createDate 2024-11-28 19:30:56
* @Entity com.get.partnercenter.entity.MLive
*/
@Mapper
public interface MLiveMapper extends BaseMapper<MLiveEntity> {

    List<MLiveVo> searchPage(IPage<MLiveVo> page, @Param("query") MLiveDto params);

    int updateByIds(@Param("type")int type,@Param("ids")Long[] ids);

    MLiveVo selectByDetail(MLiveDto params);
}




