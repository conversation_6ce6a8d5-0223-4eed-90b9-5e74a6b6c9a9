package com.get.aisplatformcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.aisplatformcenterap.dto.ReleaseInfoItemDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.entity.MReleaseInfoItem;
import com.get.aisplatformcenterap.vo.ReleaseInfoItemVo;
import java.util.List;

/**
 * (MReleaseInfoItem)表服务接口
 */
public interface MReleaseInfoItemService extends IService<MReleaseInfoItem> {

     void insert(ReleaseInfoItemDto mReleaseInfoItemDto);

     //根据父项id获取子项
     List<ReleaseInfoItemVo> getReleaseInfoItemByFkReleaseInfoId(Long fkReleaseInfoId);

     //批量删除
     void deleteBatch(List<Long> ids);

     List<ReleaseInfoItemVo> getAllReleaseInfoItemByReleaseInfoId(Long releaseInfoId);

     void editReleaseInfoItem(ReleaseInfoItemDto releaseInfoItemDto);

     List<String> getResourceKeysById(Long id);

     List<ReleaseInfoItemVo> getReleaseInfoItemByReleaseInfoIdAndResourceKeys(UserScopedDataDto userScopedDataDto);

    void deleteReleaseInfoItemById(Long id);
}

