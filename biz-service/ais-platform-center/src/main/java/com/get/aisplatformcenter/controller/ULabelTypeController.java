package com.get.aisplatformcenter.controller;

import com.get.aisplatformcenter.service.ULabelTypeService;
import com.get.aisplatformcenterap.dto.ULabelTypeDto;
import com.get.aisplatformcenterap.vo.ULabelTypeVo;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.core.secure.annotation.VerifyPermission;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

@Api(tags = "标签类型管理")
@RestController
@RequestMapping("platform/labelType")
public class ULabelTypeController {

    @Resource
    private ULabelTypeService uLabelTypeService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/标签类型管理/列表查询")
    @PostMapping("datas")
    public ResponseBo<ULabelTypeVo> searchPage(@RequestBody SearchBean<ULabelTypeDto> page){
        List<ULabelTypeVo> datas=uLabelTypeService.searchPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    @ApiOperation(value = "新增标签类型管理", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/标签类型管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(BaseVoEntity.Add.class) ULabelTypeDto uLabelTypeDto) {
        return SaveResponseBo.ok(uLabelTypeService.addULabelType(uLabelTypeDto));
    }


    @ApiOperation(value = "排序接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/标签类型管理/排序")
    @PostMapping("sortULabelType")
    public ResponseBo sortULabelType(@RequestBody List<ULabelTypeDto> uLabelTypeDtoList) {
        uLabelTypeService.sortULabelType(uLabelTypeDtoList);
        return ResponseBo.ok();
    }



    @ApiOperation(value = "更新标签类型管理", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/标签类型管理/更新")
    @PostMapping("update")
    public ResponseBo<ULabelTypeVo> update(@RequestBody  ULabelTypeDto uLabelTypeDto) {
        return UpdateResponseBo.ok( uLabelTypeService.updateULabelType(uLabelTypeDto));
    }


        /**
     * 删除用户信息管理
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除标签类型管理", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/标签类型管理/更新")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        return uLabelTypeService.delete(id);

    }


    @ApiOperation(value = "批量删除标签管理", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/标签类型管理/批量删除标签管理")
    @PostMapping("batchDelete")
    public ResponseBo batchDelete(@RequestBody Set<Long> uLabelTypes) {
        return uLabelTypeService.batchDelete(uLabelTypes);
    }



    @ApiOperation(value = "标签类型管理详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/标签类型管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<ULabelTypeVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo<>(uLabelTypeService.findULabelTypeById(id));
    }


    @ApiOperation(value = "标签类型导出")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/标签类型管理/导出")
    @PostMapping("/export")
    public void export(@RequestBody  ULabelTypeDto uLabelTypeDto, HttpServletResponse response){
        uLabelTypeService.export(uLabelTypeDto,response);
    }


//    @ApiOperation(value = "检查标签类型编码是否存在", notes = "typeKey为此条数据typeKey")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/标签类型管理/检查标签类型编码是否存在")
//    @GetMapping("/check")
//    public ResponseBo check(@RequestParam(value = "typeKey") String typeKey) {
//        return new ResponseBo<>(uLabelTypeService.check(typeKey));
//    }



    @ApiOperation(value = "标签类型Select下拉", notes = "Select下拉")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/标签类型管理/select下拉")
    @GetMapping("/selectLabelType")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<ULabelTypeVo> selectLabelType() {
        List<ULabelTypeVo> datas = uLabelTypeService.selectLabelType();
        return new ListResponseBo<>(datas);
    }

}
