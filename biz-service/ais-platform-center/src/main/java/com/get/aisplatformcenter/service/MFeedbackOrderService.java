package com.get.aisplatformcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.aisplatformcenterap.dto.work.MFeedbackOrderDto;
import com.get.aisplatformcenterap.dto.work.MFeedbackOrderReplyDto;
import com.get.aisplatformcenterap.entity.MFeedbackOrderEntity;
import com.get.aisplatformcenterap.entity.UFeedbackOrderTypeEntity;
import com.get.aisplatformcenterap.vo.work.MFeedbackOrderDetailVo;
import com.get.aisplatformcenterap.vo.work.MFeedbackOrderVo;
import com.get.common.result.Page;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_feedback_order】的数据库操作Service
* @createDate 2025-05-26 16:46:43
*/
public interface MFeedbackOrderService extends IService<MFeedbackOrderEntity> {
    /**
     * 反馈列表
     * @param params
     * @param page
     * @return
     */
    List<MFeedbackOrderVo> searchPage(MFeedbackOrderDto params, Page page);

    /**
     * 详细
     * @param id
     * @return
     */
    MFeedbackOrderDetailVo  getDetail(Long id);

    /**
     * 关闭
     * @param id
     */
    void solve(Long id);

    /**
     * 反馈 回复
     * @param replydto
     * @return
     */
    Long feedDack(MFeedbackOrderReplyDto replydto);

    List<UFeedbackOrderTypeEntity> getUFeedbackOrderType();

}
