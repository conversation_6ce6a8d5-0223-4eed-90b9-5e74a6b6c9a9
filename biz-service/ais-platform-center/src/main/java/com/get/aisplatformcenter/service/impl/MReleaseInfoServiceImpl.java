package com.get.aisplatformcenter.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aisplatformcenter.mapper.ReleaseInfoItemMapper;
import com.get.aisplatformcenter.mapper.ReleaseInfoMapper;
import com.get.aisplatformcenter.service.MReleaseInfoItemService;
import com.get.aisplatformcenter.service.MReleaseInfoService;
import com.get.aisplatformcenter.service.SystemMenuService;
import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoAndItemDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoSearchDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.entity.MReleaseInfo;
import com.get.aisplatformcenterap.enums.PlatFormType;
import com.get.aisplatformcenterap.vo.MenuTreeVo;
import com.get.aisplatformcenterap.vo.PlatFormTypeVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVos;
import com.get.aisplatformcenterap.vo.ReleaseInfoItemVo;
import com.get.common.result.Page;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ResourceVo;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 发版信息项服务实现类
 */
@Service("platformMReleaseInfoService")
public class MReleaseInfoServiceImpl extends ServiceImpl<ReleaseInfoMapper, MReleaseInfo> implements MReleaseInfoService {
    @Resource
    private ReleaseInfoMapper releaseInfoMapper;

    @Resource
    private UtilService utilService;

    @Resource
    private MReleaseInfoItemService mReleaseInfoItemService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private SystemMenuService systemMenuService;
    @Autowired
    private ReleaseInfoItemMapper releaseInfoItemMapper;


    /**
     * 获取平台类型下拉框
     *
     * @return
     */
    @Override
    public List<PlatFormTypeVo> getPlatformTypeDropDown() {
        List<PlatFormTypeVo> typeMap = PlatFormType.asSelectList();
        return typeMap;
    }

    @Override
    public List<ReleaseInfoAndItemVo> getReleaseInfoAndItem(ReleaseInfoSearchDto releaseInfoSearchDto, Page page) {
        // 查询数据
        List<ReleaseInfoAndItemVo> mReleaseInfoDatas = new ArrayList<>();
        if (page != null) {
            IPage<MReleaseInfo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            mReleaseInfoDatas = releaseInfoMapper.getReleaseInfo(pages, releaseInfoSearchDto);
            page.setAll((int) pages.getTotal());
        }
        if (GeneralTool.isNotEmpty(mReleaseInfoDatas)) {
            return mReleaseInfoDatas;
        }
        return Collections.emptyList();
    }

    /**
     * 添加发版信息项和子项
     *
     * @param releaseInfoAndItemDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        verifyParameters(releaseInfoAndItemDto);
        MReleaseInfo mReleaseInfo = BeanCopyUtils.objClone(releaseInfoAndItemDto, MReleaseInfo::new);
//        0待发布/1已发布/2已撤回
        mReleaseInfo.setStatus(0);
        utilService.setCreateInfo(mReleaseInfo);
        releaseInfoMapper.insert(mReleaseInfo);
        if (GeneralTool.isNotEmpty(releaseInfoAndItemDto.getReleaseInfoItemDtos())) {
            releaseInfoAndItemDto.getReleaseInfoItemDtos().forEach(releaseInfoItemDto -> {
                releaseInfoItemDto.setFkReleaseInfoId(mReleaseInfo.getId());
                mReleaseInfoItemService.insert(releaseInfoItemDto);
            });
        }
    }

    private static void verifyParameters(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        if (GeneralTool.isEmpty(releaseInfoAndItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getFkPlatformId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("platform_id_cannot_be_empty"));
        }
        //如何平台类型为伙伴，code则为必填
        if (releaseInfoAndItemDto.getFkPlatformId().equals(PlatFormType.PARTNER.getId())) {
            if (GeneralTool.isEmpty(releaseInfoAndItemDto.getFkPlatformCode())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("platform_code_cannot_be_empty"));
            }
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getTitle())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("title_cannot_be_empty"));
        }
        if (releaseInfoAndItemDto.getTitle().length() > 100) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("character_limit_exceeded") + "100" + " (" + releaseInfoAndItemDto.getTitle() + ")");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteReleaseInfoAndItem(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MReleaseInfo mReleaseInfo = releaseInfoMapper.selectById(id);
        if (GeneralTool.isEmpty(mReleaseInfo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //查询对应的子项数据
        List<ReleaseInfoItemVo> releaseInfoItemByFkReleaseInfoId = mReleaseInfoItemService.getReleaseInfoItemByFkReleaseInfoId(id);
        if (GeneralTool.isNotEmpty(releaseInfoItemByFkReleaseInfoId)) {
            List<Long> mReleaseInfoItemIds = releaseInfoItemByFkReleaseInfoId.stream().map(ReleaseInfoItemVo::getId).collect(Collectors.toList());
            //删除对应的子项数据
            mReleaseInfoItemService.deleteBatch(mReleaseInfoItemIds);
        }

        releaseInfoMapper.deleteById(id);

    }

    @Override
    public List<ResourceVo> getAisPermissionMenu() {
        Result<List<ResourceVo>> resourceTree = permissionCenterClient.getResourceTree();
        if (resourceTree.isSuccess() && resourceTree.getData() != null) {
            return resourceTree.getData();
        }
        return Collections.emptyList();
    }

    @Override
    public List<MenuTreeVo> getPartnerPermissionMenu(GetPermissionMenuDto getPermissionMenuDto) {
        return systemMenuService.getPermissionMenu(getPermissionMenuDto);
    }

    /**
     * 根据id获取详细信息
     *
     * @param id
     * @return
     */
    @Override
    public ReleaseInfoAndItemVo getDetailedInformationById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        MReleaseInfo mReleaseInfo = releaseInfoMapper.selectById(id);
        if (GeneralTool.isEmpty(mReleaseInfo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        ReleaseInfoAndItemVo releaseInfoAndItemVo = BeanCopyUtils.objClone(mReleaseInfo, ReleaseInfoAndItemVo::new);
        List<ReleaseInfoItemVo> releaseInfoItemByFkReleaseInfoId = mReleaseInfoItemService.getReleaseInfoItemByFkReleaseInfoId(id);
        releaseInfoAndItemVo.setReleaseInfoItemVos(releaseInfoItemByFkReleaseInfoId);
        return releaseInfoAndItemVo;
    }

    /**
     * 编辑发版信息项和子项
     *
     * @param releaseInfoAndItemDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void editReleaseInfoAndItem(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        if (GeneralTool.isEmpty(releaseInfoAndItemDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getTitle())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("title_cannot_be_empty"));
        }
        if (releaseInfoAndItemDto.getTitle().length() > 100) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("character_limit_exceeded") + "100" + " (" + releaseInfoAndItemDto.getTitle() + ")");
        }
        MReleaseInfo mReleaseInfo = BeanCopyUtils.objClone(releaseInfoAndItemDto, MReleaseInfo::new);
        utilService.updateUserInfoToEntity(mReleaseInfo);
        releaseInfoMapper.updateById(mReleaseInfo);
        if (GeneralTool.isNotEmpty(releaseInfoAndItemDto.getReleaseInfoItemDtos())) {
            releaseInfoAndItemDto.getReleaseInfoItemDtos().forEach(releaseInfoItemDto -> {
                mReleaseInfoItemService.editReleaseInfoItem(releaseInfoItemDto);
            });
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateReleaseInfoStatus(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
//        status  '枚举：0待发布/1已发布/2已撤回'
//        判断id不能为空,status不能为空
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isEmpty(releaseInfoAndItemDto.getStatus())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("status_null"));
        }
        LambdaQueryWrapper<MReleaseInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MReleaseInfo::getId, releaseInfoAndItemDto.getId());
        MReleaseInfo releaseInfoData = releaseInfoMapper.selectOne(queryWrapper);
        if (GeneralTool.isEmpty(releaseInfoData)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        LambdaUpdateWrapper<MReleaseInfo> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MReleaseInfo::getId, releaseInfoAndItemDto.getId());
        MReleaseInfo mReleaseInfo = new MReleaseInfo();
        if (releaseInfoData.getStatus().equals(releaseInfoAndItemDto.getStatus())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("status_duplicate"));
        }else {
            mReleaseInfo.setId(releaseInfoAndItemDto.getId());
            mReleaseInfo.setStatus(releaseInfoAndItemDto.getStatus());
            utilService.setUpdateInfo(mReleaseInfo);


            updateWrapper.set(MReleaseInfo::getId, releaseInfoAndItemDto.getId());
            updateWrapper.set(MReleaseInfo::getStatus, releaseInfoAndItemDto.getStatus());
            updateWrapper.set(MReleaseInfo::getGmtModified, mReleaseInfo.getGmtModified());
            updateWrapper.set(MReleaseInfo::getGmtModifiedUser, mReleaseInfo.getGmtModifiedUser());
        }
        releaseInfoMapper.update(mReleaseInfo, updateWrapper);
    }

    @Override
    public List<ReleaseInfoAndItemVo> getUserListByResourceKeys(UserScopedDataDto userScopedDataDto, Page page) {
        verifyParameters(userScopedDataDto);
        List<ReleaseInfoAndItemVo> userReleaseInfoListDatas = new ArrayList<>();
        if (page != null) {
            IPage<MReleaseInfo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            userReleaseInfoListDatas= releaseInfoMapper.getUserListByPermission(pages, userScopedDataDto);
        }else {
            userReleaseInfoListDatas= releaseInfoMapper.getUserListByPermission(null, userScopedDataDto);
        }
        if (GeneralTool.isNotEmpty(userReleaseInfoListDatas)) {
            return userReleaseInfoListDatas;
        }
        return Collections.emptyList();

    }

    @Override
    public ReleaseInfoAndItemVo getUserOneByResourceKeys(UserScopedDataDto userScopedDataDto) {
        List<ReleaseInfoAndItemVo> userListByResourceKeys = getUserListByResourceKeys(userScopedDataDto, null);
        ReleaseInfoAndItemVo releaseInfoAndItemVo = new ReleaseInfoAndItemVo();
        if (GeneralTool.isNotEmpty(userListByResourceKeys)) {
            //返回创建时间最新的数据
            releaseInfoAndItemVo = userListByResourceKeys.get(0);
            userScopedDataDto.setReleaseInfoId(releaseInfoAndItemVo.getId());
        }
        List<ReleaseInfoItemVo> releaseInfoItemByReleaseInfoIdAndResourceKeys = mReleaseInfoItemService.getReleaseInfoItemByReleaseInfoIdAndResourceKeys(userScopedDataDto);
        releaseInfoAndItemVo.setReleaseInfoItemVos(releaseInfoItemByReleaseInfoIdAndResourceKeys);
        return releaseInfoAndItemVo;

    }

    @Override
    public ReleaseInfoAndItemVos getReleaseInfoAndItemAndPage(ReleaseInfoSearchDto releaseInfoSearchDto, Page page) {
        ReleaseInfoAndItemVos releaseInfoAndItemVos = new ReleaseInfoAndItemVos();
        List<ReleaseInfoAndItemVo> releaseInfoAndItem = getReleaseInfoAndItem(releaseInfoSearchDto, page);
        if (GeneralTool.isNotEmpty(releaseInfoAndItem)) {
            releaseInfoAndItemVos.setReleaseInfoAndItemVos(releaseInfoAndItem);
            releaseInfoAndItemVos.setPage(page);
            return releaseInfoAndItemVos;
        }
        return releaseInfoAndItemVos;
    }

    @Override
    public ReleaseInfoAndItemVos getUserListByResourceKeysAndPage(SearchBean<UserScopedDataDto> page) {
        ReleaseInfoAndItemVos releaseInfoAndItemVos = new ReleaseInfoAndItemVos();
        List<ReleaseInfoAndItemVo> releaseInfoAndItem = getUserListByResourceKeys(page.getData(), page);
        if (GeneralTool.isNotEmpty(releaseInfoAndItem)) {
            releaseInfoAndItemVos.setReleaseInfoAndItemVos(releaseInfoAndItem);
            releaseInfoAndItemVos.setPage(page);
            return releaseInfoAndItemVos;
        }
        return releaseInfoAndItemVos;
    }


    private static void verifyParameters(UserScopedDataDto userScopedDataDto) {
        if (GeneralTool.isEmpty(userScopedDataDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        if (GeneralTool.isEmpty(userScopedDataDto.getFkPlatformId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("platform_id_cannot_be_empty"));
        }
        if (userScopedDataDto.getFkPlatformId().equals(PlatFormType.PARTNER.getId())) {
            if (GeneralTool.isEmpty(userScopedDataDto.getFkPlatformCode())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("platform_code_cannot_be_empty"));
            }
        }
        if (GeneralTool.isEmpty(userScopedDataDto.getFkResourceKeys())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("resource_keys_cannot_be_empty"));
        }
        if (GeneralTool.isEmpty(userScopedDataDto.getFkStaffId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staffId_is_null"));
        }
    }


}

