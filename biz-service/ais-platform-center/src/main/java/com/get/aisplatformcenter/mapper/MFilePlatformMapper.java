package com.get.aisplatformcenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.aisplatformcenterap.entity.MFilePlatformEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
/**
* <AUTHOR>
* @description 针对表【m_file_platform】的数据库操作Mapper
* @createDate 2025-03-03 18:49:44
* @Entity com.get.partnercenter.entity.MFilePlatform
*/
@Mapper
@DS("aisfiledb")
public interface MFilePlatformMapper extends BaseMapper<MFilePlatformEntity> {

}




