package com.get.aisplatformcenter.controller;


import com.baomidou.mybatisplus.extension.api.ApiController;
import com.get.aisplatformcenter.service.SystemMenuService;
import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Partner (SystemMenu)表控制层
 */
@Api(tags = "Partner系统菜单")
@RestController
@RequestMapping("platform/partnerSystemMenu")
public class SystemMenuController extends ApiController {

    @Resource
    private SystemMenuService systemMenuService;

    @ApiOperation(value = "获取AIS权限菜单", notes = "获取AIS权限菜单")
    @OperationLogger(module = LoggerModulesConsts.MIDDLECENTER, type = LoggerOptTypeConst.LIST, description = "中台中心/发版信息项管理/获取AIS权限菜单")
    @PostMapping("getPermissionMenu")
    public ResponseBo getPermissionMenu(@RequestBody GetPermissionMenuDto getPermissionMenuDto) {
        return new ResponseBo<>(systemMenuService.getPermissionMenu(getPermissionMenuDto));
    }


}

