package com.get.aisplatformcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.entity.SystemMenu;
import com.get.aisplatformcenterap.vo.MenuTreeVo;
import java.util.List;

/**
 * Partner (SystemMenu)表服务接口
 */
public interface SystemMenuService extends IService<SystemMenu> {

    List<MenuTreeVo> getPermissionMenu(GetPermissionMenuDto getPermissionMenuDto);
}

