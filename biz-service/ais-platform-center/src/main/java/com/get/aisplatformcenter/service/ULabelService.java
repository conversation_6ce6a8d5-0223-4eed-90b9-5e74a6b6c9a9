package com.get.aisplatformcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.aisplatformcenterap.dto.ULabelDto;
import com.get.aisplatformcenterap.entity.ULabelEntity;
import com.get.aisplatformcenterap.vo.LabelSearchAboutAgentVo;
import com.get.aisplatformcenterap.vo.ULabelVo;
import com.get.common.result.Page;
import com.get.salecenter.dto.AgentLabelDto;

import java.util.List;

public interface ULabelService extends IService<ULabelEntity> {
    List<ULabelVo> searchPage(ULabelDto data, Page page);

    Long addULabelType(ULabelDto uLabelDto);

    ULabelVo updateULabel(ULabelDto uLabelDto);

    void delete(Long id);

    ULabelVo findULabelById(Long id);


    void sortULabel(List<ULabelDto> uLabelDtoList);

    LabelSearchAboutAgentVo getLabelByLabelTypeIdAndKeyWord(AgentLabelDto agentLabelDto, Page page);
}
