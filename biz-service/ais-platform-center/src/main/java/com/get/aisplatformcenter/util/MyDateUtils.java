package com.get.aisplatformcenter.util;

import java.util.Calendar;
import java.util.Date;

public class MyDateUtils {

    /**
     * 获取日期的年份
     *
     * @param date
     * @return 日期的年份
     */
    public static int getYear(Date date) {
        return getCalendar(date).get(Calendar.YEAR);
    }

    public static int getMonth(Date date) {
        return getCalendar(date).get(Calendar.MONTH)+1;
    }

    /**
     * 获取day对应的Calendar对象
     *
     * @param day
     * @return 返回date对应的Calendar对象
     */
    public static Calendar getCalendar(Date day) {
        Calendar c = Calendar.getInstance();
        c.clear();
        if (day != null) {
            c.setTime(day);
        }
        return c;
    }

    public static boolean isOlderThan72Hours(Date gmtCreate) {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -72); // 当前时间减去72小时
        Date threshold = calendar.getTime();
        return gmtCreate.before(threshold);
    }
}
