<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aisplatformcenter.mapper.MLiveMapper">

    <resultMap id="BaseResultMap" type="com.get.aisplatformcenterap.entity.MLiveEntity">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="fkCompanyId" column="fk_company_id" jdbcType="BIGINT"/>
        <result property="fkPlatformId" column="fk_platform_id" jdbcType="BIGINT"/>
        <result property="fkPlatformCode" column="fk_platform_code" jdbcType="VARCHAR"/>
        <result property="fkLiveTypeId" column="fk_live_type_id" jdbcType="BIGINT"/>
        <result property="title" column="title" jdbcType="VARCHAR"/>
        <result property="num" column="num" jdbcType="VARCHAR"/>
        <result property="description" column="description" jdbcType="VARCHAR"/>
        <result property="teacherName" column="teacher_name" jdbcType="VARCHAR"/>
        <result property="teacherNameChn" column="teacher_name_chn" jdbcType="VARCHAR"/>
        <result property="teacherGender" column="teacher_gender" jdbcType="INTEGER"/>
        <result property="teacherJob" column="teacher_job" jdbcType="VARCHAR"/>
        <result property="teacherBrief" column="teacher_brief" jdbcType="VARCHAR"/>
        <result property="liveTimeStart" column="live_time_start" jdbcType="TIMESTAMP"/>
        <result property="liveTimeEnd" column="live_time_end" jdbcType="TIMESTAMP"/>
        <result property="liveUrl" column="live_url" jdbcType="VARCHAR"/>
        <result property="loopUrl" column="loop_url" jdbcType="VARCHAR"/>
        <result property="liveStatus" column="live_status" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="searchPage" resultType="com.get.aisplatformcenterap.vo.MLiveVo">
        select mLive.*,
            (
            SELECT CONCAT(#{query.mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
            INNER JOIN ais_file_center.m_file_platform platFormFile ON platFormFile.file_guid = sAttached.fk_file_guid
            WHERE sAttached.fk_table_name='m_live' AND  sAttached.type_key='m_live_logo'  AND sAttached.fk_table_id=mLive.id limit 1
            ) AS file_key,
            mCompany.num AS companyNum,uLiveType.type_name_chn AS typeNameChn,
            uLiveType.type_key AS typeKey,
            (SELECT COUNT(*) FROM app_partner_center.r_live_partner_user_appointment  rLivePartnerUserAppointment
                WHERE rLivePartnerUserAppointment.fk_live_id=mLive.id) AS appointmentNum
        from ais_platform_center.m_live mLive
        LEFT JOIN ais_permission_center.m_company AS mCompany ON mCompany.id = mLive.fk_company_id
        LEFT JOIN ais_platform_center.u_live_type AS uLiveType ON mLive.fk_live_type_id=uLiveType.id


        WHERE 1=1

        <if test="query.fkLiveTypeId!=null ">
            AND mLive.fk_live_type_id=#{query.fkLiveTypeId}
        </if>


        <if test="query.status!=null   ">
            AND mLive.status=#{query.status}
        </if>


        <if test="query.liveStatus!=null and query.liveStatus==0  ">
            AND mLive.live_status=#{query.liveStatus} AND mLive.live_time_start > now()
        </if>
        <if test="query.liveStatus!=null and  query.liveStatus==1  ">
            AND (mLive.live_status=#{query.liveStatus}  OR
                 ( uLiveType.type_key='live' AND mLive.live_time_start &lt; NOW() AND mLive.live_time_end &gt; NOW() )
                )
        </if>
        <if test="query.liveStatus!=null and  query.liveStatus==2  ">

            AND (mLive.live_status=#{query.liveStatus}  OR
            ( uLiveType.type_key='live' AND mLive.live_time_end &lt; NOW() )
            )

        </if>
        <if test="query.liveStatus!=null and  query.liveStatus==3  ">
            AND (mLive.live_status=#{query.liveStatus}  OR
            ( uLiveType.type_key='live' AND mLive.live_time_end &lt; NOW() )
            )
        </if>

        <if test="query.liveStatus!=null and (query.liveStatus==5  or   query.liveStatus==6 ) ">
            AND mLive.live_status=#{query.liveStatus}
        </if>
        <if test="query.teacherNameChn!=null  and query.teacherNameChn != ''">
            AND
            (mLive.teacher_name  LIKE CONCAT('%',#{query.teacherNameChn},'%') OR  mLive.teacher_name_chn  LIKE CONCAT('%',#{query.teacherNameChn},'%') )
        </if>
        <if test="query.title!=null  and query.title != ''">
            AND mLive.title  LIKE CONCAT('%',#{query.title},'%')
        </if>
        ORDER BY mLive.live_time_start DESC

    </select>
    <select id="selectByDetail" resultType="com.get.aisplatformcenterap.vo.MLiveVo">
        select mLive.*,
               (
                   SELECT CONCAT(#{mMageAddress}, platFormFile.file_key) from ais_platform_center.s_media_and_attached sAttached
                                                                                        INNER JOIN ais_file_center.m_file_platform platFormFile ON platFormFile.file_guid = sAttached.fk_file_guid
                   WHERE sAttached.fk_table_name='m_live' AND  sAttached.type_key='m_live_logo'  AND sAttached.fk_table_id=mLive.id limit 1
               ) AS file_key,
               mCompany.num AS companyNum,uLiveType.type_name_chn AS typeNameChn
        from ais_platform_center.m_live mLive
                 LEFT JOIN ais_permission_center.m_company AS mCompany ON mCompany.id = mLive.fk_company_id
                 LEFT JOIN ais_platform_center.u_live_type AS uLiveType ON mLive.fk_live_type_id=uLiveType.id
        WHERE 1=1 and mLive.id= #{id}


    </select>

    <update id="updateByIds">
        UPDATE m_live
        <if test="type!=null and type==0">
            SET status=0
        </if>
        <if test="type!=null and type==1">
            SET status=1
        </if>
        WHERE
        id IN
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>

    </update>
</mapper>
