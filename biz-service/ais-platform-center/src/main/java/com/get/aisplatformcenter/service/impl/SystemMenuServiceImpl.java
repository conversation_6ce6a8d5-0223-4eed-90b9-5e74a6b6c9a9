package com.get.aisplatformcenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aisplatformcenter.mapper.SystemMenuMapper;
import com.get.aisplatformcenter.service.SystemMenuService;
import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.entity.SystemMenu;
import com.get.aisplatformcenterap.enums.PlatFormType;
import com.get.aisplatformcenterap.vo.MenuTreeVo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Partner (SystemMenu)表服务实现类
 */
@Service("partnerSystemMenuService")
public class SystemMenuServiceImpl extends ServiceImpl<SystemMenuMapper, SystemMenu> implements SystemMenuService {
    @Resource
    private SystemMenuMapper systemMenuMapper;

    @Override
    public List<MenuTreeVo> getPermissionMenu(GetPermissionMenuDto getPermissionMenuDto) {
        if (GeneralTool.isNotEmpty(getPermissionMenuDto.getFkPlatformId()) && getPermissionMenuDto.getFkPlatformId().equals(PlatFormType.PARTNER.getId())){
            if (GeneralTool.isEmpty(getPermissionMenuDto.getFkPlatformCode())){
                throw new GetServiceException(LocaleMessageUtils.getMessage("platform_code_cannot_be_empty"));
            }
        }
        List<SystemMenu> systemMenus = systemMenuMapper.selectPartnerMenuList(getPermissionMenuDto);
        return buildMenuTree(systemMenus.stream().distinct().collect(Collectors.toList()));
    }

    private List<MenuTreeVo> buildMenuTree(List<SystemMenu> menus) {
        if (CollectionUtils.isEmpty(menus)) {
            return Collections.emptyList();
        }
        // 先将菜单转换成 MenuTreeVo 对象
        List<MenuTreeVo> menuTreeVos = menus.stream()
                .map(menu -> {
                    MenuTreeVo treeVo = new MenuTreeVo();
                    BeanUtils.copyProperties(menu, treeVo);
                    return treeVo;
                }).collect(Collectors.toList());

        // 构建树形结构
        Map<Long, MenuTreeVo> menuMap = menuTreeVos.stream()
                .collect(Collectors.toMap(MenuTreeVo::getId, menu -> menu));
        // 根菜单列表
        List<MenuTreeVo> rootMenuList = new ArrayList<>();
        for (MenuTreeVo menu : menuTreeVos) {
            // 如果没有父菜单ID，认为是根菜单
            if (Objects.isNull(menu.getFkParentMenuId()) || menu.getFkParentMenuId().equals(0L)) {
                rootMenuList.add(menu);
            } else {
                MenuTreeVo parentMenu = menuMap.get(menu.getFkParentMenuId());
                if (Objects.nonNull(parentMenu)) {
                    if (Objects.isNull(parentMenu.getChildTree())) {
                        parentMenu.setChildTree(new ArrayList<>());
                    }
                    parentMenu.getChildTree().add(menu);
                }
            }
        }

        return rootMenuList;
    }
}

