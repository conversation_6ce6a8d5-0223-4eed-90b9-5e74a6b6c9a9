package com.get.aisplatformcenter.controller;


import com.get.aisplatformcenter.service.MBannerService;
import com.get.aisplatformcenterap.dto.DeletePatchParamsDto;
import com.get.aisplatformcenterap.entity.MMessageEntity;
import com.get.aisplatformcenterap.vo.MBannerVo;
import com.get.aisplatformcenterap.entity.MBannerEntity;
import com.get.aisplatformcenterap.dto.BannerPutAwayParamsDto;
import com.get.aisplatformcenterap.dto.MBannerParamsDto;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "后台管理-banner管理")
@RestController
@RequestMapping("platform/mbanner")
public class MBannerController {

    @Autowired
    MBannerService mbannerservice;


    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/banner管理/列表查询")
    @PostMapping("searchPage")
    public ResponseBo<MBannerVo> searchPage(@RequestBody SearchBean<MBannerParamsDto> page){
        List<MBannerVo> datas=mbannerservice.searchBannerPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "新增-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/banner管理/新增(修改)")
    @PostMapping("insert")
    public ResponseBo insert(@RequestBody MBannerParamsDto entity){
        return SaveResponseBo.ok(mbannerservice.saveOrUpdateMessage(entity));
    }


    @ApiOperation(value = "修改-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/banner管理/新增(修改)")
    @PostMapping("update")
    public ResponseBo update(@RequestBody @Validated(BaseVoEntity.Update.class)  MBannerParamsDto entity){
        return SaveResponseBo.ok(mbannerservice.saveOrUpdateMessage(entity));
    }


    @ApiOperation(value = "一键(上架)下架-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/banner管理/一键(上架)下架-接口")
    @PostMapping("putAway")
    public ResponseBo putAway(@RequestBody  @Valid BannerPutAwayParamsDto vo){

        return SaveResponseBo.ok(mbannerservice.putAwayBanner(vo));
    }

    @ApiOperation(value = "删除-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/banner管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(  @PathVariable("id") Long id){
        return SaveResponseBo.ok(mbannerservice.deleteOne(id));
    }




    @ApiOperation(value = "一键删除-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/消息管理/一键删除")
    @PostMapping("deleteBatch")
    public ResponseBo deleteBatch(@RequestBody  @Valid DeletePatchParamsDto entity){
        mbannerservice.deleteBatch(entity);
        return SaveResponseBo.ok();
    }



    @ApiOperation(value = "详情-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/消息管理/详情")
    @PostMapping("getDetail/{id}")
    public ResponseBo<MBannerVo> getDetail(@PathVariable("id") Long id){
        MBannerVo bannerDetail=mbannerservice.getDetail(id);
        return new ResponseBo<>(bannerDetail);
    }








}
