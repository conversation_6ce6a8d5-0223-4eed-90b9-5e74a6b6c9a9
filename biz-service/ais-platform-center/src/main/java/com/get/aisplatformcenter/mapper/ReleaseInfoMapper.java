package com.get.aisplatformcenter.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.aisplatformcenterap.dto.ReleaseInfoSearchDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.entity.MReleaseInfo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * MReleaseInfoMapper 发版信息项数据访问接口
 */
@Mapper
public interface ReleaseInfoMapper extends BaseMapper<MReleaseInfo> {

    List<ReleaseInfoAndItemVo> getReleaseInfo(IPage<MReleaseInfo> pages, @Param("releaseInfoSearchDto") ReleaseInfoSearchDto releaseInfoSearchDto);

    List<ReleaseInfoAndItemVo> getUserListByPermission(IPage<MReleaseInfo> pages,@Param("userScopedDataDto") UserScopedDataDto userScopedDataDto);
}

