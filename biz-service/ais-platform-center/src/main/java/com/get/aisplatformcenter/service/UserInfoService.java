package com.get.aisplatformcenter.service;

import com.get.aisplatformcenterap.dto.UserInfoDto;
import com.get.aisplatformcenterap.vo.UserInfoVo;
import com.get.common.result.Page;


import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON>.
 * Time: 17:31
 * Date: 2021/7/8
 * Description:用户信息业务类
 */
public interface UserInfoService {

    /**
     * @Description: 根据名称模糊搜索用户ids
     * @Author: Jerry
     * @Date:14:46 2021/8/23
     */
    Set<Long> getUserIdsByParam(String userName, Long fkAreaCityId, String bdName);

    /**
     * @Description: feign调用 根据userid获取名称（微信昵称）
     * @Author: Jerry
     * @Date:14:20 2021/8/23
     */
    Map<Long, String> getUserNickNamesByUserIds(Set<Long> userIds);

    /**
     * @Description: 根据ids获取人员对应的城市名称
     * @Author: Jerry
     * @Date:10:39 2021/8/30
     */
    Map<Long, String> getCityNamesByUserIds(Set<Long> userIds);

    /**
     * @Description: feign调用 根据userid获取手机号
     * @Author: Jerry
     * @Date:10:30 2021/10/15
     */
    Map<Long, String> getMobileByUserIds(Set<Long> userIds);

    /**
     * @Description: 根据名称模糊或者手机号搜索用户ids
     * @Author: Jerry
     * @Date:11:14 2021/8/27
     */
    Set<Long> getUserIdsByNameOrMobile(String userName, String phoneNumber);

    /**
     * @Description: feign调用 根据userid获取对象
     * @Author: Jerry
     * @Date:16:51 2021/8/26
     */
    Map<Long, UserInfoVo> getUserInfoDtoByIds(Set<Long> userIds);

    /**
     * 用户信息管理列表数据
     *
     * @param userInfoDto
     * @param page
     * @return
     */
    List<UserInfoVo> getUserInfoList(UserInfoDto userInfoDto, Page page);


}
