package com.get.aisplatformcenter.service.impl;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.aisplatformcenter.mapper.MBannerMapper;
import com.get.aisplatformcenter.mapper.SMediaAndAttachedMapper;
import com.get.aisplatformcenter.service.MBannerService;
import com.get.aisplatformcenter.util.TencentCloudUtils;
import com.get.aisplatformcenterap.dto.DeletePatchParamsDto;
import com.get.aisplatformcenterap.entity.SMediaAndAttachedEntity;
import com.get.aisplatformcenterap.vo.MBannerVo;
import com.get.aisplatformcenterap.entity.MBannerEntity;
import com.get.aisplatformcenterap.dto.BannerPutAwayParamsDto;
import com.get.aisplatformcenterap.dto.MBannerParamsDto;
import com.get.common.result.Page;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
* <AUTHOR>
* @description 针对表【m_banner(banner后台管理)】的数据库操作Service实现
* @createDate 2024-12-16 18:03:59
*/
@Service
public class MBannerServiceImpl extends ServiceImpl<MBannerMapper, MBannerEntity>
    implements MBannerService {

    @Autowired
    MBannerMapper mbannerMapper;

    @Autowired
    private UtilService utilService;

    @Autowired
    private SMediaAndAttachedMapper sMediaAndAttachedMapper;

    @Autowired
    private TencentCloudUtils tencentCloudUtils;


    @Override
    public List<MBannerVo> searchBannerPage(MBannerParamsDto params, Page page) {
        IPage<MBannerVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));


        String urlBase=tencentCloudUtils.getTencentCloudUrl();
        params.setMMageAddress(urlBase);



        List<MBannerVo>  result=mbannerMapper.searchBannerPage(pages,params);
        int totalCount = (int)pages.getTotal();
        Integer showCount = page.getShowCount();
        page.setTotalPage(page.getShowCount() != null && showCount > 0 ? totalCount % showCount == 0 ? totalCount / showCount : totalCount / showCount + 1 : 0);
        page.setTotalResult(totalCount);
        return result;
    }

    @Override
    public Long saveOrUpdateMessage(MBannerParamsDto entity) {
        if (entity == null) {
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","insert_vo_null!"));
        }
        utilService.updateUserInfoToEntity(entity);
        try {
            if (entity.getWebMetaDescription() != null && entity.getWebMetaDescription().length() > 0) {
                entity.setWebMetaDescription(java.net.URLDecoder.decode(entity.getWebMetaDescription(), "utf-8"));
            }
        }catch(Exception e){
            log.error(e.getMessage());
        }

        if(entity.getId()==null){
            entity.setStatus(1);
            mbannerMapper.insert(entity);


            if(entity.getFileGuid()!=null && !"".equals(entity.getFileGuid())){
                SMediaAndAttachedEntity sMediaAndAttachedEntity=new SMediaAndAttachedEntity();
                sMediaAndAttachedEntity.setFkFileGuid(entity.getFileGuid());
                sMediaAndAttachedEntity.setFkTableName("m_banner");
                sMediaAndAttachedEntity.setFkTableId(entity.getId());
                sMediaAndAttachedEntity.setRemark("BANNER图片");
                sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
            }
        }else {
            mbannerMapper.updateById(entity);

            if(entity.getFileGuid()!=null && !"".equals(entity.getFileGuid())){
                SMediaAndAttachedEntity attPo=sMediaAndAttachedMapper.selectOne(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
                                .eq(SMediaAndAttachedEntity::getFkTableName,"m_banner")
                                .eq(SMediaAndAttachedEntity::getFkTableId,entity.getId())
                );
                if(attPo!=null){
                    if(StringUtils.isEmpty(attPo.getFkFileGuid()) ||  !attPo.getFkFileGuid().equals(entity.getFileGuid()) ){
                        sMediaAndAttachedMapper.deleteById(attPo.getId());
                        SMediaAndAttachedEntity sMediaAndAttachedEntity=new SMediaAndAttachedEntity();
                        sMediaAndAttachedEntity.setFkFileGuid(entity.getFileGuid());
                        sMediaAndAttachedEntity.setFkTableName("m_banner");
                        sMediaAndAttachedEntity.setFkTableId(entity.getId());
                        sMediaAndAttachedEntity.setRemark("BANNER图片");
                        sMediaAndAttachedEntity.setGmtCreate(new Date());
                        sMediaAndAttachedEntity.setGmtCreateUser(SecureUtil.getLoginId());
                        sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
                    }
                }else{

                    SMediaAndAttachedEntity sMediaAndAttachedEntity=new SMediaAndAttachedEntity();
                    sMediaAndAttachedEntity.setFkFileGuid(entity.getFileGuid());
                    sMediaAndAttachedEntity.setFkTableName("m_banner");
                    sMediaAndAttachedEntity.setFkTableId(entity.getId());
                    sMediaAndAttachedEntity.setRemark("BANNER图片");
                    sMediaAndAttachedEntity.setGmtCreate(new Date());
                    sMediaAndAttachedEntity.setGmtCreateUser(SecureUtil.getLoginId());
                    sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
                }


            }
        }
        return entity.getId();
    }

    @Override
    public Long putAwayBanner(BannerPutAwayParamsDto vo) {
        if (vo == null) {
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","数据为空!"));
        }

        if(vo.getType()==2){//上架
            mbannerMapper.updatePutAway(vo);
        } else if (vo.getType()==1) {//下架
            LambdaQueryWrapper<MBannerEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(MBannerEntity::getStatus, 2);
            int num=mbannerMapper.selectCount(wrapper);
            if(num<=1){
                throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                        LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","在架banner，最少保留一条在架!"));
            }
            mbannerMapper.updatePutAway(vo);
        }


        return 0l;
    }

    @Override
    public Long deleteOne(Long id) {

        MBannerEntity dbnum=mbannerMapper.selectById(id);
        if(GeneralTool.isEmpty(dbnum)){
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","删除数据不存在!"));
        }
        if(dbnum.getStatus()!=null && dbnum.getStatus()==2){
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","上架数据不能删除!"));
        }

        mbannerMapper.deleteById(id);
        return id;
    }

    @Override
    public void deleteBatch(DeletePatchParamsDto patchVo) {

        if(GeneralTool.isEmpty(patchVo.getIds())){
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","ID不能为空!"));
        }
        List<MBannerEntity> list=mbannerMapper.selectBatchIds(Arrays.asList(patchVo.getIds()));
        Set<Integer> statusArr=list.stream().map(MBannerEntity::getStatus).collect(Collectors.toSet());
        if(statusArr.contains(2)){
            throw new GetServiceException(ResultCode.BUSINESS_EXCEPTION,
                    LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"","上架数据不能删除!"));
        }
        mbannerMapper.deleteBatchIds(Arrays.asList(patchVo.getIds()));

    }

    @Override
    public MBannerVo getDetail(Long id) {

        MBannerVo result=new MBannerVo();

        MBannerParamsDto params=new MBannerParamsDto();
        params.setId(id);
        String urlBase=tencentCloudUtils.getTencentCloudUrl();
        params.setMMageAddress(urlBase);
        result=mbannerMapper.getDetail(params);

        SMediaAndAttachedEntity attPo=sMediaAndAttachedMapper.selectOne(new LambdaQueryWrapper<SMediaAndAttachedEntity>()
                .eq(SMediaAndAttachedEntity::getFkTableName, "m_banner")
                .eq(SMediaAndAttachedEntity::getFkTableId, id)
        );
        if(attPo!=null){
            result.setFileGuid(attPo.getFkFileGuid());
        }


        return result;
    }


}




