<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.aisplatformcenter.mapper.ReleaseInfoMapper">

    <select id="getReleaseInfo" resultType="com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo">
        SELECT
            mri.id,
            mri.fk_platform_id,
            mri.fk_platform_code,
            mri.title,
            mri.version_num,
            mri.status,
            mri.release_time,
            mri.release_user,
            mri.withdraw_time,
            mri.withdraw_user,
            mri.gmt_create,
            mri.gmt_create_user,
            mri.gmt_modified,
            mri.gmt_modified_user
        FROM ais_platform_center.m_release_info mri
        WHERE 1=1
        <if test="releaseInfoSearchDto.title != null and releaseInfoSearchDto.title != ''">
            AND mri.title LIKE CONCAT('%',#{releaseInfoSearchDto.title},'%')
        </if>
        <if test="releaseInfoSearchDto.versionNum != null and releaseInfoSearchDto.versionNum != ''">
            AND mri.version_num LIKE CONCAT('%',#{releaseInfoSearchDto.versionNum},'%')
        </if>
        <if test="releaseInfoSearchDto.fkPlatformId != null and releaseInfoSearchDto.fkPlatformId != ''">
            AND mri.fk_platform_id = #{releaseInfoSearchDto.fkPlatformId}
        </if>
        <if test="releaseInfoSearchDto.status != null and releaseInfoSearchDto.status !=''">
            AND mri.status = #{releaseInfoSearchDto.status}
        </if>
        <if test="releaseInfoSearchDto.releaseTimeStart !=null">
            and DATE_FORMAT(mri.release_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{releaseInfoSearchDto.releaseTimeStart},'%Y-%m-%d')
        </if>
        <if test="releaseInfoSearchDto.releaseTimeEnd !=null">
            and DATE_FORMAT(mri.release_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{releaseInfoSearchDto.releaseTimeEnd},'%Y-%m-%d')
        </if>

        <if test="releaseInfoSearchDto.withdrawTimeStart !=null">
            and DATE_FORMAT(mri.withdraw_time,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{releaseInfoSearchDto.withdrawTimeStart},'%Y-%m-%d')
        </if>
        <if test="releaseInfoSearchDto.withdrawTimeEnd !=null">
            and DATE_FORMAT(mri.withdraw_time,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{releaseInfoSearchDto.withdrawTimeEnd},'%Y-%m-%d')
        </if>
        <if test="releaseInfoSearchDto.gmtCreateUser !=null and releaseInfoSearchDto.gmtCreateUser != ''">
            and LOWER(mri.gmt_create_user) LIKE CONCAT('%',LOWER(#{releaseInfoSearchDto.gmtCreateUser}),'%')
        </if>
        ORDER BY mri.fk_platform_id,mri.gmt_create DESC

    </select>

    <select id="getUserListByPermission" resultType="com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo">
        SELECT mri.id,
               mri.fk_platform_id,
               mri.fk_platform_code,
               mri.title,
               mri.version_num,
               mri.status,
               mri.release_time,
               mri.release_user,
               mri.withdraw_time,
               mri.withdraw_user,
               mri.gmt_create,
               mri.gmt_create_user,
               mri.gmt_modified,
               mri.gmt_modified_user
        from ais_platform_center.m_release_info mri
        where 1=1 AND mri.status = 1
        <if test="userScopedDataDto.fkPlatformId != null and userScopedDataDto.fkPlatformId != ''">
            AND mri.fk_platform_id = #{userScopedDataDto.fkPlatformId}
        </if>
        <if test="userScopedDataDto.fkPlatformCode != null and userScopedDataDto.fkPlatformCode != ''">
            AND mri.fk_platform_code = #{userScopedDataDto.fkPlatformCode}
        </if>
        ORDER BY mri.gmt_create DESC
    </select>
</mapper>
