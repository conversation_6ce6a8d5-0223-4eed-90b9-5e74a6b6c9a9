package com.get.aisplatformcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.aisplatformcenterap.dto.MLiveDto;
import com.get.aisplatformcenterap.dto.MLivePutAwayParamsDto;
import com.get.aisplatformcenterap.entity.MLiveEntity;
import com.get.aisplatformcenterap.vo.MLiveVo;
import com.get.common.result.Page;


import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_live】的数据库操作Service
* @createDate 2024-11-28 19:30:56
*/
public interface MLiveService extends IService<MLiveEntity> {
    /**
     * 直播列表
     * @param params
     * @param page
     * @return
     */
    List<MLiveVo> searchPage(MLiveDto params, Page page);

    /**
     * 新增或修改直播
     * @param dto
     * @return
     */
    Long saveOrUpdateMlive(MLiveDto dto);

    /**
     * 直播详细
     * @param id
     * @return
     */
    MLiveVo getMliveDetail(long id);

    /**
     * 直播上架 下架
     * @param vo
     * @return
     */
    Long putAwayMessage(MLivePutAwayParamsDto vo);

    boolean removeByIdInfo(Long id);
}
