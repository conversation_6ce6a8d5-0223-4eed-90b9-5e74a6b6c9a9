package com.get.aisplatformcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.aisplatformcenterap.vo.MMessageVo;
import com.get.aisplatformcenterap.entity.MMessageEntity;
import com.get.aisplatformcenterap.dto.DeletePatchParamsDto;
import com.get.aisplatformcenterap.dto.MMessagePutAwayParamsDto;
import com.get.aisplatformcenterap.dto.MMessageParamsDto;
import com.get.common.result.Page;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【m_message(消息记录)】的数据库操作Service
* @createDate 2024-12-16 18:03:59
*/
public interface MMessageService extends IService<MMessageEntity> {
    /**
     * 消息列表
     * @param params
     * @param page
     * @return
     */
    List<MMessageVo>  searchPage(MMessageParamsDto params, Page page);

    /**
     * 消息新增或编辑
     * @param entity
     * @return
     */
    Long saveOrUpdateMessage(MMessageEntity entity);

    /**
     * 消息上架下架
     * @param vo
     * @return
     */
    Long putAwayMessage(MMessagePutAwayParamsDto vo);

    /**
     * 消息删除
     * @param id
     * @return
     */
    Long deleteOne(Long id);

    /**
     * 消息批量删除
     * @param patchVo
     */
    void deleteBatch(DeletePatchParamsDto patchVo);
}
