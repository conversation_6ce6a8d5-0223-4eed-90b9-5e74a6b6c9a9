package com.get.aisplatformcenter.controller;


import com.get.aisplatformcenter.service.MLiveService;
import com.get.aisplatformcenterap.dto.MLiveDto;
import com.get.aisplatformcenterap.dto.MLivePutAwayParamsDto;

import com.get.aisplatformcenterap.vo.MLiveVo;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@Api(tags = "后台管理-视频直播")
@RestController
@RequestMapping("platform/mlive")
public class MLiveController {
    @Autowired
    private MLiveService mliveService;

    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/视频直播/列表查询")
    @PostMapping("searchPage")
    public ResponseBo<MLiveVo> searchPage(@RequestBody SearchBean<MLiveDto> page) {
        List<MLiveVo> datas = mliveService.searchPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "新增(修改)接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/视频直播/新增")
    @PostMapping("saveOrUpdate")
    public ResponseBo saveOrUpdate(@RequestBody @Validated(value = {BaseVoEntity.Add.class}) MLiveDto mlivedto){
        return SaveResponseBo.ok(mliveService.saveOrUpdateMlive(mlivedto));
    }


    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.DETAIL, description = "华通伙伴/视频直播/详情")
    @GetMapping("/getDetail/{id}")
    public ResponseBo<MLiveVo> getDetail(@PathVariable Long id){
        MLiveVo data=mliveService.getMliveDetail(id);
        return new ResponseBo<>(data);
    }


    @ApiOperation(value = "数据删除", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.DETAIL, description = "华通伙伴/视频直播/删除")
    @PostMapping("/delete/{id}")
    public ResponseBo<MLiveVo> delete(@PathVariable Long id){
        mliveService.removeByIdInfo(id);
        return SaveResponseBo.ok();
    }


    @ApiOperation(value = "一键(上架)下架-接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.ADD, description = "华通伙伴/消息管理/一键(上架)下架-接口")
    @PostMapping("putAway")
    public ResponseBo putAway(@RequestBody  @Valid MLivePutAwayParamsDto vo){
        return SaveResponseBo.ok(mliveService.putAwayMessage(vo));
    }




}
