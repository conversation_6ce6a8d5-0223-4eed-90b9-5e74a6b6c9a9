package com.get.aisplatformcenter.feign;

import com.get.aisplatformcenter.service.MReleaseInfoItemService;
import com.get.aisplatformcenter.service.MReleaseInfoService;
import com.get.aisplatformcenter.service.ULabelService;
import com.get.aisplatformcenter.service.UserInfoService;
import com.get.aisplatformcenterap.dto.GetPermissionMenuDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoAndItemDto;
import com.get.aisplatformcenterap.dto.ReleaseInfoSearchDto;
import com.get.aisplatformcenterap.dto.UserScopedDataDto;
import com.get.aisplatformcenterap.feign.IPlatformCenterClient;
import com.get.aisplatformcenterap.vo.LabelSearchAboutAgentVo;
import com.get.aisplatformcenterap.vo.MenuTreeVo;
import com.get.aisplatformcenterap.vo.PlatFormTypeVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVo;
import com.get.aisplatformcenterap.vo.ReleaseInfoAndItemVos;
import com.get.aisplatformcenterap.vo.ReleaseInfoItemVo;
import com.get.aisplatformcenterap.vo.UserInfoVo;
import com.get.common.result.SearchBean;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.salecenter.dto.AgentLabelDto;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;


@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class PlatformCenterClient implements IPlatformCenterClient {

    private final UserInfoService userInfoService;
    private final ULabelService uLabelService;
    private final MReleaseInfoService mReleaseInfoService;
    private final MReleaseInfoItemService mReleaseInfoItemService;


    @Override
    public Result<Set<Long>> getUserIdsByParam(String userName, Long fkAreaCityId, String bdName) {
        return Result.data(userInfoService.getUserIdsByParam(userName, fkAreaCityId, bdName));
    }

    @Override
    public Result<Map<Long, String>> getUserNickNamesByUserIds(Set<Long> userIds) {
        return Result.data(userInfoService.getUserNickNamesByUserIds(userIds));
    }

    @Override
    public Result<Map<Long, String>> getCityNamesByUserIds(Set<Long> userIds) {
        return Result.data(userInfoService.getCityNamesByUserIds(userIds));
    }

    @Override
    public Result<Map<Long, String>> getMobileByUserIds(Set<Long> userIds) {
        return Result.data(userInfoService.getMobileByUserIds(userIds));
    }

    @Override
    public Result<Set<Long>> getUserIdsByNameOrMobile(String userName, String phoneNumber) {
        return Result.data(userInfoService.getUserIdsByNameOrMobile(userName, phoneNumber));
    }

    @Override
    public Result<Map<Long, UserInfoVo>> getUserInfoDtoByIds(Set<Long> userIds) {
        return Result.data(userInfoService.getUserInfoDtoByIds(userIds));
    }

    @Override
    public Result<LabelSearchAboutAgentVo> getLabelByLabelTypeIdAndKeyWord(SearchBean<AgentLabelDto> page) {
        return Result.data(uLabelService.getLabelByLabelTypeIdAndKeyWord(page.getData(), page));
    }

    @Override
    public Result<List<PlatFormTypeVo>> getPlatformTypeDropDown() {
        return Result.data(mReleaseInfoService.getPlatformTypeDropDown());
    }

    @Override
    public Result<ReleaseInfoAndItemVos> getReleaseInfoAndPage(SearchBean<ReleaseInfoSearchDto> page) {
        return Result.data(mReleaseInfoService.getReleaseInfoAndItemAndPage(page.getData(), page));
    }

    @Override
    public Result insertReleaseInfo(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        mReleaseInfoService.addReleaseInfoAndItem(releaseInfoAndItemDto);
        return Result.success("添加成功");
    }

    @Override
    public Result deleteReleaseInfo(Long id) {
        mReleaseInfoService.deleteReleaseInfoAndItem(id);
        return Result.success("删除成功");
    }

    @Override
    public Result<ReleaseInfoAndItemVo> getDetailedInformationById(Long id) {
        mReleaseInfoService.getDetailedInformationById(id);
        return Result.data(mReleaseInfoService.getDetailedInformationById(id));
    }

    @Override
    public Result updateReleaseInfo(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        mReleaseInfoService.editReleaseInfoAndItem(releaseInfoAndItemDto);
        return Result.success("编辑成功");
    }

    @Override
    public Result updateReleaseInfoStatus(ReleaseInfoAndItemDto releaseInfoAndItemDto) {
        mReleaseInfoService.updateReleaseInfoStatus(releaseInfoAndItemDto);
        return Result.success("状态修改成功");
    }

    @Override
    public Result<ReleaseInfoAndItemVos> getUserListByResourceKeysAndPage(SearchBean<UserScopedDataDto> page) {
        return Result.data(mReleaseInfoService.getUserListByResourceKeysAndPage(page));
    }

    @Override
    public Result<List<ReleaseInfoAndItemVo>> getUserListByResourceKeys(UserScopedDataDto userScopedDataDto) {
        return Result.data(mReleaseInfoService.getUserListByResourceKeys(userScopedDataDto, null));
    }

    @Override
    public Result<List<ReleaseInfoItemVo>> getReleaseInfoItemByReleaseInfoIdAndResourceKeys(UserScopedDataDto userScopedDataDto) {
        return Result.data(mReleaseInfoItemService.getReleaseInfoItemByReleaseInfoIdAndResourceKeys(userScopedDataDto));
    }

    @Override
    public Result<List<MenuTreeVo>> getPartnerPermissionMenu(GetPermissionMenuDto getPermissionMenuDto) {
        return Result.data(mReleaseInfoService.getPartnerPermissionMenu(getPermissionMenuDto));
    }

}
