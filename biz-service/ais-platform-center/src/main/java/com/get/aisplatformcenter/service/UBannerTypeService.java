package com.get.aisplatformcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.aisplatformcenterap.entity.UBannerTypeEntity;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【u_banner_type】的数据库操作Service
* @createDate 2024-12-16 18:03:59
*/
public interface UBannerTypeService extends IService<UBannerTypeEntity> {
    /**
     * banner类型查询
     * @param dto
     * @return
     */
    List<UBannerTypeEntity> searchList(UBannerTypeEntity dto);
    Long delete(Long id);
}
