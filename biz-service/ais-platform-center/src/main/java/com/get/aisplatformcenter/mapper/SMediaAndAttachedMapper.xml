<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.aisplatformcenter.mapper.SMediaAndAttachedMapper">

    <resultMap id="BaseResultMap" type="com.get.aisplatformcenterap.entity.SMediaAndAttachedEntity">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="fkFileGuid" column="fk_file_guid" jdbcType="VARCHAR"/>
            <result property="fkTableName" column="fk_table_name" jdbcType="VARCHAR"/>
            <result property="fkTableId" column="fk_table_id" jdbcType="BIGINT"/>
            <result property="typeKey" column="type_key" jdbcType="VARCHAR"/>
            <result property="indexKey" column="index_key" jdbcType="INTEGER"/>
            <result property="link" column="link" jdbcType="VARCHAR"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
            <result property="gmtCreateUser" column="gmt_create_user" jdbcType="VARCHAR"/>
            <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
            <result property="gmtModifiedUser" column="gmt_modified_user" jdbcType="VARCHAR"/>
    </resultMap>
    <select id="selectFileArrays" resultType="com.get.aisplatformcenterap.vo.FileArray">
        SELECT
            platFormFile.id,
            platFormFile.file_guid AS fileLiveGuid,
            CONCAT(#{mMageAddress}, platFormFile.file_key) AS fileKeyFile,
            platFormFile.file_name_orc
            from ais_platform_center.s_media_and_attached sAttached
                INNER JOIN ais_file_center.m_file_platform platFormFile ON platFormFile.file_guid = sAttached.fk_file_guid
        WHERE sAttached.fk_table_name='m_live'
                AND  sAttached.type_key='m_live_file'
                AND  sAttached.fk_table_id=#{id}


    </select>
    <select id="selectPublicPlatformFileArrays" resultType="com.get.aisplatformcenterap.vo.FilePubArray">
        SELECT
        mFilePlatform.id,
        mFilePlatform.file_guid AS fileGuid,
        CONCAT(#{mMageAddress}, mFilePlatform.file_key) AS fileKey,
        mFilePlatform.file_name_orc
        from ais_platform_center.s_media_and_attached sAttached
        INNER JOIN ais_file_center.m_file_platform mFilePlatform ON mFilePlatform.file_guid = sAttached.fk_file_guid
        WHERE sAttached.fk_table_name=#{fkTableName}
        <if test="typeKey!=null and typeKey != '' ">
            AND  sAttached.type_key=#{typeKey}
        </if>
        AND  sAttached.fk_table_id=#{fkTableId} ORDER BY sAttached.gmt_create
        LIMIT 100


    </select>


</mapper>
