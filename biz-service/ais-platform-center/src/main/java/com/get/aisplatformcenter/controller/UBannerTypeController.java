package com.get.aisplatformcenter.controller;

import com.get.aisplatformcenter.service.UBannerTypeService;
import com.get.aisplatformcenterap.entity.MPlatformEntity;
import com.get.aisplatformcenterap.entity.UBannerTypeEntity;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Api(tags = "后台管理-banner类型管理")
@RestController
@RequestMapping("platform/bannertype")
public class UBannerTypeController {
    @Autowired
    private UBannerTypeService ubannerTypeService;


    @ApiOperation(value = "列表数据")
    @OperationLogger(module = LoggerModulesConsts.PARTNERCENTER, type = LoggerOptTypeConst.LIST, description = "华通伙伴/banner类型管理/列表查询")
    @PostMapping("searchList")
    public ResponseBo<UBannerTypeEntity> searchList(@RequestBody UBannerTypeEntity dto){
        List<UBannerTypeEntity> datas=ubannerTypeService.searchList(dto);

        return new ResponseBo(datas);
    }


    @ApiOperation(value = "删除接口", notes = "")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        ubannerTypeService.delete(id);
        return DeleteResponseBo.ok();
    }
    @ApiOperation(value = "新增接口", notes = "")
    @PostMapping("insert")
    public ResponseBo<UBannerTypeEntity> insert(UBannerTypeEntity entity) {
        if(entity!=null){
            ubannerTypeService.save(entity);
        }
        return DeleteResponseBo.ok();
    }





































}
