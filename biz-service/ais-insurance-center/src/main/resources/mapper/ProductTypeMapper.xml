<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.insurancecenter.mapper.ProductTypeMapper">


    <select id="selectByKey" resultType="com.get.insurancecenter.entity.ProductType">
        select *
        from u_product_type
        where type_key = #{key}
        limit 1
    </select>
</mapper>