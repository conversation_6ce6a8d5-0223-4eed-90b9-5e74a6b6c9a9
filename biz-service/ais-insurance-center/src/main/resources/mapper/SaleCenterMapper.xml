<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.insurancecenter.mapper.SaleCenterMapper">

    <select id="selectAgentList" resultType="com.get.insurancecenter.vo.commission.AgentVo">
        select a.id as agentId,
        a.num as agentNum,
        a.name as agentName,
        a.is_active as isActive,
        a.fk_area_country_id as areaCountryId,
        a.fk_area_state_id as areaStateId,
        a.fk_area_city_id as areaCityId
        from ais_sale_center.m_agent a
        INNER JOIN ais_sale_center.r_agent_staff ras ON ras.fk_agent_id = a.id
        AND ras.is_active = 1
        <where>
            a.id in
            <foreach item="item" index="index" collection="agentIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            <if test="param.countryId != null and param.countryId > 0">
                and a.fk_area_country_id =
                #{param.countryId}
            </if>
            <if test="param.areaStateId != null and param.areaStateId > 0">
                and a.fk_area_state_id =
                #{param.areaStateId}
            </if>
            <if test="param.areaRegionId != null and param.areaRegionId > 0">
                and exists (select 1 from ais_sale_center.r_staff_bd_code bc where ras.fk_staff_id = bc.fk_staff_id
                    and find_in_set(#{param.areaRegionId}, bc.fk_area_region_id)
                )
            </if>
            <if test="param.agentName != null and param.agentName != ''">
                and a.name like concat('%',
                #{param.agentName},
                '%'
                )
            </if>
            <if test="param.insurantName != null and param.insurantName != ''">
                and exists (select 1 from app_insurance_center.m_insurance_order o where o.fk_agent_id = a.id and o.insurant_name like concat('%',
                #{param.insurantName},
                '%'
                )
                )
            </if>
        </where>
        order by a.id desc
    </select>


    <select id="selectAgentCompanyList" resultType="com.get.insurancecenter.vo.agent.AgentCompanyEntity">
        select * from ais_sale_center.r_agent_company
        where fk_agent_id in
        <foreach item="item" index="index" collection="agentIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectCompanyList" resultType="com.get.permissioncenter.vo.CompanyVo">
        select * from ais_permission_center.m_company
        where id in
        <foreach item="item" index="index" collection="companyIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="getPayablePlanList" resultType="com.get.insurancecenter.vo.commission.PayablePlanVo">
        select id as payablePlanId,
        commission_rate as commissionRate,
        commission_amount as commissionAmount
        from ais_sale_center.m_payable_plan
        where id in
        <foreach item="item" collection="payablePlanIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getAgentAccountList" resultType="com.get.insurancecenter.vo.commission.AgentAccountVo">
        select ca.id as accountId,
        ca.fk_agent_id as agentId,
        ca.fk_currency_type_num as currencyTypeNum,
        ca.account_card_type as accountCardType,
        ca.bank_account as bankAccount,
        ca.bank_account_num as bankAccountNum,
        ca.bank_name as bankName,
        ca.bank_branch_name as bankBranchName,
        ca.fk_area_country_id as fkAreaCountryId,
        ca.fk_area_state_id as fkAreaStateId,
        ca.fk_area_city_id as fkAreaCityId,
        ca.fk_area_city_division_id as fkAreaCityDivisionId,
        ca.bank_address as bankAddress,
        ca.bank_code_type as bankCodeType,
        ca.bank_code as bankCode,
        ca.area_country_code as areaCountryCode,
        ca.is_default as isDefault,
        ca.is_active as isActive,
        ca.remark as remark,
        ma.name as agentName
        from ais_sale_center.m_agent_contract_account ca
        left join ais_sale_center.m_agent ma on ma.id = ca.fk_agent_id
        where ca.id in
        <foreach item="item" collection="accountIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="getAgentAccount" resultType="com.get.insurancecenter.vo.commission.AgentAccountVo">
        select ca.id                       as accountId,
               ca.fk_agent_id              as agentId,
               ca.fk_currency_type_num     as currencyTypeNum,
               ca.account_card_type        as accountCardType,
               ca.bank_account             as bankAccount,
               ca.bank_account_num         as bankAccountNum,
               ca.bank_name                as bankName,
               ca.bank_branch_name         as bankBranchName,
               ca.fk_area_country_id       as fkAreaCountryId,
               ca.fk_area_state_id         as fkAreaStateId,
               ca.fk_area_city_id          as fkAreaCityId,
               ca.fk_area_city_division_id as fkAreaCityDivisionId,
               ca.bank_address             as bankAddress,
               ca.bank_code_type           as bankCodeType,
               ca.bank_code                as bankCode,
               ca.area_country_code        as areaCountryCode,
               ca.is_default               as isDefault,
               ca.is_active                as isActive,
               ca.remark                   as remark,
               ma.name                     as agentName
        from ais_sale_center.m_agent_contract_account ca
                 left join ais_sale_center.m_agent ma on ma.id = ca.fk_agent_id
        where ca.id = #{id}
    </select>

    <select id="selectAgentListByIds" resultType="com.get.insurancecenter.vo.commission.AgentVo">
        select a.id as agentId,
        a.num as agentNum,
        a.name as agentName,
        a.is_active as isActive,
        a.fk_area_country_id as areaCountryId,
        a.fk_area_state_id as areaStateId,
        a.fk_area_city_id as areaCityId
        from ais_sale_center.m_agent a
        INNER JOIN ais_sale_center.r_agent_staff ras ON ras.fk_agent_id = a.id
        AND ras.is_active = 1
        where a.id in
        <foreach item="item" collection="agentIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>