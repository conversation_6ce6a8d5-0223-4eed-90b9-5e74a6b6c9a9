<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.insurancecenter.mapper.InsuranceOrderMapper">


    <select id="selectByOrderNum" resultType="com.get.insurancecenter.entity.InsuranceOrder">
        SELECT *
        FROM m_insurance_order
        WHERE order_num = #{orderNum}
        limit 1
    </select>

    <select id="selectPendingOrders" resultType="com.get.insurancecenter.entity.InsuranceOrder">
        SELECT o.*,
               t.type_key as productTypeKey
        FROM m_insurance_order o
                 LEFT JOIN u_product_type t ON t.id = o.fk_product_type_id
        WHERE o.order_status = 0
        ORDER BY o.gmt_create ASC
        LIMIT #{limit}
    </select>

    <select id="selectOrderDetailById" resultType="com.get.insurancecenter.vo.order.OrderDetailVo">
        SELECT o.*,
               t.type_key  as productTypeKey,
               t.type_name as productTypeName,
               c.name      as insuranceCompanyName
        FROM m_insurance_order o
                 LEFT JOIN u_product_type t ON t.id = o.fk_product_type_id
                 LEFT JOIN u_insurance_company c ON c.id = o.fk_insurance_company_id
        WHERE o.id = #{id}
    </select>

    <select id="selectOrderList" resultType="com.get.insurancecenter.entity.InsuranceOrder">
        SELECT o.*,
        t.type_key as productTypeKey
        FROM m_insurance_order o
        LEFT JOIN u_product_type t ON t.id = o.fk_product_type_id
        <where>
            o.fk_partner_user_id = #{partnerUserId}
            <if test="orderStatus != null and orderStatus == 1">
                AND o.order_status in (0, 1)
            </if>
            <if test="orderStatus != null and orderStatus  != 1">
                AND o.order_status =
                #{orderStatus}
            </if>
            <if test="date != null and date != ''">
                AND DATE_FORMAT(o.gmt_create, '%Y-%m') =
                <choose>
                    <when test="date.length() == 7">
                        #{date}
                    </when>
                    <otherwise>
                        CONCAT(SUBSTRING_INDEX(#{date}, '-', 1), '-', LPAD(SUBSTRING_INDEX(#{date}, '-', -1), 2, '0'))
                    </otherwise>
                </choose>
            </if>
        </where>
        ORDER BY o.gmt_create ASC
    </select>

    <!--   订单状态(展示以此字段为准):-2:下单失败;1:下单中;2:已完成待生效;3:生效中;4:已失效;5:已退款(退保);6:已取消;")-->
    <select id="selectOrderPage" resultType="com.get.insurancecenter.vo.order.OrderDetailVo">
        select o.*
        from m_insurance_order o
        <where>
            <if test="param.insuranceCompanyId != null and param.insuranceCompanyId > 0">
                and o.fk_insurance_company_id =
                #{param.insuranceCompanyId}
            </if>
            <if test="param.insuranceType != null and param.insuranceType != '' ">
                and o.insurance_type =
                #{param.insuranceType}
            </if>
            <if test="param.insuranceNum != null and param.insuranceNum != '' ">
                and o.insurance_num =
                #{param.insuranceNum}
            </if>
            <if test="param.insurantName != null and param.insurantName != '' ">
                and o.insurant_name like CONCAT('%',
                #{param.insurantName},
                '%'
                )
            </if>
            <if test="param.orderNum != null and param.orderNum != '' ">
                and o.order_num like CONCAT('%',
                #{param.orderNum},
                '%'
                )
            </if>
            <if test="param.orderStatus != null and param.orderStatus == 1 ">
                and o.order_status in (0, 1)
            </if>
            <if test="param.orderStatus != null and (param.orderStatus == -2 or param.orderStatus == 5 or param.orderStatus == 6)">
                and o.order_status =
                #{param.orderStatus}
            </if>
            <if test="param.orderStatus != null and param.orderStatus == 2">
                and o.order_status = 2 and CURRENT_DATE() &lt; DATE(o.insurance_start_time)
            </if>
            <if test="param.orderStatus != null and param.orderStatus == 3">
                and o.order_status = 2 and CURRENT_DATE() BETWEEN DATE(o.insurance_start_time) AND DATE(o.insurance_end_time)
            </if>
            <if test="param.orderStatus != null and param.orderStatus == 4">
                and o.order_status = 2 and CURRENT_DATE() &gt; DATE(o.insurance_end_time)
            </if>
        </where>
        order by o.gmt_create desc
    </select>

    <select id="selectCommissionOrderByAgentId" resultType="com.get.insurancecenter.vo.order.OrderDetailVo">
        select o.*,
        c.name as insuranceCompanyName,
        t.type_key as productTypeKey,
        t.type_name as productTypeName,
        s.fk_payable_plan_id as payablePlanId,
        s.status_settlement as statusSettlement,
        s.fk_num_opt_batch as numOptBatch,
        bi.fk_currency_type_num_actual as actualCurrencyTypeNum,
        bi.service_fee_actual as serviceFee
        from m_insurance_order o
        left join u_insurance_company c on c.id = o.fk_insurance_company_id
        left join u_product_type t on t.id = o.fk_product_type_id
        inner join m_insurance_order_settlement s on s.fk_insurance_order_id = o.id
        left join m_settlement_bill_item bi on bi.fk_insurance_order_settlement_id = s.id
        <where>
            o.fk_agent_id = #{agentId}
            and o.order_status = 2
            and s.status_settlement in
            <foreach item="item" collection="settlementStatusList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
            <if test="insurantName != null and insurantName != ''">
                and o.insurant_name like CONCAT('%',
                #{insurantName},
                '%'
                )
            </if>
        </where>
        order by o.gmt_create desc
    </select>

    <select id="getOrderBySettlementStatus" resultType="com.get.insurancecenter.entity.InsuranceOrder">
        select o.*
        from m_insurance_order o
        <where>
            o.order_status = 2
            and exists (select 1 from m_insurance_order_settlement s where s.fk_insurance_order_id = o.id
            and s.status_settlement in
            <foreach item="item" collection="settlementStatusList" separator="," open="(" close=")" index="">
                #{item}
            </foreach>
            )
        </where>
    </select>

    <select id="selectCreditCardOrderPage" resultType="com.get.insurancecenter.vo.order.CreditCardOrderVo">
        select o.*,
        c.name as insuranceCompanyName,
        t.type_key as productTypeKey,
        t.type_name as productTypeName,
        ma.name as agentName,
        ma.num as agentNum
        from app_insurance_center.m_insurance_order o
        left join app_insurance_center.u_insurance_company c on c.id = o.fk_insurance_company_id
        left join app_insurance_center.u_product_type t on t.id = o.fk_product_type_id
        left join ais_sale_center.m_agent ma on ma.id = o.fk_agent_id
        <where>
            o.payment_type = 2 and o.fk_credit_card_id = #{param.creditCardId} and o.payment_type = 2
            and o.mp_payment_status = 1 and o.order_status = 2
            <if test="param.insuranceCompanyId != null and param.insuranceCompanyId > 0">
                and o.fk_company_id =
                #{param.insuranceCompanyId}
            </if>
            <if test="param.insuranceType != null and param.insuranceType != ''">
                and o.insurance_type =
                #{param.insuranceType}
            </if>
            <if test="param.insuranceNum != null and param.insuranceNum != ''">
                and o.insurance_num like concat('%',
                #{param.insuranceNum},
                '%'
                )
            </if>
            <if test="param.orderNum != null and param.orderNum != ''">
                and o.order_num like concat('%',
                #{param.orderNum},
                '%'
                )
            </if>
            <if test="param.insurantName != null and param.insurantName != ''">
                and o.insurant_name like concat('%',
                #{param.insurantName},
                '%'
                )
            </if>
            <if test="param.agentName != null and param.agentName != ''">
                and
                (ma.name like concat('%',#{param.agentName},'%') or ma.num like concat('%',#{param.agentName},'%'))
            </if>
        </where>
        order by o.gmt_create desc
    </select>
</mapper>