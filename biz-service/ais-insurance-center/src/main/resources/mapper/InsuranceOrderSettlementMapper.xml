<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.insurancecenter.mapper.InsuranceOrderSettlementMapper">

    <update id="batchUpdatePaymentFormItemId">
        <foreach collection="list" item="item" separator=";">
            UPDATE m_insurance_order_settlement
            SET
            fk_payment_form_item_id = #{item.paymentFormItemId},
            gmt_modified = NOW(),
            gmt_modified_user = #{loginUserId},
            status_settlement = 4
            WHERE fk_payable_plan_id = #{item.fkPayablePlanId}
        </foreach>
    </update>


    <select id="selectToConfirmList" resultType="com.get.insurancecenter.vo.commission.AgentAccountVo">
        select b.fk_agent_id as agentId,
        b.id as settlementBillId,
        b.fk_currency_type_num as settlementCurrencyTypeNum,
        b.amount as settlementAmount,
        b.fk_agent_contract_account_id as accountId
        from m_insurance_order_settlement ios
        inner join m_settlement_bill_item bi on ios.id = bi.fk_insurance_order_settlement_id
        inner join m_settlement_bill b on bi.fk_settlement_bill_id = b.id
        inner join m_insurance_order o on ios.fk_insurance_order_id = o.id
        where o.order_status = 2
        and ios.status_settlement in
        <foreach item="item" collection="statusList" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
    </select>


    <select id="getPayablePlanList" resultType="com.get.insurancecenter.vo.commission.PayablePlanVo">
        select id as payablePlanId,
        commission_rate as commissionRate,
        commission_amount as commissionAmount
        from ais_sale_center.m_payable_plan
        where id in
        <foreach item="item" collection="payablePlanIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>


    <select id="selectSettledOrderList" resultType="com.get.insurancecenter.vo.commission.SettledOrderVo">
        SELECT ios.*,
               io.fk_agent_id as agentId,
               sbi.fk_settlement_bill_id as settlementBillId,
               sb.fk_agent_contract_account_id as agentContractAccountId,
               sb.fk_currency_type_num as settlementCurrencyTypeNum,
               sb.amount as settlementAmount
        FROM m_insurance_order_settlement ios
                 INNER JOIN m_insurance_order io ON ios.fk_insurance_order_id = io.id
                 INNER JOIN ais_sale_center.m_agent ma ON io.fk_agent_id = ma.id
                 INNER JOIN ais_sale_center.r_agent_staff ras ON ras.fk_agent_id = ma.id
                 INNER JOIN m_settlement_bill_item sbi ON sbi.fk_insurance_order_settlement_id = ios.id
                 INNER JOIN m_settlement_bill sb ON sbi.fk_settlement_bill_id = sb.id
        WHERE ios.id IN (SELECT inner_max.id
                         FROM (SELECT MAX(t.id) AS id
                               FROM m_insurance_order_settlement t
                               WHERE t.fk_num_opt_batch IS NOT NULL
                                 AND t.fk_num_opt_batch != ''
                                 AND t.status_settlement IN (2,3, 4)
                               GROUP BY t.fk_num_opt_batch
                               ORDER BY MAX(t.gmt_create) DESC
                               LIMIT #{offset}, #{limit}) inner_max)
          AND ios.status_settlement IN
        <foreach item="item" collection="statusSettlement" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        <if test="param.countryId != null and param.countryId > 0">
            and ma.fk_area_country_id = #{param.countryId}
        </if>
        <if test="param.areaStateId != null and param.areaStateId > 0">
            and ma.fk_area_state_id = #{param.areaStateId}
        </if>
        <if test="param.areaRegionId != null and param.areaRegionId > 0">
            and exists (select 1 from ais_sale_center.r_staff_bd_code bc where ras.fk_staff_id = bc.fk_staff_id
                    and find_in_set(#{param.areaRegionId}, bc.fk_area_region_id))
        </if>
        <if test="param.agentName != null and param.agentName != ''">
            and ma.name like concat('%',#{param.agentName},'%')
        </if>
        <if test="param.insurantName != null and param.insurantName != ''">
            and exists (select 1 from app_insurance_center.m_insurance_order o where o.fk_agent_id = ma.id and o.insurant_name like concat('%',
            #{param.insurantName}, '%' ))
        </if>
        ORDER BY FIELD(ios.status_settlement, 3, 4)
    </select>

    <select id="selectSettledOrderCount" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM (
        SELECT ios.id
        FROM m_insurance_order_settlement ios
        INNER JOIN m_insurance_order io ON ios.fk_insurance_order_id = io.id
        INNER JOIN ais_sale_center.m_agent ma ON io.fk_agent_id = ma.id
        INNER JOIN ais_sale_center.r_agent_staff ras ON ras.fk_agent_id = ma.id
        INNER JOIN m_settlement_bill_item sbi ON sbi.fk_insurance_order_settlement_id = ios.id
        INNER JOIN m_settlement_bill sb ON sbi.fk_settlement_bill_id = sb.id
        WHERE ios.id IN (
        SELECT MAX(t.id)
        FROM m_insurance_order_settlement t
        WHERE t.fk_num_opt_batch IS NOT NULL
        AND t.fk_num_opt_batch != ''
        AND t.status_settlement IN (2,3, 4)
        GROUP BY t.fk_num_opt_batch
        )
        AND ios.status_settlement IN
        <foreach item="item" collection="statusSettlement" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        <if test="param.countryId != null and param.countryId > 0">
            AND ma.fk_area_country_id = #{param.countryId}
        </if>
        <if test="param.areaStateId != null and param.areaStateId > 0">
            AND ma.fk_area_state_id = #{param.areaStateId}
        </if>
        <if test="param.areaRegionId != null and param.areaRegionId > 0">
            AND EXISTS (
            SELECT 1
            FROM ais_sale_center.r_staff_bd_code bc
            WHERE ras.fk_staff_id = bc.fk_staff_id
              AND FIND_IN_SET(#{param.areaRegionId}, bc.fk_area_region_id)
            )
        </if>
        <if test="param.agentName != null and param.agentName != ''">
            AND ma.name LIKE CONCAT('%', #{param.agentName}, '%')
        </if>
        <if test="param.insurantName != null and param.insurantName != ''">
            AND EXISTS (
            SELECT 1
            FROM app_insurance_center.m_insurance_order o
            WHERE o.fk_agent_id = ma.id
              AND o.insurant_name LIKE CONCAT('%', #{param.insurantName}, '%')
            )
        </if>
        ) AS sub_count

    </select>

    <select id="selectSettlementOrderList" resultType="com.get.insurancecenter.vo.SettlementOrderVo">
        SELECT
            -- 业务ID字段
            ios.id as insuranceOrderSettlementId,
            io.id as insuranceOrderId,
            sbi.id as settlementBillItemId,
            io.fk_agent_id as agentId,
            io.fk_partner_user_id as partnerUserId,
            io.fk_insurance_company_id as insuranceCompanyId,
            io.fk_product_type_id as productTypeId,
            ios.fk_payable_plan_id as payablePlanId,
            -- 业务数据字段
            ios.status_settlement as orderStatus,
            io.order_num as orderNum,
            io.insurance_num as insuranceNum,
            io.insurant_name as insurantName,
            io.insurant_gender as insurantGender,
            io.insurant_nationality as insurantNationality,
            io.insurant_passport_num as insurantPassportNum,
            io.insurance_start_time as insuranceStartTime,
            io.insurance_end_time as insuranceEndTime,
            io.insurance_amount as insuranceAmount,
            io.order_time as orderTime,
            io.insurance_type as insuranceType,
            ic.name as insuranceCompanyName,
            pt.type_name as productTypeName,
            pt.commission_rate_agent as commissionRateAgent,
            pp.payable_amount as payableAmount,
            sbi.service_fee_actual as serviceFee,
            a.name as agentName,
            mpu.name as partnerName
        FROM m_insurance_order_settlement ios
        INNER JOIN m_insurance_order io ON ios.fk_insurance_order_id = io.id
        LEFT JOIN u_insurance_company ic ON io.fk_insurance_company_id = ic.id
        LEFT JOIN u_product_type pt ON io.fk_product_type_id = pt.id
        LEFT JOIN ais_sale_center.m_payable_plan pp ON ios.fk_payable_plan_id = pp.id
        LEFT JOIN m_settlement_bill_item sbi ON sbi.fk_insurance_order_settlement_id = ios.id
        LEFT JOIN ais_sale_center.m_agent a ON io.fk_agent_id = a.id
        LEFT JOIN app_partner_center.m_partner_user mpu ON io.fk_partner_user_id = mpu.id
        WHERE ios.status_settlement IN (0, 1)
        <if test="query.insuranceCompanyId != null">
            AND io.fk_insurance_company_id = #{query.insuranceCompanyId}
        </if>
        <if test="query.orderStatus != null">
            AND ios.status_settlement = #{query.orderStatus}
        </if>
        <if test="query.agentName != null and query.agentName != ''">
            AND a.name LIKE CONCAT('%', #{query.agentName}, '%')
        </if>
        <if test="query.insurantName != null and query.insurantName != ''">
            AND io.insurant_name LIKE CONCAT('%', #{query.insurantName}, '%')
        </if>
        <if test="query.insuranceNum != null and query.insuranceNum != ''">
            AND io.insurance_num LIKE CONCAT('%', #{query.insuranceNum}, '%')
        </if>
        <if test="query.startTime != null">
            AND io.order_time >= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND io.order_time &lt;= #{query.endTime}
        </if>
        ORDER BY ios.status_settlement ASC, io.order_time DESC
    </select>




</mapper>