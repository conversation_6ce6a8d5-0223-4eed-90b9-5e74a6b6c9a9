<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.insurancecenter.mapper.InstitutionCenterMapper">

    <select id="selectAreaCountryList" resultType="com.get.institutioncenter.entity.AreaCountry">
        select * from ais_institution_center.u_area_country
        where id in
        <foreach item="item" collection="countryIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectAreaStateList" resultType="com.get.institutioncenter.entity.AreaState">
        select * from ais_institution_center.u_area_state
        where id in
        <foreach item="item" collection="stateIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectAreaCityList" resultType="com.get.institutioncenter.entity.AreaCity">
        select * from ais_institution_center.u_area_city
        where id in
        <foreach item="item" collection="cityIds" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>