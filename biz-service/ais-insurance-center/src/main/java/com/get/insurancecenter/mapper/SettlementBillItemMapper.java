package com.get.insurancecenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.insurancecenter.entity.SettlementBillItem;
import com.get.insurancecenter.vo.commission.SettlementBillItemVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SettlementBillItemMapper extends BaseMapper<SettlementBillItem> {

    /**
     * 根据结算单Id查询结算单子项列表
     * @param billId
     * @return
     */
    List<SettlementBillItemVo> selectSettlementBillItemListByBillId(@Param("billId") Long billId);

}
