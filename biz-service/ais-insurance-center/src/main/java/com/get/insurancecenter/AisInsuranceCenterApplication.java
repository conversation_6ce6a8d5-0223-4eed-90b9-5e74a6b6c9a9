package com.get.insurancecenter;

import com.get.common.constant.AppCenterConstant;
import com.get.core.cloud.feign.EnableGetFeign;
import com.get.core.start.GetApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@EnableGetFeign
@SpringCloudApplication
@MapperScan("com.get.insurancecenter.mapper")
@EnableAsync
public class AisInsuranceCenterApplication {
    public static void main(String[] args) {
        GetApplication.run(AppCenterConstant.APPLICATION_INSURANCE_CENTER, AisInsuranceCenterApplication.class, args);
    }

}
