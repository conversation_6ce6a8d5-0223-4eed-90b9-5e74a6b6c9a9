package com.get.insurancecenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.financecenter.vo.InsurancePaymentFormVo;
import com.get.insurancecenter.dto.commission.AgentDto;
import com.get.insurancecenter.dto.SettlementOrderQueryDto;
import com.get.insurancecenter.entity.InsuranceOrderSettlement;
import com.get.insurancecenter.vo.commission.AgentAccountVo;
import com.get.insurancecenter.vo.commission.PayablePlanVo;
import com.get.insurancecenter.vo.commission.SettledOrderVo;
import com.get.insurancecenter.vo.SettlementOrderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
@Mapper
public interface InsuranceOrderSettlementMapper extends BaseMapper<InsuranceOrderSettlement> {

    /**
     * 根据状态获取结算列表
     *
     * @return
     */
    List<AgentAccountVo> selectToConfirmList(@Param("statusList") List<Integer> statusList);

    /**
     * 获取应付计划列表
     *
     * @param payablePlanIds
     * @return
     */
    List<PayablePlanVo> getPayablePlanList(@Param("payablePlanIds") List<Long> payablePlanIds);


    /**
     * 获取确认结算列表
     *
     * @param offset
     * @param limit
     * @param param
     * @return
     */
    List<SettledOrderVo> selectSettledOrderList(@Param("offset") int offset,
                                                @Param("limit") int limit,
                                                @Param("param") AgentDto param,
                                                @Param("statusSettlement") List<Integer> statusSettlement);

    /**
     * 获取确认结算列表总数
     *
     * @param param
     * @return
     */
    Integer selectSettledOrderCount(@Param("param") AgentDto param,@Param("statusSettlement") List<Integer> statusSettlement);


    /**
     * 批量确认结算
     * @param list
     * @param loginUserId
     */
    void batchUpdatePaymentFormItemId(@Param("list") List<InsurancePaymentFormVo> list,
                                      @Param("loginUserId") String loginUserId);

    /**
     * 查询结算订单列表
     */
    List<SettlementOrderVo> selectSettlementOrderList(IPage<SettlementOrderVo> page,
                                                       @Param("query") SettlementOrderQueryDto query);

}
