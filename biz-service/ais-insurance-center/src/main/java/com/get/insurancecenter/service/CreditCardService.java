package com.get.insurancecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.insurancecenter.dto.card.CreditCardPageDto;
import com.get.insurancecenter.dto.card.SaveCreditCardReminderDto;
import com.get.insurancecenter.dto.order.MoveOrderDto;
import com.get.insurancecenter.entity.CreditCard;
import com.get.insurancecenter.vo.card.CreateCardPageVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
public interface CreditCardService extends IService<CreditCard> {

    /**
     * 保存信用卡信息
     *
     * @param creditCard
     * @return
     */
    Long saveCreditCard(CreditCard creditCard);

    /**
     * 获取信用卡信息-基本信息
     *
     * @param id
     * @return
     */
    CreditCard getCreditCardBaseInfo(Long id);

    /**
     * 获取信用卡信息-分页
     *
     * @param params
     * @param page
     * @return
     */
    List<CreateCardPageVo> creditCardPage(CreditCardPageDto params, Page page);

    /**
     * 拖拽排序
     *
     * @param orderDto
     */
    void movingOrder(MoveOrderDto orderDto);

}
