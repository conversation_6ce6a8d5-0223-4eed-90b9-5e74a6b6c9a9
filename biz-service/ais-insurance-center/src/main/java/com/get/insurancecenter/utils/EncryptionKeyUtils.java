package com.get.insurancecenter.utils;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.StringUtils;
import com.get.insurancecenter.config.EncryptionConfig;
import com.get.insurancecenter.vo.encryption.EncryptionResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Component;


/**
 * @Author:Oliver
 * @Date: 2025/8/6
 * @Version 1.0
 * @apiNote:加密密钥工具类
 */
@Component
@Slf4j
public class EncryptionKeyUtils {

    @Autowired
    private EncryptionConfig encryptionConfig;

    /**
     * 获取密钥（缓存 2 小时，Spring 自动管理）
     */
    @Cacheable(value = "secretCache", key = "'secret'")
    public String getSecret() {
        log.info("【密钥】缓存未命中，开始请求远程密钥...");
        return fetchSecretFromRemote();
    }

    /**
     * 主动刷新密钥缓存（使用 CacheManager 清除缓存 + 强制更新）
     */
    @CacheEvict(value = "secretCache", key = "'secret'")
    public String refreshSecret() {
        log.info("【密钥】主动刷新缓存，清除原缓存...");
        return getSecret(); // 清除后重新走 @Cacheable 流程
    }

    /**
     * 实际从远程服务拉取密钥
     */
    private String fetchSecretFromRemote() {
        log.info("请求远程获取密钥，host:{}", encryptionConfig.getHost());
        String resultStr = HttpUtil.get(encryptionConfig.getHost());
        log.info("获取密钥结果:{}", resultStr);

        EncryptionResult encryptionResult = JSONObject.parseObject(resultStr, EncryptionResult.class);
        if (!Boolean.TRUE.equals(encryptionResult.getSuccess())) {
            log.error("远程密钥获取失败: {}", resultStr);
            throw new RuntimeException("远程密钥获取失败");
        }
        if (StringUtils.isBlank(encryptionResult.getKey())) {
            log.error("密钥内容为空: {}", resultStr);
            throw new RuntimeException("密钥内容为空");
        }
        return phaseKey(encryptionResult);
    }

    /**
     * 解析密钥 token，提取 key 字段
     */
    private String phaseKey(EncryptionResult encryptionResult) {
//        Key secretKey = EncoderUtil.changeKey(encryptionConfig.getSecret());
//        Claims claims = EncoderUtil.phaseTokenGetBody(encryptionResult.getKey(), secretKey);
//        Object keyObj = claims.get("key");
//        if (Objects.isNull(keyObj)) {
//            log.error("Token 中不包含 key 字段");
//            throw new IllegalArgumentException("Token 中不包含 key 字段");
//        }
//        return keyObj.toString();
        return null;
    }
}
