package com.get.insurancecenter.mapper;

import com.get.insurancecenter.dto.file.AppFileCenter;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author:Oliver
 * @Date: 2025/6/19
 * @Version 1.0
 * @apiNote:
 */
@Mapper
public interface AppFileCenterMapper {

    /**
     * 根据文件guid查询文件信息
     *
     * @param fileGuid
     * @return
     */
    AppFileCenter selectAppFileByGuid(@Param("fileGuid") String fileGuid);

    /**
     * 插入文件信息
     * @param appFile
     * @param loginId
     */
    void insertAppFile(@Param("appFile") AppFileCenter appFile, @Param("loginId") String loginId);

}
