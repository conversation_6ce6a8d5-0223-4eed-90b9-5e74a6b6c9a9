package com.get.insurancecenter.utils;


import com.alibaba.nacos.common.utils.StringUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.UUID;

/**
 * @DATE: 2021/3/3
 * @TIME: 12:43
 * @Description: 文件工具类
 **/
@Slf4j
public class FileUtils {

    public static String FILE_ROOT_PATH = "/insurance";

    public static String getFilePath(MultipartFile file) {
        String fileFileName = file.getOriginalFilename();
        String dateString = getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        return FILE_ROOT_PATH + dateString + fileName;
    }

    private static String getOSName() {
        Properties props = System.getProperties(); //获得系统属性集
        String osName = props.getProperty("os.name");
        return osName;
    }


    private static String getDateString() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("/yyyy/MM/dd/");
        Date date = new Date();
        String dateString = simpleDateFormat.format(date);
        return dateString;
    }

    public static String newFileName(String oldFileName) {
        // 获取最后一个"."出现的位置下标
        int index = oldFileName.lastIndexOf(".");
        // 截取最后一个"."之前的内容
        String frontStr = oldFileName.substring(0, index);
        // 获取最后一个"."之后的内容
        String behindStr = oldFileName.substring(index);
        // 重新拼接文件的名称
        String newFileName = frontStr + System.currentTimeMillis() + "_"
                + behindStr;
        return newFileName;
    }

    public static void validateFile(MultipartFile file) {
        String filename = file.getOriginalFilename();
        org.springframework.util.Assert.isTrue(StringUtils.isNotBlank(filename), "上传文件不能为空");
        int i = filename.lastIndexOf(".");
        //获取后缀名
        String substring = filename.substring(i, filename.length()).toLowerCase();
        //判断是否为规定可上传的文件
        String regexFile = "^(.doc|.pdf|.txt|.docx|.xlsx|.xls|.ppt|.apk|.jpg|.ico|.gif|.png|.jpeg|.bmp|.mp3|.mp4|.avi|.flv|.rmvb|.eml|.html|.msg|.htm|.odt|.wps|.mhtml|.zip|.rar|.asf|.wmv)$";
        String regexImage = "^(.bmp|.jpg|.pptx|.png|.tif|.gif|.pcx|.tga|.exif|.fpx|.svg|.psd|.cdr|.pcd|.dxf|.ufo|.eps|.ai|.raw|.WMF|.webp)$";
        if (!substring.matches(regexFile) && !substring.matches(regexImage)) {
//            throw new GetServiceException("不支持上传此类型文件");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_FILE_TYPE_UNSUPPORTED", "不支持上传此类型文件"));
        }
    }

}
