package com.get.insurancecenter.config;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.nacos.api.naming.NamingService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class NacosConfig {

    /**
     * 显式配置NamingService Bean
     */
    @Bean
    public NamingService namingService(NacosDiscoveryProperties discoveryProperties) {
        return discoveryProperties.namingServiceInstance();
    }
}