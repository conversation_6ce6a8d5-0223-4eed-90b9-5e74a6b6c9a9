package com.get.insurancecenter.controller;

import com.get.common.consts.AESConstant;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.AESUtils;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.utils.SecureUtil;
import com.get.insurancecenter.dto.card.*;
import com.get.insurancecenter.dto.order.CreditCardOrderListDto;
import com.get.insurancecenter.dto.order.MoveOrderDto;
import com.get.insurancecenter.entity.CreditCard;
import com.get.insurancecenter.entity.CreditCardReminder;
import com.get.insurancecenter.enums.RelationTargetEnum;
import com.get.insurancecenter.mapper.CreditCardMapper;
import com.get.insurancecenter.service.CreditCardReminderService;
import com.get.insurancecenter.service.CreditCardService;
import com.get.insurancecenter.service.CreditCardStatementService;
import com.get.insurancecenter.service.InsuranceOrderService;
import com.get.insurancecenter.vo.card.BankVo;
import com.get.insurancecenter.vo.card.CreateCardPageVo;
import com.get.insurancecenter.vo.card.TradeRecordVo;
import com.get.insurancecenter.vo.order.CreditCardOrderVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @DATE: 2020/7/14
 * @TIME: 18:17
 * @Description: 订单管理控制层
 **/

@Api(tags = "信用卡管理")
@RestController
@RequestMapping("/creditCard")
public class CreditCardController {

    @Autowired
    private CreditCardService creditCardService;
    @Autowired
    private CreditCardMapper creditCardMapper;
    @Autowired
    private CreditCardReminderService creditCardReminderService;
    @Autowired
    private CreditCardStatementService cardStatementService;
    @Autowired
    private InsuranceOrderService orderService;

    @ApiOperation(value = "信用卡列表")
    @PostMapping("/page")
    @OperationLogger(module = LoggerModulesConsts.INSURANCECENTER, type = LoggerOptTypeConst.LIST, description = "信用卡管理/信用卡列表")
    public ResponseBo<CreateCardPageVo> page(@RequestBody SearchBean<CreditCardPageDto> page) {
        List<CreateCardPageVo> list = creditCardService.creditCardPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "银行列表", notes = "银行列表")
    @GetMapping("/bankList")
    public ResponseBo<List<BankVo>> bankList() {
        return new ResponseBo<>(creditCardMapper.selectBankList());
    }

    @ApiOperation(value = "信用卡基本信息-编辑回显用", notes = "信用卡基本信息-卡号和安全码不会已明文的形式回显,前端回显需要通过base64解密" +
            "base64CardNum和base64SafetyCode")
    @GetMapping("/getCreditCardBaseInfo/{id}")
    public ResponseBo<CreditCard> getCreditCardBaseInfo(@PathVariable("id") Long id) {
        return new ResponseBo<>(creditCardService.getCreditCardBaseInfo(id));
    }

    @ApiOperation(value = "新增编辑信用卡", notes = "新增编辑信用卡-新增或编辑时,卡号和安全码不要通过明文传输," +
            "要使用base64加密后再传输,对应的参数分别是base64CardNum和base64SafetyCode")
    @PostMapping("/saveCreditCard")
    public ResponseBo<String> saveCreditCard(@RequestBody @Valid CreditCard creditCard) {
        creditCardService.saveCreditCard(creditCard);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "拖拽排序", notes = "拖拽排序-传的是viewOrder,start:排序靠前的viewOrder,end:排序靠后的viewOrder")
    @PostMapping("/movingOrder")
    public ResponseBo<String> movingOrder(@RequestBody @Valid MoveOrderDto moveOrderDto) {
        creditCardService.movingOrder(moveOrderDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "批量保存信用卡通知配置")
    @PostMapping("/batchSaveReminder")
    public ResponseBo<String> batchSaveReminder(@RequestBody @Valid BatchSaveReminderDto params) {
        creditCardReminderService.batchSaveReminder(params);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "编辑信用卡通知配置")
    @PostMapping("/saveReminder")
    public ResponseBo<String> saveReminder(@RequestBody SaveCreditCardReminderDto param) {
        creditCardReminderService.saveReminder(param);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "获取信用卡通知配置信息")
    @GetMapping("/getCreditCardReminder/{creditCardId}")
    public ResponseBo<CreditCardReminder> getCreditCardReminder(@PathVariable("creditCardId") Long creditCardId) {
        return new ResponseBo<>(creditCardReminderService.getCreditCardReminder(creditCardId));
    }

    @ApiOperation(value = "Base64加密解密", notes = "type:1加密2解密")
    @PostMapping("/encrypt")
    @SneakyThrows
    public ResponseBo<String> encrypt(@RequestBody EncryptParam param) {
        if (param.getType().equals(1)) {
            //加密
            return new ResponseBo<>(AESUtils.Encrypt(param.getStr(), AESConstant.AESKEY));
        }
        return new ResponseBo<>(AESUtils.Decrypt(param.getStr(), AESConstant.AESKEY));
    }

    @ApiOperation(value = "信用卡交易记录")
    @PostMapping("/tradeRecordPage")
    public ResponseBo<TradeRecordVo> tradeRecordPage(@RequestBody @Valid SearchBean<TradeRecordDto> page) {
        List<TradeRecordVo> list = cardStatementService.tradeRecordPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "新增信用卡交易记录", notes = "新增信用卡交易记录")
    @PostMapping("/saveStatement")
    public ResponseBo<String> saveStatement(@RequestBody @Valid SaveStatementDto saveStatementDto) {
        cardStatementService.saveStatement(saveStatementDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "编辑交易记录实付金额", notes = "编辑交易记录实付金额")
    @PostMapping("/saveStatementActualPay")
    public ResponseBo<String> saveStatementActualPay(@RequestBody @Valid SaveStatementActualPayDto actualPayDto) {
        cardStatementService.saveStatementActualPay(actualPayDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "交易记录业务类型列表")
    @GetMapping("/relationTargetTypeList")
    public ResponseBo<Map<String, String>> relationTargetTypeList() {
        return new ResponseBo<>(Arrays.stream(RelationTargetEnum.values())
                .collect(Collectors.toMap(RelationTargetEnum::getCode, RelationTargetEnum::getMsg)));
    }

    @ApiOperation(value = "信用卡关联的订单列表")
    @PostMapping("/creditCardOrderList")
    public ResponseBo<CreditCardOrderVo> creditCardOrderList(@RequestBody @Valid SearchBean<CreditCardOrderListDto> page) {
        List<CreditCardOrderVo> list = orderService.creditCardOrderList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }
}
