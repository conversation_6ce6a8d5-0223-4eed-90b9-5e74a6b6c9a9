package com.get.insurancecenter.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.get.insurancecenter.vo.agent.AgentEntity;
import com.get.permissioncenter.vo.CompanyVo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Author:Oliver
 * @Date: 2025/5/19
 * @Version 1.0
 * @apiNote:
 */
@DS("partner")
@Mapper
public interface PartnerCenterMapper {

    /**
     * 根据用户ID查询用户名称
     *
     * @param partnerUserIds
     * @return
     */
    @MapKey("id")
    Map<Long, String> selectPartnerUserNameByIds(@Param("partnerUserIds") List<Long> partnerUserIds);

    /**
     * 根据代理ID查询代理信息
     *
     * @param agentIds
     * @return
     */
    List<AgentEntity> selectAgentByIds(@Param("agentIds") List<Long> agentIds);

    /**
     * 根据公司ID查询公司信息
     *
     * @param companyIds
     * @return
     */
    List<CompanyVo> selectCompanyByIds(@Param("companyIds") List<Long> companyIds);


    /**
     * 获取汇率
     *
     * @param from
     * @param to
     * @return
     */
    BigDecimal getExchangeRate(@Param("from") String from, @Param("to") String to);

    /**
     * 添加汇率
     *
     * @param from
     * @param to
     * @param rate
     */
    void insertExchangeRate(@Param("from") String from,
                            @Param("to") String to,
                            @Param("rate") BigDecimal rate);

}
