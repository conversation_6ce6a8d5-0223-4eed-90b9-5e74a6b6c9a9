package com.get.insurancecenter.service;

import com.get.common.result.Page;
import com.get.insurancecenter.dto.SettlementOrderQueryDto;
import com.get.insurancecenter.dto.commission.AgentDto;
import com.get.insurancecenter.dto.commission.BindPayFormDto;
import com.get.insurancecenter.vo.SettlementOrderVo;
import com.get.insurancecenter.vo.commission.AgentVo;
import com.get.insurancecenter.vo.commission.SettledOrderVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/27
 * @Version 1.0
 * @apiNote:佣金结算service
 */
public interface SettlementService {

    /**
     * 代理列表
     *
     * @param params
     * @param page
     * @return
     */
    List<AgentVo> agentList(AgentDto params, Page page, Integer type);

    /**
     * 批量确认
     *
     * @param orderIdList
     */
    void batchConfirm(List<Long> orderIdList);


    /**
     * 确认结算订单列表/已结算订单列表
     *
     * @param params
     * @param page
     * @return
     */
    List<SettledOrderVo> getSettledOrderList(AgentDto params, Page page);


    /**
     * 批量确认结算
     *
     * @param numOptBatchList
     */
    void confirmSettlement(List<String> numOptBatchList);

    /**
     * 批量绑定付款单
     *
     * @param bindPayFormDto
     */
    void bindPayForm(BindPayFormDto bindPayFormDto);


    /**
     * 查询订单结算记录列表
     */
    List<SettlementOrderVo> getSettlementOrderList(SettlementOrderQueryDto queryDto, Page page);
}
