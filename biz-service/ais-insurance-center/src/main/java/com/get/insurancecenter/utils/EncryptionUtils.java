//package com.get.insurancecenter.utils;
//
//import cn.hutool.http.HttpUtil;
//import com.alibaba.fastjson.JSONObject;
//import com.alibaba.nacos.common.utils.StringUtils;
//import com.common.core.util.EncoderUtil;
//import com.get.insurancecenter.config.EncryptionConfig;
//import com.get.insurancecenter.vo.encryption.EncryptionResult;
//import io.jsonwebtoken.Claims;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//
//import java.security.Key;
//import java.util.Objects;
//
///**
// * @Author:Oliver
// * @Date: 2025/7/31
// * @Version 1.0
// * @apiNote:信用卡密钥工具类
// */
//@Component
//@Slf4j
//public class EncryptionUtils {
//
//    @Autowired
//    private EncryptionConfig encryptionConfig;
//
//
//    public String getSecret() {
//        String resultStr = HttpUtil.get(encryptionConfig.getHost());
//        log.info("获取密钥结果:{}", resultStr);
//        EncryptionResult encryptionResult = JSONObject.parseObject(resultStr, EncryptionResult.class);
//        if (!encryptionResult.getSuccess()) {
//            log.error("获取密钥失败:{}", JSONObject.toJSONString(encryptionResult));
//            throw new RuntimeException("获取密钥失败");
//        }
//        if (StringUtils.isBlank(encryptionResult.getKey())) {
//            log.error("获取密钥失败:{}", JSONObject.toJSONString(encryptionResult));
//            throw new RuntimeException("获取密钥失败");
//        }
//        return phaseKey(encryptionResult);
//    }
//
//    public String phaseKey(EncryptionResult encryptionResult) {
//        Key secretKey = EncoderUtil.changeKey(encryptionConfig.getSecret());
//        Claims claims = EncoderUtil.phaseTokenGetBody(encryptionResult.getKey(), secretKey);
//        Object keyObj = claims.get("key");
//        if (Objects.isNull(keyObj)) {
//            log.error("获取密钥失败,token中不包含key信息");
//            throw new IllegalArgumentException("token中不包含key信息");
//        }
//        return keyObj.toString();
//    }
//
//}
