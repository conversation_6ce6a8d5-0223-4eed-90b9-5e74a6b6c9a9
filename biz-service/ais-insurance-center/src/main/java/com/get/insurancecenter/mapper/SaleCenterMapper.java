package com.get.insurancecenter.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.insurancecenter.dto.commission.AgentDto;
import com.get.insurancecenter.vo.agent.AgentCompanyEntity;
import com.get.insurancecenter.vo.commission.AgentAccountVo;
import com.get.insurancecenter.vo.commission.AgentVo;
import com.get.insurancecenter.vo.commission.PayablePlanVo;
import com.get.permissioncenter.vo.CompanyVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/5/27
 * @Version 1.0
 * @apiNote:
 */
@Mapper
public interface SaleCenterMapper {

    /**
     * 查询代理列表
     *
     * @param agentIds
     * @param param
     * @param type
     * @return
     */
    List<AgentVo> selectAgentList(IPage<AgentVo> page,
                                  @Param("agentIds") List<Long> agentIds,
                                  @Param("param") AgentDto param,
                                  @Param("type") Integer type);

    /**
     * 查询代理公司列表
     *
     * @param agentIds
     * @return
     */
    List<AgentCompanyEntity> selectAgentCompanyList(@Param("agentIds") List<Long> agentIds);


    /**
     * 查询代理所属公司列表
     *
     * @param companyIds
     * @return
     */
    List<CompanyVo> selectCompanyList(@Param("companyIds") List<Long> companyIds);

    /**
     * 获取应付计划列表
     * @param payablePlanIds
     * @return
     */
    List<PayablePlanVo> getPayablePlanList(List<Long> payablePlanIds);

    /**
     * 获取代理账户列表
     * @param accountIds
     * @return
     */
    List<AgentAccountVo> getAgentAccountList(List<Long> accountIds);

    /**
     * 获取代理商账户详情
     * @param id
     * @return
     */
    AgentAccountVo getAgentAccount(@Param("id") Long id);


    /**
     * 获取代理商列表
     * @param agentIds
     * @return
     */
    List<AgentVo> selectAgentListByIds(@Param("agentIds") List<Long> agentIds);


}
