package com.get.insurancecenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.secure.utils.SecureUtil;
import com.get.insurancecenter.dto.commission.AgentDto;
import com.get.insurancecenter.dto.commission.BindPayFormDto;
import com.get.insurancecenter.dto.SettlementOrderQueryDto;
import com.get.insurancecenter.service.SettlementBillService;
import com.get.insurancecenter.service.SettlementService;
import com.get.insurancecenter.vo.commission.AgentVo;
import com.get.insurancecenter.vo.commission.SettledOrderVo;
import com.get.insurancecenter.vo.SettlementOrderVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Objects;

/**
 * @DATE: 2020/7/14
 * @TIME: 18:17
 * @Description: 订单管理控制层
 **/

@Api(tags = "佣金结算管理")
@RestController
@RequestMapping("/settlement")
public class SettlementController {

    @Autowired
    private SettlementService settlementService;
    @Autowired
    private SettlementBillService settlementBillService;

    @ApiOperation(value = "待确认/待提交结算订单列表")
    @PostMapping("/agentList")
    public ResponseBo<AgentVo> orderList(@RequestBody SearchBean<AgentDto> page) {
        List<AgentVo> list = settlementService.agentList(page.getData(), page, Objects.isNull(page.getData().getType()) ? 0 : page.getData().getType());
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "批量确认-待确认列表")
    @PostMapping("/batchConfirm")
    public ResponseBo<String> batchConfirm(@RequestBody List<Long> orderIdList) {
        settlementService.batchConfirm(orderIdList);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "确认结算订单/已结算订单列表", notes = "type:2确认结算订单列表/3已结算订单列表")
    @PostMapping("/getSettledOrderList")
    public ResponseBo<SettledOrderVo> getSettledOrderList(@RequestBody SearchBean<AgentDto> page) {
        List<SettledOrderVo> list = settlementService.getSettledOrderList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

    @ApiOperation(value = "对账单下载")
    @GetMapping("/downloadSettlementBill/{id}")
    public void getSettlementBillDetail(@PathVariable("id") Long id, HttpServletResponse response) {
        settlementBillService.downloadSettlementBill(id, response);
    }

    @ApiOperation(value = "批量确认结算-确认结算订单列表")
    @PostMapping("/confirmSettlement")
    public ResponseBo<String> confirmSettlement(@RequestBody List<String> numOptBatchList) {
        settlementService.confirmSettlement(numOptBatchList);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "批量绑定付款单-已结算订单列表")
    @PostMapping("/bindPayForm")
    public ResponseBo<String> bindPayForm(@RequestBody BindPayFormDto bindPayFormDto) {
        settlementService.bindPayForm(bindPayFormDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    /**
     * 查询订单结算记录列表
     *
     */
    @ApiOperation(value = "查询订单结算记录列表")
    @PostMapping("/getSettlementOrderList")
    public ResponseBo<SettlementOrderVo> getSettlementOrderList(@RequestBody SearchBean<SettlementOrderQueryDto> page) {
        List<SettlementOrderVo> list = settlementService.getSettlementOrderList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

}
