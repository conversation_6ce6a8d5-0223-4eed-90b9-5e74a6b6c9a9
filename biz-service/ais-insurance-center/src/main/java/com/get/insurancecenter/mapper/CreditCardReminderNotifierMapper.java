package com.get.insurancecenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.insurancecenter.entity.CreditCardReminderNotifier;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CreditCardReminderNotifierMapper extends BaseMapper<CreditCardReminderNotifier> {

    /**
     * 根据信用卡id查询提醒信息
     *
     * @param creditCardIds 信用卡id
     * @return 信用卡提醒信息
     */
    List<CreditCardReminderNotifier> selectNotifierByCreditCardIds(@Param("creditCardIds") List<Long> creditCardIds);
}
