package com.get.insurancecenter.config;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author:Oliver
 * @Date: 2025/5/7
 * @Version 1.0
 * @apiNote:获取信用卡密钥配置类
 */
@Configuration
@ConfigurationProperties(prefix = "encryption")
@Data
public class EncryptionConfig {

    @ApiModelProperty(value = "请求地址")
    private String host = "http://************:1688/api/keys/getKey/insurance:key";

    @ApiModelProperty(value = "secret")
    private String secret = "M6seZQwCKCHDsA7PD2LlJqIpsdK2VRBYYqKA96UtnMX6S7pkXLFam6Ql2o1dlzs5";
}
