package com.get.insurancecenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import com.get.insurancecenter.dto.card.BatchSaveReminderDto;
import com.get.insurancecenter.dto.card.SaveCreditCardReminderDto;
import com.get.insurancecenter.entity.CreditCardReminder;
import com.get.insurancecenter.entity.CreditCardReminderNotifier;
import com.get.insurancecenter.mapper.CreditCardReminderMapper;
import com.get.insurancecenter.service.CreditCardReminderNotifierService;
import com.get.insurancecenter.service.CreditCardReminderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class CreditCardReminderServiceImpl extends ServiceImpl<CreditCardReminderMapper, CreditCardReminder> implements CreditCardReminderService {

    @Autowired
    private CreditCardReminderMapper creditCardReminderMapper;
    @Autowired
    private CreditCardReminderNotifierService reminderNotifierService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSaveReminder(BatchSaveReminderDto params) {
        //先删除原本的
        List<Long> creditCardIds = params.getCreditCardIds();
        List<CreditCardReminder> creditCardReminders = creditCardReminderMapper.selectList(new LambdaQueryWrapper<CreditCardReminder>().in(CreditCardReminder::getFkCreditCardId, creditCardIds));
        if (CollectionUtils.isNotEmpty(creditCardReminders)) {
            List<Long> remindersIds = creditCardReminders.stream().map(CreditCardReminder::getId).collect(Collectors.toList());
            reminderNotifierService.remove(new LambdaQueryWrapper<CreditCardReminderNotifier>()
                    .in(CreditCardReminderNotifier::getFkCreditCardReminderId, remindersIds));
            creditCardReminderMapper.deleteBatchIds(remindersIds);
        }
        //新增
        List<CreditCardReminderNotifier> creditCardReminderNotifiers = new ArrayList<>();
        for (Long creditCardId : creditCardIds) {
            SaveCreditCardReminderDto param = params.getSaveCreditCardReminderDto();
            CreditCardReminder creditCardReminder = new CreditCardReminder();
            BeanUtils.copyProperties(param, creditCardReminder);
            creditCardReminder.setFkCreditCardId(creditCardId);
            creditCardReminder.setGmtCreate(new Date());
            creditCardReminder.setGmtModified(new Date());
            creditCardReminder.setGmtCreateUser(SecureUtil.getLoginId());
            creditCardReminder.setGmtModifiedUser(SecureUtil.getLoginId());
            creditCardReminderMapper.insert(creditCardReminder);
            if (CollectionUtils.isNotEmpty(param.getReminderNotifierInfoList())) {
                List<CreditCardReminderNotifier> cardReminderNotifiers = param.getReminderNotifierInfoList().stream().map(item -> {
                    CreditCardReminderNotifier creditCardReminderNotifier = new CreditCardReminderNotifier();
                    BeanUtils.copyProperties(item, creditCardReminderNotifier);
                    creditCardReminderNotifier.setFkCreditCardReminderId(creditCardReminder.getId());
                    creditCardReminderNotifier.setGmtCreate(new Date());
                    creditCardReminderNotifier.setGmtModified(new Date());
                    creditCardReminderNotifier.setGmtCreateUser(SecureUtil.getLoginId());
                    creditCardReminderNotifier.setGmtModifiedUser(SecureUtil.getLoginId());
                    return creditCardReminderNotifier;
                }).collect(Collectors.toList());
                creditCardReminderNotifiers.addAll(cardReminderNotifiers);
            }
        }
        if (CollectionUtils.isNotEmpty(creditCardReminderNotifiers)) {
            reminderNotifierService.saveBatch(creditCardReminderNotifiers);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveReminder(SaveCreditCardReminderDto params) {
        CreditCardReminder creditCardReminder;
        //新增
        if (Objects.isNull(params.getId()) || params.getId() < 1) {
            creditCardReminder = new CreditCardReminder();
            BeanUtils.copyProperties(params, creditCardReminder);
            creditCardReminder.setGmtCreate(new Date());
            creditCardReminder.setGmtCreateUser(SecureUtil.getLoginId());
            creditCardReminder.setGmtModified(new Date());
            creditCardReminder.setGmtModifiedUser(SecureUtil.getLoginId());
            creditCardReminderMapper.insert(creditCardReminder);
        } else {
            creditCardReminder = creditCardReminderMapper.selectById(params.getId());
            if (Objects.isNull(creditCardReminder) || Objects.isNull(creditCardReminder.getId())) {
                log.error("保存信用卡提醒失败，该信用卡提醒不存在:{}", JSONObject.toJSONString(params));
//                throw new GetServiceException("该信用卡提醒不存在");
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_CARD_REMINDER_NOT_EXIST", "该信用卡提醒不存在"));
            }
            if (CollectionUtils.isEmpty(params.getReminderNotifierInfoList())) {
                log.error("保存信用卡提醒通知人信息为空:{}", JSONObject.toJSONString(params));
//                throw new GetServiceException("信用卡提醒通知人信息不能为空");
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "INSURANCE_CARD_REMINDER_NOTIFY_NULL", "信用卡提醒通知人信息不能为空"));
            }
            BeanUtils.copyProperties(params, creditCardReminder);
            creditCardReminder.setGmtModified(new Date());
            creditCardReminder.setGmtModifiedUser(SecureUtil.getLoginId());
            creditCardReminderMapper.updateById(creditCardReminder);
        }
        //保存通知人信息
        List<SaveCreditCardReminderDto.ReminderNotifierInfo> reminderNotifierInfoList = params.getReminderNotifierInfoList();
        //新增的
        List<CreditCardReminderNotifier> creditCardReminderNotifiers = reminderNotifierInfoList.stream()
                .filter(item -> Objects.isNull(item.getId()) || item.getId() < 1)
                .map(item -> {
                    CreditCardReminderNotifier creditCardReminderNotifier = new CreditCardReminderNotifier();
                    BeanUtils.copyProperties(item, creditCardReminderNotifier);
                    creditCardReminderNotifier.setFkCreditCardReminderId(creditCardReminder.getId());
                    creditCardReminderNotifier.setGmtCreate(new Date());
                    creditCardReminderNotifier.setGmtModified(new Date());
                    creditCardReminderNotifier.setGmtCreateUser(SecureUtil.getLoginId());
                    creditCardReminderNotifier.setGmtModifiedUser(SecureUtil.getLoginId());
                    return creditCardReminderNotifier;
                }).collect(Collectors.toList());

        //编辑的
        List<CreditCardReminderNotifier> updateNotifiers = reminderNotifierInfoList.stream()
                .filter(item -> Objects.nonNull(item.getId()) && item.getId() > 0)
                .map(item -> {
                    CreditCardReminderNotifier creditCardReminderNotifier = new CreditCardReminderNotifier();
                    BeanUtils.copyProperties(item, creditCardReminderNotifier);
                    creditCardReminderNotifier.setFkCreditCardReminderId(creditCardReminder.getId());
                    creditCardReminderNotifier.setGmtModified(new Date());
                    creditCardReminderNotifier.setGmtModifiedUser(SecureUtil.getLoginId());
                    return creditCardReminderNotifier;
                }).collect(Collectors.toList());

        //删除的
        List<Long> existNotifierIds = reminderNotifierInfoList.stream()
                .filter(item -> Objects.nonNull(item.getId()) && item.getId() > 0)
                .map(item -> item.getId())
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(existNotifierIds)) {
            reminderNotifierService.remove(new LambdaQueryWrapper<CreditCardReminderNotifier>()
                    .eq(CreditCardReminderNotifier::getFkCreditCardReminderId, creditCardReminder.getId())
                    .notIn(CreditCardReminderNotifier::getId, existNotifierIds));
        }
        if (CollectionUtils.isNotEmpty(creditCardReminderNotifiers)) {
            reminderNotifierService.saveBatch(creditCardReminderNotifiers);
        }
        if (CollectionUtils.isNotEmpty(updateNotifiers)) {
            reminderNotifierService.updateBatchById(updateNotifiers);
        }
    }

    @Override
    public CreditCardReminder getCreditCardReminder(Long creditCardId) {
        CreditCardReminder reminder = creditCardReminderMapper.selectList(new LambdaQueryWrapper<CreditCardReminder>()
                        .eq(CreditCardReminder::getFkCreditCardId, creditCardId)
                        .orderByDesc(CreditCardReminder::getId))
                .stream()
                .findFirst()
                .orElse(null);
        if (Objects.nonNull(reminder)) {
            List<CreditCardReminderNotifier> reminderNotifierList = reminderNotifierService.list(new LambdaQueryWrapper<CreditCardReminderNotifier>()
                    .eq(CreditCardReminderNotifier::getFkCreditCardReminderId, reminder.getId()));
            reminder.setCreditCardReminderNotifierList(reminderNotifierList);
            return reminder;
        }
        return null;
    }
}
