package com.get.insurancecenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.insurancecenter.entity.SettlementBill;
import com.get.insurancecenter.vo.commission.SettlementBillDetailVo;

import javax.servlet.http.HttpServletResponse;

/**
 * @Author:Oliver
 * @Date: 2025/6/20
 * @Version 1.0
 * @apiNote:
 */
public interface SettlementBillService extends IService<SettlementBill> {

    /**
     * 对账单详情
     *
     * @param id
     * @return
     */
    SettlementBillDetailVo getSettlementBillDetail(Long id);

    /**
     * 对账单下载
     * @param id
     * @param response
     */
    void downloadSettlementBill(Long id, HttpServletResponse response);
}
