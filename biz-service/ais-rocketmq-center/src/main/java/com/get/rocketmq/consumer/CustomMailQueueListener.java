package com.get.rocketmq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.rocketmqcenter.dto.EmailCustomMQMessageDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 自定义邮件队列监听
 */
@Component
@Slf4j
//@RocketMQMessageListener(
//        consumeThreadMax = 10,
//        topic = "mail_custom_queue_topic", // topic
//        consumerGroup = "mail_system_queue_topic_consumer_group_dev", // 消费组
//        maxReconsumeTimes = 3, //最大重试次数
//        consumeMode = ConsumeMode.ORDERLY
//)
public class CustomMailQueueListener implements RocketMQListener<EmailCustomMQMessageDto> {

    @Resource
    private IReminderCenterClient reminderCenterClient;


    @Override
    public void onMessage(EmailCustomMQMessageDto emailCustomMQMessageDto) {
        try {
            Result result = reminderCenterClient.sendCustomMail(emailCustomMQMessageDto);
            if (!result.isSuccess()) {
                throw new RuntimeException(result.getMessage());
            }
        } catch (Exception e) {
            log.error("自定义邮件发送失败，emailCustomMQMessage={}", JSONObject.toJSONString(emailCustomMQMessageDto));
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }

}
