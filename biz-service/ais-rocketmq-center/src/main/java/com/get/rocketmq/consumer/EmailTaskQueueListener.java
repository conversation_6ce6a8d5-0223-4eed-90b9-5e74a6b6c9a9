package com.get.rocketmq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.get.core.tool.api.Result;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.feign.IReminderCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 邮件任务发送MQ监听
 */
@Component
@Slf4j
@RocketMQMessageListener(
        consumeThreadMax = 10,
        topic = "mail_task_queue_topic", // topic
        consumerGroup = "mail_system_queue_topic_group", // 消费组
        maxReconsumeTimes = 3, //最大重试次数
        consumeMode = ConsumeMode.ORDERLY
)
public class EmailTaskQueueListener implements RocketMQListener<EmailSenderQueue> {


    @Resource
    private IReminderCenterClient reminderCenterClient;

    /**
     * 根据邮件任务执行发邮件
     *
     * @param emailSenderQueue
     */
    @Override
    public void onMessage(EmailSenderQueue emailSenderQueue) {
        System.out.println("---------邮箱队列id----------------" + emailSenderQueue.getId());
        try {
            Result result = reminderCenterClient.sendMqEmailTask(emailSenderQueue);
            if (!result.isSuccess()) {
                throw new RuntimeException(result.getMessage());
            }
        } catch (Exception e) {
            log.error("邮件任务发送MQ发送失败，emailCustomMQMessage={}", JSONObject.toJSONString(emailSenderQueue));
            e.printStackTrace();
            throw new RuntimeException(e);
        }
    }
}
