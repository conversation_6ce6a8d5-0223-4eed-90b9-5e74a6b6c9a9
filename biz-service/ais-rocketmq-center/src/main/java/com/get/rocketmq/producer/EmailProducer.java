package com.get.rocketmq.producer;

import com.get.core.secure.annotation.VerifyPermission;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.rocketmqcenter.dto.EmailCustomMQMessageDto;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.rocketmqcenter.enums.TopicEnum;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 邮件发送生产者
 */
@Slf4j
@Component
@Data
public class EmailProducer {

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    @Resource
    private IReminderCenterClient reminderCenterClient;


    /**
     * 发送系统邮件
     * @param emailSystemMQMessageDto
     */
    public void sendSystemMail(EmailSystemMQMessageDto emailSystemMQMessageDto) {

        rocketMQTemplate.asyncSend(TopicEnum.MAIL_SYSTEM_QUEUE_TOPIC.getTopic(), emailSystemMQMessageDto, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("系统邮件消息发送成功，title={}，消息ID={}", emailSystemMQMessageDto.getTitle(), sendResult.getMsgId());
                EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                emailSenderQueue.setOperationStatus(1);
                emailSenderQueue.setId(emailSystemMQMessageDto.getEmailSenderQueueId());
                reminderCenterClient.updateEmailQueue(emailSenderQueue);
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("系统邮件消息发送失败，title={}，异常信息={}", emailSystemMQMessageDto.getTitle(), throwable.getMessage());
                // 这里可以做失败告警、日志记录等处理
            }
        });
    }

    /**
     * 发送自定义邮件
     * @param emailCustomMQMessageDto
     */
    public void sendCustomMail(EmailCustomMQMessageDto emailCustomMQMessageDto) {
        rocketMQTemplate.asyncSend(TopicEnum.MAIL_CUSTOM_QUEUE_TOPIC.getTopic(), emailCustomMQMessageDto, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("自定义邮件消息发送成功，title={}，消息ID={}", emailCustomMQMessageDto.getTitle(), sendResult.getMsgId());
                EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                emailSenderQueue.setOperationStatus(1);
                emailSenderQueue.setId(emailCustomMQMessageDto.getEmailSenderQueueId());
                reminderCenterClient.updateEmailQueue(emailSenderQueue);
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("自定义邮件消息发送失败，title={}，异常信息={}", emailCustomMQMessageDto.getTitle(), throwable.getMessage());
                // 这里可以做失败告警、日志记录等处理
            }
        });
    }

    /**
     * 发送邮件任务到MQ
     */
    public void sendEMailTaskToMq(EmailSenderQueue emailSenderQueue) {

        try {
            SendResult sendResult = rocketMQTemplate.syncSend(TopicEnum.MAIL_TASK_QUEUE_TOPIC.getTopic(), emailSenderQueue);
            log.info("发送邮件任务到MQ发送成功，邮件队列id={}，消息ID={}", emailSenderQueue.getId(), sendResult.getMsgId());
            emailSenderQueue.setOperationStatus(1);
            reminderCenterClient.updateEmailQueue(emailSenderQueue);
        } catch (Exception e) {
            log.error("发送邮件任务到MQ发送失败，邮件队列id={}，异常信息={}", emailSenderQueue.getId(), e.getMessage());
            emailSenderQueue.setOperationStatus(-1);
            reminderCenterClient.updateEmailQueue(emailSenderQueue);
            // 这里可以做失败告警、日志记录等处理
        }

//        rocketMQTemplate.asyncSend(TopicEnum.MAIL_TASK_QUEUE_TOPIC.getTopic(), emailSenderQueue, new SendCallback() {
//            @Override
//            public void onSuccess(SendResult sendResult) {
//                log.info("发送邮件任务到MQ发送成功，邮件队列id={}，消息ID={}", emailSenderQueue.getId(), sendResult.getMsgId());
//                emailSenderQueue.setOperationStatus(1);
//                reminderCenterClient.updateEmailQueue(emailSenderQueue);
//            }
//
//            @Override
//            public void onException(Throwable throwable) {
//                log.error("发送邮件任务到MQ发送失败，邮件队列id={}，异常信息={}", emailSenderQueue.getId(), throwable.getMessage());
//                emailSenderQueue.setOperationStatus(-1);
//                reminderCenterClient.updateEmailQueue(emailSenderQueue);
//                // 这里可以做失败告警、日志记录等处理
//            }
//        });
    }


}
