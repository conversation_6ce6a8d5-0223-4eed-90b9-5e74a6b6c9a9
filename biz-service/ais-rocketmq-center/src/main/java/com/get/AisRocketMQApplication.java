package com.get;

import com.get.common.constant.AppCenterConstant;
import com.get.core.cloud.feign.EnableGetFeign;
import com.get.core.start.GetApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.context.annotation.ComponentScan;

@EnableGetFeign
@SpringCloudApplication
@MapperScan("com.get.dao")
@ComponentScan(basePackages = {"com.get.rocketmq"})
public class AisRocketMQApplication {
    public static void main(String[] args) {
        GetApplication.run(AppCenterConstant.APPLICATION_ROCKETMQ_CENTER, AisRocketMQApplication.class, args);
    }
}