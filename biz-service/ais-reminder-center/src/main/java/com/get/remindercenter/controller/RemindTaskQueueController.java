package com.get.remindercenter.controller;

import com.get.common.consts.CacheKeyConstants;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.service.EmailSenderQueueService;
import com.get.remindercenter.vo.RemindTaskQueueVo;
import com.get.remindercenter.entity.RemindTaskQueue;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.rocketmqcenter.dto.EmailSenderQueueDto;
import com.get.rocketmqcenter.feign.IRocKetMqCenterClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 12:34
 * Date: 2021/12/1
 * Description:提醒任务队列控制器
 */
@Api(tags = "提醒任务队列控制器")
@RestController
@RequestMapping("reminder/remindTaskQueue")
@Slf4j
public class RemindTaskQueueController {
    @Resource
    private RemindTaskQueueService remindTaskQueueService;

    @Resource
    private EmailSenderQueueService  emailSenderQueueService;

    @Resource
    private IRocKetMqCenterClient rocKetMqCenterClient;

    @Resource
    private GetRedis getRedis;


    /**
     * @Description: 执行任务
     * @Author: Jerry
     * @Date:12:36 2021/12/1
     */
    @VerifyLogin(IsVerify = false)
    @ApiIgnore
    @PostMapping("performTasks")
    public void performTasks() {
        if (getRedis.get(CacheKeyConstants.TASKS_EMAIL_LOCK_KEY) != null) {
            return;
        }
        Boolean flag = getRedis.setNx(CacheKeyConstants.TASKS_EMAIL_LOCK_KEY, 1, 432000L);
        if (flag) {
            try {
                log.info("定时器任务启动------------------");
                List<RemindTaskQueue> remindTaskQueues = remindTaskQueueService.getRemindTaskQueues();
                log.info("<=====================待处理任务数量：" + remindTaskQueues.size() + "=========================>");
                if (GeneralTool.isNotEmpty(remindTaskQueues)) {
                    for (RemindTaskQueue remindTaskQueue : remindTaskQueues) {
                        remindTaskQueueService.performTasks(remindTaskQueue.getFkRemindTaskId(), remindTaskQueue);
                    }
                }
                log.info("定时器任务完成------------------");
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                //释放锁
                getRedis.del(CacheKeyConstants.TASKS_EMAIL_LOCK_KEY);
            }
        }else {
            log.info("已有定时器任务正在执行------------------");
        }

    }


    /**
     * @Description: 发送邮件定时任务
     * @Author: lucky
     * @Date:12:36 2025/5/13
     */
    @VerifyLogin(IsVerify = false)
    @ApiIgnore
    @PostMapping("sendEmailScheduleTask")
    public void sendEmailScheduleTask() {
        long startTime = System.currentTimeMillis();
        if (getRedis.get(CacheKeyConstants.NEW_TASKS_EMAIL_LOCK_KEY) != null) {
            return;
        }
        Boolean flag = getRedis.setNx(CacheKeyConstants.NEW_TASKS_EMAIL_LOCK_KEY, 1, 432000L);
        if (flag) {
            try {
                log.info("新机制--定时器任务启动------------------");
                List<EmailSenderQueue> emailSenderQueues = emailSenderQueueService.findDueEmailTasks();
                log.info("<=====================新机制--待处理任务数量：" + emailSenderQueues.size() + "=========================>");
                if (GeneralTool.isNotEmpty(emailSenderQueues)) {
                    int printCount = Math.min(10, emailSenderQueues.size());
                    log.info("前{}个任务数据: {}", printCount, emailSenderQueues.subList(0, printCount));

                    for (EmailSenderQueue emailSenderQueue : emailSenderQueues) {
                        EmailSenderQueueDto emailSenderQueueDto = new EmailSenderQueueDto();
                        BeanCopyUtils.copyProperties(emailSenderQueue,emailSenderQueueDto);
                        rocKetMqCenterClient.setSendEmailTaskToMq(emailSenderQueueDto);
                    }
                }
                log.info("新机制--定时器任务完成------------------");
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                //释放锁
                getRedis.del(CacheKeyConstants.NEW_TASKS_EMAIL_LOCK_KEY);
                long endTime = System.currentTimeMillis();
                log.info("新机制--定时任务执行总耗时: {} ms", (endTime - startTime));
            }
        }else {
            log.info("新机制--已有定时器任务正在执行------------------");
        }

    }


    @PostConstruct
    public void init() {
        final String lockKey = CacheKeyConstants.NEW_TASKS_EMAIL_LOCK_KEY;
        final String oldLockKey = CacheKeyConstants.TASKS_EMAIL_LOCK_KEY;
        try {
            log.info("开始清理Redis锁，键值：{}", lockKey);
            log.info("开始清理Redis锁，旧机制-键值：{}", oldLockKey);
            // 执行删除操作并获取结果
            Boolean isDeleted = getRedis.del(lockKey);
            Boolean isDeletedOld = getRedis.del(oldLockKey);
            // 处理并记录删除结果
            if (isDeleted == null) {
                log.warn("Redis删除操作返回null，键值：{}", lockKey);
            } else if (isDeleted) {
                log.info("成功删除Redis锁，键值：{}", lockKey);
            } else {
                log.info("Redis锁不存在，键值：{}", lockKey);
            }

            // 处理旧锁键结果
            if (isDeletedOld == null) {
                log.warn("Redis删除操作返回null，旧机制键: {}", oldLockKey);
            } else if (isDeletedOld) {
                log.info("成功删除Redis锁，旧机制键: {}", oldLockKey);
            } else {
                log.info("Redis锁不存在，旧机制键: {}", oldLockKey);
            }

        } catch (Exception e) {
            log.error("清理Redis锁失败，新机制键: {}, 旧机制键: {}. 错误原因: {}",
                    lockKey, oldLockKey, e.getMessage(), e);
            // 可在此添加监控告警逻辑
        }
    }




    @GetMapping("deadlock")
    @VerifyPermission(IsVerify = false)
    public void deadlock() {
        getRedis.del(CacheKeyConstants.TASKS_EMAIL_LOCK_KEY);
    }

    /**
     * @Description: 获取系统内消息
     * @Author: Jerry
     * @Date:15:33 2021/12/2
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取系统内消息", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.LIST, description = "提醒中心/提醒任务队列管理/获取系统内消息")
    @PostMapping("getSystemMessage")
    public ResponseBo<RemindTaskQueueVo> getSystemMessage() {
        return new ListResponseBo<>(remindTaskQueueService.getSystemMessage());
    }


    /**
     * @Description: 删除队列提醒
     * @Author: Jerry
     * @Date:15:46 2021/12/2
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "删除队列提醒", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.REMINDERCENTER, type = LoggerOptTypeConst.DELETE, description = "提醒中心/提醒任务队列管理/删除队列提醒")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        remindTaskQueueService.deleteById(id);
        return DeleteResponseBo.ok();
    }
}
