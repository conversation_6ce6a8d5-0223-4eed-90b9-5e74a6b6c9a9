package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.partnercenter.dto.wechat.LinkAndQrCodeVo;
import com.get.partnercenter.dto.wechat.MinProgramQrCodeDto;
import com.get.partnercenter.feign.IPartnerCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.UniversalEmailDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.EmailHtmlTitleExtractor;
import com.get.remindercenter.utils.EmailLanguageConfigUtils;
import com.get.remindercenter.utils.EmailTemplateEngine;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * 通用邮件发送Helper
 * 支持动态参数和模板的邮件处理
 *
 * <AUTHOR>
 * @date 2025-07-19
 */
@Slf4j
@Component("universalEmailHelper")
public class UniversalEmailHelper extends EmailAbstractHelper {

    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private IPartnerCenterClient partnerCenterClient;

    /**
     * 发送邮件
     *
     * @param emailSenderQueue 邮件发送队列
     */
    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        try {
            log.info("开始处理通用邮件发送，邮件ID: {}, 模板类型: {}", 
                    emailSenderQueue.getId(), emailSenderQueue.getFkEmailTypeKey());

            // 组装基础邮件数据（不处理最终标题）
            UniversalEmailDto emailDto = assembleEmailData(emailSenderQueue);
            
            // 获取原始模板内容（用于标题提取）
            String rawTemplate = getRawEmailTemplate(emailDto);
            
            // 按优先级处理邮件标题（包含变量替换）
            String finalTitle = processTitleByPriority(emailDto.getEmailTitle(), rawTemplate, emailDto.getLanguageCode(), emailDto.getDynamicParameters());
            emailDto.setEmailTitle(finalTitle);
            
            // 处理模板变量替换
            String processedTemplate = processTemplateWithVariables(rawTemplate, emailDto);
            emailDto.setProcessedContent(processedTemplate);
            
            // 更新数据库中的标题
            if (!finalTitle.equals(emailSenderQueue.getEmailTitle())) {
                emailSenderQueue.setEmailTitle(finalTitle);
                emailSenderQueueMapper.updateById(emailSenderQueue);
            }
            
            // 创建邮件消息
            EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
            emailSystemMQMessageDto.setEmailSenderQueueId(emailSenderQueue.getId());
            emailSystemMQMessageDto.setTitle(finalTitle);
            emailSystemMQMessageDto.setContent(processedTemplate);
            emailSystemMQMessageDto.setToEmail(emailDto.getRecipientEmail());
            
            // 发送邮件
            remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
            
            // 更新邮件队列状态
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())
                    .set(EmailSenderQueue::getOperationCount, emailSenderQueue.getOperationCount() + 1);
            emailSenderQueueMapper.update(null, updateWrapper);
            
            log.info("通用邮件发送成功, 邮件ID: {}, 收件人: {}, 最终标题: {}, 模板类型: {}", 
                    emailSenderQueue.getId(), emailDto.getRecipientEmail(), finalTitle, emailSenderQueue.getFkEmailTypeKey());
            
        } catch (Exception e) {
            log.error("通用邮件发送失败, 邮件ID: {}, 模板类型: {}, 错误信息: {}", 
                    emailSenderQueue.getId(), emailSenderQueue.getFkEmailTypeKey(), e.getMessage(), e);
            
            // 更新失败状态
            emailSenderQueue.setErrorMessage(e.getMessage());
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }
    }

    /**
     * 组装基础邮件数据（不处理最终标题）
     *
     * @param emailSenderQueue 邮件发送队列
     * @return 通用邮件数据DTO
     */
    @Override
    public UniversalEmailDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        UniversalEmailDto emailDto = new UniversalEmailDto();
        
        try {
            // 设置基础属性
            emailDto.setId(emailSenderQueue.getId());
            emailDto.setFkEmailTypeKey(emailSenderQueue.getFkEmailTypeKey());
            emailDto.setEmailTitle(emailSenderQueue.getEmailTitle()); // 保留原始标题，不做处理
            emailDto.setEmailParameter(emailSenderQueue.getEmailParameter());
            emailDto.setFkDbName(emailSenderQueue.getFkDbName());
            emailDto.setFkTableName(emailSenderQueue.getFkTableName());
            emailDto.setFkTableId(emailSenderQueue.getFkTableId());
            emailDto.setOperationTime(emailSenderQueue.getOperationTime());
            
            // 设置收件人邮箱
            emailDto.setRecipientEmail(emailSenderQueue.getEmailTo());
            
            // 动态解析邮件参数
            Map<String, String> dynamicParams = parseDynamicParameters(emailSenderQueue.getEmailParameter());
            emailDto.setDynamicParameters(dynamicParams);
            
            // 确定语言代码
            String languageCode = EmailLanguageConfigUtils.determineLanguageCode(dynamicParams, permissionCenterClient);
            emailDto.setLanguageCode(languageCode);
            
            // 固定添加当前日期参数（根据语言格式化）
            String formattedDate = generateFormattedDate(languageCode);
            dynamicParams.put("currentDate", formattedDate);
            
            log.info("基础邮件数据组装完成, 邮件ID: {}, 动态参数数量: {}, 语言: {}", 
                    emailDto.getId(), dynamicParams.size(), languageCode);
            
        } catch (Exception e) {
            log.error("组装邮件数据失败, 邮件ID: {}, 错误信息: {}", emailSenderQueue.getId(), e.getMessage(), e);
            emailDto.setErrorMessage(e.getMessage());
        }
        
        return emailDto;
    }

    /**
     * 动态解析邮件参数
     *
     * @param emailParameterJson 邮件参数JSON字符串
     * @return 解析后的参数Map
     */
    private Map<String, String> parseDynamicParameters(String emailParameterJson) {
        Map<String, String> parameters = new HashMap<>();
        
        if (GeneralTool.isEmpty(emailParameterJson)) {
            log.warn("邮件参数为空");
            return parameters;
        }
        
        try {
            ObjectMapper mapper = new ObjectMapper();
            parameters = mapper.readValue(emailParameterJson, Map.class);
            log.debug("成功解析邮件参数, 参数数量: {}", parameters.size());
        } catch (JsonProcessingException e) {
            log.error("解析邮件参数失败: {}, 错误信息: {}", emailParameterJson, e.getMessage(), e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("email_parameter_parse_failed"));
        }
        
        return parameters;
    }


    /**
     * 处理父模板
     *
     * @param emailDto 邮件数据
     * @param template 邮件模板实体
     * @param processedTemplate 已处理的模板内容
     * @return 最终模板内容
     */
    private String processParentTemplate(UniversalEmailDto emailDto, EmailTemplate template, String processedTemplate) {
        if (GeneralTool.isEmpty(template.getFkParentEmailTemplateId()) || template.getFkParentEmailTemplateId() == 0) {
            return processedTemplate;
        }
        
        try {
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(template.getFkParentEmailTemplateId());
            if (parentTemplate == null) {
                log.warn("父模板不存在, ID: {}", template.getFkParentEmailTemplateId());
                return processedTemplate;
            }
            
            String parentEmailTemplate = EmailLanguageConfigUtils.processParentTemplate(
                emailDto.getLanguageCode(),
                processedTemplate,
                parentTemplate,
                emailDto.getDynamicParameters()
            );
            
            return parentEmailTemplate != null ? parentEmailTemplate : processedTemplate;
            
        } catch (Exception e) {
            log.error("处理父模板异常, 父模板ID: {}, 错误信息: {}", 
                    template.getFkParentEmailTemplateId(), e.getMessage(), e);
            return processedTemplate;
        }
    }

    /**
     * 获取原始邮件模板内容
     *
     * @param emailDto 邮件数据DTO
     * @return 原始模板内容
     */
    private String getRawEmailTemplate(UniversalEmailDto emailDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        
        // 根据邮件类型Key查询模板
        if (GeneralTool.isNotEmpty(emailDto.getFkEmailTypeKey())) {
            remindTemplates = emailTemplateMapper.selectList(
                    Wrappers.<EmailTemplate>lambdaQuery()
                            .eq(EmailTemplate::getEmailTypeKey, emailDto.getFkEmailTypeKey())
            );
        }
        
        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，邮件类型Key: {}", emailDto.getFkEmailTypeKey());
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        
        EmailTemplate template = remindTemplates.get(0);
        
        // 根据语言选择对应的模板
        String emailTemplate = EmailLanguageConfigUtils.selectEmailTemplate(
            emailDto.getLanguageCode(),
            template.getEmailTemplate(),
            template.getEmailTemplateEn()
        );
        
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，邮件类型Key: {}", emailDto.getFkEmailTypeKey());
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        
        log.debug("获取原始模板完成, 邮件类型: {}, 模板长度: {}", 
                emailDto.getFkEmailTypeKey(), emailTemplate.length());
        
        return emailTemplate;
    }

    /**
     * 按优先级处理邮件标题
     *
     * @param externalTitle 外部传入的标题
     * @param htmlTemplate  HTML模板内容
     * @param languageCode  语言代码
     * @param dynamicParams 动态参数（用于替换标题中的变量）
     * @return 最终标题
     */
    private String processTitleByPriority(String externalTitle, String htmlTemplate, String languageCode, Map<String, String> dynamicParams) {
        try {
            // 先按优先级获取原始标题
            String rawTitle = EmailHtmlTitleExtractor.getTitleByPriority(externalTitle, htmlTemplate, languageCode);
            
            // 对标题中的变量进行替换
            String finalTitle = EmailTemplateEngine.processTitle(rawTitle, dynamicParams);
            
            log.info("标题处理完成, 外部标题: [{}], 原始标题: [{}], 最终标题: [{}], 语言: {}", 
                    externalTitle, rawTitle, finalTitle, languageCode);
            return finalTitle;
        } catch (Exception e) {
            log.error("处理邮件标题异常: {}", e.getMessage(), e);
            // 发生异常时返回默认标题
            return EmailHtmlTitleExtractor.getDefaultTitle(languageCode);
        }
    }

    /**
     * 处理模板变量替换
     *
     * @param rawTemplate 原始模板
     * @param emailDto    邮件数据
     * @return 处理后的模板
     */
    private String processTemplateWithVariables(String rawTemplate, UniversalEmailDto emailDto) {
        try {
            // 预处理短链和二维码参数
            Map<String, String> processedParams = preprocessLinkAndQrCode(emailDto.getDynamicParameters());
            
            // 使用通用模板引擎替换变量
            String processedTemplate = EmailTemplateEngine.processTemplate(rawTemplate, processedParams);
            
            // 替换标题占位符
            processedTemplate = processedTemplate.replace("#{taskTitle}", emailDto.getEmailTitle());
            
            // 处理父模板（如果存在）
            processedTemplate = processParentTemplateIfExists(emailDto, processedTemplate);
            
            log.info("模板变量处理完成, 邮件类型: {}, 最终模板长度: {}", 
                    emailDto.getFkEmailTypeKey(), processedTemplate.length());
            
            return processedTemplate;
            
        } catch (Exception e) {
            log.error("处理模板变量异常, 邮件类型: {}, 错误信息: {}", 
                    emailDto.getFkEmailTypeKey(), e.getMessage(), e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("email_template_process_failed"));
        }
    }

    /**
     * 处理父模板（如果存在）
     *
     * @param emailDto          邮件数据
     * @param processedTemplate 已处理的模板
     * @return 最终模板
     */
    private String processParentTemplateIfExists(UniversalEmailDto emailDto, String processedTemplate) {
        try {
            // 重新查询模板信息以获取父模板ID
            List<EmailTemplate> templates = emailTemplateMapper.selectList(
                    Wrappers.<EmailTemplate>lambdaQuery()
                            .eq(EmailTemplate::getEmailTypeKey, emailDto.getFkEmailTypeKey())
            );
            
            if (GeneralTool.isEmpty(templates)) {
                return processedTemplate;
            }
            
            EmailTemplate template = templates.get(0);
            return processParentTemplate(emailDto, template, processedTemplate);
            
        } catch (Exception e) {
            log.error("处理父模板异常: {}", e.getMessage(), e);
            return processedTemplate;
        }
    }

    /**
     * 预处理短链和二维码参数
     * 检测到qrcode或miniProgramUrl参数且值为路径格式时，调用partnerCenterClient获取实际的短链和二维码
     *
     * @param originalParams 原始参数Map
     * @return 处理后的参数Map
     */
    private Map<String, String> preprocessLinkAndQrCode(Map<String, String> originalParams) {
         if (originalParams == null) {
            return new HashMap<>();
        }
        
        // 创建新的参数Map，避免修改原始数据
        Map<String, String> processedParams = new HashMap<>(originalParams);
        
        // 检查是否包含qrcode参数
        if (processedParams.containsKey("qrcode")) {
            String qrcodeValue = processedParams.get("qrcode");
            
            // 检查是否为路径格式且不为空
            if (GeneralTool.isNotEmpty(qrcodeValue)) {
                try {
                    log.debug("检测到小程序路径参数，开始获取短链和二维码: {}", qrcodeValue);
                    
                    // 调用partnerCenterClient获取短链和二维码
                    LinkAndQrCodeVo linkAndQrCode = getLinkAndQrCodeFromPath(qrcodeValue);
                    if (linkAndQrCode != null) {
                        // 设置二维码参数
                        if (GeneralTool.isNotEmpty(linkAndQrCode.getQrCode())) {
                            processedParams.put("qrcode", linkAndQrCode.getQrCode());
                        } else {
                            processedParams.put("qrcode", "");
                        }
                        
                        // 新增短链参数
                        if (GeneralTool.isNotEmpty(linkAndQrCode.getUrl())) {
                            processedParams.put("miniProgramUrl", linkAndQrCode.getUrl());
                        } else {
                            processedParams.put("miniProgramUrl", "");
                        }
                        
                        log.info("成功获取并替换短链和二维码，原路径: {}, 短链长度: {}, 二维码长度: {}", 
                                qrcodeValue, 
                                linkAndQrCode.getUrl() != null ? linkAndQrCode.getUrl().length() : 0,
                                linkAndQrCode.getQrCode() != null ? linkAndQrCode.getQrCode().length() : 0);
                    } else {
                        log.warn("短链和二维码获取失败，原路径: {}", qrcodeValue);
                        // 失败时设置为空字符串
                        processedParams.put("qrcode", "");
                        processedParams.put("miniProgramUrl", "");
                    }
                } catch (Exception e) {
                    log.error("短链和二维码预处理异常，路径: {}, 错误信息: {}", qrcodeValue, e.getMessage(), e);
                    // 失败时使用空字符串，避免在邮件中显示错误的路径
                    processedParams.put("qrcode", "");
                    processedParams.put("miniProgramUrl", "");
                }
            } else {
                log.debug("二维码参数为空，直接使用原值: {}", qrcodeValue);
            }
        }
        
        return processedParams;
    }

    /**
     * 根据路径获取短链和二维码
     *
     * @param path 小程序路径
     * @return 短链和二维码数据，失败时返回null
     */
    private LinkAndQrCodeVo getLinkAndQrCodeFromPath(String path) {
        try {
            MinProgramQrCodeDto dto = new MinProgramQrCodeDto(path, 430);
            Result<LinkAndQrCodeVo> result = partnerCenterClient.getLinkAndQrCode(dto);
            
            if (result.isSuccess() && result.getData() != null) {
                log.debug("成功从partner-center获取短链和二维码，路径: {}", path);
                return result.getData();
            } else {
                log.warn("partner-center返回短链和二维码失败，路径: {}, 错误码: {}, 错误信息: {}", 
                        path, result.getCode(), result.getMessage());
                return null;
            }
        } catch (Exception e) {
            log.error("调用partner-center获取短链和二维码异常，路径: {}, 错误信息: {}", path, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据语言代码生成格式化的日期字符串
     *
     * @param languageCode 语言代码
     * @return 格式化的日期字符串
     */
    private String generateFormattedDate(String languageCode) {
        LocalDate currentDate = LocalDate.now();
        
        if (EmailLanguageConfigUtils.isEnglish(languageCode)) {
            // 英文格式：July 22, 2025
            return currentDate.format(DateTimeFormatter.ofPattern("MMMM d, yyyy", Locale.ENGLISH));
        } else {
            // 中文格式：2025年07月22日
            return currentDate.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"));
        }
    }

}