package com.get.remindercenter.component;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.dao.EmailSenderQueueMapper;
import com.get.remindercenter.dao.EmailTemplateMapper;
import com.get.remindercenter.dto.ApplicationFormDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.service.RemindTaskQueueService;
import com.get.remindercenter.utils.ReminderTemplateUtils;
import com.get.rocketmqcenter.dto.EmailSystemMQMessageDto;
import com.get.workflowcenter.feign.IWorkflowCenterClient;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

@Component("applicationFormEmailHelper")
@Slf4j
public class ApplicationFormEmailHelper extends EmailAbstractHelper {
    @Resource
    private IWorkflowCenterClient workflowCenterClient;



    @Resource
    private EmailTemplateMapper emailTemplateMapper;

    @Resource
    private EmailSenderQueueMapper emailSenderQueueMapper;


    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private RemindTaskQueueService remindTaskQueueService;

    @Override
    public void sendMail(EmailSenderQueue emailSenderQueue) {
        StringJoiner failedEmails = new StringJoiner("; "); // 新增：记录失败邮箱
        try {
            ApplicationFormDto applicationFormDto = assembleEmailData(emailSenderQueue);
            StringJoiner emailsCombined = new StringJoiner(", ");

            List<StaffVo> staff = permissionCenterClient.getStaffByIds(applicationFormDto.getStaffEmailSet());
            Map<Long, StaffVo> data = staff.stream()
                    .collect(Collectors.toMap(
                            StaffVo::getId,  // Key: StaffVo 的 ID
                            staffVo -> staffVo  // Value: StaffVo 本身
                    ));
            for (Long id : applicationFormDto.getStaffEmailSet()) {
                StaffVo staffVo = null;
                try {
//                    Result<StaffVo> staffDtoResult = permissionCenterClient.getCompanyIdByStaffId(Long.valueOf(id));
//                    if (staffDtoResult.isSuccess() && GeneralTool.isNotEmpty(staffDtoResult.getData())) {
//                        staffVo = staffDtoResult.getData();
//                    }
//                    //设置邮件模板
//                    String template = setEmailTemplate(workLeaveReminderDto);
//                    EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
//                    emailSystemMQMessageDto.setEmailSenderQueueId(workLeaveReminderDto.getId());
//                    emailSystemMQMessageDto.setTitle(workLeaveReminderDto.getEmailTitle());
//                    emailSystemMQMessageDto.setContent(template);
//                    StringJoiner emailsCombined = new StringJoiner(", ");
//
//                    emailSystemMQMessageDto.setToEmail(staffVo.getEmail());
//                    //rocKetMqCenterClient.getSystemSendEmail(emailSystemMQMessageDto);
//                    remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
//                    if (GeneralTool.isNotEmpty(staffVo.getEmail())) {
//                        emailsCombined.add(staffVo.getEmail());
//                    }
                    String staffName = data.get(id).getFullName();
                    applicationFormDto.getMap().put("roleName", staffName);
                    String template = setEmailTemplate(applicationFormDto);
                    EmailSystemMQMessageDto emailSystemMQMessageDto = new EmailSystemMQMessageDto();
                    emailSystemMQMessageDto.setEmailSenderQueueId(applicationFormDto.getId());
                    emailSystemMQMessageDto.setTitle(applicationFormDto.getEmailTitle());
                    emailSystemMQMessageDto.setContent(template);
                    String email = data.get(id).getEmail();
                    emailSystemMQMessageDto.setToEmail(email);
                    //emailSystemMQMessageDto.setToEmail("<EMAIL>");
                    remindTaskQueueService.sendSystemMail(emailSystemMQMessageDto);
                    if (GeneralTool.isNotEmpty(data.get(id).getEmail())) {
                        emailsCombined.add(data.get(id).getEmail());
                    }
                } catch (Exception e) {
                    // 记录发送失败的邮箱
                    String failedEmail = staffVo != null && staffVo.getEmail() != null ? staffVo.getEmail() : "staffId:" + staffVo.getId();
                    failedEmails.add(failedEmail + "(" + (e.getMessage() != null ? e.getMessage() : "发送失败") + ")");
                    log.error("邮件发送失败 - staffId: {}, email: {}, 原因: {}", staffVo.getId(), failedEmail, e.getMessage());
                }

            }

            emailSenderQueue.setEmailTo(emailsCombined.toString());
            LambdaUpdateWrapper<EmailSenderQueue> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(EmailSenderQueue::getId, emailSenderQueue.getId())  // 更新条件（按ID匹配）
                    .set(EmailSenderQueue::getEmailTo, emailsCombined.toString());  // 只更新 emailTo 字段
            // 如果 failedEmails 不为空，则额外更新 errorMessage
            if (failedEmails.length() > 0) {
                updateWrapper.set(EmailSenderQueue::getErrorMessage, "失败邮箱: " + failedEmails.toString());
            }
            emailSenderQueueMapper.update(null, updateWrapper);  // 传入 null，由 updateWrapper 控制更新
        } catch (Exception e) {
            log.error("WorkLeaveReminderEmailHelper error:{}", e);
            emailSenderQueue.setOperationStatus(-1);
            emailSenderQueue.setOperationCount(emailSenderQueue.getOperationCount() + 1);
            failedEmails.add("邮件发送异常" + e.getMessage());
            emailSenderQueue.setErrorMessage(failedEmails.toString());
            emailSenderQueueMapper.updateById(emailSenderQueue);
        }


    }

    private String setEmailTemplate(ApplicationFormDto reminderDto) {
        List<EmailTemplate> remindTemplates = new ArrayList<>();
        if (reminderDto.getFkEmailTypeKey().equals(EmailTemplateEnum.APPLICATION_FORM_APPROVAL_NOTICE.getEmailTemplateKey())) {
            remindTemplates = emailTemplateMapper.selectList(Wrappers.<EmailTemplate>lambdaQuery().eq(EmailTemplate::getEmailTypeKey, EmailTemplateEnum.APPLICATION_FORM_APPROVAL_NOTICE.getEmailTemplateKey()));
        }

        if (GeneralTool.isEmpty(remindTemplates)) {
            log.error("邮箱模板不存在，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
        String emailTemplate = null;
        if (!reminderDto.getLanguageCode().equals("en")) {
            emailTemplate = remindTemplates.get(0).getEmailTemplate();
        } else {
            emailTemplate = remindTemplates.get(0).getEmailTemplateEn();
        }
        emailTemplate  = ReminderTemplateUtils.getReminderTemplate(reminderDto.getMap(), emailTemplate);
        if (GeneralTool.isEmpty(emailTemplate)) {
            log.error("邮箱模板内容为空，需要配置邮箱模板");
            throw new GetServiceException(LocaleMessageUtils.getMessage("mailbox_template_is_empty"));
        }
//        emailTemplate = emailTemplate.replace("#{taskTitle}", reminderDto.getEmailTitle());
        //把emailTemplate插入到父模板里
        String parentEmailTemplate = null;
        if (GeneralTool.isNotEmpty(remindTemplates.get(0).getFkParentEmailTemplateId()) && remindTemplates.get(0).getFkParentEmailTemplateId() != 0) {
            EmailTemplate parentTemplate = emailTemplateMapper.selectById(remindTemplates.get(0).getFkParentEmailTemplateId());
            Map map = new HashMap();
            map.put("subtemplate", emailTemplate);
            if (reminderDto.getLanguageCode().equals("en")) {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplateEn());
            } else {
                parentEmailTemplate = ReminderTemplateUtils.getReminderTemplate(map, parentTemplate.getEmailTemplate());
            }

        }
        return parentEmailTemplate;
    }

    @Override
    public ApplicationFormDto assembleEmailData(EmailSenderQueue emailSenderQueue) {
        ApplicationFormDto applicationFormDto = new ApplicationFormDto();
        BeanUtils.copyProperties(emailSenderQueue, applicationFormDto);
        String emailParameter = emailSenderQueue.getEmailParameter();
        Long companyId = 3L;
        Map<String, String> parsedMap = null;
        if (GeneralTool.isNotEmpty(emailParameter)) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                parsedMap = mapper.readValue(emailParameter, Map.class);
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
            applicationFormDto.setMap(parsedMap);

            String id = parsedMap.get("staffId");
//
//            String superiorId = parsedMap.get("superiorId");
//            StaffVo staffVo = null;
//            if (GeneralTool.isNotEmpty(superiorId)) {
//                staffVo = permissionCenterClient.getCompanyIdByStaffId(Long.valueOf(superiorId)).getData();
//            }
            String staffIdList = parsedMap.get("staffIdList");

            if (GeneralTool.isNotEmpty(staffIdList)) {
                if (staffIdList != null && staffIdList.startsWith("[") && staffIdList.endsWith("]")) {
                    staffIdList = staffIdList.substring(1, staffIdList.length() - 1);
                }
                //转换为Set<Long>类型
                Set<Long> list = new HashSet<>();
                for (String s : staffIdList.split("\\s*,\\s*")) {
                    Long l = Long.valueOf(s);
                    list.add(l);
                }
                applicationFormDto.setStaffEmailSet(list);
            }

//            if (GeneralTool.isNotEmpty(id)) {
//                Result<StaffVo> staffDtoResult = permissionCenterClient.getCompanyIdByStaffId(Long.valueOf(id));
//                if (staffDtoResult.isSuccess() && GeneralTool.isNotEmpty(staffDtoResult.getData())) {
//                    staffVo = staffDtoResult.getData();
//                }
//            }
//            if (GeneralTool.isNotEmpty(staffVo)) {
//                companyId = staffVo.getFkCompanyId();
//            }
        }
        //获取中英文配置
        Map<Long, String> versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(companyId);
        applicationFormDto.setLanguageCode(versionValue2);
        //用户名
        String roleName = null;
        //发送日期
        Date currentDate = null;

        String fkRemindEventTypeKey = emailSenderQueue.getFkEmailTypeKey();

        if ((TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key.equals(emailSenderQueue.getFkTableName())
                ||TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key.equals(emailSenderQueue.getFkTableName())
                ||TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key.equals(emailSenderQueue.getFkTableName())
                ||TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key.equals(emailSenderQueue.getFkTableName())
        ) &&
                EmailTemplateEnum.APPLICATION_FORM_APPROVAL_NOTICE.getEmailTemplateKey().equals(fkRemindEventTypeKey)
        ) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //设置收件人和发送时间
            parsedMap.put("currentDate", sdf.format(new Date()));

//            //费用报销单
//            if (TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key.equals(emailSenderQueue.getFkTableName())) {
//
//            }
//            //借款申请单
//            else if (TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key.equals(emailSenderQueue.getFkTableName())) {
//                departmentName = formInformationVo.getDepartmentName();
//            }
//            //差旅报销单
//            else if (TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key.equals(emailSenderQueue.getFkTableName())) {
//                departmentName = formInformationVo.getDepartmentName();
//            }
//            //支付申请单
//            else if (TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key.equals(emailSenderQueue.getFkTableName())) {
//
//                departmentName = formInformationVo.getDepartmentName();
//            }
            applicationFormDto.setMap(parsedMap);
        }
        return applicationFormDto;
    }
}

