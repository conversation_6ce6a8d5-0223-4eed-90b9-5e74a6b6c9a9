package com.get.util;


import com.get.entity.MMailAccount;
import com.get.entity.MMailAttached;
import com.sun.mail.imap.IMAPStore;

import javax.mail.*;
import javax.mail.internet.MimeMessage;
import javax.mail.internet.MimeMultipart;
import javax.mail.internet.MimeUtility;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Properties;



public class MailBoxUtils {

    /**
     * 登录邮箱方法
     *
     * @param username 用户名
     * @param password 密码
     * @return 返回邮箱登录实体类
     * @throws Exception 异常抛出
     */
    public MailBox login(String username, String password, String host) throws Exception {
        // 设置属性
        Properties properties = new Properties();
        properties.put("mail.store.protocol", "imaps");
        properties.put("mail.imap.host", host);
        properties.put("mail.imap.port", "993");
        properties.put("mail.imap.ssl.enable", "true");
        properties.setProperty("mail.imap.partialfetch","false");
        properties.setProperty("mail.imap.fetchsize", "10485760");
//        properties.put("mail.imap.connectiontimeout", "1000*60*5");
/*        // 设置连接超时时间（毫秒）
        properties.put("mail.smtp.connectiontimeout", "5000");  // 5秒

        // 设置读取超时时间（毫秒）
        properties.put("mail.smtp.timeout", "5000");  // 5秒*/
        Session session = Session.getInstance(properties);
        Store store = session.getStore("imap");
//        session.setDebug(true); // 启用调试日志
        store.connect(username, password);

//        // 获取所有文件夹
//        Folder[] folders = store.getDefaultFolder().list();
        MailBox mailBox = new MailBox();
        mailBox.setSession(session);
        mailBox.setStore(store);
        return mailBox;
    }

    public MailBox login163(String username, String password, String host, String port) throws Exception {
        Properties props = new Properties();
        props.setProperty("mail.store.protocol", "imap");
        props.setProperty("mail.imap.host", host);
        props.setProperty("mail.imap.port", port);
        props.setProperty("mail.imap.connectiontimeout", "3000000"); // 30秒
        props.setProperty("mail.imap.timeout", "30000000"); // 5分钟
        HashMap IAM = new HashMap();
        //带上IMAP ID信息，由key和value组成，例如name，version，vendor，support-email等。
        IAM.put("name", "myname");
        IAM.put("version", "1.0.0");
        IAM.put("vendor", "myclient");
        IAM.put("support-email", "<EMAIL>");
        Session session = Session.getInstance(props);

        IMAPStore store = (IMAPStore) session.getStore("imap");
        store.connect(username, password);
        store.id(IAM);
        MailBox mailBox = new MailBox();
        mailBox.setSession(session);
        mailBox.setStore(store);
        return mailBox;
    }

    public static void processAttachments(Multipart multipart, List<MMailAttached> mMailAttacheds, MMailAccount mMailAccount, String mailId) throws Exception {
        int partCount = multipart.getCount();
        for (int j = 0; j < partCount; j++) {
            BodyPart bodyPart = multipart.getBodyPart(j);
            // 如果这是一个嵌套的multipart部分，则递归处理
            if (bodyPart.isMimeType("multipart/*")) {
                processAttachments((Multipart) bodyPart.getContent(), mMailAttacheds, mMailAccount, mailId);
            } else {
                try {
                    // 尝试获取文件名
                    String fileName = MimeUtility.decodeText(bodyPart.getFileName());
                    // 检查是否有文件名或disposition为attachment/inline
                    String disposition = bodyPart.getDisposition();

                    if ((fileName != null && !fileName.trim().isEmpty()) ||
                            (disposition != null &&
                                    (disposition.equalsIgnoreCase(Part.ATTACHMENT) ||
                                            disposition.equalsIgnoreCase(Part.INLINE)))) {

                        if (fileName == null || fileName.trim().isEmpty()) {
                            // 如果没有文件名，创建一个默认名称
                            fileName = "unnamed_attachment_" + j;
                        }
                        // 创建并填充附件实体对象
                        MMailAttached mMailAttached = new MMailAttached();
                        mMailAttached.setFkMailAccountId(mMailAccount.getId());
                        mMailAttached.setMailId(mailId);
                        mMailAttached.setFileName(fileName);
                        mMailAttached.setFileId(String.valueOf(j));
                        // 获取文件扩展名
                        int lastDotIndex = fileName.lastIndexOf('.');
                        if (lastDotIndex != -1) {
                            if (fileName.substring(lastDotIndex + 1).length() > 4) {
                                mMailAttached.setFileExtension(".");
                            } else {
                                mMailAttached.setFileExtension(fileName.substring(lastDotIndex + 1));
                            }

                        } else {
                            mMailAttached.setFileExtension(".");
                        }

                        mMailAttached.setGmtCreate(LocalDateTime.now());
                        mMailAttacheds.add(mMailAttached);
                    }
                } catch (Exception e) {
                    // 记录错误但继续处理其他部分
                    System.out.println("Error processing attachment: " + e.getMessage());
                }
            }
        }
    }



    /*public void fetchAllMail(MMailAccount mMailAccount, MMail mMail, MailBox mailBox, String foldName, String saveFoldName) throws Exception {
        Store store = mailBox.getStore();
        Folder folder = store.getFolder(foldName);
        folder.open(Folder.READ_ONLY);
        // 转换为 Date
        Date afterDate = Date.from(mMail.getDate().atZone(ZoneId.systemDefault()).toInstant());
        // 使用 ComparisonTerm.GT 表示"大于"，即晚于给定日期
        SearchTerm receivedAfter = new ReceivedDateTerm(ComparisonTerm.GT, afterDate);
        // 搜索邮件
        Message[] messages1 = folder.search(receivedAfter);
        List<Message> messages = new ArrayList<>();
        for (Message message : messages1) {
            MimeMessage mimeMessage = (MimeMessage) message;
            Date date = mimeMessage.getReceivedDate();
            // 将 Date 转换为 LocalDateTime
            LocalDateTime mailDate = date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            LocalDateTime targetDateExclusive = mMail.getDate();
            if (mailDate.isAfter(targetDateExclusive)) {
                messages.add(message);
            }
        }
        for (Message message : messages) {
            MimeMessage mimeMessage = (MimeMessage) message;
            List<MMailAttached> mMailAttacheds = new ArrayList<>();
            // 获取邮件id
            String mailId = "";
            if (mimeMessage.getMessageID() != null && !mimeMessage.getMessageID().isEmpty()) {
                mailId = mimeMessage.getMessageID().replaceAll("<", "").replaceAll(">", "");
            }
            // 获取邮件日期
            Date date = mimeMessage.getReceivedDate();
            // 获取邮件的主题
            String subject = mimeMessage.getSubject();
            // 获取邮件内容
            Object mailContent = mimeMessage.getContent();
            // 获取邮件正文内容和附件
            String content = "";
            if (mailContent instanceof String) {
                content = (String) mailContent;
            } else if (mailContent instanceof Multipart) {
                MimeMultipart multipart = (MimeMultipart) mailContent;
                processAttachments(multipart, mMailAttacheds, mMailAccount, mailId);
                int partCount = multipart.getCount();
                for (int j = 0; j < partCount; j++) {
                    BodyPart bodyPart = multipart.getBodyPart(j);
                    if (bodyPart.isMimeType("text/html")) {
                        try {
                            content = java.net.URLDecoder.decode(String.valueOf(bodyPart.getContent()), "utf-8");
                        } catch (Exception e) {
                            System.out.println("解析附件中的内容出错" + e.getMessage());
                        }
                    } else if (bodyPart.isMimeType("multipart/*")) {
                        // 处理嵌套的多部分内容
                        content = processNestedMultipart((MimeMultipart) bodyPart.getContent());
                    }
                }
            }
            // 获取发件人
            String from = ((InternetAddress) message.getFrom()[0]).getAddress();
            // 获取收件人
            // List<String> toName = new ArrayList<>();
            StringBuilder toName = new StringBuilder();
            // 获取收件人信息
            Address[] recipients = message.getAllRecipients();
            if (recipients != null) {
                for (Address recipient : recipients) {
                    toName.append(((InternetAddress) recipient).getAddress()).append(",");
                }
            }
            // 如果 toName 不为空，则删除最后一个逗号
            if (toName.length() > 0) {
                toName.setLength(toName.length() - 1); // 删除最后一个字符
            }
            // 获取抄送人
            StringBuilder ccName = new StringBuilder();
            Address[] ccAddresses = message.getRecipients(Message.RecipientType.CC);
            if (ccAddresses != null && ccAddresses.length > 0) {
                System.out.println("CC recipients for message with subject: " + message.getSubject());
                for (Address address : ccAddresses) {
                    ccName.append(((InternetAddress) address).getAddress()).append(",");
                }
            }
            // 如果 ccName 不为空，则删除最后一个逗号
            if (ccName.length() > 0) {
                ccName.setLength(ccName.length() - 1); // 删除最后一个字符
            }
            boolean isRead = mimeMessage.isSet(Flags.Flag.SEEN);
            boolean isStar = mimeMessage.isSet(Flags.Flag.FLAGGED);
            MMail mail = new MMail();
            mail.setFkPlatformCode(mMailAccount.getFkPlatformCode());
            mail.setFkPlatformUserId(mMailAccount.getFkPlatformUserId());
            mail.setFkMailAccountId(mMailAccount.getId());
            mail.setMailId(mailId);
            mail.setFoldBox(saveFoldName);
            mail.setSubject(subject);
            mail.setBody(content);
            mail.setFromMail(from);
            mail.setToMail(toName.toString());
            mail.setCcMail(ccName.toString());
            mail.setSeparately(false);
            // 将 Date 转换为 LocalDateTime
            LocalDateTime localDateTime = date.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDateTime();
            mail.setDate(localDateTime);
            mail.setLlmMailType(0);
            mail.setRead(isRead);
            mail.setStar(isStar);
            mail.setGmtCreate(LocalDateTime.now());
            try {
                mailMapper.insert(mail);
                for (MMailAttached mailAttached : mMailAttacheds) {
                    mailAttached.setFkMailId(mail.getId());
                    mMailAttachedMapper.insert(mailAttached);
                }
            } catch (Exception e) {
                System.out.println("邮件插入数据库失败：" + mail.getMailId() + e.getMessage());
            }
        }
    }*/


    public int getMailType(Message message) throws Exception {
        // 1、已读; 2、未读; 3、星标邮件;
        int type = 1;
        MimeMessage mimeMessage = (MimeMessage) message;
        // 判断邮件是否已读
        boolean isRead = mimeMessage.isSet(Flags.Flag.SEEN);
        if (!isRead) {
            type = 2;
        }
        boolean isStarred = mimeMessage.isSet(Flags.Flag.FLAGGED);
        if (isStarred) {
            type = 3;
        }
        return type;
    }

    private String processNestedMultipart(MimeMultipart multipart) throws Exception {
        int partCount = multipart.getCount();
        String content = "";
        for (int i = 0; i < partCount; i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            if (bodyPart.isMimeType("text/html")) {
                content = java.net.URLDecoder.decode(String.valueOf(bodyPart.getContent()), "utf-8");
            }
        }
        return content;
    }

   /* private static List<Mail> processMailBox(Message[] messages, String folderName) throws Exception {
        List<Mail> mailList = new ArrayList<>();
        for (Message message : messages) {
            MimeMessage mimeMessage = (MimeMessage) message;
            try {
                message.getSubject();
            } catch (Exception e) {
                continue;
            }
            String title = message.getSubject();
            String from = ((InternetAddress) message.getFrom()[0]).getAddress();
            List<String> toName = new ArrayList<>();
            // 获取收件人信息
            Address[] recipients = message.getAllRecipients();
            if (recipients != null) {
                for (Address recipient : recipients) {
                    toName.add(((InternetAddress) recipient).getAddress());
                }
            }
            // 获取邮件内容
            Object mailContent = mimeMessage.getContent();
            // 获取邮件正文内容和附件
            String content = "";
            List<AnnexVo> annexList = new ArrayList<>();
            if (mailContent instanceof String) {
                content = (String) mailContent;
            } else if (mailContent instanceof Multipart) {
                MimeMultipart multipart = (MimeMultipart) mailContent;
                int partCount = multipart.getCount();
                for (int j = 0; j < partCount; j++) {
                    BodyPart bodyPart = multipart.getBodyPart(j);
                    if (bodyPart.isMimeType("text/plain")) {
                        content = String.valueOf(bodyPart.getContent());
                    } else if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
                        // 获取附件名称
                        String fileName = bodyPart.getFileName();
                        // 解码
                        fileName = MimeUtility.decodeText(fileName);
                        AnnexVo annexVo = new AnnexVo();
                        annexVo.setAnnexName(fileName);
                        annexVo.setId(j);
                        annexList.add(annexVo);
                    } else if (bodyPart.isMimeType("multipart/*")) {
                        // 处理嵌套的多部分内容
                        content = processNestedMultipart((MimeMultipart) bodyPart.getContent());
                    }
                }
            }
            Mail mail = new Mail();
            mail.setDate(mimeMessage.getReceivedDate());

            LocalDate today = LocalDate.now();
            LocalDate yesterday = today.minusDays(1);
            LocalDate msgDate = mimeMessage.getReceivedDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            if (msgDate.equals(today)) {
                mail.setDateSort(1);
            } else if (msgDate.equals(yesterday)) {
                mail.setDateSort(2);
            } else {
                mail.setDateSort(3);
            }
            if (mimeMessage.getMessageID() != null && !mimeMessage.getMessageID().isEmpty()) {
                mail.setMailID(mimeMessage.getMessageID().replaceAll("<", "").replaceAll(">", ""));
            } else {
                mail.setMailID(null);
            }
            mail.setTitle(title);
            mail.setContent(content);
            mail.setFrom(from);
            mail.setAnnexs(annexList);
            mail.setToName(toName);
            switch (folderName) {
                case "inbox":
                    mail.setMailType(getMailType(message));
                    mail.setAiMailType(AiSortMail.sortByTitle(title));
                    if (message.isSet(Flags.Flag.FLAGGED)) {
                        mail.setStar(true);
                    }
                    break;
                case "drafts":
                    mail.setMailType(4);
                    mail.setAiMailType(7);
                    break;
                case "Sent Messages":
                    mail.setMailType(5);
                    mail.setAiMailType(7);
                    break;
                case "Deleted Messages":
                    mail.setMailType(6);
                    mail.setAiMailType(7);
                    break;
            }
            mailList.add(mail);
        }
        return mailList;
    }

    public static List<Mail> getMailBox(MailBox mailBox, String foldName, String boxName) throws Exception {
        Store store = mailBox.getStore();
        Folder folder = store.getFolder(foldName);
        folder.open(Folder.READ_ONLY);
        Message[] messages = folder.getMessages();
        return processMailBox(messages, boxName);
    }*/

    public Session buildSendSession(String emailUsername, String emailPassword, String host, String port, String type) throws Exception {
        // 设置邮件会话属性
        Properties props = new Properties();
        props.put("mail.smtp.host", host);
        props.put("mail.smtp.auth", "true");
        props.put("mail.smtp.port", port); // SMTP服务器端口号
//        props.put("mail.smtp.starttls.enable", "true"); // 启用TLS加密
        props.setProperty("mail.smtp.socketFactory.class", "javax.net.ssl.SSLSocketFactory"); // 使用SSL加密

        /*// 创建Session实例对象
        return Session.getInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(emailUsername, emailPassword);
            }
        });*/
        // 创建Session对象
        return Session.getDefaultInstance(props, new Authenticator() {
            protected PasswordAuthentication getPasswordAuthentication() {
                return new PasswordAuthentication(emailUsername, emailPassword);
            }
        });
    }

    /*public static int sendMail(Session session, MailBox mailBox, String foldName, SendMailVo sendMailVo) throws Exception {
        Store store = mailBox.getStore();
        // 如果是保存草稿首先判断草稿箱有没有这封邮件
        if (sendMailVo.isSaveDraft() && sendMailVo.getMailId() != null && !sendMailVo.getMailId().isEmpty()) {
            // 打开草稿箱文件夹
            Folder draftsFolder = store.getFolder(foldName);
            draftsFolder.open(Folder.READ_WRITE);
            Message[] messages = draftsFolder.getMessages();
            for (Message message : messages) {
                MimeMessage mimeMessage = (MimeMessage) message;
                if (mimeMessage.getMessageID() != null && !mimeMessage.getMessageID().isEmpty()) {
                    String mailId = mimeMessage.getMessageID().replaceAll("<", "").replaceAll(">", "");
                    if (sendMailVo.getMailId().equals(mailId)) {
                        message.setFlag(Flags.Flag.DELETED, true);
                        draftsFolder.close();
                        break;
                    }
                }
            }
        }
        // 创建MimeMessage对象
        Message message = new MimeMessage(session);
        // 发件人
        message.setFrom(new InternetAddress(sendMailVo.getEmailUsername()));
        // 接收人,允许收件人为空，为空就保存到草稿箱
        if (sendMailVo.getRecipient() != null && !sendMailVo.getRecipient().isEmpty()) {
            message.setRecipients(Message.RecipientType.TO, InternetAddress.parse(sendMailVo.getRecipient()));
        }
        if (sendMailVo.getCCPeople() != null) {
            // 抄送人，多个抄送人之间用，号隔开
            message.setRecipients(Message.RecipientType.CC, InternetAddress.parse(sendMailVo.getCCPeople()));
        }
        if (sendMailVo.getSubject() != null && !sendMailVo.getSubject().isEmpty()) {
            // 邮件标题
            message.setSubject(sendMailVo.getSubject());
        }
        BodyPart messageBodyPart = new MimeBodyPart();
        if (sendMailVo.getContent() != null && !sendMailVo.getContent().isEmpty()) {
            // 邮件内容
            messageBodyPart.setText(sendMailVo.getContent());
        }
        // 创建多部件消息
        Multipart multipart = new MimeMultipart();
        multipart.addBodyPart(messageBodyPart);
        // 添加附件
        if (sendMailVo.getAnnexs() != null) {
            for (Annex annex : sendMailVo.getAnnexs()) {
                String annexPath = annex.getPath();
                messageBodyPart = new MimeBodyPart();
                DataSource source = new FileDataSource(annexPath);
                messageBodyPart.setDataHandler(new DataHandler(source));
                messageBodyPart.setFileName(annex.getName());
                multipart.addBodyPart(messageBodyPart);
            }
        }
        // 将多部件消息设置为邮件内容
        message.setContent(multipart);
        if (sendMailVo.isSaveDraft()) {
            // 打开草稿箱文件夹
            Folder draftsFolder1 = store.getFolder(foldName);
            draftsFolder1.open(Folder.READ_WRITE);
            // 将邮件复制到已发送邮件文件夹
            draftsFolder1.appendMessages(new Message[]{message});
        } else {
            // 发送邮件
            Transport.send(message);
            // 打开已发送邮件文件夹
            Folder sentFolder = store.getFolder(foldName);
            sentFolder.open(Folder.READ_WRITE);
            // 将邮件复制到已发送邮件文件夹
            sentFolder.appendMessages(new Message[]{message});
        }
        return 1;
    }*/

    private byte[] readFully(InputStream inputStream) throws Exception {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            baos.write(buffer, 0, bytesRead);
        }
        return baos.toByteArray();
    }

    private void saveAttachment(BodyPart bodyPart, String fileName) throws Exception {
        // 创建输出文件
        File file = new File(fileName);
        try (FileOutputStream fos = new FileOutputStream(file)) {
            // 读取附件数据
            byte[] attachmentData = readFully(bodyPart.getInputStream());
            fos.write(attachmentData);
        }
    }


    /*public static String downAnnex(MailBox mailBox, List<String> foldTypes, DownAnnexVo downAnnexVo, String savePath) throws Exception {
        String AbsolutePath = savePath;
        boolean checkFindAnnex = false;
        for (String foldType : foldTypes) {
            Store store = mailBox.getStore();
            Folder folder = store.getFolder(foldType);
            folder.open(Folder.READ_ONLY);
            Message[] messages = folder.getMessages();
            for (Message message : messages) {
                MimeMessage mimeMessage = (MimeMessage) message;
                String mailId = mimeMessage.getMessageID().replaceAll("<", "").replaceAll(">", "");
                if (downAnnexVo.getMailId().equals(mailId)) {
                    if (mimeMessage.isMimeType("multipart/*")) {
                        MimeMultipart mimeMultipart = (MimeMultipart) mimeMessage.getContent();
                        int partCount = mimeMultipart.getCount();
                        for (int i = 0; i < partCount; i++) {
                            BodyPart bodyPart = mimeMultipart.getBodyPart(i);
                            if (Part.ATTACHMENT.equalsIgnoreCase(bodyPart.getDisposition())) {
                                // 获取附件名称
                                String fileName = bodyPart.getFileName();
                                fileName = MimeUtility.decodeText(fileName);
                                if (fileName.equals(downAnnexVo.getAnnexName()) && i == downAnnexVo.getAnnexId()) {
                                    AbsolutePath = AbsolutePath + UUID.randomUUID() + fileName;
                                    // 保存附件到本地
                                    saveAttachment(bodyPart, AbsolutePath);
                                    checkFindAnnex = true;
                                    break;
                                }
                            }
                        }
                    }
                }
                if (checkFindAnnex) {
                    return new File(AbsolutePath).getAbsolutePath();
                }
            }
        }
        return null;
    }

    public static int changeMailState(MailBox mailBox, List<String> mailBoxTypes, ChangeMailStateVo changeMailStateVo, String targetFold) throws Exception {
        for (String mailBoxType : mailBoxTypes) {
            Store store = mailBox.getStore();
            Folder folder = store.getFolder(mailBoxType);
            folder.open(Folder.READ_WRITE);
            Message[] messages = folder.getMessages();
            Folder targetFolder = store.getFolder(targetFold);
            targetFolder.open(Folder.READ_WRITE);
            boolean findMessage = false;
            for (Message message : messages) {
                MimeMessage mimeMessage = (MimeMessage) message;
                String mailId = mimeMessage.getMessageID().replaceAll("<", "").replaceAll(">", "");
                if (changeMailStateVo.getMailId().equals(mailId)) {
                    findMessage = true;
                    if (changeMailStateVo.getState() == 1) {
                        // 标记为已读
                        message.setFlag(Flags.Flag.SEEN, true);
                    } else if (changeMailStateVo.getState() == 2) {
                        // 标记为未读
                        message.setFlag(Flags.Flag.SEEN, false);
                    } else if (changeMailStateVo.getState() == 3) {
                        // 标记为星标
                        message.setFlag(Flags.Flag.FLAGGED, true);
                    } else if (changeMailStateVo.getState() == 4) {
                        // 取消星标
                        message.setFlag(Flags.Flag.FLAGGED, false);
                    } else if (changeMailStateVo.getState() == 5) {
                        // 标记已删除
                        message.setFlag(Flags.Flag.DELETED, true);
                        folder.copyMessages(new Message[]{message}, targetFolder);
                    } else if (changeMailStateVo.getState() == 6) {
                        // 将原始邮件删除
                        message.setFlag(Flags.Flag.DELETED, true);
                        folder.copyMessages(new Message[]{message}, targetFolder);
                        // 标记为草稿
                        message.setFlag(Flags.Flag.DRAFT, false);
                    }
                    break;
                }
            }
            folder.close();
            targetFolder.close();
            if (!findMessage) {
                return 0;
            }
        }
        return 1;
    }*/


}
