package com.get;

import com.get.common.constant.AppCenterConstant;
import com.get.core.cloud.feign.EnableGetFeign;
import com.get.core.start.GetApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.cloud.client.SpringCloudApplication;

@EnableGetFeign
@SpringCloudApplication
@MapperScan("com.get.dao")
public class AisMailFetchRocketMQApplication {
    public static void main(String[] args) {
        GetApplication.run(AppCenterConstant.APPLICATION_AIS_MAIL_FETCH_ROCKETMQ, AisMailFetchRocketMQApplication.class, args);
    }
}