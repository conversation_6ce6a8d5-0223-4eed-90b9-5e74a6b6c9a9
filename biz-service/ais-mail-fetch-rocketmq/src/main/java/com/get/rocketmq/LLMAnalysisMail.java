package com.get.rocketmq;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.get.dao.MMailDorisMapper;
import com.get.dao.MMailLLMAnalysisMapper;
import com.get.dao.MMailMapper;
import com.get.entity.MMail;
import com.get.entity.MMailDoris;
import com.get.entity.MMailLLMAnalysis;
import com.get.util.LLMAnalysis;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.jsoup.Jsoup;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
@RocketMQMessageListener(
        consumeThreadMax = 10,
        topic = "analyse_mail_topic", // topic
        consumerGroup = "analyse_mail_topic_consumer_group", // 消费组
        maxReconsumeTimes = 3, //最大重试次数
        consumeMode = ConsumeMode.ORDERLY
)
public class LLMAnalysisMail implements RocketMQListener<MMail> {
    @Resource
    private MMailMapper mailMapper;

    @Resource
    private MMailDorisMapper mailDorisMapper;

    @Resource
    private MMailLLMAnalysisMapper mailLLMAnalysisMapper;

    @Override
    public void onMessage(MMail mail) {
        log.info("收到需要分析邮件消息，mMailAccount={}", mail);
        // 避免重复插入
        QueryWrapper<MMailLLMAnalysis> analysisQueryWrapper = new QueryWrapper<>();
        analysisQueryWrapper.eq("fk_mail_id", mail.getId());
        List<MMailLLMAnalysis> analysisList = mailLLMAnalysisMapper.selectList(analysisQueryWrapper);
        if (!analysisList.isEmpty()) {
            return;
        }
        QueryWrapper<MMailLLMAnalysis> mailLLMAnalysisQueryWrapper = new QueryWrapper<>();
        mailLLMAnalysisQueryWrapper.eq("fk_mail_id", mail.getId());
        List<MMailLLMAnalysis> mailLLMAnalysisList = mailLLMAnalysisMapper.selectList(mailLLMAnalysisQueryWrapper);
        if (!mailLLMAnalysisList.isEmpty()) {
            log.info("邮件已经分析过了");
            return;
        }
        String question = "我将给你一封邮件的标题，内容，你帮我按照下面类型分类：1、新申请; 2、提交完成; 3、已录取; 4、已付学费; 5、收到签证涵; 6、获得签证; 7、其他，回答只能有一位数字。";
        question = question + String.format("title:%s。content:%s。", mail.getSubject(), Jsoup.parse(mail.getBody()).text());
        question = question + "如果是新申请类型帮我从邮件中总结出以下内容：学生姓名：、申请国家/地区：、学校：、课程等级：、课程名称：。";
        question = question + "如果是提交完成类型帮我从邮件中总结出以下内容：学生姓名：、申请国家/地区：、学校：，课程等级：、课程名称：、申请方式：、申请提交完成时间：。";
        question = question + "如果是已录取类型帮我从邮件中总结出以下内容：学生姓名：、申请国家/地区：、学校：，课程等级：、课程名称：、接受offer截止时间：、支付押金截止时间：。";
        question = question + "如果是已付学费类型帮我从邮件中总结出以下内容：学生姓名：、申请国家/地区：、学校：，课程等级：、课程名称：、付款日期：、付款方式：。";
        question = question + "如果是收到签证涵类型帮我从邮件中总结出以下内容：学生姓名：、申请国家/地区：、学校：，课程等级：、课程名称：、入学时间：、课程结束时间：、课程长度类型：、时长：、保险购买方式：。";
        question = question + "如果是获得签证类型帮我从邮件中总结出以下内容：学生姓名：、申请国家/地区：、学校：，课程等级：、课程名称：。";
        question = question + "在你的回复中只可以有两行内容，不可以出现你的思考过程，以及其他的任何东西。第一行：返回分类的数字。第二行：返回总结结果，如果有的字段提取不出来就显示未提及";
        LLMAnalysis llmAnalysis = new LLMAnalysis();
        log.info("大模型提问内容是：{}", question);
        try {
//            String res = llmAnalysis.deepSeekAnalysis(question);
            String res = llmAnalysis.tongyiAnalysis(question);
            log.info("大模型识别结果是：{}", res);
            int lLMResult = 7;
            try {
                String notThink = res.replaceAll("<think>[\\s\\S]*?</think>", "");
                String descriptionPart = notThink.replaceAll("^\\d+", "").trim();
                Pattern pattern = Pattern.compile("(^|\\s)(\\d+)");
                Matcher matcher = pattern.matcher(notThink.trim()); // 使用trim()去除首尾空白
                if (matcher.find()) {
                    // 获取匹配到的第一个数字序列
                    String type = matcher.group(2);
                    lLMResult = Integer.parseInt(type);
                }
                if (lLMResult == 0) {
                    lLMResult = 7;
                }
                mail.setLlmMailType(lLMResult);
                mailMapper.updateById(mail);

//                // doris同步进行更新，先进行删除在插入
//                mailDorisMapper.deleteMailDoris(mail.getId());
//                // 删除后重新插入
//                MMailDoris mMailDoris = new MMailDoris();
//                mMailDoris.setId(mail.getId());
//                mMailDoris.setFkMailAccountId(mail.getFkMailAccountId());
//                mMailDoris.setStar(mail.isStar());
//                mMailDoris.setDate(mail.getDate());
//                mMailDoris.setBodyText(mail.getBodyText());
//                mMailDoris.setSubject(mail.getSubject());
//                mMailDoris.setFromMail(mail.getFromMail());
//                mMailDoris.setLlmMailType(mail.getLlmMailType());
//                mMailDoris.setFoldBox(mail.getFoldBox());
//                mailDorisMapper.insertDoris(mMailDoris);

                MMailLLMAnalysis mMailLLMAnalysis = new MMailLLMAnalysis();
                mMailLLMAnalysis.setFkMailId(mail.getId());
                mMailLLMAnalysis.setLlmName("ollama-deepseek-r1:32b");
//                mMailLLMAnalysis.setLlmName("tongyi");
                mMailLLMAnalysis.setLlmMailType(lLMResult);
                mMailLLMAnalysis.setLlmMailAnalysis(descriptionPart);
                mMailLLMAnalysis.setGmtCreate(LocalDateTime.now());
                mailLLMAnalysisMapper.insert(mMailLLMAnalysis);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("{}分析失败", mail.getMailId());
        }
    }
}
