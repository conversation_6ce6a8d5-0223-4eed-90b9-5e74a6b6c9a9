package com.get.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.time.LocalDateTime;

@Data
@TableName("m_mail_llm_analysis")
public class MMailLLMAnalysis {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "唯一主键")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "用户邮件Id")
    @Column(name = "fk_mail_id")
    private Long fkMailId;

    @ApiModelProperty(value = "大模型名称")
    @Column(name = "llm_name")
    private String llmName;

    @ApiModelProperty(value = "邮件分类状态 0、未分析; 1、新申请; 2、提交完成; 3、已录取; 4、已付学费; 5、收到签证涵; 6、获得签证; 7、其他")
    @Column(name = "llm_mail_type")
    private Integer llmMailType;

    @ApiModelProperty(value = "邮件大模型分析结果")
    @Column(name = "llm_mail_analysis")
    private String llmMailAnalysis;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "gmt_create")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "创建用户(登录账号)")
    @Column(name = "gmt_create_user")
    private String gmtCreateUser;

    @ApiModelProperty(value = "修改时间")
    @Column(name = "gmt_modified")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "修改用户(登录账号)")
    @Column(name = "gmt_modified_user")
    private String gmtModifiedUser;

}
