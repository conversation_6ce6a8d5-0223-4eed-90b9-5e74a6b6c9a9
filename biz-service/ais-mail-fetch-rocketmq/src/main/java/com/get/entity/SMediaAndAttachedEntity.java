package com.get.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.time.LocalDateTime;

@Data
@TableName("s_media_and_attached")
public class SMediaAndAttachedEntity {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "媒体附件Id")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "文件guid(文档中心)")
    @Column(name = "fk_file_guid")
    private String fkFileGuid;

    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;

    @ApiModelProperty(value = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    @Column(name = "type_key")
    private String typeKey;

    @ApiModelProperty(value = "索引值(默认从0开始，同一类型下值唯一)")
    @Column(name = "index_key")
    private Integer indexKey;

    @ApiModelProperty(value = "链接")
    @Column(name = "link")
    private String link;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "gmt_create")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "创建用户(登录账号)")
    @Column(name = "gmt_create_user")
    private String gmtCreateUser;

    @ApiModelProperty(value = "修改时间")
    @Column(name = "gmt_modified")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "修改用户(登录账号)")
    @Column(name = "gmt_modified_user")
    private String gmtModifiedUser;
}
