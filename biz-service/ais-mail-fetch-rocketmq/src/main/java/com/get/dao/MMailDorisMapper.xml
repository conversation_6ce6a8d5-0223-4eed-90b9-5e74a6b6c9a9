<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.dao.MMailDorisMapper">
    <select id="selectMailDoris" resultType="Long">
        SELECT
        mail.id AS id
        FROM m_mail AS mail
        LEFT JOIN (SELECT * FROM m_mail_sync_queue AS qu1
        WHERE qu1.operation_type = 3
        AND qu1.gmt_create = (
        SELECT MAX(gmt_create)
        FROM m_mail_sync_queue AS qu2
        WHERE qu2.fk_mail_id = qu1.fk_mail_id
        AND qu2.operation_type = 3
        )
        ORDER BY qu1.gmt_create DESC) AS qu ON mail.id = qu.fk_mail_id
        <where>
            mail.fk_mail_account_id = #{searchMailVo.mailAccountId}
            <if test="searchMailVo.boxName != null and searchMailVo.boxName != '' ">
                AND (
                (qu.operation_type IS NOT NULL AND qu.operation_type != ''
                AND qu.operation_value = #{searchMailVo.boxName})
                OR
                (qu.operation_type IS NULL AND mail.fold_box = #{searchMailVo.boxName})
                )
            </if>
            <if test="searchMailVo.isStart != null and searchMailVo.isStart != '' ">
                AND mail.is_star = #{searchMailVo.isStart}
            </if>
            <if test="searchMailVo.dateSort != null and searchMailVo.dateSort != '' and searchMailVo.dateSort == 1">
                AND DATE(mail.date) = #{searchMailVo.today}
            </if>
            <if test="searchMailVo.dateSort != null and searchMailVo.dateSort != '' and searchMailVo.dateSort == 2">
                AND DATE(mail.date) = #{searchMailVo.yesterday}
            </if>
            <if test="searchMailVo.dateSort != null and searchMailVo.dateSort != '' and searchMailVo.dateSort == 3">
                AND DATE(mail.date) &lt; #{searchMailVo.yesterday}
            </if>
            <if test="searchMailVo.searchContent != null and searchMailVo.searchContent != '' ">
                AND(
                mail.body LIKE concat ('%',#{searchMailVo.searchContent},'%')
                OR mail.subject LIKE concat ('%',#{searchMailVo.searchContent},'%')
                OR mail.from_mail LIKE concat ('%',#{searchMailVo.searchContent},'%')
                )
            </if>
            <if test="searchMailVo.llmMailType != null and searchMailVo.llmMailType != '' ">
                AND mail.llm_mail_type = #{searchMailVo.llmMailType}
            </if>
            ORDER BY mail.date Desc
        </where>
    </select>

    <select id="deleteMailDoris">
        delete
            from m_mail
        where m_mail.id = #{id}
    </select>

    <insert id="insertDoris" parameterType="com.get.entity.MMailDoris">
        INSERT INTO m_mail
        (id, fk_platform_code, fk_platform_user_id, fk_mail_account_id, mail_id, fold_box, subject, body, body_text, from_mail, to_mail, cc_mail, bcc_mail, is_separately, date, llm_mail_type, is_read, is_star, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user)
        VALUES
            (#{id}, #{fkPlatformCode}, #{fkPlatformUserId}, #{fkMailAccountId}, #{mailId}, #{foldBox}, #{subject}, #{body}, #{bodyText}, #{fromMail}, #{toMail}, #{ccMail}, #{bccMail}, #{isSeparately}, #{date}, #{llmMailType}, #{isRead}, #{isStar}, #{gmtCreate}, #{gmtCreateUser}, #{gmtModified}, #{gmtModifiedUser})
    </insert>

    <select id="selectAllIds" resultType="java.lang.Long">
        SELECT id
        FROM m_mail
    </select>

    <!-- 根据日期选择所有邮件ID -->
    <select id="selectIdsByDate" resultType="long">
        SELECT id
        FROM m_mail
        WHERE DATE(m_mail.date) = #{date}
    </select>
</mapper>