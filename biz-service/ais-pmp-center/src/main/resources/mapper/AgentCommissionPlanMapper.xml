<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.AgentCommissionPlanMapper">

    <select id="selectByFkAgentCommissionTypeId" resultType="com.get.pmpcenter.entity.AgentCommissionPlan">
        select id, fk_agent_commission_type_id
        from ais_pmp2_center.m_agent_commission_plan
        where fk_agent_commission_type_id = #{id}
    </select>


    <select id="selectAgentCommissionPlan" resultType="com.get.pmpcenter.vo.agent.AgentCommissionPlanListVo">
        SELECT *
        FROM (
        SELECT t.*
        FROM (
        <if test="param.status == null or (param.status &gt;= 0 and param.status &lt;= 4)">
            SELECT
            1 AS source,
            p.id,
            p.fk_institution_provider_id AS fkInstitutionProviderId,
            p.fk_agent_commission_type_id AS fkAgentCommissionTypeId,
            p.fk_institution_provider_commission_plan_id AS fkInstitutionProviderCommissionPlanId,
            p.`name`,
            p.remark,
            p.start_time AS startTime,
            p.end_time AS endTime,
            p.is_gobal AS isGobal,
            p.is_timeless AS isTimeless,
            p.is_main AS isMain,
            p.is_active AS isActive,
            ct.type_name AS agentCommissionTypeName,
            p.gmt_create_user AS gmtCreateUser,
            p.gmt_create AS gmtCreate,
            pcp.`name` AS extendTemplateName,
            p.is_locked AS isLocked,
            p.is_institution_provider_commission_modify AS isInstitutionProviderCommissionModify,
            p.is_locked AS lockPermission,
            p.approval_status AS approvalStatus,
            p.approval_status AS approvalPermission,
            (
            SELECT GROUP_CONCAT(cpi.fk_institution_id)
            FROM r_agent_commission_plan_institution cpi
            WHERE cpi.fk_agent_commission_plan_id = p.id
            ) AS institutionIds,
            (
            SELECT cpc.fk_company_id
            FROM r_agent_commission_plan_company cpc
            WHERE cpc.fk_agent_commission_plan_id = p.id
            LIMIT 1
            ) AS companyId
            FROM m_agent_commission_plan p
            LEFT JOIN u_agent_commission_type ct ON ct.id = p.fk_agent_commission_type_id
            LEFT JOIN m_institution_provider_commission_plan pcp ON pcp.id =
            p.fk_institution_provider_commission_plan_id
            <where>
                p.id in
                <foreach collection="agentPlanIds" item="agentPlanId" open="(" separator="," close=")">
                    #{agentPlanId}
                </foreach>
                and EXISTS (
                SELECT 1
                FROM r_agent_commission_plan_institution cpi
                WHERE p.id = cpi.fk_agent_commission_plan_id
                AND cpi.fk_institution_id IN
                <foreach collection="allInstitutionIds" item="institutionId" open="(" separator="," close=")">
                    #{institutionId}
                </foreach>
                )
                <if test="param.institutionProviderId != null and param.institutionProviderId > 0">
                    AND p.fk_institution_provider_id =
                    #{param.institutionProviderId}
                </if>
                <if test="param.isActive != null">
                    AND p.is_active =
                    #{param.isActive}
                </if>
                <if test="param.agentCommissionTypeId != null and param.agentCommissionTypeId > 0">
                    AND p.fk_agent_commission_type_id =
                    #{param.agentCommissionTypeId}
                </if>
                <if test="param.agentCommissionTypeId != null and param.agentCommissionTypeId == 0">
                    AND
                    (p.fk_agent_commission_type_id = 0 or p.fk_agent_commission_type_id IS NULL)
                </if>
                <if test="param.status != null and param.status >= 0 and param.status &lt;= 3">
                    AND p.approval_status =
                    #{param.status}
                </if>
                <if test="param.status != null and param.status == 4">
                    AND p.is_institution_provider_commission_modify = 1
                </if>
                <if test="param.institutionName != null and param.institutionName != '' ">
                    AND EXISTS (
                            SELECT 1 FROM r_agent_commission_plan_institution cpi
                            JOIN ais_institution_center.m_institution i ON i.id = cpi.fk_institution_id WHERE cpi.fk_agent_commission_plan_id = p.id
                              AND (i.name LIKE CONCAT('%',
                    #{param.institutionName},
                    '%'
                    )
                    OR
                    i
                    .
                    name_chn
                    LIKE
                    CONCAT
                    (
                    '%',
                    #{param.institutionName},
                    '%'
                    )
                    )
                    )
                </if>
                <if test="param.keyword != null and param.keyword != '' ">
                    AND
                    (
                        (EXISTS (
                            SELECT 1 FROM r_agent_commission_plan_institution cpi
                            JOIN ais_institution_center.m_institution i ON i.id = cpi.fk_institution_id WHERE cpi.fk_agent_commission_plan_id = p.id
                            AND (i.name LIKE CONCAT('%',#{param.keyword},'%') OR i.name_chn LIKE CONCAT('%',#{param.keyword},'%'))))
                            or (p.name LIKE CONCAT('%', #{param.keyword},'%'))
                            or exists (select 1 from ais_institution_center.m_institution_provider mip where mip.id = p.fk_institution_provider_id
                            and (mip.name LIKE CONCAT('%', #{param.keyword},'%') or mip.name_chn LIKE CONCAT('%', #{param.keyword},'%'))))
                </if>
                <if test="countryInstitutionIds != null and countryInstitutionIds.size() > 0">
                    AND EXISTS (
                    SELECT 1
                    FROM r_agent_commission_plan_institution cpi
                    WHERE p.id = cpi.fk_agent_commission_plan_id
                    AND cpi.fk_institution_id IN
                    <foreach collection="countryInstitutionIds" item="institutionId" open="(" separator="," close=")">
                        #{institutionId}
                    </foreach>
                    )
                </if>
                <if test="param.planName != null and param.planName != '' ">
                    AND p.name LIKE CONCAT('%',
                    #{param.planName},
                    '%'
                    )
                </if>
                <if test="param.startTime != null and param.endTime != null ">
                    AND
                    (
                        (p.is_timeless = 1 AND p.start_time &lt;= STR_TO_DATE(#{param.endTime}, '%Y-%m-%d'))
                            OR (
                            p.is_timeless = 0
                            AND p.start_time &lt;= STR_TO_DATE(#{param.endTime}, '%Y-%m-%d')
                            AND p.end_time &gt;= STR_TO_DATE(#{param.startTime}, '%Y-%m-%d')))
                </if>
                <!--学校类型-->
                <if test="param.institutionTypeId != null and param.institutionTypeId > 0 ">
                    and exists (
                    select 1 from ais_pmp2_center.m_agent_commission_plan cp
                    inner join ais_pmp2_center.r_agent_commission_plan_institution cpi on cpi.fk_agent_commission_plan_id = cp.id
                    inner join ais_institution_center.m_institution mi on mi.id = cpi.fk_institution_id
                    where cp.id = p.id and  mi.fk_institution_type_id =
                    #{param.institutionTypeId}
                    )
                </if>
                <!-- 审批记录包含指定审核人-->
                <if test="param.staffId != null and param.staffId > 0">
                    and exists (
                    select 1 from ais_pmp2_center.m_agent_commission_plan cp
                    inner join ais_pmp2_center.m_agent_commission_plan_approval cpa on cp.id = cpa.fk_agent_commission_plan_id
                    where cp.id = p.id and  cpa.approval_status in (2,3) and cpa.fk_staff_id =
                    #{param.staffId}
                    )
                </if>
                <!-- 最新审批记录等于指定审核人-->
                <!--                <if test="param.staffId != null and param.staffId > 0">-->
                <!--                    AND EXISTS (SELECT 1 FROM m_agent_commission_plan_approval pa WHERE pa.fk_agent_commission_plan_id = p.id-->
                <!--                    AND pa.fk_staff_id = #{param.staffId}-->
                <!--                    AND pa.approval_status IN (2, 3)-->
                <!--                    AND NOT EXISTS (-->
                <!--                    SELECT 1-->
                <!--                    FROM m_agent_commission_plan_approval pa2-->
                <!--                    WHERE pa2.fk_agent_commission_plan_id = pa.fk_agent_commission_plan_id-->
                <!--                    AND pa2.approval_time  &gt;  pa.approval_time)-->
                <!--                    )-->
                <!--                </if>-->
            </where>
            <if test="param.status == null
                    and (userPermissionPlanIds != null and userPermissionPlanIds.size() > 0)
                    and (param.staffId == null or param.staffId == 0)">
                UNION ALL
            </if>
        </if>

        <if test="(param.status == null or param.status == -1)
                 and (userPermissionPlanIds != null and userPermissionPlanIds.size() > 0)
                 and (param.staffId == null or param.staffId == 0)">
            SELECT
            2 AS source,
            NULL AS id,
            ipcp.fk_institution_provider_id AS fkInstitutionProviderId,
            NULL AS fkAgentCommissionTypeId,
            ipcp.id AS fkInstitutionProviderCommissionPlanId,
            ipcp.`name`,
            ipcp.remark,
            ipcp.start_time AS startTime,
            ipcp.end_time AS endTime,
            ipcp.is_gobal AS isGobal,
            ipcp.is_timeless AS isTimeless,
            NULL AS isMain,
            0 AS isActive,
            NULL AS agentCommissionTypeName,
            ipcp.gmt_create_user AS gmtCreateUser,
            ipcp.gmt_create AS gmtCreate,
            ipcp.`name` AS extendTemplateName,
            NULL AS isLocked,
            NULL AS isInstitutionProviderCommissionModify,
            NULL AS lockPermission,
            NULL AS approvalStatus,
            NULL AS approvalPermission,
            (
            SELECT GROUP_CONCAT(pcpi.fk_institution_id)
            FROM r_institution_provider_commission_plan_institution pcpi
            WHERE pcpi.fk_institution_provider_commission_plan_id = ipcp.id
            ) AS institutionIds,
            NULL AS companyId
            FROM m_institution_provider_commission_plan ipcp
            <where>
                ipcp.id in
                <foreach collection="contractPlanIds" item="contractPlanId" open="(" separator="," close=")">
                    #{contractPlanId}
                </foreach>
                and ipcp.is_active = 1 and ipcp.approval_status = 2
                AND EXISTS (
                SELECT 1
                FROM r_institution_provider_commission_plan_institution pcpi
                WHERE ipcp.id = pcpi.fk_institution_provider_commission_plan_id
                AND pcpi.fk_institution_id IN
                <foreach collection="allInstitutionIds" item="institutionId" open="(" separator="," close=")">
                    #{institutionId}
                </foreach>
                )
                AND NOT EXISTS (
                SELECT 1
                FROM m_agent_commission_plan acp
                WHERE acp.fk_institution_provider_commission_plan_id = ipcp.id
                AND acp.is_gobal = 1 and EXISTS (
                SELECT 1
                FROM r_agent_commission_plan_company cpc
                WHERE cpc.fk_agent_commission_plan_id = acp.id and cpc.fk_company_id = #{param.companyId}
                )
                )
                AND EXISTS (
                SELECT 1
                FROM m_institution_provider_commission ipc
                WHERE ipc.fk_institution_provider_commission_plan_id = ipcp.id)
                and ipcp.id in
                <foreach collection="userPermissionPlanIds" item="userPermissionPlanId" open="(" separator=","
                         close=")">
                    #{userPermissionPlanId}
                </foreach>
                <if test="param.institutionProviderId != null and param.institutionProviderId > 0">
                    AND ipcp.fk_institution_provider_id =
                    #{param.institutionProviderId}
                </if>
                <if test="param.agentCommissionTypeId != null">
                    AND ipcp.id = 0
                </if>
                <if test="param.isActive != null and param.isActive != 0">
                    AND ipcp.id = 0
                </if>
                <if test="param.institutionName != null and param.institutionName != '' ">
                    AND EXISTS (
                            SELECT 1
                            FROM r_institution_provider_commission_plan_institution pcpi
                            JOIN ais_institution_center.m_institution i ON i.id = pcpi.fk_institution_id
                            WHERE pcpi.fk_institution_provider_commission_plan_id = ipcp.id
                              AND (i.name LIKE CONCAT('%',
                    #{param.institutionName},
                    '%'
                    )
                    OR
                    i
                    .
                    name_chn
                    LIKE
                    CONCAT
                    (
                    '%',
                    #{param.institutionName},
                    '%'
                    )
                    )
                    )
                </if>
                <if test="expirePlanIds != null and expirePlanIds.size() > 0">
                    AND ipcp.id NOT IN
                    <foreach collection="expirePlanIds" item="planId" open="(" separator="," close=")">
                        #{planId}
                    </foreach>
                </if>
                <if test="countryInstitutionIds != null and countryInstitutionIds.size() > 0">
                    AND EXISTS (
                    SELECT 1
                    FROM r_institution_provider_commission_plan_institution pcpi
                    WHERE ipcp.id = pcpi.fk_institution_provider_commission_plan_id
                    AND pcpi.fk_institution_id IN
                    <foreach collection="countryInstitutionIds" item="institutionId" open="(" separator="," close=")">
                        #{institutionId}
                    </foreach>
                    )
                </if>
                <if test="param.planName != null and param.planName != '' ">
                    AND ipcp.name LIKE CONCAT('%',
                    #{param.planName},
                    '%'
                    )
                </if>
                <if test="param.startTime != null and param.endTime != null ">
                    AND
                    (
                        (ipcp.is_timeless = 1 AND ipcp.start_time &lt;= STR_TO_DATE(#{param.endTime}, '%Y-%m-%d'))
                            OR (
                            ipcp.is_timeless = 0
                            AND ipcp.start_time &lt;= STR_TO_DATE(#{param.endTime}, '%Y-%m-%d')
                            AND ipcp.end_time &gt;= STR_TO_DATE(#{param.startTime}, '%Y-%m-%d')))
                </if>
                <if test="param.keyword != null and param.keyword != '' ">
                    AND
                    (
                        (EXISTS (
                            SELECT 1 FROM r_institution_provider_commission_plan_institution cpi
                            JOIN ais_institution_center.m_institution i ON i.id = cpi.fk_institution_id WHERE cpi.fk_institution_provider_commission_plan_id = ipcp.id
                            AND (i.name LIKE CONCAT('%',#{param.keyword},'%') OR i.name_chn LIKE CONCAT('%',#{param.keyword},'%'))))
                            or (ipcp.name LIKE CONCAT('%', #{param.keyword},'%'))
                            or exists (select 1 from ais_institution_center.m_institution_provider mip where mip.id = ipcp.fk_institution_provider_id
                            and (mip.name LIKE CONCAT('%', #{param.keyword},'%') or mip.name_chn LIKE CONCAT('%', #{param.keyword},'%'))))
                </if>
                <!--学校类型-->
                <if test="param.institutionTypeId != null and param.institutionTypeId > 0 ">
                    and exists (
                    select 1 from ais_pmp2_center.m_institution_provider_commission_plan cp
                    inner join ais_pmp2_center.r_institution_provider_commission_plan_institution cpi on cpi.fk_institution_provider_commission_plan_id = cp.id
                    inner join ais_institution_center.m_institution mi on mi.id = cpi.fk_institution_id
                    where cp.id = ipcp.id and  mi.fk_institution_type_id =
                    #{param.institutionTypeId}
                    )
                </if>
            </where>
        </if>
        ) t
        WHERE t.id IS NULL
        OR EXISTS (
        SELECT 1
        FROM r_agent_commission_plan_company cpc
        WHERE cpc.fk_agent_commission_plan_id = t.id
        AND cpc.fk_company_id = #{param.companyId}
        )
        ) a
        ORDER BY
        a.source ASC,
        CASE
        WHEN a.source = 1 THEN
        CASE a.approvalStatus
        WHEN 3 THEN 0
        WHEN 0 THEN 1
        WHEN 1 THEN 2
        WHEN 2 THEN 3
        ELSE 4
        END
        ELSE 0
        END ASC,
        a.gmtCreate DESC
    </select>


    <select id="selectTimeOverlapPlans" resultType="com.get.pmpcenter.entity.AgentCommissionPlan">
        SELECT p.*,
        t.type_name AS agentCommissionTypeName
        FROM m_agent_commission_plan p
        left join u_agent_commission_type t on p.fk_agent_commission_type_id = t.id
        <where>
            p.fk_institution_provider_id = #{param.institutionProviderId}
            AND EXISTS (
            SELECT 1 FROM r_agent_commission_plan_company cpc
            WHERE cpc.fk_agent_commission_plan_id = p.id
            AND cpc.fk_company_id = #{param.companyId}
            )
            AND EXISTS (
            SELECT 1 FROM r_agent_commission_plan_institution cpi
            WHERE cpi.fk_agent_commission_plan_id = p.id
            AND cpi.fk_institution_id IN
            <foreach collection="param.institutionIds" item="institutionId" open="(" separator="," close=")">
                #{institutionId}
            </foreach>
            )
            <if test="param.fkAgentCommissionTypeId != null and param.fkAgentCommissionTypeId > 0 ">
                AND p.fk_agent_commission_type_id =
                #{param.fkAgentCommissionTypeId}
            </if>
            <choose>
                <when test="param.isTimeless == 1">
                    AND (
                    (p.is_timeless = 1 AND #{param.isTimeless} = 1)
                    OR
                    (p.is_timeless = 0 AND p.end_time &gt;= #{param.startTime})
                    )
                </when>

                <when test="param.isTimeless == 0">
                    AND (
                    (p.is_timeless = 1 AND #{param.isTimeless} = 0 AND p.start_time &lt;= #{param.endTime})
                    OR
                    (p.is_timeless = 0
                    AND p.start_time &lt;= #{param.endTime}
                    AND p.end_time &gt;= #{param.startTime})
                    )
                </when>
            </choose>
        </where>
    </select>

    <select id="getAllProviderIds" resultType="java.lang.Long">
        SELECT DISTINCT fk_institution_provider_id
        FROM (SELECT fk_institution_provider_id
              FROM m_agent_commission_plan
              UNION
              SELECT fk_institution_provider_id
              FROM m_institution_provider_commission_plan
              WHERE approval_status = 2) AS combined_ids;

    </select>


    <select id="selectPlanAndCommissionTypeNameByIds"
            resultType="com.get.pmpcenter.entity.AgentCommissionPlan">
        SELECT p.*,
        t.type_name AS agentCommissionTypeName
        FROM m_agent_commission_plan p
        left join u_agent_commission_type t on p.fk_agent_commission_type_id = t.id
        WHERE p.id IN
        <foreach item="item" collection="ids" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        order by p.is_active desc,p.view_order desc,p.gmt_modified desc
    </select>

</mapper>