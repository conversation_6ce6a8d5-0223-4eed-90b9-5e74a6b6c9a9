<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.AgentCommissionPlanInstitutionMapper">

    <select id="getAgentPlanInstitution" resultType="com.get.pmpcenter.vo.common.AgentPlanInstitutionVo">
        SELECT p.name   as planName,
               pro.name as provideName
        from m_agent_commission_plan p
                 inner join r_agent_commission_plan_institution pi on p.id = pi.fk_agent_commission_plan_id
                 left join ais_institution_center.m_institution_provider pro on pro.id = p.fk_institution_provider_id
        where pi.fk_institution_id = #{institutionId}
    </select>
</mapper>