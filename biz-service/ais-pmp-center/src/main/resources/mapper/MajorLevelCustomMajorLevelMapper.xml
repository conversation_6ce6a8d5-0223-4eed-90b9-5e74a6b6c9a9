<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.MajorLevelCustomMajorLevelMapper">

    <select id="selectMajorLevel" resultType="com.get.pmpcenter.vo.common.MajorLevelVo">
        SELECT
        c.id AS levelId,
        c.custom_name AS customName,
        c.custom_name_chn AS customNameChn,
        c.is_general AS isGeneral,
        c.view_order AS viewOrder,
        c.fk_major_level_custom_id_parent AS parentLevelId,
        p.custom_name AS parentLevelName,
        p.custom_name_chn AS parentLevelNameChn
        FROM u_major_level_custom c
        LEFT JOIN u_major_level_custom p ON c.fk_major_level_custom_id_parent = p.id
        where c.is_active = 1
        <if test="isGeneral != null">
            and c.is_general =
            #{isGeneral}
        </if>
        order by c.view_order desc
    </select>


    <select id="getCurrencyTypes" resultType="com.get.pmpcenter.vo.common.CurrencyTypeVo">
        select num, type_name
        from ais_finance_center.u_currency_type
        where find_in_set(#{type}, public_level)
        order by view_order desc
    </select>

</mapper>