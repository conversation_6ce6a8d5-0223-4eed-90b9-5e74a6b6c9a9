<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.InstitutionCenterMapper">

    <select id="institutionProviderList" resultType="com.get.pmpcenter.vo.institution.ProviderVo">
        select t.id as institutionProviderId,
        t.name as name,
        t.name_chn as nameChn
        from m_institution_provider t
        where t.is_active = 1
        and t.id &gt; 1
        and exists (select 1
        from r_institution_provider_company c
        where c.fk_company_id in (
        <foreach collection="companyIds" item="companyId" separator=",">
            #{companyId}
        </foreach>
        )
        and c.fk_institution_provider_id = t.id)
        and exists (select 1
        from r_institution_provider_area_country ac
        where ac.fk_institution_provider_id = t.id
        and ac.fk_area_country_id in (
        <foreach collection="countryIds" item="countryId" separator=",">
            #{countryId}
        </foreach>
        ))
        ORDER BY CONVERT(name USING gbk) ASC
    </select>

    <select id="institutionList" resultType="com.get.pmpcenter.vo.common.InstitutionVo">
        SELECT
        i.id AS institutionId,
        i.name AS name,
        i.name_chn AS nameChn,
        i.is_active,
        i.name_display AS nameDisplay,
        c.name AS countryName,
        c.name_chn AS countryNameChn
        FROM ais_institution_center.m_institution i
        LEFT JOIN ais_institution_center.u_area_country c ON i.fk_area_country_id = c.id
        INNER JOIN ais_institution_center.r_institution_provider_institution rpi ON i.id = rpi.fk_institution_id
        WHERE
        rpi.fk_institution_provider_id = #{institutionProviderId}
        AND rpi.is_active = 1
        <!--AND i.is_active = 1-->
        <if test="companyIds != null and !companyIds.isEmpty()">
            AND (
            <foreach item="country" collection="companyIds" separator=" OR ">
                FIND_IN_SET(#{country}, rpi.fk_company_ids)
            </foreach>
            )
        </if>
        ORDER BY CONVERT(i.name USING gbk) ASC
    </select>

    <select id="countryList" resultType="com.get.pmpcenter.vo.common.CountryVo">
        select c.id       as countryId,
               c.name     as name,
               c.name_chn as nameChn
        from u_area_country c
        where c.num not in ('N/A', 'GLB')
        order by c.view_order desc
    </select>

    <select id="queryCountryListByIds" resultType="com.get.pmpcenter.vo.common.CountryVo">
        SELECT
        c.id AS countryId,
        c.name,
        c.num,
        c.name_chn AS nameChn
        FROM ais_institution_center.u_area_country c
        where c.id in
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
        </foreach>
        order by c.view_order desc
    </select>

    <select id="getInstitutionIdsCountryIds" resultType="com.get.pmpcenter.vo.common.InstitutionVo">
        select id as institutionId,
        name as name,
        name_chn as nameChn
        FROM ais_institution_center.m_institution i
        <where>
            <if test="countryIds != null and !countryIds.isEmpty()">
                i.fk_area_country_id IN
                <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
                    #{countryId}
                </foreach>
            </if>
        </where>
<!--        where-->
<!--        i.is_active = 1-->
<!--        <if test="countryIds != null and !countryIds.isEmpty()">-->
<!--            AND i.fk_area_country_id in-->
<!--            <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">-->
<!--                #{countryId}-->
<!--            </foreach>-->
<!--        </if>-->
    </select>

    <select id="getInstitutionListByProviderIds" resultType="com.get.pmpcenter.vo.common.ProviderInstitutionVo">
        SELECT
        rpi.fk_institution_id AS institutionId,
        rpi.fk_institution_provider_id AS providerId
        FROM ais_institution_center.r_institution_provider_institution rpi
        INNER JOIN ais_institution_center.m_institution i ON rpi.fk_institution_id = i.id
        WHERE
        rpi.fk_institution_provider_id IN (
        <foreach collection="providerIds" item="providerId" separator=",">
            #{providerId}
        </foreach>
        )
        AND rpi.is_active = 1
        <!--AND i.is_active = 1-->
        <if test="companyIds != null and !companyIds.isEmpty()">
            AND (
            <foreach item="country" collection="companyIds" separator=" OR ">
                FIND_IN_SET(#{country}, rpi.fk_company_ids)
            </foreach>
            )
        </if>
    </select>

    <select id="getInstitutionListBycompanyIdsAndCountryIds"
            resultType="com.get.pmpcenter.vo.common.InstitutionVo">
        SELECT
        i.id AS institutionId,
        i.name AS name,
        i.name_chn AS nameChn,
        i.name_display AS nameDisplay,
        i.is_exist_website,
        i.website,
        i.is_active,
        c.name AS countryName,
        c.name_chn AS countryNameChn,
        uas.name AS areaStateName,
        uas.name_chn AS areaStateNameChn,
        uac.name AS cityName,
        uac.name_chn AS cityNameChn
        FROM ais_institution_center.m_institution i
        LEFT JOIN ais_institution_center.u_area_country c ON i.fk_area_country_id = c.id
        LEFT JOIN ais_institution_center.u_area_state uas ON i.fk_area_state_id = uas.id
        LEFT JOIN ais_institution_center.u_area_city uac ON i.fk_area_city_id = uac.id
        INNER JOIN ais_institution_center.r_institution_provider_institution rpi ON i.id = rpi.fk_institution_id
        WHERE
        rpi.fk_institution_provider_id = #{institutionProviderId}
        AND rpi.is_active = 1
        <!--AND i.is_active = 1-->
        AND i.fk_area_country_id in
        <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
            #{countryId}
        </foreach>
        <if test="companyIds != null and !companyIds.isEmpty()">
            AND (
            <foreach item="country" collection="companyIds" separator=" OR ">
                FIND_IN_SET(#{country}, rpi.fk_company_ids)
            </foreach>
            )
        </if>
        ORDER BY CONVERT(i.name USING gbk) ASC
    </select>

    <select id="getInstitutionListByIds" resultType="com.get.pmpcenter.vo.common.InstitutionVo">
        select id as institutionId,
        name as name,
        name_chn as nameChn,
        fk_area_country_id as countryId
        FROM ais_institution_center.m_institution i
        where
        <!--i.is_active = 1 and-->
        i.id in
        <foreach collection="institutionIds" item="institutionId" open="(" separator="," close=")">
            #{institutionId}
        </foreach>
        ORDER BY CONVERT(i.name USING gbk) ASC
    </select>

    <select id="getCountryListByInstitutionIds" resultType="com.get.pmpcenter.vo.common.CountryVo">
        select c.id AS countryId,
        c.name,
        c.num,
        c.name_chn AS nameChn
        FROM ais_institution_center.u_area_country c
        where c.id in(
        select distinct (fk_area_country_id) from ais_institution_center.m_institution i
        where
        <!--i.is_active = 1 and-->
        i.id in
        <foreach collection="institutionIds" item="institutionId" open="(" separator="," close=")">
            #{institutionId}
        </foreach>
        )
        order by c.view_order desc
    </select>

    <select id="getProviderNameAndCompanyNum" resultType="com.get.pmpcenter.vo.agent.TimeOverlapPlanVo">
        SELECT p.name AS institutionProviderName,
               c.num  AS companyNum
        FROM ais_institution_center.m_institution_provider p,
             ais_permission_center.m_company c
        WHERE p.id = #{providerId}
          AND c.id = #{companyId};
    </select>

    <select id="selectInstitutionListByProviderIdsAndCompanyIds"
            resultType="com.get.pmpcenter.vo.common.ProviderInstitutionVo">
        SELECT
        rpi.fk_institution_id AS institutionId,
        rpi.fk_institution_provider_id AS providerId
        FROM
        ais_institution_center.r_institution_provider_institution rpi
        INNER JOIN ais_institution_center.m_institution i ON rpi.fk_institution_id = i.id
        INNER JOIN (
        SELECT
        cpi.fk_institution_id,
        cp.fk_institution_provider_id
        FROM
        ais_pmp2_center.r_agent_commission_plan_institution cpi
        INNER JOIN ais_pmp2_center.m_agent_commission_plan cp ON cpi.fk_agent_commission_plan_id = cp.id
        ) r ON rpi.fk_institution_id = r.fk_institution_id
        AND rpi.fk_institution_provider_id = r.fk_institution_provider_id
        WHERE
        rpi.fk_institution_provider_id IN (
        <foreach collection="providerIds" item="providerId" separator=",">
            #{providerId}
        </foreach>
        )
        AND rpi.is_active = 1
        <!--AND i.is_active = 1-->
        <if test="companyIds != null and !companyIds.isEmpty()">
            AND (
            <foreach item="country" collection="companyIds" separator=" OR ">
                FIND_IN_SET(#{country}, rpi.fk_company_ids)
            </foreach>
            )
        </if>
    </select>


    <select id="selectProviderInstitutionByCompanyIds"
            resultType="com.get.pmpcenter.vo.common.ProviderInstitutionVo">
        SELECT rpi.fk_company_ids             as compyIds,
               rpi.fk_institution_id          AS institutionId,
               rpi.fk_institution_provider_id AS providerId,
               r.id                           as planId,
               r.fk_company_id                as companyId
        FROM ais_institution_center.r_institution_provider_institution rpi
                 INNER JOIN ais_institution_center.m_institution i ON rpi.fk_institution_id = i.id
                 INNER JOIN (SELECT cpi.fk_institution_id,
                                    cp.fk_institution_provider_id,
                                    cpc.fk_company_id,
                                    cp.id
                             FROM ais_pmp2_center.m_agent_commission_plan cp
                                      INNER JOIN ais_pmp2_center.r_agent_commission_plan_institution cpi
                                                 ON cpi.fk_agent_commission_plan_id = cp.id
                                      INNER JOIN ais_pmp2_center.r_agent_commission_plan_company cpc
                                                 on cpc.fk_agent_commission_plan_id = cp.id
                                                     AND cpc.fk_company_id = #{companyId}
                             ) r
                            ON rpi.fk_institution_id = r.fk_institution_id
                                AND rpi.fk_institution_provider_id = r.fk_institution_provider_id and(
                                    <foreach item="country" collection="companyIds" separator=" OR ">
                                        FIND_IN_SET(#{country}, rpi.fk_company_ids)
                                    </foreach>
                                   )
        GROUP BY institutionId, providerId,compyIds,planId,companyId
        having institutionId in
            <foreach item="institutionId" collection="institutionIds" open="(" separator="," close=")">
                #{institutionId}
            </foreach>
    </select>

    <select id="selectContractProviderInstitutionByCompanyIds"
            resultType="com.get.pmpcenter.vo.common.ProviderInstitutionVo">
        SELECT
            rpi.fk_company_ids as compyIds,
            rpi.fk_institution_id AS institutionId,
            rpi.fk_institution_provider_id AS providerId,
            r.id as planId
        FROM
            ais_institution_center.r_institution_provider_institution rpi
                INNER JOIN ais_institution_center.m_institution i ON rpi.fk_institution_id = i.id
                INNER JOIN (
                SELECT
                    cpi.fk_institution_id,
                    cp.fk_institution_provider_id,
                    cp.id
                FROM
                    ais_pmp2_center.m_institution_provider_commission_plan cp
                        INNER JOIN ais_pmp2_center.r_institution_provider_commission_plan_institution cpi
                                   ON cpi.fk_institution_provider_commission_plan_id = cp.id
            ) r ON rpi.fk_institution_id = r.fk_institution_id
                AND rpi.fk_institution_provider_id = r.fk_institution_provider_id and(
                        <foreach item="country" collection="companyIds" separator=" OR ">
                            FIND_IN_SET(#{country}, rpi.fk_company_ids)
                        </foreach>
                    )
        GROUP BY institutionId,providerId,compyIds,planId
        having institutionId in
            <foreach item="institutionId" collection="institutionIds" open="(" separator="," close=")">
                #{institutionId}
            </foreach>
    </select>

    <select id="selectInstitutionProviderListByIds" resultType="com.get.pmpcenter.vo.institution.ProviderVo">
        select t.id as institutionProviderId,
        t.name as name,
        t.name_chn as nameChn
        from m_institution_provider t
        where t.is_active = 1
        and t.id &gt; 1
        and exists (select 1
        from r_institution_provider_company c
        where c.fk_company_id in (
        <foreach collection="companyIds" item="companyId" separator=",">
            #{companyId}
        </foreach>
        )
        and c.fk_institution_provider_id = t.id)
        and exists (select 1
        from r_institution_provider_area_country ac
        where ac.fk_institution_provider_id = t.id
        and ac.fk_area_country_id in (
        <foreach collection="countryIds" item="countryId" separator=",">
            #{countryId}
        </foreach>
        ))
        and t.id in
        <foreach collection="providerIds" item="providerId" open="(" separator="," close=")">
            #{providerId}
        </foreach>
        ORDER BY CONVERT(name USING gbk) ASC
    </select>

    <select id="getInstitutionIdByName" resultType="com.get.pmpcenter.vo.common.InstitutionVo">
        select id as institutionId,
        name as name,
        name_chn as nameChn
        FROM ais_institution_center.m_institution i
        where i.is_active in (0,1)
        <if test="countryIds != null and !countryIds.isEmpty()">
            AND i.fk_area_country_id in
            <foreach collection="countryIds" item="countryId" open="(" separator="," close=")">
                #{countryId}
            </foreach>
        </if>
        <if test="institutionName != null and institutionName != '' ">
            AND
            ( i.name like concat('%',#{institutionName},'%')
                OR i.name_chn like concat('%',#{institutionName},'%'))
        </if>
    </select>

    <select id="selectPassProviderInstitution" resultType="com.get.pmpcenter.vo.common.ProviderInstitutionVo">
        SELECT rpi.fk_company_ids as compyIds,
        rpi.fk_institution_id AS institutionId,
        rpi.fk_institution_provider_id AS providerId,
        r.id as planId,
        i.name as institutionName
        FROM ais_institution_center.r_institution_provider_institution rpi
            INNER JOIN ais_institution_center.m_institution i ON rpi.fk_institution_id = i.id
                INNER JOIN (SELECT cpi.fk_institution_id,
                cp.fk_institution_provider_id,
                cp.id
                FROM ais_pmp2_center.m_agent_commission_plan cp
                INNER JOIN ais_pmp2_center.r_agent_commission_plan_institution cpi
                ON cpi.fk_agent_commission_plan_id = cp.id and cp.approval_status = 2
            ) r
            ON rpi.fk_institution_id = r.fk_institution_id
        AND rpi.fk_institution_provider_id = r.fk_institution_provider_id and(
        <foreach item="country" collection="companyIds" separator=" OR ">
            FIND_IN_SET(#{country}, rpi.fk_company_ids)
        </foreach>
        )
        GROUP BY institutionId, providerId,compyIds,planId
        having institutionId in
        <foreach item="institutionId" collection="institutionIds" open="(" separator="," close=")">
            #{institutionId}
        </foreach>
    </select>

    <select id="selectProviderByIds" resultType="com.get.pmpcenter.vo.institution.ProviderVo">
        SELECT p.id AS institutionProviderId,
        p.name AS name,
        p.name_chn AS nameChn
        FROM ais_institution_center.m_institution_provider p
        WHERE p.id IN
        <foreach item="providerId" collection="providerIds" open="(" separator="," close=")">
            #{providerId}
        </foreach>
    </select>

    <select id="institutionTypeList" resultType="com.get.pmpcenter.vo.common.InstitutionTypeVo">
        select id            AS institutionTypeId,
               type_name     AS typeName,
               type_name_chn AS typeNameChn,
               view_order    AS viewOrder
        FROM ais_institution_center.u_institution_type
        order by view_order desc
    </select>

    <select id="regionList" resultType="com.get.pmpcenter.vo.common.RegionVo">
        select id as regionId,
               name,
               name_chn,
               num
        from ais_institution_center.u_area_region
        where fk_area_country_id = 34
          and fk_company_id = -1
        order by view_order desc
    </select>

    <select id="queryRegionListByIds" resultType="com.get.pmpcenter.vo.common.RegionVo">
        select id as regionId,
        name,
        name_chn,
        num
        from ais_institution_center.u_area_region
        where fk_area_country_id = 34 and fk_company_id = -1
        and id in
        <foreach item="item" collection="regionIds" separator="," open="(" close=")" index="">
            #{item}
        </foreach>
        order by view_order desc
    </select>

    <select id="selectCountryAreaRegionIds" resultType="java.lang.String">
        select fk_area_region_ids
        from ais_institution_center.u_area_country
        where id = #{countryId}
    </select>
</mapper>