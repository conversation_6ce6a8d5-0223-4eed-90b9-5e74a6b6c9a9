<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.AgentCommissionPlanApprovalMapper">

    <select id="getAgentApprovalList" resultType="com.get.pmpcenter.entity.AgentCommissionPlanApproval">
        select a.*,
        s.name as staffName
        from m_agent_commission_plan_approval a
        left join ais_permission_center.m_staff s on a.fk_staff_id = s.id
        where a.fk_agent_commission_plan_id
        in
        <foreach collection="plaIds" item="plaId" open="(" separator="," close=")">
            #{plaId}
        </foreach>
        order by a.gmt_create desc
    </select>
</mapper>