<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.get.pmpcenter.mapper.AgentCommissionTypeAgentMapper">

    <select id="selectByAgentCommissionTypeId" resultType="com.get.pmpcenter.entity.AgentCommissionTypeAgent">
        select a.id,
               a.fk_agent_id                 AS fkAgentId,
               a.fk_agent_commission_type_id AS fkAgentCommissionTypeId,
               a.gmt_create_user             AS gmtCreateUser,
               a.gmt_create                  AS gmtCreate
        from ais_pmp2_center.r_agent_commission_type_agent a
        where fk_agent_commission_type_id = #{agentCommissionTypeId}
    </select>

    <delete id="deleteByAgentCommissionTypeId">
        delete from ais_pmp2_center.r_agent_commission_type_agent where fk_agent_commission_type_id = #{agentCommissionTypeId}
    </delete>

    <delete id="deleteByIds">
        delete from ais_pmp2_center.r_agent_commission_type_agent where id in
        <if test="ids != null and ids.size() > 0">
            <foreach collection="ids" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </delete>
</mapper>