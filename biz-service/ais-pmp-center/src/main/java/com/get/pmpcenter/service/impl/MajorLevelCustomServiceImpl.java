package com.get.pmpcenter.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.pmpcenter.entity.MajorLevelCustom;
import com.get.pmpcenter.mapper.MajorLevelCustomMapper;
import com.get.pmpcenter.service.MajorLevelCustomService;
import com.get.pmpcenter.vo.common.MajorLevelTreeVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class MajorLevelCustomServiceImpl extends ServiceImpl<MajorLevelCustomMapper, MajorLevelCustom> implements MajorLevelCustomService {

    @Autowired
    private MajorLevelCustomMapper customMapper;

    @Override
    public List<MajorLevelTreeVo> getMajorLevelTree() {
        //先找父级
        List<MajorLevelCustom> majorLevelCustoms = customMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                .eq(MajorLevelCustom::getIsActive, 1)
                .eq(MajorLevelCustom::getFkMajorLevelCustomIdParent, 0)
                .orderByDesc(MajorLevelCustom::getViewOrder)
                .orderByDesc(MajorLevelCustom::getGmtCreate));
        return majorLevelCustoms.stream().map(majorLevelCustom -> {
            MajorLevelTreeVo treeVo = new MajorLevelTreeVo();
            treeVo.setLevelId(majorLevelCustom.getId());
            treeVo.setLevelName(majorLevelCustom.getCustomName());
            treeVo.setLevelNameChn(majorLevelCustom.getCustomNameChn());
            treeVo.setIsGeneral(majorLevelCustom.getIsGeneral() ? 1 : 0);
            treeVo.setViewOrder(majorLevelCustom.getViewOrder());
            treeVo.setChildren(getChildren(majorLevelCustom.getId()));
            return treeVo;
        }).collect(Collectors.toList());
    }

    private List<MajorLevelTreeVo> getChildren(Long parentId) {
        List<MajorLevelCustom> majorLevelCustoms = customMapper.selectList(new LambdaQueryWrapper<MajorLevelCustom>()
                .eq(MajorLevelCustom::getIsActive, 1)
                .eq(MajorLevelCustom::getFkMajorLevelCustomIdParent, parentId)
                .orderByDesc(MajorLevelCustom::getViewOrder)
                .orderByDesc(MajorLevelCustom::getGmtCreate));
        if (CollectionUtils.isEmpty(majorLevelCustoms)) {
            return new ArrayList<>();
        }
        return majorLevelCustoms.stream().map(majorLevelCustom -> {
            MajorLevelTreeVo treeVo = new MajorLevelTreeVo();
            treeVo.setLevelId(majorLevelCustom.getId());
            treeVo.setLevelName(majorLevelCustom.getCustomName());
            treeVo.setLevelNameChn(majorLevelCustom.getCustomNameChn());
            treeVo.setIsGeneral(majorLevelCustom.getIsGeneral() ? 1 : 0);
            treeVo.setViewOrder(majorLevelCustom.getViewOrder());
            treeVo.setChildren(new ArrayList<>());
            return treeVo;
        }).collect(Collectors.toList());
    }
}
