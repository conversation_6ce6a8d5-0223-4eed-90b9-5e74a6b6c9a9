package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.pmpcenter.dto.agent.AgentCommissionPlanListDto;
import com.get.pmpcenter.dto.agent.TimeOverlapVerifyDto;
import com.get.pmpcenter.entity.AgentCommissionPlan;
import com.get.pmpcenter.vo.agent.AgentCommissionPlanListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AgentCommissionPlanMapper extends BaseMapper<AgentCommissionPlan> {
    /**
     * 根据佣金类型Id查询佣金方案
     *
     * @param id
     * @return
     */
    List<AgentCommissionPlan> selectByFkAgentCommissionTypeId(@Param("id") Long id);


    /**
     * 查询佣金方案列表
     *
     * @param page
     * @param param
     * @param expirePlanIds
     * @param allInstitutionIds
     * @return
     */

    List<AgentCommissionPlanListVo> selectAgentCommissionPlan(IPage<AgentCommissionPlanListVo> page, @Param("param") AgentCommissionPlanListDto param,
                                                              @Param("loginId") String loginId,
                                                              @Param("staffId") Long staffId,
                                                              @Param("expirePlanIds") List<Long> expirePlanIds,
                                                              @Param("allInstitutionIds") List<Long> allInstitutionIds,
                                                              @Param("countryInstitutionIds") List<Long> countryInstitutionIds,
                                                              @Param("userPermissionPlanIds") List<Long> userPermissionPlanIds,
                                                              @Param("companyIds") List<Long> companyIds,
                                                              @Param("companyId") Long companyId,
                                                              @Param("agentPlanIds") List<Long> agentPlanIds,
                                                              @Param("contractPlanIds") List<Long> contractPlanIds);

    /**
     * 查询时间冲突的佣金方案
     * @param param
     * @return
     */
    List<AgentCommissionPlan> selectTimeOverlapPlans(@Param("param") TimeOverlapVerifyDto param);

    /**
     * 查询所有学校提供商Id
     * @return
     */
    List<Long> getAllProviderIds();

    /**
     * 根据Id查询佣金方案和佣金分类名称
     * @param ids
     * @return
     */
    List<AgentCommissionPlan> selectPlanAndCommissionTypeNameByIds(@Param("ids") List<Long> ids);
}
