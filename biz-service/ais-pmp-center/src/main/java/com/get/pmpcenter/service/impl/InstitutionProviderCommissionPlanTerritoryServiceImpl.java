package com.get.pmpcenter.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.pmpcenter.dto.common.LogDto;
import com.get.pmpcenter.dto.institution.PlanTerritoryDto;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanTerritory;
import com.get.pmpcenter.enums.LogEventEnum;
import com.get.pmpcenter.enums.LogTableEnum;
import com.get.pmpcenter.enums.LogTypeEnum;
import com.get.pmpcenter.enums.TerritoryRuleEnum;
import com.get.pmpcenter.mapper.InstitutionProviderCommissionPlanTerritoryMapper;
import com.get.pmpcenter.service.InstitutionProviderCommissionPlanTerritoryService;
import com.get.pmpcenter.utils.GeneratorLogsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class InstitutionProviderCommissionPlanTerritoryServiceImpl extends ServiceImpl<InstitutionProviderCommissionPlanTerritoryMapper, InstitutionProviderCommissionPlanTerritory> implements InstitutionProviderCommissionPlanTerritoryService {

    @Autowired
    private InstitutionProviderCommissionPlanTerritoryMapper commissionPlanTerritoryMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<LogDto> saveProviderCommissionPlanTerritory(Long contractId, Long providerCommissionPlanId, List<Long> countryIds, Integer isInclude, Boolean saveLog) {
        List<LogDto> logs = new ArrayList<>();
        UserInfo user = SecureUtil.getUser();
        List<InstitutionProviderCommissionPlanTerritory> currentCountryList = commissionPlanTerritoryMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId));


        List<Long> currentList = currentCountryList.stream().map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId).collect(Collectors.toList());
        Set<Long> currentSet = new HashSet<>(currentList);
        Set<Long> newSet = new HashSet<>(countryIds);

        List<Long> toAdd = new ArrayList<>(newSet);
        List<InstitutionProviderCommissionPlanTerritory> toUpdate = new ArrayList<>();
        toAdd.removeAll(currentSet);

        //更新的
        if (CollectionUtils.isNotEmpty(currentCountryList)) {
            toUpdate = currentCountryList.stream()
                    .filter(item -> countryIds.contains(item.getFkAreaCountryId()))
                    .map(item -> {
                        item.setIsInclude(isInclude);
                        item.setGmtModifiedUser(user.getLoginId());
                        item.setGmtModified(new Date());
                        return item;
                    }).collect(Collectors.toList());
        }
        // 找出需要删除的
        List<Long> toRemove = new ArrayList<>(currentSet);
        toRemove.removeAll(newSet);
        if (CollectionUtils.isNotEmpty(toAdd)) {
            List<InstitutionProviderCommissionPlanTerritory> saveList = toAdd.stream().map(item -> {
                InstitutionProviderCommissionPlanTerritory territory = new InstitutionProviderCommissionPlanTerritory();
                territory.setFkAreaCountryId(item);
                territory.setFkInstitutionProviderCommissionPlanId(providerCommissionPlanId);
                territory.setIsInclude(isInclude);
                territory.setGmtCreateUser(user.getLoginId());
                territory.setGmtCreate(new Date());
                territory.setGmtModifiedUser(user.getLoginId());
                territory.setGmtModified(new Date());
                return territory;
            }).collect(Collectors.toList());
            this.saveBatch(saveList);
            if (saveLog) {
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT, LogTableEnum.PROVIDER_PLAN,
                        providerCommissionPlanId, LogEventEnum.ADD_TERRITORY, user.getLoginId(),
                        toAdd.size(), contractId));
            }
        }
        if (CollectionUtils.isNotEmpty(toUpdate)) {
            this.updateBatchById(toUpdate);
        }
        if (CollectionUtils.isNotEmpty(toRemove)) {
            this.remove(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                    .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                    .in(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId, toRemove));
            if (saveLog) {
                logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.PROVIDER_PLAN,
                        providerCommissionPlanId, LogEventEnum.DEL_TERRITORY, user.getLoginId(),
                        toRemove.size(), contractId));
            }
        }
        return logs;
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public List<LogDto> saveProviderCommissionPlanTerritory(Long contractId, Long providerCommissionPlanId, List<PlanTerritoryDto> territoryList) {
//        territoryList = validateTerritoryList(territoryList);
//        List<LogDto> logs = new ArrayList<>();
//        UserInfo user = SecureUtil.getUser();
//        List<Integer> delGroups = new ArrayList<>();
//        AtomicReference<Integer> saveCount = new AtomicReference<>(0);
//        AtomicReference<Integer> delCount = new AtomicReference<>(0);
//        List<InstitutionProviderCommissionPlanTerritory> allCurrentCountryList = commissionPlanTerritoryMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
//                .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId));
//        if (CollectionUtils.isNotEmpty(allCurrentCountryList)) {
//            Set<Integer> originalGroups = allCurrentCountryList.stream()
//                    .map(InstitutionProviderCommissionPlanTerritory::getIsInclude)
//                    .collect(Collectors.toSet());
//
//            Set<Integer> newGroups = territoryList.stream()
//                    .map(PlanTerritoryDto::getIsInclude)
//                    .collect(Collectors.toSet());
//
//            Set<Integer> removedGroups = new HashSet<>(originalGroups);
//            removedGroups.removeAll(newGroups);
//            removedGroups.stream().forEach(group -> {
//                Integer count = commissionPlanTerritoryMapper.selectCount(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
//                        .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
//                        .eq(InstitutionProviderCommissionPlanTerritory::getIsInclude, group));
//                delCount.updateAndGet(v -> v + count);
//            });
//            delGroups = new ArrayList<>(removedGroups);
//        }
//
//        //校验 1：包括\除外\未指定区域不能共存 2：每个规则下的国家不能重复
//        territoryList.stream().forEach(dto -> {
//            List<InstitutionProviderCommissionPlanTerritory> currentCountryList = commissionPlanTerritoryMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
//                    .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
//                    .eq(InstitutionProviderCommissionPlanTerritory::getIsInclude, dto.getIsInclude()));
//
//            List<Long> countryIds = dto.getCountryIds();
//            //AU onshore 和 UK onshore，不需要再选择适用国家。意思是只要是这2个属性的方案，意味都适用所有国家，类似Global
//            if (dto.getIsInclude().equals(TerritoryRuleEnum.ON_SHORE.getCode()) || dto.getIsInclude().equals(TerritoryRuleEnum.UK_SHORE.getCode())) {
//                countryIds = Arrays.asList(0L);
//            }
//            if (CollectionUtils.isEmpty(countryIds)) {
//                countryIds = Arrays.asList(0L);
//            }
//            List<Long> currentList = currentCountryList.stream().map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId).collect(Collectors.toList());
//            Set<Long> currentSet = new HashSet<>(currentList);
//            Set<Long> newSet = new HashSet<>(countryIds);
//
//            List<Long> toAdd = new ArrayList<>(newSet);
//            List<InstitutionProviderCommissionPlanTerritory> toUpdate = new ArrayList<>();
//            toAdd.removeAll(currentSet);
//
//            //更新的
//            if (CollectionUtils.isNotEmpty(currentCountryList)) {
//                List<Long> finalCountryIds = countryIds;
//                toUpdate = currentCountryList.stream()
//                        .filter(item -> finalCountryIds.contains(item.getFkAreaCountryId()))
//                        .map(item -> {
//                            item.setGmtModifiedUser(user.getLoginId());
//                            item.setGmtModified(new Date());
//                            return item;
//                        }).collect(Collectors.toList());
//            }
//            // 找出需要删除的
//            List<Long> toRemove = new ArrayList<>(currentSet);
//            toRemove.removeAll(newSet);
//            if (CollectionUtils.isNotEmpty(toAdd)) {
//                List<InstitutionProviderCommissionPlanTerritory> saveList = toAdd.stream().map(item -> {
//                    InstitutionProviderCommissionPlanTerritory territory = new InstitutionProviderCommissionPlanTerritory();
//                    territory.setFkAreaCountryId(item);
//                    territory.setFkInstitutionProviderCommissionPlanId(providerCommissionPlanId);
//                    territory.setIsInclude(dto.getIsInclude());
//                    territory.setGmtCreateUser(user.getLoginId());
//                    territory.setGmtCreate(new Date());
//                    territory.setGmtModifiedUser(user.getLoginId());
//                    territory.setGmtModified(new Date());
//                    return territory;
//                }).collect(Collectors.toList());
//                this.saveBatch(saveList);
//                saveCount.updateAndGet(v -> v + saveList.size());
//            }
//            if (CollectionUtils.isNotEmpty(toUpdate)) {
//                this.updateBatchById(toUpdate);
//            }
//            if (CollectionUtils.isNotEmpty(toRemove)) {
//                this.remove(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
//                        .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
//                        .eq(InstitutionProviderCommissionPlanTerritory::getIsInclude, dto.getIsInclude())
//                        .in(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId, toRemove));
//                delCount.updateAndGet(v -> v + toRemove.size());
//            }
//        });
//
//        if (saveCount.get() > 0) {
//            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT, LogTableEnum.PROVIDER_PLAN,
//                    providerCommissionPlanId, LogEventEnum.ADD_TERRITORY, user.getLoginId(),
//                    saveCount.get(), contractId));
//        }
//        if (delCount.get() > 0) {
//            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.PROVIDER_PLAN,
//                    providerCommissionPlanId, LogEventEnum.DEL_TERRITORY, user.getLoginId(),
//                    delCount.get(), contractId));
//        }
//        if (CollectionUtils.isNotEmpty(delGroups)) {
//            this.remove(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
//                    .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
//                    .in(InstitutionProviderCommissionPlanTerritory::getIsInclude, delGroups));
//        }
//        return logs;
//    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<LogDto> saveProviderCommissionPlanTerritory(Long contractId, Long providerCommissionPlanId, List<PlanTerritoryDto> territoryList) {
        // 先进行数据校验（包括大区和国家ID不能重复、规则冲突校验等）
        territoryList = validateTerritoryList(territoryList);

        List<LogDto> logs = new ArrayList<>(); // 用于返回日志记录
        UserInfo user = SecureUtil.getUser(); // 获取当前登录用户信息
        List<Integer> delGroups = new ArrayList<>(); // 记录哪些 isInclude 的分组被删除
        AtomicReference<Integer> saveCount = new AtomicReference<>(0); // 插入计数
        AtomicReference<Integer> delCount = new AtomicReference<>(0); // 删除计数

        // 查询当前这个佣金方案下的所有已保存的记录（无论是国家还是大区）
        List<InstitutionProviderCommissionPlanTerritory> allCurrentList = commissionPlanTerritoryMapper.selectList(
                new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                        .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
        );

        // 如果当前存在旧数据，找出本次没有继续提交的 isInclude 分组，做删除处理
        if (CollectionUtils.isNotEmpty(allCurrentList)) {
            // 原有的所有 isInclude 值
            Set<Integer> originalGroups = allCurrentList.stream()
                    .map(InstitutionProviderCommissionPlanTerritory::getIsInclude)
                    .collect(Collectors.toSet());

            // 新提交的 isInclude 值
            Set<Integer> newGroups = territoryList.stream()
                    .map(PlanTerritoryDto::getIsInclude)
                    .collect(Collectors.toSet());

            // 找出被移除的 isInclude 分组（旧的有，新的没有）
            Set<Integer> removedGroups = new HashSet<>(originalGroups);
            removedGroups.removeAll(newGroups);

            // 统计每个被移除的分组的记录数量
            removedGroups.forEach(group -> {
                int count = commissionPlanTerritoryMapper.selectCount(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                        .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                        .eq(InstitutionProviderCommissionPlanTerritory::getIsInclude, group));
                delCount.updateAndGet(v -> v + count);
            });

            delGroups = new ArrayList<>(removedGroups); // 保存到删除组
        }

        // 遍历每个 territory 分组（一个分组对应一个 isInclude 类型）
        for (PlanTerritoryDto dto : territoryList) {
            Integer isInclude = dto.getIsInclude();
            List<Long> countryIds = dto.getCountryIds(); // 国家 ID
            List<Long> regionIds = dto.getRegionIds();   // 大区 ID

            // 特殊处理 AU/UK Onshore：强制设置为 0（表示适用所有国家/大区）
            if (isInclude.equals(TerritoryRuleEnum.ON_SHORE.getCode()) || isInclude.equals(TerritoryRuleEnum.UK_SHORE.getCode())) {
                countryIds = Collections.singletonList(0L);
                regionIds = Collections.singletonList(0L);
            }

            if (CollectionUtils.isEmpty(countryIds)) {
                countryIds = Collections.singletonList(0L); // 默认国家为 0 表示不限
            }
            if (CollectionUtils.isEmpty(regionIds)) {
                regionIds = Collections.singletonList(0L); // 默认大区为 0 表示不限
            }

            // 查询当前 isInclude 下已有的国家/大区
            List<InstitutionProviderCommissionPlanTerritory> currentList = commissionPlanTerritoryMapper.selectList(
                    new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                            .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                            .eq(InstitutionProviderCommissionPlanTerritory::getIsInclude, isInclude)
            );

            // 当前已存在的国家/大区 ID 集合（0也要保留）
            Set<Long> currentCountrySet = currentList.stream()
                    .filter(item -> item.getFkAreaCountryId() != null)
                    .map(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId)
                    .collect(Collectors.toSet());

            Set<Long> currentRegionSet = currentList.stream()
                    .filter(item -> item.getFkAreaRegionId() != null)
                    .map(InstitutionProviderCommissionPlanTerritory::getFkAreaRegionId)
                    .collect(Collectors.toSet());

            // 去重后的新提交的国家/大区 ID 集合（包含 0）
            Set<Long> newCountrySet = new HashSet<>(countryIds);
            Set<Long> newRegionSet = new HashSet<>(regionIds);

            // 插入国家
            List<Long> toAddCountries = new ArrayList<>(newCountrySet);
            toAddCountries.removeAll(currentCountrySet);
            if (CollectionUtils.isNotEmpty(toAddCountries)) {
                List<InstitutionProviderCommissionPlanTerritory> saveList = toAddCountries.stream().map(id -> {
                    InstitutionProviderCommissionPlanTerritory territory = new InstitutionProviderCommissionPlanTerritory();
                    territory.setFkAreaCountryId(id);
                    territory.setFkInstitutionProviderCommissionPlanId(providerCommissionPlanId);
                    territory.setIsInclude(isInclude);
                    territory.setGmtCreateUser(user.getLoginId());
                    territory.setGmtCreate(new Date());
                    territory.setGmtModifiedUser(user.getLoginId());
                    territory.setGmtModified(new Date());
                    return territory;
                }).collect(Collectors.toList());
                this.saveBatch(saveList);
                saveCount.updateAndGet(v -> v + saveList.size());
            }

            // 插入大区
            List<Long> toAddRegions = new ArrayList<>(newRegionSet);
            toAddRegions.removeAll(currentRegionSet);
            if (CollectionUtils.isNotEmpty(toAddRegions)) {
                List<InstitutionProviderCommissionPlanTerritory> saveList = toAddRegions.stream().map(id -> {
                    InstitutionProviderCommissionPlanTerritory territory = new InstitutionProviderCommissionPlanTerritory();
                    territory.setFkAreaRegionId(id);
                    territory.setFkInstitutionProviderCommissionPlanId(providerCommissionPlanId);
                    territory.setIsInclude(isInclude);
                    territory.setGmtCreateUser(user.getLoginId());
                    territory.setGmtCreate(new Date());
                    territory.setGmtModifiedUser(user.getLoginId());
                    territory.setGmtModified(new Date());
                    return territory;
                }).collect(Collectors.toList());
                this.saveBatch(saveList);
                saveCount.updateAndGet(v -> v + saveList.size());
            }

            // 删除国家
            List<Long> toRemoveCountries = new ArrayList<>(currentCountrySet);
            toRemoveCountries.removeAll(newCountrySet);
            if (CollectionUtils.isNotEmpty(toRemoveCountries)) {
                this.remove(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                        .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                        .eq(InstitutionProviderCommissionPlanTerritory::getIsInclude, isInclude)
                        .in(InstitutionProviderCommissionPlanTerritory::getFkAreaCountryId, toRemoveCountries));
                delCount.updateAndGet(v -> v + toRemoveCountries.size());
            }

            // 删除大区
            List<Long> toRemoveRegions = new ArrayList<>(currentRegionSet);
            toRemoveRegions.removeAll(newRegionSet);
            if (CollectionUtils.isNotEmpty(toRemoveRegions)) {
                this.remove(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                        .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                        .eq(InstitutionProviderCommissionPlanTerritory::getIsInclude, isInclude)
                        .in(InstitutionProviderCommissionPlanTerritory::getFkAreaRegionId, toRemoveRegions));
                delCount.updateAndGet(v -> v + toRemoveRegions.size());
            }
        }


        // === 日志记录 ===
        if (saveCount.get() > 0) {
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT, LogTableEnum.PROVIDER_PLAN,
                    providerCommissionPlanId, LogEventEnum.ADD_TERRITORY, user.getLoginId(),
                    saveCount.get(), contractId));
        }
        if (delCount.get() > 0) {
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.PROVIDER_PLAN,
                    providerCommissionPlanId, LogEventEnum.DEL_TERRITORY, user.getLoginId(),
                    delCount.get(), contractId));
        }

        // 如果整个 isInclude 分组都移除了，就彻底清掉该分组下的所有国家和大区记录
        if (CollectionUtils.isNotEmpty(delGroups)) {
            this.remove(new LambdaQueryWrapper<InstitutionProviderCommissionPlanTerritory>()
                    .eq(InstitutionProviderCommissionPlanTerritory::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId)
                    .in(InstitutionProviderCommissionPlanTerritory::getIsInclude, delGroups));
        }

        return logs;
    }

    private List<PlanTerritoryDto> validateTerritoryList(List<PlanTerritoryDto> territoryList) {
        if (CollectionUtils.isEmpty(territoryList)) {
            return territoryList;
        }
        territoryList = territoryList.stream()
                .filter(dto ->
                        Objects.nonNull(dto.getCountryIds()) && !dto.getCountryIds().isEmpty()).collect(Collectors.toList());
        // 规则1：校验互斥的 isInclude 值
        Set<Integer> conflictIsIncludeValues = territoryList.stream()
                .map(PlanTerritoryDto::getIsInclude)
                .filter(value -> value.equals(1) || value.equals(-1))
                .collect(Collectors.toSet());
        if (conflictIsIncludeValues.size() > 1) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_TERRITORY_RULE_CONFLICT", "包括/除外/未指定区域的规则不能共存"));
        }

        // 规则2：校验 countryIds 重复
        List<Long> allCountryIds = territoryList.stream()
                .flatMap(dto -> dto.getCountryIds().stream())
                .collect(Collectors.toList());
        // 过滤掉值为 0 的国家 ID
        List<Long> filteredCountryIds = allCountryIds.stream()
                .filter(id -> id != null && id != 0)
                .collect(Collectors.toList());
        Set<Long> uniqueCountryIds = new HashSet<>(filteredCountryIds);
        if (filteredCountryIds.size() != uniqueCountryIds.size()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_DUPLICATE_COUNTRY", "存在重复的国家/区域"));
        }

        // 规则3：校验 regionIds 重复
        List<Long> allRegionIds = territoryList.stream()
                .flatMap(dto -> Optional.ofNullable(dto.getRegionIds()).orElse(Collections.emptyList()).stream())
                .filter(id -> id != null && id != 0)
                .collect(Collectors.toList());

        Set<Long> uniqueRegionIds = new HashSet<>(allRegionIds);
        if (allRegionIds.size() != uniqueRegionIds.size()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(
                    SecureUtil.getLocale(),
                    "PMP_DUPLICATE_REGION",
                    "存在重复的大区/区域"));
        }

        // 合并逻辑：根据 isInclude 分组，并合并 countryIds 和 regionIds
        return territoryList.stream()
                .collect(Collectors.groupingBy(PlanTerritoryDto::getIsInclude))
                .entrySet().stream()
                .map(entry -> {
                    PlanTerritoryDto mergedDto = new PlanTerritoryDto();
                    mergedDto.setIsInclude(entry.getKey());

                    // 合并 countryIds
                    List<Long> mergedCountryIds = entry.getValue().stream()
                            .flatMap(dto -> dto.getCountryIds().stream())
                            .distinct()
                            .collect(Collectors.toList());
                    mergedDto.setCountryIds(mergedCountryIds);

                    // 合并 regionIds（非空判断）
                    List<Long> mergedRegionIds = entry.getValue().stream()
                            .flatMap(dto -> Optional.ofNullable(dto.getRegionIds()).orElse(Collections.emptyList()).stream())
                            .distinct()
                            .collect(Collectors.toList());
                    mergedDto.setRegionIds(mergedRegionIds);

                    return mergedDto;
                })
                .collect(Collectors.toList());

//        return territoryList.stream()
//                // 按 isInclude 分组，并合并每个分组下的 countryIds
//                .collect(Collectors.groupingBy(
//                        PlanTerritoryDto::getIsInclude,
//                        Collectors.mapping(
//                                PlanTerritoryDto::getCountryIds,
//                                Collectors.toList())
//                )).entrySet().stream()
//                // 将每个分组转换为新的 PlanTerritoryDto
//                .map(entry -> {
//                    PlanTerritoryDto mergedDto = new PlanTerritoryDto();
//                    mergedDto.setIsInclude(entry.getKey());
//                    // 合并所有 countryIds 到一个列表
//                    List<Long> mergedCountryIds = entry.getValue().stream()
//                            .flatMap(List::stream)
//                            .collect(Collectors.toList());
//                    mergedDto.setCountryIds(mergedCountryIds);
//                    return mergedDto;
//                }).collect(Collectors.toList());
    }
}
