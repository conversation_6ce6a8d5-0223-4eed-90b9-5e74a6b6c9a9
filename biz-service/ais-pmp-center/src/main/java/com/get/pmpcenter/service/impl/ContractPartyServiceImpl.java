package com.get.pmpcenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.core.secure.utils.SecureUtil;
import com.get.pmpcenter.entity.ContractParty;
import com.get.pmpcenter.entity.ContractPartyCompany;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlan;
import com.get.pmpcenter.mapper.ContractPartyCompanyMapper;
import com.get.pmpcenter.mapper.ContractPartyMapper;
import com.get.pmpcenter.mapper.InstitutionCenterMapper;
import com.get.pmpcenter.mapper.InstitutionProviderCommissionPlanMapper;
import com.get.pmpcenter.service.ContractPartyService;
import com.get.pmpcenter.vo.institution.ContractPartyVo;
import com.get.pmpcenter.vo.institution.ProviderVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class ContractPartyServiceImpl extends ServiceImpl<ContractPartyMapper, ContractParty> implements ContractPartyService {

    @Autowired
    private InstitutionCenterMapper institutionCenterMapper;
    @Autowired
    private ContractPartyMapper contractPartyMapper;
    @Autowired
    private ContractPartyCompanyMapper contractPartyCompanyMapper;
    @Autowired
    private InstitutionProviderCommissionPlanMapper commissionPlanMapper;

    @Override
    public List<ProviderVo> getProviderList() {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (CollectionUtils.isEmpty(companyIds)) {
            log.error("登录用户无companyIds:{}", JSONObject.toJSONString(SecureUtil.getUser()));
            return Collections.EMPTY_LIST;
        }
        return institutionCenterMapper.institutionProviderList(companyIds,SecureUtil.getCountryIds());
    }

    @Override
    public List<ContractPartyVo> getContractPartyList(Long institutionProviderId) {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (CollectionUtils.isEmpty(companyIds)) {
            log.error("登录用户无companyIds:{}", JSONObject.toJSONString(SecureUtil.getUser()));
            return Collections.EMPTY_LIST;
        }
        List<Long> partyIds = contractPartyCompanyMapper.selectList(new LambdaQueryWrapper<ContractPartyCompany>()
                        .in(ContractPartyCompany::getFkCompanyId, companyIds))
                .stream().map(ContractPartyCompany::getFkContractPartyId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(partyIds)) {
            log.error("分公司没有关联合同方:{}", companyIds);

            return Collections.EMPTY_LIST;
        }

        List<ContractParty> contractParties = contractPartyMapper.selectList(new LambdaQueryWrapper<ContractParty>()
                .in(ContractParty::getId, partyIds)
                .exists(Objects.nonNull(institutionProviderId) && institutionProviderId > 0,
                        "SELECT 1 FROM ais_institution_center.r_institution_provider_institution_channel" +
                                " WHERE fk_institution_channel_id = m_contract_party.id AND fk_institution_provider_id = " + institutionProviderId)
                .last("ORDER BY CONVERT(name USING gbk) ASC"));
        return contractParties.stream().map(contractParty -> {
            ContractPartyVo contractPartyVo = new ContractPartyVo();
            contractPartyVo.setContractPartyId(contractParty.getId());
            contractPartyVo.setName(contractParty.getName());
            contractPartyVo.setNameChn(contractParty.getNameChn());
            return contractPartyVo;
        }).collect(Collectors.toList());
    }
}
