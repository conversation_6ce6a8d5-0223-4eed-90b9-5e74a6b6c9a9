package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.pmpcenter.dto.common.UpdateCommissionStatusDto;
import com.get.pmpcenter.dto.institution.SaveProviderCommissionDto;
import com.get.pmpcenter.entity.InstitutionProviderCommission;
import com.get.pmpcenter.vo.institution.ProviderCommissionListVo;
import com.get.pmpcenter.vo.institution.ProviderCommissionVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface InstitutionProviderCommissionService extends IService<InstitutionProviderCommission> {

    /**
     * 获取供应商合同下的佣金计划明细列表
     *
     * @param providerCommissionPlanId
     * @return
     */
    ProviderCommissionListVo getProviderCommissionList(Long providerCommissionPlanId);

    /**
     * 保存供应商合同下的佣金计划明细
     *
     * @param saveProviderCommissionDto
     */
    ProviderCommissionVo saveProviderCommission(SaveProviderCommissionDto saveProviderCommissionDto, Boolean saveLog);

    /**
     * 根据佣金方案ID删除佣金计划明细
     *
     * @param planId
     */
    void delCommissionByPlanId(Long planId);

    /**
     * 根据佣金方案ID获取单项佣金明细列表
     *
     * @param providerCommissionPlanId
     * @return
     */
    List<ProviderCommissionListVo.CommissionInfo> getCommissionList(Long providerCommissionPlanId);

    /**
     * 根据佣金方案ID获取组合佣金明细列表
     *
     * @param providerCommissionPlanId
     * @return
     */
    List<ProviderCommissionListVo.CombinationInfo> getCombinationList(Long providerCommissionPlanId);

    /**
     * 根据佣金方案ID获取奖金明细列表
     *
     * @param providerCommissionPlanId
     */
    List<ProviderCommissionListVo.BonusInfo> getBonusInfoList(Long providerCommissionPlanId);


    /**
     * 根据佣金方案ID获取佣金方案明细列表-包含权限信息
     *
     * @param providerCommissionPlanId
     * @return
     */
    ProviderCommissionVo getProviderCommissionAndPermission(Long providerCommissionPlanId);

    /**
     * 更新佣金计划明细状态
     *
     * @param updateCommissionStatusDto
     */
    void updateCommissionStatus(UpdateCommissionStatusDto updateCommissionStatusDto);

    /**
     * 获取旧版本供应商合同下的佣金计划明细列表
     *
     * @param providerCommissionPlanId
     * @return
     */
    ProviderCommissionVo  getOldVersionProviderCommission(Long providerCommissionPlanId);

}
