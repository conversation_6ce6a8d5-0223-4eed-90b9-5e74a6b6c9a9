package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.pmpcenter.entity.InstitutionProviderCompanyAgentApproval;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/31
 * @Version 1.0
 * @apiNote:
 */
public interface InstitutionProviderCompanyAgentApprovalMapper extends BaseMapper<InstitutionProviderCompanyAgentApproval> {

    List<InstitutionProviderCompanyAgentApproval> getApprovalList(@Param("institutionProviderId") Long institutionProviderId,
                                                                  @Param("companyId") Long companyId);
}
