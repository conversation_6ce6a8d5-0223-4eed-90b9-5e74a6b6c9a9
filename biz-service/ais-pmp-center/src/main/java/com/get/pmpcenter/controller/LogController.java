package com.get.pmpcenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.pmpcenter.dto.common.LogRecordDto;
import com.get.pmpcenter.service.LogOperationService;
import com.get.pmpcenter.vo.common.LogRecordVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @DATE: 2020/7/14
 * @TIME: 18:17
 * @Description: 附件管理控制层
 **/

@Api(tags = "日志管理")
@RestController
@RequestMapping("/log")
public class LogController {

    @Autowired
    private LogOperationService logOperationService;

    @ApiOperation(value = "操作日志列表", notes = "根据方案类型和方案id获取操作日志")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "type", value = "1-学校提供商佣金方案;2-代理佣金方案", required = true),
            @ApiImplicitParam(name = "id", value = "学校提供商合同ID/代理佣金方案ID", required = true),
    })
    @GetMapping("/logList")
    public ResponseBo<List<LogRecordVo>> logList(Integer type, Long id) {
        return new ResponseBo<>(logOperationService.logList(type, id));
    }

    @ApiOperation(value = "佣金操作日志列表-分页")
    @PostMapping("/commissionLogRecordPage")
    public ResponseBo<LogRecordVo> commissionLogRecordPage(@RequestBody SearchBean<LogRecordDto> page) {
        List<LogRecordVo> list = logOperationService.commissionLogRecordPage(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(list, p);
    }

}
