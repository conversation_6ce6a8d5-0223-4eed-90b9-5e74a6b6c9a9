package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanApproval;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface InstitutionProviderCommissionPlanApprovalMapper extends BaseMapper<InstitutionProviderCommissionPlanApproval> {

    /**
     * 根据方案ID获取审批记录
     *
     * @param plaIds
     * @return
     */
    List<InstitutionProviderCommissionPlanApproval> getApprovalList(List<Long> plaIds);

    /**
     * 根据方案ID获取批量审批记录
     *
     * @param plaIds
     * @return
     */
    List<InstitutionProviderCommissionPlanApproval> getBatchApprovalList(@Param("plaIds") List<Long> plaIds,
                                                                         @Param("staffId") Long staffId);

}
