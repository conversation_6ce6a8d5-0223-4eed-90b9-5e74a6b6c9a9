package com.get.pmpcenter.service.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.pmpcenter.dto.common.LogDto;
import com.get.pmpcenter.entity.AgentCommissionPlan;
import com.get.pmpcenter.entity.AgentCommissionPlanInstitution;
import com.get.pmpcenter.enums.LogEventEnum;
import com.get.pmpcenter.enums.LogTableEnum;
import com.get.pmpcenter.enums.LogTypeEnum;
import com.get.pmpcenter.mapper.AgentCommissionPlanInstitutionMapper;
import com.get.pmpcenter.mapper.AgentCommissionPlanMapper;
import com.get.pmpcenter.service.AgentCommissionPlanInstitutionService;
import com.get.pmpcenter.utils.GeneratorLogsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class AgentCommissionPlanInstitutionServiceImpl extends ServiceImpl<AgentCommissionPlanInstitutionMapper, AgentCommissionPlanInstitution> implements AgentCommissionPlanInstitutionService {

    @Autowired
    private AgentCommissionPlanInstitutionMapper planInstitutionMapper;
    @Autowired
    private AgentCommissionPlanMapper commissionPlanMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<LogDto> saveAgentCommissionPlanInstitution(List<Long> institutionIds, Long agentCommissionPlanId) {
        UserInfo user = SecureUtil.getUser();
        List<LogDto> logs = new ArrayList<>();
        List<AgentCommissionPlanInstitution> institutionList = planInstitutionMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                .eq(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, agentCommissionPlanId));

        List<Long> currentList = institutionList.stream().map(AgentCommissionPlanInstitution::getFkInstitutionId).collect(Collectors.toList());
        Set<Long> currentSet = new HashSet<>(currentList);
        Set<Long> newSet = new HashSet<>(institutionIds);

        List<Long> toAdd = new ArrayList<>(newSet);
        List<AgentCommissionPlanInstitution> toUpdate = new ArrayList<>();
        toAdd.removeAll(currentSet);

        //更新的
        if (CollectionUtils.isNotEmpty(institutionList)) {
            toUpdate = institutionList.stream()
                    .filter(item -> institutionIds.contains(item.getFkInstitutionId()))
                    .map(item -> {
                        item.setGmtModifiedUser(user.getLoginId());
                        item.setGmtModified(new Date());
                        return item;
                    }).collect(Collectors.toList());
        }
        // 找出需要删除的
        List<Long> toRemove = new ArrayList<>(currentSet);
        toRemove.removeAll(newSet);

        if (CollectionUtils.isNotEmpty(toAdd)) {
            List<AgentCommissionPlanInstitution> saveList = toAdd.stream().map(item -> {
                AgentCommissionPlanInstitution planInstitution = new AgentCommissionPlanInstitution();
                planInstitution.setFkInstitutionId(item);
                planInstitution.setFkAgentCommissionPlanId(agentCommissionPlanId);
                planInstitution.setGmtCreateUser(user.getLoginId());
                planInstitution.setGmtCreate(new Date());
                planInstitution.setGmtModifiedUser(user.getLoginId());
                planInstitution.setGmtModified(new Date());
                return planInstitution;
            }).collect(Collectors.toList());
            this.saveBatch(saveList);
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.INSERT, LogTableEnum.AGENT_PLAN,
                    agentCommissionPlanId, LogEventEnum.ADD_INSTITUTION, user.getLoginId(),
                    toAdd.size(), null));
        }

        if (CollectionUtils.isNotEmpty(toUpdate)) {
            this.updateBatchById(toUpdate);
        }

        if (CollectionUtils.isNotEmpty(toRemove)) {
            this.remove(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                    .eq(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, agentCommissionPlanId)
                    .in(AgentCommissionPlanInstitution::getFkInstitutionId, toRemove));
            logs.add(GeneratorLogsUtil.generatorLog(LogTypeEnum.DELETE, LogTableEnum.AGENT_PLAN,
                    agentCommissionPlanId, LogEventEnum.DEL_INSTITUTION, user.getLoginId(),
                    toRemove.size(), null));
        }
        return logs;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAgentCommissionPlanInstitution(List<Long> institutionIds, Long providerCommissionPlanId, Boolean isSave) {
        List<Long> planIds = commissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
                        .eq(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId))
                .stream().map(AgentCommissionPlan::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(planIds)) {
            if (!isSave) {
                //删除学校
                List<Long> planInstitutionIds = planInstitutionMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                                .in(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, planIds)
                                .in(AgentCommissionPlanInstitution::getFkInstitutionId, institutionIds))
                        .stream().map(AgentCommissionPlanInstitution::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(planInstitutionIds)) {
                    this.removeByIds(planInstitutionIds);
                    return;
                }
            }
            //新增学校
            List<AgentCommissionPlanInstitution> allSaveList = new ArrayList<>();
            planIds.stream().forEach(id -> {
                List<AgentCommissionPlanInstitution> saveList = institutionIds.stream().map(item -> {
                    AgentCommissionPlanInstitution planInstitution = new AgentCommissionPlanInstitution();
                    planInstitution.setFkInstitutionId(item);
                    planInstitution.setFkAgentCommissionPlanId(id);
                    planInstitution.setGmtCreateUser(SecureUtil.getLoginId());
                    planInstitution.setGmtCreate(new Date());
                    planInstitution.setGmtModifiedUser(SecureUtil.getLoginId());
                    planInstitution.setGmtModified(new Date());
                    return planInstitution;
                }).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(saveList)) {
                    allSaveList.addAll(saveList);
                }
            });
            if (CollectionUtils.isNotEmpty(allSaveList)) {
                this.saveBatch(allSaveList);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncAgentCommissionPlanInstitution(List<Long> institutionIds, Long providerCommissionPlanId) {
        //找到对应的代理方案
        List<Long> planIds = commissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
                        .eq(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, providerCommissionPlanId))
                .stream().map(AgentCommissionPlan::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(planIds)) {
            return;
        }
        // 查询当前数据库已有的绑定关系
        List<AgentCommissionPlanInstitution> existingRelations = planInstitutionMapper.selectList(
                new LambdaQueryWrapper<AgentCommissionPlanInstitution>()
                        .in(AgentCommissionPlanInstitution::getFkAgentCommissionPlanId, planIds));
        // Map<planId, Set<institutionId>>
        Map<Long, Set<Long>> existingMap = existingRelations.stream()
                .collect(Collectors.groupingBy(
                        AgentCommissionPlanInstitution::getFkAgentCommissionPlanId,
                        Collectors.mapping(AgentCommissionPlanInstitution::getFkInstitutionId, Collectors.toSet())));

        //需要新增的
        List<AgentCommissionPlanInstitution> insertList = new ArrayList<>();
        //需要删除的
        Set<Long> deleteIdSet = new HashSet<>();

        for (Long planId : planIds) {
            Set<Long> current = existingMap.getOrDefault(planId, Collections.emptySet());
            Set<Long> incoming = new HashSet<>(institutionIds);

            Set<Long> toInsert = new HashSet<>(incoming);
            toInsert.removeAll(current);

            Set<Long> toDelete = new HashSet<>(current);
            toDelete.removeAll(incoming);

            for (Long id : toInsert) {
                AgentCommissionPlanInstitution rel = new AgentCommissionPlanInstitution();
                rel.setFkAgentCommissionPlanId(planId);
                rel.setFkInstitutionId(id);
                rel.setGmtCreateUser(SecureUtil.getLoginId());
                rel.setGmtCreate(new Date());
                rel.setGmtModifiedUser(SecureUtil.getLoginId());
                rel.setGmtModified(new Date());
                insertList.add(rel);
            }

            if (!toDelete.isEmpty()) {
                existingRelations.stream()
                        .filter(e -> e.getFkAgentCommissionPlanId().equals(planId) && toDelete.contains(e.getFkInstitutionId()))
                        .map(AgentCommissionPlanInstitution::getId)
                        .forEach(deleteIdSet::add);
            }
        }

        if (!deleteIdSet.isEmpty()) {
            this.removeByIds(deleteIdSet);
        }

        if (!insertList.isEmpty()) {
            this.saveBatch(insertList);
        }


    }
}
