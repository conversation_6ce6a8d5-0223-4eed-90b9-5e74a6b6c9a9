package com.get.pmpcenter.feign;

import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import com.get.pmpcenter.dto.agent.PmpWorkbenchApprovalDto;
import com.get.pmpcenter.dto.institution.UnbindProviderInstitutionDto;
import com.get.pmpcenter.service.InstitutionProviderCommissionPlanService;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:
 */
@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class PmpCenterClient implements IPmpCenterClient {

    @Autowired
    private InstitutionProviderCommissionPlanService commissionPlanService;

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> unbindProviderInstitution(UnbindProviderInstitutionDto unbindProviderInstitutionDto) {
        commissionPlanService.unbindProviderInstitution(unbindProviderInstitutionDto);
        return Result.data(Boolean.TRUE);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<WorkbenchApprovalVo>> getWorkbenchApprovalList(Long staffId, String approvalType,Integer approvalStatus,String loginId) {
        PmpWorkbenchApprovalDto approvalDto = PmpWorkbenchApprovalDto.builder()
                .staffId(staffId)
                .approvalType(approvalType)
                .approvalStatus(approvalStatus)
                .loginId(loginId)
                .build();
        return Result.data(commissionPlanService.getWorkbenchApprovalList(approvalDto));
    }
}
