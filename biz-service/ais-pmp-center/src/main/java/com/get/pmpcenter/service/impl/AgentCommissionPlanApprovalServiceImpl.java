package com.get.pmpcenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.pmpcenter.dto.common.ApprovalPlanDto;
import com.get.pmpcenter.dto.common.LockPlanDto;
import com.get.pmpcenter.dto.common.SubmitPlanApprovalDto;
import com.get.pmpcenter.entity.AgentCommissionPlan;
import com.get.pmpcenter.entity.AgentCommissionPlanApproval;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlan;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanApproval;
import com.get.pmpcenter.enums.ApprovalStatusEnum;
import com.get.pmpcenter.enums.MediaTableEnum;
import com.get.pmpcenter.enums.ProviderCommissionPlanApprovalTypeEnum;
import com.get.pmpcenter.mapper.AgentCommissionPlanApprovalMapper;
import com.get.pmpcenter.mapper.AgentCommissionPlanMapper;
import com.get.pmpcenter.mapper.InstitutionProviderCommissionPlanApprovalMapper;
import com.get.pmpcenter.mapper.PermissionCenterMapper;
import com.get.pmpcenter.service.AgentCommissionPlanApprovalService;
import com.get.pmpcenter.service.AgentCommissionPlanService;
import com.get.pmpcenter.service.InstitutionProviderCommissionPlanService;
import com.get.pmpcenter.service.PmpMediaAndAttachedService;
import com.get.pmpcenter.vo.agent.UserPlanIds;
import com.get.pmpcenter.vo.common.MediaVo;
import com.get.pmpcenter.vo.common.StaffVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/4/7
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class AgentCommissionPlanApprovalServiceImpl extends ServiceImpl<AgentCommissionPlanApprovalMapper, AgentCommissionPlanApproval> implements AgentCommissionPlanApprovalService {

    @Autowired
    private AgentCommissionPlanApprovalMapper planApprovalMapper;
    @Autowired
    private AgentCommissionPlanMapper commissionPlanMapper;
    @Autowired
    private IPermissionCenterClient permissionCenterClient;
    @Autowired
    private PermissionCenterMapper permissionCenterMapper;
    @Autowired
    private PmpMediaAndAttachedService pmpMediaAndAttachedService;
    @Autowired
    private InstitutionProviderCommissionPlanService providerCommissionPlanService;
    @Autowired
    private InstitutionProviderCommissionPlanApprovalMapper providerCommissionPlanApprovalMapper;
    @Autowired
    private AgentCommissionPlanService agentCommissionPlanService;

    @Override
    public void lockAgentPlan(LockPlanDto lockPlanDto) {
        AgentCommissionPlan plan = commissionPlanMapper.selectById(lockPlanDto.getPlanId());
        if (Objects.isNull(plan)) {
//            throw new GetServiceException("代理佣金方案不存在!");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "代理佣金方案不存在"));
        }
//        if (!plan.getGmtCreateUser().equals(SecureUtil.getLoginId())) {
//            log.error("锁定/解锁方案失败,没有权限,当前操作人:{},合同id:{},合同创建人:{}", SecureUtil.getLoginId(), SecureUtil.getLoginId(), plan.getGmtCreateUser());
////            throw new GetServiceException("您没有权限操作!");
//            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_NO_PERMISSION", "您没有权限操作"));
//        }
        plan.setIsLocked(lockPlanDto.getIsLocked());
        plan.setGmtModified(new Date());
        plan.setGmtModifiedUser(SecureUtil.getLoginId());
        commissionPlanMapper.updateById(plan);
    }

    @Override
    public void submitAgentApproval(SubmitPlanApprovalDto submitPlanApprovalDto) {
        AgentCommissionPlan plan = commissionPlanMapper.selectById(submitPlanApprovalDto.getPlanId());
        if (Objects.isNull(plan)) {
            log.error("提交审核失败,代理佣金方案不存在，合同id：{}", submitPlanApprovalDto.getPlanId());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        if (Objects.nonNull(plan.getApprovalStatus()) && plan.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
            log.error("提交审核失败,方案已提交审核，合同id：{}", submitPlanApprovalDto.getPlanId());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_SUBMITTED", "方案已提交审核"));
        }
        AgentCommissionPlanApproval planApproval = new AgentCommissionPlanApproval();
        planApproval.setFkAgentCommissionPlanId(submitPlanApprovalDto.getPlanId());
        planApproval.setApprovalStatus(ApprovalStatusEnum.PENDING_APPROVAL.getCode());
        planApproval.setFkStaffId(submitPlanApprovalDto.getStaffId());
        planApproval.setGmtCreate(new Date());
        planApproval.setGmtModified(new Date());
        planApproval.setGmtModified(new Date());
        planApproval.setGmtCreateUser(SecureUtil.getLoginId());
        planApproval.setGmtModifiedUser(SecureUtil.getLoginId());
        planApproval.setCommissionPlanName(plan.getName());
        planApproval.setSubmitNote(submitPlanApprovalDto.getSubmitNote());
        planApprovalMapper.insert(planApproval);
        //更新合同状态
        plan.setApprovalStatus(ApprovalStatusEnum.PENDING_APPROVAL.getCode());
        plan.setGmtModified(new Date());
        plan.setGmtModifiedUser(SecureUtil.getLoginId());
        //有权限提交审核的人，点击提交审核后，同时会将此合同更改为锁定状态，不管审批是否通过，都不会自动解锁，需要合同的创建人手动解锁。
        plan.setIsLocked(1);
        commissionPlanMapper.updateById(plan);
        //保存审核附件
        if (CollectionUtils.isNotEmpty(submitPlanApprovalDto.getMediaList())) {
            pmpMediaAndAttachedService.saveMedia(MediaTableEnum.AGENT_COMMISSION_PLAN_APPROVAL.getCode(),
                    planApproval.getId(), submitPlanApprovalDto.getMediaList());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void approvalAgentProviderPlan(ApprovalPlanDto approvalPlanDto) {
        AgentCommissionPlan plan = commissionPlanMapper.selectById(approvalPlanDto.getPlanId());
        if (Objects.isNull(plan)) {
            log.error("审核失败,代理佣金方案不存在，方案id：{}", approvalPlanDto.getPlanId());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
        }
        List<AgentCommissionPlanApproval> planApprovals = planApprovalMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlanApproval>()
                .eq(AgentCommissionPlanApproval::getFkAgentCommissionPlanId, approvalPlanDto.getPlanId())
                .eq(AgentCommissionPlanApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
                .orderByDesc(AgentCommissionPlanApproval::getGmtCreate));
        if (Objects.isNull(planApprovals) || planApprovals.isEmpty()) {
            log.error("审核失败,佣金方案审批记录不存在，方案id：{}", approvalPlanDto.getPlanId());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_RECORD_NOT_FOUND", "方案审批记录不存在"));
        }
        if (!planApprovals.get(0).getFkStaffId().equals(SecureUtil.getStaffId())) {
            log.error("审批失败,非方案指定审批人，合同id：{},审批人id：{},登录人id：{}", approvalPlanDto.getPlanId(),
                    planApprovals.get(0).getFkStaffId(), SecureUtil.getStaffId());
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_NOT_APPROVER", "审核失败,当前用户不是方案指定审批人"));
        }

        //如果合同佣金方案还在审核中，暂不能同时操作驳回
        Long providerCommissionPlanId = null;
        if (approvalPlanDto.getApprovalStatus() > ApprovalStatusEnum.REJECT.getCode()) {
            InstitutionProviderCommissionPlan providerCommissionPlan = providerCommissionPlanService.getById(plan.getFkInstitutionProviderCommissionPlanId());
            if (Objects.isNull(providerCommissionPlan)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_PLAN_NOT_FOUND", "方案不存在"));
            }
            if (providerCommissionPlan.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_REJECT_PROVIDER_PLAN_FAIL", "合同佣金方案还在审核中，暂不能同时操作驳回，待审核结束后，再进行操作。"));
            }
            providerCommissionPlanId = providerCommissionPlan.getId();
        }
        AgentCommissionPlanApproval planApproval = new AgentCommissionPlanApproval();
        planApproval.setFkAgentCommissionPlanId(approvalPlanDto.getPlanId());
        planApproval.setApprovalStatus(approvalPlanDto.getApprovalStatus() > ApprovalStatusEnum.REJECT.getCode() ? ApprovalStatusEnum.REJECT.getCode() : approvalPlanDto.getApprovalStatus());
        planApproval.setApprovalComment(approvalPlanDto.getApprovalComment());
        planApproval.setApprovalTime(new Date());
        planApproval.setFkStaffId(planApprovals.get(0).getFkStaffId());
        planApproval.setGmtCreate(new Date());
        planApproval.setGmtModified(new Date());
        planApproval.setGmtModified(new Date());
        planApproval.setGmtCreateUser(SecureUtil.getLoginId());
        planApproval.setGmtModifiedUser(SecureUtil.getLoginId());
        planApproval.setCommissionPlanName(plan.getName());
        planApprovalMapper.insert(planApproval);
        //更新合同状态
        plan.setApprovalStatus(approvalPlanDto.getApprovalStatus() > ApprovalStatusEnum.REJECT.getCode() ? ApprovalStatusEnum.REJECT.getCode() : approvalPlanDto.getApprovalStatus());
        plan.setGmtModified(new Date());
        plan.setGmtModifiedUser(SecureUtil.getLoginId());
        commissionPlanMapper.updateById(plan);
        //保存审核附件
        if (CollectionUtils.isNotEmpty(approvalPlanDto.getMediaList())) {
            pmpMediaAndAttachedService.saveMedia(MediaTableEnum.AGENT_COMMISSION_PLAN_APPROVAL.getCode(),
                    planApproval.getId(), approvalPlanDto.getMediaList());
        }
        //驳回合同端审核
        if (approvalPlanDto.getApprovalStatus() > ApprovalStatusEnum.REJECT.getCode()) {
            ApprovalPlanDto planDto = ApprovalPlanDto.builder()
                    .planId(providerCommissionPlanId)
                    .approvalStatus(ApprovalStatusEnum.REJECT.getCode())
                    .approvalComment(approvalPlanDto.getApprovalComment())
                    .mediaList(approvalPlanDto.getMediaList())
                    .build();
            rejectProviderPlan(planDto);
        }
    }


    @Override
    public List<AgentCommissionPlanApproval> getAgentApprovalList(Long institutionProviderId, Long companyId, Long planId, String planName,String keyword) {
        UserPlanIds userPlanIds = agentCommissionPlanService.getUserPlanIds(companyId);
        List<Long> agentPlanIds = userPlanIds.getAgentPlanIds();
        List<Long> planIds = commissionPlanMapper.selectList(new LambdaQueryWrapper<AgentCommissionPlan>()
                        .eq(Objects.nonNull(institutionProviderId), AgentCommissionPlan::getFkInstitutionProviderId, institutionProviderId)
                        .exists(Objects.nonNull(companyId), "select id from r_agent_commission_plan_company" +
                                " where fk_agent_commission_plan_id = m_agent_commission_plan.id and fk_company_id = " + companyId)
                        .eq(Objects.nonNull(planId) && planId > 0, AgentCommissionPlan::getId, planId)
                        .in(AgentCommissionPlan::getId, CollectionUtils.isEmpty(agentPlanIds) ? Arrays.asList(0L) : agentPlanIds)
                        .like(StringUtils.isNotBlank(keyword), AgentCommissionPlan::getName, "%" + keyword + "%")
                        .like(StringUtils.isNotBlank(planName), AgentCommissionPlan::getName, "%" + planName + "%"))
                .stream().map(AgentCommissionPlan::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(planIds)) {
            List<AgentCommissionPlanApproval> approvalList = planApprovalMapper.getAgentApprovalList(planIds);
            approvalList.stream().forEach(approval -> {
                List<MediaVo> mediaList = pmpMediaAndAttachedService.getMediaList(MediaTableEnum.AGENT_COMMISSION_PLAN_APPROVAL.getCode(), Arrays.asList(approval.getId()));
                approval.setMediaList(mediaList);
            });
            return approvalList;
        }
        return Collections.emptyList();
    }

    @Override
    public List<StaffVo> getAgentStaff() {
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_PMP_APPROVE.key, 2).getData();
        log.info("CompanyConfigMap: {}", JSONObject.toJSONString(companyConfigMap));
        if (Objects.nonNull(companyConfigMap) && companyConfigMap.containsKey(SecureUtil.getFkCompanyId())) {
            String examineIds = companyConfigMap.get(SecureUtil.getFkCompanyId());
            List<Long> list = JSON.parseArray(examineIds, Long.class);
            if (CollectionUtils.isEmpty(list)) {
                log.error("未获取到审批人配置信息,分公司ID：{}", SecureUtil.getFkCompanyId());
                return new ArrayList<>();
            }
            return permissionCenterMapper.getStaffList(list, null);
        } else {
            log.info("未获取到审批人配置信息,分公司ID：{}", SecureUtil.getFkCompanyId());
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rejectProviderPlan(ApprovalPlanDto approvalPlanDto) {
        //驳回合同端方案审批-新增一条审核失败记录
        InstitutionProviderCommissionPlan plan = providerCommissionPlanService.getById(approvalPlanDto.getPlanId());
        if (Objects.isNull(plan)) {
            log.error("驳回合同端方案审批失败,佣金方案不存在，方案id：{}", approvalPlanDto.getPlanId());
            return;
        }
        InstitutionProviderCommissionPlanApproval planApproval = new InstitutionProviderCommissionPlanApproval();
        planApproval.setFkInstitutionProviderCommissionPlanIds(approvalPlanDto.getPlanId().toString());
        planApproval.setApprovalStatus(approvalPlanDto.getApprovalStatus());
        planApproval.setApprovalComment(approvalPlanDto.getApprovalComment());
        planApproval.setApprovalTime(new Date());
        planApproval.setFkStaffId(SecureUtil.getStaffId());
        planApproval.setGmtCreate(new Date());
        planApproval.setGmtModified(new Date());
        planApproval.setGmtModified(new Date());
        planApproval.setGmtCreateUser(SecureUtil.getLoginId());
        planApproval.setGmtModifiedUser(SecureUtil.getLoginId());
        planApproval.setCommissionPlanName(plan.getName());
        planApproval.setTypeKey(ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode());
        providerCommissionPlanApprovalMapper.insert(planApproval);
        //修改方案状态
        plan.setApprovalStatus(approvalPlanDto.getApprovalStatus());
        plan.setGmtModified(new Date());
        plan.setGmtModifiedUser(SecureUtil.getLoginId());
        providerCommissionPlanService.updateById(plan);
        //保存审核附件
        if (CollectionUtils.isNotEmpty(approvalPlanDto.getMediaList())) {
            pmpMediaAndAttachedService.saveMedia(MediaTableEnum.PROVIDER_COMMISSION_PLAN_APPROVAL.getCode(),
                    planApproval.getId(), approvalPlanDto.getMediaList());
        }

    }
}
