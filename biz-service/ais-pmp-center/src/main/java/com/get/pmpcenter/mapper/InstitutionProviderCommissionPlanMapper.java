package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.pmpcenter.dto.institution.ProviderContractPageDto;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlan;
import com.get.pmpcenter.entity.InstitutionProviderContract;
import com.get.pmpcenter.vo.institution.InstitutionPlanVo;
import com.get.pmpcenter.vo.institution.ProviderCommissionPlanVo;
import com.get.pmpcenter.vo.institution.ProviderContractVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface InstitutionProviderCommissionPlanMapper extends BaseMapper<InstitutionProviderCommissionPlan> {

    /**
     * 根据合同id查询合同下的佣金计划
     *
     * @param providerContractId
     * @return
     */
    List<ProviderCommissionPlanVo> selectContractCommissionPlanList(@Param("providerContractId") Long providerContractId,
                                                                    @Param("userPlanIds") List<Long> userPlanIds);

    /**
     *
     *  根据ID查询佣金计划
     * @param page
     * @param planIds
     * @return
     */
    List<InstitutionPlanVo> selectInstitutionProviderCommissionPlans(IPage<InstitutionProviderCommissionPlan> page,
                                                                     @Param("planIds") List<Long> planIds);

    /**
     * 根据佣金计划ID查询合同kpi
     * @param planId
     * @return
     */
    InstitutionProviderContract selectContractKpiByPlanId(@Param("planId") Long planId);

    /**
     * 查询已过期的佣金计划
     * @param institutionProviderId
     * @return
     */
    List<InstitutionProviderCommissionPlan> selectExpirePlan(@Param("institutionProviderId") Long institutionProviderId);
}
