package com.get.pmpcenter.event.publisher;

import com.get.pmpcenter.event.ExpireAgentCommissionPlanEvent;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/27  17:17
 * @Version 1.0
 * 代理方案过期事件发布者
 */
@Component
public class ExpireAgentCommissionPlanEventPublisher {

    private final ApplicationEventPublisher eventPublisher;

    @Autowired
    public ExpireAgentCommissionPlanEventPublisher(ApplicationEventPublisher eventPublisher) {
        this.eventPublisher = eventPublisher;
    }

    public void publishExpireAgentCommissionPlanEvent(List<Long> expirePlanIds) {
        ExpireAgentCommissionPlanEvent event = new ExpireAgentCommissionPlanEvent(this, expirePlanIds);
        eventPublisher.publishEvent(event);
    }
}
