package com.get.pmpcenter.utils;

import com.get.pmpcenter.dto.common.LogDto;
import com.get.pmpcenter.enums.LogEventEnum;
import com.get.pmpcenter.enums.LogTableEnum;
import com.get.pmpcenter.enums.LogTypeEnum;
import org.apache.commons.lang.StringUtils;

import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/2/28  09:58
 * @Version 1.0
 */
public class GeneratorLogsUtil {

    public static LogDto generatorLog(LogTypeEnum typeEnum,
                                      LogTableEnum logTableEnum,
                                      Long id,
                                      LogEventEnum logEventEnum,
                                      String loginId,
                                      Object msg,
                                      Long contractId) {
        String msgStr = StringUtils.EMPTY;
        if (Objects.nonNull(msg)) {
            msgStr = msg.toString();
        }
        String desc = logEventEnum.getPrefix() + msgStr + logEventEnum.getMsg();
        return LogDto.builder()
                .action(typeEnum.getCode())
                .loginId(loginId)
                .tableName(logTableEnum.getCode())
                .tableId(id)
                .desc(desc.trim())
                .fkInstitutionProviderContractId(contractId)
                .build();
    }
}
