package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.pmpcenter.dto.agent.SaveAgentCommissionDto;
import com.get.pmpcenter.dto.agent.SavePlanAndCommissionDto;
import com.get.pmpcenter.entity.AgentCommission;
import com.get.pmpcenter.vo.agent.ExtendAgentCommissionVo;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface AgentCommissionService extends IService<AgentCommission> {

    /**
     * 继承模板下的佣金方案明细
     *
     * @param type
     * @param id
     * @return
     */
    ExtendAgentCommissionVo getExtendAgentCommissionList(Integer type, Long id, Long companyId, Long agentCommissionTypeId);


    /**
     * 保存代理佣金方案（方案+明细）
     *
     * @param agentCommissionDto
     */
    Long saveAgentCommission(SavePlanAndCommissionDto agentCommissionDto);

    /**
     * 更新代理佣金明细
     *
     * @param agentCommissionDto
     */
    ExtendAgentCommissionVo updateAgentCommission(SaveAgentCommissionDto agentCommissionDto);

    /**
     * 获取代理佣金方案明细列表
     *
     * @param id
     * @return
     */
    ExtendAgentCommissionVo getAgentCommissionDetail(Long id, Boolean initCommission, Boolean checkPermission, Long agentCommissionTypeId);

    /**
     * 获取旧版本代理佣金方案明细列表
     *
     * @param agentCommissionPlanId
     * @return
     */
    ExtendAgentCommissionVo getOldVersionAgentCommission(Long agentCommissionPlanId);

}
