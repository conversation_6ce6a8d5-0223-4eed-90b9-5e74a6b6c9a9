package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.pmpcenter.entity.ContractParty;
import com.get.pmpcenter.vo.institution.ContractPartyVo;
import com.get.pmpcenter.vo.institution.ProviderVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface ContractPartyService extends IService<ContractParty> {

    /**
     * 获取供应商列表
     * @return
     */
    List<ProviderVo> getProviderList();

    /**
     * 获取合同方列表
     * @return
     */
    List<ContractPartyVo> getContractPartyList(Long institutionProviderId);
}
