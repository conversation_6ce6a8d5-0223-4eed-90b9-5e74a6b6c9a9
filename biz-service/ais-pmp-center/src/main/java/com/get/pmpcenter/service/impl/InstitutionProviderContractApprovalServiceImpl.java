package com.get.pmpcenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.pmpcenter.dto.institution.ApprovalContractDto;
import com.get.pmpcenter.dto.institution.SubmitApprovalDto;
import com.get.pmpcenter.entity.InstitutionProviderContract;
import com.get.pmpcenter.entity.InstitutionProviderContractApproval;
import com.get.pmpcenter.enums.ApprovalStatusEnum;
import com.get.pmpcenter.mapper.InstitutionProviderContractApprovalMapper;
import com.get.pmpcenter.mapper.InstitutionProviderContractMapper;
import com.get.pmpcenter.mapper.PermissionCenterMapper;
import com.get.pmpcenter.service.InstitutionProviderContractApprovalService;
import com.get.pmpcenter.service.MailService;
import com.get.pmpcenter.vo.common.StaffVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class InstitutionProviderContractApprovalServiceImpl extends ServiceImpl<InstitutionProviderContractApprovalMapper, InstitutionProviderContractApproval> implements InstitutionProviderContractApprovalService {

    @Autowired
    private InstitutionProviderContractApprovalMapper institutionProviderContractApprovalMapper;
    @Autowired
    private IPermissionCenterClient permissionCenterClient;
    @Autowired
    private PermissionCenterMapper permissionCenterMapper;
    @Autowired
    private InstitutionProviderContractMapper contractMapper;
    @Autowired
    private MailService mailService;

    @Override
    public List<StaffVo> getStaffList() {
//        //获取业务下属
//        Long staffId = SecureUtil.getStaffId();
//        //员工id + 业务下属员工ids
//        List<Long> staffFollowerIds = permissionCenterClient.getStaffFollowerIds(staffId).getData();
//        return permissionCenterMapper.getStaffList(staffFollowerIds);
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_PMP_APPROVE.key, 1).getData();
        log.info("CompanyConfigMap: {}", JSONObject.toJSONString(companyConfigMap));
        if (Objects.nonNull(companyConfigMap) && companyConfigMap.containsKey(SecureUtil.getFkCompanyId())) {
            String examineIds = companyConfigMap.get(SecureUtil.getFkCompanyId());
            List<Long> list = JSON.parseArray(examineIds, Long.class);
            if (CollectionUtils.isEmpty(list)) {
                log.error("未获取到审批人配置信息,分公司ID：{}", SecureUtil.getFkCompanyId());
                return new ArrayList<>();
            }
            return permissionCenterMapper.getStaffList(list, null);
        } else {
            log.info("未获取到审批人配置信息,分公司ID：{}", SecureUtil.getFkCompanyId());
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitApproval(SubmitApprovalDto submitApprovalDto) {
        InstitutionProviderContract contract = contractMapper.selectById(submitApprovalDto.getContractId());
        if (Objects.isNull(contract)) {
            log.error("提交审核失败,合同不存在，合同id：{}", submitApprovalDto.getContractId());
//            throw new GetServiceException("合同不存在");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_CONTRACT_NOT_FOUND","合同不存在"));
        }
        if (Objects.nonNull(contract.getApprovalStatus()) && contract.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
            log.error("提交审核失败,合同已提交审核，合同id：{}", submitApprovalDto.getContractId());
//            throw new GetServiceException("合同已提交审核");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_CONTRACT_SUBMITTED","合同已提交审核"));
        }
        InstitutionProviderContractApproval contractApproval = getInstitutionProviderContractApproval(submitApprovalDto.getContractId(), submitApprovalDto.getStaffId(),
                ApprovalStatusEnum.PENDING_APPROVAL.getCode(), StringUtils.EMPTY);
        institutionProviderContractApprovalMapper.insert(contractApproval);
        //更新合同状态
        contract.setApprovalStatus(ApprovalStatusEnum.PENDING_APPROVAL.getCode());
        contract.setGmtModified(new Date());
        contract.setGmtModifiedUser(SecureUtil.getLoginId());
        //有权限提交审核的人，点击提交审核后，同时会将此合同更改为锁定状态，不管审批是否通过，都不会自动解锁，需要合同的创建人手动解锁。
        contract.setIsLocked(1);
        contractMapper.updateById(contract);
//        mailService.sendContractApprovalMail(contract.getId(), submitApprovalDto.getEmail());
    }

    private static InstitutionProviderContractApproval getInstitutionProviderContractApproval(Long contractId, Long staffId,
                                                                                              Integer approvalStatus, String approvalComment) {
        InstitutionProviderContractApproval contractApproval = new InstitutionProviderContractApproval();
        contractApproval.setFkInstitutionProviderContractId(contractId);
        contractApproval.setFkStaffId(staffId);
        contractApproval.setApprovalStatus(approvalStatus);
        contractApproval.setApprovalComment(approvalComment);
        contractApproval.setApprovalTime(new Date());
        contractApproval.setGmtCreate(new Date());
        contractApproval.setGmtModified(new Date());
        contractApproval.setGmtModified(new Date());
        contractApproval.setGmtCreateUser(SecureUtil.getLoginId());
        contractApproval.setGmtModifiedUser(SecureUtil.getLoginId());
        return contractApproval;
    }

    @Override
    public void approvalContract(ApprovalContractDto approvalContractDto) {
        InstitutionProviderContract contract = contractMapper.selectById(approvalContractDto.getContractId());
        if (Objects.isNull(contract)) {
            log.error("审批失败,合同不存在，合同id：{}", approvalContractDto.getContractId());
//            throw new GetServiceException("合同不存在");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_CONTRACT_NOT_FOUND","合同不存在"));
        }
        List<InstitutionProviderContractApproval> approvalList = getApprovalList(contract.getId());
        if (CollectionUtils.isEmpty(approvalList)) {
            log.error("审批失败,合同审批记录不存在，合同id：{}", approvalContractDto.getContractId());
//            throw new GetServiceException("合同审批记录不存在");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_CONTRACT_RECORD_NOT_FOUND","合同审批记录不存在"));
        }
        if (!approvalList.get(0).getFkStaffId().equals(SecureUtil.getStaffId())) {
            log.error("审批失败,非合同指定审批人，合同id：{},审批人id：{},登录人id：{}", approvalContractDto.getContractId(),
                    approvalList.get(0).getFkStaffId(), SecureUtil.getStaffId());
//            throw new GetServiceException("审核失败,当前用户不是合同指定审批人");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_NOT_APPROVER","审核失败,当前用户不是指定审批人"));
        }

        InstitutionProviderContractApproval contractApproval = getInstitutionProviderContractApproval(contract.getId(), approvalList.get(0).getFkStaffId(),
                approvalContractDto.getApprovalStatus(), approvalContractDto.getApprovalComment());
        institutionProviderContractApprovalMapper.insert(contractApproval);
        contract.setApprovalStatus(approvalContractDto.getApprovalStatus());
        contract.setGmtModified(new Date());
        contract.setGmtModifiedUser(SecureUtil.getLoginId());
        contractMapper.updateById(contract);
    }

    @Override
    public List<InstitutionProviderContractApproval> getApprovalList(Long contractId) {
        return institutionProviderContractApprovalMapper.getApprovalList(contractId);
    }
}
