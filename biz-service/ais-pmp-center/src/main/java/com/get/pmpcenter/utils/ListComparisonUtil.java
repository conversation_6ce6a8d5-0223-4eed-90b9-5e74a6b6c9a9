package com.get.pmpcenter.utils;

import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @Author:Oliver
 * @Date: 2025/2/28  11:13
 * @Version 1.0
 */
public class ListComparisonUtil {
    public static boolean areListsEqual(List<Long> list1, List<Long> list2) {
        if (Objects.isNull(list1) || Objects.isNull(list2)) {
            return list1 == list2;
        }

        // 如果两个列表长度不一样，直接返回false
        if (list1.size() != list2.size()) {
            return false;
        }

        // 将两个列表转换为 Set 来比较元素是否相同
        Set<Long> set1 = new HashSet<>(list1);
        Set<Long> set2 = new HashSet<>(list2);

        // 如果两个 Set 相等，说明元素相同（忽略顺序）
        return set1.equals(set2);
    }
}
