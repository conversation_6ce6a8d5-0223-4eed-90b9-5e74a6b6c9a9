package com.get.pmpcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.pmpcenter.entity.AgentCommissionTypeAgent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AgentCommissionTypeAgentMapper extends BaseMapper<AgentCommissionTypeAgent> {
    List<AgentCommissionTypeAgent> selectByAgentCommissionTypeId(Long id);

    int deleteByIds(@Param("ids") List<Long> ids);
}
