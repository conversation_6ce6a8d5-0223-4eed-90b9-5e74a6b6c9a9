package com.get.pmpcenter.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.filecenter.vo.FileVo;
import com.get.pmpcenter.dto.common.MediaDto;
import com.get.pmpcenter.dto.common.UpdateMediaFileNameDto;
import com.get.pmpcenter.entity.PmpMediaAndAttached;
import com.get.pmpcenter.enums.MediaTableEnum;
import com.get.pmpcenter.mapper.PmpMediaAndAttachedMapper;
import com.get.pmpcenter.service.PmpMediaAndAttachedService;
import com.get.pmpcenter.vo.common.MediaVo;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONArray;
import net.sf.json.JsonConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:26
 * @Version 1.0
 */
@Service
@Slf4j
public class PmpMediaAndAttachedServiceImpl extends ServiceImpl<PmpMediaAndAttachedMapper, PmpMediaAndAttached> implements PmpMediaAndAttachedService {

    @Autowired
    private PmpMediaAndAttachedMapper pmpMediaAndAttachedMapper;
    @Autowired
    private PmpMediaAndAttachedService pmpMediaAndAttachedService;
    @Resource
    private IFileCenterClient fileCenterClient;

    @Override
    public List<MediaVo> getMediaList(String tableName, List<Long> tableIds) {
        return pmpMediaAndAttachedMapper.selectMediaList(tableName, tableIds);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMedia(String tableName, Long tableId, List<MediaDto> mediaDtoList) {
        List<PmpMediaAndAttached> insertList = new ArrayList<>();
        List<Long> delList = new ArrayList<>();
        List<PmpMediaAndAttached> updateList = new ArrayList<>();
        UserInfo user = SecureUtil.getUser();
        //新增
        List<MediaDto> newMediaList = mediaDtoList.stream().filter(mediaDto -> Objects.isNull(mediaDto.getId()) || mediaDto.getId() < 1).collect(Collectors.toList());
        //编辑
        List<MediaDto> updateMediaList = mediaDtoList.stream().filter(mediaDto -> Objects.nonNull(mediaDto.getId()) && mediaDto.getId() > 1).collect(Collectors.toList());
        //删除
        List<Long> currentList = pmpMediaAndAttachedMapper.selectList(new LambdaQueryWrapper<PmpMediaAndAttached>()
                .eq(PmpMediaAndAttached::getFkTableName, tableName)
                .eq(PmpMediaAndAttached::getFkTableId, tableId)).stream().map(PmpMediaAndAttached::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(currentList)) {
            List<Long> updateIds = updateMediaList.stream().map(MediaDto::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(updateIds)) {
                delList = currentList.stream().filter(id -> !updateIds.contains(id)).collect(Collectors.toList());
            } else {
                delList = currentList;
            }
        }
        if (CollectionUtils.isNotEmpty(newMediaList)) {
            insertList = newMediaList.stream().map(mediaDto -> {
                PmpMediaAndAttached attached = new PmpMediaAndAttached();
                attached.setFkTableName(tableName);
                attached.setFkTableId(tableId);
                attached.setFkFileGuid(mediaDto.getFileGuid());
                attached.setLink(mediaDto.getLink());
                attached.setRemark(mediaDto.getRemark());
                attached.setGmtCreate(Objects.nonNull(mediaDto.getGmtCreate()) ? mediaDto.getGmtCreate() : new Date());
                attached.setGmtCreateUser(user.getLoginId());
                attached.setGmtModified(new Date());
                attached.setGmtCreateUser(user.getLoginId());
                return attached;
            }).collect(Collectors.toList());
        }
        if (CollectionUtils.isNotEmpty(updateMediaList)) {
            updateList = updateMediaList.stream().map(mediaDto -> {
                PmpMediaAndAttached mediaAndAttached = pmpMediaAndAttachedMapper.selectById(mediaDto.getId());
                if (Objects.nonNull(mediaAndAttached)) {
                    mediaAndAttached.setFkFileGuid(mediaDto.getFileGuid());
                    mediaAndAttached.setLink(mediaDto.getLink());
                    mediaAndAttached.setRemark(mediaDto.getRemark());
                    mediaAndAttached.setGmtModified(new Date());
                    mediaAndAttached.setGmtCreateUser(user.getLoginId());
                    return mediaAndAttached;
                } else {
                    return null;
                }
            }).collect(Collectors.toList());
            updateList.removeIf(Objects::isNull);
        }

        if (CollectionUtils.isNotEmpty(insertList)) {
            this.saveBatch(insertList);
        }
        if (CollectionUtils.isNotEmpty(updateList)) {
            this.updateBatchById(updateList);
        }
        if (CollectionUtils.isNotEmpty(delList)) {
            this.removeByIds(delList);
        }
    }

    @Override
    public void delMedia(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        PmpMediaAndAttached mediaAndAttached = pmpMediaAndAttachedMapper.selectById(id);
        if (GeneralTool.isEmpty(mediaAndAttached)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_not_exist"));
        }
        Result<Boolean> result = fileCenterClient.delete(mediaAndAttached.getFkFileGuid(), LoggerModulesConsts.PMPCENTER);
        log.info("删除附件返回结果:{}", JSONObject.toJSONString(result));
        if (result.isSuccess()) {
            int i = pmpMediaAndAttachedMapper.deleteById(id);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public List<FileDto> uploadAttached(MultipartFile[] multipartFiles) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
        Result<List<FileDto>> result = fileCenterClient.uploadAppendix(multipartFiles, LoggerModulesConsts.PMPCENTER);
        log.info("上传附件返回结果:{}", JSONObject.toJSONString(result));
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            JSONArray jsonArray = JSONArray.fromObject(result.getData());
            fileDtos = JSONArray.toList(jsonArray, new FileDto(), new JsonConfig());
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        return fileDtos;
    }

    @Override
    public List<MediaVo> uploadContractAttached(MultipartFile[] multipartFiles, Long tableId, Date gmtCreate, String remark) {
        UserInfo user = SecureUtil.getUser();
        List<FileDto> list = uploadAttached(multipartFiles);
        List<PmpMediaAndAttached> attachedList = list.stream().map(fileDto -> {
            PmpMediaAndAttached attached = new PmpMediaAndAttached();
            attached.setFkTableName(MediaTableEnum.PROVIDER_CONTRACT.getCode());
            attached.setFkTableId(tableId);
            attached.setFkFileGuid(fileDto.getFileGuid());
            attached.setLink(fileDto.getFilePath());
            attached.setGmtCreate(Objects.nonNull(gmtCreate) ? gmtCreate : new Date());
            attached.setGmtCreateUser(user.getLoginId());
            attached.setGmtModified(new Date());
            attached.setRemark(remark);
            attached.setGmtCreateUser(user.getLoginId());
            return attached;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(attachedList)) {
            pmpMediaAndAttachedService.saveBatch(attachedList);
        }
//        FileDto fileDto = list.get(0);
//        PmpMediaAndAttached attached = new PmpMediaAndAttached();
//        attached.setFkTableName(MediaTableEnum.PROVIDER_CONTRACT.getCode());
//        attached.setFkTableId(tableId);
//        attached.setFkFileGuid(fileDto.getFileGuid());
//        attached.setLink(fileDto.getFilePath());
//        attached.setGmtCreate(Objects.nonNull(gmtCreate)                            ? gmtCreate : new Date());
//        attached.setGmtCreateUser(user.getLoginId());
//        attached.setGmtModified(new Date());
//        attached.setRemark(remark);
//        attached.setGmtCreateUser(user.getLoginId());
//        pmpMediaAndAttachedMapper.insert(attached);
        return getMediaList(MediaTableEnum.PROVIDER_CONTRACT.getCode(), Arrays.asList(tableId));
    }

    @Override
    public void downloadFile(HttpServletResponse response, Long id) {
        MediaVo mediaVo = pmpMediaAndAttachedMapper.selectMedia(id);
        if (Objects.isNull(mediaVo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_not_exist"));
        }
        FileVo fileVo = new FileVo();
        fileVo.setFileKey(mediaVo.getFileKey());
        fileVo.setGuid(mediaVo.getFileGuid());
        fileVo.setFileNameOrc(mediaVo.getFileNameOrc());
        fileVo.setType(mediaVo.getFileTypeOrc());
        SaleFileDto saleFileDto = fileCenterClient.getDownloadFile(fileVo).getData();
        log.info("下载附件返回结果:{}", JSONObject.toJSONString(saleFileDto));
        if (Objects.nonNull(saleFileDto)) {
            BufferedOutputStream outputStream = null;
            try {
                outputStream = new BufferedOutputStream(response.getOutputStream());
                response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(new String(mediaVo.getFileName().getBytes("utf-8"), "UTF-8")));
                byte[] bytes = null;
                bytes = saleFileDto.getBytes();
                outputStream.write(bytes);
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                try {
                    if (outputStream != null) {
                        outputStream.flush();
                        outputStream.close();
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } else {
//            throw new GetServiceException("文件下载失败");
            throw new GetServiceException(LocaleMessageUtils.getMessage(SecureUtil.getLocale(),"PMP_FILE_DOWNLOAD_FAILED","文件下载失败"));
        }

    }

    @Override
    public void updateMediaFileName(UpdateMediaFileNameDto updateMediaFileNameDto) {
        pmpMediaAndAttachedMapper.updateFileName(updateMediaFileNameDto.getFileGuid(), updateMediaFileNameDto.getFileName());
    }
}
