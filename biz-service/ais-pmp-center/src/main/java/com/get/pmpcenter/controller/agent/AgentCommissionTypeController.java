package com.get.pmpcenter.controller.agent;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.pmpcenter.dto.agent.AgentCommissionTypeDto;
import com.get.pmpcenter.service.AgentCommissionTypeService;
import com.get.pmpcenter.vo.agent.AgentCommissionTypeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Api(tags = "代理佣金分类")
@RestController
@RequestMapping("/agentCommissionType")
public class AgentCommissionTypeController {
    @Resource
    private AgentCommissionTypeService agentCommissionTypeService;

    /**
     * 新增代理佣金分类
     *
     * @param
     * @return
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.ADD, description = "功能中心/代理佣金分类/新增")
    @PostMapping("addAgentCommissionType")
    public ResponseBo addAgentCommissionType(@RequestBody @Validated AgentCommissionTypeDto agentCommissionTypeDto) {
        return new ResponseBo<>(agentCommissionTypeService.add(agentCommissionTypeDto));
    }

    @ApiOperation(value = "查询接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "功能中心/代理佣金分类/查询佣金分类")
    @PostMapping("selectAgentCommissionType")
    public ResponseBo<AgentCommissionTypeVo> getAgentCommissionTypeList(@RequestBody SearchBean<AgentCommissionTypeDto> page) {
        List<AgentCommissionTypeVo> datas = agentCommissionTypeService.dataList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    @ApiOperation(value = "修改接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "功能中心/代理佣金分类/修改佣金分类")
    @PostMapping("updateAgentCommissionType")
    public ResponseBo<Long> updateAgentCommissionType(@RequestBody AgentCommissionTypeDto agentCommissionTypeDto) {
        return new  ResponseBo<>(agentCommissionTypeService.update(agentCommissionTypeDto));
    }

    @ApiOperation(value = "删除接口", notes = "删除代理佣金分类")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.DELETE, description = "功能中心/代理佣金分类/删除佣金分类")
    @DeleteMapping("deleteAgentCommissionType")
    public ResponseBo deleteAgentCommissionType(@RequestParam("id") Long id) {
        agentCommissionTypeService.delete(id);
        return ResponseBo.ok();
    }

    @ApiOperation(value = "排序接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.EDIT, description = "功能中心/代理佣金分类/排序")
    @PostMapping("sortAgentCommissionType")
    public ResponseBo sortAgentCommissionType(@RequestBody List<AgentCommissionTypeDto> agentCommissionTypeDtos) {
        agentCommissionTypeService.sortAgentCommissionType(agentCommissionTypeDtos);
        return ResponseBo.ok();
    }
}
