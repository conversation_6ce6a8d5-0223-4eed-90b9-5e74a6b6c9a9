package com.get.pmpcenter.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @Author:Oliver
 * @Date: 2025/2/27  17:00
 * @Version 1.0
 * 异步任务配置
 */
@Configuration
@EnableAsync
@Slf4j
public class AsyncConfig {

    private final AsyncExecutorConfig asyncExecutorConfig;

    @Autowired
    public AsyncConfig(AsyncExecutorConfig asyncExecutorConfig) {
        this.asyncExecutorConfig = asyncExecutorConfig;
    }

    /**
     * 佣金CommissionLog异步任务线程池
     *
     * @return
     */
    @Primary
    @Bean(name = "commissionLogTaskExecutor")
    public Executor commissionLogTaskExecutor() {

        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(asyncExecutorConfig.getCorePoolSize());
        executor.setMaxPoolSize(asyncExecutorConfig.getMaxPoolSize());
        executor.setQueueCapacity(asyncExecutorConfig.getQueueCapacity());
        executor.setThreadNamePrefix(asyncExecutorConfig.getThreadNamePrefix() + "commission");
        /**
         * CallerRunsPolicy：当线程池和队列都满时，任务由调用者线程处理。如果调用者线程也满了，
         * 则抛出异常。这个策略是最常见的策略之一，适用于较轻的任务，可以接受任务排队并延迟处理。
         */
        log.info("executor getThreadNamePrefix:{}", asyncExecutorConfig.getThreadNamePrefix());
        log.info("commissionLogTaskExecutor initialized with thread pool size: {}",
                asyncExecutorConfig.getMaxPoolSize());
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
