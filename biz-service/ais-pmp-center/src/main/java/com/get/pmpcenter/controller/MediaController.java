package com.get.pmpcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.SecureUtil;
import com.get.filecenter.dto.FileDto;
import com.get.pmpcenter.dto.common.IdDto;
import com.get.pmpcenter.dto.common.UpdateMediaFileNameDto;
import com.get.pmpcenter.enums.MediaTableEnum;
import com.get.pmpcenter.service.PmpMediaAndAttachedService;
import com.get.pmpcenter.vo.common.MediaVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @DATE: 2020/7/14
 * @TIME: 18:17
 * @Description: 附件管理控制层
 **/

@Api(tags = "附件管理")
@RestController
@RequestMapping("/media")
public class MediaController {

    @Autowired
    private PmpMediaAndAttachedService attachedService;

    @ApiOperation(value = "删除附件", notes = "id为删除数据的ID")
    @OperationLogger(module = LoggerModulesConsts.RESUMECENTER, type = LoggerOptTypeConst.DELETE, description = "附件删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        attachedService.delMedia(id);
        return ResponseBo.ok();
    }


    @ApiOperation(value = "上传附件")
    @OperationLogger(module = LoggerModulesConsts.PMPCENTER, type = LoggerOptTypeConst.ADD, description = "附件上传")
    @PostMapping("/uploadAttached")
    public ResponseBo uploadAttached(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        List<FileDto> upload = attachedService.uploadAttached(files);
        return new ResponseBo<>(upload);
    }

    @ApiOperation(value = "上传合同附件")
    @OperationLogger(module = LoggerModulesConsts.PMPCENTER, type = LoggerOptTypeConst.ADD, description = "附件上传")
    @PostMapping("/uploadContractAttached")
    public ResponseBo<List<MediaVo>> uploadContractAttached(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files,
                                                            @RequestParam("contractId") @NotNull(message = "合同id不能为空") Long contractId,
                                                            @RequestParam("gmtCreate") String gmtCreate,
                                                            String remark) throws ParseException {
        Date date = new Date();
        if (StringUtils.isNotBlank(gmtCreate)) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            date = sdf.parse(gmtCreate);
        }
        List<MediaVo> mediaVos = attachedService.uploadContractAttached(files, contractId, date, remark);
        return new ResponseBo<>(mediaVos);
    }

    @VerifyLogin(IsVerify = false)
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "下载文件")
    @PostMapping("/download")
    public void download(HttpServletResponse response, @RequestBody IdDto idDto) {
         //设置文件路径
        response.setHeader("content-type", "application/octet-stream");
        response.setContentType("application/octet-stream");
        attachedService.downloadFile(response, idDto.getId());
    }

    @ApiOperation(value = "修改附件名称")
    @PostMapping("/updateMediaFileName")
    public ResponseBo updateMediaFileName(@RequestBody @Valid UpdateMediaFileNameDto updateMediaFileNameDto) {
        attachedService.updateMediaFileName(updateMediaFileNameDto);
        String message = LocaleMessageUtils.getMessage(SecureUtil.getLocale(), "PMP_SAVE_SUCCESS", "保存成功");
        return new ResponseBo<>(message);
    }

    @ApiOperation(value = "附件列表", notes = "type:附件类型1:合同附件;2:方案附件;id:合同或者方案id")
    @PostMapping("/getMediaList")
    public ResponseBo<List<MediaVo>> getMediaList(@ApiParam(value = "附件类型1:合同附件;2:方案附件") Integer type,
                                                  @ApiParam(value = "合同或者方案id,根据类型区分") Long id) {
        String tableName = type.equals(1) ? MediaTableEnum.PROVIDER_CONTRACT.getCode() : MediaTableEnum.PROVIDER_COMMISSION_PLAN.getCode();
        return new ResponseBo<>(attachedService.getMediaList(tableName, Arrays.asList(id)));
    }

}
