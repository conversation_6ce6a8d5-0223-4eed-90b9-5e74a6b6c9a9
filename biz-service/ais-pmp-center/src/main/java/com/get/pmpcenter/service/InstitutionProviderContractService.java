package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.pmpcenter.dto.common.IdDto;
import com.get.pmpcenter.dto.common.IdListDto;
import com.get.pmpcenter.dto.institution.EnableProviderContractDto;
import com.get.pmpcenter.dto.institution.LockContractDto;
import com.get.pmpcenter.dto.institution.ProviderContractPageDto;
import com.get.pmpcenter.dto.institution.SaveProviderContractDto;
import com.get.pmpcenter.entity.InstitutionProviderContract;
import com.get.pmpcenter.vo.common.CountryVo;
import com.get.pmpcenter.vo.institution.*;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface InstitutionProviderContractService extends IService<InstitutionProviderContract> {

    /**
     * 保存供应商合同
     *
     * @param saveProviderContractDto
     */
    Long saveProviderContract(SaveProviderContractDto saveProviderContractDto, Boolean updateProvider);

    /**
     * 启用/停用供应商合同
     *
     * @param enableProviderContractDto
     */
    void enableProviderContract(EnableProviderContractDto enableProviderContractDto);

    /**
     * 供应商合同分页
     *
     * @param params
     * @param page
     * @return
     */
    List<ProviderContractVo> providerContractPage(ProviderContractPageDto params, Page page);

    /**
     * 供应商合同详情
     *
     * @param id
     * @return
     */
    ProviderContractDetailVo providerContractDetail(Long id);

    /**
     * 获取供应商合同基本信息
     *
     * @param id
     * @return
     */
    InstitutionProviderContract getProviderContractBaseInfo(Long id);

    /**
     * 合同类型列表
     *
     * @return
     */
    List<ContractTypeVo> getContractTypeList();


    /**
     * 合同详情
     *
     * @param id
     * @return
     */
    ContractDetailVo renewalContractDetail(Long id);


    /**
     * 续签合同
     *
     * @param contractDetail
     * @return
     */
    Long renewalContract(ContractDetailVo contractDetail);

    /**
     * 删除合同
     *
     * @param id
     */
    void delContract(IdListDto ids);

    /**
     * 锁定、解锁合同
     *
     * @param lockContractDto
     */
    void lockContract(LockContractDto lockContractDto);

    /**
     * 获取已经过期的方案ID
     *
     * @return
     */
    List<Long> getExpirePlanIds(Long institutionProviderId);

    /**
     * 检查方案权限
     *
     * @param providerCommissionPlanVo
     * @return
     */
    ProviderCommissionPlanVo checkPlanPermission(ProviderCommissionPlanVo providerCommissionPlanVo);

    /**
     * 国家列表-根据权限过滤
     *
     * @return
     */
    List<CountryVo> getCountryList();

}
