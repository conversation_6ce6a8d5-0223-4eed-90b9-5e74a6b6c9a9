package com.get.pmpcenter.event.listener;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.get.pmpcenter.entity.LogOperation;
import com.get.pmpcenter.event.CommissionChangedEvent;
import com.get.pmpcenter.service.LogOperationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/2/27  17:19
 * @Version 1.0
 */
@Service
@Slf4j
public class CommissionChangedListener {

    @Autowired
    private LogOperationService logOperationService;

    @Async("commissionLogTaskExecutor")
    @EventListener
    @Transactional(rollbackFor = Exception.class)
    public void onCommissionChanged(CommissionChangedEvent event) {

        List<LogOperation> operationList = event.getLogs().stream().map(l -> {
            log.info("佣金日志事件:{}", JSONObject.toJSONString(l));
            LogOperation logOperation = new LogOperation();
            logOperation.setFkTableName(l.getTableName());
            logOperation.setFkTableId(l.getTableId());
            logOperation.setOperationName(l.getAction());
            logOperation.setOperationDescription(l.getDesc());
            logOperation.setGmtCreate(new Date());
            logOperation.setGmtCreateUser(l.getLoginId());
            logOperation.setGmtModified(new Date());
            logOperation.setGmtModifiedUser(l.getLoginId());
            logOperation.setFkInstitutionProviderContractId(l.getFkInstitutionProviderContractId());
            return logOperation;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(operationList)) {
            logOperationService.saveBatch(operationList);
        }
    }
}
