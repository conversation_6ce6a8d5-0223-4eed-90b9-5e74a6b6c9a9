package com.get.pmpcenter;

import com.get.common.constant.AppCenterConstant;
import com.get.core.cloud.feign.EnableGetFeign;
import com.get.core.start.GetApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.cloud.client.SpringCloudApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * <AUTHOR>
 */
@EnableGetFeign
@SpringCloudApplication
@MapperScan("com.get.pmpcenter.mapper")
@EnableAsync
@ComponentScan(basePackages = {"com.get.pmpcenter","com.get.institutioncenter.feign"})
public class AisPmpCenterApplication {
    public static void main(String[] args) {
        GetApplication.run(AppCenterConstant.APPLICATION_PMP_CENTER, AisPmpCenterApplication.class, args);
    }

}
