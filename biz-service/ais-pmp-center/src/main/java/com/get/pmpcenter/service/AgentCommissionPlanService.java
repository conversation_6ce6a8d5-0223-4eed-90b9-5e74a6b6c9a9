package com.get.pmpcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.pmpcenter.dto.agent.AgentCommissionPlanListDto;
import com.get.pmpcenter.dto.agent.InstitutionAgentPlanDto;
import com.get.pmpcenter.dto.agent.TimeOverlapVerifyDto;
import com.get.pmpcenter.dto.agent.UpdateAgentCommissionPlanDto;
import com.get.pmpcenter.dto.common.UpdatePlanStatusDto;
import com.get.pmpcenter.entity.AgentCommissionPlan;
import com.get.pmpcenter.vo.agent.*;
import com.get.pmpcenter.vo.common.CountryVo;
import com.get.pmpcenter.vo.common.InstitutionVo;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  15:17
 * @Version 1.0
 */
public interface AgentCommissionPlanService extends IService<AgentCommissionPlan> {

    /**
     * 保存代理佣金计划
     *
     * @param saveAgentCommissionPlanDto
     * @return
     */
    Long updateAgentCommissionPlan(UpdateAgentCommissionPlanDto saveAgentCommissionPlanDto);

    /**
     * 获取代理佣金计划基本信息
     *
     * @param id
     * @return
     */
    AgentCommissionPlanBaseInfoVo getAgentCommissionPlanBaseInfo(Long id);

    /**
     * 获取代理佣金计划列表
     *
     * @param institutionProviderId
     * @return
     */
    List<AgentCommissionPlanVo> getAgentCommissionPlanList(Long institutionProviderId, Long companyId);

    /**
     * 获取代理佣金计划适用学校列表
     *
     * @param institutionProviderId -学校提供商ID
     * @param id                    -代理佣金方案ID、学校提供商佣金方案ID，根据type区分
     * @param type                  1:代理佣金方案，2：学校提供商佣金方案
     * @return
     */
    List<InstitutionVo> getAgentInstitutionList(Integer type, Long institutionProviderId, Long id);

    /**
     * 获取模板列表
     *
     * @param institutionProviderId
     * @param companyId
     * @return
     */
    List<ExtendTemplateVo> getExtendTemplateList(Long institutionProviderId, Long companyId);

    /**
     * 删除代理佣金方案
     *
     * @param id
     */
    void delAgentCommissionPlan(Long id);

    /**
     * 失效代理佣金计划
     *
     * @param providerPlanIds
     */
    void ineffectiveAgentCommissionPlan(List<Long> providerPlanIds);

    /**
     * 根据学校提供商佣金方案ID删除代理佣金方案
     *
     * @param providerPlanId
     */
    void delAgentCommissionPlanByProviderPlanId(Long providerPlanId);


    /**
     * 校验代理佣金方案权限
     *
     * @param agentCommissionPlanVo
     * @return
     */
    AgentCommissionPlanVo checkAgentCommissionPlanPermission(AgentCommissionPlanVo agentCommissionPlanVo);


    /**
     * 修改代理佣金方案名称
     *
     * @param providerCommissionPlanId
     * @param name
     */
    void updateAgentCommissionPlanName(Long providerCommissionPlanId, String name);


    /**
     * 上下架代理方案
     *
     * @param statusDto
     */
    void updateAgentCommissionPlanStatus(UpdatePlanStatusDto statusDto);

    /**
     * 代理佣金方案列表
     *
     * @param params
     * @param page
     * @return
     */
    List<AgentCommissionPlanListVo> agentCommissionPlanList(AgentCommissionPlanListDto params, Page page);


    /**
     * 代理合同国家线列表
     *
     * @return
     */
    List<CountryVo> getAgentCountryList();

    /**
     * 已读代理佣金方案调整标记
     *
     * @param planId
     */
    void readAgentCommissionPlanModify(Long planId);


    /**
     * 根据学校获取代理佣金方案
     *
     * @param agentPlanDto
     * @return
     */
    List<InstitutionAgentPlanListVo> getInstitutionAgentPlanList(InstitutionAgentPlanDto agentPlanDto);

    /**
     * 判断代理佣金方案下的学校是否包含多个方案
     *
     * @param planId
     * @param institutionIds
     * @return
     */
    Integer hasManyPlans(Long planId, List<Long> institutionIds);

    /**
     * 排序代理佣金方案
     *
     * @param sortList
     */
    void sortAgentPlan(List<Long> sortList);


    /**
     * 获取时间重叠方案
     * @param verifyDto
     * @return
     */
    List<TimeOverlapPlanVo> getTimeOverlapPlanList(TimeOverlapVerifyDto verifyDto);


    /**
     * 获取用户权限的方案ID-根据分公司/业务国家/权限组别
     * @return
     */
    UserPlanIds getUserPlanIds(Long companyId);

    /**
     * 获取学校下代理佣金方案列表-AI助手用
     * @param institutionName
     * @return
     */
    List<ExtendAgentCommissionVo> getInstitutionAgentCommissionList(String institutionName);

    /**
     * 拖拽
     * @param start
     * @param end
     */
    void movingOrder(Integer start, Integer end);

    /**
     * 获取学校下代理佣金方案列表-AI助手用
     * @param institutionName
     * @return
     */
    List<AiInstitutionCommissionVo> getAiInstitutionCommissionList(String institutionName);
}
