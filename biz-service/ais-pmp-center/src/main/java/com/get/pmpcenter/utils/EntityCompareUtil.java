package com.get.pmpcenter.utils;

import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * @Author:Oliver
 * @Date: 2025/2/28  10:29
 * @Version 1.0
 * 实体类对比工具类
 */
@Slf4j
public class EntityCompareUtil {

    // 忽略的字段名，可以根据需要定制
    private static final Set<String> IGNORE_FIELDS = new HashSet<String>() {{
        add("gmtCreateUser");
        add("gmtCreate");
        add("gmtModified");
        add("gmtModifiedUser");
    }};

    /**
     * 对比两个对象的字段是否发生变化，忽略某些字段
     *
     * @param original 原始对象
     * @param updated  更新后的对象
     * @param <T>      泛型类型，适用于任何实体类
     * @return 如果有任何字段变化，返回true，否则返回false
     */
    public static <T> boolean compareFields(T original, T updated) {
        // 获取对象的类类型
        Class<?> clazz = original.getClass();

        // 获取类中的所有字段
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            // 设置字段可以访问
            field.setAccessible(true);

            // 如果字段名在忽略列表中，则跳过
            if (IGNORE_FIELDS.contains(field.getName())) {
                continue;
            }

            try {
                // 获取字段的值
                Object originalValue = field.get(original);
                Object updatedValue = field.get(updated);

                // 如果值是 BigDecimal 类型，进行特别的比较
                if (originalValue instanceof BigDecimal && updatedValue instanceof BigDecimal) {
                    // 将 BigDecimal 转换为标准的字符串表示进行比较，忽略精度差异
                    String originalStr = ((BigDecimal) originalValue).stripTrailingZeros().toPlainString();
                    String updatedStr = ((BigDecimal) updatedValue).stripTrailingZeros().toPlainString();

                    // 比较字符串值
                    if (!originalStr.equals(updatedStr)) {
                        log.info("字段{}的值发生变化，原始值：{}，更新值：{}", field.getName(), originalStr, updatedStr);
                        return true;
                    }
                } else {
                    // 对于其他类型的字段，使用原有的比较方式
                    if (!Objects.equals(originalValue, updatedValue)) {
                        log.info("字段{}的值发生变化，原始值：{}，更新值：{}", field.getName(), originalValue, updatedValue);
                        return true;
                    }
                }

            } catch (IllegalAccessException e) {
                log.info("字段访问异常:{},异常信息", field.getName(), e.getMessage());
                e.printStackTrace();
            }
        }
        // 如果所有字段都没有变化，返回false
        return false;
    }
}
