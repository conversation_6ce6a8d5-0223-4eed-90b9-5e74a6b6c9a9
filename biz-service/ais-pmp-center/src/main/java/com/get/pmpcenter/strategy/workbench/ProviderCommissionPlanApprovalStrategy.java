package com.get.pmpcenter.strategy.workbench;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.permissioncenter.enums.WorkbenchApprovalTypeEnum;
import com.get.permissioncenter.vo.workbench.WorkbenchApprovalVo;
import com.get.pmpcenter.dto.agent.PmpWorkbenchApprovalDto;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlan;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlanApproval;
import com.get.pmpcenter.enums.ApprovalStatusEnum;
import com.get.pmpcenter.enums.ProviderCommissionPlanApprovalTypeEnum;
import com.get.pmpcenter.mapper.InstitutionProviderCommissionPlanApprovalMapper;
import com.get.pmpcenter.mapper.InstitutionProviderCommissionPlanMapper;
import com.get.pmpcenter.mapper.PermissionCenterMapper;
import com.get.pmpcenter.strategy.WorkbenchApprovalStrategy;
import com.get.pmpcenter.vo.common.StaffVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/4/27
 * @Version 1.0
 * @apiNote:
 */
@Component
public class ProviderCommissionPlanApprovalStrategy implements WorkbenchApprovalStrategy {

    @Autowired
    private InstitutionProviderCommissionPlanApprovalMapper commissionPlanApprovalMapper;
    @Autowired
    private PermissionCenterMapper permissionCenterMapper;
    @Autowired
    private InstitutionProviderCommissionPlanMapper institutionProviderCommissionPlanMapper;

    @Override
    public List<WorkbenchApprovalVo> getApprovalList(PmpWorkbenchApprovalDto workbenchApprovalDto) {
        List<InstitutionProviderCommissionPlanApproval> approvals;
        if (Objects.nonNull(workbenchApprovalDto.getApprovalStatus())) {
            approvals = getApprovalListByStatus(workbenchApprovalDto.getStaffId()
                    , workbenchApprovalDto.getApprovalStatus(), workbenchApprovalDto.getLoginId());
        } else {
            approvals = new ArrayList<>();
            List<InstitutionProviderCommissionPlanApproval> finalApprovals = approvals;
            Arrays.asList(ApprovalStatusEnum.PENDING_APPROVAL.getCode(), ApprovalStatusEnum.REJECT.getCode()).stream().forEach(status -> {
                List<InstitutionProviderCommissionPlanApproval> statusList = getApprovalListByStatus(workbenchApprovalDto.getStaffId(),
                        status, workbenchApprovalDto.getLoginId());
                finalApprovals.addAll(statusList);
            });
        }
        if (CollectionUtils.isNotEmpty(approvals)) {
            Map<Long, InstitutionProviderCommissionPlan> planMap = institutionProviderCommissionPlanMapper.selectList(
                            new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                                    .in(InstitutionProviderCommissionPlan::getId,
                                            approvals.stream().map(InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds)
                                                    .flatMap(s -> Arrays.stream(s.split(","))).collect(Collectors.toList())))
                    .stream()
                    .collect(Collectors.toMap(
                            InstitutionProviderCommissionPlan::getId,
                            Function.identity(),
                            (existing, replacement) -> replacement));
            List<String> initiatorLoginIds = approvals.stream().map(InstitutionProviderCommissionPlanApproval::getGmtCreateUser).distinct().collect(Collectors.toList());
            List<String> passOrRejectPlanIds = approvals.stream().filter(approval -> approval.getApprovalStatus() >= ApprovalStatusEnum.PASS.getCode())
                    .map(InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds)
                    .collect(Collectors.toList());
            Map<String, String> passOrRejectPlanApprovalMap = Collections.emptyMap();
            if (CollectionUtils.isNotEmpty(passOrRejectPlanIds)) {
                passOrRejectPlanApprovalMap = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                                .eq(InstitutionProviderCommissionPlanApproval::getTypeKey, ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode())
                                .eq(InstitutionProviderCommissionPlanApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
                                .in(InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds, passOrRejectPlanIds))
                        .stream()
                        .collect(Collectors.groupingBy(
                                InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds,
                                Collectors.collectingAndThen(
                                        Collectors.maxBy(Comparator.comparing(InstitutionProviderCommissionPlanApproval::getGmtCreate)),
                                        opt -> opt.map(InstitutionProviderCommissionPlanApproval::getGmtCreateUser).orElse(null)
                                )
                        ));
                passOrRejectPlanApprovalMap = passOrRejectPlanApprovalMap.entrySet().stream()
                        .filter(entry -> entry.getValue() != null)
                        .collect(Collectors.toMap(
                                Map.Entry::getKey,
                                Map.Entry::getValue
                        ));
                initiatorLoginIds.addAll(passOrRejectPlanApprovalMap.values());
            }
            List<StaffVo> staffVos = permissionCenterMapper.getStaffListByLoginIds(initiatorLoginIds);
            Map<String, String> nameMap = staffVos.stream().collect(Collectors.toMap(StaffVo::getLoginId, StaffVo::getName));
            Map<String, String> finalPassOrRejectPlanApprovalMap = passOrRejectPlanApprovalMap;
            return approvals.stream().map(approval -> {
                if (StringUtils.isEmpty(approval.getFkInstitutionProviderCommissionPlanIds())) {
                    return null;
                }
                Long id = Long.parseLong(approval.getFkInstitutionProviderCommissionPlanIds().split(",")[0]);
                Map<String, Object> defData = new HashMap<>();
                defData.put("contractId", Objects.nonNull(planMap.get(id)) ? planMap.get(id).getFkInstitutionProviderContractId() : null);
                defData.put("providerCommissionPlanId", id);
                WorkbenchApprovalVo vo = new WorkbenchApprovalVo();
                vo.setId(Objects.nonNull(planMap.get(id)) ? planMap.get(id).getId() : null);
                vo.setName(Objects.nonNull(planMap.get(id)) ? planMap.get(id).getName() : "");
                vo.setDefData(defData);
                vo.setApprovalType(WorkbenchApprovalTypeEnum.PMP_PROVIDER.getCode());
                vo.setApprovalStatus(approval.getApprovalStatus());
                vo.setGmtCreate(approval.getGmtCreate());
                if (approval.getApprovalStatus() >= ApprovalStatusEnum.PASS.getCode()) {
                    String initiatorId = finalPassOrRejectPlanApprovalMap.getOrDefault(id.toString(), "");
                    if (StringUtils.isNotBlank(initiatorId)) {
                        vo.setInitiatorIdName(nameMap.getOrDefault(initiatorId, ""));
                    }
                } else {
                    vo.setInitiatorIdName(nameMap.getOrDefault(approval.getGmtCreateUser(), ""));
                }
                return vo;
            }).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public String getApprovalType() {
        return WorkbenchApprovalTypeEnum.PMP_PROVIDER.getCode();
    }

    private List<InstitutionProviderCommissionPlanApproval> getApprovalListByStatus(Long staffId, Integer approvalStatus, String loginId) {
        //只需要查询待审核和审核失败的方案 只会有1和3两种状态：1：需要我审核的 3：我提交的但是是审核失败的
        //去重-保留最新一条
        if (approvalStatus.equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode())) {
            List<InstitutionProviderCommissionPlanApproval> approvals = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                            .eq(InstitutionProviderCommissionPlanApproval::getTypeKey, ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode())
                            .eq(InstitutionProviderCommissionPlanApproval::getFkStaffId, staffId)
                            .eq(InstitutionProviderCommissionPlanApproval::getApprovalStatus, approvalStatus)
                            .exists("select 1 from m_institution_provider_commission_plan where" +
                                    " id = m_institution_provider_commission_plan_approval.fk_institution_provider_commission_plan_ids and approval_status = 1 and is_active = 1"))
                    .stream()
                    .filter(item -> item.getFkInstitutionProviderCommissionPlanIds() != null)
                    .collect(Collectors.toMap(
                            InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds,
                            Function.identity(),
                            (a, b) -> a.getGmtCreate().after(b.getGmtCreate()) ? a : b))
                    .values()
                    .stream()
                    .collect(Collectors.toList());
            return approvals.stream().map(approval -> {
                List<InstitutionProviderCommissionPlanApproval> approvalList = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                        .eq(InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds, approval.getFkInstitutionProviderCommissionPlanIds())
                        .orderByDesc(InstitutionProviderCommissionPlanApproval::getGmtCreate));
                InstitutionProviderCommissionPlanApproval latestPlanApproval = approvalList.get(0);
                if (latestPlanApproval.getApprovalStatus().equals(ApprovalStatusEnum.PASS.getCode())
                        || latestPlanApproval.getApprovalStatus().equals(ApprovalStatusEnum.REJECT.getCode())) {
                    //如果最新的审核记录存在审核通过或者拒绝的记录,就说明该批量审批不是处于待审核的状态
                    return null;
                }
                //如果最新的审核记录不是批量审核,剔除
                if (!latestPlanApproval.getTypeKey().equals(ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode())) {
                    return null;
                }
                //如果最新的审核记录的审核人不是当前用户,剔除
                if (!latestPlanApproval.getFkStaffId().equals(staffId)) {
                    return null;
                }
                return approval;
            }).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        }
        //3：我提交的但是是审核失败的(提审人不一定是创建人,提审人可能是创建人,也有可能是有全局提交审核权限的人)
        //只查询上架的方案
        List<InstitutionProviderCommissionPlanApproval> approvals = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                        .eq(InstitutionProviderCommissionPlanApproval::getTypeKey, ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode())
                        .eq(InstitutionProviderCommissionPlanApproval::getApprovalStatus, approvalStatus)
                        .exists("select 1 from m_institution_provider_commission_plan where" +
                                " id = m_institution_provider_commission_plan_approval.fk_institution_provider_commission_plan_ids and approval_status = " + approvalStatus +
//                                " and is_active = 1 and gmt_create_user = '" + loginId + "'"))
                                " and is_active = 1"))
                .stream()
                .filter(item -> item.getFkInstitutionProviderCommissionPlanIds() != null)
                .collect(Collectors.toMap(
                        InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds,
                        Function.identity(),
                        (a, b) -> a.getGmtCreate().after(b.getGmtCreate()) ? a : b))
                .values()
                .stream()
                .collect(Collectors.toList());
        return approvals.stream().map(approval -> {
            List<InstitutionProviderCommissionPlanApproval> approvalList = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
                    .eq(InstitutionProviderCommissionPlanApproval::getFkInstitutionProviderCommissionPlanIds, approval.getFkInstitutionProviderCommissionPlanIds())
                    .orderByDesc(InstitutionProviderCommissionPlanApproval::getGmtCreate));
            InstitutionProviderCommissionPlanApproval latestPlanApproval = approvalList.get(0);
            if (!latestPlanApproval.getApprovalStatus().equals(ApprovalStatusEnum.REJECT.getCode())) {
                //如果存在非拒绝的记录,就说明该审批不是处于审核失败的状态
                return null;
            }
            //如果最新的审核记录不是当个审核,剔除
            if (!latestPlanApproval.getTypeKey().equals(ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode())) {
                return null;
            }
            //查询最新的提交审核的记录,判断提交人是否是当前用户
            Optional<InstitutionProviderCommissionPlanApproval> latestSubmitApproval = approvalList.stream()
                    .filter(item -> item.getApprovalStatus().equals(ApprovalStatusEnum.PENDING_APPROVAL.getCode()))
                    .max(Comparator.comparing(InstitutionProviderCommissionPlanApproval::getGmtCreate));
            if (!latestSubmitApproval.isPresent()) {
                return null;
            }
            InstitutionProviderCommissionPlanApproval latestSubmit = latestSubmitApproval.get();
            if (!latestSubmit.getGmtCreateUser().equals(loginId)) {
                return null;
            }
            return approval;
        }).collect(Collectors.toList()).stream().filter(Objects::nonNull).collect(Collectors.toList());
        //0-待审核-需要指定员工审核
//        if (approvalStatus.equals(ApprovalStatusEnum.UN_COMMITTED.getCode())) {
//            return commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
//                    .eq(InstitutionProviderCommissionPlanApproval::getFkStaffId, staffId)
//                    .eq(InstitutionProviderCommissionPlanApproval::getTypeKey, ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode())
//                    .eq(InstitutionProviderCommissionPlanApproval::getApprovalStatus, ApprovalStatusEnum.PENDING_APPROVAL.getCode())
//                    .exists("select 1 from m_institution_provider_commission_plan where" +
//                            " id = m_institution_provider_commission_plan_approval.fk_institution_provider_commission_plan_ids and approval_status = 1 "));
//        }
//        //1-审核中-表示当前员工创建的需要等待其他人审核
//        //2-已通过-当前员工创建的并且审核通过的+当前员工审核通过的
//        //3-已拒绝-当前员工创建的并且审核拒绝的+当前员工审核拒绝的
//        if (approvalStatus >= ApprovalStatusEnum.PENDING_APPROVAL.getCode()) {
//            //当前员工创建的并且是指定状态的方案
//            List<InstitutionProviderCommissionPlanApproval> approvalList = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
//                    .eq(InstitutionProviderCommissionPlanApproval::getTypeKey, ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode())
//                    .eq(InstitutionProviderCommissionPlanApproval::getApprovalStatus, approvalStatus)
//                    .exists("select 1 from m_institution_provider_commission_plan where" +
//                            " id = m_institution_provider_commission_plan_approval.fk_institution_provider_commission_plan_ids and approval_status = " + approvalStatus +
//                            " and gmt_create_user = '" + loginId + "'"));
//            if (approvalStatus >= ApprovalStatusEnum.PASS.getCode()) {
//                //当前员工审核通过或者驳回的方案
//                List<InstitutionProviderCommissionPlanApproval> currentApprovalList = commissionPlanApprovalMapper.selectList(new LambdaQueryWrapper<InstitutionProviderCommissionPlanApproval>()
//                        .eq(InstitutionProviderCommissionPlanApproval::getFkStaffId, staffId)
//                        .eq(InstitutionProviderCommissionPlanApproval::getTypeKey, ProviderCommissionPlanApprovalTypeEnum.SINGLE.getCode())
//                        .eq(InstitutionProviderCommissionPlanApproval::getApprovalStatus, approvalStatus)
//                        .exists("select 1 from m_institution_provider_commission_plan where" +
//                                " id = m_institution_provider_commission_plan_approval.fk_institution_provider_commission_plan_ids and approval_status =  " + approvalStatus));
//                approvalList.addAll(currentApprovalList);
//            }
//            return approvalList;
//        }
//        return Collections.emptyList();
    }
}
