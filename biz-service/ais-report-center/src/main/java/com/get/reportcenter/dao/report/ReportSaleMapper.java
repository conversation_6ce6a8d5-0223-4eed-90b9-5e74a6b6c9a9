package com.get.reportcenter.dao.report;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.reportcenter.dto.ReportSaleDto;
import com.get.reportcenter.entity.ReportSale;
//import com.get.reportcenter.vo.ReportSaleVo;
import com.get.salecenter.dto.StudentApplicationStatisticsDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface ReportSaleMapper extends BaseMapper<ReportSale> {

    /**
     * 获取上次查询的报表记录
     * <AUTHOR>
     * @DateTime 2023/1/3 16:23
     */
    ReportSale getLastReportSale(@Param("fkStaffId") Long fkStaffId);

    /**
     * 用户ID获取报表
     * <AUTHOR>
     * @DateTime 2023/1/3 16:23
     */
    ReportSale getReportSale(@Param("studentApplicationStatisticsDto") StudentApplicationStatisticsDto studentApplicationStatisticsDto,
                             @Param("fkUserId") Long fkUserId);

    /**
     * 更新报表状态
     * <AUTHOR>
     * @DateTime 2023/1/3 16:23
     */
    void updateReportSaleStatus(@Param("fkReportSaleId") Long fkReportSaleId,@Param("reportStatus") Integer reportStatus);

    /**
     * 新增
     * @param reportSaleVo
     * @return
     */
    Long insertSelective(ReportSaleDto reportSaleVo);


    Integer getReportSaleStatusById(@Param("fkReportSaleId")Long fkReportSaleId);

    ReportSaleDto getReportSaleById(@Param("fkReportSaleId")Long fkReportSaleId);

    ReportSaleDto getLastReportSaleVoByReportNameAndUserId(@Param("key")String key,@Param("staffId") Long staffId);
}