package com.get.reportcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
//import com.get.competitioncenter.dto1.CompetitionRegistrationListDto;
import com.get.core.log.annotation.OperationLogger;
import com.get.officecenter.dto.query.LeaveApplicationFormQueryDto;
import com.get.reportcenter.vo.AdvSearchConfigVo;
import com.get.reportcenter.service.IAdvSearchConfigService;
import com.get.reportcenter.service.IExportService;
import com.get.reportcenter.dto.AdvSearchRunDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/3/5
 * @TIME: 10:03
 * @Description:
 **/
@Api(tags = "报表管理")
@RestController
@RequestMapping("report/advSearchConfig")
public class AdvSearchConfigController {
    @Resource
    private IAdvSearchConfigService advSearchConfigService;
    @Resource
    private IExportService exportService;

    /**
     * 列表数据
     *
     * @param queryKey
     * @return
     */
    @ApiOperation(value = "获取高级搜索配置", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REPORTCENTER, type = LoggerOptTypeConst.LIST, description = "报表中心/高级搜索管理/查询高级搜索配置")
    @GetMapping("getAdvSearchConfigByKey/{queryKey}")
    public ResponseBo<AdvSearchConfigVo> getAdvSearchConfigByKey(@PathVariable("queryKey") String queryKey) {
        AdvSearchConfigVo data = advSearchConfigService.getAdvSearchConfigByQueryKey(queryKey);
        return new ResponseBo<>(data);
    }

    /**
     * 获取高级搜索结果
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "获取高级搜索结果", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REPORTCENTER, type = LoggerOptTypeConst.LIST, description = "报表中心/高级搜索管理/获取搜索结果")
    @PostMapping("getDatasByAdvSearchRunVo")
    public ResponseBo<LinkedHashMap<String, String>> getDatasByAdvSearchRunVo(@RequestBody SearchBean<List<AdvSearchRunDto>> page) {
        List<LinkedHashMap<String, String>> datas = advSearchConfigService.getDatasByAdvSearchRunVo(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<LinkedHashMap<String, String>>(datas, p);
    }

    @ApiOperation(value = "导出高级搜索Excel")
    @PostMapping("/exportSearchExcel")
    @OperationLogger(module = LoggerModulesConsts.REPORTCENTER, type = LoggerOptTypeConst.LIST, description = "报表中心/报表管理/导出高级搜索Excel")
    @ResponseBody
    public void exportSearchExcel(HttpServletResponse response, @RequestBody List<AdvSearchRunDto> advSearchRunDtos) {
        advSearchConfigService.exportEventExcel(response, advSearchRunDtos);
    }

    /**
     * @return void
     * @Description :导出参会人员名册Excel
     * @Param [response, conventionSponsorVo]
     * <AUTHOR>
     */
//    @ResponseBody
//    @ApiOperation(value = "导出竞赛报名名册Excel")
//    @OperationLogger(module = LoggerModulesConsts.REPORTCENTER, type = LoggerOptTypeConst.LIST, description = "报表中心/报表管理/导出竞赛报名名册Excel")
//    @PostMapping("/exportCompetitionRegistrationExcel")
//    public void exportCompetitionRegistrationExcel(HttpServletResponse response, @RequestBody CompetitionRegistrationListDto competitionRegistrationListVo) {
//        exportService.exportCompetitionRegistrationExcel(response, competitionRegistrationListVo);
//    }

    @ResponseBody
    @ApiOperation(value = "导出工休单Excel")
    @OperationLogger(module = LoggerModulesConsts.REPORTCENTER, type = LoggerOptTypeConst.LIST, description = "报表中心/报表管理/导出工休单Excel")
    @PostMapping("/exportLeaveApplicationFormExcel")
    public void exportLeaveApplicationFormExcel(HttpServletResponse response, @RequestBody LeaveApplicationFormQueryDto leaveApplicationFormVo) {
        exportService.exportLeaveApplicationFormExcel(response, leaveApplicationFormVo);
    }
}
