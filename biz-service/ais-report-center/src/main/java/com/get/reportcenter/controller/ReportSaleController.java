package com.get.reportcenter.controller;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.reportcenter.entity.ReportSale;
import com.get.reportcenter.service.IReportSaleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;


@Api(tags = "销售统计报表")
@RestController
@RequestMapping("report/reportSale")
public class ReportSaleController {
    @Resource
    private IReportSaleService reportSaleService;


    /**
     * 测试获取销售列表数据
     */
    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "测试获取销售列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REPORTCENTER, type = LoggerOptTypeConst.LIST, description = "报表中心/销售报表/测试查询报表")
    @GetMapping("getReportSale")
    public ResponseBo<List<ReportSale>> getReportSale() {
        List<ReportSale> reportSales = reportSaleService.list();
        return new ResponseBo<>(reportSales);
    }

//    /**
//     * 生成测试数据报表
//     */
//    @VerifyLogin(IsVerify = false)
//    @ApiOperation(value = "生成测试数据报表", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.REPORTCENTER, type = LoggerOptTypeConst.LIST, description = "报表中心/销售报表/生成测试数据报表")
//    @GetMapping("autoCreate")
//    public ResponseBo<List<ReportSale>> autoCreate() {
//        ReportSale reportSale = new ReportSale();
//        reportSale.setFkUserId(1L);
//        reportSale.setReportName("test");
//
//        StudentApplicationStatisticsDto studentApplicationStatisticsVo = new StudentApplicationStatisticsDto();
//        studentApplicationStatisticsVo.setBdName("test");
//        studentApplicationStatisticsVo.setFkAreaRegionId(2L);
//        reportSale.setReportQuery(studentApplicationStatisticsVo);
//
//        List<StatisticsVo> statisticsDtoList = new ArrayList<>();
//        StatisticsVo statisticsDto = new StatisticsVo();
//        statisticsDto.setCumulativeDateEndOne(new Date());
//        statisticsDto.setFkAreaCountryName("test111");
//        statisticsDtoList.add(statisticsDto);
//
//        StatisticsVo statisticsDto_ = new StatisticsVo();
//        statisticsDto_.setCumulativeDateEndOne(new Date());
//        statisticsDto_.setFkAreaCountryName("test222");
//        statisticsDtoList.add(statisticsDto_);
//
//        reportSale.setReportResult(statisticsDtoList);
//
//        reportSaleService.save(reportSale);
//
//        return new ResponseBo<>();
//    }


}
