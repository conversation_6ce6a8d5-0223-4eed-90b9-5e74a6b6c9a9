<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.reportcenter.dao.sale.StaffMapper">
  <resultMap id="BaseResultMap" type="com.get.reportcenter.entity.ReportStaff">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId" />
    <result column="fk_department_id" jdbcType="BIGINT" property="fkDepartmentId" />
    <result column="fk_position_id" jdbcType="BIGINT" property="fkPositionId" />
    <result column="fk_office_id" jdbcType="BIGINT" property="fkOfficeId" />
    <result column="fk_resume_guid" jdbcType="VARCHAR" property="fkResumeGuid" />
    <result column="login_id" jdbcType="VARCHAR" property="loginId" />
    <result column="login_ps" jdbcType="VARCHAR" property="loginPs" />
    <result column="num" jdbcType="VARCHAR" property="num" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="name_en" jdbcType="VARCHAR" property="nameEn" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="birthday" jdbcType="DATE" property="birthday" />
    <result column="identity_card" jdbcType="VARCHAR" property="identityCard" />
    <result column="home_tel" jdbcType="VARCHAR" property="homeTel" />
    <result column="work_tel" jdbcType="VARCHAR" property="workTel" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="zip_code" jdbcType="VARCHAR" property="zipCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="qq" jdbcType="VARCHAR" property="qq" />
    <result column="wechat" jdbcType="VARCHAR" property="wechat" />
    <result column="whatsapp" jdbcType="VARCHAR" property="whatsapp" />
    <result column="emergency_contact" jdbcType="VARCHAR" property="emergencyContact" />
    <result column="job_description" jdbcType="VARCHAR" property="jobDescription" />
    <result column="salary_effective_date" jdbcType="DATE" property="salaryEffectiveDate" />
    <result column="salary_base" jdbcType="DECIMAL" property="salaryBase" />
    <result column="salary_performance" jdbcType="DECIMAL" property="salaryPerformance" />
    <result column="allowance_position" jdbcType="DECIMAL" property="allowancePosition" />
    <result column="allowance_catering" jdbcType="DECIMAL" property="allowanceCatering" />
    <result column="allowance_transportation" jdbcType="DECIMAL" property="allowanceTransportation" />
    <result column="allowance_telecom" jdbcType="DECIMAL" property="allowanceTelecom" />
    <result column="allowance_other" jdbcType="DECIMAL" property="allowanceOther" />
    <result column="entry_date" jdbcType="DATE" property="entryDate" />
    <result column="pass_probation_date" jdbcType="DATE" property="passProbationDate" />
    <result column="leave_date" jdbcType="DATE" property="leaveDate" />
    <result column="is_on_duty" jdbcType="BIT" property="isOnDuty" />
    <result column="is_get_leaving_certificate" jdbcType="BIT" property="isGetLeavingCertificate" />
    <result column="is_stop_social_insurance" jdbcType="BIT" property="isStopSocialInsurance" />
    <result column="stop_social_insurance_month" jdbcType="VARCHAR" property="stopSocialInsuranceMonth" />
    <result column="is_modified_ps" jdbcType="BIT" property="isModifiedPs" />
    <result column="is_modified_resume" jdbcType="BIT" property="isModifiedResume" />
    <result column="is_admin" jdbcType="BIT" property="isAdmin" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.reportcenter.entity.ReportStaff">
    insert into m_staff (id, fk_company_id, fk_department_id, 
      fk_position_id, fk_office_id, fk_resume_guid, 
      login_id, login_ps, num, 
      name, name_en, gender, 
      birthday, identity_card, home_tel, 
      work_tel, mobile, email, 
      zip_code, address, qq, 
      wechat, whatsapp, emergency_contact, 
      job_description, salary_effective_date, salary_base, 
      salary_performance, allowance_position, allowance_catering, 
      allowance_transportation, allowance_telecom, 
      allowance_other, entry_date, pass_probation_date, 
      leave_date, is_on_duty, is_get_leaving_certificate, 
      is_stop_social_insurance, stop_social_insurance_month, 
      is_modified_ps, is_modified_resume, is_admin, 
      is_active, session_id, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkCompanyId,jdbcType=BIGINT}, #{fkDepartmentId,jdbcType=BIGINT}, 
      #{fkPositionId,jdbcType=BIGINT}, #{fkOfficeId,jdbcType=BIGINT}, #{fkResumeGuid,jdbcType=VARCHAR}, 
      #{loginId,jdbcType=VARCHAR}, #{loginPs,jdbcType=VARCHAR}, #{num,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{nameEn,jdbcType=VARCHAR}, #{gender,jdbcType=INTEGER}, 
      #{birthday,jdbcType=DATE}, #{identityCard,jdbcType=VARCHAR}, #{homeTel,jdbcType=VARCHAR}, 
      #{workTel,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, 
      #{zipCode,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{qq,jdbcType=VARCHAR}, 
      #{wechat,jdbcType=VARCHAR}, #{whatsapp,jdbcType=VARCHAR}, #{emergencyContact,jdbcType=VARCHAR}, 
      #{jobDescription,jdbcType=VARCHAR}, #{salaryEffectiveDate,jdbcType=DATE}, #{salaryBase,jdbcType=DECIMAL}, 
      #{salaryPerformance,jdbcType=DECIMAL}, #{allowancePosition,jdbcType=DECIMAL}, #{allowanceCatering,jdbcType=DECIMAL}, 
      #{allowanceTransportation,jdbcType=DECIMAL}, #{allowanceTelecom,jdbcType=DECIMAL}, 
      #{allowanceOther,jdbcType=DECIMAL}, #{entryDate,jdbcType=DATE}, #{passProbationDate,jdbcType=DATE}, 
      #{leaveDate,jdbcType=DATE}, #{isOnDuty,jdbcType=BIT}, #{isGetLeavingCertificate,jdbcType=BIT}, 
      #{isStopSocialInsurance,jdbcType=BIT}, #{stopSocialInsuranceMonth,jdbcType=VARCHAR}, 
      #{isModifiedPs,jdbcType=BIT}, #{isModifiedResume,jdbcType=BIT}, #{isAdmin,jdbcType=BIT}, 
      #{isActive,jdbcType=BIT}, #{sessionId,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.reportcenter.entity.ReportStaff">
    insert into m_staff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkCompanyId != null">
        fk_company_id,
      </if>
      <if test="fkDepartmentId != null">
        fk_department_id,
      </if>
      <if test="fkPositionId != null">
        fk_position_id,
      </if>
      <if test="fkOfficeId != null">
        fk_office_id,
      </if>
      <if test="fkResumeGuid != null">
        fk_resume_guid,
      </if>
      <if test="loginId != null">
        login_id,
      </if>
      <if test="loginPs != null">
        login_ps,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameEn != null">
        name_en,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="identityCard != null">
        identity_card,
      </if>
      <if test="homeTel != null">
        home_tel,
      </if>
      <if test="workTel != null">
        work_tel,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="zipCode != null">
        zip_code,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="qq != null">
        qq,
      </if>
      <if test="wechat != null">
        wechat,
      </if>
      <if test="whatsapp != null">
        whatsapp,
      </if>
      <if test="emergencyContact != null">
        emergency_contact,
      </if>
      <if test="jobDescription != null">
        job_description,
      </if>
      <if test="salaryEffectiveDate != null">
        salary_effective_date,
      </if>
      <if test="salaryBase != null">
        salary_base,
      </if>
      <if test="salaryPerformance != null">
        salary_performance,
      </if>
      <if test="allowancePosition != null">
        allowance_position,
      </if>
      <if test="allowanceCatering != null">
        allowance_catering,
      </if>
      <if test="allowanceTransportation != null">
        allowance_transportation,
      </if>
      <if test="allowanceTelecom != null">
        allowance_telecom,
      </if>
      <if test="allowanceOther != null">
        allowance_other,
      </if>
      <if test="entryDate != null">
        entry_date,
      </if>
      <if test="passProbationDate != null">
        pass_probation_date,
      </if>
      <if test="leaveDate != null">
        leave_date,
      </if>
      <if test="isOnDuty != null">
        is_on_duty,
      </if>
      <if test="isGetLeavingCertificate != null">
        is_get_leaving_certificate,
      </if>
      <if test="isStopSocialInsurance != null">
        is_stop_social_insurance,
      </if>
      <if test="stopSocialInsuranceMonth != null">
        stop_social_insurance_month,
      </if>
      <if test="isModifiedPs != null">
        is_modified_ps,
      </if>
      <if test="isModifiedResume != null">
        is_modified_resume,
      </if>
      <if test="isAdmin != null">
        is_admin,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkCompanyId != null">
        #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="fkDepartmentId != null">
        #{fkDepartmentId,jdbcType=BIGINT},
      </if>
      <if test="fkPositionId != null">
        #{fkPositionId,jdbcType=BIGINT},
      </if>
      <if test="fkOfficeId != null">
        #{fkOfficeId,jdbcType=BIGINT},
      </if>
      <if test="fkResumeGuid != null">
        #{fkResumeGuid,jdbcType=VARCHAR},
      </if>
      <if test="loginId != null">
        #{loginId,jdbcType=VARCHAR},
      </if>
      <if test="loginPs != null">
        #{loginPs,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=DATE},
      </if>
      <if test="identityCard != null">
        #{identityCard,jdbcType=VARCHAR},
      </if>
      <if test="homeTel != null">
        #{homeTel,jdbcType=VARCHAR},
      </if>
      <if test="workTel != null">
        #{workTel,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="qq != null">
        #{qq,jdbcType=VARCHAR},
      </if>
      <if test="wechat != null">
        #{wechat,jdbcType=VARCHAR},
      </if>
      <if test="whatsapp != null">
        #{whatsapp,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContact != null">
        #{emergencyContact,jdbcType=VARCHAR},
      </if>
      <if test="jobDescription != null">
        #{jobDescription,jdbcType=VARCHAR},
      </if>
      <if test="salaryEffectiveDate != null">
        #{salaryEffectiveDate,jdbcType=DATE},
      </if>
      <if test="salaryBase != null">
        #{salaryBase,jdbcType=DECIMAL},
      </if>
      <if test="salaryPerformance != null">
        #{salaryPerformance,jdbcType=DECIMAL},
      </if>
      <if test="allowancePosition != null">
        #{allowancePosition,jdbcType=DECIMAL},
      </if>
      <if test="allowanceCatering != null">
        #{allowanceCatering,jdbcType=DECIMAL},
      </if>
      <if test="allowanceTransportation != null">
        #{allowanceTransportation,jdbcType=DECIMAL},
      </if>
      <if test="allowanceTelecom != null">
        #{allowanceTelecom,jdbcType=DECIMAL},
      </if>
      <if test="allowanceOther != null">
        #{allowanceOther,jdbcType=DECIMAL},
      </if>
      <if test="entryDate != null">
        #{entryDate,jdbcType=DATE},
      </if>
      <if test="passProbationDate != null">
        #{passProbationDate,jdbcType=DATE},
      </if>
      <if test="leaveDate != null">
        #{leaveDate,jdbcType=DATE},
      </if>
      <if test="isOnDuty != null">
        #{isOnDuty,jdbcType=BIT},
      </if>
      <if test="isGetLeavingCertificate != null">
        #{isGetLeavingCertificate,jdbcType=BIT},
      </if>
      <if test="isStopSocialInsurance != null">
        #{isStopSocialInsurance,jdbcType=BIT},
      </if>
      <if test="stopSocialInsuranceMonth != null">
        #{stopSocialInsuranceMonth,jdbcType=VARCHAR},
      </if>
      <if test="isModifiedPs != null">
        #{isModifiedPs,jdbcType=BIT},
      </if>
      <if test="isModifiedResume != null">
        #{isModifiedResume,jdbcType=BIT},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=BIT},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>