<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.reportcenter.dao.report.AdvSearchConfigMapper">
  <resultMap id="BaseResultMap" type="com.get.reportcenter.entity.AdvSearchConfig">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="query_key" jdbcType="VARCHAR" property="queryKey" />
    <result column="query_title" jdbcType="VARCHAR" property="queryTitle" />
    <result column="query_width" jdbcType="INTEGER" property="queryWidth" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.get.reportcenter.entity.AdvSearchConfig">
    <result column="query_sql" jdbcType="LONGVARCHAR" property="querySql" />
    <result column="query_paramter" jdbcType="LONGVARCHAR" property="queryParamter" />
  </resultMap>
  <sql id="Blob_Column_List">
    query_sql, query_paramter
  </sql>
  <insert id="insert" parameterType="com.get.reportcenter.entity.AdvSearchConfig">
    insert into m_adv_search_config (id, query_key, query_title, 
      query_width, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user, query_sql, 
      query_paramter)
    values (#{id,jdbcType=BIGINT}, #{queryKey,jdbcType=VARCHAR}, #{queryTitle,jdbcType=VARCHAR}, 
      #{queryWidth,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}, #{querySql,jdbcType=LONGVARCHAR}, 
      #{queryParamter,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.reportcenter.entity.AdvSearchConfig">
    insert into m_adv_search_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="queryKey != null">
        query_key,
      </if>
      <if test="queryTitle != null">
        query_title,
      </if>
      <if test="queryWidth != null">
        query_width,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="querySql != null">
        query_sql,
      </if>
      <if test="queryParamter != null">
        query_paramter,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="queryKey != null">
        #{queryKey,jdbcType=VARCHAR},
      </if>
      <if test="queryTitle != null">
        #{queryTitle,jdbcType=VARCHAR},
      </if>
      <if test="queryWidth != null">
        #{queryWidth,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="querySql != null">
        #{querySql,jdbcType=LONGVARCHAR},
      </if>
      <if test="queryParamter != null">
        #{queryParamter,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getQueryObject" parameterType="java.lang.String" resultType="java.util.LinkedHashMap">
       ${query}
  </select>
</mapper>