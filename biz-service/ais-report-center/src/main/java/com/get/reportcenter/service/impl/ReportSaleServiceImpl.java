package com.get.reportcenter.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.reportcenter.dao.report.ReportSaleMapper;
import com.get.reportcenter.dto.ReportSaleDto;
import com.get.reportcenter.entity.ReportSale;
import com.get.reportcenter.service.IReportSaleService;
//import com.get.reportcenter.vo.ReportSaleVo;
import com.get.salecenter.vo.PeriodicStatisticsVo;
import com.get.salecenter.dto.StudentApplicationStatisticsDto;
import com.get.salecenter.vo.StudentApplicationStatisticsVo;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.util.Date;

@Service
public class  ReportSaleServiceImpl extends ServiceImpl<ReportSaleMapper, ReportSale> implements IReportSaleService  {
    @Resource
    private ReportSaleMapper reportSaleMapper;
    @Resource
    private UtilService utilService;

    @Override
    public ReportSale getLastReportSale(Long fkStaffId){
        ReportSale data = reportSaleMapper.getLastReportSale(fkStaffId);
        if(GeneralTool.isEmpty(data)){
            return null;
        }
        return data;
    }

    @Override
    public ReportSale getReportSale(StudentApplicationStatisticsDto studentApplicationStatisticsDto) {
        ReportSale data = reportSaleMapper.getReportSale(studentApplicationStatisticsDto, SecureUtil.getStaffId());
        if(GeneralTool.isEmpty(data)){
            return null;
        }
        return data;
    }

    @Override
    public void updateReportSaleStatus(Long fkReportSaleId,Integer reportStatus){
        if(GeneralTool.isEmpty(fkReportSaleId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        reportSaleMapper.updateReportSaleStatus(fkReportSaleId,reportStatus);
    }

    @Override
    public Long addReportSale(@RequestBody StudentApplicationStatisticsDto studentApplicationStatisticsDto){
        ReportSale reportSale = new ReportSale();
        reportSale.setReportQuery(studentApplicationStatisticsDto);
        reportSale.setReportName(ProjectKeyEnum.STUDENT_APPLICATION_STATISTICS.value);
        reportSale.setFkUserId(SecureUtil.getStaffId());
        reportSale.setReportTime(new Date());
        reportSale.setReportStatus(1);
        utilService.updateUserInfoToEntity(reportSale);
        reportSaleMapper.insert(reportSale);
        return reportSale.getId();
    }

    @Override
    public void updateReportSale(PeriodicStatisticsVo periodicStatisticsVo, Long fkReportSaleId){
        ReportSale reportSale =  reportSaleMapper.selectById(fkReportSaleId);
        reportSale.setReportResult(periodicStatisticsVo);
        reportSale.setReportStatus(0);
        reportSaleMapper.updateById(reportSale);
    }

    @Override
    public Long addReportSaleCommon(ReportSaleDto reportSaleVo) {
        reportSaleMapper.insertSelective(reportSaleVo);
        return reportSaleVo.getId();
    }

    @Override
    public Integer getReportSaleStatusById(Long fkReportSaleId) {
        Integer status = reportSaleMapper.getReportSaleStatusById(fkReportSaleId);
        return status;
    }

    @Override
    public ReportSaleDto getReportSaleById(Long fkReportSaleId) {
        ReportSaleDto reportSaleVo = reportSaleMapper.getReportSaleById(fkReportSaleId);
        return reportSaleVo;
    }

    @Override
    public ReportSaleDto getLastReportSaleVoByReportNameAndUserId(String key, Long staffId) {
        return reportSaleMapper.getLastReportSaleVoByReportNameAndUserId(key,staffId);
    }
}
