<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.reportcenter.dao.finance.ReceiptFormItemMapper">
  <insert id="insert" parameterType="com.get.reportcenter.entity.ReportReceiptFormItem">
    insert into m_receipt_form_item (id, fk_receipt_form_id, fk_receivable_plan_id, 
      amount_receipt, service_fee, exchange_rate_receivable, 
      amount_receivable, amount_exchange_rate, exchange_rate_hkd, 
      amount_hkd, exchange_rate_rmb, amount_rmb, 
      summary, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkReceiptFormId,jdbcType=BIGINT}, #{fkReceivablePlanId,jdbcType=BIGINT}, 
      #{amountReceipt,jdbcType=DECIMAL}, #{serviceFee,jdbcType=DECIMAL}, #{exchangeRateReceivable,jdbcType=DECIMAL}, 
      #{amountReceivable,jdbcType=DECIMAL}, #{amountExchangeRate,jdbcType=DECIMAL}, #{exchangeRateHkd,jdbcType=DECIMAL}, 
      #{amountHkd,jdbcType=DECIMAL}, #{exchangeRateRmb,jdbcType=DECIMAL}, #{amountRmb,jdbcType=DECIMAL}, 
      #{summary,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.reportcenter.entity.ReportReceiptFormItem">
    insert into m_receipt_form_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkReceiptFormId != null">
        fk_receipt_form_id,
      </if>
      <if test="fkReceivablePlanId != null">
        fk_receivable_plan_id,
      </if>
      <if test="amountReceipt != null">
        amount_receipt,
      </if>
      <if test="serviceFee != null">
        service_fee,
      </if>
      <if test="exchangeRateReceivable != null">
        exchange_rate_receivable,
      </if>
      <if test="amountReceivable != null">
        amount_receivable,
      </if>
      <if test="amountExchangeRate != null">
        amount_exchange_rate,
      </if>
      <if test="exchangeRateHkd != null">
        exchange_rate_hkd,
      </if>
      <if test="amountHkd != null">
        amount_hkd,
      </if>
      <if test="exchangeRateRmb != null">
        exchange_rate_rmb,
      </if>
      <if test="amountRmb != null">
        amount_rmb,
      </if>
      <if test="summary != null">
        summary,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkReceiptFormId != null">
        #{fkReceiptFormId,jdbcType=BIGINT},
      </if>
      <if test="fkReceivablePlanId != null">
        #{fkReceivablePlanId,jdbcType=BIGINT},
      </if>
      <if test="amountReceipt != null">
        #{amountReceipt,jdbcType=DECIMAL},
      </if>
      <if test="serviceFee != null">
        #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="exchangeRateReceivable != null">
        #{exchangeRateReceivable,jdbcType=DECIMAL},
      </if>
      <if test="amountReceivable != null">
        #{amountReceivable,jdbcType=DECIMAL},
      </if>
      <if test="amountExchangeRate != null">
        #{amountExchangeRate,jdbcType=DECIMAL},
      </if>
      <if test="exchangeRateHkd != null">
        #{exchangeRateHkd,jdbcType=DECIMAL},
      </if>
      <if test="amountHkd != null">
        #{amountHkd,jdbcType=DECIMAL},
      </if>
      <if test="exchangeRateRmb != null">
        #{exchangeRateRmb,jdbcType=DECIMAL},
      </if>
      <if test="amountRmb != null">
        #{amountRmb,jdbcType=DECIMAL},
      </if>
      <if test="summary != null">
        #{summary,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.reportcenter.entity.ReportReceiptFormItem">
    update m_receipt_form_item
    <set>
      <if test="fkReceiptFormId != null">
        fk_receipt_form_id = #{fkReceiptFormId,jdbcType=BIGINT},
      </if>
      <if test="fkReceivablePlanId != null">
        fk_receivable_plan_id = #{fkReceivablePlanId,jdbcType=BIGINT},
      </if>
      <if test="amountReceipt != null">
        amount_receipt = #{amountReceipt,jdbcType=DECIMAL},
      </if>
      <if test="serviceFee != null">
        service_fee = #{serviceFee,jdbcType=DECIMAL},
      </if>
      <if test="exchangeRateReceivable != null">
        exchange_rate_receivable = #{exchangeRateReceivable,jdbcType=DECIMAL},
      </if>
      <if test="amountReceivable != null">
        amount_receivable = #{amountReceivable,jdbcType=DECIMAL},
      </if>
      <if test="amountExchangeRate != null">
        amount_exchange_rate = #{amountExchangeRate,jdbcType=DECIMAL},
      </if>
      <if test="exchangeRateHkd != null">
        exchange_rate_hkd = #{exchangeRateHkd,jdbcType=DECIMAL},
      </if>
      <if test="amountHkd != null">
        amount_hkd = #{amountHkd,jdbcType=DECIMAL},
      </if>
      <if test="exchangeRateRmb != null">
        exchange_rate_rmb = #{exchangeRateRmb,jdbcType=DECIMAL},
      </if>
      <if test="amountRmb != null">
        amount_rmb = #{amountRmb,jdbcType=DECIMAL},
      </if>
      <if test="summary != null">
        summary = #{summary,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.reportcenter.entity.ReportReceiptFormItem">
    update m_receipt_form_item
    set fk_receipt_form_id = #{fkReceiptFormId,jdbcType=BIGINT},
      fk_receivable_plan_id = #{fkReceivablePlanId,jdbcType=BIGINT},
      amount_receipt = #{amountReceipt,jdbcType=DECIMAL},
      service_fee = #{serviceFee,jdbcType=DECIMAL},
      exchange_rate_receivable = #{exchangeRateReceivable,jdbcType=DECIMAL},
      amount_receivable = #{amountReceivable,jdbcType=DECIMAL},
      amount_exchange_rate = #{amountExchangeRate,jdbcType=DECIMAL},
      exchange_rate_hkd = #{exchangeRateHkd,jdbcType=DECIMAL},
      amount_hkd = #{amountHkd,jdbcType=DECIMAL},
      exchange_rate_rmb = #{exchangeRateRmb,jdbcType=DECIMAL},
      amount_rmb = #{amountRmb,jdbcType=DECIMAL},
      summary = #{summary,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>