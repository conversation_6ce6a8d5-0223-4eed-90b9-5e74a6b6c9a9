package com.get.core.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.log.dto.LogLoginDto;
import com.get.core.log.model.LogLogin;
import com.get.core.log.vo.LogLoginVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

@Mapper
public interface LogLoginMapper extends BaseMapper<LogLogin> {
    int insert(LogLogin record);

    int insertSelective(LogLogin record);

    /**
     * 列表
     *
     * @param logLoginVo
     * @return
     */
    List<LogLogin> getLogLogins(IPage<LogLogin> page, @Param("logLoginVo") LogLoginVo logLoginVo);


    /**
     * 获取员工在线信息
     * @param iPage
     * @param logLoginVo
     * @return
     */
    List<LogLoginDto> getStaffLoginInfo(IPage<LogLoginDto> iPage,@Param("logLoginVo") LogLoginVo logLoginVo);

    LogLogin getLoginInfoByIpAndId(@Param("sessionId")String sessionId, @Param("fkStaffId") Long fkStaffId);


    /**
     * 更新登出时间
     * @param fkStaffId
     * @param now
     * @param loginId
     */
    void updateLogoutTime(@Param("fkStaffId")Long fkStaffId,@Param("now") Date now,@Param("loginId") String loginId);
}