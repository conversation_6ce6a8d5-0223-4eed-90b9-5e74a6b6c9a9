package com.get.core.log.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.core.log.dto.LogOperationDto;
import com.get.core.log.model.LogOperation;
import com.get.core.log.vo.LogOperationVo;

import java.util.List;

/**
 * @author: jack
 * @create: 2020/6/10
 * @verison: 1.0
 * @description: 操作日志业务接口
 */
public interface ILogOperationService extends IService<LogOperation> {


    /**
     * 详情
     *
     * @param id
     * @return
     */
    LogOperationDto findLogOperationById(Long id);

    /**
     * 列表数据
     *
     * @param logOperationVo
     * @param page
     * @return
     */
    List<LogOperationDto> getLogOperations(LogOperationVo logOperationVo, Page page);

    /**
     * 保存
     *
     * @param logOperation
     * @return
     */
    Long addLogOperation(LogOperation logOperation);
}
