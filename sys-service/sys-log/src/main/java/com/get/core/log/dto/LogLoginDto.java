package com.get.core.log.dto;

import com.get.core.log.model.LogLogin;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Sea
 * @create: 2020/6/19 18:16
 * @verison: 1.0
 * @description:
 */
@ApiModel("登录日志返回类")
@Data
public class LogLoginDto extends LogLogin implements Serializable {
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "在线时长数值")
    private Long onlineDurationVal;

    @ApiModelProperty("在线时长")
    private String onlineDurationInfo;


    @ApiModelProperty("职位名称")
    private String positionName;

    @ApiModelProperty("是否在线")
    private Boolean isOnline;

    @ApiModelProperty("部门名称")
    private String departmentName;
}
