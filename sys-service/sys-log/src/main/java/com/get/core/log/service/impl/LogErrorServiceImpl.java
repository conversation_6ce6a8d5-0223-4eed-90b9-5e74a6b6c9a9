package com.get.core.log.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.core.log.mapper.LogErrorMapper;
import com.get.core.log.model.LogError;
import com.get.core.log.service.ILogErrorService;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 */
@Service
public class LogErrorServiceImpl extends ServiceImpl<LogErrorMapper, LogError> implements ILogErrorService {

}
