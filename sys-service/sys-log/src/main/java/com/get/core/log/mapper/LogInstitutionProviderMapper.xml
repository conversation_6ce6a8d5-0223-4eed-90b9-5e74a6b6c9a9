<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.core.log.mapper.LogInstitutionProviderMapper">
  <resultMap id="BaseResultMap" type="com.get.core.log.model.LogInstitutionProvider">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_institution_provider_id" jdbcType="BIGINT" property="fkInstitutionProviderId" />
    <result column="opt_type" jdbcType="VARCHAR" property="optType" />
    <result column="opt_info" jdbcType="VARCHAR" property="optInfo" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <!--TODO 注释sql-->
<!--  <insert id="insert" parameterType="com.get.core.log.model.LogInstitutionProvider">-->
<!--    insert into log_institution_provider (id, fk_institution_provider_id, opt_type, -->
<!--      opt_info, gmt_create, gmt_create_user, -->
<!--      gmt_modified, gmt_modified_user)-->
<!--    values (#{id,jdbcType=BIGINT}, #{fkInstitutionProviderId,jdbcType=BIGINT}, #{optType,jdbcType=VARCHAR}, -->
<!--      #{optInfo,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, -->
<!--      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})-->
<!--  </insert>-->
  <!--TODO 注释sql-->
<!--  <insert id="insertSelective" parameterType="com.get.core.log.model.LogInstitutionProvider">-->
<!--    insert into log_institution_provider-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="fkInstitutionProviderId != null">-->
<!--        fk_institution_provider_id,-->
<!--      </if>-->
<!--      <if test="optType != null">-->
<!--        opt_type,-->
<!--      </if>-->
<!--      <if test="optInfo != null">-->
<!--        opt_info,-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create,-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user,-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified,-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkInstitutionProviderId != null">-->
<!--        #{fkInstitutionProviderId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="optType != null">-->
<!--        #{optType,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="optInfo != null">-->
<!--        #{optInfo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
</mapper>