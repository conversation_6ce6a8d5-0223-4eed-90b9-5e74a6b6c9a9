package com.get.core.log.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.dto.LogOperationDto;
import com.get.core.log.model.LogOperation;
import com.get.core.log.service.ILogOperationService;
import com.get.core.log.vo.LogOperationVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

/**
 * @author: jack
 * @create: 2020/6/8
 * @verison: 1.0
 * @description: 日志中心控制器
 */
@Api(tags = "操作日志管理")
@RestController
@RequestMapping("log/operation")
public class OperationLogController {
    @Autowired
    private ILogOperationService logOperationService;

    /**
     * 详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "操作日志详情接口", notes = "id为此条数据id")
    @GetMapping("/{id}")
    public ResponseBo<LogOperationDto> detail(@PathVariable("id") Long id) {
        LogOperationDto data = logOperationService.findLogOperationById(id);
        return new ResponseBo<>(data);
    }

    /**
     * 列表数据
     *
     * @param page
     * @return
     */
    @ApiOperation(value = "列表数据", notes = "staffName为姓名，fkStaffId为员工id")
    @PostMapping("datas")
    public ResponseBo<LogOperationDto> datas(@RequestBody SearchBean<LogOperationVo> page) {
        List<LogOperationDto> datas = logOperationService.getLogOperations(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }

    /**
     * 新增信息
     *
     * @param logOperation
     * @return
     */
    @ApiIgnore
    @PostMapping("add")
    public Long add(@RequestBody LogOperation logOperation) {
        return this.logOperationService.addLogOperation(logOperation);
    }
}
