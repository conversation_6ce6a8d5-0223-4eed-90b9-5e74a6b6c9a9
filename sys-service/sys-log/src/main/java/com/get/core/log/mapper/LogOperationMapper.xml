<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.core.log.mapper.LogOperationMapper">
  <resultMap id="BaseResultMap" type="com.get.core.log.model.LogOperation">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId" />
    <result column="staff_login_id" jdbcType="VARCHAR" property="staffLoginId" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="opt_code" jdbcType="VARCHAR" property="optCode" />
    <result column="opt_module_name" jdbcType="VARCHAR" property="optModuleName" />
    <result column="opt_type" jdbcType="VARCHAR" property="optType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.get.core.log.model.LogOperation">
    insert into log_operation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkStaffId != null">
        fk_staff_id,
      </if>
      <if test="staffLoginId != null">
        staff_login_id,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="optCode != null">
        opt_code,
      </if>
      <if test="optModuleName != null">
        opt_module_name,
      </if>
      <if test="optType != null">
        opt_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="fkCompanyId != null">
        fk_company_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkStaffId != null">
        #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="staffLoginId != null">
        #{staffLoginId,jdbcType=VARCHAR},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="optCode != null">
        #{optCode,jdbcType=VARCHAR},
      </if>
      <if test="optModuleName != null">
        #{optModuleName,jdbcType=VARCHAR},
      </if>
      <if test="optType != null">
        #{optType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fkCompanyId != null">
        #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <select id="getLogOperations" resultType="com.get.core.log.model.LogOperation">
    SELECT
     id,gmt_create,gmt_modified,gmt_create_user,gmt_modified_user,fk_company_id,fk_staff_id,staff_login_id,staff_name,opt_code,opt_module_name,opt_type,remark
    FROM
     log_operation
    WHERE
    fk_company_id = #{logOperationVo.fkCompanyId}
    <if test=" logOperationVo.gmtCreateUser != null and logOperationVo.gmtCreateUser != ''">
      and gmt_create_user like #{logOperationVo.gmtCreateUser}
    </if>
    <if test=" logOperationVo.staffName != null and logOperationVo.staffName != ''">
      and staff_name like #{logOperationVo.staffName}
    </if>
    <if test=" logOperationVo.logOperationStartTime != null and logOperationVo.logOperationStartTime.toString() != ''">
      and DATE_FORMAT(gmt_create,'%Y-%m-%d') <![CDATA[>= ]]> DATE_FORMAT(#{logOperationVo.logOperationStartTime},'%Y-%m-%d')
    </if>
    <if test=" logOperationVo.logOperationEndTime != null and logOperationVo.logOperationEndTime.toString() != ''">
      and DATE_FORMAT(gmt_create,'%Y-%m-%d') <![CDATA[<= ]]> DATE_FORMAT(#{logOperationVo.logOperationEndTime},'%Y-%m-%d')
    </if>
    <if test=" logOperationVo.optCode != null and logOperationVo.optCode != ''">
      and opt_code like #{logOperationVo.optCode}
    </if>
    <if test=" logOperationVo.remark != null and logOperationVo.remark != ''">
      and remark like #{logOperationVo.remark}
    </if>
    order by gmt_create desc
  </select>
</mapper>