package com.get.core.log.service;


import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.log.dto.LogLoginDto;
import com.get.core.log.model.LogLogin;
import com.get.core.log.vo.LogLoginVo;

import java.util.List;

/**
 * @author: jack
 * @create: 2020/6/10
 * @verison: 1.0
 * @description: 登录日志业务接口
 */
public interface ILogLoginService extends IService<LogLogin> {
    /**
     * 详情
     *
     * @param id
     * @return
     */
    LogLoginDto findLogLoginById(Long id);

    /**
     * 列表数据
     *
     * @param logLoginVo
     * @param page
     * @return
     */
    List<LogLoginDto> getLogLogins(LogLoginVo logLoginVo, Page page);

    /**
     * 保存
     *
     * @param logLogin
     * @return
     */
    Long addLogLogin(LogLogin logLogin);


    /**
     * 强制登出
     *
     * @param id@return
     */
    SaveResponseBo forceLogout(Long id);

    /**
     * 获取员工在线信息
     * @param logLoginVo
     * @param page
     * @return
     */
    List<LogLoginDto> getStaffLoginInfo(LogLoginVo logLoginVo, SearchBean<LogLoginVo> page);


    /**
     * 更新员工在线信息
     * @param sessionId
     * @param fkStaffId
     */
    Boolean updateStaffLoginInfo(String sessionId,Long fkStaffId);
    /**
     * 登出时间
     *
     * @return
     */
    void updateLogLogin();
}
