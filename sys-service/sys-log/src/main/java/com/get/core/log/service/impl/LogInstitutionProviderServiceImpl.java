package com.get.core.log.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.dto.LogInstitutionProviderDto;
import com.get.core.log.exception.GetServiceException;
import com.get.core.log.mapper.LogInstitutionProviderMapper;
import com.get.core.log.model.LogInstitutionProvider;
import com.get.core.log.service.ILogInstitutionProviderService;
import com.get.core.log.vo.LogInstitutionProviderVo;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/4/15
 * @TIME: 18:31
 * @Description:
 **/
@Service
public class LogInstitutionProviderServiceImpl extends ServiceImpl<LogInstitutionProviderMapper, LogInstitutionProvider> implements ILogInstitutionProviderService {
//    @Resource
//    private LogInstitutionProviderMapper logInstitutionProviderMapper;
    @Autowired
    private UtilService utilService;

//    @Override
//    public LogInstitutionProviderDto findLogInstitutionProviderById(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        LogInstitutionProvider logInstitutionProvider = logInstitutionProviderMapper.selectById(id);
//        LogInstitutionProviderDto logInstitutionProviderDto = BeanCopyUtils.objClone(logInstitutionProvider, LogInstitutionProviderDto::new);
//        return logInstitutionProviderDto;
//    }

    @Override
    public List<LogInstitutionProviderDto> getLogInstitutionProviders(LogInstitutionProviderVo logInstitutionProviderVo, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<LogInstitutionProvider> logInstitutionProviders = logInstitutionProviderMapper.selectAll();
//        page.restPage(logInstitutionProviders);

        LambdaQueryWrapper<LogInstitutionProvider> wrapper = new LambdaQueryWrapper();
        IPage<LogInstitutionProvider> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<LogInstitutionProvider> logInstitutionProviders = pages.getRecords();
        List<LogInstitutionProviderDto> convertDatas = BeanCopyUtils.copyListProperties(logInstitutionProviders, LogInstitutionProviderDto::new);

//        List<LogInstitutionProviderDto> convertDatas = new ArrayList<>();
//        for (LogInstitutionProvider logInstitutionProvider : logInstitutionProviders) {
//            LogInstitutionProviderDto logInstitutionProviderDto = Tools.objClone(logInstitutionProvider, LogInstitutionProviderDto.class);
//            convertDatas.add(logInstitutionProviderDto);
//        }
        return convertDatas;
    }

//    @Override
//    public Long addLogInstitutionProvider(LogInstitutionProviderVo institutionProviderVo) {
//        LogInstitutionProvider logInstitutionProvider = BeanCopyUtils.objClone(institutionProviderVo, LogInstitutionProvider::new);
//        utilService.updateUserInfoToEntity(logInstitutionProvider);
//        logInstitutionProviderMapper.insert(logInstitutionProvider);
//        return institutionProviderVo.getId();
//    }
}
