package com.get.core.log.feign;

import com.get.core.log.model.LogError;
import com.get.core.log.model.LogInstitutionProvider;
import com.get.core.log.model.LogLogin;
import com.get.core.log.model.LogOperation;
import com.get.core.log.service.ILogErrorService;
import com.get.core.log.service.ILogInstitutionProviderService;
import com.get.core.log.service.ILogLoginService;
import com.get.core.log.service.ILogOperationService;
import com.get.core.mybatis.base.UtilService;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * 日志服务Feign实现类
 */
@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class LogClient implements ILogClient {

    private final ILogErrorService errorLogService;
    private final ILogInstitutionProviderService logInstitutionProviderService;
    private final ILogLoginService logLoginService;
    private final ILogOperationService logOperationService;
    private final UtilService utilService;


    @Override
    @PostMapping(API_PREFIX + "/saveErrorLog")
    public Result<Boolean> saveErrorLog(@RequestBody LogError log) {
        log.setParams(log.getParams().replace("&amp;", "&"));
        return Result.data(errorLogService.save(log));
    }

    @Override
    public Result<Boolean> saveLogInstitutionProvider(LogInstitutionProvider log) {
        utilService.updateUserInfoToEntity(log);
        System.out.println("save-LogInstitutionProvider:" + GeneralTool.toJson(log));
        return Result.data(logInstitutionProviderService.save(log));
    }

    @Override
    public Result<Boolean> saveLogLogin(LogLogin log) {
        System.out.println("save-LogLogin:" + GeneralTool.toJson(log));
        return Result.data(logLoginService.save(log));
    }

    @Override
    public Result<Boolean> updateLogLogin(LogLogin log) {
        return Result.data(logLoginService.updateStaffLoginInfo(log.getSessionId(),log.getFkStaffId()));
    }

    @Override
    public Result<Boolean> saveLogOperation(LogOperation log) {
        System.out.println("save-LogOperation:" + GeneralTool.toJson(log));
        return Result.data(logOperationService.save(log));
    }
}
