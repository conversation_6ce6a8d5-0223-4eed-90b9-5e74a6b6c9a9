package com.get.core.log.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.result.Page;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.jwt.JwtUtil;
import com.get.core.log.dto.LogLoginDto;
import com.get.core.log.exception.GetServiceException;
import com.get.core.log.mapper.LogLoginMapper;
import com.get.core.log.model.LogLogin;
import com.get.core.log.service.ILogLoginService;
import com.get.core.log.vo.LogLoginVo;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @author: jack
 * @create: 2020/6/8
 * @verison: 1.0
 * @description: 登录日志业务实现类
 */
@Service
public class LogLoginServiceImpl extends ServiceImpl<LogLoginMapper, LogLogin> implements ILogLoginService {
    @Autowired
    private UtilService utilService;
    @Resource
    private LogLoginMapper logLoginMapper;
    @Resource
    @Lazy
    private IPermissionCenterClient permissionCenterClient;

    private Map<String, Object> map = new HashMap<>();

    @Override
    public LogLoginDto findLogLoginById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LogLogin logLogin = logLoginMapper.selectById(id);
        LogLoginDto logLoginDto = BeanCopyUtils.objClone(logLogin, LogLoginDto::new);
        String companyName = "";
        Result<String> result = permissionCenterClient.getCompanyNameById(logLogin.getFkCompanyId());
        if (result.isSuccess()) {
            companyName = result.getData();
        }
        logLoginDto.setCompanyName(companyName);
        return logLoginDto;
    }

    @Override
    public List<LogLoginDto> getLogLogins(LogLoginVo logLoginVo, Page page) {
        //设置查询条件
        if (GeneralTool.isNotEmpty(logLoginVo)) {
            if (GeneralTool.isNotEmpty(logLoginVo.getGmtCreateUser())) {
                logLoginVo.setGmtCreateUser("%" + logLoginVo.getGmtCreateUser() + "%");
            }
            if (GeneralTool.isNotEmpty(logLoginVo.getStaffName())) {
                logLoginVo.setStaffName("%" + logLoginVo.getStaffName() + "%");
            }
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
        //该处分页待测试
        IPage<LogLogin> logLoginIPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<LogLogin> logLogins = logLoginMapper.getLogLogins(logLoginIPage, logLoginVo);
        page.setAll((int) logLoginIPage.getTotal());
//        page.restPage(logLogins);
        List<LogLoginDto> convertDatas = new ArrayList<>();
        for (LogLogin logLogin : logLogins) {
            String companyName = "";
            Result<String> result = permissionCenterClient.getCompanyNameById(logLogin.getFkCompanyId());
            if (result.isSuccess()) {
                companyName = result.getData();
            }
            LogLoginDto logLoginDto = BeanCopyUtils.objClone(logLogin, LogLoginDto::new);
            logLoginDto.setCompanyName(companyName);
            convertDatas.add(logLoginDto);
        }
        return convertDatas;
    }

    @Override
    public Long addLogLogin(LogLogin logLogin) {
        utilService.updateUserInfoToEntity(logLogin);
        logLoginMapper.insert(logLogin);
        map.put("log", logLogin);
        return logLogin.getId();
    }


    /**
     * 强制登出
     *
     * @param id@return
     */
    @Override
    public SaveResponseBo forceLogout(Long id) {
        if (Objects.isNull(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        LogLogin login = logLoginMapper.selectById(id);
        if (GeneralTool.isNotEmpty(login)) {
            String sessionId = login.getSessionId();
            if (StringUtils.isNotBlank(sessionId)) {
                JwtUtil.removeAccessToken(String.valueOf(login.getFkStaffId()),sessionId);
            }
            login.setLogoutTime(new Date());
            login.setGmtModified(new Date());
            login.setGmtModifiedUser(SecureUtil.getLoginId());
            logLoginMapper.updateById(login);
        }
        return SaveResponseBo.ok(id);
    }

    /**
     * Author Cream
     * Description : //获取员工在线信息
     * Date 2023/4/4 14:30
     * Params:
     * Return
     */
    @Override
    public List<LogLoginDto> getStaffLoginInfo(LogLoginVo logLoginVo, SearchBean<LogLoginVo> page) {
        IPage<LogLoginDto> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<LogLoginDto> logLoginDtoList = logLoginMapper.getStaffLoginInfo(iPage,logLoginVo);
        page.setAll((int) iPage.getTotal());
        LocalDateTime now = LocalDateTime.now();
        for (LogLoginDto loginDto : logLoginDtoList) {
            if (loginDto.getIsOnline()) {
                LocalDateTime loginTime = loginDto.getLoginTime().toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
                Duration duration = Duration.between(loginTime, now);
                loginDto.setOnlineDurationInfo(CommonUtil.formatSeconds(Math.abs(duration.getSeconds())));
            }else {
                loginDto.setOnlineDurationInfo(CommonUtil.formatSeconds(loginDto.getOnlineDurationVal()));
            }
        }
        return logLoginDtoList;
    }

    /**
     * Author Cream
     * Description : //更新员工在线信息
     * Date 2023/4/4 14:51
     * Params:
     * Return
     */
    @Override
    public Boolean updateStaffLoginInfo(String sessionId, Long fkStaffId) {
        if (Objects.isNull(sessionId)) {
            Date now = new Date();
            String loginId = SecureUtil.getLoginId();
            logLoginMapper.updateLogoutTime(fkStaffId,now,loginId);
            return true;
        }else {
            LogLogin logLogin = logLoginMapper.getLoginInfoByIpAndId(sessionId,fkStaffId);
            if (logLogin!=null) {
                logLogin.setLogoutTime(new Date());
                logLogin.setGmtModified(new Date());
                logLogin.setGmtModifiedUser(SecureUtil.getLoginId());
                logLoginMapper.updateById(logLogin);
                return true;
            }
        }
        return false;
    }

    @Override
    public void updateLogLogin() {
        LogLogin logLogin = (LogLogin) map.get("log");
        logLogin.setLogoutTime(new Date());
        int i = logLoginMapper.updateById(logLogin);
        System.out.println("i = " + i);
    }
}
