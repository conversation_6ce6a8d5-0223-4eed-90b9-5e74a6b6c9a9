package com.get.core.log.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.log.model.LogOperation;
import com.get.core.log.vo.LogOperationVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
public interface LogOperationMapper extends BaseMapper<LogOperation> {

    int insertSelective(LogOperation record);

    /**
     * 列表
     *
     * @param logOperationVo
     * @return
     */
    List<LogOperation> getLogOperations(IPage<LogOperation> page, @Param("logOperationVo") LogOperationVo logOperationVo);
}