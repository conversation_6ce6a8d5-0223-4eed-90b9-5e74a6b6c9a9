xxl:
  job:
    accessToken: 'get-xxl-job-2022-02-09'
    admin:
      #      内网调试使用
      addresses: http://************:30021/xxl-job-admin
    #      生产使用
    #      addresses: http://**********:9009/xxl-job-admin
    executor:
      appname: get-job
      address:
      ip: 127.0.0.1
      logpath: ../data/applogs/xxl-job/jobhandler
      logretentiondays: -1
      port: 7018

#算法地址
algorithmAddress: http://************:5015/course_name_match/?is_strict=true

#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: xxljob
      datasource:
        xxljob:
          url: *****************************************************************************************************************************************
          username: root
          password: fzhmysql
        permissiondb:
          url: *******************************************************************************************************************************************************
          username: root
          password: fzhmysql
        officedb:
          url: ***************************************************************************************************************************************************
          username: root
          password: fzhmysql
        institutiondb:
          url: *********************************************************************************************************************************
          username: root
          password: fzhmysql
        financedb:
          url: *****************************************************************************************************************************
          username: root
          password: fzhmysql
        saledb:
          url: **************************************************************************************************************************
          username: root
          password: fzhmysql
        logdb:
          url: *************************************************************************************************************************
          username: root
          password: fzhmysql
        rpaIssuedb:
          url: *******************************************************************************************************************************
          username: root
          password: fzhmysql
        iaeCrmFiledb:
          url: ***************************************************************
          username: iaefileuser
          password: GETIAE_FILE_USER@hsdu#S612A
        iaeCrmNewsdb:
          url: ***********************************************************
          username: iaenewsuser
          password: iaenewsuser@js7!#$h2
        iaedb:
          url: **************************************************************************************************************************
          username: root
          password: fzhmysql
        twdb:
          url: **************************************************************************************************************************
          username: root
          password: fzhmysql
        saledb-doris:
          url: **************************************************************************************************************************
          username: root
          password: fzhmysql
        xxlJobCenterDb:
          url: *****************************************************************************************************************************
          username: root
          password: fzhmysql
        mail:
          url: ******************************************************************************************************************************************************************************************************************************************************************
          username: root
          password: fzhmysql
        file:
          username: root
          password: fzhmysql
          url: **************************************************************************************************************************************************************************************************************************************************************************************************
        pmp:
          username: root
          password: fzhmysql
          url: **************************************************************************************************************************************************************************************************************************************************************************************************
  #        cppdb:
  #          #测试
  #          url: *******************************************************************
  #          username: road
  #          password: GETCPP_ROAD_2F82KF7V
  #          #    生产
  #  #          url: ********************************************************************
  #  #          username: road
  #  #          password: GETCPP_ROAD_2F82KF7V

  tencentcloudfile:
    bucketname: hti-ais-files-dev-1301376564

#nacos远程访问
nacos:
  bms:
    username: nacos
    pwd: nacos
  app:
    username: nacos
    pwd: nacos

#canal远程访问
canal:
  username: test
  pwd: test


mail:
  exmail: imap.exmail.qq.com
  exmail-get-port: 993
  exmail-send: imap.exmail.qq.com
  exmail-send-port: 587
  exmail-inboxList: INBOX
  exmail-sentList: Sent Messages
  exmail-draftList: drafts
  exmail-deletedList: Deleted Messages

  gmail: imap.gmail.com
  gmail-inboxList: INBOX
  gmail-sentList: Sent Messages
  gmail-draftList: drafts
  gmail-deletedList: Deleted Messages

  163mail: imap.163.com
  163mail-get-port: 143
  163mail-send: smtp.163.com
  163mail-send-port: 465
  163mail-inboxList: INBOX
  163mail-sentList: 已发送
  163mail-draftList: 草稿箱
  163mail-deletedList: 已删除

  outlookmail: outlook.office365.com

encrypt:
  KEY: FZH8888888888888

thread-pool:
  max-thread-num: 5
  sync-thread-num: 5

file:
  bucketName: s3demo
  local:
    enable: true
    base-path: /Users/<USER>/Downloads/img
  tencentcloudimage:
    secretId: AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza
    secretKey: lQ8oRyOk12uETcyCGILG0b8sgchpttiT
    apCity: ap-shanghai
    bucketname: hti-email-images-dev-1301376564

  tencentcloudfile:
    secretId: AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza
    secretKey: lQ8oRyOk12uETcyCGILG0b8sgchpttiT
    apCity: ap-shanghai
    bucketname: hti-email-files-dev-1301376564

rocketmq:
  # 配置 NameServer 地址
  name-server: 124.223.77.112:9876
  # 生产者分组
  producer:
    group: ais_mail
    # 发送超时时间（毫秒）
    send-message-timeout: 3000
    # 生产者发送失败的最大重试次数
    retry-times-when-send-failed: 3
  consumer:
    # 消费者分组
    group: ais_mail
    # 消息最大重试次数（超出后进入死信队列）
    max-reconsume-times: 3
    # 开启消息轨迹
    enable-msg-trace: true