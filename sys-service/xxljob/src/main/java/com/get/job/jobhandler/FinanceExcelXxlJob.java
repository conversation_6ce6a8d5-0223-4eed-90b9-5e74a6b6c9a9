package com.get.job.jobhandler;

import com.get.job.component.FinanceExcelInfoHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * Time: 18:18
 * Date: 2022/4/1
 * Description:
 */
@Component
public class FinanceExcelXxlJob {

//    @Resource
//    private FinanceExcelInfoHelper financeExcelInfoHelper;

//    @XxlJob("FinanceExcelHandler")
//    public void FinanceExcelHandler() {
//        try {
//            XxlJobHelper.log("财务Excel定时器启动...");
//            financeExcelInfoHelper.financeExcelInfoSynchronization();
//        } catch (Exception e) {
//            XxlJobHelper.log("func[{}] e[{}-{}] desc[财务Excel定时器 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
//                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
//            e.getStackTrace();
//        }
//    }
}
