package com.get.job.dao.finance;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.job.entity.FinanceExcelInfo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("financedb")
public interface FinanceExcelInfoMapper extends BaseMapper<FinanceExcelInfo> {
    int insert(FinanceExcelInfo record);

    int insertSelective(FinanceExcelInfo record);
}