package com.get.job.jobhandler;

import com.get.job.service.mail.impl.SyncEmailStatusServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

@Component
public class SyncMail {
    private static final Logger logger = LoggerFactory.getLogger(DynamicRouteHealthCheckJob.class);
    @Resource
    private SyncEmailStatusServiceImpl syncEmailStatusService;

    @XxlJob("syncMailJob")
    public void fetchMailJob() throws Exception {
        try {
            logger.info("开始同步邮件");
            syncEmailStatusService.syncEmailStatus();
            logger.info("同步邮件结束");
        } catch (Exception e) {
            e.getStackTrace();
            XxlJobHelper.log("同步邮件失败");
            logger.error("同步邮件失败，{}", Arrays.deepToString(e.getStackTrace()));
        }
    }
}
