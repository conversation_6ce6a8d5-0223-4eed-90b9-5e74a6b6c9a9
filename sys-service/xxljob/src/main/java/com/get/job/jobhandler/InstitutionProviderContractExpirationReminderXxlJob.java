package com.get.job.jobhandler;

import com.get.job.service.permission.InstitutionProviderContractExpirationService;

import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


@Component
public class InstitutionProviderContractExpirationReminderXxlJob {

    private static final Logger logger = LoggerFactory.getLogger(InstitutionProviderContractExpirationReminderXxlJob.class);
    @Resource
    private InstitutionProviderContractExpirationService expirationService;

    @XxlJob("sendContractExpirationReminder")
    public void staffContractExpireTaskJobHandler() throws Exception {
        logger.info("XXL-JOB, sendContractExpirationReminder start...");
        boolean successful= expirationService.sendInstitutionProviderContractExpirationReminder();
        if (successful){
            logger.info("XXL-JOB, sendContractExpirationReminder success...");
        }else {
            logger.info("XXL-JOB, sendContractExpirationReminder fail...");
        }
    }


}
