<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.log.LogFinanceToBmsMapper">
  <!--TODO 注释sql-->
<!--  <insert id="insert" parameterType="com.get.job.entity.LogFinanceToBms">-->
<!--    insert into log_finance_to_bms (id, opt_key, finance_num, -->
<!--      finance_student_id, finance_country_num, finance_agent_name, -->
<!--      finance_institution_name, finance_institution_course_name, -->
<!--      fk_agent_id, fk_student_id, fk_area_country_id, -->
<!--      fk_student_offer_id, fk_student_offer_item_id, fk_institution_id, -->
<!--      fk_institution_course_id, log_type, remark-->
<!--      )-->
<!--    values (#{id,jdbcType=BIGINT}, #{optKey,jdbcType=VARCHAR}, #{financeNum,jdbcType=VARCHAR}, -->
<!--      #{financeStudentId,jdbcType=VARCHAR}, #{financeCountryNum,jdbcType=VARCHAR}, #{financeAgentName,jdbcType=VARCHAR}, -->
<!--      #{financeInstitutionName,jdbcType=VARCHAR}, #{financeInstitutionCourseName,jdbcType=VARCHAR}, -->
<!--      #{fkAgentId,jdbcType=BIGINT}, #{fkStudentId,jdbcType=BIGINT}, #{fkAreaCountryId,jdbcType=BIGINT}, -->
<!--      #{fkStudentOfferId,jdbcType=BIGINT}, #{fkStudentOfferItemId,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, -->
<!--      #{fkInstitutionCourseId,jdbcType=BIGINT}, #{logType,jdbcType=INTEGER}, #{remark,jdbcType=VARCHAR}-->
<!--      )-->
<!--  </insert>-->
  <!--TODO 注释sql-->
<!--  <insert id="insertSelective" parameterType="com.get.job.entity.LogFinanceToBms">-->
<!--    insert into log_finance_to_bms-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="optKey != null">-->
<!--        opt_key,-->
<!--      </if>-->
<!--      <if test="financeNum != null">-->
<!--        finance_num,-->
<!--      </if>-->
<!--      <if test="financeStudentId != null">-->
<!--        finance_student_id,-->
<!--      </if>-->
<!--      <if test="financeCountryNum != null">-->
<!--        finance_country_num,-->
<!--      </if>-->
<!--      <if test="financeAgentName != null">-->
<!--        finance_agent_name,-->
<!--      </if>-->
<!--      <if test="financeInstitutionName != null">-->
<!--        finance_institution_name,-->
<!--      </if>-->
<!--      <if test="financeInstitutionCourseName != null">-->
<!--        finance_institution_course_name,-->
<!--      </if>-->
<!--      <if test="fkAgentId != null">-->
<!--        fk_agent_id,-->
<!--      </if>-->
<!--      <if test="fkStudentId != null">-->
<!--        fk_student_id,-->
<!--      </if>-->
<!--      <if test="fkAreaCountryId != null">-->
<!--        fk_area_country_id,-->
<!--      </if>-->
<!--      <if test="fkStudentOfferId != null">-->
<!--        fk_student_offer_id,-->
<!--      </if>-->
<!--      <if test="fkStudentOfferItemId != null">-->
<!--        fk_student_offer_item_id,-->
<!--      </if>-->
<!--      <if test="fkInstitutionId != null">-->
<!--        fk_institution_id,-->
<!--      </if>-->
<!--      <if test="fkInstitutionCourseId != null">-->
<!--        fk_institution_course_id,-->
<!--      </if>-->
<!--      <if test="logType != null">-->
<!--        log_type,-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        remark,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="optKey != null">-->
<!--        #{optKey,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="financeNum != null">-->
<!--        #{financeNum,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="financeStudentId != null">-->
<!--        #{financeStudentId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="financeCountryNum != null">-->
<!--        #{financeCountryNum,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="financeAgentName != null">-->
<!--        #{financeAgentName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="financeInstitutionName != null">-->
<!--        #{financeInstitutionName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="financeInstitutionCourseName != null">-->
<!--        #{financeInstitutionCourseName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="fkAgentId != null">-->
<!--        #{fkAgentId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkStudentId != null">-->
<!--        #{fkStudentId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkAreaCountryId != null">-->
<!--        #{fkAreaCountryId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkStudentOfferId != null">-->
<!--        #{fkStudentOfferId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkStudentOfferItemId != null">-->
<!--        #{fkStudentOfferItemId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkInstitutionId != null">-->
<!--        #{fkInstitutionId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkInstitutionCourseId != null">-->
<!--        #{fkInstitutionCourseId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="logType != null">-->
<!--        #{logType,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        #{remark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
</mapper>