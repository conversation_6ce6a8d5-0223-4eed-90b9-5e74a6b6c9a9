package com.get.job.controller.mail;


import com.get.common.result.ResponseBo;
import com.get.job.jobhandler.FetchMailXxlJob;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "邮件获取")
@RestController
@RequestMapping("schedule/mail")
public class FetchMailController {
    @Resource
    private FetchMailXxlJob fetchMailXxlJob;

    @GetMapping("/fetchMail")
    public ResponseBo fetchMail() throws Exception {
        fetchMailXxlJob.fetchMailJob();
        return ResponseBo.ok();
    }
}
