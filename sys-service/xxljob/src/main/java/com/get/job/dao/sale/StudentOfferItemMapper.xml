<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.sale.StudentOfferItemMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.StudentOfferItem" keyProperty="id"
          useGeneratedKeys="true">
    insert into m_student_offer_item (id, fk_parent_student_offer_item_id, fk_student_id, 
      fk_agent_id, fk_staff_id, fk_student_offer_id, 
      fk_area_country_id, fk_institution_id, fk_institution_zone_id, 
      fk_institution_course_id, fk_institution_course_custom_id, 
      fk_institution_provider_id, fk_institution_channel_id, 
      fk_student_offer_item_step_id, fk_issue_rpa_order_id, 
      num, student_id, duration_type, 
      duration, opening_time, closing_time, 
      fk_currency_type_num, tuition_amount, course_website, 
      is_main, is_follow, is_credit_exemption, 
      is_add_app, is_step_follow, app_method, 
      app_remark, remark, condition_type, 
      fk_enrol_failure_reason_id, other_failure_reason, 
      is_defer_entrance, learning_mode, deposit_deadline, 
      accept_offer_deadline, status, rpa_opt_time, 
      rpa_finish_time, status_rpa, fk_platform_type, 
      old_institution_name, old_institution_full_name, 
      old_course_custom_name, old_course_major_level_name, 
      id_gea_finance, id_issue, id_gea, 
      id_iae, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user, rpa_remark
      )
    values (#{id,jdbcType=BIGINT}, #{fkParentStudentOfferItemId,jdbcType=BIGINT}, #{fkStudentId,jdbcType=BIGINT}, 
      #{fkAgentId,jdbcType=BIGINT}, #{fkStaffId,jdbcType=BIGINT}, #{fkStudentOfferId,jdbcType=BIGINT}, 
      #{fkAreaCountryId,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, #{fkInstitutionZoneId,jdbcType=BIGINT}, 
      #{fkInstitutionCourseId,jdbcType=BIGINT}, #{fkInstitutionCourseCustomId,jdbcType=BIGINT}, 
      #{fkInstitutionProviderId,jdbcType=BIGINT}, #{fkInstitutionChannelId,jdbcType=BIGINT}, 
      #{fkStudentOfferItemStepId,jdbcType=BIGINT}, #{fkIssueRpaOrderId,jdbcType=BIGINT}, 
      #{num,jdbcType=VARCHAR}, #{studentId,jdbcType=VARCHAR}, #{durationType,jdbcType=INTEGER}, 
      #{duration,jdbcType=DECIMAL}, #{openingTime,jdbcType=DATE}, #{closingTime,jdbcType=DATE}, 
      #{fkCurrencyTypeNum,jdbcType=VARCHAR}, #{tuitionAmount,jdbcType=DECIMAL}, #{courseWebsite,jdbcType=VARCHAR}, 
      #{isMain,jdbcType=BIT}, #{isFollow,jdbcType=BIT}, #{isCreditExemption,jdbcType=BIT}, 
      #{isAddApp,jdbcType=BIT}, #{isStepFollow,jdbcType=BIT}, #{appMethod,jdbcType=INTEGER}, 
      #{appRemark,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{conditionType,jdbcType=VARCHAR}, 
      #{fkEnrolFailureReasonId,jdbcType=BIGINT}, #{otherFailureReason,jdbcType=VARCHAR}, 
      #{isDeferEntrance,jdbcType=BIT}, #{learningMode,jdbcType=INTEGER}, #{depositDeadline,jdbcType=DATE}, 
      #{acceptOfferDeadline,jdbcType=DATE}, #{status,jdbcType=INTEGER}, #{rpaOptTime,jdbcType=TIMESTAMP}, 
      #{rpaFinishTime,jdbcType=TIMESTAMP}, #{statusRpa,jdbcType=VARCHAR}, #{fkPlatformType,jdbcType=VARCHAR}, 
      #{oldInstitutionName,jdbcType=VARCHAR}, #{oldInstitutionFullName,jdbcType=VARCHAR}, 
      #{oldCourseCustomName,jdbcType=VARCHAR}, #{oldCourseMajorLevelName,jdbcType=VARCHAR}, 
      #{idGeaFinance,jdbcType=VARCHAR}, #{idIssue,jdbcType=VARCHAR}, #{idGea,jdbcType=VARCHAR}, 
      #{idIae,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}, #{rpaRemark,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentOfferItem" keyProperty="id"
          useGeneratedKeys="true">
    insert into m_student_offer_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkParentStudentOfferItemId != null">
        fk_parent_student_offer_item_id,
      </if>
      <if test="fkStudentId != null">
        fk_student_id,
      </if>
      <if test="fkAgentId != null">
        fk_agent_id,
      </if>
      <if test="fkStaffId != null">
        fk_staff_id,
      </if>
      <if test="fkStudentOfferId != null">
        fk_student_offer_id,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="fkInstitutionId != null">
        fk_institution_id,
      </if>
      <if test="fkInstitutionZoneId != null">
        fk_institution_zone_id,
      </if>
      <if test="fkInstitutionCourseId != null">
        fk_institution_course_id,
      </if>
      <if test="fkInstitutionCourseCustomId != null">
        fk_institution_course_custom_id,
      </if>
      <if test="fkInstitutionProviderId != null">
        fk_institution_provider_id,
      </if>
      <if test="fkInstitutionChannelId != null">
        fk_institution_channel_id,
      </if>
      <if test="fkStudentOfferItemStepId != null">
        fk_student_offer_item_step_id,
      </if>
      <if test="fkIssueRpaOrderId != null">
        fk_issue_rpa_order_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="studentId != null">
        student_id,
      </if>
      <if test="durationType != null">
        duration_type,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="openingTime != null">
        opening_time,
      </if>
      <if test="closingTime != null">
        closing_time,
      </if>
      <if test="fkCurrencyTypeNum != null">
        fk_currency_type_num,
      </if>
      <if test="tuitionAmount != null">
        tuition_amount,
      </if>
      <if test="courseWebsite != null">
        course_website,
      </if>
      <if test="isMain != null">
        is_main,
      </if>
      <if test="isFollow != null">
        is_follow,
      </if>
      <if test="isCreditExemption != null">
        is_credit_exemption,
      </if>
      <if test="isAddApp != null">
        is_add_app,
      </if>
      <if test="isStepFollow != null">
        is_step_follow,
      </if>
      <if test="appMethod != null">
        app_method,
      </if>
      <if test="appRemark != null">
        app_remark,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="conditionType != null">
        condition_type,
      </if>
      <if test="fkEnrolFailureReasonId != null">
        fk_enrol_failure_reason_id,
      </if>
      <if test="otherFailureReason != null">
        other_failure_reason,
      </if>
      <if test="isDeferEntrance != null">
        is_defer_entrance,
      </if>
      <if test="learningMode != null">
        learning_mode,
      </if>
      <if test="depositDeadline != null">
        deposit_deadline,
      </if>
      <if test="acceptOfferDeadline != null">
        accept_offer_deadline,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="rpaOptTime != null">
        rpa_opt_time,
      </if>
      <if test="rpaFinishTime != null">
        rpa_finish_time,
      </if>
      <if test="statusRpa != null">
        status_rpa,
      </if>
      <if test="fkPlatformType != null">
        fk_platform_type,
      </if>
      <if test="oldInstitutionName != null">
        old_institution_name,
      </if>
      <if test="oldInstitutionFullName != null">
        old_institution_full_name,
      </if>
      <if test="oldCourseCustomName != null">
        old_course_custom_name,
      </if>
      <if test="oldCourseMajorLevelName != null">
        old_course_major_level_name,
      </if>
      <if test="idGeaFinance != null">
        id_gea_finance,
      </if>
      <if test="idIssue != null">
        id_issue,
      </if>
      <if test="idGea != null">
        id_gea,
      </if>
      <if test="idIae != null">
        id_iae,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="rpaRemark != null">
        rpa_remark,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkParentStudentOfferItemId != null">
        #{fkParentStudentOfferItemId,jdbcType=BIGINT},
      </if>
      <if test="fkStudentId != null">
        #{fkStudentId,jdbcType=BIGINT},
      </if>
      <if test="fkAgentId != null">
        #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="fkStaffId != null">
        #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="fkStudentOfferId != null">
        #{fkStudentOfferId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionId != null">
        #{fkInstitutionId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionZoneId != null">
        #{fkInstitutionZoneId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionCourseId != null">
        #{fkInstitutionCourseId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionCourseCustomId != null">
        #{fkInstitutionCourseCustomId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionProviderId != null">
        #{fkInstitutionProviderId,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionChannelId != null">
        #{fkInstitutionChannelId,jdbcType=BIGINT},
      </if>
      <if test="fkStudentOfferItemStepId != null">
        #{fkStudentOfferItemStepId,jdbcType=BIGINT},
      </if>
      <if test="fkIssueRpaOrderId != null">
        #{fkIssueRpaOrderId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="studentId != null">
        #{studentId,jdbcType=VARCHAR},
      </if>
      <if test="durationType != null">
        #{durationType,jdbcType=INTEGER},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=DECIMAL},
      </if>
      <if test="openingTime != null">
        #{openingTime,jdbcType=DATE},
      </if>
      <if test="closingTime != null">
        #{closingTime,jdbcType=DATE},
      </if>
      <if test="fkCurrencyTypeNum != null">
        #{fkCurrencyTypeNum,jdbcType=VARCHAR},
      </if>
      <if test="tuitionAmount != null">
        #{tuitionAmount,jdbcType=DECIMAL},
      </if>
      <if test="courseWebsite != null">
        #{courseWebsite,jdbcType=VARCHAR},
      </if>
      <if test="isMain != null">
        #{isMain,jdbcType=BIT},
      </if>
      <if test="isFollow != null">
        #{isFollow,jdbcType=BIT},
      </if>
      <if test="isCreditExemption != null">
        #{isCreditExemption,jdbcType=BIT},
      </if>
      <if test="isAddApp != null">
        #{isAddApp,jdbcType=BIT},
      </if>
      <if test="isStepFollow != null">
        #{isStepFollow,jdbcType=BIT},
      </if>
      <if test="appMethod != null">
        #{appMethod,jdbcType=INTEGER},
      </if>
      <if test="appRemark != null">
        #{appRemark,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="conditionType != null">
        #{conditionType,jdbcType=VARCHAR},
      </if>
      <if test="fkEnrolFailureReasonId != null">
        #{fkEnrolFailureReasonId,jdbcType=BIGINT},
      </if>
      <if test="otherFailureReason != null">
        #{otherFailureReason,jdbcType=VARCHAR},
      </if>
      <if test="isDeferEntrance != null">
        #{isDeferEntrance,jdbcType=BIT},
      </if>
      <if test="learningMode != null">
        #{learningMode,jdbcType=INTEGER},
      </if>
      <if test="depositDeadline != null">
        #{depositDeadline,jdbcType=DATE},
      </if>
      <if test="acceptOfferDeadline != null">
        #{acceptOfferDeadline,jdbcType=DATE},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="rpaOptTime != null">
        #{rpaOptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="rpaFinishTime != null">
        #{rpaFinishTime,jdbcType=TIMESTAMP},
      </if>
      <if test="statusRpa != null">
        #{statusRpa,jdbcType=VARCHAR},
      </if>
      <if test="fkPlatformType != null">
        #{fkPlatformType,jdbcType=VARCHAR},
      </if>
      <if test="oldInstitutionName != null">
        #{oldInstitutionName,jdbcType=VARCHAR},
      </if>
      <if test="oldInstitutionFullName != null">
        #{oldInstitutionFullName,jdbcType=VARCHAR},
      </if>
      <if test="oldCourseCustomName != null">
        #{oldCourseCustomName,jdbcType=VARCHAR},
      </if>
      <if test="oldCourseMajorLevelName != null">
        #{oldCourseMajorLevelName,jdbcType=VARCHAR},
      </if>
      <if test="idGeaFinance != null">
        #{idGeaFinance,jdbcType=VARCHAR},
      </if>
      <if test="idIssue != null">
        #{idIssue,jdbcType=VARCHAR},
      </if>
      <if test="idGea != null">
        #{idGea,jdbcType=VARCHAR},
      </if>
      <if test="idIae != null">
        #{idIae,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="rpaRemark != null">
        #{rpaRemark,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>

  <select id="selectWithNotNullIssueId" resultType="string">
    SELECT id,id_issue,status_rpa from m_student_offer_item WHERE id_issue is not null
    AND (status_rpa &lt;&gt; "C" OR status_rpa IS NULL) AND gmt_create > DATE_SUB( NOW(),INTERVAL 120 DAY);

  </select>

  <update id="updateByAplOrder" parameterType="com.get.salecenter.entity.StudentOfferItem">
    UPDATE m_student_offer_item

    <set>
    <if test="fkIssueRpaOrderId !=null and fkIssueRpaOrderId !=''">
      fk_issue_rpa_order_id = #{fkIssueRpaOrderId,jdbcType=BIGINT},
    </if>

      <if test="rpaOptTime !=null ">
        rpa_opt_time = #{rpaOptTime,jdbcType=DATE},
      </if>

      <if test="rpaFinishTime !=null">
        rpa_finish_time = #{rpaFinishTime,jdbcType=DATE},
      </if>

      <if test="rpaRemark !=null and rpaRemark !=''">
        rpa_remark = #{rpaRemark,jdbcType=VARCHAR},
      </if>

      <if test="statusRpa !=null and statusRpa !=''">
        status_rpa = #{statusRpa,jdbcType=VARCHAR}
      </if>
    </set>


    WHERE
        id_issue = #{idIssue,jdbcType=BIGINT};


  </update>

  <update id="updateByAplOrderBatch" >
    <foreach collection="studentOfferItems" item="item" index="index" separator=";" >
    UPDATE m_student_offer_item
      <set>
        <if test="item.fkIssueRpaOrderId !=null and item.fkIssueRpaOrderId !=''">
          fk_issue_rpa_order_id = #{item.fkIssueRpaOrderId,jdbcType=BIGINT},
        </if>

        <if test="item.rpaOptTime !=null ">
          rpa_opt_time = #{item.rpaOptTime,jdbcType=DATE},
        </if>

        <if test="item.rpaFinishTime !=null">
          rpa_finish_time = #{item.rpaFinishTime,jdbcType=DATE},
        </if>

        <if test="item.rpaRemark !=null and item.rpaRemark !=''">
          rpa_remark = #{item.rpaRemark,jdbcType=VARCHAR},
        </if>

        <if test="item.statusRpa !=null and item.statusRpa !=''">
          status_rpa = #{item.statusRpa,jdbcType=VARCHAR}
        </if>

      </set>
      <where>
        id_issue = #{item.idIssue,jdbcType=BIGINT}

      </where>

    </foreach>

  </update>

  <update id="updateIssueRpaStatusBatch" >
    <foreach collection="studentOfferItems" item="item" index="index" separator=";" >
      UPDATE m_student_offer_item
      <set>

        <if test="item.rpaFinishTime !=null">
          rpa_finish_time = #{item.rpaFinishTime,jdbcType=DATE},
        </if>

        <if test="item.rpaRemark !=null and item.rpaRemark !=''">
          rpa_remark = #{item.rpaRemark,jdbcType=VARCHAR},
        </if>

        <if test="item.statusRpa !=null and item.statusRpa !=''">
          status_rpa = #{item.statusRpa,jdbcType=VARCHAR}
        </if>

      </set>
      <where>
        fk_issue_rpa_order_id = #{item.fkIssueRpaOrderId,jdbcType=BIGINT}

      </where>

    </foreach>

  </update>
  <update id="updateIssueRpaStatus">
    UPDATE m_student_offer_item
    <set>

      <if test="studentOfferItem.rpaFinishTime !=null">
        rpa_finish_time = #{studentOfferItem.rpaFinishTime,jdbcType=TIMESTAMP},
      </if>

      <if test="studentOfferItem.rpaRemark !=null and studentOfferItem.rpaRemark !=''">
        rpa_remark = #{studentOfferItem.rpaRemark,jdbcType=VARCHAR},
      </if>

      <if test="studentOfferItem.statusRpa !=null and studentOfferItem.statusRpa !=''">
        status_rpa = #{studentOfferItem.statusRpa,jdbcType=VARCHAR},
      </if>
    <if test="studentOfferItem.rpaOptTime !=null  ">
      rpa_opt_time = #{studentOfferItem.rpaOptTime,jdbcType=TIMESTAMP}
    </if>

    </set>
    <where>
      fk_issue_rpa_order_id = #{studentOfferItem.fkIssueRpaOrderId,jdbcType=BIGINT}

    </where>

  </update>


</mapper>