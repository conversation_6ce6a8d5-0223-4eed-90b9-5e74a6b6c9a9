package com.get.job.component;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.entity.InstitutionCourse;
import com.get.institutioncenter.entity.InstitutionCourseRpaMappingRelation;
import com.get.job.dao.institution.InstitutionCourseMapper;
import com.get.job.dao.institution.InstitutionCourseRpaMappingRelationMapper;
import com.get.job.dto.CourseMatchDto;
import com.get.job.dto.CourseMatchScoreDto;
import com.get.job.dto.CourseNameMatchDto;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RPA课程同步辅助类
 *
 * <AUTHOR>
 * @date 2021/9/16 12:27
 */
@Component
public class RpaCourseMappingHelper {
    private static final Logger logger = LoggerFactory.getLogger(RpaCourseMappingHelper.class);
    /**
     * 算法地址
     */
    @Value(value = "${algorithmAddress}")
    private String algorithmAddress;
    @Resource
    private InstitutionCourseMapper institutionCourseMapper;
    @Resource
    private InstitutionCourseRpaMappingRelationMapper institutionCourseRpaMappingRelationMapper;

    @Async("defaultThreadPool")
    public void insertRpaMapping(Map<Integer, Long> institutionSchMap, Map<Integer, Map<String, List<Integer>>> schCourseRpaMap, Map.Entry<Integer, Map<String, List<Integer>>> entry) throws InterruptedException {
        Integer schId = entry.getKey();
        Long institutionId = institutionSchMap.get(schId);
        //旧课程名-RPA课程地图Ids
        Map<String, List<Integer>> courseRpaMap = entry.getValue();

        Map<String, Long> courseNameMap;
        LambdaQueryWrapper<InstitutionCourse> wrapper = new LambdaQueryWrapper();
        wrapper.eq(InstitutionCourse::getFkInstitutionId, institutionId).eq(InstitutionCourse::getIsActive, 1);
        List<InstitutionCourse> institutionCourses = institutionCourseMapper.selectList(wrapper);
//        Example example = new Example(InstitutionCourse.class);
//        example.createCriteria().andEqualTo("fkInstitutionId", institutionId).andEqualTo("isActive", 1);
//        List<InstitutionCourse> institutionCourses = institutionCourseMapper.selectByExample(example);
        if (GeneralTool.isNotEmpty(institutionCourses)) {
            courseNameMap = new HashMap<>();
            for (InstitutionCourse institutionCourse : institutionCourses) {
                courseNameMap.put(institutionCourse.getName(), institutionCourse.getId());
            }
            //新系统该学校下没有课程，不需要匹配
            if (GeneralTool.isEmpty(courseNameMap)) {
                return;
            }
        } else {
            return;
        }

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("short_list", courseRpaMap.keySet());
        jsonObject.put("long_list", courseNameMap.keySet());
        logger.info("RPA课程算法请求json:{}", jsonObject.toJSONString());
        String body = HttpUtil.createPost(algorithmAddress)
                .body(jsonObject.toJSONString()).header("accept", "application/json").header("Content-Type", "application/json").execute().body();
        if ("Internal Server info".equals(body) || "Internal Server Error".equals(body)) {
            logger.error("RPA课程算法匹配报错<|>schId:{}<|>body:{}", schId, jsonObject.toJSONString());
        }
        logger.info("RPA课程算法 body:{}", body);
        CourseNameMatchDto courseNameMatchDto = JSONObject.parseObject(body, CourseNameMatchDto.class);
        List<CourseMatchDto> data = courseNameMatchDto.getData();
        for (CourseMatchDto datum : data) {
            //rpa课程名
            String rpaCourseName = datum.getCourse_name();
            List<CourseMatchScoreDto> matchList = datum.getMatch_list();
            if (GeneralTool.isEmpty(matchList)) {
                continue;
            }
            //只取分数最高的一个课程
            CourseMatchScoreDto courseMatchScore = matchList.get(0);
            //新系统课程名
            String courseName = courseMatchScore.getCourse_name();
            //新系统课程id
            Long courseId = courseNameMap.get(courseName);
            //RPA课程地图Ids
            List<Integer> rpaIds = schCourseRpaMap.get(schId).get(rpaCourseName);
            for (Integer rpaId : rpaIds) {
                LambdaQueryWrapper<InstitutionCourseRpaMappingRelation> queryWrapper = new LambdaQueryWrapper();
                queryWrapper.eq(InstitutionCourseRpaMappingRelation::getFkInstitutionCourseId, courseId)
                        .eq(InstitutionCourseRpaMappingRelation::getFkInstitutionCourseRpaMappingId, rpaId);
                List<InstitutionCourseRpaMappingRelation> institutionCourseRpaMappingRelations = institutionCourseRpaMappingRelationMapper.selectList(queryWrapper);
//                example = new Example(InstitutionCourseRpaMappingRelation.class);
//                example.createCriteria().andEqualTo("fkInstitutionCourseId", courseId)
//                        .andEqualTo("fkInstitutionCourseRpaMappingId", rpaId);
//                List<InstitutionCourseRpaMappingRelation> institutionCourseRpaMappingRelations = institutionCourseRpaMappingRelationMapper.selectByExample(example);
                if (GeneralTool.isEmpty(institutionCourseRpaMappingRelations)) {
                    InstitutionCourseRpaMappingRelation institutionCourseRpaMappingRelation = new InstitutionCourseRpaMappingRelation();
                    institutionCourseRpaMappingRelation.setFkInstitutionCourseId(courseId);
                    institutionCourseRpaMappingRelation.setFkInstitutionCourseRpaMappingId(Long.valueOf(rpaId));
                    institutionCourseRpaMappingRelation.setGmtCreate(new Date());
                    institutionCourseRpaMappingRelationMapper.insert(institutionCourseRpaMappingRelation);
                }
            }
        }
    }
}
