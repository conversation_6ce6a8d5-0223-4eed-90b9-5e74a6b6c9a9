<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.institution.InstitutionZoneMapper">
  <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionZone">
    insert into m_institution_zone (id, fk_institution_id, fk_area_country_id, 
      fk_area_state_id, fk_area_city_id, name, 
      name_chn, view_order, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user, 
      description)
    values (#{id,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, #{fkAreaCountryId,jdbcType=BIGINT}, 
      #{fkAreaStateId,jdbcType=BIGINT}, #{fkAreaCityId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, 
      #{nameChn,jdbcType=VARCHAR}, #{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}, 
      #{description,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionZone">
    insert into m_institution_zone
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkInstitutionId != null">
        fk_institution_id,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id,
      </if>
      <if test="fkAreaCityId != null">
        fk_area_city_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameChn != null">
        name_chn,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
      <if test="description != null">
        description,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionId != null">
        #{fkInstitutionId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityId != null">
        #{fkAreaCityId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameChn != null">
        #{nameChn,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
</mapper>