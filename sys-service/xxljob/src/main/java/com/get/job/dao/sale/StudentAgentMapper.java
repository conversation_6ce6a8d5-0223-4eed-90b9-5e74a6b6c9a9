package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.StudentAgent;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("saledb")
public interface StudentAgentMapper extends BaseMapper<StudentAgent> {
    int insert(StudentAgent record);

    int insertSelective(StudentAgent record);

    int updateByPrimaryKeySelective(StudentAgent record);

    int updateByPrimaryKey(StudentAgent record);
}