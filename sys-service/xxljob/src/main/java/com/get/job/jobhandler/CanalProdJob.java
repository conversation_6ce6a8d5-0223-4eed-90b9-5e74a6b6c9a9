package com.get.job.jobhandler;

import com.get.job.service.canaljob.ICanalJobService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

@Component
public class CanalProdJob {

    private static final Logger logger = LoggerFactory.getLogger(CanalProdJob.class);
    @Resource
    private ICanalJobService canalJobService;



    /**
     * 同步canal平台的数据校验
     * 第一种 IAE和GEA数据库部分同步表的数据一致性校验
     * 每日运行后，输出校验结果到调度日志，根据调度日志来手工处理数据不一致的问题
     * 详细请登录：http://43.129.251.169:16784/ 管理canal调度任务
     */
    @XxlJob("synCanalDataMethodJob1")
    private void synCanalDataMethodJob1() {
        try {
            canalJobService.checkIaeAndGeaData();
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[测试校验IaeAndGea日志文件数据库数据 error]");
            logger.error("func[{}] e[{}-{}] desc[测试校验IaeAndGea日志文件数据库数据 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }

    }

    /**
     * 同步canal平台的数据校验
     * 第二种 GEA和Doris数据库一致性校验
     * 每日运行后，输出校验结果到调度日志，根据调度日志来手工处理数据不一致的问题
     * 详细请登录：http://43.129.251.169:16784/ 管理canal调度任务
     */
    @XxlJob("synCanalDataMethodJob2")
    private void synCanalDataMethodJob2() {
        try {
            canalJobService.checkGeaMysqlAndDorisData();
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[测试校验MysqlAndDoris日志文件数据库数据 error]");
            logger.error("func[{}] e[{}-{}] desc[测试校验MysqlAndDoris日志文件数据库数据 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));

        }

    }

    /**
     * 同步canal平台的数据校验
     * 第一种 GEA和TW数据库部分同步表的数据一致性校验
     * 每日运行后，输出校验结果到调度日志，根据调度日志来手工处理数据不一致的问题
     */
    @XxlJob("synCanalDataMethodJob3")
    private void synCanalDataMethodJob3() {
        try {
            canalJobService.checkGeaAndTWData();
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[测试校验twAndGea日志文件数据库数据 error]");
            logger.error("func[{}] e[{}-{}] desc[测试校验TwAndGea日志文件数据库数据 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }

    }
}
