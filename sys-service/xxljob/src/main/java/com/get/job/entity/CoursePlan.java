package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("COURSEPLAN")
public class CoursePlan {
    private static final long serialVersionUID = 1L;
    private BigDecimal id;
    /**
     * 学生id
     */
    private BigDecimal userid;
    private String country;
    /**
     * 1是加申 0和null是默认
     */
    private String addcptab;
    private String school;
    private BigDecimal price;
    private BigDecimal money;
    private Short valid;
    private String validState;
    private Date begintime;
    private Date finishtime;
    private BigDecimal tuition;
    private String currency;
    private String data;
    private Date createtime;
    private String coursename;
    private BigDecimal coursetypeid;
    private BigDecimal complete;
    private BigDecimal visacomplete;
    private BigDecimal per;
    private BigDecimal rate;
    private BigDecimal yongjin;
    private BigDecimal visaprice;
    private BigDecimal tuition2;
    private BigDecimal tuition3;
    private BigDecimal per2;
    private BigDecimal per3;
    private BigDecimal agentyongjin;
    private String coe;
    private Date coedeadtm;
    private BigDecimal otherin;
    private BigDecimal payschool;
    private Date onepayschooltime;
    private Date payschooltime;
    private BigDecimal payvisa;
    private Date onepayvisatime;
    private Date payvisatime;
    private BigDecimal payvisash;
    private Date onepayvisashtime;
    private Date payvisashtime;
    private BigDecimal payiae;
    private BigDecimal paylocaliae;
    private Date oneiaepaytime;
    private Date iaepaytime;
    private BigDecimal agentyongjinb;
    private BigDecimal otherout;
    private Date onevisapricetime;
    private Date visapricetime;
    private Date oneotherintime;
    private Date otherintime;
    private Date oneotherouttime;
    private Date otherouttime;
    private BigDecimal yongjin2;
    private BigDecimal yongjin3;
    private BigDecimal tuition4;
    private BigDecimal per4;
    private BigDecimal tuition5;
    private BigDecimal per5;
    private String bizhong;
    private String area;
    private BigDecimal alimony;
    private String coursename2;
    private String coursename3;
    private Date begintime2;
    private Date begintime3;
    private Date finishtime2;
    private Date finishtime3;
    private String apptime;
    private String apptime2;
    private String apptime3;
    private String getapptime;
    private String getapptime2;
    private String getapptime3;
    private BigDecimal writing;
    private BigDecimal writing2;
    private BigDecimal writing3;
    private BigDecimal listening;
    private BigDecimal listening2;
    private BigDecimal listening3;
    private BigDecimal reading;
    private BigDecimal reading2;
    private BigDecimal reading3;
    private BigDecimal oral;
    private BigDecimal oral2;
    private BigDecimal oral3;
    private String englishtest;
    private String englishtest2;
    private String englishtest3;
    private Date visatime;
    private Date getvisatime;
    private Date overseastime;
    private BigDecimal bail;
    private BigDecimal stub;
    private String eduroad;
    private String topeducation;
    private String metier;
    private String title;
    private String income;
    private BigDecimal emigration;
    private String academic;
    private String academic2;
    private String academic3;
    private BigDecimal ielts;
    private BigDecimal ielts2;
    private BigDecimal ielts3;
    private String specialty;
    private String specialty2;
    private String specialty3;
    private String appfile;
    private String appeal;
    private String remark;
    private String toefl;
    private String toefl2;
    private String toefl3;
    private String gre;
    private String gre2;
    private String gre3;
    private String gmat;
    private String gmat2;
    private String gmat3;
    private BigDecimal visapass;
    private BigDecimal chopid;
    private String studentnum;
    /**
     * 申报学生id
     */
    private String studentid;
    private String course1;
    private String course2;
    private String course3;
    private String course4;
    private String course5;
    private Date bgtime1;
    private Date bgtime2;
    private Date bgtime3;
    private Date bgtime4;
    private Date bgtime5;
    private Date fstime1;
    private Date fstime2;
    private Date fstime3;
    private Date fstime4;
    private Date fstime5;
    private BigDecimal xyper;
    private BigDecimal xyper2;
    private BigDecimal xyper3;
    private BigDecimal xyper5;
    private String pscurrency;
    private Date completetime;
    private Date datatime;
    private Date validtime;
    private Date coetime;
    private Date visacompletetime;
    private Date finishstatetime;
    private String appform;
    private BigDecimal parentsite;
    private BigDecimal selfsite;
    private BigDecimal finishstate;
    private String cashform;
    private String ticket;
    private String homelatter;
    private Date appformtime;
    private String visaform;
    private String acceptance;
    private BigDecimal homestate;
    private BigDecimal state;
    private Date statetime;
    private Short status;
    private Short choosestatus;
    private BigDecimal epid;
    private BigDecimal countryid;
    private BigDecimal areaid;
    private BigDecimal coursetype;
    private BigDecimal schooltype;
    private Date begindate;
    private BigDecimal confirmstate;
    private BigDecimal courseid;
    private BigDecimal schoolid;
    private BigDecimal dormservice;
    private BigDecimal planservice;
    private String coursetypecode;
    private BigDecimal majortype;
    private BigDecimal submittype;
    /**
     * 申请备注
     */
    private String submitcomment;
    private BigDecimal maincourse;
    private String payschoolremark;
    private String stagecourse;
    private BigDecimal stagetuition;
    private BigDecimal stageper;
    private BigDecimal stagexyper;
    private Date stagebgtime;
    private Date stagefstime;
    private String planeserviceremark;
    private String dormserviceremark;
    private BigDecimal coursecommission;
    private BigDecimal agentcommission;
    private String followUpCourse;
    private BigDecimal followUpTuition;
    private BigDecimal followUpPer;
    private BigDecimal followUpXyper;
    private Date followUpBgtime;
    private Date followUpFstime;
    private BigDecimal followUpCoursecommission;
    private BigDecimal followUpAgentcommission;
    private String otherschool;
    private BigDecimal arrangement;
    private BigDecimal deadcommission;
    private BigDecimal deadcommissionagent;
    private BigDecimal followUpArrangement;
    private BigDecimal followUpDeadcommission;
    private BigDecimal followUpDeadcommissionagent;
    private BigDecimal otherincome;
    private BigDecimal otherincomeagent;
    private String incomeremark;
    private String incomeremarkagent;
    private BigDecimal courseover;
    private BigDecimal commissionover;
    private String courseoverremark;
    private BigDecimal stagecoursebonus;
    private BigDecimal stagecourseeffect;
    private BigDecimal followUpCoursebonus;
    private BigDecimal followUpCourseeffect;
    private String schoolname;
    private BigDecimal courseCommission;
    private BigDecimal followUpCourseCommission;
    private BigDecimal agentCommission;
    private BigDecimal followUpAgentCommission;
    private BigDecimal stagexyper2;
    private BigDecimal followUpXyper2;
    private BigDecimal deadcommissionagent2;
    private BigDecimal followUpDeadcommissionagent2;
    private BigDecimal stagerate;
    private BigDecimal followrate;
    private BigDecimal doublecheck;
    private Date doublechecktime;
    private String doublecheckname;
    private BigDecimal stagexyper3;
    private BigDecimal followUpXyper3;
    private BigDecimal deadcommissionagent3;
    private BigDecimal followUpDeadcommissionagent3;
    private BigDecimal stagebonus;
    private BigDecimal arrangement3;
    private BigDecimal followUpbonus;
    private BigDecimal followUpArrangement3;
    private String followUpCourse2;
    private BigDecimal followUpTuition2;
    private Date followUpBgtime2;
    private Date followUpFstime2;
    private BigDecimal followUpCoursebonus2;
    private BigDecimal followUpCourseeffect2;
    private BigDecimal followUpPer2;
    private BigDecimal followUpDeadcommission2;
    private BigDecimal followUpArrangement2;
    private String followUpCourse3;
    private BigDecimal followUpTuition3;
    private Date followUpBgtime3;
    private Date followUpFstime3;
    private BigDecimal followUpCoursebonus3;
    private BigDecimal followUpCourseeffect3;
    private BigDecimal followUpPer3;
    private BigDecimal followUpDeadcommission3;
    private BigDecimal followUpAgentCommission2;
    private BigDecimal followUpAgentCommission3;
    private BigDecimal followUpCourseCommission2;
    private BigDecimal followUpCourseCommission3;
    private BigDecimal followUpCourseCommission1;
    private BigDecimal followUpAgentCommission1;
    private BigDecimal guardian;
    private String guardianremark;
    private BigDecimal guardianfee;
    private BigDecimal guardianPer;
    private BigDecimal guardianDeadcommission;
    private BigDecimal guardianCommission;
    private BigDecimal planservicefee;
    private BigDecimal planservicePer;
    private BigDecimal planserviceDeadcommission;
    private BigDecimal planserviceCommission;
    private BigDecimal dormservicefee;
    private BigDecimal dormservicePer;
    private BigDecimal dormserviceDeadcommission;
    private BigDecimal dormserviceCommission;
    private BigDecimal schoolgroup;
    private Date offerdeadline;
    private BigDecimal failstate;
    private BigDecimal laststate;
    private BigDecimal rcid;
    private BigDecimal integral;
    private BigDecimal adid;
    private BigDecimal liid;
    private BigDecimal typecarry;
    private BigDecimal numone;
    private BigDecimal chopcode;
    private Date admissiontime;
    private Date admissiontotime;
    private String prove;
    private BigDecimal outstate;
    private BigDecimal outstate1;
    private BigDecimal outstate2;
    private BigDecimal outstate3;
    private Date outexceldate;
    private Date outexceldate1;
    private Date outexceldate2;
    private Date outexceldate3;
    private BigDecimal outexcelstate;
    private BigDecimal outexcelstate1;
    private BigDecimal outexcelstate2;
    private BigDecimal outexcelstate3;
    private BigDecimal doublestate;
    private BigDecimal insurancefee;
    private BigDecimal insurance;
    private BigDecimal insurancePer;
    private BigDecimal insuranceDeadcommission;
    private BigDecimal insuranceCommission;
    private Date autodate;
    private BigDecimal isuStepId;
    private String pic;
    private Date submitreceipttime;
    private String cityid;
    private String fullname;
    private BigDecimal inrobot;
    private BigDecimal isuCdetId;
    private BigDecimal isuCdetState;
    private String orderidList;
    private String sendrobotstatus;
    private String issuestate;
    private String orderSchRegisterStatus;
    private String rpaStatus;
    private String completedinfostate;
    private String display;
    private String courseinfostate;
}