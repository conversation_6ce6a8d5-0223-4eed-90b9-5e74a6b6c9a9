package com.get.job.service.nacos.impl;

import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.job.service.nacos.INacosJobService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Base64;
import java.util.Date;

@Service
@Slf4j
public class NacosJobServiceImpl implements INacosJobService {
    @Resource
    private IFileCenterClient fileCenterClient;

    @Value("${spring.tencentcloudfile.bucketname}")
    private String fileBucketName;

    private static final String ZIP_FILE_PATH = "D:\\BaiduDown\\nacosFile\\";


    @Override
    public Boolean nacosBackup(String nacosIpAndPort, String username, String pwd, String key_) {
        InputStream input = null;
        FileOutputStream output = null;
        HttpURLConnection connection = null;
        try {
            String url = String.format("%s/nacos/v1/cs/configs?export=true&tenant=&group=&appName=&dataId=&ids=", nacosIpAndPort);
            String auth = String.format("%s:%s", username, pwd);
            String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());

            connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Authorization", "Basic " + encodedAuth);

            input = connection.getInputStream();

            String fileName = key_+"_"+DateUtil.formatDateTimeMini(new Date())+".zip";


            //保存文件到本地
//            output = new FileOutputStream(ZIP_FILE_PATH+fileName);
//            byte[] buffer = new byte[1024];
//            int length;
//            while ((length = input.read(buffer)) > 0) {
//                output.write(buffer, 0, length);
//            }

            //上传文件到腾讯云OSS
            byte[] bytes = this.readStream(input);
            if(GeneralTool.isNotEmpty(bytes))
            {
                MultipartFile multipartFile = this.getMultipartFile(fileName,bytes);
                //上传到GEA的私有桶中
                //路径和文件如：/nacosBackup/nacos_dev_20230417154132.zip
                fileCenterClient.uploadObjectByXxl(multipartFile,false,fileBucketName,"/nacosBackup/"+fileName);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }finally {
            if(connection!=null)
            {
                connection.disconnect();
            }
            try {
                if(input!=null)
                {
                    input.close();
                }
                if(output!=null)
                {
                    output.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return true;
    }

    //二进制文件转换MultipartFile，该方法会在项目路径新增文件
    public MultipartFile getMultipartFile(String name, byte[] bytes) {
        MultipartFile mfile = null;
        ByteArrayInputStream in = null;
        try {
            in = new ByteArrayInputStream(bytes);
            FileItemFactory factory = new DiskFileItemFactory(16, null);
            FileItem fileItem = factory.createItem("mainFile", "text/plain", false, name);
            IOUtils.copy(new ByteArrayInputStream(bytes), fileItem.getOutputStream());
            mfile = new CommonsMultipartFile(fileItem);
            in.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        return mfile;
    }

    private byte[] readStream(InputStream inputStream) {
        ByteArrayOutputStream outputStream = null;
        try {
            outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead = -1;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }catch (Exception e){
            e.printStackTrace();
        }
        return outputStream.toByteArray();
    }
}

