package com.get.job.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.permissioncenter.entity.StaffContract;
import lombok.Data;

import java.util.Date;

/**
 * @author: <PERSON>
 * @create: 2024/4/3 11:28
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffContractReminderDto extends StaffContract {

    private String staffFullName;

    private String companyName;

    private String departmentName;

    private String positionName;

    private String title;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractEndDate;

    private Long fkCompanyId;
}
