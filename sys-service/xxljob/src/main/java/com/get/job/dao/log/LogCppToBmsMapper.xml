<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.log.LogCppToBmsMapper">
  <!--TODO 注释sql-->
<!--  <insert id="insert" parameterType="com.get.job.entity.LogCppToBms">-->
<!--    insert into log_cpp_to_bms (id, cpp_student_id, cpp_area_country_id,-->
<!--      cpp_student_offer_id, cpp_student_offer_item_id,-->
<!--      cpp_institution_id, cpp_institution_course_name,-->
<!--      fk_student_id, fk_area_country_id, fk_student_offer_id,-->
<!--      fk_student_offer_item_id, fk_institution_id, fk_institution_course_id,-->
<!--      fk_institution_course_custom_id, remark, status,-->
<!--      gmt_create, gmt_create_user, gmt_modified,-->
<!--      gmt_modified_user)-->
<!--    values (#{id,jdbcType=BIGINT}, #{cppStudentId,jdbcType=BIGINT}, #{cppAreaCountryId,jdbcType=BIGINT},-->
<!--      #{cppStudentOfferId,jdbcType=BIGINT}, #{cppStudentOfferItemId,jdbcType=VARCHAR},-->
<!--      #{cppInstitutionId,jdbcType=BIGINT}, #{cppInstitutionCourseName,jdbcType=VARCHAR},-->
<!--      #{fkStudentId,jdbcType=BIGINT}, #{fkAreaCountryId,jdbcType=BIGINT}, #{fkStudentOfferId,jdbcType=BIGINT},-->
<!--      #{fkStudentOfferItemId,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, #{fkInstitutionCourseId,jdbcType=BIGINT},-->
<!--      #{fkInstitutionCourseCustomId,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER},-->
<!--      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      #{gmtModifiedUser,jdbcType=VARCHAR})-->
<!--  </insert>-->

<!--  <insert id="insertSelective" parameterType="com.get.job.entity.LogCppToBms">-->
<!--    insert into log_cpp_to_bms-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="cppStudentId != null">-->
<!--        cpp_student_id,-->
<!--      </if>-->
<!--      <if test="cppAreaCountryId != null">-->
<!--        cpp_area_country_id,-->
<!--      </if>-->
<!--      <if test="cppStudentOfferId != null">-->
<!--        cpp_student_offer_id,-->
<!--      </if>-->
<!--      <if test="cppStudentOfferItemId != null">-->
<!--        cpp_student_offer_item_id,-->
<!--      </if>-->
<!--      <if test="cppInstitutionId != null">-->
<!--        cpp_institution_id,-->
<!--      </if>-->
<!--      <if test="cppInstitutionCourseName != null">-->
<!--        cpp_institution_course_name,-->
<!--      </if>-->
<!--      <if test="fkStudentId != null">-->
<!--        fk_student_id,-->
<!--      </if>-->
<!--      <if test="fkAreaCountryId != null">-->
<!--        fk_area_country_id,-->
<!--      </if>-->
<!--      <if test="fkStudentOfferId != null">-->
<!--        fk_student_offer_id,-->
<!--      </if>-->
<!--      <if test="fkStudentOfferItemId != null">-->
<!--        fk_student_offer_item_id,-->
<!--      </if>-->
<!--      <if test="fkInstitutionId != null">-->
<!--        fk_institution_id,-->
<!--      </if>-->
<!--      <if test="fkInstitutionCourseId != null">-->
<!--        fk_institution_course_id,-->
<!--      </if>-->
<!--      <if test="fkInstitutionCourseCustomId != null">-->
<!--        fk_institution_course_custom_id,-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        remark,-->
<!--      </if>-->
<!--      <if test="status != null">-->
<!--        status,-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create,-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user,-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified,-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="cppStudentId != null">-->
<!--        #{cppStudentId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="cppAreaCountryId != null">-->
<!--        #{cppAreaCountryId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="cppStudentOfferId != null">-->
<!--        #{cppStudentOfferId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="cppStudentOfferItemId != null">-->
<!--        #{cppStudentOfferItemId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="cppInstitutionId != null">-->
<!--        #{cppInstitutionId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="cppInstitutionCourseName != null">-->
<!--        #{cppInstitutionCourseName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="fkStudentId != null">-->
<!--        #{fkStudentId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkAreaCountryId != null">-->
<!--        #{fkAreaCountryId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkStudentOfferId != null">-->
<!--        #{fkStudentOfferId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkStudentOfferItemId != null">-->
<!--        #{fkStudentOfferItemId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkInstitutionId != null">-->
<!--        #{fkInstitutionId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkInstitutionCourseId != null">-->
<!--        #{fkInstitutionCourseId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkInstitutionCourseCustomId != null">-->
<!--        #{fkInstitutionCourseCustomId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        #{remark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="status != null">-->
<!--        #{status,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->

</mapper>