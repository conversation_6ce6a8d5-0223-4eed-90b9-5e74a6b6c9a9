package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_finance_excel_info")
public class FinanceExcelInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * id
     */
    @ApiModelProperty(value = "id")
    @Column(name = "id")
    private Long id;
    /**
     * 唯一编号
     */
    @ApiModelProperty(value = "唯一编号")
    @Column(name = "num")
    private String num;
    /**
     * 成功年份
     */
    @ApiModelProperty(value = "成功年份")
    @Column(name = "year")
    private Integer year;
    /**
     * 成功月份
     */
    @ApiModelProperty(value = "成功月份")
    @Column(name = "succ_date")
    private String succDate;
    /**
     * 申请时间
     */
    @ApiModelProperty(value = "申请时间")
    @Column(name = "application_time")
    private Date applicationTime;
    /**
     * 市场部员工名字
     */
    @ApiModelProperty(value = "市场部员工名字")
    @Column(name = "rr")
    private String rr;
    /**
     * 代理名字
     */
    @ApiModelProperty(value = "代理名字")
    @Column(name = "agent_source_name")
    private String agentSourceName;
    /**
     * 代理省份
     */
    @ApiModelProperty(value = "代理省份")
    @Column(name = "agent_region")
    private String agentRegion;
    /**
     * 学生名字
     */
    @ApiModelProperty(value = "学生名字")
    @Column(name = "student_name")
    private String studentName;
    /**
     * cpp学生编号
     */
    @ApiModelProperty(value = "cpp学生编号")
    @Column(name = "our_id")
    private String ourId;
    /**
     * 国家代号
     */
    @ApiModelProperty(value = "国家代号")
    @Column(name = "country")
    private String country;
    /**
     * 学校名字
     */
    @ApiModelProperty(value = "学校名字")
    @Column(name = "school_name")
    private String schoolName;
    /**
     * 课程名字
     */
    @ApiModelProperty(value = "课程名字")
    @Column(name = "course_name")
    private String courseName;
    /**
     * offer ID
     */
    @ApiModelProperty(value = "offer ID")
    @Column(name = "std_id")
    private String stdId;
    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    @Column(name = "chop")
    private String chop;
    /**
     * 入学年份
     */
    @ApiModelProperty(value = "入学年份")
    @Column(name = "intake_year")
    private Integer intakeYear;
    /**
     * 入学月份
     */
    @ApiModelProperty(value = "入学月份")
    @Column(name = "intake_mth")
    private String intakeMth;
    /**
     * 应收币种编号
     */
    @ApiModelProperty(value = "应收币种编号")
    @Column(name = "source_cyy")
    private String sourceCyy;
    /**
     * 学费
     */
    @ApiModelProperty(value = "学费")
    @Column(name = "school_fee")
    private BigDecimal schoolFee;
    /**
     * 应收学校提供商比例
     */
    @ApiModelProperty(value = "应收学校提供商比例")
    @Column(name = "school_rate")
    private String schoolRate;
    /**
     * 上家手续费
     */
    @ApiModelProperty(value = "上家手续费")
    @Column(name = "net")
    private String net;
    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    @Column(name = "amount")
    private BigDecimal amount;
    /**
     * 奖励金额
     */
    @ApiModelProperty(value = "奖励金额")
    @Column(name = "additional_amount")
    private BigDecimal additionalAmount;
    /**
     * 总应收金额（应收+奖励｝
     */
    @ApiModelProperty(value = "总应收金额（应收+奖励｝")
    @Column(name = "total_amt_in")
    private BigDecimal totalAmtIn;
    /**
     * 收款单币种编号
     */
    @ApiModelProperty(value = "收款单币种编号")
    @Column(name = "rev_cyy")
    private String revCyy;
    /**
     * 第一部分实收金额
     */
    @ApiModelProperty(value = "第一部分实收金额")
    @TableField("st_term_1")
    private BigDecimal stTerm1;
    /**
     * 第一次部分收款时间
     */
    @ApiModelProperty(value = "第一次部分收款时间")
    @TableField("st_date_1")
    private Date stDate1;
    /**
     * 银行
     */
    @ApiModelProperty(value = "银行")
    @TableField("st_bank_1")
    private String stBank1;
    /**
     * 第二部分实收金额
     */
    @ApiModelProperty(value = "第二部分实收金额")
    @TableField("st_term_2")
    private BigDecimal stTerm2;
    /**
     * 第二部分收款时间
     */
    @ApiModelProperty(value = "第二部分收款时间")
    @TableField("st_date_2")
    private Date stDate2;
    /**
     * 银行
     */
    @ApiModelProperty(value = "银行")
    @TableField("st_bank_2")
    private String stBank2;
    /**
     * 第三部分收款金额
     */
    @ApiModelProperty(value = "第三部分收款金额")
    @TableField("st_term_3")
    private BigDecimal stTerm3;
    /**
     * 第三部分实收时间
     */
    @ApiModelProperty(value = "第三部分实收时间")
    @TableField("st_date_3")
    private Date stDate3;
    /**
     * 银行
     */
    @ApiModelProperty(value = "银行")
    @TableField("st_bank_3")
    private String stBank3;
    /**
     * 第四部分收款金额
     */
    @ApiModelProperty(value = "第四部分收款金额")
    @TableField("st_term_4")
    private BigDecimal stTerm4;
    /**
     * 第四部分收款时间
     */
    @ApiModelProperty(value = "第四部分收款时间")
    @TableField("st_date_4")
    private Date stDate4;
    /**
     * 银行
     */
    @ApiModelProperty(value = "银行")
    @TableField("st_bank_4")
    private String stBank4;
    /**
     * 总实收金额
     */
    @ApiModelProperty(value = "总实收金额")
    @Column(name = "total_received")
    private BigDecimal totalReceived;
    /**
     * 银行手续费
     */
    @ApiModelProperty(value = "银行手续费")
    @Column(name = "bank_charges")
    private BigDecimal bankCharges;
    /**
     * 代理比例
     */
    @ApiModelProperty(value = "代理比例")
    @Column(name = "agent_rate")
    private String agentRate;
    /**
     * 代理分成比例
     */
    @ApiModelProperty(value = "代理分成比例")
    @Column(name = "split_rate")
    private String splitRate;
    /**
     * 代理应付学费佣金
     */
    @ApiModelProperty(value = "代理应付学费佣金")
    @Column(name = "agent_comm")
    private BigDecimal agentComm;
    /**
     * 奖励
     */
    @ApiModelProperty(value = "奖励")
    @Column(name = "additional_agent_comm")
    private BigDecimal additionalAgentComm;
    /**
     * 应付计划总金额
     */
    @ApiModelProperty(value = "应付计划总金额")
    @Column(name = "total_agent_comm")
    private BigDecimal totalAgentComm;
    /**
     * 第一部分实付款金额
     */
    @ApiModelProperty(value = "第一部分实付款金额")
    @TableField("st_payment_1")
    private BigDecimal stPayment1;
    /**
     * 第一次部分付款时间
     */
    @ApiModelProperty(value = "第一次部分付款时间")
    @TableField("st_payment_date_1")
    private Date stPaymentDate1;
    /**
     * 第二部分实付金额
     */
    @ApiModelProperty(value = "第二部分实付金额")
    @TableField("st_payment_2")
    private BigDecimal stPayment2;
    /**
     * 第二部分付款时间
     */
    @ApiModelProperty(value = "第二部分付款时间")
    @TableField("st_payment_date_2")
    private Date stPaymentDate2;
    /**
     * 第三部分付款金额
     */
    @ApiModelProperty(value = "第三部分付款金额")
    @TableField("st_payment_3")
    private BigDecimal stPayment3;
    /**
     * 第三部分实付时间
     */
    @ApiModelProperty(value = "第三部分实付时间")
    @TableField("st_payment_date_3")
    private Date stPaymentDate3;
    /**
     * 第四部分付款金额
     */
    @ApiModelProperty(value = "第四部分付款金额")
    @TableField("st_payment_4")
    private BigDecimal stPayment4;
    /**
     * 第四部分付款时间
     */
    @ApiModelProperty(value = "第四部分付款时间")
    @TableField("st_payment_date_4")
    private Date stPaymentDate4;
    /**
     * 代理总实收金额
     */
    @ApiModelProperty(value = "代理总实收金额")
    @Column(name = "total_paid")
    private BigDecimal totalPaid;
    /**
     * 代理奖励
     */
    @ApiModelProperty(value = "代理奖励")
    @Column(name = "agent_bonus")
    private BigDecimal agentBonus;
    /**
     * 代理手续费
     */
    @ApiModelProperty(value = "代理手续费")
    @Column(name = "agent_bank_charges")
    private BigDecimal agentBankCharges;
}