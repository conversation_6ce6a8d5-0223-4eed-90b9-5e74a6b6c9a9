<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.institution.AreaCountryMapper">
    <insert id="insert" parameterType="com.get.institutioncenter.entity.AreaCountry">
        insert into u_area_country (id, fk_currency_type_num, num,
                                    name, name_chn, capital,
                                    population, area, language,
                                    religion, time_difference, president,
                                    flag_meaning, emblem_meaning, remark,
                                    business_area_key, public_level, view_order,
                                    id_gea, id_iae, gmt_create,
                                    gmt_create_user, gmt_modified, gmt_modified_user)
        values (#{id,jdbcType=BIGINT}, #{fkCurrencyTypeNum,jdbcType=VARCHAR}, #{num,jdbcType=VARCHAR},
                #{name,jdbcType=VARCHAR}, #{nameChn,jdbcType=VARCHAR}, #{capital,jdbcType=VARCHAR},
                #{population,jdbcType=VARCHAR}, #{area,jdbcType=VARCHAR}, #{language,jdbcType=VARCHAR},
                #{religion,jdbcType=VARCHAR}, #{timeDifference,jdbcType=VARCHAR}, #{president,jdbcType=VARCHAR},
                #{flagMeaning,jdbcType=VARCHAR}, #{emblemMeaning,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR},
                #{businessAreaKey,jdbcType=VARCHAR}, #{publicLevel,jdbcType=VARCHAR}, #{viewOrder,jdbcType=INTEGER},
                #{idGea,jdbcType=VARCHAR}, #{idIae,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
                #{gmtModifiedUser,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.AreaCountry">
        insert into u_area_country
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkCurrencyTypeNum != null">
                fk_currency_type_num,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="nameChn != null">
                name_chn,
            </if>
            <if test="capital != null">
                capital,
            </if>
            <if test="population != null">
                population,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="language != null">
                language,
            </if>
            <if test="religion != null">
                religion,
            </if>
            <if test="timeDifference != null">
                time_difference,
            </if>
            <if test="president != null">
                president,
            </if>
            <if test="flagMeaning != null">
                flag_meaning,
            </if>
            <if test="emblemMeaning != null">
                emblem_meaning,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="businessAreaKey != null">
                business_area_key,
            </if>
            <if test="publicLevel != null">
                public_level,
            </if>
            <if test="viewOrder != null">
                view_order,
            </if>
            <if test="idGea != null">
                id_gea,
            </if>
            <if test="idIae != null">
                id_iae,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkCurrencyTypeNum != null">
                #{fkCurrencyTypeNum,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="capital != null">
                #{capital,jdbcType=VARCHAR},
            </if>
            <if test="population != null">
                #{population,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="language != null">
                #{language,jdbcType=VARCHAR},
            </if>
            <if test="religion != null">
                #{religion,jdbcType=VARCHAR},
            </if>
            <if test="timeDifference != null">
                #{timeDifference,jdbcType=VARCHAR},
            </if>
            <if test="president != null">
                #{president,jdbcType=VARCHAR},
            </if>
            <if test="flagMeaning != null">
                #{flagMeaning,jdbcType=VARCHAR},
            </if>
            <if test="emblemMeaning != null">
                #{emblemMeaning,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="businessAreaKey != null">
                #{businessAreaKey,jdbcType=VARCHAR},
            </if>
            <if test="publicLevel != null">
                #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="idGea != null">
                #{idGea,jdbcType=VARCHAR},
            </if>
            <if test="idIae != null">
                #{idIae,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.get.institutioncenter.entity.AreaCountry">
        update u_area_country
        <set>
            <if test="fkCurrencyTypeNum != null">
                fk_currency_type_num = #{fkCurrencyTypeNum,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                name_chn = #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="capital != null">
                capital = #{capital,jdbcType=VARCHAR},
            </if>
            <if test="population != null">
                population = #{population,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="language != null">
                language = #{language,jdbcType=VARCHAR},
            </if>
            <if test="religion != null">
                religion = #{religion,jdbcType=VARCHAR},
            </if>
            <if test="timeDifference != null">
                time_difference = #{timeDifference,jdbcType=VARCHAR},
            </if>
            <if test="president != null">
                president = #{president,jdbcType=VARCHAR},
            </if>
            <if test="flagMeaning != null">
                flag_meaning = #{flagMeaning,jdbcType=VARCHAR},
            </if>
            <if test="emblemMeaning != null">
                emblem_meaning = #{emblemMeaning,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="businessAreaKey != null">
                business_area_key = #{businessAreaKey,jdbcType=VARCHAR},
            </if>
            <if test="publicLevel != null">
                public_level = #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                view_order = #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="idGea != null">
                id_gea = #{idGea,jdbcType=VARCHAR},
            </if>
            <if test="idIae != null">
                id_iae = #{idIae,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.institutioncenter.entity.AreaCountry">
        update u_area_country
        set fk_currency_type_num = #{fkCurrencyTypeNum,jdbcType=VARCHAR},
            num                  = #{num,jdbcType=VARCHAR},
            name                 = #{name,jdbcType=VARCHAR},
            name_chn             = #{nameChn,jdbcType=VARCHAR},
            capital              = #{capital,jdbcType=VARCHAR},
            population           = #{population,jdbcType=VARCHAR},
            area                 = #{area,jdbcType=VARCHAR},
            language             = #{language,jdbcType=VARCHAR},
            religion             = #{religion,jdbcType=VARCHAR},
            time_difference      = #{timeDifference,jdbcType=VARCHAR},
            president            = #{president,jdbcType=VARCHAR},
            flag_meaning         = #{flagMeaning,jdbcType=VARCHAR},
            emblem_meaning       = #{emblemMeaning,jdbcType=VARCHAR},
            remark               = #{remark,jdbcType=VARCHAR},
            business_area_key    = #{businessAreaKey,jdbcType=VARCHAR},
            public_level         = #{publicLevel,jdbcType=VARCHAR},
            view_order           = #{viewOrder,jdbcType=INTEGER},
            id_gea               = #{idGea,jdbcType=VARCHAR},
            id_iae               = #{idIae,jdbcType=VARCHAR},
            gmt_create           = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user      = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified         = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user    = #{gmtModifiedUser,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectCourtryListByFindInSetGeaId" resultType="com.get.institutioncenter.entity.AreaCountry">
      SELECT * FROM u_area_country WHERE FIND_IN_SET(#{idGea}, id_gea)
    </select>
</mapper>