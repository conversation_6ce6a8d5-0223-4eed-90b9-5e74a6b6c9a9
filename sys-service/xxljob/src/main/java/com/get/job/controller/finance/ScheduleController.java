package com.get.job.controller.finance;

import com.get.common.result.ResponseBo;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.job.jobhandler.FinanceSettlementRollBackXxlJob;
import com.get.job.service.finance.IScheduleService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;

/**
 * @author: Sea
 * @create: 2021/2/20 15:03
 * @verison: 1.0
 * @description:
 */
@Api(tags = "定时器管理")
@RestController
@RequestMapping("schedule/schedule")
public class ScheduleController {
    @Resource
    private IScheduleService scheduleService;
    @Resource
    private FinanceSettlementRollBackXxlJob financeSettlementRollBackXxlJob;

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :重新运行获取汇率定时器
     * @Param []
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("setExchangeRateTime")
    public ResponseBo setExchangeRateTime() {
        scheduleService.setExchangeRateTime();
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @GetMapping("financeSettlementRollBackXxlJob")
    public ResponseBo financeSettlementRollBackXxlJob() {
        financeSettlementRollBackXxlJob.financeSettlementRollBackHandler();
        return ResponseBo.ok();
    }
}
