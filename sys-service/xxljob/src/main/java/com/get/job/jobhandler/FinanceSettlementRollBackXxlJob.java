package com.get.job.jobhandler;

import com.get.job.component.FinanceSettlementRollBackHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * 财务佣金回退定时任务
 *
 * <AUTHOR>
 * @date 2022/7/14 11:17
 */
@Component
public class FinanceSettlementRollBackXxlJob {
    @Resource
    private FinanceSettlementRollBackHelper financeSettlementRollBackHelper;

    @XxlJob("FinanceSettlementRollBackHandler")
    public void financeSettlementRollBackHandler() {
//        try {
            XxlJobHelper.log("财务佣金回退定时任务启动...");
            financeSettlementRollBackHelper.financeSettlementRollBack();
//        } catch (Exception e) {
//            XxlJobHelper.log("func[{}] e[{}-{}] desc[财务佣金回退定时任务 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
//                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
//            e.getStackTrace();
//        }
    }
}
