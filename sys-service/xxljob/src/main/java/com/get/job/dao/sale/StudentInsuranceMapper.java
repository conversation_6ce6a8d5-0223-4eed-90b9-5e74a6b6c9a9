package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.StudentInsurance;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("saledb")
public interface StudentInsuranceMapper extends BaseMapper<StudentInsurance> {
    @Override
    int insert(StudentInsurance record);

    int insertSelective(StudentInsurance record);
}