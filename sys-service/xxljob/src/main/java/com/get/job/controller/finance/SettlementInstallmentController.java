//package com.get.job.controller.finance;
//
//import com.get.common.consts.LoggerModulesConsts;
//import com.get.common.consts.LoggerOptTypeConst;
//import com.get.common.result.ResponseBo;
//import com.get.core.log.annotation.OperationLogger;
//import com.get.core.secure.annotation.VerifyPermission;
//import com.get.job.component.SettlementInstallmentHelper;
//import io.swagger.annotations.ApiOperation;
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
//import javax.annotation.Resource;
//
///**
// * 财务同步：同步佣金结算
// *
// * <AUTHOR>
// * @date 2022/4/28 10:39
// */
//@RestController
//@RequestMapping("schedule/settlementInstallment")
//public class SettlementInstallmentController {
//    @Resource
//    private SettlementInstallmentHelper settlementInstallmentHelper;
//
//    @VerifyPermission(IsVerify = false)
//    @ApiOperation(value = "财务佣金同步")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "定时任务中心/财务同步管理/财务佣金同步")
//    @GetMapping("insertSettlementInstallment")
//    public ResponseBo insertSettlementInstallment() {
//        settlementInstallmentHelper.insertSettlementInstallment();
//        return ResponseBo.ok();
//    }
//
//}
