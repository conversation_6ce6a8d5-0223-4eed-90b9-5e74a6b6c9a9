package com.get.job.jobhandler;

import com.get.core.tool.utils.DateUtil;
import com.get.job.service.pmp.InstitutionProviderContractService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/3/31
 * @Version 1.0
 * @apiNote:
 */
@Component
@Slf4j
public class PmpContractExpireXxlJob {

    @Resource
    private InstitutionProviderContractService contractService;


    @XxlJob("PmpContractExpireXxlJob")
    public void contractExpireXxlJob() {
        try {
            log.info("PmpContractExpireXxlJob start...{}", DateUtil.formatDateTime(new Date()));
            contractService.unActiveContract();
        } catch (Exception e) {
            log.error("PmpContractExpireXxlJob error:{}", Arrays.toString(e.getStackTrace()));
            log.error("PmpContractExpireXxlJob error:{}", e.getMessage());
            throw e; // 继续抛出异常，事务才能回滚
        }
    }
}
