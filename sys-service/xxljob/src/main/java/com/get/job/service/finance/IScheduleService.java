package com.get.job.service.finance;

/**
 * @author: Sea
 * @create: 2020/12/23 15:09
 * @verison: 1.0
 * @description: 定义接口（定时任务启动及停止）
 */
public interface IScheduleService {
    /**
     * 开启定时器
     */
    void start();

    /**
     * 停止定时器
     */
    void stop();

    /**
     * @return void
     * @Description :重新运行获取汇率定时器
     * @Param []
     * <AUTHOR>
     */
    void setExchangeRateTime();

}
