package com.get.job.service.getXxlJob.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.core.tool.utils.GeneralTool;
import com.get.job.dao.getXxlJob.SqlCronJobMapper;
import com.get.job.entity.SqlCronJob;
import com.get.job.service.getXxlJob.ISqlCronJobService;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.SqlSessionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

@Service
@Slf4j
public class SqlCronJobServiceImpl implements ISqlCronJobService {
    @Resource
    private SqlCronJobMapper sqlCronJobMapper;

    @Autowired
    private SqlSessionTemplate sqlSessionTemplate;

    @Override
    public void execSqlScriptJob() {
        //查询已激活的SQL脚本
        List<SqlCronJob> sqlCronJobs = sqlCronJobMapper.selectList(Wrappers.<SqlCronJob>lambdaQuery().eq(SqlCronJob::getIsActive, true));
        if(GeneralTool.isNotEmpty(sqlCronJobs))
        {
            for(SqlCronJob sqlCronJob:sqlCronJobs)
            {
                String sql = sqlCronJob.getSql();
                if(GeneralTool.isNotEmpty(sql))
                {
                    Object result = this.excuteUpdateSql(sql);
                    if(GeneralTool.isNotEmpty(sqlCronJob.getInterval()))
                    {
                        try {
                            //休息时间
                            Thread.sleep(sqlCronJob.getInterval());
                            XxlJobHelper.log("执行SQL后的结果：[{}]",GeneralTool.toJson(result));
                        }catch (Exception e)
                        {
                            e.printStackTrace();
                        }

                    }
                }
            }
        }else
        {
            XxlJobHelper.log("没有需要执行的SQL！！！");
        }

    }

    public Object excuteUpdateSql(String sql){
        XxlJobHelper.log("执行的sql:[{}]",sql);
//        List<Map<String, Object>> list = new ArrayList<Map<String,Object>>();
        PreparedStatement pst = null;
        SqlSession session = getSqlSession();
//        ResultSet result = null;
        Integer result = null;
        try {
            pst = session.getConnection().prepareStatement(sql);
//            result = pst.executeQuery();
            result = pst.executeUpdate();
//            ResultSetMetaData md = result.getMetaData();
//            int columnCount = md.getColumnCount();
//            while (result.next()) {
//                Map<String,Object> rowData = new HashMap<String,Object>();
//                for (int i = 1; i <= columnCount; i++) {
//                    rowData.put(md.getColumnName(i), result.getObject(i));
//                }
//                list.add(rowData);
//            }
        } catch (SQLException e) {
            e.printStackTrace();
        }finally {
            if(pst!=null){
                try {
                    pst.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }
            closeSqlSession(session);
        }
        return result;
    }

    /**
     * 获取sqlSession
     * @return
     */
    public SqlSession getSqlSession(){
        return SqlSessionUtils.getSqlSession(sqlSessionTemplate.getSqlSessionFactory(),
                sqlSessionTemplate.getExecutorType(), sqlSessionTemplate.getPersistenceExceptionTranslator());
    }

    /**
     * 关闭sqlSession
     * @param session
     */
    public void closeSqlSession(SqlSession session){
        SqlSessionUtils.closeSqlSession(session, sqlSessionTemplate.getSqlSessionFactory());
    }

}
