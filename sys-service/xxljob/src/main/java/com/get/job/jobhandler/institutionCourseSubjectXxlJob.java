package com.get.job.jobhandler;

import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.mybatis.logging.Logger;
import org.mybatis.logging.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Author: Smail
 * Date: 24/6/2024
 */
@Component
public class institutionCourseSubjectXxlJob {
    private static final Logger logger = LoggerFactory.getLogger(institutionCourseSubjectXxlJob.class);
    @Resource
    private IInstitutionCenterClient iInstitutionCenterClient;

    @XxlJob("TranslationTask")//确保名称是唯一的
    public void TranslationTask() {

//        BatchTranslationInstitutionAndCourseInfoDto batchTranslationInstitutionAndCourseInfoVo = new BatchTranslationInstitutionAndCourseInfoDto();
        //88,84,85,83,50,60,81,80,91,101,242,105,82,87,1048,4535,109,131,124,136,111,10,116,119,122,112,8458,120,108,127,132,40,70,155,117,118,140,128,145,135
        //新增学校id
//        List<Long> institutionIds = Arrays.asList(
//                Long.valueOf(10), Long.valueOf(40), Long.valueOf(50),Long.valueOf(60),
//                Long.valueOf(70), Long.valueOf(80),Long.valueOf(81), Long.valueOf(82),
//                Long.valueOf(83), Long.valueOf(84), Long.valueOf(85), Long.valueOf(87),
//                Long.valueOf(88),Long.valueOf(91), Long.valueOf(101),Long.valueOf(105),
//                Long.valueOf(108), Long.valueOf(109),Long.valueOf(111), Long.valueOf(112),
//                Long.valueOf(119),Long.valueOf(117), Long.valueOf(118), Long.valueOf(116),
//                Long.valueOf(120), Long.valueOf(122), Long.valueOf(124),Long.valueOf(127),
//                Long.valueOf(128), Long.valueOf(131), Long.valueOf(132), Long.valueOf(135),
//                Long.valueOf(136),Long.valueOf(140), Long.valueOf(145), Long.valueOf(155),
//                Long.valueOf(242), Long.valueOf(1048), Long.valueOf(8458),Long.valueOf(4535),
//
//                Long.valueOf(110), Long.valueOf(133), Long.valueOf(139),Long.valueOf(141),
//                Long.valueOf(142), Long.valueOf(147), Long.valueOf(125),Long.valueOf(130),
//                Long.valueOf(137), Long.valueOf(143), Long.valueOf(146),Long.valueOf(150),
//                Long.valueOf(153), Long.valueOf(154), Long.valueOf(156),Long.valueOf(160),
//                Long.valueOf(162), Long.valueOf(165), Long.valueOf(168),Long.valueOf(170),
//                Long.valueOf(172), Long.valueOf(174), Long.valueOf(176),Long.valueOf(179),
//                Long.valueOf(182), Long.valueOf(183), Long.valueOf(206),Long.valueOf(240),
//                Long.valueOf(30)
//
//        );
//        batchTranslationInstitutionAndCourseInfoVo.setInstitutionIds(institutionIds);
        try {
            XxlJobHelper.log("课程同步翻译定时任务开始...");
        iInstitutionCenterClient.batchTranslationInstitutionAndCourseInfo();

        } catch (Exception e) {
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[课程同步翻译定时任务 error]");
//            logger.error("func[{}] e[{}-{}] desc[RPA课程同步定时任务 error]",Thread.currentThread().getStackTrace()[1].getMethodName(),e.getMessage(),Arrays.deepToString(e.getStackTrace()));

        } finally {

        }
    }



}

