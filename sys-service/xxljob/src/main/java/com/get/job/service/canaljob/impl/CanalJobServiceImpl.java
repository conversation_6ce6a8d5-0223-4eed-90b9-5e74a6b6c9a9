package com.get.job.service.canaljob.impl;

import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.job.service.canaljob.ICanalJobService;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.HttpCookie;
import java.sql.*;
import java.util.*;

@Service
@Slf4j
public class CanalJobServiceImpl implements ICanalJobService {

    public static final String dirver = "com.mysql.jdbc.Driver";
    @Value("${spring.datasource.dynamic.datasource.iaedb.url}")
	private String iaeUrl;
    @Value("${spring.datasource.dynamic.datasource.iaedb.username}")
	private String iaeUserName;
    @Value("${spring.datasource.dynamic.datasource.iaedb.password}")
	private String iaePwd;

    @Value("${spring.datasource.dynamic.datasource.saledb.url}")
	private String geaUrl;
    @Value("${spring.datasource.dynamic.datasource.saledb.username}")
	private String geaUserName;
    @Value("${spring.datasource.dynamic.datasource.saledb.password}")
	private String geaPwd;

    @Value("${spring.datasource.dynamic.datasource.saledb-doris.url}")
	private String dorisUrl;
    @Value("${spring.datasource.dynamic.datasource.saledb-doris.username}")
	private String dorisUserName;
    @Value("${spring.datasource.dynamic.datasource.saledb-doris.password}")
	private String dorisPwd;

    @Value("${spring.datasource.dynamic.datasource.twdb.url}")
    private String twUrl;
    @Value("${spring.datasource.dynamic.datasource.twdb.username}")
    private String twUserName;
    @Value("${spring.datasource.dynamic.datasource.twdb.password}")
    private String twPwd;

    @Value("${canal.username}")
	private String canalUsername;
    @Value("${canal.pwd}")
	private String canalPWD;

    private String canalUrl = "http://172.19.0.4:8111/";//canal登录的生产地址
    private HttpCookie httpCookie;

    @Override
    public Boolean checkIaeAndGeaData() {
        //初始化本次请求的token
        this.getCannalPlatformLoginToken();
        if(GeneralTool.isEmpty(httpCookie))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("login_failure"));
        }
        //获取任务列表，返回jobList
        List<Integer> jobList = this.getCannalJobList("iae");
        log.info("=====>待执行任务列表1："+GeneralTool.toJson(jobList));

        if(GeneralTool.isEmpty(jobList) || jobList.size()<1)
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("get_job_list_fail"));
        }
        //根据返回的调度任务列表，返回指定任务相关的库及表
        Map<String,List<String>> tableMap = this.getCannalJobTables(jobList);
        log.info("=====>待匹配的库表明细1："+GeneralTool.toJson(tableMap));

        //将库表集合传递到对比方法中（区分IAE和GEA、GEA的mysql和Doris两个方法）
        if(this.iaeAndGeaCheck(tableMap))
        {
            log.info("=====>匹配1完成。");
        }
        return true;
    }

    @Override
    public Boolean checkGeaAndTWData() {
        //初始化本次请求的token
        this.getCannalPlatformLoginToken();
        if(GeneralTool.isEmpty(httpCookie))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("login_failure"));
        }
        //获取任务列表，返回jobList
        List<Integer> jobList = this.getCannalJobList("tw");
        log.info("=====>待执行任务列表3："+GeneralTool.toJson(jobList));

        if(GeneralTool.isEmpty(jobList) || jobList.size()<1)
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("get_job_list_fail"));
        }
        //根据返回的调度任务列表，返回指定任务相关的库及表
        Map<String,List<String>> tableMap = this.getCannalJobTables(jobList);
        log.info("=====>待匹配的库表明细3："+GeneralTool.toJson(tableMap));

        //将库表集合传递到对比方法中（区分IAE和GEA、GEA的mysql和Doris两个方法）
        if(this.geaAndTWCheck(tableMap))
        {
            log.info("=====>匹配3完成。");
        }
        return true;
    }

    @Override
    public Boolean checkGeaMysqlAndDorisData() {
        //初始化本次请求的token
        this.getCannalPlatformLoginToken();
        if(GeneralTool.isEmpty(httpCookie))
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("login_failure"));
        }
        //获取任务列表，返回jobList
        List<Integer> jobList = this.getCannalJobList("doris");
        log.info("=====>待执行任务列表2："+GeneralTool.toJson(jobList));

        if(GeneralTool.isEmpty(jobList) || jobList.size()<1)
        {
            throw new GetServiceException(LocaleMessageUtils.getMessage("get_job_list_fail"));
        }
        //根据返回的调度任务列表，返回指定任务相关的库及表
        Map<String,List<String>> tableMap = this.getCannalJobTables(jobList);
        log.info("=====>待匹配的库表明细2："+GeneralTool.toJson(tableMap));

        //将库表集合传递到对比方法中（区分IAE和GEA、GEA的mysql和Doris两个方法）
        if(this.mysqlAndDorisCheck(tableMap))
        {
            log.info("=====>匹配2完成。");
        }
        return true;
    }



    /**
     * 传入待匹配的库名及表信息，输出匹配IAE和GEA的结果
     * @param tableMap
     * @return
     */
    private Boolean iaeAndGeaCheck(Map<String,List<String>> tableMap)
    {
        //存储IAE的表值
        Map<String,String> iaeTableMap = this.ExecuteSQL("iae",false, tableMap);
        //存储GEA的表值
        Map<String,String> geaTableMap = this.ExecuteSQL("gea",false,tableMap);

        //对比两边的map值是否一致，不一致的库及表打印出日志参考
        for (String key1 : iaeTableMap.keySet()) {
            String dbNameAndTableName1 = key1;
            String value1 = iaeTableMap.get(dbNameAndTableName1);
            String value2 = geaTableMap.get(dbNameAndTableName1);
            if(!value1.equals(value2))
            {
                log.info("iaeAndGeaCheck=>[{}]:iaeValue[{}],geaValue[{}]",dbNameAndTableName1,value1,value2);
                XxlJobHelper.log("iaeAndGeaCheck=>[{}]:iaeValue[{}],geaValue[{}]",dbNameAndTableName1,value1,value2);
            }
        }
        return true;
    }

    /**
     * 传入待匹配的库名及表信息，输出匹配TW和GEA的结果
     * @param tableMap
     * @return
     */
    private Boolean geaAndTWCheck(Map<String,List<String>> tableMap)
    {
        //存储TW的表值
        Map<String,String> twTableMap = this.ExecuteSQL("tw",false, tableMap);
        //存储GEA的表值
        Map<String,String> geaTableMap = this.ExecuteSQL("gea",false,tableMap);

        //对比两边的map值是否一致，不一致的库及表打印出日志参考
        for (String key1 : twTableMap.keySet()) {
            String dbNameAndTableName1 = key1;
            String value1 = twTableMap.get(dbNameAndTableName1);
            String value2 = geaTableMap.get(dbNameAndTableName1);
            if(!value1.equals(value2))
            {
                log.info("twAndGeaCheck=>[{}]:twValue[{}],geaValue[{}]",dbNameAndTableName1,value1,value2);
                XxlJobHelper.log("twAndGeaCheck=>[{}]:twValue[{}],geaValue[{}]",dbNameAndTableName1,value1,value2);
            }
        }
        return true;
    }

    /**
     * 传入待匹配的库名及表信息，输出匹配GEA的mysql和Doris结果
     * 实现思路：
     * 根据库表查询总数，如总数不一致，则标记为不一致；目前只实现根据总数来校验
     * @param tableMap
     * @return
     */
    private Boolean mysqlAndDorisCheck(Map<String,List<String>> tableMap)
    {
        //存储GEA的表值
        Map<String,String> geaTableMap = this.ExecuteSQL("gea",true,tableMap);
        //存储IAE的表值
        Map<String,String> dorisTableMap = this.ExecuteSQL("doris",true, tableMap);

        //按字母排序
        final List<Map.Entry<String, String>> geaTableList = this.sortMap(geaTableMap);
        final List<Map.Entry<String, String>> dorisTableList = this.sortMap(dorisTableMap);

        //对比两边的map值是否一致，不一致的库及表打印出日志参考
        for (final Map.Entry<String, String> m : geaTableList) {
            for (final Map.Entry<String, String> n : dorisTableList) {
                if(m.getKey().equals(n.getKey()) && !m.getValue().equals(n.getValue()))
                {
                    log.info("mysqlAndDorisCheck=>[{}]:geaValue[{}],dorisValue[{}]",m.getKey(),m.getValue(),n.getValue());
                    XxlJobHelper.log("mysqlAndDorisCheck=>[{}]:geaValue[{}],dorisValue[{}]",m.getKey(),m.getValue(),n.getValue());
                }
            }
        }

//        //对比两边的map值是否一致，不一致的库及表打印出日志参考
//        for (String key1 : geaTableMap.keySet()) {
//            String dbNameAndTableName1 = key1;
//            String value1 = geaTableMap.get(dbNameAndTableName1);
//            String value2 = dorisTableMap.get(dbNameAndTableName1);
//            if(!value1.equals(value2))
//            {
//                log.info("mysqlAndDorisCheck=>[{}]:geaValue[{}],dorisValue[{}]",dbNameAndTableName1,value1,value2);
//            }
//        }
        return true;
    }


    private Map<String,String> ExecuteSQL(String dbType,Boolean isDorisCheck,Map<String,List<String>> tableMap)
    {
        //存储数据库执行返回的表值
        Map<String,String> tableMapResult = new HashMap<>();

        Connection conn = null;
        PreparedStatement preparedStatement = null;
        ResultSet resultSet = null;

        String dbUrl = "";
        String dbUserName = "";
        String dbPwd = "";

        //如为iae，则使用iae的连接参数
        if("iae".equals(dbType))
        {
            dbUrl = iaeUrl;
            dbUserName = iaeUserName;
            dbPwd = iaePwd;
        }else if("gea".equals(dbType))
        {
            dbUrl = geaUrl;
            dbUserName = geaUserName;
            dbPwd = geaPwd;
        }else if("doris".equals(dbType))
        {
            dbUrl = dorisUrl;
            dbUserName = dorisUserName;
            dbPwd = dorisPwd;
        }else if("tw".equals(dbType))
        {
            dbUrl = twUrl;
            dbUserName = twUserName;
            dbPwd = twPwd;
        }

        //IAE加载配置：
        try {
            Class.forName(dirver);
            conn = DriverManager.getConnection(dbUrl, dbUserName, dbPwd);
            log.info("========================>开始执行校验{}库表",dbType);
            for (String key : tableMap.keySet()) {
                String dbName = key;
                List<String> tableNames = tableMap.get(dbName);
                for (String tableName:tableNames)
                {
                    String sql = "checksum table "+dbName+"."+tableName+" EXTENDED;";
                    //如为doris和mysql匹配则取总数即可
                    if(isDorisCheck)
                    {
                        sql = "select count(*) as count from "+dbName+"."+tableName+";";
                    }
                    preparedStatement = conn.prepareStatement(sql);
                    resultSet = preparedStatement.executeQuery();
                    while (resultSet.next()) {
                        if(isDorisCheck)
                        {
                            String count = resultSet.getString("count");
                            String tableMapKey = dbName+"#"+tableName;
                            tableMapResult.put(tableMapKey,count);
                        }else
                        {
                            String checkSum = resultSet.getString("Checksum");
                            String tableMapKey = dbName+"#"+tableName;
                            tableMapResult.put(tableMapKey,checkSum);
                        }
                        Thread.sleep(500);//休息0.5秒
                    }
                }
            }
            log.info("========================>结束执行校验{}库表",dbType);
        }catch (Exception e)
        {
            e.printStackTrace();
        }finally {
            if (preparedStatement != null) {
                try {
                    preparedStatement.close();
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
            }
            if (null != conn) {
                try {
                    conn.close();
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
            }
            if (null != resultSet) {
                try {
                    resultSet.close();
                } catch (SQLException throwables) {
                    throwables.printStackTrace();
                }
            }
        }
        return tableMapResult;
    }







    /**
     * 初始化登录的cookie
     */
    private void getCannalPlatformLoginToken()
    {
        String result = HttpRequest.post(canalUrl+"login")
                .body("{\"email\":\"\",\"password\":\""+canalPWD+"\",\"ifLogin\":false,\"phone\":\"\",\"username\":\"\",\"company\":\"\",\"code\":\"\",\"verifyCode\":\"\",\"account\":\""+canalUsername+"\",\"loginType\":\"LOGIN_PASSWORD\",\"passwordAgain\":\"\",\"inviteCode\":\"\"}")
                .execute().body();
        JSONObject jsonObject = JSONObject.parseObject(result);
        String jwtToken = "";
        if(GeneralTool.isNotEmpty(jsonObject))
        {
            JSONObject jsonData = jsonObject.getJSONObject("data");
            jwtToken = jsonData.getString("token");
            if(GeneralTool.isNotEmpty(jwtToken))
            {
                httpCookie = new HttpCookie("jwt_token",jwtToken);
            }
        }
        log.info("=======>模拟登录返回的token："+jwtToken);
    }



    /**
     * 根据任务类型来获取jobId集合，“doris” or "iae"
     * doris:表示GEA的mysql同步到doris的数据
     * iae：表示IAE和GEA双向同步的数据
     * @param jobType
     * @return
     */
    private List<Integer> getCannalJobList(String jobType)
    {
        List<Integer> iaeJobList = new ArrayList<>();
        List<Integer> dorisJobList = new ArrayList<>();
        List<Integer> twJobList = new ArrayList<>();
        String result = HttpRequest.post(canalUrl+"cloudcanal/console/api/v1/inner/datajob/listbypage")
                .cookie(httpCookie)
                .body("{\"timeRange\":[],\"dataJobName\":null,\"dataJobType\":null,\"desc\":null,\"status\":null,\"type\":null,\"sourceType\":null,\"sinkType\":null,\"sourceName\":null,\"sinkName\":null,\"sourceInstanceId\":0,\"targetInstanceId\":0,\"orderType\":\"GMT_CREATE\",\"pageSize\":20,\"pageNum\":1}")
                .execute().body();
//        log.info("=====>任务列表："+result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        if(GeneralTool.isNotEmpty(jsonObject))
        {
            JSONObject jsonData = jsonObject.getJSONObject("data");
            if(GeneralTool.isNotEmpty(jsonData))
            {
                JSONArray jobVos = jsonData.getJSONArray("jobVos");
                if(GeneralTool.isNotEmpty(jobVos) && jobVos.size()>0)
                {
                    for (int i=0;i<jobVos.size();i++)
                    {
                        JSONObject jsonObject_ =  jobVos.getJSONObject(i);
                        Integer jobId =jsonObject_.getInteger("dataJobId");
                        String dataJobDesc = jsonObject_.getString("dataJobDesc");
                        String fsmActive = jsonObject_.getString("fsmActive");
                        log.info("============================》jobId[{}],dataJobDesc[{}],fsmActive[{}]:",jobId,dataJobDesc,fsmActive);
                        //获取已激活的Job
                        if(GeneralTool.isNotEmpty(jobId))
                        {
                            if(dataJobDesc.contains("Doris"))
                            {
                                dorisJobList.add(jobId);
                            }else if(dataJobDesc.contains("TW"))
                            {
                                twJobList.add(jobId);
                            }else
                            {
                                iaeJobList.add(jobId);
                            }
                        }
                    }
                }
            }
        }
        if("doris".equals(jobType))
        {
            return dorisJobList;
        }else if("tw".equals(jobType))
        {
            return twJobList;
        }
        return iaeJobList;
    }

    /**
     * 根据jobList获取所有需要匹配的库及表信息
     * @param jobList
     * @return
     */
    private Map<String,List<String>> getCannalJobTables(List<Integer> jobList)
    {
        Map<String,List<String>> tableMap = new HashMap<>();
        List<String> jobJsonStrings = new ArrayList<>();
        for (int jobId:jobList)
        {
            String result = HttpRequest.post(canalUrl+"cloudcanal/console/api/v1/inner/datajob/queryjobschemabyid")
                    .cookie(httpCookie)
                    .body("{\"jobId\":"+jobId+",\"srcDsType\":\"MySQL\",\"dstDsType\":\"MySQL\"}")
                    .execute().body();
            jobJsonStrings.add(result);
            try {
                Thread.sleep(500);//休息0.5秒，避免过于频繁
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }

        //解析json
        for(String jobJsonString:jobJsonStrings)
        {
            JSONObject jsonObject = JSONObject.parseObject(jobJsonString);
            if(GeneralTool.isNotEmpty(jsonObject))
            {
                JSONObject jsonData = jsonObject.getJSONObject("data");
                if(GeneralTool.isNotEmpty(jsonData))
                {
                    String dbJson = jsonData.getString("sourceSchema");
                    if(GeneralTool.isNotEmpty(dbJson))
                    {
                        JSONArray dbArrays = JSONObject.parseArray(dbJson);
                        if(GeneralTool.isNotEmpty(dbArrays) && dbArrays.size()>0)
                        {
                            for (int i=0;i<dbArrays.size();i++)
                            {
                                List<String> tableNames = new ArrayList<>();
                                JSONObject jsonObject_ =  dbArrays.getJSONObject(i);
                                String dbName = jsonObject_.getString("db");
                                JSONArray tableArray = jsonObject_.getJSONArray("tables");
                                if(GeneralTool.isNotEmpty(dbName)&& GeneralTool.isNotEmpty(tableArray) && tableArray.size()>0)
                                {
                                    for (int j=0;j<tableArray.size();j++)
                                    {
                                        JSONObject tableObject_ =  tableArray.getJSONObject(j);
                                        String tableName = tableObject_.getString("table");
                                        tableNames.add(tableName);
                                    }
                                }
                                if(GeneralTool.isNotEmpty(tableNames) && tableNames.size()>0)
                                {
                                    tableMap.put(dbName,tableNames);
                                }
                            }
                        }

                    }
                }
            }
        }
        return tableMap;
    }


    /**
     *
     * @Title: sortMap
     * @Description: 对集合内的数据按key的字母顺序做排序
     */
    public List<Map.Entry<String, String>> sortMap(final Map<String, String> map) {
        final List<Map.Entry<String, String>> infos = new ArrayList<Map.Entry<String, String>>(map.entrySet());

        // 重写集合的排序方法：按字母顺序
        Collections.sort(infos, new Comparator<Map.Entry<String, String>>() {
            @Override
            public int compare(final Map.Entry<String, String> o1, final Map.Entry<String, String> o2) {
                return (o1.getKey().toString().compareTo(o2.getKey()));
            }
        });

        return infos;
    }
}
