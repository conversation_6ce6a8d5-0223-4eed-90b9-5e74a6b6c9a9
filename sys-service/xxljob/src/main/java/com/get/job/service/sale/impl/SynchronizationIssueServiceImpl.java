package com.get.job.service.sale.impl;


import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.get.core.tool.utils.GeneralTool;
import com.get.job.dao.dbgea.IssueAplOrderMapper;
import com.get.job.dao.sale.StudentOfferItemMapper;
import com.get.job.service.sale.SynchronizationIssueService;
import com.get.salecenter.entity.AplOldIssueOrder;
import com.get.salecenter.entity.StudentOfferItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author:GSHG
 * @date: 2022-04-22 3:21 PM
 * description:
 */
@Service
public class SynchronizationIssueServiceImpl implements SynchronizationIssueService {

    private static final Logger logger = LoggerFactory.getLogger(SynchronizationIssueServiceImpl.class);
//        @Resource
//        private IssueAplOrderMapper issueAplOrderMapper;
        @Resource
        private StudentOfferItemMapper studentOfferItemMapper;

//    @Override
//    @DSTransactional
//    @Async("defaultThreadPool")
//    public void synchronizationOldIssue() {
//        //查询符合条件的order
//        List<AplOldIssueOrder> aplOldIssueOrders = issueAplOrderMapper.selectByStatus();
//        if(!GeneralTool.isEmpty(aplOldIssueOrders)){
//            aplOldIssueOrders.forEach(aplOldIssueOrder ->{
//                StudentOfferItem studentOfferItem = new StudentOfferItem();
//                studentOfferItem.setFkIssueRpaOrderId(Long.valueOf(aplOldIssueOrder.getOrderId()));
//                studentOfferItem.setRpaFinishTime(aplOldIssueOrder.getRobotStatusTime());
//                studentOfferItem.setRpaRemark(aplOldIssueOrder.getRobotReturnMsg());
//                studentOfferItem.setStatusRpa(aplOldIssueOrder.getRobotStatus());
//                studentOfferItem.setRpaOptTime(aplOldIssueOrder.getOrderCreatetime());
//                //多数据源修改新issue的学生计划
//                studentOfferItemMapper.updateIssueRpaStatus(studentOfferItem);
//                UpdateWrapper updateWrapper = new UpdateWrapper();
//                updateWrapper.eq("order_id", aplOldIssueOrder.getOrderId());
//                updateWrapper.set("order_status","B");
//                AplOldIssueOrder aplOldIssueOrder1 = new AplOldIssueOrder();
//                //多数据源修改旧版issue
//                issueAplOrderMapper.update(aplOldIssueOrder1,updateWrapper);
//            });
//
//        }
//    }
}
