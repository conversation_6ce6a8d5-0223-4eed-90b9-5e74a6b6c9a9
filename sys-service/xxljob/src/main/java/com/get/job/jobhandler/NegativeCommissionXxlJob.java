package com.get.job.jobhandler;

import com.get.job.component.NegativeCommissionHelper;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/7/11 17:10
 */
@Component
public class NegativeCommissionXxlJob {
    @Resource
    private NegativeCommissionHelper negativeCommissionHelper;

    /**
     * 负数佣金定时任务
     *
     * @Date 17:18 2024/7/11
     * <AUTHOR>
     */
    @XxlJob("negativeCommission")
    public void negativeCommission() {
        try {
            XxlJobHelper.log("负数佣金定时任务...");
            negativeCommissionHelper.negativeCommission();
        } catch (Exception e) {
            XxlJobHelper.log("func[{}] e[{}-{}] desc[负数佣金定时器 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
            e.getStackTrace();
        }
    }

}
