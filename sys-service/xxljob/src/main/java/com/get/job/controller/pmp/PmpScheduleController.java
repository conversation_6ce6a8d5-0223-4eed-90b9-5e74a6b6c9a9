package com.get.job.controller.pmp;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.tool.result.ResponseBo;
import com.get.core.tool.result.UpdateResponseBo;
import com.get.job.service.pmp.InstitutionProviderContractService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author:Oliver
 * @Date: 2025/3/28
 * @Version 1.0
 * @apiNote:
 */
@Api(tags = "pmp定时任务管理")
@RestController
@RequestMapping("schedule/pmp")
public class PmpScheduleController {

    @Autowired
    private InstitutionProviderContractService contractService;

    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "合同自动下架")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "定时任务中心/PMP合同自动下架")
    @GetMapping("/unActiveContract")
    public ResponseBo autoUpdateInstitutionCourseData() {
        contractService.unActiveContract();
        return UpdateResponseBo.ok();
    }
}
