package com.get.job.jobhandler;

import com.get.core.redis.cache.GetRedis;
import com.get.job.service.institution.SynchronizationService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * Time: 11:03
 * Date: 2022/3/31
 * Description:
 */
@Component
public class InstitutionCourseXxlJob {

    private static final Logger logger = LoggerFactory.getLogger(InstitutionCourseXxlJob.class);

    @Resource
    private GetRedis getRedis;

    @Resource
    private SynchronizationService synchronizationService;

    /**
     * RPA课程同步定时任务
     *
     * @throws Exception
     */
    @XxlJob("autoUpdateInstitutionCourseData")
    public void autoUpdateInstitutionCourseData() {
        try {
            XxlJobHelper.log("RPA课程同步定时任务开始...");
            //如果CPP课程还在同步 则不会重新刷新RPA课程同步定时任务
            synchronizationService.autoUpdateInstitutionCourseData();
//            if (redisClient.getRedisLock(CacheKeyConstants.AUTO_UPDATE_STUDENT_OFFER_ITEM_LOCK_KEY, CacheKeyConstants.REDIS_KEY_EXPIRE_ONE_DAY * 5)) {
//                synchronizationService.autoUpdateInstitutionCourseData();
//            }
        } catch (Exception e) {
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[RPA课程同步定时任务 error]");
            logger.error("func[{}] e[{}-{}] desc[RPA课程同步定时任务 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        } finally {
            //getRedis.decr(CacheKeyConstants.AUTO_UPDATE_STUDENT_OFFER_ITEM_LOCK_KEY);
        }
    }

    public void init() {
        logger.info("init");
    }

    public void destroy() {
        logger.info("destory");
    }
}
