<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.dbgea.IssueAplOrderMapper">
    <resultMap id="BaseResultMap" type="com.get.salecenter.entity.AplOldIssueOrder">
        <id column="order_id" jdbcType="INTEGER" property="orderId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="stu_id" jdbcType="INTEGER" property="stuId"/>
        <result column="cdet_id" jdbcType="INTEGER" property="cdetId"/>
        <result column="stu_source" jdbcType="VARCHAR" property="stuSource"/>
        <result column="stu_surname" jdbcType="VARCHAR" property="stuSurname"/>
        <result column="stu_first_name" jdbcType="VARCHAR" property="stuFirstName"/>
        <result column="email" jdbcType="VARCHAR" property="email"/>
        <result column="order_createtime" jdbcType="TIMESTAMP" property="orderCreatetime"/>
        <result column="cdet_schl_ctry" jdbcType="VARCHAR" property="cdetSchlCtry"/>
        <result column="cdet_schl_id" jdbcType="INTEGER" property="cdetSchlId"/>
        <result column="cdet_crse_name" jdbcType="VARCHAR" property="cdetCrseName"/>
        <result column="cdet_crse_code" jdbcType="VARCHAR" property="cdetCrseCode"/>
        <result column="cdet_star_date" jdbcType="DATE" property="cdetStarDate"/>
        <result column="cdet_sch_login_name" jdbcType="VARCHAR" property="cdetSchLoginName"/>
        <result column="cdet_sch_login_pw" jdbcType="VARCHAR" property="cdetSchLoginPw"/>
        <result column="order_status" jdbcType="VARCHAR" property="orderStatus"/>
        <result column="robot_status" jdbcType="VARCHAR" property="robotStatus"/>
        <result column="robot_status_time" jdbcType="TIMESTAMP" property="robotStatusTime"/>
        <result column="error_bp_no" jdbcType="INTEGER" property="errorBpNo"/>
        <result column="creater_id" jdbcType="INTEGER" property="createrId"/>
        <result column="people_msg" jdbcType="VARCHAR" property="peopleMsg"/>
        <result column="order_create_msg" jdbcType="LONGVARCHAR" property="orderCreateMsg"/>
        <result column="robot_return_msg" jdbcType="LONGVARCHAR" property="robotReturnMsg"/>
        <result column="Run_Duration" jdbcType="LONGVARCHAR" property="runDuration"/>
    </resultMap>
    <!--TODO 注释sql-->
<!--    <select id="selectByStatus" resultType="com.get.salecenter.entity.AplOldIssueOrder">-->

<!--        SELECT-->
<!--        `ao`.`robot_status` AS `robot_status`,-->
<!--        `ao`.`order_id` AS `order_id`,-->
<!--        `ao`.`cdet_id` AS `cdet_id`,-->
<!--        `ao`.`robot_status_time` AS `robot_status_time`,-->
<!--        `ao`.`robot_return_msg` AS `robot_return_msg`,-->
<!--        'B' AS `order_status`,-->
<!--        `ao`.`cdet_sch_login_name` AS `cdet_sch_login_name`,-->
<!--        `ao`.`cdet_sch_login_pw` AS `cdet_sch_login_pw`,-->
<!--        `ao`.`error_bp_no` AS `error_bp_no`,-->
<!--        `ao`.`Run_Duration` AS `Run_Duration`,-->
<!--        `ao`.order_createtime as orderCreatetime-->
<!--        FROM-->
<!--        `apl_order` `ao`-->
<!--        WHERE-->
<!--        (-->
<!--        (-->
<!--        ifnull(`ao`.`order_status`, '') &lt;&gt; 'B'-->
<!--        )-->
<!--        AND (-->
<!--        ifnull(`ao`.`robot_status`, '') NOT IN ('N', 'T')-->
<!--        )-->
<!--        )-->
<!--        ORDER BY-->
<!--        `ao`.`order_id` DESC-->
<!--    </select>-->


</mapper>