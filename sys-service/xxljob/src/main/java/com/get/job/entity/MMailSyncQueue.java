package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.time.LocalDateTime;

@Data
@TableName("m_mail_sync_queue")
public class MMailSyncQueue {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "唯一主键")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "用户邮箱账号id")
    @Column(name = "fk_mail_account_id")
    private Long fkMailAccountId;

    @ApiModelProperty(value = "用户邮件Id")
    @Column(name = "fk_mail_id")
    private Long fkMailId;

    @ApiModelProperty(value = "邮件的Id（邮件服务器自带邮件Id）")
    @Column(name = "mail_id")
    private String mailId;

    @ApiModelProperty(value = "操作类型：1是否已读，2是否星标，3移动邮件")
    @Column(name = "operation_type")
    private Integer operationType;

    @ApiModelProperty(value = "操作数值")
    @Column(name = "operation_value")
    private String operationValue;

    @ApiModelProperty(value = "是否已经同步，0否/1是")
    @Column(name = "is_sync")
    private boolean isSync;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "gmt_create")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "创建用户(登录账号)")
    @Column(name = "gmt_create_user")
    private String gmtCreateUser;

    @ApiModelProperty(value = "修改时间")
    @Column(name = "gmt_modified")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "修改用户(登录账号)")
    @Column(name = "gmt_modified_user")
    private String gmtModifiedUser;
}
