package com.get.job.dao.institution;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.AreaCity;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("institutiondb")
public interface AreaCityMapper extends BaseMapper<AreaCity> {
    int insert(AreaCity record);

    int insertSelective(AreaCity record);

    int updateByPrimaryKeySelective(AreaCity record);

    int updateByPrimaryKey(AreaCity record);
}