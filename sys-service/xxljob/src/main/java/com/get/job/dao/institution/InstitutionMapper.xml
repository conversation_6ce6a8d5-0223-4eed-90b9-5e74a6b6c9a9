<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.institution.InstitutionMapper">
    <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.Institution">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_institution_type_id" jdbcType="BIGINT" property="fkInstitutionTypeId"/>
        <result column="fk_area_country_id" jdbcType="BIGINT" property="fkAreaCountryId"/>
        <result column="fk_area_state_id" jdbcType="BIGINT" property="fkAreaStateId"/>
        <result column="fk_area_city_id" jdbcType="BIGINT" property="fkAreaCityId"/>
        <result column="fk_currency_type_num" jdbcType="VARCHAR" property="fkCurrencyTypeNum"/>
        <result column="num" jdbcType="VARCHAR" property="num"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="name_chn" jdbcType="VARCHAR" property="nameChn"/>
        <result column="short_name" jdbcType="VARCHAR" property="shortName"/>
        <result column="short_name_chn" jdbcType="VARCHAR" property="shortNameChn"/>
        <result column="nature" jdbcType="VARCHAR" property="nature"/>
        <result column="established_date" jdbcType="VARCHAR" property="establishedDate"/>
        <result column="apply_date" jdbcType="VARCHAR" property="applyDate"/>
        <result column="apply_fee_min" jdbcType="DECIMAL" property="applyFeeMin"/>
        <result column="apply_fee_max" jdbcType="DECIMAL" property="applyFeeMax"/>
        <result column="website" jdbcType="VARCHAR" property="website"/>
        <result column="zip_code" jdbcType="VARCHAR" property="zipCode"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="is_kpi" jdbcType="BIT" property="isKpi"/>
        <result column="kpi_level" jdbcType="INTEGER" property="kpiLevel"/>
        <result column="is_active" jdbcType="BIT" property="isActive"/>
        <result column="public_level" jdbcType="VARCHAR" property="publicLevel"/>
        <result column="data_level" jdbcType="INTEGER" property="dataLevel"/>
        <result column="id_gea" jdbcType="VARCHAR" property="idGea"/>
        <result column="id_iae" jdbcType="VARCHAR" property="idIae"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.get.institutioncenter.entity.Institution">
        <result column="detail" jdbcType="LONGVARCHAR" property="detail"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , fk_institution_type_id, fk_area_country_id, fk_area_state_id, fk_area_city_id,
    fk_currency_type_num, num, name, name_chn, short_name, short_name_chn, nature, established_date, 
    apply_date, apply_fee_min, apply_fee_max, website, zip_code, address, is_kpi, kpi_level, 
    is_active, public_level, data_level, id_gea, id_iae, gmt_create, gmt_create_user, 
    gmt_modified, gmt_modified_user
    </sql>
    <sql id="Blob_Column_List">
        detail
    </sql>
    <insert id="insert" parameterType="com.get.institutioncenter.entity.Institution">
        insert into m_institution (id, fk_institution_type_id, fk_area_country_id,
                                   fk_area_state_id, fk_area_city_id, fk_currency_type_num,
                                   num, name, name_chn,
                                   short_name, short_name_chn, nature,
                                   established_date, apply_date, apply_fee_min,
                                   apply_fee_max, website, zip_code,
                                   address, is_kpi, kpi_level,
                                   is_active, public_level, data_level,
                                   id_gea, id_iae, gmt_create,
                                   gmt_create_user, gmt_modified, gmt_modified_user,
                                   detail)
        values (#{id,jdbcType=BIGINT}, #{fkInstitutionTypeId,jdbcType=BIGINT}, #{fkAreaCountryId,jdbcType=BIGINT},
                #{fkAreaStateId,jdbcType=BIGINT}, #{fkAreaCityId,jdbcType=BIGINT},
                #{fkCurrencyTypeNum,jdbcType=VARCHAR},
                #{num,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{nameChn,jdbcType=VARCHAR},
                #{shortName,jdbcType=VARCHAR}, #{shortNameChn,jdbcType=VARCHAR}, #{nature,jdbcType=VARCHAR},
                #{establishedDate,jdbcType=VARCHAR}, #{applyDate,jdbcType=VARCHAR}, #{applyFeeMin,jdbcType=DECIMAL},
                #{applyFeeMax,jdbcType=DECIMAL}, #{website,jdbcType=VARCHAR}, #{zipCode,jdbcType=VARCHAR},
                #{address,jdbcType=VARCHAR}, #{isKpi,jdbcType=BIT}, #{kpiLevel,jdbcType=INTEGER},
                #{isActive,jdbcType=BIT}, #{publicLevel,jdbcType=VARCHAR}, #{dataLevel,jdbcType=INTEGER},
                #{idGea,jdbcType=VARCHAR}, #{idIae,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
                #{gmtModifiedUser,jdbcType=VARCHAR},
                #{detail,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.Institution">
        insert into m_institution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkInstitutionTypeId != null">
                fk_institution_type_id,
            </if>
            <if test="fkAreaCountryId != null">
                fk_area_country_id,
            </if>
            <if test="fkAreaStateId != null">
                fk_area_state_id,
            </if>
            <if test="fkAreaCityId != null">
                fk_area_city_id,
            </if>
            <if test="fkCurrencyTypeNum != null">
                fk_currency_type_num,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="nameChn != null">
                name_chn,
            </if>
            <if test="shortName != null">
                short_name,
            </if>
            <if test="shortNameChn != null">
                short_name_chn,
            </if>
            <if test="nature != null">
                nature,
            </if>
            <if test="establishedDate != null">
                established_date,
            </if>
            <if test="applyDate != null">
                apply_date,
            </if>
            <if test="applyFeeMin != null">
                apply_fee_min,
            </if>
            <if test="applyFeeMax != null">
                apply_fee_max,
            </if>
            <if test="website != null">
                website,
            </if>
            <if test="zipCode != null">
                zip_code,
            </if>
            <if test="address != null">
                address,
            </if>
            <if test="isKpi != null">
                is_kpi,
            </if>
            <if test="kpiLevel != null">
                kpi_level,
            </if>
            <if test="isActive != null">
                is_active,
            </if>
            <if test="publicLevel != null">
                public_level,
            </if>
            <if test="dataLevel != null">
                data_level,
            </if>
            <if test="idGea != null">
                id_gea,
            </if>
            <if test="idIae != null">
                id_iae,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
            <if test="detail != null">
                detail,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionTypeId != null">
                #{fkInstitutionTypeId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCountryId != null">
                #{fkAreaCountryId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaStateId != null">
                #{fkAreaStateId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCityId != null">
                #{fkAreaCityId,jdbcType=BIGINT},
            </if>
            <if test="fkCurrencyTypeNum != null">
                #{fkCurrencyTypeNum,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="shortNameChn != null">
                #{shortNameChn,jdbcType=VARCHAR},
            </if>
            <if test="nature != null">
                #{nature,jdbcType=VARCHAR},
            </if>
            <if test="establishedDate != null">
                #{establishedDate,jdbcType=VARCHAR},
            </if>
            <if test="applyDate != null">
                #{applyDate,jdbcType=VARCHAR},
            </if>
            <if test="applyFeeMin != null">
                #{applyFeeMin,jdbcType=DECIMAL},
            </if>
            <if test="applyFeeMax != null">
                #{applyFeeMax,jdbcType=DECIMAL},
            </if>
            <if test="website != null">
                #{website,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                #{address,jdbcType=VARCHAR},
            </if>
            <if test="isKpi != null">
                #{isKpi,jdbcType=BIT},
            </if>
            <if test="kpiLevel != null">
                #{kpiLevel,jdbcType=INTEGER},
            </if>
            <if test="isActive != null">
                #{isActive,jdbcType=BIT},
            </if>
            <if test="publicLevel != null">
                #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="dataLevel != null">
                #{dataLevel,jdbcType=INTEGER},
            </if>
            <if test="idGea != null">
                #{idGea,jdbcType=VARCHAR},
            </if>
            <if test="idIae != null">
                #{idIae,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="detail != null">
                #{detail,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.get.institutioncenter.entity.Institution">
        update m_institution
        <set>
            <if test="fkInstitutionTypeId != null">
                fk_institution_type_id = #{fkInstitutionTypeId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCountryId != null">
                fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaStateId != null">
                fk_area_state_id = #{fkAreaStateId,jdbcType=BIGINT},
            </if>
            <if test="fkAreaCityId != null">
                fk_area_city_id = #{fkAreaCityId,jdbcType=BIGINT},
            </if>
            <if test="fkCurrencyTypeNum != null">
                fk_currency_type_num = #{fkCurrencyTypeNum,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                name_chn = #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="shortName != null">
                short_name = #{shortName,jdbcType=VARCHAR},
            </if>
            <if test="shortNameChn != null">
                short_name_chn = #{shortNameChn,jdbcType=VARCHAR},
            </if>
            <if test="nature != null">
                nature = #{nature,jdbcType=VARCHAR},
            </if>
            <if test="establishedDate != null">
                established_date = #{establishedDate,jdbcType=VARCHAR},
            </if>
            <if test="applyDate != null">
                apply_date = #{applyDate,jdbcType=VARCHAR},
            </if>
            <if test="applyFeeMin != null">
                apply_fee_min = #{applyFeeMin,jdbcType=DECIMAL},
            </if>
            <if test="applyFeeMax != null">
                apply_fee_max = #{applyFeeMax,jdbcType=DECIMAL},
            </if>
            <if test="website != null">
                website = #{website,jdbcType=VARCHAR},
            </if>
            <if test="zipCode != null">
                zip_code = #{zipCode,jdbcType=VARCHAR},
            </if>
            <if test="address != null">
                address = #{address,jdbcType=VARCHAR},
            </if>
            <if test="isKpi != null">
                is_kpi = #{isKpi,jdbcType=BIT},
            </if>
            <if test="kpiLevel != null">
                kpi_level = #{kpiLevel,jdbcType=INTEGER},
            </if>
            <if test="isActive != null">
                is_active = #{isActive,jdbcType=BIT},
            </if>
            <if test="publicLevel != null">
                public_level = #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="dataLevel != null">
                data_level = #{dataLevel,jdbcType=INTEGER},
            </if>
            <if test="idGea != null">
                id_gea = #{idGea,jdbcType=VARCHAR},
            </if>
            <if test="idIae != null">
                id_iae = #{idIae,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="detail != null">
                detail = #{detail,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.get.institutioncenter.entity.Institution">
        update m_institution
        set fk_institution_type_id = #{fkInstitutionTypeId,jdbcType=BIGINT},
            fk_area_country_id     = #{fkAreaCountryId,jdbcType=BIGINT},
            fk_area_state_id       = #{fkAreaStateId,jdbcType=BIGINT},
            fk_area_city_id        = #{fkAreaCityId,jdbcType=BIGINT},
            fk_currency_type_num   = #{fkCurrencyTypeNum,jdbcType=VARCHAR},
            num                    = #{num,jdbcType=VARCHAR},
            name                   = #{name,jdbcType=VARCHAR},
            name_chn               = #{nameChn,jdbcType=VARCHAR},
            short_name             = #{shortName,jdbcType=VARCHAR},
            short_name_chn         = #{shortNameChn,jdbcType=VARCHAR},
            nature                 = #{nature,jdbcType=VARCHAR},
            established_date       = #{establishedDate,jdbcType=VARCHAR},
            apply_date             = #{applyDate,jdbcType=VARCHAR},
            apply_fee_min          = #{applyFeeMin,jdbcType=DECIMAL},
            apply_fee_max          = #{applyFeeMax,jdbcType=DECIMAL},
            website                = #{website,jdbcType=VARCHAR},
            zip_code               = #{zipCode,jdbcType=VARCHAR},
            address                = #{address,jdbcType=VARCHAR},
            is_kpi                 = #{isKpi,jdbcType=BIT},
            kpi_level              = #{kpiLevel,jdbcType=INTEGER},
            is_active              = #{isActive,jdbcType=BIT},
            public_level           = #{publicLevel,jdbcType=VARCHAR},
            data_level             = #{dataLevel,jdbcType=INTEGER},
            id_gea                 = #{idGea,jdbcType=VARCHAR},
            id_iae                 = #{idIae,jdbcType=VARCHAR},
            gmt_create             = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user        = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified           = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user      = #{gmtModifiedUser,jdbcType=VARCHAR},
            detail                 = #{detail,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.institutioncenter.entity.Institution">
        update m_institution
        set fk_institution_type_id = #{fkInstitutionTypeId,jdbcType=BIGINT},
            fk_area_country_id     = #{fkAreaCountryId,jdbcType=BIGINT},
            fk_area_state_id       = #{fkAreaStateId,jdbcType=BIGINT},
            fk_area_city_id        = #{fkAreaCityId,jdbcType=BIGINT},
            fk_currency_type_num   = #{fkCurrencyTypeNum,jdbcType=VARCHAR},
            num                    = #{num,jdbcType=VARCHAR},
            name                   = #{name,jdbcType=VARCHAR},
            name_chn               = #{nameChn,jdbcType=VARCHAR},
            short_name             = #{shortName,jdbcType=VARCHAR},
            short_name_chn         = #{shortNameChn,jdbcType=VARCHAR},
            nature                 = #{nature,jdbcType=VARCHAR},
            established_date       = #{establishedDate,jdbcType=VARCHAR},
            apply_date             = #{applyDate,jdbcType=VARCHAR},
            apply_fee_min          = #{applyFeeMin,jdbcType=DECIMAL},
            apply_fee_max          = #{applyFeeMax,jdbcType=DECIMAL},
            website                = #{website,jdbcType=VARCHAR},
            zip_code               = #{zipCode,jdbcType=VARCHAR},
            address                = #{address,jdbcType=VARCHAR},
            is_kpi                 = #{isKpi,jdbcType=BIT},
            kpi_level              = #{kpiLevel,jdbcType=INTEGER},
            is_active              = #{isActive,jdbcType=BIT},
            public_level           = #{publicLevel,jdbcType=VARCHAR},
            data_level             = #{dataLevel,jdbcType=INTEGER},
            id_gea                 = #{idGea,jdbcType=VARCHAR},
            id_iae                 = #{idIae,jdbcType=VARCHAR},
            gmt_create             = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user        = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified           = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user      = #{gmtModifiedUser,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <select id="selectInstitutionByNameChnOrNameEng" resultType="com.get.institutioncenter.entity.Institution">
        SELECT
            *
        FROM
            `m_institution`
        WHERE 1=0
        <if test="chineseName != null and chineseName != ''">
            or name_chn =  #{chineseName}
        </if>
        <if test="englishName != null and englishName != ''">
            or LOWER(REPLACE(name," ","")) = #{englishName}
            or LOWER(REPLACE(name," ","")) = replace(#{englishName}, ',', '')
            or LOWER(REPLACE(name," ","")) = concat(replace(#{englishName}, 'universityof', '') , 'university')
            or LOWER(REPLACE(name," ","")) = concat(replace(replace(#{englishName}, ',', ''), 'universityof', ''),'university')
        </if>
        <if test="fkInstitutionNameEducation != null and fkInstitutionNameEducation != ''">
            or name = #{fkInstitutionNameEducation}
            or name_chn = #{fkInstitutionNameEducation}
        </if>
        limit 1
    </select>


     <select id="getProvidersWithContractsExpiringInDays" resultType="com.get.institutioncenter.vo.InstitutionProviderContractReminderVo">
         SELECT
             mip.id,
             CASE
                 WHEN IFNULL(mip.name_chn,'')='' THEN mip.name
                 ELSE CONCAT(mip.name,'（',mip.name_chn,'）')
                 END AS fullName,
             mipc.id AS contractId,
             mipc.contract_title AS contractName,
             mipc.start_time AS startTime,
             mipc.end_time AS endTime,
             (
                 SELECT mipcpa.fk_staff_id
                 FROM ais_pmp2_center.m_institution_provider_commission_plan_approval mipcpa
                 WHERE mipcpa.id = (
                     SELECT MAX(approval.id)
                     FROM ais_pmp2_center.m_institution_provider_commission_plan_approval approval
                     WHERE (
                                 approval.fk_institution_provider_commission_plan_id IN (
                                 SELECT plan.id
                                 FROM ais_pmp2_center.m_institution_provider_commission_plan plan
                                 WHERE plan.fk_institution_provider_contract_id = mipc.id
                             )
                             OR EXISTS (
                                         SELECT 1
                                         FROM ais_pmp2_center.m_institution_provider_commission_plan plan
                                         WHERE plan.fk_institution_provider_contract_id = mipc.id
                                           AND FIND_IN_SET(plan.id, approval.fk_institution_provider_commission_plan_ids) > 0
                                     )
                         )
                       AND approval.fk_staff_id IS NOT NULL
                 )
                 LIMIT 1
             ) AS fkStaffId,
    (
        SELECT ms.id
        FROM ais_permission_center.m_staff ms
        WHERE ms.login_id = mipc.gmt_create_user
        LIMIT 1
    ) AS gmtCreateUserId
         FROM
             ais_institution_center.m_institution_provider mip
             JOIN
             ais_pmp2_center.m_institution_provider_contract mipc ON mip.id = mipc.fk_institution_provider_id
         WHERE
             DATE(mipc.end_time) = DATE_ADD(CURDATE(), INTERVAL #{day} DAY)
           AND mipc.is_active = 1
           AND mip.contract_status = 1
           AND mipc.is_timeless = 0
    </select>

</mapper>