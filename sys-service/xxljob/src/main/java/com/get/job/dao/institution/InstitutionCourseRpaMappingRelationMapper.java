package com.get.job.dao.institution;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionCourseRpaMappingRelation;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("institutiondb")
public interface InstitutionCourseRpaMappingRelationMapper extends BaseMapper<InstitutionCourseRpaMappingRelation> {
    int insert(InstitutionCourseRpaMappingRelation record);

    int insertSelective(InstitutionCourseRpaMappingRelation record);

    int updateByPrimaryKeySelective(InstitutionCourseRpaMappingRelation record);

    int updateByPrimaryKey(InstitutionCourseRpaMappingRelation record);
}