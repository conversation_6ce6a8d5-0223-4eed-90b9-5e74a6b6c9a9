<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.iaecrm.MSysAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.get.job.entity.MSysAttachment">
    <id column="AttachmentID" jdbcType="BIGINT" property="AttachmentID"/>
  </resultMap>

  <!--TODO 注释sql-->
  <select id="getHtiNews" resultType="com.get.job.dto.CrmNewsDto">
    SELECT
      MIns_News.NewsID as news_id,
      MIns_News.NewsClassID as new_class_id,
      MIns_NewsClass.ClassType as class_type,
      MIns_NewsClass.ClassName as class_name,
      MIns_News.NewsTitle as news_title,
      MIns_News.NewsContent as news_content,
      MIns_News.InstitutionCountryID as institution_country_id,
      MSys_InstitutionCountry.InstitutionCountryNum as institution_country_num,
      MSys_InstitutionCountry.InstitutionCountryName as institution_country_name,
      MSys_InstitutionCountry.InstitutionCountryNameEng as institution_country_name_eng,
      MIns_News.SendEmailTime as send_email_time,
      MIns_News.CrtUserID as crt_user_id,
      MIns_News.CrtDate as crt_date
    FROM
        MIns_News
        LEFT JOIN MIns_NewsClass ON MIns_News.NewsClassID = MIns_NewsClass.NewsClassID
        LEFT JOIN MSys_InstitutionCountry ON MIns_News.InstitutionCountryID = MSys_InstitutionCountry.InstitutionCountryID
    WHERE
        1 = 1
    ORDER BY MIns_News.NewsID
  </select>
</mapper>