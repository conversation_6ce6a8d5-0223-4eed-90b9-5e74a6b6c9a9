package com.get.job.dao.institution;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionRpaMappingRelation;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("institutiondb")
public interface InstitutionRpaMappingRelationMapper extends BaseMapper<InstitutionRpaMappingRelation> {
    int insert(InstitutionRpaMappingRelation record);

    int insertSelective(InstitutionRpaMappingRelation record);

    int updateByPrimaryKeySelective(InstitutionRpaMappingRelation record);

    int updateByPrimaryKey(InstitutionRpaMappingRelation record);
}