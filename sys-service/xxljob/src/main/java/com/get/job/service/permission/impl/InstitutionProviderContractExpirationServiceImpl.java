package com.get.job.service.permission.impl;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.vo.InstitutionProviderContractReminderVo;
import com.get.job.dao.institution.InstitutionMapper;

import com.get.job.dao.permission.StaffMapper;
import com.get.job.jobhandler.InstitutionCourseXxlJob;
import com.get.job.service.permission.InstitutionProviderContractExpirationService;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static com.get.core.tool.utils.DateUtil.now;

@Service
public class InstitutionProviderContractExpirationServiceImpl implements InstitutionProviderContractExpirationService {

    private static final Logger logger = LoggerFactory.getLogger(InstitutionProviderContractExpirationServiceImpl.class);

    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private InstitutionMapper institutionMapper;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private StaffMapper staffMapper;
    /**
     * 发送学校提供商合同到期提醒
     * @return
     */
    @Override
    public boolean sendInstitutionProviderContractExpirationReminder() {
        //获取配置信息
        String value1 = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.REMINDER_EMAIL_PROVIDER_CONTRACT_EXPIRATION.key).getData();
        //从配置信息获取收件人id
        List<Long> staffIds = new ArrayList<>();
        //从配置信息获取提醒天数
        List<Integer> days = new ArrayList<>();
        if(GeneralTool.isNotEmpty(value1)){
            cn.hutool.json.JSONObject configJSON = JSONUtil.parseObj(value1);
            cn.hutool.json.JSONArray daysArray = configJSON.getJSONArray("Days");
            JSONArray staffIdsArray = configJSON.getJSONArray("Notifier");
            if (daysArray != null) {
                for (int i = 0; i < daysArray.size(); i++) {
                    days.add(daysArray.getInt(i)); // 添加每个整数值到daysList
                }
            }

            // 同样地，处理staffIdsArray并将数据添加到staffIds列表中
            if (staffIdsArray != null) {
                for (int i = 0; i < staffIdsArray.size(); i++) {
                    staffIds.add(staffIdsArray.getLong(i)); // 假设Notifier包含的是Long类型的数据
                }
            }

        }
        List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Set<Long> staffIdSet = staffIds.stream().collect(Collectors.toSet());
        String versionValue2 = "zh";
            if(GeneralTool.isNotEmpty(days)){
                List<EmailSenderQueue> emailSenderQueueList = new ArrayList<>();
                for (Integer i :days){//提前提醒天数

                    //获取到期合同的学校提供商
                    List<InstitutionProviderContractReminderVo> schoolProviders = institutionMapper.getProvidersWithContractsExpiringInDays(i);

                    if(GeneralTool.isNotEmpty(schoolProviders)){ //到期合同
                        for (InstitutionProviderContractReminderVo reminderVo:schoolProviders){
                            EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                            //获取创建人
                            Long creatorEmails = reminderVo.getGmtCreateUserId();
                            staffIdSet.add(creatorEmails);
                            //获取审批人
                            Long fkStaffId = reminderVo.getFkStaffId();
                            if(GeneralTool.isNotEmpty(fkStaffId)){
                                staffIdSet.add(fkStaffId);
                            }
                            //获取审批人直属上司
                            Long supervisors = permissionCenterClient.getStaffSupervisorIdByStaffId(fkStaffId).getData();
                            if(GeneralTool.isNotEmpty(supervisors)){
                                staffIdSet.add(supervisors);
                            }
                            String title1 = "";
                            if(!versionValue2.equals("en")){
                                title1 = "【合同到期提醒】"+reminderVo.getFullName()+"("
                                        + sdf.format(reminderVo.getStartTime()) + "至" + sdf.format(reminderVo.getEndTime())
                                        + "，提前" + i + "天提醒)";
                            }else {
                                title1 = "[Contract expiration reminder]"+reminderVo.getFullName()+"("
                                        + sdf.format(reminderVo.getStartTime()) + "TO" + sdf.format(reminderVo.getEndTime())
                                        + ",Remind" + i + "days in advance)";
                            }

                            for(Long staffId : staffIdSet){
                                reminderVo.setCount(i);
                                //合同创建人
                                RemindTaskDto remindTaskDto = new RemindTaskDto();
                                remindTaskDto.setFkStaffId(staffId);
                                remindTaskDto.setFkRemindEventTypeKey(EmailTemplateEnum.PROVIDER_CONTRACT_EXPIRE.getEmailTemplateKey());
                                remindTaskDto.setStartTime(now());
                                remindTaskDto.setTaskBgColor("#3788d8");
                                remindTaskDto.setRemindMethod("1");
                                remindTaskDto.setTaskTitle(title1);
                                remindTaskDto.setStatus(1);
                                remindTaskDto.setFkTableName(TableEnum.INSTITUTION_PROVIDER_CONTRACT.key);
                                remindTaskDto.setFkTableId(reminderVo.getId());
                                remindTaskDtos.add(remindTaskDto);
                                logger.info("学校提供商合同信息==="+reminderVo.toString());
                            }
                        emailSenderQueue.setEmailParameter(String.valueOf(i));
                        emailSenderQueue.setFkDbName(ProjectKeyEnum.INSTITUTION_CENTER.key);
                        emailSenderQueue.setFkTableName(TableEnum.INSTITUTION_PROVIDER_CONTRACT.key);
                        emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.PROVIDER_CONTRACT_EXPIRE.getEmailTemplateKey());
                        emailSenderQueue.setOperationTime(now());
                        emailSenderQueue.setFkTableId(reminderVo.getContractId());
                        emailSenderQueueList.add(emailSenderQueue);
                        }
                    }

                }
                if(GeneralTool.isNotEmpty(emailSenderQueueList)){
                    reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
                }
            }

        if(GeneralTool.isNotEmpty(remindTaskDtos)){
            reminderCenterClient.batchAddTask(remindTaskDtos);
        }

        return true;
    }
}
