<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.institution.InstitutionCourseMapper">
    <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionCourse">
        insert into m_institution_course (id, fk_institution_id, code,
                                          num, name, name_chn,
                                          start_month, start_date_note, apply_month,
                                          apply_date_note, fee, fee_max,
                                          fee_note, fee_cny, duration_year,
                                          duration_note, core_course, entry_standards,
                                          occupation_development, remark, is_active,
                                          public_level, data_level, view_order,
                                          id_gea, id_iae, gmt_create,
                                          gmt_create_user, gmt_modified, gmt_modified_user,
                                          introduction)
        values (#{id,jdbcType=BIGINT}, #{fkInstitutionId,jdbcType=BIGINT}, #{code,jdbcType=VARCHAR},
                #{num,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{nameChn,jdbcType=VARCHAR},
                #{startMonth,jdbcType=VARCHAR}, #{startDateNote,jdbcType=VARCHAR}, #{applyMonth,jdbcType=VARCHAR},
                #{applyDateNote,jdbcType=VARCHAR}, #{fee,jdbcType=DECIMAL}, #{feeMax,jdbcType=DECIMAL},
                #{feeNote,jdbcType=VARCHAR}, #{feeCny,jdbcType=DECIMAL}, #{durationYear,jdbcType=DECIMAL},
                #{durationNote,jdbcType=VARCHAR}, #{coreCourse,jdbcType=VARCHAR}, #{entryStandards,jdbcType=VARCHAR},
                #{occupationDevelopment,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isActive,jdbcType=BIT},
                #{publicLevel,jdbcType=VARCHAR}, #{dataLevel,jdbcType=INTEGER}, #{viewOrder,jdbcType=INTEGER},
                #{idGea,jdbcType=VARCHAR}, #{idIae,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP},
                #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP},
                #{gmtModifiedUser,jdbcType=VARCHAR},
                #{introduction,jdbcType=LONGVARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionCourse">
        insert into m_institution_course
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkInstitutionId != null">
                fk_institution_id,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="nameChn != null">
                name_chn,
            </if>
            <if test="startMonth != null">
                start_month,
            </if>
            <if test="startDateNote != null">
                start_date_note,
            </if>
            <if test="applyMonth != null">
                apply_month,
            </if>
            <if test="applyDateNote != null">
                apply_date_note,
            </if>
            <if test="fee != null">
                fee,
            </if>
            <if test="feeMax != null">
                fee_max,
            </if>
            <if test="feeNote != null">
                fee_note,
            </if>
            <if test="feeCny != null">
                fee_cny,
            </if>
            <if test="durationYear != null">
                duration_year,
            </if>
            <if test="durationNote != null">
                duration_note,
            </if>
            <if test="coreCourse != null">
                core_course,
            </if>
            <if test="entryStandards != null">
                entry_standards,
            </if>
            <if test="occupationDevelopment != null">
                occupation_development,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="isActive != null">
                is_active,
            </if>
            <if test="publicLevel != null">
                public_level,
            </if>
            <if test="dataLevel != null">
                data_level,
            </if>
            <if test="viewOrder != null">
                view_order,
            </if>
            <if test="idGea != null">
                id_gea,
            </if>
            <if test="idIae != null">
                id_iae,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
            <if test="introduction != null">
                introduction,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkInstitutionId != null">
                #{fkInstitutionId,jdbcType=BIGINT},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="startMonth != null">
                #{startMonth,jdbcType=VARCHAR},
            </if>
            <if test="startDateNote != null">
                #{startDateNote,jdbcType=VARCHAR},
            </if>
            <if test="applyMonth != null">
                #{applyMonth,jdbcType=VARCHAR},
            </if>
            <if test="applyDateNote != null">
                #{applyDateNote,jdbcType=VARCHAR},
            </if>
            <if test="fee != null">
                #{fee,jdbcType=DECIMAL},
            </if>
            <if test="feeMax != null">
                #{feeMax,jdbcType=DECIMAL},
            </if>
            <if test="feeNote != null">
                #{feeNote,jdbcType=VARCHAR},
            </if>
            <if test="feeCny != null">
                #{feeCny,jdbcType=DECIMAL},
            </if>
            <if test="durationYear != null">
                #{durationYear,jdbcType=DECIMAL},
            </if>
            <if test="durationNote != null">
                #{durationNote,jdbcType=VARCHAR},
            </if>
            <if test="coreCourse != null">
                #{coreCourse,jdbcType=VARCHAR},
            </if>
            <if test="entryStandards != null">
                #{entryStandards,jdbcType=VARCHAR},
            </if>
            <if test="occupationDevelopment != null">
                #{occupationDevelopment,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isActive != null">
                #{isActive,jdbcType=BIT},
            </if>
            <if test="publicLevel != null">
                #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="dataLevel != null">
                #{dataLevel,jdbcType=INTEGER},
            </if>
            <if test="viewOrder != null">
                #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="idGea != null">
                #{idGea,jdbcType=VARCHAR},
            </if>
            <if test="idIae != null">
                #{idIae,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="introduction != null">
                #{introduction,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.get.institutioncenter.entity.InstitutionCourse">
        update m_institution_course
        <set>
            <if test="fkInstitutionId != null">
                fk_institution_id = #{fkInstitutionId,jdbcType=BIGINT},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                name_chn = #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="startMonth != null">
                start_month = #{startMonth,jdbcType=VARCHAR},
            </if>
            <if test="startDateNote != null">
                start_date_note = #{startDateNote,jdbcType=VARCHAR},
            </if>
            <if test="applyMonth != null">
                apply_month = #{applyMonth,jdbcType=VARCHAR},
            </if>
            <if test="applyDateNote != null">
                apply_date_note = #{applyDateNote,jdbcType=VARCHAR},
            </if>
            <if test="fee != null">
                fee = #{fee,jdbcType=DECIMAL},
            </if>
            <if test="feeMax != null">
                fee_max = #{feeMax,jdbcType=DECIMAL},
            </if>
            <if test="feeNote != null">
                fee_note = #{feeNote,jdbcType=VARCHAR},
            </if>
            <if test="feeCny != null">
                fee_cny = #{feeCny,jdbcType=DECIMAL},
            </if>
            <if test="durationYear != null">
                duration_year = #{durationYear,jdbcType=DECIMAL},
            </if>
            <if test="durationNote != null">
                duration_note = #{durationNote,jdbcType=VARCHAR},
            </if>
            <if test="coreCourse != null">
                core_course = #{coreCourse,jdbcType=VARCHAR},
            </if>
            <if test="entryStandards != null">
                entry_standards = #{entryStandards,jdbcType=VARCHAR},
            </if>
            <if test="occupationDevelopment != null">
                occupation_development = #{occupationDevelopment,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="isActive != null">
                is_active = #{isActive,jdbcType=BIT},
            </if>
            <if test="publicLevel != null">
                public_level = #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="dataLevel != null">
                data_level = #{dataLevel,jdbcType=INTEGER},
            </if>
            <if test="viewOrder != null">
                view_order = #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="idGea != null">
                id_gea = #{idGea,jdbcType=VARCHAR},
            </if>
            <if test="idIae != null">
                id_iae = #{idIae,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
            <if test="introduction != null">
                introduction = #{introduction,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.get.institutioncenter.entity.InstitutionCourse">
        update m_institution_course
        set fk_institution_id      = #{fkInstitutionId,jdbcType=BIGINT},
            code                   = #{code,jdbcType=VARCHAR},
            num                    = #{num,jdbcType=VARCHAR},
            name                   = #{name,jdbcType=VARCHAR},
            name_chn               = #{nameChn,jdbcType=VARCHAR},
            start_month            = #{startMonth,jdbcType=VARCHAR},
            start_date_note        = #{startDateNote,jdbcType=VARCHAR},
            apply_month            = #{applyMonth,jdbcType=VARCHAR},
            apply_date_note        = #{applyDateNote,jdbcType=VARCHAR},
            fee                    = #{fee,jdbcType=DECIMAL},
            fee_max                = #{feeMax,jdbcType=DECIMAL},
            fee_note               = #{feeNote,jdbcType=VARCHAR},
            fee_cny                = #{feeCny,jdbcType=DECIMAL},
            duration_year          = #{durationYear,jdbcType=DECIMAL},
            duration_note          = #{durationNote,jdbcType=VARCHAR},
            core_course            = #{coreCourse,jdbcType=VARCHAR},
            entry_standards        = #{entryStandards,jdbcType=VARCHAR},
            occupation_development = #{occupationDevelopment,jdbcType=VARCHAR},
            remark                 = #{remark,jdbcType=VARCHAR},
            is_active              = #{isActive,jdbcType=BIT},
            public_level           = #{publicLevel,jdbcType=VARCHAR},
            data_level             = #{dataLevel,jdbcType=INTEGER},
            view_order             = #{viewOrder,jdbcType=INTEGER},
            id_gea                 = #{idGea,jdbcType=VARCHAR},
            id_iae                 = #{idIae,jdbcType=VARCHAR},
            gmt_create             = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user        = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified           = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user      = #{gmtModifiedUser,jdbcType=VARCHAR},
            introduction           = #{introduction,jdbcType=LONGVARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.institutioncenter.entity.InstitutionCourse">
        update m_institution_course
        set fk_institution_id      = #{fkInstitutionId,jdbcType=BIGINT},
            code                   = #{code,jdbcType=VARCHAR},
            num                    = #{num,jdbcType=VARCHAR},
            name                   = #{name,jdbcType=VARCHAR},
            name_chn               = #{nameChn,jdbcType=VARCHAR},
            start_month            = #{startMonth,jdbcType=VARCHAR},
            start_date_note        = #{startDateNote,jdbcType=VARCHAR},
            apply_month            = #{applyMonth,jdbcType=VARCHAR},
            apply_date_note        = #{applyDateNote,jdbcType=VARCHAR},
            fee                    = #{fee,jdbcType=DECIMAL},
            fee_max                = #{feeMax,jdbcType=DECIMAL},
            fee_note               = #{feeNote,jdbcType=VARCHAR},
            fee_cny                = #{feeCny,jdbcType=DECIMAL},
            duration_year          = #{durationYear,jdbcType=DECIMAL},
            duration_note          = #{durationNote,jdbcType=VARCHAR},
            core_course            = #{coreCourse,jdbcType=VARCHAR},
            entry_standards        = #{entryStandards,jdbcType=VARCHAR},
            occupation_development = #{occupationDevelopment,jdbcType=VARCHAR},
            remark                 = #{remark,jdbcType=VARCHAR},
            is_active              = #{isActive,jdbcType=BIT},
            public_level           = #{publicLevel,jdbcType=VARCHAR},
            data_level             = #{dataLevel,jdbcType=INTEGER},
            view_order             = #{viewOrder,jdbcType=INTEGER},
            id_gea                 = #{idGea,jdbcType=VARCHAR},
            id_iae                 = #{idIae,jdbcType=VARCHAR},
            gmt_create             = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user        = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified           = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user      = #{gmtModifiedUser,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>