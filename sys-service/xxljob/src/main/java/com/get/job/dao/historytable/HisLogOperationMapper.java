package com.get.job.dao.historytable;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.log.model.LogOperation;
import com.get.job.entity.LogOperationHis;
import com.get.job.entity.LogOperationHisDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


@Mapper
@DS("logdb")
public interface HisLogOperationMapper extends BaseMapper<LogOperation> {

    int insertSelective(LogOperation record);

    int insertBatch(@Param("hisTableName")String hisTableName,@Param("records")List<LogOperation> records);

    int createHisLogOperationTable(@Param("hisTableName")String hisTableName);

//    List<LogOperationHisDto> getLogOperationHis(@Param("iPage") IPage<LogOperationHisDto> iPage);
    List<LogOperationHisDto> getLogOperationHis();
}