package com.get.job.service.permission.impl;


import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.GetDateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.job.dao.permission.StaffMapper;
import com.get.job.service.permission.IAutoUpdateAnnualLeaveService;
import com.get.officecenter.feign.IOfficeCenterClient;
import com.get.officecenter.dto.LeaveLogDto;
import com.get.officecenter.dto.LeaveStockDto;
import com.get.permissioncenter.entity.Staff;
import com.xxl.job.core.context.XxlJobHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/1/10 17:01
 * @verison: 1.0
 * @description:
 */
@Service
public class AutoUpdateAnnualLeaveServiceImpl implements IAutoUpdateAnnualLeaveService {

    private static final Logger logger = LoggerFactory.getLogger(DiseaseVacationUpdateServiceImpl.class);

    @Resource
    private StaffMapper staffMapper;
    @Resource
    private IOfficeCenterClient officeService;

    /**
     * 年假更新
     */
    @Override
    public void autoUpdateAnnualLeave() {
        XxlJobHelper.log("年假自动更新定时器开始");

        Date date = new Date();
        Calendar nowCalendar = Calendar.getInstance();
        //测试 时间定死为一月一日
//        nowCalendar.set(2025,1,1,0,0,0);
//        Date date = nowCalendar.getTime();
        nowCalendar.setTime(date);
        int nowYear = nowCalendar.get(Calendar.YEAR);

        //---------------  HTI(非悉尼公司) 开始 ---------------------
        //特殊日期2月29号 不处理
        if (!(nowCalendar.get(Calendar.MONTH) == 2 && nowCalendar.get(Calendar.DAY_OF_MONTH) == 29)) {
        //        国内的年假规则（已确认，也需要调整）
        //
        //        a. 入职员工，在第二年入职时间，系统自动加5天年假
        //
        //        b. 然后按入职时间每年加1天，加到15天（封顶）
        //
        //        c. 年假有效期为（2年）
        //
        //        d. 病假暂时不动
            //获取日期和当天日期相同的员工
            List<Staff> staffDtos = staffMapper.getStaffByEntryDate(date, false);
            //如果当前时间为特殊日期2月29号的后一天 3月1号，则把29号的员工带上一起算假期
            if (nowCalendar.get(Calendar.MONTH) == 3 && nowCalendar.get(Calendar.DAY_OF_MONTH) == 1) {
                //2月29日的日期
                Date effectiveDeadlineDate = GetDateUtil.makeDay(nowYear, 2, 29, 23, 59, 59);
                List<Staff> staffDtos2 = staffMapper.getStaffByEntryDate(effectiveDeadlineDate, false);
                staffDtos.addAll(staffDtos2);
            }
            if (GeneralTool.isNotEmpty(staffDtos)) {
                for (Staff staffDto : staffDtos) {
                    Calendar calendar = Calendar.getInstance();
                    calendar.setTime(staffDto.getEntryDate());
                    int staffEntryYear = calendar.get(Calendar.YEAR);
                    int offSet = nowYear - staffEntryYear;
                    //查询当天入职的员工 并刷新
                    if (offSet >= 1) {
                        offSet--;
                        try {
                            BigDecimal hour = new BigDecimal("40");
                            if (offSet > 10) {
                                offSet = 10;
                            }
                            hour = hour.add(new BigDecimal(String.valueOf(offSet)).multiply(new BigDecimal("8")));
                            addStockAndLog(nowYear, staffDto, hour);
                        } catch (Exception e) {
                            logger.error("添加年假库存异常------------" + e.getMessage());
                            XxlJobHelper.log("添加年假库存异常------------");
                        }
                    }
                }
            }
        }
        //---------------  HTI(非悉尼公司) 结束 ---------------------


        //---------------  悉尼公司 开始 ----------------------------
        //a. 1.1日刷新，每人每年20日年假，10日病假（目前没约束病假，没病假也可以申请，暂时不动）
        //b. 年假有效期为（1年，1.1-12.31）
        if (nowCalendar.get(Calendar.MONTH) == 1 && nowCalendar.get(Calendar.DAY_OF_MONTH) == 1) {
            List<Staff> staffList = staffMapper.getStaffByEntryDate(null, true);
            if (GeneralTool.isNotEmpty(staffList)) {
                for (Staff staff : staffList) {
                    LeaveStockDto leaveStockDto = new LeaveStockDto();
                    leaveStockDto.setFkCompanyId(staff.getFkCompanyId());
                    leaveStockDto.setFkStaffId(staff.getId());
                    leaveStockDto.setLeaveTypeKey(ProjectKeyEnum.ANNUAL_VACATION.key);
                    leaveStockDto.setLeaveStock(new BigDecimal(160));
                    //12月31
                    Date effectiveDeadlineDate = GetDateUtil.makeDay(nowYear, 12, 31, 23, 59, 59);
                    leaveStockDto.setEffectiveDeadline(effectiveDeadlineDate);
                    leaveStockDto.setGmtCreate(new Date());
                    leaveStockDto.setGmtCreateUser("[system]");
                    //新增一条记录
                    Long fkLeaveStockId = officeService.addSystem(leaveStockDto).getData();

                    //添加日志
                    LeaveLogDto leaveLogDto = new LeaveLogDto();
                    leaveLogDto.setFkCompanyId(staff.getFkCompanyId());
                    leaveLogDto.setFkStaffId(staff.getId());
                    leaveLogDto.setLeaveTypeKey(ProjectKeyEnum.ANNUAL_VACATION.key);
                    leaveLogDto.setOptTypeKey(ProjectKeyEnum.ANNUAL_REFRESH_ANNUAL_LEAVE.key);
                    leaveLogDto.setDuration(new BigDecimal(160));
                    //加FkLeaveStockId
                    leaveLogDto.setFkLeaveStockId(fkLeaveStockId);
                    leaveLogDto.setGmtCreate(new Date());
                    leaveLogDto.setGmtCreateUser("[system]");
                    leaveLogDto.setRemark(nowYear + "年假刷新");
                    officeService.addSystemLeaveLog(leaveLogDto);
                }
            }
        }
        //---------------  悉尼公司 结束 ----------------------------

        logger.info("--------------------年假自动更新定时器结束-------------");
        XxlJobHelper.log("年假自动更新定时器结束------------");
    }

    private void addStockAndLog(int nowYear, Staff staff, BigDecimal hour) throws Exception {
        Date effectiveDeadlineDate = null;

            if (GeneralTool.isNotEmpty(staff.getEntryDate())){
                //修改为当前执行时间-1天，加两年
                Date advanceDateByDay = GetDateUtil.getAdvanceDateByDay(new Date(), 1L);
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(advanceDateByDay);
                calendar.set(Calendar.YEAR, calendar.get(Calendar.YEAR) + 2);
                effectiveDeadlineDate = GetDateUtil.makeDay(calendar.get(Calendar.YEAR),calendar.get(Calendar.MONTH)+1,calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
            }

        LeaveStockDto leaveStockDto = new LeaveStockDto();
        leaveStockDto.setFkCompanyId(staff.getFkCompanyId());
        leaveStockDto.setFkStaffId(staff.getId());
        leaveStockDto.setLeaveTypeKey(ProjectKeyEnum.ANNUAL_VACATION.key);
        leaveStockDto.setLeaveStock(hour);
        leaveStockDto.setEffectiveDeadline(effectiveDeadlineDate);
        leaveStockDto.setGmtCreate(new Date());
        leaveStockDto.setGmtCreateUser("[system]");
        //新增一条记录
        Long fkLeaveStockId = officeService.addSystem(leaveStockDto).getData();

        //添加日志
        LeaveLogDto leaveLogDto = new LeaveLogDto();
        leaveLogDto.setFkCompanyId(staff.getFkCompanyId());
        leaveLogDto.setFkStaffId(staff.getId());
        leaveLogDto.setLeaveTypeKey(ProjectKeyEnum.ANNUAL_VACATION.key);
        leaveLogDto.setOptTypeKey(ProjectKeyEnum.ANNUAL_REFRESH_ANNUAL_LEAVE.key);
        leaveLogDto.setDuration(hour);
        //加FkLeaveStockId
        leaveLogDto.setFkLeaveStockId(fkLeaveStockId);
        leaveLogDto.setGmtCreate(new Date());
        leaveLogDto.setGmtCreateUser("[system]");
        leaveLogDto.setRemark(nowYear+"年假刷新");
        officeService.addSystemLeaveLog(leaveLogDto);
    }
}
