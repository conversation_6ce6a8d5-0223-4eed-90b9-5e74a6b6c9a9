package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.Agent;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
@DS("saledb")
public interface AgentMapper extends BaseMapper<Agent> {
    int insert(Agent record);

    int insertSelective(Agent record);

    List<Agent> selectAgentByIdGea(@Param("idGea") BigDecimal idGea);
}