<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.institution.AreaCityMapper">
    <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.AreaCity">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_area_state_id" jdbcType="BIGINT" property="fkAreaStateId"/>
        <result column="num" jdbcType="VARCHAR" property="num"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="name_chn" jdbcType="VARCHAR" property="nameChn"/>
        <result column="population" jdbcType="VARCHAR" property="population"/>
        <result column="area" jdbcType="VARCHAR" property="area"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="public_level" jdbcType="VARCHAR" property="publicLevel"/>
        <result column="view_order" jdbcType="INTEGER" property="viewOrder"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
        id
        , fk_area_state_id, num, name, name_chn, population, area, remark, public_level,
    view_order, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
    </sql>
    <insert id="insert" parameterType="com.get.institutioncenter.entity.AreaCity">
        insert into u_area_city (id, fk_area_state_id, num,
                                 name, name_chn, population,
                                 area, remark, public_level,
                                 view_order, gmt_create, gmt_create_user,
                                 gmt_modified, gmt_modified_user)
        values (#{id,jdbcType=BIGINT}, #{fkAreaStateId,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR},
                #{name,jdbcType=VARCHAR}, #{nameChn,jdbcType=VARCHAR}, #{population,jdbcType=VARCHAR},
                #{area,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{publicLevel,jdbcType=VARCHAR},
                #{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR},
                #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.AreaCity">
        insert into u_area_city
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkAreaStateId != null">
                fk_area_state_id,
            </if>
            <if test="num != null">
                num,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="nameChn != null">
                name_chn,
            </if>
            <if test="population != null">
                population,
            </if>
            <if test="area != null">
                area,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="publicLevel != null">
                public_level,
            </if>
            <if test="viewOrder != null">
                view_order,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkAreaStateId != null">
                #{fkAreaStateId,jdbcType=BIGINT},
            </if>
            <if test="num != null">
                #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="population != null">
                #{population,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                #{area,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                #{remark,jdbcType=VARCHAR},
            </if>
            <if test="publicLevel != null">
                #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.get.institutioncenter.entity.AreaCity">
        update u_area_city
        <set>
            <if test="fkAreaStateId != null">
                fk_area_state_id = #{fkAreaStateId,jdbcType=BIGINT},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="nameChn != null">
                name_chn = #{nameChn,jdbcType=VARCHAR},
            </if>
            <if test="population != null">
                population = #{population,jdbcType=VARCHAR},
            </if>
            <if test="area != null">
                area = #{area,jdbcType=VARCHAR},
            </if>
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="publicLevel != null">
                public_level = #{publicLevel,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                view_order = #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.institutioncenter.entity.AreaCity">
        update u_area_city
        set fk_area_state_id  = #{fkAreaStateId,jdbcType=BIGINT},
            num               = #{num,jdbcType=VARCHAR},
            name              = #{name,jdbcType=VARCHAR},
            name_chn          = #{nameChn,jdbcType=VARCHAR},
            population        = #{population,jdbcType=VARCHAR},
            area              = #{area,jdbcType=VARCHAR},
            remark            = #{remark,jdbcType=VARCHAR},
            public_level      = #{publicLevel,jdbcType=VARCHAR},
            view_order        = #{viewOrder,jdbcType=INTEGER},
            gmt_create        = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user   = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified      = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>
</mapper>