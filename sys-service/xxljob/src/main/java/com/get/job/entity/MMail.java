package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.time.LocalDateTime;

@Data
@TableName("M_MAIL")
public class MMail {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "唯一主键")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "平台应用CODE，AIS / PARTNER")
    @Column(name = "fk_platform_code")
    private String fkPlatformCode;

    @ApiModelProperty(value = "平台应用对应的创建用户Id，AIS=fk_staff_id / PARTNER=fk_partner_user_id")
    @Column(name = "fk_platform_user_id")
    private Long fkPlatformUserId;

    @ApiModelProperty(value = "用户邮箱账号Id")
    @Column(name = "fk_mail_account_id")
    private Long fkMailAccountId;

    @ApiModelProperty(value = "邮件的Id（邮件服务器自带邮件Id）")
    @Column(name = "mail_id")
    private String mailId;

    @ApiModelProperty(value = "邮件的邮箱文件夹")
    @Column(name = "fold_box")
    private String foldBox;

    @ApiModelProperty(value = "邮件标题")
    @Column(name = "subject")
    private String subject;

    @ApiModelProperty(value = "邮件内容")
    @Column(name = "body")
    private String body;

    @ApiModelProperty(value = "发件人")
    @Column(name = "from_mail")
    private String fromMail;

    @ApiModelProperty(value = "收件人")
    @Column(name = "to_mail")
    private String toMail;

    @ApiModelProperty(value = "抄送人")
    @Column(name = "cc_mail")
    private String ccMail;

    @ApiModelProperty(value = "密送人")
    @Column(name = "bcc_mail")
    private String bccMail;

    @ApiModelProperty(value = "是否分别发送，0否/1是")
    @Column(name = "is_separately")
    private boolean isSeparately;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "收件日期")
    @Column(name = "date")
    private LocalDateTime date;

    @ApiModelProperty(value = "邮件分类状态 0、未分析; 1、新申请; 2、提交完成; 3、已录取; 4、已付学费; 5、收到签证涵; 6、获得签证; 7、其他 ")
    @Column(name = "llm_mail_type")
    private Integer llmMailType;

    @ApiModelProperty(value = "是否已读：0否/1是")
    @Column(name = "is_read")
    private boolean isRead;

    @ApiModelProperty(value = "是否星标邮件：0否/1是")
    @Column(name = "is_star")
    private boolean isStar;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    @Column(name = "gmt_create")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "创建用户(登录账号)")
    @Column(name = "gmt_create_user")
    private String gmtCreateUser;

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "修改时间")
    @Column(name = "gmt_modified")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "修改用户(登录账号)")
    @Column(name = "gmt_modified_user")
    private String gmtModifiedUser;


}
