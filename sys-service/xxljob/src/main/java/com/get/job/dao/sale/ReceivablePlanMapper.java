package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.job.dto.SettlementInstallmentListDto;
import com.get.salecenter.entity.ReceivablePlan;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("saledb")
public interface ReceivablePlanMapper extends BaseMapper<ReceivablePlan> {
    int insert(ReceivablePlan record);

    int insertSelective(ReceivablePlan record);

    /**
     * 获取开学时间为 2021-09-01后得，应收=实收的 实付小于应付的数据 用以生成佣金数据
     *
     * @Date 17:28 2022/4/29
     * <AUTHOR>
     */
    List<SettlementInstallmentListDto> getSettlementInstallmentData();
}