package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@ApiModel("cpp留学保险实体类")
@Data
@TableName("STUFORINSURE")
public class StuForInsure implements Serializable {
    private static final long serialVersionUID = 1L;
    private BigDecimal id;
    private String name;
    private String englishname;
    private Date birthday;
    private String sex;
    private Date starttm;
    private Date endtm;
    private Date buytm;
    private String insureamount;
    private String policynumber;
    private String passport;
    private String company;
    private String account;
    private String arc;
    private String rm;
    private String remarks1;
    private String acceptedpay;
    private String agencypay;
    private String remarks2;
    private String country;
    private String excelstate;
    private String currency;
    private Date exceltime;
    private String agentid;
    private String stuid;
    private String isbuyalone;
    private String createrid;
    private String rcid;
    private Date createtime;
    private Date editime;
}