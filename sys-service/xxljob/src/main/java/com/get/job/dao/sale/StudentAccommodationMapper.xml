<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.sale.StudentAccommodationMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.StudentAccommodation" keyProperty="id" useGeneratedKeys="true">
    insert into m_student_accommodation (id, fk_student_id, fk_agent_id, 
      fk_staff_id, num, fk_area_country_id, 
      fk_area_state_id, fk_area_city_id, apartment_name, 
      check_in_date, check_out_date, duration, 
      fk_business_channel_id, deposit_date, fk_currency_type_num_accommodation, 
      accommodation_amount_per, accommodation_amount_per_unit, 
      accommodation_amount, accommodation_amount_note, 
      fk_currency_type_num_commission, commission_rate_receivable,
      commission_rate_payable, fixed_amount_receivable,
      fixed_amount_payable, remark, status, 
      id_gea, id_iae, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkStudentId,jdbcType=BIGINT}, #{fkAgentId,jdbcType=BIGINT}, 
      #{fkStaffId,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR}, #{fkAreaCountryId,jdbcType=BIGINT}, 
      #{fkAreaStateId,jdbcType=BIGINT}, #{fkAreaCityId,jdbcType=BIGINT}, #{apartmentName,jdbcType=VARCHAR}, 
      #{checkInDate,jdbcType=DATE}, #{checkOutDate,jdbcType=DATE}, #{duration,jdbcType=INTEGER}, 
      #{fkBusinessChannelId,jdbcType=BIGINT}, #{depositDate,jdbcType=DATE}, #{fkCurrencyTypeNumAccommodation,jdbcType=VARCHAR}, 
      #{accommodationAmountPer,jdbcType=DECIMAL}, #{accommodationAmountPerUnit,jdbcType=INTEGER}, 
      #{accommodationAmount,jdbcType=DECIMAL}, #{accommodationAmountNote,jdbcType=VARCHAR}, 
      #{fkCurrencyTypeNumCommission,jdbcType=VARCHAR}, #{commissionRateReceivable,jdbcType=DECIMAL},
      #{commissionRatePayable,jdbcType=DECIMAL}, #{fixedAmountReceivable,jdbcType=DECIMAL},
      #{fixedAmountPayable,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, 
      #{idGea,jdbcType=VARCHAR}, #{idIae,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentAccommodation" keyProperty="id" useGeneratedKeys="true">
    insert into m_student_accommodation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkStudentId != null">
        fk_student_id,
      </if>
      <if test="fkAgentId != null">
        fk_agent_id,
      </if>
      <if test="fkStaffId != null">
        fk_staff_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id,
      </if>
      <if test="fkAreaCityId != null">
        fk_area_city_id,
      </if>
      <if test="apartmentName != null">
        apartment_name,
      </if>
      <if test="checkInDate != null">
        check_in_date,
      </if>
      <if test="checkOutDate != null">
        check_out_date,
      </if>
      <if test="duration != null">
        duration,
      </if>
      <if test="fkBusinessChannelId != null">
        fk_business_channel_id,
      </if>
      <if test="depositDate != null">
        deposit_date,
      </if>
      <if test="fkCurrencyTypeNumAccommodation != null">
        fk_currency_type_num_accommodation,
      </if>
      <if test="accommodationAmountPer != null">
        accommodation_amount_per,
      </if>
      <if test="accommodationAmountPerUnit != null">
        accommodation_amount_per_unit,
      </if>
      <if test="accommodationAmount != null">
        accommodation_amount,
      </if>
      <if test="accommodationAmountNote != null">
        accommodation_amount_note,
      </if>
      <if test="fkCurrencyTypeNumCommission != null">
        fk_currency_type_num_commission,
      </if>
      <if test="commissionRateReceivable != null">
        commission_rate_receivable,
      </if>
      <if test="commissionRatePayable != null">
        commission_rate_payable,
      </if>
      <if test="fixedAmountReceivable != null">
        fixed_amount_receivable,
      </if>
      <if test="fixedAmountPayable != null">
        fixed_amount_payable,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="idGea != null">
        id_gea,
      </if>
      <if test="idIae != null">
        id_iae,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkStudentId != null">
        #{fkStudentId,jdbcType=BIGINT},
      </if>
      <if test="fkAgentId != null">
        #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="fkStaffId != null">
        #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityId != null">
        #{fkAreaCityId,jdbcType=BIGINT},
      </if>
      <if test="apartmentName != null">
        #{apartmentName,jdbcType=VARCHAR},
      </if>
      <if test="checkInDate != null">
        #{checkInDate,jdbcType=DATE},
      </if>
      <if test="checkOutDate != null">
        #{checkOutDate,jdbcType=DATE},
      </if>
      <if test="duration != null">
        #{duration,jdbcType=INTEGER},
      </if>
      <if test="fkBusinessChannelId != null">
        #{fkBusinessChannelId,jdbcType=BIGINT},
      </if>
      <if test="depositDate != null">
        #{depositDate,jdbcType=DATE},
      </if>
      <if test="fkCurrencyTypeNumAccommodation != null">
        #{fkCurrencyTypeNumAccommodation,jdbcType=VARCHAR},
      </if>
      <if test="accommodationAmountPer != null">
        #{accommodationAmountPer,jdbcType=DECIMAL},
      </if>
      <if test="accommodationAmountPerUnit != null">
        #{accommodationAmountPerUnit,jdbcType=INTEGER},
      </if>
      <if test="accommodationAmount != null">
        #{accommodationAmount,jdbcType=DECIMAL},
      </if>
      <if test="accommodationAmountNote != null">
        #{accommodationAmountNote,jdbcType=VARCHAR},
      </if>
      <if test="fkCurrencyTypeNumCommission != null">
        #{fkCurrencyTypeNumCommission,jdbcType=VARCHAR},
      </if>
      <if test="commissionRateReceivable != null">
        #{commissionRateReceivable,jdbcType=DECIMAL},
      </if>
      <if test="commissionRatePayable != null">
        #{commissionRatePayable,jdbcType=DECIMAL},
      </if>
      <if test="fixedAmountReceivable != null">
        #{fixedAmountReceivable,jdbcType=DECIMAL},
      </if>
      <if test="fixedAmountPayable != null">
        #{fixedAmountPayable,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="idGea != null">
        #{idGea,jdbcType=VARCHAR},
      </if>
      <if test="idIae != null">
        #{idIae,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>