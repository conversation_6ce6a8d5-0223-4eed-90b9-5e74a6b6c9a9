package com.get.job.dao.log;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.job.entity.LogCppToBms;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("logdb")
public interface LogCppToBmsMapper extends BaseMapper<LogCppToBms> {
//    int insert(LogCppToBms record);
//
//    int insertSelective(LogCppToBms record);

}