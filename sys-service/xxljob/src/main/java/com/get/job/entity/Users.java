package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("USERS")
public class Users extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String name;
    private String englishname;
    private String email;
    private Date birthday;
    private Short gender;
    private Date accessiontime;
    private String enclosure;
    private String company;
    private BigDecimal passpeid;
    private String englishlevel;
    private String msn;
    private String education;
    private String address;
    private BigDecimal depositstate;
    private String agencyname;
    private Long signet;
    private BigDecimal crmuserid;
    private BigDecimal contacthistoryid;
    private BigDecimal epid;
    private String office;
    private Date createtime;
    private BigDecimal companyid;
    private BigDecimal adid;
    private BigDecimal state;
    private String enclosure2;
    private BigDecimal leadid;
    private String maritalstatus;
    private String postcode;
    private String areacode;
    private BigDecimal qq;
    private String centerep;
    private BigDecimal datacome;
    private BigDecimal flowstate;
    private Date statetime;
    private Date vcptime;
    private Date ctime;
    private Date vtime;
    private Date dtime;
    private Date cptime;
    private BigDecimal cj;
    private BigDecimal hzrq;
    private BigDecimal zjrq;
    private BigDecimal gzzm;
    private BigDecimal lvli;
    private BigDecimal pf;
    private BigDecimal sp;
    private BigDecimal rl;
    private BigDecimal ps;
    private String adviser;
    private BigDecimal finishstate;
    private String appcountry;
    private String appcourse;
    private String studyplan;
    private Date schoolday;
    private BigDecimal adstate;
    private String sif;
    private String vipno;
    private BigDecimal genmemberid;
    private BigDecimal rcid;
    private String otherdatacome;
    private String relative;
    private String relationship;
    private BigDecimal relativemob;
    private String preferredcountries;
    private String preferredintake;
    private String preferredschool1;
    private String preferredschool2;
    private String preferredschool3;
    private String preferredprogramme1;
    private String preferredprogramme2;
    private String preferredprogramme3;
    private String remark;
    private String phone1;
    private String phone2;
    private String engfirstname;
    private BigDecimal stuno;
    private String stumark;
    private BigDecimal liid;
    private BigDecimal enclosurecheck;
    private BigDecimal standardtest;
    private String standardtestmark;
    private BigDecimal englishtype;
    private String englishmark;
    private BigDecimal precommission;
    private BigDecimal prerate;
    private BigDecimal precurrency;
    private BigDecimal failstate;
    private BigDecimal schoollevel;
    private BigDecimal agentid;
    private BigDecimal rcmark;
    private String originalschool;
    private String highestdegree;
    private String workseniority;
    private String graduation;
    private String major;
    private Date abroadtime;
    private String interesting;
    private String intereststage;
    private String professionalinterest;
    private BigDecimal centerlinkaccount;
    private String degree;
    private String otherdegree;
    private Date enclosurechecktime;
    private BigDecimal unbundling;
    private Date adtime;
    private BigDecimal epidAssist;
    private BigDecimal sddid;
    private BigDecimal sdmid;
    private BigDecimal createOrigin;
    private BigDecimal issueStuId;
    private String provinceval;
    private BigDecimal graduateby;
    private String schoolname;
    private String school;
    private BigDecimal grade;
    private String ulcedu;
    private String satedu;
    private String actedu;
    private String aleveledu;
    private String gecedu;
    private BigDecimal gpaedu;
    private String percentgrade;
    private BigDecimal jidian;
    private String otheredu;
    private String nationality;
    private String stmobile;
    private String stemail;
    private String iaecode;
    private BigDecimal isuStepReviewState;
    private BigDecimal isuStuId;
    private BigDecimal isuStuUserId;
    private String internalgrade;
    private BigDecimal createrid;
    private BigDecimal accuPoint;
    private String schedule;
}