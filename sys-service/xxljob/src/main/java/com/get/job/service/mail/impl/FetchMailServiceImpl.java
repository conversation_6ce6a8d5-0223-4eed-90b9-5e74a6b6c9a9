package com.get.job.service.mail.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.get.core.redis.cache.GetRedis;
import com.get.job.dao.mail.*;
import com.get.job.entity.*;
import com.get.job.service.mail.FetchMailService;
import com.get.job.service.mail.ITencentCloudService;
import com.get.job.utils.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.search.ComparisonTerm;
import javax.mail.search.ReceivedDateTerm;
import javax.mail.search.SearchTerm;
import java.io.*;
import java.nio.ByteBuffer;
import java.nio.channels.Channels;
import java.nio.channels.FileChannel;
import java.nio.channels.ReadableByteChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
@Service
public class FetchMailServiceImpl implements FetchMailService{
    @Resource
    private MMailMapper mailMapper;

    @Resource
    private MMailAccountMapper mailAccountMapper;

    @Resource
    private MMailAttachedMapper attachedMapper;

    @Resource
    private MFileMailMapper mFileMailMapper;
    @Resource
    private SMediaAndAttachedMapper sMediaAndAttachedMapper;

    @Resource
    private ITencentCloudService tencentCloudService;

    @Value("${file.tencentcloudfile.bucketname}")
    private String fileBucketName;

    @Value("${file.tencentcloudimage.bucketname}")
    private String imageBucketName;

    @Resource
    private Environment env;

//    @Resource
//    private GetRedis getRedis;

    @Override
    public void fetchMail() throws Exception {
        List<MailAccount> accounts = mailAccountMapper.selectAccount();
        int threadNum = Integer.parseInt(Objects.requireNonNull(env.getProperty("thread-pool.max-thread-num")));
        byte[] Key = Objects.requireNonNull(env.getProperty("encrypt.KEY")).getBytes();
        ExecutorService executor = Executors.newFixedThreadPool(threadNum);
//        List<String> isFetchAccounts = new ArrayList<>();
        log.info("一共有{}个邮箱需要获取邮件", accounts.size());
        for (MailAccount account : accounts) {
            // 判断某个用户是否执行完成
            /*Object emailAccountRedis = getRedis.getValueOps().get(account.getEmailAccount());
            if (emailAccountRedis != null) {
                System.out.println(account.getEmailAccount() + "上一次获取还没有执行结束");
                continue;
            }*/
            log.info(account.getEmailAccount() + "已经提交任务给线程");
            executor.submit(() -> {
                try {
                    // 给某个用户增加redis锁
                    log.info(account.getEmailAccount() + "开始获取");
//                    isFetchAccounts.add(account.getEmailAccount());
//                    getRedis.getValueOps().set(account.getEmailAccount(), true);
                    MailBoxUtils mailBoxUtils = new MailBoxUtils();
                    String emailAccount = account.getEmailAccount();
                    String emailPassword = AesUtil.decrypt(account.getEmailPassword(), Key);
                    String emailType = account.getEmailType();
                    String host = env.getProperty("mail." + emailType);
                    MailBox mailBox = mailBoxUtils.login(emailAccount, emailPassword, host);
                    QueryWrapper<MMail> mMailQueryWrapper = new QueryWrapper<>();
                    mMailQueryWrapper.eq("fk_mail_account_id", account.getId())
                            .eq("fold_box", "inboxList")
                            .orderByDesc("date") // 按照日期降序排列
                            .last("LIMIT 1"); // 只取一条记录;
                    MMail mMail = mailMapper.selectOne(mMailQueryWrapper);
                    if (mMail == null) {
                        mMail = new MMail();
                        mMail.setDate(account.getGmtCreate());
                    }
                    // 获取收件箱最新的邮件
                    String inFoldName = env.getProperty("mail." + emailType + "-inboxList");
                    log.info("正在获取的账号是{}，获取的文件夹是：{}", account.getId(), inFoldName);
                    fetchAllMail(account, mMail, mailBox, inFoldName, "inboxList");
                    // 获取草稿箱最新的邮件
                    String drFoldName = env.getProperty("mail." + emailType + "-draftList");
                    QueryWrapper<MMail> mMailQueryWrapper1 = new QueryWrapper<>();
                    mMailQueryWrapper1.eq("fk_mail_account_id", account.getId())
                            .eq("fold_box", "draftList")
                            .orderByDesc("date") // 按照日期降序排列
                            .last("LIMIT 1"); // 只取一条记录;
                    mMail = mailMapper.selectOne(mMailQueryWrapper1);
                    if (mMail == null) {
                        mMail = new MMail();
                        mMail.setDate(account.getGmtCreate());
                    }
                    log.info("正在获取的账号是{}，获取的文件夹是：{}", account.getId(), drFoldName);
                    fetchAllMail(account, mMail, mailBox, drFoldName, "draftList");
                    // 获取发件箱最新的邮件
                    String seFoldName = env.getProperty("mail." + emailType + "-sentList");
                    QueryWrapper<MMail> mMailQueryWrapper2 = new QueryWrapper<>();
                    mMailQueryWrapper2.eq("fk_mail_account_id", account.getId())
                            .eq("fold_box", "sentList")
                            .orderByDesc("date") // 按照日期降序排列
                            .last("LIMIT 1"); // 只取一条记录;
                    mMail = mailMapper.selectOne(mMailQueryWrapper2);
                    if (mMail == null) {
                        mMail = new MMail();
                        mMail.setDate(account.getGmtCreate());
                    }
                    log.info("正在获取的账号是{}，获取的文件夹是：{}", account.getId(), seFoldName);
                    fetchAllMail(account, mMail, mailBox, seFoldName, "sentList");
                    // 获取已删除最新的邮件
                    String deFoldName = env.getProperty("mail." + emailType + "-deletedList");
                    QueryWrapper<MMail> mMailQueryWrapper3 = new QueryWrapper<>();
                    mMailQueryWrapper3.eq("fk_mail_account_id", account.getId())
                            .eq("fold_box", "deletedList")
                            .orderByDesc("date") // 按照日期降序排列
                            .last("LIMIT 1"); // 只取一条记录;
                    mMail = mailMapper.selectOne(mMailQueryWrapper3);
                    if (mMail == null) {
                        mMail = new MMail();
                        mMail.setDate(account.getGmtCreate());
                    }
                    log.info("正在获取的账号是{}，获取的文件夹是：{}", account.getId(), deFoldName);
                    fetchAllMail(account, mMail, mailBox, deFoldName, "deletedList");
                    // 将用户在redis删除
                    log.info(account.getEmailAccount() + "正常执行完成，redis解锁");
//                    getRedis.del(account.getEmailAccount());
//                    isFetchAccounts.remove(account.getEmailAccount());
                    // 关闭连接
                    mailBox.getStore().close();
                } catch (Exception e) {
                    e.printStackTrace();
                    // 将用户在redis删除
                    log.info("{}执行异常，redis解锁", account.getEmailAccount());
                    log.info("多线程中的异常：获取邮件失败{}", account.getEmailAccount());
                }
            });
        }
    }

    private StringBuilder processNestedMultipart(MimeMultipart multipart) throws Exception {
        int partCount = multipart.getCount();
        boolean htmlFound = false;
        StringBuilder contentBuilder = new StringBuilder();
        List<String> textContents = new ArrayList<>();

        // 第一遍：查找是否有HTML内容
        for (int i = 0; i < partCount; i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            if (bodyPart.isMimeType("multipart/*")) {
                MimeMultipart innerMultipart = (MimeMultipart) bodyPart.getContent();
                htmlFound = htmlFound || findHtmlContent(innerMultipart);
            } else if (bodyPart.isMimeType("text/html")) {
                htmlFound = true;
            }
        }

        // 第二遍：基于htmlFound标志决定处理方式
        for (int i = 0; i < partCount; i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            String disposition = bodyPart.getDisposition();

            if (bodyPart.isMimeType("multipart/*")) {
                contentBuilder.append(processNestedMultipart((MimeMultipart) bodyPart.getContent()));
            } else if (!htmlFound && bodyPart.isMimeType("text/plain")) {
                // 如果没有找到HTML内容，则处理纯文本
                Object contentObject = bodyPart.getContent();
                if (contentObject instanceof String) {
                    ContentType ct = new ContentType(bodyPart.getContentType());
                    String charset = ct.getParameter("charset");
                    if (charset == null || charset.isEmpty()) {
                        charset = "utf-8"; // 默认使用utf-8
                    }
                    try {
                        String content = new String(((String) contentObject).getBytes(charset), charset);
                        contentBuilder.append(content);
                    } catch (UnsupportedEncodingException e) {
//                        e.printStackTrace();
                        log.info("邮件内容解码失败");
                    }
                }
            } else if (bodyPart.isMimeType("text/html") && htmlFound) {
                // 如果已经确定要使用HTML内容，则在这里处理它
                Object contentObject = bodyPart.getContent();
                if (contentObject instanceof String) {
                    contentBuilder.append(contentObject.toString());
                }
            } else if (disposition != null && (disposition.equalsIgnoreCase(Part.ATTACHMENT) || disposition.equalsIgnoreCase(Part.INLINE))) {
                // 跳过附件或内联资源
                continue;
            } else {
                // 对于其他MIME类型，记录或按需处理
                log.info("未处理的MIME类型: {}", bodyPart.getContentType());
            }
        }

        return contentBuilder;
    }

    private boolean findHtmlContent(MimeMultipart multipart) throws MessagingException, IOException {
        int count = multipart.getCount();
        for (int i = 0; i < count; i++) {
            BodyPart bodyPart = multipart.getBodyPart(i);
            if (bodyPart.isMimeType("multipart/*")) {
                if (findHtmlContent((MimeMultipart) bodyPart.getContent())) {
                    return true;
                }
            } else if (bodyPart.isMimeType("text/html")) {
                return true;
            }
        }
        return false;
    }


    private void inputStreamToFile(InputStream ins, File file) throws Exception {
        FileOutputStream fos = new FileOutputStream(file);
        FileChannel fileChannel = fos.getChannel();
        ReadableByteChannel inputChannel = Channels.newChannel(ins);
        ByteBuffer buffer = ByteBuffer.allocateDirect(65536); // 直接分配缓冲区
        while (inputChannel.read(buffer) != -1) {
            buffer.flip(); // 准备缓冲区进行写入
            fileChannel.write(buffer);
            buffer.clear(); // 清空缓冲区以便下次读取
        }
        ins.close();
        /*try (FileOutputStream fos = new FileOutputStream(file);
             FileChannel fileChannel = fos.getChannel();
             ReadableByteChannel inputChannel = Channels.newChannel(ins)) {
            ByteBuffer buffer = ByteBuffer.allocateDirect(65536); // 直接分配缓冲区
            while (inputChannel.read(buffer) != -1) {
                buffer.flip(); // 准备缓冲区进行写入
                fileChannel.write(buffer);
                buffer.clear(); // 清空缓冲区以便下次读取
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                if (ins != null) {
                    ins.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }*/
    }

    public void processAttachments(Multipart multipart, List<File> files, List<EmbeddedFile> embeddedFiles, List<MMailAttached> mMailAttacheds, MailAccount
            mMailAccount, String mailId) throws Exception {
        int partCount = multipart.getCount();
        for (int j = 0; j < partCount; j++) {
            BodyPart bodyPart = multipart.getBodyPart(j);
            // 如果这是一个嵌套的multipart部分，则递归处理
            if (bodyPart.isMimeType("multipart/*")) {
                processAttachments((Multipart) bodyPart.getContent(), files, embeddedFiles, mMailAttacheds, mMailAccount, mailId);
            } else {
                try {
                    if (bodyPart.getFileName() == null || bodyPart.getFileName().isEmpty()) {
                        continue;
                    }
                    // 尝试获取文件名
                    String fileName = MimeUtility.decodeText(bodyPart.getFileName());
                    // 检查是否有文件名或disposition为attachment
                    String disposition = bodyPart.getDisposition();
                    // 获取 Content-ID 头信息并去除角括号
                    String contentId = null;
                    String[] contentIdHeaders = bodyPart.getHeader("Content-ID");
                    if (contentIdHeaders != null && contentIdHeaders.length > 0) {
                        contentId = contentIdHeaders[0].trim();
                        if (contentId.startsWith("<") && contentId.endsWith(">")) {
                            contentId = contentId.substring(1, contentId.length() - 1).trim();
                        }
                    }
                    // 处理附件
                    if ((fileName != null && !fileName.trim().isEmpty()) ||
                            (disposition != null && disposition.equalsIgnoreCase(Part.ATTACHMENT))) {
                        // 忽略具有Content-ID的部件，它们通常是内嵌资源
                        if (contentId != null && !contentId.isEmpty()) {

                            EmbeddedFile embeddedFile = new EmbeddedFile();
                            embeddedFile.setContentId(contentId);

                            // 创建临时文件，并确保它被正确关闭
                            try (InputStream inputStream = bodyPart.getInputStream()) {
                                Path tempFile = Files.createTempFile(UUID.randomUUID() + contentId, null); // 创建临时文件，前缀为"email-attachment-"
                                Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING); // 将输入流内容复制到临时文件

                                File file = tempFile.toFile(); // 转换Path为File对象
                                embeddedFile.setFile(file);
                                embeddedFiles.add(embeddedFile);
                            } catch (IOException e) {
                                e.printStackTrace();
                                log.info("处理内嵌资源出错：{}", e.getMessage());
                            }
                            continue;
                        }
                        if (fileName == null || fileName.trim().isEmpty()) {
                            // 如果没有文件名，创建一个默认名称
                            fileName = "unnamed_attachment_" + j;
                        }
                        // 创建并填充附件实体对象
                        MMailAttached mMailAttached = new MMailAttached();
                        mMailAttached.setFkMailAccountId(mMailAccount.getId());
                        mMailAttached.setMailId(mailId);
                        mMailAttached.setFileName(fileName);
                        mMailAttached.setFileId(String.valueOf(j));
                        // 获取文件扩展名
                        int lastDotIndex = fileName.lastIndexOf('.');
                        if (lastDotIndex != -1) {
                            if (fileName.substring(lastDotIndex + 1).length() > 4) {
                                mMailAttached.setFileExtension(fileName.substring(fileName.length() - 4));
                            } else {
                                mMailAttached.setFileExtension(fileName.substring(lastDotIndex + 1));
                            }
                        } else {
                            mMailAttached.setFileExtension(".");
                        }
                        mMailAttached.setGmtCreate(LocalDateTime.now());
                        mMailAttacheds.add(mMailAttached);
                        if (mMailAttached.getFileExtension() != null && mMailAttached.getFileExtension().equals("docx") || mMailAttached.getFileExtension().equals("doc")) {
                            try (InputStream inputStream = bodyPart.getInputStream()) {
                                // 创建临时文件，并确保它被正确关闭
                                Path tempFile = Files.createTempFile(fileName, null); // 创建临时文件，前缀为"email-attachment-"
                                Files.copy(inputStream, tempFile, StandardCopyOption.REPLACE_EXISTING); // 将输入流内容复制到临时文件

                                File file = tempFile.toFile(); // 转换Path为File对象
                                files.add(file); // 添加到你的文件列表中
                            } catch (IOException e) {
                                files.add(null);
//                                e.printStackTrace();
                                log.info("处理附件出错：{}", e.getMessage());
                            }
                        } else {
                            files.add(null);
                        }
                    }
                } catch (Exception e) {
//                    e.printStackTrace();
                    // 记录错误但继续处理其他部分
                    log.info("Error processing attachment: {}", e.getMessage());
                }
            }
        }
    }

    public static String subString(String url) {
        if (url.contains("target")) {
            url = url.replace("/data/project/get/file-center/target", "");
            url = url.replace("/data/project/get/app-file-center/target", "");
            return url;
        } else {
            return url;
        }

    }

    @Override
    public void fetchAllMail(MailAccount mMailAccount, MMail mMail, MailBox mailBox, String foldName, String
            saveFoldName) throws Exception {
        Store store = mailBox.getStore();
        Folder folder = store.getFolder(foldName);
        folder.open(Folder.READ_ONLY);
        // 获取目标邮件的UID，之后以此为参考来获取该邮件之后的邮件
        UIDFolder uidFolder = (UIDFolder) folder;
        // 转换为 Date
        Date afterDate = Date.from(mMail.getDate().atZone(ZoneId.systemDefault()).toInstant());
        // 使用 ComparisonTerm.GT 表示"大于"，即晚于给定日期
        SearchTerm receivedAfter = new ReceivedDateTerm(ComparisonTerm.GT, afterDate);
        QueryWrapper<MMail> queryWrapper = new QueryWrapper<>();
        queryWrapper.apply("DATE(date) = {0}", mMail.getDate().toLocalDate());
        queryWrapper.eq("fk_mail_account_id", mMailAccount.getId());
        queryWrapper.eq("fold_box", saveFoldName);
        queryWrapper.orderByDesc("gmt_create");
        // 添加限制结果数量的部分
        List<MMail> mails = mailMapper.selectList(queryWrapper);
        List<String> mailIds = mails.stream()
                .map(MMail::getMailId) // 提取每个 MMail 对象的 mailId 属性
                .filter(Objects::nonNull) // 可选：过滤掉可能为 null 的值
                .collect(Collectors.toList()); // 收集结果到一个新的 List<String> 中
        Message[] messages = folder.search(receivedAfter);
        if (messages.length == 0) {
            log.info("No messages found");
            return;
        }
        long mailMaxUid;
        if (!mails.isEmpty()) {
            mailMaxUid = Long.parseLong(mails.get(0).getMailId());
            log.info("拿到{}邮箱中最大的邮件uid是：{}", foldName, mails.get(0).getMailId());
        } else {
            mailMaxUid = uidFolder.getUID(messages[0]);
        }
        log.info("今天目前邮件的总数是：{}", messages.length);
        if (uidFolder.getUID(messages[messages.length - 1]) == mailMaxUid) {
            log.info("没有收到新的邮件");
            return;
        }
        // 避免线程执行过久一次最多只拿十封邮件
        int allMailNum = 0;
        int maxMail = 10;
        for (int i = messages.length - 1; i >= 0; i--) {
            Message message = messages[i];
            long uid = uidFolder.getUID(message);
            // 获取邮件id
            String mailId = String.valueOf(uid);
            if (mailIds.contains(mailId)) {
                continue;
            } else {
                mailIds.add(mailId);
                allMailNum = allMailNum + 1;
            }
            if (allMailNum > maxMail) {
                log.info("已经达到了单次获取邮件的最大值");
                break;
            }
            // 获取到第i封邮件
            log.info("正在获取的邮件的uuid是：{}", uid);
            /*if (!mailId.equals("79867")) {
                continue;
            }*/
            MimeMessage mimeMessage = (MimeMessage) message;
            try {
                // 文件夹如果关闭要打开
                if (!folder.isOpen()) {
                    folder.open(Folder.READ_ONLY);
                }
                List<MMailAttached> mMailAttacheds = new ArrayList<>();
                List<File> files = new ArrayList<>();
                List<EmbeddedFile> embeddedFiles = new ArrayList<>();
                // 获取邮件的主题
                // 文件夹如果关闭要打开
                if (!folder.isOpen()) {
                    folder.open(Folder.READ_ONLY);
                }
                String subject = mimeMessage.getSubject();
                // 获取邮件内容
                // 文件夹如果关闭要打开
                if (!folder.isOpen()) {
                    folder.open(Folder.READ_ONLY);
                }

                Object mailContent = mimeMessage.getContent();
                // 获取邮件正文内容
                String content = "";
                if (mailContent instanceof String) {
                    content = (String) mailContent;
                } else if (mailContent instanceof Multipart) {
                    MimeMultipart multipart = (MimeMultipart) mailContent;

                    StringBuilder contentBuilder = new StringBuilder();
                    try {
                        // 处理嵌套的多部分内容
                        contentBuilder.append(processNestedMultipart(multipart));
                    } catch (Exception e) {
                        log.info("处理邮件嵌套多部份出错主流程继续执行");
                    }
                    /*int partCount = multipart.getCount();
                    StringBuilder contentBuilder = new StringBuilder();
                    for (int j = 0; j < partCount; j++) {
                        BodyPart bodyPart = multipart.getBodyPart(j);
                        String contentType = bodyPart.getContentType();
                        if (bodyPart.isMimeType("text/plain") || bodyPart.isMimeType("text/html")) {
                            try {
                                // 处理纯文本和HTML内容
                                Object contentObject = bodyPart.getContent();
                                if (contentObject instanceof String) {
                                    // 解析contentType以获取charset
                                    ContentType ct = new ContentType(contentType);
                                    String charset = ct.getParameter("charset");
                                    if (charset == null || charset.isEmpty()) {
                                        charset = "utf-8"; // 默认使用utf-8
                                    }
                                    contentBuilder.append(new String(((String) contentObject).getBytes(charset), charset));
                                }
                            } catch (Exception e) {
                                log.info("解析附件中的内容不成功主流程继续");
                            }
                        } else if (bodyPart.isMimeType("multipart/*")) {
                            try {
                                // 处理嵌套的多部分内容
                                contentBuilder.append(processNestedMultipart((MimeMultipart) bodyPart.getContent()));
                            } catch (Exception e) {
//                                e.printStackTrace();
                                log.info("处理邮件嵌套多部份出错主流程继续执行");
                            }
                        }
                    }*/
                    content = contentBuilder.toString();
                    // 处理附件
                    MimeMessage mimeMessage1 = (MimeMessage) message;
                    Object mailContent1 = mimeMessage1.getContent();
                    MimeMultipart multipart1 = (MimeMultipart) mailContent1;
                    processAttachments(multipart1, files, embeddedFiles, mMailAttacheds, mMailAccount, mailId);
                    // 将内嵌图片上传到桶里
                    for (EmbeddedFile embeddedFile : embeddedFiles) {
                        try {
                            File file = embeddedFile.getFile();
                            MFileMail fileMail = new MFileMail();
                            // 源文件名
                            String fileNameOrc = file.getName();

                            // 获取文件扩展名
                            // 获取后缀名
                            String fileTypeOrc = "";
                            int lastDotIndex = file.getName().lastIndexOf('.');
                            if (lastDotIndex != -1) {
                                if (file.getName().substring(lastDotIndex + 1).length() > 4) {
                                    fileTypeOrc = file.getName().substring(file.getName().length() - 4);
                                } else {
                                    fileTypeOrc = file.getName().substring(lastDotIndex + 1);
                                }
                            }
                            // 文件guid
                            String fileGuid = UUID.randomUUID().toString().replaceAll("-", "");
                            String fileurl = subString(AppendixUtils.getFileHtiPath(fileNameOrc));
                            int j = fileurl.lastIndexOf("/");
                            // 新文件名
                            String fileName = fileurl.substring(j + 1, fileurl.length());
                            fileMail.setFilePath(fileurl);
                            fileMail.setFileNameOrc(fileNameOrc);
                            fileMail.setFileTypeOrc(fileTypeOrc);
                            fileMail.setFileName(fileName);
                            fileMail.setFileGuid(fileGuid);
                            fileMail.setGmtCreate(LocalDateTime.now());
                            fileMail.setGmtCreateUser(mMailAccount.getGmtCreateUser());
                            log.info("将内嵌文件传入的桶名称是：{}", imageBucketName);
                            tencentCloudService.uploadObject(true, imageBucketName, file, fileurl);
                            fileMail.setFileKey(fileurl);
                            mFileMailMapper.insert(fileMail);
                            // 本地删除
//                            Files.delete(file.toPath());
                            String embeddedUrl = "https://" + imageBucketName + ".cos.ap-shanghai.myqcloud.com" + fileurl;
                            String embeddedContentId = "cid:" + embeddedFile.getContentId();
                            content = content.replace(embeddedContentId, embeddedUrl);
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.info("将嵌套附件保存出错");
                        }
                    }
                }
                // 文件夹如果关闭要打开
                if (!folder.isOpen()) {
                    folder.open(Folder.READ_ONLY);
                }
                // 获取发件人
                String from = ((InternetAddress) message.getFrom()[0]).getAddress();
                // 文件夹如果关闭要打开
                if (!folder.isOpen()) {
                    folder.open(Folder.READ_ONLY);
                }
                // 获取收件人
                StringBuilder toName = new StringBuilder();
                // 文件夹如果关闭要打开
                if (!folder.isOpen()) {
                    folder.open(Folder.READ_ONLY);
                }
                // 获取收件人信息
                Address[] recipients = message.getRecipients(Message.RecipientType.TO);
                if (recipients != null) {
                    for (Address recipient : recipients) {
                        toName.append(((InternetAddress) recipient).getAddress()).append(",");
                    }
                }
                // 如果 toName 不为空，则删除最后一个逗号
                if (toName.length() > 0) {
                    toName.setLength(toName.length() - 1); // 删除最后一个字符
                }
                // 获取抄送人
                StringBuilder ccName = new StringBuilder();
                // 文件夹如果关闭要打开
                if (!folder.isOpen()) {
                    folder.open(Folder.READ_ONLY);
                }
                Address[] ccAddresses = message.getRecipients(Message.RecipientType.CC);
                if (ccAddresses != null && ccAddresses.length > 0) {
                    for (Address address : ccAddresses) {
                        ccName.append(((InternetAddress) address).getAddress()).append(",");
                    }
                }
                // 如果 ccName 不为空，则删除最后一个逗号
                if (ccName.length() > 0) {
                    ccName.setLength(ccName.length() - 1); // 删除最后一个字符
                }
                // 获取密送人
                StringBuilder bccName = new StringBuilder();
                Address[] bccAddresses = message.getRecipients(Message.RecipientType.BCC);
                if (bccAddresses != null && bccAddresses.length > 0) {
                    for (Address address : bccAddresses) {
                        bccName.append(((InternetAddress) address).getAddress()).append(",");
                    }
                }
                // 如果密送人不为空，删除最后一个逗号
                if (bccName.length() > 0) {
                    bccName.setLength(bccName.length() - 1);
                }
                // 文件夹如果关闭要打开
                if (!folder.isOpen()) {
                    folder.open(Folder.READ_ONLY);
                }
                boolean isRead = mimeMessage.isSet(Flags.Flag.SEEN);
                boolean isStar = mimeMessage.isSet(Flags.Flag.FLAGGED);
                MMail mail = new MMail();
                mail.setFkPlatformCode(mMailAccount.getFkPlatformCode());
                mail.setFkPlatformUserId(mMailAccount.getFkPlatformUserId());
                mail.setFkMailAccountId(mMailAccount.getId());
                mail.setMailId(mailId);
                mail.setFoldBox(saveFoldName);
                mail.setSubject(subject);
                // 根据标题区分出系统提醒邮件
                Pattern pattern = Pattern.compile("^【.*提醒】.*");
                Matcher matcher = pattern.matcher(subject);
                if (matcher.find()) {
                    mail.setLlmMailType(8);
                } else {
                    mail.setLlmMailType(0);
                }
                if (content.contains("https://hti-public-image-prd-**********.cos.ap-shanghai.myqcloud.com/share/newbg.png")) {
                    mail.setLlmMailType(8);
                }
                mail.setBody(content);
                mail.setFromMail(from);
                mail.setToMail(toName.toString());
                mail.setCcMail(ccName.toString());
                mail.setBccMail(bccName.toString());
                mail.setSeparately(false);
                // 文件夹如果关闭要打开
                if (!folder.isOpen()) {
                    folder.open(Folder.READ_ONLY);
                }
                // 将 Date 转换为 LocalDateTime
                // 获取邮件日期
                Date date = mimeMessage.getReceivedDate();
                LocalDateTime localDateTime = date.toInstant()
                        .atZone(ZoneId.systemDefault())
                        .toLocalDateTime();
                mail.setDate(localDateTime);
                mail.setRead(isRead);
                mail.setStar(isStar);
                mail.setGmtCreate(LocalDateTime.now());
                try {
                    QueryWrapper<MMail> queryWrapper1 = new QueryWrapper<>();
                    queryWrapper1.eq("mail_id", mail.getMailId());
                    queryWrapper1.eq("fold_box", saveFoldName);
                    queryWrapper1.eq("fk_mail_account_id", mMailAccount.getId());
                    List<MMail> mMails = mailMapper.selectList(queryWrapper1);
                    if (!mMails.isEmpty()) {
                        log.info("{}的{}邮箱的{}邮件已经存在了", mMailAccount.getId(), saveFoldName, mail.getMailId());
                        continue;
                    }
                    mailMapper.insert(mail);
                    for (int k = 0; k < mMailAttacheds.size(); k++) {
                        MMailAttached mailAttached = mMailAttacheds.get(k);
                        mailAttached.setFkMailId(mail.getId());
                        attachedMapper.insert(mailAttached);
                        if (mailAttached.getFileExtension() != null && mailAttached.getFileExtension().equals("docx") || mailAttached.getFileExtension().equals("doc")) {
                            // 将附件保存
                            File file = files.get(k);
                            MFileMail fileMail = new MFileMail();
                            // 源文件名
                            String fileNameOrc = mailAttached.getFileName();
                            // 获取后缀名
                            String fileTypeOrc = mailAttached.getFileExtension();
                            // 文件guid
                            String fileGuid = UUID.randomUUID().toString().replaceAll("-", "");
                            String fileurl = subString(AppendixUtils.getFileHtiPath(fileNameOrc));
                            int j = fileurl.lastIndexOf("/");
                            // 新文件名
                            String fileName = fileurl.substring(j + 1, fileurl.length());
                            fileMail.setFilePath(fileurl);
                            fileMail.setFileNameOrc(fileNameOrc);
                            fileMail.setFileTypeOrc(fileTypeOrc);
                            fileMail.setFileName(fileName);
                            fileMail.setFileGuid(fileGuid);
                            fileMail.setGmtCreate(LocalDateTime.now());
                            fileMail.setGmtCreateUser(mMailAccount.getGmtCreateUser());
                            log.info("将文件传入的桶名称是：{}", fileBucketName);
                            tencentCloudService.uploadObject(true, fileBucketName, file, fileurl);
                            fileMail.setFileKey(fileurl);
                            mFileMailMapper.insert(fileMail);
                            // 本地删除
//                            Files.delete(file.toPath());
                            SMediaAndAttachedEntity sMediaAndAttachedEntity = new SMediaAndAttachedEntity();
                            sMediaAndAttachedEntity.setFkFileGuid(fileGuid);
                            sMediaAndAttachedEntity.setFkTableId(mailAttached.getId());
                            sMediaAndAttachedEntity.setFkTableName("m_mail_attached");
                            sMediaAndAttachedEntity.setGmtCreate(LocalDateTime.now());
                            sMediaAndAttachedEntity.setGmtCreateUser(mMailAccount.getGmtCreateUser());
                            sMediaAndAttachedMapper.insert(sMediaAndAttachedEntity);
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.info("邮件插入数据库失败：{}{}", mail.getMailId(), e.getMessage());
                } finally {
                    //删除保存在本地的内嵌资源
                    log.info("{}删除内嵌资源", mMailAccount.getEmailAccount());
                    for (EmbeddedFile embeddedFile : embeddedFiles) {
                        try {
                            Files.delete(embeddedFile.getFile().toPath());
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.info("{}删除内嵌资源出错", mMailAccount.getEmailAccount());
                        }
                    }
                    //删除本地保存的附件
                    log.info("{}删除附件资源", mMailAccount.getEmailAccount());
                    for (File file : files) {
                        if (file != null) {
                            try {
                                Files.delete(file.toPath());
                            } catch (Exception e) {
                                e.printStackTrace();
                                log.info("{}删除附件资源出错", mMailAccount.getEmailAccount());
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.info("拉取邮件失败{}", mMailAccount.getEmailAccount());
            }
        }

    }
}
