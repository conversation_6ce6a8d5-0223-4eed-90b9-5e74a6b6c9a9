package com.get.job.controller.finance;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.job.component.FinanceExcelInfoHelper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.ExecutionException;

/**
 * 财务Excel定时任务
 *
 * <AUTHOR>
 * @date 2021/12/16 10:32
 */

@Api(tags = "财务同步管理")
@RestController
@RequestMapping("schedule/finance_excel")
public class FinanceExcelSchedule {
//    @Resource
//    private FinanceExcelInfoHelper financeExcelInfoHelper;


//    @ApiOperation(value = "Excel同步")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "定时任务中心/财务同步管理/Excel同步")
//    @GetMapping("updateFinanceExcelInfo")
//    public ResponseBo setExchangeRateTime() throws ExecutionException, InterruptedException {
//        financeExcelInfoHelper.financeExcelInfoSynchronization();
//        return ResponseBo.ok();
//    }
}
