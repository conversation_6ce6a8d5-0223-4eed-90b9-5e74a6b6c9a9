<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.sale.StudentInsuranceMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.StudentInsurance" keyProperty="id" useGeneratedKeys="true">
    insert into m_student_insurance (id, fk_student_id, fk_agent_id, 
      fk_staff_id, num, fk_area_country_id, 
      insurance_num, insurance_start_time, insurance_end_time, 
      insurance_buy_time, buy_account, fk_business_channel_id, 
      fk_currency_type_num_insurance, insurance_amount, 
      insurance_amount_note, fk_currency_type_num_commission,
      commission_rate_receivable, commission_rate_payable,
      fixed_amount_receivable, fixed_amount_payable, 
      remark, status, id_gea, 
      id_iae, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkStudentId,jdbcType=BIGINT}, #{fkAgentId,jdbcType=BIGINT}, 
      #{fkStaffId,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR}, #{fkAreaCountryId,jdbcType=BIGINT}, 
      #{insuranceNum,jdbcType=VARCHAR}, #{insuranceStartTime,jdbcType=TIMESTAMP}, #{insuranceEndTime,jdbcType=TIMESTAMP}, 
      #{insuranceBuyTime,jdbcType=TIMESTAMP}, #{buyAccount,jdbcType=VARCHAR}, #{fkBusinessChannelId,jdbcType=BIGINT}, 
      #{fkCurrencyTypeNumInsurance,jdbcType=VARCHAR}, #{insuranceAmount,jdbcType=DECIMAL}, 
      #{insuranceAmountNote,jdbcType=VARCHAR}, #{fkCurrencyTypeNumCommission,jdbcType=VARCHAR},
      #{commissionRateReceivable,jdbcType=DECIMAL}, #{commissionRatePayable,jdbcType=DECIMAL},
      #{fixedAmountReceivable,jdbcType=DECIMAL}, #{fixedAmountPayable,jdbcType=DECIMAL}, 
      #{remark,jdbcType=VARCHAR}, #{status,jdbcType=INTEGER}, #{idGea,jdbcType=VARCHAR}, 
      #{idIae,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentInsurance" keyProperty="id" useGeneratedKeys="true">
    insert into m_student_insurance
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkStudentId != null">
        fk_student_id,
      </if>
      <if test="fkAgentId != null">
        fk_agent_id,
      </if>
      <if test="fkStaffId != null">
        fk_staff_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="insuranceNum != null">
        insurance_num,
      </if>
      <if test="insuranceStartTime != null">
        insurance_start_time,
      </if>
      <if test="insuranceEndTime != null">
        insurance_end_time,
      </if>
      <if test="insuranceBuyTime != null">
        insurance_buy_time,
      </if>
      <if test="buyAccount != null">
        buy_account,
      </if>
      <if test="fkBusinessChannelId != null">
        fk_business_channel_id,
      </if>
      <if test="fkCurrencyTypeNumInsurance != null">
        fk_currency_type_num_insurance,
      </if>
      <if test="insuranceAmount != null">
        insurance_amount,
      </if>
      <if test="insuranceAmountNote != null">
        insurance_amount_note,
      </if>
      <if test="fkCurrencyTypeNumCommission != null">
        fk_currency_type_num_commission,
      </if>
      <if test="commissionRateReceivable != null">
        commission_rate_receivable,
      </if>
      <if test="commissionRatePayable != null">
        commission_rate_payable,
      </if>
      <if test="fixedAmountReceivable != null">
        fixed_amount_receivable,
      </if>
      <if test="fixedAmountPayable != null">
        fixed_amount_payable,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="idGea != null">
        id_gea,
      </if>
      <if test="idIae != null">
        id_iae,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkStudentId != null">
        #{fkStudentId,jdbcType=BIGINT},
      </if>
      <if test="fkAgentId != null">
        #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="fkStaffId != null">
        #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="insuranceNum != null">
        #{insuranceNum,jdbcType=VARCHAR},
      </if>
      <if test="insuranceStartTime != null">
        #{insuranceStartTime,jdbcType=TIMESTAMP},
      </if>
      <if test="insuranceEndTime != null">
        #{insuranceEndTime,jdbcType=TIMESTAMP},
      </if>
      <if test="insuranceBuyTime != null">
        #{insuranceBuyTime,jdbcType=TIMESTAMP},
      </if>
      <if test="buyAccount != null">
        #{buyAccount,jdbcType=VARCHAR},
      </if>
      <if test="fkBusinessChannelId != null">
        #{fkBusinessChannelId,jdbcType=BIGINT},
      </if>
      <if test="fkCurrencyTypeNumInsurance != null">
        #{fkCurrencyTypeNumInsurance,jdbcType=VARCHAR},
      </if>
      <if test="insuranceAmount != null">
        #{insuranceAmount,jdbcType=DECIMAL},
      </if>
      <if test="insuranceAmountNote != null">
        #{insuranceAmountNote,jdbcType=VARCHAR},
      </if>
      <if test="fkCurrencyTypeNumCommission != null">
        #{fkCurrencyTypeNumCommission,jdbcType=VARCHAR},
      </if>
      <if test="commissionRateReceivable != null">
        #{commissionRateReceivable,jdbcType=DECIMAL},
      </if>
      <if test="commissionRatePayable != null">
        #{commissionRatePayable,jdbcType=DECIMAL},
      </if>
      <if test="fixedAmountReceivable != null">
        #{fixedAmountReceivable,jdbcType=DECIMAL},
      </if>
      <if test="fixedAmountPayable != null">
        #{fixedAmountPayable,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="idGea != null">
        #{idGea,jdbcType=VARCHAR},
      </if>
      <if test="idIae != null">
        #{idIae,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>