package com.get.job.service.office.impl;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.job.service.office.SendMultiUserTaskReminderService;
import com.get.officecenter.entity.Task;
import com.get.officecenter.feign.IOfficeCenterClient;
import com.get.officecenter.vo.TaskItemVo;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.get.core.tool.utils.DateUtil.now;

@Service
public class SendMultiUserTaskReminderServiceImpl implements SendMultiUserTaskReminderService {

    @Resource
    private IReminderCenterClient reminderCenterClient;

    @Resource
    private IOfficeCenterClient officeCenterClient;



    @Override
    public boolean sendMultiUserTaskReminder() {

        //获取当天截止的任务
        List<Task> tasks = officeCenterClient.getTaskByEndTime().getData();
        List<Long> taskItemsUnfinished = null;
        List<EmailSenderQueue> queueList = new ArrayList<>();
        for (Task task:tasks){
            //获取任务下的未完成的接收人id
           taskItemsUnfinished = officeCenterClient.getUnfinishedTaskItemReceiver(task.getId()).getData();
           //获取任务下未完成的子任务
           if(GeneralTool.isNotEmpty(taskItemsUnfinished)){
               Set<Long> ids = taskItemsUnfinished.stream().collect(Collectors.toSet());
                for (Long id:ids){
                    EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                    emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.NEW_MULTI_USER_TASK_REMINDER.getEmailTemplateKey());
                    emailSenderQueue.setFkTableName(TableEnum.M_TASK.key);
                    emailSenderQueue.setFkTableId(task.getId());
                    emailSenderQueue.setEmailParameter(String.valueOf(id));
                    emailSenderQueue.setFkDbName(ProjectKeyEnum.OFFICE_CENTER.key);
                    emailSenderQueue.setOperationTime(now());
                    queueList.add(emailSenderQueue);
                }
           }

        }
        Result<Boolean> bolleanResult =reminderCenterClient.batchAddEmailQueue(queueList);
        if (!bolleanResult.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(bolleanResult.getMessage()));
        }
        return true;
    }
}
