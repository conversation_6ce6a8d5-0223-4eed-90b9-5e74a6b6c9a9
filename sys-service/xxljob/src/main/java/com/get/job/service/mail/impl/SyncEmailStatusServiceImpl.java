package com.get.job.service.mail.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.job.dao.mail.MMailAccountMapper;
import com.get.job.dao.mail.MMailMapper;
import com.get.job.dao.mail.MMailSyncQueueMapper;
import com.get.job.entity.MMail;
import com.get.job.entity.MMailAccount;
import com.get.job.entity.MMailSyncQueue;
import com.get.job.service.mail.ISyncEmailStatusService;
import com.get.job.utils.AesUtil;
import com.get.job.utils.MailBox;
import com.get.job.utils.MailBoxUtils;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import javax.annotation.Resource;
import javax.mail.Flags;
import javax.mail.Folder;
import javax.mail.Message;
import javax.mail.Store;
import javax.mail.internet.MimeMessage;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

@Service
public class SyncEmailStatusServiceImpl implements ISyncEmailStatusService {
    @Resource
    private MMailMapper mailMapper;
    @Resource
    private MMailAccountMapper mailAccountMapper;
    @Resource
    private MMailSyncQueueMapper mailSyncQueueMapper;
    @Resource
    private Environment env;

    @Override
    public void syncEmailStatus() {
        QueryWrapper<MMailSyncQueue> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_sync", 0);
        queryWrapper.orderByAsc("fk_mail_id");
        queryWrapper.orderByAsc("gmt_create");
        List<MMailSyncQueue> mMailSyncQueueList = mailSyncQueueMapper.selectList(queryWrapper);
        Map<String, List<MMailSyncQueue>> groupedByMailId = mMailSyncQueueList.stream()
                .collect(Collectors.groupingBy(
                        MMailSyncQueue::getMailId,
                        LinkedHashMap::new,
                        Collectors.toList()
                ));
        byte[] key = Objects.requireNonNull(env.getProperty("encrypt.KEY")).getBytes();
        int threadNum = Integer.parseInt(Objects.requireNonNull(env.getProperty("thread-pool.sync-thread-num")));
        ExecutorService executor = Executors.newFixedThreadPool(threadNum);
        List<String> mailKey = new ArrayList<>(groupedByMailId.keySet());
        for (String mailId : mailKey) {
            executor.submit(() -> {
                try {
                    // 获取数据库中对应的邮件
                    Wrapper<MMail> mailWrapper = Wrappers.<MMail>lambdaQuery().eq(MMail::getMailId, mailId);
                    MMail mMail = mailMapper.selectOne(mailWrapper);
                    // 获取该邮件的对应账号
                    Wrapper<MMailAccount> accountWrapper = Wrappers.<MMailAccount>lambdaQuery().eq(MMailAccount::getId, mMail.getFkMailAccountId());
                    MMailAccount mMailAccount = mailAccountMapper.selectOne(accountWrapper);

                    String emailAccount = mMailAccount.getEmailAccount();
                    String emailType = mMailAccount.getEmailType();
                    String password = AesUtil.decrypt(mMailAccount.getEmailPassword(), key);

                    String foldBox = env.getProperty("mail." + emailType + "-" + mMail.getFoldBox());
                    String host = env.getProperty("mail." + emailType);
                    MailBoxUtils mailBoxUtils = new MailBoxUtils();
                    MailBox mailBox = mailBoxUtils.login(emailAccount, password, host);

                    Store store = mailBox.getStore();
                    Folder folder = store.getFolder(foldBox);
                    folder.open(Folder.READ_WRITE);
                    Message[] messages = folder.getMessages();
                    Message targetMessage = null;
                    boolean findMessage = false;
                    for (Message message : messages) {
                        MimeMessage mimeMessage = (MimeMessage) message;
                        if (mMail.getMailId().equals(mimeMessage.getMessageID().replaceAll("<", "").replaceAll(">", ""))) {
                            targetMessage = message;
                            findMessage = true;
                            break;
                        }
                    }
                    if (findMessage) {
                        String tragetFold = "";
                        for (MMailSyncQueue mMailSyncQueue : groupedByMailId.get(mailId)) {
                            if (mMailSyncQueue.getOperationType() == 1) {
                                targetMessage.setFlag(Flags.Flag.SEEN, "1".equals(mMailSyncQueue.getOperationValue()));
                            }
                            if (mMailSyncQueue.getOperationType() == 2) {
                                targetMessage.setFlag(Flags.Flag.FLAGGED, "1".equals(mMailSyncQueue.getOperationValue()));
                            }
                            if (mMailSyncQueue.getOperationType() == 3) {
                                tragetFold = mMailSyncQueue.getOperationValue();
                            }
                            if (!tragetFold.isEmpty()) {
                                String targetFoldBox = env.getProperty("mail." + emailType + "-" + tragetFold);
                                Folder targetFolder = store.getFolder(targetFoldBox);
                                targetFolder.open(Folder.READ_WRITE);
                                // 将邮件加入到新的文件夹中
                                folder.copyMessages(new Message[]{targetMessage}, targetFolder);
                                // 将邮件标记为已删除
                                targetMessage.setFlag(Flags.Flag.DELETED, true);
                                folder.close(true);
                                targetFolder.close(true);
                            }
                            mMailSyncQueue.setSync(true);
                            mMailSyncQueue.setGmtModified(LocalDateTime.now());
                            mMailSyncQueue.setGmtModifiedUser(mMailAccount.getGmtCreateUser());
                            mailSyncQueueMapper.updateById(mMailSyncQueue);
                        }
                    }
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });
        }
        executor.shutdown();
    }
}
