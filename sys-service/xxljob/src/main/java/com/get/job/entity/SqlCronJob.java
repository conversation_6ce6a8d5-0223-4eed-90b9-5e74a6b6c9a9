package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_sql_cron_job")
public class SqlCronJob extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableField(value="`sql`")
    private String sql;
    private String remark;
    @TableField(value="`interval`")
    private Integer interval;
    private Boolean isActive;
}