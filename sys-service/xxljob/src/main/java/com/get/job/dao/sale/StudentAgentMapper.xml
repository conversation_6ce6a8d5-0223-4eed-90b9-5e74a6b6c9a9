<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.sale.StudentAgentMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.StudentAgent">
    insert into r_student_agent (id, fk_student_id, fk_agent_id, 
      is_active, active_date, unactive_date, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkStudentId,jdbcType=BIGINT}, #{fkAgentId,jdbcType=BIGINT}, 
      #{isActive,jdbcType=BIT}, #{activeDate,jdbcType=TIMESTAMP}, #{unactiveDate,jdbcType=TIMESTAMP}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentAgent">
    insert into r_student_agent
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkStudentId != null">
        fk_student_id,
      </if>
      <if test="fkAgentId != null">
        fk_agent_id,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="activeDate != null">
        active_date,
      </if>
      <if test="unactiveDate != null">
        unactive_date,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkStudentId != null">
        #{fkStudentId,jdbcType=BIGINT},
      </if>
      <if test="fkAgentId != null">
        #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="activeDate != null">
        #{activeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="unactiveDate != null">
        #{unactiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.salecenter.entity.StudentAgent">
    update r_student_agent
    <set>
      <if test="fkStudentId != null">
        fk_student_id = #{fkStudentId,jdbcType=BIGINT},
      </if>
      <if test="fkAgentId != null">
        fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=BIT},
      </if>
      <if test="activeDate != null">
        active_date = #{activeDate,jdbcType=TIMESTAMP},
      </if>
      <if test="unactiveDate != null">
        unactive_date = #{unactiveDate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.salecenter.entity.StudentAgent">
    update r_student_agent
    set fk_student_id = #{fkStudentId,jdbcType=BIGINT},
      fk_agent_id = #{fkAgentId,jdbcType=BIGINT},
      is_active = #{isActive,jdbcType=BIT},
      active_date = #{activeDate,jdbcType=TIMESTAMP},
      unactive_date = #{unactiveDate,jdbcType=TIMESTAMP},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>