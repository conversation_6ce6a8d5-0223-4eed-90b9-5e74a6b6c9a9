<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.mail.MMailAccountMapper">
    <select id="selectAccount" resultType="com.get.job.dao.mail.MailAccount">
        SELECT account.id                  AS id,
               account.email_account       As emailAccount,
               account.email_password      AS emailPassword,
               account.email_type          AS emailType,
               account.gmt_create          AS gmtCreate,
               account.fk_platform_code    AS fkPlatformCode,
               account.fk_platform_user_id AS fkPlatformUserId,
               account.gmt_create_user     AS gmtCreateUser
        FROM m_mail_account AS account
<!--        where id = 106-->
    </select>
</mapper>