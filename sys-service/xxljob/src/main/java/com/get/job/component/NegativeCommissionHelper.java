package com.get.job.component;

import com.get.common.eunms.ProjectExtraEnum;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.entity.PayablePlanSettlementInstallment;
import com.get.job.dao.sale.PayablePlanMapper;
import com.get.job.dao.sale.PayablePlanSettlementInstallmentMapper;
import com.get.job.dao.sale.PayablePlanSettlementStatusMapper;
import com.get.job.dto.NegativeCommissionDto;
import com.get.financecenter.entity.PayablePlanSettlementStatus;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 负数佣金导入帮助类
 *
 * <AUTHOR>
 * @date 2021/12/10 11:33
 */
@Component
public class NegativeCommissionHelper {
    @Resource
    private PayablePlanMapper payablePlanMapper;
    @Resource
    private PayablePlanSettlementInstallmentMapper payablePlanSettlementInstallmentMapper;
    @Resource
    private PayablePlanSettlementStatusMapper payablePlanSettlementStatusMapper;
    @Resource
    private UtilService utilService;

    /**
     * 负数佣金定时任务
     *
     * @Date 17:22 2024/7/11
     * <AUTHOR>
     */
    public void negativeCommission() {
        List<NegativeCommissionDto> negativeCommissionDtoList = payablePlanMapper.getNegativeCommission();
        if (GeneralTool.isNotEmpty(negativeCommissionDtoList)) {
            for (NegativeCommissionDto negativeCommissionDto : negativeCommissionDtoList) {
                PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
                payablePlanSettlementInstallment.setFkPayablePlanId(negativeCommissionDto.getId());
                payablePlanSettlementInstallment.setFkReceiptFormItemId(0L);
                payablePlanSettlementInstallment.setAmountExpect(negativeCommissionDto.getDifferenceAmount());
                payablePlanSettlementInstallment.setAmountActual(negativeCommissionDto.getDifferenceAmount());
                payablePlanSettlementInstallment.setAmountActualInit(negativeCommissionDto.getDifferenceAmount());
                payablePlanSettlementInstallment.setServiceFeeExpect(BigDecimal.ZERO);
                payablePlanSettlementInstallment.setServiceFeeActual(BigDecimal.ZERO);
                payablePlanSettlementInstallment.setServiceFeeActualInit(BigDecimal.ZERO);
                payablePlanSettlementInstallment.setRollBack(false);
                payablePlanSettlementInstallment.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
                payablePlanSettlementInstallment.setGmtCreate(new Date());
                payablePlanSettlementInstallment.setGmtCreateUser("负数定时任务");
                payablePlanSettlementInstallmentMapper.insert(payablePlanSettlementInstallment);

                PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
                payablePlanSettlementStatus.setFkPayablePlanId(negativeCommissionDto.getId());
                payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(payablePlanSettlementInstallment.getId());
                payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                payablePlanSettlementStatus.setGmtCreate(new Date());
                payablePlanSettlementStatus.setGmtCreateUser("负数定时任务");
                payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);
            }
        }
    }

}
