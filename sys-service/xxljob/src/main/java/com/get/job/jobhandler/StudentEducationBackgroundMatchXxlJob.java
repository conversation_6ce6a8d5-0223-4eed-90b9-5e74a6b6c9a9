package com.get.job.jobhandler;

import com.get.job.service.sale.IStudentEducationBackgroundMatchService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * @author: Hardy
 * @create: 2023/12/25 12:27
 * @verison: 1.0
 * @description:
 */
@Component
public class StudentEducationBackgroundMatchXxlJob {

    private static final Logger logger = LoggerFactory.getLogger(StudentEducationBackgroundMatchXxlJob.class);

    @Resource
    private IStudentEducationBackgroundMatchService studentEducationBackgroundMatchService;


    @XxlJob("studentEducationBackgroundMatchJob")
    private void studentEducationBackgroundMatchJob() {
        try {
            XxlJobHelper.log("匹配学生国内教育背景学校定时任务开始!");
            studentEducationBackgroundMatchService.matchStudentEducationBackground();
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[匹配学生国内教育背景学校 error]");
            logger.error("func[{}] e[{}-{}] desc[匹配学生国内教育背景学校 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }
    }



}
