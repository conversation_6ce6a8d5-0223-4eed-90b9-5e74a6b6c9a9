package com.get.job.dao.institution;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionCourseRpaMapping;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
@DS("institutiondb")
public interface InstitutionCourseRpaMappingMapper extends BaseMapper<InstitutionCourseRpaMapping> {
    int insert(InstitutionCourseRpaMapping record);

    int insertSelective(InstitutionCourseRpaMapping record);

    int updateByPrimaryKeySelective(InstitutionCourseRpaMapping record);

    int updateByPrimaryKey(InstitutionCourseRpaMapping record);

    /**
     * 查询课程信息
     *
     * @Date 16:51 2021/9/13
     * <AUTHOR>
     */
    List<InstitutionCourseRpaMapping> selectInstitutionCourseRpaMappings(@Param("outdated") String outdated, @Param("schId") Long schId,
                                                                         @Param("parentIdFlag") boolean parentIdFlag,
                                                                         @Param("schSet") Set<Integer> schSet);

    List<InstitutionCourseRpaMapping> selectInstitutionCourseRpaMappingsByType();


}