package com.get.job.utils;

import lombok.experimental.UtilityClass;

import java.util.regex.Pattern;

/**
 * @author: Hardy
 * @create: 2022/3/14 10:41
 * @verison: 1.0
 * @description:
 */
@UtilityClass
public class MyStringUtils {

    public static boolean isNumeric(String str) {
        Pattern pattern = Pattern.compile("[0-9]*");
        return pattern.matcher(str).matches();
    }


    /**
     * 提取中文字符串
     * @param source
     * @return
     */
    public String getChineseString(String source){
        return source.replaceAll("\\s*","").replaceAll("[^(\\u4e00-\\u9fa5)]","");
    }

    /**
     * 获取英文字符串
     * @param source
     * @return
     */
    public String getEnglishString(String source){
        return source.replaceAll("\\s*","").replaceAll("[(\\u4e00-\\u9fa5)]","");
    }


//    public static void main(String[] args) {
//        System.out.println("isNumeric(\"蔡东\") = " + isNumeric("4654蔡东"));
//    }
}
