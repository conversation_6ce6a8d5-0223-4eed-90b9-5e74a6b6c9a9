package com.get.job.jobhandler;

import com.get.salecenter.feign.ISaleCenterClient;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class SaleXxJob {

    @Resource
    private ISaleCenterClient iSaleCenterClient;


    @XxlJob(value = "dataImport")
    public void dataImport(){
        log.info("开始导入财务数据");
        iSaleCenterClient.dataImport();
        log.info("请求结束");
    }

    @XxlJob(value = "dataImportItemEmail")
    public void dataImportItemEmail(){
        log.info("开始导入截至时间数据");
        //iSaleCenterClient.dataImportItemEmail();
        log.info("请求结束");
    }

    /**
     * 发送代理合同未签署提醒邮件定时任务
     */
    @XxlJob(value = "sendAgentContractUnsignedReminders")
    public void sendAgentContractUnsignedReminders(){
        log.info("开始执行代理合同未签署提醒邮件发送任务");
        try {
            iSaleCenterClient.sendAgentContractUnsignedReminders();
            log.info("代理合同未签署提醒邮件发送任务执行完成");
        } catch (Exception e) {
            log.error("代理合同未签署提醒邮件发送任务执行异常", e);
            throw e;
        }
    }

//    @XxlJob(value = "analyzeOffer")
//    public void analyzeOffer(){
//        log.info("开始解析offer");
//        iSaleCenterClient.analyzeOffer();
//        log.info("请求结束");
//    }
}
