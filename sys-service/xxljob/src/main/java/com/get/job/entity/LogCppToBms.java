package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("log_cpp_to_bms")
public class LogCppToBms extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * cpp学生Id
     */
    @ApiModelProperty(value = "cpp学生Id")
    @Column(name = "cpp_student_id")
    private Long cppStudentId;
    /**
     * cpp国家Id
     */
    @ApiModelProperty(value = "cpp国家Id")
    @Column(name = "cpp_area_country_id")
    private Long cppAreaCountryId;
    /**
     * cpp学习计划Id（bms学习方案）
     */
    @ApiModelProperty(value = "cpp学习计划Id（bms学习方案）")
    @Column(name = "cpp_student_offer_id")
    private Long cppStudentOfferId;
    /**
     * cpp学习阶段Id（bms学习计划）
     */
    @ApiModelProperty(value = "cpp学习阶段Id（bms学习计划）")
    @Column(name = "cpp_student_offer_item_id")
    private String cppStudentOfferItemId;
    /**
     * cpp学校Id
     */
    @ApiModelProperty(value = "cpp学校Id")
    @Column(name = "cpp_institution_id")
    private Long cppInstitutionId;
    /**
     * cpp学校课程名称
     */
    @ApiModelProperty(value = "cpp学校课程名称")
    @Column(name = "cpp_institution_course_name")
    private String cppInstitutionCourseName;
    /**
     * bms学生Id
     */
    @ApiModelProperty(value = "bms学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * bms国家Id
     */
    @ApiModelProperty(value = "bms国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * bms学习方案Id
     */
    @ApiModelProperty(value = "bms学习方案Id")
    @Column(name = "fk_student_offer_id")
    private Long fkStudentOfferId;
    /**
     * bms学习计划Id
     */
    @ApiModelProperty(value = "bms学习计划Id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;
    /**
     * bms学校Id
     */
    @ApiModelProperty(value = "bms学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * bms学校课程Id
     */
    @ApiModelProperty(value = "bms学校课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * bms自定义课程Id
     */
    @ApiModelProperty(value = "bms自定义课程Id")
    @Column(name = "fk_institution_course_custom_id")
    private Long fkInstitutionCourseCustomId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 状态：失败0/成功1
     */
    @ApiModelProperty(value = "状态：失败0/成功1")
    @Column(name = "status")
    private Integer status;
}