package com.get.job.dao.institution;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionCourse;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("institutiondb")
public interface InstitutionCourseMapper extends BaseMapper<InstitutionCourse> {
    int insert(InstitutionCourse record);

    int insertSelective(InstitutionCourse record);

    int updateByPrimaryKeySelective(InstitutionCourse record);

    int updateByPrimaryKeyWithBLOBs(InstitutionCourse record);

    int updateByPrimaryKey(InstitutionCourse record);
}