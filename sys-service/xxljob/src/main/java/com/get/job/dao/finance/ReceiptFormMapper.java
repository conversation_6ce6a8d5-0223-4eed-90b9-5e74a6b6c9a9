package com.get.job.dao.finance;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.ReceiptForm;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("financedb")
public interface ReceiptFormMapper extends BaseMapper<ReceiptForm> {
    int insert(ReceiptForm record);

    int insertSelective(ReceiptForm record);
}