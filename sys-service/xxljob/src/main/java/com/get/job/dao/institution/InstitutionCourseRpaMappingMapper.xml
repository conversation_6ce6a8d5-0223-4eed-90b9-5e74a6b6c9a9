<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.institution.InstitutionCourseRpaMappingMapper">
    <resultMap id="BaseResultMap" type="com.get.institutioncenter.entity.InstitutionCourseRpaMapping">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="sch_id" jdbcType="INTEGER" property="schId"/>
        <result column="gb_sch_name" jdbcType="VARCHAR" property="gbSchName"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="key" jdbcType="VARCHAR" property="key"/>
        <result column="value" jdbcType="VARCHAR" property="value"/>
        <result column="parent_id" jdbcType="INTEGER" property="parentId"/>
        <result column="step_level" jdbcType="INTEGER" property="stepLevel"/>
        <result column="branch" jdbcType="VARCHAR" property="branch"/>
        <result column="course_name" jdbcType="VARCHAR" property="courseName"/>
        <result column="qualifications" jdbcType="VARCHAR" property="qualifications"/>
        <result column="web" jdbcType="VARCHAR" property="web"/>
        <result column="code" jdbcType="VARCHAR" property="code"/>
        <result column="outdated" jdbcType="VARCHAR" property="outdated"/>
        <result column="createtime" jdbcType="TIMESTAMP" property="createtime"/>
        <result column="udpatetime" jdbcType="TIMESTAMP" property="udpatetime"/>
    </resultMap>
    <insert id="insert" parameterType="com.get.institutioncenter.entity.InstitutionCourseRpaMapping">
        insert into m_institution_course_rpa_mapping (id, sch_id, gb_sch_name,
                                                      name, key, value, parent_id,
                                                      step_level, branch, course_name,
                                                      qualifications, web, code,
                                                      outdated, createtime, udpatetime)
        values (#{id,jdbcType=INTEGER}, #{schId,jdbcType=INTEGER}, #{gbSchName,jdbcType=VARCHAR},
                #{name,jdbcType=VARCHAR}, #{key,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR},
                #{parentId,jdbcType=INTEGER},
                #{stepLevel,jdbcType=INTEGER}, #{branch,jdbcType=VARCHAR}, #{courseName,jdbcType=VARCHAR},
                #{qualifications,jdbcType=VARCHAR}, #{web,jdbcType=VARCHAR}, #{code,jdbcType=VARCHAR},
                #{outdated,jdbcType=VARCHAR}, #{createtime,jdbcType=TIMESTAMP}, #{udpatetime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.get.institutioncenter.entity.InstitutionCourseRpaMapping">
        insert into m_institution_course_rpa_mapping
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="schId != null">
                sch_id,
            </if>
            <if test="gbSchName != null">
                gb_sch_name,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="key != null">
                key,
            </if>
            <if test="value != null">
                value,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
            <if test="stepLevel != null">
                step_level,
            </if>
            <if test="branch != null">
                branch,
            </if>
            <if test="courseName != null">
                course_name,
            </if>
            <if test="qualifications != null">
                qualifications,
            </if>
            <if test="web != null">
                web,
            </if>
            <if test="code != null">
                code,
            </if>
            <if test="outdated != null">
                outdated,
            </if>
            <if test="createtime != null">
                createtime,
            </if>
            <if test="udpatetime != null">
                udpatetime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=INTEGER},
            </if>
            <if test="schId != null">
                #{schId,jdbcType=INTEGER},
            </if>
            <if test="gbSchName != null">
                #{gbSchName,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="key != null">
                #{key,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                #{value,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                #{parentId,jdbcType=INTEGER},
            </if>
            <if test="stepLevel != null">
                #{stepLevel,jdbcType=INTEGER},
            </if>
            <if test="branch != null">
                #{branch,jdbcType=VARCHAR},
            </if>
            <if test="courseName != null">
                #{courseName,jdbcType=VARCHAR},
            </if>
            <if test="qualifications != null">
                #{qualifications,jdbcType=VARCHAR},
            </if>
            <if test="web != null">
                #{web,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                #{code,jdbcType=VARCHAR},
            </if>
            <if test="outdated != null">
                #{outdated,jdbcType=VARCHAR},
            </if>
            <if test="createtime != null">
                #{createtime,jdbcType=TIMESTAMP},
            </if>
            <if test="udpatetime != null">
                #{udpatetime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.get.institutioncenter.entity.InstitutionCourseRpaMapping">
        update m_institution_course_rpa_mapping
        <set>
            <if test="schId != null">
                sch_id = #{schId,jdbcType=INTEGER},
            </if>
            <if test="gbSchName != null">
                gb_sch_name = #{gbSchName,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="key != null">
                key = #{key,jdbcType=VARCHAR},
            </if>
            <if test="value != null">
                value = #{value,jdbcType=VARCHAR},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=INTEGER},
            </if>
            <if test="stepLevel != null">
                step_level = #{stepLevel,jdbcType=INTEGER},
            </if>
            <if test="branch != null">
                branch = #{branch,jdbcType=VARCHAR},
            </if>
            <if test="courseName != null">
                course_name = #{courseName,jdbcType=VARCHAR},
            </if>
            <if test="qualifications != null">
                qualifications = #{qualifications,jdbcType=VARCHAR},
            </if>
            <if test="web != null">
                web = #{web,jdbcType=VARCHAR},
            </if>
            <if test="code != null">
                code = #{code,jdbcType=VARCHAR},
            </if>
            <if test="outdated != null">
                outdated = #{outdated,jdbcType=VARCHAR},
            </if>
            <if test="createtime != null">
                createtime = #{createtime,jdbcType=TIMESTAMP},
            </if>
            <if test="udpatetime != null">
                udpatetime = #{udpatetime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.institutioncenter.entity.InstitutionCourseRpaMapping">
        update m_institution_course_rpa_mapping
        set sch_id         = #{schId,jdbcType=INTEGER},
            gb_sch_name    = #{gbSchName,jdbcType=VARCHAR},
            name           = #{name,jdbcType=VARCHAR},
            key            = #{key,jdbcType=VARCHAR},
            value          = #{value,jdbcType=VARCHAR},
            parent_id      = #{parentId,jdbcType=INTEGER},
            step_level     = #{stepLevel,jdbcType=INTEGER},
            branch         = #{branch,jdbcType=VARCHAR},
            course_name    = #{courseName,jdbcType=VARCHAR},
            qualifications = #{qualifications,jdbcType=VARCHAR},
            web            = #{web,jdbcType=VARCHAR},
            code           = #{code,jdbcType=VARCHAR},
            outdated       = #{outdated,jdbcType=VARCHAR},
            createtime     = #{createtime,jdbcType=TIMESTAMP},
            udpatetime     = #{udpatetime,jdbcType=TIMESTAMP}
        where id = #{id,jdbcType=INTEGER}
    </update>
    <select id="selectInstitutionCourseRpaMappings"
            resultMap="BaseResultMap">
        SELECT a.*
        FROM m_institution_course_rpa_mapping AS a
        WHERE a.outdated = #{outdated}
        AND a.sch_id IS NOT NULL
        AND trim(a.sch_id) != ''
        AND a.course_identification ='institution_course'
        AND IFNULL(a.value,'') != 'N/A'
        <if test="schId != null">
            AND a.sch_id = #{schId}
        </if>
        <if test="schSet != null and schSet.size() > 0">
            AND a.sch_id NOT IN
            <foreach collection="schSet" item="schId" index="index" open="(" separator="," close=")">
                #{schId}
            </foreach>
        </if>
        order by a.sch_id
    </select>
    <select id="selectInstitutionCourseRpaMappingsByType"
            resultType="com.get.institutioncenter.entity.InstitutionCourseRpaMapping">
SELECT id,sch_id,`value` FROM `m_institution_course_rpa_mapping`  where course_identification ='institution_course' and outdated='F';

    </select>
</mapper>