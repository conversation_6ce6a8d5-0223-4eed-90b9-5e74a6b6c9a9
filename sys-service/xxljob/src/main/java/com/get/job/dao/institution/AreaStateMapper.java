package com.get.job.dao.institution;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.AreaState;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("institutiondb")
public interface AreaStateMapper extends BaseMapper<AreaState> {
    int insert(AreaState record);

    int insertSelective(AreaState record);

    int updateByPrimaryKeySelective(AreaState record);

    int updateByPrimaryKey(AreaState record);
}