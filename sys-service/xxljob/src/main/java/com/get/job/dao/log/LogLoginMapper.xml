<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.get.job.dao.log.LogLoginMapper">


    <select id="getYesterdayLoginLogList" resultType="com.get.core.log.model.LogLogin">
        SELECT
            id,
            fk_staff_id,
            session_id,
            login_time
        FROM
            log_login
        WHERE
            logout_time IS NULL
        AND session_id IS NOT NULL
       <!-- AND login_time BETWEEN DATE_SUB(CURDATE(), INTERVAL 1 DAY)
        AND CURDATE() - INTERVAL 1 SECOND; -->
    </select>
</mapper>
