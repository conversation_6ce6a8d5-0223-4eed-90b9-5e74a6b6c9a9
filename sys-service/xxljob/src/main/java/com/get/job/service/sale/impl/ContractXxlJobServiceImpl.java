package com.get.job.service.sale.impl;

import com.alibaba.fastjson.JSONObject;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.GetDateUtil;
import com.get.core.start.constant.AppConstant;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.job.service.sale.IContractXxlJobService;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.vo.AgentContractVo;
import com.get.salecenter.feign.ISaleCenterClient;
import com.xxl.job.core.context.XxlJobHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2022/11/7 10:57
 * @verison: 1.0
 * @description:
 */
@Slf4j
@Service
public class ContractXxlJobServiceImpl implements IContractXxlJobService {

    @Resource
    private ISaleCenterClient saleCenterClient;

    @Resource
    private IReminderCenterClient reminderCenterClient;

    @Resource
    private IPermissionCenterClient permissionCenterClient;


    /**
     *
     * @return
     */
    @Override
    public Boolean addContractRemind() {

        //获取需要添加提醒的合同
        List<AgentContractVo> agentContracts = doGetAgentContractNeedRemmind();

        //添加提醒
        try {
            doAddAgentContractRemind(agentContracts);
        }catch (Exception e){
            XxlJobHelper.log("XXL-JOB, AontractTaskJobHandler remind add fail...");
            return false;
        }

        return true;
    }


    /**
     * 添加提醒队列
     *
     * @param agentContracts
     */
    private void doAddAgentContractRemind(List<AgentContractVo> agentContracts) throws Exception{

        if (GeneralTool.isNotEmpty(agentContracts)){
            Set<Long> agentIds = agentContracts.stream().map(AgentContractVo::getFkAgentId).collect(Collectors.toSet());
            Map<Long, String> agentNameMap = saleCenterClient.getAgentNamesByIds(agentIds).getData();

            Result<Map<Long, List<Long>>> bdIdByAgentIds = saleCenterClient.getBdIdByAgentIds(agentIds);
            if (!bdIdByAgentIds.isSuccess()||GeneralTool.isEmpty(bdIdByAgentIds.getData())){
                return;
            }
//            Set<String> users = agentContracts.stream().map(AgentContractVo::getGmtCreateUser).filter(Objects::nonNull).collect(Collectors.toSet());
//            Result<List<StaffVo>> staffByCreateUsers = permissionCenterClient.getStaffByCreateUsers(users);
//            if (!staffByCreateUsers.isSuccess()||GeneralTool.isEmpty(staffByCreateUsers.getData())){
//                return;
//            }

            String domainName = "";
            Properties props = System.getProperties();
            String profile = props.getProperty("spring.profiles.active");
            if (GeneralTool.isNotEmpty(profile)) {
                Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.FILE_SRC_PREFIX.key, 2).getData();
                Long companyId = profile.equals(AppConstant.PROD_CODE) ? 2L : 3L;
                String configValue2 = companyConfigMap.get(companyId);
                if (GeneralTool.isNotEmpty(configValue2)){
                    domainName = configValue2;
                } else {
                    Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
                    if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
                        domainName = domainNameResult.getData();
                    }
                }

//                if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)|| profile.equals(AppConstant.TW_CODE)) {
//                    ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
//                    if (GeneralTool.isNotEmpty(configDto)){
//                        String configJson = configDto.getValue2();
//                        JSONObject configJsonObject = JSON.parseObject(configJson);
//                        if (profile.equals(AppConstant.IAE_CODE)){
//                            domainName = configJsonObject.getString("IAE");
//                        }else {
//                            domainName = configJsonObject.getString("OTHER");
//                        }
//                    }
//                }else {
//                    Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
//                    if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
//                        domainName = domainNameResult.getData();
//                    }
//                }

            }

            Map<Long, List<Long>> bdIdsMap = bdIdByAgentIds.getData();
//            List<StaffVo> staffDtos = staffByCreateUsers.getData();
//            Map<String, Long> staffIdMap = staffDtos.stream().collect(HashMap::new, (m, v) -> m.put(v.getLoginId(), v.getId()), HashMap::putAll);
            List<RemindTaskDto> remindTaskVos = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            for (AgentContractVo agentContract : agentContracts) {
                if (GeneralTool.isNotEmpty(bdIdsMap)&&GeneralTool.isNotEmpty(bdIdsMap.get(agentContract.getFkAgentId()))){
                    List<Long> bdIds = bdIdsMap.get(agentContract.getFkAgentId());
                    if (GeneralTool.isNotEmpty(bdIds)){
                        for (Long bdId : bdIds) {
                            //TODO CUSTOM 邮件
                            RemindTaskDto remindTaskVo = new RemindTaskDto();
                            String title = "【代理合同到期提醒】-【"+agentContract.getContractNum()+"】"+agentNameMap.get(agentContract.getFkAgentId());
                            String startTime = GeneralTool.isNotEmpty(agentContract.getStartTime())?sdf.format(agentContract.getStartTime()):"未设置开始时间";
                            String endTime = sdf.format(agentContract.getEndTime());
                            String taskRemark = "合同有效时间：<span style=\"color: #f7883a;\">"+startTime+"至"+endTime+"</span><br>" +
                                    "<a href=\""+domainName+"/sales-center_agent_contract-management_contract-detail/"+agentContract.getId()+"?tabtype=account&companyid="+agentContract.getFkcompanyId()+"\">点击查看详情</a>";
                            remindTaskVo.setTaskTitle(title);
                            remindTaskVo.setTaskRemark(taskRemark);
                            //邮件方式发送
                            remindTaskVo.setRemindMethod("1");
                            //默认设置执行中
                            remindTaskVo.setStatus(1);
                            //默认背景颜色
                            remindTaskVo.setFkRemindEventTypeKey("CUSTOM");
                            remindTaskVo.setTaskBgColor("#3788d8");
                            remindTaskVo.setFkStaffId(bdId);
                            remindTaskVo.setStartTime(new Date());
                            remindTaskVo.setAdvanceDays("0");
                            remindTaskVo.setFkTableName(TableEnum.SALE_CONTRACT.key);
                            remindTaskVo.setFkTableId(agentContract.getId());
                            remindTaskVo.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
                            remindTaskVos.add(remindTaskVo);
                        }
                    }
                }
            }

            log.info("==========================================={}",remindTaskVos.get(0).getTaskRemark());
            reminderCenterClient.batchAdd(remindTaskVos);
        }

    }

    /**
     * 获取需要添加提醒的合同
     *
     * @return
     */
    private List<AgentContractVo> doGetAgentContractNeedRemmind() {
        //默认配置
        String type = "week";
        Integer count = 4;

        //获取系统参数 得设置下  获取那天0点的时间
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date date = calendar.getTime();

        ConfigVo configVo = permissionCenterClient.getConfigByKey(ProjectKeyEnum.AGENT_CONTRACT_CONFIG.key).getData();
        if (GeneralTool.isNotEmpty(configVo) && GeneralTool.isNotEmpty(configVo.getValue1())){
            String value1 = configVo.getValue1();
            JSONObject jsonObject = JSONObject.parseObject(value1);
            type = jsonObject.getString("type");
            count = jsonObject.getInteger("count");
        }

        if ("week".equals(type)){
            date = GetDateUtil.getDateAfterWeeks(date,count*(-1));
        }
        if ("day".equals(type)){
            date = GetDateUtil.getDateAfterDays(date,count*(-1));
        }
        if ("month".equals(type)){
            date = GetDateUtil.getDateAfterMonths(date,count*(-1));
        }
        if ("year".equals(type)){
            date = GetDateUtil.getDateAfterYears(date,count*(-1));
        }

        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formatDate = sf.format(date);
        Result<List<AgentContractVo>> result = saleCenterClient.getAgentContractsByEndTime(formatDate);
        if (result.isSuccess()&& GeneralTool.isNotEmpty(result.getData())){
            return result.getData();
        }
        return null;
    }

}
