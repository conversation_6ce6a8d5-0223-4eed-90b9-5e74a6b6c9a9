package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.MediaAndAttached;
import com.get.salecenter.entity.SaleMediaAndAttached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("saledb")
public interface SaleMediaAndAttachedMapper extends BaseMapper<SaleMediaAndAttached> {
    int insert(MediaAndAttached record);

    int insertSelective(MediaAndAttached record);

    int updateByPrimaryKeySelective(MediaAndAttached record);

    int updateByPrimaryKey(MediaAndAttached record);

    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);

}