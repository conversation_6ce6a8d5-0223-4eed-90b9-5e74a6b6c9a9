package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.PayablePlanSettlementInstallment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

@Mapper
@DS("financedb")
public interface PayablePlanSettlementInstallmentMapper extends BaseMapper<PayablePlanSettlementInstallment> {

    /**
     * 获取该应付计划分期表各状态已生成的实付总金额
     *
     * @Date 16:30 2022/4/20
     * <AUTHOR>
     */
    BigDecimal getInsertSettlementAmountActual(@Param("payablePlanId") Long payablePlanId);

    /**
     * 获取该应付计划之前已生成的预付金额
     *
     * @Date 16:30 2022/4/20
     * <AUTHOR>
     */
    BigDecimal getInsertSettlementPrepaidAmount(@Param("payablePlanId") Long payablePlanId);

    /**
     * 获取该应付计划非实付总金额
     *
     * @Date 16:30 2022/4/20
     * <AUTHOR>
     */
    BigDecimal getInsertSettlementNotPrepaidAmount(@Param("payablePlanId") Long payablePlanId);

    /**
     * 该应付计划已产生的分期表数据的非预付金额 算出被预付对冲掉的金额
     *
     * @Date 17:12 2022/4/22
     * <AUTHOR>
     */
    BigDecimal getInsertSettlementPrepaidHedgingAmount(@Param("payablePlanId") Long payablePlanId);

    /**
     * 该应付计划已产生的分期表数据的非预付手续费金额 算出被预付对冲掉的手续金额
     *
     * @Date 17:12 2022/4/22
     * <AUTHOR>
     */
    BigDecimal getInsertSettlementPrepaidHedgingServiceFeeAmount(@Param("payablePlanId") Long payablePlanId);

    /**
     * 获取需要回退佣金的应付计划
     *
     * @Date 15:23 2022/7/19
     * <AUTHOR>
     */
    List<PayablePlanSettlementInstallment> getInvalidCommission();
}