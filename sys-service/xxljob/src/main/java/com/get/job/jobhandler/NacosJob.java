package com.get.job.jobhandler;

import com.get.job.service.nacos.INacosJobService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

@Component
public class NacosJob {
    private static final Logger logger = LoggerFactory.getLogger(NacosJob.class);
    @Resource
    private INacosJobService nacosJobService;

    @Value("${nacos.bms.username}")
    private String bmsUserName;
    @Value("${nacos.bms.pwd}")
    private String bmsPWD;

    @Value("${nacos.app.username}")
    private String appUserName;
    @Value("${nacos.app.pwd}")
    private String appPWD;

    /**
     * 定时备份GEA-nacos配置文件
     */
    @XxlJob("downNacosConfigFileFromGea")
    private void downNacosConfigFileFromGea() {
        try {
            XxlJobHelper.log("定时备份GEA-nacos配置文件任务开始");
            nacosJobService.nacosBackup("http://172.19.0.2:18848",bmsUserName,bmsPWD,"nacos_gea_prd");
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[定时备份GEA-nacos配置文件 error]");
            logger.error("func[{}] e[{}-{}] desc[定时备份GEA-nacos配置文件 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));

        }
    }
    /**
     * 定时备份APP-nacos配置文件
     */
    @XxlJob("downNacosConfigFileFromApp")
    private void downNacosConfigFileFromApp() {
        try {
            XxlJobHelper.log("定时备份app-nacos配置文件任务开始");
            nacosJobService.nacosBackup("http://172.19.0.2:28848",appUserName,appPWD,"nacos_app_prd");
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[定时备份app-nacos配置文件 error]");
            logger.error("func[{}] e[{}-{}] desc[定时备份app-nacos配置文件 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));

        }
    }
    /**
     * 定时备份IAE-nacos配置文件
     */
    @XxlJob("downNacosConfigFileFromIae")
    private void downNacosConfigFileFromIae() {
        try {
            XxlJobHelper.log("定时备份iae-nacos配置文件任务开始");
            nacosJobService.nacosBackup("http://172.17.0.9:18848",bmsUserName,bmsPWD,"nacos_iae_prd");
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[定时备份iae-nacos配置文件 error]");
            logger.error("func[{}] e[{}-{}] desc[定时备份iae-nacos配置文件 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }
    }
    /**
     * 定时备份IAE-app-nacos配置文件
     */
    @XxlJob("downNacosConfigFileFromIaeApp")
    private void downNacosConfigFileFromIaeApp() {
        try {
            XxlJobHelper.log("定时备份iae-app-nacos配置文件任务开始");
            nacosJobService.nacosBackup("http://172.17.0.9:28848",appUserName,appPWD,"nacos_iae_prd_app");
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[定时备份iae-app-nacos配置文件 error]");
            logger.error("func[{}] e[{}-{}] desc[定时备份iae-app-nacos配置文件 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }
    }
    /**
     * 定时备份TW-nacos配置文件
     */
    @XxlJob("downNacosConfigFileFromTW")
    private void downNacosConfigFileFromTW() {
        try {
            XxlJobHelper.log("定时备份TW-nacos配置文件任务开始");
            nacosJobService.nacosBackup("http://172.19.0.8:18848",bmsUserName,bmsPWD,"nacos_tw_prd");
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[定时备份TW-nacos配置文件 error]");
            logger.error("func[{}] e[{}-{}] desc[定时备份TW-nacos配置文件 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));

        }
    }
}
