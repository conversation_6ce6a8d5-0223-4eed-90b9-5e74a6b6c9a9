package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("saledb")
public interface StudentProjectRoleStaffMapper extends BaseMapper<StudentProjectRoleStaff> {
    int insert(StudentProjectRoleStaff record);

    int insertSelective(StudentProjectRoleStaff record);

    int updateByPrimaryKeySelective(StudentProjectRoleStaff record);

    int updateByPrimaryKey(StudentProjectRoleStaff record);
}