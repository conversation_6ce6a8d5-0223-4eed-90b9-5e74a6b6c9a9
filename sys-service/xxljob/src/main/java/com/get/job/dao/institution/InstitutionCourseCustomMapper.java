package com.get.job.dao.institution;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionCourseCustom;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("institutiondb")
public interface InstitutionCourseCustomMapper extends BaseMapper<InstitutionCourseCustom> {
    int insert(InstitutionCourseCustom record);

    int insertSelective(InstitutionCourseCustom record);

    int updateByPrimaryKeySelective(InstitutionCourseCustom record);

    int updateByPrimaryKey(InstitutionCourseCustom record);
}