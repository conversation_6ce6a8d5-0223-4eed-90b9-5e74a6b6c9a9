package com.get.job.jobhandler;

import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Time: 16:23
 * Date: 2022/3/31
 * Description:
 */
@Component
public class GetDayOfStaffBirthdayXxlJob {
    private static final Logger logger = LoggerFactory.getLogger(GetDayOfStaffBirthdayXxlJob.class);

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @XxlJob("GetDayOfStaffBirthdayHandler")
    public void GetDayOfStaffBirthday() {
        try {
            permissionCenterClient.getDayOfStaffBirthday();
        } catch (Exception e) {
            XxlJobHelper.log("获取当天生日的员工失败-------------------------", e);
        }
    }
}
