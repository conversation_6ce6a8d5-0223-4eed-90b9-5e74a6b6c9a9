package com.get.job.dao.institution;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.Institution;
import com.get.institutioncenter.vo.InstitutionProviderContractReminderVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
@DS("institutiondb")
public interface InstitutionMapper extends BaseMapper<Institution> {
    int insert(Institution record);

    int insertSelective(Institution record);

    int updateByPrimaryKeySelective(Institution record);

    int updateByPrimaryKeyWithBLOBs(Institution record);

    int updateByPrimaryKey(Institution record);

    List<InstitutionProviderContractReminderVo> getProvidersWithContractsExpiringInDays(int day);


    /**
     * 查找匹配的学校
     *
     * @Date 11:00 2021/9/29
     * <AUTHOR>
     */
    @Select("SELECT * FROM m_institution WHERE FIND_IN_SET(#{idGea}, id_gea) AND is_active = 1")
    List<Institution> selectMatchingInstitutions(@Param("idGea") String idGea);

    /**
     * 按照中英文查找学校
     * @param chineseName
     * @param englishName
     * @return
     */
    Institution selectInstitutionByNameChnOrNameEng(@Param("chineseName") String chineseName, @Param("englishName")String englishName,@Param("fkInstitutionNameEducation")String fkInstitutionNameEducation);
}