package com.get.job.controller.mail;

import com.get.common.result.ResponseBo;
import com.get.job.jobhandler.LLMAnalysisMail;
import com.get.job.service.mail.impl.MailAnalysisService;
import com.get.job.service.mail.impl.SyncEmailStatusServiceImpl;
import com.get.job.utils.LLMAnalysis;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Api(tags = "同步邮箱")
@RestController
@RequestMapping("schedule/mail")
public class MailSyncController {
    @Resource
    private SyncEmailStatusServiceImpl syncEmailStatusService;
    @Resource
    private MailAnalysisService mailAnalysisService;

    @Resource
    private LLMAnalysisMail llmAnalysisMail;

    @GetMapping("/syncMail")
    public ResponseBo fetchMail() throws Exception {
        syncEmailStatusService.syncEmailStatus();
        return ResponseBo.ok();
    }

    @GetMapping("/lLMAnalysis")
    public ResponseBo lLMAnalysis() throws Exception {
        llmAnalysisMail.syncMailJob();
//        mailAnalysisService.mailAnalysis();
        return ResponseBo.ok();
    }
}
