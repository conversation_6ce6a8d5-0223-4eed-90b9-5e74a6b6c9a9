package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.StudentOfferItem;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("saledb")
public interface StudentOfferItemMapper extends BaseMapper<StudentOfferItem> {
    @Override
    int insert(StudentOfferItem record);

    int insertSelective(StudentOfferItem record);

    List<String> selectWithNotNullIssueId();

    void updateByAplOrder(StudentOfferItem studentOfferItem);

    void updateByAplOrderBatch(@Param("studentOfferItems") List<StudentOfferItem> studentOfferItems);

    /**
     * 批量修改RPA运行状态
     * @param studentOfferItems
     */
    void updateIssueRpaStatusBatch(@Param("studentOfferItems")List<StudentOfferItem> studentOfferItems);

    /**
     *  同步修改新旧issue  RPA运行状态
     * @param studentOfferItem
     */
    void updateIssueRpaStatus(@Param("studentOfferItem") StudentOfferItem studentOfferItem);
}