package com.get.job.jobhandler;

import com.get.core.tool.utils.GeneralTool;
import com.get.job.service.iaecrm.IIaeCrmSynService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.util.Arrays;
import java.util.List;

@Component
public class IaeCrmJob {

    private static final Logger logger = LoggerFactory.getLogger(IaeCrmJob.class);
    @Resource
    private IIaeCrmSynService iaeCrmSynService;


    /**
     * 权限中心IAE-OSS
     * fkTableNames:m_staff,m_staff_contract等
     */
    @XxlJob("synMAttachmentMethodJob")
    private void synMAttachmentMethodJob() {
        try {
            String fkTableNames = XxlJobHelper.getJobParam();//获取任务参数
            XxlJobHelper.log("同步IAE权限中心文件库的MAttachment表定时任务开始："+fkTableNames);
            iaeCrmSynService.synMAttachmentMethod(fkTableNames);
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[同步IAE权限中心文件库的MAttachment表 error]");
            logger.error("func[{}] e[{}-{}] desc[同步IAE权限中心文件库的MAttachment表 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));

        }
    }

    /**
     * 销售中心IAE-OSS
     */
    @XxlJob("synIaeSaleMAttachmentMethodJob")
    private void synIaeSaleMAttachmentMethodJob() {
        try {
            XxlJobHelper.log("同步IAE销售文件库的MAttachment表定时任务开始=》");
            iaeCrmSynService.synIaeSaleMAttachmentMethodJob();
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[同步IAE文件库的MAttachment表 error]");
            logger.error("func[{}] e[{}-{}] desc[同步IAE销售文件库的MAttachment表 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));

        }
    }

    /**
     * GEA自定义文件下载（从OSS下载到本地，特殊任务）
     */
    @XxlJob("downGeaAgentFileJob")
    private void downGeaAgentFileJob() {
        try {
//            String fkAgentIds = XxlJobHelper.getJobParam();//获取任务参数
            String fkAgentIds = "10001,10008,10021,10022,10041,10042,10049,10053,10073,10075,10077,10079,10084,10085,10112,10114,10116,10121,10123,10124,10127,10130,10133,10134,10135,10136,14595,10152,10154,10155,10157,10161,10164,10168,10177,10186,10209,10214,10218,10220,10239,10259,10275,10280,10281,10294,10303,10319,10326,10327,10331,10350,10356,10363,10367,10383,10400,10404,10421,10431,10443,10445,10464,10473,10476,10478,10481,10484,10497,10503,10507,10508,10524,10537,10540,10552,10559,10565,10567,10568,10571,10582,10587,10591,10596,10606,10607,10608,10610,10611,10616,10624,10626,10646,10648,10650,10652,10679,10680,10684,10685,10690,10697,10702,10712,10715,10716,10727,10732,10745,10754,10757,10759,10775,10777,10778,10790,10791,10794,10798,10799,10800,10802,10804,10806,10813,10814,10816,10820,10837,10838,10841,10843,10850,10856,10862,10876,10877,10878,10886,10890,10898,10903,10913,10914,10918,10934,10941,10943,10944,10946,10949,10959,10964,10978,10981,10983,10994,11011,11016,11017,11021,11068,11071,11074,11085,11090,11092,11093,11100,11110,11113,11114,11115,11117,11118,11120,11126,11129,11130,11131,11132,11133,11143,11144,11160,11162,11163,11166,11174,11176,11179,11180,11183,11184,11188,11193,11194,11195,11202,11207,11227,11228,11234,11236,11242,11249,11250,11257,11260,11266,11268,11269,11273,11275,11276,11278,11280,11283,11285,11286,11287,11296,11298,11301,11304,11311,11313,11314,11315,11316,11323,11329,11330,11331,11335,11339,11345,11350,11353,11355,11359,11363,11364,11365,11369,11370,11371,11378,11383,11385,11386,11390,11397,11402,11403,11406,11408,11420,11426,11429,11430,11432,11440,11442,11443,11446,11451,11463,11467,11469,11475,11478,11484,11492,11497,11501,11502,11503,11505,11508,11513,11514,11520,11521,11523,13867,11529,11538,11540,11541,11546,11548,11549,11558,11559,11563,11572,11573,11577,11578,11581,11582,11583,11585,11586,11587,11594,11596,11599,11604,11620,11624,11630,11632,11633,11634,11635,11636,11641,11644,11645,11646,11647,11653,11654,11657,11661,11662,11664,11668,11684,11686,11692,11693,11697,11698,11707,11708,11711,11712,11714,11715,11719,11728,11735,11742,11748,11751,11752,11755,11757,11760,11762,11770,11774,11777,11779,11781,11782,11786,11787,11792,11794,11796,11797,11801,11802,11806,11807,11808,11809,11810,11821,11824,15769,11829,11830,11832,11842,11846,11852,11853,11854,11856,11857,11860,11871,11879,11890,11893,11894,11897,11900,11904,11908,11914,11916,11921,11922,11924,11925,11929,11932,11934,10389,11941,11943,11944,11945,11946,11949,11956,11958,11959,11961,11962,11964,11973,11978,11980,11989,11990,11991,11994,11996,11997,12001,12009,12012,12014,12020,12021,12022,12023,12027,12029,12038,12040,12044,12047,12049,12050,12052,12054,12057,12060,12062,12063,12065,12066,12067,12068,12073,12074,12085,12086,12087,12095,12099,12102,12106,12110,12111,12112,12116,12117,12118,12119,12120,12121,12129,12131,12133,12135,12136,12140,12141,12145,12146,14135,12149,12153,12157,12161,12164,12166,12168,12169,12172,12173,12176,12182,12184,12188,12191,12192,12194,12199,12201,12209,12210,12215,12216,12220,12221,12222,12224,12225,12226,12241,12244,12254,12256,12257,12262,12264,12265,12267,12271,12272,12276,12278,12279,12283,12285,12286,12292,12294,12296,12312,12314,12316,12317,12320,12328,12329,12332,12333,12335,12339,12340,12343,12344,12349,12352,12356,12360,12363,12368,12370,12376,16153,12380,12383,12385,12388,12390,12391,12395,12396,12403,12404,12406,12407,12410,12411,12414,12416,12420,12422,12424,12425,12427,12429,12431,12432,12438,12440,12442,12445,12447,12450,12451,12452,12457,12458,12460,12465,12466,12470,12475,12486,12487,12488,12490,12492,12493,12497,12500,12502,12503,12504,12506,12508,12509,12510,12511,12516,12517,12518,12519,12521,12522,12523,12526,12528,12540,12541,12542,12543,12547,12548,12555,12556,12560,12562,12563,12564,12574,12576,12579,12580,12585,12586,12588,12589,12592,12594,13570,12599,12602,12608,12609,12610,12619,12621,12623,12624,12628,12633,12634,12637,12638,12639,12640,12641,12642,12643,12644,12645,12646,12647,12650,12653,12656,12660,12661,12662,12663,12680,12688,12689,12696,12697,12698,12701,12703,12705,12709,12713,12716,12717,12719,12726,12728,12730,12731,12732,12740,12741,12744,12748,12749,12750,12751,12752,12756,12757,12759,12764,12766,12769,12772,12774,12776,12777,12783,12788,12790,12791,12792,12794,12796,12800,12807,12808,12822,12824,12825,12826,12830,12836,12837,12841,12843,12844,12846,12848,12853,12861,12862,12866,13556,12872,12873,12876,12879,12885,12888,12893,12894,12896,12899,12902,12911,12923,12927,12928,12930,12937,12941,12944,12946,12950,12960,12961,12964,12965,12972,12973,12974,12978,12980,12981,12982,12984,12985,12990,12991,12993,12994,12995,12998,13000,13001,13005,13007,13012,13013,13020,13024,13026,13029,13030,13031,13033,13035,13036,13037,13040,13045,13046,13050,13051,13054,13055,13056,13065,13074,13079,13083,13087,13097,13101,13103,13109,13118,13125,13126,13127,13137,13142,13143,13144,13145,13152,13153,13156,13160,13163,13166,13169,13170,13172,13177,13185,13190,13194,13195,13200,13202,13205,13208,13213,13214,13216,13217,13219,13225,13227,13232,13235,13238,13241,13247,13248,13252,13254,13257,13261,13262,13263,13264,13266,13267,13270,13276,13277,13278,13280,13286,13287,13289,13290,13295,13303,13304,13305,13310,13311,13314,13317,13321,13323,13326,13331,13332,13337,13340,13345,13346,13353,13357,13361,13366,13367,13368,13370,13371,13373,13376,13377,13379,13382,13383,13386,13387,13392,13399,13400,13403,13409,13410,13414,13416,13425,13429,13431,13432,13433,13438,13442,13444,13445,13448,13455,13459,13461,13470,13476,13478,13482,13489,13490,13494,13497,13502,13506,13509,13511,13512,13517,13519,13520,13521,13522,13524,13525,13526,13538,13539,13540,13541,13542,13543,13545,13547,13550,13553,13555,13559,13560,13561,13563,13567,13568,13573,13578,13579,13581,13583,13586,13588,13591,13594,13596,13597,13601,13605,13608,13614,13615,13618,13620,13624,13626,13627,13629,13630,13631,13633,13644,13645,13647,13655,13657,13658,13659,13661,13662,13664,13673,13674,13676,13678,13681,13682,13684,13686,13688,13689,13693,13698,13700,13702,13703,13709,13712,13719,13721,13731,13734,13735,13737,13739,13741,13744,13747,13756,13765,13766,13778,13779,13781,13784,13787,13790,13792,13797,13798,13803,13804,13805,13808,13814,13815,13821,13826,13829,13832,13834,13837,13839,13848,13850,13851,13862,13863,13866,13870,13872,13874,13887,13888,13891,13899,13901,13905,13911,13914,13915,13921,13928,13929,13932,13936,13938,13948,13950,13951,13953,13954,13956,13957,13958,13963,13965,13967,13976,13977,13978,13980,13986,13990,30417,13999,14005,14006,14011,14013,14018,14019,14021,14022,14023,14024,14025,14027,14032,14034,14037,14040,14041,14047,14053,14056,14058,14060,14067,14068,14069,14071,14072,14074,14075,14076,14077,14081,14083,14087,14088,14092,14101,14105,14107,14113,14114,14115,14118,14120,14121,14122,14123,14124,14128,14129,14132,14139,14142,14150,14154,14157,14159,14161,14162,14168,14175,14180,14184,14187,14195,14196,14197,14198,14199,14201,14204,14221,14222,14223,14227,14229,14231,14236,14237,14241,14243,14253,14256,14258,14259,14261,14262,14263,14266,14270,14272,14276,14278,14280,14284,14287,14292,14298,14301,14302,14314,14315,14317,14323,14325,14326,14327,14332,14333,14335,14337,14338,14339,14342,14348,14352,14354,14360,14361,14363,14372,14376,14377,14379,14380,14381,14390,14391,14393,14394,14396,14397,14400,14405,14410,14413,14414,14416,14418,14420,14421,14426,14435,14437,14441,14442,14448,14449,14450,14451,14456,14463,14467,14468,14474,14480,14483,14487,14489,14495,14497,14500,14501,14504,14508,14510,14511,14516,14518,14520,14522,14524,14527,14529,14531,14533,14539,14541,14542,14548,14549,14552,14553,14556,14557,14561,14562,14564,14569,14570,14572,14576,14580,16220,14583,14585,14593,14600,14609,14610,14615,14619,14620,14624,14634,14635,14639,14641,14642,14644,14646,14653,14655,14658,14661,14663,14666,14668,14669,14670,14672,14678,14679,14680,14683,14685,14688,14698,14699,14700,14702,14706,14721,14723,14733,14737,14739,14740,14746,14749,14754,14759,14764,14765,14768,14769,14770,14774,14778,14784,14786,14787,14796,14797,14813,14817,14820,14822,14824,14832,14833,14837,14839,14843,14856,14859,14864,14870,14873,14879,14881,14883,14885,14887,14899,14900,14903,14906,14908,14909,14912,14917,14918,14920,14922,14924,14925,14926,14928,14929,14937,14938,14939,14941,14947,14952,14961,14964,14967,14970,14972,14978,14984,14985,14986,14987,14989,14992,14996,14999,15004,15005,15006,15008,15013,15014,15015,15018,15024,15025,15027,15028,15030,15038,15039,15041,15042,15044,15045,15049,15055,15059,15060,15069,15078,15079,15086,15088,15090,15094,15097,15099,15104,15106,15111,15113,15117,15119,15121,15127,15129,15130,15131,15134,15138,15142,15145,15147,15151,15156,15159,15165,15169,15170,15175,15176,15177,15179,15180,15185,15189,15190,15192,15193,15194,15197,15198,15200,15207,15210,15212,15213,15215,15216,15218,15219,15220,15222,15224,15226,15228,15231,15232,15233,15234,15237,15239,15240,15242,15251,15255,15256,15257,15264,15265,15268,15269,15271,15281,15286,15290,15291,15292,15294,15295,15296,15297,15299,15302,15308,15309,15311,15315,15317,15322,15330,15332,15337,15341,15343,15346,15347,15351,15353,15355,15357,15362,15365,15375,15377,15379,15381,15392,15393,15394,15397,15407,15408,15411,15412,15417,15419,15420,15422,15425,15426,15429,15437,15446,15448,15449,15452,15453,15455,15464,15465,15470,15476,15478,15482,15484,15485,15487,15490,15491,15493,15498,15499,15501,15503,15505,15510,15513,15515,15518,15520,15524,15529,15532,15535,15536,15541,15544,15547,15548,15553,15559,15560,15563,15565,15566,15567,15570,15572,15573,15576,15578,15583,15585,15587,15588,15589,15592,15593,15599,15600,15601,15603,15605,15606,15608,15611,15612,15613,15615,15616,15618,15619,15627,15628,15629,15632,15633,15635,15641,15646,15648,15652,15654,15655,15657,15658,15661,15663,15664,15666,15668,15671,15672,15674,15677,15680,15681,15682,15683,15684,15687,15690,15694,15696,15698,15702,15707,15708,15710,15711,15720,15722,15724,15725,15726,15728,15730,15732,15734,15735,15737,15740,15746,15747,15750,15755,15756,15757,15759,15761,15762,15765,15767,15768,15771,15773,15775,15778,15779,15780,15782,15786,15789,15790,15791,15795,15797,15798,15802,15803,15804,15806,15808,15809,15810,15812,15813,15815,15816,15818,15820,15823,15825,15826,15827,15829,15830,15831,15835,15836,15837,15838,15841,15842,15845,15848,15850,15851,15852,15853,15855,15857,15859,15860,15861,15863,15869,15870,15872,15873,15874,15875,15876,15879,15880,15881,15882,15887,15889,15890,15892,15893,15894,15895,15896,15897,15898,15901,15902,15904,15905,15906,15907,15908,15909,15910,15913,15914,15915,15916,15917,15919,15924,15925,15929,15931,15935,15937,15938,15939,15942,15946,15949,15950,15951,15953,15954,15956,15958,15959,15960,15965,15968,15969,15970,15971,15973,15975,15977,15978,15983,15984,15985,15988,15990,15996,16003,16005,16012,16013,16015,16016,16017,16022,16023,16028,16029,16030,16031,16033,16034,16038,16040,16042,16052,16053,16054,16060,16062,16064,16065,16067,16068,16076,16077,16078,16079,16080,16083,16087,16088,16089,16093,16094,16096,16100,16102,16103,16104,16106,16107,16108,16110,16111,16114,16116,16120,16122,16123,16124,16125,16129,16139,16140,16145,16147,16148,16152,16155,16161,16162,16166,16168,16171,16173,16174,16177,16178,16179,16180,16181,16183,16184,16185,16196,16197,16200,16202,16203,16207,16208,16209,16216,16219,16221,16225,16227,16234,16235,16236,16243,16244,16245,16248,16250,16251,16253,16254,16255,16256,16260,16261,16262,16263,16266,16268,16273,16274,16275,16276,16278,16282,16284,16285,16286,16288,16290,16291,16293,16294,16299,16300,16304,16305,16307,16310,16314,16317,16323,16332,16333,16338,16339,16340,16343,16345,16347,16349,16350,16352,16358,16364,20027,20041,20080,20085,20152,20160,20211,20216,20219,20269,20280,20343,20367,20396,20416,20420,20430,20435,20451,20496,20550,20593,20655,20678,20686,20724,20765,20767,20822,20856,20935,20938,20972,20978,21017,21019,21073,21104,21106,21122,21124,21130,21137,21154,21229,21239,21294,21300,21305,21353,21355,21356,21375,21401,21415,21446,21480,21519,21520,21535,21543,21645,21680,21686,21693,21781,21793,21848,21860,21919,22007,22032,22087,22094,22103,22116,22125,22171,22175,22185,22195,22199,22224,22225,22252,22310,22312,22358,22374,22382,22416,22451,22499,22520,22552,22580,22631,22636,22650,22659,22662,22666,22689,22706,22717,22775,14896,12601,12331,22931,22938,23036,23046,23101,23160,23178,23209,23210,23229,23250,23257,23287,23312,23342,11849,11170,23443,23450,23457,23491,23504,23511,14554,23554,23560,23588,23628,23658,23693,23730,23765,23785,23787,23852,23943,23987,23996,24010,24014,24021,24027,24030,24081,24089,24097,24102,24125,24178,24244,24312,24374,24389,24502,30514,24639,24765,30871,24866,24940,30002,30003,30004,30007,30008,30010,30013,30014,30025,30028,30030,30036,30037,30038,30039,30040,30041,30043,30044,30047,30049,30058,30061,30068,30069,30070,30072,30075,30076,30077,30079,30080,30081,30082,30084,30100,30125,30128,30132,30134,30136,30137,30138,30144,30148,30149,30152,30153,30154,30162,30163,30165,30170,30176,30183,30184,30185,30189,30193,30197,30200,30201,30202,30208,30210,30211,30213,30215,30218,30219,30231,30235,30236,30237,30240,30294,30298,30299,30300,30303,30307,30315,30360,30371,30372,30374,30377,30380,30381,30382,30391,30393,30404,30407,30408,30409,30412,30416,30419,30421,30430,30431,30437,30442,30446,30449,30451,30454,30459,30467,30470,30474,30477,30481,30485,30493,30494,30495,30496,30497,30506,30507,30508,30509,30513,30515,30516,30517,30520,30521,30524,30525,30526,30527,30529,30548,30554,30560,30562,30563,30566,30575,30576,30589,30592,30596,30602,30605,30613,30616,30619,30633,30659,30661,30739,30757,30905,31099,31125,31161,31593,10605,13011,21650,24086,30203,30209";//获取任务参数
//            String fkAgentIds = "22636,21543,21300,20211,20451,21781,30380,20219,22631,21305,20216,12837,11748,12713,11505,10537,11980,10650,11742,20343,21793,16089,24940,23730,22520,23852,24702,12726,12848,11879,30372,30371,30374,13938,30010,12961,11751,14900,11752,11513,10303,11633,15086,16054,21401,16174,16173,21520,20430,20550,16179,16299,20678,20435,23943,21645,10877,30485,10878,11846,10759,30360,12937,12938,10994,11961,10754,11842,22866,20686,16064,23838,21650,16062,21415,16181,21535,11856,12826,30474,10524,12705,12944,30477,30592,30237,10400,16033,15185,16030,21860,22717,16034,20659,20416,14092,15180,20655,11943,11827,10616,30467,10732,16286,16042,20420,15194,23939,16288,21519,16161,10624,10745,11956,10862,10983,11832,14074,15045,22938,14198,16012,21848,14071,22931,30442,30562,30201,11802,10716,10838,10959,30209,30446,30566,16022,15296,16262,16140,22706,16266,16023,11809,16260,20767,20765,10606,10727,11939,30437,23209,12792,12790,23443,22358,22116,15949,15707,12438,14859,23560,11586,13520,14972,14856,15825,11345,13526,10259,14733,12560,10383,21154,12680,23210,22125,23457,22007,12329,14508,12328,23450,23693,11110,11594,10021,11113,12442,12201,14746,12689,15836,13779,15956,15835,11117,12445,15954,14987,21124,24639,21122,20396,20152,23540,22451,23664,23785,23787,10239,11329,14837,10473,12411,21480,12650,10112,10596,10356,13624,15803,12899,15802,14832,13744,10478,14833,12776,21017,21137,21375,12660,23554,23312,30193,22224,22103,22225,24765,21019,15937,15816,15815,30075,22580,30077,11573,20041,10363,11331,21130,14721,20160,12541,10001,20280,11577,13998,12788,13512,12424,21104,20496,10690,22310,22552,22312,23765,21229,21106,15906,15904,12757,10218,11549,12637,14939,15907,12873,12994,11781,10571,11662,11420,11786,12634,11664,12756,11304,20027,23658,20269,23417,21356,24508,21355,21353,24502,21239,22689,24866,12407,14706,11558,13739,13618,15915,11430,13731,12642,10582,14941,11311,12403,15913,11313,11797,12646,12764,23504,21446,22416,22659,21686,23628,20593,22650,22775,16093,23987,24833,13703,10559,11529,10319,30041,11408,30044,11760,21680,10794,10431,11524,10798,12973,13821,20367,21693,22662,23511,23996,22666,10327,12506,10326,13957,13950,11770,12741,10684,11654,12623,10685,12624,11777,14920,10445,13000,15663,11183,14332,12272,14572,23287,22199,23046,24014,23160,22195,24010,24374,15425,13368,12276,13126,14333,15302,15786,15422,15429,12038,15668,11194,14582,12044,13252,13494,23178,24025,24389,24027,24021,22087,13497,12049,15520,11283,11285,10075,15880,22171,22175,11166,13345,11287,13103,12378,14556,12254,13101,15648,15889,14679,14680,24244,23036,24125,15419,22185,15898,15897,13357,14204,15896,12147,15654,15411,12149,15417,13238,14327,15657,11260,10053,11383,22032,13202,12597,12356,15985,11385,15984,13563,14410,14653,15627,15505,13688,13205,14778,15872,12121,24102,23257,13219,23491,23250,15876,11276,14786,12244,12488,14787,14784,21073,14549,15879,13578,13579,23229,22374,23342,23101,24312,23588,22499,11129,14759,22252,15722,11484,14754,11363,15841,12452,10155,11126,12458,12579,12215,10157,14999,15725,12335,11249,15603,13425,12216,11250,14881,13790,12220,22382,12344,15975,20085,21294,12466,15611,13555,15852,14641,13311,14527,15857,20080,15615,11378,15613,16352,15142,16110,20972,15381,16114,16234,20978,20856,30661,10813,10934,24097,15269,16236,16120,15393,15271,16125,16124,14068,14187,16243,11908,14060,10946,16129,30419,30412,15362,14391,15365,16332,13065,15242,30521,21919,15127,30407,16219,14041,15493,15498,16103,30509,15134,15255,16102,16343,10918,15490,20724,10802,24086,24089,15018,15139,15138,16227,14168,15257,16104,16225,24081,13280,13286,15465,30619,13287,12074,13163,14132,24178,20935,14379,12199,15104,15346,30507,13289,30506,14139,14018,15228,14262,14380,15353,13054,20822,20938,16203,30613,13386,12173,14354,13020,15683,24030,22094,14236,11092,13392,15570,15690,13270,13031,12068,15216,14489,16304,13035,15698,15455,15576,16307";//获取任务参数
            //获取所有的文件
            String filePath = "D:\\BaiduDown\\agentFile\\";
            List<String> fkAgentIds_1 = GeneralTool.toStrList(fkAgentIds);
            File file = new File(filePath);
            // 如果这个路径是文件夹
            if (file.isDirectory()) {
                // 获取路径下的所有文件,把不在列表中的文件删除
                File[] files = file.listFiles();
                for (int i = 0; i < files.length; i++) {
                    File file_1 = files[i];//10114.pdf
                    String name = file_1.getName().substring(0,5);
                    if(!fkAgentIds_1.contains(name))
                    {
                        File file_2 = new File(filePath+file_1.getName());
                        file_2.delete();
                        System.out.println("==================>删除的文件名："+name);
                    }
                }
            }
            XxlJobHelper.log("GEA自定义文件下载 定时任务开始=》"+fkAgentIds);
            //执行重新下载文件
            iaeCrmSynService.downGeaAgentFileJob(fkAgentIds);
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[GEA自定义文件下载 error]");
            logger.error("func[{}] e[{}-{}] desc[GEA自定义文件下载 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));

        }
    }
    /**
     * HTI历史新闻数据导入到BMS中
     */
//    @XxlJob("synCrmNewsToBmsMethod")
//    private void synCrmNewsToBmsMethod() {
//        try {
//            XxlJobHelper.log("HTI导入历史新闻数据 定时任务开始=》");
//            //执行重新下载文件
//            iaeCrmSynService.synCrmNewsToBmsMethod();
//            XxlJobHelper.log("HTI导入历史新闻数据 定时任务结束=》");
//        }catch (Exception e){
//            e.getStackTrace();
//            XxlJobHelper.log("func[{}] e[{}-{}] desc[HTI导入历史新闻数据 error]");
//            logger.error("func[{}] e[{}-{}] desc[HTI导入历史新闻数据 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
//                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
//
//        }
//    }
}
