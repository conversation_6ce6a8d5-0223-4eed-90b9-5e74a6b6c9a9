package com.get.job;

import com.get.common.constant.AppCenterConstant;
import com.get.core.cloud.feign.EnableGetFeign;
import com.get.core.start.GetApplication;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.cloud.client.SpringCloudApplication;

/**
 * Job启动器
 */
@EnableGetFeign
@SpringCloudApplication
@MapperScan("com.get.job.dao")
public class JobApplication {
    public static void main(String[] args) {
        GetApplication.run(AppCenterConstant.APPLICATION_XXLJOB_NAME, com.get.job.JobApplication.class, args);
    }
}

