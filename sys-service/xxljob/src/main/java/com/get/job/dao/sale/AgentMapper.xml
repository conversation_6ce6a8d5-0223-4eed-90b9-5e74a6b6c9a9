<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.sale.AgentMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.Agent">
    insert into m_agent (id, fk_parent_agent_id, fk_area_country_id, 
      fk_area_state_id, fk_area_city_id, num, 
      name, nick_name, nature, 
      nature_note, legal_person, tax_code, 
      id_card_num, address, remark, 
      invitation_code, is_settlement_port, is_key_agent, 
      key_agent_failure_time, is_active, id_gea, 
      id_iae, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkParentAgentId,jdbcType=BIGINT}, #{fkAreaCountryId,jdbcType=BIGINT}, 
      #{fkAreaStateId,jdbcType=BIGINT}, #{fkAreaCityId,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{nickName,jdbcType=VARCHAR}, #{nature,jdbcType=VARCHAR}, 
      #{natureNote,jdbcType=VARCHAR}, #{legalPerson,jdbcType=VARCHAR}, #{taxCode,jdbcType=VARCHAR}, 
      #{idCardNum,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{invitationCode,jdbcType=VARCHAR}, #{isSettlementPort,jdbcType=BIT}, #{isKeyAgent,jdbcType=BIT}, 
      #{keyAgentFailureTime,jdbcType=TIMESTAMP}, #{isActive,jdbcType=BIT}, #{idGea,jdbcType=VARCHAR}, 
      #{idIae,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.Agent">
    insert into m_agent
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkParentAgentId != null">
        fk_parent_agent_id,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id,
      </if>
      <if test="fkAreaCityId != null">
        fk_area_city_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nickName != null">
        nick_name,
      </if>
      <if test="nature != null">
        nature,
      </if>
      <if test="natureNote != null">
        nature_note,
      </if>
      <if test="legalPerson != null">
        legal_person,
      </if>
      <if test="taxCode != null">
        tax_code,
      </if>
      <if test="idCardNum != null">
        id_card_num,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="invitationCode != null">
        invitation_code,
      </if>
      <if test="isSettlementPort != null">
        is_settlement_port,
      </if>
      <if test="isKeyAgent != null">
        is_key_agent,
      </if>
      <if test="keyAgentFailureTime != null">
        key_agent_failure_time,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="idGea != null">
        id_gea,
      </if>
      <if test="idIae != null">
        id_iae,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkParentAgentId != null">
        #{fkParentAgentId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityId != null">
        #{fkAreaCityId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nickName != null">
        #{nickName,jdbcType=VARCHAR},
      </if>
      <if test="nature != null">
        #{nature,jdbcType=VARCHAR},
      </if>
      <if test="natureNote != null">
        #{natureNote,jdbcType=VARCHAR},
      </if>
      <if test="legalPerson != null">
        #{legalPerson,jdbcType=VARCHAR},
      </if>
      <if test="taxCode != null">
        #{taxCode,jdbcType=VARCHAR},
      </if>
      <if test="idCardNum != null">
        #{idCardNum,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="invitationCode != null">
        #{invitationCode,jdbcType=VARCHAR},
      </if>
      <if test="isSettlementPort != null">
        #{isSettlementPort,jdbcType=BIT},
      </if>
      <if test="isKeyAgent != null">
        #{isKeyAgent,jdbcType=BIT},
      </if>
      <if test="keyAgentFailureTime != null">
        #{keyAgentFailureTime,jdbcType=TIMESTAMP},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="idGea != null">
        #{idGea,jdbcType=VARCHAR},
      </if>
      <if test="idIae != null">
        #{idIae,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="selectAgentByIdGea" resultType="com.get.salecenter.entity.Agent">
    SELECT * FROM m_agent where FIND_IN_SET(#{idGea}, id_gea)
  </select>
</mapper>