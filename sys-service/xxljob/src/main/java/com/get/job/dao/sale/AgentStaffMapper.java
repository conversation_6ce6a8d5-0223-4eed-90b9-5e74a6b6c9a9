package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AgentStaff;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("saledb")
public interface AgentStaffMapper extends BaseMapper<AgentStaff> {
    int insert(AgentStaff record);

    int insertSelective(AgentStaff record);

    int updateByPrimaryKeySelective(AgentStaff record);

    int updateByPrimaryKey(AgentStaff record);
}