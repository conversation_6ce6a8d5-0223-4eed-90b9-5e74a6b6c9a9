package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("STUFORCASITA")
public class StuForCasita implements Serializable {
    private static final long serialVersionUID = 1L;
    private BigDecimal id;
    private String name;
    private String englishname;
    private Date birthday;
    private String rcname;
    private String countryname;
    private String city;
    private String rmname;
    private String agencyname;
    private String apartmentname;
    private Date intotm;
    private String intodays;
    private String intomonth;
    private String intoyear;
    private String stayweek;
    private String weekfee;
    private String totalfee;
    private String chanel;
    private String agentrate;
    private String settlestate;
    private String rategea;
    private Date depositpaymenttm;
    private String remarks1;
    private String remarks2;
    private Date outtm;
    private String state;
    private String country;
    private String currency;
    private String agentid;
    private Date exceltime;
    private String excelstate;
    private String isbuyalone;
    private String stuid;
    private Date createtime;
    private Date editime;
}