package com.get.job.jobhandler;

import com.get.core.tool.api.Result;
import com.get.job.service.sale.IContractXxlJobService;
import com.get.salecenter.feign.ISaleCenterClient;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: Hardy
 * @create: 2022/11/7 10:12
 * @verison: 1.0
 * @description:
 */
@Component
public class ContractXxlJob {


    @Resource
    private IContractXxlJobService contractXxlJobService;

    /**
     * 关键代理过期任务调度
     *
     * @throws Exception
     */
    @XxlJob("contractTaskJobHandler")
    public void contractTaskJobHandler() throws Exception {
        XxlJobHelper.log("XXL-JOB, AontractTaskJobHandler start...");
        Boolean successful = contractXxlJobService.addContractRemind();
        if (successful){
            XxlJobHelper.log("XXL-JOB, AontractTaskJobHandler success...");
        }else {
            XxlJobHelper.log("XXL-JOB, AontractTaskJobHandler fail...");
        }
    }

}
