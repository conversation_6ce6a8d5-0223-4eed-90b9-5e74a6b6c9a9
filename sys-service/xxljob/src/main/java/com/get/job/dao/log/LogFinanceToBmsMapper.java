package com.get.job.dao.log;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.job.entity.LogFinanceToBms;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("logdb")
public interface LogFinanceToBmsMapper extends BaseMapper<LogFinanceToBms> {
//    @Override
//    int insert(LogFinanceToBms record);
//
//    int insertSelective(LogFinanceToBms record);
}