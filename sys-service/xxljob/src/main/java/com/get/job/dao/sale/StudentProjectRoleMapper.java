package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.StudentProjectRole;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("saledb")
public interface StudentProjectRoleMapper extends BaseMapper<StudentProjectRole> {
    int insert(StudentProjectRole record);

    int insertSelective(StudentProjectRole record);

    int updateByPrimaryKeySelective(StudentProjectRole record);

    int updateByPrimaryKey(StudentProjectRole record);
}