package com.get.job.service.pmp.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.job.dao.pmp.InstitutionProviderContractMapper;
import com.get.job.service.pmp.AgentCommissionPlanService;
import com.get.job.service.pmp.InstitutionProviderCommissionPlanService;
import com.get.job.service.pmp.InstitutionProviderContractService;
import com.get.pmpcenter.entity.AgentCommissionPlan;
import com.get.pmpcenter.entity.InstitutionProviderCommissionPlan;
import com.get.pmpcenter.entity.InstitutionProviderContract;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author:Oliver
 * @Date: 2025/3/28
 * @Version 1.0
 * @apiNote:
 */
@Service
@Slf4j
public class InstitutionProviderContractServiceImpl extends ServiceImpl<InstitutionProviderContractMapper, InstitutionProviderContract>
        implements InstitutionProviderContractService {

    @Autowired
    private InstitutionProviderContractMapper institutionProviderContractMapper;
    @Autowired
    private AgentCommissionPlanService agentCommissionPlanService;
    @Autowired
    private InstitutionProviderCommissionPlanService commissionPlanService;

    @Override
    @DSTransactional
    @DS("pmp")
    public void unActiveContract() {
        //查询已过期的合同
        log.info("开始查询已过期的合同,时间:{}", DateUtil.formatDateTime(new Date()));
        List<InstitutionProviderContract> expireContract = institutionProviderContractMapper.selectList(new LambdaQueryWrapper<InstitutionProviderContract>()
                .eq(InstitutionProviderContract::getIsTimeless, 0)
                .lt(InstitutionProviderContract::getEndTime, new Date())
                .eq(InstitutionProviderContract::getIsActive, 1));
        if (CollectionUtils.isNotEmpty(expireContract)) {
            //查询佣金方案
            List<InstitutionProviderCommissionPlan> commissionPlanList = commissionPlanService.list(new LambdaQueryWrapper<InstitutionProviderCommissionPlan>()
                    .in(InstitutionProviderCommissionPlan::getFkInstitutionProviderContractId,
                            expireContract.stream().map(InstitutionProviderContract::getId).collect(Collectors.toList())));
            if (CollectionUtils.isNotEmpty(commissionPlanList)) {
                List<Long> planIds = commissionPlanList.stream().map(InstitutionProviderCommissionPlan::getId).collect(Collectors.toList());
                commissionPlanList.stream().forEach(plan -> {
                    plan.setIsActive(0);
                    plan.setGmtModified(new Date());
                });
                commissionPlanService.updateBatchById(commissionPlanList);

                if (CollectionUtils.isNotEmpty(planIds)) {
                    List<AgentCommissionPlan> agentCommissionPlans = agentCommissionPlanService.list(new LambdaQueryWrapper<AgentCommissionPlan>()
                            .in(AgentCommissionPlan::getFkInstitutionProviderCommissionPlanId, planIds));
                    if (CollectionUtils.isNotEmpty(agentCommissionPlans)) {
                        agentCommissionPlans.stream().forEach(plan -> {
                            plan.setIsActive(0);
                            plan.setGmtModified(new Date());
                        });
                        agentCommissionPlanService.updateBatchById(agentCommissionPlans);
                    }
                }
            }
            expireContract.stream().forEach(contract -> {
                contract.setIsActive(0);
                contract.setGmtModified(new Date());
            });
            this.updateBatchById(expireContract);
            log.info("合同自动下架定时器执行结束,时间:{}", DateUtil.formatDateTime(new Date()));
        }
    }
}
