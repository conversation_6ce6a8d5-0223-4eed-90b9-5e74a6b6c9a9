package com.get.job.jobhandler;

import com.get.job.service.finance.IScheduleService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * Time: 17:08
 * Date: 2022/4/1
 * Description:
 */
@Component
public class SetExchangeRateTimeXxlJob {


    @Resource
    private IScheduleService scheduleService;

    @XxlJob("setExchangeRateTime")
    public void setExchangeRateTime() {
        try {
            XxlJobHelper.log("重新运行获取汇率定时器启动...");
            scheduleService.setExchangeRateTime();
        } catch (Exception e) {
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[运行获取汇率定时器 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }
    }
}
