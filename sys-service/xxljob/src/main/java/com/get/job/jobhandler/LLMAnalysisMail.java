package com.get.job.jobhandler;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.get.job.dao.mail.MMailLLMAnalysisMapper;
import com.get.job.dao.mail.MMailMapper;
import com.get.job.entity.MMail;
import com.get.job.entity.MMailLLMAnalysis;
import com.get.job.service.mail.impl.MailAnalysisService;
import com.get.job.service.mail.impl.SyncEmailStatusServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Component
public class LLMAnalysisMail {
    private static final Logger logger = LoggerFactory.getLogger(DynamicRouteHealthCheckJob.class);
    @Resource
    private MailAnalysisService MailAnalysisService;

    @Resource
    private RocketMQTemplate rocketMQTemplate;

    //定义发送的topic
    private static final String TOPIC = "analyse_mail_topic";

    @Resource
    private MMailMapper mailMapper;

    @Resource
    private MMailLLMAnalysisMapper mailLLMAnalysisMapper;

    @XxlJob("LLMAnalysisMail")
    public void syncMailJob() throws Exception {
        try {
            logger.info("开始分析邮件");
            QueryWrapper<MMail> mMailQueryWrapper = new QueryWrapper<>();
            mMailQueryWrapper.eq("llm_mail_type", 0);
            mMailQueryWrapper.orderByDesc("date");
            mMailQueryWrapper.last("limit 100");
            List<MMail> mailList = mailMapper.selectList(mMailQueryWrapper);
            // 控制一次分析的邮件数量
            for (MMail mail : mailList) {
                // 避免重复插入
                QueryWrapper<MMailLLMAnalysis> analysisQueryWrapper = new QueryWrapper<>();
                analysisQueryWrapper.eq("fk_mail_id", mail.getId());
                List<MMailLLMAnalysis> analysisList = mailLLMAnalysisMapper.selectList(analysisQueryWrapper);
                if (!analysisList.isEmpty()) {
                    continue;
                }
                logger.info("发送分析邮件消息，mail={},topic={}", mail, TOPIC);
                rocketMQTemplate.asyncSend(TOPIC, mail, new SendCallback() {
                    @Override
                    public void onSuccess(SendResult sendResult) {
                        logger.info("发送分析邮件成功，mMailAccount={}，消息ID={}", mail, sendResult.getMsgId());
                    }
                    @Override
                    public void onException(Throwable throwable) {
                        logger.error("发送分析邮件失败，mMailAccount={}，异常信息={}", mail, throwable.getMessage());
                        // 这里可以做失败告警、日志记录等处理
                    }
                });
            }
//            MailAnalysisService.mailAnalysis();
            logger.info("分析邮件结束");
        } catch (Exception e) {
            e.getStackTrace();
            XxlJobHelper.log("分析邮件失败");
            logger.error("分析邮件失败，{}", Arrays.deepToString(e.getStackTrace()));
        }
    }
}
