package com.get.job.service.finance.impl;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * @author: Sea
 * @create: 2020/12/23 15:34
 * @verison: 1.0
 * @description: 继承Application接口后项目启动时会按照执行顺序执行run方法
 * 通过设置Order的value来指定执行的顺序
 */
@Component
@Order(value = 1)
public class StartService implements ApplicationRunner {
    @Autowired
    private com.get.job.service.finance.IScheduleService IScheduleService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        System.out.println("加载启动定时器xxjob");
        IScheduleService.start();
    }
}
