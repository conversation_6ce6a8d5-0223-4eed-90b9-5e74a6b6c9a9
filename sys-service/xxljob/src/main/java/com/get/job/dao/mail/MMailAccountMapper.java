package com.get.job.dao.mail;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.job.entity.MMailAccount;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@DS("mail")
@Mapper
public interface MMailAccountMapper extends BaseMapper<MMailAccount> {
    List<MailAccount> selectAccount();
}
