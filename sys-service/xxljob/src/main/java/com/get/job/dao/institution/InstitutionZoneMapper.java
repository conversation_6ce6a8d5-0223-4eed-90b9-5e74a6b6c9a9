package com.get.job.dao.institution;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.InstitutionZone;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("institutiondb")
public interface InstitutionZoneMapper extends BaseMapper<InstitutionZone> {
    int insert(InstitutionZone record);

    int insertSelective(InstitutionZone record);
}