package com.get.job.jobhandler;

import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.get.core.tool.utils.DateUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.remindercenter.dto.MailDto;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

@Component
public class DynamicRouteHealthCheckJob {

    private static final Logger logger = LoggerFactory.getLogger(DynamicRouteHealthCheckJob.class);
    @Resource
    private IReminderCenterClient reminderCenterClient;

    /**
     * GEA动态路由健康检查：如发生不符合条件的路由信息，则发送邮件警告
     *                 //解析json校验合法性,示例参考如下，如果uri里面的服务名和paths不匹配则异常报警
     * //                {
     * //                    "predicate": "Paths: [/reminder-center/**], match trailing slash: true",
     * //                        "metadata": {
     * //                    "nacos.instanceId": null,
     * //                            "nacos.weight": "1.0",
     * //                            "nacos.cluster": "DEFAULT",
     * //                            "nacos.ephemeral": "true",
     * //                            "nacos.healthy": "true",
     * //                            "preserved.register.source": "SPRING_CLOUD"
     * //                       },
     * //                    "route_id": "CompositeDiscoveryClient_reminder-center",
     * //                        "filters": ["[[RewritePath /reminder-center/(?<remaining>.*) = '/${remaining}'], order = 1]"],
     * //                    "uri": "lb://reminder-center",
     * //                        "order": 0
     * //                }
     */
    @XxlJob("dynamicRouteHealthCheckForGea")
    private void dynamicRouteHealthCheckForGea() {
        String ip = "**********";
        try {
            this.healthCheck(ip,"GEA","8090");
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[GEA动态路由健康检查 error]");
            logger.error("func[{}] e[{}-{}] desc[GEA动态路由健康检查 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }
    }

    @XxlJob("dynamicRouteHealthCheckForTest")
    private void dynamicRouteHealthCheckForTest() {
        String ip = "************";
        try {
            this.healthCheck(ip,"TEST","8090");
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[TEST动态路由健康检查 error]");
            logger.error("func[{}] e[{}-{}] desc[TEST动态路由健康检查 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }
    }

    /**
     * IAE动态路由健康检查：如发生不符合条件的路由信息，则发送邮件警告
     */
    @XxlJob("dynamicRouteHealthCheckForIae")
    private void dynamicRouteHealthCheckForIae() {
        try {
            String ip = "**********";
            this.healthCheck(ip,"IAE","8090");
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[IAE动态路由健康检查 error]");
            logger.error("func[{}] e[{}-{}] desc[IAE动态路由健康检查 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }
    }

    /**
     * APP动态路由健康检查：如发生不符合条件的路由信息，则发送邮件警告
     */
    @XxlJob("dynamicRouteHealthCheckForApp")
    private void dynamicRouteHealthCheckForApp() {
        try {
            String ip = "***********";
            this.healthCheck(ip,"APP","9084");
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[APP动态路由健康检查 error]");
            logger.error("func[{}] e[{}-{}] desc[APP动态路由健康检查 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }
    }


    /**
     * 使用http协议获取内网路由信息，针对异常的路由信息邮件告警
     * @param hostIp
     * @param serverName
     * @throws Exception
     */
    private void healthCheck(String hostIp,String serverName,String port)
    {
        String url = "http://"+hostIp+":"+port+"/actuator/gateway/routes";
        Map<String,String> exceptionMap = new HashMap<>();
        String result = HttpUtil.get(url, null, 60000);//1分钟
//        String result = "[{\"predicate\":\"Paths: [/reminder-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_reminder-center\",\"filters\":[\"[[RewritePath /reminder-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://reminder-center/123/321\",\"order\":0},{\"predicate\":\"Paths: [/office-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_office-center\",\"filters\":[\"[[RewritePath /office-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://office-center\",\"order\":0},{\"predicate\":\"Paths: [/resume-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_resume-center\",\"filters\":[\"[[RewritePath /resume-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://resume-center/test\",\"order\":0},{\"predicate\":\"Paths: [/school-gate-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_school-gate-center\",\"filters\":[\"[[RewritePath /school-gate-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://school-gate-center\",\"order\":0},{\"predicate\":\"Paths: [/swagger/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_swagger\",\"filters\":[\"[[RewritePath /swagger/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://swagger\",\"order\":0},{\"predicate\":\"Paths: [/help-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_help-center\",\"filters\":[\"[[RewritePath /help-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://help-center\",\"order\":0},{\"predicate\":\"Paths: [/workflow-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_workflow-center\",\"filters\":[\"[[RewritePath /workflow-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://workflow-center\",\"order\":0},{\"predicate\":\"Paths: [/xxljob-admin/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\",\"management.context-path\":\"/xxl-job-admin/actuator\"},\"route_id\":\"CompositeDiscoveryClient_xxljob-admin\",\"filters\":[\"[[RewritePath /xxljob-admin/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://xxljob-admin\",\"order\":0},{\"predicate\":\"Paths: [/platform-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_platform-center\",\"filters\":[\"[[RewritePath /platform-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://platform-center\",\"order\":0},{\"predicate\":\"Paths: [/finance-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_finance-center\",\"filters\":[\"[[RewritePath /finance-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://finance-center\",\"order\":0},{\"predicate\":\"Paths: [/sale-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_sale-center\",\"filters\":[\"[[RewritePath /sale-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://sale-center\",\"order\":0},{\"predicate\":\"Paths: [/institution-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_institution-center\",\"filters\":[\"[[RewritePath /institution-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://institution-center\",\"order\":0},{\"predicate\":\"Paths: [/voting-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_voting-center\",\"filters\":[\"[[RewritePath /voting-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://voting-center\",\"order\":0},{\"predicate\":\"Paths: [/xxljob/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_xxljob\",\"filters\":[\"[[RewritePath /xxljob/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://xxljob\",\"order\":0},{\"predicate\":\"Paths: [/gateway/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_gateway\",\"filters\":[\"[[RewritePath /gateway/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://gateway\",\"order\":0},{\"predicate\":\"Paths: [/log-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_log-center\",\"filters\":[\"[[RewritePath /log-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://log-center\",\"order\":0},{\"predicate\":\"Paths: [/report-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_report-center\",\"filters\":[\"[[RewritePath /report-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://report-center\",\"order\":0},{\"predicate\":\"Paths: [/permission-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_permission-center\",\"filters\":[\"[[RewritePath /permission-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://permission-center\",\"order\":0},{\"predicate\":\"Paths: [/file-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_file-center\",\"filters\":[\"[[RewritePath /file-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://file-center\",\"order\":0},{\"predicate\":\"Paths: [/authentication/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_authentication\",\"filters\":[\"[[RewritePath /authentication/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://authentication\",\"order\":0},{\"predicate\":\"Paths: [/competition-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_competition-center\",\"filters\":[\"[[RewritePath /competition-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://competition-center\",\"order\":0},{\"predicate\":\"Paths: [/registration-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_registration-center\",\"filters\":[\"[[RewritePath /registration-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://registration-center\",\"order\":0},{\"predicate\":\"Paths: [/exam-center/**], match trailing slash: true\",\"metadata\":{\"nacos.instanceId\":null,\"nacos.weight\":\"1.0\",\"nacos.cluster\":\"DEFAULT\",\"nacos.ephemeral\":\"true\",\"nacos.healthy\":\"true\",\"preserved.register.source\":\"SPRING_CLOUD\"},\"route_id\":\"CompositeDiscoveryClient_exam-center\",\"filters\":[\"[[RewritePath /exam-center/(?<remaining>.*) = '/${remaining}'], order = 1]\"],\"uri\":\"lb://exam-center\",\"order\":0}]";
//        logger.info("【"+serverName+"】"+"执行校验路由返回的result:"+result);
        XxlJobHelper.log("【"+serverName+"】"+"dynamicRouteHealthCheck=>[{}]:result[{}]",hostIp,result);

        JSONArray jsonArray = JSONObject.parseArray(result);
        if(GeneralTool.isNotEmpty(jsonArray))
        {
            if(jsonArray.size()>0)
            {
                for (int i=0;i<jsonArray.size();i++)
                {
                    JSONObject jsonData = jsonArray.getJSONObject(i);
                    String uri = jsonData.getString("uri");//主要检测URL，格式如："uri": "lb://reminder-center"
                    String predicate = jsonData.getString("predicate");//"predicate": "Paths: [/reminder-center/**], match trailing slash: true"
                    String checkUriString = uri.substring(5);//reminder-center
                    XxlJobHelper.log("【"+serverName+"】"+"dynamicRouteHealthCheckForGea=>匹配URL信息：predicate["+predicate+"],url["+checkUriString+"]");
                    //不存在则邮件报警
                    if(!predicate.contains(checkUriString))
                    {
                        exceptionMap.put(uri,predicate);
                    }
                }
            }
        }
        //如有异常则发送邮件报警
        if(GeneralTool.isNotEmpty(exceptionMap) && exceptionMap.size()>0)
        {
            String title = "【"+serverName+"】"+"网关路由检测异常报警，通知编号："+ DateUtil.formatDateTimeMini(new Date());
            String template = "获取到的匹配路由详情：【"+result+"】"+"，异常路由信息uri：【【【"+GeneralTool.toJson(exceptionMap)+"】】】";
            String toEmail = "<EMAIL>";
            String ccEmail = "<EMAIL>";//抄送邮箱

            MailDto mailDto = new MailDto();
            mailDto.setDefaultEncoding("utf-8");
            mailDto.setHost("smtp.exmail.qq.com");
            mailDto.setPort(465);
            mailDto.setProtocol("smtps");
            mailDto.setUserName("<EMAIL>");
            mailDto.setPassword("GZEtLhkf6WzpcPd9");
            mailDto.setTitle(title);
            mailDto.setToEmail(toEmail);
            mailDto.setCcEmail(ccEmail);
            mailDto.setTemplate(template);
            mailDto.setFlag(false);//是否html

            reminderCenterClient.customSendMailByBody(mailDto);//是否html

            XxlJobHelper.log("【"+serverName+"】"+"dynamicRouteHealthCheckForGea=>[{}]:异常路由[{}]",hostIp,GeneralTool.toJson(exceptionMap));
        }
        XxlJobHelper.log("【"+serverName+"】"+"dynamicRouteHealthCheckForGea=>[{}]","检测完成！");
    }
}
