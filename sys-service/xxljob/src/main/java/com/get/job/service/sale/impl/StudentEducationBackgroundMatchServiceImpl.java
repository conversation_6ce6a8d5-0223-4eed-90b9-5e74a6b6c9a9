package com.get.job.service.sale.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.entity.Institution;
import com.get.job.dao.institution.InstitutionMapper;
import com.get.job.dao.sale.StudentMapper;
import com.get.job.jobhandler.StudentEducationBackgroundMatchXxlJob;
import com.get.job.service.sale.IStudentEducationBackgroundMatchService;
import com.get.job.utils.MyStringUtils;
import com.get.salecenter.entity.Student;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/12/25 12:32
 * @verison: 1.0
 * @description:
 */
@Service
public class StudentEducationBackgroundMatchServiceImpl implements IStudentEducationBackgroundMatchService {

    private static final Logger logger = LoggerFactory.getLogger(StudentEducationBackgroundMatchServiceImpl.class);

    @Resource
    private StudentMapper studentMapper;
    @Resource
    private InstitutionMapper institutionMapper;

    @Async
    @Override
    public void matchStudentEducationBackground() {
        List<Student> students = studentMapper.getStudentsWithoutInstitutionIdEducation();
        if (GeneralTool.isEmpty(students)){
            return;
        }

        logger.info("开始匹配学生国内教育背景---------------，一共{}个学生",students.size());
        for (Student student : students) {
            String fkInstitutionNameEducation = student.getFkInstitutionNameEducation();
            if (GeneralTool.isEmpty(fkInstitutionNameEducation)){
                continue;
            }
            String chineseName = MyStringUtils.getChineseString(fkInstitutionNameEducation);
            String englishName = MyStringUtils.getEnglishString(fkInstitutionNameEducation);
            if (GeneralTool.isNotEmpty(englishName)){
                englishName = englishName.toLowerCase();
            }

            if (GeneralTool.isNotEmpty(chineseName)||GeneralTool.isNotEmpty(englishName)||GeneralTool.isNotEmpty(fkInstitutionNameEducation)){
                Institution institution = institutionMapper.selectInstitutionByNameChnOrNameEng(chineseName,englishName,fkInstitutionNameEducation);
                if (GeneralTool.isNotEmpty(institution)){
                    student.setFkInstitutionIdEducation(institution.getId());
                    student.setFkAreaCountryIdEducation(institution.getFkAreaCountryId());
                    student.setFkAreaStateIdEducation(institution.getFkAreaStateId());
                    student.setFkAreaCityIdEducation(institution.getFkAreaCityId());
                    student.setGmtModified(new Date());
                    student.setGmtModifiedUser("[StudentEducationBackgroundMatchJob]");
                    studentMapper.updateById(student);
                }
            }

            //国际背景
            fkInstitutionNameEducation = student.getFkInstitutionNameEducation2();
            if (GeneralTool.isEmpty(fkInstitutionNameEducation)){
                continue;
            }
            chineseName = MyStringUtils.getChineseString(fkInstitutionNameEducation);
            englishName = MyStringUtils.getEnglishString(fkInstitutionNameEducation);
            if (GeneralTool.isNotEmpty(englishName)) {
                englishName = englishName.toLowerCase();
            }
            if (GeneralTool.isNotEmpty(chineseName)||GeneralTool.isNotEmpty(englishName)||GeneralTool.isNotEmpty(fkInstitutionNameEducation)){
                Institution institution = institutionMapper.selectInstitutionByNameChnOrNameEng(chineseName,englishName,fkInstitutionNameEducation);
                if (GeneralTool.isNotEmpty(institution)){
                    student.setFkInstitutionIdEducation2(institution.getId());
                    student.setFkAreaCountryIdEducation2(institution.getFkAreaCountryId());
                    student.setFkAreaStateIdEducation2(institution.getFkAreaStateId());
                    student.setFkAreaCityIdEducation2(institution.getFkAreaCityId());
                    student.setGmtModified(new Date());
                    student.setGmtModifiedUser("[StudentEducationBackgroundMatchJob]");
                    studentMapper.updateById(student);
                }
            }
        }
        logger.info("匹配学生国内教育背景结束---------------");


    }
}
