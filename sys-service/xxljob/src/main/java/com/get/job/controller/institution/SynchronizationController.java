package com.get.job.controller.institution;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.tool.result.ResponseBo;
import com.get.core.tool.result.UpdateResponseBo;
import com.get.job.service.institution.SynchronizationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 数据同步控制类
 *
 * <AUTHOR>
 * @date 2021/9/13 15:03
 */
@Api(tags = "数据同步管理")
@RestController
@RequestMapping("schedule/institution/synchronization")
public class SynchronizationController {
    @Resource
    private SynchronizationService synchronizationService;

    @VerifyLogin(IsVerify = false)
    @ApiOperation(value = "RPA课程同步")
    @OperationLogger(module = LoggerModulesConsts.SALECENTER, type = LoggerOptTypeConst.LIST, description = "定时任务中心/数据同步/RPA课程同步")
    @GetMapping("autoUpdateInstitutionCourseData")
    public ResponseBo autoUpdateInstitutionCourseData() {
        synchronizationService.autoUpdateInstitutionCourseData();
        return UpdateResponseBo.ok();
    }

//    @ApiOperation(value = "文件同步")
//    @OperationLogger(module = LoggerModulesConsts.FILECENTER, type = LoggerOptTypeConst.ADD, description = "文件中心/下载文件测试接口")
//    @GetMapping("downloadAndUpload")
//    public ResponseBo downloadAndUpload() throws YException {
//        List<FileDto> fileDtos = synchronizationService.downloadAndUpload("8641791", "锆德教育业务问题.docx", "docx");
//        return new ListResponseBo<>(fileDtos);
//    }
}
