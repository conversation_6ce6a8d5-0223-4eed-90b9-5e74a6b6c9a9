package com.get.job.dao.dbgea;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.AplOldIssueOrder;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@DS("rpaIssuedb")
public interface IssueAplOrderMapper extends BaseMapper<AplOldIssueOrder> {

    //TODO 注释ISSUE相关功能 lucky  2024/12/23
//    List<AplOldIssueOrder> selectByStatus();

}