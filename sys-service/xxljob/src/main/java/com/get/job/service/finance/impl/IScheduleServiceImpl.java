package com.get.job.service.finance.impl;

import com.alibaba.fastjson.JSONObject;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dto.ExchangeRateDto;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.dto.ExchangeRateDto;
import com.get.job.service.finance.IScheduleService;
import com.get.job.utils.GetExchangeRateUtils;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ScheduledFuture;

/**
 * @author: Sea
 * @create: 2020/12/23 15:09
 * @verison: 1.0
 * @description: 实现接口
 * 参考网址：https://blog.csdn.net/qq_37342720/article/details/108417179
 */

@Configuration
@EnableScheduling
public class IScheduleServiceImpl implements IScheduleService {
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private TaskScheduler scheduler;
    @Resource
    private GetExchangeRateUtils getExchangeRateUtils;

    private ScheduledFuture future;

    @Override
    public void start() {
//        future = scheduler.schedule(new Runnable() {
//            @Override
//            public void run() {
        try {
            //为防止设置为某日期59分59秒 代码运行到下面设置日期时变为第二天 所以先获取到日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String format = sdf.format(new Date());
            Date getDate = sdf.parse(format);
            //系统配置-汇率开启/关闭(1/0)
            String getExchangeRate = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.GET_EXCHANGE_RATE.key).getData();
            if ("0".equals(getExchangeRate)) {
                return;
            }
            //系统配置-汇率参照币种编号
            String exchangeFromCurrencyType =
                    permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.EXCHANGE_FROM_CURRENCY_TYPE.key).getData();
            //系统配置-汇率目标币种编号
            String exchangeToCurrencyType = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.EXCHANGE_TO_CURRENCY_TYPE.key).getData();
            //批量新增对象集合
            List<ExchangeRateDto> exchangeRateVos = new ArrayList<>();
            for (String from : exchangeFromCurrencyType.split(",")) {
                for (String to : exchangeToCurrencyType.split(",")) {
                    //当参照币种和目标币种不一样时才进行
                    if (!from.equals(to)) {
                        ExchangeRateDto exchangeRateVo = new ExchangeRateDto();
                        //获取汇率
                        String exchangeRate = getExchangeRateUtils.getExchangeRateUtils(from, to);
                        String result = JSONObject.parseObject(JSONObject.parseObject(exchangeRate).get("result").toString()).get("rate").toString();
                        //设置要批量新增的vo
                        exchangeRateVo.setGetDate(getDate);
                        exchangeRateVo.setFkCurrencyTypeNumFrom(from);
                        exchangeRateVo.setFkCurrencyTypeNumTo(to);
                        exchangeRateVo.setExchangeRate(new BigDecimal(result));
                        exchangeRateVos.add(exchangeRateVo);
                    }
                }
            }
            if (GeneralTool.isNotEmpty(exchangeRateVos)) {
                financeCenterClient.batchAddAuTo(exchangeRateVos);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
//
//            }
//        }, new Trigger() {
//            @Override
//            public Date nextExecutionTime(TriggerContext triggerContext) {
//                String cron = getCorn();
//                CronTrigger trigger = new CronTrigger(cron);
//                return trigger.nextExecutionTime(triggerContext);
//            }
//        });
    }

    @Override
    public void stop() {
        if (GeneralTool.isNotEmpty(future)) {
            future.cancel(false);
        }
    }

    /**
     * @return java.lang.String
     * @Description :汇率每天获取时间
     * @Param []
     * <AUTHOR>
     */
//    private String getCorn() {
//        //默认定时凌晨过1秒
//        String corn = "1 0 0 * * *";
//        //系统配置-汇率每天获取时间 格式是 HH:mm:ss
//        String time = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.GET_EXCHANGE_RATE_TIME.key).getData();
//        String[] split = time.split(":");
//        if (GeneralTool.isNotEmpty(split)) {
//            //需要转换成cron表达式  格式为 ss mm HH * * *
//            String temp;
//            temp = split[0];
//            split[0] = split[2];
//            split[2] = temp;
//            corn = StringUtils.join(split, " ") + " * * *";
//        }
//        return corn;
//    }
    @Override
    public void setExchangeRateTime() {
        //重新运行获取汇率定时器
        stop();
        start();
    }
}
