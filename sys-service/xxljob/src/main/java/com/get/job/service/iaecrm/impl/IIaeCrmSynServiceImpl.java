package com.get.job.service.iaecrm.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.common.consts.LoggerModulesConsts;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.filecenter.vo.FileVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.job.dao.iaecrm.MAttachmentMapper;
import com.get.job.entity.MAttachment;
import com.get.job.service.iaecrm.IIaeCrmSynService;
import com.get.permissioncenter.entity.PermissionMediaAndAttached;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.vo.MediaAndAttachedVo;
import com.get.salecenter.entity.SaleMediaAndAttached;
import com.get.salecenter.feign.ISaleCenterClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileItemFactory;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.io.IOUtils;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class IIaeCrmSynServiceImpl extends ServiceImpl<MAttachmentMapper, MAttachment>  implements IIaeCrmSynService {
    @Resource
    private MAttachmentMapper mAttachmentMapper;
//    @Resource
//    private MSysAttachmentMapper mSysAttachmentMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private UtilService utilService;

    /**
     * 根据fkTableName来获取权限中心文件表的列表（司徒总会提前将IAE关联数据写入的文件表s_media_and_attached，文件guid为空，remark备注：[AttachmentFileID=1023]）
     * remark解析出AttachmentFileID，将该ID对应的文件上传到云OSS
     * 文件上传成功后，返回的GUID回写到文件表s_media_and_attached
     * 文件只支持逐个上传
     * @param fkTableNames
     */
    @Override
    public void synMAttachmentMethod(String fkTableNames) {
        List<String> tablesList = GeneralTool.toStrList(fkTableNames);
        List<PermissionMediaAndAttached> permissionMediaAndAttacheds = new ArrayList<>();
        List<String> queryTableNameList = new ArrayList<>();
        //如不为空则区分table来查询
        if(GeneralTool.isNotEmpty(tablesList))
        {
            for (String tableName : tablesList)
            {
                queryTableNameList.add(tableName);
                permissionMediaAndAttacheds  = permissionCenterClient.getMediaAndAttachedByIaeCrm(queryTableNameList);
                if(permissionMediaAndAttacheds.size()>0)
                {
                    this.uploadPermissionFileByList(tableName,permissionMediaAndAttacheds);
                }
                queryTableNameList.clear();//清空临时查询集合
            }
        }else
        {
            permissionMediaAndAttacheds  = permissionCenterClient.getMediaAndAttachedByIaeCrm(queryTableNameList);
            if(permissionMediaAndAttacheds.size()>0)
            {
                this.uploadPermissionFileByList("",permissionMediaAndAttacheds);
            }
        }
    }

    @Override
    public void synIaeSaleMAttachmentMethodJob() {

        List<SaleMediaAndAttached> mediaAndAttacheds = saleCenterClient.getMediaAndAttachedByIaeCrm();
        if(mediaAndAttacheds.size()>0 && mediaAndAttacheds.size()==177)
        {
            this.uploadSaleFileByList(mediaAndAttacheds);
        }

    }

    @Override
    public void downGeaAgentFileJob(String fkAgentIds) {
        List<String> fkAgentIds_1 = GeneralTool.toStrList(fkAgentIds);

        Set<String> fkAgentIds_01 = new HashSet<>();//存在文件ID
        Set<String> fkAgentIds_02 = new HashSet<>();//不存在文件ID
        Set<String> fkAgentIds_03 = new HashSet<>();//有合同资料，但没有合同文件



        System.out.println("======去重前代理总数=======>["+fkAgentIds_1.size()+"]");
        Set<String> fkAgentIds_ = fkAgentIds_1.stream().collect(Collectors.toSet());
        System.out.println("======去重后代理总数=======>["+fkAgentIds_.size()+"]");

        //根据单个代理ID下载文件
        for (String fkAgentId :fkAgentIds_)
        {
            List<String> queryFkAgentIds = new ArrayList<>();
            queryFkAgentIds.add(fkAgentId);

            //查找符合条件的文件,限定了只返回一个
            List<MediaAndAttachedVo> mediaAndAttacheds = saleCenterClient.getMediaAndAttachedByAgentIds(queryFkAgentIds);
            if(mediaAndAttacheds!=null && mediaAndAttacheds.size()>0)
            {
                MediaAndAttachedVo mediaAndAttachedVo = mediaAndAttacheds.get(0);
                //下载文件到本地
                FileVo fileVo = new FileVo();
                fileVo.setFileKey(mediaAndAttachedVo.getFileKey());
                fileVo.setFileNameOrc(mediaAndAttachedVo.getFileNameOrc());
                //判断文件是否存在，存在则无需重复下载
                String fileName = fkAgentId+ mediaAndAttachedVo.getFileTypeOrc();
                String filePath = "D:\\BaiduDown\\agentFile\\"+fileName;
                File file = new File(filePath);
                if(file.exists())
                {
                    fkAgentIds_01.add(fkAgentId);
//                    System.out.println("======已下载文件代理id=======>fkAgentId:["+fkAgentId+"]");
                    //已存在则取消下载
                    continue;
                }

                Result<SaleFileDto> result = fileCenterClient.getDownloadFile(fileVo);
                if(result.isSuccess())
                {
                    SaleFileDto saleFileDto = result.getData();
                    if(GeneralTool.isNotEmpty(saleFileDto) && GeneralTool.isNotEmpty(saleFileDto.getBytes()))
                    {
                        //重命名
//                        String fileName = fkAgentId+mediaAndAttachedVo.getFileTypeOrc();
//                        String filePath = "D:\\BaiduDown\\agentFile\\"+fileName;
//                        File file = new File(filePath);
//                        if(file.exists())
//                        {
//                            file.delete();
//                        }

                        //輸出文件到本地路徑，文件名：代理ID+文件后缀
                        try {
                            FileOutputStream fileOutputStream = new FileOutputStream(file);
                            fileOutputStream.write(saleFileDto.getBytes(),0,saleFileDto.getBytes().length);
                            fileOutputStream.flush();;
                            fileOutputStream.close();
                            Thread.sleep(500);
                            fkAgentIds_01.add(fkAgentId);
//                            System.out.println("======已下载文件代理id=======>fkAgentId:["+fkAgentId+"]");
                        }catch (Exception e)
                        {
                            fkAgentIds_02.add(fkAgentId);
                            e.printStackTrace();
                        }
                    }else
                    {
                        fkAgentIds_03.add(fkAgentId);
//                        System.out.println("======代理id没有找到文件内容=======>fkAgentId:["+fkAgentId+"] and fileKey:["+mediaAndAttachedVo.getFileKey()+"]");
                    }
                }
            }else
            {
                fkAgentIds_02.add(fkAgentId);
//                System.out.println("======代理id没有合同文件=======>:["+fkAgentId+"]");
            }
        }
        if(fkAgentIds_01.size()>0)
        {
            System.out.println("======已下载文件代理ids=======>fkAgentIds:["+fkAgentIds_01.size()+"]["+ String.join(",",fkAgentIds_01)+"]");
        }
        if(fkAgentIds_02.size()>0)
        {
            System.out.println("======代理id没有合同文件=======>:["+fkAgentIds_02.size()+"]["+String.join(",",fkAgentIds_02)+"]");
        }
        if(fkAgentIds_03.size()>0)
        {
            System.out.println("======代理id有合同文件资料，但是没有文件=======>:["+fkAgentIds_03.size()+"]["+String.join(",",fkAgentIds_03)+"]");
        }


    }

//    /**
//     * 同步CRM的新闻数据到GEA（指定公司ID为HTI，插入GEA数据库，自动同步到HTI数据库）
//     */
//    @Override
//    public void synCrmNewsToBmsMethod() {
//        int pageNum = 500;//分页500
//        int currentPage = 1; //当前页
//        Long total = 0L;
//        List<CrmNewsDto> crmNewsDtos = new ArrayList<>();
//
//        // 先查询总数：
//        IPage<CrmNewsDto> iPage = GetCondition.getPage(PageUtil.convertToQuery(currentPage, pageNum));
//        crmNewsDtos = mSysAttachmentMapper.getHtiNews(iPage);
//        total = iPage.getTotal();
//
//        // 计算总页数
//        int totalPages = (int) Math.ceil(total / (double) pageNum);
////        totalPages = 1;        //临时调试5条数据
//        // 分页获取符合条件的所有数据
//        if (totalPages > 0) {
//            //获取所有的类型
//            List<NewsTypeVo> newsTypeDtos = institutionCenterClient.getNewsTypes();
//            List<AreaCountryVo>  areaCountryDtos = institutionCenterClient.getAllCountry();
//            for (int i = 1; i <= totalPages; i++) {
////                iPage.setPages(5);//临时调试
//                iPage.setCurrent(i);
//                crmNewsDtos = mSysAttachmentMapper.getHtiNews(iPage);
//                if(crmNewsDtos.size()>0)
//                {
//                    for (CrmNewsDto crmNewsDto:crmNewsDtos)
//                    {
////                        if(crmNewsDto.getNewsID()<=18000)
////                        {
////                            continue;
////                        }
//                        //{"companyId":2,"fkTableId":"","description":"<p>1111111</p>","publicLevel":"1","tableName":"","title":"testsetst1111","profile":"",
//                        // "fkNewsTypeIdRecommend":"","fkNewsTypeId":"","webTitle":"","webMetaDescription":"","webMetaKeywords":"","publishTime":"2023-03-28",
//                        // "effectiveStartTime":"","effectiveEndTime":"","gotoUrl":"","rnewsTypeVo":[{"fkNewsId":"","fkNewsTypeId":13,"fkTableId":4,
//                        // "fkTableName":"u_area_country","uniqueKey":1679996227524,"id":""}]}
//                        NewsDto newsVo = new NewsDto();
//                        newsVo.setCompanyId(3L);//指定HTI公司
//                        newsVo.setTitle(crmNewsDto.getNewsTitle());
//                        newsVo.setDescription(crmNewsDto.getNewsContent());
//                        newsVo.setPublicLevel("1");//1公开
//                        if(GeneralTool.isNotEmpty(crmNewsDto.getCrtDate()))
//                        {
//                            newsVo.setPublishTime(DateUtil.toDate(crmNewsDto.getCrtDate()));//发布时间理论就是创建时间
//                            newsVo.setGmtCreate(DateUtil.toDate(crmNewsDto.getCrtDate()));
//                        }
//                        newsVo.setGmtCreateUser(crmNewsDto.getCrtUserID());//创建人
//                        newsVo.setGmtModifiedUser("HTI-NEWS");//修改人变更为固定人员，方便数据核对，无误后删除
//                        if(GeneralTool.isNotEmpty(crmNewsDto.getSendEmailTime()))
//                        {
//                            newsVo.setSendEmailTime(DateUtil.toDate(crmNewsDto.getSendEmailTime()));
//                        }
//
//                        //新闻类型集合
//                        RNewsTypeDto rNewsTypeVo = new RNewsTypeDto();
//
//                        if(GeneralTool.isNotEmpty(crmNewsDto.getClassName()) && "奖学金信息".equals(crmNewsDto.getClassName()))
//                        {
//                            crmNewsDto.setClassName("奖学金资讯");
//                        }
//
//                        if(GeneralTool.isNotEmpty(newsTypeDtos))
//                        {
//                            for(NewsTypeVo newsTypeDto : newsTypeDtos)
//                            {
//                                //CM >> 新闻信息   包含  CM 和 新闻信息
//                                if(GeneralTool.isNotEmpty(newsTypeDto.getTypeName()) && newsTypeDto.getTypeName().contains(crmNewsDto.getClassType()) && newsTypeDto.getTypeName().contains(crmNewsDto.getClassName()))
//                                {
//                                    rNewsTypeVo.setFkNewsTypeId(newsTypeDto.getId());//新闻类型id
//                                    break;
//                                }
//                            }
//                        }
//                        //匹配国家,纠正数据
//                        if(GeneralTool.isNotEmpty(crmNewsDto.getInstitutionCountryName()))
//                        {
//                            if(crmNewsDto.getInstitutionCountryName().equals("阿联酋"))
//                            {
//                                crmNewsDto.setInstitutionCountryName("迪拜（阿联酋）");
//                            }
//                            if(crmNewsDto.getInstitutionCountryName().equals("香港"))
//                            {
//                                crmNewsDto.setInstitutionCountryName("中国香港");
//                            }
//                        }
//                        if(GeneralTool.isNotEmpty(crmNewsDto.getInstitutionCountryName()) && GeneralTool.isNotEmpty(areaCountryDtos))
//                        {
//                            for (AreaCountryVo areaCountryDto :areaCountryDtos){
//                                if(areaCountryDto.getFullName().contains(crmNewsDto.getInstitutionCountryName()))
//                                {
//                                    rNewsTypeVo.setFkTableId(areaCountryDto.getId());//目标类型id
//                                    break;
//                                }
//                            }
//                        }
//                        rNewsTypeVo.setFkTableName("u_area_country");//目标类型名称，指定u_area_country
//
//                        List<RNewsTypeDto> rNewsTypeVos = new ArrayList<>();
//                        rNewsTypeVos.add(rNewsTypeVo);
//                        newsVo.setRNewsTypeDto(rNewsTypeVos);
//
//                        //新增新闻
//                        //判断是否存在，已存在则返回不用重复插入（支持增量）
//                        Boolean checkResult = institutionCenterClient.checkNews(newsVo);
//                        if(checkResult)
//                        {
//                            log.info("HTI新闻ID：{}==》已存在",crmNewsDto.getNewsID());
//                            continue;
//                        }
//                        Long newsId = institutionCenterClient.addNews(newsVo);
//                        log.info("HTI新闻ID：{}==》BMS新闻ID{}",crmNewsDto.getNewsID(),newsId);
//                        //如返回不为空
//                        if(newsId!=null)
//                        {
//                            //上传附件
//                            List<MSysAttachment> mSysAttachments = mSysAttachmentMapper.selectList(Wrappers.<MSysAttachment>lambdaQuery()
//                                    .eq(MSysAttachment::getTarget,"News")
//                                    .eq(MSysAttachment::getTargetID,crmNewsDto.getNewsID()));
//
//                            if(GeneralTool.isNotEmpty(mSysAttachments))
//                            {
//                                List<MediaAndAttachedDto> mediaAttachedVos = new ArrayList<>();//附件集合
//                                for(MSysAttachment mSysAttachment:mSysAttachments)
//                                {
//                                    //根据attachmentFileId查询文件
//                                    //可能有多个附件
//                                    MAttachment mAttachment = this.mAttachmentMapper.selectById(mSysAttachment.getAttachmentFileID());
//                                    if(GeneralTool.isNotEmpty(mAttachment))
//                                    {
//                                        log.info("HTI新闻ID：{}==》HTI文件ID{}==>HTI文件总数{}",crmNewsDto.getNewsID(),mSysAttachment.getAttachmentFileID(),mSysAttachments.size());
//                                        MultipartFile multipartFile = this.getMultipartFile(mAttachment.getFileName(),mAttachment.getAttachmentFile());
//                                        MultipartFile[] multipartFiles = new MultipartFile[]{multipartFile};
//                                        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.INSTITUTIONCENTER);
//                                        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                                            List<FileDto> fileDtos = result.getData();
//                                            if(GeneralTool.isNotEmpty(fileDtos))
//                                            {
//                                                for(FileDto fileDto:fileDtos)
//                                                {
//                                                    MediaAndAttachedDto mediaAndAttachedVo = new MediaAndAttachedDto();
//                                                    mediaAndAttachedVo.setFileNameOrc(fileDto.getFileNameOrc());
//                                                    mediaAndAttachedVo.setFilePath(fileDto.getFilePath());
//                                                    mediaAndAttachedVo.setFkFileGuid(fileDto.getFileGuid());
//                                                    mediaAndAttachedVo.setFkTableId(newsId);//新闻ID
//                                                    mediaAndAttachedVo.setTypeKey("institution_news_mail_appendix");//新闻附件类型
//                                                    mediaAndAttachedVo.setFileKey(fileDto.getFileKey());
//                                                    mediaAttachedVos.add(mediaAndAttachedVo);
//                                                }
//                                            }
//                                        } else {
//                                            log.info("upload新闻文件任务：新闻id[{}],文件id[{}]上传失败",newsId,mAttachment.getAttachmentID());
//                                        }
//                                    }
//                                }
//                                //更新新闻附件信息到附件相关表中
//                                if(GeneralTool.isNotEmpty(mediaAttachedVos))
//                                {
//                                    //news/addMediaAndAttached
//                                    //[{"fileNameOrc":"微信图片_20230328221204.png","filePath":"/files/2023/03/29/17c10577-21ed-47bd-a608-62060ec0f894.png",
//                                    // "fkFileGuid":"5d9544a6a41d4f8787dff9ab9de3c079","fkTableId":"1611","name":"微信图片_20230328221204.png",
//                                    // "typeKey":"institution_news_mail_appendix","fileKey":"/files/2023/03/29/17c10577-21ed-47bd-a608-62060ec0f894.png",
//                                    // "uid":1680059893763,"status":"success"}]
//                                    institutionCenterClient.addNewsMedia(mediaAttachedVos);
//                                }
//                            }
//                        }
//                        try {
//                            Thread.sleep(200);//
//                        }catch (Exception e)
//                        {
//                            System.out.println(e.fillInStackTrace());
//                        }
//                    }
//
//                }
//
//            }
//        }
//
//
//
//
//    }

    /**
     * 上传IAE权限中心的文件
     * @param tableName
     * @param permissionMediaAndAttacheds
     * @return
     */
    private Boolean uploadPermissionFileByList(String tableName, List<PermissionMediaAndAttached> permissionMediaAndAttacheds)
    {
        log.info("upload文件任务：[{}],PermissionMediaAndAttached表一共有{}条数据",GeneralTool.isEmpty(tableName)?"全部table":tableName,permissionMediaAndAttacheds.size());
        //上传文件、上传成功后只需要回写fileGUID修改时间到PermissionMediaAndAttached即可
//        List<Long> attachmentFileIds = permissionMediaAndAttacheds.stream().map(permissionMediaAndAttached-> Long.parseLong(permissionMediaAndAttached.getRemark().substring(1,permissionMediaAndAttached.getRemark().length()-1).split("=")[1])).distinct().collect(Collectors.toList());

        for (PermissionMediaAndAttached permissionMediaAndAttached:permissionMediaAndAttacheds)
        {
            Long attachmentFileId = Long.parseLong(permissionMediaAndAttached.getRemark().substring(1,permissionMediaAndAttached.getRemark().length()-1).split("=")[1]);
            if(GeneralTool.isEmpty(attachmentFileId))
            {
                continue;
            }
            //根据attachmentFileId查询文件
            MAttachment mAttachment = this.mAttachmentMapper.selectById(attachmentFileId);
            if(GeneralTool.isNotEmpty(mAttachment))
            {
                MultipartFile multipartFile = this.getMultipartFile(mAttachment.getFileName(),mAttachment.getAttachmentFile());
                MultipartFile[] multipartFiles = new MultipartFile[]{multipartFile};
                Result<List<FileDto>> result = fileCenterClient.uploadAppendix(multipartFiles, LoggerModulesConsts.PERMISSIONCENTER);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    List<FileDto> fileDtos = result.getData();
                    permissionMediaAndAttached.setFkFileGuid(fileDtos.get(0).getFileGuid());
                    utilService.setUpdateInfo(permissionMediaAndAttached);//更新修改时间
                    Boolean updateResult = permissionCenterClient.updateMediaAndAttachedById(permissionMediaAndAttached);
                    if(!updateResult)
                    {
                        log.info("upload文件任务：permissionMediaAndAttached表id[{}],文件id[{}]上传失败，更新GUID失败：文件名称[{}],GUID[{}]",permissionMediaAndAttached.getId(),attachmentFileId,fileDtos.get(0).getFileName(),fileDtos.get(0).getFileGuid());
                    }
                } else {
                    log.info("upload文件任务：permissionMediaAndAttached表id[{}],文件id[{}]上传失败",permissionMediaAndAttached.getId(),attachmentFileId);
                }
//                log.info("upload文件任务：permissionMediaAndAttached表id[{}],文件id[{}],上传成功",permissionMediaAndAttached.getId(),attachmentFileId);
            }else
            {
                log.info("upload文件任务：permissionMediaAndAttached表id[{}],指定的文件id[{}],不存在",permissionMediaAndAttached.getId(),attachmentFileId);
            }

            //休息50毫秒
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        return null;
    }


    /**
     * 上传IAE销售中心的文件
     * @param mediaAndAttacheds
     * @return
     */
    private Boolean uploadSaleFileByList(List<SaleMediaAndAttached> mediaAndAttacheds)
    {
        log.info("upload文件任务：SaleMediaAndAttached表一共有{}条数据",mediaAndAttacheds.size());
        for (SaleMediaAndAttached mediaAndAttached:mediaAndAttacheds)
        {
            Long attachmentFileId = Long.parseLong(mediaAndAttached.getRemark().substring(1,mediaAndAttached.getRemark().length()-1).split("=")[1]);
            if(GeneralTool.isEmpty(attachmentFileId))
            {
                continue;
            }
            //根据attachmentFileId查询文件
            MAttachment mAttachment = this.mAttachmentMapper.selectById(attachmentFileId);
            if(GeneralTool.isNotEmpty(mAttachment))
            {
                MultipartFile multipartFile = this.getMultipartFile(mAttachment.getFileName(),mAttachment.getAttachmentFile());
                MultipartFile[] multipartFiles = new MultipartFile[]{multipartFile};
                Result<List<FileDto>> result = fileCenterClient.uploadAppendix(multipartFiles, LoggerModulesConsts.SALECENTER);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    List<FileDto> fileDtos = result.getData();
                    mediaAndAttached.setFkFileGuid(fileDtos.get(0).getFileGuid());
                    utilService.setUpdateInfo(mediaAndAttached);//更新修改时间
                    Boolean updateResult = saleCenterClient.updateMediaAndAttachedById(mediaAndAttached);
                    if(!updateResult)
                    {
                        log.info("upload文件任务：SaleMediaAndAttached表id[{}],文件id[{}]上传失败，更新GUID失败：文件名称[{}],GUID[{}]",mediaAndAttached.getId(),attachmentFileId,fileDtos.get(0).getFileName(),fileDtos.get(0).getFileGuid());
                    }
                } else {
                    log.info("upload文件任务：SaleMediaAndAttached表id[{}],文件id[{}]上传失败",mediaAndAttached.getId(),attachmentFileId);
                }
//                log.info("upload文件任务：permissionMediaAndAttached表id[{}],文件id[{}],上传成功",permissionMediaAndAttached.getId(),attachmentFileId);
            }else
            {
                log.info("upload文件任务：SaleMediaAndAttached表id[{}],指定的文件id[{}],不存在",mediaAndAttached.getId(),attachmentFileId);
            }

            //休息50毫秒
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }         }
        return null;
    }

    //二进制文件转换MultipartFile，该方法会在项目路径新增文件
    public MultipartFile getMultipartFile(String name, byte[] bytes) {
        MultipartFile mfile = null;
        ByteArrayInputStream in = null;
        try {
            in = new ByteArrayInputStream(bytes);
            FileItemFactory factory = new DiskFileItemFactory(16, null);
            FileItem fileItem = factory.createItem("mainFile", "text/plain", false, name);
            IOUtils.copy(new ByteArrayInputStream(bytes), fileItem.getOutputStream());
            mfile = new CommonsMultipartFile(fileItem);
            in.close();
        }catch (Exception e){
            e.printStackTrace();
        }
        return mfile;
    }

    private MultipartFile getMultipartFile2(String name, byte[] bytes) {
        MockMultipartFile mockMultipartFile = null;
        try {
//            MockMultipartFile(String name, @Nullable String originalFilename, @Nullable String contentType, @Nullable byte[] content)
            mockMultipartFile = new MockMultipartFile("name",name,"text/plain", bytes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return mockMultipartFile;
    }

}
