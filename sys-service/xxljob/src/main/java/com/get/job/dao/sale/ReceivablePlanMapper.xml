<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.sale.ReceivablePlanMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.ReceivablePlan" useGeneratedKeys="true"
          keyProperty="id">
    insert into m_receivable_plan (id, fk_type_key, fk_type_target_id, 
      summary, fk_currency_type_num, tuition_amount, 
      commission_rate, net_rate, commission_amount,
      fixed_amount, bonus_amount, receivable_amount, 
      receivable_plan_date, fk_receivable_reason_id, 
      status, id_gea_finance, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkTypeKey,jdbcType=VARCHAR}, #{fkTypeTargetId,jdbcType=BIGINT}, 
      #{summary,jdbcType=VARCHAR}, #{fkCurrencyTypeNum,jdbcType=VARCHAR}, #{tuitionAmount,jdbcType=DECIMAL}, 
      #{commissionRate,jdbcType=DECIMAL}, #{netRate,jdbcType=DECIMAL}, #{commissionAmount,jdbcType=DECIMAL},
      #{fixedAmount,jdbcType=DECIMAL}, #{bonusAmount,jdbcType=DECIMAL}, #{receivableAmount,jdbcType=DECIMAL}, 
      #{receivablePlanDate,jdbcType=TIMESTAMP}, #{fkReceivableReasonId,jdbcType=INTEGER}, 
      #{status,jdbcType=INTEGER}, #{idGeaFinance,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.ReceivablePlan" useGeneratedKeys="true"
          keyProperty="id">
    insert into m_receivable_plan
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkTypeKey != null">
        fk_type_key,
      </if>
      <if test="fkTypeTargetId != null">
        fk_type_target_id,
      </if>
      <if test="summary != null">
        summary,
      </if>
      <if test="fkCurrencyTypeNum != null">
        fk_currency_type_num,
      </if>
      <if test="tuitionAmount != null">
        tuition_amount,
      </if>
      <if test="commissionRate != null">
        commission_rate,
      </if>
      <if test="netRate != null">
        net_rate,
      </if>
      <if test="commissionAmount != null">
        commission_amount,
      </if>
      <if test="fixedAmount != null">
        fixed_amount,
      </if>
      <if test="bonusAmount != null">
        bonus_amount,
      </if>
      <if test="receivableAmount != null">
        receivable_amount,
      </if>
      <if test="receivablePlanDate != null">
        receivable_plan_date,
      </if>
      <if test="fkReceivableReasonId != null">
        fk_receivable_reason_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="idGeaFinance != null">
        id_gea_finance,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkTypeKey != null">
        #{fkTypeKey,jdbcType=VARCHAR},
      </if>
      <if test="fkTypeTargetId != null">
        #{fkTypeTargetId,jdbcType=BIGINT},
      </if>
      <if test="summary != null">
        #{summary,jdbcType=VARCHAR},
      </if>
      <if test="fkCurrencyTypeNum != null">
        #{fkCurrencyTypeNum,jdbcType=VARCHAR},
      </if>
      <if test="tuitionAmount != null">
        #{tuitionAmount,jdbcType=DECIMAL},
      </if>
      <if test="commissionRate != null">
        #{commissionRate,jdbcType=DECIMAL},
      </if>
      <if test="netRate != null">
        #{netRate,jdbcType=DECIMAL},
      </if>
      <if test="commissionAmount != null">
        #{commissionAmount,jdbcType=DECIMAL},
      </if>
      <if test="fixedAmount != null">
        #{fixedAmount,jdbcType=DECIMAL},
      </if>
      <if test="bonusAmount != null">
        #{bonusAmount,jdbcType=DECIMAL},
      </if>
      <if test="receivableAmount != null">
        #{receivableAmount,jdbcType=DECIMAL},
      </if>
      <if test="receivablePlanDate != null">
        #{receivablePlanDate,jdbcType=TIMESTAMP},
      </if>
      <if test="fkReceivableReasonId != null">
        #{fkReceivableReasonId,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="idGeaFinance != null">
        #{idGeaFinance,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <select id="getSettlementInstallmentData" resultType="com.get.job.dto.SettlementInstallmentListDto">
      SELECT rp.id AS receivablePlanId, b.id AS payablePlanId,
             a.receivable_amount,a.amount_receivable,b.payable_amount,b.amount_payable,b.payable_amount - b.amount_payable AS difference
      FROM ais_sale_center.m_receivable_plan AS rp
             INNER JOIN ais_sale_center.m_student_offer_item AS msoi ON msoi.id = rp.fk_type_target_id AND rp.fk_type_key = 'm_student_offer_item'
-- 应收=实收的应收计划
             INNER JOIN (
        SELECT
          mrp.id,mrp.receivable_amount,
          IFNULL(SUM( mrfi.amount_receivable ),0) AS amount_receivable

        FROM
          ais_finance_center.m_receipt_form_item AS mrfi
            INNER JOIN ais_sale_center.m_receivable_plan AS mrp ON mrp.id = mrfi.fk_receivable_plan_id
        WHERE
          mrp.gmt_create_user = 'admin-1'
          AND mrp.status = 1
        GROUP BY
          mrp.id
        HAVING
          mrp.receivable_amount - amount_receivable = 0
      )a ON a.id = rp.id

     INNER JOIN (
        SELECT
          mpp.id,
          mpp.fk_receivable_plan_id,
          mpp.payable_amount,
          IFNULL(SUM( mpfi.amount_payable),0) AS amount_payable
        FROM
          ais_sale_center.m_payable_plan AS mpp
            LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi ON  mpfi.fk_payable_plan_id = mpp.id
        WHERE
          mpp.gmt_create_user IN ('admin-1','admin-2','admin-x')
          AND mpp.status = 1
        GROUP BY
          mpp.id
        HAVING mpp.payable_amount - amount_payable > 0
      )b ON b.fk_receivable_plan_id = rp.id
      where msoi.defer_opening_time >= '2021-09-01 00:00:00' AND a.receivable_amount>=b.payable_amount
    </select>
</mapper>