//package com.get.job.component;
//
//import com.baomidou.mybatisplus.core.toolkit.Wrappers;
//import com.get.common.eunms.ProjectExtraEnum;
//import com.get.core.mybatis.base.UtilService;
//import com.get.financecenter.entity.ReceiptFormItem;
//import com.get.job.dao.finance.ReceiptFormItemMapper;
//import com.get.job.dao.sale.PayablePlanSettlementInstallmentMapper;
//import com.get.job.dao.sale.ReceivablePlanMapper;
//import com.get.job.vo.SettlementInstallmentListDto;
//import com.get.salecenter.entity.PayablePlanSettlementInstallment;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.math.BigDecimal;
//import java.sql.Wrapper;
//import java.util.Date;
//import java.util.List;
//
///**
// * 财务佣金辅助处理类
// *
// * <AUTHOR>
// * @date 2022/4/28 11:21
// */
//@Component
//public class SettlementInstallmentHelper {
//
//    @Resource
//    private ReceivablePlanMapper receivablePlanMapper;
//    @Resource
//    private ReceiptFormItemMapper receiptFormItemMapper;
//    @Resource
//    private PayablePlanSettlementInstallmentMapper payablePlanSettlementInstallmentMapper;
//    @Resource
//    private UtilService utilService;
//
//
//    /**
//     * 财务佣金同步
//     *
//     * @Date 11:30 2022/4/28
//     * <AUTHOR>
//     */
//    public void insertSettlementInstallment() {
//        List<SettlementInstallmentListDto> settlementInstallmentListDtoList = receivablePlanMapper.getSettlementInstallmentData();
//        for (SettlementInstallmentListDto settlementInstallmentListDto : settlementInstallmentListDtoList) {
//            List<ReceiptFormItem> receiptFormItems = receiptFormItemMapper.selectList(Wrappers.<ReceiptFormItem>lambdaQuery().eq(ReceiptFormItem::getFkReceivablePlanId, settlementInstallmentListDto.getReceivablePlanId()));
//            for (int i = 0; i < receiptFormItems.size(); i++) {
//                ReceiptFormItem receiptFormItem = receiptFormItems.get(i);
//                if (i == receiptFormItems.size() - 1) {
//                    PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
//                    payablePlanSettlementInstallment.setFkPayablePlanId(settlementInstallmentListDto.getPayablePlanId());
//                    payablePlanSettlementInstallment.setFkReceiptFormItemId(receiptFormItem.getId());
//                    payablePlanSettlementInstallment.setAmountExpect(settlementInstallmentListDto.getDifference());
//                    payablePlanSettlementInstallment.setAmountActual(settlementInstallmentListDto.getDifference());
//                    payablePlanSettlementInstallment.setAmountActualInit(settlementInstallmentListDto.getDifference());
//                    payablePlanSettlementInstallment.setServiceFeeExpect(new BigDecimal(0));
//                    payablePlanSettlementInstallment.setServiceFeeActual(new BigDecimal(0));
//                    payablePlanSettlementInstallment.setServiceFeeActualInit(new BigDecimal(0));
//                    payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
//                    payablePlanSettlementInstallment.setGmtCreate(new Date());
//                    payablePlanSettlementInstallment.setGmtCreateUser("2021-09-01");
//                    payablePlanSettlementInstallmentMapper.insert(payablePlanSettlementInstallment);
//                } else {
//                    PayablePlanSettlementInstallment payablePlanSettlementInstallment = new PayablePlanSettlementInstallment();
//                    payablePlanSettlementInstallment.setFkPayablePlanId(settlementInstallmentListDto.getPayablePlanId());
//                    payablePlanSettlementInstallment.setFkReceiptFormItemId(receiptFormItem.getId());
//                    payablePlanSettlementInstallment.setAmountExpect(new BigDecimal(0));
//                    payablePlanSettlementInstallment.setAmountActual(new BigDecimal(0));
//                    payablePlanSettlementInstallment.setAmountActualInit(new BigDecimal(0));
//                    payablePlanSettlementInstallment.setServiceFeeExpect(new BigDecimal(0));
//                    payablePlanSettlementInstallment.setServiceFeeActual(new BigDecimal(0));
//                    payablePlanSettlementInstallment.setServiceFeeActualInit(new BigDecimal(0));
//                    payablePlanSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
//                    payablePlanSettlementInstallment.setGmtCreate(new Date());
//                    payablePlanSettlementInstallment.setGmtCreateUser("2021-09-01");
//                    payablePlanSettlementInstallmentMapper.insert(payablePlanSettlementInstallment);
//                }
//            }
//
//        }
//
//    }
//
//
//}
