package com.get.job.jobhandler;

import com.get.job.service.historytable.IHistoryTableService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

@Component
public class HistoryTableJob {
    private static final Logger logger = LoggerFactory.getLogger(HistoryTableJob.class);
    @Resource
    private IHistoryTableService historyTableService;


    /**
     * 定时按季度创建历史表，并将3个月以前的数据插入到各自的历史表中
     */
    @XxlJob("historyTableHandle")
    private void historyTableHandle() {
        try {
//            String fkTableNames = XxlJobHelper.getJobParam();//获取需要创建历史表的实体表名
//            if(StringUtils.isEmpty(fkTableNames))
//            {
//                XxlJobHelper.log("historyTableHandle执行任务尚未配置表名，取消执行");
//                return;
//            }
            XxlJobHelper.log("historyTableHandle执行任务开始");
            historyTableService.historyTableHandle();
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[historyTableHandle执行任务开始 error]");
            logger.error("func[{}] e[{}-{}] desc[historyTableHandle执行任务开始 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));

        }
    }
}
