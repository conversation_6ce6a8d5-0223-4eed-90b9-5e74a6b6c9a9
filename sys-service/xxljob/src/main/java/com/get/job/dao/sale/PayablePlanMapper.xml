<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.sale.PayablePlanMapper">

    <select id="getNegativeCommission" resultType="com.get.job.dto.NegativeCommissionDto">
SELECT
        mpp2.id,
        mpp2.payable_amount - IFNULL( mpp4.paidAmount, 0 ) AS differenceAmount
    FROM
        ais_sale_center.m_payable_plan AS mpp2
        LEFT JOIN (
        SELECT
            mpp3.id,
            IFNULL( SUM( mpfi2.amount_payable ), 0 ) + IFNULL( SUM( mpfi2.amount_exchange_rate ), 0 ) AS paidAmount
        FROM
            ais_sale_center.m_payable_plan AS mpp3
            LEFT JOIN ais_finance_center.m_payment_form_item AS mpfi2 ON mpfi2.fk_payable_plan_id = mpp3.id
            LEFT JOIN ais_finance_center.m_payment_form AS mpf2 ON mpf2.id = mpfi2.fk_payment_form_id
        WHERE
            mpp3.STATUS = 1
            AND mpf2.STATUS = 1
        GROUP BY
        mpp3.id
        ) mpp4 ON mpp4.id = mpp2.id
        WHERE mpp2.payable_amount >= 0
        AND mpp2.gmt_create >= '2022-01-01 00:00:00'
        AND NOT EXISTS (SELECT 1 FROM ais_finance_center.r_payable_plan_settlement_installment AS rppsi WHERE rppsi.fk_payable_plan_id = mpp2.id AND rppsi.status IN (0,1))
        HAVING differenceAmount &lt; 0
    </select>

</mapper>