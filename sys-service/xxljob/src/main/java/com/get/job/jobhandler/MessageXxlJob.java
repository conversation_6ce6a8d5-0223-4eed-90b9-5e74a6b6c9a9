package com.get.job.jobhandler;

import com.get.remindercenter.feign.IReminderCenterClient;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * Time: 15:53
 * Date: 2022/3/31
 * Description:
 */
@Component
public class MessageXxlJob {
    private static final Logger logger = LoggerFactory.getLogger(MessageXxlJob.class);

    @Resource
    IReminderCenterClient reminderCenterClient;

    @XxlJob("performTasks")
    public void sendMessage() {
        try {
            XxlJobHelper.log("发送队列提醒开始");
            reminderCenterClient.performTasks();
            XxlJobHelper.log("发送队列提醒结束");
//            if (redisClient.getRedisLock(CacheKeyConstants.MESSAGE_TASK_LOCK_KEY)) {
//                feignReminderCenter.performTasks();
//            }
        } catch (Exception e) {
            e.getStackTrace();
            logger.error("func[{}] e[{}-{}] desc[发送队列提醒定时任务 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }
    }


    @XxlJob("sendEmailScheduleTask")
    public void sendEmail() {
        try {
            XxlJobHelper.log("新机制--发送队列提醒开始");
           reminderCenterClient.sendEmailScheduleTask();
           XxlJobHelper.log("新机制--发送队列提醒结束");
        } catch (Exception e) {
            e.getStackTrace();
            logger.error("func[{}] e[{}-{}] desc[新机制--发送队列提醒定时任务 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }
    }


}
