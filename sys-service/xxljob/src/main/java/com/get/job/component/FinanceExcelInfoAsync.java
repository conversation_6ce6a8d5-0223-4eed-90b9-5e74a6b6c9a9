package com.get.job.component;


import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.GetStringUtils;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.entity.PaymentForm;
import com.get.financecenter.entity.PaymentFormItem;
import com.get.financecenter.entity.ReceiptForm;
import com.get.financecenter.entity.ReceiptFormItem;
import com.get.institutioncenter.entity.Institution;
import com.get.job.dao.finance.PaymentFormItemMapper;
import com.get.job.dao.finance.PaymentFormMapper;
import com.get.job.dao.finance.ReceiptFormItemMapper;
import com.get.job.dao.finance.ReceiptFormMapper;
import com.get.job.dao.institution.InstitutionCourseMapper;
import com.get.job.dao.institution.InstitutionMapper;
import com.get.job.dao.log.LogFinanceToBmsMapper;
import com.get.job.dao.sale.AgentMapper;
import com.get.job.dao.sale.PayablePlanMapper;
import com.get.job.dao.sale.ReceivablePlanMapper;
import com.get.job.dao.sale.StudentMapper;
import com.get.job.dao.sale.StudentOfferItemMapper;
import com.get.job.dao.sale.StudentOfferMapper;
import com.get.job.dto.CourseMatchDto;
import com.get.job.dto.CourseMatchScoreDto;
import com.get.job.dto.CourseNameMatchDto;
import com.get.job.dto.SchoolMatchDto;
import com.get.job.entity.FinanceExcelInfo;
import com.get.job.entity.LogFinanceToBms;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.entity.PayablePlan;
import com.get.salecenter.entity.ReceivablePlan;
import com.get.salecenter.entity.Student;
import com.get.salecenter.entity.StudentOffer;
import com.get.salecenter.entity.StudentOfferItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/12/15 15:07
 */
@Component
public class FinanceExcelInfoAsync {

//    private static final Logger logger = LoggerFactory.getLogger(FinanceExcelInfoAsync.class);
//    /**
//     * 算法地址
//     */
//    @Value(value = "${algorithmAddress}")
//    private String algorithmAddress;
//    /**
//     * 精确算法地址
//     */
//    @Value(value = "${exactAlgorithmAddress}")
//    private String exactAlgorithmAddress;
//    @Resource
//    private StudentMapper studentMapper;
//    @Resource
//    private StudentOfferMapper studentOfferMapper;
//    @Resource
//    private StudentOfferItemMapper studentOfferItemMapper;
//    @Resource
//    private AgentMapper agentMapper;
//    @Resource
//    private InstitutionMapper institutionMapper;
//    @Resource
//    private InstitutionCourseMapper institutionCourseMapper;
//    @Resource
//    private ReceivablePlanMapper receivablePlanMapper;
//    @Resource
//    private PayablePlanMapper payablePlanMapper;
//    @Resource
//    private PaymentFormMapper paymentFormMapper;
//    @Resource
//    private PaymentFormItemMapper paymentFormItemMapper;
//    @Resource
//    private ReceiptFormMapper receiptFormMapper;
//    @Resource
//    private ReceiptFormItemMapper receiptFormItemMapper;
////    @Resource
//    private LogFinanceToBmsMapper logFinanceToBmsMapper;

    /**
     * 去掉括号里面的内容
     *
     * @param context
     * @return
     */
//    private static String clearBracket(String context) {
//        if (context.equals("橙谷英达教育咨询有限公司(原吴宇晨（江西南昌橙谷教育))")) {
//            context = "橙谷英达教育咨询有限公司";
//        } else if (context.equals("河南智之轩教育咨询有限公司(原个人代理-毛亚刚（原河南飞洋出国留学)")) {
//            context = "河南智之轩教育咨询有限公司";
//        }
//        // 修改原来的逻辑，防止右括号出现在左括号前面的位置
//        // 标记第一个使用左括号的位置
//        int head = context.indexOf('（');
//        if (head == -1) {
//            // 如果context中不存在括号，什么也不做，直接跑到函数底端返回初值str
//            return context;
//        } else {
//            // 从head+1起检查每个字符
//            int next = head + 1;
//            // 记录括号情况
//            int count = 1;
//            do {
//                if (context.charAt(next) == '（') {
//                    count++;
//                } else if (context.charAt(next) == '）') {
//                    count--;
//                }
//                // 更新即将读取的下一个字符的位置
//                next++;
//                // 已经找到匹配的括号
//                if (count == 0) {
//                    String temp = context.substring(head, next);
//                    // 用空内容替换，复制给context
//                    context = context.replace(temp, "");
//                    // 找寻下一个左括号
//                    head = context.indexOf('（');
//                    // 标记下一个左括号后的字符位置
//                    next = head + 1;
//                    // count的值还原成1
//                    count = 1;
//                }
//                // 如果在该段落中找不到左括号了，就终止循环
//            } while (head != -1);
//        }
//        // 返回更新后的context
//        return context;
//    }

//    @Async("defaultThreadPool")
//    public void updateFinancelExcelInfo(Map<String, Long> currencyMap, FinanceExcelInfo financeExcelInfo, String optKye) {
//        if (GeneralTool.isEmpty(financeExcelInfo.getNum())) {
//            inserLog(optKye, FinanceExcelLogTypeEnum.STUDENT_OUR_ID_INFO.key, financeExcelInfo.getNum(), financeExcelInfo.getOurId(), financeExcelInfo.getCountry(), financeExcelInfo.getAgentSourceName(),
//                    financeExcelInfo.getSchoolName(), financeExcelInfo.getCourseName(), "学生our_id不存在", null, null,
//                    null, null, null, null);
////            logger.error("财务excel信息导入 学生信息不存在<|>num:{}<|>OurId:{}", financeExcelInfo.getNum(), financeExcelInfo.getOurId());
//            return;
//        }
//        //国家id
//        Long countryId = FinanceExcelCountryEnum.getValue(financeExcelInfo.getCountry().trim());
//        if (GeneralTool.isEmpty(countryId)) {
//            inserLog(optKye, FinanceExcelLogTypeEnum.COUNTRY_INFO.key, financeExcelInfo.getNum(), financeExcelInfo.getOurId(), financeExcelInfo.getCountry(), financeExcelInfo.getAgentSourceName(),
//                    financeExcelInfo.getSchoolName(), financeExcelInfo.getCourseName(), "国家信息不存在", null, null,
//                    null, null, null, null);
////            logger.error("财务excel信息导入 国家信息不存在<|>num:{}<|>country:{}", financeExcelInfo.getNum(), financeExcelInfo.getCountry());
//            return;
//        }
//        //财务代理名
//        String agentSourceName = financeExcelInfo.getAgentSourceName();
////        Example example = new Example(Agent.class);
////        example.createCriteria().andEqualTo("name", agentSourceName);
////        List<Agent> agents = agentMapper.selectByExample(example);
//        LambdaQueryWrapper<Agent> wrapperAgent = new LambdaQueryWrapper<>();
//        wrapperAgent.eq(Agent::getName, agentSourceName);
//        List<Agent> agents = agentMapper.selectList(wrapperAgent);
//        if (GeneralTool.isEmpty(agents)) {
////            example.clear();
////            example.createCriteria().andLike("name", "%" + agentSourceName.replaceAll("\\(.*\\)","").replaceAll("（.*）","") + "%");
////            agents = agentMapper.selectByExample(example);
//            wrapperAgent.clear();
//            wrapperAgent.like(Agent::getName, "%" + agentSourceName.replaceAll("\\(.*\\)", "").replaceAll("（.*）", "") + "%");
//            agents = agentMapper.selectList(wrapperAgent);
//            if (GeneralTool.isEmpty(agents)) {
//                inserLog(optKye, FinanceExcelLogTypeEnum.AGENT_INFO.key, financeExcelInfo.getNum(), financeExcelInfo.getOurId(), financeExcelInfo.getCountry(), financeExcelInfo.getAgentSourceName(),
//                        financeExcelInfo.getSchoolName(), financeExcelInfo.getCourseName(), "代理信息不存在", null, null,
//                        null, null, null, null);
////                logger.error("财务excel信息导入 代理不存在<|>num:{}<|>agentSourceName:{}", financeExcelInfo.getNum(), agentSourceName);
//                return;
//            }
//        }
//        if (1 < agents.size()) {
//            inserLog(optKye, FinanceExcelLogTypeEnum.TWO_AGENT_INFO.key, financeExcelInfo.getNum(), financeExcelInfo.getOurId(), financeExcelInfo.getCountry(), financeExcelInfo.getAgentSourceName(),
//                    financeExcelInfo.getSchoolName(), financeExcelInfo.getCourseName(), JSONArray.toJSONString(agents), null, null,
//                    null, null, null, null);
////            logger.error("财务excel信息导入 查询到两个代理，有误差<|>num:{}<|>agentSourceName:{}", financeExcelInfo.getNum(), agentSourceName);
//            return;
//        }
//        Agent agent = agents.get(0);
//
////        example = new Example(StudentOfferItem.class);
////        example.createCriteria().andEqualTo("idGeaFinance", financeExcelInfo.getNum());
////        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectByExample(example);
//        LambdaQueryWrapper<StudentOfferItem> wrapperStudentOfferItem = new LambdaQueryWrapper<>();
//        wrapperStudentOfferItem.eq(StudentOfferItem::getIdGeaFinance, financeExcelInfo.getNum());
//        List<StudentOfferItem> studentOfferItems = studentOfferItemMapper.selectList(wrapperStudentOfferItem);
//        StudentOfferItem studentOfferItem = null;
//        //true:未同步过财务信息 或 财务信息有改动导致学习计划指向变动   false:同步过该财务信息，不需要重新查找匹配学习计划
//        boolean flag = true;
//        if (GeneralTool.isNotEmpty(studentOfferItems)) {
//            studentOfferItem = studentOfferItems.get(0);
//            //排查该计划的国家 学生 代理 如果有一项有变动或者匹配不上的 表示财务修改了数据,数据不指向这条学习计划了  置空该计划
////            StudentOffer studentOffer = studentOfferMapper.selectByPrimaryKey(studentOfferItem.getFkStudentOfferId());
////            Student student = studentMapper.selectByPrimaryKey(studentOffer.getFkStudentId());
////            Long fkAgentId = studentOffer.getFkAgentId();
////            Institution institution = institutionMapper.selectByPrimaryKey(studentOfferItem.getFkInstitutionId());
//            StudentOffer studentOffer = studentOfferMapper.selectById(studentOfferItem.getFkStudentOfferId());
//            Student student = studentMapper.selectById(studentOffer.getFkStudentId());
//            Long fkAgentId = studentOffer.getFkAgentId();
//            Institution institution = institutionMapper.selectById(studentOfferItem.getFkInstitutionId());
//            //学校名算法
//            List<String> schoolNameMatching = schoolMatching(financeExcelInfo.getSchoolName(), Collections.singleton(institution.getName()));
//
//
//            //代理/国家/学生/学校/课程名 有变动 回滚该计划的财务信息
//            if (!(fkAgentId.equals(agent.getId()) || countryId.equals(studentOffer.getFkAreaCountryId()) ||
//                    countryId.equals(studentOfferItem.getFkAreaCountryId()) || student.getNumGea().equals(financeExcelInfo.getOurId()) ||
//                    GeneralTool.isNotEmpty(schoolNameMatching) || studentOfferItem.getOldCourseCustomName().equals(financeExcelInfo.getCourseName()) ||
//                    student.getNumGea().equals(financeExcelInfo.getOurId()))) {
//                //回滚学习计划
//                studentOfferItem.setIdGeaFinance(null);
//                studentOfferItem.setGmtModified(new Date());
//                studentOfferItem.setGmtModifiedUser("admin");
////                studentOfferItemMapper.updateByPrimaryKey(studentOfferItem);
//                studentOfferItemMapper.updateById(studentOfferItem);
//                //删除应收应付
//                LambdaQueryWrapper<ReceivablePlan> wrapperReceivablePlan = new LambdaQueryWrapper<>();
//                wrapperReceivablePlan.eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_OFFER_ITEM.key).eq(ReceivablePlan::getFkTypeTargetId,
//                        studentOfferItem.getId());
//                receivablePlanMapper.delete(wrapperReceivablePlan);
//                LambdaQueryWrapper<PayablePlan> wrapperPayablePlan = new LambdaQueryWrapper<>();
//                wrapperPayablePlan.eq(PayablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_OFFER_ITEM.key).eq(PayablePlan::getFkTypeTargetId,
//                        studentOfferItem.getId());
//                payablePlanMapper.delete(wrapperPayablePlan);
////                example = new Example(ReceivablePlan.class);
////                example.createCriteria().andEqualTo("fkTypeKey", TableEnum.SALE_STUDENT_OFFER_ITEM.key).andEqualTo("fkTypeTargetId", studentOfferItem.getId());
////                receivablePlanMapper.deleteByExample(example);
////                example = new Example(PayablePlan.class);
////                example.createCriteria().andEqualTo("fkTypeKey", TableEnum.SALE_STUDENT_OFFER_ITEM.key).andEqualTo("fkTypeTargetId", studentOfferItem.getId());
////                payablePlanMapper.deleteByExample(example);
//                //删除收付款单
////                example = new Example(ReceiptForm.class);
////                example.createCriteria().andEqualTo("idGeaFinance", financeExcelInfo.getNum());
////                List<ReceiptForm> receiptForms = receiptFormMapper.selectByExample(example);
////                for (ReceiptForm receiptForm : receiptForms) {
////                    Example example1 = new Example(ReceiptFormItem.class);
////                    example1.createCriteria().andEqualTo("fkReceiptFormId", receiptForm.getId());
////                    receiptFormItemMapper.deleteByExample(example1);
////                }
////                receiptFormMapper.deleteByExample(example);
//
////                example = new Example(PaymentForm.class);
////                example.createCriteria().andEqualTo("idGeaFinance", financeExcelInfo.getNum());
////                List<PaymentForm> paymentForms = paymentFormMapper.selectByExample(example);
////                for (PaymentForm paymentForm : paymentForms) {
////                    Example example1 = new Example(PaymentFormItem.class);
////                    example1.createCriteria().andEqualTo("fkPaymentFormId", paymentForm.getId());
////                    paymentFormItemMapper.deleteByExample(example1);
////                }
////                paymentFormMapper.deleteByExample(example);
//
//                //删除收付款单
//                LambdaQueryWrapper<ReceiptForm> wrapperReceiptForm = new LambdaQueryWrapper<>();
//                wrapperReceiptForm.eq(ReceiptForm::getIdGeaFinance, financeExcelInfo.getNum());
//                List<ReceiptForm> receiptForms = receiptFormMapper.selectList(wrapperReceiptForm);
//                for (ReceiptForm receiptForm : receiptForms) {
//                    LambdaQueryWrapper<ReceiptFormItem> wrapperReceiptFormItem = new LambdaQueryWrapper<>();
//                    wrapperReceiptFormItem.eq(ReceiptFormItem::getFkReceiptFormId, receiptForm.getId());
//                    receiptFormItemMapper.delete(wrapperReceiptFormItem);
//                }
//                receiptFormMapper.delete(wrapperReceiptForm);
//
//                LambdaQueryWrapper<PaymentForm> wrapperPaymentForm = new LambdaQueryWrapper<>();
//                wrapperPaymentForm.eq(PaymentForm::getIdGeaFinance, financeExcelInfo.getNum());
//                List<PaymentForm> paymentForms = paymentFormMapper.selectList(wrapperPaymentForm);
//                for (PaymentForm paymentForm : paymentForms) {
//                    LambdaQueryWrapper<PaymentFormItem> wrapperPaymentFormItem = new LambdaQueryWrapper<>();
//                    wrapperPaymentFormItem.eq(PaymentFormItem::getFkPaymentFormId, paymentForm.getId());
//                    paymentFormItemMapper.delete(wrapperPaymentFormItem);
//                }
//                paymentFormMapper.delete(wrapperPaymentForm);
//
//            } else {
//                flag = false;
//            }
//
//        }
//
//        if (flag) {
////            example = new Example(Student.class);
////            example.createCriteria().andEqualTo("numGea", financeExcelInfo.getOurId());
////            List<Student> students = studentMapper.selectByExample(example);
//            LambdaQueryWrapper<Student> wrapperStudent = new LambdaQueryWrapper<>();
//            wrapperStudent.eq(Student::getNumGea, financeExcelInfo.getOurId());
//            List<Student> students = studentMapper.selectList(wrapperStudent);
//            if (GeneralTool.isEmpty(students)) {
//                inserLog(optKye, FinanceExcelLogTypeEnum.STUDENT_INFO.key, financeExcelInfo.getNum(), financeExcelInfo.getOurId(), financeExcelInfo.getCountry(), financeExcelInfo.getAgentSourceName(),
//                        financeExcelInfo.getSchoolName(), financeExcelInfo.getCourseName(), null, agent.getId(), null,
//                        null, null, null, null);
////                logger.error("财务excel信息导入 学生信息不存在<|>num:{}<|>OurId:{}", financeExcelInfo.getNum(), financeExcelInfo.getOurId());
//                return;
//            }
//            Student student = students.get(0);
////            example = new Example(StudentOffer.class);
////            example.createCriteria().andEqualTo("fkStudentId", student.getId()).andEqualTo("fkAreaCountryId", countryId).andEqualTo("fkAgentId", agent.getId());
////            List<StudentOffer> studentOffers = studentOfferMapper.selectByExample(example);
//            LambdaQueryWrapper<StudentOffer> wrapperStudentOffer = new LambdaQueryWrapper<>();
//            wrapperStudentOffer.eq(StudentOffer::getFkStudentId, student.getId()).eq(StudentOffer::getFkAreaCountryId, countryId).eq(StudentOffer::getFkAgentId,
//                    agent.getId());
//            List<StudentOffer> studentOffers = studentOfferMapper.selectList(wrapperStudentOffer);
//            if (GeneralTool.isEmpty(studentOffers)) {
//                inserLog(optKye, FinanceExcelLogTypeEnum.STUDENT_OFFER.key, financeExcelInfo.getNum(), financeExcelInfo.getOurId(), financeExcelInfo.getCountry(), financeExcelInfo.getAgentSourceName(),
//                        financeExcelInfo.getSchoolName(), financeExcelInfo.getCourseName(), "学习方案不存在", agent.getId(),
//                        student.getId(), countryId, null, null, null);
////                logger.error("财务excel信息导入 学习方案不存在<|>studentId:{}<|>num:{}<|>countryId:{}<|>fkAgentId:{}", student.getId(), financeExcelInfo.getNum(), countryId, agent.getId());
//                return;
//            }
//            //符合条件的申请方案下的所有学习计划
//            List<Long> studentOfferIdList = studentOffers.stream().map(StudentOffer::getId).collect(Collectors.toList());
////            example = new Example(StudentOfferItem.class);
////            example.createCriteria().andIn("fkStudentOfferId", studentOfferIdList);
////            List<StudentOfferItem> studentOfferItemList = studentOfferItemMapper.selectByExample(example);
//            LambdaQueryWrapper<StudentOfferItem> LambdaQueryStudentOfferItem = new LambdaQueryWrapper<>();
//            LambdaQueryStudentOfferItem.in(StudentOfferItem::getFkStudentOfferId, studentOfferIdList);
//            List<StudentOfferItem> studentOfferItemList = studentOfferItemMapper.selectList(LambdaQueryStudentOfferItem);
//
//            if (GeneralTool.isEmpty(studentOfferItemList)) {
//                inserLog(optKye, FinanceExcelLogTypeEnum.STUDENT_OFFER_ITEM.key, financeExcelInfo.getNum(), financeExcelInfo.getOurId(), financeExcelInfo.getCountry(), financeExcelInfo.getAgentSourceName(),
//                        financeExcelInfo.getSchoolName(), financeExcelInfo.getCourseName(), JSONArray.toJSONString(studentOfferIdList), agent.getId(),
//                        student.getId(), countryId, null, null, null);
////                logger.error("财务excel信息导入 学习方案下无学习计划<|>studentId:{}<|>num:{}<|>studentOfferIdList:{}", student.getId(), financeExcelInfo.getNum(), JSONArray.toJSONString(studentOfferIdList));
//                return;
//            }
//            // 学习计划id-学校名
//            Map<Long, String> itemInstitutionNameMap = new HashMap<>();
//            // 学习计划id-课程名
//            Map<Long, String> itemCourseNameMap = new HashMap<>();
//            // 学校id-学校信息对象
//            Map<Long, Institution> institutionMap = new HashMap<>();
////            Map<Long, String> courseMap = new HashMap<>();
//            for (StudentOfferItem offerItem : studentOfferItemList) {
//                Long fkInstitutionId = offerItem.getFkInstitutionId();
////                Institution institution = institutionMapper.selectByPrimaryKey(fkInstitutionId);
//                Institution institution = institutionMapper.selectById(fkInstitutionId);
//                itemInstitutionNameMap.put(offerItem.getId(), institution.getName());
//                institutionMap.put(institution.getId(), institution);
////                Long fkInstitutionCourseId = offerItem.getFkInstitutionCourseId();
////                InstitutionCourse institutionCourse = institutionCourseMapper.selectByPrimaryKey(fkInstitutionCourseId);
//                itemCourseNameMap.put(offerItem.getId(), offerItem.getOldCourseCustomName());
////                courseMap.put(institutionCourse.getId(), institutionCourse.getName());
//            }
//            //学校名算法
//            List<String> schoolNameMatching = schoolMatching(financeExcelInfo.getSchoolName(), itemInstitutionNameMap.values());
//            if (GeneralTool.isEmpty(schoolNameMatching)) {
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.put("short_list", new ArrayList<>(Collections.singleton(financeExcelInfo.getSchoolName())));
//                jsonObject.put("long_list", itemInstitutionNameMap.values());
//                inserLog(optKye, FinanceExcelLogTypeEnum.SCHOOL_NAME.key, financeExcelInfo.getNum(), financeExcelInfo.getOurId(), financeExcelInfo.getCountry(), financeExcelInfo.getAgentSourceName(),
//                        financeExcelInfo.getSchoolName(), financeExcelInfo.getCourseName(), jsonObject.toJSONString(), agent.getId(),
//                        student.getId(), countryId, null, null, null);
////                logger.error("财务excel信息导入 学校名无法匹配<|>studentId:{}<|>num:{}<|>SchoolName:{}<|>itemInstitutionNameMap:{}", student.getId(), financeExcelInfo.getNum(), financeExcelInfo.getSchoolName(), JSONArray.toJSONString(itemInstitutionNameMap.values()));
//                return;
//            }
//
//            //能匹配上学校名的 学习计划id组
//            List<Long> itemIds = new ArrayList<>();
//            for (Long itemId : itemInstitutionNameMap.keySet()) {
//                String schoolName = itemInstitutionNameMap.get(itemId);
//                for (String schoolMatchingName : schoolNameMatching) {
//                    if (schoolMatchingName.equals(schoolName)) {
//                        itemIds.add(itemId);
//                    }
//                }
//            }
//
//            //学校名字能匹配上的学习计划对应的课程名  用于课程算法匹配    学校能匹配的学习计划id - 课程名
//            Map<Long, String> matchItemCourseNameMap = new HashMap<>();
//            for (Long itemId : itemIds) {
//                String courseName = itemCourseNameMap.get(itemId);
//                matchItemCourseNameMap.put(itemId, courseName);
//            }
//
//            //最为匹配的课程名
//            List<String> courseNameList = courseMatching(financeExcelInfo.getCourseName(), matchItemCourseNameMap.values());
//            if (GeneralTool.isEmpty(courseNameList)) {
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.put("courseName", financeExcelInfo.getCourseName());
//                jsonObject.put("matchItemCourseNameMap", new HashSet(matchItemCourseNameMap.values()));
//                inserLog(optKye, FinanceExcelLogTypeEnum.COURSE_NAME.key, financeExcelInfo.getNum(), financeExcelInfo.getOurId(), financeExcelInfo.getCountry(), financeExcelInfo.getAgentSourceName(),
//                        financeExcelInfo.getSchoolName(), financeExcelInfo.getCourseName(), jsonObject.toJSONString(), agent.getId(),
//                        student.getId(), countryId, null, null, null);
////                logger.error("财务excel信息导入 课程名无法匹配<|>studentId:{}<|>num:{}<|>CourseName:{}<|>matchItemCourseNameMap:{}", student.getId(), financeExcelInfo.getNum(), financeExcelInfo.getCourseName(), JSONArray.toJSONString(matchItemCourseNameMap.values()));
//                return;
//            }
//            if (courseNameList.size() >= 2) {
//                JSONObject jsonObject = new JSONObject();
//                jsonObject.put("financeExcelCourseName", financeExcelInfo.getCourseName());
//                jsonObject.put("courseNameList", courseNameList);
//                inserLog(optKye, FinanceExcelLogTypeEnum.TWO_COURSE_NAME.key, financeExcelInfo.getNum(), financeExcelInfo.getOurId(), financeExcelInfo.getCountry(), financeExcelInfo.getAgentSourceName(),
//                        financeExcelInfo.getSchoolName(), financeExcelInfo.getCourseName(), jsonObject.toJSONString(), agent.getId(),
//                        student.getId(), countryId, null, null, null);
//                logger.error("财务excel信息导入 课程名匹配有误差,有两条课程名<|>num:{}<|>CourseName:{}<|>courseNameList:{}", financeExcelInfo.getNum(), financeExcelInfo.getCourseName(), JSONArray.toJSONString(courseNameList));
//                return;
//            }
//            String courseNameMatching = courseNameList.get(0);
//            //经过学校算法 课程算法最后得出对应学习计划的id
//            Long itemMatchId = null;
//            for (Long itemId : matchItemCourseNameMap.keySet()) {
//                String courseName = matchItemCourseNameMap.get(itemId);
//                if (courseName.equals(courseNameMatching)) {
//                    itemMatchId = itemId;
//                }
//            }
//            if (GeneralTool.isEmpty(itemMatchId)) {
//                logger.error("财务excel信息导入 找不到对应的学习计划<|>studentId:{}<|>num:{}", student.getId(), financeExcelInfo.getNum());
//                return;
//            }
//
//            //判断该学习计划以及对应的方案状态是否为激活状态 处于未激活状态则将其激活
////            example = new Example(StudentOfferItem.class);
////            example.createCriteria().andEqualTo("id", itemMatchId);
////            List<StudentOfferItem> studentOfferItemList1 = studentOfferItemMapper.selectByExample(example);
//            LambdaQueryWrapper<StudentOfferItem> queryWrapperStudentOfferItem = new LambdaQueryWrapper<>();
//            queryWrapperStudentOfferItem.eq(StudentOfferItem::getId, itemMatchId);
//            List<StudentOfferItem> studentOfferItemList1 = studentOfferItemMapper.selectList(queryWrapperStudentOfferItem);
//            studentOfferItem = studentOfferItemList1.get(0);
//            studentOfferItem.setGmtModified(new Date());
//            studentOfferItem.setGmtModifiedUser("admin");
//            if (studentOfferItem.getStatus() == 0) {
//                studentOfferItem.setStatus(1);
//                //studentOfferItemMapper.updateByPrimaryKey(studentOfferItem);
//                studentOfferItemMapper.updateById(studentOfferItem);
//            }
////            example = new Example(StudentOffer.class);
////            example.createCriteria().andEqualTo("id", studentOfferItem.getFkStudentOfferId());
////            List<StudentOffer> studentOffers1 = studentOfferMapper.selectByExample(example);
//            LambdaQueryWrapper<StudentOffer> LambdaQueryWrapperStudentOffer = new LambdaQueryWrapper<>();
//            LambdaQueryWrapperStudentOffer.eq(StudentOffer::getId, studentOfferItem.getFkStudentOfferId());
//            List<StudentOffer> studentOffers1 = studentOfferMapper.selectList(LambdaQueryWrapperStudentOffer);
//            StudentOffer studentOffer1 = studentOffers1.get(0);
//
//            if (studentOffer1.getStatus() == 0) {
//                studentOffer1.setStatus(1);
//                studentOffer1.setGmtModified(new Date());
//                studentOffer1.setGmtModifiedUser("admin");
//                //studentOfferMapper.updateByPrimaryKey(studentOffer1);
//                studentOfferMapper.updateById(studentOffer1);
//            }
//
//            //赋值财务唯一key
//            studentOfferItem.setIdGeaFinance(financeExcelInfo.getNum());
//            //studentOfferItemMapper.updateByPrimaryKey(studentOfferItem);
//            studentOfferItemMapper.updateById(studentOfferItem);
//        }
//
//        //删除并重新生成应收应付逻辑
//        //删除应收应付计划以及对应的收付款单
////        example = new Example(ReceivablePlan.class);
////        example.createCriteria().andEqualTo("fkTypeKey", TableEnum.SALE_STUDENT_OFFER_ITEM.key).andEqualTo("fkTypeTargetId", studentOfferItem.getId()).andEqualTo("idGeaFinance", financeExcelInfo.getNum());
////        List<ReceivablePlan> receivablePlans = receivablePlanMapper.selectByExample(example);
////        receivablePlanMapper.deleteByExample(example);
////        example = new Example(PayablePlan.class);
////        example.createCriteria().andEqualTo("fkTypeKey", TableEnum.SALE_STUDENT_OFFER_ITEM.key).andEqualTo("fkTypeTargetId", studentOfferItem.getId()).andEqualTo("idGeaFinance", financeExcelInfo.getNum());
////        List<PayablePlan> payablePlans = payablePlanMapper.selectByExample(example);
////        payablePlanMapper.deleteByExample(example);
////        example = new Example(ReceiptForm.class);
////        example.createCriteria().andEqualTo("fkTypeKey", TableEnum.INSTITUTION_PROVIDER.key).andEqualTo("idGeaFinance", financeExcelInfo.getNum());
////        receiptFormMapper.deleteByExample(example);
////        example = new Example(PaymentForm.class);
////        example.createCriteria().andEqualTo("fkTypeKey", TableEnum.SALE_AGENT.key).andEqualTo("fkTypeTargetId", studentOfferItem.getId()).andEqualTo("idGeaFinance", financeExcelInfo.getNum());
////        paymentFormMapper.deleteByExample(example);
////
////
////        if (CheckUtils.isNotEmpty(receivablePlans)) {
////            for (ReceivablePlan receivablePlan : receivablePlans) {
////                example = new Example(ReceiptFormItem.class);
////                example.createCriteria().andEqualTo("fkReceivablePlanId", receivablePlan.getId());
////                receiptFormItemMapper.deleteByExample(example);
////            }
////        }
////        if (CheckUtils.isNotEmpty(payablePlans)) {
////            for (PayablePlan payablePlan : payablePlans) {
////                example = new Example(PaymentFormItem.class);
////                example.createCriteria().andEqualTo("fkPayablePlanId", payablePlan.getId());
////                paymentFormItemMapper.deleteByExample(example);
////            }
////        }
//
//
//        //删除应收应付
////        example = new Example(ReceivablePlan.class);
////        example.createCriteria().andEqualTo("fkTypeKey", TableEnum.SALE_STUDENT_OFFER_ITEM.key).andEqualTo("fkTypeTargetId", studentOfferItem.getId());
////        receivablePlanMapper.deleteByExample(example);
////        example = new Example(PayablePlan.class);
////        example.createCriteria().andEqualTo("fkTypeKey", TableEnum.SALE_STUDENT_OFFER_ITEM.key).andEqualTo("fkTypeTargetId", studentOfferItem.getId());
////        payablePlanMapper.deleteByExample(example);
////        //删除收付款单
////        example = new Example(ReceiptForm.class);
////        example.createCriteria().andEqualTo("idGeaFinance", financeExcelInfo.getNum());
////        List<ReceiptForm> receiptForms = receiptFormMapper.selectByExample(example);
////        for (ReceiptForm receiptForm : receiptForms) {
////            Example example1 = new Example(ReceiptFormItem.class);
////            example1.createCriteria().andEqualTo("fkReceiptFormId", receiptForm.getId());
////            receiptFormItemMapper.deleteByExample(example1);
////        }
////        receiptFormMapper.deleteByExample(example);
////
////        example = new Example(PaymentForm.class);
////        example.createCriteria().andEqualTo("idGeaFinance", financeExcelInfo.getNum());
////        List<PaymentForm> paymentForms = paymentFormMapper.selectByExample(example);
////        for (PaymentForm paymentForm : paymentForms) {
////            Example example1 = new Example(PaymentFormItem.class);
////            example1.createCriteria().andEqualTo("fkPaymentFormId", paymentForm.getId());
////            paymentFormItemMapper.deleteByExample(example1);
////        }
////        paymentFormMapper.deleteByExample(example);
//
//        //删除应收应付
//        LambdaQueryWrapper<ReceivablePlan> lambdaQueryWrapperReceivablePlan = new LambdaQueryWrapper<>();
//        lambdaQueryWrapperReceivablePlan.eq(ReceivablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_OFFER_ITEM.key).eq(ReceivablePlan::getFkTypeTargetId,
//                studentOfferItem.getId());
//        receivablePlanMapper.delete(lambdaQueryWrapperReceivablePlan);
//        LambdaQueryWrapper<PayablePlan> lambdaQueryWrapperPayablePlan = new LambdaQueryWrapper<>();
//        lambdaQueryWrapperPayablePlan.eq(PayablePlan::getFkTypeKey, TableEnum.SALE_STUDENT_OFFER_ITEM.key).eq(PayablePlan::getFkTypeTargetId,
//                studentOfferItem.getId());
//        payablePlanMapper.delete(lambdaQueryWrapperPayablePlan);
//        //删除收付款单
//        LambdaQueryWrapper<ReceiptForm> lambdaQueryWrapperReceiptForm = new LambdaQueryWrapper<>();
//        lambdaQueryWrapperReceiptForm.eq(ReceiptForm::getIdGeaFinance, financeExcelInfo.getNum());
//        List<ReceiptForm> receiptForms = receiptFormMapper.selectList(lambdaQueryWrapperReceiptForm);
//        for (ReceiptForm receiptForm : receiptForms) {
//            LambdaQueryWrapper<ReceiptFormItem> wrapperReceiptFormItem = new LambdaQueryWrapper<>();
//            wrapperReceiptFormItem.eq(ReceiptFormItem::getFkReceiptFormId, receiptForm.getId());
//            receiptFormItemMapper.delete(wrapperReceiptFormItem);
//        }
//        receiptFormMapper.delete(lambdaQueryWrapperReceiptForm);
//
//        LambdaQueryWrapper<PaymentForm> wrapperPaymentForm = new LambdaQueryWrapper<>();
//        wrapperPaymentForm.eq(PaymentForm::getIdGeaFinance, financeExcelInfo.getNum());
//        List<PaymentForm> paymentForms = paymentFormMapper.selectList(wrapperPaymentForm);
//        for (PaymentForm paymentForm : paymentForms) {
//            LambdaQueryWrapper<PaymentFormItem> wrapperPaymentFormItem = new LambdaQueryWrapper<>();
//            wrapperPaymentFormItem.eq(PaymentFormItem::getFkPaymentFormId, paymentForm.getId());
//            paymentFormItemMapper.delete(wrapperPaymentFormItem);
//        }
//        paymentFormMapper.delete(wrapperPaymentForm);
//
//
//        //应收币种id
//        Long currencyId = currencyMap.get(financeExcelInfo.getSourceCyy());
//        if (GeneralTool.isEmpty(currencyId)) {
//            logger.error("财务excel信息导入 应收币种不匹配<|>SourceCyy:{}<|>num:{}", financeExcelInfo.getSourceCyy(), financeExcelInfo.getNum());
//            return;
//        }
//
//        //应收应付计划创建时间计算
//        String intakeMth = financeExcelInfo.getIntakeMth();
//        int month = 0;
//        if ("JAN".equals(intakeMth)) {
//            month = 1;
//        } else if ("FEB".equals(intakeMth)) {
//            month = 2;
//        } else if ("MAR".equals(intakeMth)) {
//            month = 3;
//        } else if ("APR".equals(intakeMth)) {
//            month = 4;
//        } else if ("MAY".equals(intakeMth)) {
//            month = 5;
//        } else if ("JUN".equals(intakeMth)) {
//            month = 6;
//        } else if ("JUL".equals(intakeMth)) {
//            month = 7;
//        } else if ("AUG".equals(intakeMth)) {
//            month = 8;
//        } else if ("SEP".equals(intakeMth)) {
//            month = 9;
//        } else if ("OCT".equals(intakeMth)) {
//            month = 10;
//        } else if ("NOV".equals(intakeMth)) {
//            month = 11;
//        } else if ("DEC".equals(intakeMth)) {
//            month = 12;
//        }
//
//        Date planCreateTime = GetDateUtil.makeDate(financeExcelInfo.getIntakeYear(), month, 1);
//
//        //重新生成应收应付
//        ReceivablePlan receivablePlan = new ReceivablePlan();
//        receivablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
//        receivablePlan.setFkTypeTargetId(studentOfferItem.getId());
//        receivablePlan.setFkCurrencyTypeNum(financeExcelInfo.getSourceCyy());
//        receivablePlan.setTuitionAmount(financeExcelInfo.getSchoolFee());
//
//        if (GeneralTool.isNotEmpty(financeExcelInfo.getSchoolRate())) {
//            if ("FIX".equals(financeExcelInfo.getSchoolRate())) {
//                receivablePlan.setFixedAmount(financeExcelInfo.getAmount());
//            } else {
//                receivablePlan.setCommissionRate(new BigDecimal(financeExcelInfo.getSchoolRate())
//                        .multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
//            }
//        }
//        if (GeneralTool.isNotEmpty(financeExcelInfo.getNet())) {
//            receivablePlan.setNetRate(new BigDecimal(financeExcelInfo.getNet()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
//        }
//        receivablePlan.setCommissionAmount(financeExcelInfo.getAmount());
//        receivablePlan.setBonusAmount(financeExcelInfo.getAdditionalAmount());
//        receivablePlan.setReceivableAmount(financeExcelInfo.getTotalAmtIn());
//        //如果应收金额=实收金额总额 应该就是已完成了
//        if (financeExcelInfo.getTotalAmtIn().compareTo(financeExcelInfo.getTotalReceived()) == 0) {
//            receivablePlan.setStatus(2);
//        } else {
//            receivablePlan.setStatus(1);
//        }
//        receivablePlan.setIdGeaFinance(financeExcelInfo.getNum());
//        receivablePlan.setGmtCreate(planCreateTime);
//        receivablePlan.setGmtCreateUser("admin");
//        receivablePlanMapper.insert(receivablePlan);
//
//
//        //重新生成收款单
//        //第一部分实收金额
//        BigDecimal stTerm1 = financeExcelInfo.getStTerm1();
//        //第二部分实收金额
//        BigDecimal stTerm2 = financeExcelInfo.getStTerm2();
//        //第三部分实收金额
//        BigDecimal stTerm3 = financeExcelInfo.getStTerm3();
//        //第四部分实收金额
//        BigDecimal stTerm4 = financeExcelInfo.getStTerm4();
//
//        //手续费
//        BigDecimal bankCharges = financeExcelInfo.getBankCharges();
//        int i = 0;
//        if (GeneralTool.isNotEmpty(stTerm1)) {
//            i++;
//        }
//        if (GeneralTool.isNotEmpty(stTerm2)) {
//            i++;
//        }
//        if (GeneralTool.isNotEmpty(stTerm3)) {
//            i++;
//        }
//        if (GeneralTool.isNotEmpty(stTerm4)) {
//            i++;
//        }
//        //手续费要均分
//        BigDecimal bankChargesAverage = GeneralTool.isEmpty(bankCharges) ? null : bankCharges.divide(BigDecimal.valueOf(i), 2, RoundingMode.HALF_UP);
//        BigDecimal lastBankChargesAverage = GeneralTool.isEmpty(bankCharges) && GeneralTool.isEmpty(bankChargesAverage) ? null : bankCharges.subtract(bankChargesAverage.multiply(BigDecimal.valueOf(i - 1)));
//
//        //创建收款单 - 收款单子项
//        if (GeneralTool.isNotEmpty(stTerm1)) {
//            insertReceiptFrom(receivablePlan, stTerm1, bankChargesAverage, financeExcelInfo.getStDate1(), financeExcelInfo.getNum(), financeExcelInfo.getRevCyy());
//        }
//        if (GeneralTool.isNotEmpty(stTerm2)) {
//            BigDecimal charges;
//            if (GeneralTool.isNotEmpty(stTerm3)) {
//                charges = bankChargesAverage;
//            } else {
//                charges = lastBankChargesAverage;
//            }
//            insertReceiptFrom(receivablePlan, stTerm2, charges, financeExcelInfo.getStDate2(), financeExcelInfo.getNum(), financeExcelInfo.getRevCyy());
//        }
//        if (GeneralTool.isNotEmpty(stTerm3)) {
//            BigDecimal charges;
//            if (GeneralTool.isNotEmpty(stTerm4)) {
//                charges = bankChargesAverage;
//            } else {
//                charges = lastBankChargesAverage;
//            }
//            insertReceiptFrom(receivablePlan, stTerm3, charges, financeExcelInfo.getStDate3(), financeExcelInfo.getNum(), financeExcelInfo.getRevCyy());
//        }
//        if (GeneralTool.isNotEmpty(stTerm4)) {
//            insertReceiptFrom(receivablePlan, stTerm4, lastBankChargesAverage, financeExcelInfo.getStDate4(), financeExcelInfo.getNum(), financeExcelInfo.getRevCyy());
//        }
//
//
//        //生成应付计划
//        PayablePlan payablePlan = new PayablePlan();
//        payablePlan.setFkTypeKey(TableEnum.SALE_STUDENT_OFFER_ITEM.key);
//        payablePlan.setFkTypeTargetId(studentOfferItem.getId());
//        payablePlan.setFkCurrencyTypeNum(financeExcelInfo.getSourceCyy());
//        payablePlan.setTuitionAmount(financeExcelInfo.getSchoolFee());
//
//        if (GeneralTool.isNotEmpty(financeExcelInfo.getAgentRate())) {
//            if ("FIX".equals(financeExcelInfo.getAgentRate())) {
//                payablePlan.setFixedAmount(financeExcelInfo.getAgentComm());
//            } else {
//                payablePlan.setCommissionRate(new BigDecimal(financeExcelInfo.getAgentRate()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
//            }
//        }
//        payablePlan.setSplitRate(new BigDecimal(financeExcelInfo.getSplitRate()).multiply(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP));
//        payablePlan.setCommissionAmount(financeExcelInfo.getAgentComm());
//        payablePlan.setBonusAmount(financeExcelInfo.getAdditionalAgentComm());
//        payablePlan.setPayableAmount(financeExcelInfo.getTotalAgentComm());
//        //如果应付金额=实付金额总额 应该就是已完成了
//        if (financeExcelInfo.getTotalAgentComm().compareTo(financeExcelInfo.getTotalPaid()) == 0) {
//            payablePlan.setStatus(2);
//        } else {
//            payablePlan.setStatus(1);
//        }
//        payablePlan.setIdGeaFinance(financeExcelInfo.getNum());
//        payablePlan.setGmtCreate(planCreateTime);
//        payablePlan.setGmtCreateUser("admin");
//        payablePlan.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
//        payablePlan.setIsPayInAdvance(false);
//        payablePlanMapper.insert(payablePlan);
//
//
//        //重新生成付款单
//        //第一部分实付金额
//        BigDecimal stPayment1 = financeExcelInfo.getStPayment1();
//        //第二部分实付金额
//        BigDecimal stPayment2 = financeExcelInfo.getStPayment2();
//        //第三部分实付金额
//        BigDecimal stPayment3 = financeExcelInfo.getStPayment3();
//        //第四部分实付金额
//        BigDecimal stPayment4 = financeExcelInfo.getStPayment4();
//
//        //手续费
//        BigDecimal agentBankCharges = financeExcelInfo.getAgentBankCharges();
//        i = 0;
//        if (GeneralTool.isNotEmpty(stPayment1)) {
//            i++;
//        }
//        if (GeneralTool.isNotEmpty(stPayment2)) {
//            i++;
//        }
//        if (GeneralTool.isNotEmpty(stPayment3)) {
//            i++;
//        }
//        if (GeneralTool.isNotEmpty(stPayment4)) {
//            i++;
//        }
//
//
//        //手续费要均分
//        BigDecimal agentBankChargesAverage = GeneralTool.isEmpty(agentBankCharges) ? null : agentBankCharges.divide(BigDecimal.valueOf(i), 2, RoundingMode.HALF_UP);
//        BigDecimal agentLastBankChargesAverage = GeneralTool.isEmpty(agentBankCharges) ? null : agentBankCharges.subtract(agentBankChargesAverage.multiply(BigDecimal.valueOf(i - 1)));
//
//        //创建收款单 - 收款单子项
//        if (GeneralTool.isNotEmpty(stPayment1)) {
//            insertPaymentFrom(payablePlan, stPayment1, agentBankChargesAverage, financeExcelInfo.getStPaymentDate1(), financeExcelInfo.getNum(), financeExcelInfo.getRevCyy(), agent);
//        }
//        if (GeneralTool.isNotEmpty(stPayment2)) {
//            BigDecimal charges;
//            if (GeneralTool.isNotEmpty(stPayment3)) {
//                charges = agentBankChargesAverage;
//            } else {
//                charges = agentLastBankChargesAverage;
//            }
//            insertPaymentFrom(payablePlan, stPayment2, charges, financeExcelInfo.getStPaymentDate2(), financeExcelInfo.getNum(), financeExcelInfo.getRevCyy(), agent);
//        }
//        if (GeneralTool.isNotEmpty(stPayment3)) {
//            BigDecimal charges;
//            if (GeneralTool.isNotEmpty(stPayment4)) {
//                charges = agentBankChargesAverage;
//            } else {
//                charges = agentLastBankChargesAverage;
//            }
//            insertPaymentFrom(payablePlan, stPayment3, charges, financeExcelInfo.getStPaymentDate3(), financeExcelInfo.getNum(), financeExcelInfo.getRevCyy(), agent);
//        }
//        if (GeneralTool.isNotEmpty(stPayment4)) {
//            insertPaymentFrom(payablePlan, stPayment4, agentLastBankChargesAverage, financeExcelInfo.getStPaymentDate4(), financeExcelInfo.getNum(), financeExcelInfo.getRevCyy(), agent);
//        }
//
//        inserLog(optKye, FinanceExcelLogTypeEnum.SUCCESS.key, financeExcelInfo.getNum(), financeExcelInfo.getOurId(), financeExcelInfo.getCountry(), financeExcelInfo.getAgentSourceName(),
//                financeExcelInfo.getSchoolName(), financeExcelInfo.getCourseName(), "成功", agent.getId(), null,
//                countryId, null, null, null);
//
//    }

    /**
     * 创建收款单-收款单子项
     *
     * @Date 10:15 2021/12/14
     * <AUTHOR>
     */
//    private void insertReceiptFrom(ReceivablePlan receivablePlan, BigDecimal stTerm, BigDecimal bankChargesAverage,
//                                   Date stDate, String num, String currency) {
//        ReceiptForm receiptForm = new ReceiptForm();
//        receiptForm.setFkCompanyId(2L);
//        receiptForm.setFkTypeKey(TableEnum.INSTITUTION_PROVIDER.key);
//        // 提供商是找不到的 银行账号也是木有的 收款费用类型也没定义
//        receiptForm.setFkTypeTargetId(null);
//        receiptForm.setFkBankAccountId(null);
//        receiptForm.setFkReceiptFeeTypeId(1L);
//        receiptForm.setFkCurrencyTypeNum(currency);
//        receiptForm.setAmount(stTerm);
//        receiptForm.setServiceFee(bankChargesAverage);
//        receiptForm.setStatus(1);
//        receiptForm.setIdGeaFinance(num);
//        receiptForm.setGmtCreate(stDate);
//        receiptForm.setGmtCreateUser("admin");
//        receiptFormMapper.insert(receiptForm);
//
//
//        if (GeneralTool.isNotEmpty(receiptForm)) {
//            //receiptForm.setNumSystem(MyStringUtils.getReceiptFormNum(receiptForm.getId()));
//        }
//        receiptForm.setNumSystem(GetStringUtils.getReceiptFormNum(receiptForm.getId()));
////        receiptFormMapper.updateByPrimaryKey(receiptForm);
//        receiptFormMapper.updateById(receiptForm);
//
//        ReceiptFormItem receiptFormItem = new ReceiptFormItem();
//        receiptFormItem.setFkReceiptFormId(receiptForm.getId());
//        receiptFormItem.setFkReceivablePlanId(receivablePlan.getId());
//        receiptFormItem.setAmountReceipt(stTerm);
//        receiptFormItem.setServiceFee(bankChargesAverage);
//        receiptFormItem.setGmtCreate(stDate);
//        receiptFormItem.setGmtCreateUser("admin");
//        receiptFormItemMapper.insert(receiptFormItem);
//    }

    /**
     * 创建付款单-付款单子项
     *
     * @Date 14:39 2021/12/14
     * <AUTHOR>
     */
//    private void insertPaymentFrom(PayablePlan payablePlan, BigDecimal stPayment, BigDecimal bankChargesAverage,
//                                   Date stDate, String num, String currency, Agent agent) {
//        PaymentForm paymentForm = new PaymentForm();
//        paymentForm.setFkCompanyId(2L);
//        paymentForm.setFkTypeKey(TableEnum.SALE_AGENT.key);
//        // 提供商是找不到的 银行账号也是木有的 收款费用类型也没定义
//        paymentForm.setFkTypeTargetId(agent.getId());
//        paymentForm.setFkBankAccountId(null);
//        paymentForm.setFkPaymentFeeTypeId(1L);
//        paymentForm.setFkCurrencyTypeNum(currency);
//        //付款总金额（含手续费）
//        BigDecimal amount = stPayment;
//        if (GeneralTool.isNotEmpty(bankChargesAverage)) {
//            amount = amount.add(bankChargesAverage);
//        }
//        paymentForm.setAmount(amount);
//        paymentForm.setServiceFee(bankChargesAverage);
//        paymentForm.setStatus(1);
//        paymentForm.setIdGeaFinance(num);
//        paymentForm.setGmtCreate(stDate);
//        paymentForm.setGmtCreateUser("admin");
//        paymentFormMapper.insert(paymentForm);
//
//        if (GeneralTool.isNotEmpty(paymentForm)) {
//            paymentForm.setNumSystem(GetStringUtils.getPayFormNum(paymentForm.getId()));
//        }
////        paymentFormMapper.updateByPrimaryKey(paymentForm);
//        paymentFormMapper.updateById(paymentForm);
//
//
//        PaymentFormItem paymentFormItem = new PaymentFormItem();
//        paymentFormItem.setFkPaymentFormId(paymentForm.getId());
//        paymentFormItem.setFkPayablePlanId(payablePlan.getId());
//        BigDecimal amountPayment = stPayment;
//        if (GeneralTool.isNotEmpty(bankChargesAverage)) {
//            amountPayment = amountPayment.add(bankChargesAverage);
//        }
//        paymentFormItem.setAmountPayment(amountPayment);
//        paymentFormItem.setServiceFee(bankChargesAverage);
//        paymentFormItem.setGmtCreate(stDate);
//        paymentFormItem.setGmtCreateUser("admin");
//        paymentFormItemMapper.insert(paymentFormItem);
//    }

    /**
     * 学校名字匹配
     *
     * @Date 17:55 2021/12/10
     * <AUTHOR>
     */
//    private List<String> schoolMatching(String financeSchoolName, Collection<String> longNameSet) {
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("short_list", new ArrayList<>(Collections.singleton(financeSchoolName)));
//        jsonObject.put("long_list", longNameSet);
//
//        String body = null;
//        try {
//            body = HttpUtil.createPost(exactAlgorithmAddress)
//                    .body(jsonObject.toJSONString()).header("accept", "application/json").header("Content-Type", "application/json").execute().body();
//            SchoolMatchDto schoolMatchDto = JSONObject.parseObject(body, SchoolMatchDto.class);
//            String status = schoolMatchDto.getStatus();
//            if (!"success".equals(status)) {
//                throw new Exception();
//            }
//            return schoolMatchDto.getShort_trans_dict().get(financeSchoolName);
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("func[{}] e[{}-{}] desc[财务excel信息导入 课程算法error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
//                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
//
//            logger.error("财务excel信息导入 课程算法匹配报错<|>body:{}<|>jsonObject:{}", body, jsonObject.toJSONString());
//            return null;
//        }
//
//    }

    /**
     * 课程名 算法匹配
     *
     * @return
     * @Date 17:40 2021/12/10
     * <AUTHOR>
     */
//    private List<String> courseMatching(String financeCourseName, Collection<String> longNameSet) {
//        //算法 CPP课程名与RPA课程名 算法比对
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("short_list", new ArrayList<>(Collections.singleton(financeCourseName)));
//        jsonObject.put("long_list", longNameSet);
//        String body = null;
//        try {
//            body = HttpUtil.createPost(algorithmAddress)
//                    .body(jsonObject.toJSONString()).header("accept", "application/json").header("Content-Type", "application/json").execute().body();
//            CourseNameMatchDto courseNameMatchDto = JSONObject.parseObject(body, CourseNameMatchDto.class);
//            List<CourseMatchDto> data = courseNameMatchDto.getData();
//
//            //取第一个
//            List<CourseMatchScoreDto> matchList = data.get(0).getMatch_list();
//            if (GeneralTool.isEmpty(matchList)) {
//                return null;
//            }
//            //匹配上的名
//            List<String> courseNameList = new ArrayList<>();
//            for (CourseMatchScoreDto courseMatchScoreDto : matchList) {
//                courseNameList.add(courseMatchScoreDto.getCourse_name());
//            }
//            return courseNameList;
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("func[{}] e[{}-{}] desc[财务excel信息导入 课程算法error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
//                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
//
//            logger.error("财务excel信息导入 课程算法匹配报错<|>body:{}<|>jsonObject:{}", body, jsonObject.toJSONString());
//            return null;
//        }
//
//
//    }

    /**
     * 插入日志
     *
     * @Date 17:11 2021/12/16
     * <AUTHOR>
     */
//    private void inserLog(String optKye, Integer logType, String financeNum, String financeStudentId, String financeCountryNum, String financeAgentName,
//                          String financeInstitutionName, String financeInstitutionCourseName, String remark, Long fkAgentId, Long fkStudentId,
//                          Long fkAreaCountryId, Long fkStudentOfferId, Long fkInstitutionId, Long fkInstitutionCourseId) {
//        LogFinanceToBms logFinanceToBms = new LogFinanceToBms();
//        logFinanceToBms.setOptKey(optKye);
//        logFinanceToBms.setFinanceNum(financeNum);
//        logFinanceToBms.setFinanceStudentId(financeStudentId);
//        logFinanceToBms.setFinanceCountryNum(financeCountryNum);
//        logFinanceToBms.setFinanceAgentName(financeAgentName);
//        logFinanceToBms.setFinanceInstitutionName(financeInstitutionName);
//        logFinanceToBms.setFinanceInstitutionCourseName(financeInstitutionCourseName);
//        logFinanceToBms.setFkAgentId(fkAgentId);
//        logFinanceToBms.setFkStudentId(fkStudentId);
//        logFinanceToBms.setFkAreaCountryId(fkAreaCountryId);
//        logFinanceToBms.setFkStudentOfferId(fkStudentOfferId);
//        logFinanceToBms.setFkInstitutionId(fkInstitutionId);
//        logFinanceToBms.setFkInstitutionCourseId(fkInstitutionCourseId);
//        logFinanceToBms.setLogType(logType);
//        logFinanceToBms.setRemark(remark);
//        //logFinanceToBmsMapper.insert(logFinanceToBms);
//    }


    /**
     * 财务excel国家枚举
     *
     * @Date 14:58 2021/12/10
     * <AUTHOR>
     */
    enum FinanceExcelCountryEnum {
        /**
         * 澳大利亚
         */
        AU("AU", 6L),
        /**
         * 加拿大
         */
        CA("CA", 5L),
        /**
         * 中国
         */
        CN("CN", 3L),
        /**
         * 中国-香港
         */
        HK("HK", 9L),
        /**
         * 爱尔兰
         */
        IR("IR", 23L),
        /**
         * 日本
         */
        JP("JP", 12L),
        /**
         * 马来西亚
         */
        MY("MY", 15L),
        /**
         * 荷兰
         */
        NETH("Neth", 20L),
        /**
         * 新西兰
         */
        NZ("NZ", 8L),
        /**
         * 新加坡
         */
        SG("SG", 11L),
        /**
         * 瑞士
         */
        SWISS("SWISS", 21L),

        /**
         * 英国
         */
        UK("UK", 7L),
        /**
         * 美国
         */
        US("US", 4L);

        public String key;
        public Long value;

        FinanceExcelCountryEnum(String key, Long value) {
            this.key = key;
            this.value = value;
        }

        public static Long getValue(String key) {
            FinanceExcelCountryEnum[] financeExcelCountryEnums = values();
            for (FinanceExcelCountryEnum financeExcelCountryEnum : financeExcelCountryEnums) {
                if (financeExcelCountryEnum.key.equals(key)) {
                    return financeExcelCountryEnum.value;
                }
            }
            return null;
        }


    }

    enum FinanceExcelLogTypeEnum {
        STUDENT_OUR_ID_INFO(1, "学生Our_id不存在"),
        COUNTRY_INFO(2, "国家信息不存在"),
        AGENT_INFO(3, "代理信息不存在"),
        TWO_AGENT_INFO(4, "查询到两个代理，有误差"),
        STUDENT_INFO(5, "学生信息不存在"),
        STUDENT_OFFER(6, "学习申请方案不存在"),
        STUDENT_OFFER_ITEM(7, "学习方案下无学习计划"),
        SCHOOL_NAME(8, "学校名无法匹配"),
        COURSE_NAME(9, "课程名无法匹配"),
        TWO_COURSE_NAME(10, "课程名匹配有误差,有两条课程名"),
        SUCCESS(11, "成功生成");


        public int key;
        public String value;

        FinanceExcelLogTypeEnum(int key, String value) {
            this.key = key;
            this.value = value;
        }

    }
}
