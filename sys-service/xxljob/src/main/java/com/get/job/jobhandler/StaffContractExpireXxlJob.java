package com.get.job.jobhandler;

import com.get.job.service.permission.IStaffContractExpireService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: Hardy
 * @create: 2022/11/7 10:12
 * @verison: 1.0
 * @description:
 */
@Component
public class StaffContractExpireXxlJob {


    @Resource
    private IStaffContractExpireService staffContractExpireService;

    /**
     * 员工劳动合同过期提醒任务调度
     *
     * @throws Exception
     */
    @XxlJob("staffContractExpireTaskJobHandler")
    public void staffContractExpireTaskJobHandler() throws Exception {
        XxlJobHelper.log("XXL-JOB, staffContractExpireTaskJobHandler start...");
        Boolean successful = staffContractExpireService.sendStaffContractExpireReminder();
        if (successful){
            XxlJobHelper.log("XXL-JOB, staffContractExpireTaskJobHandler success...");
        }else {
            XxlJobHelper.log("XXL-JOB, staffContractExpireTaskJobHandler fail...");
        }
    }

}
