package com.get.job.dao.finance;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.CurrencyType;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("financedb")
public interface CurrencyTypeMapper extends BaseMapper<CurrencyType> {
    int insert(CurrencyType record);

    int insertSelective(CurrencyType record);

    int updateByPrimaryKeySelective(CurrencyType record);

    int updateByPrimaryKey(CurrencyType record);
}