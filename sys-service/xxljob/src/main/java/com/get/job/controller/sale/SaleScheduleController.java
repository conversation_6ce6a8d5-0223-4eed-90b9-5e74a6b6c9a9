package com.get.job.controller.sale;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.job.service.permission.IAutoUpdateAnnualLeaveService;
import com.get.job.service.sale.SynchronizationIssueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author:GSHG
 * @date: 2022-04-22 2:55 PM
 * description:
 */
@RequestMapping("schedule/sale")
@RestController
@Api(tags = "oldISSUE同步器")
public class SaleScheduleController {
    @Resource
    private SynchronizationIssueService synchronizationIssueService;
    @Resource
    private IAutoUpdateAnnualLeaveService autoUpdateAnnualLeaveService;

//    @GetMapping("synchronizationOldIssue")
//    @ApiOperation(value = "同步oldIssue",notes = "")
//    @OperationLogger(module = LoggerModulesConsts.SALECENTER,type = LoggerOptTypeConst.EDIT,description = "同步issue状态")
//    public ResponseBo synchronizationOldIssue(){
//        synchronizationIssueService.synchronizationOldIssue();
//        return  ResponseBo.ok();
//    }
    @GetMapping("test")
    public void test() {
        autoUpdateAnnualLeaveService.autoUpdateAnnualLeave();
    }
}
