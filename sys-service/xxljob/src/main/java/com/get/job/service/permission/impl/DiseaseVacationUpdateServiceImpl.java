package com.get.job.service.permission.impl;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.log.model.LogLogin;
import com.get.core.redis.cache.GetRedis;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.job.dao.log.LogLoginMapper;
import com.get.job.dao.permission.StaffMapper;
import com.get.job.service.permission.IDiseaseVacationUpdateService;
import com.get.officecenter.feign.IOfficeCenterClient;
import com.get.officecenter.dto.LeaveLogDto;
import com.get.officecenter.dto.LeaveStockDto;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.xxl.job.core.context.XxlJobHelper;
import org.apache.commons.lang.time.DateUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DateTime.now;

/**
 * @author: Hardy
 * @create: 2022/1/26 11:50
 * @verison: 1.0
 * @description:
 */
@Service
public class DiseaseVacationUpdateServiceImpl implements IDiseaseVacationUpdateService {

    private static final Logger logger = LoggerFactory.getLogger(AutoUpdateAnnualLeaveServiceImpl.class);

    @Resource
    private IOfficeCenterClient officeService;
    @Resource
    private StaffMapper staffMapper;

    @Resource
    private LogLoginMapper logLoginMapper;

    @Resource
    private GetRedis getRedis;

    @Resource
    private IReminderCenterClient reminderCenterClient;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    /**
     * 更新病假
     */
    @Override
    public void autoUpdateDiseaseVacation() {
        logger.info("--------------------病假自动更新定时器开始-------------");
        Date date = new Date();
        Calendar nowCalendar = Calendar.getInstance();
        nowCalendar.setTime(date);
        int nowYear = nowCalendar.get(Calendar.YEAR);
        List<Staff> staffs = staffMapper.getAllActivateStaff();
        if (GeneralTool.isEmpty(staffs)) {
            return;
        }
        //去除gea病假年度刷新逻辑
        staffs = staffs.stream().filter(s->!s.getFkCompanyId().equals(2L)&&!s.getFkCompanyId().equals(3L)).collect(Collectors.toList());

        Date effectiveDeadline = null;
        if (Calendar.JANUARY == nowCalendar.get(Calendar.MONTH)&&nowCalendar.get(Calendar.DAY_OF_MONTH)==1){
            effectiveDeadline = GetDateUtil.makeDay(nowYear,3,31,23,59,59);
        }else if (Calendar.APRIL == nowCalendar.get(Calendar.MONTH)&&nowCalendar.get(Calendar.DAY_OF_MONTH)==1){
            effectiveDeadline = GetDateUtil.makeDay(nowYear,6,30,23,59,59);
        }else if (Calendar.JULY == nowCalendar.get(Calendar.MONTH)&&nowCalendar.get(Calendar.DAY_OF_MONTH)==1){
            effectiveDeadline = GetDateUtil.makeDay(nowYear,9,30,23,59,59);
        }else if (Calendar.OCTOBER == nowCalendar.get(Calendar.MONTH)&&nowCalendar.get(Calendar.DAY_OF_MONTH)==1){
            effectiveDeadline = GetDateUtil.makeDay(nowYear,12,31,23,59,59);
        }

        if (GeneralTool.isNotEmpty(effectiveDeadline)) {
            for (Staff staff : staffs) {
                try {
                    addStockAndLog(staff, new BigDecimal("24"), effectiveDeadline);
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("func[{}] e[{}-{}] desc[更新病假添加库存 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                            e.getMessage(), Arrays.deepToString(e.getStackTrace()));
                }
            }
        }

        logger.info("--------------------病假自动更新定时器结束-------------");
        XxlJobHelper.log("病假自动更新定时器结束");
    }

    /**
     * 更新登陆日志信息
     */
    @Override
    public void autoUpdateLoginLog() {
        String time = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH::mm:ss"));
        logger.info(time+ "--------------------开始更新登陆日志信息-------------");
        List<LogLogin> logList = logLoginMapper.getYesterdayLoginLogList();
        if (GeneralTool.isNotEmpty(logList)) {
            String key = "get:token".concat("::").concat("token:state:");
            List<LogLogin> updateList = new ArrayList<>(128);
            for (LogLogin login : logList) {
                String sessionId = login.getSessionId();
                Long fkStaffId = login.getFkStaffId();
                if (!getRedis.exists(key.concat(fkStaffId + ":" + sessionId))) {
                    Date loginTime = login.getLoginTime();
                    login.setLogoutTime(DateUtils.addDays(loginTime,1));
                    updateList.add(login);
                }
            }
            if (GeneralTool.isNotEmpty(updateList)) {
                logger.info("更新login信息{}条",updateList.size());
                for (LogLogin logLogin : updateList) {
                    logLoginMapper.updateById(logLogin);
                }
            }
        }
        logger.info("--------------------更新登陆日志信息定时器结束-------------");
        XxlJobHelper.log("更新登陆日志信息定时器结束");
    }

    /**
     * Author Cream
     * Description : //发送生日提示邮件
     * Date 2023/5/26 15:12
     * Params:
     * Return
     */
    @Override
    public void sendBirthdayEmail() {
        logger.info("--------------------开始生日祝福邮件发送任务-------------");
        List<Staff> staffList = staffMapper.getTodaysBirthdayEmployee();
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();

        if (GeneralTool.isNotEmpty(staffList)) {
            //List<Map<String,String>> list = new ArrayList<>(staffList.size());
           List<EmailSenderQueue>  emailSenderQueueList = new ArrayList<>();
            for (Staff staff : staffList) {
                Map<String,String> map = new HashMap<>(4);
                String versionValue2 = versionConfigMap.get(staff.getFkCompanyId());
                map.put("name", staff.getName());
                map.put("englishName", staff.getNameEn());
                map.put("email", staff.getEmail());
                map.put("title", staff.getNameEn() + "生日快乐！");
                map.put("versionValue", versionValue2);
                EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
                emailSenderQueue.setFkDbName(ProjectKeyEnum.PERMISSION_CENTER.key);
                emailSenderQueue.setFkTableId(staff.getId());
                emailSenderQueue.setFkTableName(TableEnum.PERMISSION_STAFF.key);
                emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.EMPLOYEE_BIRTHDAY.getEmailTemplateKey());
                emailSenderQueue.setOperationTime(now());
                emailSenderQueue.setEmailParameter(map.toString());
                emailSenderQueue.setEmailTo(staff.getId().toString());
                emailSenderQueueList.add(emailSenderQueue);
//                list.add(map);
            }
            //reminderCenterClient.batchSendEmail(list ,ProjectKeyEnum.EMPLOYEE_BIRTHDAY.key);
            Result<Boolean> booleanResult = reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
            if (!booleanResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage(booleanResult.getMessage()));
            }

        }
        logger.info("--------------------生日祝福邮件发送任务结束-------------");
        XxlJobHelper.log("生日祝福邮件发送任务结束");
    }


    private void addStockAndLog(Staff staff, BigDecimal days, Date date) throws Exception {
        LeaveStockDto leaveStockDto = new LeaveStockDto();
        leaveStockDto.setFkCompanyId(staff.getFkCompanyId());
        leaveStockDto.setFkStaffId(staff.getId());
        leaveStockDto.setLeaveTypeKey(ProjectKeyEnum.DISEASE_VACATION.key);
        leaveStockDto.setLeaveStock(days);
        leaveStockDto.setEffectiveDeadline(date);
        leaveStockDto.setGmtCreate(new Date());
        leaveStockDto.setGmtCreateUser("[system]");
        //新增一条记录
        officeService.addSystem(leaveStockDto);

        //添加日志
        LeaveLogDto leaveLogDto = new LeaveLogDto();
        leaveLogDto.setFkCompanyId(staff.getFkCompanyId());
        leaveLogDto.setFkStaffId(staff.getId());
        leaveLogDto.setLeaveTypeKey(ProjectKeyEnum.DISEASE_VACATION.key);
        leaveLogDto.setOptTypeKey(ProjectKeyEnum.SICK_LEAVE_QUARTERLY_REFRESH.key);
        leaveLogDto.setDuration(days);
        //新增不需要加FkLeaveStockId
        leaveLogDto.setGmtCreate(new Date());
        leaveLogDto.setGmtCreateUser("[system]");
        officeService.addSystemLeaveLog(leaveLogDto);
    }
}
