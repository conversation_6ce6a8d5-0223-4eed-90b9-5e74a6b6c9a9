package com.get.job.dao.finance;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.PaymentForm;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("financedb")
public interface PaymentFormMapper extends BaseMapper<PaymentForm> {
    int insert(PaymentForm record);

    int insertSelective(PaymentForm record);
}