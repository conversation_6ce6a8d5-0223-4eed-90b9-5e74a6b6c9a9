package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("log_finance_to_bms")
public class LogFinanceToBms implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "主键id")
    @Column(name = "id")
    private Long id;
    /**
     * 操作批次key
     */
    @ApiModelProperty(value = "操作批次key")
    @Column(name = "opt_key")
    private String optKey;
    /**
     * 财务申请项编号
     */
    @ApiModelProperty(value = "财务申请项编号")
    @Column(name = "finance_num")
    private String financeNum;
    /**
     * 财务申请项学生Id（Our ID）
     */
    @ApiModelProperty(value = "财务申请项学生Id（Our ID）")
    @Column(name = "finance_student_id")
    private String financeStudentId;
    /**
     * 财务申请项国家编号
     */
    @ApiModelProperty(value = "财务申请项国家编号")
    @Column(name = "finance_country_num")
    private String financeCountryNum;
    /**
     * 财务申请项代理名
     */
    @ApiModelProperty(value = "财务申请项代理名")
    @Column(name = "finance_agent_name")
    private String financeAgentName;
    /**
     * 财务申请项学校名
     */
    @ApiModelProperty(value = "财务申请项学校名")
    @Column(name = "finance_institution_name")
    private String financeInstitutionName;
    /**
     * 财务申请项课程名
     */
    @ApiModelProperty(value = "财务申请项课程名")
    @Column(name = "finance_institution_course_name")
    private String financeInstitutionCourseName;
    /**
     * bms代理Id
     */
    @ApiModelProperty(value = "bms代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * bms学生Id
     */
    @ApiModelProperty(value = "bms学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * bms国家Id
     */
    @ApiModelProperty(value = "bms国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * bms学习方案Id
     */
    @ApiModelProperty(value = "bms学习方案Id")
    @Column(name = "fk_student_offer_id")
    private Long fkStudentOfferId;
    /**
     * bms学习计划Id
     */
    @ApiModelProperty(value = "bms学习计划Id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;
    /**
     * bms学校Id
     */
    @ApiModelProperty(value = "bms学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * bms学校课程Id
     */
    @ApiModelProperty(value = "bms学校课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 日志类型
     */
    @ApiModelProperty(value = "日志类型")
    @Column(name = "log_type")
    private Integer logType;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
}