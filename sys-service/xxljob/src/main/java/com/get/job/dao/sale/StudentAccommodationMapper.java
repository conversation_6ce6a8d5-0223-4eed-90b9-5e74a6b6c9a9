package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.StudentAccommodation;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("saledb")
public interface StudentAccommodationMapper extends BaseMapper<StudentAccommodation> {
    @Override
    int insert(StudentAccommodation record);

    int insertSelective(StudentAccommodation record);

}