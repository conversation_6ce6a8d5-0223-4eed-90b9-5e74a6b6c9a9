package com.get.job.jobhandler;

import com.get.job.service.office.SendMultiUserTaskReminderService;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MultiUserTaskXxlJob {
    private static final Logger logger = LoggerFactory.getLogger(MultiUserTaskXxlJob.class);

    @Resource
    private SendMultiUserTaskReminderService taskReminderService;


    @XxlJob("multiUserTask")
    public void multiUserTaskJobHandler() throws Exception {
        logger.info("XXL-JOB, 多人任务截止时间 开始...");
        boolean successful= taskReminderService.sendMultiUserTaskReminder();
        if (successful){
            logger.info("XXL-JOB, 多人任务截止时间成功...");
        }else {
            logger.info("XXL-JOB, 多人任务截止时间 失误...");
        }
    }

}
