package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.job.dto.NegativeCommissionDto;
import com.get.salecenter.entity.PayablePlan;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("saledb")
public interface PayablePlanMapper extends BaseMapper<PayablePlan> {

    List<NegativeCommissionDto> getNegativeCommission();
}