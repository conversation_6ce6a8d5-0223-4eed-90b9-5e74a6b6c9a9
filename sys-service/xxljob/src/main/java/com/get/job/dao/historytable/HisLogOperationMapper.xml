<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.historytable.HisLogOperationMapper">
  <resultMap id="BaseResultMap" type="com.get.core.log.model.LogOperation">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId" />
    <result column="staff_login_id" jdbcType="VARCHAR" property="staffLoginId" />
    <result column="staff_name" jdbcType="VARCHAR" property="staffName" />
    <result column="opt_code" jdbcType="VARCHAR" property="optCode" />
    <result column="opt_module_name" jdbcType="VARCHAR" property="optModuleName" />
    <result column="opt_type" jdbcType="VARCHAR" property="optType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insertSelective" parameterType="com.get.core.log.model.LogOperation">
    insert into log_operation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkStaffId != null">
        fk_staff_id,
      </if>
      <if test="staffLoginId != null">
        staff_login_id,
      </if>
      <if test="staffName != null">
        staff_name,
      </if>
      <if test="optCode != null">
        opt_code,
      </if>
      <if test="optModuleName != null">
        opt_module_name,
      </if>
      <if test="optType != null">
        opt_type,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="fkCompanyId != null">
        fk_company_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkStaffId != null">
        #{fkStaffId,jdbcType=BIGINT},
      </if>
      <if test="staffLoginId != null">
        #{staffLoginId,jdbcType=VARCHAR},
      </if>
      <if test="staffName != null">
        #{staffName,jdbcType=VARCHAR},
      </if>
      <if test="optCode != null">
        #{optCode,jdbcType=VARCHAR},
      </if>
      <if test="optModuleName != null">
        #{optModuleName,jdbcType=VARCHAR},
      </if>
      <if test="optType != null">
        #{optType,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="fkCompanyId != null">
        #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="insertBatch">
    insert into ${hisTableName}(
    id,
    fk_staff_id,
    staff_login_id,
    staff_name,
    opt_code,
    opt_module_name,
    opt_type,
    opt_param,
    remark,
    fk_company_id,
    gmt_create,
    gmt_create_user,
    gmt_modified,
    gmt_modified_user
    )
    values
    <foreach collection="records" item="item" index="index" separator=",">
    (
      #{item.id},
      #{item.fkStaffId},
      #{item.staffLoginId},
      #{item.staffName},
      #{item.optCode},
      #{item.optModuleName},
      #{item.optType},
      #{item.optParam},
      #{item.remark},
      #{item.fkCompanyId},
      #{item.gmtCreate},
      #{item.gmtCreateUser},
      #{item.gmtModified},
      #{item.gmtModifiedUser}
      )
    </foreach>
  </insert>


  <update id="createHisLogOperationTable">
      CREATE TABLE IF NOT EXISTS ais_log_center.${hisTableName} LIKE ais_log_center.log_operation
   </update>

  <select id="getLogOperationHis" resultType="com.get.job.entity.LogOperationHisDto">
    SELECT DISTINCT
    (
    CONCAT('_his_',YEAR ( gmt_create ),'_','Q',QUARTER ( gmt_create ))) AS table_his_name
    FROM
    log_operation
    WHERE gmt_create &lt; (DATE_SUB(CURDATE(),INTERVAL 3 MONTH))
  </select>

</mapper>