package com.get.job.dao.permission;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.job.dto.StaffContractReminderDto;
import com.get.permissioncenter.entity.Staff;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

@Mapper
@DS("permissiondb")
public interface StaffMapper extends BaseMapper<Staff> {
    int insert(Staff record);

    int insertSelective(Staff record);

    int updateByPrimaryKeySelective(Staff record);

    int updateByPrimaryKey(Staff record);

    @Select("select * from m_staff where FIND_IN_SET(#{idGea},id_gea) AND is_active = 1")
    List<Staff> selectStaffByIdGea(@Param("idGea") Long idGea);

    /**
     * 获取当天入职的员工且满一年
     *
     * @param isSyd 是否为悉尼公司
     * @return
     */
    List<Staff> getStaffByEntryDate(@Param("date") Date date, @Param("isSyd") Boolean isSyd);

    /**
     * 获取
     *
     * @return
     */
    List<Staff> getAllStaff();

    /***
     * 所有生效员工
     *
     * @return
     */
    List<Staff> getAllActivateStaff();


    /**
     * 获取今天生日的员工
     * @return
     */
    List<Staff> getTodaysBirthdayEmployee();


    List<StaffContractReminderDto> getStaffContractExpireReminderToday(@Param("date") Date date,
                                                                       @Param("days") Long days,
                                                                       @Param("companyId") Long companyId);
    List<Map<String, Long>> getCreateUserIds(@Param("createUsers") Set<String> createUsers);
}