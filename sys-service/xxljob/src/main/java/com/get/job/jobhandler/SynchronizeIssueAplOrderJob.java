package com.get.job.jobhandler;

import com.get.job.service.sale.SynchronizationIssueService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

@Component
public class SynchronizeIssueAplOrderJob {

    private static final Logger logger = LoggerFactory.getLogger(SynchronizeIssueAplOrderJob.class);
    @Resource
    private SynchronizationIssueService synchronizationIssueService;


//    @XxlJob("SynchronizeUpdateIssueAplOrderJob")
//    private void SynchronizeUpdateIssueAplOrderJob() {
//        try {
//            XxlJobHelper.log("同步aplorder到 StudentOfferItem定时任务开始");
//            synchronizationIssueService.synchronizationOldIssue();
//        }catch (Exception e){
//            e.getStackTrace();
//            XxlJobHelper.log("func[{}] e[{}-{}] desc[RPA课程同步定时任务 error]");
//            logger.error("func[{}] e[{}-{}] desc[RPA课程同步定时任务 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
//                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
//
//        }
//
//    }
}
