package com.get.job.jobhandler;

import com.get.job.service.canaljob.ICanalJobService;
import com.get.job.service.getXxlJob.ISqlCronJobService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;

@Component
public class ExecSqlScriptJob {

    private static final Logger logger = LoggerFactory.getLogger(ExecSqlScriptJob.class);
    @Resource
    private ISqlCronJobService sqlCronJobService;



    /**
     * 每天凌晨3点定时执行SQL脚本（可能是多个，从数据库读取最新的脚本）
     */
    @XxlJob("execSqlScriptJob")
    private void execSqlScriptJob() {
        try {
            sqlCronJobService.execSqlScriptJob();
        }catch (Exception e){
            e.getStackTrace();
            XxlJobHelper.log("func[{}] e[{}-{}] desc[数据库脚本执行失败 error]");
            logger.error("func[{}] e[{}-{}] desc[数据库脚本执行失败 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }

    }
}
