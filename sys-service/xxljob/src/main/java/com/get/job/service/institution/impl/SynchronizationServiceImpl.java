package com.get.job.service.institution.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.consts.CacheKeyConstants;
import com.get.core.redis.cache.GetRedis;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.entity.InstitutionCourseRpaMapping;
import com.get.institutioncenter.entity.InstitutionRpaMappingRelation;
import com.get.job.component.RpaCourseMappingHelper;
import com.get.job.dao.institution.InstitutionCourseRpaMappingMapper;
import com.get.job.dao.institution.InstitutionCourseRpaMappingRelationMapper;
import com.get.job.dao.institution.InstitutionRpaMappingRelationMapper;
import com.get.job.service.institution.SynchronizationService;
import com.xxl.job.core.context.XxlJobHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/9/13 15:07
 */
@Service
public class SynchronizationServiceImpl implements SynchronizationService {
    private static final Logger logger = LoggerFactory.getLogger(SynchronizationServiceImpl.class);
    @Resource
    private InstitutionCourseRpaMappingMapper institutionCourseRpaMappingMapper;
    @Resource
    private InstitutionRpaMappingRelationMapper institutionRpaMappingRelationMapper;
    @Resource
    private GetRedis getRedis;
    @Resource
    private RpaCourseMappingHelper rpaCourseMappingHelper;
    @Resource
    private InstitutionCourseRpaMappingRelationMapper institutionCourseRpaMappingRelationMapper;


    /**
     * RPA课程同步
     *
     * @Date 16:25 2021/9/15
     * <AUTHOR>
     */
    @Override
    public void autoUpdateInstitutionCourseData() {
//        logger.info("RPA课程同步开始");
        XxlJobHelper.log("RPA课程同步开始");
        institutionCourseRpaMappingRelationMapper.delete(null);
        //institutionCourseRpaMappingRelationMapper.delete(new InstitutionCourseRpaMappingRelation());
        //先把所有RPA课程查出来
        List<InstitutionCourseRpaMapping> institutionCourseRpaMappings = institutionCourseRpaMappingMapper.selectInstitutionCourseRpaMappingsByType();

        Map<Integer, String> notExistMap = new HashMap<>();
    /*    //有些课程只有一级菜单,把一级菜单的也插进去
        Set<Integer> schSet = institutionCourseRpaMappings.stream().map(InstitutionCourseRpaMapping::getSchId).collect(Collectors.toSet());
        List<InstitutionCourseRpaMapping> institutionCourseRpaMappingList = institutionCourseRpaMappingMapper.selectInstitutionCourseRpaMappings("F", null, false, schSet);
        if (CheckUtils.isNotEmpty(institutionCourseRpaMappingList)) {
            institutionCourseRpaMappings.addAll(institutionCourseRpaMappingList);
        }*/

        //旧学校-新学校映射 旧学校-新学校多对一关系
        Map<Integer, Long> institutionSchMap = new HashMap<>();
        //旧学校-旧课程名-RPA课程地图Ids 注意 每个学校可能不一样 里面有RPA每一所学校，以及改学校里的所有课程
        Map<Integer, Map<String, List<Integer>>> schCourseRpaMap = new HashMap<>();

        //要查到每个旧学校id 以及对应的课程
        try {
            for (InstitutionCourseRpaMapping institutionCourseRpaMapping : institutionCourseRpaMappings) {
                //学校Id(旧系统ISSUE Id)
                Integer schId = institutionCourseRpaMapping.getSchId();
                //不存在学校映射缓存key
                String notExistSchMappingCacheKey = CacheKeyConstants.NOT_EXIST_INSTITUTION_MAPPING_CACHE_KEY + schId;
                //新系统学校id
                Long fkInstitutionId = institutionSchMap.get(schId);
                //如果不存在学校映射则不做课程映射
                if (GeneralTool.isNotEmpty(notExistMap.get(schId))) {
                    continue;
                }

                if (GeneralTool.isEmpty(fkInstitutionId)) {
                    LambdaQueryWrapper<InstitutionRpaMappingRelation> lambdaQueryWrapper = new LambdaQueryWrapper();
                    lambdaQueryWrapper.eq(InstitutionRpaMappingRelation::getFkInstitutionRpaMappingId, schId);
                    List<InstitutionRpaMappingRelation> institutionRpaMappingRelations = institutionRpaMappingRelationMapper.selectList(lambdaQueryWrapper);
                    //                Example example = new Example(InstitutionRpaMappingRelation.class);
                    //                example.createCriteria().andEqualTo("fkInstitutionRpaMappingId", schId);
                    //                List<InstitutionRpaMappingRelation> institutionRpaMappingRelations = institutionRpaMappingRelationMapper.selectByExample(example);
                    //如果不存在学校映射则不做课程映射
                    if (GeneralTool.isEmpty(institutionRpaMappingRelations)) {
//                        getRedis.incrBy(notExistSchMappingCacheKey, CacheKeyConstants.REDIS_KEY_EXPIRES_TWENTY_SECOND);
                        //redisClient.incr(notExistSchMappingCacheKey, CacheKeyConstants.REDIS_KEY_EXPIRES_TWENTY_SECOND);
                        notExistMap.put(schId,null);
                        continue;
                    }
                    fkInstitutionId = institutionRpaMappingRelations.get(0).getFkInstitutionId();
                    institutionSchMap.put(schId, fkInstitutionId);
                }
                //旧课程名-RPA课程地图Ids
                Map<String, List<Integer>> courseRpaMap = schCourseRpaMap.get(schId);
                if (GeneralTool.isEmpty(courseRpaMap)) {
                    courseRpaMap = new HashMap<>();
                    //待修改
                    courseRpaMap.put(institutionCourseRpaMapping.getValue(), new ArrayList<>(Collections.singleton(institutionCourseRpaMapping.getId().intValue())));
                    schCourseRpaMap.put(schId, courseRpaMap);
                } else {
                    List<Integer> rpaIds = courseRpaMap.get(institutionCourseRpaMapping.getValue());
                    if (GeneralTool.isEmpty(rpaIds)) {
                        rpaIds = new ArrayList<>();
                    }
                    rpaIds.add(institutionCourseRpaMapping.getId().intValue());

                    courseRpaMap.put(institutionCourseRpaMapping.getValue(), rpaIds);
                    schCourseRpaMap.put(schId, courseRpaMap);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            for (Map.Entry<Integer, Map<String, List<Integer>>> entry : schCourseRpaMap.entrySet()) {
                rpaCourseMappingHelper.insertRpaMapping(institutionSchMap, schCourseRpaMap, entry);
            }
        } catch (Exception e) {
            e.getStackTrace();
            logger.error("func[{}] e[{}-{}] desc[RPA课程同步定时任务 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }


//        logger.info("RPA课程同步结束");
        XxlJobHelper.log("RPA课程同步结束");

    }

}
