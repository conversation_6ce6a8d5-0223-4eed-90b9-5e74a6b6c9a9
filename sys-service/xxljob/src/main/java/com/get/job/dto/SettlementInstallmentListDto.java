package com.get.job.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/4/29 17:25
 */
@Data
public class SettlementInstallmentListDto {

    /**
     * 应收id
     */
    private Long receivablePlanId;

    /**
     * 应付id
     */
    private Long payablePlanId;

    /**
     * 应收
     */
    private BigDecimal receivableAmount;

    /**
     * 实收
     */
    private BigDecimal amountReceivable;

    /**
     * 应付
     */
    private BigDecimal payableAmount;

    /**
     * 实付
     */
    private BigDecimal amountPayable;

    /**
     * 应付未付
     */
    private BigDecimal difference;

}
