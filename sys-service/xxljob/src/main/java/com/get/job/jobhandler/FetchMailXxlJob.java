package com.get.job.jobhandler;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.get.job.dao.mail.MMailAccountMapper;
import com.get.job.dao.mail.MailAccount;
import com.get.job.service.mail.FetchMailService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@Component
public class FetchMailXxlJob {
    private static final Logger logger = LoggerFactory.getLogger(DynamicRouteHealthCheckJob.class);
    @Resource
    private FetchMailService fetchMailService;


    @Resource
    private RocketMQTemplate rocketMQTemplate;

    //定义发送的topic
    private static final String TOPIC = "fetch_mail_topic";

    @Resource
    private MMailAccountMapper mailAccountMapper;

    @XxlJob("fetchMailJob")
    public void fetchMailJob() throws Exception {
        try {
            logger.info("开始获取邮件");
            List<MailAccount> mailAccountList = mailAccountMapper.selectAccount();
            for (MailAccount mailAccount : mailAccountList) {
                logger.info("发送用户获取邮件消息，mMailAccount={},topic={}", mailAccount, TOPIC);
                rocketMQTemplate.asyncSend(TOPIC, mailAccount, new SendCallback() {
                    @Override
                    public void onSuccess(SendResult sendResult) {
                        logger.info("用户获取邮件消息发送成功，mMailAccount={}，消息ID={}", mailAccount, sendResult.getMsgId());
                    }
                    @Override
                    public void onException(Throwable throwable) {
                        logger.error("用户获取邮件消息发送失败，mMailAccount={}，异常信息={}", mailAccount, throwable.getMessage());
                        // 这里可以做失败告警、日志记录等处理
                    }
                });
            }
//            fetchMailService.fetchMail();
        } catch (Exception e) {
            e.getStackTrace();
            XxlJobHelper.log("获取邮件失败");
            logger.error("获取邮件失败，{}", Arrays.deepToString(e.getStackTrace()));
        }
    }
}
