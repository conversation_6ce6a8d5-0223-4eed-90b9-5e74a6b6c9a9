package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("CURRENCY")
public class Currency extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private String currency;
    private BigDecimal country;
}