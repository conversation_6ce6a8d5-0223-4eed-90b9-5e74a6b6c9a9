<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.permission.StaffMapper">
  <insert id="insert" parameterType="com.get.permissioncenter.entity.Staff" keyProperty="id" useGeneratedKeys="true">
    insert into m_staff (id, fk_company_id, fk_department_id,
      fk_position_id, fk_office_id, fk_staff_id_supervisor,
      fk_resume_guid, login_id, login_ps,
      num, name, name_en,
      gender, birthday, hukou,
      identity_type, identity_num, identity_card_address,
      home_tel_area_code, home_tel, work_tel_area_code,
      work_tel, mobile_area_code, mobile,
      email, zip_code, fk_area_country_id,
      fk_area_state_id, fk_area_city_id, fk_area_city_division_id,
      address, qq, wechat,
      whatsapp, emergency_contact, emergency_relationship,
      emergency_tel, job_description, salary_effective_date,
      salary_base, salary_performance, allowance_position,
      allowance_catering, allowance_transportation,
      allowance_telecom, allowance_other, entry_date,
      pass_probation_date, leave_date, is_on_duty,
      is_get_leaving_certificate, is_stop_social_insurance,
      stop_social_insurance_month, attendance_num,
      annual_leave_base, compensatory_leave_base,
      is_modified_ps, is_modified_resume, is_admin,
      is_active, id_gea, id_iae,
      session_id, gmt_create, gmt_create_user,
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkCompanyId,jdbcType=BIGINT}, #{fkDepartmentId,jdbcType=BIGINT},
      #{fkPositionId,jdbcType=BIGINT}, #{fkOfficeId,jdbcType=BIGINT}, #{fkStaffIdSupervisor,jdbcType=BIGINT},
      #{fkResumeGuid,jdbcType=VARCHAR}, #{loginId,jdbcType=VARCHAR}, #{loginPs,jdbcType=VARCHAR},
      #{num,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{nameEn,jdbcType=VARCHAR},
      #{gender,jdbcType=INTEGER}, #{birthday,jdbcType=DATE}, #{hukou,jdbcType=INTEGER},
      #{identityType,jdbcType=INTEGER}, #{identityNum,jdbcType=VARCHAR}, #{identityCardAddress,jdbcType=VARCHAR},
      #{homeTelAreaCode,jdbcType=VARCHAR}, #{homeTel,jdbcType=VARCHAR}, #{workTelAreaCode,jdbcType=VARCHAR},
      #{workTel,jdbcType=VARCHAR}, #{mobileAreaCode,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR},
      #{email,jdbcType=VARCHAR}, #{zipCode,jdbcType=VARCHAR}, #{fkAreaCountryId,jdbcType=BIGINT},
      #{fkAreaStateId,jdbcType=BIGINT}, #{fkAreaCityId,jdbcType=BIGINT}, #{fkAreaCityDivisionId,jdbcType=BIGINT},
      #{address,jdbcType=VARCHAR}, #{qq,jdbcType=VARCHAR}, #{wechat,jdbcType=VARCHAR},
      #{whatsapp,jdbcType=VARCHAR}, #{emergencyContact,jdbcType=VARCHAR}, #{emergencyRelationship,jdbcType=VARCHAR},
      #{emergencyTel,jdbcType=VARCHAR}, #{jobDescription,jdbcType=VARCHAR}, #{salaryEffectiveDate,jdbcType=DATE},
      #{salaryBase,jdbcType=DECIMAL}, #{salaryPerformance,jdbcType=DECIMAL}, #{allowancePosition,jdbcType=DECIMAL},
      #{allowanceCatering,jdbcType=DECIMAL}, #{allowanceTransportation,jdbcType=DECIMAL},
      #{allowanceTelecom,jdbcType=DECIMAL}, #{allowanceOther,jdbcType=DECIMAL}, #{entryDate,jdbcType=DATE},
      #{passProbationDate,jdbcType=DATE}, #{leaveDate,jdbcType=DATE}, #{isOnDuty,jdbcType=BIT},
      #{isGetLeavingCertificate,jdbcType=BIT}, #{isStopSocialInsurance,jdbcType=BIT},
      #{stopSocialInsuranceMonth,jdbcType=VARCHAR}, #{attendanceNum,jdbcType=VARCHAR},
      #{annualLeaveBase,jdbcType=DECIMAL}, #{compensatoryLeaveBase,jdbcType=DECIMAL},
      #{isModifiedPs,jdbcType=BIT}, #{isModifiedResume,jdbcType=BIT}, #{isAdmin,jdbcType=BIT},
      #{isActive,jdbcType=BIT}, #{idGea,jdbcType=VARCHAR}, #{idIae,jdbcType=VARCHAR},
      #{sessionId,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR},
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.permissioncenter.entity.Staff" keyProperty="id" useGeneratedKeys="true">
    insert into m_staff
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkCompanyId != null">
        fk_company_id,
      </if>
      <if test="fkDepartmentId != null">
        fk_department_id,
      </if>
      <if test="fkPositionId != null">
        fk_position_id,
      </if>
      <if test="fkOfficeId != null">
        fk_office_id,
      </if>
      <if test="fkStaffIdSupervisor != null">
        fk_staff_id_supervisor,
      </if>
      <if test="fkResumeGuid != null">
        fk_resume_guid,
      </if>
      <if test="loginId != null">
        login_id,
      </if>
      <if test="loginPs != null">
        login_ps,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameEn != null">
        name_en,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="hukou != null">
        hukou,
      </if>
      <if test="identityType != null">
        identity_type,
      </if>
      <if test="identityNum != null">
        identity_num,
      </if>
      <if test="identityCardAddress != null">
        identity_card_address,
      </if>
      <if test="homeTelAreaCode != null">
        home_tel_area_code,
      </if>
      <if test="homeTel != null">
        home_tel,
      </if>
      <if test="workTelAreaCode != null">
        work_tel_area_code,
      </if>
      <if test="workTel != null">
        work_tel,
      </if>
      <if test="mobileAreaCode != null">
        mobile_area_code,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="zipCode != null">
        zip_code,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id,
      </if>
      <if test="fkAreaCityId != null">
        fk_area_city_id,
      </if>
      <if test="fkAreaCityDivisionId != null">
        fk_area_city_division_id,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="qq != null">
        qq,
      </if>
      <if test="wechat != null">
        wechat,
      </if>
      <if test="whatsapp != null">
        whatsapp,
      </if>
      <if test="emergencyContact != null">
        emergency_contact,
      </if>
      <if test="emergencyRelationship != null">
        emergency_relationship,
      </if>
      <if test="emergencyTel != null">
        emergency_tel,
      </if>
      <if test="jobDescription != null">
        job_description,
      </if>
      <if test="salaryEffectiveDate != null">
        salary_effective_date,
      </if>
      <if test="salaryBase != null">
        salary_base,
      </if>
      <if test="salaryPerformance != null">
        salary_performance,
      </if>
      <if test="allowancePosition != null">
        allowance_position,
      </if>
      <if test="allowanceCatering != null">
        allowance_catering,
      </if>
      <if test="allowanceTransportation != null">
        allowance_transportation,
      </if>
      <if test="allowanceTelecom != null">
        allowance_telecom,
      </if>
      <if test="allowanceOther != null">
        allowance_other,
      </if>
      <if test="entryDate != null">
        entry_date,
      </if>
      <if test="passProbationDate != null">
        pass_probation_date,
      </if>
      <if test="leaveDate != null">
        leave_date,
      </if>
      <if test="isOnDuty != null">
        is_on_duty,
      </if>
      <if test="isGetLeavingCertificate != null">
        is_get_leaving_certificate,
      </if>
      <if test="isStopSocialInsurance != null">
        is_stop_social_insurance,
      </if>
      <if test="stopSocialInsuranceMonth != null">
        stop_social_insurance_month,
      </if>
      <if test="attendanceNum != null">
        attendance_num,
      </if>
      <if test="annualLeaveBase != null">
        annual_leave_base,
      </if>
      <if test="compensatoryLeaveBase != null">
        compensatory_leave_base,
      </if>
      <if test="isModifiedPs != null">
        is_modified_ps,
      </if>
      <if test="isModifiedResume != null">
        is_modified_resume,
      </if>
      <if test="isAdmin != null">
        is_admin,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="idGea != null">
        id_gea,
      </if>
      <if test="idIae != null">
        id_iae,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkCompanyId != null">
        #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="fkDepartmentId != null">
        #{fkDepartmentId,jdbcType=BIGINT},
      </if>
      <if test="fkPositionId != null">
        #{fkPositionId,jdbcType=BIGINT},
      </if>
      <if test="fkOfficeId != null">
        #{fkOfficeId,jdbcType=BIGINT},
      </if>
      <if test="fkStaffIdSupervisor != null">
        #{fkStaffIdSupervisor,jdbcType=BIGINT},
      </if>
      <if test="fkResumeGuid != null">
        #{fkResumeGuid,jdbcType=VARCHAR},
      </if>
      <if test="loginId != null">
        #{loginId,jdbcType=VARCHAR},
      </if>
      <if test="loginPs != null">
        #{loginPs,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=DATE},
      </if>
      <if test="hukou != null">
        #{hukou,jdbcType=INTEGER},
      </if>
      <if test="identityType != null">
        #{identityType,jdbcType=INTEGER},
      </if>
      <if test="identityNum != null">
        #{identityNum,jdbcType=VARCHAR},
      </if>
      <if test="identityCardAddress != null">
        #{identityCardAddress,jdbcType=VARCHAR},
      </if>
      <if test="homeTelAreaCode != null">
        #{homeTelAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="homeTel != null">
        #{homeTel,jdbcType=VARCHAR},
      </if>
      <if test="workTelAreaCode != null">
        #{workTelAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="workTel != null">
        #{workTel,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityId != null">
        #{fkAreaCityId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityDivisionId != null">
        #{fkAreaCityDivisionId,jdbcType=BIGINT},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="qq != null">
        #{qq,jdbcType=VARCHAR},
      </if>
      <if test="wechat != null">
        #{wechat,jdbcType=VARCHAR},
      </if>
      <if test="whatsapp != null">
        #{whatsapp,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContact != null">
        #{emergencyContact,jdbcType=VARCHAR},
      </if>
      <if test="emergencyRelationship != null">
        #{emergencyRelationship,jdbcType=VARCHAR},
      </if>
      <if test="emergencyTel != null">
        #{emergencyTel,jdbcType=VARCHAR},
      </if>
      <if test="jobDescription != null">
        #{jobDescription,jdbcType=VARCHAR},
      </if>
      <if test="salaryEffectiveDate != null">
        #{salaryEffectiveDate,jdbcType=DATE},
      </if>
      <if test="salaryBase != null">
        #{salaryBase,jdbcType=DECIMAL},
      </if>
      <if test="salaryPerformance != null">
        #{salaryPerformance,jdbcType=DECIMAL},
      </if>
      <if test="allowancePosition != null">
        #{allowancePosition,jdbcType=DECIMAL},
      </if>
      <if test="allowanceCatering != null">
        #{allowanceCatering,jdbcType=DECIMAL},
      </if>
      <if test="allowanceTransportation != null">
        #{allowanceTransportation,jdbcType=DECIMAL},
      </if>
      <if test="allowanceTelecom != null">
        #{allowanceTelecom,jdbcType=DECIMAL},
      </if>
      <if test="allowanceOther != null">
        #{allowanceOther,jdbcType=DECIMAL},
      </if>
      <if test="entryDate != null">
        #{entryDate,jdbcType=DATE},
      </if>
      <if test="passProbationDate != null">
        #{passProbationDate,jdbcType=DATE},
      </if>
      <if test="leaveDate != null">
        #{leaveDate,jdbcType=DATE},
      </if>
      <if test="isOnDuty != null">
        #{isOnDuty,jdbcType=BIT},
      </if>
      <if test="isGetLeavingCertificate != null">
        #{isGetLeavingCertificate,jdbcType=BIT},
      </if>
      <if test="isStopSocialInsurance != null">
        #{isStopSocialInsurance,jdbcType=BIT},
      </if>
      <if test="stopSocialInsuranceMonth != null">
        #{stopSocialInsuranceMonth,jdbcType=VARCHAR},
      </if>
      <if test="attendanceNum != null">
        #{attendanceNum,jdbcType=VARCHAR},
      </if>
      <if test="annualLeaveBase != null">
        #{annualLeaveBase,jdbcType=DECIMAL},
      </if>
      <if test="compensatoryLeaveBase != null">
        #{compensatoryLeaveBase,jdbcType=DECIMAL},
      </if>
      <if test="isModifiedPs != null">
        #{isModifiedPs,jdbcType=BIT},
      </if>
      <if test="isModifiedResume != null">
        #{isModifiedResume,jdbcType=BIT},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=BIT},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="idGea != null">
        #{idGea,jdbcType=VARCHAR},
      </if>
      <if test="idIae != null">
        #{idIae,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.permissioncenter.entity.Staff">
    update m_staff
    <set>
      <if test="fkCompanyId != null">
        fk_company_id = #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="fkDepartmentId != null">
        fk_department_id = #{fkDepartmentId,jdbcType=BIGINT},
      </if>
      <if test="fkPositionId != null">
        fk_position_id = #{fkPositionId,jdbcType=BIGINT},
      </if>
      <if test="fkOfficeId != null">
        fk_office_id = #{fkOfficeId,jdbcType=BIGINT},
      </if>
      <if test="fkStaffIdSupervisor != null">
        fk_staff_id_supervisor = #{fkStaffIdSupervisor,jdbcType=BIGINT},
      </if>
      <if test="fkResumeGuid != null">
        fk_resume_guid = #{fkResumeGuid,jdbcType=VARCHAR},
      </if>
      <if test="loginId != null">
        login_id = #{loginId,jdbcType=VARCHAR},
      </if>
      <if test="loginPs != null">
        login_ps = #{loginPs,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        name_en = #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=INTEGER},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=DATE},
      </if>
      <if test="hukou != null">
        hukou = #{hukou,jdbcType=INTEGER},
      </if>
      <if test="identityType != null">
        identity_type = #{identityType,jdbcType=INTEGER},
      </if>
      <if test="identityNum != null">
        identity_num = #{identityNum,jdbcType=VARCHAR},
      </if>
      <if test="identityCardAddress != null">
        identity_card_address = #{identityCardAddress,jdbcType=VARCHAR},
      </if>
      <if test="homeTelAreaCode != null">
        home_tel_area_code = #{homeTelAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="homeTel != null">
        home_tel = #{homeTel,jdbcType=VARCHAR},
      </if>
      <if test="workTelAreaCode != null">
        work_tel_area_code = #{workTelAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="workTel != null">
        work_tel = #{workTel,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        mobile_area_code = #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="zipCode != null">
        zip_code = #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id = #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityId != null">
        fk_area_city_id = #{fkAreaCityId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityDivisionId != null">
        fk_area_city_division_id = #{fkAreaCityDivisionId,jdbcType=BIGINT},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="qq != null">
        qq = #{qq,jdbcType=VARCHAR},
      </if>
      <if test="wechat != null">
        wechat = #{wechat,jdbcType=VARCHAR},
      </if>
      <if test="whatsapp != null">
        whatsapp = #{whatsapp,jdbcType=VARCHAR},
      </if>
      <if test="emergencyContact != null">
        emergency_contact = #{emergencyContact,jdbcType=VARCHAR},
      </if>
      <if test="emergencyRelationship != null">
        emergency_relationship = #{emergencyRelationship,jdbcType=VARCHAR},
      </if>
      <if test="emergencyTel != null">
        emergency_tel = #{emergencyTel,jdbcType=VARCHAR},
      </if>
      <if test="jobDescription != null">
        job_description = #{jobDescription,jdbcType=VARCHAR},
      </if>
      <if test="salaryEffectiveDate != null">
        salary_effective_date = #{salaryEffectiveDate,jdbcType=DATE},
      </if>
      <if test="salaryBase != null">
        salary_base = #{salaryBase,jdbcType=DECIMAL},
      </if>
      <if test="salaryPerformance != null">
        salary_performance = #{salaryPerformance,jdbcType=DECIMAL},
      </if>
      <if test="allowancePosition != null">
        allowance_position = #{allowancePosition,jdbcType=DECIMAL},
      </if>
      <if test="allowanceCatering != null">
        allowance_catering = #{allowanceCatering,jdbcType=DECIMAL},
      </if>
      <if test="allowanceTransportation != null">
        allowance_transportation = #{allowanceTransportation,jdbcType=DECIMAL},
      </if>
      <if test="allowanceTelecom != null">
        allowance_telecom = #{allowanceTelecom,jdbcType=DECIMAL},
      </if>
      <if test="allowanceOther != null">
        allowance_other = #{allowanceOther,jdbcType=DECIMAL},
      </if>
      <if test="entryDate != null">
        entry_date = #{entryDate,jdbcType=DATE},
      </if>
      <if test="passProbationDate != null">
        pass_probation_date = #{passProbationDate,jdbcType=DATE},
      </if>
      <if test="leaveDate != null">
        leave_date = #{leaveDate,jdbcType=DATE},
      </if>
      <if test="isOnDuty != null">
        is_on_duty = #{isOnDuty,jdbcType=BIT},
      </if>
      <if test="isGetLeavingCertificate != null">
        is_get_leaving_certificate = #{isGetLeavingCertificate,jdbcType=BIT},
      </if>
      <if test="isStopSocialInsurance != null">
        is_stop_social_insurance = #{isStopSocialInsurance,jdbcType=BIT},
      </if>
      <if test="stopSocialInsuranceMonth != null">
        stop_social_insurance_month = #{stopSocialInsuranceMonth,jdbcType=VARCHAR},
      </if>
      <if test="attendanceNum != null">
        attendance_num = #{attendanceNum,jdbcType=VARCHAR},
      </if>
      <if test="annualLeaveBase != null">
        annual_leave_base = #{annualLeaveBase,jdbcType=DECIMAL},
      </if>
      <if test="compensatoryLeaveBase != null">
        compensatory_leave_base = #{compensatoryLeaveBase,jdbcType=DECIMAL},
      </if>
      <if test="isModifiedPs != null">
        is_modified_ps = #{isModifiedPs,jdbcType=BIT},
      </if>
      <if test="isModifiedResume != null">
        is_modified_resume = #{isModifiedResume,jdbcType=BIT},
      </if>
      <if test="isAdmin != null">
        is_admin = #{isAdmin,jdbcType=BIT},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=BIT},
      </if>
      <if test="idGea != null">
        id_gea = #{idGea,jdbcType=VARCHAR},
      </if>
      <if test="idIae != null">
        id_iae = #{idIae,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.permissioncenter.entity.Staff">
    update m_staff
    set fk_company_id = #{fkCompanyId,jdbcType=BIGINT},
      fk_department_id = #{fkDepartmentId,jdbcType=BIGINT},
      fk_position_id = #{fkPositionId,jdbcType=BIGINT},
      fk_office_id = #{fkOfficeId,jdbcType=BIGINT},
      fk_staff_id_supervisor = #{fkStaffIdSupervisor,jdbcType=BIGINT},
      fk_resume_guid = #{fkResumeGuid,jdbcType=VARCHAR},
      login_id = #{loginId,jdbcType=VARCHAR},
      login_ps = #{loginPs,jdbcType=VARCHAR},
      num = #{num,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      name_en = #{nameEn,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=INTEGER},
      birthday = #{birthday,jdbcType=DATE},
      hukou = #{hukou,jdbcType=INTEGER},
      identity_type = #{identityType,jdbcType=INTEGER},
      identity_num = #{identityNum,jdbcType=VARCHAR},
      identity_card_address = #{identityCardAddress,jdbcType=VARCHAR},
      home_tel_area_code = #{homeTelAreaCode,jdbcType=VARCHAR},
      home_tel = #{homeTel,jdbcType=VARCHAR},
      work_tel_area_code = #{workTelAreaCode,jdbcType=VARCHAR},
      work_tel = #{workTel,jdbcType=VARCHAR},
      mobile_area_code = #{mobileAreaCode,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      zip_code = #{zipCode,jdbcType=VARCHAR},
      fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
      fk_area_state_id = #{fkAreaStateId,jdbcType=BIGINT},
      fk_area_city_id = #{fkAreaCityId,jdbcType=BIGINT},
      fk_area_city_division_id = #{fkAreaCityDivisionId,jdbcType=BIGINT},
      address = #{address,jdbcType=VARCHAR},
      qq = #{qq,jdbcType=VARCHAR},
      wechat = #{wechat,jdbcType=VARCHAR},
      whatsapp = #{whatsapp,jdbcType=VARCHAR},
      emergency_contact = #{emergencyContact,jdbcType=VARCHAR},
      emergency_relationship = #{emergencyRelationship,jdbcType=VARCHAR},
      emergency_tel = #{emergencyTel,jdbcType=VARCHAR},
      job_description = #{jobDescription,jdbcType=VARCHAR},
      salary_effective_date = #{salaryEffectiveDate,jdbcType=DATE},
      salary_base = #{salaryBase,jdbcType=DECIMAL},
      salary_performance = #{salaryPerformance,jdbcType=DECIMAL},
      allowance_position = #{allowancePosition,jdbcType=DECIMAL},
      allowance_catering = #{allowanceCatering,jdbcType=DECIMAL},
      allowance_transportation = #{allowanceTransportation,jdbcType=DECIMAL},
      allowance_telecom = #{allowanceTelecom,jdbcType=DECIMAL},
      allowance_other = #{allowanceOther,jdbcType=DECIMAL},
      entry_date = #{entryDate,jdbcType=DATE},
      pass_probation_date = #{passProbationDate,jdbcType=DATE},
      leave_date = #{leaveDate,jdbcType=DATE},
      is_on_duty = #{isOnDuty,jdbcType=BIT},
      is_get_leaving_certificate = #{isGetLeavingCertificate,jdbcType=BIT},
      is_stop_social_insurance = #{isStopSocialInsurance,jdbcType=BIT},
      stop_social_insurance_month = #{stopSocialInsuranceMonth,jdbcType=VARCHAR},
      attendance_num = #{attendanceNum,jdbcType=VARCHAR},
      annual_leave_base = #{annualLeaveBase,jdbcType=DECIMAL},
      compensatory_leave_base = #{compensatoryLeaveBase,jdbcType=DECIMAL},
      is_modified_ps = #{isModifiedPs,jdbcType=BIT},
      is_modified_resume = #{isModifiedResume,jdbcType=BIT},
      is_admin = #{isAdmin,jdbcType=BIT},
      is_active = #{isActive,jdbcType=BIT},
      id_gea = #{idGea,jdbcType=VARCHAR},
      id_iae = #{idIae,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <select id="getStaffByEntryDate" resultType="com.get.permissioncenter.entity.Staff">
        SELECT * FROM `m_staff` where 1=1
        <if test="date != null">
            and DATE_FORMAT(entry_date,'%m-%d') <![CDATA[= ]]> DATE_FORMAT(#{date},'%m-%d')
        </if>
        AND is_active = 1
        AND is_on_duty = 1
        <if test="isSyd">
          AND fk_company_id = 30
        </if>
      <if test="!isSyd">
        AND fk_company_id != 30
      </if>
    </select>
    <select id="getAllStaff" resultType="com.get.permissioncenter.entity.Staff">
        SELECT * FROM `m_staff` where is_active = 1 and is_on_duty = 1 and entry_date is not NULL
    </select>
    <select id="getAllActivateStaff" resultType="com.get.permissioncenter.entity.Staff">
        SELECT * FROM `m_staff` where is_active = 1 and is_on_duty = 1
    </select>
    <select id="getTodaysBirthdayEmployee" resultType="com.get.permissioncenter.entity.Staff">
      SELECT
        *
    FROM
        m_staff
    WHERE
        fk_company_id = 3
        AND DATE_FORMAT(birthday, '%m-%d') = DATE_FORMAT(NOW(), '%m-%d')
        AND is_active = 1
    </select>
  <select id="getStaffContractExpireReminderToday" resultType="com.get.job.dto.StaffContractReminderDto">
    SELECT
      a.*,
      b.fk_company_id,
      CONCAT(b.name,IF(b.name_en is null or b.name_en = '','',CONCAT("，",b.name_en))) as staffFullName,
      a.signing_company as companyName,
      c.`name` as departmentName,
      d.`name` as positionName,
      a.end_time as contractEndDate
    FROM
      m_staff_contract a
        LEFT JOIN m_staff b ON a.fk_staff_id = b.id
        LEFT JOIN m_department c on b.fk_department_id = c.id
        LEFT JOIN m_position d on d.id = b.fk_position_id
        INNER JOIN (	SELECT max(e.end_time) as end_time,e.fk_staff_id
                        FROM m_staff_contract e GROUP BY e.fk_staff_id) a1 on a1.fk_staff_id = a.fk_staff_id and a1.end_time = a.end_time
      where a.is_active = 1
      AND b.is_active = 1
      AND b.is_on_duty = 1
      and DATE_FORMAT( #{date}, '%Y-%m-%d' ) = DATE_FORMAT( DATE_SUB( a1.end_time, INTERVAL #{days} DAY ), '%Y-%m-%d' )
      AND b.fk_company_id = #{companyId}
  </select>

      <select id="getCreateUserIds" resultType="map">
      SELECT login_id,id FROM ais_permission_center.m_staff
       <where>
        is_active = 1
         <if test="createUsers != null and createUsers.size() > 0">
          AND login_id IN
           <foreach collection="createUsers" item="user" open="(" separator="," close=")">
             #{user}
           </foreach>
         </if>
       </where>
    </select>
</mapper>