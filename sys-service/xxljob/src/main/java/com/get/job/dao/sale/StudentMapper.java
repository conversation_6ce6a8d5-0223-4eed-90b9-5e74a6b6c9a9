package com.get.job.dao.sale;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.salecenter.entity.Student;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

@Mapper
@DS("saledb")
public interface StudentMapper extends BaseMapper<Student> {
    @Override
    int insert(Student record);

    int insertSelective(Student record);

    @Select("select * from m_student where FIND_IN_SET(#{id},id_gea)")
    List<Student> selectStudentById(@Param("id") BigDecimal id);

    List<Student> getStudentsWithoutInstitutionIdEducation();
}