package com.get.job.dao.finance;


import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.ReceiptFormItem;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("financedb")
public interface ReceiptFormItemMapper extends BaseMapper<ReceiptFormItem> {
    int insert(ReceiptFormItem record);

    int insertSelective(ReceiptFormItem record);
}