<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.sale.StudentMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.Student" useGeneratedKeys="true"
          keyProperty="id">
    insert into m_student (id, fk_company_id, num, 
      name, last_name, first_name, 
      gender, birthday, fk_area_country_id_nationality, 
      fk_area_country_name_nationality, fk_area_country_id_green_card, 
      passport_num, fk_area_country_id_birth, fk_area_state_id_birth, 
      fk_area_city_id_birth, fk_area_country_name_birth, 
      fk_area_state_name_birth, fk_area_city_name_birth, 
      mobile_area_code, mobile, tel_area_code, 
      tel, email, fk_area_country_id, 
      fk_area_state_id, fk_area_city_id, fk_area_country_name, 
      fk_area_state_name, fk_area_city_name, zipcode, 
      contact_address, education_level_type, education_major, 
      fk_area_country_id_education, fk_area_state_id_education, 
      fk_area_city_id_education, fk_area_country_name_education, 
      fk_area_state_name_education, fk_area_city_name_education, 
      fk_institution_id_education, fk_institution_name_education, 
      institution_type_education, education_level_type2, 
      education_major2, fk_area_country_id_education2, 
      fk_area_state_id_education2, fk_area_city_id_education2, 
      fk_area_country_name_education2, fk_area_state_name_education2, 
      fk_area_city_name_education2, fk_institution_id_education2, 
      fk_institution_name_education2, education_project, 
      education_degree, high_school_test_type, high_school_test_score, 
      standard_test_type, standard_test_score, english_test_type, 
      english_test_score, remark, condition_type, 
      num_gea, num_iae, id_issue, 
      id_issue_info, id_gea, id_iae, 
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkCompanyId,jdbcType=BIGINT}, #{num,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{lastName,jdbcType=VARCHAR}, #{firstName,jdbcType=VARCHAR}, 
      #{gender,jdbcType=VARCHAR}, #{birthday,jdbcType=TIMESTAMP}, #{fkAreaCountryIdNationality,jdbcType=BIGINT}, 
      #{fkAreaCountryNameNationality,jdbcType=VARCHAR}, #{fkAreaCountryIdGreenCard,jdbcType=BIGINT}, 
      #{passportNum,jdbcType=VARCHAR}, #{fkAreaCountryIdBirth,jdbcType=BIGINT}, #{fkAreaStateIdBirth,jdbcType=BIGINT}, 
      #{fkAreaCityIdBirth,jdbcType=BIGINT}, #{fkAreaCountryNameBirth,jdbcType=VARCHAR}, 
      #{fkAreaStateNameBirth,jdbcType=VARCHAR}, #{fkAreaCityNameBirth,jdbcType=VARCHAR}, 
      #{mobileAreaCode,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, #{telAreaCode,jdbcType=VARCHAR}, 
      #{tel,jdbcType=VARCHAR}, #{email,jdbcType=VARCHAR}, #{fkAreaCountryId,jdbcType=BIGINT}, 
      #{fkAreaStateId,jdbcType=BIGINT}, #{fkAreaCityId,jdbcType=BIGINT}, #{fkAreaCountryName,jdbcType=VARCHAR}, 
      #{fkAreaStateName,jdbcType=VARCHAR}, #{fkAreaCityName,jdbcType=VARCHAR}, #{zipcode,jdbcType=VARCHAR}, 
      #{contactAddress,jdbcType=VARCHAR}, #{educationLevelType,jdbcType=VARCHAR}, #{educationMajor,jdbcType=VARCHAR},
      #{fkAreaCountryIdEducation,jdbcType=BIGINT}, #{fkAreaStateIdEducation,jdbcType=BIGINT}, 
      #{fkAreaCityIdEducation,jdbcType=BIGINT}, #{fkAreaCountryNameEducation,jdbcType=VARCHAR}, 
      #{fkAreaStateNameEducation,jdbcType=VARCHAR}, #{fkAreaCityNameEducation,jdbcType=VARCHAR}, 
      #{fkInstitutionIdEducation,jdbcType=BIGINT}, #{fkInstitutionNameEducation,jdbcType=VARCHAR}, 
      #{institutionTypeEducation,jdbcType=VARCHAR}, #{educationLevelType2,jdbcType=VARCHAR},
      #{educationMajor2,jdbcType=VARCHAR}, #{fkAreaCountryIdEducation2,jdbcType=BIGINT}, 
      #{fkAreaStateIdEducation2,jdbcType=BIGINT}, #{fkAreaCityIdEducation2,jdbcType=BIGINT}, 
      #{fkAreaCountryNameEducation2,jdbcType=VARCHAR}, #{fkAreaStateNameEducation2,jdbcType=VARCHAR}, 
      #{fkAreaCityNameEducation2,jdbcType=VARCHAR}, #{fkInstitutionIdEducation2,jdbcType=BIGINT}, 
      #{fkInstitutionNameEducation2,jdbcType=VARCHAR}, #{educationProject,jdbcType=INTEGER}, 
      #{educationDegree,jdbcType=INTEGER}, #{highSchoolTestType,jdbcType=VARCHAR}, #{highSchoolTestScore,jdbcType=VARCHAR}, 
      #{standardTestType,jdbcType=VARCHAR}, #{standardTestScore,jdbcType=VARCHAR}, #{englishTestType,jdbcType=VARCHAR}, 
      #{englishTestScore,jdbcType=DECIMAL}, #{remark,jdbcType=VARCHAR}, #{conditionType,jdbcType=VARCHAR}, 
      #{numGea,jdbcType=VARCHAR}, #{numIae,jdbcType=VARCHAR}, #{idIssue,jdbcType=VARCHAR}, 
      #{idIssueInfo,jdbcType=VARCHAR}, #{idGea,jdbcType=VARCHAR}, #{idIae,jdbcType=VARCHAR}, 
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.Student" useGeneratedKeys="true"
          keyProperty="id">
    insert into m_student
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkCompanyId != null">
        fk_company_id,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="lastName != null">
        last_name,
      </if>
      <if test="firstName != null">
        first_name,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="fkAreaCountryIdNationality != null">
        fk_area_country_id_nationality,
      </if>
      <if test="fkAreaCountryNameNationality != null">
        fk_area_country_name_nationality,
      </if>
      <if test="fkAreaCountryIdGreenCard != null">
        fk_area_country_id_green_card,
      </if>
      <if test="passportNum != null">
        passport_num,
      </if>
      <if test="fkAreaCountryIdBirth != null">
        fk_area_country_id_birth,
      </if>
      <if test="fkAreaStateIdBirth != null">
        fk_area_state_id_birth,
      </if>
      <if test="fkAreaCityIdBirth != null">
        fk_area_city_id_birth,
      </if>
      <if test="fkAreaCountryNameBirth != null">
        fk_area_country_name_birth,
      </if>
      <if test="fkAreaStateNameBirth != null">
        fk_area_state_name_birth,
      </if>
      <if test="fkAreaCityNameBirth != null">
        fk_area_city_name_birth,
      </if>
      <if test="mobileAreaCode != null">
        mobile_area_code,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="telAreaCode != null">
        tel_area_code,
      </if>
      <if test="tel != null">
        tel,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id,
      </if>
      <if test="fkAreaCityId != null">
        fk_area_city_id,
      </if>
      <if test="fkAreaCountryName != null">
        fk_area_country_name,
      </if>
      <if test="fkAreaStateName != null">
        fk_area_state_name,
      </if>
      <if test="fkAreaCityName != null">
        fk_area_city_name,
      </if>
      <if test="zipcode != null">
        zipcode,
      </if>
      <if test="contactAddress != null">
        contact_address,
      </if>
      <if test="educationLevelType != null">
        education_level_type,
      </if>
      <if test="educationMajor != null">
        education_major,
      </if>
      <if test="fkAreaCountryIdEducation != null">
        fk_area_country_id_education,
      </if>
      <if test="fkAreaStateIdEducation != null">
        fk_area_state_id_education,
      </if>
      <if test="fkAreaCityIdEducation != null">
        fk_area_city_id_education,
      </if>
      <if test="fkAreaCountryNameEducation != null">
        fk_area_country_name_education,
      </if>
      <if test="fkAreaStateNameEducation != null">
        fk_area_state_name_education,
      </if>
      <if test="fkAreaCityNameEducation != null">
        fk_area_city_name_education,
      </if>
      <if test="fkInstitutionIdEducation != null">
        fk_institution_id_education,
      </if>
      <if test="fkInstitutionNameEducation != null">
        fk_institution_name_education,
      </if>
      <if test="institutionTypeEducation != null">
        institution_type_education,
      </if>
      <if test="educationLevelType2 != null">
        education_level_type2,
      </if>
      <if test="educationMajor2 != null">
        education_major2,
      </if>
      <if test="fkAreaCountryIdEducation2 != null">
        fk_area_country_id_education2,
      </if>
      <if test="fkAreaStateIdEducation2 != null">
        fk_area_state_id_education2,
      </if>
      <if test="fkAreaCityIdEducation2 != null">
        fk_area_city_id_education2,
      </if>
      <if test="fkAreaCountryNameEducation2 != null">
        fk_area_country_name_education2,
      </if>
      <if test="fkAreaStateNameEducation2 != null">
        fk_area_state_name_education2,
      </if>
      <if test="fkAreaCityNameEducation2 != null">
        fk_area_city_name_education2,
      </if>
      <if test="fkInstitutionIdEducation2 != null">
        fk_institution_id_education2,
      </if>
      <if test="fkInstitutionNameEducation2 != null">
        fk_institution_name_education2,
      </if>
      <if test="educationProject != null">
        education_project,
      </if>
      <if test="educationDegree != null">
        education_degree,
      </if>
      <if test="highSchoolTestType != null">
        high_school_test_type,
      </if>
      <if test="highSchoolTestScore != null">
        high_school_test_score,
      </if>
      <if test="standardTestType != null">
        standard_test_type,
      </if>
      <if test="standardTestScore != null">
        standard_test_score,
      </if>
      <if test="englishTestType != null">
        english_test_type,
      </if>
      <if test="englishTestScore != null">
        english_test_score,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="conditionType != null">
        condition_type,
      </if>
      <if test="numGea != null">
        num_gea,
      </if>
      <if test="numIae != null">
        num_iae,
      </if>
      <if test="idIssue != null">
        id_issue,
      </if>
      <if test="idIssueInfo != null">
        id_issue_info,
      </if>
      <if test="idGea != null">
        id_gea,
      </if>
      <if test="idIae != null">
        id_iae,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkCompanyId != null">
        #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="lastName != null">
        #{lastName,jdbcType=VARCHAR},
      </if>
      <if test="firstName != null">
        #{firstName,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=VARCHAR},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=TIMESTAMP},
      </if>
      <if test="fkAreaCountryIdNationality != null">
        #{fkAreaCountryIdNationality,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCountryNameNationality != null">
        #{fkAreaCountryNameNationality,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryIdGreenCard != null">
        #{fkAreaCountryIdGreenCard,jdbcType=BIGINT},
      </if>
      <if test="passportNum != null">
        #{passportNum,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryIdBirth != null">
        #{fkAreaCountryIdBirth,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateIdBirth != null">
        #{fkAreaStateIdBirth,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityIdBirth != null">
        #{fkAreaCityIdBirth,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCountryNameBirth != null">
        #{fkAreaCountryNameBirth,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaStateNameBirth != null">
        #{fkAreaStateNameBirth,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCityNameBirth != null">
        #{fkAreaCityNameBirth,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="telAreaCode != null">
        #{telAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="tel != null">
        #{tel,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityId != null">
        #{fkAreaCityId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCountryName != null">
        #{fkAreaCountryName,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaStateName != null">
        #{fkAreaStateName,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCityName != null">
        #{fkAreaCityName,jdbcType=VARCHAR},
      </if>
      <if test="zipcode != null">
        #{zipcode,jdbcType=VARCHAR},
      </if>
      <if test="contactAddress != null">
        #{contactAddress,jdbcType=VARCHAR},
      </if>
      <if test="educationLevelType != null">
        #{educationLevelType,jdbcType=VARCHAR},
      </if>
      <if test="educationMajor != null">
        #{educationMajor,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryIdEducation != null">
        #{fkAreaCountryIdEducation,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateIdEducation != null">
        #{fkAreaStateIdEducation,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityIdEducation != null">
        #{fkAreaCityIdEducation,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCountryNameEducation != null">
        #{fkAreaCountryNameEducation,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaStateNameEducation != null">
        #{fkAreaStateNameEducation,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCityNameEducation != null">
        #{fkAreaCityNameEducation,jdbcType=VARCHAR},
      </if>
      <if test="fkInstitutionIdEducation != null">
        #{fkInstitutionIdEducation,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionNameEducation != null">
        #{fkInstitutionNameEducation,jdbcType=VARCHAR},
      </if>
      <if test="institutionTypeEducation != null">
        #{institutionTypeEducation,jdbcType=VARCHAR},
      </if>
      <if test="educationLevelType2 != null">
        #{educationLevelType2,jdbcType=VARCHAR},
      </if>
      <if test="educationMajor2 != null">
        #{educationMajor2,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryIdEducation2 != null">
        #{fkAreaCountryIdEducation2,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateIdEducation2 != null">
        #{fkAreaStateIdEducation2,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityIdEducation2 != null">
        #{fkAreaCityIdEducation2,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCountryNameEducation2 != null">
        #{fkAreaCountryNameEducation2,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaStateNameEducation2 != null">
        #{fkAreaStateNameEducation2,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCityNameEducation2 != null">
        #{fkAreaCityNameEducation2,jdbcType=VARCHAR},
      </if>
      <if test="fkInstitutionIdEducation2 != null">
        #{fkInstitutionIdEducation2,jdbcType=BIGINT},
      </if>
      <if test="fkInstitutionNameEducation2 != null">
        #{fkInstitutionNameEducation2,jdbcType=VARCHAR},
      </if>
      <if test="educationProject != null">
        #{educationProject,jdbcType=INTEGER},
      </if>
      <if test="educationDegree != null">
        #{educationDegree,jdbcType=INTEGER},
      </if>
      <if test="highSchoolTestType != null">
        #{highSchoolTestType,jdbcType=VARCHAR},
      </if>
      <if test="highSchoolTestScore != null">
        #{highSchoolTestScore,jdbcType=VARCHAR},
      </if>
      <if test="standardTestType != null">
        #{standardTestType,jdbcType=VARCHAR},
      </if>
      <if test="standardTestScore != null">
        #{standardTestScore,jdbcType=VARCHAR},
      </if>
      <if test="englishTestType != null">
        #{englishTestType,jdbcType=VARCHAR},
      </if>
      <if test="englishTestScore != null">
        #{englishTestScore,jdbcType=DECIMAL},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="conditionType != null">
        #{conditionType,jdbcType=VARCHAR},
      </if>
      <if test="numGea != null">
        #{numGea,jdbcType=VARCHAR},
      </if>
      <if test="numIae != null">
        #{numIae,jdbcType=VARCHAR},
      </if>
      <if test="idIssue != null">
        #{idIssue,jdbcType=VARCHAR},
      </if>
      <if test="idIssueInfo != null">
        #{idIssueInfo,jdbcType=VARCHAR},
      </if>
      <if test="idGea != null">
        #{idGea,jdbcType=VARCHAR},
      </if>
      <if test="idIae != null">
        #{idIae,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
    <select id="getStudentsWithoutInstitutionIdEducation" resultType="com.get.salecenter.entity.Student">
      select * from m_student
      where
        (
          fk_institution_name_education IS NOT NULL
            AND (
            fk_institution_id_education is null
              or fk_institution_id_education = ''
            )
          )
         OR
        (
          fk_institution_name_education2 IS NOT NULL
            AND (
            fk_institution_id_education2 is null
              or fk_institution_id_education2 = ''
            )
          )

    </select>
</mapper>