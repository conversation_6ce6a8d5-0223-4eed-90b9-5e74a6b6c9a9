package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("log_operation")
public class LogOperationHis implements Serializable {
    protected static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @ApiModelProperty("id，主键")
    @TableId(value = "id", type = IdType.AUTO)
    protected Long id;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty("修改时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date gmtModified;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @ApiModelProperty("修改人")
    private String gmtModifiedUser;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    /**
     * 登陆用户Id
     */
    @ApiModelProperty(value = "登陆用户Id")
    private String staffLoginId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String staffName;

    /**
     * 操作编号
     */
    @ApiModelProperty(value = "操作编号")
    private String optCode;

    /**
     * 操作模块名称
     */
    @ApiModelProperty(value = "操作模块名称")
    private String optModuleName;

    /**
     * 操作类型
     */
    @ApiModelProperty(value = "操作类型")
    private String optType;

    /**
     * 操作提交的数据
     */
    @ApiModelProperty(value = "操作提交的参数")
    protected String optParam;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
}
