package com.get.job.component;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.entity.PayablePlanSettlementInstallment;
import com.get.job.dao.sale.PayablePlanMapper;
import com.get.job.dao.sale.PayablePlanSettlementAgentAccountMapper;
import com.get.job.dao.sale.PayablePlanSettlementInstallmentMapper;
import com.get.job.dao.sale.PayablePlanSettlementStatusMapper;
import com.get.financecenter.entity.PayablePlanSettlementStatus;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 财务佣金回退定时任务
 *
 * <AUTHOR>
 * @date 2022/7/15 10:56
 */
@Component
public class FinanceSettlementRollBackHelper {

    @Resource
    private PayablePlanSettlementInstallmentMapper payablePlanSettlementInstallmentMapper;
    @Resource
    private PayablePlanSettlementAgentAccountMapper payablePlanSettlementAgentAccountMapper;
    @Resource
    private PayablePlanMapper payablePlanMapper;
    @Resource
    private PayablePlanSettlementStatusMapper payablePlanSettlementStatusMapper;

    @DSTransactional
    public void financeSettlementRollBack(){
        List<PayablePlanSettlementInstallment> payablePlanSettlementInstallmentList = payablePlanSettlementInstallmentMapper.getInvalidCommission();
        if (GeneralTool.isNotEmpty(payablePlanSettlementInstallmentList)) {
            for (PayablePlanSettlementInstallment payablePlanSettlementInstallment : payablePlanSettlementInstallmentList) {
                PayablePlanSettlementInstallment planSettlementInstallment = new PayablePlanSettlementInstallment();
                planSettlementInstallment.setStatus(ProjectExtraEnum.INSTALLMENT_UNTREATED.key);
                planSettlementInstallment.setRollBack(true);
                planSettlementInstallment.setGmtModifiedUser("System rollback");
                planSettlementInstallment.setGmtModified(new Date());
                planSettlementInstallment.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                planSettlementInstallment.setAmountActual(payablePlanSettlementInstallment.getAmountActualInit());
                planSettlementInstallment.setServiceFeeActual(payablePlanSettlementInstallment.getServiceFeeActualInit());
                payablePlanSettlementInstallmentMapper.update(planSettlementInstallment,Wrappers.<PayablePlanSettlementInstallment>lambdaUpdate()
                        .in(PayablePlanSettlementInstallment::getId, payablePlanSettlementInstallment.getId())
                        .set(PayablePlanSettlementInstallment::getRollBackTime, null)
                        .set(PayablePlanSettlementInstallment::getFkAgentContractAccountId, null));

                PayablePlanSettlementStatus payablePlanSettlementStatus = new PayablePlanSettlementStatus();
                payablePlanSettlementStatus.setFkPayablePlanId(payablePlanSettlementInstallment.getFkPayablePlanId());
                payablePlanSettlementStatus.setFkPayablePlanSettlementInstallmentId(payablePlanSettlementInstallment.getId());
                payablePlanSettlementStatus.setStatusSettlement(ProjectExtraEnum.UNSETTLED.key);
                payablePlanSettlementStatus.setGmtCreateUser("System rollback");
                payablePlanSettlementStatus.setGmtCreate(new Date());
                payablePlanSettlementStatusMapper.insert(payablePlanSettlementStatus);

            }

        }



//        payablePlanSettlementInstallmentMapper.se
    }
}
