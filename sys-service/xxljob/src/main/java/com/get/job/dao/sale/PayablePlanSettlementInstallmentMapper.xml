<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.sale.PayablePlanSettlementInstallmentMapper">

  <select id="getInsertSettlementAmountActual" resultType="java.math.BigDecimal">
    SELECT SUM(IFNULL(a1.amount_actual,0)) + SUM(IFNULL(a2.amount_actual,0)) + SUM(IFNULL(a3.amount_actual,0)) AS amount_actual
    FROM m_payable_plan AS mpp
    -- 计算应付计划分期表为未处理状态的实付金额
    LEFT JOIN (
    SELECT mpp1.id,SUM(rppsi1.amount_actual) AS amount_actual FROM m_payable_plan AS mpp1
    INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 0
    WHERE mpp1.id = #{payablePlanId}
    GROUP BY mpp1.id
    )a1 ON a1.id = mpp.id
    -- 计算应付计划分期表为处理中状态，2-4步情况的实付金额
    LEFT JOIN (
    SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual FROM m_payable_plan AS mpp1
    INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 1 AND rppsi1.num_settlement_batch is null
    WHERE mpp1.id = #{payablePlanId}
    GROUP BY mpp1.id
    )a2 ON a2.id = mpp.id
    -- 计算应付计划分期表为处理中状态，第5步情况的实付金额
    LEFT JOIN (
    SELECT b.id,SUM(b.amount_actual) AS amount_actual FROM (
    SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual FROM m_payable_plan AS mpp1
    INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id
    AND rppsi1.num_settlement_batch is not null
    WHERE mpp1.id = #{payablePlanId}
    GROUP BY mpp1.id,rppsi1.num_settlement_batch)b
    GROUP BY b.id
    )a3 ON a3.id = mpp.id
  </select>
  <select id="getInsertSettlementPrepaidAmount" resultType="java.math.BigDecimal">
    SELECT SUM(IFNULL(a1.amount_actual,0)) + SUM(IFNULL(a2.amount_actual,0)) + SUM(IFNULL(a3.amount_actual,0)) AS amount_actual
    FROM m_payable_plan AS mpp
           -- 计算应付计划分期表为未处理状态的实付金额
           LEFT JOIN (
      SELECT mpp1.id,SUM(rppsi1.amount_actual) AS amount_actual FROM m_payable_plan AS mpp1
      INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 0
      WHERE mpp1.id = #{payablePlanId} AND rppsi1.fk_receipt_form_item_id IS NULL
      GROUP BY mpp1.id
    )a1 ON a1.id = mpp.id
      -- 计算应付计划分期表为处理中状态，2-4步情况的实付金额
      LEFT JOIN (
      SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual FROM m_payable_plan AS mpp1
      INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 1 AND rppsi1.num_settlement_batch is null
      WHERE mpp1.id = #{payablePlanId} AND rppsi1.fk_receipt_form_item_id IS NULL
      GROUP BY mpp1.id
    )a2 ON a2.id = mpp.id
      -- 计算应付计划分期表为处理中状态，第5步情况的实付金额
      LEFT JOIN (
      SELECT b.id,SUM(b.amount_actual) AS amount_actual FROM (
      SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual FROM m_payable_plan AS mpp1
      INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id
      AND rppsi1.num_settlement_batch is not null
      WHERE mpp1.id = #{payablePlanId} AND rppsi1.fk_receipt_form_item_id IS NULL
      GROUP BY mpp1.id,rppsi1.num_settlement_batch)b
      GROUP BY b.id
    )a3 ON a3.id = mpp.id
  </select>
  <select id="getInsertSettlementNotPrepaidAmount" resultType="java.math.BigDecimal">
    SELECT SUM(IFNULL(a1.amount_actual,0)) + SUM(IFNULL(a2.amount_actual,0)) + SUM(IFNULL(a3.amount_actual,0)) AS amount_actual
    FROM m_payable_plan AS mpp
           -- 计算应付计划分期表为未处理状态的实付金额
           LEFT JOIN (
      SELECT mpp1.id,SUM(rppsi1.amount_actual) AS amount_actual FROM m_payable_plan AS mpp1
      INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 0
      WHERE mpp1.id = #{payablePlanId} AND rppsi1.fk_receipt_form_item_id IS NOT NULL
      GROUP BY mpp1.id
    )a1 ON a1.id = mpp.id
      -- 计算应付计划分期表为处理中状态，2-4步情况的实付金额
      LEFT JOIN (
      SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual FROM m_payable_plan AS mpp1
      INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id AND rppsi1.status = 1 AND rppsi1.num_settlement_batch is null
      WHERE mpp1.id = #{payablePlanId} AND rppsi1.fk_receipt_form_item_id IS NOT NULL
      GROUP BY mpp1.id
    )a2 ON a2.id = mpp.id
      -- 计算应付计划分期表为处理中状态，第5步情况的实付金额
           LEFT JOIN (
      SELECT b.id,SUM(b.amount_actual) AS amount_actual FROM (
      SELECT mpp1.id,MAX(rppsi1.amount_actual) AS amount_actual FROM m_payable_plan AS mpp1
      INNER JOIN ais_finance_center.r_payable_plan_settlement_installment AS rppsi1 ON rppsi1.fk_payable_plan_id = mpp1.id
      AND rppsi1.num_settlement_batch is not null
      WHERE mpp1.id = #{payablePlanId} AND rppsi1.fk_receipt_form_item_id IS NOT NULL
      GROUP BY mpp1.id,rppsi1.num_settlement_batch)b
      GROUP BY b.id
    )a3 ON a3.id = mpp.id
  </select>
  <select id="getInsertSettlementPrepaidHedgingAmount" resultType="java.math.BigDecimal">
    SELECT IFNULL(SUM(amount_expect), 0) + IFNULL(SUM(service_fee_expect), 0) AS amount_expect FROM ais_finance_center.r_payable_plan_settlement_installment where fk_payable_plan_id = #{payablePlanId} AND status = 0 AND fk_receipt_form_item_id is not null
  </select>
  <select id="getInsertSettlementPrepaidHedgingServiceFeeAmount" resultType="java.math.BigDecimal">
    SELECT IFNULL(SUM(service_fee_expect), 0) + IFNULL(SUM(service_fee_expect), 0) AS amount_expect FROM ais_finance_center.r_payable_plan_settlement_installment where fk_payable_plan_id = #{payablePlanId} AND status = 0 AND fk_receipt_form_item_id is not null
  </select>
    <select id="getInvalidCommission" resultType="com.get.financecenter.entity.PayablePlanSettlementInstallment">
      SELECT
        *
      FROM
        ais_finance_center.r_payable_plan_settlement_installment
      WHERE
          roll_back_time is not null  AND DATE_FORMAT( roll_back_time, '%Y-%m-%d' ) <![CDATA[< ]]> DATE_FORMAT( now(), '%Y-%m-%d' ) AND status_settlement = 2
    </select>

</mapper>