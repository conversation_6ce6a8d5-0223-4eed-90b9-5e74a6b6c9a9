package com.get.job.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * IAECRM
 */
@Data
@TableName("MSys_Attachment")
public class MSysAttachment implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId(
            value = "AttachmentID",
            type = IdType.NONE
    )
    private Long AttachmentID;

    @TableField("TargetID")
    private Long TargetID;

    @TableField("Target")
    private String Target;

    @TableField("Description")
    private String Description;

    @TableField("AttachmentFileID")
    private String AttachmentFileID;
}