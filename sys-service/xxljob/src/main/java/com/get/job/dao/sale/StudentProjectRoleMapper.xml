<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.sale.StudentProjectRoleMapper">
  <insert id="insert" parameterType="com.get.salecenter.entity.StudentProjectRole">
    insert into u_student_project_role (id, fk_company_id, role_name, 
      role_key, department_num, remark, 
      view_order, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkCompanyId,jdbcType=BIGINT}, #{roleName,jdbcType=VARCHAR}, 
      #{roleKey,jdbcType=VARCHAR}, #{departmentNum,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, 
      #{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.salecenter.entity.StudentProjectRole">
    insert into u_student_project_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkCompanyId != null">
        fk_company_id,
      </if>
      <if test="roleName != null">
        role_name,
      </if>
      <if test="roleKey != null">
        role_key,
      </if>
      <if test="departmentNum != null">
        department_num,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkCompanyId != null">
        #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="roleName != null">
        #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="roleKey != null">
        #{roleKey,jdbcType=VARCHAR},
      </if>
      <if test="departmentNum != null">
        #{departmentNum,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.salecenter.entity.StudentProjectRole">
    update u_student_project_role
    <set>
      <if test="fkCompanyId != null">
        fk_company_id = #{fkCompanyId,jdbcType=BIGINT},
      </if>
      <if test="roleName != null">
        role_name = #{roleName,jdbcType=VARCHAR},
      </if>
      <if test="roleKey != null">
        role_key = #{roleKey,jdbcType=VARCHAR},
      </if>
      <if test="departmentNum != null">
        department_num = #{departmentNum,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="viewOrder != null">
        view_order = #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.salecenter.entity.StudentProjectRole">
    update u_student_project_role
    set fk_company_id = #{fkCompanyId,jdbcType=BIGINT},
      role_name = #{roleName,jdbcType=VARCHAR},
      role_key = #{roleKey,jdbcType=VARCHAR},
      department_num = #{departmentNum,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      view_order = #{viewOrder,jdbcType=INTEGER},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>