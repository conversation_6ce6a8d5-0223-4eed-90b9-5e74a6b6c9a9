package com.get.job.dao.iaecrm;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.job.dto.CrmNewsDto;
import com.get.job.entity.MAttachment;
import com.get.job.entity.MSysAttachment;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
@DS("iaeCrmNewsdb")
public interface MSysAttachmentMapper extends BaseMapper<MSysAttachment> {
//    List<CrmNewsDto> getHtiNews(IPage<CrmNewsDto> iPage);
}