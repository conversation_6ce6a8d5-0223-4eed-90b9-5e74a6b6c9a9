package com.get.job.dao.log;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.log.model.LogLogin;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * @Author:cream
 * @Date: 2023/5/23  17:03
 */
@Mapper
@DS("logdb")
public interface LogLoginMapper extends BaseMapper<LogLogin> {

    /**
     * 获取昨天的数据
     * @return
     */
    List<LogLogin> getYesterdayLoginLogList();
}
