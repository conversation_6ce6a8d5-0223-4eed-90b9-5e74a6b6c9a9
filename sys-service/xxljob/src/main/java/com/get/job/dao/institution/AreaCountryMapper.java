package com.get.job.dao.institution;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.institutioncenter.entity.AreaCountry;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
@DS("institutiondb")
public interface AreaCountryMapper extends BaseMapper<AreaCountry> {
    int insert(AreaCountry record);

    int insertSelective(AreaCountry record);

    int updateByPrimaryKeySelective(AreaCountry record);

    int updateByPrimaryKey(AreaCountry record);

    /**
     * 根据idGea查找国家
     *
     * @Date 17:30 2021/11/10
     * <AUTHOR>
     */
    List<AreaCountry> selectCourtryListByFindInSetGeaId(@Param("idGea") String idGea);
}