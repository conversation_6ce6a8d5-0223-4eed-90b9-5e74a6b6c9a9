package com.get.job.service.historytable.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.get.core.log.model.LogOperation;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.job.dao.historytable.HisLogOperationMapper;
import com.get.job.entity.LogOperationHisDto;
import com.get.job.service.historytable.IHistoryTableService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class HistoryTableServiceImpl extends ServiceImpl<HisLogOperationMapper, LogOperation> implements IHistoryTableService {

    @Resource
    private HisLogOperationMapper hisLogOptionMapper;

    @Override
    public void historyTableHandle() {
        //获取所有的年份+季度，先创建相关的表
        List<LogOperationHisDto> logOperationHisDtos = hisLogOptionMapper.getLogOperationHis();
        if(GeneralTool.isNotEmpty(logOperationHisDtos) && logOperationHisDtos.size()>0)
        {
            for (LogOperationHisDto logOperationHisDto:logOperationHisDtos)
            {
                String hisTableName = "log_operation"+logOperationHisDto.getTableHisName();
                //创建相关的历史表
                hisLogOptionMapper.createHisLogOperationTable(hisTableName);
                //根据本次查询的季度去查询数据，将数据分页写入到历史表中
                this.logOperationToHis(hisTableName);
            }
        }
    }

    //根据tableHisName获取所有相关的数据，分页插入到历史表
    private void logOperationToHis(String hisTableName)
    {
        //查询符合条件的id和历史表名集合
        Integer currentPage = 1;
        Integer size = 500;//每次处理500
        Long total = 0L;//总数默认为0
        IPage<LogOperation> iPage = GetCondition.getPage(PageUtil.convertToQuery(currentPage, size));
        List<LogOperation> records = new ArrayList<>();
        String sql = " CONCAT('log_operation_his_',YEAR ( gmt_create ),'_','Q',QUARTER ( gmt_create )) = '"+hisTableName+"' and gmt_create < (DATE_SUB(CURDATE(),INTERVAL 3 MONTH))";
        Wrapper<LogOperation> wrapper = Wrappers.<LogOperation>lambdaQuery().orderByAsc(LogOperation::getId).apply(sql);
        iPage = hisLogOptionMapper.selectPage(iPage,wrapper);
        records = iPage.getRecords();

        if(records.size()>0)
        {
            hisLogOptionMapper.insertBatch(hisTableName,records);
        }

        if(GeneralTool.isNotEmpty(records))
        {
            total = iPage.getTotal();
            // 计算总页数
            int totalPages = (int) Math.ceil(total / (double) size);
            // 分页获取符合条件的所有数据
            if (totalPages > 0) {
                for (int i = 2; i <= totalPages; i++) {
                    iPage.setCurrent(i);
                    iPage = hisLogOptionMapper.selectPage(iPage,wrapper);
                    records = iPage.getRecords();
                    if(records.size()>0)
                    {
                        hisLogOptionMapper.insertBatch(hisTableName,records);
                    }
                    try
                    {
                        Thread.sleep(1000);//休息一下
                    }catch (Exception e)
                    {
                        e.printStackTrace();
                    }
                }
                //删除3个月前的季度数据
                hisLogOptionMapper.delete(wrapper);
            }

        }
    }
}

