<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.job.dao.iaecrm.MAttachmentMapper">
  <resultMap id="BaseResultMap" type="com.get.job.entity.MAttachment">
    <id column="AttachmentID" jdbcType="BIGINT" property="AttachmentID"/>
    <result column="AttachmentFunction" jdbcType="VARCHAR" property="AttachmentFunction" />
    <result column="AttachmentType" jdbcType="VARCHAR" property="AttachmentType" />
    <result column="FileName" jdbcType="VARCHAR" property="FileName" />
    <result column="AttachmentFile" jdbcType="LONGVARBINARY" property="AttachmentFile" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.get.job.entity.MAttachment">
    <result column="AttachmentFile" jdbcType="LONGVARBINARY" property="AttachmentFile" />
  </resultMap>
  <sql id="Base_Column_List">
    AttachmentID, AttachmentFunction, AttachmentType, FileName, AttachmentFile
  </sql>
  <sql id="Blob_Column_List">
    AttachmentFile
  </sql>

</mapper>