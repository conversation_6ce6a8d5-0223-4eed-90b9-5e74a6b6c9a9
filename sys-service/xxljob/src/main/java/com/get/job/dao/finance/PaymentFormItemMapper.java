package com.get.job.dao.finance;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.financecenter.entity.PaymentFormItem;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("financedb")
public interface PaymentFormItemMapper extends BaseMapper<PaymentFormItem> {

    int insert(PaymentFormItem record);

    int insertSelective(PaymentFormItem record);
}