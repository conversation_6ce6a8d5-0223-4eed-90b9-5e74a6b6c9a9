package com.get.job.jobhandler;

import com.get.core.tool.api.Result;
import com.get.salecenter.feign.ISaleCenterClient;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Time: 17:09
 * Date: 2022/3/31
 * Description:
 */
@Component
public class AgentXxlJob {
    private static final Logger logger = LoggerFactory.getLogger(InstitutionCourseXxlJob.class);

    @Resource
    private ISaleCenterClient saleCenterClient;

    /**
     * 关键代理过期任务调度
     *
     * @throws Exception
     */
    @XxlJob("agentTaskJobHandler")
    public void agentTaskJobHandler() throws Exception {
        XxlJobHelper.log("XXL-JOB, AgentTaskJobHandler start...");
        Result<<PERSON><PERSON><PERSON>> booleanResult = saleCenterClient.agentIsKeyExpired();
        XxlJobHelper.log(booleanResult.toString());
    }
}
