package com.get.job.jobhandler;

import com.get.job.service.permission.IAutoUpdateAnnualLeaveService;
import com.get.job.service.permission.IDiseaseVacationUpdateService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 员工假期更新任务
 */
@Component
public class AutoUpdateVacationXxlJob {
    private static final Logger logger = LoggerFactory.getLogger(InstitutionCourseXxlJob.class);

    @Resource
    private IAutoUpdateAnnualLeaveService autoUpdateAnnualLeaveService;

    @Resource
    private IDiseaseVacationUpdateService diseaseVacationUpdateService;

    /**
     * 年假更新定时器
     *
     * @throws Exception
     */
    @XxlJob("autoUpdateAnnualLeave")
    public void autoUpdateAnnualLeave() {
        XxlJobHelper.log("年假自动更新定时器开始");
        try {
            autoUpdateAnnualLeaveService.autoUpdateAnnualLeave();
//			if (getRedis.expire(CacheKeyConstants.ANNUAL_LEAVE_UPDATE_TASK_LOCK_KEY, CacheKeyConstants.REDIS_KEY_EXPIRES_FIVE_MINUTES)) {
//				autoUpdateAnnualLeaveService.autoUpdateAnnualLeave();
//			}
        } catch (Exception e) {
            e.getStackTrace();
//            logger.error("func[{}] e[{}-{}] desc[年假更新定时任务 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
//                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        } finally {
//            Boolean del = getRedis.del(CacheKeyConstants.ANNUAL_LEAVE_UPDATE_TASK_LOCK_KEY);
//            XxlJobHelper.log(del.toString());
        }
    }

    /**
     * 更新登陆日志信息
     */
    @XxlJob("autoUpdateLoginLog")
    public void autoUpdateLoginLog() {
        try {
            diseaseVacationUpdateService.autoUpdateLoginLog();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @XxlJob("sendBirthdayEmail")
    public void sendBirthdayEmail(){
        try {
            diseaseVacationUpdateService.sendBirthdayEmail();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 病假更新定时器
     * 1月1日、4月1日、7月1日、10月1日更新
     *
     * @throws Exception
     */
    @XxlJob("autoUpdateDiseaseVacation")
    public void autoUpdateDiseaseVacation() throws Exception {
        try {
            XxlJobHelper.log("病假自动更新定时器开始");
            return;
//            diseaseVacationUpdateService.autoUpdateDiseaseVacation();


//            if (redisClient.getRedisLock(CacheKeyConstants.DISEASE_VACATION_UPDATE_TASK_LOCK_KEY, CacheKeyConstants.REDIS_KEY_EXPIRES_FIVE_MINUTES)) {
//                diseaseVacationUpdateService.autoUpdateDiseaseVacation();
//            }
        } catch (Exception e) {
            e.getStackTrace();
//            logger.error("func[{}] e[{}-{}] desc[年假更新定时任务 error]", Thread.currentThread().getStackTrace()[1].getMethodName(),
//                    e.getMessage(), Arrays.deepToString(e.getStackTrace()));
        }
//        finally {
//            getRedis.del(CacheKeyConstants.DISEASE_VACATION_UPDATE_TASK_LOCK_KEY);
//        }
    }
}
