package com.get.job.service.permission.impl;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.core.tool.utils.GeneralTool;
import com.get.job.dao.permission.StaffMapper;
import com.get.job.dto.StaffContractReminderDto;
import com.get.job.service.permission.IStaffContractExpireService;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.entity.RemindTemplate;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.remindercenter.dto.RemindTaskDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2024/4/3 10:28
 * @verison: 1.0
 * @description:
 */
@Service
public class StaffContractExpireServiceImpl implements IStaffContractExpireService {

    @Resource
    private StaffMapper staffMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IReminderCenterClient reminderCenterClient;


    //发送员工合同到期提醒
    @Override
    public Boolean sendStaffContractExpireReminder() {
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_CONTRACT_EXPIRATION.key, 1).getData();

        RemindTemplate template = reminderCenterClient.getRemindTemplateByTypeKey(ProjectKeyEnum.STAFF_CONTRACT_EXPIRE.key).getData();
        if (GeneralTool.isEmpty(template)){
            return true;
        }

        for (Map.Entry<Long, String> entry : companyConfigMap.entrySet()) {
            Long companyId = entry.getKey();
            Long day = Long.valueOf(entry.getValue());
            if (day <= 0) {
                continue;
            }
            List<StaffContractReminderDto> staffs = staffMapper.getStaffContractExpireReminderToday(new Date(), day, companyId);
            if (GeneralTool.isEmpty(staffs)){
                continue;
            }
            for (StaffContractReminderDto staff : staffs) {
                List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
                RemindTaskDto remindTaskDto = new RemindTaskDto();
                //remindTaskDto.setFkStaffId(1L);
                remindTaskDto.setFkStaffId(staff.getFkStaffId());
                remindTaskDto.setFkRemindEventTypeKey(ProjectKeyEnum.STAFF_CONTRACT_EXPIRE.key);
                Date endTime = staff.getEndTime();
                remindTaskDto.setStartTime(new Date());
                remindTaskDto.setAdvanceDays("0");
                remindTaskDto.setTaskBgColor("#3788d8");
                remindTaskDto.setRemindMethod("1");
                remindTaskDto.setStatus(1);
                remindTaskDto.setTaskTitle("【"+staff.getStaffFullName()+"】员工到期合同提醒，合同到期日："+new SimpleDateFormat("yyyy-MM-dd").format(endTime));
                remindTaskDto.setFkTableName(TableEnum.PERMISSION_CONTRACT.key);
                remindTaskDto.setFkTableId(staff.getId());
                remindTaskDtos.add(remindTaskDto);
                reminderCenterClient.batchAdd(remindTaskDtos);
            }

        }


//        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.REMINDER_EMAIL_CONTRACT_EXPIRATION.key).getData();
//        Long days4Gea = 0L;
//        Long days4Iae = 0L;
//        Long days4Other = 0L;
//        if (GeneralTool.isNotEmpty(configDto)) {
//            String value1 = configDto.getValue1();
//            JSONObject value1JsonObject = JSON.parseObject(value1);
//            days4Gea = value1JsonObject.getLong("GEA");
//            days4Iae = value1JsonObject.getLong("IAE");
//            days4Other = value1JsonObject.getLong("OTHER");
//        }
//        RemindTemplate template = reminderCenterClient.getRemindTemplateByTypeKey(ProjectKeyEnum.STAFF_CONTRACT_EXPIRE.key).getData();
//        if (GeneralTool.isEmpty(template)){
//            return true;
//        }
//        Properties props = System.getProperties();
//        String profile = props.getProperty("spring.profiles.active");
//        if (profile.equals(AppConstant.IAE_CODE)) {
//            //获取今天需要发送提醒的员工
//            if (days4Iae <= 0L){
//                return true;
//            }
//            List<StaffContractReminderDto> staffs = staffMapper.getStaffContractExpireReminderToday(new Date(),days4Iae);
//            if (GeneralTool.isEmpty(staffs)){
//                return true;
//            }
//            staffs = staffs.stream().filter(staff -> staff.getFkCompanyId() == 3L).collect(Collectors.toList());
//            if (GeneralTool.isEmpty(staffs)){
//                return true;
//            }
//            for (StaffContractReminderDto staff : staffs) {
//                List<RemindTaskDto> remindTaskVos = new ArrayList<>();
//                RemindTaskDto remindTaskVo = new RemindTaskDto();
//                remindTaskVo.setFkStaffId(1L);
//                remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.STAFF_CONTRACT_EXPIRE.key);
//                Date endTime = staff.getEndTime();
//                remindTaskVo.setStartTime(new Date());
//                remindTaskVo.setAdvanceDays("0");
//                remindTaskVo.setTaskBgColor("#3788d8");
//                remindTaskVo.setRemindMethod("1");
//                remindTaskVo.setStatus(1);
//                remindTaskVo.setTaskTitle("【"+staff.getStaffFullName()+"】员工到期合同提醒，合同到期日："+new SimpleDateFormat("yyyy-MM-dd").format(endTime));
//                remindTaskVo.setFkTableName(TableEnum.PERMISSION_CONTRACT.key);
//                remindTaskVo.setFkTableId(staff.getId());
//                remindTaskVos.add(remindTaskVo);
//                reminderCenterClient.batchAdd(remindTaskVos);
//            }
//        }else {
//            if (days4Gea > 0L){
//                List<StaffContractReminderDto> staffs = staffMapper.getStaffContractExpireReminderToday(new Date(),days4Gea);
//                if (GeneralTool.isEmpty(staffs)){
//                    return true;
//                }
//                staffs = staffs.stream().filter(staff -> staff.getFkCompanyId() == 2L).collect(Collectors.toList());
//                if (GeneralTool.isEmpty(staffs)){
//                    return true;
//                }
//                for (StaffContractReminderDto staff : staffs) {
//                    List<RemindTaskDto> remindTaskVos = new ArrayList<>();
//                    RemindTaskDto remindTaskVo = new RemindTaskDto();
//                    remindTaskVo.setFkStaffId(1L);
//                    remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.STAFF_CONTRACT_EXPIRE.key);
//                    Date endTime = staff.getEndTime();
//                    remindTaskVo.setStartTime(new Date());
//                    remindTaskVo.setAdvanceDays("0");
//                    remindTaskVo.setTaskBgColor("#3788d8");
//                    remindTaskVo.setRemindMethod("1");
//                    remindTaskVo.setStatus(1);
//                    remindTaskVo.setTaskTitle("【"+staff.getStaffFullName()+"】员工到期合同提醒，合同到期日："+new SimpleDateFormat("yyyy-MM-dd").format(endTime));
//                    remindTaskVo.setFkTableName(TableEnum.PERMISSION_CONTRACT.key);
//                    remindTaskVo.setFkTableId(staff.getId());
//                    remindTaskVos.add(remindTaskVo);
//                    reminderCenterClient.batchAdd(remindTaskVos);
//                }
//            }
//            if (days4Other > 0L){
//                    List<StaffContractReminderDto> staffs = staffMapper.getStaffContractExpireReminderToday(new Date(),days4Other);
//                    if (GeneralTool.isEmpty(staffs)){
//                        return true;
//                    }
//                    staffs = staffs.stream().filter(staff -> staff.getFkCompanyId() != 2L&&staff.getFkCompanyId() != 3L).collect(Collectors.toList());
//                    if (GeneralTool.isEmpty(staffs)){
//                        return true;
//                    }
//                    for (StaffContractReminderDto staff : staffs) {
//                        List<RemindTaskDto> remindTaskVos = new ArrayList<>();
//                        RemindTaskDto remindTaskVo = new RemindTaskDto();
//                        remindTaskVo.setFkStaffId(1L);
//                        remindTaskVo.setFkRemindEventTypeKey(ProjectKeyEnum.STAFF_CONTRACT_EXPIRE.key);
//                        Date endTime = staff.getEndTime();
//                        remindTaskVo.setStartTime(new Date());
//                        remindTaskVo.setAdvanceDays("0");
//                        remindTaskVo.setTaskBgColor("#3788d8");
//                        remindTaskVo.setRemindMethod("1");
//                        remindTaskVo.setStatus(1);
//                        remindTaskVo.setTaskTitle("【"+staff.getStaffFullName()+"】员工到期合同提醒，合同到期日："+new SimpleDateFormat("yyyy-MM-dd").format(endTime));
//                        remindTaskVo.setFkTableName(TableEnum.PERMISSION_CONTRACT.key);
//                        remindTaskVo.setFkTableId(staff.getId());
//                        remindTaskVos.add(remindTaskVo);
//                        reminderCenterClient.batchAdd(remindTaskVos);
//                    }
//                }
//            }
        return true;
    }





}
