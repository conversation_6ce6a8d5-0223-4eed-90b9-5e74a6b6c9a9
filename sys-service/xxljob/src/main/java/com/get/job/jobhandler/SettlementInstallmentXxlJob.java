//package com.get.job.jobhandler;
//
//import com.get.job.component.SettlementInstallmentHelper;
//import com.get.permissioncenter.feign.IPermissionCenterClient;
//import com.xxl.job.core.context.XxlJobHelper;
//import com.xxl.job.core.handler.annotation.XxlJob;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//
///**
// * 佣金结算定时任务
// *
// * <AUTHOR>
// * @date 2022/4/29 22:15
// */
//@Component
//public class SettlementInstallmentXxlJob {
//
//    private static final Logger logger = LoggerFactory.getLogger(SettlementInstallmentXxlJob.class);
//    @Resource
//    private SettlementInstallmentHelper settlementInstallmentHelper;
//
//
//    @XxlJob("SettlementInstallmentXxlJobHandler")
//    public void settlementInstallmentXxlJobHandler() {
//        try {
//            settlementInstallmentHelper.insertSettlementInstallment();
//        } catch (Exception e) {
//            XxlJobHelper.log("佣金结算定时任务-------------------------", e);
//        }
//    }
//}
