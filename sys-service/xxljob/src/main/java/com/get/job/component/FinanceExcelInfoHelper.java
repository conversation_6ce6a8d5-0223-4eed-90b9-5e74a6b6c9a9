package com.get.job.component;


import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.get.common.utils.GetDateUtil;
import com.get.financecenter.entity.CurrencyType;
import com.get.job.dao.finance.CurrencyTypeMapper;
import com.get.job.dao.finance.FinanceExcelInfoMapper;
import com.get.job.entity.FinanceExcelInfo;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 财务Excel信息导入帮助类
 *
 * <AUTHOR>
 * @date 2021/12/10 11:33
 */
@Component
public class FinanceExcelInfoHelper {

    @Resource
    private FinanceExcelInfoMapper financeExcelInfoMapper;
    @Resource
    private CurrencyTypeMapper currencyTypeMapper;
    @Resource
    private FinanceExcelInfoAsync financeExcelInfoAsync;


    /**
     * 财务excel信息导入
     *
     * @Date 11:47 2021/12/10
     * <AUTHOR>
     */
//    @DSTransactional
//    public void financeExcelInfoSynchronization() {
//        //List<CurrencyType> currencyTypes = currencyTypeMapper.selectAll();
//        LambdaQueryWrapper<CurrencyType> lambdaQueryWrapper = new LambdaQueryWrapper();
//        List<CurrencyType> currencyTypes = currencyTypeMapper.selectList(lambdaQueryWrapper);
//        //币种编号 - 币种id
//        Map<String, Long> currencyMap = new HashMap<>();
//        for (CurrencyType currencyType : currencyTypes) {
//            currencyMap.put(currencyType.getNum(), currencyType.getId());
//        }
//        String optKye = GetDateUtil.getCurrentTime("yyyy-MM-dd HH:mm:ss");
//
////        Example example = new Example(FinanceExcelInfo.class);
////        example.createCriteria().andEqualTo("num", "E202012011509128569");
////        List<FinanceExcelInfo> financeExcelInfos = financeExcelInfoMapper.selectByExample(example);
////        example.orderBy("num").desc();
////        List<FinanceExcelInfo> financeExcelInfos = financeExcelInfoMapper.selectByExample(example);
//        LambdaQueryWrapper<FinanceExcelInfo> queryWrapper = new LambdaQueryWrapper();
//        queryWrapper.orderByDesc(FinanceExcelInfo::getNum);
//        List<FinanceExcelInfo> financeExcelInfos = financeExcelInfoMapper.selectList(queryWrapper);
//        for (FinanceExcelInfo financeExcelInfo : financeExcelInfos) {
//            financeExcelInfoAsync.updateFinancelExcelInfo(currencyMap, financeExcelInfo, optKye);
//        }
//
//    }


}
