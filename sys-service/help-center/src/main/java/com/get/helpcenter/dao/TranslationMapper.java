package com.get.helpcenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.helpcenter.vo.TranslationVo;
import com.get.helpcenter.entity.HelpTranslation;
import com.get.helpcenter.dto.TranslationDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface TranslationMapper extends BaseMapper<HelpTranslation> {
    int insert(HelpTranslation record);

    int insertSelective(HelpTranslation record);

    String getTranslation(TranslationDto translationDto);

    List<TranslationVo> getTranslationInfo(TranslationDto translationDto);
}