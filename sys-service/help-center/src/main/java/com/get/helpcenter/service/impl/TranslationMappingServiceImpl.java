package com.get.helpcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.helpcenter.dao.HelpMapper;
import com.get.helpcenter.dao.HelpTypeMapper;
import com.get.helpcenter.dao.TranslationMapper;
import com.get.helpcenter.dao.TranslationMappingMapper;
import com.get.helpcenter.vo.TranslationMappingVo;
import com.get.helpcenter.entity.Help;
import com.get.helpcenter.entity.HelpTranslation;
import com.get.helpcenter.entity.HelpTranslationMapping;
import com.get.helpcenter.entity.HelpType;
import com.get.helpcenter.service.ITranslationMappingService;
import com.get.helpcenter.dto.TranslationDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

;

/**
 * @author: Hardy
 * @create: 2021/5/11 15:37
 * @verison: 1.0
 * @description:
 */
@Service
public class TranslationMappingServiceImpl implements ITranslationMappingService {
    @Resource
    private TranslationMappingMapper translationMappingMapper;
    @Resource
    private TranslationMapper translationMapper;
    @Resource
    private HelpMapper helpMapper;
    @Resource
    private HelpTypeMapper helpTypeMapper;
    @Resource
    private UtilService utilService;

    @Override
    public List<TranslationMappingVo> getTranslationMappingDtos(TranslationDto translationDto) {
        if (GeneralTool.isEmpty(translationDto.getType()) || GeneralTool.isEmpty(translationDto.getLanguageCode())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
//        Example example = new Example(TranslationMapping.class);
//        Example.Criteria criteria = example.createCriteria();
//        if(GeneralTool.isNotEmpty(translationDto)){
//            if(GeneralTool.isNotEmpty(translationDto.getFkTableName())){
//                criteria.andEqualTo("fkTableName",translationDto.getFkTableName());
//            }
//        }
//        Map<String,Object> map = fromJavaBean(translationDto.getFkTableName(),translationDto.getFkTableId());
//        List<TranslationMapping> translationMappings = translationMappingMapper.selectByExample(example);

        LambdaQueryWrapper<HelpTranslationMapping> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(translationDto)) {
            if (GeneralTool.isNotEmpty(translationDto.getFkTableName())) {
                lambdaQueryWrapper.eq(HelpTranslationMapping::getFkTableName, translationDto.getFkTableName());
            }
        }
        Map<String, Object> map = fromJavaBean(translationDto.getFkTableName(), translationDto.getFkTableId());
        List<HelpTranslationMapping> translationMappings = translationMappingMapper.selectList(lambdaQueryWrapper);
        List<TranslationMappingVo> translationMappingVos = translationMappings.stream().map(TranslationMapping -> BeanCopyUtils.objClone(TranslationMapping, TranslationMappingVo::new)).collect(Collectors.toList());
        if (translationDto.getType() == 1 && translationDto.getLanguageCode().equals(ProjectKeyEnum.ZH_CN.key)) {
            for (TranslationMappingVo translationMappingVo : translationMappingVos) {
                for (String key : map.keySet()) {
                    if (key.equals(translationMappingVo.getFkColumnName())) {
                        /*translationMappingVo.setStandardContent(map.get(key).toString());
                        translationDto.setFkTranslationMappingId(translationMappingVo.getId());
                        translationMappingVo.setTranslationContent(translationMapper.getTranslation(translationDto));*/
                        translationDto.setFkTranslationMappingId(translationMappingVo.getId());
                        if (GeneralTool.isNotEmpty(map.get(key))) {
                            translationMappingVo.setTranslationContent(map.get(key).toString());
                        }
                    }
                }
            }
        } else {
            for (TranslationMappingVo translationMappingVo : translationMappingVos) {
                for (String key : map.keySet()) {
                    if (key.equals(translationMappingVo.getFkColumnName())) {
                        translationDto.setFkTranslationMappingId(translationMappingVo.getId());
                        translationMappingVo.setTranslationContent(translationMapper.getTranslation(translationDto));
                    }
                }
            }
        }

        return translationMappingVos;
    }

    @Override
    public void updateTranslations(List<TranslationDto> translationDtos) {
        if (GeneralTool.isEmpty(translationDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (translationDtos.get(0).getLanguageCode().equals(ProjectKeyEnum.ZH_CN.key)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("Simplified_Chinese_not_translation"));
        }
        for (TranslationDto translationDto : translationDtos) {
//            Example example = new Example(Translation.class);
//            Example.Criteria criteria = example.createCriteria();
//            if(GeneralTool.isNotEmpty(translationDto)){
//                if(GeneralTool.isNotEmpty(translationDto.getFkTableName())){
//                    criteria.andEqualTo("fkTableName",translationDto.getFkTableName());
//                }
//                if(GeneralTool.isNotEmpty(translationDto.getFkTableId())){
//                    criteria.andEqualTo("fkTableId",translationDto.getFkTableId());
//                }
//                if(GeneralTool.isNotEmpty(translationDto.getLanguageCode())){
//                    criteria.andEqualTo("languageCode",translationDto.getLanguageCode());
//                }
//                if(GeneralTool.isNotEmpty(translationDto.getFkTranslationMappingId())){
//                    criteria.andEqualTo("fkTranslationMappingId",translationDto.getFkTranslationMappingId());
//                }
//            }
//            int i = translationMapper.deleteByExample(example);
            LambdaQueryWrapper<HelpTranslation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            if (GeneralTool.isNotEmpty(translationDto)) {
                if (GeneralTool.isNotEmpty(translationDto.getFkTableName())) {
                    lambdaQueryWrapper.eq(HelpTranslation::getFkTableName, translationDto.getFkTableName());
                }
                if (GeneralTool.isNotEmpty(translationDto.getFkTableId())) {
                    lambdaQueryWrapper.eq(HelpTranslation::getFkTableId, translationDto.getFkTableId());
                }
                if (GeneralTool.isNotEmpty(translationDto.getLanguageCode())) {
                    lambdaQueryWrapper.eq(HelpTranslation::getLanguageCode, translationDto.getLanguageCode());
                }
                if (GeneralTool.isNotEmpty(translationDto.getFkTranslationMappingId())) {
                    lambdaQueryWrapper.eq(HelpTranslation::getFkTranslationMappingId, translationDto.getFkTranslationMappingId());
                }
            }
            int i = translationMapper.delete(lambdaQueryWrapper);
            if (i < 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
            }
            HelpTranslation translation = BeanCopyUtils.objClone(translationDto, HelpTranslation::new);
            utilService.updateUserInfoToEntity(translation);
            translationMapper.insert(translation);
        }
    }

    /**
     * 删除翻译内容
     *
     * @Date 12:11 2021/8/17
     * <AUTHOR>
     */
    @Override
    public void deleteTranslations(String fkTableName, Long fkTableId) {
//        Example example = new Example(Translation.class);
//        example.createCriteria().andEqualTo("fkTableName", fkTableName).andEqualTo("fkTableId", fkTableId);
//        translationMapper.deleteByExample(example);
        translationMapper.delete(Wrappers.<HelpTranslation>lambdaQuery().eq(HelpTranslation::getFkTableName, fkTableName).eq(HelpTranslation::getFkTableId, fkTableId));
    }

//    @Override
//    public List<Map<String, Object>> findLanguageType()  {
//        return ProjectKeyEnum.enums2Arrays(ProjectKeyEnum.LANGUAGE_TYPE);
//    }

    private Map<String, Object> fromJavaBean(String fkTableName, Long fkTableId) {
        if (GeneralTool.isEmpty(fkTableName) || GeneralTool.isEmpty(fkTableId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Map<String, Object> map = null;
        if (fkTableName.equals(TableEnum.HELP.key)) {
            Help help = helpMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(help)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(help);
        } else if (fkTableName.equals(TableEnum.HELP_TYPE.key)) {
            HelpType helpType = helpTypeMapper.selectById(fkTableId);
            if (GeneralTool.isEmpty(helpType)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
            }
            map = BeanCopyUtils.setConditionMap(helpType);
        }
        return map;
    }
}
