package com.get.helpcenter.service.impl;

import com.get.core.translation.baidu.api.TransApi;
import com.get.core.translation.baidu.result.TransResult;
import com.get.core.translation.baidu.result.TransVo;
import com.get.helpcenter.service.ITranslationService;
import org.springframework.stereotype.Service;

/**
 * @author: Hardy
 * @create: 2021/5/11 15:36
 * @verison: 1.0
 * @description:
 */
@Service
public class TranslationServiceImpl implements ITranslationService {
    @Override
    public TransResult getTranslation(TransVo transVo) throws Exception {
        TransResult transResult = new TransResult();
        if ("en_us".equals(transVo.getTo())) {
            if ("zh_hk".equals(transVo.getFrom())) {
                transResult = TransApi.getTransResultFromHk2En(transVo.getQuery());
            } else {
                transResult = TransApi.getTransResult(transVo.getQuery(), TransApi.AUTO, TransApi.EN_US);
            }
        } else if ("zh_cn".equals(transVo.getTo())) {
            if ("zh_hk".equals(transVo.getFrom())) {
                transResult = TransApi.getTransResultFromHk2Cn(transVo.getQuery());
            } else {
                transResult = TransApi.getTransResult(transVo.getQuery(), TransApi.AUTO, TransApi.ZH_CN);
            }
        } else if ("zh_hk".equals(transVo.getTo()) || "zh_tw".equals(transVo.getTo())) {
            transResult = TransApi.getTransResult(transVo.getQuery(), TransApi.AUTO, TransApi.ZH_HK);
        }

        return transResult;
    }
}
