package com.get.helpcenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.helpcenter.vo.HelpVo;
import com.get.helpcenter.vo.HelpInfoVo;
import com.get.helpcenter.entity.Help;
import com.get.helpcenter.entity.ParentHelp;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface HelpMapper extends BaseMapper<Help> {
    int insertSelective(Help record);

    Integer getMaxViewOrder();

    Long getAllChilHelpId(@Param("fkParentHelpId") Long fkParentHelpId);

    void updateByFkParentId(@Param("fkParentHelpId") Long fkParentHelpId, @Param("fkHelpId") Long fkHelpId);

    /**
     * 关联问题
     *
     * @param fkHelpId
     * @return
     */
    List<Long> getRelationHelpIds(@Param("fkHelpId") Long fkHelpId);

    Help getHelpById(@Param("fkHelpId") Long fkHelpId);

    List<HelpVo> getHelpSelect();

    List<HelpVo> getHelpByHelpTypeId(@Param("fkHelpTypeId") Long fkHelpTypeId);

    Long getRelationHelp(@Param("fkHelpId") Long fkHelpId, @Param("parentId") Long parentId);

    void insertParents(ParentHelp parentHelp);

    void deleteParentIds(@Param("fkHelpId") Long fkHelpId);

    List<Long> getHelpByParentId(@Param("fkHelpId") Long fkHelpId);

    List<HelpInfoVo> getHelpInfo();
}