package com.get.helpcenter.endpoint;

import com.alibaba.cloud.nacos.NacosDiscoveryProperties;
import com.alibaba.nacos.api.exception.NacosException;
import com.alibaba.nacos.api.naming.NamingService;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.endpoint.annotation.Endpoint;
import org.springframework.boot.actuate.endpoint.annotation.ReadOperation;
import org.springframework.stereotype.Component;


@Component
@Endpoint(id = "deregister")
@Log4j2
public class DeregisterEndpoint {

    @Autowired
    private final NamingService namingService;

    @Autowired
    private NacosDiscoveryProperties nacosDiscoveryProperties;


    @Autowired
    public DeregisterEndpoint(NamingService namingService, NacosDiscoveryProperties nacosDiscoveryProperties) {
        this.namingService = namingService;
        this.nacosDiscoveryProperties = nacosDiscoveryProperties;
    }


    /**
     * 从 nacos 主动下线
     *
     * @param
     * <AUTHOR>
     */
    @ReadOperation
    public String endpoint() {

        try {
            String serviceName = nacosDiscoveryProperties.getService();
            String groupName = nacosDiscoveryProperties.getGroup();
            String clusterName = nacosDiscoveryProperties.getClusterName();
            String ip = nacosDiscoveryProperties.getIp();
            int port = nacosDiscoveryProperties.getPort();

            log.info("deregister from nacos, serviceName:{}, groupName:{}, clusterName:{}, ip:{}, port:{}", serviceName, groupName, clusterName, ip, port);

            namingService.deregisterInstance(serviceName, groupName, ip, port);
        } catch (NacosException e) {
            throw new RuntimeException(e);
        }

        return "Discover Success";
    }

}
