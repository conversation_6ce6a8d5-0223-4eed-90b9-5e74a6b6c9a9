package com.get.helpcenter.service;

import com.get.helpcenter.vo.TranslationMappingVo;
import com.get.helpcenter.dto.TranslationDto;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/5/11 15:36
 * @verison: 1.0
 * @description:
 */
public interface ITranslationMappingService {
    List<TranslationMappingVo> getTranslationMappingDtos(TranslationDto translationDto);

    void updateTranslations(List<TranslationDto> translationDtos);

    /**
     * 删除翻译内容
     *
     * @Date 12:09 2021/8/17
     * <AUTHOR>
     */
    void deleteTranslations(String key, Long id);


    //List<Map<String, Object>> findLanguageType()  ;
}
