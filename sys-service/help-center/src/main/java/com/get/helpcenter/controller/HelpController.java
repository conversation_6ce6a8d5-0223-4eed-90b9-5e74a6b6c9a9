package com.get.helpcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.UpdateResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.helpcenter.vo.HelpVo;
import com.get.helpcenter.vo.HelpInfoVo;
import com.get.helpcenter.vo.HelpTypeVo;
import com.get.helpcenter.service.IHelpService;
import com.get.helpcenter.dto.ExchangeHelpDto;
import com.get.helpcenter.dto.HelpDto;
import com.get.helpcenter.dto.query.HelpQueryDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Hardy
 * @create: 2021/5/11 14:56
 * @verison: 1.0
 * @description:
 */
@Api(tags = "帮助信息管理")
@RestController
@RequestMapping("/help/help")
@Slf4j
public class HelpController {

    @Autowired
    private IHelpService helpService;

    /**
     * 树形结构数据
     *
     * @param helpVo
     * @return
     * @
     */
    @ApiOperation(value = "树形结构数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.LIST, description = "帮助中心/帮助信息管理")
    @PostMapping("dataThree")
    @VerifyPermission(IsVerify = false)
    public ListResponseBo<HelpVo> dataTree(@RequestBody HelpQueryDto helpVo) {
        List<HelpVo> datas = helpService.getHelpTree(helpVo);
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "获取帮助中心信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.LIST, description = "帮助中心/帮助类型管理/获取帮助中心信息")
    @PostMapping("getHelpInfo")
    @VerifyLogin(IsVerify = false)
    public ListResponseBo<HelpInfoVo> getHelpInfo() {
        List<HelpInfoVo> datas = helpService.getHelpInfo();
        return new ListResponseBo<>(datas);
    }

    @ApiOperation(value = "帮助问题拖拽", notes = "")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.LIST, description = "帮助中心/帮助信息管理")
    @PostMapping("exchangeHelp")
    public ResponseBo exchangeHelp(@RequestBody @Validated ExchangeHelpDto exchangeHelpDto) {
        helpService.exchangeHelp(exchangeHelpDto);
        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [helpDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.ADD, description = "帮助中心/帮助信息管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(HelpDto.Add.class) HelpDto helpDto) {
        return SaveResponseBo.ok(helpService.addHelp(helpDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [helpDto]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.EDIT, description = "帮助中心/帮助信息管理/修改")
    @PostMapping("update")
    public ResponseBo<HelpTypeVo> update(@RequestBody @Validated(HelpDto.Update.class) HelpDto helpDto) {
        return UpdateResponseBo.ok(helpService.updateHelpVo(helpDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.DELETE, description = "帮助中心/帮助信息管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        helpService.deleteHelp(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.helpcenter.vo.HelpVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "帮助详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.DETAIL, description = "帮助中心/帮助信息管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<HelpVo> detail(@PathVariable("id") Long id) {
        HelpVo data = helpService.findHelpById(id);
        return new ResponseBo<>(data);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 上移下移
     * @Param [helpDtoList]
     * <AUTHOR>
     */
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.EDIT, description = "帮助中心/帮助信息管理/排序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<HelpDto> helpDtoList) {
        helpService.movingOrder(helpDtoList);
        return ResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "帮助问题下拉框", notes = "")
    @PostMapping("getHelpTypeSelect")
    public ResponseBo<HelpVo> getHelpSelect() {
        return new ListResponseBo<>(helpService.getHelpSelect());
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :显示类型下拉框
     * @Param
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "显示类型下拉框", notes = "")
    @PostMapping("getShowTypeObjectsSelect")
    public ResponseBo getShowTypeObjectsSelect() {
        List<Map<String, Object>> datas = helpService.getShowTypeObjectsSelect();
        return new ListResponseBo<>(datas);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description: 根据keyCode获取help
     * @Param [helpVoList]
     * <AUTHOR>
     */
    @ApiOperation(value = "根据KeyCode获取帮助信息", notes = "")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.EDIT, description = "帮助中心/帮助信息管理/根据KeyCode获取帮助信息")
    @GetMapping("getHelpByKeyCode/{keyCode}")
    public ResponseBo<HelpVo> getHelpByKeyCode(@PathVariable("keyCode") String keyCode) {
        HelpVo data = helpService.getHelpByKeyCode(keyCode);
        return new ResponseBo<>(data);
    }


    /**
     * feign调用 根据帮助id获取帮助详细
     *
     * @Date `10:45` 2021/9/8
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getHelpDtoByHelpId")
    public List<HelpVo> getHelpDtoByHelpId(@RequestBody Set<Long> helpIds) {
        return helpService.getHelpDtoByHelpId(helpIds);
    }

    /**
     * 树形结构数据(免登录免权限)
     *
     * @param helpVo
     * @return
     * @
     */
    @ApiOperation(value = "树形结构数据(免登录免权限)", notes = "")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.LIST, description = "帮助中心/帮助信息管理/树形结构数据(免登录免权限)")
    @PostMapping("dataThreeNoPermission")
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    public ListResponseBo<HelpVo> dataThreeNoPermission(@RequestBody HelpQueryDto helpVo) {
        List<HelpVo> datas = helpService.getHelpTree(helpVo);
        return new ListResponseBo<>(datas);
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.helpcenter.vo.HelpVo>
     * @Description :详情(免登录免权限)
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "帮助详情接口(免登录免权限)", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.DETAIL, description = "帮助中心/帮助信息管理/详情(免登录免权限)")
    @GetMapping("noPermission/{id}")
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    public ResponseBo<HelpVo> getHelpById(@PathVariable("id") Long id) {
        HelpVo data = helpService.findHelpById(id);
        return new ResponseBo<>(data);
    }
}
