package com.get.helpcenter.service;

import com.get.common.result.SearchBean;
import com.get.helpcenter.vo.HelpTypeVo;
import com.get.helpcenter.vo.tree.HelpTypeTreeVo;
import com.get.helpcenter.dto.HelpTypeDto;

import java.util.List;


/**
 * @author: Hardy
 * @create: 2021/5/11 15:34
 * @verison: 1.0
 * @description:
 */
public interface IHelpTypeService {
    /**
     * @return java.lang.Long
     * @Description :新增帮助类型
     * @Param [helpTypeDto]
     * <AUTHOR>
     */
    Long addHelpType(HelpTypeDto helpTypeDto);

    /**
     * @return void
     * @Description :删除帮助类型
     * @Param [id]
     * <AUTHOR>
     */
    void deleteHelpType(Long id);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :递归查询所有子帮助类型
     * @Param [id]
     * <AUTHOR>
     */
    List<Long> getChildHelpType(Long id);

    /**
     * @return com.get.helpcenter.vo.HelpTypeVo
     * @Description :更新帮助类型
     * @Param [helpTypeDto]
     * <AUTHOR>
     */
    HelpTypeVo updateHelpTypeVo(HelpTypeDto helpTypeDto);

    /**
     * @return com.get.helpcenter.vo.HelpTypeVo
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    HelpTypeVo findHelpTypeById(Long id);

    /**
     * @return void
     * @Description : 排序
     * @Param [helpTypeDtoList]
     * <AUTHOR>
     */
    void movingOrder(List<HelpTypeDto> helpTypeDtoList);

    /**
     * @return com.get.common.result.ResponseBo<com.get.helpcenter.vo.HelpTypeVo>
     * @Description :列表数据
     * @Param [data, page]
     * <AUTHOR>
     */
    List<HelpTypeVo> getHelpTypes(HelpTypeDto data, SearchBean<HelpTypeDto> page);

    /**
     * @return java.util.List<com.get.helpcenter.vo.HelpTypeTreeVo>
     * @Description :获取树形结构图
     * @Param [helpTypeId]
     * <AUTHOR>
     */
    List<HelpTypeTreeVo> getHelpTypeTreeDto(Long helpTypeId);

    /**
     * @return java.util.List<com.get.helpcenter.vo.HelpTypeVo>
     * @Description :帮助类型下拉框
     * @Param
     * <AUTHOR>
     */
    List<HelpTypeVo> getHelpTypeSelect();
}
