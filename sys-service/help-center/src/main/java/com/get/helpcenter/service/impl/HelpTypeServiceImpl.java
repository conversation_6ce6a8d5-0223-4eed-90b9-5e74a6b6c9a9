package com.get.helpcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.helpcenter.dao.HelpMapper;
import com.get.helpcenter.dao.HelpTypeMapper;
import com.get.helpcenter.vo.HelpTypeVo;
import com.get.helpcenter.vo.HelpVo;
import com.get.helpcenter.vo.tree.HelpTypeTreeVo;
import com.get.helpcenter.entity.HelpType;
import com.get.helpcenter.service.IHelpTypeService;
import com.get.helpcenter.service.ITranslationMappingService;
import com.get.helpcenter.dto.HelpTypeDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Hardy
 * @create: 2021/5/11 15:35
 * @verison: 1.0
 * @description:
 */
@Service
public class HelpTypeServiceImpl implements IHelpTypeService {
    @Resource
    private UtilService utilService;
    @Resource
    private HelpTypeMapper helpTypeMapper;
    @Resource
    private HelpMapper helpMapper;
    @Resource
    private ITranslationMappingService translationMappingService;

    /**
     * 新增帮助类型
     *
     * @param helpTypeDto
     * @return Long
     */
    @Override
    public Long addHelpType(HelpTypeDto helpTypeDto) {
        if (GeneralTool.isEmpty(helpTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        //判断新增是否存在
        if (!validateChange(helpTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
        }
        //判断是否是否有父帮助类型  否则设置一个根的父帮助类型 0
        if (GeneralTool.isEmpty(helpTypeDto.getFkParentHelpTypeId())) {
            helpTypeDto.setFkParentHelpTypeId(0L);
        }
        HelpType helpType = BeanCopyUtils.objClone(helpTypeDto, HelpType::new);
        Integer maxViewOrder = helpTypeMapper.getMaxViewOrder();
        helpType.setViewOrder(maxViewOrder);
        utilService.updateUserInfoToEntity(helpType);
        int i = helpTypeMapper.insertSelective(helpType);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
        }
        return helpType.getId();
    }

    /**
     * 删除帮助类型
     *
     * @param id
     * @
     */
    @Override
    public void deleteHelpType(Long id) {
        if (GeneralTool.isEmpty(id) || id == 0L) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        HelpType helpType = helpTypeMapper.selectById(id);
        if (GeneralTool.isEmpty(helpType)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        //判断有无子帮助类型
        if (getChildHelpType(id).size() > 1) {
            //有子帮助类型，不能删除
            throw new GetServiceException(LocaleMessageUtils.getMessage("helpType_has_child_helpType"));
        }
        List<HelpVo> helpByHelpTypeList = helpMapper.getHelpByHelpTypeId(id);
        if (helpByHelpTypeList.size() > 0) {
            //被引用 不能删除
            throw new GetServiceException(LocaleMessageUtils.getMessage("helpType_is_used"));
        }
        //直接删除
        helpTypeMapper.deleteById(id);

        //删除翻译内容
        translationMappingService.deleteTranslations(TableEnum.HELP_TYPE.key, id);
    }

    /**
     * 获取子帮助类型
     *
     * @param id
     * @return List<Long>
     * @
     */
    @Override
    public List<Long> getChildHelpType(Long id) {
        List<Long> ids = new ArrayList<>();
        List<Long> helpTypeIds = new ArrayList<>();
        ids.add(id);
        helpTypeIds.add(id);
        getAllChildHelpType(ids, helpTypeIds);
        return ids;

    }

    /**
     * 更新帮助类型
     *
     * @param helpTypeDto
     * @return
     * @
     */
    @Override
    public HelpTypeVo updateHelpTypeVo(HelpTypeDto helpTypeDto) {
        if (GeneralTool.isEmpty(helpTypeDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(helpTypeDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Long aLong = validateUpdate(helpTypeDto);
        if (!helpTypeDto.getId().equals(aLong)) {
            //类型名称存在
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
        }
        HelpType helpType = BeanCopyUtils.objClone(helpTypeDto, HelpType::new);
        utilService.updateUserInfoToEntity(helpType);
        helpTypeMapper.updateById(helpType);
        return findHelpTypeById(helpTypeDto.getId());
    }

    /**
     * 根据id查询帮助类型
     *
     * @param id
     * @return HelpTypeVo
     * @
     */
    @Override
    public HelpTypeVo findHelpTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        HelpType helpType = helpTypeMapper.selectById(id);
        HelpTypeVo helpTypeVo = BeanCopyUtils.objClone(helpType, HelpTypeVo::new);

        return helpTypeVo;
    }

    /**
     * 上移下移
     *
     * @param helpTypeDtoList
     * @
     */
    @Override
    public void movingOrder(List<HelpTypeDto> helpTypeDtoList) {
        if (GeneralTool.isEmpty(helpTypeDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        HelpType ro = BeanCopyUtils.objClone(helpTypeDtoList.get(0), HelpType::new);
        Integer oneorder = ro.getViewOrder();
        HelpType rt = BeanCopyUtils.objClone(helpTypeDtoList.get(1), HelpType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        helpTypeMapper.updateById(ro);
        helpTypeMapper.updateById(rt);
    }

    /**
     * 获得帮助类型列表
     *
     * @param helpTypeDto
     * @param page
     * @return
     * @
     */
    @Override
    public List<HelpTypeVo> getHelpTypes(HelpTypeDto helpTypeDto, SearchBean<HelpTypeDto> page) {
//        Example example = new Example(HelpType.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(helpTypeDto)) {
//            //查询条件-类型名称
//            if (GeneralTool.isNotEmpty(helpTypeDto.getTypeName())) {
//                criteria.andLike("typeName", "%" + helpTypeDto.getTypeName() + "%");
//            }
//            //查询条件
//            if (GeneralTool.isNotEmpty(helpTypeDto.getFkParentHelpTypeId())) {
//                criteria.andEqualTo("fkParentHelpTypeId", helpTypeDto.getFkParentHelpTypeId());
//            }
//        }
//        example.orderBy("viewOrder").desc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<HelpType> helpTypes = helpTypeMapper.selectByExample(example);
//        page.restPage(helpTypes);
        LambdaQueryWrapper<HelpType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(helpTypeDto)) {
            //查询条件-类型名称
            if (GeneralTool.isNotEmpty(helpTypeDto.getTypeName())) {
                lambdaQueryWrapper.like(HelpType::getTypeName, helpTypeDto.getTypeName());
            }
            //查询条件
            if (GeneralTool.isNotEmpty(helpTypeDto.getFkParentHelpTypeId())) {
                lambdaQueryWrapper.eq(HelpType::getFkParentHelpTypeId, helpTypeDto.getFkParentHelpTypeId());
            }
        }
        lambdaQueryWrapper.orderByDesc(HelpType::getViewOrder);
        IPage<HelpType> pages = helpTypeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<HelpType> helpTypes = pages.getRecords();
        page.setAll((int) pages.getTotal());

        List<HelpTypeVo> helpTypeVoList = helpTypes.stream().map(helpType -> BeanCopyUtils.objClone(helpType, HelpTypeVo::new)).collect(Collectors.toList());
        for (HelpTypeVo helpTypeVo : helpTypeVoList) {
            helpTypeVo.setFkTableName(TableEnum.HELP_TYPE.key);
        }
        return helpTypeVoList;
    }

    /**
     * 获取树形结构
     *
     * @param helpTypeId
     * @return List<HelpTypeTreeVo>
     * @
     */
    @Override
    public List<HelpTypeTreeVo> getHelpTypeTreeDto(Long helpTypeId) {
        return getTreeList(getAllHelpTypeDto(helpTypeId));
    }

    /**
     * 获取帮助类型下拉
     *
     * @return List<HelpTypeVo>
     */
    @Override
    public List<HelpTypeVo> getHelpTypeSelect() {
        return helpTypeMapper.getHelpTypeSelect();
    }

    /**
     * 获取树形列表
     *
     * @param entityList
     * @return List<HelpTypeTreeVo>
     * @
     */
    private List<HelpTypeTreeVo> getTreeList(List<HelpTypeTreeVo> entityList) {
        if (GeneralTool.isEmpty(entityList)) {
            return null;
        }
        List<HelpTypeTreeVo> resultList = new ArrayList<>();
        // 获取顶层元素集合
        String parentId;
        for (HelpTypeTreeVo entity : entityList) {
            parentId = String.valueOf(entity.getFkParentHelpTypeId());
            //最顶级的帮助类型节点
            if (parentId == null || "0".equals(parentId)) {
                resultList.add(entity);
            }
        }
        //假如没有父节点，就是说 所有类型平级
        if (GeneralTool.isEmpty(resultList)) {
            //获取最小节点，就是最小的帮助类型id 当成最大的帮助类型
            HelpTypeTreeVo minTreeNode = entityList.stream().min(Comparator.comparing(HelpTypeTreeVo::getFkParentHelpTypeId)).get();
            resultList.add(minTreeNode);
            if (GeneralTool.isNotEmpty(minTreeNode)) {
                //获取相同的最小节点，把平级的帮助类型都放进去List.然后分别获取各个平级帮助类型的子帮助类型
                List<HelpTypeTreeVo> minTreeNodes = entityList.stream().filter(treeDto ->
                        treeDto.getFkParentHelpTypeId().equals(minTreeNode.getFkParentHelpTypeId()) &&
                                !treeDto.getId().equals(minTreeNode.getId())).distinct().collect(Collectors.toList());
                resultList.addAll(minTreeNodes);
            }
        }
        // 获取每个顶层元素的子数据集合
        for (HelpTypeTreeVo entity : resultList) {
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        return resultList;
    }

    /**
     * 获取子树
     *
     * @param id
     * @param entityList
     * @return List<HelpTypeTreeVo>
     * @
     */
    private List<HelpTypeTreeVo> getSubList(Long id, List<HelpTypeTreeVo> entityList) {
        //entityList最顶层帮助类型list  id 父id  entityList 1，2，3
        List<HelpTypeTreeVo> childList = new ArrayList<>();
        Long parentId;
        // 子集的直接子对象
        for (HelpTypeTreeVo entity : entityList) {//查子对象
            parentId = entity.getFkParentHelpTypeId();
            if (id.longValue() == parentId.longValue()) {
                //获取子节点的帮助类型信息
                childList.add(entity);
            }
        }
        // 子集的间接子对象
        for (HelpTypeTreeVo entity : childList) {
            //递归调用
            entity.setChildList(getSubList(entity.getId(), entityList));
        }
        // 递归退出条件
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }

    /**
     * 获取帮助类型dto
     *
     * @param helpTypeId
     * @return
     * @
     */
    private List<HelpTypeTreeVo> getAllHelpTypeDto(Long helpTypeId) {
        List<HelpTypeVo> helpTypes = getAllHelpTypes(helpTypeId);
        List<HelpTypeTreeVo> collect =
                helpTypes.stream().map(helpTypeDto -> BeanCopyUtils.objClone(helpTypeDto, HelpTypeTreeVo::new)).collect(Collectors.toList());
        return collect;
    }

    /**
     * 获取所有帮助类型
     *
     * @param helpTypeId
     * @return
     * @
     */
    private List<HelpTypeVo> getAllHelpTypes(Long helpTypeId) {
//        Example example = new Example(HelpType.class);
//        Example.Criteria criteria = example.createCriteria();
//        //获取帮助类型
//        if (GeneralTool.isEmpty(helpTypeId)) {
//            //helpTypeId为空则使用根id 0L
//            List<Long> helpTypeIds = getHelpTypeIds(0L);
//            criteria.andIn("id", helpTypeIds);
//        } else {
//            List<Long> helpTypeIds = getHelpTypeIds(helpTypeId);//
//            criteria.andIn("id", helpTypeIds);//
//            //criteria.andEqualTo("id", helpTypeId);
//        }
//        example.orderBy("viewOrder").desc();
//        List<HelpType> helpTypes = helpTypeMapper.selectByExample(example);
        LambdaQueryWrapper<HelpType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        //获取帮助类型
        if (GeneralTool.isEmpty(helpTypeId)) {
            //helpTypeId为空则使用根id 0L
            List<Long> helpTypeIds = getHelpTypeIds(0L);
            lambdaQueryWrapper.in(HelpType::getId, helpTypeIds);
        } else {
            List<Long> helpTypeIds = getHelpTypeIds(helpTypeId);//
            lambdaQueryWrapper.in(HelpType::getId, helpTypeIds);//
            //criteria.andEqualTo("id", helpTypeId);
        }
        lambdaQueryWrapper.orderByDesc(HelpType::getViewOrder);
        List<HelpType> helpTypes = helpTypeMapper.selectList(lambdaQueryWrapper);
        return helpTypes.stream().map(helpType -> BeanCopyUtils.objClone(helpType, HelpTypeVo::new)).collect(Collectors.toList());
    }

    /**
     * 查询当前和子类型ids
     *
     * @param helpTypeId
     * @return
     */
    private List<Long> getHelpTypeIds(Long helpTypeId) {
        //查询当前id和子类型id
//        Example example = new Example(HelpType.class);
//        //Example.Criteria criteria1 = example.createCriteria();
//        Example.Criteria criteria2 = example.createCriteria();
//        //criteria1.andEqualTo("fkParentHelpTypeId",helpTypeId);
//        if (helpTypeId != 0L) {
//            criteria2.andEqualTo("id", helpTypeId);
//            criteria2.orEqualTo("fkParentHelpTypeId",helpTypeId);
//        }
//        example.orderBy("viewOrder").desc();
//        List<HelpType> helpTypes = helpTypeMapper.selectByExample(example);

        LambdaQueryWrapper<HelpType> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (helpTypeId != 0L) {
            lambdaQueryWrapper.eq(HelpType::getId, helpTypeId);
            lambdaQueryWrapper.or();
            lambdaQueryWrapper.eq(HelpType::getFkParentHelpTypeId, helpTypeId);
        }
        lambdaQueryWrapper.orderByDesc(HelpType::getViewOrder);
        List<HelpType> helpTypes = helpTypeMapper.selectList(lambdaQueryWrapper);
        List<Long> helpTypeIds = helpTypes.stream().map(helpType -> helpType.getId()).collect(Collectors.toList());
        helpTypeIds.add(helpTypeId);
        return helpTypeIds;
    }

    /**
     * 判断新增是否存在
     *
     * @param helpTypeDto
     * @return
     */
    private boolean validateChange(HelpTypeDto helpTypeDto) {
//        Example example = new Example(HelpType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", helpTypeDto.getTypeName());
//        List<HelpType> helpTypes = helpTypeMapper.selectByExample(example);

        List<HelpType> helpTypes = helpTypeMapper.selectList(Wrappers.<HelpType>lambdaQuery().eq(HelpType::getTypeName, helpTypeDto.getTypeName()));
        return GeneralTool.isEmpty(helpTypes);
    }

    /**
     * 编辑校验是否存在
     *
     * @param helpTypeDto
     * @return Long
     */
    private Long validateUpdate(HelpTypeDto helpTypeDto) {
//        Example example = new Example(HelpType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", helpTypeDto.getTypeName());
//        List<HelpType> helpTypes = helpTypeMapper.selectByExample(example);
        List<HelpType> helpTypes = helpTypeMapper.selectList(Wrappers.<HelpType>lambdaQuery().eq(HelpType::getTypeName, helpTypeDto.getTypeName()));
        return GeneralTool.isNotEmpty(helpTypes) ? helpTypes.get(0).getId() : helpTypeDto.getId();
    }

    /**
     * 获取所有子帮助类型
     *
     * @param ids
     * @param helpTypeIds
     */
    private void getAllChildHelpType(List<Long> ids, List<Long> helpTypeIds) {
        helpTypeIds = helpTypeMapper.getAllChildHelpTypeId(helpTypeIds);
        if (GeneralTool.isNotEmpty(helpTypeIds)) {
            if (Collections.disjoint(ids, helpTypeIds)) {
                ids.addAll(helpTypeIds);
                getAllChildHelpType(ids, helpTypeIds);
            }
        }
    }
}
