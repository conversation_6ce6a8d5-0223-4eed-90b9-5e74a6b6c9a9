package com.get.helpcenter.utils;

import java.security.SecureRandom;

/**
 * @author: <PERSON>
 * @create: 2021/5/13 11:55
 * @verison: 1.0
 * @description:
 */
public class MyStringUtils {
    /**
     * @Description：系统生成，8位字母（大写）+数字，例：373H9G6K
     * @Param
     * @Date 16:52 2021/4/30
     * <AUTHOR>
     */
    public static String getEightBitCode() {
        int length = 8;
        StringBuilder str = new StringBuilder();
        SecureRandom random = new SecureRandom();

        //参数length，表示生成几位随机数
        for (int i = 0; i < length; i++) {

            String charOrNum = random.nextInt(2) % 2 == 0 ? "char" : "num";
            //输出字母还是数字
            if ("char".equalsIgnoreCase(charOrNum)) {
                //输出是大写字母还是小写字母
                int temp = random.nextInt(2) % 2 == 0 ? 65 : 97;
                str.append((char) (random.nextInt(26) + temp));
            } else {
                str.append(random.nextInt(10));
            }
        }
        return str.toString().toUpperCase();
    }
}
