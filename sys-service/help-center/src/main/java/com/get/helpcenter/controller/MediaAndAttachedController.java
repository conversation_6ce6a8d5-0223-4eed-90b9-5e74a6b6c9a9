package com.get.helpcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ResponseBo;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.helpcenter.service.IMediaAndAttachedService;
import com.get.helpcenter.dto.MediaAndAttachedDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/5/11 15:23
 * @verison: 1.0
 * @description:
 */
@Api(tags = "附件管理")
@RestController
@RequestMapping("help/media")
public class MediaAndAttachedController {
    @Resource
    private IMediaAndAttachedService attachedService;


    /**
     * 删除
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除文件")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.DELETE, description = "帮助中心/附件管理/删除文件")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        attachedService.deleteMediaAttached(id);
        return ResponseBo.ok();
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传文件")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.ADD, description = "帮助中心/附件管理/上传文件")
    @PostMapping("upload")
    public ResponseBo upload(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", attachedService.upload(files));
        return responseBo;
    }

    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "上传附件")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.ADD, description = "帮助中心/附件管理/上传附件")
    @PostMapping("uploadAttached")
    public ResponseBo uploadAttached(@RequestParam("files") @NotNull(message = "文件不能为空") MultipartFile[] files) {
        ResponseBo responseBo = new ResponseBo();
        responseBo.put("data", attachedService.uploadAppendix(files));
        return responseBo;
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description：上移下移
     * @Param [mediaAttachedVos]
     * <AUTHOR>
     **/
    @ApiOperation(value = "上移下移", notes = "")
    @OperationLogger(module = LoggerModulesConsts.HELPCENTER, type = LoggerOptTypeConst.EDIT, description = "帮助中心/附件管理/移动顺序")
    @PostMapping("movingOrder")
    public ResponseBo movingOrder(@RequestBody List<MediaAndAttachedDto> mediaAttachedVos) {
        attachedService.movingOrder(mediaAttachedVos);
        return ResponseBo.ok();
    }
}
