spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    druid:
      validation-query: select 1

get:
  datasource:
    job:
      dev:
        # MySql
        url: **************************************************************************************************************************************************************************************************************************
        username: get
        password: root
