#服务器端口
server:
  port: 8088

#数据源配置
spring:
  datasource:
    dynamic:
      #设置默认的数据源master
      primary: filedb
      datasource:
        filedb:
          url: **************************************************************************************************************************
          username: root
          password: fzhmysql
        appfile:
          url: **************************************************************************************************************************
          username: root
          password: fzhmysql

  tencentcloudimage:
    secretId: AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza
    secretKey: lQ8oRyOk12uETcyCGILG0b8sgchpttiT
    apCity: ap-shanghai
    bucketname: hti-ais-images-dev-1301376564

  tencentcloudfile:
    secretId: AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza
    secretKey: lQ8oRyOk12uETcyCGILG0b8sgchpttiT
    apCity: ap-shanghai
    bucketname: hti-ais-files-dev-1301376564

  #学校中心默认使用GET私密桶作为IAE和GET共享桶
  tencentCloudShareFile:
    secretId: AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza
    secretKey: lQ8oRyOk12uETcyCGILG0b8sgchpttiT
    apCity: ap-shanghai
    bucketname: hti-ais-files-dev-1301376564

  #hti公开桶配置
  htitencentcloudimage:
    secretId: AKIDvkNr8KK71757bv8GDygJoGgEfw0HNHza
    secretKey: lQ8oRyOk12uETcyCGILG0b8sgchpttiT
    apCity: ap-shanghai
    bucketname: hti-ais-images-dev-1301376564

  servlet:
    multipart:
      enabled: true
      file-size-threshold: 0
      max-file-size: 200MB
      max-request-size: 300MB
