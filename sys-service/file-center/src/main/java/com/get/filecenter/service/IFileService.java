package com.get.filecenter.service;

import com.get.filecenter.dto.FileDto;
import com.get.filecenter.vo.FileVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: jack
 * @create: 2020/7/3
 * @verison: 1.0
 * @description: 文件管理业务接口
 */
public interface IFileService {
    /**
     * 上传文件
     *
     * @param files
     * @param type
     * @return
     */
    List<FileDto> upload(MultipartFile[] files, String type);

    /**
     * 上传附件
     *
     * @param files
     * @param type
     * @return
     */
    List<FileDto> uploadAppendix(MultipartFile[] files, String type);

    /**
     * 上传私有桶文件
     *
     * @param files
     * @param type
     * @param gmtCreateUser
     * @param prefix
     * @return
     */
    List<FileDto> uploadPrivateBucketFile(MultipartFile[] files, String type, String gmtCreateUser, String prefix);

    /**
     * 删除文件
     *
     * @param
     * @return
     */
    Boolean delete(String guid, String type);

    /**
     * 详情
     *
     * @param filevo
     * @return
     */
    FileDto findFileById(FileVo filevo);

    /**
     * 根据GUID查询
     *
     * @param guidList
     * @return
     */
    List<FileDto> findFileByGuid(List<String> guidList, String type);
    /**
     * 上传头像
     * @param files
     * @param type
     * @return
     */
    /* List<FileDto> uploadImage(MultipartFile[] files, String type);*/

    /**
     * 初始化返回富文本图片
     *
     * @param response
     * @param path
     */
    void initFileInputStream(HttpServletResponse response, String path);

    OutputStream getFile(HttpServletResponse response, FileVo fileVo);


    OutputStream downloadDemo(HttpServletResponse response, String path, String url);

    Map<String,String> getFilePathByGuids(List<String> guId);

    Map<String,String> getSaleFileNameOrcByGuids(List<String> guids);

    Map<String,String> getSaleFileKeyByGuids(List<String> guids);

    void deleteAll(Set<String> guids);

    List<FileDto> uploadHtiPublicFile(MultipartFile[] files, String type, String gmtCreateUser, String prefix);
}
