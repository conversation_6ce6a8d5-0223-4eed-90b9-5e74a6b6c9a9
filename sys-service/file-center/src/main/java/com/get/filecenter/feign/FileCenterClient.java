package com.get.filecenter.feign;

import com.get.common.utils.BeanCopyUtils;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.service.IFileService;
import com.get.filecenter.service.ITencentCloudService;
import com.get.filecenter.vo.FileVo;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Feign实现
 */
@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class FileCenterClient implements IFileCenterClient {
    private final IFileService fileService;

    private final ITencentCloudService tencentCloudService;

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<FileDto>> upload(MultipartFile[] files, String type) {
        return Result.data(fileService.upload(files, type).stream().map(fileDto -> BeanCopyUtils.objClone(fileDto, FileDto::new)).collect(Collectors.toList()));
    }

    /**
     * 上传附件
     * @param files
     * @param type
     * @return
     */
    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<FileDto>> uploadAppendix(MultipartFile[] files, String type) {
        return Result.data(fileService.uploadAppendix(files, type).stream().map(fileDto -> BeanCopyUtils.objClone(fileDto, FileDto::new)).collect(Collectors.toList()));
    }

    @Override
    public Result<List<FileDto>> uploadPrivateBucketFile(MultipartFile[] files, String type, String gmtCreateUser, String prefix) {
        return Result.data(fileService.uploadPrivateBucketFile(files, type, gmtCreateUser, prefix).stream().map(fileDto -> BeanCopyUtils.objClone(fileDto, FileDto::new)).collect(Collectors.toList()));
    }

    @Override
    public Result<List<FileDto>> uploadHtiPublicFile(MultipartFile[] files, String type, String gmtCreateUser, String prefix) {
        return Result.data(fileService.uploadHtiPublicFile(files, type, gmtCreateUser, prefix).stream().map(fileDto -> BeanCopyUtils.objClone(fileDto, FileDto::new)).collect(Collectors.toList()));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<FileDto>> findFileByGuid(Map<String, List<String>> guidListWithType) {
        String type = guidListWithType.entrySet().iterator().next().getKey();
        List<String> list = guidListWithType.get(type);
        List<FileDto> fileByGuid = fileService.findFileByGuid(list, type);
        return Result.data(fileByGuid.stream().map(fileDto -> BeanCopyUtils.objClone(fileDto, FileDto::new)).filter(Objects::nonNull).collect(Collectors.toList()));
    }

    @Override
    public Result<List<FileDto>> getFile(@NotNull(message = "guid不能为空") List<String> guidList, @NotBlank(message = "type不能为空") String type) {
        return Result.data(fileService.findFileByGuid(guidList, type));
    }

    @Override
    public Result<Boolean> delete(String guid, String type) {
        return Result.data(fileService.delete(guid, type));
    }

    @Override
    public void deleteAll(Set<String> guids) {
        fileService.deleteAll(guids);
    }

    @Override
    public Result<List<FileDto>> feignUploadAppendix(@RequestPart("files") MultipartFile[] files, @RequestParam("type") String type) {
        return Result.data(fileService.uploadAppendix(files, type).stream().map(fileDto -> BeanCopyUtils.objClone(fileDto, FileDto::new)).collect(Collectors.toList()));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<String, String>> getFilePathByGuids(List<String> guId) {
        return Result.data(fileService.getFilePathByGuids(guId));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<SaleFileDto> getDownloadFile(FileVo fileVo) {
        return Result.data(tencentCloudService.getDownloadFile(fileVo));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<String, String>> getSaleFileNameOrcByGuids(List<String> guids) {
        return Result.data(fileService.getSaleFileNameOrcByGuids(guids));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<String, String>> getSaleFileKeyByGuids(List<String> guids) {
        return Result.data(fileService.getSaleFileKeyByGuids(guids));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Boolean> uploadObjectByXxl(MultipartFile file, Boolean isPub, String bucketName, String ossPath) {
        tencentCloudService.uploadObject(isPub,bucketName,file,ossPath);
        return Result.data(true);
    }

//    @Override
//    public Result<List<FileDto>> findFileByGuid(@NotNull(message = "guid不能为空") List<String> guidList, @NotBlank(message = "type不能为空") String type) {
//        List<FileDto> fileByGuid = fileService.findFileByGuid(guidList, type);
//        return Result.data(fileByGuid.stream().map(fileDto -> BeanCopyUtils.objClone(fileDto, FileDto::new)).collect(Collectors.toList()));
//    }
}
