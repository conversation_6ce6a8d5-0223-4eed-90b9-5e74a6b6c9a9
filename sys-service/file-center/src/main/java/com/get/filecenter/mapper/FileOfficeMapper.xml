<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.filecenter.mapper.FileOfficeMapper">
  <insert id="insert" parameterType="com.get.filecenter.entity.FileOffice" keyProperty="id" useGeneratedKeys="true">
    insert into m_file_office (id, file_guid, file_type_orc, 
      file_name_orc, file_name, file_path, 
      file_key, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fileGuid,jdbcType=VARCHAR}, #{fileTypeOrc,jdbcType=VARCHAR}, 
      #{fileNameOrc,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{filePath,jdbcType=VARCHAR}, 
      #{fileKey,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.filecenter.entity.FileOffice" keyProperty="id" useGeneratedKeys="true">
    insert into m_file_office
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fileGuid != null">
        file_guid,
      </if>
      <if test="fileTypeOrc != null">
        file_type_orc,
      </if>
      <if test="fileNameOrc != null">
        file_name_orc,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="fileKey != null">
        file_key,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fileGuid != null">
        #{fileGuid,jdbcType=VARCHAR},
      </if>
      <if test="fileTypeOrc != null">
        #{fileTypeOrc,jdbcType=VARCHAR},
      </if>
      <if test="fileNameOrc != null">
        #{fileNameOrc,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>