<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.filecenter.mapper.FileMsoMapper">
  <resultMap id="BaseResultMap" type="com.get.filecenter.entity.FileMso">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="file_guid" jdbcType="VARCHAR" property="fileGuid" />
    <result column="file_type_orc" jdbcType="VARCHAR" property="fileTypeOrc" />
    <result column="file_name_orc" jdbcType="VARCHAR" property="fileNameOrc" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="file_path" jdbcType="VARCHAR" property="filePath" />
    <result column="file_key" jdbcType="VARCHAR" property="fileKey" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, file_guid, file_type_orc, file_name_orc, file_name, file_path, file_key, gmt_create, 
    gmt_create_user, gmt_modified, gmt_modified_user
  </sql>
  <insert id="insert" parameterType="com.get.filecenter.entity.FileMso" keyProperty="id" useGeneratedKeys="true">
    insert into m_file_mso (id, file_guid, file_type_orc, 
      file_name_orc, file_name, file_path, 
      file_key, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fileGuid,jdbcType=VARCHAR}, #{fileTypeOrc,jdbcType=VARCHAR}, 
      #{fileNameOrc,jdbcType=VARCHAR}, #{fileName,jdbcType=VARCHAR}, #{filePath,jdbcType=VARCHAR}, 
      #{fileKey,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.filecenter.entity.FileMso" keyProperty="id" useGeneratedKeys="true">
    insert into m_file_mso
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fileGuid != null">
        file_guid,
      </if>
      <if test="fileTypeOrc != null">
        file_type_orc,
      </if>
      <if test="fileNameOrc != null">
        file_name_orc,
      </if>
      <if test="fileName != null">
        file_name,
      </if>
      <if test="filePath != null">
        file_path,
      </if>
      <if test="fileKey != null">
        file_key,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fileGuid != null">
        #{fileGuid,jdbcType=VARCHAR},
      </if>
      <if test="fileTypeOrc != null">
        #{fileTypeOrc,jdbcType=VARCHAR},
      </if>
      <if test="fileNameOrc != null">
        #{fileNameOrc,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.filecenter.entity.FileMso">
    update m_file_mso
    <set>
      <if test="fileGuid != null">
        file_guid = #{fileGuid,jdbcType=VARCHAR},
      </if>
      <if test="fileTypeOrc != null">
        file_type_orc = #{fileTypeOrc,jdbcType=VARCHAR},
      </if>
      <if test="fileNameOrc != null">
        file_name_orc = #{fileNameOrc,jdbcType=VARCHAR},
      </if>
      <if test="fileName != null">
        file_name = #{fileName,jdbcType=VARCHAR},
      </if>
      <if test="filePath != null">
        file_path = #{filePath,jdbcType=VARCHAR},
      </if>
      <if test="fileKey != null">
        file_key = #{fileKey,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.filecenter.entity.FileMso">
    update m_file_mso
    set file_guid = #{fileGuid,jdbcType=VARCHAR},
      file_type_orc = #{fileTypeOrc,jdbcType=VARCHAR},
      file_name_orc = #{fileNameOrc,jdbcType=VARCHAR},
      file_name = #{fileName,jdbcType=VARCHAR},
      file_path = #{filePath,jdbcType=VARCHAR},
      file_key = #{fileKey,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>