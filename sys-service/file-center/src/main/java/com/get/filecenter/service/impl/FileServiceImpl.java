package com.get.filecenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.api.ResultCode;
import com.get.core.tool.utils.GeneralTool;
import com.get.file.utils.AppendixUtils;
import com.get.file.utils.FileUtils;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.entity.FilePermission;
import com.get.filecenter.entity.*;
import com.get.filecenter.mapper.*;
import com.get.filecenter.service.IFileService;
import com.get.filecenter.service.ITencentCloudService;
import com.get.filecenter.utils.DownloadFileUtils;
import com.get.filecenter.vo.FileVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.apache.http.util.TextUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.FileNameMap;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

import static com.get.filecenter.service.impl.TencentCloudServiceImpl.subString;

/**
 * @author: jack
 * @create: 2020/7/3
 * @verison: 1.0
 * @description: 文件管理业务实现类
 */
@Service
public class FileServiceImpl implements IFileService {
    private final static String PREFIX_VIDEO = "video/";
    @Resource
    private FileFinanceMapper fileFinanceMapper;
    @Resource
    private FileInstitutionMapper fileInstitutionMapper;
    @Resource
    private FilePermissionMapper filePermissionMapper;
    @Resource
    private FileExportMapper fileExportMapper;
    @Resource
    private FileSaleMapper fileSaleMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private FileResumeMapper fileResumeMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private ITencentCloudService tencentCloudService;
    @Resource
    private FileMsoMapper fileMsoMapper;
    @Resource
    private FileIssueMapper fileIssueMapper;
    @Resource
    private FileExamMapper fileExamMapper;
    @Resource
    private FileVotingMapper fileVotingMapper;
    @Resource
    private FileOfficeMapper fileOfficeMapper;
    @Resource
    private FileHelpMapper fileHelpMapper;
    @Resource
    private FileConventionMapper fileConventionMapper;
    @Resource
    private FileCompetitionMapper fileCompetitionMapper;
    @Resource
    private FilePmpMapper filePmpMapper;
    @Resource
    private FilePartnerMapper filePartnerMapper;
    //公开桶
    @Value("${spring.tencentcloudimage.bucketname}")
    private String imageBucketName;
    //私密桶
    @Value("${spring.tencentcloudfile.bucketname}")
    private String fileBucketName;
    //GET和IAE共享私密桶（也就是原来的GET私密桶）
    @Value("${spring.tencentCloudShareFile.bucketname}")
    private String shareBucketName;

    @Value("${spring.htitencentcloudimage.bucketname}")
    private String htiBucketName;

    /**
     * Get the Mime Type from a File
     *
     * @param fileName 文件名
     * @return 返回MIME类型
     * thx https://www.oschina.net/question/571282_223549
     * add by fengwenhua 2017年5月3日09:55:01
     */
    private static String getMimeType(String fileName) {
        FileNameMap fileNameMap = URLConnection.getFileNameMap();
        String type = fileNameMap.getContentTypeFor(fileName);
        return type;
    }

    /**
     * 根据文件后缀名判断 文件是否是视频文件
     *
     * @param fileName 文件名
     * @return 是否是视频文件
     */
    public static boolean isVedioFile(String fileName) {
        String mimeType = getMimeType(fileName);
        if (!TextUtils.isEmpty(fileName) && mimeType.contains(PREFIX_VIDEO)) {
            return true;
        }
        return false;
    }

    @Override
    public List<FileDto> upload(MultipartFile[] files, String type) {
        validatefile(files);
        List<FileDto> datas = new ArrayList<>();
        for (MultipartFile file : files) {
            String fileurl = FileUtils.getFilePath(file);
            FileDto fileDto = saveFileInfo(imageBucketName, file, fileurl, type, null,true, null);
            datas.add(fileDto);
        }
        return datas;
    }

    /**
     * 上传附件
     * @param files
     * @param type
     * @return
     */
    @Override
    public List<FileDto> uploadAppendix(MultipartFile[] files, String type) {
        validatefile(files);
        List<FileDto> datas = new ArrayList<>();
        for (MultipartFile file : files) {
            String fileurl = AppendixUtils.getFilePath(file, null);
            String bucketName = fileBucketName;
            FileDto fileDto = saveFileInfo(bucketName, file, fileurl, type, getAppendixPerfix(),false, null);
            datas.add(fileDto);
        }
        return datas;
    }

    /**
     * 上传私有桶文件
     *
     * @param files
     * @param type
     * @param prefix
     * @return
     */
    @Override
    public List<FileDto> uploadPrivateBucketFile(MultipartFile[] files, String type, String gmtCreateUser, String prefix) {
        validatefile(files);
        List<FileDto> datas = new ArrayList<>();
        for (MultipartFile file : files) {
            String fileurl = AppendixUtils.getFilePath(file,prefix);
            String bucketName = fileBucketName;
            FileDto fileDto = saveFileInfo(bucketName, file, fileurl, type, getAppendixPerfix(),false, gmtCreateUser);
            datas.add(fileDto);
        }
        return datas;
    }

    @Override
    public Boolean delete(String guid, String type) {
        String fileKey = null;
        boolean success = false;
        if ((type).equals(LoggerModulesConsts.EXPORT)) {
            FileExport fileExport = fileExportMapper.selectOne(Wrappers.<FileExport>lambdaQuery().eq(FileExport::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(fileExport)) {
                fileKey = fileExport.getFileKey();
                fileExportMapper.deleteById(fileExport.getId());
                success = true;
            }
        }else if ((type).equals(LoggerModulesConsts.PERMISSIONCENTER)) {
            FilePermission filePermission = filePermissionMapper.selectOne(Wrappers.<FilePermission>lambdaQuery().eq(FilePermission::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(filePermission)) {
                fileKey = filePermission.getFileKey();
                filePermissionMapper.deleteById(filePermission.getId());
                success = true;
            }
        } else if ((type).equals(LoggerModulesConsts.INSTITUTIONCENTER)) {
            FileInstitution fileInstitution = fileInstitutionMapper.selectOne(Wrappers.<FileInstitution>lambdaQuery().eq(FileInstitution::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(fileInstitution)) {
                fileKey = fileInstitution.getFileKey();
                fileInstitutionMapper.deleteById(fileInstitution.getId());
                success = true;
            }
        } else if ((type).equals(LoggerModulesConsts.FINANCECENTER)) {
            FileFinance fileFinance = fileFinanceMapper.selectOne(Wrappers.<FileFinance>lambdaQuery().eq(FileFinance::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(fileFinance)) {
                fileKey = fileFinance.getFileKey();
                fileFinanceMapper.deleteById(fileFinance.getId());
                success = true;
            }
        } else if ((type).equals(LoggerModulesConsts.SALECENTER)) {
            FileSale fileSale = fileSaleMapper.selectOne(Wrappers.<FileSale>lambdaQuery().eq(FileSale::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(fileSale)) {
                fileKey = fileSale.getFileKey();
                fileSaleMapper.deleteById(fileSale.getId());
                success = true;
            }
        } else if ((type).equals(LoggerModulesConsts.RESUMECENTER)) {
            FileResume fileResume = fileResumeMapper.selectOne(Wrappers.<FileResume>lambdaQuery().eq(FileResume::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(fileResume)) {
                fileKey = fileResume.getFileKey();
                fileResumeMapper.deleteById(fileResume.getId());
                success = true;
            }
        } else if ((type).equals(LoggerModulesConsts.EXAMCENTER)) {
            FileExam fileExam = fileExamMapper.selectOne(Wrappers.<FileExam>lambdaQuery().eq(FileExam::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(fileExam)) {
                fileKey = fileExam.getFileKey();
                fileExamMapper.deleteById(fileExam.getId());
                success = true;
            }
        } else if ((type).equals(LoggerModulesConsts.VOTINGCENTER)) {
            FileVoting fileVoting = fileVotingMapper.selectOne(Wrappers.<FileVoting>lambdaQuery().eq(FileVoting::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(fileVoting)) {
                fileKey = fileVoting.getFileKey();
                fileVotingMapper.deleteById(fileVoting.getId());
                success = true;
            }
        } else if ((type).equals(LoggerModulesConsts.OFFICECENTER)) {
            FileOffice fileOffice = fileOfficeMapper.selectOne(Wrappers.<FileOffice>lambdaQuery().eq(FileOffice::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(fileOffice)) {
                fileKey = fileOffice.getFileKey();
                fileOfficeMapper.deleteById(fileOffice.getId());
                success = true;
            }
        } else if ((type).equals(LoggerModulesConsts.CONVENTIONCENTER)) {
            FileConvention fileConvention = fileConventionMapper.selectOne(Wrappers.<FileConvention>lambdaQuery().eq(FileConvention::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(fileConvention)) {
                fileKey = fileConvention.getFileKey();
                fileConventionMapper.deleteById(fileConvention.getId());
                success = true;
            }
        } else if ((type).equals(LoggerModulesConsts.PLATFORMCENTER_MSO)) {
            FileMso fileMso = fileMsoMapper.selectOne(Wrappers.<FileMso>lambdaQuery().eq(FileMso::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(fileMso)) {
                fileKey = fileMso.getFileKey();
                fileMsoMapper.deleteById(fileMso.getId());
                success = true;
            }
        } else if ((type).equals(LoggerModulesConsts.PLATFORMCENTER_ISSUE)) {
            FileIssue fileIssue = fileIssueMapper.selectOne(Wrappers.<FileIssue>lambdaQuery().eq(FileIssue::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(fileIssue)) {
                fileKey = fileIssue.getFileKey();
                fileIssueMapper.deleteById(fileIssue.getId());
                success = true;
            }
        } else if ((type).equals(LoggerModulesConsts.COMPETITIONCENTER)) {
            FileCompetition fileCompetition = fileCompetitionMapper.selectOne(Wrappers.<FileCompetition>lambdaQuery().eq(FileCompetition::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(fileCompetition)) {
                fileKey = fileCompetition.getFileKey();
                fileCompetitionMapper.deleteById(fileCompetition.getId());
                success = true;
            }
        } else if ((type).equals(LoggerModulesConsts.PMPCENTER)) {
            FilePmp filePmp = filePmpMapper.selectOne(new LambdaQueryWrapper<FilePmp>().eq(FilePmp::getFileGuid, guid));
            if (GeneralTool.isNotEmpty(filePmp)) {
                fileKey = filePmp.getFileKey();
                filePmpMapper.deleteById(filePmp.getId());
                success = true;
            }
        }
        if (!success) {
            throw new GetServiceException(ResultCode.INVALID_PARAM, LocaleMessageUtils.getMessage("search_file_null"));
        }
//        else {
//            tencentCloudService.deleteObjectRequest(fileKey);
//        }
        return success;
    }

/*    @Override
    public List<FileDto> uploadImage(MultipartFile[] files, String type)  {
        validateimage(files);
        List<FileDto> datas = new ArrayList<>();
        for (MultipartFile file : files) {
            String fileurl = null;
            try {
                fileurl = FileUtils.saveFile(file);
            } catch (IOException e) {
                throw new YServerException("图片上传错误");
            }
            FileDto fileDto = saveFileInfo(file, fileurl, type);
            datas.add(fileDto);
        }
        return datas;
    }*/

    @Override
    public FileDto findFileById(FileVo filevo) {
        String type = filevo.getType();
        FileDto fileDto = null;
        if ((type).equals(LoggerModulesConsts.PERMISSIONCENTER)) {
            FilePermission filePermission = filePermissionMapper.selectById(filevo.getId());
            fileDto = BeanCopyUtils.objClone(filePermission, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.INSTITUTIONCENTER)) {
            FileInstitution fileInstitution = fileInstitutionMapper.selectById(filevo.getId());
            fileDto = BeanCopyUtils.objClone(fileInstitution, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.FINANCECENTER)) {
            FileFinance fileFinance = fileFinanceMapper.selectById(filevo.getId());
            fileDto = BeanCopyUtils.objClone(fileFinance, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.SALECENTER)) {
            FileSale fileSale = fileSaleMapper.selectById(filevo.getId());
            fileDto = BeanCopyUtils.objClone(fileSale, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.RESUMECENTER)) {
            FileResume fileResume = fileResumeMapper.selectById(filevo.getId());
            fileDto = BeanCopyUtils.objClone(fileResume, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.EXAMCENTER)) {
            FileExam fileExam = fileExamMapper.selectById(filevo.getId());
            fileDto = BeanCopyUtils.objClone(fileExam, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.COMPETITIONCENTER)) {
            FileCompetition fileCompetition = fileCompetitionMapper.selectById(filevo.getId());
            fileDto = BeanCopyUtils.objClone(fileCompetition, FileDto::new);
        }
        return fileDto;
    }

    @Override
    public List<FileDto> findFileByGuid(List<String> guidList, String type) {
        if (GeneralTool.isEmpty(guidList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("guid_null"));
        }
        List<FileDto> fileDtos = null;
        if ((type).equals(LoggerModulesConsts.PERMISSIONCENTER)) {
//            Example example = new Example(FilePermission.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andIn("fileGuid", guidList);
//            List<FilePermission> filePermissions = filePermissionMapper.selectByExample(example);
            List<FilePermission> filePermissions = filePermissionMapper.selectList(Wrappers.<FilePermission>lambdaQuery().in(FilePermission::getFileGuid, guidList));
            fileDtos = filePermissions.stream().map(fileFinance -> BeanCopyUtils.objClone(fileFinance, FileDto::new)).collect(Collectors.toList());

        } else if ((type).equals(LoggerModulesConsts.RESUMECENTER)) {
//            Example example = new Example(FileResume.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andIn("fileGuid", guidList);
//            List<FileResume> fileResumes = fileResumeMapper.selectByExample(example);
            List<FileResume> fileResumes = fileResumeMapper.selectList(Wrappers.<FileResume>lambdaQuery().in(FileResume::getFileGuid, guidList));
            fileDtos = fileResumes.stream().map(fileResume -> BeanCopyUtils.objClone(fileResume, FileDto::new)).collect(Collectors.toList());
        } else if ((type).equals(LoggerModulesConsts.INSTITUTIONCENTER)) {
//            Example example = new Example(FileInstitution.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andIn("fileGuid", guidList);
//            List<FileInstitution> fileInstitutions = fileInstitutionMapper.selectByExample(example);
            List<FileInstitution> fileInstitutions = fileInstitutionMapper.selectList(Wrappers.<FileInstitution>lambdaQuery().in(FileInstitution::getFileGuid, guidList));
            fileDtos = fileInstitutions.stream().map(fileInstitution -> BeanCopyUtils.objClone(fileInstitution, FileDto::new)).collect(Collectors.toList());
        } else if ((type).equals(LoggerModulesConsts.SALECENTER)) {
//            Example example = new Example(FileSale.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andIn("fileGuid", guidList);
//            List<FileSale> fileSales = fileSaleMapper.selectByExample(example);
            List<FileSale> fileSales = fileSaleMapper.selectList(Wrappers.<FileSale>lambdaQuery().in(FileSale::getFileGuid, guidList));
            fileDtos = fileSales.stream().map(fileSale -> BeanCopyUtils.objClone(fileSale, FileDto::new)).collect(Collectors.toList());
        } else if ((type).equals(LoggerModulesConsts.FINANCECENTER)) {
//            Example example = new Example(FileFinance.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andIn("fileGuid", guidList);
//            List<FileFinance> fileFinances = fileFinanceMapper.selectByExample(example);
            List<FileFinance> fileFinances = fileFinanceMapper.selectList(Wrappers.<FileFinance>lambdaQuery().in(FileFinance::getFileGuid, guidList));
            fileDtos = fileFinances.stream().map(fileFinance -> BeanCopyUtils.objClone(fileFinance, FileDto::new)).collect(Collectors.toList());
        } else if ((type).equals(LoggerModulesConsts.PLATFORMCENTER_MSO)) {
//            Example example = new Example(FileMso.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andIn("fileGuid", guidList);
//            List<FileMso> fileMsos = fileMsoMapper.selectByExample(example);
            List<FileMso> fileMsos = fileMsoMapper.selectList(Wrappers.<FileMso>lambdaQuery().in(FileMso::getFileGuid, guidList));
            fileDtos = fileMsos.stream().map(fileMso -> BeanCopyUtils.objClone(fileMso, FileDto::new)).collect(Collectors.toList());
        } else if ((type).equals(LoggerModulesConsts.PLATFORMCENTER_ISSUE)) {
//            Example example = new Example(FileIssue.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andIn("fileGuid", guidList);
//            List<FileIssue> fileIssues = fileIssueMapper.selectByExample(example);
            List<FileIssue> fileIssues = fileIssueMapper.selectList(Wrappers.<FileIssue>lambdaQuery().in(FileIssue::getFileGuid, guidList));
            fileDtos = fileIssues.stream().map(fileIssue -> BeanCopyUtils.objClone(fileIssue, FileDto::new)).collect(Collectors.toList());
        } else if ((type).equals(LoggerModulesConsts.EXAMCENTER)) {
//            Example example = new Example(FileExam.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andIn("fileGuid", guidList);
//            List<FileExam> fileExams = fileExamMapper.selectByExample(example);
            List<FileExam> fileExams = fileExamMapper.selectList(Wrappers.<FileExam>lambdaQuery().in(FileExam::getFileGuid, guidList));
            fileDtos = fileExams.stream().map(fileExam -> BeanCopyUtils.objClone(fileExam, FileDto::new)).collect(Collectors.toList());
        } else if ((type).equals(LoggerModulesConsts.VOTINGCENTER)) {
//            Example example = new Example(FileVoting.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andIn("fileGuid", guidList);
//            List<FileVoting> fileVotings = fileVotingMapper.selectByExample(example);
            List<FileVoting> fileVotings = fileVotingMapper.selectList(Wrappers.<FileVoting>lambdaQuery().in(FileVoting::getFileGuid, guidList));
            fileDtos = fileVotings.stream().map(fileVoting -> BeanCopyUtils.objClone(fileVoting, FileDto::new)).collect(Collectors.toList());
        } else if ((type).equals(LoggerModulesConsts.OFFICECENTER)) {
//            Example example = new Example(FileOffice.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andIn("fileGuid", guidList);
//            List<FileOffice> fileOffices = fileOfficeMapper.selectByExample(example);
            List<FileOffice> fileOffices = fileOfficeMapper.selectList(Wrappers.<FileOffice>lambdaQuery().in(FileOffice::getFileGuid, guidList));
            fileDtos = fileOffices.stream().map(fileOffice -> BeanCopyUtils.objClone(fileOffice, FileDto::new)).collect(Collectors.toList());
        } else if ((type).equals(LoggerModulesConsts.COMPETITIONCENTER)) {
//            Example example = new Example(FileOffice.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andIn("fileGuid", guidList);
//            List<FileOffice> fileOffices = fileOfficeMapper.selectByExample(example);
            List<FileCompetition> fileCompetitions = fileCompetitionMapper.selectList(Wrappers.<FileCompetition>lambdaQuery().in(FileCompetition::getFileGuid, guidList));
            fileDtos = fileCompetitions.stream().map(fileCompetition -> BeanCopyUtils.objClone(fileCompetition, FileDto::new)).collect(Collectors.toList());
        }
        for (FileDto fileDto : fileDtos) {
            if (!fileDto.getFilePath().contains("appendix")&&!fileDto.getFilePath().contains("student_file")) {
                fileDto.setFilePath(getFilePerfix() + fileDto.getFilePath());
            }
        }
        return fileDtos;
    }

    private void validatefile(MultipartFile[] files) {
        for (MultipartFile file : files) {
            String filename = file.getOriginalFilename();
            if (filename == null || "".equals(filename.trim())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("file_name_null"));
            }
            int i = filename.lastIndexOf(".");
            //获取后缀名
            String substring = filename.substring(i, filename.length()).toLowerCase();
            //判断是否为规定可上传的文件
            String regexfile = "^(.doc|.pdf|.txt|.docx|.xlsx|.xls|.ppt|.apk|.jpg|.ico|.gif|.png|.jpeg|.bmp|.mp3|.mp4|.avi|.flv|.rmvb|.eml|.html|.msg|.htm|.odt|.wps|.mhtml|.zip|.rar|.asf|.wmv)$";
            String regeximage = "^(.bmp|.jpg|.pptx|.png|.tif|.gif|.pcx|.tga|.exif|.fpx|.svg|.psd|.cdr|.pcd|.dxf|.ufo|.eps|.ai|.raw|.WMF|.webp)$";
            if (!substring.matches(regexfile) && !substring.matches(regeximage)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("file_format_error"));
            }
                     /*   if(substring.matches(regeximage)){
                if(file.getSize()>1048576){
                    throw new YServiceException(LocaleMessageUtils.getMessage(" picture_too_big"));
                }
            }else if(isVedioFile(filename)){
                if(file.getSize()>52428800){
                    throw new YServiceException(LocaleMessageUtils.getMessage(" video_too_big"));
                }
            }else if(substring.matches("^(.pdf)$")){
                if(file.getSize()>20971520){
                    throw new YServiceException(LocaleMessageUtils.getMessage(" pdf_too_big"));
                }
            }else{
                if(file.getSize()>10485760){
                    throw new YServiceException(LocaleMessageUtils.getMessage(" file_too_big"));
                }
            }*/
        }
    }

    private String getFilePerfix() {
        Result<String> result = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return "";
    }

    private String getAppendixPerfix() {
//        return permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.APPENDIX_SRC_PREFIX.key);
        Result<String> result = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.APPENDIX_SRC_PREFIX.key);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            return result.getData();
        }
        return "";
    }

    /**
     * 保存文件
     *
     * @param bucketName
     * @param file
     * @param fileurl
     * @param type
     * @param perfix
     * @param isPub         true:公开桶 false:私密桶
     * @param gmtCreateUser
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public FileDto saveFileInfo(String bucketName, MultipartFile file, String fileurl, String type, String perfix, boolean isPub, String gmtCreateUser) {
        FileDto fileDto = null;
        if (GeneralTool.isNotEmpty(perfix)) {
            fileurl = perfix + fileurl;
        }
        String filename = file.getOriginalFilename();
        int i = filename.lastIndexOf(".");
        int j = fileurl.lastIndexOf("/");
        //获取后缀名
        String substring = filename.substring(i, filename.length()).toLowerCase();
        //获取目标文件名称
        String targetFileName = fileurl.substring(j + 1, fileurl.length());
        if (LoggerModulesConsts.EXPORT.equals(type)){
            FileExport fileExport = new FileExport();
            fileExport.setFilePath(fileurl);
            fileExport.setFileNameOrc(filename);
            fileExport.setFileTypeOrc(substring);
            fileExport.setFileName(targetFileName);
            fileExport.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileExport.setGmtCreateUser(gmtCreateUser);
                fileExport.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileExport);
            }
            fileExportMapper.insert(fileExport);
            String ossPath = subString(fileExport.getFilePath());//如：/m_file_finance/files/2021/09/07/67db88c4-e052-42be-b13f-de487fd7aa98.jpg
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileExport.setFileKey(ossPath);
            fileExportMapper.updateById(fileExport);
            fileDto = BeanCopyUtils.objClone(fileExport, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.PERMISSIONCENTER)) {
            FilePermission filePermission = new FilePermission();
            filePermission.setFilePath(fileurl);
            filePermission.setFileNameOrc(filename);
            filePermission.setFileTypeOrc(substring);
            filePermission.setFileName(targetFileName);
            filePermission.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                filePermission.setGmtCreateUser(gmtCreateUser);
                filePermission.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(filePermission);
            }
            filePermissionMapper.insert(filePermission);
            String ossPath = subString(filePermission.getFilePath());//如：/m_file_finance/files/2021/09/07/67db88c4-e052-42be-b13f-de487fd7aa98.jpg
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            filePermission.setFileKey(ossPath);
            filePermissionMapper.updateById(filePermission);
            fileDto = BeanCopyUtils.objClone(filePermission, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.INSTITUTIONCENTER)) {
            FileInstitution fileInstitution = new FileInstitution();
            fileInstitution.setFilePath(fileurl);
            fileInstitution.setFileNameOrc(filename);
            fileInstitution.setFileTypeOrc(substring);
            fileInstitution.setFileName(targetFileName);
            fileInstitution.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileInstitution.setGmtCreateUser(gmtCreateUser);
                fileInstitution.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileInstitution);
            }
            fileInstitutionMapper.insert(fileInstitution);
//            tencentCloudService.uploadObject(fileInstitution.getFilePath(),"m_file_institution_"+fileInstitution.getId());
//            fileInstitution.setFileKey("m_file_institution_"+fileInstitution.getId());

            String ossPath = subString(fileInstitution.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileInstitution.setFileKey(ossPath);

            fileInstitutionMapper.updateById(fileInstitution);
            fileDto = BeanCopyUtils.objClone(fileInstitution, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.FINANCECENTER)) {
            FileFinance fileFinance = new FileFinance();
            fileFinance.setFilePath(fileurl);
            fileFinance.setFileNameOrc(filename);
            fileFinance.setFileTypeOrc(substring);
            fileFinance.setFileName(targetFileName);
            fileFinance.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileFinance.setGmtCreateUser(gmtCreateUser);
                fileFinance.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileFinance);
            }
            fileFinanceMapper.insert(fileFinance);
//            tencentCloudService.uploadObject(fileFinance.getFilePath(),"m_file_finance_"+fileFinance.getId());
//            fileFinance.setFileKey("m_file_finance_"+fileFinance.getId());

            String ossPath = subString(fileFinance.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileFinance.setFileKey(ossPath);
            fileFinanceMapper.updateById(fileFinance);
            fileDto = BeanCopyUtils.objClone(fileFinance, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.SALECENTER)) {
            FileSale fileSale = new FileSale();
            fileSale.setFilePath(fileurl);
            fileSale.setFileNameOrc(filename);
            fileSale.setFileTypeOrc(substring);
            fileSale.setFileName(targetFileName);
            fileSale.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileSale.setGmtCreateUser(gmtCreateUser);
                fileSale.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileSale);
            }
            fileSaleMapper.insert(fileSale);
//            tencentCloudService.uploadObject(fileSale.getFilePath(),"m_file_sale_"+fileSale.getId());
//            fileSale.setFileKey("m_file_sale_"+fileSale.getId());

            String ossPath = subString(fileSale.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileSale.setFileKey(ossPath);

            fileSaleMapper.updateById(fileSale);
            fileDto = BeanCopyUtils.objClone(fileSale, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.RESUMECENTER)) {
            FileResume fileResume = new FileResume();
            fileResume.setFilePath(fileurl);
            fileResume.setFileNameOrc(filename);
            fileResume.setFileTypeOrc(substring);
            fileResume.setFileName(targetFileName);
            fileResume.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileResume.setGmtCreateUser(gmtCreateUser);
                fileResume.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileResume);
            }
            fileResumeMapper.insert(fileResume);
//            tencentCloudService.uploadObject(fileResume.getFilePath(),"m_file_resume_"+fileResume.getId());
//            fileResume.setFileKey("m_file_resume_"+fileResume.getId());

            String ossPath = subString(fileResume.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileResume.setFileKey(ossPath);

            fileResumeMapper.updateById(fileResume);
            fileDto = BeanCopyUtils.objClone(fileResume, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.PLATFORMCENTER_MSO)) {
            FileMso fileMso = new FileMso();
            fileMso.setFilePath(fileurl);
            fileMso.setFileNameOrc(filename);
            fileMso.setFileTypeOrc(substring);
            fileMso.setFileName(targetFileName);
            fileMso.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileMso.setGmtCreateUser(gmtCreateUser);
                fileMso.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileMso);
            }
            fileMsoMapper.insert(fileMso);
//            tencentCloudService.uploadObject(fileMso.getFilePath(),"m_file_mso_"+fileMso.getId());
//            fileMso.setFileKey("m_file_mso_"+fileMso.getId());
            String ossPath = subString(fileMso.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileMso.setFileKey(ossPath);

            fileMsoMapper.updateById(fileMso);
            fileDto = BeanCopyUtils.objClone(fileMso, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.PLATFORMCENTER_ISSUE)) {
            FileIssue fileIssue = new FileIssue();
            fileIssue.setFilePath(fileurl);
            fileIssue.setFileNameOrc(filename);
            fileIssue.setFileTypeOrc(substring);
            fileIssue.setFileName(targetFileName);
            fileIssue.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileIssue.setGmtCreateUser(gmtCreateUser);
                fileIssue.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileIssue);
            }
            fileIssueMapper.insert(fileIssue);
//            tencentCloudService.uploadObject(fileIssue.getFilePath(),"m_file_issue_"+fileIssue.getId());
//            fileIssue.setFileKey("m_file_issue_"+fileIssue.getId());

            String ossPath = subString(fileIssue.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileIssue.setFileKey(ossPath);

            fileIssueMapper.updateById(fileIssue);
            fileDto = BeanCopyUtils.objClone(fileIssue, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.EXAMCENTER)) {
            FileExam fileExam = new FileExam();
            fileExam.setFilePath(fileurl);
            fileExam.setFileNameOrc(filename);
            fileExam.setFileTypeOrc(substring);
            fileExam.setFileName(targetFileName);
            fileExam.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileExam.setGmtCreateUser(gmtCreateUser);
                fileExam.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileExam);
            }
            fileExamMapper.insert(fileExam);
//            tencentCloudService.uploadObject(fileExam.getFilePath(),"m_file_exam_"+fileExam.getId());
//            fileExam.setFileKey("m_file_exam_"+fileExam.getId());

            String ossPath = subString(fileExam.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileExam.setFileKey(ossPath);

            fileExamMapper.updateById(fileExam);
            fileDto = BeanCopyUtils.objClone(fileExam, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.CONVENTIONCENTER)) {
            FileConvention fileConvention = new FileConvention();
            fileConvention.setFilePath(fileurl);
            fileConvention.setFileNameOrc(filename);
            fileConvention.setFileTypeOrc(substring);
            fileConvention.setFileName(targetFileName);
            fileConvention.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileConvention.setGmtCreateUser(gmtCreateUser);
                fileConvention.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileConvention);
            }
            fileConventionMapper.insert(fileConvention);
//            tencentCloudService.uploadObject(fileExam.getFilePath(),"m_file_schedule_"+fileExam.getId());
//            fileExam.setFileKey("m_file_schedule_"+fileExam.getId());

            String ossPath = subString(fileConvention.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileConvention.setFileKey(ossPath);

            fileConventionMapper.updateById(fileConvention);
            fileDto = BeanCopyUtils.objClone(fileConvention, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.VOTINGCENTER)) {
            FileVoting fileVoting = new FileVoting();
            fileVoting.setFilePath(fileurl);
            fileVoting.setFileNameOrc(filename);
            fileVoting.setFileTypeOrc(substring);
            fileVoting.setFileName(targetFileName);
            fileVoting.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileVoting.setGmtCreateUser(gmtCreateUser);
                fileVoting.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileVoting);
            }
            fileVotingMapper.insert(fileVoting);

//            tencentCloudService.uploadObject(fileVoting.getFilePath(),"m_file_voting_"+fileVoting.getId());
//            fileVoting.setFileKey("m_file_voting_"+fileVoting.getId());

            String ossPath = subString(fileVoting.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileVoting.setFileKey(ossPath);

            fileVotingMapper.updateById(fileVoting);
            fileDto = BeanCopyUtils.objClone(fileVoting, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.OFFICECENTER)) {
            FileOffice fileOffice = new FileOffice();
            fileOffice.setFilePath(fileurl);
            fileOffice.setFileNameOrc(filename);
            fileOffice.setFileTypeOrc(substring);
            fileOffice.setFileName(targetFileName);
            fileOffice.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileOffice.setGmtCreateUser(gmtCreateUser);
                fileOffice.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileOffice);
            }
            fileOfficeMapper.insert(fileOffice);
//            tencentCloudService.uploadObject(fileOffice.getFilePath(),"m_file_office_"+fileOffice.getId());
//            fileOffice.setFileKey("m_file_office_"+fileOffice.getId());

            String ossPath = subString(fileOffice.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileOffice.setFileKey(ossPath);

            fileOfficeMapper.updateById(fileOffice);
            fileDto = BeanCopyUtils.objClone(fileOffice, FileDto::new);
        }
        else if ((type).equals(LoggerModulesConsts.HELPCENTER)) {
            FileHelp fileHelp = new FileHelp();
            fileHelp.setFilePath(fileurl);
            fileHelp.setFileNameOrc(filename);
            fileHelp.setFileTypeOrc(substring);
            fileHelp.setFileName(targetFileName);
            fileHelp.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileHelp.setGmtCreateUser(gmtCreateUser);
                fileHelp.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileHelp);
            }
            fileHelpMapper.insert(fileHelp);
//            tencentCloudService.uploadObject(fileOffice.getFilePath(),"m_file_office_"+fileOffice.getId());
//            fileOffice.setFileKey("m_file_office_"+fileOffice.getId());

            String ossPath = subString(fileHelp.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileHelp.setFileKey(ossPath);

            fileHelpMapper.updateById(fileHelp);
            fileDto = BeanCopyUtils.objClone(fileHelp, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.COMPETITIONCENTER)) {
            FileCompetition fileCompetition = new FileCompetition();
            fileCompetition.setFilePath(fileurl);
            fileCompetition.setFileNameOrc(filename);
            fileCompetition.setFileTypeOrc(substring);
            fileCompetition.setFileName(targetFileName);
            fileCompetition.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                fileCompetition.setGmtCreateUser(gmtCreateUser);
                fileCompetition.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(fileCompetition);
            }
            fileCompetitionMapper.insert(fileCompetition);
//            tencentCloudService.uploadObject(fileOffice.getFilePath(),"m_file_office_"+fileOffice.getId());
//            fileOffice.setFileKey("m_file_office_"+fileOffice.getId());

            String ossPath = subString(fileCompetition.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub,bucketName, file, ossPath);
            fileCompetition.setFileKey(ossPath);

            fileCompetitionMapper.updateById(fileCompetition);
            fileDto = BeanCopyUtils.objClone(fileCompetition, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.PMPCENTER)) {
            FilePmp filePmp = new FilePmp();
            filePmp.setFilePath(fileurl);
            filePmp.setFileNameOrc(filename);
            filePmp.setFileTypeOrc(substring);
            filePmp.setFileName(filename);
            filePmp.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                filePmp.setGmtCreateUser(gmtCreateUser);
                filePmp.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(filePmp);
            }
            filePmpMapper.insert(filePmp);
            String ossPath = subString(filePmp.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub, bucketName, file, ossPath);
            filePmp.setFileKey(ossPath);

            filePmpMapper.updateById(filePmp);
            fileDto = BeanCopyUtils.objClone(filePmp, FileDto::new);
        } else if ((type).equals(LoggerModulesConsts.PARTNERCENTER)) {
            FilePartner filePartner =  new FilePartner();
            filePartner.setFilePath(fileurl);
            filePartner.setFileNameOrc(filename);
            filePartner.setFileTypeOrc(substring);
            filePartner.setFileName(targetFileName);
            filePartner.setFileGuid(UUID.randomUUID().toString().replaceAll("-", ""));
            if (gmtCreateUser != null) {
                filePartner.setGmtCreateUser(gmtCreateUser);
                filePartner.setGmtCreate(new Date());
            } else {
                utilService.updateUserInfoToEntity(filePartner);
            }
            filePartnerMapper.insert(filePartner);
            String ossPath = subString(filePartner.getFilePath());//如：/m_file_finance/files/2021/09/07/
            tencentCloudService.uploadObject(isPub, bucketName, file, ossPath);
            filePartner.setFileKey(ossPath);

            filePartnerMapper.updateById(filePartner);
            fileDto = BeanCopyUtils.objClone(filePartner, FileDto::new);
        }
        return fileDto;
    }

    @Override
    public OutputStream getFile(HttpServletResponse response, FileVo fileVo) {
        /**
         * 1. 获取文件，转化为文件流
         * 2. 返回controller
         */
        try {
            String fileName;
            if (GeneralTool.isNotEmpty(fileVo.getTypeValue())) {
                fileName = fileVo.getTypeValue() + "_" + fileVo.getFileNameOrc();
            } else {
                fileName = fileVo.getFileNameOrc();
            }
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes("utf-8"), "ISO-8859-1"));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        File file = new File(fileVo.getFilePath());
        OutputStream os = null;
        if (file.exists()) {
            byte[] buffer = new byte[1024];
            FileInputStream fis = null;
            BufferedInputStream bis = null;
            try {
                fis = new FileInputStream(file);
                bis = new BufferedInputStream(fis);
                os = response.getOutputStream();
                int i = bis.read(buffer);
                while (i != -1) {
                    os.write(buffer, 0, i);
                    i = bis.read(buffer);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (bis != null) {
                    try {
                        bis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (fis != null) {
                    try {
                        fis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_not_exist"));
        }
        return os;
    }


    @Override
    public OutputStream downloadDemo(HttpServletResponse response, String path, String url) {
        try {
            String reString = URLDecoder.decode(url, "UTF-8");

            String[] split = reString.split("/");
            String fileName = split[split.length - 1];

            System.out.println("fileName = " + fileName);
            DownloadFileUtils.downloadByNIO2(url, path, "img");

            InputStream ins = new URL(url).openStream();
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("download_failed") + e.getMessage());
        }
        return null;
    }


    @Override
    public void initFileInputStream(HttpServletResponse response, String path) {
        File file = new File(path);

        if (file.exists()) {
            byte[] buffer = new byte[1024];
            FileInputStream fis = null;
            BufferedInputStream bis = null;
            try {
                fis = new FileInputStream(file);
                bis = new BufferedInputStream(fis);
                OutputStream os = response.getOutputStream();
                int i = bis.read(buffer);
                while (i != -1) {
                    os.write(buffer, 0, i);
                    i = bis.read(buffer);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                if (bis != null) {
                    try {
                        bis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (fis != null) {
                    try {
                        fis.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_not_exist"));
        }
    }
    @Override
    public Map<String, String> getFilePathByGuids(List<String> guId) {
        LambdaQueryWrapper<FileInstitution>lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(FileInstitution::getFileGuid, guId);
        List<FileInstitution> fileInstitutions = fileInstitutionMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(fileInstitutions)){
           return fileInstitutions.stream().collect(Collectors.toMap(FileInstitution::getFileGuid, FileInstitution::getFileKey));
        }
        return null;
    }

    @Override
    public Map<String, String> getSaleFileNameOrcByGuids(List<String> guId) {
        LambdaQueryWrapper<FileSale>lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(FileSale::getFileGuid, guId);
        List<FileSale> fileSales = fileSaleMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(fileSales)){
            return fileSales.stream().collect(Collectors.toMap(FileSale::getFileGuid, FileSale::getFileNameOrc));
        }
        return null;
    }

    @Override
    public void deleteAll(Set<String> guids) {
        if (GeneralTool.isEmpty(guids)) {
           throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        fileExportMapper.delete(Wrappers.<FileExport>lambdaQuery().in(FileExport::getFileGuid,guids));
    }

    @Override
    public List<FileDto> uploadHtiPublicFile(MultipartFile[] files, String type, String gmtCreateUser, String prefix) {
        validatefile(files);
        List<FileDto> datas = new ArrayList<>();
        for (MultipartFile file : files) {
            String fileurl = FileUtils.getFileHtiPath(file, prefix);
            String bucketName = htiBucketName;//fileBucketName如在IAE环境下默认配置的是IAE的私密桶，如是学校中心或者平台配置使用的MSO和ISSUE应使用GET的私密桶
            FileDto fileDto = saveFileInfo(bucketName, file, fileurl, type, getAppendixPerfix(),true, gmtCreateUser);
            datas.add(fileDto);
        }
        return datas;
    }

    @Override
    public Map<String, String> getSaleFileKeyByGuids(List<String> guId) {
        LambdaQueryWrapper<FileSale>lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(FileSale::getFileGuid, guId);
        List<FileSale> fileSales = fileSaleMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isNotEmpty(fileSales)){
           return fileSales.stream().collect(Collectors.toMap(FileSale::getFileGuid, FileSale::getFileKey));
        }
        return null;
    }
}
