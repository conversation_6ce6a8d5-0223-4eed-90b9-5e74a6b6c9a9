package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.entity.StaffResource;
import com.get.permissioncenter.dto.StaffResourceDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface StaffResourceMapper extends BaseMapper<StaffResource> {
    @Override
    int insert(StaffResource record);

    List<StaffResource> findStaffResourcesByStaffId(Long StaffId);

    List<StaffResource> findStaffResourcesByVo(StaffResourceDto staffResourceDto);

    /**
     * 删除员工允许禁止的权限
     *
     * @param fkStaffId
     * @return
     */
    void deleteByStaffId(@Param("fkStaffId") Long fkStaffId);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByStaffId(@Param("staffId") Long staffId);


    /**
     *
     * @param staffIds
     * @param key
     * @return
     */
    List<Long> getStaffIdByKey(@Param("staffIds") List<Long> staffIds,@Param("key") String key);
}