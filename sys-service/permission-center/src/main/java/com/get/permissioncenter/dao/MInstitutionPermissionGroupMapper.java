package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.permissioncenter.dto.MInstitutionPermissionGroupSearchDto;
import com.get.permissioncenter.entity.MInstitutionPermissionGroup;
import com.get.permissioncenter.vo.MInstitutionPermissionGroupVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 学校权限组Mapper接口
 */
@Mapper
public interface MInstitutionPermissionGroupMapper extends BaseMapper<MInstitutionPermissionGroup> {

    List<MInstitutionPermissionGroupVo> getInstitutionPermissionGroups(IPage<MInstitutionPermissionGroup> pages,
                                                                       @Param("mInstitutionPermissionGroupSearchDto") MInstitutionPermissionGroupSearchDto mInstitutionPermissionGroupSearchDto,
                                                                       @Param("companyIds") List<Long> companyIds,
                                                                       @Param("staffFollowerIds") List<Long> staffFollowerIds);

    Integer selectViewOrder();

    /**
     * 获取权限组学校Ids
     *
     * @param staffId
     * @return
     */
    List<Long> getPermissionGroupsInstitutionIds(@Param("staffId") Long staffId);
}

