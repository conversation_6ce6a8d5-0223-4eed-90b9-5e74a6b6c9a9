package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.permissioncenter.vo.PositionVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.entity.Position;
import com.get.permissioncenter.dto.PositionDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Mapper
public interface PositionMapper extends BaseMapper<Position> {

    /**
     * 查找排序最大值
     *
     * @param
     * @return
     */
    Integer getMaxViewOrder();


    /**
     * @return java.util.List<com.get.common.com.get.permissioncenter.vo.entity.BaseSelectEntity>
     * @Description: 下拉
     * @Param [companyId, departmentId]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getPositionSelect(@Param("companyId") Long companyId, @Param("departmentId") Long departmentId);

    int insertSelective(Position position);

    /**
     * @return java.util.List<com.get.permissioncenter.com.get.permissioncenter.vo.entity.Position>
     * @Description:
     * @Param [positionDto]
     * <AUTHOR>
     */
    List<Position> getPositions(IPage<Position> iPage, PositionDto positionDto);


    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByDepartment(Long departmentId);


    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByCompanyId(Long companyId);

    /**
     * 通过员工ids 查找对应的职位编号
     *
     * @param ids
     * @return
     */
    List<PositionVo> getPositionNumByIds(@Param("ids") String ids);

    /**
     * 获取职位下的ids
     *
     * @param ids
     * @return
     */
    List<StaffVo> getPositionNumAndStaffIdMap(@Param("ids") Set<Long> ids);
}