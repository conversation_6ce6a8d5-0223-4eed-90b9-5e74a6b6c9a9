<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.StaffOfficeMapper">
    <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.StaffOffice">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId"/>
        <result column="fk_office_id" jdbcType="BIGINT" property="fkOfficeId"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    s.id,  s.fk_staff_id,  s.fk_office_id,  s.gmt_create,  s.gmt_create_user,  s.gmt_modified,  s.gmt_modified_user
    </sql>

    <select id="isExistByStaffId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id FROM  r_staff_office where fk_staff_id =#{staffId}
    </select>

    <select id="isExistByOfficeId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id FROM  r_staff_office where fk_office_id =#{officeId}
    </select>
</mapper>