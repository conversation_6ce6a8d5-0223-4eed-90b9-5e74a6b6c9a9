package com.get.permissioncenter.utils;

import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import sun.misc.BASE64Encoder;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Map;

/**
 * Created by Jerry.
 * User: 14:52
 * Date: 2021/6/11
 * Description:Http链接工具类
 */
public class HttpClientUtils {
    /**
     * 向目的URL发送post请求
     *
     * @param url          目的url
     * @param headerParams 请求头参数	key：value
     * @param bodyParams   请求体参数 	key：value
     * @return
     */
    public static String sendPostRequest(String url, Map<String, String> headerParams,
                                         Map<String, String> bodyParams) {
        RestTemplate client = new RestTemplate();
        //新建Http头，add方法可以添加参数
        HttpHeaders headers = new HttpHeaders();
        //给请求头设置参数
        for (String key : headerParams.keySet()) {
            headers.add(key, headerParams.get(key));
        }
        //设置请求发送方式HttpMethod.GET、HttpMethod.DELETE等
        HttpMethod method = HttpMethod.POST;
        // 设置提交方式这里设置成application/json格式
        headers.setContentType(MediaType.APPLICATION_JSON);
        //将请求头部和参数合成一个请求
        HttpEntity<Map<String, String>> requestEntity = new HttpEntity<>(bodyParams, headers);
        //执行HTTP请求，将返回的结构使用String 类格式化（可设置为对应返回值格式的类）
        ResponseEntity<String> response = client.exchange(url, method, requestEntity, String.class);
        //返回类型也可以自动填充到实体类当中去，比如我自己创建了User类，当然字段名称要和返回字段一致
        return response.getBody();
    }


    /**
     * 从服务器中下载图片并将流转换成base64
     *
     * @param url
     * @param jSessionId
     * @return
     */
    public static String downloadImagesToBase64(String url, String jSessionId) {
        // 创建httpclient实例
        CloseableHttpClient httpclient = HttpClients.createDefault();
        // Http请求
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Cookie", jSessionId);
            CloseableHttpResponse pictureResponse = httpclient.execute(httpPost);

            org.apache.http.HttpEntity pictureEntity = pictureResponse.getEntity();
            InputStream inputStream = pictureEntity.getContent();

            ByteArrayOutputStream outStream = new ByteArrayOutputStream();
            //创建一个Buffer字符串
            byte[] buffer = new byte[1024];
            //每次读取的字符串长度，如果为-1，代表全部读取完毕
            int len = 0;
            //使用一个输入流从buffer里把数据读取出来
            while ((len = inputStream.read(buffer)) != -1) {
                //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
                outStream.write(buffer, 0, len);
            }
            byte[] data = outStream.toByteArray();
            //对字节数组Base64编码
            BASE64Encoder encoder = new BASE64Encoder();
            String base64 = encoder.encode(data);
            return base64;
        } catch (IOException e) {
            throw new RuntimeException(e.getMessage());
        }
    }
}
