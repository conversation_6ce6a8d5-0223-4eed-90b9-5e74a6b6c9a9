<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.PermissionGradeMapper">
    <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.PermissionGrade">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_company_id" jdbcType="BIGINT" property="fkCompanyId"/>
        <result column="grade_num" jdbcType="VARCHAR" property="gradeNum"/>
        <result column="grade_name" jdbcType="VARCHAR" property="gradeName"/>
        <result column="view_order" jdbcType="INTEGER" property="viewOrder"/>
        <result column="grade_level" jdbcType="INTEGER" property="gradeLevel"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <insert id="insert" parameterType="com.get.permissioncenter.entity.PermissionGrade" keyProperty="id"
            useGeneratedKeys="true">
    insert into m_permission_grade (id, fk_company_id, grade_num, 
      grade_name, view_order, grade_level,gmt_create,
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{fkCompanyId,jdbcType=BIGINT}, #{gradeNum,jdbcType=VARCHAR}, 
      #{gradeName,jdbcType=VARCHAR}, #{viewOrder,jdbcType=INTEGER},#{gradeLevel,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP},
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
    <insert id="insertSelective" parameterType="com.get.permissioncenter.entity.PermissionGrade" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_permission_grade
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkCompanyId != null">
                fk_company_id,
            </if>
            <if test="gradeNum != null">
                grade_num,
            </if>
            <if test="gradeName != null">
                grade_name,
            </if>
            <if test="viewOrder != null">
                view_order,
            </if>
            <if test="gradeLevel != null">
                grade_level,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkCompanyId != null">
                #{fkCompanyId,jdbcType=BIGINT},
            </if>
            <if test="gradeNum != null">
                #{gradeNum,jdbcType=VARCHAR},
            </if>
            <if test="gradeName != null">
                #{gradeName,jdbcType=VARCHAR},
            </if>
            <if test="viewOrder != null">
                #{viewOrder,jdbcType=INTEGER},
            </if>
            <if test="gradeLevel != null">
                #{gradeLevel,jdbcType=INTEGER},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>
    <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select ifnull(max(view_order)+1,1) from m_permission_grade
  </select>
    <select id="getMaxId" resultType="java.lang.Long">
    select ifnull(max(id)+1,1) from m_permission_grade
</select>
    <select id="isExistByCompanyId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id FROM m_permission_grade where fk_company_id =#{companyId}
    </select>
</mapper>