package com.get.permissioncenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.utils.AESUtils;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.ConfigMapper;
import com.get.permissioncenter.dto.CompanyConfigInfoDto;
import com.get.permissioncenter.dto.ConfigDto;
import com.get.permissioncenter.vo.CompanyConfigAnalysisVo;
import com.get.permissioncenter.vo.CompanyConfigValueVo;
import com.get.permissioncenter.vo.CompanyConfigVo;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import com.get.permissioncenter.entity.Config;
import com.get.permissioncenter.service.ICompanyService;
import com.get.permissioncenter.service.IConfigService;
import com.get.salecenter.feign.ISaleCenterClient;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: jack
 * @create: 2020/6/8
 * @verison: 1.0
 * @description: 系统配置业务实现类
 */
@Service
public class ConfigServiceImpl extends BaseServiceImpl<ConfigMapper, Config> implements IConfigService {
    @Resource
    private ConfigMapper configMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private ICompanyService companyService;
    @Resource
    private ISaleCenterClient saleCenterClient;

    @Override
    public ConfigVo findConfigById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Config config = configMapper.selectById(id);
        if (GeneralTool.isEmpty(config)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        ConfigVo configVo = BeanCopyUtils.objClone(config, ConfigVo::new);
        return configVo;
    }

    @Override
    public List<ConfigVo> getConfigs(ConfigDto configDto, Page page) {
//        Example example = new Example(Config.class);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        if (GeneralTool.isNotEmpty(configDto)) {
//            if (GeneralTool.isNotEmpty(configDto.getConfigGroup())) {
//                criteria.andEqualTo("configGroup", configDto.getConfigGroup());
//            }
//            if (GeneralTool.isNotEmpty(configDto.getKeyWord())) {
//                criteria.andLike("configKey", "%" + configDto.getKeyWord().trim() + "%");
//            }
//            if (GeneralTool.isNotEmpty(configDto.getConfigGroup())) {
//                criteria1.andEqualTo("configGroup", configDto.getConfigGroup());
//
//            }
//            if (GeneralTool.isNotEmpty(configDto.getKeyWord())) {
//                criteria1.andLike("description", "%" + configDto.getKeyWord().trim() + "%");
//            }
//            configDto.setConfigGroup(null);
//            configDto.setKeyWord(null);
//            example.or(criteria1);
//            example.orderBy("configGroup").orderBy("configKey");
//        }
//        //获取分页数据
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<Config> configs = configMapper.selectByExample(example);
//        PageInfo<Config> pageInfo = new PageInfo<Config>(configs);
//        page.setTotalResult(new Long(pageInfo.getTotal()).intValue());

        LambdaQueryWrapper<Config> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(configDto)) {
            if (GeneralTool.isNotEmpty(configDto.getConfigGroup())) {
                wrapper.eq(Config::getConfigGroup, configDto.getConfigGroup());
            }
            if (GeneralTool.isNotEmpty(configDto.getKeyWord())) {
                wrapper.and(wrapper_ ->
                        wrapper_.like(Config::getConfigKey, configDto.getKeyWord().trim()).or()
                                .like(Config::getDescription, configDto.getKeyWord().trim()));
            }
            configDto.setConfigGroup(null);
            configDto.setKeyWord(null);
            wrapper.orderByAsc(Config::getConfigGroup, Config::getConfigKey);
        }
        IPage<Config> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<Config> comments = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<ConfigVo> configVos = BeanCopyUtils.copyListProperties(comments, ConfigVo::new);
        return configVos;
    }


    @Override
    public ConfigVo updateConfig(ConfigDto configDto) {
        if (configDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        if (GeneralTool.isEmpty(configDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Config cf = configMapper.selectById(configDto.getId());
        if (cf == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Config config = BeanCopyUtils.objClone(configDto, Config::new);
        if (validateUpdate(configDto)) {
            utilService.updateUserInfoToEntity(config);
            configMapper.updateById(config);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        //当修改成功并且修改的是汇率时间的时候,重新运行获取汇率定时器 111111 修改为job
//        if (ProjectKeyEnum.GET_EXCHANGE_RATE_TIME.key.equals(configDto.getConfigKey()) && !cf.getValue1().equals(configDto.getValue1())) {
//            feginScheduleService.setExchangeRateTime();
//        }
        return findConfigById(config.getId());
    }

    @Override
    public Long addConfig(ConfigDto configDto) {
        Config config = BeanCopyUtils.objClone(configDto, Config::new);
        if (validateAdd(configDto)) {
            utilService.updateUserInfoToEntity(config);
            configMapper.insert(config);
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("record_exist"));
        }
        return config.getId();
    }

    @Override
    public String getConfigValueByConfigKey(String key) {
        if (GeneralTool.isEmpty(key)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
//        Example example = new Example(Config.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("configKey", key);
//        List<Config> list = this.configMapper.selectByExample(example);
        List<Config> list = this.configMapper.selectList(Wrappers.<Config>query().lambda().eq(Config::getConfigKey, key));
        return GeneralTool.isEmpty(list) ? "" : list.get(0).getValue1();
    }


    /**
     * @Description: 配置key和value1查询对象
     * @Author: Jerry
     * @Date:17:56 2021/9/15
     */
    @Override
    public ConfigVo getConfigValueByConfigKeyAndValue(String key, Long value1) {
        List<Config> list = this.configMapper.getConfigValueByConfigKeyAndValue(key, value1);
        if (GeneralTool.isEmpty(list)) {
            return null;
        }
        return BeanCopyUtils.objClone(list.get(0), ConfigVo::new);
    }

    /**
     * @Description: 配置key查询value（AES加密key，需解密）
     * @Author: Jerry
     * @Date:12:10 2021/9/15
     */
    @Override
    public String getConfigValueByConfigKeyAndAESKey(String key, String AESKey) {
        String value = "";
        if (GeneralTool.isEmpty(key)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
//        Example example = new Example(Config.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("configKey", key);
//        List<Config> list = this.configMapper.selectByExample(example);
//        if(GeneralTool.isEmpty(list)){
//            return value;
//        }
        List<Config> list = this.configMapper.selectList(Wrappers.<Config>query().lambda().eq(Config::getConfigKey, key));
        try {
            value = AESUtils.Decrypt(list.get(0).getValue1(), AESKey);
        } catch (Exception e) {
            throw new GetServiceException(e.getMessage());
        }

        return value;
    }

    @Override
    public List<String> getGroupSelect() {
        return configMapper.getGroupSelect();
    }

    private boolean validateAdd(ConfigDto configDto) {
//        Example example = new Example(Config.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("configKey", configDto.getConfigKey());
//        List<Config> list = this.configMapper.selectByExample(example);
        List<Config> list = this.configMapper.selectList(Wrappers.<Config>query().lambda().eq(Config::getConfigKey, configDto.getConfigKey()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(ConfigDto configDto) {
//        Example example = new Example(Config.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("configKey", configDto.getConfigKey());
//        List<Config> list = this.configMapper.selectByExample(example);
        List<Config> list = this.configMapper.selectList(Wrappers.<Config>query().lambda().eq(Config::getConfigKey, configDto.getConfigKey()));
        return list.size() <= 0 || list.get(0).getId().equals(configDto.getId());
    }

    @Override
    public void delete(Long id) {
        //TODO 改过
//        Config config = findConfigById(id);
        ConfigVo config = findConfigById(id);
        if (config == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        int i = configMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    /**
     * feign调用 查询配置信息
     *
     * @Date 14:40 2021/8/16
     * <AUTHOR>
     */
    @Override
    public ConfigVo getConfigByKey(String key) {
//        Example example = new Example(Config.class);
//        example.createCriteria().andEqualTo("configKey", key);
//        List<Config> configs = configMapper.selectByExample(example);
//        if (GeneralTool.isNotEmpty(configs)) {
//            return Tools.objClone(configs.get(0), com.get.common.entity.fegin.ConfigVo.class);
//        }
        List<Config> list = this.configMapper.selectList(Wrappers.<Config>query().lambda().eq(Config::getConfigKey, key));
        if (CollectionUtil.isNotEmpty(list)) {
            return BeanCopyUtils.objClone(list.get(0), ConfigVo::new);
        }
        return null;
    }

    /**
     * Author Cream
     * Description : //获取公司业务配置信息
     * Date 2023/1/3 16:30
     * Params: column value1 OR value2
     * Return
     */
    @Override
    public CompanyConfigVo getCompanyConfigInfo(String key, Integer column){
        Config config = configMapper.selectOne(Wrappers.<Config>query().lambda().eq(Config::getConfigKey, key));
        JSONObject jsonObject = null;
        if (column==1) {
            jsonObject = JSON.parseObject(config.getValue1());
        }
        if (column == 2){
            jsonObject = JSON.parseObject(config.getValue2());
        }
        if (jsonObject!=null) {
            String iae = jsonObject.getString("IAE");
            String other = jsonObject.getString("OTHER");
            return new CompanyConfigVo(iae, other);
        }
        return new CompanyConfigVo();
    }

    @Override
    public ListResponseBo<CompanyConfigInfoDto> getCompanySettlementConfigInfo(String configKey) {
        if (StringUtils.isBlank(configKey)) {
            return new ListResponseBo<>(Collections.emptyList());
        }
        ConfigVo configVo = getConfigByKey(configKey);
        if (configVo ==null || StringUtils.isBlank(configVo.getValue1())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("MISSING_RELATED_BUSINESS_CONFIGURATION"));
        }
        //{"GEA":0,"IAE":0,"SEA":1,"SEAMYS":1,"SEAVNM":1,"OTHER":0}
        JSONObject jsonObject = JSON.parseObject(configVo.getValue1());
        List<CompanyTreeVo> data = companyService.getAllCompanyDto();
        List<CompanyConfigInfoDto> configInfoVos = new ArrayList<>();
        for (CompanyTreeVo datum : data) {
            CompanyConfigInfoDto companyConfigInfoDto = new CompanyConfigInfoDto();
            companyConfigInfoDto.setCompanyId(datum.getId());
            String companyString = jsonObject.getString(datum.getNum());
            if (GeneralTool.isNotEmpty(companyString)){
                companyConfigInfoDto.setValue(Integer.parseInt(companyString));
            } else if (datum.getId()==3 && GeneralTool.isNotEmpty(jsonObject.getString("IAE"))) {
                companyConfigInfoDto.setValue(Integer.parseInt(jsonObject.getString("IAE")));
            } else {
                companyConfigInfoDto.setValue(Integer.parseInt(jsonObject.getString("OTHER")));
            }


            if (GeneralTool.isNotEmpty(configVo.getValue2())) {
                if (isValidJsonFormat(configVo.getValue2())) {
                    String value = getConfigByCompany(datum, configVo.getValue2());
                    companyConfigInfoDto.setValue2(value);
                } else {
                    companyConfigInfoDto.setValue2(configVo.getValue2());
                }
            }

            if (GeneralTool.isNotEmpty(configVo.getValue3())) {
                if (isValidJsonFormat(configVo.getValue3())) {
                    String value = getConfigByCompany(datum, configVo.getValue3());
                    companyConfigInfoDto.setValue3(value);
                } else {
                    companyConfigInfoDto.setValue3(configVo.getValue3());
                }
            }

            if (GeneralTool.isNotEmpty(configVo.getValue4())) {
                if (isValidJsonFormat(configVo.getValue4())) {
                    String value = getConfigByCompany(datum, configVo.getValue4());
                    companyConfigInfoDto.setValue4(value);
                } else {
                    companyConfigInfoDto.setValue4(configVo.getValue4());
                }
            }


            configInfoVos.add(companyConfigInfoDto);
        }
        return new ListResponseBo<>(configInfoVos);
    }

    /**
     * 获取公司配置
     *
     * @Date 16:35 2024/1/17
     * <AUTHOR>
     */
    private String getConfigByCompany(CompanyTreeVo companyTreeVo, String value) {
        JSONObject jsonObject = JSON.parseObject(value);
        String companyString = jsonObject.getString(companyTreeVo.getNum());
        if (GeneralTool.isNotEmpty(companyString)){
            return companyString;
        } else if (companyTreeVo.getId()==3 && GeneralTool.isNotEmpty(jsonObject.getString("IAE"))) {
            return jsonObject.getString("IAE");
        } else {
            return jsonObject.getString("OTHER");
        }
    }

    /**
     * 检查是否为json
     *
     * @Date 16:38 2024/1/17
     * <AUTHOR>
     */
    private boolean isValidJsonFormat(String str) {
        ObjectMapper objectMapper = new ObjectMapper();
        try {
            objectMapper.readTree(str);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }

    @Override
    public CompanyConfigValueVo getProjectLimitConfigKey(String configKey) {
        CompanyConfigValueVo companyConfigValueVo = new CompanyConfigValueVo();

        Map<Long, CompanyConfigAnalysisVo> companyConfigAnalysis = companyService.getCompanyConfigAnalysis(configKey);
        CompanyConfigAnalysisVo companyConfigAnalysisVo = companyConfigAnalysis.get(SecureUtil.getFkCompanyId());
        if (GeneralTool.isNotEmpty(companyConfigAnalysisVo) && companyConfigAnalysisVo.getValue1().equals("1")){
            companyConfigValueVo.setLimitStepString(saleCenterClient.getItemStepSelectByStepKey(JSONArray.parseArray(companyConfigAnalysisVo.getValue2(), String.class)).getData());

        }
        return companyConfigValueVo;
//
//        List<CompanyConfigInfoDto> value1 = getCompanySettlementConfigInfo(configKey).getDatas();
//        ConfigVo configDto = getConfigByKey(configKey);
//        if (configDto==null || StringUtils.isBlank(configDto.getValue2())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("缺少相关业务配置"));
//        }
//        companyConfigValueVo.setValue1(value1);
//        JSONObject jsonObject = JSON.parseObject(configDto.getValue2());
//        String iaeValue = jsonObject.getString("IAE");
//        List<String> stepString = JSONArray.parseArray(iaeValue, String.class);
//        List<BaseSelectEntity> itemStep = saleCenterClient.getItemStepSelect().getData();
//        Set<Long> ids = itemStep.stream().filter(i -> stepString.contains(i.getNum())).map(BaseSelectEntity::getId).collect(Collectors.toSet());
//        companyConfigValueVo.setValue2(ids);
////        Set<String> stepNames = itemStep.stream().filter(i -> stepString.contains(i.getNum())).map(BaseSelectEntity::getName).collect(Collectors.toSet());
//        companyConfigValueVo.setLimitStepString(saleCenterClient.getItemStepSelectByStepKey(stepString).getData());
//        return null;
    }

    @Override
    public Map<Long, Integer> getCompanySettlementConfigInfoMap(String configKey) {
        List<CompanyConfigInfoDto> datas = getCompanySettlementConfigInfo(configKey).getDatas();
        return datas.stream().collect(Collectors.toMap(CompanyConfigInfoDto::getCompanyId, CompanyConfigInfoDto::getValue));
    }
}
