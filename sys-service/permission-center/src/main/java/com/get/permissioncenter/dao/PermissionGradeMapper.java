package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.entity.PermissionGrade;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PermissionGradeMapper extends BaseMapper<PermissionGrade> {
    int insert(PermissionGrade record);

    int insertSelective(PermissionGrade record);

    /**
     * @return java.lang.Integer
     * @Description: 获取最大的排序
     * <AUTHOR>
     */
    Integer getMaxViewOrder();


    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByCompanyId(Long companyId);

    /**
     * @return java.lang.Long
     * @Description: 获取最大的id
     * <AUTHOR>
     */
    Long getMaxId();
}