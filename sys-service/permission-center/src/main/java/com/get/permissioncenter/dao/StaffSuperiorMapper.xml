<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.StaffSuperiorMapper">
    <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.StaffSuperior">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId"/>
        <result column="fk_staff_superior_id" jdbcType="BIGINT" property="fkStaffSuperiorId"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>

    <resultMap id="StaffSuperior" type="com.get.permissioncenter.vo.StaffSuperiorVo">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="depart_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="position_name" jdbcType="VARCHAR" property="positionName"/>
        <result column="num" jdbcType="VARCHAR" property="num"/>
    </resultMap>

    <resultMap id="StaffSuperiorTree" type="com.get.permissioncenter.vo.tree.StaffSuperiorTreeVo">
        <id column="company_id" jdbcType="BIGINT" property="companyId"/>
        <result column="company_name" jdbcType="VARCHAR" property="companyName"/>
        <result column="company_num" jdbcType="VARCHAR" property="companyNum"/>
        <result column="fk_parent_company_id" jdbcType="BIGINT" property="fkParentCompanyId"/>
        <result column="depart_name" jdbcType="VARCHAR" property="departmentName"/>
        <result column="position_name" jdbcType="VARCHAR" property="positionName"/>
        <result column="depart_id" jdbcType="BIGINT" property="departmentId"/>
        <result column="position_id" jdbcType="BIGINT" property="positionId"/>
        <result column="staff_name" jdbcType="VARCHAR" property="staffName"/>
        <result column="staff_num" jdbcType="VARCHAR" property="staffNum"/>
        <result column="staff_id" jdbcType="BIGINT" property="staffId"/>
    </resultMap>


    <sql id="Base_Column_List">
    id, fk_staff_id, fk_staff_superior_id, gmt_create, gmt_create_user, gmt_modified, 
    gmt_modified_user
    </sql>

    <select id="getStaffSuperior" resultMap="StaffSuperior">
        SELECT S.ID,s.fk_staff_superior_id,M.NAME,M.NUM,Y.SHORT_NAME COMPANY_NAME,D.NAME DEPART_NAME,P.NAME POSITION_NAME FROM  M_STAFF M
        LEFT JOIN R_STAFF_SUPERIOR S ON M.ID=S.FK_STAFF_SUPERIOR_ID
        LEFT JOIN M_COMPANY Y ON M.FK_COMPANY_ID=Y.ID
        LEFT JOIN M_DEPARTMENT D ON M.FK_DEPARTMENT_ID=D.ID
        LEFT JOIN M_POSITION P ON M.FK_POSITION_ID=P.ID
        WHERE S.FK_STAFF_ID=#{staffId}
    </select>

    <select id="getStaffFollower" resultType="com.get.permissioncenter.vo.StaffFollowerVo">
        SELECT k.id,k.level,k.followerName,k.leaderName,o1.short_name followerCompanyName,o2.short_name
        leaderCompanyName from(
        SELECT
        s.id,s.level,m1.name followerName,m2.name leaderName,m1.fk_company_id followeCompanyId,m2.fk_company_id
        leaderCompanyId
        from(
        SELECT
        max(level) level,max(id) id, fk_staff_id,fk_staff_superior_id
        FROM(
        SELECT
        @ids AS _ids,
        (SELECT @ids := GROUP_CONCAT(fk_staff_id)
        FROM r_staff_superior
        WHERE FIND_IN_SET(fk_staff_superior_id, @ids)
        ) AS cids,
        @l := @l+1 AS LEVEL
        FROM r_staff_superior,
        (SELECT @ids :=#{staffId}, @l := 0 ) b
        WHERE @ids IS NOT NULL
        ) id, r_staff_superior DATA
        WHERE FIND_IN_SET(DATA.fk_staff_superior_id,id._ids)
        GROUP BY fk_staff_id,fk_staff_superior_id
        ORDER BY level,fk_staff_superior_id
        )s
        left join M_STAFF M1 on M1.ID=S.FK_STAFF_ID
        left join M_STAFF M2 on M2.ID=S.fk_staff_superior_id
        ) k
        LEFT join m_company o1 on k.followeCompanyId =o1.id
        LEFT join m_company o2 on k.leaderCompanyId =o2.id
        <where>
            <if test="keyWord!=null and keyWord !=''">
                and position(#{keyWord,jdbcType=VARCHAR} in k.followerName)
            </if>
            <if test="keyWord!=null and keyWord !=''">
                or position(#{keyWord,jdbcType=VARCHAR} in k.leaderName)
            </if>
        </where>

    </select>

    <select id="getStaffSuperiorTree" resultMap="StaffSuperiorTree">
      SELECT y.id company_id,y.name company_name,y.num company_num,y.fk_parent_company_id,
      d.name depart_name,d.id depart_id,p.name position_name,p.id position_id,
      m.name staff_name,m.num staff_num,m.id staff_id FROM  m_staff m
      left JOIN r_staff_superior s on m.id=s.fk_staff_superior_id
      left join m_company y on m.fk_company_id=y.id
      left join m_department d on m.fk_department_id=d.id
      left join m_position p on m.fk_position_id=p.id
      where s.fk_staff_id=#{staffId}
      union all
      SELECT  y.id company_id,y.name company_name,y.num company_num,y.fk_parent_company_id,
      null,null,null,null,null,null,null
      FROM (
      SELECT
      @r AS _id,
      (SELECT @r := fk_parent_company_id FROM m_company WHERE id = _id) AS v2,
      @l := @l + 1 AS vl
      FROM
      (SELECT @r := #{companyId}) vars,
      m_company h
      WHERE @r &lt;&gt; 0) T1
      JOIN m_company y
      ON T1._id = y.id

    </select>

    <select id="getSuperiorParentCompanyIds" resultType="java.lang.Long">
    select
    distinct y.fk_parent_company_id grand_company_id
    from  m_staff m
    left join r_staff_superior s on m.id=s.fk_staff_superior_id
    left join m_company y on m.fk_company_id=y.id
    where s.fk_staff_id=#{staffId}
    </select>

    <select id="isExistByStaffId" resultType="java.lang.Boolean">
        SELECT IFNULL(max(id),0) id FROM  r_staff_superior where fk_staff_superior_id =#{staffId}
    </select>

    <select id="getStaffFollowerId" resultType="java.lang.Long">
        SELECT
            fk_staff_id
        FROM(
            SELECT
            @ids AS _ids,
            (SELECT @ids := GROUP_CONCAT(fk_staff_id)
            FROM r_staff_superior
            WHERE FIND_IN_SET(fk_staff_superior_id, @ids)
            ) AS cids,
            @l := @l+1 AS LEVEL
            FROM r_staff_superior,
            (SELECT @ids :=#{staffId}, @l := 0 ) b
            WHERE @ids IS NOT NULL
            ) id, r_staff_superior DATA
        WHERE FIND_IN_SET(DATA.fk_staff_superior_id,id._ids)
        GROUP BY fk_staff_id,fk_staff_superior_id
        ORDER BY fk_staff_id
    </select>

    <select id="getAllFollowerIds" resultType="java.lang.Long">
        SELECT
            DISTINCT fk_staff_id
        FROM(
                SELECT
                    @ids AS _ids,
            (SELECT @ids := GROUP_CONCAT(fk_staff_id)
            FROM r_staff_superior
            WHERE FIND_IN_SET(fk_staff_superior_id, @ids)
            ) AS cids,
            @l := @l+1 AS LEVEL
                FROM r_staff_superior,
                    (SELECT @ids :=#{followerIds}, @l := 0 ) b
                WHERE @ids IS NOT NULL
            ) id, r_staff_superior DATA
        WHERE FIND_IN_SET(DATA.fk_staff_superior_id,id._ids)
        GROUP BY fk_staff_id,fk_staff_superior_id
        ORDER BY fk_staff_id
    </select>
    <select id="getStaffFollowerSelect" resultType="com.get.core.mybatis.base.BaseSelectEntity">
    SELECT m.id,m.name as nameChn,m.name_en as name,
    CASE WHEN IFNULL( m.name_en, '' )= ''
    THEN m.name
    ELSE CONCAT( m.name, '（', m.name_en, '）' )
                END fullName
    FROM(
     SELECT DISTINCT fk_staff_id
       FROM (
            SELECT @ids AS _ids,
            (SELECT @ids := GROUP_CONCAT(fk_staff_id)
            FROM r_staff_superior
            WHERE FIND_IN_SET(fk_staff_superior_id, @ids)
            ) AS cids, @l := @l+1 AS LEVEL
            FROM r_staff_superior,
            (SELECT @ids :=#{staffId}, @l := 0 ) b WHERE @ids IS NOT NULL
            ) id, r_staff_superior DATA
			 WHERE FIND_IN_SET(DATA.fk_staff_superior_id,id._ids)
        GROUP BY fk_staff_id,fk_staff_superior_id
        ORDER BY fk_staff_id
		) s LEFT JOIN m_staff m ON m.id=s.fk_staff_id
		where m.fk_company_id=#{companyId}
    </select>

    <select id="getStaffSuperiorByIds" resultType="com.get.permissioncenter.vo.StaffSuperiorVo">
        SELECT
        fk_staff_id,
        fk_staff_superior_id
        FROM
        `r_staff_superior`
        WHERE
        fk_staff_id IN
        <foreach collection="staffIds" item="staffId" index="index" open="(" separator="," close=")">
            #{staffIds}
        </foreach>
    </select>

    <select id="getBusinessSubordinatesIds" resultType="java.lang.Long">
        SELECT
        rss.fk_staff_id
        FROM r_staff_superior rss
        WHERE
        rss.fk_staff_superior_id=#{staffId}
    </select>
</mapper>