<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.ResourceMapper">
  <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.Resource">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_parent_resource_id" jdbcType="BIGINT" property="fkParentResourceId" />
    <result column="resource_key" jdbcType="VARCHAR" property="resourceKey" />
    <result column="resource_name" jdbcType="VARCHAR" property="resourceName" />
    <result column="api_key" jdbcType="VARCHAR" property="apiKey" />
    <result column="is_menu" jdbcType="BIT" property="isMenu" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.permissioncenter.entity.Resource" keyProperty="id" useGeneratedKeys="true">
    insert into m_resource (id, fk_parent_resource_id, resource_key, 
      resource_name, api_key, is_menu, 
      view_order, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkParentResourceId,jdbcType=BIGINT}, #{resourceKey,jdbcType=VARCHAR}, 
      #{resourceName,jdbcType=VARCHAR}, #{apiKey,jdbcType=VARCHAR}, #{isMenu,jdbcType=BIT}, 
      #{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.permissioncenter.entity.Resource" keyProperty="id" useGeneratedKeys="true">
    insert into m_resource
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkParentResourceId != null">
        fk_parent_resource_id,
      </if>
      <if test="resourceKey != null">
        resource_key,
      </if>
      <if test="resourceName != null">
        resource_name,
      </if>
      <if test="apiKey != null">
        api_key,
      </if>
      <if test="isMenu != null">
        is_menu,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkParentResourceId != null">
        #{fkParentResourceId,jdbcType=BIGINT},
      </if>
      <if test="resourceKey != null">
        #{resourceKey,jdbcType=VARCHAR},
      </if>
      <if test="resourceName != null">
        #{resourceName,jdbcType=VARCHAR},
      </if>
      <if test="apiKey != null">
        #{apiKey,jdbcType=VARCHAR},
      </if>
      <if test="isMenu != null">
        #{isMenu,jdbcType=BIT},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="getResources" resultMap="BaseResultMap">
    select * from m_resource where 1=1
    <if test="keyWord != null">
     and (resource_key like concat("%",#{keyWord},"%") or resource_name like concat("%",#{keyWord},"%") or api_key like concat("%",#{keyWord},"%"))
    </if>
  </select>

  <select id="getApiKeysByResourceKeys" resultType="java.lang.String">
    select distinct api_key from m_resource where 1=1
    <if test="resourcekeys != null and resourcekeys.size()>0">
      AND resource_key IN
      <foreach collection="resourcekeys" item="resourcekey" index="index" open="(" separator="," close=")">
        #{resourcekey,jdbcType=VARCHAR}
      </foreach>
    </if>
  </select>

  <select id="getTopLevelResources" resultType="com.get.permissioncenter.vo.ResourceVo">
    SELECT
        *
    FROM
        m_resource
    WHERE
        fk_parent_resource_id = 0
        <if test="fkCompanyId!=null">
          AND
          IF (
          fk_company_ids IS NULL
          OR fk_company_ids = '',
          1 = 1,
          FIND_IN_SET(#{fkCompanyId}, fk_company_ids)
          )
        </if>
    ORDER BY
	view_order
  </select>
  <select id="getChildResources" resultType="com.get.permissioncenter.vo.ResourceVo">
    select * from m_resource where fk_parent_resource_id = #{id}
    <if test="fkCompanyId!=null">
      AND
      IF (
      fk_company_ids IS NULL
      OR fk_company_ids = '',
      1 = 1,
      FIND_IN_SET(#{fkCompanyId}, fk_company_ids)
      )
    </if>
     order by view_order
  </select>
  <select id="getMaxViewOrder" resultType="java.lang.Integer">
    select ifnull(max(view_order)+1,1) from m_resource
  </select>
</mapper>