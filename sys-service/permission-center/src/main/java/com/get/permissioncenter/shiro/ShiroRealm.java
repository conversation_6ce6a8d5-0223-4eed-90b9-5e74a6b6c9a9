//package com.get.permissioncenter.shiro;
//
//import com.get.common.com.get.permissioncenter.vo.entity.StaffContext;
//import com.get.common.com.get.permissioncenter.vo.entity.fegin.StaffVo;
//import com.get.common.eunms.ErrorCodeEnum;
//import com.get.common.exception.YException;
//import com.get.common.result.ResponseBo;
//import com.get.core.tool.utils.GeneralTool;
//import com.get.common.utils.Tools;
//import com.get.permissioncenter.dao.PermissionGroupGradeResourceMapper;
//import com.get.permissioncenter.dao.StaffAreaCountryMapper;
//import com.get.permissioncenter.dao.StaffMapper;
//import com.get.permissioncenter.dao.StaffResourceMapper;
//import com.get.permissioncenter.feign.FeignInstitutionService;
//import com.get.permissioncenter.service.ICompanyService;
//import com.get.permissioncenter.service.IGroupGradeResourceService;
//import org.apache.shiro.SecurityUtils;
//import org.apache.shiro.authc.*;
//import org.apache.shiro.authz.AuthorizationInfo;
//import org.apache.shiro.authz.SimpleAuthorizationInfo;
//import org.apache.shiro.realm.AuthorizingRealm;
//import org.apache.shiro.subject.PrincipalCollection;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Set;
//
///**
// * 自定义实现 ShiroRealm，包含认证和授权两大模块
// */
//@Component("shiroRealm")
//public class ShiroRealm extends AuthorizingRealm {
//    @Resource
//    private StaffMapper staffMapper;
//    @Resource
//    private PermissionGroupGradeResourceMapper permissionGroupGradeResourceMapper;
//    @Resource
//    private StaffResourceMapper staffResourceMapper;
//    @Resource
//    private IGroupGradeResourceService groupGradeResourceService;
//    @Resource
//    private StaffAreaCountryMapper staffAreaCountryMapper;
//    @Resource
//    private FeignInstitutionService feignInstitutionService;
//    @Resource
//    private ICompanyService companyService;
//
//    /**
//     * 授权模块，获取用户角色和权限
//     *
//     * @param principal principal
//     * @return AuthorizationInfo 权限信息
//     */
//    @Override
//    protected AuthorizationInfo doGetAuthorizationInfo(PrincipalCollection principal) {
//        System.out.println("进入用户授权");
//        Staff staff = (Staff) SecurityUtils.getSubject().getPrincipal();
//        String loginId = staff.getLoginId();
//        SimpleAuthorizationInfo simpleAuthorizationInfo = new SimpleAuthorizationInfo();
//        Long staffId = staffMapper.findStaffIdByLoginId(loginId);
//        if (staffId == null) {
//            throw new IncorrectCredentialsException(ErrorCodeEnum.VERIFY_FAILED.getMessage());
//        }
//        Set<String> apikeys = groupGradeResourceService.getStaffApiKeys(staffId);
//        simpleAuthorizationInfo.setStringPermissions(apikeys);
//        return simpleAuthorizationInfo;
////        return null;
//    }
//
//    /**
//     * 用户认证
//     *
//     * @param token AuthenticationToken 身份认证 token
//     * @return AuthenticationInfo 身份认证信息
//     */
//    @Override
//    protected AuthenticationInfo doGetAuthenticationInfo(AuthenticationToken token) throws AuthenticationException {
//        System.out.println("进入用户登录");
//        // 获取用户输入的用户名和密码
//        String userName = (String) token.getPrincipal();
//        Object o = token.getCredentials();
//        String password = GeneralTool.isEmpty(o) ? "" : (new String((char[]) token.getCredentials()));
//    /*    Example example = new Example(Staff.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(userName)) {
//            criteria.andEqualTo("loginId", userName);
//        }
//        if (GeneralTool.isNotEmpty(password)) {
//            criteria.andEqualTo("loginPs", password);
//        }*/
//        List<Staff> staffs = staffMapper.selectStaffByIdPs(userName,password);
//        if (staffs.size() == 0) {
//            throw new IncorrectCredentialsException("用户名或密码错误");
//        }
//        Staff staff = staffs.get(0);
//        if(GeneralTool.isEmpty(staff.getIsActive())||!staff.getIsActive()){
//            throw new IncorrectCredentialsException("用户没激活");
//        }
//        //获取员工apikey
//        Set<String> apikeys = groupGradeResourceService.getStaffApiKeys(staff.getId());
//        if (GeneralTool.isEmpty(apikeys)) {
//            apikeys.add(null);
//        }
//        //获取员工资源权限
//        List<String> resourcekeys = groupGradeResourceService.getStaffResourceKeys(staff.getId());
//        StaffVo staffDto = Tools.objClone(staff, StaffVo.class);
//        staffDto.setApiKeys(apikeys);
//        staffDto.setResourceKeys(resourcekeys);
//        List<String> keys = new ArrayList<>();
//        List<StaffAreaCountry> staffAreaCountrys = staffAreaCountryMapper.getStaffAreaCountrysByfkStaffId(staffDto.getId());
//        for (StaffAreaCountry staffAreaCountry : staffAreaCountrys) {
//            keys.add(staffAreaCountry.getFkAreaCountryKey());
//        }
//        try {
//            ResponseBo responseBo = feignInstitutionService.getCountryIdByKey(keys);
//            List<Long> ids = (List<Long>) responseBo.getData();
//            staffDto.setCountryIds(ids);
//        } catch (YException e) {
//            throw new IncorrectCredentialsException("服务调用异常");
//        }
//        if (GeneralTool.isNotEmpty(staffDto.getFkCompanyId())) {
//            try {
//                staffDto.setCompanyIds(companyService.getChildCompany(staffDto.getFkCompanyId()));
//            } catch (YException e) {
//                throw new IncorrectCredentialsException("获取员工公司子公司出错");
//            }
//        }
//        StaffContext.staffRemove();
//        StaffContext.setStaff(staffDto);
//        return new SimpleAuthenticationInfo(staffDto, password, getName());
//    }
//
//    /**
//     * 清除权限缓存
//     * 使用方法：在需要清除用户权限的地方注入 ShiroRealm,
//     * 然后调用其clearCache方法。
//     */
//    public void clearCache() {
//        PrincipalCollection principals = SecurityUtils.getSubject().getPrincipals();
//        super.clearCache(principals);
//    }
//
//}
