package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.permissioncenter.dto.StaffDto;
import com.get.permissioncenter.dto.query.StaffQueryDto;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.vo.DepartmentAndStaffVo;
import com.get.permissioncenter.vo.ResetPasswordTemplateVo;
import com.get.permissioncenter.vo.StaffExportVo;
import com.get.permissioncenter.vo.StaffListByDepartmentVo;
import com.get.permissioncenter.vo.StaffVo;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper
public interface StaffMapper extends GetMapper<Staff> {

    Long findStaffIdByLoginId(@Param("loginId") String loginId);

    String getStaffNameById(Long id);


    /**
     * 获取员工导出信息
     * @param staffDto
     * @return
     */
    List<StaffExportVo> getExportInfo(@Param("staffDto") StaffDto staffDto);

    /**
     * 根据员工ids获取员工LoginIds
     *
     * @Date 18:33 2021/6/24
     * <AUTHOR>
     */
    String getStaffLoginIdByIds(@Param("ids") Set<Long> ids);

    /**
     * 员工下拉框数据
     *
     * @return
     */
    List<BaseSelectEntity> getStaffList(@Param("fkCompanyIds") List<Long> fkCompanyIds,
                                        @Param("departNums") List<String> departNums);

    /**
     * 根据部门id获取员工下拉框数据
     *
     * @Date 17:15 2021/7/6
     * <AUTHOR>
     */
    List<BaseSelectEntity> getStaffByDepartmentIds(@Param("departmentIds") List<Long> departmentIds);
    //todo 这三个员工mapper好像可以合并

    /**
     * fei调用根据员工id获取员工下拉框数据
     *
     * @Date 15:02 2021/8/20
     * <AUTHOR>
     */
    List<BaseSelectEntity> getStaffByStaffIds(@Param("staffIds") Set<Long> staffIds);

    /**
     * 根据员工考勤号匹配员工ID
     * @param attendanceNums
     * @return
     */
    List<Staff> getStaffIdsByAttendanceNum(@Param("attendanceNums") Set<String> attendanceNums,@Param("fkCompanyId") Long fkCompanyId);

    /**
     * 根据员工中文以及英文名匹配员工ID
     * @param name
     * @param nameChn
     * @return
     */
    Long getStaffIdByNameAndEnName(@Param("name") String name,@Param("nameChn") String nameChn);

    /**
     * 获取公司员工ids
     *
     * @return
     */
    List<Long> getStaffIdsByCompanyId(@Param("fkCompanyIds") List<Long> fkCompanyIds);


    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByOfficeId(Long officeId);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByPositionId(Long positionId);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByDepartmentId(Long departmentId);


    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByCompanyId(Long companyId);

    /**
     * @return java.lang.String
     * @Description :通过员工id查找对应公司名称
     * @Param [staffId]
     * <AUTHOR>
     */
    String getCompanyNameByStaffId(@Param("staffId") Long staffId);

    /**
     * @return java.lang.String
     * @Description :通过员工ids查找对应公司名称
     * @Param [staffId]
     * <AUTHOR>
     */
    List<StaffVo> getCompanyNameByStaffIds(@Param("staffIds") Set<Long> staffIds);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取部门最高职位员工ids
     * @Param [companyId, departmentId]
     * <AUTHOR>
     */
    List<Long> getStaffIdsByDepartmentIds(@Param("companyId") Long companyId, @Param("departmentId") Long departmentId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取部门最高职位员工ids
     * @Param [companyId, departmentId]
     * <AUTHOR>
     */
    List<Long> getTopPositionStaffIds(@Param("companyId") Long companyId, @Param("departmentId") Long departmentId);

    /**
     * @return java.util.List<java.lang.Long>
     * @Description :获取职位的所有员工ids
     * @Param [companyId, positionIdList]
     * <AUTHOR>
     */
    List<Long> getAllPositionStaffIds(@Param("companyId") Long companyId, @Param("positionIdList") List<Long> positionIdList);

    /**
     * @ Description :根据职位编号查询人
     * @ Param []
     * @ return com.get.permissioncenter.com.get.permissioncenter.vo.StaffVo
     * @ author LEO
     */
    List<Long> getPositionByNum(@Param("num") List<String> num);

    /**
     * @ Description :根据创建用户查询公司id
     * @ Param [createUser]
     * @ return com.get.permissioncenter.com.get.permissioncenter.vo.StaffVo
     * @ author LEO
     */
    StaffVo getCompanyIdByCreateUser(@Param("createUser") String createUser);


    List<StaffVo> getCompanyIdByCreateUsers(@Param("createUsers") Set<String> createUsers);


    List<Staff> selectStaffByIdPs(@Param("loginId") String loginId, @Param("loginPs") String loginPs);


    /**
     * 获取所有的直属下属包括下属的下属的ids
     *
     * @param staffId
     * @return
     */
    List<Long> getAllSubordinateIds(@Param("staffId") String staffId);

    /**
     * @Description: 获取当天生日的员工
     * @Author: Jerry
     * @Date:14:31 2021/12/1
     */
    List<Staff> getDayOfStaffBirthday(@Param("nowDate") Date nowDate);


    /**
     * @Description: 获取行政部的所有人员
     * @Author: Jerry
     * @Date:15:59 2021/12/15
     */
    List<Long> getAdminDepartmentStaff();

    /**
     * 根据职位编号positionNums获取员工ids
     *
     * @param nums
     * @return
     */
    List<Long> getStaffIdsByPositionNums(@Param("nums") Set<String> nums);

    /**
     * 修改工资生效日期
     *
     * @param fkStaffId
     * @param salaryEffectiveDate
     */
    void updateSalaryEffectiveDate(@Param("fkStaffId") Long fkStaffId, @Param("salaryEffectiveDate") Date salaryEffectiveDate);

    /**
     * 模糊查询员工Ids
     *
     * @param keyWord
     * @return
     */
    List<Long> getStaffIdsBykeyWord(@Param("keyWord") String keyWord);

    /**
     * 根据公司ID获取员工下拉
     *
     * @param fkCompanyId
     * @return
     */
    List<BaseSelectEntity> getStaffByCompanyId(@Param("fkCompanyId") Long fkCompanyId);

    /**
     * 根据公司ID获取员工列表
     *
     * @param fkCompanyId
     * @return
     */
    List<Staff> getStaffs(@Param("fkCompanyId") Long fkCompanyId);

    /**
     * 根据公司ID获取员工DTO
     * @param fkCompanyId
     * @return
     */
    List<StaffVo> getStaffDtoByFkCompanyId(@Param("fkCompanyId")Long fkCompanyId);

    /**
     * 根据公司ID或者部门ID获取所有员工
     * @param fkCompanyId
     * @param fkDepartmentId
     * @return
     */
    List<StaffVo> getStaffDtos(@Param("fkCompanyId") Long fkCompanyId,
                               @Param("fkDepartmentId") Long fkDepartmentId,
                               @Param("staffNameKeyOrEnNameKey")String staffNameKeyOrEnNameKey);

    List<StaffVo> getStaffByIds(@Param("staffIds") Set<Long> staffIds);

    /**
     * 根据输入关键字模糊查询员工列表
     *
     * @Date 2:20 2022/03/11
     * <AUTHOR>
     */
    List<Long> getStaffListByStaffName(@Param("staffName") String staffName);

    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByStaffIdSupervisor(@Param("staffId") Long staffId);


    List<DepartmentAndStaffVo> getDepartmentAndStaffDtoByStaffIds(@Param("staffIds") Set<Long> staffIds);

    /**
     * 根据员工ids获取员工信息
     * <AUTHOR>
     * @DateTime 2022/12/6 14:58
     */
    List<StaffVo> getStaffDtoByIds(@Param("staffIds") Set<Long> staffIds);

    /**
     * 根据公司id和部门id返回员工下拉框数据
     *
     * @param companyId
     * @param departmentId
     * @return
     */
    List<StaffListByDepartmentVo> getStaffListByDepartmentId(@Param("companyId")Long companyId, @Param("departmentId")Long departmentId);

    List<BaseSelectEntity> getStaffByStaffName(@Param("companyIds")List<Long> companyIds, @Param("staffName") String staffName);

    StaffVo selectStaffInfoById(Long staffId);

    /**
     * 部门编号获取员工
     * @param nums
     * @return
     */
    List<StaffVo> getStaffDtosByDepartmentNums(@Param("countryId")Long countryId, @Param("nums") Set<String> nums);

    StaffVo getStaffByLoginId(String loginId);

    List<BaseSelectEntity> getStaffByDepartmentIdsAndCountryNum(@Param("departmentIds") List<Long> departmentIds, @Param("fkCountryNum")String fkCountryNum);

    List<BaseSelectEntity> getOnDutyStaffList(@Param("fkCompanyIds") List<Long> companyIds);

    /**
     * 获取拥有resourceKey权限的所有用户ids
     * @param resourceKey
     * @param isContainAdmin
     * @return
     */
    List<Long> getStaffIdsByResourceKey(@Param("resourceKey")String resourceKey, @Param("isContainAdmin")Boolean isContainAdmin);

    /**
     * 获取推送部门人员的邮箱
     * @param fkCountryId
     * @param fkInstitutionId
     * @param departmentList
     * @return
     */
    List<String> getPushDepartmentStaffEmail(@Param("fkCountryId") Long fkCountryId, @Param("fkInstitutionId") Long fkInstitutionId, @Param("departmentList") List<String> departmentList);

    /**
     * 获取指定权限资源key的员工id集合
     *
     * @param resourceKey 权限资源key
     * @return
     */
    List<Long> getAuthorizedStaffIdsByResourceKey(@Param("resourceKey") String resourceKey);

    /**
     * 获取重置密码模板
     * @return
     */
    ResetPasswordTemplateVo getResetPasswordTemplate();

//    IPage<StaffVo> selectStaffPageWithGroupBind(IPage<Staff> iPage, @Param(Constants.WRAPPER) LambdaQueryWrapper<Staff> StaffPageWithGroupBindWrapper,@Param("fkInstitutionPermissionGroupId") Long fkInstitutionPermissionGroupId,@Param("isBind") Integer isBind);
    IPage<StaffVo> selectStaffPageWithGroupBind(IPage<Staff> iPage, @Param("staffVo") StaffQueryDto staffVo,@Param("companyIds") List<Long> companyIds);

    List<Staff> selectStaffByIdsAndCompanyIds(@Param("staffIds") Set<Long> ids,@Param("companyIds") List<Long> companyIds);

    /**
     * 获取直接下级员工id（一层）
     * @param staffId
     * @return
     */
    List<Long> getObtainDirectSubordinatesIds(Long staffId);

    IPage<StaffVo> getStaffDatas(IPage<Staff> iPage, @Param(Constants.WRAPPER) LambdaQueryWrapper<Staff> staffWrapper);
}