<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.PermissionGroupGradeStaffMapper">
  <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.PermissionGroupGradeStaff">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_permission_group_id" jdbcType="BIGINT" property="fkPermissionGroupId" />
    <result column="fk_permission_grade_id" jdbcType="BIGINT" property="fkPermissionGradeId" />
    <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.permissioncenter.entity.PermissionGroupGradeStaff" keyProperty="id" useGeneratedKeys="true">
    insert into r_permission_group_grade_staff (id, fk_permission_group_id, fk_permission_grade_id,
      fk_staff_id, gmt_create, gmt_create_user,
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkPermissionGroupId,jdbcType=BIGINT}, #{fkPermissionGradeId,jdbcType=BIGINT},
      #{fkStaffId,jdbcType=BIGINT}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR},
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>

  <select id="CountByGroupAndGrade" resultType="java.lang.Integer">
    select count(*) from r_permission_group_grade_staff pggs where
      pggs.fk_permission_group_id = #{groupId} and pggs.fk_permission_grade_id = #{gradeId}
  </select>
  <select id="deleteByGroupAndGrade">
    delete from r_permission_group_grade_staff  where
      fk_permission_group_id = #{groupId} and fk_permission_grade_id = #{gradeId}
  </select>

  <select id="selectStaffByGroupAndGrade" resultType="java.lang.Long">
    select pggs.fk_staff_id from r_permission_group_grade_staff pggs where
      pggs.fk_permission_group_id = #{groupId} and pggs.fk_permission_grade_id = #{gradeId}
  </select>
  <select id="isExistByStaffId" resultType="java.lang.Boolean">
    SELECT IFNULL(max(id),0) id FROM  r_permission_group_grade_staff where fk_staff_id =#{staffId}
  </select>
</mapper>