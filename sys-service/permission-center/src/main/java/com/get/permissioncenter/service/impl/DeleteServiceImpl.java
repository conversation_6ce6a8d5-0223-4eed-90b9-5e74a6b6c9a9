package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.permissioncenter.dao.CompanyMapper;
import com.get.permissioncenter.dao.DepartmentMapper;
import com.get.permissioncenter.dao.MediaAndAttachedMapper;
import com.get.permissioncenter.dao.OfficeMapper;
import com.get.permissioncenter.dao.PermissionGradeMapper;
import com.get.permissioncenter.dao.PermissionGroupGradeStaffMapper;
import com.get.permissioncenter.dao.PermissionGroupMapper;
import com.get.permissioncenter.dao.PositionMapper;
import com.get.permissioncenter.dao.StaffAreaCountryMapper;
import com.get.permissioncenter.dao.StaffConfigMapper;
import com.get.permissioncenter.dao.StaffContractMapper;
import com.get.permissioncenter.dao.StaffHrEventMapper;
import com.get.permissioncenter.dao.StaffMapper;
import com.get.permissioncenter.dao.StaffOfficeMapper;
import com.get.permissioncenter.dao.StaffResourceMapper;
import com.get.permissioncenter.dao.StaffSuperiorMapper;
import com.get.permissioncenter.entity.PermissionGroupGradeStaff;
import com.get.permissioncenter.entity.PermissionMediaAndAttached;
import com.get.permissioncenter.service.IDeleteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE: 2020/11/25
 * @TIME: 17:33
 * @Description:
 **/
@Service
public class DeleteServiceImpl implements IDeleteService {
    @Resource
    private StaffSuperiorMapper staffSuperiorMapper;
    @Resource
    private StaffAreaCountryMapper staffAreaCountryMapper;
    @Resource
    private StaffOfficeMapper staffOfficeMapper;
    @Resource
    private StaffContractMapper staffContractMapper;
    @Resource
    private StaffHrEventMapper staffHrEventMapper;
    @Resource
    private StaffMapper staffMapper;
    @Resource
    private PositionMapper positionMapper;
    @Resource
    private CompanyMapper companyMapper;
    @Resource
    private DepartmentMapper departmentMapper;
    @Resource
    private OfficeMapper officeMapper;
    @Resource
    private PermissionGradeMapper permissionGradeMapper;
    @Resource
    private PermissionGroupMapper permissionGroupMapper;
    @Resource
    private PermissionGroupGradeStaffMapper permissionGroupGradeStaffMapper;
    @Resource
    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private StaffConfigMapper staffConfigMapper;
    @Resource
    private StaffResourceMapper staffResourceMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteValidateStaff(Long staffId) {
        if (staffSuperiorMapper.isExistByStaffId(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_followStaff_data_association"));
        }
        if (staffAreaCountryMapper.isExistByStaffId(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_country_data_association"));
        }
        if (staffOfficeMapper.isExistByStaffId(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_office_data_association"));
        }
        if (staffContractMapper.isExistByStaffId(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_contract_data_association"));
        }
        if (staffHrEventMapper.isExistByStaffId(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_event_data_association"));
        }
        if (staffMapper.isExistByStaffIdSupervisor(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_supervisor_data_association"));
        }
        if (staffConfigMapper.isExistByStaffId(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_config_data_association"));
        }
        if (staffResourceMapper.isExistByStaffId(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_resource_data_association"));
        }
        if (permissionGroupGradeStaffMapper.isExistByStaffId(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("staff_group_grade_data_association"));
        }

        //删除相关权限
        deleteStaffPermission(staffId);
        //删除员工附件
        deleteStaffMedia(staffId);

        return true;
    }

    @Override
    public Boolean deleteValidateCompany(Long companyId) {
        if (companyMapper.isExistByCompanyId(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_followCompany_data_association"));
        }
        if (departmentMapper.isExistByCompanyId(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_department_data_association"));
        }
        if (positionMapper.isExistByCompanyId(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_position_data_association"));
        }
        if (officeMapper.isExistByCompanyId(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_office_data_association"));
        }
        if (staffMapper.isExistByCompanyId(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_staff_data_association"));
        }
        if (permissionGradeMapper.isExistByCompanyId(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_permissionGrade_data_association"));
        }
        if (permissionGroupMapper.isExistByCompanyId(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_permissionGroup_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateDepartment(Long departmentId) {
        if (positionMapper.isExistByDepartment(departmentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("department_position_data_association"));
        }
        if (staffMapper.isExistByDepartmentId(departmentId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("department_staff_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidatePosition(Long positionId) {
        if (staffMapper.isExistByPositionId(positionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("position_staff_data_association"));
        }
        return true;
    }

    @Override
    public Boolean deleteValidateOffice(Long officeId) {
        if (staffOfficeMapper.isExistByOfficeId(officeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("office_staffOffice_data_association"));
        }
        if (staffMapper.isExistByOfficeId(officeId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("office_staff_data_association"));
        }
        return true;
    }

    private void deleteStaffPermission(Long staffId) {
//        Example example = new Example(PermissionGroupGradeStaff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", staffId);
//        permissionGroupGradeStaffMapper.deleteByExample(example);
        permissionGroupGradeStaffMapper.delete(Wrappers.<PermissionGroupGradeStaff>query().lambda().eq(PermissionGroupGradeStaff::getFkStaffId, staffId));
    }

    private void deleteStaffMedia(Long staffId) {
//        Example example = new Example(MediaAndAttached.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkTableName", TableEnum.PERMISSION_STAFF.key);
//        criteria.andEqualTo("fkTableId", staffId);
//        mediaAndAttachedMapper.deleteByExample(example);
        mediaAndAttachedMapper.delete(Wrappers.<PermissionMediaAndAttached>query().lambda().eq(PermissionMediaAndAttached::getFkTableName, TableEnum.PERMISSION_STAFF.key).eq(PermissionMediaAndAttached::getFkTableId, staffId));

    }
}
