<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.StaffHrEventMapper">
    <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.StaffHrEvent">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_staff_id" jdbcType="BIGINT" property="fkStaffId"/>
        <result column="event_type" jdbcType="VARCHAR" property="eventType"/>
        <result column="fk_office_id_from" jdbcType="BIGINT" property="fkOfficeIdFrom"/>
        <result column="fk_department_id_from" jdbcType="BIGINT" property="fkDepartmentIdFrom"/>
        <result column="fk_position_id_from" jdbcType="BIGINT" property="fkPositionIdFrom"/>
        <result column="fk_office_id_to" jdbcType="BIGINT" property="fkOfficeIdTo"/>
        <result column="fk_department_id_to" jdbcType="BIGINT" property="fkDepartmentIdTo"/>
        <result column="fk_position_id_to" jdbcType="BIGINT" property="fkPositionIdTo"/>
        <result column="from_salary_base" jdbcType="DECIMAL" property="fromSalaryBase"/>
        <result column="from_salary_performance" jdbcType="DECIMAL" property="fromSalaryPerformance"/>
        <result column="from_allowance_position" jdbcType="DECIMAL" property="fromAllowancePosition"/>
        <result column="from_allowance_catering" jdbcType="DECIMAL" property="fromAllowanceCatering"/>
        <result column="from_allowance_transportation" jdbcType="DECIMAL" property="fromAllowanceTransportation"/>
        <result column="from_allowance_telecom" jdbcType="DECIMAL" property="fromAllowanceTelecom"/>
        <result column="from_allowance_other" jdbcType="DECIMAL" property="fromAllowanceOther"/>
        <result column="to_salary_base" jdbcType="DECIMAL" property="toSalaryBase"/>
        <result column="to_salary_performance" jdbcType="DECIMAL" property="toSalaryPerformance"/>
        <result column="to_allowance_position" jdbcType="DECIMAL" property="toAllowancePosition"/>
        <result column="to_allowance_catering" jdbcType="DECIMAL" property="toAllowanceCatering"/>
        <result column="to_allowance_transportation" jdbcType="DECIMAL" property="toAllowanceTransportation"/>
        <result column="to_allowance_telecom" jdbcType="DECIMAL" property="toAllowanceTelecom"/>
        <result column="to_allowance_other" jdbcType="DECIMAL" property="toAllowanceOther"/>
        <result column="effective_date" jdbcType="DATE" property="effectiveDate"/>
        <result column="reason" jdbcType="VARCHAR" property="reason"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, fk_staff_id, event_type, fk_office_id_from, fk_department_id_from, fk_position_id_from,
    fk_office_id_to, fk_department_id_to, fk_position_id_to, from_salary_base, from_salary_performance,
    from_allowance_position, from_allowance_catering, from_allowance_transportation, 
    from_allowance_telecom, from_allowance_other, to_salary_base, to_salary_performance, 
    to_allowance_position, to_allowance_catering, to_allowance_transportation, to_allowance_telecom, 
    to_allowance_other, effective_date, reason, gmt_create, gmt_create_user, gmt_modified, 
    gmt_modified_user
  </sql>

    <insert id="insertSelective" parameterType="com.get.permissioncenter.entity.StaffHrEvent" keyProperty="id"
            useGeneratedKeys="true">
        insert into m_staff_hr_event
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkStaffId != null">
                fk_staff_id,
            </if>
            <if test="eventType != null">
                event_type,
            </if>
            <if test="fkOfficeIdFrom != null">
                fk_office_id_from,
            </if>
            <if test="fkDepartmentIdFrom != null">
                fk_department_id_from,
            </if>
            <if test="fkPositionIdFrom != null">
                fk_position_id_from,
            </if>
            <if test="fkOfficeIdTo != null">
                fk_office_id_to,
            </if>
            <if test="fkDepartmentIdTo != null">
                fk_department_id_to,
            </if>
            <if test="fkPositionIdTo != null">
                fk_position_id_to,
            </if>
            <if test="fromSalaryBase != null">
                from_salary_base,
            </if>
            <if test="fromSalaryPerformance != null">
                from_salary_performance,
            </if>
            <if test="fromAllowancePosition != null">
                from_allowance_position,
            </if>
            <if test="fromAllowanceCatering != null">
                from_allowance_catering,
            </if>
            <if test="fromAllowanceTransportation != null">
                from_allowance_transportation,
            </if>
            <if test="fromAllowanceTelecom != null">
                from_allowance_telecom,
            </if>
            <if test="fromAllowanceOther != null">
                from_allowance_other,
            </if>
            <if test="toSalaryBase != null">
                to_salary_base,
            </if>
            <if test="toSalaryPerformance != null">
                to_salary_performance,
            </if>
            <if test="toAllowancePosition != null">
                to_allowance_position,
            </if>
            <if test="toAllowanceCatering != null">
                to_allowance_catering,
            </if>
            <if test="toAllowanceTransportation != null">
                to_allowance_transportation,
            </if>
            <if test="toAllowanceTelecom != null">
                to_allowance_telecom,
            </if>
            <if test="toAllowanceOther != null">
                to_allowance_other,
            </if>
            <if test="effectiveDate != null">
                effective_date,
            </if>
            <if test="reason != null">
                reason,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkStaffId != null">
                #{fkStaffId,jdbcType=BIGINT},
            </if>
            <if test="eventType != null">
                #{eventType,jdbcType=VARCHAR},
            </if>
            <if test="fkOfficeIdFrom != null">
                #{fkOfficeIdFrom,jdbcType=BIGINT},
            </if>
            <if test="fkDepartmentIdFrom != null">
                #{fkDepartmentIdFrom,jdbcType=BIGINT},
            </if>
            <if test="fkPositionIdFrom != null">
                #{fkPositionIdFrom,jdbcType=BIGINT},
            </if>
            <if test="fkOfficeIdTo != null">
                #{fkOfficeIdTo,jdbcType=BIGINT},
            </if>
            <if test="fkDepartmentIdTo != null">
                #{fkDepartmentIdTo,jdbcType=BIGINT},
            </if>
            <if test="fkPositionIdTo != null">
                #{fkPositionIdTo,jdbcType=BIGINT},
            </if>
            <if test="fromSalaryBase != null">
                #{fromSalaryBase,jdbcType=DECIMAL},
            </if>
            <if test="fromSalaryPerformance != null">
                #{fromSalaryPerformance,jdbcType=DECIMAL},
            </if>
            <if test="fromAllowancePosition != null">
                #{fromAllowancePosition,jdbcType=DECIMAL},
            </if>
            <if test="fromAllowanceCatering != null">
                #{fromAllowanceCatering,jdbcType=DECIMAL},
            </if>
            <if test="fromAllowanceTransportation != null">
                #{fromAllowanceTransportation,jdbcType=DECIMAL},
            </if>
            <if test="fromAllowanceTelecom != null">
                #{fromAllowanceTelecom,jdbcType=DECIMAL},
            </if>
            <if test="fromAllowanceOther != null">
                #{fromAllowanceOther,jdbcType=DECIMAL},
            </if>
            <if test="toSalaryBase != null">
                #{toSalaryBase,jdbcType=DECIMAL},
            </if>
            <if test="toSalaryPerformance != null">
                #{toSalaryPerformance,jdbcType=DECIMAL},
            </if>
            <if test="toAllowancePosition != null">
                #{toAllowancePosition,jdbcType=DECIMAL},
            </if>
            <if test="toAllowanceCatering != null">
                #{toAllowanceCatering,jdbcType=DECIMAL},
            </if>
            <if test="toAllowanceTransportation != null">
                #{toAllowanceTransportation,jdbcType=DECIMAL},
            </if>
            <if test="toAllowanceTelecom != null">
                #{toAllowanceTelecom,jdbcType=DECIMAL},
            </if>
            <if test="toAllowanceOther != null">
                #{toAllowanceOther,jdbcType=DECIMAL},
            </if>
            <if test="effectiveDate != null">
                #{effectiveDate,jdbcType=DATE},
            </if>
            <if test="reason != null">
                #{reason,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <select id="isExistByStaffId" resultType="java.lang.Boolean">
      SELECT IFNULL(max(id),0) id from m_staff_hr_event where fk_staff_id=#{staffId}
    </select>
</mapper>