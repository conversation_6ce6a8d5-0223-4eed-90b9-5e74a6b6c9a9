<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.MInstitutionPermissionGroupMapper">

    <select id="getInstitutionPermissionGroups" resultType="com.get.permissioncenter.vo.MInstitutionPermissionGroupVo">
        SELECT
            mipg.id,
            mipg.fk_company_id AS fkCompanyId,
            mc.short_name AS companyName,
            mipg.group_num AS groupNum,
            mipg.group_name AS groupName,
            mipg.view_order AS viewOrder,
            mipg.gmt_create AS gmtCreate,
            mipg.gmt_create_user AS gmtCreateUser,
            mipg.gmt_modified AS gmtModified,
            mipg.gmt_modified_user AS gmtModifiedUser
        FROM
            m_institution_permission_group mipg
            LEFT JOIN m_company mc ON mipg.fk_company_id = mc.id
            LEFT JOIN ais_permission_center.m_staff AS ms ON ms.login_id = mipg.gmt_create_user
        WHERE
            ms.id IN
            <foreach collection="staffFollowerIds" item="staffId" index="index" open="(" separator="," close=")">
                #{staffId}
            </foreach>
            <if test="mInstitutionPermissionGroupSearchDto.fkCompanyId != null and mInstitutionPermissionGroupSearchDto.fkCompanyId !=''">
                AND mipg.fk_company_id =  #{mInstitutionPermissionGroupSearchDto.fkCompanyId}
            </if>
            <if test="mInstitutionPermissionGroupSearchDto.fkCompanyIds != null and mInstitutionPermissionGroupSearchDto.fkCompanyIds.size() > 0">
                AND mipg.fk_company_id IN
                <foreach collection="mInstitutionPermissionGroupSearchDto.fkCompanyIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="mInstitutionPermissionGroupSearchDto.institutionPermissionKeyWord != null and mInstitutionPermissionGroupSearchDto.institutionPermissionKeyWord != ''">
                AND mipg.group_num LIKE CONCAT('%',#{mInstitutionPermissionGroupSearchDto.institutionPermissionKeyWord},'%')
                OR mipg.group_name LIKE CONCAT('%',#{mInstitutionPermissionGroupSearchDto.institutionPermissionKeyWord},'%')
            </if>
        ORDER BY mipg.view_order DESC
    </select>

    <select id="selectViewOrder" resultType="java.lang.Integer">
        SELECT
            COALESCE(MAX(view_order), 0)
        FROM
            m_institution_permission_group
    </select>
    <select id="getPermissionGroupsInstitutionIds" resultType="java.lang.Long">
        SELECT ripgi.fk_institution_id FROM m_institution_permission_group AS mipg
        INNER JOIN r_institution_permission_group_staff AS ripgs ON ripgs.fk_institution_permission_group_id = mipg.id
        INNER JOIN r_institution_permission_group_institution AS ripgi ON ripgi.fk_institution_permission_group_id = mipg.id
        WHERE ripgs.fk_staff_id = #{staffId}
        GROUP BY ripgi.fk_institution_id
    </select>
</mapper>