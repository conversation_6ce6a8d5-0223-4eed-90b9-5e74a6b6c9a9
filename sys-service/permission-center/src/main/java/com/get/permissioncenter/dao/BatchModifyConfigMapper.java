package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.entity.BatchModifyConfig;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface BatchModifyConfigMapper extends BaseMapper<BatchModifyConfig> {
    /**
     * @return int
     * @Description :
     * @Param [record]
     * <AUTHOR>
     */
    int insert(BatchModifyConfig record);

    /**
     * @return int
     * @Description :
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(BatchModifyConfig record);

}