package com.get.permissioncenter.feign;

import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.LoginLogger;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.permissioncenter.dto.BatchModifyConfigDto;
import com.get.permissioncenter.dto.CompanyConfigInfoDto;
import com.get.permissioncenter.dto.StaffByIdsAndCompanyIdsDto;
import com.get.permissioncenter.entity.Department;
import com.get.permissioncenter.entity.Office;
import com.get.permissioncenter.entity.PermissionMediaAndAttached;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.entity.StaffAreaCountry;
import com.get.permissioncenter.entity.StaffConfig;
import com.get.permissioncenter.entity.StaffCourseLevelTypeConfig;
import com.get.permissioncenter.entity.StaffDownload;
import com.get.permissioncenter.service.IBatchModifyConfigService;
import com.get.permissioncenter.service.ICompanyService;
import com.get.permissioncenter.service.IConfigService;
import com.get.permissioncenter.service.IDepartmentService;
import com.get.permissioncenter.service.IGroupGradeResourceService;
import com.get.permissioncenter.service.IMediaAndAttachedService;
import com.get.permissioncenter.service.IOfficeService;
import com.get.permissioncenter.service.IPositionService;
import com.get.permissioncenter.service.IResourceService;
import com.get.permissioncenter.service.IStaffAreaCountryService;
import com.get.permissioncenter.service.IStaffService;
import com.get.permissioncenter.service.IStaffSuperiorService;
import com.get.permissioncenter.service.StaffCourseLevelTypeConfigService;
import com.get.permissioncenter.service.StaffDownloadService;
import com.get.permissioncenter.vo.BatchModifyConfigVo;
import com.get.permissioncenter.vo.CompanyConfigAnalysisVo;
import com.get.permissioncenter.vo.CompanyConfigValueVo;
import com.get.permissioncenter.vo.CompanyConfigVo;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.DepartmentAndStaffVo;
import com.get.permissioncenter.vo.DepartmentVo;
import com.get.permissioncenter.vo.MediaAndAttachedVo;
import com.get.permissioncenter.vo.OfficeVo;
import com.get.permissioncenter.vo.ResourceVo;
import com.get.permissioncenter.vo.StaffInfoVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

@RestController
@AllArgsConstructor
@VerifyPermission(IsVerify = false)
public class PermissionCenterClient implements IPermissionCenterClient {

    private final IStaffService staffService;
    private final ICompanyService companyService;
    private final IOfficeService officeService;
    private final IDepartmentService departmentService;
    private final IConfigService configService;
    private final IStaffSuperiorService superiorService;
    private final IGroupGradeResourceService groupGradeResourceService;
    private final IBatchModifyConfigService batchModifyConfigService;
    private final IPositionService positionService;
    private final IStaffAreaCountryService areaCountryService;
    private final StaffDownloadService staffDownloadService;
    private final IMediaAndAttachedService mediaAndAttachedService;
    private final StaffCourseLevelTypeConfigService staffCourseLevelTypeConfigService;
    private IResourceService resourceService;


    @Override
    public Result<List<Staff>> getAllStaff() {
        return Result.data(staffService.getAllStaff());
    }

    @Override
    public Result<List<CompanyTreeVo>> getCompanyTree(Long companyId) {
        return Result.data(companyService.getCompanyTreeDto(companyId));
    }

    @Override
    public Result<List<Long>> getHtiAndChildCompany() {
        return Result.data(companyService.getHtiAndChildCompany());
    }

    @Override
    public Result<CompanyVo> getCompanyVo() {
        return Result.data(companyService.getChildCompanyDto());
    }

    @Override
    @VerifyLogin(IsVerify = false)
    @LoginLogger
    public Result<StaffInfoVo> staffInfo(String userName, String password, String isAvatarLogin, String avatarLogin) {
        return Result.data(staffService.staffInfo(userName, password, isAvatarLogin, avatarLogin));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<String> getStaffName(Long id) {
        return Result.data(staffService.getStaffNameById(id));
    }

    @Override
    public Result<Boolean> updateLoginStaffInfoByStaffId(Long staffId) {
        return Result.data(staffService.updateLoginStaffInfoByStaffId(staffId));
    }

    @Override
    public Result<Boolean> updateAnnualLeaveBase(Long staffId, BigDecimal annualLeaveBase) {
        return Result.data(staffService.updateAnnualLeaveBase(staffId, annualLeaveBase));
    }

    @Override
    public Result<Boolean> updateCompensatoryLeaveBase(Long staffId, BigDecimal compensatoryLeaveBase) {
        return Result.data(staffService.updateCompensatoryLeaveBase(staffId, compensatoryLeaveBase));
    }

    @Override
    public Result<List<Long>> getStaffIdsByNameKeyOrEnNameKey(String staffNameKeyOrEnNameKey) {
        return Result.data(staffService.getStaffIdsByNameKeyOrEnNameKey(staffNameKeyOrEnNameKey));
    }

    @Override
    public Result<Map<String,Long>> getStaffIdsByAttendanceNum(Set<String> attendanceNums,Long fkCompanyId){
        return Result.data(staffService.getStaffIdsByAttendanceNum(attendanceNums,fkCompanyId));
    }

    @Override
    public Result<Map<String,BaseSelectEntity>> getStaffIdsByNameAndEnName(List<BaseSelectEntity> entities){
        return Result.data(staffService.getStaffIdsByNameAndEnName(entities));
    }

    @Override
    public Result<List<Long>> getStaffIdsByCompanyId(Long companyId) {
        return Result.data(staffService.getStaffIdsByCompanyId(companyId));
    }

    @Override
    public Result<List<Long>> getStaffIdsByCompanyIds(List<Long> companyIds) {
        return Result.data(staffService.getStaffIdsByCompanyIds(companyIds));
    }

    @Override
    public Result<Map<Long, String>> getStaffLoginIdByIds(Set<Long> ids) {
        return Result.data(staffService.getStaffLoginIdByIds(ids));
    }

    @Override
    public Result<Boolean> deleteGuid(String guid) {
        return Result.data(staffService.deleteGuid(guid));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<String> getCompanyNameById(Long id) {
        return Result.data(companyService.getCompanyNameById(id));
    }

    @Override
    public Result<Map<Long, String>> getStaffNameMap(Set<Long> staffIds) {
        return Result.data(staffService.getStaffNameMap(staffIds));
    }
    @Override
    public Result<Map<Long, String>> getStaffNameDepartMentMap(Set<Long> staffIds) {
        return Result.data(staffService.getStaffNameDepartMentMap(staffIds));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, Staff>> getStaffMapByStaffIds(Set<Long> staffIds) {
        return Result.data(staffService.getStaffMapByStaffIds(staffIds));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public List<StaffVo> getStaffByIds(Set<Long> staffIds) {
        return staffService.getStaffByIds(staffIds);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<StaffVo> getStaffById(Long staffId) {
        return Result.data(staffService.getStaffById(staffId));
    }

    @Override
    public Result<List<BaseSelectEntity>> getStaffByDepartmentIds(List<Long> departmentIds) {
        return Result.data(staffService.getStaffByDepartmentIds(departmentIds));
    }

    @Override
    public Result<List<BaseSelectEntity>> getStaffByDepartmentIdsAndCountryNum(List<Long> departmentIds, String fkCountryNum) {
        return Result.data(staffService.getStaffByDepartmentIdsAndCountryNum(departmentIds,fkCountryNum));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<List<BaseSelectEntity>> getStaffByStaffIds(Set<Long> staffIds) {
        return Result.data(staffService.getStaffByStaffIds(staffIds));
    }

    @Override
    public List<BaseSelectEntity> getStaffByCompanyId(Long fkCompanyId) {
        return staffService.getStaffByCompanyId(fkCompanyId);
    }

    @Override
    public List<Staff> getStaffs(Long fkCompanyId) {
        return staffService.getStaffs(fkCompanyId);
    }

    @Override
    public List<StaffVo> getStaffDtoByFkCompanyId(Long fkCompanyId){
        return staffService.getStaffDtoByFkCompanyId(fkCompanyId);
    }

    @Override
    public List<StaffVo> getStaffDtos(Long fkCompanyId, Long fkDepartmentId, String staffNameKeyOrEnNameKey) {
        return staffService.getStaffDtos(fkCompanyId,fkDepartmentId,staffNameKeyOrEnNameKey);
    }

    @Override
    public List<Long> getAllSubordinateIds(Long staffId){
        return staffService.getAllSubordinateIds(staffId);
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<List<StaffVo>> getStaffByCreateUsers(Set<String> staffIds) {
        return Result.data(staffService.getCompanyIdByCreateUsers(staffIds));
    }

    @Override
    public Result<Map<Long, Long>> getCompanyIdByStaffIds(Set<Long> staffIds) {
        return Result.data(staffService.getCompanyIdByStaffIds(staffIds));
    }

    @Override
    public Result<List<CompanyTreeVo>> getAllCompanyDto() {
        return Result.data(companyService.getAllCompanyDto());
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, String>> getCompanyNamesByIds(Set<Long> companyIds) {
        return Result.data(companyService.getCompanyNamesByIds(companyIds));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<String>> getCompanyNamesByIdsDESC(Set<Long> companyIds) {
        return Result.data(companyService.getCompanyNamesByIdsDESC(companyIds));
    }

    @Override
    public Result<Map<Long, String>> getCompanyNamesByStaffIds(Set<Long> staffIds) {
        return Result.data(staffService.getCompanyNamesByStaffIds(staffIds));
    }

    @Override
    @VerifyLogin(IsVerify = false)  //定时任务免登陆
    public Result<StaffVo> getStaffByCreateUser(String createUser) {
        return Result.data(staffService.getCompanyIdByCreateUser(createUser));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<StaffVo> getCompanyIdByStaffId(Long staffId) {
        return Result.data(staffService.getCompanyIdByStaffId(staffId));
    }

    @ApiIgnore
    @Override
    public Result<Map<Long, String>> getofficeNamesByIds(Set<Long> ids) {
        return Result.data(officeService.getofficeNamesByIds(ids));
    }

    @Override
    public List<Long> getCompanyIdByName(String keyWord) {
        return companyService.getCompanyIdByName(keyWord);
    }

    @Override
    public Result<Map<Long, String>> getDepartmentNamesByIds(Set<Long> ids) {
        return Result.data(departmentService.getDepartmentNamesByIds(ids));
    }

    @Override
    public Result<List<String>> getDepartmentNameList(String[] departmentNumList) {
        return Result.data(departmentService.getDepartmentNameList(departmentNumList));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<String> getDepartmentNameById(Long id) {
        return Result.data(departmentService.getDepartmentNameById(id));
    }

    @Override
    public Result<List<Long>> getAllDepartmentStaffIds(Long companyId, Long departmentId) {
        return Result.data(staffService.getAllDepartmentStaffIds(companyId, departmentId));
    }

    @Override
    public Result<Long> getDepartmentIdByNum(String num) {
        return Result.data(departmentService.getDepartmentIdByNum(num));
    }

    @Override
    public Result<String> getDepartmentNumById(Long id) {
        return Result.data(departmentService.getDepartmentNumById(id));
    }

    @Override
    public Result<List<Long>> getPositionByNum(List<String> num) {
        return Result.data(staffService.getPositionByNum(num));
    }

    @Override
    public Result<List<Long>> getTopPositionStaffIds(Long companyId, Long departmentId) {
        return Result.data(staffService.getTopPositionStaffIds(companyId, departmentId));
    }

    @Override
    public Result<List<Long>> getAllPositionStaffIds(Long companyId, List<Long> positionIdList) {
        return Result.data(staffService.getAllPositionStaffIds(companyId, positionIdList));
    }

    @Override
    public Result<DepartmentVo> getDepartmentById(Long id) {
        Department department = departmentService.getById(id);
        if (department != null) {
            return Result.data(BeanCopyUtils.objClone(department, DepartmentVo::new));
        }
        return Result.data(null);//如没有则为空
    }

    @Override
    public Result<OfficeVo> getOfficeById(Long id) {
        Office office = officeService.getById(id);
        if (office != null) {
            return Result.data(BeanCopyUtils.objClone(office, OfficeVo::new));
        }
        return Result.data(null);//如没有则为空
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<String> getOfficeNameById(Long id) {
        return Result.data(officeService.getOfficeNameById(id));//如没有则为空
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Long> getStaffSupervisorIdByStaffId(Long id) {
        return Result.data(staffService.getStaffSupervisorIdByStaffId(id));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Set<Long>> getStaffSupervisorIds(Set<Long> ids) {
        return Result.data(staffService.getStaffSupervisorIds(ids));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<List<Long>> getStaffFollowerIds(Long id) {

        return Result.data(superiorService.getStaffFollowerId(id));
    }

    /**
     * 获取业务下级id(一层)
     * @param id
     * @return
     */
    @VerifyLogin(IsVerify = false)
    @Override
    public Result<List<Long>> getBusinessSubordinatesIds(Long id) {

        return Result.data(superiorService.getBusinessSubordinatesIds(id));
    }

    /**
     * 获取直接下属ids（一层）
     * @param id
     * @return
     */
    @Override
    public Result<List<Long>> getObtainDirectSubordinatesIds(Long id) {
        return Result.data(staffService.getObtainDirectSubordinatesIds(id));
    }


    @Override
    public Result<List<String>> getStaffAreaCountryKeysByfkStaffId(Long id) {
        return Result.data(areaCountryService.getStaffAreaCountryKeysByfkStaffId(id));
    }

    @Override
    public Result<Set<Long>> getStaffAreaCountryIdsByfkStaffId(Long id) {
        return  Result.data(areaCountryService.getStaffAreaCountryByfkStaffId(id));
    }

    @Override
    public Result<Boolean> getGroupGradeResourcesByResource(String resourceKey) {
        return Result.data(groupGradeResourceService.getGroupGradeResourcesByResource(resourceKey));
    }

    @Override
    public Result<MediaAndAttachedVo> getCompanyIcon(Long fkCompanyId) {
        return Result.data(companyService.getCompanyIcon(fkCompanyId));
    }

    @Override
    public Result<Boolean> saveResumeGuid(String guid) {
        return Result.data(staffService.saveResumeGuid(guid));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<String> getConfigValueByConfigKey(String key) {
        return Result.data(configService.getConfigValueByConfigKey(key));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<ConfigVo> getConfigByKey(String key) {
        return Result.data(configService.getConfigByKey(key));
    }

    @Override
    public CompanyConfigVo getCompanyConfigInfo(String key, Integer column) {
        return null;
    }

    @Override
    public Result<ConfigVo> getConfigValueByConfigKeyAndValue(String key, Long value1) {
        return Result.data(configService.getConfigValueByConfigKeyAndValue(key, value1));
    }

    @Override
    public Set<Long> getAllStaffIds() {
        return staffService.getAllStaffIds();
    }

    @Override
    public List<Long> getStaffIdsByNameKey(String staffNameKey) {
        return staffService.getStaffIdsByNameKey(staffNameKey);
    }

    @Override
    public Map<Long, String> getStaffNamesByIds(Set<Long> ids) {
        return staffService.getStaffNamesByIds(ids);
    }

    @Override
    public Result<List<BatchModifyConfigVo>> getBatchUpdateItems(BatchModifyConfigDto batchModifyConfigDto) {
        return Result.data(batchModifyConfigService.getBatchUpdateItems(batchModifyConfigDto));
    }

    @Override
    public Result<BatchModifyConfigVo> detail(Long id) {
        return Result.data(batchModifyConfigService.findbatchModifyConfigById(id));
    }

    @Override
    public Map<Long, String> getPositionNumByIds(Set<Long> ids) {
        return positionService.getPositionNumByIds(ids);
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public List<Long> getStaffIdsByPositionNums(Set<String> positionNums) {
        return staffService.getStaffIdsByPositionNums(positionNums);
    }

    @Override
    public Map<String, List<Long>> getPositionNumAndStaffIdMap(Set<Long> ids) {
        return positionService.getPositionNumAndStaffIdMap(ids);
    }

    @Override
    public Map<Long, List<Long>> getStaffSuperiorByIds(Set<Long> staffIds) {
        return superiorService.getStaffSuperiorByIds(staffIds);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public void getDayOfStaffBirthday() {
        staffService.getDayOfStaffBirthday();
    }

    @Override
    public List<Long> getStaffListByStaffName(String staffName) {
        return staffService.getStaffListByStaffName(staffName);
    }

    @Override
    public Result<Map<Long, Boolean>> getStaffIsOnDuty(Set<Long> staffIds) {
        return staffService.doGetStaffIsOnDuty(staffIds);
    }

    @Override
    public Result<List<BaseSelectEntity>> fuzzySearchCompanyName(String keyword,Long companyId) {
        return Result.data(companyService.fuzzySearchCompanyName(keyword,companyId));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<DepartmentAndStaffVo>> getDepartmentAndStaffDtoByStaffIds(Set<Long> staffIds) {
        return Result.data(staffService.getDepartmentAndStaffDtoByStaffIds(staffIds));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public List<StaffVo> getStaffDtoByIds(Set<Long> staffIds){
        return staffService.getStaffDtoByIds(staffIds);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Set<Long>> getStaffDepartmentsById(Long staffId) {
        return Result.data(staffService.getStaffDepartmentsById(staffId));
    }

    @Override
    public Map<Long, String> getStaffEnNameByIds(Set<Long> ids) {
        return staffService.getStaffEnNameByIds(ids);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Map<Long, String> getStaffChnNameByIds(Set<Long> ids) {
        return staffService.getStaffChnNameByIds(ids);
    }

    @Override
    public List<Long> getChildCompanyIds(Long companyId) {
        return companyService.getChildCompany(companyId);
    }

    @Override
    public Long addDownloadRecord(StaffDownload staffDownload) {
        return staffDownloadService.addRecord(staffDownload);
    }

    @Override
    public void updateDownload(StaffDownload staffDownload) {
        staffDownloadService.update(staffDownload);
    }

    @Override
    public void saveStaffConfigByType(String type, List<String> keys) {
        staffService.saveStaffConfigByType(type,keys);
    }

    @Override
    public StaffConfig getStaffConfigByType(String type) {
        return staffService.getStaffConfigByType(type);
    }

    @Override
    public List<Long> getReStaffIdByKey(String key, List<Long> staffIds) {
        return staffService.getReStaffIdByKey(key,staffIds);
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public List<PermissionMediaAndAttached> getMediaAndAttachedByIaeCrm(List<String> fkTableNames) {
        return mediaAndAttachedService.getMediaAndAttachedByIaeCrm(fkTableNames);
    }

    @LoginLogger
    @VerifyLogin(IsVerify = false)
    @Override
    public Result<StaffInfoVo> wxCpLogin(String code, String platformType) {
        return Result.data(staffService.wxCpLogin(code,platformType));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Boolean updateMediaAndAttachedById(PermissionMediaAndAttached permissionMediaAndAttached) {
        return mediaAndAttachedService.updateMediaAndAttachedById(permissionMediaAndAttached);
    }

    @Override
    public Result<List<StaffVo>> getStaffDtosByDepartmentNums(Long countryId, Set<String> nums) {
        return Result.data(staffService.getStaffDtosByDepartmentNums(countryId,nums));
    }

    @Override
    public StaffVo getStaffByLoginId(String loginId) {
        return staffService.getStaffByLoginId(loginId);
    }

    @Override
    public Result<CompanyConfigValueVo> getProjectLimitConfigKey(String configKey) {
        return Result.data(configService.getProjectLimitConfigKey(configKey));
    }

    @Override
    public Result<List<Long>> getStaffIdsByLikeCondition(String gmtCreateUser) {
        return Result.data(staffService.getStaffIdsByLikeCondition(gmtCreateUser));
    }

    @Override
    public List<Staff> getStaffByLoginIds(List<String> staffGmtCreate) {
        return staffService.getStaffByLoginIds(staffGmtCreate);
    }

    @Override
    public List<CompanyConfigInfoDto> getCompanySettlementConfigInfo(String configKey) {
        return configService.getCompanySettlementConfigInfo(configKey).getDatas();
    }

    @Override
    public Map<Long, Integer> getCompanySettlementConfigInfoMap(String configKey) {
        return configService.getCompanySettlementConfigInfoMap(configKey);
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public List<Long> getStaffIdsByResourceKey(String resourceKey, Boolean isContainAdmin) {
        return staffService.getStaffIdsByResourceKey(resourceKey,isContainAdmin);
    }
    @Override
    public List<StaffCourseLevelTypeConfig> getStaffCourseLevelTypeConfig(Long fkStaffId,Integer type){
        return staffCourseLevelTypeConfigService.getStaffCourseLevelTypeConfig(fkStaffId,type);
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<Map<Long, String>> getCompanyConfigMap(String configKey, int value) {
        return Result.data(companyService.getCompanyConfigMap(configKey, value));
    }

    @VerifyLogin(IsVerify = false)
    @Override
    public Result<Map<Long, CompanyConfigAnalysisVo>> getCompanyConfigAnalysis(String configKey) {
        return Result.data(companyService.getCompanyConfigAnalysis( configKey));
    }


    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<StaffAreaCountry>> getStaffAreaCountryByStaffIds(List<Long> fkStaffIds){
        return Result.data(areaCountryService.getStaffAreaCountryByStaffIds(fkStaffIds));
    }

    @Override
    public Result<Boolean> getIsStaffBusiness(Long fkStaffId) {
        return Result.data(staffService.getIsStaffBusiness(fkStaffId));
    }

    @Override
    public Result<Map<String, StaffVo>> getStaffDtoMapByLoginIds(Set<String> loginIds) {
        return Result.data(staffService.getStaffDtoMapByLoginIds(loginIds));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<Map<Long, Set<Long>>> getAllStaffSuperiorByStaffIds(Set<Long> staffIds) {
        return Result.data(staffService.getAllStaffSuperiorByStaffIds(staffIds));
    }

    @Override
    public Result<List<String>> getPushDepartmentStaffEmail(Long fkCountryId, Long fkInstitutionId, List<String> departmentList) {
        return Result.data(staffService.getPushDepartmentStaffEmail(fkCountryId, fkInstitutionId, departmentList));
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<Long>> getAuthorizedStaffIdsByResourceKey(String resourceKey) {
        return Result.data(staffService.getAuthorizedStaffIdsByResourceKey(resourceKey));
    }

    @Override
    public Map<Long, String> getStaffListByIdsAndCompanyIds(StaffByIdsAndCompanyIdsDto staffByIdsAndCompanyIdsDto) {
        return staffService.getStaffByIdsAndCompanyIds(staffByIdsAndCompanyIdsDto);
    }

    @Override
    public Result<List<ResourceVo>> getResourceTree() {
        return Result.data(resourceService.getResourceTree());
    }


}
