package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.cache.CacheNames;
import com.get.common.consts.AESConstant;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.utils.AESUtils;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.jwt.JwtUtil;
import com.get.core.jwt.props.JwtProperties;
import com.get.core.log.exception.GetServiceException;
import com.get.core.log.feign.ILogClient;
import com.get.core.log.model.LogLogin;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.redis.cache.GetRedis;
import com.get.core.secure.StaffInfo;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.start.constant.TokenConstant;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.WebUtil;
import com.get.file.utils.ConvertToMultipartFile;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.helpcenter.feign.IHelpCenterClient;
import com.get.helpcenter.vo.HelpInfoVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.officecenter.feign.IOfficeCenterClient;
import com.get.permissioncenter.dao.MInstitutionPermissionGroupMapper;
import com.get.permissioncenter.dao.StaffAreaCountryMapper;
import com.get.permissioncenter.dao.StaffBdMapper;
import com.get.permissioncenter.dao.StaffCompanyMapper;
import com.get.permissioncenter.dao.StaffConfigMapper;
import com.get.permissioncenter.dao.StaffEmailMapper;
import com.get.permissioncenter.dao.StaffInstitutionMapper;
import com.get.permissioncenter.dao.StaffMapper;
import com.get.permissioncenter.dao.StaffResourceMapper;
import com.get.permissioncenter.dao.StaffSuperiorMapper;
import com.get.permissioncenter.dto.AddUserPreferencesAddDto;
import com.get.permissioncenter.dto.AssignBusinessSchoolDto;
import com.get.permissioncenter.dto.BusinessSchoolDto;
import com.get.permissioncenter.dto.CommentDto;
import com.get.permissioncenter.dto.MediaAndAttachedDto;
import com.get.permissioncenter.dto.StaffByIdsAndCompanyIdsDto;
import com.get.permissioncenter.dto.StaffDto;
import com.get.permissioncenter.dto.StaffEmailDto;
import com.get.permissioncenter.dto.StaffEmailPasswordUpdateDto;
import com.get.permissioncenter.dto.StaffInfoUpdateDto;
import com.get.permissioncenter.dto.StaffInstitutionDto;
import com.get.permissioncenter.dto.query.StaffQueryDto;
import com.get.permissioncenter.entity.PermissionMediaAndAttached;
import com.get.permissioncenter.entity.Staff;
import com.get.permissioncenter.entity.StaffAreaCountry;
import com.get.permissioncenter.entity.StaffBd;
import com.get.permissioncenter.entity.StaffCompany;
import com.get.permissioncenter.entity.StaffConfig;
import com.get.permissioncenter.entity.StaffContract;
import com.get.permissioncenter.entity.StaffEmail;
import com.get.permissioncenter.entity.StaffInstitution;
import com.get.permissioncenter.entity.StaffSuperior;
import com.get.permissioncenter.service.ICommentService;
import com.get.permissioncenter.service.ICompanyService;
import com.get.permissioncenter.service.IDeleteService;
import com.get.permissioncenter.service.IDepartmentService;
import com.get.permissioncenter.service.IGroupGradeResourceService;
import com.get.permissioncenter.service.IMediaAndAttachedService;
import com.get.permissioncenter.service.IOfficeService;
import com.get.permissioncenter.service.IPositionService;
import com.get.permissioncenter.service.IStaffAreaCountryService;
import com.get.permissioncenter.service.IStaffContractService;
import com.get.permissioncenter.service.IStaffOfficeService;
import com.get.permissioncenter.service.IStaffService;
import com.get.permissioncenter.service.IStaffSuperiorService;
import com.get.permissioncenter.utils.FileUtils;
import com.get.permissioncenter.utils.MD5Utils;
import com.get.permissioncenter.utils.MyStringUtils;
import com.get.permissioncenter.vo.AreaCountryVo;
import com.get.permissioncenter.vo.AssignBusinessSchoolVo;
import com.get.permissioncenter.vo.BusinessSchoolVo;
import com.get.permissioncenter.vo.CommentVo;
import com.get.permissioncenter.vo.CompanyVo;
import com.get.permissioncenter.vo.DepartmentAndStaffVo;
import com.get.permissioncenter.vo.DepartmentVo;
import com.get.permissioncenter.vo.MediaAndAttachedVo;
import com.get.permissioncenter.vo.OfficeVo;
import com.get.permissioncenter.vo.PositionVo;
import com.get.permissioncenter.vo.StaffAreaCountryVo;
import com.get.permissioncenter.vo.StaffEmailVo;
import com.get.permissioncenter.vo.StaffExportVo;
import com.get.permissioncenter.vo.StaffInfoVo;
import com.get.permissioncenter.vo.StaffListByDepartmentVo;
import com.get.permissioncenter.vo.StaffSuperiorVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.vo.WxCpUserVo;
import com.get.remindercenter.dto.RemindTaskDto;
import com.get.remindercenter.dto.SmsDto;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import static com.get.common.cache.CacheNames.*;
import static com.get.common.cache.CacheNames.BIZ_CACHE;
import static com.get.core.cache.constant.CacheConstant.*;
import static com.get.core.cache.constant.CacheConstant.API_KEYS_CODE;
import static com.get.core.cache.constant.CacheConstant.API_RESOURCE_KEYS_CODE;
import static com.get.core.cache.constant.CacheConstant.STAFF_COMPANY_IDS_CODE;
import static com.get.core.cache.constant.CacheConstant.STAFF_COUNTRY_IDS_CODE;
import static com.get.core.cache.constant.CacheConstant.STAFF_INFO_CODE;
import static com.get.core.cache.constant.CacheConstant.USER_CACHE;

@Service
@Slf4j
public class StaffServiceImpl extends BaseServiceImpl<StaffMapper, Staff> implements IStaffService {
    @Resource
    private StaffMapper staffMapper;
    @Resource
    private StaffResourceMapper staffResourceMapper;
    @Resource
    private IStaffContractService contractService;
    @Resource
    private UtilService utilService;
    @Resource
    private StaffCompanyMapper staffCompanyMapper;
    @Resource
    private StaffAreaCountryMapper staffAreaCountryMapper;
    @Resource
    private IOfficeService officeService;
    @Resource
    private IMediaAndAttachedService attachedService;
    @Resource
    private ICompanyService companyService;
    @Resource
    private IDepartmentService departmentService;
    @Resource
    private IPositionService positionService;
    @Resource
    private ICommentService commentService;
    @Resource
    private ILogClient logClient;
    @Resource
    private StaffBdMapper staffBdMapper;
    @Resource
    private IDeleteService deleteService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IGroupGradeResourceService groupGradeResourceService;
    @Resource
    private JwtProperties jwtProperties;
    @Resource
    private IStaffContractService staffContractService;
    @Resource
    private StaffConfigMapper staffConfigMapper;
    @Resource
    private StaffEmailMapper staffEmailMapper;
    @Resource
    private GetRedis getRedis;
    @Resource
    private IOfficeCenterClient officeCenterClient;
    @Resource
    private MInstitutionPermissionGroupMapper institutionPermissionGroupMapper;
    @Resource
    private IStaffAreaCountryService staffAreaCountryService;
    @Resource
    private IStaffOfficeService staffOfficeService;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private StaffSuperiorMapper staffSuperiorMapper;
    @Resource
    private IHelpCenterClient helpCenterClient;
    @Resource
    private StaffInstitutionMapper staffInstitutionMapper;
    @Resource
    private IStaffSuperiorService superiorService;


    @Override
    public List<StaffVo> getStaffDto(StaffQueryDto staffVo, Page page) {
//        Example example = getStaffExample(staffVo);
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<Staff> staffList = staffMapper.selectByExample(example);
//        page.restPage(staffList);
//        List<StaffVo> staffVos = staffList.stream().map(staff -> Tools.objClone(staff, StaffVo.class)).collect(Collectors.toList());

        //原始获取数据的方法
//        LambdaQueryWrapper<Staff> wrapper = getStaffLambdaQueryWrapper(staffVo);
//        wrapper.orderByDesc(Staff::getIsOnDuty,Staff::getIsActive);

//        IPage<Staff> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
//        List<Staff> staffs = pages.getRecords();

        //修改为sql语句，方便学校权限组获取数据员工数据
//        LambdaQueryWrapper<Staff> StaffPageWithGroupBindWrapper = getStaffLambdaQueryWrapper(staffVo);
        List<Long> companyIds = getCompanyIds();
        IPage<Staff> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));

        IPage<StaffVo> pages = staffMapper.selectStaffPageWithGroupBind(iPage, staffVo,companyIds);
//        staffVo.getFkInstitutionPermissionGroupId(),
//                staffVo.getIsBind()
        List<StaffVo> staffs = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<StaffVo> staffVos = BeanCopyUtils.copyListProperties(staffs, StaffVo::new);

        //        Example example = new Example(StaffAreaCountry.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", id);
//        //获取所有的业务国家
//        List<StaffAreaCountry> areaCountries = areaCountryMapper.selectByExample(example);

        //查询所有的国家
        List<com.get.institutioncenter.vo.AreaCountryVo> areaCountryVos = institutionCenterClient.getAreaCountrys(null).getData();
        List<StaffAreaCountry> areaCountries = new ArrayList<>();
        if(GeneralTool.isNotEmpty(staffs))
        {
            List<Long> staffIds = staffs.stream().map(StaffVo::getId).distinct().collect(Collectors.toList());
            areaCountries = staffAreaCountryMapper.selectList(Wrappers.<StaffAreaCountry>lambdaQuery().in(StaffAreaCountry::getFkStaffId,staffIds));
        }
        setPropertiesName(staffVos,areaCountries, areaCountryVos);
        return staffVos;
    }

    @Override
    public void exportStaffList(StaffDto staffDto1, HttpServletResponse response) {
        List<StaffExportVo> exportInfo = staffMapper.getExportInfo(staffDto1);
        Set<Long> ids = exportInfo.stream().map(StaffExportVo::getId).collect(Collectors.toSet());
        List<StaffContract> contract = contractService.getStaffContract(ids);
        Map<Long, StaffContract> collect = contract.stream().collect(Collectors.toMap(StaffContract::getFkStaffId, Function.identity()));
        for (StaffExportVo staffDto : exportInfo) {
            if (collect.containsKey(staffDto.getId())) {
                StaffContract staffContract = collect.get(staffDto.getId());
                staffDto.setStartTime(staffContract.getStartTime());
                staffDto.setEndTime(staffContract.getEndTime());
            }
            if (GeneralTool.isNotEmpty(staffDto.getHukou())) {
                switch (staffDto.getHukou()) {
                    case 0:
                        staffDto.setHukouName(ProjectExtraEnum.LOCAL_NON_AGRICULTURAL_ACCOUNT.value);
                        break;
                    case 1:
                        staffDto.setHukouName(ProjectExtraEnum.LOCAL_AGRICULTURAL_ACCOUNT.value);
                        break;
                    case 2:
                        staffDto.setHukouName(ProjectExtraEnum.NON_AGRICULTURAL_ACCOUNT_IN_OTHER_PLACES.value);
                        break;
                    case 3:
                        staffDto.setHukouName(ProjectExtraEnum.REMOTE_AGRICULTURAL_ACCOUNT.value);
                        break;
                    case 4:
                        staffDto.setHukouName(ProjectExtraEnum.HONGKONG_MACAO_TAIWAN.value);
                        break;
                    case 5:
                        staffDto.setHukouName(ProjectExtraEnum.FOREIGN.value);
                        break;
                    default:
                        break;
                }
            }
            if (GeneralTool.isNotEmpty(staffDto.getGender())) {
                staffDto.setGenderName(staffDto.getGender()==0?"女":"男");
            }
        }
        Set<String> ignoreFields = new HashSet<>(1);
        ignoreFields.add("id");
        ignoreFields.add("gender");
        ignoreFields.add("companyId");
        ignoreFields.add("hukou");
        Map<String, String> field = FileUtils.getFileMapIgnoreSomeField(StaffExportVo.class, ignoreFields);
        FileUtils.exportExcel(response,exportInfo,"exportStaffList",field);
    }

    @Override
    public List<StaffVo> getAllStaffDto(Long companyId, Long departId, Long positionId) {
        if (GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("company_id_null"));
        }
        if (GeneralTool.isEmpty(departId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("department_id_null"));
        }
        if (GeneralTool.isEmpty(positionId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("position_id_null"));
        }
        //查询员工
//        Example example = new Example(Staff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkCompanyId", companyId);
//        criteria.andEqualTo("fkDepartmentId", departId);
//        criteria.andEqualTo("fkPositionId", positionId);
//        List<Staff> staffList = mapper.selectByExample(example);
//        List<StaffVo> staffVos = staffList.stream().map(staff -> Tools.objClone(staff, StaffVo.class)).collect(Collectors.toList());

        List<Staff> staffList = staffMapper.selectList(Wrappers.<Staff>query().lambda()
                .eq(Staff::getFkCompanyId, companyId)
                .eq(Staff::getFkDepartmentId, departId)
                .eq(Staff::getFkPositionId, positionId));
        List<StaffVo> staffVos = BeanCopyUtils.copyListProperties(staffList, StaffVo::new);
        if (GeneralTool.isNotEmpty(staffVos)) {
            for (StaffVo staffVo : staffVos) {
                //获取业务国家
                staffVo.setAreaCountryDtos(getCountryList(staffVo.getId()));
                //获取业务办公室
                staffVo.setStaffOfficeDtos(getOfficeList(staffVo.getId()));
            }
        }
        return staffVos;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public List<Long> batchAddStaff(List<StaffDto> staffDtoList) {
        if (GeneralTool.isEmpty(staffDtoList)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        List<Long> ids = Lists.newArrayList();
        for (StaffDto staffDto : staffDtoList) {
                Staff staff = BeanCopyUtils.objClone(staffDto, Staff::new);
                utilService.updateUserInfoToEntity(staff);
//                //密码加密
//                if (GeneralTool.isNotEmpty(staffDto.getLoginPs())) {
//                    staff.setLoginPs(MD5Utils.encrypt(staffDto.getLoginPs()));
//                }
                //密码加密
                List<String> staffName = staffEmailMapper.checkStaffEmail(staff.getFkCompanyId(), staff.getEmail(), null);
                if (GeneralTool.isNotEmpty(staffName)) {
                    throw new GetServiceException(LocaleMessageUtils.getFormatMessage("staff_email_exist", staff.getEmail(), staffName));
                }
                if (GeneralTool.isNotEmpty(staffDto.getEmailPassword())) {
                    try {
                        staff.setEmailPassword(AESUtils.Encrypt(staffDto.getEmailPassword(), AESConstant.AESKEY));
                    } catch (Exception e) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
                    }
                }
                staff.setIsActive(GeneralTool.isNotEmpty(staffDto.getIsActive())? staffDto.getIsActive():true);
                staff.setIsOnDuty(GeneralTool.isNotEmpty(staffDto.getIsOnDuty())? staffDto.getIsOnDuty():true);
                staff.setIsAdmin(false);
                staff.setIsModifiedPs(true);
                staff.setIsVcodeRequired(staffDto.getIsVcodeRequired());
                staff.setIpWhiteList(staffDto.getIpWhiteList());
                staffMapper.insert(staff);
                //邮箱
                StaffEmail staffEmail = new StaffEmail();
                staffEmail.setFkStaffId(staff.getId());
                staffEmail.setEmail(staff.getEmail());
                staffEmail.setIsPrimary(true);
                utilService.setCreateInfo(staffEmail);
                staffEmailMapper.insert(staffEmail);

                setMediaTableId(staffDto.getFkMediaId(), staff.getId());

                //修改编号
                StaffDto returnStaff = BeanCopyUtils.objClone(staff, StaffDto::new);
                returnStaff.setNum(MyStringUtils.getStaffNum(staff.getId()));
                updateStaff(returnStaff);
                staffDto.setNum(returnStaff.getNum());
                staffDto.setId(returnStaff.getId());
            if (!validateChange(staffDto)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("account_exist"));
            }
            ids.add(staff.getId());
        }
        CacheUtil.clear(STAFF_NAME_CACHE);
        CacheUtil.clear(ALL_STAFF_IDS_CACHE);
        return ids;
    }

    @Override
    public StaffVo findStaffById(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        Staff staff = staffMapper.selectByPrimaryKey(staffId);
        Staff staff = staffMapper.selectById(staffId);
        if (GeneralTool.isEmpty(staff)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        //权限校验
        if (!SecureUtil.validateCompany(staff.getFkCompanyId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
        }
        StaffVo staffVo = BeanCopyUtils.objClone(staff, StaffVo::new);
        //设置联系地址名称
        setAddressName(staffVo);
        setCompanyName(staffVo);
        setDepartmentName(staffVo);
        setPositionName(staffVo);
        setOfficeName(staffVo);
//        if (GeneralTool.isNotEmpty(staffVo.getFkStaffIdSupervisor())) {
////            staff = staffMapper.selectByPrimaryKey(staffVo.getFkStaffIdSupervisor());
//            staff = staffMapper.selectById(staffVo.getFkStaffIdSupervisor());
//            if (GeneralTool.isNotEmpty(staff)) {
//                CompanyVo companyDto = companyService.findCompanyById(staff.getFkCompanyId());
//                String supervisorName = "[" + companyDto.getNum() + "]" + staff.getName() + ((GeneralTool.isNotEmpty(staff.getNameEn())) ?
//                        "(" + staff.getNameEn() + ")" : null);
//                staffVo.setSupervisorName(supervisorName);
//                staffVo.setSupervisorCompanyId(staff.getFkCompanyId());
//            }
//        }

        if (GeneralTool.isNotEmpty(staffVo.getFkStaffIdSupervisor())) {
            Staff staffIdSupervisor = staffMapper.selectById(staffVo.getFkStaffIdSupervisor());
            if (GeneralTool.isNotEmpty(staffIdSupervisor)) {
                CompanyVo companyVo = companyService.findStaffIdSupervisorCompanyById(staffIdSupervisor.getFkCompanyId());
                String supervisorName = "[" + companyVo.getNum() + "]" + staffIdSupervisor.getName() + ((GeneralTool.isNotEmpty(staffIdSupervisor.getNameEn())) ?
                        "(" + staffIdSupervisor.getNameEn() + ")" : "");
                staffVo.setSupervisorName(supervisorName);
                staffVo.setSupervisorCompanyId(staff.getFkCompanyId());
            }
        }
        if (GeneralTool.isNotEmpty(staffVo.getMobileAreaCode())) {
            staffVo.setMobileAreaCodeName(ProjectExtraEnum.getValueByKey(Integer.valueOf(staffVo.getMobileAreaCode()), ProjectExtraEnum.AREA_CODE_TYPE));
        }
        //获取员工图片
        MediaAndAttachedVo headImageDto = getHeadIconImg(staffId);
        //设置属性
        staffVo.setHeadImageDto(headImageDto);
        //密码隐藏
        staffVo.setLoginPs(null);
        //劳动合同生效时间
        StaffContract staffContract = staffContractService.getStaffContractByStaffId(staffId);
        if (GeneralTool.isNotEmpty(staffContract)) {
            staffVo.setContractStartTime(staffContract.getStartTime());
        }
        //户口性质名
        if (GeneralTool.isNotEmpty(staffVo.getHukou())) {
            switch (staffVo.getHukou()) {
                case 0:
                    staffVo.setHukouName(ProjectExtraEnum.LOCAL_NON_AGRICULTURAL_ACCOUNT.value);
                    break;
                case 1:
                    staffVo.setHukouName(ProjectExtraEnum.LOCAL_AGRICULTURAL_ACCOUNT.value);
                    break;
                case 2:
                    staffVo.setHukouName(ProjectExtraEnum.NON_AGRICULTURAL_ACCOUNT_IN_OTHER_PLACES.value);
                    break;
                case 3:
                    staffVo.setHukouName(ProjectExtraEnum.REMOTE_AGRICULTURAL_ACCOUNT.value);
                    break;
                case 4:
                    staffVo.setHukouName(ProjectExtraEnum.HONGKONG_MACAO_TAIWAN.value);
                    break;
                case 5:
                    staffVo.setHukouName(ProjectExtraEnum.FOREIGN.value);
                    break;
                default:
                    break;
            }
        }
        //证件类别名
        if (GeneralTool.isNotEmpty(staffVo.getIdentityType())) {
            switch (staffVo.getIdentityType()) {
                case 0:
                    staffVo.setIdentityTypeName(ProjectExtraEnum.IDENTITY_CARD.value);
                    break;
                case 1:
                    staffVo.setIdentityTypeName(ProjectExtraEnum.PASSPORT.value);
                    break;
                case 2:
                    staffVo.setIdentityTypeName(ProjectExtraEnum.PASS.value);
                    break;
                case 3:
                    staffVo.setIdentityTypeName(ProjectExtraEnum.RETURN_PERMIT.value);
                    break;
                default:
                    break;
            }
        }
        //全称
        StringBuffer stringBuffer = new StringBuffer();
        if (staffVo.getIsOnDuty()) {
            stringBuffer.append("【离职】");
        }
        stringBuffer.append(staffVo.getName());
        if (GeneralTool.isNotEmpty(staffVo.getNameEn())) {
            stringBuffer.append('(').append(staffVo.getNameEn()).append(')');
        }
        staffVo.setFullName(stringBuffer.toString());
        List<StaffEmail> staffEmails = staffEmailMapper.selectList(Wrappers.<StaffEmail>lambdaQuery().eq(StaffEmail::getFkStaffId, staff.getId()));
        if (GeneralTool.isNotEmpty(staffEmails)) {
            staffEmails = staffEmails.stream().sorted(Comparator.comparing(StaffEmail::getIsPrimary).reversed()).collect(Collectors.toList());
        }
        staffVo.setStaffEmailList(staffEmails);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        simpleDateFormat.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
        try {
            String birthdyDateStr =(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(staffVo.getBirthday());
            Date birthdyDate = simpleDateFormat.parse(birthdyDateStr);
            staffVo.setBirthday(birthdyDate);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return staffVo;
    }

    @Override
    public StaffVo updateStaff(StaffDto staffDto) {
        if (GeneralTool.isEmpty(staffDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        //判断直属上司是否可以添加
        validateSupervisor(staffDto);
        Long aLong = validateUpdate(staffDto);
        if (!staffDto.getId().equals(aLong)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("num_exist"));
        }
        Staff staff = staffMapper.selectById(staffDto.getId());
        String loginPs = staff.getLoginPs();
        BeanUtils.copyProperties(staffDto, staff);
        if (GeneralTool.isNotEmpty(staff.getLoginPs())) {
            staff.setLoginPs(MD5Utils.encrypt(staff.getLoginPs()));
        } else {
            staff.setLoginPs(loginPs);
        }
        if (GeneralTool.isNotEmpty(staffDto.getEmailPassword())) {
            if (!staffDto.getEmailPassword().equals(staff.getEmailPassword())){
                try {
                    staff.setEmailPassword(AESUtils.Encrypt(staffDto.getEmailPassword(), AESConstant.AESKEY));
                } catch (Exception e) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                }
            }
        }


//        utilService.setUpdateInfo(staff);
//        staffMapper.updateByPrimaryKeySelective(staff);
        utilService.updateUserInfoToEntity(staff);
        staffMapper.updateByIdWithNull(staff);
        setMediaTableId(staffDto.getFkMediaId(), staffDto.getId());
        CacheUtil.clear(STAFF_NAME_CACHE);
        CacheUtil.clear(ALL_STAFF_IDS_CACHE);
        return findStaffById(staff.getId());
    }

    /**
     * 判断直属上司的添加
     *
     * @param staffDto
     */
    private void validateSupervisor(StaffDto staffDto) {
        if (GeneralTool.isNotEmpty(staffDto.getFkStaffIdSupervisor())) {
            List<Long> subordinateIds = staffMapper.getAllSubordinateIds(String.valueOf(staffDto.getId()));
            List<Long> list = new ArrayList<>();
            list.add(staffDto.getId());
            list.addAll(subordinateIds);
            if (list.contains(staffDto.getFkStaffIdSupervisor())) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("supervisor_set_error"));
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Boolean isActive = SecureUtil.getStaffInfo().getIsActive();
        if (!isActive) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("user_no_active"));
        }
        deleteService.deleteValidateStaff(id);
//        int i = staffMapper.deleteByPrimaryKey(id);
        int i = staffMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }

        LambdaQueryWrapper<StaffSuperior> staffSuperiorLambdaQueryWrapper = new LambdaQueryWrapper<>();
        staffSuperiorLambdaQueryWrapper.eq(StaffSuperior::getFkStaffId,id);
        staffSuperiorMapper.delete(staffSuperiorLambdaQueryWrapper);
        //同步删除业务上级关系表
        CacheUtil.clear(STAFF_NAME_CACHE);
        CacheUtil.clear(ALL_STAFF_IDS_CACHE);
    }

    @Override
    public String getStaffNameById(Long id) {
        String staffName = CacheUtil.get(
                STAFF_NAME_CACHE,
                "staffId:",
                id,
                ()->staffMapper.getStaffNameById(id)
        );
        return staffName;
    }

    /**
     * 获取资源key对应的id
     * @param key
     * @param staffIds
     * @return
     */
    @Override
    public List<Long> getReStaffIdByKey(String key,List<Long> staffIds) {
        if (GeneralTool.isEmpty(staffIds) || GeneralTool.isEmpty(key)) {
            return Collections.emptyList();
        }
        return staffResourceMapper.getStaffIdByKey(staffIds,key);
    }

    @Override
    public Boolean updateLoginStaffInfoByStaffId(Long staffId) {
        Staff staff = this.getById(staffId);
        if (staff == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        StaffInfo staffInfo = BeanCopyUtils.objClone(staff, StaffInfo::new);
        staffInfo.setStaffId(staffId);
        //将最新的staffInfo更新到全局redis缓存中
        SecureUtil.updateStaffInfoByStaffId(staffId, staffInfo);

        CacheUtil.clear(STAFF_NAME_CACHE);
        CacheUtil.clear(ALL_STAFF_IDS_CACHE);
        return true;
    }

    @Override
    public Map<Long, String> getStaffNamesByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return map;
        }
//        Example example = new Example(Staff.class);
//        example.createCriteria().andIn("id", ids);
//        List<Staff> staffList = staffMapper.selectByExample(example);
        List<Staff> staffList = staffMapper.selectBatchIds(ids);
        if (GeneralTool.isEmpty(staffList)) {
            return map;
        }
        Set<Long> departmentIds = staffList.stream().map(Staff::getFkDepartmentId).collect(Collectors.toSet());
//        Map<Long, String> departmentNameMap = departmentService.getDepartmentNamesByIds(departmentIds);
        for (Staff staff : staffList) {
            if (GeneralTool.isNotEmpty(staff.getNameEn())) {
//                if (GeneralTool.isNotEmpty(departmentIds) && GeneralTool.isNotEmpty(staff.getFkDepartmentId())) {
//                    map.put(staff.getId(), staff.getName() + "（" + staff.getNameEn() + "）" + "（" + departmentNameMap.get(staff.getFkDepartmentId()) + "）");
//                } else {
                    map.put(staff.getId(), staff.getName() + "（" + staff.getNameEn() + "）");
//                }

            } else {
//                if (GeneralTool.isNotEmpty(departmentIds) && GeneralTool.isNotEmpty(staff.getFkDepartmentId())) {
//                    map.put(staff.getId(), staff.getName() + "（" + departmentNameMap.get(staff.getFkDepartmentId()) + "）");
//                } else {
                    map.put(staff.getId(), staff.getName());
//                }
            }
        }
        return map;
    }

    @Override
    public Map<Long, String> getStaffByIdsAndCompanyIds(StaffByIdsAndCompanyIdsDto staffByIdsAndCompanyIdsDto) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(staffByIdsAndCompanyIdsDto.getStaffIds())) {
            return map;
        }
        List<Staff> staffList = staffMapper.selectStaffByIdsAndCompanyIds(staffByIdsAndCompanyIdsDto.getStaffIds(),staffByIdsAndCompanyIdsDto.getCompanyIds());
        if (GeneralTool.isEmpty(staffList)) {
            return map;
        }
        for (Staff staff : staffList) {
//            备用状态，目前这个是直接加在名字后面的，后期可能会改
            String status = staff.getIsActive() ? "":"离";
            if (GeneralTool.isNotEmpty(staff.getNameEn())) {
//                map.put(staff.getId(), staff.getName() +"（" + status + "）"+"（" + staff.getNameEn() + "）");
                map.put(staff.getId(), staff.getName() + "（" + staff.getNameEn() + "）");
            } else {
//                map.put(staff.getId(), staff.getName() +"（" + status + "）");
                map.put(staff.getId(), staff.getName());
            }
        }
        return map;
    }

    /**
     * 根据员工ids获取员工LoginIds
     *
     * @Date 18:31 2021/6/24
     * <AUTHOR>
     */
    @Override
    public Map<Long, String> getStaffLoginIdByIds(Set<Long> ids) {
//        Example example = new Example(Staff.class);
//        example.createCriteria().andIn("id", ids);
//        List<Staff> staffList = staffMapper.selectByExample(example);

        List<Staff> staffList = staffMapper.selectBatchIds(ids);
        Map<Long, String> map = new HashMap<>();
        for (Staff staff : staffList) {
            map.put(staff.getId(), staff.getLoginId());
        }
//        for (Long id : ids) {
//            String staffName = staffMapper.getStaffLoginIdByIds(id);
//            map.put(id, staffName);
//        }
        return map;
    }

    @Override
    public List<MediaAndAttachedVo> addStaffMedia(List<MediaAndAttachedDto> mediaAttachedVos) {
        if (GeneralTool.isEmpty(mediaAttachedVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        if (mediaAttachedVos.get(0).getTypeKey().equals(FileTypeEnum.PERMISSION_HEAD_ICON.key)) {
            if (GeneralTool.isNotEmpty(mediaAttachedVos.get(0).getFkTableId())) {
                MediaAndAttachedDto mediaAndAttached = new MediaAndAttachedDto();
                mediaAndAttached.setFkTableName(mediaAttachedVos.get(0).getFkTableName());
                mediaAndAttached.setFkTableId(mediaAttachedVos.get(0).getFkTableId());
                mediaAndAttached.setTypeKey(mediaAttachedVos.get(0).getTypeKey());
                attachedService.delete(mediaAndAttached);
            }
        }
        List<MediaAndAttachedVo> mediaAndAttachedVos = new ArrayList<>();
        for (MediaAndAttachedDto mediaAndAttachedDto : mediaAttachedVos) {
            //设置插入的表
            mediaAndAttachedDto.setFkTableName(TableEnum.PERMISSION_STAFF.key);
            mediaAndAttachedVos.add(attachedService.addMediaAndAttached(mediaAndAttachedDto));
        }
        return mediaAndAttachedVos;
    }

    @Override
    public List<BaseSelectEntity> getStaffList(Long companyId, List<String> departNums) {
        List<Long> companyIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(companyId)) {
            if (!SecureUtil.validateCompany(companyId)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
            companyIds.add(companyId);
        } else {
            companyIds = SecureUtil.getCompanyIds();
            if (GeneralTool.isEmpty(companyIds)) {
                companyIds.add(0L);
            }
        }
        return staffMapper.getStaffList(companyIds, departNums);
    }

    @Override
    public List<BaseSelectEntity> getOnDutyStaffList(Long companyId) {
        List<Long> companyIds = new ArrayList<>();
        if (GeneralTool.isNotEmpty(companyId)) {
            if (!SecureUtil.validateCompany(companyId)) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
            }
            companyIds.add(companyId);
        } else {
            companyIds = SecureUtil.getCompanyIds();
            if (GeneralTool.isEmpty(companyIds)) {
                companyIds.add(0L);
            }
        }
        return staffMapper.getOnDutyStaffList(companyIds);
    }

    @Override
    public void loginOut() {
        UserInfo user = GetAuthInfo.getUser();
        if (user != null && jwtProperties.getState()) {
            String token = JwtUtil.getToken(WebUtil.getRequest().getHeader(TokenConstant.HEADER));
            JwtUtil.removeAccessToken(String.valueOf(user.getLoginId()), token);
        }
//        return Kv.create().set("success", "true").set("msg", "success");
    }

    @Override
    public List<String> getStaffCountry(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return getCountryList(staffId);
    }

    @Override
    public List<String> getStaffOffice(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return getOfficeList(staffId);
    }

    @Override
    public List<MediaAndAttachedVo> getStaffMedia(MediaAndAttachedDto attachedVo, Page page) {
        if (GeneralTool.isEmpty(attachedVo.getFkTableId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        attachedVo.setFkTableName(TableEnum.PERMISSION_STAFF.key);
        return attachedService.getMediaAndAttachedDto(attachedVo, page);
    }

    @Override
    public List<StaffVo> getAllStaffDtoList(StaffQueryDto staffVo) {
//        Example example = getStaffExample(staffVo);
//        example.setOrderByClause("fk_department_id,fk_position_id,name");
//        example.setOrderByClause("is_on_duty DESC,fk_department_id ASC,fk_position_id ASC,name ASC");
        LambdaQueryWrapper<Staff> wrapper = getStaffLambdaQueryWrapper(staffVo);
        wrapper.orderByAsc(Staff::getFkDepartmentId, Staff::getFkPositionId, Staff::getName);
        wrapper.orderByDesc(Staff::getIsOnDuty);

//        List<Staff> staffList = staffMapper.selectByExample(example);
//        List<StaffVo> staffVos = staffList.stream().map(staff -> Tools.objClone(staff, StaffVo.class)).collect(Collectors.toList());
        List<Staff> staffList = staffMapper.selectList(wrapper);
        List<StaffVo> staffVos = BeanCopyUtils.copyListProperties(staffList, StaffVo::new);
        setName(staffVos);
        return staffVos.stream().sorted(Comparator.comparing(StaffVo::getIsOnDuty).reversed()).collect(Collectors.toList());
    }

//    @Override
//    public void updateStaffSessionId(Long id, Serializable sessionid) {
////        Staff staff = staffMapper.selectByPrimaryKey(id);
//        Staff staff = staffMapper.selectById(id);
//        if (GeneralTool.isEmpty(staff)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        staff.setSessionId(sessionid.toString());
////        staffMapper.updateByPrimaryKey(staff);
//        staffMapper.updateById(staff);
//    }

    @Override
    public Long editComment(CommentDto commentDto) {
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getId())) {
                commentDto.setFkTableName(TableEnum.PERMISSION_STAFF.key);
                commentService.updateComment(commentDto);
            } else {
                commentDto.setFkTableName(TableEnum.PERMISSION_STAFF.key);
                commentService.addComment(commentDto);
            }
        }
        return commentDto.getId();
    }

    @Override
    public List<CommentVo> getComments(CommentDto commentDto, Page page) {
        commentDto.setFkTableName(TableEnum.PERMISSION_STAFF.key);
        return commentService.datas(commentDto, page);
    }

    @Override
    public void updateStaffPwd(StaffDto staffDto) {
        if (GeneralTool.isEmpty(staffDto)) {
            /*throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));*/
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_is_empty"));
        }
        /* String PW_PATTERN = "^(?![A-Za-z]+$)(?![A-Z\\d]+$)(?![A-Z\\W]+$)(?![a-z\\d]+$)(?![a-z\\W]+$)(?![\\d\\W]+$)\\S{8,12}$";*/
        String PW_PATTERN = "^(?=.*\\d)(?=.*[a-zA-Z])(?=.*\\W)\\S{8,12}";
        if (!staffDto.getNewLoginPs().matches(PW_PATTERN)) {
            /* throw new GetServiceException(LocaleMessageUtils.getMessage("pwd_check_msg"));*/
            throw new GetServiceException(LocaleMessageUtils.getMessage("pwd_check_msg"));
        }
        Staff staff = staffMapper.selectById(GetAuthInfo.getStaffId());
        if (GeneralTool.isEmpty(staff)) {
            /*throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));*/
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (!staff.getLoginPs().equals(MD5Utils.encrypt(staffDto.getLoginPs()))) {
            /*throw new GetServiceException(LocaleMessageUtils.getMessage("old_pwd_incorrect"));*/
            throw new GetServiceException(LocaleMessageUtils.getMessage("old_pwd_incorrect"));
        }
        staff.setLoginPs(MD5Utils.encrypt(staffDto.getNewLoginPs()));
        staff.setIsModifiedPs(false);
//        staffMapper.updateByPrimaryKey(staff);
        staffMapper.updateById(staff);
        String key = "get:token".concat("::").concat("token:state:").concat(String.valueOf(SecureUtil.getStaffId()));
        Set<String> keys = getRedis.keys(key + "*");
        if (GeneralTool.isNotEmpty(keys)) {
            getRedis.del(keys);
            LogLogin logLogin = new LogLogin();
            logLogin.setFkStaffId(SecureUtil.getStaffId());
            logClient.updateLogLogin(logLogin);
            log.info("修改密码，清除该账户所有token信息。");
        }
        //修改密码后，要求重新登录或者下次登录有效，这里不处理表示下次登录时才有效
        //shiro登录操作
//        UsernamePasswordToken token = new UsernamePasswordToken(staffDto.getLoginId(), MD5Utils.encrypt(staffDto.getNewLoginPs()));
//        if (subject != null) {
//            subject.logout();
//        }
//        LoginUtil.login(token);
    }

    @Override
    public ResponseBo getVerifyCode(String username) {
        if (StringUtils.isBlank(username)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Staff staff = staffMapper.selectOne(Wrappers.<Staff>lambdaQuery().eq(Staff::getLoginId, username));
        StringBuilder stringBuilder;
        if (staff != null) {
            Boolean required = staff.getIsVcodeRequired();
            if (required != null && required) {
                String mobile = staff.getMobile();
                if (GeneralTool.isEmpty(mobile)) {
                    log.error("人员手机号码为空");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("phone_number_is_empty"));
                }
                //手机区号
                String mobileAreaCode = staff.getMobileAreaCode();
                if (GeneralTool.isEmpty(mobileAreaCode)) {
                    log.error("人员手机区号为空");
                    throw new GetServiceException(LocaleMessageUtils.getMessage("phone_area_code_is_empty"));
                }
                stringBuilder = new StringBuilder(mobile);
                String tplId;
                //香港区号
                if (CommonUtil.INLAND_AREA_CODE.equals(mobileAreaCode)){
                    tplId = "248255";
                    stringBuilder.replace(3,7,"****");
                } else if (CommonUtil.HMT.contains(mobileAreaCode)) {
                    stringBuilder.replace(3,6,"***");
                    tplId = "12253";
                }else {
                    stringBuilder.replace(3,5,"**");
                    tplId = "12252";
                }
                String key  = CacheNames.USER_CACHE + ":smsCode" + ":"+ staff.getId() + ":";
                Object o = getRedis.getValueOps().get(key);
                if (o != null) {
                    return new ResponseBo<>(ErrorCodeEnum.REQUEST_OK.getCode(),true,"已发送验证码,请稍后再试!有效期为10分钟", stringBuilder.toString());
                }
                //短信模板参数
                Map<String, Object> maps = new HashMap<>(16);
                String verifyCode = CommonUtil.getVerifyCode(6);
                maps.put("code", verifyCode);
//                String areaCode852 = "852";  15096008327

                SmsDto smsDto = new SmsDto();
                smsDto.setPhoneNumber(mobile);
                smsDto.setPhoneArea(mobileAreaCode);
                smsDto.setTplId(tplId);
                smsDto.setParamMaps(maps);
                Result<Boolean> result = reminderCenterClient.sendSms(smsDto);
                if (result.isSuccess() && result.getData()!=null && result.getData()) {
                    if (verifyCode!= null) {
                        getRedis.getValueOps().set(key,verifyCode,10, TimeUnit.MINUTES);
                    }
                }else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("failed_to_send_sms_verification_code"));
                }
            }else {
                return new ResponseBo<>(ErrorCodeEnum.REQUEST_OK.getCode(),false,null,null);
            }
        }else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("user_does_not_exist"));
        }
        return new ResponseBo<>(ErrorCodeEnum.REQUEST_OK.getCode(),true,"短信验证码发送成功！有效期为10分钟",stringBuilder.toString());
    }

    @Override
    public ResponseBo verifyUser(String username) {
        if (StringUtils.isBlank(username)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
        }
        Staff staff = staffMapper.selectOne(Wrappers.<Staff>lambdaQuery().eq(Staff::getLoginId, username));
        if (staff != null) {
            Boolean required = staff.getIsVcodeRequired();
            if (required != null && required) {
               return new ResponseBo<>(ErrorCodeEnum.REQUEST_OK.getCode(),true,null,null);
            }
        }
        return new ResponseBo<>(ErrorCodeEnum.REQUEST_OK.getCode(),false,null,null);
    }

    @Override
    public List<Long> getStaffIdsByNameKey(String staffNameKey) {
//        Example example = new Example(Staff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andLike("name", "%" + staffNameKey + "%");
//        List<Staff> staffs = staffMapper.selectByExample(example);
        List<Staff> staffs = staffMapper.selectList(Wrappers.<Staff>query().lambda().like(Staff::getName, staffNameKey).or().like(Staff::getNameEn, staffNameKey));
        if (GeneralTool.isNotEmpty(staffs)) {
            return staffs.stream().map(staff -> staff.getId()).collect(Collectors.toList());
        }
        List<Long> result = new ArrayList<>();
        result.add(0L);
        return result;
    }

    @Override
    public List<Long> getStaffIdsByNameKeyOrEnNameKey(String staffNameKeyOrEnNameKey) {
//        Example example = new Example(Staff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.orLike("name", "%" + staffNameKeyOrEnNameKey + "%").
//                orLike("nameEn", "%" + staffNameKeyOrEnNameKey + "%");
//        List<Staff> staffs = staffMapper.selectByExample(example);
        List<Staff> staffs = staffMapper.selectList(Wrappers.<Staff>query().lambda().like(Staff::getName, staffNameKeyOrEnNameKey).or().like(Staff::getNameEn, staffNameKeyOrEnNameKey));
        if (GeneralTool.isNotEmpty(staffs)) {
            return staffs.stream().map(staff -> staff.getId()).collect(Collectors.toList());
        }
        List<Long> result = new ArrayList<>();
        result.add(0L);
        return result;
    }

    @Override
    public Map<String,Long> getStaffIdsByAttendanceNum(Set<String> attendanceNums,Long fkCompanyId){
       List<Staff> staffs= staffMapper.getStaffIdsByAttendanceNum(attendanceNums,fkCompanyId);
       Map<String,Long> data = new HashMap<>();
       if(GeneralTool.isNotEmpty(staffs)){
           data = staffs.stream().collect(Collectors.toMap(Staff::getAttendanceNum,Staff::getId,(k, v) -> k));
       }
       return data;
    }

    @Override
    public Map<String,BaseSelectEntity> getStaffIdsByNameAndEnName(List<BaseSelectEntity> entities){
        for(BaseSelectEntity entity:entities){
           Long staffId = staffMapper.getStaffIdByNameAndEnName(entity.getName(),entity.getNameChn());
           if(GeneralTool.isNotEmpty(staffId)){
               entity.setId(staffId);
           }
        }
       Map<String,BaseSelectEntity> data= entities.stream().collect(Collectors.toMap(BaseSelectEntity::getFullName, Function.identity()));
        return data;
    }

    @Override
    public List<Long> getStaffIdsByCompanyId(Long companyId) {
//        List<Long> staffIds = staffMapper.getStaffIdsByCompanyId(Collections.singletonList(companyId));
//        //防止in()报错
//        if (GeneralTool.isEmpty(staffIds)) {
//            staffIds.add(0L);
//        }
        return staffMapper.getStaffIdsByCompanyId(Collections.singletonList(companyId));
    }

    @Override
    public Boolean saveResumeGuid(String guid) {
        Staff staff = staffMapper.selectById(GetAuthInfo.getStaffId());
        staff.setFkResumeGuid(guid);
        staffMapper.updateById(staff);
        updateLoginStaffInfoByStaffId(staff.getId());
        return true;
    }

    @Override
    public Map<Long, String> getCompanyNamesByStaffIds(Set<Long> staffIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(staffIds)) {
            return map;
        }
        List<StaffVo> companyNameByStaffIds = staffMapper.getCompanyNameByStaffIds(staffIds);
        if (GeneralTool.isEmpty(companyNameByStaffIds)) {
            return map;
        }
        for (StaffVo companyNameByStaffId : companyNameByStaffIds) {
            map.put(companyNameByStaffId.getId(), companyNameByStaffId.getCompanyName());
        }
        return map;
    }

    @Override
    public void saveResume(String guid) {
//        Example example = new Example(Staff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andLike("fkResumeGuid", guid);
//        List<Staff> staffs = staffMapper.selectByExample(example);
        List<Staff> staffs = staffMapper.selectList(Wrappers.<Staff>query().lambda().like(Staff::getFkResumeGuid, guid));
        for (Staff staff : staffs) {
            staff.setIsModifiedResume(false);
            staffMapper.updateById(staff);
            setIsModifiedResume(staff.getId());
        }
    }

    /**
     * 更新缓存中存储的staffinfo
     *
     * @param staffId
     */
    public void setIsModifiedResume(Long staffId) {
        Staff staff = staffMapper.selectById(staffId);
        StaffInfo staffInfo = SecureUtil.getStaffInfoByStaffId(staffId);
        if (GeneralTool.isNotEmpty(staffInfo)) {
            staffInfo = BeanCopyUtils.objClone(staff, StaffInfo::new);
            staffInfo.setStaffId(staffId);
            SecureUtil.updateStaffInfoByStaffId(staffId, staffInfo);
        }
    }

    private void setName(List<StaffVo> staffVos) {
        if (GeneralTool.isNotEmpty(staffVos)) {
            //查询所有公司类型Map
            Set<Long> companyIds = staffVos.stream().map(StaffVo::getFkCompanyId).collect(Collectors.toSet());
            Map<Long, String> companyFullNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(companyIds)) {
                companyFullNamesByIds = companyService.getCompanyFullNamesByIds(companyIds);
            }
            //查询所有部门类型
            Set<Long> departmentIds = staffVos.stream().map(StaffVo::getFkDepartmentId).collect(Collectors.toSet());
            Map<Long, String> departmentNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(departmentIds)) {
                departmentNamesByIds = departmentService.getDepartmentNamesByIds(departmentIds);
            }
            //查询所有职位类型
            Set<Long> posiitionIds = staffVos.stream().map(StaffVo::getFkPositionId).collect(Collectors.toSet());
            Map<Long, String> posiitionNamesByIds = new HashMap<>();
            if (GeneralTool.isNotEmpty(posiitionIds)) {
                posiitionNamesByIds = positionService.getPositionNamesByIds(posiitionIds);
            }
            for (StaffVo staffVo : staffVos) {
                //查询公司名称
                //setCompanyName(staffVo);
                if (GeneralTool.isNotEmpty(staffVo.getFkCompanyId())){
                    staffVo.setCompanyName(companyFullNamesByIds.get(staffVo.getFkCompanyId()));
                }
                //部门名称
                //setDepartmentName(staffVo);
                if (GeneralTool.isNotEmpty(staffVo.getFkDepartmentId())){
                    staffVo.setDepartmentName(departmentNamesByIds.get(staffVo.getFkDepartmentId()));
                }
                //职位
                //setPositionName(staffVo);
                if (GeneralTool.isNotEmpty(staffVo.getFkPositionId())){
                    staffVo.setPositionName(posiitionNamesByIds.get(staffVo.getFkPositionId()));
                }
                //密码隐藏
                staffVo.setLoginPs("************");
                //全称
                StringBuffer stringBuffer = new StringBuffer();
                if (GeneralTool.isNotEmpty(staffVo.getIsOnDuty()) && !staffVo.getIsOnDuty()) {
                    stringBuffer.append("【离职】");
                }
                stringBuffer.append(staffVo.getName());
                if (GeneralTool.isNotEmpty(staffVo.getNameEn())) {
                    stringBuffer.append('(').append(staffVo.getNameEn()).append(')');
                }
                staffVo.setFullName(stringBuffer.toString());
            }
        }
    }

    private void setPropertiesName(List<StaffVo> staffVos, List<StaffAreaCountry> areaCountries, List<com.get.institutioncenter.vo.AreaCountryVo> areaCountryVos) {
        if (GeneralTool.isNotEmpty(staffVos)) {
            for (StaffVo staffVo : staffVos) {
                List<String> fkAreaCountryKeys = areaCountries.stream().filter(staffAreaCountry -> staffAreaCountry.getFkStaffId().equals(staffVo.getId())).distinct().map(StaffAreaCountry::getFkAreaCountryKey).collect(Collectors.toList());
                List<String> stringList = new ArrayList<>();
                if(GeneralTool.isNotEmpty(fkAreaCountryKeys) && GeneralTool.isNotEmpty(areaCountryVos))
                {
                    fkAreaCountryKeys.stream().forEach(o->{
                        areaCountryVos.stream().forEach(i->{
                            if(o.equals(i.getNum()))
                            {
                                stringList.add(i.getNameChn());
                            }
                        });
                    });
                }
                //获取业务国家
                staffVo.setAreaCountryDtos(stringList);
                //获取业务办公室
                staffVo.setStaffOfficeDtos(getOfficeList(staffVo.getId()));
                //查询公司名称
                setCompanyName(staffVo);
                //部门名称
                setDepartmentName(staffVo);
                //职位
                setPositionName(staffVo);
                //密码隐藏
                staffVo.setLoginPs("************");
                if (GeneralTool.isNotEmpty(staffVo.getIsActive())) {
                    //是否激活
                    staffVo.setIsActiveName(staffVo.getIsActive() ? "是" : "否");
                }
                //全称
                StringBuffer stringBuffer = new StringBuffer();
                if (GeneralTool.isNotEmpty(staffVo.getIsOnDuty()) && !staffVo.getIsOnDuty()) {
                    stringBuffer.append("【离职】");
                }
                stringBuffer.append(staffVo.getName());
                if (GeneralTool.isNotEmpty(staffVo.getNameEn())) {
                    stringBuffer.append('(').append(staffVo.getNameEn()).append(')');
                }
                staffVo.setFullName(stringBuffer.toString());
            }
        }
    }

    //    private Example getStaffExample(StaffDto staffVo) {
//        Example example = new Example(Staff.class);
//        Example.Criteria criteria = example.createCriteria();
//        Example.Criteria criteria1 = example.createCriteria();
//        if (GeneralTool.isEmpty(staffVo) || GeneralTool.isEmpty(staffVo.getFkCompanyId())) {
//            List<Long> companyIds = getCompanyIds();
//            criteria.andIn("fkCompanyId", companyIds);
//        }
//        if (GeneralTool.isNotEmpty(staffVo)) {
//            if (GeneralTool.isNotEmpty(staffVo.getFkCompanyId())) {
//                criteria.andEqualTo("fkCompanyId", staffVo.getFkCompanyId());
//            }
//            if (GeneralTool.isNotEmpty(staffVo.getFkDepartmentId())) {
//                criteria.andEqualTo("fkDepartmentId", staffVo.getFkDepartmentId());
//            }
//            if (GeneralTool.isNotEmpty(staffVo.getFkPositionId())) {
//                criteria.andEqualTo("fkPositionId", staffVo.getFkPositionId());
//            }
//            if (GeneralTool.isNotEmpty(staffVo.getIsActive())) {
//                criteria.andEqualTo("isActive", staffVo.getIsActive());
//            }
//            if (GeneralTool.isNotEmpty(staffVo.getIsOnDuty())) {
//                criteria.andEqualTo("isOnDuty", staffVo.getIsOnDuty());
//            }
//            if (GeneralTool.isNotEmpty(staffVo.getKeyWord())) {
//                criteria1.orLike("name", "%" + staffVo.getKeyWord() + "%");
//                criteria1.orLike("nameEn", "%" + staffVo.getKeyWord() + "%");
//                criteria1.orLike("num", "%" + staffVo.getKeyWord() + "%");
//                criteria1.orLike("loginId", "%" + staffVo.getKeyWord() + "%");
//                example.and(criteria1);
//            }
//
//        }
//        return example;
//    }
    private LambdaQueryWrapper<Staff> getStaffLambdaQueryWrapper(StaffQueryDto staffVo) {
        LambdaQueryWrapper<Staff> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isEmpty(staffVo) || GeneralTool.isEmpty(staffVo.getFkCompanyId())) {
            List<Long> companyIds = getCompanyIds();
            wrapper.in(Staff::getFkCompanyId, companyIds);
        }
        if (GeneralTool.isNotEmpty(staffVo)) {
            if (GeneralTool.isNotEmpty(staffVo.getFkCompanyId())) {
                wrapper.eq(Staff::getFkCompanyId, staffVo.getFkCompanyId());
            }
            if (GeneralTool.isNotEmpty(staffVo.getFkDepartmentId())) {
                wrapper.eq(Staff::getFkDepartmentId, staffVo.getFkDepartmentId());
            }
            if (GeneralTool.isNotEmpty(staffVo.getFkPositionId())) {
                wrapper.eq(Staff::getFkPositionId, staffVo.getFkPositionId());
            }
            if (GeneralTool.isNotEmpty(staffVo.getIsActive())) {
                wrapper.eq(Staff::getIsActive, staffVo.getIsActive());
            }
            if (GeneralTool.isNotEmpty(staffVo.getIsOnDuty())) {
                wrapper.eq(Staff::getIsOnDuty, staffVo.getIsOnDuty());
            }
            if (GeneralTool.isNotEmpty(staffVo.getKeyWord())) {
                wrapper.and(wrapper_ ->
                        wrapper_.like(Staff::getName, staffVo.getKeyWord()).or()
                                .like(Staff::getNameEn, staffVo.getKeyWord()).or()
                                .like(Staff::getNum, staffVo.getKeyWord()).or()
                                .like(Staff::getLoginId, staffVo.getKeyWord()));
            }

            if (GeneralTool.isNotEmpty(staffVo.getStaffKeyWord())) {
                wrapper.and(wrapper_ ->
                        wrapper_.like(Staff::getName, staffVo.getStaffKeyWord()).or()
                                .like(Staff::getNameEn, staffVo.getStaffKeyWord()).or()
                                .like(Staff::getNum, staffVo.getStaffKeyWord()).or()
                                .like(Staff::getLoginId, staffVo.getStaffKeyWord()));
            }


//            if (GeneralTool.isNotEmpty(staffVo.getFkInstitutionPermissionGroupId())) {
//                LambdaQueryWrapper<Staff> subWrapper = new LambdaQueryWrapper<>();
//                subWrapper.eq(RInstitutionPermissionGroupStaff::getFkInstitutionPermissionGroupId, staffVo.getFkInstitutionPermissionGroupId())
//                        .eq(RInstitutionPermissionGroupStaff::getFkStaffId, Staff::getId);
//                //关联r_institution_permission_group_staff表
//                Long groupId = staffVo.getFkInstitutionPermissionGroupId();
//                if (GeneralTool.isNotEmpty(staffVo.getIsBind())){
//
//                    //根据isBind的值进行查询
//                    if (staffVo.getIsBind() == 1) {
//                        //绑定的参数
//                        // EXISTS 逻辑
////                        wrapper.exists(
////                                "SELECT 1 FROM r_institution_permission_group_staff WHERE " +
////                                        "fk_institution_permission_group_id = {0}",
////                                groupId, Staff::getId
////                        );
//                        wrapper.exists(subWrapper.select(RInstitutionPermissionGroupStaff::getFkStaffId));
//                    } else if (staffVo.getIsBind()== 0) {
//                        //未绑定的参数
//                        // NOT EXISTS 逻辑
////                        wrapper.notExists(
////                                "SELECT 1 FROM r_institution_permission_group_staff WHERE " +
////                                        "fk_institution_permission_group_id = {0} AND fk_staff_id = {1}",
////                                groupId, Staff::getId
////                        );
//                        wrapper.notExists(subWrapper.select(RInstitutionPermissionGroupStaff::getFkStaffId));
//                    }
//                }

//            }
        }
        return wrapper;
    }

    private List<Long> getCompanyIds() {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (GeneralTool.isEmpty(companyIds)) {
            companyIds.add(0L);
        }
        return companyIds;
    }

    //新增校验是否存在
    private boolean validateChange(StaffDto staffDto) {
//        Example example = new Example(Staff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("num", staffDto.getNum());
//        criteria.andEqualTo("fkCompanyId", staffDto.getFkCompanyId());
//        List<Staff> staff = staffMapper.selectByExample(example);
        List<Staff> staff = staffMapper.selectList(Wrappers.<Staff>query().lambda().like(Staff::getNum, staffDto.getNum()).eq(Staff::getFkCompanyId, staffDto.getFkCompanyId()).ne(Staff::getId, staffDto.getId()));
        List<Staff> loginStaff = staffMapper.selectList(Wrappers.<Staff>query().lambda().eq(Staff::getLoginId, staffDto.getLoginId()).ne(Staff::getId, staffDto.getId()));
        return GeneralTool.isEmpty(staff)&&GeneralTool.isEmpty(loginStaff);
    }

    //编辑校验是否存在
    private Long validateUpdate(StaffDto staffDto) {
//        Example example = new Example(Staff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("num", staffDto.getNum());
//        criteria.andEqualTo("fkCompanyId", staffDto.getFkCompanyId());
//        criteria.andEqualTo("fkDepartmentId", staffDto.getFkDepartmentId());
//        List<Staff> positions = staffMapper.selectByExample(example);
        List<Staff> staffs = staffMapper.selectList(Wrappers.<Staff>query().lambda().like(Staff::getNum, staffDto.getNum())
                .eq(Staff::getNum, staffDto.getNum())
                .eq(Staff::getFkCompanyId, staffDto.getFkCompanyId())
                .eq(Staff::getFkDepartmentId, staffDto.getFkDepartmentId()));
        return GeneralTool.isNotEmpty(staffs) ? staffs.get(0).getId() : staffDto.getId();
    }

    //国家list转换
    private ArrayList<StaffAreaCountryVo> ConvertCountryList(List<AreaCountryVo> areaCountryFeign) {
        ArrayList<StaffAreaCountryVo> staffAreaCountryVos = new ArrayList<>();
        for (AreaCountryVo countryDto : areaCountryFeign) {
            StaffAreaCountryVo staffAreaCountry = new StaffAreaCountryVo();
            staffAreaCountry.setName(countryDto.getName());
            staffAreaCountry.setNum(countryDto.getNum());
            staffAreaCountry.setNameChn(countryDto.getNameChn());
            staffAreaCountryVos.add(staffAreaCountry);
        }
        return staffAreaCountryVos;
    }

    //获取业务国家
    private List<String> getCountryList(Long id) {
        //定义员工业务国家
        List<String> stringList = new ArrayList<>();
//        ArrayList<StaffAreaCountryVo> staffAreaCountryDtos = null;
//        //查询中间表的数据
//        Example example = new Example(StaffAreaCountry.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkStaffId", id);
//        //获取所有的业务国家
//        List<StaffAreaCountry> areaCountries = areaCountryMapper.selectByExample(example);
//        //服务调用查询所有的国家
//        ListResponseBo responseBo = institutionService.getAreaCountrys(null);
//        if (ErrorCodeEnum.REQUEST_OK.getCode().equals(responseBo.getCode())) {
//            JSONArray objects = JSONUtil.parseArray(responseBo.getDatas());
//            List<AreaCountryVo> areaCountryFeign = JSONUtil.toList(objects, AreaCountryVo.class);
//            if (GeneralTool.isNotEmpty(areaCountries) && GeneralTool.isNotEmpty(areaCountryFeign)) {
//                staffAreaCountryDtos = ConvertCountryList(areaCountryFeign);
//                //遍历判断是否选中
//                for (StaffAreaCountryVo areaCountryDto : staffAreaCountryDtos) {
//                    for (StaffAreaCountry areaCountry : areaCountries) {
//                        boolean flag = areaCountry.getFkAreaCountryKey().equals(areaCountryDto.getNum());
//                        if (flag) {
//                            stringList.add(areaCountryDto.getNameChn());
//                        }
//                    }
//                }
//            }
//        }
        return stringList;
    }

    /**
     * 头像链接设置tableId
     *
     * @param fkMediaId
     * @param returnId
     */
    private void setMediaTableId(Long fkMediaId, long returnId) {
        if (GeneralTool.isNotEmpty(fkMediaId)) {
            attachedService.updateTableId(fkMediaId, returnId);
        }
    }

    //获取业务办公室
    private List<String> getOfficeList(Long staffId) {
        List<String> officeList = new ArrayList<>();
        List<OfficeVo> staffOffice = officeService.getStaffOffice(staffId);
        if (GeneralTool.isNotEmpty(staffOffice)) {
            officeList = staffOffice.stream().map(OfficeVo::getName).collect(Collectors.toList());
        }
        return officeList;
    }


    //获取头像下载链接
    private MediaAndAttachedVo getHeadIconImg(Long staffId) {
        MediaAndAttachedDto attachedVo = new MediaAndAttachedDto();
        attachedVo.setFkTableName(TableEnum.PERMISSION_STAFF.key);
        attachedVo.setFkTableId(staffId);
        attachedVo.setTypeKey(FileTypeEnum.PERMISSION_HEAD_ICON.key);

        List<MediaAndAttachedVo> headIcon = attachedService.getMediaAndAttachedDto(attachedVo);
        if (GeneralTool.isNotEmpty(headIcon)) {
            return headIcon.get(0);
        }
        return null;
    }

    private void setPositionName(StaffVo staffVo) {
        if (GeneralTool.isNotEmpty(staffVo.getFkPositionId())) {
            PositionVo positionVo = positionService.findPositionById(staffVo.getFkPositionId());
            if (GeneralTool.isNotEmpty(positionVo)) {
                staffVo.setPositionName(positionVo.getName());
            }
        }
    }

    private void setDepartmentName(StaffVo staffVo) {
        if (GeneralTool.isNotEmpty(staffVo.getFkDepartmentId())) {
            DepartmentVo departMentVo = departmentService.findDepartmentById(staffVo.getFkDepartmentId());
            if (GeneralTool.isNotEmpty(departMentVo)) {
                staffVo.setDepartmentName(departMentVo.getName());
            }
        }
    }


    private void setAddressName(StaffVo staffVo) {
        if (GeneralTool.isNotEmpty(staffVo)) {
            //联系地址国家名称
            if (GeneralTool.isNotEmpty(staffVo.getFkAreaCountryId())) {
                Set<Long> countryIds = new HashSet<>();
                countryIds.add(staffVo.getFkAreaCountryId());
                Result<Map<Long, String>> result = institutionCenterClient.getCountryNamesByIds(countryIds);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    staffVo.setAreaCountryName(result.getData().get(staffVo.getFkAreaCountryId()));
                }

            }
            //联系地址州省名称
            if (GeneralTool.isNotEmpty(staffVo.getFkAreaStateId())) {
                Set<Long> stateIds = new HashSet<>();
                stateIds.add(staffVo.getFkAreaStateId());
                Result<Map<Long, String>> result = institutionCenterClient.getStateFullNamesByIds(stateIds);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    staffVo.setAreaStateName(result.getData().get(staffVo.getFkAreaStateId()));
                }

            }
            //联系地址城市名称
            if (GeneralTool.isNotEmpty(staffVo.getFkAreaCityId())) {
                Set<Long> cityIds = new HashSet<>();
                cityIds.add(staffVo.getFkAreaCityId());
                Result<Map<Long, String>> result = institutionCenterClient.getCityFullNamesByIds(cityIds);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    staffVo.setAreaCityName(result.getData().get(staffVo.getFkAreaCityId()));
                }

            }
            //联系地址城市区域名称
            if (GeneralTool.isNotEmpty(staffVo.getFkAreaCityDivisionId())) {
                Set<Long> cityDivisionIds = new HashSet<>();
                cityDivisionIds.add(staffVo.getFkAreaCityDivisionId());
                Result<Map<Long, String>> result = institutionCenterClient.getCityDivisionFullNamesByIds(cityDivisionIds);
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    staffVo.setAreaCityDivisionName(result.getData().get(staffVo.getFkAreaCityDivisionId()));
                }
            }
        }
    }

    private void setCompanyName(StaffVo staffVo) {
        if (GeneralTool.isNotEmpty(staffVo.getFkCompanyId())) {
            CompanyVo companyVo = companyService.findCompanyById(staffVo.getFkCompanyId());
            if (GeneralTool.isNotEmpty(companyVo)) {
                staffVo.setCompanyName(companyVo.getName());
            }
        }
    }

    private void setOfficeName(StaffVo staffVo) {
        if (GeneralTool.isNotEmpty(staffVo.getFkOfficeId())) {
            OfficeVo officeVo = officeService.findOfficeById(staffVo.getFkOfficeId());
            if (GeneralTool.isNotEmpty(officeVo)) {
                staffVo.setOfficeName(officeVo.getName());
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteGuid(String guid) {
//        Example example = new Example(Staff.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("fkResumeGuid", guid);
//        List<Staff> staffs = staffMapper.selectByExample(example);

        List<Staff> staffs = staffMapper.selectList(Wrappers.<Staff>query().lambda().eq(Staff::getFkResumeGuid, guid));

        for (Staff staff : staffs) {
            staff.setFkResumeGuid(null);
//            staffMapper.updateByPrimaryKey(staff);
            staffMapper.updateById(staff);
//            updateStaffSession(staff.getId());
            updateLoginStaffInfoByStaffId(staff.getId());
        }
        CacheUtil.clear(STAFF_NAME_CACHE);
        CacheUtil.clear(ALL_STAFF_IDS_CACHE);
        return true;
    }

    @Override
    public List<Long> getAllDepartmentStaffIds(Long companyId, Long departmentId) {
        return staffMapper.getStaffIdsByDepartmentIds(companyId, departmentId);
    }

    /**
     * 根据部门id获取员工下拉框数据
     *
     * @Date 16:28 2021/7/6
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getStaffByDepartmentIds(List<Long> departmentIds) {
        return staffMapper.getStaffByDepartmentIds(departmentIds);
    }

    @Override
    public List<Long> getTopPositionStaffIds(Long companyId, Long departmentId) {
        return staffMapper.getTopPositionStaffIds(companyId, departmentId);
    }

    @Override
    public List<Long> getAllPositionStaffIds(Long companyId, List<Long> positionIdList) {
        return staffMapper.getAllPositionStaffIds(companyId, positionIdList);
    }

    @Override
    public List<Long> getPositionByNum(List<String> num) {
        List<Long> positionbynum = staffMapper.getPositionByNum(num);
        return positionbynum;

    }

    @Override
    public StaffVo getCompanyIdByCreateUser(String createUser) {
        return staffMapper.getCompanyIdByCreateUser(createUser);
    }

    @Override
    public List<StaffVo> getCompanyIdByCreateUsers(Set<String> createUsers) {
        if (GeneralTool.isEmpty(createUsers)) {
            return new ArrayList<>();
        }
        return staffMapper.getCompanyIdByCreateUsers(createUsers);
    }

    @Override
    public StaffVo getCompanyIdByStaffId(Long staffId) {
//        Staff staff = staffMapper.selectByPrimaryKey(staffId);
//        return Tools.objClone(staff, StaffVo.class);
        Staff staff = staffMapper.selectById(staffId);
        if (GeneralTool.isEmpty(staff)){
            return null;
        }
        StaffVo dto = BeanCopyUtils.objClone(staff, StaffVo::new);
        StringBuffer stringBuffer = new StringBuffer();
        if (GeneralTool.isNotEmpty(dto.getIsOnDuty()) && !dto.getIsOnDuty()) {
            stringBuffer.append("【离职】");
        }
        stringBuffer.append(staff.getName());
        if (GeneralTool.isNotEmpty(staff.getNameEn())) {
            stringBuffer.append('(').append(staff.getNameEn()).append(')');
        }
        dto.setFullName(stringBuffer.toString());
        return dto;
    }

    @Override
    public Boolean updateAnnualLeaveBase(Long id, BigDecimal annualLeaveBase) {
//        Staff staff = staffMapper.selectByPrimaryKey(id);
//        staff.setAnnualLeaveBase(annualLeaveBase);
//        staffMapper.updateByPrimaryKey(staff);

        Staff staff = staffMapper.selectById(id);
        staff.setAnnualLeaveBase(annualLeaveBase);
        staffMapper.updateById(staff);
        return true;
    }

    @Override
    public Boolean updateCompensatoryLeaveBase(Long id, BigDecimal compensatoryLeaveBase) {
//        Staff staff = staffMapper.selectByPrimaryKey(id);
//        staff.setCompensatoryLeaveBase(compensatoryLeaveBase);
//        staffMapper.updateByPrimaryKey(staff);
        Staff staff = staffMapper.selectById(id);
        staff.setCompensatoryLeaveBase(compensatoryLeaveBase);
        staffMapper.updateById(staff);
        return true;
    }

    /**
     * feign 调用根据员工ids获取对应的公司id
     *
     * @Date 10:24 2021/7/29
     * <AUTHOR>
     */
    @Override
    public Map<Long, Long> getCompanyIdByStaffIds(Set<Long> staffIds) {
        Map<Long, Long> map = new HashMap<>();
//        Example example = new Example(Staff.class);
//        example.createCriteria().andIn("id", staffIds);
//        List<Staff> staffList = staffMapper.selectByExample(example);
        List<Staff> staffList = staffMapper.selectBatchIds(staffIds);

        for (Staff staff : staffList) {
            map.put(staff.getId(), staff.getFkCompanyId());
        }
        return map;
    }

    /**
     * fei调用根据员工id获取员工下拉框数据
     *
     * @Date 14:58 2021/8/20
     * <AUTHOR>
     */
    @Override
    public List<BaseSelectEntity> getStaffByStaffIds(Set<Long> staffIds) {
        return staffMapper.getStaffByStaffIds(staffIds);
    }

    @Override
    public Map<Long, String> getStaffNameMap(Set<Long> staffId) {
//        Map<Long, String> map = new HashMap<>();
//        if (GeneralTool.isEmpty(staffId)) {
//            return map;
//        }
////        Example example = new Example(Staff.class);
////        example.createCriteria().andIn("id", staffId);
////        List<Staff> staffs = staffMapper.selectByExample(example);
//
//        List<Staff> staffs = staffMapper.selectBatchIds(staffId);
//        if (GeneralTool.isEmpty(staffs)) {
//            return map;
//        }
//        for (Staff staff1 : staffs) {
//            map.put(staff1.getId(), staff1.getName());
//        }
//        return map;

        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(staffId)) {
            return map;
        }
        List<Staff> staffList = staffMapper.selectBatchIds(staffId);
        if (GeneralTool.isEmpty(staffList)) {
            return map;
        }
        Set<Long> departmentIds = staffList.stream().map(Staff::getFkDepartmentId).collect(Collectors.toSet());
        Map<Long, String> departmentNameMap = departmentService.getDepartmentNamesByIds(departmentIds);
        for (Staff staff : staffList) {
            String staffName = "";
            if (!staff.getIsOnDuty()) {
                staffName = "【离职】";
            }
            if (GeneralTool.isNotEmpty(staff.getNameEn())) {
                staffName = staffName + staff.getName() + "（" + staff.getNameEn() + "）";
            } else {
                staffName = staffName + staff.getName();
            }
            map.put(staff.getId(), staffName);
        }
        return map;
    }

    @Override
    public Map<Long, String> getStaffNameDepartMentMap(Set<Long> staffId) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(staffId)) {
            return map;
        }
        List<Staff> staffList = staffMapper.selectBatchIds(staffId);
        if (GeneralTool.isEmpty(staffList)) {
            return map;
        }
        Set<Long> departmentIds = staffList.stream().map(Staff::getFkDepartmentId).collect(Collectors.toSet());
        Map<Long, String> departmentNameMap = departmentService.getDepartmentNamesByIds(departmentIds);
        for (Staff staff : staffList) {
            StringBuilder staffName = new StringBuilder();
            if (GeneralTool.isNotEmpty(departmentNameMap) && GeneralTool.isNotEmpty(departmentNameMap.get(staff.getFkDepartmentId()))){
                staffName.append("【"+departmentNameMap.get(staff.getFkDepartmentId())+"】");
            }
            map.put(staff.getId(),staffName.toString());
        }
        return map;
    }

    @Override
    public Map<Long, Staff> getStaffMapByStaffIds(Set<Long> staffId) {
        Map<Long, Staff> map = new HashMap<>();
        if (GeneralTool.isEmpty(staffId)) {
            return map;
        }
        List<Staff> staffList = staffMapper.selectBatchIds(staffId);
        if (GeneralTool.isEmpty(staffList)) {
            return map;
        }
        for (Staff staff : staffList) {
            map.put(staff.getId(), staff);
        }
        return map;
    }

    @Override
    public List<Long> getStaffIdByName(String startStaffName) {

//        Example example = new Example(Staff.class);
//        example.createCriteria().andLike("name", "%" + startStaffName + "%");
//        List<Staff> staff = staffMapper.selectByExample(example);

        List<Staff> staffs = staffMapper.selectList(Wrappers.<Staff>query().lambda().like(Staff::getName, startStaffName));
        if (CollectionUtil.isNotEmpty(staffs)) {
            return staffs.stream().map(Staff::getId).collect(Collectors.toList());
        } else {
            return null;
        }

    }

    @Override
    public Long getStaffSupervisorIdByStaffId(Long staffId) {
//        Staff staff = staffMapper.selectByPrimaryKey(staffId);
        Staff staff = staffMapper.selectById(staffId);
        if (GeneralTool.isEmpty(staff)) {
            return null;
        }
        if (GeneralTool.isEmpty(staff.getFkStaffIdSupervisor())) {
            return null;
        }
        return staff.getFkStaffIdSupervisor();
    }

    /**
     * 员工基本信息修改接口
     *
     * @Date 16:07 2021/11/22
     * <AUTHOR>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBasicStaffInfo(StaffInfoUpdateDto staffInfoUpdateDto) {
        Long id = GetAuthInfo.getStaffId();
        Staff staff = staffMapper.selectById(id);
        if (GeneralTool.isEmpty(staff)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        BeanUtils.copyProperties(staffInfoUpdateDto, staff);
        utilService.updateUserInfoToEntity(staff);
        staffMapper.updateById(staff);
        setMediaTableId(staffInfoUpdateDto.getFkMediaId(), id);
        StaffInfo staffInfo = SecureUtil.getStaffInfoByStaffId(id);
        if (staffInfo != null) {
            BeanUtils.copyProperties(staff,staffInfo);
            staffInfo.setStaffId(id);
            SecureUtil.updateStaffInfoByStaffId(id, staffInfo);
        }
//        List<Long> staffids = new ArrayList<>();
//        staffids.add(id);
//        groupGradeResourceService.updateStaffSession(staffids);
        CacheUtil.clear(STAFF_NAME_CACHE);
        CacheUtil.clear(ALL_STAFF_IDS_CACHE);
    }

    /**
     * 根据loginId获取员工信息
     *
     * @Date 12:04 2021/11/25
     * <AUTHOR>
     */
    @Override
    public Staff findStaffByLoginId(String loginId) {
        List<Staff> staff = staffMapper.selectList(Wrappers.<Staff>query().lambda().eq(Staff::getLoginId, loginId));
        if (GeneralTool.isEmpty(staff)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("target_login_id_not_exists"));
        }
        return staff.get(0);
    }

    @Override
    public StaffInfoVo staffInfo(String userName, String password, String isAvatarLogin, String avatarLogin) {
        //先判断用户是否存在，如属于阿尔法登录，则判断管理员的密码是否一致；如不属于阿尔法登录，则直接判断改用户的密码是否一致
        Staff staff = staffMapper.selectOne(Wrappers.<Staff>query().lambda().eq(Staff::getLoginId, userName).last("limit 1"));
        if (staff == null) {
            return null;
        }
        if (GeneralTool.isNotEmpty(isAvatarLogin) && "1".equals(isAvatarLogin)) {
            //判断管理员的密码是否正确(密码为空不校验，表示为刷新token)
            if (GeneralTool.isNotEmpty(password)) {
                if (!"admin".equals(avatarLogin)) {
                    return null;
                }
                Staff adminStaff = staffMapper.selectOne(Wrappers.<Staff>query().lambda().eq(Staff::getLoginId, "admin").eq(Staff::getLoginPs, password));
                if (adminStaff == null) {
                    return null;
                }
            }
        } else {
            //判断管理员的密码是否正确(密码为空不校验，表示为刷新token)
            if (GeneralTool.isNotEmpty(password)) {
                staff = staffMapper.selectOne(Wrappers.<Staff>query().lambda().eq(Staff::getLoginId, userName).eq(Staff::getLoginPs, password));
                if (staff == null) {
                    return null;
                }
            }
        }
        //清理该员工的缓存：
        CacheUtil.clear(USER_CACHE + String.valueOf(staff.getId()));

        //获取员工apikey
        Set<String> apikeys = groupGradeResourceService.getStaffApiKeys(staff.getId());
        if (CollectionUtil.isNotEmpty(apikeys)) {
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), API_KEYS_CODE);//清除缓存
            CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), API_KEYS_CODE, apikeys);
        }
        //获取员工资源权限
        List<String> resourcekeys = groupGradeResourceService.getStaffResourceKeys(staff.getId());
        if (CollectionUtil.isNotEmpty(resourcekeys)) {
            log.info("==resourcekeys==>员工ID：{},明细{}",staff.getId(),GeneralTool.toJson(resourcekeys));
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), API_RESOURCE_KEYS_CODE);//清除缓存
            CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), API_RESOURCE_KEYS_CODE, resourcekeys);
        }
        StaffInfoVo staffInfoVo = BeanCopyUtils.objClone(staff, StaffInfoVo::new);
        staffInfoVo.setInterfaceConfiguration("HTI");
        if (CollectionUtil.isNotEmpty(apikeys)) {
            staffInfoVo.setApiKeys(apikeys);
        }
        if (CollectionUtil.isNotEmpty(resourcekeys)) {
            staffInfoVo.setResourceKeys(resourcekeys);
        }
        List<String> keys = new ArrayList<>();
        List<StaffAreaCountry> staffAreaCountrys = staffAreaCountryMapper.getStaffAreaCountrysByfkStaffId(staffInfoVo.getId());
        for (StaffAreaCountry staffAreaCountry : staffAreaCountrys) {
            keys.add(staffAreaCountry.getFkAreaCountryKey());
        }
        Result<List<Long>> result = institutionCenterClient.getCountryIdByKey(keys);
        if (!result.isSuccess()) {
            throw new GetServiceException(result.getMessage());
        }
        if (GeneralTool.isNotEmpty(result.getData())) {
            List<Long> ids = (List<Long>) result.getData();
            staffInfoVo.setCountryIds(ids);
        }
        //学校组权限
        List<Long> permissionGroupInstitutionIds = institutionPermissionGroupMapper.getPermissionGroupsInstitutionIds(staffInfoVo.getId());
        staffInfoVo.setPermissionGroupInstitutionIds(permissionGroupInstitutionIds);
        //bd权限
        List<StaffBd> staffBds = staffBdMapper.selectList(Wrappers.<StaffBd>lambdaQuery().eq(StaffBd::getFkStaffId, staffInfoVo.getId()));
        if (GeneralTool.isNotEmpty(staffBds)) {
            staffInfoVo.setBoundBdIds(staffBds.stream().map(StaffBd::getFkStaffIdBd).collect(Collectors.toList()));
        }

        List<StaffInstitution> staffInstitutions = staffInstitutionMapper.selectList(Wrappers.lambdaQuery(StaffInstitution.class).eq(StaffInstitution::getFkStaffId, staffInfoVo.getId()));
        if (GeneralTool.isNotEmpty(staffInstitutions)){
            List<Long> institutionIds = staffInstitutions.stream().map(StaffInstitution::getFkInstitutionId).collect(Collectors.toList());
            if (GeneralTool.isNotEmpty(institutionIds)) {
                staffInfoVo.setInstitutionIds(institutionIds);
            }
        }

        if (GeneralTool.isNotEmpty(staffInfoVo.getFkCompanyId())) {
            List<Long> companyIds = new ArrayList<>();
            List<Long> staffLimitList = companyService.getCompanyLimit(staffInfoVo.getFkCompanyId());
            if (GeneralTool.isNotEmpty(staffLimitList) && staffLimitList.contains(staffInfoVo.getId())){
                companyIds.add(staffInfoVo.getFkCompanyId());
            }else {
                companyIds = companyService.getChildCompany(staffInfoVo.getFkCompanyId());
            }
            List<StaffCompany> staffCompanies = staffCompanyMapper.selectList(Wrappers.<StaffCompany>lambdaQuery().eq(StaffCompany::getFkStaffId, staffInfoVo.getId()));
            if (GeneralTool.isNotEmpty(staffCompanies)) {
                companyIds.addAll(staffCompanies.stream().map(StaffCompany::getFkCompanyId).collect(Collectors.toList()));
            }
            staffInfoVo.setCompanyIds(companyIds);
        }
        //写入学校缓存
        if (CollectionUtil.isNotEmpty(staffInfoVo.getInstitutionIds())) {
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_INSTITUTION_IDS_CODE);//清除缓存
            CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), STAFF_INSTITUTION_IDS_CODE, staffInfoVo.getInstitutionIds());
        } else {
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_INSTITUTION_IDS_CODE);//清除缓存
        }
        //写入国家缓存
        if (CollectionUtil.isNotEmpty(staffInfoVo.getCountryIds())) {
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_COUNTRY_IDS_CODE);//清除缓存
            CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), STAFF_COUNTRY_IDS_CODE, staffInfoVo.getCountryIds());
        } else {
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_COUNTRY_IDS_CODE);//清除缓存
        }
        //写入学校权限组缓存
        if (CollectionUtil.isNotEmpty(staffInfoVo.getPermissionGroupInstitutionIds())) {
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_PERMISSION_GROUP_INSTITUTION_IDS_CODE);//清除缓存
            CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), STAFF_PERMISSION_GROUP_INSTITUTION_IDS_CODE, staffInfoVo.getPermissionGroupInstitutionIds());
        } else {
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_PERMISSION_GROUP_INSTITUTION_IDS_CODE);//清除缓存
        }
        //写入bd缓存
        if (CollectionUtil.isNotEmpty(staffInfoVo.getBoundBdIds())) {
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_BOUND_BD_IDS_CODE);//清除缓存
            CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), STAFF_BOUND_BD_IDS_CODE, staffInfoVo.getBoundBdIds());
        } else {
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_BOUND_BD_IDS_CODE);//清除缓存
        }
        //写入公司缓存
        if (CollectionUtil.isNotEmpty(staffInfoVo.getCompanyIds())) {
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_COMPANY_IDS_CODE);//清除缓存
            CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), STAFF_COMPANY_IDS_CODE, staffInfoVo.getCompanyIds());
        } else {
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_COMPANY_IDS_CODE);//清除缓存
        }
        //写入帮助信息缓存
        List<HelpInfoVo> helpInfoVos = helpCenterClient.getHelpInfo().getData();
        if (GeneralTool.isNotEmpty(helpInfoVos)){
            CacheUtil.evict(BIZ_CACHE,String.valueOf(staff.getId()),HELP_DESCRIPTION_CODE);//清除缓存
            CacheUtil.put(BIZ_CACHE,String.valueOf(staff.getId()),HELP_DESCRIPTION_CODE, helpInfoVos);
        }

        //写入员工信息缓存
        if (staff != null) {
            StaffInfo staffInfo = BeanCopyUtils.objClone(staff, StaffInfo::new);
            staffInfo.setInterfaceConfiguration("HTI");
            //更新fullName
            StringBuilder sb = new StringBuilder();
            sb.append(staffInfo.getName());
            if (GeneralTool.isNotEmpty(staffInfo.getNameEn())) {
                sb.append("（").append(staffInfo.getNameEn()).append("）");
            }
            staffInfo.setFullName(sb.toString());
            if (CollectionUtil.isNotEmpty(apikeys) && apikeys.contains(ProjectKeyEnum.STUDENT_OFFER_ITEM_FINANCIAL_HIDING.key)) {
                staffInfo.setIsStudentOfferItemFinancialHiding(true);
            } else {
                staffInfo.setIsStudentOfferItemFinancialHiding(false);
            }
            staffInfo.setStaffId(staff.getId());
            CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_INFO_CODE);//清除缓存
            CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), STAFF_INFO_CODE, staffInfo);
        }
        staffInfoVo.setTokenSessionId(CommonUtil.getTokenSessionId());
        return staffInfoVo;
    }

    /**
     * @Description: 获取当天生日的员工
     * @Author: Jerry
     * @Date:14:24 2021/12/1
     */
    @Override
    public void getDayOfStaffBirthday() {
        Date date = new Date();
        List<Staff> staffList = staffMapper.getDayOfStaffBirthday(date);
        //生成提醒任务
        if (GeneralTool.isNotEmpty(staffList)) {
            List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
            RemindTaskDto remindTaskDto = null;
            for (Staff staff : staffList) {
                remindTaskDto = new RemindTaskDto();
                remindTaskDto.setFkStaffId(staff.getId());
                remindTaskDto.setTaskTitle("员工生日关怀");
                remindTaskDto.setFkRemindEventTypeKey(ProjectKeyEnum.STAFF_BIRTHDAY_CARE.key);
                remindTaskDto.setStartTime(date);
                remindTaskDto.setTaskBgColor("#3788d8");
                remindTaskDto.setRemindMethod("1");
                remindTaskDto.setStatus(1);
                remindTaskDto.setFkTableName(TableEnum.PERMISSION_STAFF.key);
                remindTaskDto.setFkTableId(staff.getId());
                remindTaskDto.setFkDbName(ProjectKeyEnum.REMINDER_CENTER.key);
                remindTaskDto.setGmtCreateUser("admin");
                remindTaskDto.setGmtCreate(new Date());
                remindTaskDtos.add(remindTaskDto);
            }
            reminderCenterClient.batchAdd(remindTaskDtos);
        }
    }

    /**
     * 根据职位编号positionNums获取员工ids
     *
     * @param positionNums
     * @return
     */
    @Override
    public List<Long> getStaffIdsByPositionNums(Set<String> positionNums) {
        if (GeneralTool.isEmpty(positionNums)) {
           return Collections.emptyList();
        }
        return staffMapper.getStaffIdsByPositionNums(positionNums);
    }

    @Override
    public List<Long> getStaffIdsBykeyWord(String keyWord) {
        return staffMapper.getStaffIdsBykeyWord(keyWord);
    }

    @Override
    public List<BaseSelectEntity> getStaffByCompanyId(Long fkCompanyId) {
        return staffMapper.getStaffByCompanyId(fkCompanyId);
    }


    @Override
    public CompanyVo batchObtainStaffList() {
        List<Long> companyIds = SecureUtil.getCompanyIds();
        if (GeneralTool.isEmpty(companyIds)) {
            return null;
        }
        CompanyVo companyVos = companyService.getChildCompanyDto();
        Map<Long, List<StaffVo>> companyStaffMap = companyIds.stream()
                .collect(Collectors.toMap(
                        companyId -> companyId,
                        companyId -> staffMapper.getStaffDtos(companyId,null,null),
                        (oldList, newList) -> oldList  // 避免公司ID重复时的冲突（保留先加载的数据）
                ));
        companyVos.setStaffVoList(companyStaffMap.getOrDefault(companyVos.getId(), Collections.emptyList()));
        bindChildStaffVo(companyVos.getChildCompanyDto(), companyStaffMap);

        return companyVos;
    }

    @Override
    public List<Staff> getAllStaff() {
        List<Staff> staffs = staffMapper.selectList(new LambdaQueryWrapper<>());
        return staffs;
    }

    private void bindChildStaffVo(List<CompanyVo> childCompanies,
                                     Map<Long, List<StaffVo>> staffVoMap) {
        if (GeneralTool.isEmpty(childCompanies)) {
            return;
        }

        for (CompanyVo child : childCompanies) {
            child.setStaffVoList(staffVoMap.getOrDefault(child.getId(), Collections.emptyList()));
            if (GeneralTool.isEmpty(child.getChildCompanyDto())) {
                bindChildStaffVo(child.getChildCompanyDto(), staffVoMap);
            }
        }
    }


    @Override
    public List<Staff> getStaffs(Long fkCompanyId) {
        if (GeneralTool.isEmpty(fkCompanyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<Staff> staffs = staffMapper.getStaffs(fkCompanyId);
        if (GeneralTool.isNotEmpty(staffs)) {
            return staffs.stream().map(staff -> BeanCopyUtils.objClone(staff, Staff::new)).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<StaffVo> getStaffDtoByFkCompanyId(Long fkCompanyId){
        if (GeneralTool.isEmpty(fkCompanyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        List<StaffVo> staffVos = staffMapper.getStaffDtoByFkCompanyId(fkCompanyId);
        if (GeneralTool.isNotEmpty(staffVos)) {
            return staffVos;
        }
        return null;
    }

    @Override
    public List<StaffVo> getStaffDtos(Long fkCompanyId, Long fkDepartmentId, String staffNameKeyOrEnNameKey) {
        List<StaffVo> staffVos = staffMapper.getStaffDtos(fkCompanyId,fkDepartmentId,staffNameKeyOrEnNameKey);
        if (GeneralTool.isNotEmpty(staffVos)) {
            return staffVos;
        }
        return null;
    }

    @Override
    public List<Long> getAllSubordinateIds(Long staffId){
        if(GeneralTool.isEmpty(staffId)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        return staffMapper.getAllSubordinateIds(String.valueOf(staffId));
    }

    @Override
    public List<StaffVo> getStaffByIds(Set<Long> staffIds) {
        List<StaffVo> staffVos = staffMapper.getStaffByIds(staffIds);
        return staffVos;
    }

    @Override
    public StaffVo getStaffById(Long staffId) {
        return staffMapper.selectStaffInfoById(staffId);
    }

    @Override
    public List<Long> getStaffListByStaffName(String staffName) {
        List<Long> listStaff = staffMapper.getStaffListByStaffName(staffName);
        return listStaff;
    }

    @Override
    public Set<Long> getAllStaffIds() {
        List<Staff> staffs = CacheUtil.get(
                ALL_STAFF_IDS_CACHE,
                "staffId:",
                "all",
                ()->staffMapper.selectList(Wrappers.<Staff>lambdaQuery())
        );
        return staffs.stream().map(Staff::getId).collect(Collectors.toSet());
    }

    /**
     * Author Cream
     * Description : 获取员工在职状态
     * Date 2022/4/20 14:58
     * Params:  Set<Long> staffIds
     */
    @Override
    public Result<Map<Long, Boolean>> doGetStaffIsOnDuty(Set<Long> staffIds) {
        if (GeneralTool.isEmpty(staffIds)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(LocaleMessageUtils.getMessage("id_null")));
        }
        List<Staff> staff = staffMapper.selectList(Wrappers.<Staff>lambdaQuery().in(Staff::getId, staffIds));
        Map<Long,Boolean> resultMap=new HashMap<>();
        for (Staff s : staff) {
           resultMap.put(s.getId(),s.getIsOnDuty());
        }
        return Result.data(resultMap);
    }

    @Override //复制粘贴的接口 有部分修改
    public Map<Long, String> getStaffEnNameByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return null;
        }
        List<Staff> staffList = staffMapper.selectBatchIds(ids);
        if (GeneralTool.isEmpty(staffList)) {
            return null;
        }
        Set<Long> departmentIds = staffList.stream().map(Staff::getFkDepartmentId).collect(Collectors.toSet());
        for (Staff staff : staffList) {
            if (GeneralTool.isNotEmpty(staff.getNameEn())) {
                map.put(staff.getId(), staff.getNameEn());

            }
        }
        return map;
    }

    @Override
    public Map<Long, String> getStaffChnNameByIds(Set<Long> ids) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(ids)) {
            return null;
        }
        List<Staff> staffList = staffMapper.selectBatchIds(ids);
        if (GeneralTool.isEmpty(staffList)) {
            return null;
        }
        for (Staff staff : staffList) {
            if (GeneralTool.isNotEmpty(staff.getName())) {
                map.put(staff.getId(), staff.getName());
            }
        }
        return map;
    }

    @Override
    public List<DepartmentAndStaffVo> getDepartmentAndStaffDtoByStaffIds(Set<Long> staffIds) {
        if (GeneralTool.isEmpty(staffIds)){
            return null;
        }
        return staffMapper.getDepartmentAndStaffDtoByStaffIds(staffIds);
    }

    @Override
    public List<StaffVo> getStaffDtoByIds(Set<Long> staffIds){
        if (GeneralTool.isEmpty(staffIds)){
            return null;
        }
        return staffMapper.getStaffDtoByIds(staffIds);
    }

    @Override
    public StaffConfig getStaffConfigByType(String type) {
        if (GeneralTool.isBlank(type)) {
            return null;
        }
        Long staffId = SecureUtil.getStaffId();
        return staffConfigMapper.selectOne(Wrappers.<StaffConfig>lambdaQuery().eq(StaffConfig::getConfigTypeKey, type).eq(StaffConfig::getFkStaffId,staffId));
    }

    @Override
    public void saveStaffConfigByType(String type,List<String> keys) {
        if (GeneralTool.isBlank(type) || GeneralTool.isEmpty(keys)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        Long staffId = SecureUtil.getStaffId();
        StaffConfig staffConfig = staffConfigMapper.selectOne(Wrappers.<StaffConfig>lambdaQuery().eq(StaffConfig::getConfigTypeKey, type).eq(StaffConfig::getFkStaffId, staffId));
        CommonUtil.sort(keys);
        if (staffConfig != null) {
            String configValue = staffConfig.getConfigValue();
            if (StringUtils.isNotBlank(configValue)) {
                String key1 = configValue.trim();
                String key2 = keys.stream().collect(Collectors.joining(","));
                if (!key2.equals(key1)) {
                    staffConfig.setConfigValue(key2);
                    utilService.setUpdateInfo(staffConfig);
                    staffConfigMapper.updateById(staffConfig);
                }
            }
        }else {
            StaffConfig config = new StaffConfig();
            config.setFkStaffId(staffId);
            config.setConfigValue(keys.stream().collect(Collectors.joining(",")));
            config.setConfigTypeKey(type);
            utilService.setCreateInfo(config);
            staffConfigMapper.insert(config);
        }
    }

    @Override
    public List<StaffListByDepartmentVo> getStaffListByDepartmentId(Long companyId, Long departmentId) {
        if (GeneralTool.isEmpty(companyId)||GeneralTool.isEmpty(departmentId)){
            return new ArrayList<>();
        }
        return staffMapper.getStaffListByDepartmentId(companyId,departmentId);
    }

    @Override
    public List<BaseSelectEntity> getStaffByStaffName(List<Long> companyIds, String staffName) {
//        List<Long> companyIds = new ArrayList<>();
//        if (GeneralTool.isNotEmpty(companyId)) {
//            if (!SecureUtil.validateCompany(companyId)) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("no_data_permission"));
//            }
//            companyIds.add(companyId);
//        } else {
//            companyIds = SecureUtil.getCompanyIds();
//            if (GeneralTool.isEmpty(companyIds)) {
//                companyIds.add(0L);
//            }
//        }
        return staffMapper.getStaffByStaffName(companyIds, staffName);
    }

    /**
     * 用户偏好设置保存(自定义列)
     *
     * @Date 16:37 2022/6/1
     * <AUTHOR>
     */
    @Override
    public void addCustomColumnJson(AddUserPreferencesAddDto addUserPreferencesAddDto) {
        List<StaffConfig> staffConfigs = staffConfigMapper.selectList(Wrappers.<StaffConfig>lambdaQuery().eq(StaffConfig::getFkStaffId, GetAuthInfo.getStaffId()).eq(StaffConfig::getConfigTypeKey, ProjectKeyEnum.CUSTOM_COLUMN_JSON.key));
        if (GeneralTool.isEmpty(staffConfigs)) {
            StaffConfig staffConfig = new StaffConfig();
            staffConfig.setFkStaffId(GetAuthInfo.getStaffId());
            staffConfig.setConfigTypeKey(ProjectKeyEnum.CUSTOM_COLUMN_JSON.key);
            staffConfig.setConfigValue(addUserPreferencesAddDto.getCustomColumnJson());
            utilService.setCreateInfo(staffConfig);
            staffConfigMapper.insert(staffConfig);
        } else {
            StaffConfig staffConfig = staffConfigs.get(0);
            staffConfig.setConfigValue(addUserPreferencesAddDto.getCustomColumnJson());
            utilService.setUpdateInfo(staffConfig);
            staffConfigMapper.updateById(staffConfig);
        }
        StringBuffer str = new StringBuffer();
        str.append(ProjectKeyEnum.CUSTOM_COLUMN_JSON.key).append("_").append(GetAuthInfo.getStaffId());
        CacheUtil.put(USER_CACHE, String.valueOf(GetAuthInfo.getStaffId()), ProjectKeyEnum.CUSTOM_COLUMN_JSON.key, addUserPreferencesAddDto.getCustomColumnJson());
    }

    @Override
    public StaffInfoVo wxCpLogin(String code, String platformType) {
        if ("wxcptp".equals(platformType)){
            String userid;
            String avatarUrl;
            Result<WxCpUserVo> getUserIdResult = officeCenterClient.getWxCpUserIdByCode(code);
            if (getUserIdResult.isSuccess()&&GeneralTool.isNotEmpty(getUserIdResult.getData())){
                WxCpUserVo wxCpUserVo = getUserIdResult.getData();
                userid = wxCpUserVo.getUserId();
                avatarUrl = wxCpUserVo.getAvatarUrl();
            }else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("unable_to_find_employee"));
            }


            //企业微信登录 bms移动版
            List<Staff> staffList = staffMapper.selectList(Wrappers.<Staff>lambdaQuery()
                    .eq(Staff::getLoginId, userid).eq(Staff::getIsOnDuty, 1)
                    .eq(Staff::getIsActive, 1).last("limit 1"));
            if (GeneralTool.isNotEmpty(staffList)){
                Staff staff = staffList.get(0);

                List<PermissionMediaAndAttached> mediaAndAttacheds = attachedService.list(Wrappers.<PermissionMediaAndAttached>lambdaQuery()
                        .eq(PermissionMediaAndAttached::getTypeKey, FileTypeEnum.PERMISSION_HEAD_ICON.key)
                        .eq(PermissionMediaAndAttached::getFkTableName, TableEnum.PERMISSION_STAFF.key)
                        .eq(PermissionMediaAndAttached::getFkTableId, staff.getId()));

                if (GeneralTool.isEmpty(mediaAndAttacheds)){
                    //无头像时 上传保存头像
                    Boolean flag = doSetHeadIcon(staff, avatarUrl);
                    log.info("@doSetHeadIcon#保存头像flag：{}",flag);
                    if (!flag){
                        log.error("doSetHeadIcon保存用户头像失败！！");
                    }
                }

                //清理该员工的缓存：
                CacheUtil.clear(USER_CACHE + String.valueOf(staff.getId()));

                //获取员工apikey
                Set<String> apikeys = groupGradeResourceService.getStaffApiKeys(staff.getId());
                if (CollectionUtil.isNotEmpty(apikeys)) {
                    CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), API_KEYS_CODE);//清除缓存
                    CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), API_KEYS_CODE, apikeys);
                }
                //获取员工资源权限
                List<String> resourcekeys = groupGradeResourceService.getStaffResourceKeys(staff.getId());
                if (CollectionUtil.isNotEmpty(resourcekeys)) {
                    log.info("==resourcekeys==>员工ID：{},明细{}",staff.getId(),GeneralTool.toJson(resourcekeys));
                    CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), API_RESOURCE_KEYS_CODE);//清除缓存
                    CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), API_RESOURCE_KEYS_CODE, resourcekeys);
                }
                StaffInfoVo staffInfoVo = BeanCopyUtils.objClone(staff, StaffInfoVo::new);
                if (CollectionUtil.isNotEmpty(apikeys)) {
                    staffInfoVo.setApiKeys(apikeys);
                }
                if (CollectionUtil.isNotEmpty(resourcekeys)) {
                    staffInfoVo.setResourceKeys(resourcekeys);
                }
                List<String> keys = new ArrayList<>();
                List<StaffAreaCountry> staffAreaCountrys = staffAreaCountryMapper.getStaffAreaCountrysByfkStaffId(staffInfoVo.getId());
                for (StaffAreaCountry staffAreaCountry : staffAreaCountrys) {
                    keys.add(staffAreaCountry.getFkAreaCountryKey());
                }
                Result<List<Long>> result = institutionCenterClient.getCountryIdByKey(keys);
                if (!result.isSuccess()) {
                    throw new GetServiceException(result.getMessage());
                }
                if (GeneralTool.isNotEmpty(result.getData())) {
                    List<Long> ids = (List<Long>) result.getData();
                    staffInfoVo.setCountryIds(ids);
                }
                if (GeneralTool.isNotEmpty(staffInfoVo.getFkCompanyId())) {
                    List<Long> companyIds = companyService.getChildCompany(staffInfoVo.getFkCompanyId());
                    staffInfoVo.setCompanyIds(companyIds);
                }
                //写入国家缓存
                if (CollectionUtil.isNotEmpty(staffInfoVo.getCountryIds())) {
                    CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_COUNTRY_IDS_CODE);//清除缓存
                    CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), STAFF_COUNTRY_IDS_CODE, staffInfoVo.getCountryIds());
                }
                //写入公司缓存
                if (CollectionUtil.isNotEmpty(staffInfoVo.getCompanyIds())) {
                    CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_COMPANY_IDS_CODE);//清除缓存
                    CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), STAFF_COMPANY_IDS_CODE, staffInfoVo.getCompanyIds());
                }
                //写入员工信息缓存
                if (staff != null) {
                    StaffInfo staffInfo = BeanCopyUtils.objClone(staff, StaffInfo::new);
                    //更新fullName
                    StringBuilder sb = new StringBuilder();
                    sb.append(staffInfo.getName());
                    if (GeneralTool.isNotEmpty(staffInfo.getNameEn())) {
                        sb.append("（").append(staffInfo.getNameEn()).append("）");
                    }
                    staffInfo.setFullName(sb.toString());
                    if (CollectionUtil.isNotEmpty(apikeys) && apikeys.contains(ProjectKeyEnum.STUDENT_OFFER_ITEM_FINANCIAL_HIDING.key)) {
                        staffInfo.setIsStudentOfferItemFinancialHiding(true);
                    } else {
                        staffInfo.setIsStudentOfferItemFinancialHiding(false);
                    }
                    staffInfo.setStaffId(staff.getId());
                    CacheUtil.evict(USER_CACHE, String.valueOf(staff.getId()), STAFF_INFO_CODE);//清除缓存
                    CacheUtil.put(USER_CACHE, String.valueOf(staff.getId()), STAFF_INFO_CODE, staffInfo);
                }
                return staffInfoVo;
            }else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("unable_to_find_employee"));
            }
        }
        return null;
    }

    /**
     * 新增员工邮箱
     *
     * @Date 17:14 2022/11/21
     * <AUTHOR>
     */
    @Override
    public void addStaffEmail(StaffEmailDto staffEmailDto) {
        //新增邮件需要验证同分公司下的员工邮件地址是否有重复，如果有
        //需要提示：新增【<EMAIL>】电邮地址失败，XXX员工已经设置了该邮件地址。
        Staff staff = staffMapper.selectById(staffEmailDto.getFkStaffId());

        List<String> staffName = staffEmailMapper.checkStaffEmail(staff.getFkCompanyId(), staffEmailDto.getEmail(), staffEmailDto.getFkStaffId());
        if (GeneralTool.isNotEmpty(staffName)) {
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("staff_email_exist", staffEmailDto.getEmail(), staffName));
        }
        StaffEmail staffEmail = new StaffEmail();
        BeanCopyUtils.copyProperties(staffEmailDto, staffEmail);
        //新增邮件，第一个邮件，默认为主邮箱
        List<StaffEmail> staffEmails = staffEmailMapper.selectList(Wrappers.<StaffEmail>lambdaQuery().eq(StaffEmail::getFkStaffId, staffEmailDto.getFkStaffId()));
        if (GeneralTool.isEmpty(staffEmails)) {
            staffEmail.setIsPrimary(true);
            staff.setEmail(staffEmailDto.getEmail());
            if (GeneralTool.isNotEmpty(staffEmailDto.getEmailPassword())){
                try {
                    staff.setEmailPassword(AESUtils.Encrypt(staffEmailDto.getEmailPassword(), AESConstant.AESKEY));
                } catch (Exception e) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                }
            }
            utilService.setUpdateInfo(staff);
            staffMapper.updateById(staff);
        } else {
            staffEmail.setIsPrimary(false);
        }
        if (GeneralTool.isNotEmpty(staffEmailDto.getEmailPassword())){
            try {
                staffEmail.setEmailPassword(AESUtils.Encrypt(staffEmailDto.getEmailPassword(), AESConstant.AESKEY));
            } catch (Exception e) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
            }
        }
        utilService.setCreateInfo(staffEmail);
        staffEmailMapper.insert(staffEmail);
    }

    /**
     * 员工邮箱列表
     *
     * @Date 17:21 2022/11/21
     * <AUTHOR>
     */
    @Override
    public List<StaffEmailVo> staffEmailDatas(Long staffId) {
        List<StaffEmail> staffEmails = staffEmailMapper.selectList(Wrappers.<StaffEmail>lambdaQuery().eq(StaffEmail::getFkStaffId, staffId));
        return BeanCopyUtils.copyListProperties(staffEmails, StaffEmailVo::new);
    }

    /**
     * 删除员工邮箱
     *
     * @Date 17:47 2022/11/21
     * <AUTHOR>
     */
    @Override
    public void staffEmailDelete(Long id) {
        StaffEmail staffEmail = staffEmailMapper.selectById(id);
        Long fkStaffId = staffEmail.getFkStaffId();
        Staff staff = staffMapper.selectById(fkStaffId);
        if (GeneralTool.isEmpty(staff)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("target_login_id_not_exists"));
        }
        //删除，只有离职的员工才可以删除所有邮件设置，当删除主邮箱时，默认将主邮箱标记放到剩下的第一个邮箱上。只有离职的员工才可以删除最后一个邮箱。
        List<StaffEmail> staffEmails = staffEmailMapper.selectList(Wrappers.<StaffEmail>lambdaQuery().eq(StaffEmail::getFkStaffId, fkStaffId).ne(StaffEmail::getId, id));
        if (GeneralTool.isEmpty(staffEmails) && staff.getIsOnDuty()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("cannot_delete_message"));
        }
        //当删除主邮箱时，默认将主邮箱标记放到剩下的第一个邮箱上
        if (staffEmail.getIsPrimary() && GeneralTool.isNotEmpty(staffEmails)) {
            StaffEmail staffEmailOne = staffEmails.get(0);
            staffEmailOne.setIsPrimary(true);
            utilService.setUpdateInfo(staffEmailOne);
            staffEmailMapper.updateById(staffEmailOne);

            staff.setEmail(staffEmailOne.getEmail());
            utilService.setUpdateInfo(staff);
            staffMapper.updateById(staff);
        } else if (staffEmail.getIsPrimary() && GeneralTool.isEmpty(staffEmails)){
            staff.setEmail(null);
            utilService.setUpdateInfo(staff);
            staffMapper.updateByIdWithNull(staff);
        }
        staffEmailMapper.deleteById(id);
    }

    /**
     * 标记员工主邮箱
     *
     * @Date 17:50 2022/11/21
     * <AUTHOR>
     */
    @Override
    public void mainStaffEmailMark(Long id) {
        //一个员工只能标记一个主邮箱，当标记另一个时，需要取消原来的一个。
        StaffEmail staffEmail = staffEmailMapper.selectById(id);
        Long fkStaffId = staffEmail.getFkStaffId();
        StaffEmail staffEmailUpdate = new StaffEmail();
        staffEmailUpdate.setIsPrimary(false);
        utilService.setUpdateInfo(staffEmailUpdate);
        staffEmailMapper.update(staffEmailUpdate, Wrappers.<StaffEmail>lambdaUpdate().eq(StaffEmail::getFkStaffId, fkStaffId).eq(StaffEmail::getIsPrimary, true));

        staffEmail.setIsPrimary(true);
        utilService.setUpdateInfo(staffEmail);
        staffEmailMapper.updateById(staffEmail);

        Staff staff = staffMapper.selectById(staffEmail.getFkStaffId());
        staff.setEmail(staffEmail.getEmail());
        staff.setEmailPassword(staffEmail.getEmailPassword());
        utilService.setUpdateInfo(staff);
        staffMapper.updateById(staff);
    }

    /**
     * 编辑员工邮箱
     *
     * @Date 15:46 2022/11/22
     * <AUTHOR>
     */
    @Override
    public void staffEmailUpdate(StaffEmailDto staffEmailDto) {
        //需要提示：新增【<EMAIL>】电邮地址失败，XXX员工已经设置了该邮件地址。
        Staff staff = staffMapper.selectById(staffEmailDto.getFkStaffId());
        List<String> staffName = staffEmailMapper.checkStaffEmail(staff.getFkCompanyId(), staffEmailDto.getEmail(), staffEmailDto.getFkStaffId());
        if (GeneralTool.isNotEmpty(staffName)) {
            throw new GetServiceException(LocaleMessageUtils.getFormatMessage("staff_email_exist", staffEmailDto.getEmail(), staffName));
        }

        StaffEmail staffEmail = staffEmailMapper.selectById(staffEmailDto.getId());
        String emailPassword = staffEmail.getEmailPassword();
        BeanCopyUtils.copyProperties(staffEmailDto, staffEmail);
        if (GeneralTool.isNotEmpty(staffEmailDto.getEmailPassword())){
            if (!staffEmailDto.getEmailPassword().equals(emailPassword)){
                try {
                    staffEmail.setEmailPassword(AESUtils.Encrypt(staffEmailDto.getEmailPassword(), AESConstant.AESKEY));
                } catch (Exception e) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                }
            }
        }

        utilService.setUpdateInfo(staffEmail);
        staffEmailMapper.updateById(staffEmail);

        if (staffEmail.getIsPrimary()) {
            staff.setEmail(staffEmail.getEmail());
            if (GeneralTool.isNotEmpty(staffEmailDto.getEmailPassword())){
                if (!staffEmailDto.getEmailPassword().equals(staff.getEmailPassword())){
                    try {
                        staff.setEmailPassword(AESUtils.Encrypt(staffEmailDto.getEmailPassword(), AESConstant.AESKEY));
                    } catch (Exception e) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
                    }
                }
            }
            utilService.setUpdateInfo(staff);
            staffMapper.updateById(staff);
        }
    }

    @Override
    public List<StaffVo> getStaffDtosByDepartmentNums(Long countryId, Set<String> nums) {
        if (GeneralTool.isEmpty(nums)){
            return Lists.newArrayList();
        }
        List<StaffVo> staffVos = staffMapper.getStaffDtosByDepartmentNums(countryId,nums);
        return staffVos;
    }

    @Override
    public StaffVo getStaffByLoginId(String loginId) {
        return staffMapper.getStaffByLoginId(loginId);
    }

    @Override
    public List<BaseSelectEntity> getStaffByDepartmentIdsAndCountryNum(List<Long> departmentIds, String fkCountryNum) {
        return staffMapper.getStaffByDepartmentIdsAndCountryNum(departmentIds,fkCountryNum);
    }

    @Override
    public List<Long> getStaffIdsByLikeCondition(String gmtCreateUser) {
        if (GeneralTool.isEmpty(gmtCreateUser)){
            return Collections.emptyList();
        }
        List<Staff> staffs = list(Wrappers.lambdaQuery(Staff.class)
//                .eq(Staff::getIsOnDuty, true)
//                .eq(Staff::getIsActive, true)
                .and(wrapper -> wrapper.like(Staff::getName, gmtCreateUser)
                        .or().like(Staff::getNameEn, gmtCreateUser)
                        .or().like(Staff::getLoginId, gmtCreateUser)));
        List<Long> staffIds = Lists.newArrayList();
        if (GeneralTool.isNotEmpty(staffs)){
            staffIds = staffs.stream().map(Staff::getId).collect(Collectors.toList());
        }
        return staffIds;
    }

    @Override
    public List<Staff> getStaffByLoginIds(List<String> staffGmtCreate) {
        if (staffGmtCreate.isEmpty()) {
            return Collections.emptyList();
        }
        return staffMapper.selectList(Wrappers.<Staff>lambdaQuery().in(Staff::getLoginId, staffGmtCreate));
    }

    /**
     * Author Cream
     * Description : //获取所有下属员工
     * Date 2023/6/1 17:00
     * Params:
     * Return
     */
    @Override
    public List<StaffVo> getAllSubordinateInfo() {
        Long staffId = SecureUtil.getStaffId();
        List<Long> allSubordinateIds = getAllSubordinateIds(staffId);
        allSubordinateIds.add(staffId);
        return getStaffByIds(new HashSet<>(allSubordinateIds));
    }

    /**
     * 获取拥有resourceKey权限的所有用户ids
     * @param resourceKey
     * @param isContainAdmin
     * @return
     */
    @Override
    public List<Long> getStaffIdsByResourceKey(String resourceKey, Boolean isContainAdmin) {
        return staffMapper.getStaffIdsByResourceKey(resourceKey,isContainAdmin);
    }

    /**
     * 员工email password update
     *
     * @Date 16:08 2023/12/19
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateStaffEmailPassword(StaffEmailPasswordUpdateDto staffEmailPasswordUpdateDto) {
        Staff staff = staffMapper.selectById(staffEmailPasswordUpdateDto.getStaffId());
        if (GeneralTool.isEmpty(staff)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        try {
            staff.setEmailPassword(AESUtils.Encrypt(staffEmailPasswordUpdateDto.getEmailPassword(), AESConstant.AESKEY));
        } catch (Exception e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
        }
        utilService.setUpdateInfo(staff);
        staffMapper.updateById(staff);
        StaffEmail staffEmail = staffEmailMapper.selectOne(Wrappers.<StaffEmail>lambdaQuery().eq(StaffEmail::getFkStaffId, staffEmailPasswordUpdateDto.getStaffId()).eq(StaffEmail::getEmail, staffEmailPasswordUpdateDto.getEmail()));
        if (GeneralTool.isNotEmpty(staffEmail)) {
            staffEmail.setEmailPassword(staff.getEmailPassword());
            utilService.setUpdateInfo(staffEmail);
            staffEmailMapper.updateById(staffEmail);
        }
    }

    /**
     * 分配业务学校
     * @param staffInstitutionDtos
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void assignSchool(List<StaffInstitutionDto> staffInstitutionDtos) {
        if (GeneralTool.isEmpty(staffInstitutionDtos)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
//        Set<Long> staffIds = staffInstitutionDtos.stream().map(StaffInstitutionDto::getFkStaffId).filter(Objects::nonNull).collect(Collectors.toSet());

//        //先删后增
//        staffInstitutionMapper.delete(Wrappers.<StaffInstitution>lambdaQuery().in(StaffInstitution::getFkStaffId, staffIds));

        List<StaffInstitution> staffInstitutions = BeanCopyUtils.copyListProperties(staffInstitutionDtos, StaffInstitution::new,(staffInstitutionDto, staffInstitution)->{
            utilService.setCreateInfo(staffInstitution);
        });

        for (StaffInstitution staffInstitution : staffInstitutions) {
            staffInstitutionMapper.insert(staffInstitution);
        }
    }

    @Override
    public List<BusinessSchoolVo> getBusinessSchoolList(BusinessSchoolDto businessSchoolDto, Page page) {
        if (GeneralTool.isEmpty(businessSchoolDto)){
            return Collections.emptyList();
        }
        IPage<BusinessSchoolVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<BusinessSchoolVo> businessSchoolVos = staffInstitutionMapper.getBusinessSchoolList(iPage, businessSchoolDto);
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(businessSchoolVos)){
            return Collections.emptyList();
        }
        //获取集合的所有国家ids
        Set<Long> countryIdSet = businessSchoolVos.stream().map(BusinessSchoolVo::getFkAreaCountryId).collect(Collectors.toSet());
        Map<Long, String> countryFullNameMap = institutionCenterClient.getCountryFullNamesByIds(countryIdSet).getData();
        //根据学校类型ids获取名称
        Map<Long, String> institutionTypeNameByIds = institutionCenterClient.getAllInstitutionTypeName();
        for (BusinessSchoolVo businessSchoolVo : businessSchoolVos) {
            if (GeneralTool.isNotEmpty(businessSchoolVo.getFkAreaCountryId())){
                businessSchoolVo.setCountryName(countryFullNameMap.get(businessSchoolVo.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(businessSchoolVo.getFkInstitutionTypeId())){
                businessSchoolVo.setInstitutionTypeName(institutionTypeNameByIds.get(businessSchoolVo.getFkInstitutionTypeId()));
            }
        }
        return businessSchoolVos;
    }

    @Override
    public void deleteBusinessSchool(Long staffId, Long id) {
        if (GeneralTool.isEmpty(staffId) || GeneralTool.isEmpty(id)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        staffInstitutionMapper.delete(Wrappers.<StaffInstitution>lambdaQuery().eq(StaffInstitution::getFkStaffId, staffId).eq(StaffInstitution::getFkInstitutionId, id));
    }

    @Override
    public List<AssignBusinessSchoolVo> getAssignBusinessSchoolList(AssignBusinessSchoolDto assignBusinessSchoolDto, Page page) {
        if (GeneralTool.isEmpty(assignBusinessSchoolDto)){
            return Collections.emptyList();
        }
        IPage<AssignBusinessSchoolVo> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<AssignBusinessSchoolVo> assignBusinessSchoolVos = staffInstitutionMapper.getAssignBusinessSchoolList(iPage, assignBusinessSchoolDto);
        page.setAll((int) iPage.getTotal());
        if (GeneralTool.isEmpty(assignBusinessSchoolVos)){
            return Collections.emptyList();
        }
        if (GeneralTool.isEmpty(assignBusinessSchoolVos)){
            return Collections.emptyList();
        }
        //获取集合的所有国家ids
        Set<Long> countryIdSet = assignBusinessSchoolVos.stream().map(AssignBusinessSchoolVo::getFkAreaCountryId).collect(Collectors.toSet());
        Map<Long, String> countryFullNameMap = institutionCenterClient.getCountryFullNamesByIds(countryIdSet).getData();
        //根据学校类型ids获取名称
        Map<Long, String> institutionTypeNameByIds = institutionCenterClient.getAllInstitutionTypeName();
        for (AssignBusinessSchoolVo assignBusinessSchoolVo : assignBusinessSchoolVos) {
            if (GeneralTool.isNotEmpty(assignBusinessSchoolVo.getFkAreaCountryId())){
                assignBusinessSchoolVo.setCountryName(countryFullNameMap.get(assignBusinessSchoolVo.getFkAreaCountryId()));
            }
            if (GeneralTool.isNotEmpty(assignBusinessSchoolVo.getFkInstitutionTypeId())){
                assignBusinessSchoolVo.setInstitutionTypeName(institutionTypeNameByIds.get(assignBusinessSchoolVo.getFkInstitutionTypeId()));
            }
        }
        return assignBusinessSchoolVos;
    }

    @Override
    public Set<Long> getStaffDepartmentsById(Long staffId) {
        //获取下属员工所有部门
        Set<Long> staffFollowerIds = new HashSet<>();
        //员工id + 业务下属员工ids
        List<Long> staffFollowerIdsResult = superiorService.getStaffFollowerId(staffId);
        if (GeneralTool.isNotEmpty(staffFollowerIdsResult)) {
            staffFollowerIds = staffFollowerIdsResult.stream().filter(Objects::nonNull).distinct().collect(Collectors.toSet());
        }
        staffFollowerIds.add(staffId);

        List<StaffVo> staffVos = getStaffDtoByIds(staffFollowerIds);
        return staffVos.stream().map(StaffVo::getFkDepartmentId).collect(Collectors.toSet());
    }

    @Override
    public LocalDate getBirthdayByIDCard(String idCard) {
        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        if (idCard == null || idCard.length() != 18) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_length_of_id_card_number_is_incorrect")); // 身份证号码长度不正确
        }
        try {
            // 身份证号码中的出生日期位于第7位到第14位
            String birthdateStr = idCard.substring(6, 14);
            return LocalDate.parse(birthdateStr, formatter);
        } catch (DateTimeParseException e) {
            // 解析日期失败，可能是格式不正确
            return null;
        }
    }

    @Override
    public Boolean getIsStaffBusiness(Long fkStaffId) {
        List<StaffCompany> staffCompanies = staffCompanyMapper.selectList(Wrappers.<StaffCompany>lambdaQuery().eq(StaffCompany::getFkStaffId, fkStaffId));
        return Optional.ofNullable(GeneralTool.isNotEmpty(staffCompanies)).orElse(false);
    }

    @Override
    public Map<String, StaffVo> getStaffDtoMapByLoginIds(Set<String> loginIds) {
        Map<String, StaffVo> map = new HashMap<>();
        if (GeneralTool.isEmpty(loginIds)) {
            return map;
        }
        List<Staff> staffList = staffMapper.selectList(Wrappers.<Staff>lambdaQuery().in(Staff::getLoginId, loginIds));
        for (Staff staff : staffList) {
            StaffVo dto = BeanCopyUtils.objClone(staff, StaffVo::new);
            StringBuilder sb = new StringBuilder();
            sb.append(staff.getName());
            if (GeneralTool.isNotEmpty(staff.getNameEn())) {
                sb.append("（").append(staff.getNameEn()).append("）");
            }
            dto.setFullName(sb.toString());
            map.put(dto.getLoginId(), dto);
        }
        return map;
    }

    @Override
    public List<Long> getStaffIdsByCompanyIds(List<Long> companyIds) {
        return staffMapper.getStaffIdsByCompanyId(companyIds);
    }

    @Override
    public Map<Long, Set<Long>> getAllStaffSuperiorByStaffIds(Set<Long> staffIds) {
        if (GeneralTool.isEmpty(staffIds)) {
            return Collections.emptyMap();
        }
        Map<Long, Set<Long>> map = Maps.newHashMap();
        for (Long staffId : staffIds) {
            Set<Long> allStaffSuperior = getAllStaffSuperior(staffId);
            map.put(staffId, allStaffSuperior);
        }
        return map;
    }

    /**
     * 获取推送部门人员的邮箱
     * @param fkCountryId
     * @param fkInstitutionId
     * @param departmentList
     * @return
     */
    @Override
    public List<String> getPushDepartmentStaffEmail(Long fkCountryId, Long fkInstitutionId, List<String> departmentList) {
        return staffMapper.getPushDepartmentStaffEmail(fkCountryId, fkInstitutionId, departmentList);
    }

    /**
     * 获取指定权限资源key的员工id集合
     *
     * @param resourceKey 权限资源key
     * @return
     */
    @Override
    public List<Long> getAuthorizedStaffIdsByResourceKey(String resourceKey) {
        return staffMapper.getAuthorizedStaffIdsByResourceKey(resourceKey);
    }

    /**
     * 获取直接下属id集合（一层）
     * @param staffId
     * @return
     */
    @Override
    public List<Long> getObtainDirectSubordinatesIds(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            return null;
        }
        List<Long> staffFollowerIds = staffMapper.getObtainDirectSubordinatesIds(staffId);
        if (GeneralTool.isEmpty(staffFollowerIds)) {
            staffFollowerIds = new ArrayList<>();
            return staffFollowerIds;
        } else {
            return staffFollowerIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toList());
        }
    }

    @Override
    public Set<Long> getStaffSupervisorIds(Set<Long> ids) {
        List<Staff> staffs = staffMapper.selectList(Wrappers.<Staff>lambdaQuery().eq(Staff::getId, ids));
        if (GeneralTool.isEmpty(staffs)) {
            return null;
        }
        Set<Long> staffIds = staffs.stream().map(Staff::getFkStaffIdSupervisor).collect(Collectors.toSet());
        if (GeneralTool.isEmpty(staffIds)) {
            return null;
        }
        return staffIds;
    }

    @Override
    public List<StaffVo> getStaffDatas(StaffQueryDto staffQueryDto, Page page) {

        LambdaQueryWrapper<Staff> staffWrapper = getStaffLambdaQueryWrapper(staffQueryDto);
        IPage<Staff> iPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));

        IPage<StaffVo> pages = staffMapper.getStaffDatas(iPage, staffWrapper);
        List<StaffVo> staffs = pages.getRecords();
        page.setAll((int) pages.getTotal());
        List<StaffVo> staffVos = BeanCopyUtils.copyListProperties(staffs, StaffVo::new);

        if (GeneralTool.isNotEmpty(staffVos)) {
            for (StaffVo staffVo : staffVos) {
                //获取业务办公室
                staffVo.setStaffOfficeDtos(getOfficeList(staffVo.getId()));
                //查询公司名称
                setCompanyName(staffVo);
                //部门名称
                setDepartmentName(staffVo);
                //职位
                setPositionName(staffVo);

                if (GeneralTool.isNotEmpty(staffVo.getIsActive())) {
                    //是否激活
                    staffVo.setIsActiveName(staffVo.getIsActive() ? "是" : "否");
                }
                //全称
                StringBuffer stringBuffer = new StringBuffer();
                if (GeneralTool.isNotEmpty(staffVo.getIsOnDuty()) && !staffVo.getIsOnDuty()) {
                    stringBuffer.append("【离职】");
                }
                stringBuffer.append(staffVo.getName());
                if (GeneralTool.isNotEmpty(staffVo.getNameEn())) {
                    stringBuffer.append('(').append(staffVo.getNameEn()).append(')');
                }
                staffVo.setFullName(stringBuffer.toString());
            }
        }

        return staffVos;
    }

    /**
     * 获取某个员工的所有上级id集合
     *
     * @param staffId 员工id
     * @return
     */
    private Set<Long> getAllStaffSuperior(Long staffId) {
        if (GeneralTool.isEmpty(staffId)) {
            return Collections.emptySet();
        }
        Set<Long> superiorIds = Sets.newHashSet();
        List<StaffSuperiorVo> staffSuperiorVos = staffSuperiorMapper.getStaffSuperiorByIds(Collections.singleton(staffId));
        if (GeneralTool.isNotEmpty(staffSuperiorVos)) {
            Set<Long> superiors = staffSuperiorVos.stream()
                    .map(StaffSuperiorVo::getFkStaffSuperiorId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toSet());
            superiorIds.addAll(superiors);
            for (Long superiorId : superiors) {
                Set<Long> ids = getAllStaffSuperior(superiorId);
                superiorIds.addAll(ids);
            }
        }
        return superiorIds;
    }


    /**
     * 保存头像
     *
     * @param staff
     * @param avatarUrl
     */
    private Boolean doSetHeadIcon(Staff staff, String avatarUrl) {
        String sourceUrl = avatarUrl;
        int i = avatarUrl.lastIndexOf("/");
        int k = avatarUrl.lastIndexOf(".");
        if (i>k){
            avatarUrl = avatarUrl+".png";
            k = avatarUrl.lastIndexOf(".");
        }
        String orcName = avatarUrl.substring(i+1, avatarUrl.length());
        String fileName = avatarUrl.substring(i+1, k);
        int j = orcName.lastIndexOf(".");
        String contentType = orcName.substring(j+1, orcName.length());

        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        InputStream is = null;

        try {
            byte[] buff = new byte[8192];
            is = new URL(sourceUrl).openStream();

            int count = 0;
            while ((count = is.read(buff)) != -1) {
                bos.write(buff, 0, count);
            }

            MultipartFile multipartFile = new ConvertToMultipartFile(bos.toByteArray(),fileName,orcName,contentType,bos.toByteArray().length);
            MultipartFile[] multipartFiles = new MultipartFile[1];
            multipartFiles[0] = multipartFile;
            //上传头像
            List<FileDto> fileDtos = fileCenterClient.upload(multipartFiles, LoggerModulesConsts.PERMISSIONCENTER).getData();
            FileDto fileDto = fileDtos.get(0);
            List<MediaAndAttachedDto> mediaAndAttachedDtos = Lists.newArrayList();
            MediaAndAttachedDto mediaAndAttachedDto = new MediaAndAttachedDto();
            mediaAndAttachedDto.setFkFileGuid(fileDto.getFileGuid());
            mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
            mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
            mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
            mediaAndAttachedDto.setFkTableName(TableEnum.PERMISSION_STAFF.key);
            mediaAndAttachedDto.setTypeKey(FileTypeEnum.PERMISSION_HEAD_ICON.key);
            mediaAndAttachedDto.setFkTableId(staff.getId());
            mediaAndAttachedDtos.add(mediaAndAttachedDto);
            //保存头像
            addStaffMedia(mediaAndAttachedDtos);

        }catch (Exception e){
            return false;
        }finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }

            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return true;
    }

}
