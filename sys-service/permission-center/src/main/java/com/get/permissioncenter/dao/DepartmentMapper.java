package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.permissioncenter.entity.Department;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface DepartmentMapper extends BaseMapper<Department> {

    /**
     * 查找排序最大值
     *
     * @return
     */
    Integer getMaxViewOrder();

    int insertSelective(Department department);

    /**
     * @return java.util.List<com.get.common.com.get.permissioncenter.vo.entity.BaseSelectEntity>
     * @Description :部门下拉框数据
     * @Param [id]
     * <AUTHOR>
     */
    List<BaseSelectEntity> getDepartmentSelect(@Param("id") Long id);

    /**
     * @return java.util.List<java.lang.String>
     * @Description :销售中心feign调用 部门编号对应的部门名称
     * @Param [departmentNumList]
     * <AUTHOR>
     */
    List<String> getDepartmentNameList(@Param("departmentNumList") List<String> departmentNumList);


    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Boolean isExistByCompanyId(Long companyId);

    /**
     * @return java.lang.String
     * @Description :根据部门id查找对应部门名称
     * @Param [departmentId]
     * <AUTHOR>
     */
    String getDepartmentNameById(@Param("departmentId") Long departmentId);
}