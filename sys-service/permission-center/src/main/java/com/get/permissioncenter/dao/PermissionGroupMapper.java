package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.entity.PermissionGroup;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface PermissionGroupMapper extends BaseMapper<PermissionGroup> {

    int insert(PermissionGroup record);

    int insertSelective(PermissionGroup record);

    Integer getMaxViewOrder();


    /**
     * @return java.lang.Boolean
     * @Description: 是否有关联数据
     * @Param [staffId]
     * <AUTHOR>
     */
    Bo<PERSON>an isExistByCompanyId(Long companyId);

    Long getMaxId();
}