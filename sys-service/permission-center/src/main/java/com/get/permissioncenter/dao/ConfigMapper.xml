<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.ConfigMapper">
  <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.Config">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="config_group" jdbcType="VARCHAR" property="configGroup" />
    <result column="config_key" jdbcType="VARCHAR" property="configKey" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="value1" jdbcType="VARCHAR" property="value1" />
    <result column="value2" jdbcType="VARCHAR" property="value2" />
    <result column="value3" jdbcType="VARCHAR" property="value3" />
    <result column="value4" jdbcType="VARCHAR" property="value4" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <insert id="insert" parameterType="com.get.permissioncenter.entity.Config" keyProperty="id" useGeneratedKeys="true">
    insert into m_config (id, config_group, config_key, 
      description, value1, value2, 
      value3, value4, gmt_create, 
      gmt_create_user, gmt_modified, gmt_modified_user
      )
    values (#{id,jdbcType=BIGINT}, #{configGroup,jdbcType=VARCHAR}, #{configKey,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{value1,jdbcType=VARCHAR}, #{value2,jdbcType=VARCHAR}, 
      #{value3,jdbcType=VARCHAR}, #{value4,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR}
      )
  </insert>
  <select id="datas" resultType="com.get.permissioncenter.vo.ConfigVo">
    SELECT
    mc.*
    FROM
    m_config mc
    <where>
      <if test="configGroup!=null and configGroup !=''" >
        and  mc.config_group = #{configGroup}
      </if>
      <if test="keyWord!=null and keyWord !=''" >
        and  (mc.config_key like concat("%",#{keyWord},"%") or mc.description like concat("%",#{keyWord},"%") )
      </if>
    </where>
  </select>

  <select id="getGroupSelect" resultType="java.lang.String">
     select distinct config_group from m_config
  </select>

  <select id="getConfigValueByConfigKeyAndValue" resultType="com.get.permissioncenter.entity.Config">
    select id, config_group, config_key,
    description, value1, value2,
    value3, value4, gmt_create,
    gmt_create_user, gmt_modified, gmt_modified_user
    from m_config where config_key = #{key} and FIND_IN_SET(#{value1},value1)
  </select>
</mapper>