package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.core.mybatis.mapper.GetMapper;
import com.get.permissioncenter.entity.PermissionMediaAndAttached;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MediaAndAttachedMapper extends GetMapper<PermissionMediaAndAttached> {

    int insertSelective(PermissionMediaAndAttached record);

    /**
     * @return java.lang.Integer
     * @Description: 查询下一个索引值
     * @Param typeKey, tableName
     * <AUTHOR>
     */
    Integer getNextIndexKey(@Param("tableId") Long tableId, @Param("tableName") String tableName);

    /**
     * @return java.lang.List
     * @Description: 获取文件guid
     * @Param id, tableName
     * <AUTHOR>
     */
    List<String> getFileGuids(@Param("tableName") String tableName, @Param("id") Long id);

}