<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.BatchModifyConfigMapper">
  <insert id="insert" parameterType="com.get.permissioncenter.entity.BatchModifyConfig">
    insert into m_batch_modify_config (id, fk_db_name, fk_table_name, 
      fk_key_column_name, relation_type, fk_sub_table_name, 
      fk_sub_key_column_name, fk_target_column_name, 
      column_type, column_title, input_type, 
      input_src_api, max_length, is_required, 
      view_order, gmt_create, gmt_create_user, 
      gmt_modified, gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkDbName,jdbcType=VARCHAR}, #{fkTableName,jdbcType=VARCHAR}, 
      #{fkKeyColumnName,jdbcType=VARCHAR}, #{relationType,jdbcType=INTEGER}, #{fkSubTableName,jdbcType=VARCHAR}, 
      #{fkSubKeyColumnName,jdbcType=VARCHAR}, #{fkTargetColumnName,jdbcType=VARCHAR}, 
      #{columnType,jdbcType=INTEGER}, #{columnTitle,jdbcType=VARCHAR}, #{inputType,jdbcType=INTEGER}, 
      #{inputSrcApi,jdbcType=VARCHAR}, #{maxLength,jdbcType=INTEGER}, #{isRequired,jdbcType=BIT}, 
      #{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, 
      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.permissioncenter.entity.BatchModifyConfig">
    insert into m_batch_modify_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkDbName != null">
        fk_db_name,
      </if>
      <if test="fkTableName != null">
        fk_table_name,
      </if>
      <if test="fkKeyColumnName != null">
        fk_key_column_name,
      </if>
      <if test="relationType != null">
        relation_type,
      </if>
      <if test="fkSubTableName != null">
        fk_sub_table_name,
      </if>
      <if test="fkSubKeyColumnName != null">
        fk_sub_key_column_name,
      </if>
      <if test="fkTargetColumnName != null">
        fk_target_column_name,
      </if>
      <if test="columnType != null">
        column_type,
      </if>
      <if test="columnTitle != null">
        column_title,
      </if>
      <if test="inputType != null">
        input_type,
      </if>
      <if test="inputSrcApi != null">
        input_src_api,
      </if>
      <if test="maxLength != null">
        max_length,
      </if>
      <if test="isRequired != null">
        is_required,
      </if>
      <if test="viewOrder != null">
        view_order,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkDbName != null">
        #{fkDbName,jdbcType=VARCHAR},
      </if>
      <if test="fkTableName != null">
        #{fkTableName,jdbcType=VARCHAR},
      </if>
      <if test="fkKeyColumnName != null">
        #{fkKeyColumnName,jdbcType=VARCHAR},
      </if>
      <if test="relationType != null">
        #{relationType,jdbcType=INTEGER},
      </if>
      <if test="fkSubTableName != null">
        #{fkSubTableName,jdbcType=VARCHAR},
      </if>
      <if test="fkSubKeyColumnName != null">
        #{fkSubKeyColumnName,jdbcType=VARCHAR},
      </if>
      <if test="fkTargetColumnName != null">
        #{fkTargetColumnName,jdbcType=VARCHAR},
      </if>
      <if test="columnType != null">
        #{columnType,jdbcType=INTEGER},
      </if>
      <if test="columnTitle != null">
        #{columnTitle,jdbcType=VARCHAR},
      </if>
      <if test="inputType != null">
        #{inputType,jdbcType=INTEGER},
      </if>
      <if test="inputSrcApi != null">
        #{inputSrcApi,jdbcType=VARCHAR},
      </if>
      <if test="maxLength != null">
        #{maxLength,jdbcType=INTEGER},
      </if>
      <if test="isRequired != null">
        #{isRequired,jdbcType=BIT},
      </if>
      <if test="viewOrder != null">
        #{viewOrder,jdbcType=INTEGER},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
</mapper>