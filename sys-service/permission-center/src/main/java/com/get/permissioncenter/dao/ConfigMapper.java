package com.get.permissioncenter.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.entity.Config;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface ConfigMapper extends BaseMapper<Config> {
    int insert(Config record);

    List<ConfigVo> datas(Map params);

    List<String> getGroupSelect();

    List<Config> getConfigValueByConfigKeyAndValue(@Param("key") String key, @Param("value1") Long value1);

}