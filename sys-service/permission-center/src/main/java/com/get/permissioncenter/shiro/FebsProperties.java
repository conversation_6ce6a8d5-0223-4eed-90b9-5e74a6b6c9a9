//package com.get.permissioncenter.shiro;
//
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.stereotype.Component;
//
//
////配置
//
//@Component
//@Configuration
//@ConfigurationProperties(prefix = "febs")
//public class FebsProperties {
//
//    private ShiroProperties shiro = new ShiroProperties();
//
////    private ValidateCodeProperties validateCode = new ValidateCodeProperties();
//
//    private String timeFormat = "yyyy-MM-dd HH:mm:ss";
//
//    private boolean openAopLog = true;
//
//    public ShiroProperties getShiro() {
//        return shiro;
//    }
//
//    public void setShiro(ShiroProperties shiro) {
//        this.shiro = shiro;
//    }
//
////    public ValidateCodeProperties getValidateCode() {
////        return validateCode;
////    }
////
////    public void setValidateCode(ValidateCodeProperties validateCode) {
////        this.validateCode = validateCode;
////    }
//
//    public String getTimeFormat() {
//        return timeFormat;
//    }
//
//    public void setTimeFormat(String timeFormat) {
//        this.timeFormat = timeFormat;
//    }
//
//    public boolean isOpenAopLog() {
//        return openAopLog;
//    }
//
//    public void setOpenAopLog(boolean openAopLog) {
//        this.openAopLog = openAopLog;
//    }
//}
