package com.get.permissioncenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.BaseServiceImpl;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.dao.CommentMapper;
import com.get.permissioncenter.dao.StaffMapper;
import com.get.permissioncenter.vo.CommentVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.entity.PermissionComment;
import com.get.permissioncenter.service.ICommentService;
import com.get.permissioncenter.dto.CommentDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/11/5
 * @TIME: 15:44
 * @Description:
 **/
@Service
public class CommentServiceImpl extends BaseServiceImpl<CommentMapper, PermissionComment> implements ICommentService {

    @Resource
    private CommentMapper commentMapper;
    @Resource
    private StaffMapper staffMapper;
    @Resource
    private UtilService utilService;

    @Override
    public List<CommentVo> datas(CommentDto commentDto, Page page) {
//        Example example = new Example(Comment.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(commentDto)) {
//            if (GeneralTool.isNotEmpty(commentDto.getFkTableName())) {
//                criteria.andEqualTo("fkTableName", commentDto.getFkTableName());
//            }
//            if (GeneralTool.isNotEmpty(commentDto.getFkTableId())) {
//                criteria.andEqualTo("fkTableId", commentDto.getFkTableId());
//            }
//        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<Comment> comments = commentMapper.selectByExample(example);
//        page.restPage(comments);
//        List<CommentVo> commentVos = comments.stream().map(comment -> Tools.objClone(comment, CommentVo.class)).collect(Collectors.toList());

        LambdaQueryWrapper<PermissionComment> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(commentDto)) {
            if (GeneralTool.isNotEmpty(commentDto.getFkTableName())) {
                wrapper.eq(PermissionComment::getFkTableName, commentDto.getFkTableName());
            }
            if (GeneralTool.isNotEmpty(commentDto.getFkTableId())) {
                wrapper.eq(PermissionComment::getFkTableId, commentDto.getFkTableId());
            }
        }
        IPage<PermissionComment> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<PermissionComment> comments = pages.getRecords();
        List<CommentVo> commentVos = BeanCopyUtils.copyListProperties(comments, CommentVo::new);
        //返回创建人员工id
        Set<String> createUsers = commentVos.stream().map(CommentVo::getGmtCreateUser).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(createUsers)) {
            List<StaffVo> staffVos = staffMapper.getCompanyIdByCreateUsers(createUsers);
            Map<String, Long> staffMap = new HashMap<>();
            for (StaffVo staffVo : staffVos) {
                staffMap.put(staffVo.getLoginId(), staffVo.getId());
            }
            for (CommentVo commentVo : commentVos) {
                commentVo.setFkStaffId(staffMap.get(commentVo.getGmtCreateUser()));
            }
        }
        return commentVos;
    }

    @Override
    public void addComment(CommentDto commentDto) {
//        Comment comment = Tools.objClone(commentDto, Comment.class);
//        utilService.setCreateInfo(comment);
        PermissionComment comment = new PermissionComment();
        BeanCopyUtils.copyProperties(commentDto, comment);
        utilService.updateUserInfoToEntity(comment);
        commentMapper.insertSelective(comment);
    }

    @Override
    public void updateComment(CommentDto commentDto) {
//        Comment comment = Tools.objClone(commentDto, Comment.class);
//        utilService.setUpdateInfo(comment);
//        commentMapper.updateByPrimaryKeySelective(comment);
        PermissionComment comment = new PermissionComment();
        BeanCopyUtils.copyProperties(commentDto, comment);
        utilService.updateUserInfoToEntity(comment);
        commentMapper.updateById(comment);
    }

    @Override
    public void delete(Long id) {
//        commentMapper.deleteByPrimaryKey(id);
        commentMapper.deleteById(id);
    }
}
