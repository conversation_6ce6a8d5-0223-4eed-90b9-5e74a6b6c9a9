<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.permissioncenter.dao.PermissionGroupGradeNameMapper">
  <resultMap id="BaseResultMap" type="com.get.permissioncenter.entity.PermissionGroupGradeName">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_permission_group_id" jdbcType="BIGINT" property="fkPermissionGroupId" />
    <result column="fk_permission_grade_id" jdbcType="BIGINT" property="fkPermissionGradeId" />
    <result column="permission_name" jdbcType="VARCHAR" property="permissionName" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
<!--  <insert id="insert" parameterType="com.get.permissioncenter.entity.PermissionGroupGradeName">-->
<!--    insert into r_permission_group_grade_name (id, fk_permission_group_id, fk_permission_grade_id, -->
<!--      permission_name, gmt_create, gmt_create_user, -->
<!--      gmt_modified, gmt_modified_user)-->
<!--    values (#{id,jdbcType=BIGINT}, #{fkPermissionGroupId,jdbcType=BIGINT}, #{fkPermissionGradeId,jdbcType=BIGINT}, -->
<!--      #{permissionName,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, -->
<!--      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})-->
<!--  </insert>-->
  <insert id="insertSelective" parameterType="com.get.permissioncenter.entity.PermissionGroupGradeName">
    insert into r_permission_group_grade_name
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkPermissionGroupId != null">
        fk_permission_group_id,
      </if>
      <if test="fkPermissionGradeId != null">
        fk_permission_grade_id,
      </if>
      <if test="permissionName != null">
        permission_name,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkPermissionGroupId != null">
        #{fkPermissionGroupId,jdbcType=BIGINT},
      </if>
      <if test="fkPermissionGradeId != null">
        #{fkPermissionGradeId,jdbcType=BIGINT},
      </if>
      <if test="permissionName != null">
        #{permissionName,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="selectNameByGroupAndGrade" resultType="java.lang.String">
    select permission_name from r_permission_group_grade_name pggs where
      pggs.fk_permission_group_id = #{groupId} and pggs.fk_permission_grade_id = #{gradeId}
  </select>
  <select id="selectByGroupAndGrade"  parameterType="com.get.permissioncenter.entity.PermissionGroupGradeName" resultType="com.get.permissioncenter.entity.PermissionGroupGradeName">
    select * from r_permission_group_grade_name pggs where
      pggs.fk_permission_group_id = #{groupId} and pggs.fk_permission_grade_id = #{gradeId}
  </select>
</mapper>