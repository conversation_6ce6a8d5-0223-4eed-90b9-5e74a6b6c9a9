package com.get.demo.entity;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.*;
import com.get.core.mybatis.annotation.UpdateWithNull;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-01-07
 */

@Data
@TableName("user")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("主键id")
    @TableId(
            value = "id",
            type = IdType.ASSIGN_ID
    )
    private Long id;

    @TableField("name")
    private String name;

    /**
     * 订单数量
     */
    @UpdateWithNull
    private Integer orderNum;

    /**
     * 订单单价
     */
    @UpdateWithNull
    private BigDecimal price;

    /**
     * 订单时间
     */
    private DateTime time;
}
