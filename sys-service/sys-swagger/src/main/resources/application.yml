server:
  port: 18080
knife4j:
  enableAggregation: true
  cloud:
    enable: true
spring:
  servlet:
    multipart:
      # 根据实际需求作调整
      # 默认最大上传文件大小为1M，单个文件大小
      max-file-size: 10MB
      # 默认最大请求大小为10M，总上传的数据大小
      max-request-size: 15MB
  datasource:
    url: jdbc:mysql://************:3316/ais_log_center?useUnicode=true&characterEncoding=UTF-8&useSSL=false&serverTimezone=GMT%2b8
    username: root
    password: fzhmysql