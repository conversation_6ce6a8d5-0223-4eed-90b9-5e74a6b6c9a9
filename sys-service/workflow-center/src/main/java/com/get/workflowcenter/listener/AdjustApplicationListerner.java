package com.get.workflowcenter.listener;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.core.tool.utils.SpringUtil;
import com.get.permissioncenter.vo.StaffVo;
import com.get.workflowcenter.component.IWorkFlowHelper;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.repository.ProcessDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: Hardy
 * @create: 2022/1/17 11:53
 * @verison: 1.0
 * @description:
 */
@Slf4j
public class AdjustApplicationListerner implements TaskListener {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void notify(DelegateTask delegateTask) {
        logger.info("进入AdjustApplicationListerner监听--------------------------------");
        //获取feign调用service
        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);
        ProcessEngine processEngine = SpringUtil.getBean(ProcessEngine.class);
        ProcessDefinition processDefinition = processEngine.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionId(delegateTask.getProcessDefinitionId())
                .singleResult();
        StaffVo staffVo = workFlowHelper.getStaffDto(delegateTask);
        List<String> staffIdList = new ArrayList<>(1);
        staffIdList.add(String.valueOf(staffVo.getId()));
        StringJoiner stringJoiner = new StringJoiner(",");
        stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_DO.key);
        if (TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key.equals(processDefinition.getKey())) {
            stringJoiner.add(ProjectKeyEnum.OFFICE_CENTER.key);
        }
        workFlowHelper.sendMessage(staffVo, staffIdList, processDefinition, "待调整", delegateTask, stringJoiner.toString(),null);
        logger.info("结束AdjustApplicationListerner监听--------------------------------");
    }

}
