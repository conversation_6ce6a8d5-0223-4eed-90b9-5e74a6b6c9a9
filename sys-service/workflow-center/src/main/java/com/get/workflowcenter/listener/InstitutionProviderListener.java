package com.get.workflowcenter.listener;

import com.get.common.utils.BeanCopyUtils;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.institutioncenter.vo.ContractVo;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.workflowcenter.entity.WorkFlowContract;
import org.activiti.engine.HistoryService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.history.HistoricActivityInstance;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/20 12:25
 */
public class InstitutionProviderListener implements Serializable, ExecutionListener, TaskListener {
    @Override
    public void notify(DelegateExecution delegateExecution) {
        String processInstanceBusinessKey = delegateExecution.getProcessInstanceBusinessKey();
        String processInstanceId = delegateExecution.getProcessInstanceId();
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        IInstitutionCenterClient institutionCenterClient = SpringUtil.getBean(IInstitutionCenterClient.class);

        if ("end".equals(delegateExecution.getEventName())) {
            List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).orderByHistoricActivityInstanceStartTime().desc().list();
            WorkFlowContract institutionProvider = null;
            Result<ContractVo> result = institutionCenterClient.getInstitutionContractById(Long.valueOf(processInstanceBusinessKey));
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                institutionProvider = BeanCopyUtils.objClone(result.getData(), com.get.workflowcenter.vo.ContractVo::new);
            }
            for (HistoricActivityInstance hti : list) {
                if (hti.getActivityName() != null && "资料有误重新申请".equals(hti.getActivityName())) {
                    institutionProvider.setStatus(4);
                    break;
                } else {
                    institutionProvider.setStatus(1);
                    break;
                }
            }
            com.get.institutioncenter.entity.Contract contract_ = BeanCopyUtils.objClone(institutionProvider, com.get.institutioncenter.entity.Contract::new);
            institutionCenterClient.updateChangeStatus(contract_);
        } else {
            Object sequenceFlowsStatus = delegateExecution.getVariable("sequenceFlowsStatus");
            WorkFlowContract institutionProvider = null;
            Result<ContractVo> result = institutionCenterClient.getInstitutionContractById(Long.valueOf(processInstanceBusinessKey));
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                institutionProvider = BeanCopyUtils.objClone(result.getData(), com.get.workflowcenter.vo.ContractVo::new);
            }

            if ("0".equals(sequenceFlowsStatus)) {
                institutionProvider.setStatus(3);
            } else {
                institutionProvider.setStatus(2);
            }
            com.get.institutioncenter.entity.Contract contract_ = BeanCopyUtils.objClone(institutionProvider, com.get.institutioncenter.entity.Contract::new);
            institutionCenterClient.updateChangeStatus(contract_);
        }

    }

    @Override
    public void notify(DelegateTask delegateTask) {

    }
}
