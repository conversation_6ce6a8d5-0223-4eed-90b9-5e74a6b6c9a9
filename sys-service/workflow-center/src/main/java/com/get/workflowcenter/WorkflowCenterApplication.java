package com.get.workflowcenter;

import com.get.common.constant.AppCenterConstant;
import com.get.core.cloud.feign.EnableGetFeign;
import com.get.core.start.GetApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.cloud.client.SpringCloudApplication;

@EnableAutoConfiguration(exclude = {org.activiti.spring.boot.SecurityAutoConfiguration.class})
@EnableGetFeign
@SpringCloudApplication
public class WorkflowCenterApplication {
    public static void main(String[] args) {
        GetApplication.run(AppCenterConstant.APPLICATION_WORKFLOW_CENTER, com.get.workflowcenter.WorkflowCenterApplication.class, args);
    }
}
