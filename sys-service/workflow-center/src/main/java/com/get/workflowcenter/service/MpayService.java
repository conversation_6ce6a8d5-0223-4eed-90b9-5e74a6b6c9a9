package com.get.workflowcenter.service;

import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.PaymentApplicationFormVo;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/3/18 10:34
 */
public interface MpayService {


    List<PaymentApplicationFormVo> getPayFlowData(String businessKey, String key);


    List<Long> getPersonalHistoryTasks(String key);

    int getSignOrGet(String taskId, Integer version);


    ActRuTaskVo getTaskDataByBusinessKey(String businessKey, String key);

    Boolean startPayFlow(String businessKey, String procdefKey, String companyId);
}
