package com.get.workflowcenter.service;

import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.AgentContractVo;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/15 18:32
 */
public interface IAgentContractService {

    ActRuTaskVo getTaskDataByBusinessKey(String businessKey, String key);

    List<AgentContractVo> getPayFlowData(String businessKey, String key);

    int getSignOrGet(String taskId, Integer version);

    Boolean startContractFlow(String businessKey, String procdefKey, String companyId);

}
