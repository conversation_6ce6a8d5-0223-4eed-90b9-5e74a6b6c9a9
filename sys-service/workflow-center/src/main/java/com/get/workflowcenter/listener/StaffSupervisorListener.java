package com.get.workflowcenter.listener;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.workflowcenter.component.IWorkFlowHelper;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.repository.ProcessDefinition;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: Hardy
 * @create: 2021/11/17 12:04
 * @verison: 1.0
 * @description:
 */
@Slf4j
public class StaffSupervisorListener implements TaskListener {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void notify(DelegateTask task) {
        logger.info("进入任务最上级监听--------------------------------");
        //获取feign调用service
        IPermissionCenterClient feignPermissionService = SpringUtil.getBean(IPermissionCenterClient.class);
        TaskService taskService = SpringUtil.getBean(TaskService.class);
        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);
//        //获取流程名称
        ProcessEngine processEngine = SpringUtil.getBean(ProcessEngine.class);

        ProcessDefinition processDefinition = processEngine.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionId(task.getProcessDefinitionId())
                .singleResult();

        StaffVo staffVo = workFlowHelper.getStaffDto(task);

        //获取登录人的直属上司id
        Long staffSupervisorId = null;
        Result<Long> result = feignPermissionService.getStaffSupervisorIdByStaffId(GetAuthInfo.getStaffId());
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            staffSupervisorId = result.getData();
        }

        logger.info("用户上司：" + staffSupervisorId + "，" + "--------------------------------");
        if (GeneralTool.isNotEmpty(staffSupervisorId)) {
            //只有一个人就是是待办人
            taskService.setAssignee(task.getId(), String.valueOf(staffSupervisorId));

            List<String> staffIdList = new ArrayList<>(1);
            staffIdList.add(String.valueOf(staffSupervisorId));
            StringJoiner stringJoiner = new StringJoiner(",");
            stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_DO.key);
            if (TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key.equals(processDefinition.getKey())) {
                stringJoiner.add(ProjectKeyEnum.OFFICE_CENTER.key);
            }
            workFlowHelper.sendMessage(staffVo, staffIdList, processDefinition, "待审核", task, stringJoiner.toString(),null);
        } else {
            //没有处理人，系统自动驳回
            HashMap<String, Object> map = new HashMap<>();
            map.put("sequenceFlowsStatus", 0);
            //设置本地变量 这个可能是后面判断曾经驳回过
            taskService.setVariableLocal(task.getId(), "approvalAction", 0);
            //向任务和/或流程实例添加注释
            taskService.addComment(task.getId(), task.getProcessInstanceId(), "系统自动驳回，" + task.getName() + "：找不到处理人。");
            taskService.complete(task.getId(), map);
        }

    }
}
