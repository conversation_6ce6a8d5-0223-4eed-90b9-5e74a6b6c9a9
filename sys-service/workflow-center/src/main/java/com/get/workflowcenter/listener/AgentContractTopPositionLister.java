package com.get.workflowcenter.listener;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.vo.AgentContractVo;
import com.get.salecenter.entity.AgentContract;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.workflowcenter.component.IWorkFlowHelper;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.task.Task;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.StringJoiner;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/15 16:48
 */
public class AgentContractTopPositionLister implements Serializable, ExecutionListener, TaskListener {
//    @Resource
//    private ISaleCenterClient saleCenterClient;

    @Override
    public void notify(DelegateExecution delegateExecution) {
        try {
            String processInstanceBusinessKey = delegateExecution.getProcessInstanceBusinessKey();
            String processInstanceId = delegateExecution.getProcessInstanceId();
            HistoryService historyService = SpringUtil.getBean(HistoryService.class);
            ISaleCenterClient saleCenterClient = SpringUtil.getBean(ISaleCenterClient.class);
            UtilService utilService = SpringUtil.getBean(UtilService.class);

            if ("end".equals(delegateExecution.getEventName())) {
                List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).orderByHistoricActivityInstanceStartTime().desc().list();
                AgentContract agentContract = null;
                Result<AgentContractVo> agentContractDtoResult = saleCenterClient.getAgentContractById(Long.valueOf(processInstanceBusinessKey));
                if (agentContractDtoResult.isSuccess() && GeneralTool.isNotEmpty(agentContractDtoResult.getData())) {
                    AgentContractVo agentContractVo = agentContractDtoResult.getData();
                    if (agentContractVo != null) {
                        agentContract = BeanCopyUtils.objClone(agentContractVo, AgentContract::new);
                    }
                }
                for (HistoricActivityInstance hti : list) {
                    if (hti.getActivityName() != null && "资料有误重新申请".equals(hti.getActivityName())) {
                        utilService.updateUserInfoToEntity(agentContract);
                        agentContract.setStatus(4);
                        break;
                    } else {
                        utilService.updateUserInfoToEntity(agentContract);
                        agentContract.setStatus(1);
                        break;
                    }
                }
                saleCenterClient.updateChangeStatus(agentContract);
            } else {
                Object sequenceFlowsStatus = delegateExecution.getVariable("sequenceFlowsStatus");
                AgentContract paymentApplicationForm = null;
                Result<AgentContractVo> result = saleCenterClient.getAgentContractById(Long.valueOf(processInstanceBusinessKey));
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    AgentContractVo agentContractVo = result.getData();
                    paymentApplicationForm = BeanCopyUtils.objClone(agentContractVo, AgentContract::new);
                }

                if ("0".equals(sequenceFlowsStatus)) {
                    utilService.updateUserInfoToEntity(paymentApplicationForm);
                    paymentApplicationForm.setStatus(3);
                } else if ("1".equals(sequenceFlowsStatus)) {
                    utilService.updateUserInfoToEntity(paymentApplicationForm);
                    paymentApplicationForm.setStatus(2);
                }
                saleCenterClient.updateChangeStatus(paymentApplicationForm);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public void notify(DelegateTask delegateTask) {
        ISaleCenterClient saleCenterClient = SpringUtil.getBean(ISaleCenterClient.class);
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        TaskService taskService = SpringUtil.getBean(TaskService.class);
        IPermissionCenterClient permissionCenterClient = SpringUtil.getBean(IPermissionCenterClient.class);

        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);
        //获取流程名称
        ProcessEngine processEngine = SpringUtil.getBean(ProcessEngine.class);
        Task result = taskService.createTaskQuery().taskId(delegateTask.getId()).singleResult();
        result.getProcessDefinitionId();
        ProcessDefinition processDefinition = processEngine.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionId(delegateTask.getProcessDefinitionId())
                .singleResult();

        StaffVo staff = workFlowHelper.getStaffDto(delegateTask);

        if ("合同跟进人审批".equals(delegateTask.getName())) {
            AgentContractVo agentContract = delegateTask.getVariable("agentContract", AgentContractVo.class);
            //一个代理只能对应一个员工 判断是否有效
            Result<Long> bdStaffResult = saleCenterClient.getStaffByAgentId(agentContract.getFkAgentId());
            if (bdStaffResult.isSuccess() && GeneralTool.isNotEmpty(bdStaffResult.getData())) {
                Long bdStaff = bdStaffResult.getData();
                taskService.setAssignee(delegateTask.getId(), String.valueOf(bdStaff));
                List<String> staffIdList = new ArrayList<>(1);
                staffIdList.add(String.valueOf(bdStaff));
                StringJoiner stringJoiner = new StringJoiner(",");
                stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_DO.key);
                workFlowHelper.sendMessage(staff, staffIdList, processDefinition, "待审核", delegateTask, stringJoiner.toString(),null);
            }
        } else if ("跟进人的部门领导".equals(delegateTask.getName())) {
            HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().processInstanceId(delegateTask.getProcessInstanceId()).unfinished().orderByTaskCreateTime().desc().singleResult();
            Result<StaffVo> staffDtoResult = permissionCenterClient.getCompanyIdByStaffId(Long.valueOf(historicTaskInstance.getAssignee()));
            StaffVo staffVo = new StaffVo();
            if (staffDtoResult.isSuccess() && GeneralTool.isNotEmpty(staffDtoResult.getData())) {
                staffVo = staffDtoResult.getData();
            }
            Result<List<Long>> staffIdListResult = permissionCenterClient.getTopPositionStaffIds(staffVo.getFkCompanyId(), staffVo.getFkDepartmentId());
            if (staffIdListResult.isSuccess() && CollectionUtil.isNotEmpty(staffIdListResult.getData())) {
                JSONArray objects = JSONUtil.parseArray(staffIdListResult.getData());
                List<String> staffIdList = JSONUtil.toList(objects, String.class);
                if (staffIdList.size() != 0) {
                    if (staffIdList.size() > 1) {
                        delegateTask.addCandidateUsers(staffIdList);
                        StringJoiner stringJoiner = new StringJoiner(",");
                        stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_SIGN.key);
                        workFlowHelper.sendMessage(staff, staffIdList, processDefinition, "待签取", delegateTask, stringJoiner.toString(),null);
                    } else {
                        taskService.setAssignee(delegateTask.getId(), staffIdList.get(0));
                        StringJoiner stringJoiner = new StringJoiner(",");
                        stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_DO.key);
                        workFlowHelper.sendMessage(staff, staffIdList, processDefinition, "待审核", delegateTask, stringJoiner.toString(),null);
                    }
                }
            }


        }

    }
}

