package com.get.workflowcenter.service.impl;

import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.ContractVo;
import com.get.workflowcenter.mapper.ActRuTaskMapper;
import com.get.workflowcenter.service.IWorkFlowInstitutionProviderService;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.get.common.utils.GetDateUtil.dateDiff;


/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/20 22:23
 */
@Service
public class WorkFlowInstitutionProviderServiceImpl implements IWorkFlowInstitutionProviderService {
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private HistoryService historyService;
    @Resource
    private ActRuTaskMapper actRuTaskMapper;
    @Autowired
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Autowired
    private TaskService taskService;
    @Autowired
    private RuntimeService runtimeService;

    @Override
    public ActRuTaskVo getTaskDataByBusinessKey(String businessKey, String key) {

        ActRuTaskVo actRuTaskVo = new ActRuTaskVo();
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceBusinessKey(businessKey).processDefinitionKey(key).singleResult();
        if (historicProcessInstance != null) {
            actRuTaskVo.setProcInstId(historicProcessInstance.getId());
            ActRuTaskVo taskVersionByProcessInId = actRuTaskMapper.getTaskVersionByProcessInId(historicProcessInstance.getId());
            if (taskVersionByProcessInId != null) {
                ProcessDefinition processDefinitionQuery = repositoryService.createProcessDefinitionQuery().processDefinitionId(historicProcessInstance.getProcessDefinitionId()).singleResult();
                actRuTaskVo.setId(taskVersionByProcessInId.getId());
                actRuTaskVo.setDeployId(processDefinitionQuery.getDeploymentId());
                actRuTaskVo.setTaskVersion(taskVersionByProcessInId.getRev());
                return actRuTaskVo;
            }
        }
        return actRuTaskVo;
    }

    @Override
    public List<ContractVo> getInstitutionProviderData(String businessKey, String key) {
        if (StringUtils.isBlank(businessKey)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceBusinessKey(businessKey).processDefinitionKey(key).singleResult();


        List<ContractVo> agentContractVos = new ArrayList<>();
        if (historicProcessInstance!=null) {
            List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery().processInstanceId(historicProcessInstance.getId()).orderByHistoricActivityInstanceStartTime().asc().list();

            for (HistoricActivityInstance hti : list) {
                ContractVo institutionProviderDto = new ContractVo();
                if ("startEvent".equals(hti.getActivityType())) {
                    institutionProviderDto.setActName("开始流程");
                    institutionProviderDto.setAssignee(historicProcessInstance.getStartUserId());
                    Date startTime = hti.getStartTime();
                    institutionProviderDto.setStartTimeDto(startTime);
                    agentContractVos.add(institutionProviderDto);

                } else if ("endEvent".equals(hti.getActivityType())) {
                    institutionProviderDto.setActName("流程结束");
                    Date startTime = hti.getStartTime();
                    institutionProviderDto.setStartTimeDto(startTime);
                    institutionProviderDto.setEndTimeDto(hti.getEndTime());
                    agentContractVos.add(institutionProviderDto);
                }
                if (hti.getActivityName() != null) {
                    Date startTime = hti.getStartTime();
                    String startformat = simpleDateFormat.format(startTime);
                    institutionProviderDto.setStartTimeDto(startTime);
                    institutionProviderDto.setActName(hti.getActivityName());
                    if (null != hti.getAssignee()) {
//                    String staffName = permissionCenterClient.getStaffName(Long.valueOf(hti.getAssignee()));
                        Result<String> result = permissionCenterClient.getStaffName(Long.valueOf(hti.getAssignee()));
                        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                            institutionProviderDto.setAssignee(result.getData());
                        }
                    }
                    if (hti.getEndTime() != null) {
                        Date endTime = hti.getEndTime();
                        String endformat = simpleDateFormat.format(endTime);
                        String durationTime = dateDiff(startformat, endformat, simpleDateFormat.toPattern());
                        institutionProviderDto.setTotalTime(durationTime);
                        institutionProviderDto.setEndTimeDto(endTime);
                    }
                    List<Comment> taskComments = taskService.getTaskComments(hti.getTaskId());
                    for (Comment comlist : taskComments) {
                        institutionProviderDto.setMsg(comlist.getFullMessage());
                    }
                    agentContractVos.add(institutionProviderDto);
                }

            }
        }
        return agentContractVos;
    }

    @Override
    public int getSignOrGet(String taskId, Integer version) {
//        StaffVo staff = StaffContext.getStaff();
//        String userid = String.valueOf(staff.getId());
        String userid = String.valueOf(GetAuthInfo.getStaffId());
        if (GeneralTool.isNotEmpty(taskId) && GeneralTool.isNotEmpty(version)) {
            Task taskQuery = taskService.createTaskQuery().taskId(taskId).taskAssignee(userid).singleResult();
            if (taskQuery == null) {
                Task task = taskService.createTaskQuery().taskCandidateUser(userid).taskId(taskId).singleResult();
                if (task != null) {
                    ActRuTaskVo actRuTaskVo = actRuTaskMapper.comparisonVersion(task.getId(), version);
                    if (actRuTaskVo != null) {
                        //签收
                        return 0;
                    }
                } else {
                    //什么都不用干
                    return 2;
                }
            } else {
                //1待办
                return 1;
            }
        }
        return 2;
    }

    @Override
    public Boolean startInstitutionContractFlow(String businessKey, String procdefKey, String companyId) {
        if (GeneralTool.isEmpty(businessKey) || GeneralTool.isEmpty(procdefKey) || GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
//        StaffVo staff = StaffContext.getStaff();
        String username = String.valueOf(GetAuthInfo.getStaffId());
        Authentication.setAuthenticatedUserId(username);

        ContractVo institutionProviderById = null;
        Result<com.get.institutioncenter.vo.ContractVo> result = institutionCenterClient.getInstitutionContractById(Long.valueOf(businessKey));
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            institutionProviderById = BeanCopyUtils.objClone(result.getData(), ContractVo::new);
        }
        if (null == institutionProviderById || institutionProviderById.getStatus() != 0 || institutionProviderById.getStatus() == 5) {
            return false;
        }

        ContractVo institutionProvider = BeanCopyUtils.objClone(institutionProviderById, ContractVo::new);
        Map<String, Object> map = new HashMap<>();
        map.put("userid", username);
        map.put("companyId", companyId);
        map.put("tableName", "m_institution_provider");
        map.put("businessKey", institutionProvider.getId());
        map.put("InstitutionProvider", institutionProvider);
        map.put("contractApprovalMode", institutionProvider.getContractApprovalMode());
        try {

            //表名 +公司id
            List<ProcessDefinition> processDefinition =
                    repositoryService.createProcessDefinitionQuery().processDefinitionKey(procdefKey).processDefinitionTenantId(companyId).orderByProcessDefinitionVersion().desc().list();
            String id = "";
            for (ProcessDefinition pdid : processDefinition) {
                id = pdid.getId();
                break;
            }
            ProcessInstance processInstance = runtimeService.startProcessInstanceById(id, String.valueOf(institutionProvider.getId()), map);
//            ContractVo ifstatus = institutionCenterClient.getInstitutionContractById(Long.valueOf(businessKey));
            Result<com.get.institutioncenter.vo.ContractVo> contractDtoResult = institutionCenterClient.getInstitutionContractById(Long.valueOf(businessKey));
            if (contractDtoResult.isSuccess() && GeneralTool.isNotEmpty(contractDtoResult.getData())) {
                ContractVo ifstatus = BeanCopyUtils.objClone(contractDtoResult.getData(), ContractVo::new);
                if (ifstatus.getStatus() == 0) {
                    institutionProvider.setStatus(2);
                    institutionCenterClient.updateChangeStatus(BeanCopyUtils.objClone(institutionProvider, com.get.institutioncenter.vo.ContractVo::new));
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    @Override
    public Boolean getContractUserSubmit(String taskId, String status) {
        HashMap<String, Object> sequenceFlowsStatus = new HashMap<>();
        //0放弃
        if ("0".equals(status)) {
            sequenceFlowsStatus.put("sequenceFlowsStatus", 0);
            taskService.setVariableLocal(taskId, "approvalAction", 0);
            taskService.complete(taskId, sequenceFlowsStatus);
            return true;
        }
        taskService.setVariableLocal(taskId, "approvalAction", 1);
        taskService.setVariable(taskId, "statusToDo", 1);
        String id = taskService.getVariable(taskId, "businessKey").toString();
        //能修改签订状态
//        ContractVo institutionContractById = institutionCenterClient.getInstitutionContractById(Long.valueOf(id));
        Result<com.get.institutioncenter.vo.ContractVo> result = institutionCenterClient.getInstitutionContractById(Long.valueOf(Long.valueOf(id)));
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            ContractVo institutionContractById = BeanCopyUtils.objClone(result.getData(), ContractVo::new);
            //不能修改 签订状态的话就解开注释，注释掉上面
       /* Object contractApprovalMode = taskService.getVariable(taskId, "contractApprovalMode");
        String contractApprovalModeStatus = String.valueOf(contractApprovalMode);*/
            if (institutionContractById.getContractApprovalMode() == 1) {
                sequenceFlowsStatus.put("sequenceFlowsStatus", 1);
                taskService.complete(taskId, sequenceFlowsStatus);
                return true;
            } else if (institutionContractById.getContractApprovalMode() == 0) {
                sequenceFlowsStatus.put("sequenceFlowsStatus", 2);
                taskService.complete(taskId, sequenceFlowsStatus);
                return true;
            }
        }
        return false;
    }
}
