package com.get.workflowcenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.tool.utils.GeneralTool;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.PaymentApplicationFormVo;
import com.get.workflowcenter.service.IWorkFlowService;
import com.get.workflowcenter.service.MpayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.impl.identity.Authentication;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/3/18 10:12
 */


@Api(tags = "支付流程")
@RestController
@RequestMapping("workflow/payflow")
@Slf4j
public class WpayFlowController {

    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private MpayService mpayService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private IWorkFlowService iWorkFlowService;


    /**
     * @ Description :
     * @ Param [wpayVo, proceId]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiIgnore
    @ApiOperation("支付流程开始")
    @PostMapping("/startPayFlow")
    public Boolean startPayFlow(@RequestParam("businessKey") String businessKey,
                                @RequestParam("procdefKey") String procdefKey,
                                @RequestParam("companyId") String companyId) {
//        if (GeneralTool.isEmpty(businessKey) || GeneralTool.isEmpty(procdefKey) || GeneralTool.isEmpty(companyId)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
//        String username = String.valueOf(GetAuthInfo.getStaffId());
//        Authentication.setAuthenticatedUserId(username);
//        try {
//
//            PaymentApplicationFormVo mpayById = financeCenterClient.getMpayById(Long.valueOf(businessKey));
//            if (mpayById == null || mpayById.getStatus() != 0 || mpayById.getStatus() == 5) {
//                return false;
//            }
//            PaymentApplicationForm paymentApplicationForm = BeanCopyUtils.objClone(mpayById, PaymentApplicationForm::new);
//            Map<String, Object> map = new HashMap<>();
//            map.put("userid", username);
//            map.put("prepay", paymentApplicationForm);
//
//            //表名 +公司id b
//            List<ProcessDefinition> processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(procdefKey).processDefinitionTenantId(companyId).orderByProcessDefinitionVersion().desc().list();
//            String id = "";
//            for (ProcessDefinition pdid : processDefinition) {
//                id = pdid.getId();
//                break;
//            }
//
//            ProcessInstance processInstance = runtimeService.startProcessInstanceById(id, String.valueOf(paymentApplicationForm.getId()), map);
//            mpayById.setStatus(2);
//            financeCenterClient.updateMpay(mpayById);
//            return true;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        }
//        return true;

        return mpayService.startPayFlow(businessKey, procdefKey, companyId);
    }

    /**
     * @ Description :
     * @ Param [status, taskId, msg]
     * @ return void
     * @ author LEO
     */
    @ApiOperation("审批")
    @GetMapping("/getExamineAndApprove")
    public ResponseBo getExamineAndApprove(@RequestParam("status") String status, @RequestParam("taskId") String taskId,
                                           @RequestParam("procInstId") String procInstId, @RequestParam("msg") String msg) {

        if (GeneralTool.isEmpty(status) || GeneralTool.isEmpty(taskId) || GeneralTool.isEmpty(msg) || GeneralTool.isEmpty(procInstId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        StaffVo staff = StaffContext.getStaff();
        String username = String.valueOf(GetAuthInfo.getStaffId());
        Authentication.setAuthenticatedUserId(username);
        taskService.addComment(taskId, procInstId, msg);
        Map<String, Object> map = new HashMap<>();
        map.put("sequenceFlowsStatus", status);
        if ("0".equals(status)) {
            taskService.setVariableLocal(taskId, "approvalAction", 0);
        } else {
            taskService.setVariableLocal(taskId, "approvalAction", 1);
        }
        taskService.complete(taskId, map);
        iWorkFlowService.getStatusToDoSingle(status, procInstId);

        return ResponseBo.ok();
    }


    /**
     * @ Description :
     * @ Param [businessKey]
     * @ return com.get.common.result.ResponseBo<com.get.workflowcenter.vo.PaymentApplicationFormVo>
     * @ author LEO
     */
    @ApiOperation("显示支付流程流转信息")
    @GetMapping("/getPayFlowData")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<PaymentApplicationFormVo> getPayFlowData(@RequestParam("businessKey") String businessKey,
                                                               @RequestParam("procdefKey") String key) throws GetServiceException {
        List<PaymentApplicationFormVo> mpayDtos = mpayService.getPayFlowData(businessKey, key);
        ListResponseBo<PaymentApplicationFormVo> mpayDtoListResponseBo = new ListResponseBo<>(mpayDtos);
        return mpayDtoListResponseBo;
    }


    @ApiIgnore
    @ApiOperation("暂时共用，查找他是否有历史审批和候选人")
    @GetMapping("getPersonalHistoryTasks")
    public List<Long> getPersonalHistoryTasks(@RequestParam("key") String key) {
        List<Long> payFormDtos = mpayService.getPersonalHistoryTasks(key);

        return payFormDtos;

    }


    @ApiOperation("判断去待签还是代表页面，0待签，1待办")
    @GetMapping("getSignOrGet")
    public int getSignOrGet(@RequestParam(required = false, value = "taskId") String taskId,
                            @RequestParam(required = false, value = "version") Integer version) {
        int signOrGet = mpayService.getSignOrGet(taskId, version);
        return signOrGet;
    }

    @ApiIgnore
    @ApiOperation("查询表单所有任务task版本")
    @GetMapping("getTaskDataByBusinessKey")
    public ActRuTaskVo getTaskDataByBusinessKey(@RequestParam("businessKey") String businessKey, @RequestParam("procdefKey") String key) {

        return mpayService.getTaskDataByBusinessKey(businessKey, key);

    }
}
