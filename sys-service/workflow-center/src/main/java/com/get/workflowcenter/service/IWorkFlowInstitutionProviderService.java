package com.get.workflowcenter.service;

import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.ContractVo;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/20 22:23
 */
public interface IWorkFlowInstitutionProviderService {
    ActRuTaskVo getTaskDataByBusinessKey(String businessKey, String key);

    List<ContractVo> getInstitutionProviderData(String businessKey, String key);

    int getSignOrGet(String taskId, Integer version);

    Boolean startInstitutionContractFlow(String businessKey, String procdefKey, String companyId);

    Boolean getContractUserSubmit(String taskId, String status);

}
