package com.get.workflowcenter.service.impl;

import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.entity.PrepayApplicationForm;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.PrepayApplicationFormVo;
import com.get.workflowcenter.entity.WorkFlowPrepayApplicationForm;
import com.get.workflowcenter.mapper.ActRuTaskMapper;
import com.get.workflowcenter.service.ImBorrowMoneyServiec;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.get.common.utils.GetDateUtil.dateDiff;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/1 10:57
 */
@Service
public class MborrowMoneyServiceImpl implements ImBorrowMoneyServiec {

    private final Logger LOG = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private ActRuTaskMapper actRuTaskMapper;
    @Autowired
    private IPermissionCenterClient permissionCenterClient;

    @Override
    public List<PrepayApplicationFormVo> getPrepayFlowData(String businessKey, String key) {
        if (StringUtils.isBlank(businessKey)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

//        PrepayApplicationFormVo prepayApplicationFormDto = null;
        com.get.financecenter.vo.PrepayApplicationFormVo prepayApplicationFormVo = null;
        Result<com.get.financecenter.vo.PrepayApplicationFormVo> prepayApplicationFormDtoResult = financeCenterClient.getBorrowMoneyById(Long.valueOf(businessKey));
        if (prepayApplicationFormDtoResult.isSuccess() && GeneralTool.isNotEmpty(prepayApplicationFormDtoResult.getData())) {
            prepayApplicationFormVo = prepayApplicationFormDtoResult.getData();
        }
//        PrepayApplicationFormVo prepayApplicationFormDto = null;

        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceBusinessKey(businessKey).processDefinitionKey(key).singleResult();


        List<PrepayApplicationFormVo> prepayApplicationFormVos = new ArrayList<>();
        List<HistoricActivityInstance> list =
                historyService.createHistoricActivityInstanceQuery().processInstanceId(historicProcessInstance.getId()).orderByHistoricActivityInstanceStartTime().asc().list();

        for (HistoricActivityInstance hti : list) {
            PrepayApplicationFormVo newMpayDto = new PrepayApplicationFormVo();
            if ("startEvent".equals(hti.getActivityType())) {
                newMpayDto.setActName("开始流程");
                if (prepayApplicationFormVo != null) {
                    newMpayDto.setAssignee(prepayApplicationFormVo.getGmtCreateUser());
                }
                Date startTime = hti.getStartTime();
                newMpayDto.setStartTimeDto(startTime);
                prepayApplicationFormVos.add(newMpayDto);

            } else if ("endEvent".equals(hti.getActivityType())) {
                newMpayDto.setActName("流程结束");
                Date startTime = hti.getStartTime();
                newMpayDto.setStartTimeDto(startTime);
                newMpayDto.setEndTimeDto(hti.getEndTime());
                prepayApplicationFormVos.add(newMpayDto);
            }
            if (hti.getActivityName() != null) {
                Date startTime = hti.getStartTime();
                String startformat = simpleDateFormat.format(startTime);
                newMpayDto.setStartTimeDto(startTime);
                newMpayDto.setActName(hti.getActivityName());

                if (null != hti.getAssignee()) {
//                    String staffName = permissionCenterClient.getStaffName(Long.valueOf(hti.getAssignee()));
                    Result<String> result = permissionCenterClient.getStaffName(Long.valueOf(hti.getAssignee()));
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                        agentContractDto.setAssignee(result.getData());
                        newMpayDto.setAssignee(result.getData());
                    }
                }

                if (hti.getEndTime() != null) {
                    Date endTime = hti.getEndTime();
                    String endformat = simpleDateFormat.format(endTime);
                    String durationTime = dateDiff(startformat, endformat, simpleDateFormat.toPattern());
                    newMpayDto.setTotalTime(durationTime);
                    newMpayDto.setEndTimeDto(endTime);
                }
                List<Comment> taskComments = taskService.getTaskComments(hti.getTaskId());
                for (Comment comlist : taskComments) {
                    newMpayDto.setMsg(comlist.getFullMessage());
                }
                prepayApplicationFormVos.add(newMpayDto);
            }

        }

        return prepayApplicationFormVos;


    }

    @Override
    public Boolean getDetectionUpdate(String businessKey) {


        return null;
    }


    @Override
    public Boolean getBack(String businessKey, String msg) {
        return null;
    }

    @Override
    public int getSignOrGet(String taskId, Integer version) {
//        StaffVo staff = StaffContext.getStaff();
        String userid = String.valueOf(GetAuthInfo.getStaffId());

        if (GeneralTool.isNotEmpty(taskId) && GeneralTool.isNotEmpty(version)) {
            Task taskQuery = taskService.createTaskQuery().taskId(taskId).taskAssignee(userid).singleResult();
            if (taskQuery == null) {
                Task task = taskService.createTaskQuery().taskCandidateUser(userid).taskId(taskId).singleResult();
                if (task != null) {
                    ActRuTaskVo actRuTaskVo = actRuTaskMapper.comparisonVersion(task.getId(), version);
                    if (actRuTaskVo != null) {
                        //签收
                        return 0;
                    }
                } else {
                    //什么都不用干
                    return 2;
                }
            } else {
                //1待办
                return 1;
            }
        }


        return 2;
    }

    @Override
    public Boolean startBorrowFlow(String businessKey, String procdefKey, String companyId) {
        if (GeneralTool.isEmpty(businessKey) || GeneralTool.isEmpty(procdefKey) || GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        String username = String.valueOf(GetAuthInfo.getStaffId());
        PrepayApplicationFormVo mborrowMoneyDto = null;
        Result<com.get.financecenter.vo.PrepayApplicationFormVo> result = financeCenterClient.getBorrowMoneyById(Long.valueOf(businessKey));
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            com.get.financecenter.vo.PrepayApplicationFormVo mborrowMoneyDto_ = result.getData();
            mborrowMoneyDto = BeanCopyUtils.objClone(mborrowMoneyDto_, PrepayApplicationFormVo::new);
        }
        if (mborrowMoneyDto == null || mborrowMoneyDto.getStatus() != 0 || mborrowMoneyDto.getStatus() == 5) {
            return false;
        }
        Result<StaffVo> result1 = permissionCenterClient.getStaffById(GetAuthInfo.getStaffId());
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }
        StaffVo staffVo = result1.getData();
        WorkFlowPrepayApplicationForm payForm = BeanCopyUtils.objClone(mborrowMoneyDto, WorkFlowPrepayApplicationForm::new);
        Map<String, Object> map = new HashMap<>();
        map.put("userid", username);
        map.put("companyId", companyId);
        map.put("tableName", TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key);
        map.put("businessKey", payForm.getId());
        map.put("prepay", payForm);
        map.put("staff", staffVo);
        map.put("applyUser", GetAuthInfo.getStaffId());
        try {

            //表名 +公司id
            List<ProcessDefinition> processDefinition =
                    repositoryService.createProcessDefinitionQuery().processDefinitionKey(procdefKey).processDefinitionTenantId(companyId).orderByProcessDefinitionVersion().desc().list();
            String id = "";
            for (ProcessDefinition pdid : processDefinition) {
                id = pdid.getId();
                break;
            }
            Authentication.setAuthenticatedUserId(username);
            ProcessInstance processInstance = runtimeService.startProcessInstanceById(id, String.valueOf(payForm.getId()), map);
            payForm.setStatus(2);
            PrepayApplicationForm prepayApplicationForm_ = BeanCopyUtils.objClone(payForm, PrepayApplicationForm::new);
            financeCenterClient.updateBorrowMoneyStatus(prepayApplicationForm_);
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }


    @Override
    public ActRuTaskVo getTaskDataByBusinessKey(String businessKey, String key) {
        ActRuTaskVo actRuTaskVo = new ActRuTaskVo();
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processDefinitionKey(key).processInstanceBusinessKey(businessKey).singleResult();
        if (historicProcessInstance != null) {
            actRuTaskVo.setProcInstId(historicProcessInstance.getId());

            ActRuTaskVo taskVersionByProcessInId = actRuTaskMapper.getTaskVersionByProcessInId(historicProcessInstance.getId());
            if (taskVersionByProcessInId != null) {
                ProcessDefinition processDefinitionQuery = repositoryService.createProcessDefinitionQuery().processDefinitionId(historicProcessInstance.getProcessDefinitionId()).singleResult();
                actRuTaskVo.setId(taskVersionByProcessInId.getId());
                actRuTaskVo.setDeployId(processDefinitionQuery.getDeploymentId());
                actRuTaskVo.setRev(taskVersionByProcessInId.getRev());
                return actRuTaskVo;

            }

        }
        return actRuTaskVo;
    }
}
