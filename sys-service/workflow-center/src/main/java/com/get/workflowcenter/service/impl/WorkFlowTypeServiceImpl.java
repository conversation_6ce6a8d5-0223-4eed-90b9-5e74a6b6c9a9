package com.get.workflowcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.workflowcenter.vo.WorkFlowTypeVo;
import com.get.workflowcenter.entity.WorkFlowType;
import com.get.workflowcenter.mapper.WorkFlowTypeMapper;
import com.get.workflowcenter.service.IWorkFlowTypeService;
import com.get.workflowcenter.dto.WorkFlowTypeDto;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/1/13 12:08
 * @verison: 1.0
 * @description:
 */
@Service
public class WorkFlowTypeServiceImpl implements IWorkFlowTypeService {
    @Resource
    private WorkFlowTypeMapper workFlowTypeMapper;
    @Resource
    private UtilService utilService;

    @Override
    public WorkFlowTypeVo findWorkFlowTypeById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        WorkFlowType workFlowType = workFlowTypeMapper.selectById(id);
        return BeanCopyUtils.objClone(workFlowType, WorkFlowTypeVo::new);
    }

    //    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = YException.class)
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void batchAdd(List<WorkFlowTypeDto> workFlowTypeDtos) {
        if (GeneralTool.isEmpty(workFlowTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        for (WorkFlowTypeDto workFlowTypeDto : workFlowTypeDtos) {
            if (validateAdd(workFlowTypeDto)) {
                //获取最大排序
                workFlowTypeDto.setViewOrder(workFlowTypeMapper.getMaxViewOrder());
                WorkFlowType workFlowType = BeanCopyUtils.objClone(workFlowTypeDto, WorkFlowType::new);
                utilService.updateUserInfoToEntity(workFlowType);
                int i = workFlowTypeMapper.insertSelective(workFlowType);
                if (i <= 0) {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("insert_fail"));
                }
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
            }
        }
    }

    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (workFlowTypeMapper.selectById(id) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        int i = workFlowTypeMapper.deleteById(id);
        if (i <= 0) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
    }

    @Override
    public WorkFlowTypeVo updateWorkFlowType(WorkFlowTypeDto workFlowTypeDto) {
        if (workFlowTypeDto == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        WorkFlowType result = workFlowTypeMapper.selectById(workFlowTypeDto.getId());
        if (result == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        if (validateUpdate(workFlowTypeDto)) {
            WorkFlowType workFlowType = BeanCopyUtils.objClone(workFlowTypeDto, WorkFlowType::new);
            utilService.updateUserInfoToEntity(workFlowType);
            int i = workFlowTypeMapper.updateById(workFlowType);
            if (i <= 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("update_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("type_name_exists"));
        }
        return findWorkFlowTypeById(workFlowTypeDto.getId());
    }

    @Override
    public List<WorkFlowTypeVo> getWorkFlowTypes(WorkFlowTypeDto workFlowTypeDto, Page page) {
//        Example example = new Example(WorkFlowType.class);
//        Example.Criteria criteria = example.createCriteria();
//        if (GeneralTool.isNotEmpty(workFlowTypeDto)){
//            if (GeneralTool.isNotEmpty(workFlowTypeDto.getTypeName())){
//                criteria.andLike("typeName","%" + workFlowTypeDto.getTypeName() + "%");
//            }
//        }
//        example.orderBy("viewOrder").desc();
//        PageHelper.startPage(page.getCurrentPage(),page.getShowCount());
//        List<WorkFlowType> workFlowTypes = workFlowTypeMapper.selectByExample(example);
//        page.restPage(workFlowTypes);
        LambdaQueryWrapper<WorkFlowType> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(workFlowTypeDto)) {
            if (GeneralTool.isNotEmpty(workFlowTypeDto.getTypeName())) {
                wrapper.like(WorkFlowType::getTypeName, workFlowTypeDto.getTypeName());
            }
        }
        IPage<WorkFlowType> pages = this.workFlowTypeMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<WorkFlowType> workFlowTypes = pages.getRecords();
        return workFlowTypes.stream().map(workFlowType -> BeanCopyUtils.objClone(workFlowType, WorkFlowTypeVo::new)).collect(Collectors.toList());
    }

    @Override
    public void movingOrder(List<WorkFlowTypeDto> workFlowTypeDtos) {
        if (GeneralTool.isEmpty(workFlowTypeDtos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        WorkFlowType ro = BeanCopyUtils.objClone(workFlowTypeDtos.get(0), WorkFlowType::new);
        Integer oneorder = ro.getViewOrder();
        WorkFlowType rt = BeanCopyUtils.objClone(workFlowTypeDtos.get(1), WorkFlowType::new);
        Integer twoorder = rt.getViewOrder();
        ro.setViewOrder(twoorder);
        utilService.updateUserInfoToEntity(ro);
        rt.setViewOrder(oneorder);
        utilService.updateUserInfoToEntity(rt);
        workFlowTypeMapper.updateById(ro);
        workFlowTypeMapper.updateById(rt);
    }

    @Override
    public String getWorkFlowTypeNameById(Long workFlowTypeId) {
        return workFlowTypeMapper.getWorkFlowTypeNameById(workFlowTypeId);
    }

    @Override
    public List<WorkFlowTypeVo> getWorkFlowTypeList() {
//        Example example = new Example(WorkFlowType.class);
//        example.orderBy("viewOrder").desc();
//        List<WorkFlowType> workFlowTypes = workFlowTypeMapper.selectByExample(example);
        List<WorkFlowType> workFlowTypes = workFlowTypeMapper.selectList(Wrappers.<WorkFlowType>query().lambda().orderByDesc(WorkFlowType::getViewOrder));
        return BeanCopyUtils.copyListProperties(workFlowTypes, WorkFlowTypeVo::new);
    }

    private boolean validateAdd(WorkFlowTypeDto workFlowTypeDto) {
//        Example example = new Example(WorkFlowType.class);
////        Example.Criteria criteria = example.createCriteria();
////        criteria.andEqualTo("typeName", workFlowTypeDto.getTypeName());
////        List<WorkFlowType> list = this.workFlowTypeMapper.selectByExample(example);
        List<WorkFlowType> list = workFlowTypeMapper.selectList(Wrappers.<WorkFlowType>query().lambda().eq(WorkFlowType::getTypeName, workFlowTypeDto.getTypeName()));
        return GeneralTool.isEmpty(list);
    }

    private boolean validateUpdate(WorkFlowTypeDto workFlowTypeDto) {
//        Example example = new Example(WorkFlowType.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("typeName", workFlowTypeDto.getTypeName());
//        List<WorkFlowType> list = this.workFlowTypeMapper.selectByExample(example);
        List<WorkFlowType> list = this.workFlowTypeMapper.selectList(Wrappers.<WorkFlowType>query().lambda().eq(WorkFlowType::getTypeName, workFlowTypeDto.getTypeName()));
        return list.size() <= 0 || list.get(0).getId().equals(workFlowTypeDto.getId());
    }
}
