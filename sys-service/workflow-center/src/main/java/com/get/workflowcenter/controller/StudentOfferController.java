package com.get.workflowcenter.controller;

import com.get.workflowcenter.service.IStudentOfferWorkFlowService;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

/**
 * Created by <PERSON>.
 * Time: 17:25
 * Date: 2021/11/3
 * Description:学生申请方案流程管理
 */
@Api(tags = "学生申请方案流程")
@RestController
@RequestMapping("workflow/studentOfferFlow")
@Slf4j
public class StudentOfferController {

    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private IStudentOfferWorkFlowService studentOfferWorkFlowService;
//    @Autowired
//    private ISaleCenterClient saleCenterClient;


    /**
     * @Description: 发起学生申请方案流程
     * @Author: Jerry
     * @Date:17:27 2021/11/3
     */
    @ApiIgnore
    @PostMapping("/startStudentOfferFlow")
    public Boolean startStudentOfferFlow(@RequestParam("businessKey") String businessKey,
                                         @RequestParam("procdefKey") String procdefKey,
                                         @RequestParam("companyId") String companyId,
                                         @RequestParam("buttonType") String buttonType,
                                         @RequestParam(value = "submitReason",required = false) String submitReason,
                                         @RequestParam(value = "fkCancelOfferReasonId",required = false) Long fkCancelOfferReasonId) {
        return studentOfferWorkFlowService.startStudentOfferFlow(businessKey, procdefKey, companyId, buttonType,submitReason,fkCancelOfferReasonId);
//        if (GeneralTool.isEmpty(businessKey) || GeneralTool.isEmpty(procdefKey) || GeneralTool.isEmpty(companyId)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
//        String username = String.valueOf(GetAuthInfo.getStaffId());
//        Authentication.setAuthenticatedUserId(username);
//
//        Map<String, Object> map = new HashMap<>();
//        map.put("userid", username);
//        map.put("createStaffId", username);
//        map.put("companyId", companyId);
//        map.put("tableName", procdefKey);
//        map.put("businessKey", businessKey);
//        map.put("buttonType", buttonType);
//
//        try {
//
//            //表名 +公司id
//            List<ProcessDefinition> processDefinition =
//                    repositoryService.createProcessDefinitionQuery().processDefinitionKey(procdefKey).processDefinitionTenantId(companyId).orderByProcessDefinitionVersion().desc().list();
//            if(GeneralTool.isEmpty(processDefinition)){
//                throw new RuntimeException("未找到相关流程");
//            }
//
//            //发起流程
//            runtimeService.startProcessInstanceById(processDefinition.get(0).getId(), businessKey, map);
//            Result<com.get.salecenter.vo.StudentOfferVo> studentOfferDtoResult = saleCenterClient.getStudentOfferDetail(Long.valueOf(businessKey));
//            if(studentOfferDtoResult.isSuccess() && GeneralTool.isNotEmpty(studentOfferDtoResult.getData()))
//            {
//                com.get.salecenter.vo.StudentOfferVo studentOfferDetail = studentOfferDtoResult.getData();
//                studentOfferDetail.setStatusWorkflow(2);
//                studentOfferDetail.setFkStaffIdWorkflow(GetAuthInfo.getStaffId());
//                saleCenterClient.updateStudentOffer(studentOfferDetail);
//            }
//
//            return true;
//        } catch (Exception e) {
//            log.error("发起学生申请方案流程异常",e);
//            return false;
//        }
    }
}
