<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.workflowcenter.mapper.ActHiCommentMapper">
  <resultMap id="BaseResultMap" type="com.get.workflowcenter.entity.ActHiComment">
    <id column="ID_" jdbcType="VARCHAR" property="id" />
    <result column="TYPE_" jdbcType="VARCHAR" property="type" />
    <result column="TIME_" jdbcType="TIMESTAMP" property="time" />
    <result column="USER_ID_" jdbcType="VARCHAR" property="userId" />
    <result column="TASK_ID_" jdbcType="VARCHAR" property="taskId" />
    <result column="PROC_INST_ID_" jdbcType="VARCHAR" property="procInstId" />
    <result column="ACTION_" jdbcType="VARCHAR" property="action" />
    <result column="MESSAGE_" jdbcType="VARCHAR" property="message" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.get.workflowcenter.entity.ActHiComment">
    <result column="FULL_MSG_" jdbcType="LONGVARBINARY" property="fullMsg" />
  </resultMap>
  <sql id="Base_Column_List">
    ID_, TYPE_, TIME_, USER_ID_, TASK_ID_, PROC_INST_ID_, ACTION_, MESSAGE_
  </sql>
  <sql id="Blob_Column_List">
    FULL_MSG_
  </sql>

  <insert id="insertSelective" parameterType="com.get.workflowcenter.entity.ActHiComment" keyProperty="id"
          useGeneratedKeys="true">
    insert into act_hi_comment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        ID_,
      </if>
      <if test="type != null">
        TYPE_,
      </if>
      <if test="time != null">
        TIME_,
      </if>
      <if test="userId != null">
        USER_ID_,
      </if>
      <if test="taskId != null">
        TASK_ID_,
      </if>
      <if test="procInstId != null">
        PROC_INST_ID_,
      </if>
      <if test="action != null">
        ACTION_,
      </if>
      <if test="message != null">
        MESSAGE_,
      </if>
      <if test="fullMsg != null">
        FULL_MSG_,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="time != null">
        #{time,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=VARCHAR},
      </if>
      <if test="procInstId != null">
        #{procInstId,jdbcType=VARCHAR},
      </if>
      <if test="action != null">
        #{action,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        #{message,jdbcType=VARCHAR},
      </if>
      <if test="fullMsg != null">
        #{fullMsg,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>

</mapper>