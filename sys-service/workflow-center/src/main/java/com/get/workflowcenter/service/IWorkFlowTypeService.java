package com.get.workflowcenter.service;

import com.get.common.result.Page;
import com.get.workflowcenter.vo.WorkFlowTypeVo;
import com.get.workflowcenter.dto.WorkFlowTypeDto;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/13 12:08
 * @verison: 1.0
 * @description:
 */
public interface IWorkFlowTypeService {

    /**
     * @return com.get.workflowcenter.vo.WorkFlowTypeDto
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    WorkFlowTypeVo findWorkFlowTypeById(Long id);

    /**
     * @return void
     * @Description :批量新增
     * @Param [workFlowTypeDtos]
     * <AUTHOR>
     */
    void batchAdd(List<WorkFlowTypeDto> workFlowTypeDtos);

    /**
     * @return void
     * @Description :删除
     * @Param [id]
     * <AUTHOR>
     */
    void delete(Long id);

    /**
     * @return com.get.workflowcenter.vo.WorkFlowTypeDto
     * @Description :修改
     * @Param [workFlowTypeDto]
     * <AUTHOR>
     */
    WorkFlowTypeVo updateWorkFlowType(WorkFlowTypeDto workFlowTypeDto);

    /**
     * @return java.util.List<com.get.workflowcenter.vo.WorkFlowTypeDto>
     * @Description :列表
     * @Param [workFlowTypeDto, page]
     * <AUTHOR>
     */
    List<WorkFlowTypeVo> getWorkFlowTypes(WorkFlowTypeDto workFlowTypeDto, Page page);

    /**
     * @return void
     * @Description :上移下移
     * @Param [workFlowTypeDtos]
     * <AUTHOR>
     */
    void movingOrder(List<WorkFlowTypeDto> workFlowTypeDtos);

    /**
     * @return java.lang.String
     * @Description :通过id查找流程类型名称
     * @Param [workFlowTypeId]
     * <AUTHOR>
     */
    String getWorkFlowTypeNameById(Long workFlowTypeId);

    /**
     * @return java.util.List<com.get.workflowcenter.vo.WorkFlowTypeDto>
     * @Description :流程类型下拉框数据
     * @Param []
     * <AUTHOR>
     */
    List<WorkFlowTypeVo> getWorkFlowTypeList();
}
