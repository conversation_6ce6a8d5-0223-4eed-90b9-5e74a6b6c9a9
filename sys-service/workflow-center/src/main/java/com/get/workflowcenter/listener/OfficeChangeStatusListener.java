package com.get.workflowcenter.listener;

import com.get.common.cache.CacheNames;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.officecenter.dto.LeaveLogDto;
import com.get.officecenter.dto.LeaveStockDto;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.officecenter.feign.IOfficeCenterClient;
import com.get.officecenter.vo.LeaveLogVo;
import com.get.officecenter.vo.LeaveStockVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.workflowcenter.component.IWorkFlowHelper;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.task.Task;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: Sea
 * @create: 2021/4/8 11:57
 * @verison: 1.0
 * @description: 办公中心根据流程节点连线判断修改业务表单状态监听器
 */
@Slf4j
public class OfficeChangeStatusListener implements ExecutionListener {
    private static final String PARAM = "sequenceFlowsStatus";
    private static final String ANNUAL_LEAVE = "年假";
    private static final String COMPENSATORY_LEAVE = "补休";
    private static final String cache_key = "task_count";

    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    @Override
    public void notify(DelegateExecution execution) {
        IOfficeCenterClient feginOfficeService = SpringUtil.getBean(IOfficeCenterClient.class);
        IPermissionCenterClient feignPermissionService = SpringUtil.getBean(IPermissionCenterClient.class);
        TaskService taskService = SpringUtil.getBean(TaskService.class);
        RepositoryService repositoryService = SpringUtil.getBean(RepositoryService.class);
        //通过流程定义对象查找出KEY_  就是表名
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(execution.getProcessDefinitionId()).singleResult();
        String tableName = processDefinition.getKey();
        String businessKey = execution.getProcessInstanceBusinessKey();
        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);
        StaffVo staffVo = workFlowHelper.getExecutionStaffDto(execution);
        Long starterId = staffVo.getId();

        LeaveApplicationForm leaveApplicationForm = new LeaveApplicationForm();
        Result<LeaveApplicationForm> result = feginOfficeService.getLeaveApplicationForm(Long.valueOf(businessKey));
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            leaveApplicationForm = result.getData();
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("form_not_found"));
        }

        List<Long> staffIdsByResourceKey = feignPermissionService.getStaffIdsByResourceKey("officeLeaveApplicationForm.AllForms", true);

        //结束节点 start状态设置已结束
        if ("start".equals(execution.getEventName())) {
            Result<Boolean> changeStatusResult = feginOfficeService.changeStatus(1, tableName, Long.valueOf(businessKey));
            if (!changeStatusResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("form_status_update_fail"));
            }

            if (GeneralTool.isNotEmpty(staffIdsByResourceKey)) {
                for (Long aLong : staffIdsByResourceKey) {
                    CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(aLong), cache_key);
                }
            }
            //判断为年假或者补休，减去相应时间
            String leaveTypeKey = feginOfficeService.getLeaveTypeKeyById(Long.valueOf(businessKey));
            BigDecimal days = leaveApplicationForm.getDays();
            //如果为年假
            if (ProjectKeyEnum.ANNUAL_VACATION.key.equals(leaveTypeKey)) {
                if (!Long.valueOf(1L).equals(leaveApplicationForm.getFkCompanyId())) {
                    //获取库存
                    List<LeaveStockVo> stock = getLeaveStockDtos(workFlowHelper, starterId, leaveApplicationForm, leaveTypeKey);

                    for (LeaveStockVo leaveStockVo : stock) {
                        //正常流程
                        if (GeneralTool.isEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())) {
                            //库存足够
                            if (leaveStockVo.getLeaveStock().compareTo(days) >= 0) {
                                leaveStockVo.setLeaveStock(leaveStockVo.getLeaveStock().subtract(days));
                                //更新库存
                                LeaveStockDto stockVo = new LeaveStockDto();
                                BeanUtils.copyProperties(leaveStockVo, stockVo);
                                stockVo.setGmtModified(new Date());
                                stockVo.setGmtModifiedUser("[system]");
                                workFlowHelper.updateSystemLeavetock(stockVo);
                                //添加日志
                                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.ANNUAL_LEAVE_DEDUCTION, days.negate(), leaveStockVo.getId());
                                break;
                            } else {
                                //库存不足减
                                //库存置为0
                                BigDecimal bigDecimal;
                                bigDecimal = leaveStockVo.getLeaveStock();
                                days = days.subtract(bigDecimal);

                                leaveStockVo.setLeaveStock(BigDecimal.ZERO);
                                //更新库存
                                LeaveStockDto stockVo = new LeaveStockDto();
                                BeanUtils.copyProperties(leaveStockVo, stockVo);
                                stockVo.setGmtModified(new Date());
                                stockVo.setGmtModifiedUser("[system]");
                                workFlowHelper.updateSystemLeavetock(stockVo);
                                //添加日志
                                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.ANNUAL_LEAVE_DEDUCTION, bigDecimal.negate(), leaveStockVo.getId());
                            }
                        } else {
                            //撤单流程
                            LeaveLogDto leaveLogVo = new LeaveLogDto();
                            leaveLogVo.setFkLeaveApplicationFormId(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke());
                            List<LeaveLogVo> leaveLogVos = workFlowHelper.getSystemLeaveLog(leaveLogVo);
                            leaveLogVos = leaveLogVos.stream().filter(leaveLogDto -> leaveLogDto.getDuration().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());

                            for (LeaveLogVo leaveLogDto1 : leaveLogVos) {
                                //根据logvo的FkLeaveStockId查出库存信息
                                LeaveStockDto stockVo = new LeaveStockDto();
                                stockVo.setId(leaveLogDto1.getFkLeaveStockId());
                                List<LeaveStockVo> stockDtos = workFlowHelper.getLeaveStockDtos(stockVo);
                                for (LeaveStockVo dto : stockDtos) {
                                    if (!leaveStockVo.getId().equals(dto.getId())) {
                                        continue;
                                    }
                                    //加回库存
                                    dto.setLeaveStock(dto.getLeaveStock().add(leaveLogDto1.getDuration().negate()));
                                    //更新库存
                                    LeaveStockDto updateStockVo = new LeaveStockDto();
                                    BeanUtils.copyProperties(dto, updateStockVo);
                                    updateStockVo.setGmtModified(new Date());
                                    updateStockVo.setGmtModifiedUser("[system]");
                                    workFlowHelper.updateSystemLeavetock(updateStockVo);
                                    //添加日志
                                    addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.WORKFLOW_CANCELLATION, leaveLogDto1.getDuration().negate(), leaveLogDto1.getFkLeaveStockId());
                                }

                            }
                        }
                    }
                } else {
                    //库存可以负数
                    List<LeaveStockVo> stock = getEfficientLeaveStockDtos(workFlowHelper, starterId, leaveApplicationForm, leaveTypeKey);
                    if (GeneralTool.isEmpty(stock)) {
                        LeaveStockDto stockVo = new LeaveStockDto();
                        stockVo.setFkCompanyId(staffVo.getFkCompanyId());
                        stockVo.setFkStaffId(starterId);
                        stockVo.setLeaveTypeKey(leaveTypeKey);
                        stockVo.setLeaveStock(BigDecimal.ZERO);
                        stockVo.setGmtCreate(new Date());
                        stockVo.setGmtCreateUser("[system]");
                        //新增一条记录
                        workFlowHelper.addSystem(stockVo);

                        //重新查询
                        stock = getEfficientLeaveStockDtos(workFlowHelper, starterId, leaveApplicationForm, leaveTypeKey);
                    }

                    for (LeaveStockVo leaveStockVo : stock) {
                        if (GeneralTool.isEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())) {
                            leaveStockVo.setLeaveStock(leaveStockVo.getLeaveStock().subtract(days));
                            //更新库存
                            LeaveStockDto stockVo = new LeaveStockDto();
                            BeanUtils.copyProperties(leaveStockVo, stockVo);
                            stockVo.setGmtModified(new Date());
                            stockVo.setGmtModifiedUser("[system]");
                            workFlowHelper.updateSystemLeavetock(stockVo);
                            //添加日志
                            addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.ANNUAL_LEAVE_DEDUCTION, days.negate(), leaveStockVo.getId());
                            break;
                        } else {
                            //撤单流程
                            LeaveLogDto leaveLogVo = new LeaveLogDto();
                            leaveLogVo.setFkLeaveApplicationFormId(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke());
                            List<LeaveLogVo> leaveLogVos = workFlowHelper.getSystemLeaveLog(leaveLogVo);
                            leaveLogVos = leaveLogVos.stream().filter(leaveLogDto -> leaveLogDto.getDuration().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());

                            for (LeaveLogVo leaveLogDto1 : leaveLogVos) {
                                //根据logvo的FkLeaveStockId查出库存信息
                                LeaveStockDto stockVo = new LeaveStockDto();
                                stockVo.setId(leaveLogDto1.getFkLeaveStockId());
                                List<LeaveStockVo> stockDtos = workFlowHelper.getLeaveStockDtos(stockVo);
                                for (LeaveStockVo dto : stockDtos) {
                                    if (!leaveStockVo.getId().equals(dto.getId())) {
                                        continue;
                                    }
                                    //加回库存
                                    dto.setLeaveStock(dto.getLeaveStock().add(leaveLogDto1.getDuration().negate()));
                                    //更新库存
                                    LeaveStockDto updateStockVo = new LeaveStockDto();
                                    BeanUtils.copyProperties(dto, updateStockVo);
                                    updateStockVo.setGmtModified(new Date());
                                    updateStockVo.setGmtModifiedUser("[system]");
                                    workFlowHelper.updateSystemLeavetock(updateStockVo);
                                    //添加日志
                                    addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.WORKFLOW_CANCELLATION, leaveLogDto1.getDuration().negate(), leaveLogDto1.getFkLeaveStockId());
                                }

                            }
                        }
                    }
                }
                //补休
            } else if (ProjectKeyEnum.TAKE_DEFERRED_HOLIDAYS.key.equals(leaveTypeKey)) {
                if (!Long.valueOf(1L).equals(leaveApplicationForm.getFkCompanyId())) {
                    //获取库存
                    List<LeaveStockVo> stock = getLeaveStockDtos(workFlowHelper, starterId, leaveApplicationForm, leaveTypeKey);
                    for (LeaveStockVo leaveStockVo : stock) {
                        //正常流程
                        if (GeneralTool.isEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())) {
                            //库存足够
                            if (leaveStockVo.getLeaveStock().compareTo(days) >= 0) {
                                leaveStockVo.setLeaveStock(leaveStockVo.getLeaveStock().subtract(days));
                                //更新库存
                                LeaveStockDto stockVo = new LeaveStockDto();
                                BeanUtils.copyProperties(leaveStockVo, stockVo);
                                stockVo.setGmtModified(new Date());
                                stockVo.setGmtModifiedUser("[system]");
                                workFlowHelper.updateSystemLeavetock(stockVo);
                                //添加日志
                                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.COMPENSATORY_LEAVE_DEDUCTION, days.negate(), leaveStockVo.getId());
                                break;
                            } else {
                                //库存不足减
                                //库存置为0
                                BigDecimal bigDecimal;
                                bigDecimal = leaveStockVo.getLeaveStock();
                                days = days.subtract(leaveStockVo.getLeaveStock());
                                leaveStockVo.setLeaveStock(BigDecimal.ZERO);
                                //更新库存
                                LeaveStockDto stockVo = new LeaveStockDto();
                                BeanUtils.copyProperties(leaveStockVo, stockVo);
                                stockVo.setGmtModified(new Date());
                                stockVo.setGmtModifiedUser("[system]");
                                workFlowHelper.updateSystemLeavetock(stockVo);
                                //添加日志
                                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.COMPENSATORY_LEAVE_DEDUCTION, bigDecimal.negate(), leaveStockVo.getId());
                            }
                        } else {
                            //撤单流程
                            LeaveLogDto leaveLogVo = new LeaveLogDto();
                            leaveLogVo.setFkLeaveApplicationFormId(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke());
                            List<LeaveLogVo> leaveLogVos = workFlowHelper.getSystemLeaveLog(leaveLogVo);
                            leaveLogVos = leaveLogVos.stream().filter(leaveLogDto -> leaveLogDto.getDuration().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
                            for (LeaveLogVo leaveLogDto1 : leaveLogVos) {
                                LeaveStockDto stockVo = new LeaveStockDto();
                                stockVo.setId(leaveLogDto1.getFkLeaveStockId());
                                List<LeaveStockVo> stockDtos = workFlowHelper.getLeaveStockDtos(stockVo);
                                for (LeaveStockVo dto : stockDtos) {
                                    if (!leaveStockVo.getId().equals(dto.getId())) {
                                        continue;
                                    }
                                    //加回库存
                                    dto.setLeaveStock(dto.getLeaveStock().add(leaveLogDto1.getDuration().negate()));
                                    //更新库存
                                    LeaveStockDto updateStockVo = new LeaveStockDto();
                                    BeanUtils.copyProperties(dto, updateStockVo);
                                    updateStockVo.setGmtModified(new Date());
                                    updateStockVo.setGmtModifiedUser("[system]");
                                    workFlowHelper.updateSystemLeavetock(updateStockVo);
                                    //添加日志
                                    addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.WORKFLOW_CANCELLATION, leaveLogDto1.getDuration().negate(), dto.getId());
                                }
                            }
                        }
                    }
                } else {
                    //库存可以负数
                    List<LeaveStockVo> stock = getEfficientLeaveStockDtos(workFlowHelper, starterId, leaveApplicationForm, leaveTypeKey);
                    if (GeneralTool.isEmpty(stock)) {
                        LeaveStockDto stockVo = new LeaveStockDto();
                        stockVo.setFkCompanyId(staffVo.getFkCompanyId());
                        stockVo.setFkStaffId(starterId);
                        stockVo.setLeaveTypeKey(leaveTypeKey);
                        stockVo.setLeaveStock(BigDecimal.ZERO);
                        stockVo.setGmtCreate(new Date());
                        stockVo.setGmtCreateUser("[system]");
                        //新增一条记录
                        workFlowHelper.addSystem(stockVo);

                        //重新查询
                        stock = getEfficientLeaveStockDtos(workFlowHelper, starterId, leaveApplicationForm, leaveTypeKey);
                    }

                    for (LeaveStockVo leaveStockVo : stock) {
                        if (GeneralTool.isEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())) {
                            leaveStockVo.setLeaveStock(leaveStockVo.getLeaveStock().subtract(days));
                            //更新库存
                            LeaveStockDto stockVo = new LeaveStockDto();
                            BeanUtils.copyProperties(leaveStockVo, stockVo);
                            stockVo.setGmtModified(new Date());
                            stockVo.setGmtModifiedUser("[system]");
                            workFlowHelper.updateSystemLeavetock(stockVo);
                            //添加日志
                            addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.COMPENSATORY_LEAVE_DEDUCTION, days.negate(), leaveStockVo.getId());
                            break;
                        } else {
                            //撤单流程
                            LeaveLogDto leaveLogVo = new LeaveLogDto();
                            leaveLogVo.setFkLeaveApplicationFormId(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke());
                            List<LeaveLogVo> leaveLogVos = workFlowHelper.getSystemLeaveLog(leaveLogVo);
                            leaveLogVos = leaveLogVos.stream().filter(leaveLogDto -> leaveLogDto.getDuration().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());

                            for (LeaveLogVo leaveLogDto1 : leaveLogVos) {
                                //根据logvo的FkLeaveStockId查出库存信息
                                LeaveStockDto stockVo = new LeaveStockDto();
                                stockVo.setId(leaveLogDto1.getFkLeaveStockId());
                                List<LeaveStockVo> stockDtos = workFlowHelper.getLeaveStockDtos(stockVo);
                                for (LeaveStockVo dto : stockDtos) {
                                    if (!leaveStockVo.getId().equals(dto.getId())) {
                                        continue;
                                    }
                                    //加回库存
                                    dto.setLeaveStock(dto.getLeaveStock().add(leaveLogDto1.getDuration().negate()));
                                    //更新库存
                                    LeaveStockDto updateStockVo = new LeaveStockDto();
                                    BeanUtils.copyProperties(dto, updateStockVo);
                                    updateStockVo.setGmtModified(new Date());
                                    updateStockVo.setGmtModifiedUser("[system]");
                                    workFlowHelper.updateSystemLeavetock(updateStockVo);
                                    //添加日志
                                    addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.WORKFLOW_CANCELLATION, leaveLogDto1.getDuration().negate(), leaveLogDto1.getFkLeaveStockId());
                                }
                            }
                        }
                    }
                }
                //加班
            } else if (ProjectKeyEnum.OVERTIEM.key.equals(leaveTypeKey)) {
                //加班类型
                //获取补休库存
                List<LeaveStockVo> stock = getLeaveStockDtos(workFlowHelper, starterId, leaveApplicationForm, ProjectKeyEnum.TAKE_DEFERRED_HOLIDAYS.key);
                if (GeneralTool.isEmpty(stock)) {
                    //没有补休-新增补休信息
                    //更新库存
                    LeaveStockDto stockVo = new LeaveStockDto();
                    stockVo.setFkCompanyId(staffVo.getFkCompanyId());
                    stockVo.setFkStaffId(starterId);
                    stockVo.setLeaveTypeKey(ProjectKeyEnum.TAKE_DEFERRED_HOLIDAYS.key);
                    stockVo.setLeaveStock(days);
                    stockVo.setGmtCreate(new Date());
                    stockVo.setGmtCreateUser("[system]");
                    //新增一条记录
                    workFlowHelper.addSystem(stockVo);
                    //添加日志
                    //新增不需要加FkLeaveStockId
                    addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.OVERTIME_TO_INCREASE_COMPENSATORY_TIME_OFF, days, null);
                    return;
                }
                for (LeaveStockVo leaveStockVo : stock) {
                    //正常流程
//                    if (GeneralTool.isEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke()) && leaveStockVo.getEffectiveDeadline() == null) {
                    if (GeneralTool.isEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())) {
                        //更新库存
                        leaveStockVo.setLeaveStock(leaveStockVo.getLeaveStock().add(days));
                        LeaveStockDto stockVo = new LeaveStockDto();
                        BeanUtils.copyProperties(leaveStockVo, stockVo);
                        stockVo.setGmtModified(new Date());
                        stockVo.setGmtModifiedUser("[system]");
                        workFlowHelper.updateSystemLeavetock(stockVo);
                        //添加日志
                        addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.OVERTIME_TO_INCREASE_COMPENSATORY_TIME_OFF, days, leaveStockVo.getId());
                        break;
                    } else {
                        //撤单流程
                        LeaveLogDto leaveLogVo = new LeaveLogDto();
                        leaveLogVo.setFkLeaveApplicationFormId(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke());
                        List<LeaveLogVo> leaveLogVos = workFlowHelper.getSystemLeaveLog(leaveLogVo);
                        leaveLogVos = leaveLogVos.stream().filter(leaveLogDto -> leaveLogDto.getDuration().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                        for (LeaveLogVo leaveLogDto1 : leaveLogVos) {
                            LeaveStockDto stockVo = new LeaveStockDto();
                            stockVo.setId(leaveLogDto1.getFkLeaveStockId());
                            List<LeaveStockVo> stockDtos = workFlowHelper.getLeaveStockDtos(stockVo);
                            for (LeaveStockVo dto : stockDtos) {
                                if (!leaveStockVo.getId().equals(dto.getId())) {
                                    continue;
                                }
                                //加回库存
                                dto.setLeaveStock(dto.getLeaveStock().add(leaveLogDto1.getDuration().negate()));
                                //更新库存
                                LeaveStockDto updateStockVo = new LeaveStockDto();
                                BeanUtils.copyProperties(dto, updateStockVo);
                                updateStockVo.setGmtModified(new Date());
                                updateStockVo.setGmtModifiedUser("[system]");
                                workFlowHelper.updateSystemLeavetock(updateStockVo);
                                //添加日志
                                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.WORKFLOW_CANCELLATION, leaveLogDto1.getDuration().negate(), updateStockVo.getId());
                            }
                        }
                    }
                }

            } else if (ProjectKeyEnum.DISEASE_VACATION.key.equals(leaveTypeKey)) {
//                //gea不用考虑病假库存、iae也不考虑、get不考虑
//                if (!Long.valueOf(2L).equals(leaveApplicationForm.getFkCompanyId()) && !Long.valueOf(3L).equals(leaveApplicationForm.getFkCompanyId()) && !Long.valueOf(1L).equals(leaveApplicationForm.getFkCompanyId())) {
//                    //获取库存
//                    List<LeaveStockVo> stock = getLeaveStockDtos(workFlowHelper, starterId, leaveApplicationForm, ProjectKeyEnum.TAKE_DEFERRED_HOLIDAYS.key);
//                    for (LeaveStockVo leaveStockVo : stock) {
//                        //正常流程
//                        if (GeneralTool.isEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())) {
//                            //库存足够
//                            if (leaveStockVo.getLeaveStock().compareTo(days) >= 0) {
//                                leaveStockVo.setLeaveStock(leaveStockVo.getLeaveStock().subtract(days));
//                                //更新库存
//                                LeaveStockDto stockVo = new LeaveStockDto();
//                                BeanUtils.copyProperties(leaveStockVo, stockVo);
//                                stockVo.setGmtModified(new Date());
//                                stockVo.setGmtModifiedUser("[system]");
//                                workFlowHelper.updateSystemLeavetock(stockVo);
//                                //添加日志
//                                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.SICK_LEAVE_DEDUCTION, days.negate(), leaveStockVo.getId());
//                                break;
//                            } else {
//                                //库存不足减
//                                //库存置为0
//                                BigDecimal bigDecimal;
//                                bigDecimal = leaveStockVo.getLeaveStock();
//                                days = days.subtract(leaveStockVo.getLeaveStock());
//                                leaveStockVo.setLeaveStock(BigDecimal.ZERO);
//                                //更新库存
//                                LeaveStockDto stockVo = new LeaveStockDto();
//                                BeanUtils.copyProperties(leaveStockVo, stockVo);
//                                stockVo.setGmtModified(new Date());
//                                stockVo.setGmtModifiedUser("[system]");
//                                workFlowHelper.updateSystemLeavetock(stockVo);
//                                //添加日志
//                                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.SICK_LEAVE_DEDUCTION, bigDecimal.negate(), leaveStockVo.getId());
//                            }
//                        } else {
//                            //撤单流程
//                            LeaveLogDto leaveLogVo = new LeaveLogDto();
//                            leaveLogVo.setFkLeaveApplicationFormId(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke());
//                            List<LeaveLogVo> leaveLogVos = workFlowHelper.getSystemLeaveLog(leaveLogVo);
//                            leaveLogVos = leaveLogVos.stream().filter(leaveLogDto -> leaveLogDto.getDuration().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());
//                            for (LeaveLogVo leaveLogDto1 : leaveLogVos) {
//                                LeaveStockDto stockVo = new LeaveStockDto();
//                                stockVo.setId(leaveLogDto1.getFkLeaveStockId());
//                                List<LeaveStockVo> stockDtos = workFlowHelper.getLeaveStockDtos(stockVo);
//                                for (LeaveStockVo dto : stockDtos) {
//                                    if (!leaveStockVo.getId().equals(dto.getId())) {
//                                        continue;
//                                    }
//                                    //加回库存
//                                    dto.setLeaveStock(dto.getLeaveStock().add(leaveLogDto1.getDuration().negate()));
//                                    //更新库存
//                                    LeaveStockDto updateStockVo = new LeaveStockDto();
//                                    BeanUtils.copyProperties(dto, updateStockVo);
//                                    updateStockVo.setGmtModified(new Date());
//                                    updateStockVo.setGmtModifiedUser("[system]");
//                                    workFlowHelper.updateSystemLeavetock(updateStockVo);
//                                    //添加日志
//                                    addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.WORKFLOW_CANCELLATION, leaveLogDto1.getDuration().negate(), dto.getId());
//                                }
//                            }
//                        }
//                    }
//
//                }
//                else {
                    //库存可以负数  //查库存有问题
                    List<LeaveStockVo> stock = getEfficientLeaveStockDtos(workFlowHelper, starterId, leaveApplicationForm, leaveTypeKey);
                    if (GeneralTool.isEmpty(stock)) {
                        LeaveStockDto stockVo = new LeaveStockDto();
                        stockVo.setFkCompanyId(staffVo.getFkCompanyId());
                        stockVo.setFkStaffId(starterId);
                        stockVo.setLeaveTypeKey(leaveTypeKey);
                        stockVo.setLeaveStock(BigDecimal.ZERO);
                        stockVo.setGmtCreate(new Date());
                        stockVo.setGmtCreateUser("[system]");
                        //新增一条记录
                        workFlowHelper.addSystem(stockVo);

                        //重新查询
                        stock = getEfficientLeaveStockDtos(workFlowHelper, starterId, leaveApplicationForm, leaveTypeKey);
                    }
                    for (LeaveStockVo leaveStockVo : stock) {
                        if (GeneralTool.isEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())) {
                            leaveStockVo.setLeaveStock(leaveStockVo.getLeaveStock().subtract(days));
                            //更新库存
                            LeaveStockDto stockVo = new LeaveStockDto();
                            BeanUtils.copyProperties(leaveStockVo, stockVo);
                            stockVo.setGmtModified(new Date());
                            stockVo.setGmtModifiedUser("[system]");
                            workFlowHelper.updateSystemLeavetock(stockVo);
                            //添加日志
                            addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.SICK_LEAVE_DEDUCTION, days.negate(), leaveStockVo.getId());
                            break;
                        } else {
                            //撤单流程
                            LeaveLogDto leaveLogVo = new LeaveLogDto();
                            leaveLogVo.setFkLeaveApplicationFormId(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke());
                            List<LeaveLogVo> leaveLogVos = workFlowHelper.getSystemLeaveLog(leaveLogVo);
                            leaveLogVos = leaveLogVos.stream().filter(leaveLogDto -> leaveLogDto.getDuration().compareTo(BigDecimal.ZERO) < 0).collect(Collectors.toList());

                            for (LeaveLogVo leaveLogDto1 : leaveLogVos) {
                                //根据logvo的FkLeaveStockId查出库存信息
                                LeaveStockDto stockVo = new LeaveStockDto();
                                stockVo.setId(leaveLogDto1.getFkLeaveStockId());
                                List<LeaveStockVo> stockDtos = workFlowHelper.getLeaveStockDtos(stockVo);
                                for (LeaveStockVo dto : stockDtos) {
                                    if (!leaveStockVo.getId().equals(dto.getId())) {
                                        continue;
                                    }
                                    //加回库存
                                    dto.setLeaveStock(dto.getLeaveStock().add(leaveLogDto1.getDuration().negate()));
                                    //更新库存
                                    LeaveStockDto updateStockVo = new LeaveStockDto();
                                    BeanUtils.copyProperties(dto, updateStockVo);
                                    updateStockVo.setGmtModified(new Date());
                                    updateStockVo.setGmtModifiedUser("[system]");
                                    workFlowHelper.updateSystemLeavetock(updateStockVo);
                                    //添加日志
                                    addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.WORKFLOW_CANCELLATION, leaveLogDto1.getDuration().negate(), leaveLogDto1.getFkLeaveStockId());
                                }
                            }
                        }
                    }
//                }

            } else if (ProjectKeyEnum.LEAVE_VACATION.key.equals(leaveTypeKey)) {
                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.UNPAID_LEAVE_DEDUCTION, days.negate(), null);
            } else if (ProjectKeyEnum.ONLINE_WORK.key.equals(leaveTypeKey)) {
                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.ONLINE_WORK_ADD, days, null);
            } else if (ProjectKeyEnum.OUTSOURCING.key.equals(leaveTypeKey)) {
                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.OUTSOURCING_ADD, days, null);
            } else if (ProjectKeyEnum.EVECTION.key.equals(leaveTypeKey)) {
                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.EVECTION_ADD, days, null);
            } else if (ProjectKeyEnum.PREMARITAL_EXAMINATION.key.equals(leaveTypeKey)) {
                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.PREMARITAL_EXAMINATION_DEDUCTION, days.negate(), null);
            } else if (ProjectKeyEnum.MARRIAGE_VACATION.key.equals(leaveTypeKey)) {
                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.MARRIAGE_VACATION_DEDUCTION, days.negate(), null);
            } else if (ProjectKeyEnum.MATERNITY_CHECK_VACATION.key.equals(leaveTypeKey)) {
                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.MATERNITY_CHECK_VACATION_DEDUCTION, days.negate(), null);
            } else if (ProjectKeyEnum.MATERNITY_VACATION.key.equals(leaveTypeKey)) {
                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.MATERNITY_VACATION_DEDUCTION, days.negate(), null);
            } else if (ProjectKeyEnum.ESCORT_VACATION.key.equals(leaveTypeKey)) {
                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.ESCORT_VACATION_DEDUCTION, days.negate(), null);
            } else if (ProjectKeyEnum.FUNERAL_VACATION.key.equals(leaveTypeKey)) {
                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.FUNERAL_VACATION_DEDUCTION, days.negate(), null);
            } else if (ProjectKeyEnum.WORK_RELATED_VACATION.key.equals(leaveTypeKey)) {
                addLog(businessKey, workFlowHelper, staffVo, starterId, leaveTypeKey, ProjectKeyEnum.WORK_RELATED_VACATION_DEDUCTION, days.negate(), null);
            }
            return;
        }
        //如果当前任务节点是调整申请，根据操作结果修改状态
        List<Task> taskList = taskService.createTaskQuery().processDefinitionKey(tableName).taskDefinitionKey(execution.getCurrentActivityId()).list();
        if (taskList != null && !taskList.isEmpty()) {
            if ("调整申请".equals(taskList.get(0).getName())) {
                if ("1".equals(execution.getVariable(PARAM))) {
                    //值是1 表示重新申请 状态改为2
                    Result<Boolean> changeStatusResult = feginOfficeService.changeStatus(2, tableName, Long.valueOf(businessKey));
                    if (!changeStatusResult.isSuccess()) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("form_status_update_fail"));
                    }
                } else if ("0".equals(execution.getVariable(PARAM))) {
                    //值是0 表示放弃申请 状态改为4
                    Result<Boolean> changeStatusResult = feginOfficeService.changeStatus(4, tableName, Long.valueOf(businessKey));
                    if (!changeStatusResult.isSuccess()) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("form_status_update_fail"));
                    }
                }
            }
        } else {
            //只要进入这里 说明是审批拒绝  修改状态为3
            Result<Boolean> changeStatusResult = feginOfficeService.changeStatus(3, tableName, Long.valueOf(businessKey));
            if (!changeStatusResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("form_status_update_fail"));
            }
        }

        if (GeneralTool.isNotEmpty(staffIdsByResourceKey)) {
            for (Long aLong : staffIdsByResourceKey) {
                CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(aLong), cache_key);
            }
        }

    }

    private List<LeaveStockVo> getLeaveStockDtos(IWorkFlowHelper workFlowHelper, Long starterId, LeaveApplicationForm leaveApplicationForm, String leaveTypeKey) {
        LeaveStockDto leaveStockDto = new LeaveStockDto();
        leaveStockDto.setFkStaffIds(Lists.newArrayList(starterId));
        leaveStockDto.setLeaveTypeKey(leaveTypeKey);
        List<LeaveStockVo> leaveStockVos = workFlowHelper.getLeaveStockDtos(leaveStockDto);
        List<LeaveStockVo> stock = leaveStockVos.stream().filter(leaveStockVo -> leaveStockVo.getEffectiveDeadline() != null
                && leaveStockVo.getEffectiveDeadline().compareTo(leaveApplicationForm.getStartTime()) >= 0
                && leaveStockVo.getEffectiveDeadline().compareTo(leaveApplicationForm.getEndTime()) >= 0).collect(Collectors.toList());
        List<LeaveStockVo> longTimeleaveStockVos = leaveStockVos.stream().filter(leaveStockVo -> leaveStockVo.getEffectiveDeadline() == null).collect(Collectors.toList());
        if (GeneralTool.isEmpty(stock)) {
            stock = new ArrayList<>();
        }
        stock.addAll(longTimeleaveStockVos);
        return stock;
    }

    private List<LeaveStockVo> getEfficientLeaveStockDtos(IWorkFlowHelper workFlowHelper, Long starterId, LeaveApplicationForm leaveApplicationForm, String leaveTypeKey) {
        LeaveStockDto leaveStockDto = new LeaveStockDto();
        leaveStockDto.setFkStaffIds(Lists.newArrayList(starterId));
        leaveStockDto.setLeaveTypeKey(leaveTypeKey);
        List<LeaveStockVo> leaveStockVos = workFlowHelper.getEfficientLeaveStockDtos(leaveStockDto);
        List<LeaveStockVo> stock = leaveStockVos.stream().filter(leaveStockVo -> leaveStockVo.getEffectiveDeadline() != null
                && leaveStockVo.getEffectiveDeadline().compareTo(leaveApplicationForm.getStartTime()) >= 0
                && leaveStockVo.getEffectiveDeadline().compareTo(leaveApplicationForm.getEndTime()) >= 0).collect(Collectors.toList());
        List<LeaveStockVo> longTimeleaveStockVos = leaveStockVos.stream().filter(leaveStockVo -> leaveStockVo.getEffectiveDeadline() == null).collect(Collectors.toList());
        if (GeneralTool.isEmpty(stock)) {
            stock = new ArrayList<>();
        }
        stock.addAll(longTimeleaveStockVos);
        return stock;
    }

    private void addLog(String businessKey, IWorkFlowHelper workFlowHelper, StaffVo staffVo, Long starterId, String leaveTypeKey, ProjectKeyEnum unpaidLeaveDeduction, BigDecimal negate, Long stockId) {
        LeaveLogDto leaveLogDto = new LeaveLogDto();
        leaveLogDto.setFkCompanyId(staffVo.getFkCompanyId());
        leaveLogDto.setFkStaffId(starterId);
        leaveLogDto.setLeaveTypeKey(leaveTypeKey);
        leaveLogDto.setOptTypeKey(unpaidLeaveDeduction.key);
        leaveLogDto.setDuration(negate);
        leaveLogDto.setFkLeaveApplicationFormId(Long.valueOf(businessKey));
        if (GeneralTool.isNotEmpty(stockId)) {
            leaveLogDto.setFkLeaveStockId(stockId);
        }
        leaveLogDto.setGmtCreate(new Date());
        leaveLogDto.setGmtCreateUser("[system]");
        workFlowHelper.addSystemLeaveLog(leaveLogDto);
    }
}