package com.get.workflowcenter.listener;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;

/**
 * 工作流执行监听器  查询部门属性
 *
 * @author: Hardy
 * @create: 2021/7/1 14:45
 * @verison: 1.0
 * @description:
 */
public class DepartmentListener implements ExecutionListener {

    @Override
    public void notify(DelegateExecution execution) {
        //Map<String, Object> map = new HashMap<>();
        IPermissionCenterClient permissionCenterClient = SpringUtil.getBean(IPermissionCenterClient.class);
//        Long fkDepartmentId = StaffContext.getStaff().getFkDepartmentId();
        Long fkDepartmentId = SecureUtil.getFkDepartmentId();
        ProjectKeyEnum[] marketingDepartmentNumGea = ProjectKeyEnum.MARKETING_DEPARTMENT_NUM_GEA;
        boolean flag = false;
        for (ProjectKeyEnum projectKeyEnum : marketingDepartmentNumGea) {
//            Long departmentId = permissionCenterClient.getDepartmentIdByNum(projectKeyEnum.key);
            Result<Long> result = permissionCenterClient.getDepartmentIdByNum(projectKeyEnum.key);
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                        institutionProviderDto.setAssignee(result.getData());
//                newMpayDto.setAssignee(result.getData());
                Long departmentId = result.getData();
                if (fkDepartmentId.equals(departmentId)) {
                    flag = true;
                    break;
                } else {
                    flag = false;
                }
            }

        }
        //是否为市场部员工
        if (flag) {
            execution.setVariable("isMarketing", 1);
        } else {
            execution.setVariable("isMarketing", 0);
        }

    }
}
