package com.get.workflowcenter.service;

import com.get.common.result.Page;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.workflowcenter.vo.*;
import com.get.workflowcenter.vo.ActReProcdefVo;
import com.get.workflowcenter.dto.ActHiProcinstDto;
import com.get.workflowcenter.dto.ActReProcdefDto;
import com.get.workflowcenter.dto.ActRuExecutionDto;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.engine.history.HistoricActivityInstance;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/9/29
 * @TIME: 12:43
 * @Description:
 **/
public interface IWorkFlowService {

    /**
     * @return byte[]
     * @Description :高亮正在运行的流程图片
     * @Param [processInstanceId]
     * <AUTHOR>
     */
    byte[] getProcessImage(String processInstanceId, String key);


    /**
     * @return java.util.List<java.lang.String>
     * @Description :高亮正在运行的流程图片
     * @Param [bpmnModel, historicActivityInstances]
     * <AUTHOR>
     */
    List<String> getHighLightedFlows(BpmnModel bpmnModel, List<HistoricActivityInstance> historicActivityInstances);


    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActReProcdefDto>
     * @Description :流程管理或者发起事务中的所有流程
     * @Param [procdefVo, page]
     * <AUTHOR>
     */
    List<ActReProcdefVo> getProcefList(ActReProcdefDto procdefVo, Page page);

    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActRuExecutionDto>
     * @Description :流程跟踪中的所有正在执行流程
     * @Param [actRuExecutionDto, page]
     * <AUTHOR>
     */
    List<ActRuExecutionVo> getExecutionList(ActRuExecutionDto actRuExecutionDto, Page page);


    /**
     * @return void
     * @Description :删除流程定义
     * @Param [id]
     * <AUTHOR>
     */
    void deleteDeployment(String id);


    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActRuTaskVo>
     * @Description :待办事务的查询
     * @Param [procdefVo, page]
     * <AUTHOR>
     */
    List<ActRuTaskVo> getToDoSelect(ActReProcdefDto procdefVo, Page page);

    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActRuTaskVo>
     * @Description :待签事务的查询
     * @Param [procdefVo, page]
     * <AUTHOR>
     */
    List<ActRuTaskVo> getSignedSelect(ActReProcdefDto procdefVo, Page page);

    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActRuTaskVo>
     * @Description :待办事务中的获取全部和搜索
     * @Param [procdefVo, page]
     * <AUTHOR>
     */
    //   List<ActRuTaskVo> getAllWaitTask(ActReProcdefDto procdefVo, Page page) ;

    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActHiActinstDto>
     * @Description :办结事务查询和搜索
     * @Param [procdefVo, page]
     * <AUTHOR>
     */
    List<ActHiProcinstVo> getCompleteTask(ActHiProcinstDto hiProcinstVo, Page page);


    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActHiActinstDto>
     * @Description :已发事务查询和搜索
     * @Param [procdefVo, page]
     * <AUTHOR>
     */
    List<ActHiProcinstVo> getRunningTask(ActHiProcinstDto hiProcinstVo, Page page);

    /**
     * @return java.lang.Boolean
     * @Description :保存批注和流程推进
     * @Param [id, taskId, comment, outcome]
     * <AUTHOR>
     */
    void saveCommentAndComplete(String taskId, String comment, String outcome);

    Map<Long, Integer> getFromIdsByStaffId(Long staffId, String key);

    /**
     * @Description :根据业务id查找正在执行的任务id
     * @Param [expenseClaimFormIds, procdefKey]
     * <AUTHOR>
     */
    Map<Long, ActRuTaskVo> getActRuTaskDtosByBusinessKey(List<Long> businessIds, String procdefKey);

    /**
     * @return java.util.List<org.activiti.engine.task.Comment>
     * @Description :
     * @Param [businessKey]
     * <AUTHOR>
     */
    HiCommentVo viewHisCommentByBusinessKey(Long businessKey, String procdefKey);


    /**
     * @Description: 根据流程历史实例以及名称获取变量值
     * @Author: Jerry
     * @Date:11:47 2021/11/9
     */
    Object getVariableByHisInstanceAndName(String processInstanceId, String name);

    String getStartUserId(String processInstId);


    void getStatusToDoSingle(String status, String procInstId);

    Boolean StopExecution(String processInstId, String msg, String procdefKey, String businessKey);

    /**
     * @return void
     * @Description :开启流程
     * @Param [businessKey, procdefKey, companyId, map]
     * <AUTHOR>
     */
    Boolean startProcess(String businessKey, String procdefKey, String companyId, Map<String, Object> map);

    /**
     * @return Boolean
     * @Description :修改节点单独提交(重新申请或放弃)
     * @Param [businessKey, procdefKey, companyId, map]
     * <AUTHOR>
     */
    Boolean getUserSubmit(String taskId, String status);

    /**
     * 获取审批按钮状态
     *
     * @param businessKey
     * @param procdefKey
     * @return
     */
    HiCommentFeignVo getHiComment(Long businessKey, String procdefKey);

    /**
     * 获取工休单相关
     *
     * @param fkTableId
     * @return
     */
    ActHiTaskInstVo getActHiTaskInstDtoAndLeaveFormMessage(Long fkTableId);


    /**
     * 获取businessKey下的ActHiTaskInstDtos
     *
     * @param businessKey
     * @return
     */
    List<ActHiTaskInstVo> getActHiTaskInstDtosByBusinessKey(String businessKey, String procdefKey);

    /**
     * 获取待办的数据
     *
     * @param staffId
     * @param key
     * @return
     */
    Set<String> getToDoByStaffIdAndTableName(Long staffId, String key);

    /**
     * 获取待签的数据
     *
     * @param staffId
     * @param key
     * @return
     */
    Set<String> getToSignByStaffIdAndTableName(Long staffId, String key);

    /**
     * 发送提醒
     *
     * @param leaveApplicationForm
     * @param title
     */
    void doSendRemind(LeaveApplicationForm leaveApplicationForm, String title);

    /**
     * 发送邮件提醒
     *
     * @param leaveApplicationForm
     * @param title
     */
    void doSendEmail(LeaveApplicationForm leaveApplicationForm, String title,Long superiorId);

    /**
     * 获取申请方案信息
     * @param fkTableId
     * @return
     */
    ActHiTaskInstVo getActHiTaskInstDtoAndStudentOffer(Long fkTableId);

    /**
     *
     * @param id
     * @return
     */
    Boolean stopStudentOfferWorkFlow(Long id);

    /**
     * 获取流程申请人
     * @param id
     * @param procdefKey
     * @return
     */
    Long getStartUserIdByIdAndProcdefKey(Long id, String procdefKey);

    /**
     * 获取审批人id
     * @param businessKey
     * @return
     */
    Long getAssigneeStaffId(String businessKey);


    /**
     * 审批状态下拉框
     * @return
     */
    List<Map<String, Object>> approvalStatusSelected();

    /**
     * 申请表单流程发起
     * @param tableId
     * @param tableName
     * @param companyId
     */
    void applicationFormStartProcess(Long tableId, String tableName, String companyId);
}
