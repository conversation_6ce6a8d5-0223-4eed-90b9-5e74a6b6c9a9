package com.get.workflowcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.workflowcenter.vo.ActReModelVo;
import com.get.workflowcenter.entity.ActReModel;
import com.get.workflowcenter.mapper.ActReModelMapper;
import com.get.workflowcenter.service.IModelService;
import com.get.workflowcenter.dto.ActReModelDto;
import com.get.workflowcenter.dto.ActReProcdefDto;
import org.activiti.bpmn.converter.BpmnXMLConverter;
import org.activiti.bpmn.exceptions.XMLException;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.editor.constants.ModelDataJsonConstants;
import org.activiti.editor.language.json.converter.BpmnJsonConverter;
import org.activiti.engine.IdentityService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.repository.Model;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import javax.xml.stream.XMLInputFactory;
import javax.xml.stream.XMLStreamReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

import static org.activiti.editor.constants.ModelDataJsonConstants.MODEL_DESCRIPTION;
import static org.activiti.editor.constants.ModelDataJsonConstants.MODEL_NAME;

/**
 * @author: Sea
 * @create: 2020/11/20 16:29
 * @verison: 1.0
 * @description:
 */
@Service
public class ModelServiceImpl extends ServiceImpl<ActReModelMapper, ActReModel> implements IModelService {
    private static final Logger LOGGER = LoggerFactory.getLogger(ModelServiceImpl.class);
    @Resource
    private RepositoryService repositoryService;
    @Resource
    private ObjectMapper objectMapper;
    @Resource
    private RuntimeService runtimeService;
    @Resource
    private IdentityService identityService;
    @Resource
    private ActReModelMapper actReModelMapper;

    @Override
    public String create(String name, String key) {
        LOGGER.info("创建模型入参name：{},key:{}", name, key);
        //初始化一个空模型
        Model model = repositoryService.newModel();
        ObjectNode modelNode = objectMapper.createObjectNode();
        modelNode.put(MODEL_NAME, name);
        modelNode.put(MODEL_DESCRIPTION, "");
        modelNode.put(ModelDataJsonConstants.MODEL_REVISION, 1);
        model.setName(name);
        model.setKey(key);
        model.setMetaInfo(modelNode.toString());
        //通过RepositoryService的saveModel方法将模型的元数据存入数据库的ACT_RE_MODEL表
        repositoryService.saveModel(model);
        createObjectNode(model.getId());
        LOGGER.info("创建模型结束，返回模型ID：{}", model.getId());
        return model.getId();
    }

    @Override
    public String uploadModel(MultipartFile modelFile) {
        if (GeneralTool.isEmpty(modelFile)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        InputStreamReader in = null;
        Model modelData = repositoryService.newModel();
        GetServiceException GetServiceException = new GetServiceException("");
        try {
            try {
                String fileName = modelFile.getOriginalFilename();
                if (fileName.endsWith(".bpmn20.xml") || fileName.endsWith(".bpmn")) {
                    in = new InputStreamReader(new ByteArrayInputStream(modelFile.getBytes()), "UTF-8");
                    BpmnXMLConverter converter = new BpmnXMLConverter();
                    XMLInputFactory factory = XMLInputFactory.newInstance();
                    XMLStreamReader reader = factory.createXMLStreamReader(in);
                    //将xml文件转换成BpmnModel
                    BpmnModel bpmnModel = converter.convertToBpmnModel(reader);
                    if (bpmnModel.getMainProcess() == null || bpmnModel.getMainProcess().getId() == null) {
                        throw new GetServiceException(LocaleMessageUtils.getMessage("model_error"));
                    } else {
                        if (bpmnModel.getLocationMap().isEmpty()) {
                            throw new GetServiceException(LocaleMessageUtils.getMessage("model_error"));
                        } else {
                            String processName;
                            if (StringUtils.isNotEmpty(bpmnModel.getMainProcess().getName())) {
                                processName = bpmnModel.getMainProcess().getName();
                            } else {
                                processName = bpmnModel.getMainProcess().getId();
                            }
                            ObjectNode modelObjectNode = new ObjectMapper().createObjectNode();
                            modelObjectNode.put(MODEL_NAME, processName);
                            modelObjectNode.put(ModelDataJsonConstants.MODEL_REVISION, 1);
                            modelData.setMetaInfo(modelObjectNode.toString());
                            modelData.setName(processName);
                            repositoryService.saveModel(modelData);

                            BpmnJsonConverter jsonConverter = new BpmnJsonConverter();
                            ObjectNode editorNode = jsonConverter.convertToJson(bpmnModel);
                            repositoryService.addModelEditorSource(modelData.getId(), editorNode.toString().getBytes("utf-8"));
                        }
                    }
                } else {
                    throw new GetServiceException(LocaleMessageUtils.getMessage("file_type_error"));
                }
            } catch (GetServiceException e) {
                throw GetServiceException;
            } catch (Exception e) {
                e.printStackTrace();
                throw new GetServiceException(LocaleMessageUtils.getMessage("import_model_fail"));
            }
        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    LOGGER.info("关闭io流异常 ;{}", e);
                }
            }
        }
        return modelData.getId();
    }

    @Override
    public void delete(String modelId) {
        if (GeneralTool.isEmpty(modelId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        Model model = repositoryService.createModelQuery().modelId(modelId).singleResult();
        if (model == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
        }
        repositoryService.deleteModel(modelId);
        LOGGER.info("{}删除成功", modelId);
    }

    @Override
    public List<ActReModelVo> getModels(ActReModelDto actReModelDto, Page page) {
//        Example example = new Example(ActReModel.class);
//        Example.Criteria criteria = example.createCriteria();
//
//        if (GeneralTool.isNotEmpty(actReModelDto)) {
//            if (GeneralTool.isNotEmpty(actReModelDto.getKey())) {
//                criteria.andLike("key", "%" + actReModelDto.getKey() + "%");
//            }
//            if (GeneralTool.isNotEmpty(actReModelDto.getName())) {
//                criteria.andLike("name", "%" + actReModelDto.getName() + "%");
//            }
//        }
//        example.orderBy("lastUpdateTime").desc();
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<ActReModel> actReModels = actReModelMapper.selectByExample(example);
//        page.restPage(actReModels);

        LambdaQueryWrapper<ActReModel> wrapper = new LambdaQueryWrapper();
        if (GeneralTool.isNotEmpty(actReModelDto)) {
            if (GeneralTool.isNotEmpty(actReModelDto.getKey())) {
                wrapper.like(ActReModel::getKey, actReModelDto.getKey());
            }
            if (GeneralTool.isNotEmpty(actReModelDto.getName())) {
                wrapper.like(ActReModel::getName, actReModelDto.getName());
            }
        }
        wrapper.orderByDesc(ActReModel::getLastUpdateTime);
        IPage<ActReModel> pages = this.page(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), wrapper);
        List<ActReModel> actReModels = pages.getRecords();

        return actReModels.stream().map(model -> BeanCopyUtils.objClone(model, ActReModelVo::new)).collect(Collectors.toList());
    }

    @Override
    public void export(String modelId, HttpServletResponse response) {
        System.out.println("response = " + response);
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/json; charset=utf-8");
        String header = response.getHeader("code");
        int status = response.getStatus();
        Collection<String> headerNames = response.getHeaderNames();
        try {
            Model modelData = repositoryService.getModel(modelId);
            BpmnJsonConverter jsonConverter = new BpmnJsonConverter();
            //获取节点信息
            byte[] arg0 = repositoryService.getModelEditorSource(modelData.getId());
            JsonNode editorNode = new ObjectMapper().readTree(arg0);
            //将节点信息转换为xml
            BpmnModel bpmnModel = jsonConverter.convertToBpmnModel(editorNode);
            BpmnXMLConverter xmlConverter = new BpmnXMLConverter();
            byte[] bpmnBytes = xmlConverter.convertToXML(bpmnModel);

            ByteArrayInputStream in = new ByteArrayInputStream(bpmnBytes);
//                String filename = bpmnModel.getMainProcess().getId() + ".bpmn20.xml";
            String filename = modelData.getName() + ".bpmn20.xml";
            response.setHeader("Content-Disposition", "attachment; filename=" + java.net.URLEncoder.encode(filename, "UTF-8"));
            ServletOutputStream outputStream = response.getOutputStream();
            IOUtils.copy(in, outputStream);
            response.flushBuffer();

            outputStream.close();
            in.close();

        } catch (Exception e) {
            LOGGER.info("下载失败 打印异常：{}", e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("downLoad_bpmn_fail"));
        }
    }

    @Override
    public void deployModel(ActReProcdefDto actReProcdefDto) {
        if (GeneralTool.isEmpty(actReProcdefDto) || GeneralTool.isEmpty(actReProcdefDto.getModelId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        String modelId = actReProcdefDto.getModelId();
        Model modelData = repositoryService.getModel(modelId);
        byte[] bytes = repositoryService.getModelEditorSource(modelData.getId());
        if (repositoryService.getModelEditorSourceExtra(modelData.getId()) == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("model_null_publish_fail"));
        }
        try {
            JsonNode modelNode = new ObjectMapper().readTree(bytes);
            BpmnModel model = new BpmnJsonConverter().convertToBpmnModel(modelNode);
            //所属租户
            String[] tenantIds = actReProcdefDto.getTenantId().split(",");
            for (String tenantId : tenantIds) {
                Deployment deployment = repositoryService.createDeployment()
                        .name(modelData.getName())
                        .addBpmnModel(modelData.getKey() + ".bpmn20.xml", model)
                        .tenantId(tenantId)
                        .category(actReProcdefDto.getCategory())
                        .deploy();
                modelData.setDeploymentId(deployment.getId());
                modelData.setCategory(actReProcdefDto.getCategory());
                modelData.setTenantId(actReProcdefDto.getTenantId());
                repositoryService.saveModel(modelData);
            }
        } catch (XMLException e) {
            //当页面保存空的流程图时，会报这个错误
            throw new GetServiceException(LocaleMessageUtils.getMessage("model_null_publish_fail"));
        } catch (Exception e) {
            LOGGER.info("部署模型失败 打印异常：{}", e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("deploy_model_fail"));
        }
    }

    @Override
    public void deployProcess(MultipartFile file, String tenantId, String category) {
        //bpmn上传文件名
        String fileName = file.getOriginalFilename();
        if (GeneralTool.isEmpty(fileName)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        String substring = fileName.substring(0, fileName.indexOf("."));
        String suffix1 = ".bpmn20.xml";
        String suffix2 = ".bpmn";
        if (fileName.endsWith(suffix1) || fileName.endsWith(suffix2)) {
            //所属租户
            String[] tenantIds = tenantId.split(",");
            try {
                for (String id : tenantIds) {
                    Deployment deployment = repositoryService.createDeployment()
                            .addBytes(fileName, file.getBytes())
                            .tenantId(id)
                            .category(category)
                            .name(substring)
                            .deploy();
                    //部署id
                    System.out.println("部署id：" + deployment.getId());
                    System.out.println("部署时间：" + deployment.getDeploymentTime());
                }
            } catch (Exception e) {
                LOGGER.info("部署流程失败 打印异常：{}", e);
                throw new GetServiceException(LocaleMessageUtils.getMessage("deploy_process_fail"));
            }
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_type_error"));
        }
    }

    /**
     * @return void
     * @Description :创建模型完善ModelEditorSource
     * @Param [modelId]
     * <AUTHOR>
     */
    private void createObjectNode(String modelId) {
        LOGGER.info("创建模型完善ModelEditorSource入参模型ID：{}", modelId);
        ObjectNode editorNode = objectMapper.createObjectNode();
        editorNode.put("id", "canvas");
        editorNode.put("resourceId", "canvas");
        ObjectNode stencilSetNode = objectMapper.createObjectNode();
        stencilSetNode.put("namespace", "http://b3mn.org/stencilset/bpmn2.0#");
        editorNode.put("stencilset", stencilSetNode);
        try {
            //通过RepositoryService的addModelEditorSource方法将模型JSON数据UTF8字符串存入数据库的ACT_GE_BYTEARRAY表
            repositoryService.addModelEditorSource(modelId, editorNode.toString().getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            LOGGER.info("创建模型时完善ModelEditorSource服务异常：{}", e);
            throw new GetServiceException(LocaleMessageUtils.getMessage("create_model_fail"));
        }
        LOGGER.info("创建模型完善ModelEditorSource结束");
    }
}
