package com.get.workflowcenter.listener;

import com.get.core.tool.utils.SpringUtil;
import com.get.financecenter.feign.IFinanceCenterClient;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.task.Task;

import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/8 11:57
 * @verison: 1.0
 * @description: 财务中心根据流程节点连线判断修改业务表单状态监听器
 */
public class FinanceChangeStatusListener implements ExecutionListener {
    private static final String PARAM = "sequenceFlowsStatus";

    @Override
    public void notify(DelegateExecution execution) {
        IFinanceCenterClient financeCenterClient = SpringUtil.getBean(IFinanceCenterClient.class);
        TaskService taskService = SpringUtil.getBean(TaskService.class);
        RepositoryService repositoryService = SpringUtil.getBean(RepositoryService.class);
        //通过流程定义对象查找出KEY_  就是表名
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(execution.getProcessDefinitionId()).singleResult();
        String tableName = processDefinition.getKey();
        String businessKey = execution.getProcessInstanceBusinessKey();
        //结束节点 start状态设置已结束
        if ("start".equals(execution.getEventName())) {
            financeCenterClient.changeStatus(1, tableName, Long.valueOf(businessKey));
            return;
        }
        //如果当前任务节点是调整申请，根据操作结果修改状态
        List<Task> taskList = taskService.createTaskQuery().processDefinitionKey(tableName).taskDefinitionKey(execution.getCurrentActivityId()).list();
        if (taskList != null && !taskList.isEmpty()) {
            if ("调整申请".equals(taskList.get(0).getName())) {
                if ("1".equals(execution.getVariable(PARAM))) {
                    //值是1 表示重新申请 状态改为2
                    financeCenterClient.changeStatus(2, tableName, Long.valueOf(businessKey));
                } else if ("0".equals(execution.getVariable(PARAM))) {
                    //值是0 表示放弃申请 状态改为4
                    financeCenterClient.changeStatus(4, tableName, Long.valueOf(businessKey));
                }
            }
        } else {
            //只要进入这里 说明是审批拒绝  修改状态为3
            financeCenterClient.changeStatus(3, tableName, Long.valueOf(businessKey));
        }

    }
}