package com.get.workflowcenter.listener;

import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.salecenter.vo.StudentOfferVo;
import com.get.salecenter.feign.ISaleCenterClient;
import org.activiti.engine.HistoryService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.history.HistoricActivityInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * Created by Jerry.
 * Time: 12:29
 * Date: 2021/11/4
 * Description:学生申请方案监听
 */
public class StudentOfferListener implements Serializable, ExecutionListener {
    private Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    public void notify(DelegateExecution delegateExecution) {
        System.out.println("进入学生申请方案监听------------------------");
        String processInstanceBusinessKey = delegateExecution.getProcessInstanceBusinessKey();
        String processInstanceId = delegateExecution.getProcessInstanceId();
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        ISaleCenterClient saleCenterClient = SpringUtil.getBean(ISaleCenterClient.class);
        UtilService utilService = SpringUtil.getBean(UtilService.class);
        if ("end".equals(delegateExecution.getEventName())) {
            List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).orderByHistoricActivityInstanceStartTime().desc().list();
            StudentOfferVo studentOfferDetail = null;
            Result<StudentOfferVo> studentOfferDtoResult = saleCenterClient.getStudentOfferDetail(Long.valueOf(processInstanceBusinessKey));
            if (studentOfferDtoResult.isSuccess() && GeneralTool.isNotEmpty(studentOfferDtoResult.getData())) {
                studentOfferDetail = studentOfferDtoResult.getData();
            }else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("student_offer_not_found"));
            }
            for (HistoricActivityInstance hti : list) {
                if (hti.getActivityName() != null && "调整申请".equals(hti.getActivityName())) {
                    studentOfferDetail.setStatusWorkflow(4);
                    break;
                } else {
                    studentOfferDetail.setStatusWorkflow(1);
                    //0代表申请终止按钮  1代表申请结案按钮
                    Object buttonType = delegateExecution.getVariable("buttonType");
                    if ("0".equals(buttonType)) {
//                        studentOfferDetail.setStatus(2);
                        //更改为作废
                        studentOfferDetail.setStatus(0);
                        studentOfferDetail.setInvalidateOfferItem(true);
                    } else if ("1".equals(buttonType)) {
                        studentOfferDetail.setStatus(3);
                    }
                    break;
                }
            }
            studentOfferDetail.setGmtModified(new Date());
            studentOfferDetail.setGmtModifiedUser("admin");
            Result<Boolean> updateResult = saleCenterClient.updateStudentOffer(studentOfferDetail);
            if (!updateResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("student_offer_status_update_fail"));
            }
        } else {
            Object sequenceFlowsStatus = delegateExecution.getVariable("sequenceFlowsStatus");
            StudentOfferVo studentOfferDetail = null;
            Result<StudentOfferVo> studentOfferDtoResult = saleCenterClient.getStudentOfferDetail(Long.valueOf(processInstanceBusinessKey));
            if (studentOfferDtoResult.isSuccess() && GeneralTool.isNotEmpty(studentOfferDtoResult.getData())) {
                studentOfferDetail = studentOfferDtoResult.getData();
            }else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("student_offer_not_found"));
            }
            if ("0".equals(sequenceFlowsStatus)) {
                studentOfferDetail.setStatusWorkflow(3);
            } else {
                studentOfferDetail.setStatusWorkflow(2);
            }
            studentOfferDetail.setGmtModified(new Date());
            studentOfferDetail.setGmtModifiedUser("admin");
            Result<Boolean> updateResult = saleCenterClient.updateStudentOffer(studentOfferDetail);
            if (!updateResult.isSuccess()) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("form_status_update_fail"));
            }
        }
    }
}
