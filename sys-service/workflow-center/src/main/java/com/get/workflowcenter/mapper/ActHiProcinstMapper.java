package com.get.workflowcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.get.workflowcenter.vo.ActHiProcinstVo;
import com.get.workflowcenter.entity.ActHiProcinst;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ActHiProcinstMapper extends BaseMapper<ActHiProcinst> {


    int insertSelective(ActHiProcinst record);

    List<ActHiProcinstVo> getCompleteTaskList(IPage<ActHiProcinstVo> page, @Param("category") String category, @Param("name") String name, @Param("staffIdByNames") List<Long> staffIdByNames);

    List<ActHiProcinstVo> getCompleteTaskListByNormalUser(IPage<ActHiProcinstVo> page,
                                                          @Param("category") String category,
                                                          @Param("name") String name,
                                                          @Param("username") String username,
                                                          @Param("staffIdByNames") List<Long> staffIdByNames);


    List<ActHiProcinstVo> getRunningTaskList(IPage<ActHiProcinstVo> page,
                                             @Param("category") String category,
                                             @Param("name") String name);

    List<ActHiProcinstVo> getRunningTaskListByNormalUser(IPage<ActHiProcinstVo> page,
                                                         @Param("category") String category, @Param("name") String name, @Param("username") String username);

}