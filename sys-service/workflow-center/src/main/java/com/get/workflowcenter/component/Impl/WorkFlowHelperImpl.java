package com.get.workflowcenter.component.Impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.FormInformationVo;
import com.get.officecenter.dto.LeaveLogDto;
import com.get.officecenter.dto.LeaveStockDto;
import com.get.officecenter.feign.IOfficeCenterClient;
import com.get.officecenter.vo.LeaveLogVo;
import com.get.officecenter.vo.LeaveStockVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.workflowcenter.component.IWorkFlowHelper;
import com.get.workflowcenter.service.IWorkFlowService;
import com.google.common.collect.Lists;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.history.HistoricIdentityLink;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import static com.get.core.tool.utils.DateUtil.now;

/**
 * @author: Hardy
 * @create: 2021/11/18 15:43
 * @verison: 1.0
 * @description:
 */
@Component
public class WorkFlowHelperImpl implements IWorkFlowHelper {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private IReminderCenterClient reminderCenterClient;

    @Resource
    private IPermissionCenterClient permissionCenterClient;

    @Resource
    private IOfficeCenterClient officeCenterClient;

    @Resource
    private RuntimeService runtimeService;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private IWorkFlowService workFlowService;

    @Override
    public void sendMessage(StaffVo staffVo, List<String> staffIdList, ProcessDefinition processDefinition, String status, DelegateTask task, String dbName, String remindKey) {
        StringBuilder sb = new StringBuilder();
        Object buttonType = task.getVariable("buttonType");
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(staffVo.getFkCompanyId());
        //TODO WORKFLOW_STUDENT_OFFER
        if (GeneralTool.isNotEmpty(buttonType)) {
            if ("0".equals(buttonType)) {
                if(!versionValue2.equals("en")){
                    sb.append("Termination process for student application plan");
                }else {
                    sb.append("学生申请方案终止流程");
                }

            } else if ("1".equals(buttonType)) {
                if(!versionValue2.equals("en")){
                    sb.append("Closing process of student application plan");
                }else {
                    sb.append("学生申请方案结案流程");
                }

            }
        } else {

            sb.append(processDefinition.getName());
        }
        if (GeneralTool.isNotEmpty(staffVo)) {
            sb.append("-");
            if (GeneralTool.isNotEmpty(staffVo.getName())) {
                sb.append(staffVo.getName());
            }
            if (GeneralTool.isNotEmpty(staffVo.getNameEn())) {
                sb.append("（").append(staffVo.getNameEn()).append("）");
            }
        }
        if(!processDefinition.getKey().contains(TableEnum.SALE_STUDENT_OFFER.key)){
            sb.append("-").append(status);
        }

        //发送提醒消息
        String domainName = "";
//        Properties props = System.getProperties();
//        String profile = props.getProperty("spring.profiles.active");
//        if (GeneralTool.isNotEmpty(profile)) {
//            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)|| profile.equals(AppConstant.TW_CODE)) {
//                ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
//                if (GeneralTool.isNotEmpty(configDto)){
//                    String configJson = configDto.getValue2();
//                    JSONObject configJsonObject = JSON.parseObject(configJson);
//                    if (staffVo.getFkCompanyId().equals(3L)){
//                        domainName = configJsonObject.getString("IAE");
//                    }else {
//                        domainName = configJsonObject.getString("OTHER");
//                    }
//                }
//            }else {
//                Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
//                if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
//                    domainName = domainNameResult.getData();
//                }
//            }
//        }
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.FILE_SRC_PREFIX.key, 2).getData();
        String configValue2 = companyConfigMap.get(staffVo.getFkCompanyId());
        if (GeneralTool.isNotEmpty(configValue2)){
            domainName = configValue2;
        } else {
            Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
            if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
                domainName = domainNameResult.getData();
            }
        }

        String toSignPage = "";
        Result<String> toSignPageResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.REMINDER_TASK_LINK_WF_TAKE.key);
        if (toSignPageResult.isSuccess() && GeneralTool.isNotEmpty(toSignPageResult.getData())) {
            toSignPage = toSignPageResult.getData();
        }
        String toDoPage = "";
        Result<String> toDoPageResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.REMINDER_TASK_LINK_WF_APPROVE.key);
        if (toDoPageResult.isSuccess() && GeneralTool.isNotEmpty(toDoPageResult.getData())) {
            toDoPage = toDoPageResult.getData();
        }
        Map<String,String> param = new HashMap();
        if(GeneralTool.isNotEmpty(staffIdList)){
            param.put("staffIdList",staffIdList.toString());
        }else {
            param.put("staffIdList","");
        }

        if (GeneralTool.isNotEmpty(staffVo)) {
            param.put("staffId",staffVo.getId().toString());
        }else {
             param.put("staffId","");
        }

        EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
        //费用报销单邮件模版参数设置
        String businessKey = String.valueOf(task.getVariable("businessKey"));
        //转换为long类型
        long tableId = Long.parseLong(businessKey);
        if (processDefinition.getKey().contains(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key)) {
            setEmailAndTable( emailSenderQueue,param,TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key, status, tableId);
            param.put("tableName",TableEnum.FINANCE_EXPENSE_CLAIM_FORM.value);
        }
        //借款申请单邮件模版参数设置
        else if (processDefinition.getKey().contains(TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key)) {
            setEmailAndTable( emailSenderQueue,param,TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key, status, tableId);
            param.put("tableName",TableEnum.FINANCE_PREPAY_APPLICATION_FORM.value);
        }
        //差旅报销单邮件模版参数设置
        else if (processDefinition.getKey().contains(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key)) {
            setEmailAndTable( emailSenderQueue,param,TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key, status, tableId);
            param.put("tableName",TableEnum.FINANCE_TRAVEL_CLAIM_FORM.value);
        }
        //支付申请单邮件模版参数设置
        else if (processDefinition.getKey().contains(TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key)) {
            setEmailAndTable( emailSenderQueue,param,TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key, status, tableId);
            param.put("tableName",TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.value);
        }
        else if (processDefinition.getKey().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
                ||processDefinition.getId().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)){
            emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey());
        }else {
            emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.NON_WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey());
        }
        if (GeneralTool.isNotEmpty(remindKey)){
            emailSenderQueue.setFkEmailTypeKey(remindKey);
        }else{
                if (processDefinition.getKey().contains(TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key)
                        ||processDefinition.getId().contains(TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key)
                        ||processDefinition.getId().contains(TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key)
                        ||processDefinition.getId().contains(TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key)
                ){
                    emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.APPLICATION_FORM_APPROVAL_NOTICE.getEmailTemplateKey());
                } else if (processDefinition.getKey().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
                        ||processDefinition.getId().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)){
                    emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey());
                    emailSenderQueue.setFkTableName(TableEnum.ACT_HI_TASKINST.key);
                }else if (processDefinition.getKey().contains(TableEnum.SALE_STUDENT_OFFER.key)){
                    emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.APPLN_TERM_INVAL_WORKFLOW_REMINDER.getEmailTemplateKey());
                    emailSenderQueue.setFkTableName(TableEnum.ACT_HI_TASKINST.key);
                }else {
                    emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.NON_WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey());
                    emailSenderQueue.setFkTableName(TableEnum.ACT_HI_TASKINST.key);
                }
            }

        String  map = null;
        ObjectMapper mapper = new ObjectMapper();
        try {
            map = mapper.writeValueAsString(param);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        emailSenderQueue.setEmailTitle(sb.toString());
        emailSenderQueue.setEmailParameter(map);
        emailSenderQueue.setFkDbName(dbName);
        emailSenderQueue.setFkTableId(Long.valueOf(task.getId()));
        emailSenderQueue.setOperationTime(now());
        List<EmailSenderQueue> emailSenderQueueList = Lists.newArrayList(emailSenderQueue);

        try {
            Result<Boolean> booleanResult = reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("发送提醒信息异常" + e.getMessage());
        }
        //List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
//        for (String s : staffIdList) {
//            RemindTaskDto remindTaskDto = new RemindTaskDto();
//            remindTaskDto.setFkTableName(TableEnum.ACT_HI_TASKINST.key);
//            remindTaskDto.setTaskRemark(" ");
//            remindTaskDto.setFkTableId(Long.valueOf(task.getId()));
//            remindTaskDto.setFkStaffId(Long.parseLong(s));
//            remindTaskDto.setStartTime(new Date());
//            //邮件方式发送
//            remindTaskDto.setRemindMethod("1");
//            //默认设置执行中
//            remindTaskDto.setStatus(1);
//            //默认背景颜色
//            if (GeneralTool.isNotEmpty(remindKey)){
//                remindTaskDto.setFkRemindEventTypeKey(remindKey);
//            }else{
//                if (processDefinition.getKey().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
//                        ||processDefinition.getId().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)){
//                    remindTaskDto.setFkRemindEventTypeKey("WORKFLOW_LEAVE_FORM");
//                }else if (processDefinition.getKey().contains(TableEnum.SALE_STUDENT_OFFER.key)){
//                    remindTaskDto.setFkRemindEventTypeKey("WORKFLOW_STUDENT_OFFER");
//                }else {
//                    remindTaskDto.setFkRemindEventTypeKey("WORKFLOW");
//                }
//            }
//            remindTaskDto.setTaskBgColor("#3788d8");
//            remindTaskDto.setTaskTitle(sb.toString());
//            remindTaskDto.setFkDbName(dbName);
//            //如果ids等于1 跳转详情页，大于1跳转待签页
//            if (staffIdList.size() > 1) {
//                //待签页
//                String taskId = task.getId();
//                String procInstId = task.getProcessInstanceId();
//                String businessKey = task.getExecution().getProcessInstanceBusinessKey();
//                String procdefKey = processDefinition.getKey();
//                if (processDefinition.getKey().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
//                        ||processDefinition.getId().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)){
////                    String url = domainName + "" +
////                            "/office_office-form-management_workbreak-detail/"+businessKey
////                            +"?taskId="+taskId
////                            +"&procInstId="+procInstId
////                            +"&procdefKey="+TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key;
////                    remindTaskDto.setTaskLink(url);
//                }else {
////                        String url = domainName + toSignPage;
////                        remindTaskDto.setTaskLink(url);
//                }
//            } else if (staffIdList.size() == 1) {
//                //待办详情
//                String taskId = task.getId();
//                String procInstId = task.getProcessInstanceId();
//                String businessKey = task.getExecution().getProcessInstanceBusinessKey();
//                String procdefKey = processDefinition.getKey();
//                if (processDefinition.getKey().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
//                        ||processDefinition.getId().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)){
////                    String url = domainName + "/office_office-form-management_workbreak-detail/"+businessKey
////                            +"?taskId="+taskId
////                            +"&procInstId="+procInstId
////                            +"&procdefKey="+TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key;
////                    remindTaskDto.setTaskLink(url);
//                }else {
//                    String url = domainName + toDoPage + taskId + "?procInstId=" + procInstId + "&businessKey="
//                            + businessKey + "&procdefKey=" + procdefKey;
//                    //remindTaskDto.setTaskLink(url);
//                }
//            }
//
//
//            remindTaskDtos.add(remindTaskDto);
//        }
//        try {
//            reminderCenterClient.batchAdd(remindTaskDtos);
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("发送提醒信息异常" + e.getMessage());
//        }
    }

    //设置财务四种申请单的邮件参数
    private void setEmailAndTable( EmailSenderQueue emailSenderQueue,Map<String,String> param,String tableKey, String status,Long tableId) {
        emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.APPLICATION_FORM_APPROVAL_NOTICE.getEmailTemplateKey());
        emailSenderQueue.setFkTableName(tableKey);
        Result<FormInformationVo> result = financeCenterClient.getFormInformationByFormId(tableKey, tableId);
        FormInformationVo formInformationVo = new FormInformationVo();
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            formInformationVo = result.getData();
        }
        //表名
        String tableName = null;
        //申请单编号
        String num = null;
        //申请人
        String applicant = null;
        //申请部门名称
        String departmentName = null;
        //申请日期
        Date createDate = null;
        //申请金额
        String amount = null;
        //申请摘要
        String summary = null;
        //币种
        String currencyTypeName = null;

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (GeneralTool.isNotEmpty(formInformationVo)) {
            num = formInformationVo.getNum();
            applicant = formInformationVo.getApplicant();
            departmentName = formInformationVo.getDepartmentName();
            createDate = formInformationVo.getCreateDate();
            amount = formInformationVo.getAmount();
            summary = formInformationVo.getSummary();
            currencyTypeName = formInformationVo.getCurrencyTypeName();
        }
        if(GeneralTool.isNotEmpty(status)){
            String s = "（" + status + "）";
            param.put("status", s);
        }else {
            param.put("status","");
        }
        param.put("num", num);
        param.put("applicant", applicant);
        param.put("department", departmentName);
        param.put("createDate", sdf.format(createDate));
        param.put("amount", amount);
        param.put("summary", summary);
        if (GeneralTool.isNotEmpty(currencyTypeName)) {
            param.put("currencyTypeName", currencyTypeName);
        }else {
            param.put("currencyTypeName", "");
        }

    }


    @Override
    public void sendMessage(StaffVo staffVo, List<String> staffIdList, ProcessDefinition processDefinition, String status, Task task, String dbName) {
        StringBuilder sb = new StringBuilder();
        sb.append(processDefinition.getName());
        if (GeneralTool.isNotEmpty(staffVo)) {
            sb.append("-");
            if (GeneralTool.isNotEmpty(staffVo.getName())) {
                sb.append(staffVo.getName());
            }
            if (GeneralTool.isNotEmpty(staffVo.getNameEn())) {
                sb.append("（").append(staffVo.getNameEn()).append("）");
            }
        }
        sb.append("-").append(status);

        //发送提醒消息
        String domainName = "";
//        Properties props = System.getProperties();
//        String profile = props.getProperty("spring.profiles.active");
//        if (GeneralTool.isNotEmpty(profile)) {
//            if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)|| profile.equals(AppConstant.TW_CODE)) {
//                ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.FILE_SRC_PREFIX.key).getData();
//                if (GeneralTool.isNotEmpty(configDto)){
//                    String configJson = configDto.getValue2();
//                    JSONObject configJsonObject = JSON.parseObject(configJson);
//                    if (staffVo.getFkCompanyId().equals(3L)){
//                        domainName = configJsonObject.getString("IAE");
//                    }else {
//                        domainName = configJsonObject.getString("OTHER");
//                    }
//                }
//            }else {
//                Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
//                if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
//                    domainName = domainNameResult.getData();
//                }
//            }
//        }
        //TODO WORKFLOW WORKFLOW_LEAVE_FORM
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.FILE_SRC_PREFIX.key, 2).getData();
        String configValue2 = companyConfigMap.get(staffVo.getFkCompanyId());
        if (GeneralTool.isNotEmpty(configValue2)){
            domainName = configValue2;
        } else {
            Result<String> domainNameResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.FILE_SRC_PREFIX.key);
            if (domainNameResult.isSuccess() && GeneralTool.isNotEmpty(domainNameResult.getData())) {
                domainName = domainNameResult.getData();
            }
        }


        String toSignPage = "";
        Result<String> toSignPageResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.REMINDER_TASK_LINK_WF_TAKE.key);
        if (toSignPageResult.isSuccess() && GeneralTool.isNotEmpty(toSignPageResult.getData())) {
            toSignPage = toSignPageResult.getData();
        }
        String toDoPage = "";
        Result<String> toDoPageResult = permissionCenterClient.getConfigValueByConfigKey(ProjectKeyEnum.REMINDER_TASK_LINK_WF_APPROVE.key);
        if (toDoPageResult.isSuccess() && GeneralTool.isNotEmpty(toDoPageResult.getData())) {
            toDoPage = toDoPageResult.getData();
        }
        //List<RemindTaskDto> remindTaskDtos = new ArrayList<>();
        Map<String,String> param = new HashMap();
        param.put("staffIdList",staffIdList.toString());
        String  map = null;
        ObjectMapper mapper = new ObjectMapper();
        try {
            map = mapper.writeValueAsString(param);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
        if (processDefinition.getKey().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
                    ||processDefinition.getId().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)){
            emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey());
            }else {
            emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.NON_WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey());
            }

        emailSenderQueue.setEmailTitle(sb.toString());
        emailSenderQueue.setEmailParameter(map);
        emailSenderQueue.setFkDbName(dbName);
        emailSenderQueue.setFkTableName(TableEnum.ACT_HI_TASKINST.key);
        emailSenderQueue.setFkTableId(Long.valueOf(task.getId()));
        emailSenderQueue.setOperationTime(now());
        List<EmailSenderQueue> emailSenderQueueList = Lists.newArrayList(emailSenderQueue);

        try {
            Result<Boolean> booleanResult = reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("发送提醒信息异常" + e.getMessage());
        }
//        for (String s : staffIdList) {
//            RemindTaskDto remindTaskDto = new RemindTaskDto();
//            remindTaskDto.setFkTableName(TableEnum.ACT_HI_TASKINST.key);
//            remindTaskDto.setFkTableId(Long.valueOf(task.getId()));
//            remindTaskDto.setFkStaffId(Long.parseLong(s));
//            remindTaskDto.setStartTime(new Date());
//            //邮件方式发送
//            remindTaskDto.setRemindMethod("1");
//            //默认设置执行中
//            remindTaskDto.setStatus(1);
//            //默认背景颜色
//            if (processDefinition.getKey().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
//                    ||processDefinition.getId().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)){
//                remindTaskDto.setFkRemindEventTypeKey("WORKFLOW_LEAVE_FORM");
//            }else {
//                remindTaskDto.setFkRemindEventTypeKey("WORKFLOW");
//            }
//            remindTaskDto.setTaskBgColor("#3788d8");
//            remindTaskDto.setTaskTitle(sb.toString());
//            if (GeneralTool.isNotEmpty(dbName)){
//                remindTaskDto.setFkDbName(dbName);
//            }
//            //如果ids等于1 跳转详情页，大于1跳转待签页
//            if (staffIdList.size() > 1) {
//                //待签页
//                String taskId = task.getId();
//                String procInstId = task.getProcessInstanceId();
//                String businessKey = "";
//                List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery().processInstanceId(procInstId).list();
//                for (ProcessInstance processInstance : processInstances) {
//                    if (GeneralTool.isNotEmpty(processInstance.getBusinessKey())){
//                        businessKey = processInstance.getBusinessKey();
//                        break;
//                    }
//                }
//                String procdefKey = processDefinition.getKey();
//                if (processDefinition.getKey().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
//                        ||processDefinition.getId().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)){
//                    String url = domainName + "/office_office-form-management_workbreak-detail/"+businessKey
//                            +"?taskId="+taskId
//                            +"&procInstId="+procInstId
//                            +"&procdefKey="+TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key;
//                    //remindTaskDto.setTaskLink(url);
//                }else {
//                        String url = domainName + toSignPage;
//                       // remindTaskDto.setTaskLink(url);
//                }
//            } else if (staffIdList.size() == 1) {
//                //待办详情
//                String taskId = task.getId();
//                String procInstId = task.getProcessInstanceId();
//                String businessKey = "";
//                List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery().processInstanceId(procInstId).list();
//                for (ProcessInstance processInstance : processInstances) {
//                    if (GeneralTool.isNotEmpty(processInstance.getBusinessKey())){
//                        businessKey = processInstance.getBusinessKey();
//                        break;
//                    }
//                }
//                String procdefKey = processDefinition.getKey();
//                if (processDefinition.getKey().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)
//                        ||processDefinition.getId().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)){
//                    String url = domainName + "/office_office-form-management_workbreak-detail/"+businessKey
//                            +"?taskId="+taskId
//                            +"&procInstId="+procInstId
//                            +"&procdefKey="+TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key;
//                    //remindTaskDto.setTaskLink(url);
//                }else {
//                    String url = domainName + toDoPage + taskId + "?procInstId=" + procInstId + "&businessKey="
//                            + businessKey + "&procdefKey=" + procdefKey;
//                   // remindTaskDto.setTaskLink(url);
//                }
//            }
//
//
//            remindTaskDtos.add(remindTaskDto);
//        }
//        try {
//            reminderCenterClient.batchAdd(remindTaskDtos);
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("发送提醒信息异常" + e.getMessage());
//        }
    }

    @Override
    public StaffVo getStaffDto(DelegateTask task) {
        StaffVo staffVo = null;
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        List<HistoricIdentityLink> identityLinkList = historyService.getHistoricIdentityLinksForProcessInstance(task.getProcessInstanceId());
        String starterId = null;
        for (HistoricIdentityLink identityLink : identityLinkList) {
            if (Objects.equals("starter", identityLink.getType())) {
                starterId = identityLink.getUserId();
                break;
            }
        }
        if (GeneralTool.isNotEmpty(starterId)) {
            Result<StaffVo> result = permissionCenterClient.getCompanyIdByStaffId(Long.valueOf(starterId));
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                staffVo = result.getData();
            }
        }
        return staffVo;
    }

    @Override
    public StaffVo getExecutionStaffDto(DelegateExecution execution) {
        StaffVo staffVo = null;
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        List<HistoricIdentityLink> identityLinkList = historyService.getHistoricIdentityLinksForProcessInstance(execution.getProcessInstanceId());
        String starterId = null;
        for (HistoricIdentityLink identityLink : identityLinkList) {
            if (Objects.equals("starter", identityLink.getType())) {
                starterId = identityLink.getUserId();
                break;
            }
        }
        if (GeneralTool.isNotEmpty(starterId)) {
            Result<StaffVo> result = permissionCenterClient.getCompanyIdByStaffId(Long.valueOf(starterId));
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                staffVo = result.getData();
            }
        }
        return staffVo;
    }

    @Override
    public void addSystemLeaveLog(LeaveLogDto leaveLogDto) {
        Result<Boolean> result = officeCenterClient.addSystemLeaveLog(leaveLogDto);
        if (!result.isSuccess()){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
    }

    @Override
    public List<LeaveStockVo> getLeaveStockDtos(LeaveStockDto leaveStockDto) {
        return officeCenterClient.getLeaveStockDtos(leaveStockDto);
    }

    @Override
    public List<LeaveStockVo> getEfficientLeaveStockDtos(LeaveStockDto leaveStockDto) {
        return officeCenterClient.getEfficientLeaveStockDtos(leaveStockDto);
    }

    @Override
    public void addSystem(LeaveStockDto leaveStockDto) {
        Result<Long> result = officeCenterClient.addSystem(leaveStockDto);
        if (!result.isSuccess()){
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
    }

    @Override
    public void updateSystemLeavetock(LeaveStockDto leaveStockDto) {
        Result<Boolean> result = officeCenterClient.updateSystemLeavetock(leaveStockDto);
        if (!result.isSuccess()){
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
    }

    @Override
    public List<LeaveLogVo> getSystemLeaveLog(LeaveLogDto leaveLogDto) {
        return officeCenterClient.getSystemLeaveLog(leaveLogDto);
    }
}
