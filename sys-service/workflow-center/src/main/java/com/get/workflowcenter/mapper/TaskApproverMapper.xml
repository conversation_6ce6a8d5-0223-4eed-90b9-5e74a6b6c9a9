<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.workflowcenter.mapper.TaskApproverMapper">
  <select id="findByTaskId" resultType="com.get.workflowcenter.entity.WorkFlowTaskApprover">
    select t from TaskApprover t
    <where>
      <if test="taskId!=null and taskId !=''" >
        and  t.taskId = #{taskId}
      </if>
    </where>
  </select>
</mapper>