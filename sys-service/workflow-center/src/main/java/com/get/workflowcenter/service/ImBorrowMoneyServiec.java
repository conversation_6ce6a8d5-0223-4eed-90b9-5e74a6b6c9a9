package com.get.workflowcenter.service;

import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.PrepayApplicationFormVo;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/1 10:55
 */
public interface ImBorrowMoneyServiec {
    List<PrepayApplicationFormVo> getPrepayFlowData(String businessKey, String key);

    Boolean getDetectionUpdate(String businessKey);

    Boolean getBack(String businessKey, String msg);

    ActRuTaskVo getTaskDataByBusinessKey(String businessKey, String key);

    int getSignOrGet(String taskId, Integer version);

    Boolean startBorrowFlow(String businessKey, String procdefKey, String companyId);
}
