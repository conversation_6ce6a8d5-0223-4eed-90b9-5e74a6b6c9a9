package com.get.workflowcenter.listener;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.workflowcenter.component.IWorkFlowHelper;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.repository.ProcessDefinition;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.StringJoiner;

/**
 * @author: Sea
 * @create: 2021/3/24 10:23
 * @verison: 1.0
 * @description: 流程节点用来根据任务职位设置候选人的监听器
 */
public class TaskPositionListener implements TaskListener {
    @Override
    public void notify(DelegateTask task) {
        //获取注入类
        IPermissionCenterClient permissionCenterClient = SpringUtil.getBean(IPermissionCenterClient.class);
        TaskService taskService = SpringUtil.getBean(TaskService.class);
        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);

        //获取流程名称
        ProcessEngine processEngine = SpringUtil.getBean(ProcessEngine.class);

        ProcessDefinition processDefinition = processEngine.getRepositoryService()
                .createProcessDefinitionQuery()
                .processDefinitionId(task.getProcessDefinitionId())
                .singleResult();

        StaffVo staffVo = workFlowHelper.getStaffDto(task);

        HashMap<String, Object> map = new HashMap<>();
        map.put("sequenceFlowsStatus", 0);
        //通过登录人公司id和描述中设置的职位编号
        String positionNum = task.getDescription();
        if (GeneralTool.isNotEmpty(positionNum)) {
            //多职位逗号隔开
            String[] split = positionNum.split(",");
            List<String> positionNumList = Arrays.asList(split);
            //feign调用查找职位全部员工id
            Result<List<Long>> result = permissionCenterClient.getPositionByNum(positionNumList);
            List<String> staffIdList = new ArrayList<>();
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                staffIdList = BeanCopyUtils.copyListProperties(result.getData(),String::new);
                for (Long datum : result.getData()) {
                    if (GeneralTool.isNotEmpty(datum)) {
                        staffIdList.add(String.valueOf(datum));
                    }
                }
            }
            if (GeneralTool.isNotEmpty(staffIdList)) {
                if (staffIdList.size() == 1) {
                    //只有一个人就是是待办人
                    taskService.setAssignee(task.getId(), staffIdList.get(0));
                    StringJoiner stringJoiner = new StringJoiner(",");
                    stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_DO.key);
                    if (TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key.equals(processDefinition.getKey())) {
                        stringJoiner.add(ProjectKeyEnum.OFFICE_CENTER.key);
                    }
                    workFlowHelper.sendMessage(staffVo, staffIdList, processDefinition, "待审核", task, stringJoiner.toString(),null);
                } else {
                    //有多个就全部设置为候选人
                    task.addCandidateUsers(staffIdList);
                    StringJoiner stringJoiner = new StringJoiner(",");
                    stringJoiner.add(ProjectKeyEnum.WORKFLOW_CENTER.key).add(ProjectKeyEnum.WORKFLOW_CENTER_TO_SIGN.key);
                    if (TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key.equals(processDefinition.getKey())) {
                        stringJoiner.add(ProjectKeyEnum.OFFICE_CENTER.key);
                    }
                    workFlowHelper.sendMessage(staffVo, staffIdList, processDefinition, "待签取", task, stringJoiner.toString(),null);
                }
            } else {
                //没有处理人，系统自动驳回
                taskService.setVariableLocal(task.getId(), "approvalAction", 0);
                taskService.addComment(task.getId(), task.getProcessInstanceId(), "系统自动驳回，" + task.getName() + "：找不到处理人。");
                taskService.complete(task.getId(), map);
            }
        } else {
            //没有设置任务节点处理人职位，自动驳回
            taskService.setVariableLocal(task.getId(), "approvalAction", 0);
            taskService.addComment(task.getId(), task.getProcessInstanceId(), "系统自动驳回，" + task.getName() + "：没有设置任务节点处理人职位。");
            taskService.complete(task.getId(), map);
        }
    }
}
