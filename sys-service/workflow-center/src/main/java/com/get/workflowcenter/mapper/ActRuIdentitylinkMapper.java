package com.get.workflowcenter.mapper;

import com.get.workflowcenter.entity.ActRuIdentitylink;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface ActRuIdentitylinkMapper {
    int insert(ActRuIdentitylink record);

    int insertSelective(ActRuIdentitylink record);

    int updateByPrimaryKeySelective(ActRuIdentitylink record);

    int updateByPrimaryKey(ActRuIdentitylink record);

    /**
     * 查找申请人
     *
     * @param procInstId
     * @return
     */
    ActRuIdentitylink getStarterId(@Param("procInstId") String procInstId);
}