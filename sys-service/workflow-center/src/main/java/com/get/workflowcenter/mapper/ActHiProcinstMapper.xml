<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.workflowcenter.mapper.ActHiProcinstMapper">
    <resultMap id="BaseResultMap" type="com.get.workflowcenter.entity.ActHiProcinst">
        <id column="ID_" jdbcType="VARCHAR" property="id"/>
        <result column="PROC_INST_ID_" jdbcType="VARCHAR" property="procInstId"/>
        <result column="BUSINESS_KEY_" jdbcType="VARCHAR" property="businessKey"/>
        <result column="PROC_DEF_ID_" jdbcType="VARCHAR" property="procDefId"/>
        <result column="START_TIME_" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="END_TIME_" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="DURATION_" jdbcType="BIGINT" property="duration"/>
        <result column="START_USER_ID_" jdbcType="VARCHAR" property="startUserId"/>
        <result column="START_ACT_ID_" jdbcType="VARCHAR" property="startActId"/>
        <result column="END_ACT_ID_" jdbcType="VARCHAR" property="endActId"/>
        <result column="SUPER_PROCESS_INSTANCE_ID_" jdbcType="VARCHAR" property="superProcessInstanceId"/>
        <result column="DELETE_REASON_" jdbcType="VARCHAR" property="deleteReason"/>
        <result column="TENANT_ID_" jdbcType="VARCHAR" property="tenantId"/>
        <result column="NAME_" jdbcType="VARCHAR" property="name"/>
    </resultMap>
    <sql id="Base_Column_List">
    ID_, PROC_INST_ID_, BUSINESS_KEY_, PROC_DEF_ID_, START_TIME_, END_TIME_, DURATION_, 
    START_USER_ID_, START_ACT_ID_, END_ACT_ID_, SUPER_PROCESS_INSTANCE_ID_, DELETE_REASON_, 
    TENANT_ID_, NAME_
  </sql>


    <insert id="insertSelective" parameterType="com.get.workflowcenter.entity.ActHiProcinst" keyProperty="id"
            useGeneratedKeys="true">
        insert into act_hi_procinst
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                ID_,
            </if>
            <if test="procInstId != null">
                PROC_INST_ID_,
            </if>
            <if test="businessKey != null">
                BUSINESS_KEY_,
            </if>
            <if test="procDefId != null">
                PROC_DEF_ID_,
            </if>
            <if test="startTime != null">
                START_TIME_,
            </if>
            <if test="endTime != null">
                END_TIME_,
            </if>
            <if test="duration != null">
                DURATION_,
            </if>
            <if test="startUserId != null">
                START_USER_ID_,
            </if>
            <if test="startActId != null">
                START_ACT_ID_,
            </if>
            <if test="endActId != null">
                END_ACT_ID_,
            </if>
            <if test="superProcessInstanceId != null">
                SUPER_PROCESS_INSTANCE_ID_,
            </if>
            <if test="deleteReason != null">
                DELETE_REASON_,
            </if>
            <if test="tenantId != null">
                TENANT_ID_,
            </if>
            <if test="name != null">
                NAME_,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=VARCHAR},
            </if>
            <if test="procInstId != null">
                #{procInstId,jdbcType=VARCHAR},
            </if>
            <if test="businessKey != null">
                #{businessKey,jdbcType=VARCHAR},
            </if>
            <if test="procDefId != null">
                #{procDefId,jdbcType=VARCHAR},
            </if>
            <if test="startTime != null">
                #{startTime,jdbcType=TIMESTAMP},
            </if>
            <if test="endTime != null">
                #{endTime,jdbcType=TIMESTAMP},
            </if>
            <if test="duration != null">
                #{duration,jdbcType=BIGINT},
            </if>
            <if test="startUserId != null">
                #{startUserId,jdbcType=VARCHAR},
            </if>
            <if test="startActId != null">
                #{startActId,jdbcType=VARCHAR},
            </if>
            <if test="endActId != null">
                #{endActId,jdbcType=VARCHAR},
            </if>
            <if test="superProcessInstanceId != null">
                #{superProcessInstanceId,jdbcType=VARCHAR},
            </if>
            <if test="deleteReason != null">
                #{deleteReason,jdbcType=VARCHAR},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=VARCHAR},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>


    <select id="getCompleteTaskList" resultType="com.get.workflowcenter.vo.ActHiProcinstVo">
        select distinct arp.ID_ AS procDefId,arp.NAME_ as procesName,arp.KEY_ as procdefKey ,arp.VERSION_ as
        procesVersion
        ,ahp.START_TIME_ as startTime,ahp.BUSINESS_KEY_ as businessKey,ahp.ID_ as id, ahp.PROC_INST_ID_ as
        procInstId,ahp.START_USER_ID_ as startByName,ahp.END_TIME_ as endTime
        ,case when ahp.DELETE_REASON_ is null then aa.actName else ahp.DELETE_REASON_ end as actName from
        ACT_HI_PROCINST ahp
        LEFT JOIN ACT_RE_PROCDEF arp on arp.ID_=ahp.PROC_DEF_ID_
        LEFT JOIN ACT_RE_DEPLOYMENT ard on ard.ID_ =arp.DEPLOYMENT_ID_
        LEFT JOIN act_hi_taskinst aht on aht.PROC_INST_ID_ =ahp.ID_
        LEFT JOIN (select MAX(aht.PROC_INST_ID_)proid ,MAX(aht.NAME_)actName from act_hi_taskinst aht where
        aht.START_TIME_ in (select MAX(START_TIME_)from act_hi_taskinst group by PROC_INST_ID_) GROUP BY
        aht.PROC_INST_ID_)aa
        ON aa.proid =ahp.ID_
        where 1=1
        <if test="category!=null and category!=''">
            and ard.CATEGORY_=#{category}
        </if>
        <if test="name!=null and name!=''">
            and arp.NAME_ like #{name}
        </if>
        <if test="staffIdByNames!=null and staffIdByNames.size>0">
            and ahp.START_USER_ID_ in
            <foreach collection="staffIdByNames" item="staffIdByName" index="index" open="(" separator="," close=")">
                #{staffIdByName}
            </foreach>
        </if>
        and ahp.END_TIME_ is not
        null ORDER BY ahp.END_TIME_ DESC

    </select>


    <select id="getCompleteTaskListByNormalUser" resultType="com.get.workflowcenter.vo.ActHiProcinstVo">
        select distinct arp.ID_ AS procDefId,arp.NAME_ as procesName,arp.KEY_ as procdefKey ,arp.VERSION_ as
        procesVersion
        ,ahp.START_TIME_ as startTime,ahp.BUSINESS_KEY_ as businessKey,ahp.ID_ as id, ahp.PROC_INST_ID_ as
        procInstId,ahp.START_USER_ID_ as startByName<!--,ahp.END_TIME_ as endTime-->,aht.END_TIME_ as endTime
        ,case when ahp.DELETE_REASON_ is null then aa.actName else ahp.DELETE_REASON_ end as actName from
        ACT_HI_PROCINST ahp
        LEFT JOIN ACT_RE_PROCDEF arp on arp.ID_=ahp.PROC_DEF_ID_
        LEFT JOIN ACT_RE_DEPLOYMENT ard on ard.ID_ =arp.DEPLOYMENT_ID_
        LEFT JOIN (select DISTINCT MAX(aht.PROC_INST_ID_)as PROC_INST_ID_,MAX(aht.ID_)as ID_,MAX(aht.ASSIGNEE_)as
        ASSIGNEE_,MAX(aht.END_TIME_) as END_TIME_ from act_hi_taskinst aht left join act_hi_procinst ahp on ahp.ID_=aht.PROC_INST_ID_ where 1=1
        <if test="username !=null and username != ''">
            and aht.ASSIGNEE_=#{username}
        </if>
        <!--查出所有审核过的流程-->
        <!--<and ahp.END_TIME_ is not null>-->
        and aht.END_TIME_ is not null
        group BY aht.PROC_INST_ID_) aht on aht.PROC_INST_ID_ =ahp.ID_
        LEFT JOIN (select MAX(aht.PROC_INST_ID_)proid ,MAX(aht.NAME_)actName from act_hi_taskinst aht left join
        act_hi_procinst ahp on ahp.PROC_INST_ID_ = aht.PROC_INST_ID_ where
        aht.START_TIME_ in (select MAX(START_TIME_)from act_hi_taskinst group by PROC_INST_ID_)
        <!--<and ahp.END_TIME_ is not null>-->
        GROUP BY
        aht.PROC_INST_ID_)aa ON aa.proid =ahp.ID_
        where 1=1
        <if test="category!=null and category!=''">
            and ard.CATEGORY_=#{category}
        </if>
        <if test="name!=null and name!=''">
            and arp.NAME_ like "%",#{name},"%"
        </if>

        <choose>
            <when test="staffIdByNames!=null and staffIdByNames.size>0">
                and ahp.START_USER_ID_ in
                <foreach collection="staffIdByNames" item="staffIdByName" index="index" open="(" separator=","
                         close=")">
                    #{staffIdByName}
                </foreach>
                and aht.ASSIGNEE_=#{username}
            </when>
            <otherwise>
                and aht.ASSIGNEE_=#{username} or(ahp.START_USER_ID_=#{username})
            </otherwise>
        </choose>
        <!--查出所有审核过的流程-->
        <!--and ahp.END_TIME_ is not null-->
        <!--        <ORDER BY ahp.END_TIME_ DESC>-->
        ORDER BY aht.END_TIME_ DESC
    </select>


    <select id="getRunningTaskList" resultType="com.get.workflowcenter.vo.ActHiProcinstVo">
        select arp.NAME_ as procesName, arp.VERSION_ as procesVersion, arp.KEY_ as procdefKey
        ,IF(ahp.END_TIME_>0,'已完成','未完成')as whetherComplete ,IF(ahp.END_TIME_>0,1,0)as whetherCompleteState ,ahp.PROC_INST_ID_ as procInstId,ahp.DURATION_ as deployId ,
        ahp.START_USER_ID_ as startUserId,ahp.START_TIME_ as startTime,ahp.ID_ as id,ahp.BUSINESS_KEY_ as businessKey
        ,case WHEN ahp.DELETE_REASON_ is null then aa.actName else ahp.DELETE_REASON_ end as actName
        from ACT_HI_PROCINST ahp
        LEFT JOIN ACT_RE_PROCDEF arp on ahp.PROC_DEF_ID_=arp.ID_
        LEFT JOIN ACT_RE_DEPLOYMENT ard on ard.ID_=arp.DEPLOYMENT_ID_
        LEFT JOIN (select MAX(aht.PROC_INST_ID_)proid ,MAX(aht.NAME_)actName from act_hi_taskinst aht where
        aht.START_TIME_ in (select MAX(START_TIME_)from act_hi_taskinst group by PROC_INST_ID_) GROUP BY
        aht.PROC_INST_ID_)aa ON aa.proid =ahp.ID_
        where 1=1
        <if test="category!=null and category!=''">
            and ard.CATEGORY_=#{category}
        </if>
        <if test="name!=null and name!=''">
            and arp.NAME_ like #{name}
        </if>
        order by ahp.START_TIME_ DESC

    </select>

    <select id="getRunningTaskListByNormalUser" resultType="com.get.workflowcenter.vo.ActHiProcinstVo">
        select arp.NAME_ as procesName, arp.VERSION_ as procesVersion, arp.KEY_ as procdefKey
        ,IF(ahp.END_TIME_>0,'已完成','未完成')as whetherComplete ,IF(ahp.END_TIME_>0,1,0)as whetherCompleteState  ,ahp.PROC_INST_ID_ as procInstId,ahp.DURATION_ as deployId ,
        ahp.START_USER_ID_ as startUserId,ahp.START_TIME_ as startTime,ahp.ID_ as id,ahp.BUSINESS_KEY_ as businessKey
        ,case WHEN ahp.DELETE_REASON_ is null then aa.actName else ahp.DELETE_REASON_ end as actName
        from ACT_HI_PROCINST ahp
        LEFT JOIN ACT_RE_PROCDEF arp on ahp.PROC_DEF_ID_=arp.ID_
        LEFT JOIN ACT_RE_DEPLOYMENT ard on ard.ID_=arp.DEPLOYMENT_ID_
        LEFT JOIN (select MAX(aht.PROC_INST_ID_)proid ,MAX(aht.NAME_)actName from act_hi_taskinst aht where
        aht.START_TIME_ in (select MAX(START_TIME_)from act_hi_taskinst group by PROC_INST_ID_) GROUP BY
        aht.PROC_INST_ID_)aa ON aa.proid =ahp.ID_
        where 1=1
        <if test="category!=null and category!=''">
            and ard.CATEGORY_=#{category}
        </if>
        <if test="name!=null and name!=''">
            and arp.NAME_ like #{name}
        </if>
        and ahp.START_USER_ID_=#{username}
        order by ahp.START_TIME_ DESC

    </select>

</mapper>