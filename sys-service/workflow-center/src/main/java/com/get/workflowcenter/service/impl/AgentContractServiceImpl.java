package com.get.workflowcenter.service.impl;

import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.entity.AgentContract;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.AgentContractVo;
import com.get.workflowcenter.mapper.ActRuTaskMapper;
import com.get.workflowcenter.service.IAgentContractService;
import com.google.common.collect.Lists;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.get.common.utils.GetDateUtil.dateDiff;


/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/15 18:34
 */
@Service
public class AgentContractServiceImpl implements IAgentContractService {
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private HistoryService historyService;
    @Resource
    private ActRuTaskMapper actRuTaskMapper;
    @Autowired
    private ISaleCenterClient saleCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Autowired
    private TaskService taskService;
    @Autowired
    private RuntimeService runtimeService;


    @Override
    public ActRuTaskVo getTaskDataByBusinessKey(String businessKey, String key) {
        ActRuTaskVo actRuTaskVo = new ActRuTaskVo();
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceBusinessKey(businessKey).processDefinitionKey(key).singleResult();
        if (historicProcessInstance != null) {
            actRuTaskVo.setProcInstId(historicProcessInstance.getId());
            ActRuTaskVo taskVersionByProcessInId = actRuTaskMapper.getTaskVersionByProcessInId(historicProcessInstance.getId());
            if (taskVersionByProcessInId != null) {
                ProcessDefinition processDefinitionQuery = repositoryService.createProcessDefinitionQuery().processDefinitionId(historicProcessInstance.getProcessDefinitionId()).singleResult();
                actRuTaskVo.setId(taskVersionByProcessInId.getId());
                actRuTaskVo.setDeployId(processDefinitionQuery.getDeploymentId());
                actRuTaskVo.setTaskVersion(taskVersionByProcessInId.getRev());
                return actRuTaskVo;
            }
        }
        return actRuTaskVo;
    }

    @Override
    public List<AgentContractVo> getPayFlowData(String businessKey, String key) {
        if (StringUtils.isBlank(businessKey)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceBusinessKey(businessKey).processDefinitionKey(key).singleResult();

        System.out.println("==============>" + GeneralTool.toJson(historicProcessInstance));
        if (historicProcessInstance == null) {
            return Lists.newArrayList();
        }
        List<AgentContractVo> agentContractVos = new ArrayList<>();
        List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery().processInstanceId(historicProcessInstance.getId()).orderByHistoricActivityInstanceStartTime().asc().list();

        for (HistoricActivityInstance hti : list) {
            AgentContractVo agentContractVo = new AgentContractVo();
            if ("startEvent".equals(hti.getActivityType())) {
                agentContractVo.setActName("开始流程");
                agentContractVo.setAssignee(historicProcessInstance.getStartUserId());
                Date startTime = hti.getStartTime();
                agentContractVo.setStartTimeDto(startTime);
                agentContractVos.add(agentContractVo);

            } else if ("endEvent".equals(hti.getActivityType())) {
                agentContractVo.setActName("流程结束");
                Date startTime = hti.getStartTime();
                agentContractVo.setStartTimeDto(startTime);
                agentContractVo.setEndTimeDto(hti.getEndTime());
                agentContractVos.add(agentContractVo);
            }
            if (hti.getActivityName() != null) {
                Date startTime = hti.getStartTime();
                String startformat = simpleDateFormat.format(startTime);
                agentContractVo.setStartTimeDto(startTime);
                agentContractVo.setActName(hti.getActivityName());
                if (null != hti.getAssignee()) {
//                    String staffName = permissionCenterClient.getStaffName(Long.valueOf(hti.getAssignee()));
                    Result<String> result = permissionCenterClient.getStaffName(Long.valueOf(hti.getAssignee()));
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                        agentContractVo.setAssignee(result.getData());
                    }
                }
                if (hti.getEndTime() != null) {
                    Date endTime = hti.getEndTime();

                    String endformat = simpleDateFormat.format(endTime);
                    String durationTime = dateDiff(startformat, endformat, simpleDateFormat.toPattern());
                    agentContractVo.setTotalTime(durationTime);
                    agentContractVo.setEndTimeDto(endTime);
                }
                List<Comment> taskComments = taskService.getTaskComments(hti.getTaskId());
                for (Comment comlist : taskComments) {
                    agentContractVo.setMsg(comlist.getFullMessage());
                }
                agentContractVos.add(agentContractVo);
            }

        }
        return agentContractVos;

    }

    @Override
    public int getSignOrGet(String taskId, Integer version) {
//        StaffVo staff = StaffContext.getStaff();
        String userid = String.valueOf(GetAuthInfo.getStaffId());
        if (GeneralTool.isNotEmpty(taskId) && GeneralTool.isNotEmpty(version)) {
            Task taskQuery = taskService.createTaskQuery().taskId(taskId).taskAssignee(userid).singleResult();
            if (taskQuery == null) {
                Task task = taskService.createTaskQuery().taskCandidateUser(userid).taskId(taskId).singleResult();
                if (task != null) {
                    ActRuTaskVo actRuTaskVo = actRuTaskMapper.comparisonVersion(task.getId(), version);
                    if (actRuTaskVo != null) {
                        //签收
                        return 0;
                    }
                } else {
                    //什么都不用干
                    return 2;
                }
            } else {
                //1待办
                return 1;
            }
        }
        return 2;
    }

    @Override
    public Boolean startContractFlow(String businessKey, String procdefKey, String companyId) {
        if (GeneralTool.isEmpty(businessKey) || GeneralTool.isEmpty(procdefKey) || GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        String username = String.valueOf(GetAuthInfo.getStaffId());
        Authentication.setAuthenticatedUserId(username);
        AgentContractVo agentContractById = null;
        Result<com.get.salecenter.vo.AgentContractVo> result = saleCenterClient.getAgentContractById(Long.valueOf(businessKey));
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            com.get.salecenter.vo.AgentContractVo agentContractById_ = result.getData();
            agentContractById = BeanCopyUtils.objClone(agentContractById_, AgentContractVo::new);
        }
        if (agentContractById == null || agentContractById.getStatus() != 0 || agentContractById.getStatus() == 5) {
            return false;
        }

        AgentContractVo agentContract = BeanCopyUtils.objClone(agentContractById, AgentContractVo::new);
        Map<String, Object> map = new HashMap<>();
        map.put("userid", username);
        map.put("companyId", companyId);
        map.put("tableName", "m_agent_contract");
        map.put("businessKey", agentContract.getId());
        map.put("agentContract", agentContract);
        try {

            //表名 +公司id
            List<ProcessDefinition> processDefinition =
                    repositoryService.createProcessDefinitionQuery().processDefinitionKey(procdefKey).processDefinitionTenantId(companyId).orderByProcessDefinitionVersion().desc().list();
            String id = "";
            for (ProcessDefinition pdid : processDefinition) {
                id = pdid.getId();
                break;
            }

            ProcessInstance processInstance = runtimeService.startProcessInstanceById(id, String.valueOf(agentContract.getId()), map);
//            AgentContractVo agentContractDto = saleCenterClient.getAgentContractById(Long.valueOf(businessKey));
            Result<com.get.salecenter.vo.AgentContractVo> agentContractDtoResult = saleCenterClient.getAgentContractById(Long.valueOf(businessKey));
            if (agentContractDtoResult.isSuccess() && GeneralTool.isNotEmpty(agentContractDtoResult.getData())) {
                com.get.salecenter.vo.AgentContractVo agentContractVo = agentContractDtoResult.getData();
                if (agentContractVo.getStatus() == 0) {
                    agentContract.setStatus(2);
                    AgentContract agentContract_ = BeanCopyUtils.objClone(agentContract, AgentContract::new);
                    saleCenterClient.updateChangeStatus(agentContract_);
                }

            }

        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }
}
