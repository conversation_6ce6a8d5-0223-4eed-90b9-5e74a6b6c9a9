package com.get.workflowcenter.controller;

import com.get.common.cache.CacheNames;
import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.TableEnum;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.log.exception.GetServiceException;
import com.get.core.redis.lock.RedisLock;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.workflowcenter.vo.*;
import com.get.workflowcenter.vo.ActReProcdefVo;
import com.get.workflowcenter.entity.ActReProcdef;
import com.get.workflowcenter.entity.ActRuTask;
import com.get.workflowcenter.mapper.ActRuTaskMapper;
import com.get.workflowcenter.service.IWorkFlowService;
import com.get.workflowcenter.dto.ActHiProcinstDto;
import com.get.workflowcenter.dto.ActReProcdefDto;
import com.get.workflowcenter.dto.ActRuExecutionDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.IdentityService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricIdentityLink;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Api(tags = "流程管理")
@RestController
@RequestMapping("/workflow")
@Slf4j
public class WorkFlowController {

    private static final String cache_key = "task_count";

    @Autowired
    /**
     * 管理流程定义
     * */
    private RepositoryService repositoryService;
    @Autowired
    /**
     * 执行管理、包括启动、推进、删除流程实例等操作
     * */
    private RuntimeService runtimeService;
    @Autowired
    /**
     * 任务管理
     * */
    private TaskService taskService;
    @Autowired
    private IWorkFlowService iWorkFlowService;

    /**
     * 工作流服务
     */
    @Autowired
    private IWorkFlowService workFlowService;
    @Autowired
    private IdentityService identityService;
    @Resource
    private ActRuTaskMapper actRuTaskMapper;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private HistoryService historyService;


    /**
     * @return com.get.common.result.ListResponseBo<com.get.workflowcenter.vo.ActReProcdefDto>
     * @Description :流程管理中的所有流程
     * @Param [page]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation("流程管理或者发起事务中的所有流程")
    @PostMapping("/getAllProcdef")
    public ListResponseBo<ActReProcdefVo> getAllProcdef(@RequestBody SearchBean<ActReProcdefDto> page) {
        List<ActReProcdefVo> procdefDtos = workFlowService.getProcefList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        ListResponseBo<ActReProcdefVo> actReProcdefDtoListResponseBo = new ListResponseBo<>(procdefDtos, p);
        return actReProcdefDtoListResponseBo;
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.workflowcenter.entity.ActReProcdef>
     * @Description :变更流程定义状态
     * @Param [procdef_id]
     * <AUTHOR>
     */
    @ApiOperation("流程管理中的变更流程定义状态")
    @GetMapping("/updateProcdefStatus")
    public ResponseBo<ActReProcdef> updateProcdefStatus(@RequestParam("procdef_id") String procdef_id) {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(procdef_id).singleResult();
        Boolean isSuspended = processDefinition.isSuspended();
        if (isSuspended) {
            repositoryService.activateProcessDefinitionById(procdef_id, true, null);
        } else {
            repositoryService.suspendProcessDefinitionById(procdef_id, true, null);
        }
        return ResponseBo.ok();
    }

    /**
     * @return void
     * @Description :查看流程图
     * @Param [response, id]
     * <AUTHOR>
     */
    @ApiOperation("查看流程图")
    @GetMapping("/ViewFlowChart")
    @VerifyPermission(IsVerify = false)
    public void look(HttpServletResponse response, @RequestParam String deployId) throws IOException {
        response.setContentType("image/png");
        List<String> deploymentResourceNames = repositoryService.getDeploymentResourceNames(deployId);
        String png = "";
        for (String dd : deploymentResourceNames) {
            if (dd.endsWith("png")) {
                png = dd;
                break;
            }
        }
        InputStream resourceAsStream = repositoryService.getResourceAsStream(deployId, png);
        ServletOutputStream outputStream = response.getOutputStream();
        int len = 0;
        byte[] bytes = new byte[1024];
        while ((len = resourceAsStream.read(bytes)) != -1) {
            outputStream.write(bytes, 0, len);
            outputStream.flush();
        }
        outputStream.close();
        resourceAsStream.close();
    }


    /**
     * @return void
     * @Description : 高亮正在运行的流程图片
     * @Param [processId, response]
     * <AUTHOR>
     */
    @ApiOperation("高亮正在运行的流程图片")
    @PostMapping("viewProcessImg")
    @VerifyPermission(IsVerify = false)
    @VerifyLogin(IsVerify = false)
    public void viewProcessImg(@RequestBody ActRuTask actRuTask, HttpServletResponse response, @RequestParam(
            "procdefKey") String procdefKey) {
        try {
            String processId = actRuTask.getProcInstId();
            byte[] processImage = workFlowService.getProcessImage(processId, procdefKey);
            System.out.println(processImage.length);
            response.setContentType("text/html;charset=UTF-8");
            OutputStream outputStream = response.getOutputStream();
            response.setContentType("image/png");
            outputStream.write(processImage);
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
            log.error("viewProcessImg---- {}", e.getLocalizedMessage());

        }
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.workflowcenter.vo.ActRuExecutionDto>
     * @Description :流程跟踪中的所有正在执行流程
     * @Param [page]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation("流程跟踪中的所有正在执行流程")
    @PostMapping("getAllExecution")
    public ResponseBo<ActRuExecutionVo> getAllExecution(@RequestBody SearchBean<ActRuExecutionDto> page) {
        List<ActRuExecutionVo> executionList = workFlowService.getExecutionList(page.getData(), page);

        Page p = BeanCopyUtils.objClone(page, Page::new);
        ListResponseBo<ActRuExecutionVo> actReProcdefDtoListResponseBo = new ListResponseBo<>(executionList, p);

        return actReProcdefDtoListResponseBo;
    }

    /**
     * @ Description : 流程管理中删除
     * @ Param [prcessId]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("流程管理中删除")
    @PostMapping("deletePrcess")
    public ResponseBo deletePrcess(@RequestParam("prcessId") String prcessId) {
        workFlowService.deleteDeployment(prcessId);
        return ResponseBo.ok();
    }


    /**
     * @ Description :流程跟踪中的终止流程或待办事务中的关闭流程
     * @ Param [processInstId, msg, procdefKey, businessKey]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation("流程跟踪中的终止流程或待办事务中的关闭流程")
    @DeleteMapping("/StopExecution")
    public ResponseBo StopExecution(@RequestParam("processInstId") String processInstId,
                                    @RequestParam("msg") String msg, @RequestParam("procdefKey") String procdefKey,
                                    @RequestParam("businessKey") String businessKey) throws Exception {
        Boolean aBoolean = workFlowService.StopExecution(processInstId, msg, procdefKey, businessKey);
        return new ResponseBo(aBoolean);
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :拾取或归还任务 1拾取，0归还
     * @Param [taskId, userName, status]
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation("拾取或归还任务,1拾取，0归还")
    @GetMapping("getClaim")
    public ResponseBo getClaim(@RequestParam("taskID") String taskId,
                               @RequestParam("status") Integer status,
                               @RequestParam("version") int version) {
        //清除提醒数缓存
        CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(SecureUtil.getStaffId()), cache_key);
        if (StringUtils.isNotBlank(taskId) && status != null) {
            Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
//            StaffVo staff = StaffContext.getStaff();
            if (task != null) {
                if (status.equals(1)) {
                    ActRuTaskVo actRuTaskVo = actRuTaskMapper.comparisonVersion(task.getId(), version);
                    if (actRuTaskVo != null) {
                        taskService.claim(taskId, String.valueOf(GetAuthInfo.getStaffId()));
                        try {
                            //签取了任务就把提醒删除
                            reminderCenterClient.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(taskId));
                        } catch (Exception e) {
                            log.error("提醒任务删除异常----------------" + e.getMessage());
                        }


                        return ResponseBo.ok();
                    }
                } else {
                    //加了权限就要去掉注释---------------------------
                    //if (!staff.getIsAdmin()) {
                    List<IdentityLink> identityLinksForProcessInstance = taskService.getIdentityLinksForTask(taskId);
                    for (IdentityLink ie : identityLinksForProcessInstance) {

                        if ("candidate".equals(ie.getType()) || "assignee".equals(ie.getType())) {
                            if (ie.getUserId().equals(String.valueOf(GetAuthInfo.getStaffId()))) {
                                taskService.setAssignee(taskId, null);
                                return ResponseBo.ok();
                            }
                        }
                    }

                    throw new GetServiceException(LocaleMessageUtils.getMessage("return_the_task_error"));
          /*          } else {                    //加了权限就要去掉注释---------------------------

                        taskService.setAssignee(taskId, null);
                        return ResponseBo.ok();
            }*/
                }
            }
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.workflowcenter.vo.ActRuTaskVo>
     * @Description :待办事务查询和搜索
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation("待办事务的查询")
    @PostMapping("getToDoSelect")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<ActRuTaskVo> getToDoSelect(@RequestBody SearchBean<ActReProcdefDto> page) {
        List<ActRuTaskVo> toSign = workFlowService.getToDoSelect(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        ListResponseBo<ActRuTaskVo> actRuTaskDtoListResponseBo = new ListResponseBo<>(toSign, p);
        return actRuTaskDtoListResponseBo;
    }

    /**
     * @return com.get.common.result.ResponseBo<com.get.workflowcenter.vo.ActRuTaskVo>
     * @Description :待办事务查询和搜索
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation("待签事务的查询")
    @PostMapping("getSignedSelect")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<ActRuTaskVo> getSignedSelect(@RequestBody SearchBean<ActReProcdefDto> page) {
        List<ActRuTaskVo> toSign = workFlowService.getSignedSelect(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        ListResponseBo<ActRuTaskVo> actRuTaskDtoListResponseBo = new ListResponseBo<>(toSign, p);
        return actRuTaskDtoListResponseBo;
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.workflowcenter.vo.ActHiActinstDto>
     * @Description :办结事务查询和搜索
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation("办结事务查询和搜索")
    @PostMapping("getCompleteTask")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<ActHiProcinstVo> getHolidayStatus(@RequestBody SearchBean<ActHiProcinstDto> page) {

        List<ActHiProcinstVo> actHiActinstDtos = workFlowService.getCompleteTask(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        ListResponseBo<ActHiProcinstVo> actRuTaskDtoListResponseBo = new ListResponseBo<>(actHiActinstDtos, p);
        return actRuTaskDtoListResponseBo;
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.workflowcenter.vo.ActHiActinstDto>
     * @Description :已发事务查询和搜索
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation("已发事务查询和搜索")
    @PostMapping("getRunningTask")
    @VerifyPermission(IsVerify = false)
    public ResponseBo<ActHiProcinstVo> getRunningTask(@RequestBody SearchBean<ActHiProcinstDto> page) {
        List<ActHiProcinstVo> actHiActinstDtos = workFlowService.getRunningTask(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        ListResponseBo<ActHiProcinstVo> actRuTaskDtoListResponseBo = new ListResponseBo<>(actHiActinstDtos, p);
        return actRuTaskDtoListResponseBo;
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :保存批注和流程推进(审批)
     * @Param [id, taskId, comment, outcome]
     * <AUTHOR>
     */
    @ApiOperation(value = "保存批注和流程推进(审批)", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.WORKFLOWCENTER, type = LoggerOptTypeConst.EDIT, description = "工作流/流程管理/保存批注和流程推进(审批)")
    @PostMapping("saveCommentAndComplete")
    public ResponseBo saveCommentAndComplete(@RequestParam("taskId") String taskId,
                                             @RequestParam("comment") String comment,
                                             @RequestParam("outcome") String outcome) {
        workFlowService.saveCommentAndComplete(taskId, comment, outcome);
        return ResponseBo.ok();
    }


    /**
     * @Description :feign调用 通过员工id查找对应业务表单id
     * @Param [staffId, key]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getFromIdsByStaffId")
    public Map<Long, Integer> getFromIdsByStaffId(@RequestParam("staffId") Long staffId, @RequestParam("key") String key) {
        return workFlowService.getFromIdsByStaffId(staffId, key);
    }

    /**
     * @Description :feign调用，根据业务id查找需要的数据放入ActRuTaskDto中
     * @Param [businessIds, procdefKey]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getActRuTaskDtosByBusinessKey")
    public Map<Long, ActRuTaskVo> getActRuTaskDtosByBusinessKey(@RequestBody List<Long> businessIds, @RequestParam("procdefKey") String procdefKey) {
        return workFlowService.getActRuTaskDtosByBusinessKey(businessIds, procdefKey);
    }

    /**
     * @ Description : 修改节点单独提交(重新申请或放弃)
     * @ Param [taskId, status]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation("修改节点单独提交(重新申请或放弃)")
    @GetMapping("/getUserSubmit")
    public ResponseBo getUserSubmit(@RequestParam("taskId") String taskId, @RequestParam("status") String status) {
        Map<String, Object> map = new HashMap<>();
        map.put("sequenceFlowsStatus", status);
        if ("0".equals(status)) {
            taskService.setVariableLocal(taskId, "approvalAction", 0);
        } else {
            taskService.setVariableLocal(taskId, "approvalAction", 1);
        }
        taskService.setVariable(taskId, "statusToDo", 1);
        taskService.complete(taskId, map);



        try {
            HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
            if (GeneralTool.isNotEmpty(historicTaskInstance)){
                ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                        .processDefinitionId(historicTaskInstance.getProcessDefinitionId())
                        .singleResult();

                if (TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key.equals(processDefinition.getKey())) {
                    List<HistoricIdentityLink> identityLinkList = historyService.getHistoricIdentityLinksForProcessInstance(historicTaskInstance.getProcessInstanceId());
                    String starterId = null;
                    for (HistoricIdentityLink identityLink : identityLinkList) {
                        if (Objects.equals("starter", identityLink.getType())) {
                            starterId = identityLink.getUserId();
                            break;
                        }
                    }
                    if (GeneralTool.isNotEmpty(starterId)){
                        //重新申请更新缓存
                        CacheUtil.evict(CacheNames.TASK_CACHE, starterId, cache_key);
                    }
                }
            }
        }catch (Exception e) {
            log.error(e.getMessage());
        }

        //重新提交删除提醒任务
        reminderCenterClient.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(taskId));

        return ResponseBo.ok();
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :查看审批记录
     * @Param [businessKey]
     * <AUTHOR>
     */
    @ApiOperation("查看审批记录")
    @OperationLogger(module = LoggerModulesConsts.WORKFLOWCENTER, type = LoggerOptTypeConst.DETAIL, description = "工作流/流程管理/查看审批记录")
    @PostMapping("viewHisCommentByBusinessKey")
    public ResponseBo viewHisCommentByBusinessKey(@RequestParam("businessKey") Long businessKey, @RequestParam("procdefKey") String procdefKey) {
        return new ResponseBo<>(workFlowService.viewHisCommentByBusinessKey(businessKey, procdefKey));
    }


    /**
     * @Description: 根据流程历史实例以及名称获取变量值
     * @Author: Jerry
     * @Date:10:21 2021/11/9
     */
    @ApiIgnore
    @GetMapping("getVariableByHisInstanceAndName")
    public ResponseBo getVariableByHisInstanceAndName(@RequestParam("processInstanceId") String processInstanceId,
                                                      @RequestParam("name") String name) {
        return new ResponseBo<>(workFlowService.getVariableByHisInstanceAndName(processInstanceId, name));
    }

    /**
     * @ Description :查詢发起人id
     * @ Param [processInstId]
     * @ return java.lang.String
     * @ author LEO
     */
    @ApiOperation("查詢发起人id")
    @ApiIgnore
    @VerifyPermission(IsVerify = false)
    @GetMapping("getStartUserId")
    public String getStartUserId(@RequestParam("processInstId") String processInstId) {
        String startUserId = workFlowService.getStartUserId(processInstId);
        return startUserId;

    }

//    /**
//     * @return com.get.common.result.ResponseBo<com.get.workflowcenter.vo.ActRuTaskVo>
//     * @Description :feign调用 开启流程
//     * @Param [businessKey, procdefKey, companyId]
//     * <AUTHOR>
//     */
//    @ApiIgnore
//    @PostMapping("startProcess")
//    public Boolean startProcess(@RequestParam("businessKey") String businessKey,
//                                @RequestParam("procdefKey") String procdefKey,
//                                @RequestParam("companyId") String companyId,
//                                @RequestBody Map<String, Object> map) {
//        return workFlowService.startProcess(businessKey, procdefKey, companyId, map);
//    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :查看审批记录
     * @Param [businessKey]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping("getHiComment")
    public HiCommentFeignVo getHiComment(@RequestParam("businessKey") Long businessKey, @RequestParam("procdefKey") String procdefKey) {
        return workFlowService.getHiComment(businessKey, procdefKey);
    }

/*
    @ApiIgnore
    @VerifyLogin(IsVerify = false)
    @GetMapping("getttt")
    public void getttt() {
        runtimeService.startProcessInstanceById("tt2tt:6:1192504");


    }

    @ApiIgnore
    @VerifyLogin(IsVerify = false)
    @GetMapping("com")
    public void com(@RequestParam("taskid") String taskid, @RequestParam("status") String status) {
        HashMap<String, Object> map = new HashMap<>();

        if (status.equals("0")) {
            map.put("pp", 0);
            taskService.complete(taskid, map);
        } else {
            map.put("pp", 1);
            taskService.complete(taskid, map);
        }

    }

    @ApiIgnore
    @VerifyLogin(IsVerify = false)
    @GetMapping("aaa")
    public void aaa(@RequestParam("procInstId") String procInstId)  {
        workFlowService.getStatusToDoSingle("0", procInstId, null);
    }*/


    @ApiOperation(value = "审批状态下拉框", notes = "")
    @GetMapping("approvalStatusSelected")
    @VerifyPermission(IsVerify = false)
    public ResponseBo approvalStatusSelected() {
        List<Map<String, Object>> datas = workFlowService.approvalStatusSelected();
        return new ListResponseBo<>(datas);
    }


    @ApiOperation(value = "申请表单流程发起", notes = "targetId:表id tableName:表名 companyId：公司id ")
    @OperationLogger(module = LoggerModulesConsts.WORKFLOWCENTER, type = LoggerOptTypeConst.DETAIL, description = "财务中心/申请表单管理/流程发起")
    @PostMapping("applicationFormStartProcess")
    @RedisLock(value = "fzh:applicationFormStartProcess", param = "#tableId", waitTime = 5L)
    public ResponseBo applicationFormStartProcess(@RequestParam("tableId") Long tableId,
                                @RequestParam("tableName") String tableName,
                                @RequestParam("companyId") String companyId) {
        workFlowService.applicationFormStartProcess(tableId, tableName, companyId);
        return ResponseBo.ok();
    }

    @ApiOperation("审批")
    @GetMapping("/getExamineAndApprove")
    public ResponseBo getExamineAndApprove(@RequestParam("status") String status, @RequestParam("taskId") String taskId,
                                           @RequestParam("procInstId") String procInstId, @RequestParam("msg") String msg) {

        if (GeneralTool.isEmpty(status) || GeneralTool.isEmpty(taskId) || GeneralTool.isEmpty(msg) || GeneralTool.isEmpty(procInstId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        StaffVo staff = StaffContext.getStaff();
        String username = String.valueOf(GetAuthInfo.getStaffId());
        Authentication.setAuthenticatedUserId(username);
        taskService.addComment(taskId, procInstId, msg);
        Map<String, Object> map = new HashMap<>();
        map.put("sequenceFlowsStatus", status);
        if ("0".equals(status)) {
            taskService.setVariableLocal(taskId, "approvalAction", 0);
        } else {
            taskService.setVariableLocal(taskId, "approvalAction", 1);
        }
        taskService.complete(taskId, map);
        iWorkFlowService.getStatusToDoSingle(status, procInstId);

        return ResponseBo.ok();
    }

}
