package com.get.workflowcenter.service.impl;

import com.get.common.eunms.TableEnum;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.salecenter.vo.StudentOfferVo;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.workflowcenter.mapper.ActRuTaskMapper;
import com.get.workflowcenter.service.IStudentOfferWorkFlowService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/20 22:23
 */
@Service
@Slf4j
public class StudentOfferWorkFlowServiceImpl implements IStudentOfferWorkFlowService {
    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private HistoryService historyService;
    @Resource
    private ActRuTaskMapper actRuTaskMapper;
    @Autowired
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Autowired
    private TaskService taskService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private ISaleCenterClient saleCenterClient;

    @Override
    public Boolean startStudentOfferFlow(String businessKey, String procdefKey, String companyId, String buttonType,String submitReason,Long fkCancelOfferReasonId) {
        if (GeneralTool.isEmpty(businessKey) || GeneralTool.isEmpty(procdefKey) || GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        String username = String.valueOf(GetAuthInfo.getStaffId());
        Authentication.setAuthenticatedUserId(username);

        Map<String, Object> map = new HashMap<>();
        map.put("userid", username);
        map.put("createStaffId", username);
        map.put("companyId", companyId);
        map.put("tableName", procdefKey);
        map.put("businessKey", businessKey);
        map.put("buttonType", buttonType);

        if(TableEnum.SALE_STUDENT_OFFER.key.equals(procdefKey)){
            map.put("submitReason", submitReason);
        }
        try {

            //表名 +公司id
            List<ProcessDefinition> processDefinition =
                    repositoryService.createProcessDefinitionQuery().processDefinitionKey(procdefKey).processDefinitionTenantId(companyId).orderByProcessDefinitionVersion().desc().list();
            if (GeneralTool.isEmpty(processDefinition)) {
                throw new RuntimeException("未找到相关流程");
            }

            //发起流程
            runtimeService.startProcessInstanceById(processDefinition.get(0).getId(), businessKey, map);
            Result<StudentOfferVo> studentOfferDtoResult = saleCenterClient.getStudentOfferDetail(Long.valueOf(businessKey));
            if (studentOfferDtoResult.isSuccess() && GeneralTool.isNotEmpty(studentOfferDtoResult.getData())) {
                StudentOfferVo studentOfferDetail = studentOfferDtoResult.getData();
                studentOfferDetail.setFkCancelOfferReasonId(fkCancelOfferReasonId);
                studentOfferDetail.setStatusWorkflow(2);
                studentOfferDetail.setFkStaffIdWorkflow(GetAuthInfo.getStaffId());
                saleCenterClient.updateStudentOffer(studentOfferDetail);
            }
            return true;
        } catch (Exception e) {
            log.error("发起学生申请方案流程异常", e);
            return false;
        }
    }
}
