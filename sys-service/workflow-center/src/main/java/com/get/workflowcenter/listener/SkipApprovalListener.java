package com.get.workflowcenter.listener;

import com.get.common.eunms.TableEnum;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.permissioncenter.vo.StaffVo;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.workflowcenter.component.IWorkFlowHelper;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.TaskService;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.delegate.TaskListener;
import org.activiti.engine.history.HistoricTaskInstance;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 跳过审批
 * @author: Hardy
 * @create: 2021/12/24 12:06
 * @verison: 1.0
 * @description:
 */
@Slf4j
public class SkipApprovalListener implements TaskListener {

    private Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void notify(DelegateTask delegateTask) {
        IPermissionCenterClient feignPermissionService = SpringUtil.getBean(IPermissionCenterClient.class);
        IReminderCenterClient feignReminderService = SpringUtil.getBean(IReminderCenterClient.class);
        ProcessEngine processEngine = SpringUtil.getBean(ProcessEngine.class);
        TaskService taskService = processEngine.getTaskService();
        IWorkFlowHelper workFlowHelper = SpringUtil.getBean(IWorkFlowHelper.class);
        //获取创建人信息
        StaffVo staffVo = workFlowHelper.getStaffDto(delegateTask);
        Long starterId = staffVo.getId();

        Set<Long> idSet = new HashSet<>(1);
        idSet.add(starterId);
        //查找对应的职位编号
        Map<Long, String> positionNum = feignPermissionService.getPositionNumByIds(idSet);
        String staffNum = positionNum.get(starterId);

        //todo 如果职位编号和流程描述一样并且没有指派任务受理人,则设置发起人为任务受理人？
        if (Objects.equals(staffNum, delegateTask.getDescription()) && GeneralTool.isEmpty(delegateTask.getAssignee())) {
            taskService.claim(delegateTask.getId(), String.valueOf(starterId));
            //同步删除提醒任务
            try {
                feignReminderService.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(delegateTask.getId()));
            } catch (Exception e) {
                log.error("提醒任务删除异常：" + e.getMessage());
            }
        }

        //查询前面的节点 若当前节点的审批人在前面的节点审批过，则直接通过
        //若是自己审批自己则直接通过
        //调整申请前的节点不能取
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        List<HistoricTaskInstance> historicTaskInstances = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(delegateTask.getProcessInstanceId())
                .orderByHistoricTaskInstanceStartTime().desc()
                .orderByTaskId().desc()
                .list();

        List<HistoricTaskInstance> historicTaskInstanceList = new ArrayList<>();
        if (GeneralTool.isNotEmpty(historicTaskInstances)) {
            for (HistoricTaskInstance historicTaskInstance : historicTaskInstances) {
                if (Objects.equals("调整申请", historicTaskInstance.getName())) {
                    break;
                } else {
                    historicTaskInstanceList.add(historicTaskInstance);
                }
            }

            if (GeneralTool.isNotEmpty(historicTaskInstanceList)) {
                Set<Long> staffIds = new HashSet<>();
                Set<String> nums = new HashSet<>();
                //调整申请前走过的节点，即正常发起走的所有节点
                for (HistoricTaskInstance historicTaskInstance : historicTaskInstanceList) {
//                    if ("部门领导审批".equals(delegateTask.getName())){
//                        ListResponseBo responseBo = feignPermissionService.getTopPositionStaffIds(staffVo.getFkCompanyId(), staffVo.getFkDepartmentId());
//                        JSONArray objects = JSONUtil.parseArray(responseBo.getDatas());
//                        List<String> staffIdList = JSONUtil.toList(objects, String.class);
//                        if ("直属上司审批".equals(historicTaskInstance.getName())){
//                            if (staffIdList.contains(historicTaskInstance.getAssignee())){
//                                if (staffIdList.size() == 1) {
//                                    HashMap<String, Object> map = new HashMap<>();
//                                    map.put("sequenceFlowsStatus", 1);
//                                    delegateTask.setAssignee(String.valueOf(starterId));
//                                    //自动通过审批
//                                    taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), "系统自动通过审核，" + delegateTask.getName() + "：已存在重复的审批人或本人即审批人。");
//                                    taskService.complete(delegateTask.getId(), map);
//                                    //同步删除提醒任务
//                                    try {
//                                        feignReminderService.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(delegateTask.getId()));
//                                    } catch (Exception e) {
//                                        log.error("提醒任务删除异常：" + e.getMessage());
//                                    }
//                                    return;
//                                } else {
//                                    //签取
//                                    taskService.claim(delegateTask.getId(), String.valueOf(starterId));
//                                    HashMap<String, Object> map = new HashMap<>();
//                                    map.put("sequenceFlowsStatus", 1);
//                                    delegateTask.setAssignee(String.valueOf(starterId));
//                                    //自动通过审批
//                                    taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), "系统自动通过审核，" + delegateTask.getName() + "：已存在重复的审批人或本人即审批人。");
//                                    taskService.complete(delegateTask.getId(), map);
//                                    //同步删除提醒任务
//                                    try {
//                                        feignReminderService.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(delegateTask.getId()));
//                                    } catch (Exception e) {
//                                        log.error("提醒任务删除异常：" + e.getMessage());
//                                    }
//                                    return;
//                                }
//                            }
//                        }
//                    }
                    //todo 如果流程描述和历史描述一样 并且 职位编号和流程描述不一样 并且受理人为空 则设置历史受理人为当前受理人？
                    if (Objects.equals(delegateTask.getDescription(), historicTaskInstance.getDescription()) && !Objects.equals(staffNum, delegateTask.getDescription()) && GeneralTool.isEmpty(delegateTask.getAssignee())
                            && delegateTask.getDescription() != null && historicTaskInstance.getDescription() != null) {
                        taskService.claim(delegateTask.getId(), String.valueOf(historicTaskInstance.getAssignee()));
                        //同步删除提醒任务
                        try {
                            feignReminderService.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(delegateTask.getId()));
                        } catch (Exception e) {
                            log.error("提醒任务删除异常：" + e.getMessage());
                        }
                    }

                    //职位编号
                    if (GeneralTool.isNotEmpty(historicTaskInstance.getDescription())) {
                        nums.add(historicTaskInstance.getDescription());
                    } else {
                        //审批人
                        if (GeneralTool.isNotEmpty(historicTaskInstance.getAssignee())) {
                            staffIds.add(Long.valueOf(historicTaskInstance.getAssignee()));
                        }
                    }

                }

                if ("部门领导审批".equals(delegateTask.getName())) {
                    staffIds.add(starterId);
                }
                if (GeneralTool.isNotEmpty(staffIds)) {
                    Map<Long, String> positionNumMap = feignPermissionService.getPositionNumByIds(staffIds);
                    if (GeneralTool.isNotEmpty(positionNumMap)) {
                        for (Long aLong : positionNumMap.keySet()) {
                            if (GeneralTool.isNotEmpty(positionNumMap.get(aLong))) {
                                nums.add(positionNumMap.get(aLong));
                            }
                        }
                    }
                }

                List<Long> staffIdsByPositionNums = feignPermissionService.getStaffIdsByPositionNums(nums);
                nums.removeIf(Objects::isNull);
                staffIdsByPositionNums.removeIf(Objects::isNull);
                //如果当前的节点包括在staffIdsByPositionNums或者nums里 直接跳过
                Long assigneeId = 0L;
                String num = "";
                if (GeneralTool.isNotEmpty(delegateTask.getAssignee())) {
                    assigneeId = Long.valueOf(delegateTask.getAssignee());
                }
                if (GeneralTool.isNotEmpty(delegateTask.getDescription())) {
                    num = delegateTask.getDescription();
                }
                //todo 如果该员工之前流程审核同意过 则自动审批？
                if (staffIdsByPositionNums.contains(assigneeId) || nums.contains(num) || assigneeId.equals(starterId)) {
                    HashMap<String, Object> map = new HashMap<>();
                    map.put("sequenceFlowsStatus", 1);
                    taskService.setVariableLocal(delegateTask.getId(), "approvalAction", 1);
                    //自动通过审批
                    taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), "系统自动通过审核，" + delegateTask.getName() + "：已存在重复的审批人或本人即审批人。");
                    taskService.complete(delegateTask.getId(), map);
                    //同步删除提醒任务
                    try {
                        feignReminderService.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(delegateTask.getId()));
                    } catch (Exception e) {
                        log.error("提醒任务删除异常：" + e.getMessage());
                    }
                    return;
                }
                if ("部门领导审批".equals(delegateTask.getName()) && delegateTask.getAssignee() == null) {
                    List<String> staffIdList = new ArrayList<>();
                    Result<List<Long>> result = feignPermissionService.getTopPositionStaffIds(staffVo.getFkCompanyId(), staffVo.getFkDepartmentId());
                    if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
                        for (Long datum : result.getData()) {
                            if (GeneralTool.isNotEmpty(datum)) {
                                staffIdList.add(String.valueOf(datum));
                            }
                        }
                    }
                    for (String s : staffIdList) {
                        if (staffIdsByPositionNums.contains(Long.valueOf(s))) {
                            if (staffIdList.size() == 1) {
                                HashMap<String, Object> map = new HashMap<>();
                                map.put("sequenceFlowsStatus", 1);
                                taskService.setVariableLocal(delegateTask.getId(), "approvalAction", 1);
                                delegateTask.setAssignee(s);
                                //自动通过审批
                                taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), "系统自动通过审核，" + delegateTask.getName() + "：已存在重复的审批人或本人即审批人。");
                                taskService.complete(delegateTask.getId(), map);
                                //同步删除提醒任务
                                try {
                                    feignReminderService.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(delegateTask.getId()));
                                } catch (Exception e) {
                                    log.error("提醒任务删除异常：" + e.getMessage());
                                }
                                return;
                            } else {
                                //签取
                                taskService.claim(delegateTask.getId(), "1");
                                HashMap<String, Object> map = new HashMap<>();
                                map.put("sequenceFlowsStatus", 1);
                                taskService.setVariableLocal(delegateTask.getId(), "approvalAction", 1);
                                delegateTask.setAssignee("1");
                                //自动通过审批
                                taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), "系统自动通过审核，" + delegateTask.getName() + "：已存在重复的审批人或本人即审批人。");
                                taskService.complete(delegateTask.getId(), map);
//                                System.out.println("==================>"+GeneralTool.toJson(delegateTask));
                                //同步删除提醒任务
                                try {
                                    feignReminderService.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(delegateTask.getId()));
                                } catch (Exception e) {
                                    log.error("提醒任务删除异常：" + e.getMessage());
                                }
                                return;


//                                //签取
//                                taskService.claim(delegateTask.getId(), "1");
//                                HashMap<String, Object> map = new HashMap<>();
//                                map.put("sequenceFlowsStatus", 1);
//                                taskService.setVariableLocal(delegateTask.getId(),"approvalAction",1);
//                                delegateTask.setAssignee(String.valueOf(starterId));
//                                //自动通过审批
//                                taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), "系统自动通过审核，" + delegateTask.getName() + "：已存在重复的审批人或本人即审批人。");
//                                taskService.complete(delegateTask.getId(), map);
//                                //同步删除提醒任务
//                                try {
//                                    feignReminderService.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(delegateTask.getId()));
//                                } catch (Exception e) {
//                                    log.error("提醒任务删除异常：" + e.getMessage());
//                                }
                            }
                        }
                    }
                }

            }

        } else {
            //如果审批人和发起人是同一个人，自动通过审批
            if (Objects.equals(delegateTask.getAssignee(), String.valueOf(starterId))) {
                HashMap<String, Object> map = new HashMap<>();
                map.put("sequenceFlowsStatus", 1);
                taskService.setVariableLocal(delegateTask.getId(), "approvalAction", 1);
                delegateTask.setAssignee(String.valueOf(starterId));
                //自动通过审批
                taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), "系统自动通过审核，" + delegateTask.getName() + "：已存在重复的审批人或本人即审批人。");
                taskService.complete(delegateTask.getId(), map);
                //同步删除提醒任务
                try {
                    feignReminderService.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(delegateTask.getId()));
                } catch (Exception e) {
                    log.error("提醒任务删除异常：" + e.getMessage());
                }
                return;
            } else {
//                Set<Long> starterIdSet = new HashSet<>(1);
//                starterIdSet.add(starterId);
//                Map<Long, String> starterPositionNumByIds = feignPermissionService.getPositionNumByIds(starterIdSet);
                if (GeneralTool.isNotEmpty(delegateTask.getAssignee())) {

                    Set<Long> assigneeId = new HashSet<>(1);
                    assigneeId.add(Long.valueOf(delegateTask.getAssignee()));
                    Map<Long, String> positionNumByIds = feignPermissionService.getPositionNumByIds(assigneeId);
                    String assigneeNum = positionNumByIds.get(Long.valueOf(delegateTask.getAssignee()));
                    if (Objects.equals(assigneeNum, staffNum)) {
                        HashMap<String, Object> map = new HashMap<>();
                        map.put("sequenceFlowsStatus", 1);
                        taskService.setVariableLocal(delegateTask.getId(), "approvalAction", 1);
                        delegateTask.setAssignee(String.valueOf(starterId));
                        //自动通过审批
                        taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), "系统自动通过审核，" + delegateTask.getName() + "：已存在重复的审批人或本人即审批人。");
                        taskService.complete(delegateTask.getId(), map);
                        //同步删除提醒任务
                        try {
                            feignReminderService.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(delegateTask.getId()));
                        } catch (Exception e) {
                            log.error("提醒任务删除异常：" + e.getMessage());
                        }
                        return;
                    }
                } else if (GeneralTool.isEmpty(delegateTask.getAssignee()) && "部门领导审批".equals(delegateTask.getName())) {
                    Result<List<Long>> result = feignPermissionService.getTopPositionStaffIds(staffVo.getFkCompanyId(), staffVo.getFkDepartmentId());
                    List<String> staffIdList = new ArrayList<>();
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                        staffIdList = BeanCopyUtils.copyListProperties(result.getData(),String::new);
                        for (Long datum : result.getData()) {
                            if (GeneralTool.isNotEmpty(datum)) {
                                staffIdList.add(String.valueOf(datum));
                            }
                        }
                    }
                    if (staffIdList.contains(String.valueOf(starterId))) {
                        if (staffIdList.size() == 1) {
                            HashMap<String, Object> map = new HashMap<>();
                            map.put("sequenceFlowsStatus", 1);
                            taskService.setVariableLocal(delegateTask.getId(), "approvalAction", 1);
                            delegateTask.setAssignee(String.valueOf(starterId));
                            //自动通过审批
                            taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), "系统自动通过审核，" + delegateTask.getName() + "：已存在重复的审批人或本人即审批人。");
                            taskService.complete(delegateTask.getId(), map);
                            //同步删除提醒任务
                            try {
                                feignReminderService.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(delegateTask.getId()));
                            } catch (Exception e) {
                                log.error("提醒任务删除异常：" + e.getMessage());
                            }
                            return;
                        } else {
                            //签取
                            taskService.claim(delegateTask.getId(), String.valueOf(starterId));
                            HashMap<String, Object> map = new HashMap<>();
                            map.put("sequenceFlowsStatus", 1);
                            taskService.setVariableLocal(delegateTask.getId(), "approvalAction", 1);
                            delegateTask.setAssignee(String.valueOf(starterId));
                            //自动通过审批
                            taskService.addComment(delegateTask.getId(), delegateTask.getProcessInstanceId(), "系统自动通过审核，" + delegateTask.getName() + "：已存在重复的审批人或本人即审批人。");
                            taskService.complete(delegateTask.getId(), map);
                            //同步删除提醒任务
                            try {
                                feignReminderService.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(delegateTask.getId()));
                            } catch (Exception e) {
                                log.error("提醒任务删除异常：" + e.getMessage());
                            }
                        }
                    }
                }
            }
        }
    }
}
