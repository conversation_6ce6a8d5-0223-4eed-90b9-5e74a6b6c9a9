package com.get.workflowcenter.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.cache.CacheNames;
import com.get.common.eunms.ErrorCodeEnum;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.eunms.TableEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.GetDateUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.cache.utils.CacheUtil;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.secure.UserInfo;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.CollectionUtil;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.dto.PaymentApplicationFormDto;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.entity.PaymentApplicationForm;
import com.get.financecenter.entity.PrepayApplicationForm;
import com.get.financecenter.entity.TravelClaimForm;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.officecenter.feign.IOfficeCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.ConfigVo;
import com.get.permissioncenter.vo.StaffVo;
import com.get.remindercenter.entity.EmailSenderQueue;
import com.get.remindercenter.entity.EmailTemplate;
import com.get.remindercenter.entity.RemindTemplate;
import com.get.remindercenter.enums.EmailTemplateEnum;
import com.get.remindercenter.feign.IReminderCenterClient;
import com.get.salecenter.feign.ISaleCenterClient;
import com.get.salecenter.vo.StudentOfferVo;
import com.get.workflowcenter.component.IWorkFlowHelper;
import com.get.workflowcenter.dto.ActHiProcinstDto;
import com.get.workflowcenter.dto.ActReProcdefDto;
import com.get.workflowcenter.dto.ActRuExecutionDto;
import com.get.workflowcenter.entity.WorkFlowPrepayApplicationForm;
import com.get.workflowcenter.mapper.ActHiProcinstMapper;
import com.get.workflowcenter.mapper.ActReProcdefMapper;
import com.get.workflowcenter.mapper.ActRuExecutionMapper;
import com.get.workflowcenter.mapper.ActRuTaskMapper;
import com.get.workflowcenter.service.IActRuIdentitylinkService;
import com.get.workflowcenter.service.IWorkFlowService;
import com.get.workflowcenter.service.IWorkFlowTypeService;
import com.get.workflowcenter.vo.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.FlowNode;
import org.activiti.bpmn.model.SequenceFlow;
import org.activiti.engine.*;
import org.activiti.engine.delegate.DelegateTask;
import org.activiti.engine.history.*;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.activiti.engine.repository.Deployment;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.Execution;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.runtime.ProcessInstanceQuery;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.IdentityLink;
import org.activiti.engine.task.Task;
import org.activiti.image.ProcessDiagramGenerator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.get.common.utils.GetDateUtil.dateDiff;
import static com.get.core.tool.utils.DateUtil.now;


/**
 * <AUTHOR> by Ron
 * @date 2020/5/17 18:00
 */
@Service
@Slf4j
public class WorkFlowServiceImpl implements IWorkFlowService {

    private static final String cache_key = "task_count";

    /**
     * 运行时服务
     */
    @Autowired
    private RuntimeService runtimeService;

    /**
     * 用户任务服务
     */

    @Autowired
    private TaskService taskService;

    /**
     * 管理流程定义
     */
    @Autowired
    private RepositoryService repositoryService;

    /**
     * 任务管理
     */
    @Autowired
    private HistoryService historyService;

    /**
     * 工作流引擎
     */
    @Autowired
    private ProcessEngine processEngine;

    @Resource
    private ActReProcdefMapper actReProcdefMapper;
    @Resource
    private IWorkFlowTypeService workFlowTypeService;
    @Resource
    private ActRuExecutionMapper ruExecutionMapper;
    @Resource
    private ActRuTaskMapper ruTaskMapper;
    @Resource
    private ActHiProcinstMapper actHiProcinstMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;
    @Resource
    private IWorkFlowService iWorkFlowService;
    @Resource
    private IOfficeCenterClient officeCenterClient;
    @Resource
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IReminderCenterClient reminderCenterClient;
    @Resource
    private IActRuIdentitylinkService actRuIdentitylinkService;
    @Resource
    private IWorkFlowHelper workFlowHelper;
    /**
     * 返回图片流
     *
     * @param processInstanceId
     * @return
     * @throws Exception
     */
    @Override
    public byte[] getProcessImage(String processInstanceId, String key) {
        //  获取历史流程实例
        HistoricProcessInstance historicProcessInstance = historyService
                .createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .processDefinitionKey(key)
                .orderByProcessInstanceStartTime()
                .asc()
                .singleResult();
        if (historicProcessInstance == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("history_process_null"));
        } else {
            // 获取流程定义
            ProcessDefinitionEntity processDefinition = (ProcessDefinitionEntity) repositoryService
                    .getProcessDefinition(historicProcessInstance.getProcessDefinitionId());

            // 获取流程历史中已执行节点，并按照节点在流程中执行先后顺序排序
            List<HistoricActivityInstance> historicActivityInstanceList = historyService
                    .createHistoricActivityInstanceQuery().processInstanceId(processInstanceId)
                    .orderByHistoricActivityInstanceStartTime()
//                    .orderByHistoricActivityInstanceId()
                    .asc().list();
            // 已执行的节点ID集合
            List<String> executedActivityIdList = new ArrayList<>();
            @SuppressWarnings("unused") int index = 1;
            log.info("获取已经执行的节点ID");
//            for (HistoricActivityInstance activityInstance : historicActivityInstanceList) {
//                executedActivityIdList.add(activityInstance.getActivityId());
//                index++;
//            }
            executedActivityIdList.add(historicActivityInstanceList.get(historicActivityInstanceList.size() - 1).getActivityId());
            System.out.println("已执行的节点ID集合" + executedActivityIdList);
            // 获取流程图图像字符流
            BpmnModel bpmnModel = repositoryService.getBpmnModel(processDefinition.getId());
            System.out.println("获取流程图图像字符流" + bpmnModel);
            //已执行flow的集和
            List<String> executedFlowIdList = getHighLightedFlows(bpmnModel, historicActivityInstanceList);
            System.out.println("已执行flow的集和" + executedFlowIdList);

            ProcessDiagramGenerator processDiagramGenerator = processEngine.getProcessEngineConfiguration().getProcessDiagramGenerator();
            System.out.println("获取流程" + processDiagramGenerator);
            InputStream imageStream = processDiagramGenerator.generateDiagram(bpmnModel, "png", executedActivityIdList, executedFlowIdList, "黑体", "黑体", "黑体", null, 1.0);

            try {
                byte[] buffer = new byte[imageStream.available()];
                imageStream.read(buffer);
                imageStream.close();
                return buffer;
            } catch (Exception e) {
                e.printStackTrace();
                log.info("执行异常:" + e.getMessage());
                throw new GetServiceException(LocaleMessageUtils.getMessage("exception_message") + e.getMessage());
            }
        }
    }


    /**
     * 获取已经流转的线
     *
     * @param bpmnModel
     * @param historicActivityInstances
     * @return
     */
    @Override
    public List<String> getHighLightedFlows(BpmnModel bpmnModel, List<HistoricActivityInstance> historicActivityInstances) {
        // 高亮流程已发生流转的线id集合
        List<String> highLightedFlowIds = new ArrayList<>();
        // 全部活动节点
        List<FlowNode> historicActivityNodes = new ArrayList<>();
        // 已完成的历史活动节点
        List<HistoricActivityInstance> finishedActivityInstances = new ArrayList<>();

        for (HistoricActivityInstance historicActivityInstance : historicActivityInstances) {
            FlowNode flowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(historicActivityInstance.getActivityId(), true);
            historicActivityNodes.add(flowNode);
            if (historicActivityInstance.getEndTime() != null) {
                finishedActivityInstances.add(historicActivityInstance);
            }
        }

        FlowNode currentFlowNode = null;
        FlowNode targetFlowNode = null;
        // 遍历已完成的活动实例，从每个实例的outgoingFlows中找到已执行的0
        for (HistoricActivityInstance currentActivityInstance : finishedActivityInstances) {
            // 获得当前活动对应的节点信息及outgoingFlows信息
            currentFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(currentActivityInstance.getActivityId(), false);


            //这里有2条线都搞了。
            List<SequenceFlow> sequenceFlows = currentFlowNode.getOutgoingFlows();
            //   Collections.sort(sequenceFlows);

            /**
             * 遍历outgoingFlows并找到已已流转的 满足如下条件认为已已流转： 1.当前节点是并行网关或兼容网关，则通过outgoingFlows能够在历史活动中找到的全部节点均为已流转 2.当前节点是以上两种类型之外的，通过outgoingFlows查找到的时间最早的流转节点视为有效流转
             * exclusiveGateway
             */
            if ("parallelGateway".equals(currentActivityInstance.getActivityType()) || "inclusiveGateway".equals(currentActivityInstance.getActivityType())) {
                // 遍历历史活动节点，找到匹配流程目标节点的
                for (SequenceFlow sequenceFlow : sequenceFlows) {
                    targetFlowNode = (FlowNode) bpmnModel.getMainProcess().getFlowElement(sequenceFlow.getTargetRef(), true);
                    if (historicActivityNodes.contains(targetFlowNode)) {
                        highLightedFlowIds.add(targetFlowNode.getId());
                    }
                }
            } else {
                List<Map<String, Object>> tempMapList = new ArrayList<>();
                for (SequenceFlow sequenceFlow : sequenceFlows) {
//                    for (HistoricActivityInstance historicActivityInstance : historicActivityInstances) {
//                        if (historicActivityInstance.getActivityId().equals(sequenceFlow.getTargetRef())) {
//
//                            Map<String, Object> map = new HashMap<>();
//                            map.put("highLightedFlowId", sequenceFlow.getId());
//                            map.put("highLightedFlowStartTime", historicActivityInstance.getStartTime().getTime());
//                            tempMapList.add(map);
//                        }
//                    }
                    for (int i = 0; i < historicActivityInstances.size(); i++) {
                        if (i == historicActivityInstances.size() - 1) {
                            break;
                        } else {
                            if (Objects.equals(historicActivityInstances.get(i).getActivityId(), sequenceFlow.getSourceRef())
                                    && Objects.equals(historicActivityInstances.get(i + 1).getActivityId(), sequenceFlow.getTargetRef())) {
                                Map<String, Object> map = new HashMap<>();
                                map.put("highLightedFlowId", sequenceFlow.getId());
                                map.put("highLightedFlowStartTime", historicActivityInstances.get(i).getStartTime().getTime());
                                tempMapList.add(map);
                                highLightedFlowIds.add(sequenceFlow.getId());
                            }
                        }
                    }
                }


//                if (!CollectionUtils.isEmpty(tempMapList)) {
//                    // 遍历匹配的集合，取得开始时间最早的一个
//                    long earliestStamp = 0L;
//                    String highLightedFlowId = null;
//                    for (Map<String, Object> map : tempMapList) {
//                        long highLightedFlowStartTime = Long.valueOf(map.get("highLightedFlowStartTime").toString());
//                        if (earliestStamp == 0 || earliestStamp <= highLightedFlowStartTime) {
//                            highLightedFlowId = map.get("highLightedFlowId").toString();
//                            earliestStamp = highLightedFlowStartTime;
//                        }
//                    }
//
//                    highLightedFlowIds.add(highLightedFlowId);
//                }

            }

        }
        return highLightedFlowIds;

    }


    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActReProcdefDto>
     * @Description :流程管理或者发起事务中的所有流程
     * @Param [procdefVo, page]
     * <AUTHOR>
     */
    @Override
    public List<ActReProcdefVo> getProcefList(ActReProcdefDto procdefVo, Page page) {
        if (GeneralTool.isNotEmpty(procdefVo)) {
            //查询条件-流程类型
            if (GeneralTool.isNotEmpty(procdefVo.getCategory())) {
                //查出该类型对应流程部署的ids
                List<Deployment> deployments = repositoryService.createDeploymentQuery().deploymentCategory(procdefVo.getCategory()).list();
                List<String> deploymentIds = deployments.stream().map(Deployment::getId).collect(Collectors.toList());
                //属于这些流程部署ids
                procdefVo.setDeploymentIds(deploymentIds);
            }
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<ActReProcdefDto> actReProcdefVos = actReProcdefMapper.getProcefList(procdefVo);
//        page.restPage(actReProcdefVos);

        List<ActReProcdefVo> actReProcdefVos = actReProcdefMapper.getProcefList(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), procdefVo);
        List<ActReProcdefVo> actReProcdefVoList = new ArrayList<>();
//        Map<Long, String> companyNameMap = permissionCenterClient.getCompanyNamesByIds(actReProcdefVos.stream().filter(actReProcdefDto -> GeneralTool.isNotEmpty(actReProcdefDto.getTenantId())).map(actReProcdefDto -> Long.valueOf(actReProcdefDto.getTenantId())).collect(Collectors.toSet()));
        Map<Long, String> companyNameMap = new HashMap<>();
        Result<Map<Long, String>> result = permissionCenterClient.getCompanyNamesByIds(actReProcdefVos.stream().filter(actReProcdefVo -> GeneralTool.isNotEmpty(actReProcdefVo.getTenantId())).map(actReProcdefVo -> Long.valueOf(actReProcdefVo.getTenantId())).collect(Collectors.toSet()));
        if (result.isSuccess() && result.getData() != null) {
            companyNameMap = result.getData();
        }
        for (ActReProcdefVo actReProcdefVo : actReProcdefVos) {
            Deployment deployment = repositoryService.createDeploymentQuery().deploymentId(actReProcdefVo.getDeploymentId()).singleResult();
            if (GeneralTool.isNotEmpty(deployment.getCategory())) {
                String workFlowTypeName = workFlowTypeService.getWorkFlowTypeNameById(Long.valueOf(deployment.getCategory()));
                actReProcdefVo.setWorkFlowTypeName(workFlowTypeName);
            }
            actReProcdefVo.setDeployTime(deployment.getDeploymentTime());
            actReProcdefVoList.add(actReProcdefVo);

            //设置公司名字
            if (GeneralTool.isNotEmpty(actReProcdefVo.getTenantId())) {
//                ResponseBo companyNameById = feignPermissionService.getCompanyNameById(Long.valueOf(actReProcdefVo.getTenantId()));
//                actReProcdefVo.setCompanyName(String.valueOf(companyNameById.getData()));
                actReProcdefVo.setCompanyName(companyNameMap.get(Long.valueOf(actReProcdefVo.getTenantId())));
            }
        }
        return actReProcdefVoList;
    }

    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActRuExecutionDto>
     * @Description :流程跟踪中的所有正在执行流程
     * @Param [actRuExecutionDto, page]
     * <AUTHOR>
     */
    @Override
    public List<ActRuExecutionVo> getExecutionList(ActRuExecutionDto actRuExecutionDto, Page page) {
        if (page.getShowCount() == 0 || page.getShowCount() == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("Paging_error"));
        }

        UserInfo staff = GetAuthInfo.getUser();
        String username = String.valueOf(staff.getStaffId());

        List<ActRuExecutionVo> executions = null;
        if (staff.getIsAdmin()) {
            String pkey = "";
            if (GeneralTool.isNotEmpty(actRuExecutionDto.getKey())) {
                pkey = "%" + actRuExecutionDto.getKey() + "%";
            }
//            PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//
//            List<ActRuExecutionDto> executionList = ruExecutionMapper.getExecutionList(pkey, actRuExecutionDto.getExeid(), actRuExecutionDto.getProcInstId());
//            executions = executionList;
//            page.restPage(executionList);

            List<ActRuExecutionVo> executionList = ruExecutionMapper.getExecutionList(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), pkey, actRuExecutionDto.getExeid(), actRuExecutionDto.getProcInstId());
            executions = executionList;

        } else {
            String pkey = "";
            if (GeneralTool.isNotEmpty(actRuExecutionDto.getKey())) {
                pkey = "%" + actRuExecutionDto.getKey() + "%";
            }
//            PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//            List<ActRuExecutionDto> executionList = ruExecutionMapper.getExecutionListByNormalUser(pkey, actRuExecutionDto.getExeid(), actRuExecutionDto.getProcInstId(), username);
//            executions = executionList;
//            page.restPage(executionList);
            List<ActRuExecutionVo> executionList = ruExecutionMapper.getExecutionListByNormalUser(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), pkey, actRuExecutionDto.getExeid(), actRuExecutionDto.getProcInstId(), username);
            executions = executionList;
        }
        return executions;

    }


    /**
     * @return void
     * @Description :删除流程定义
     * @Param [id]
     * <AUTHOR>
     */
    @Override
    public void deleteDeployment(String id) throws GetServiceException {
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(id).singleResult();
        if (processDefinition != null) {
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processDefinitionId(processDefinition.getId()).singleResult();
            if (historicProcessInstance != null) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("process_instance_exist"));
            }
            repositoryService.deleteDeployment(processDefinition.getDeploymentId(), true);
            return;
        } else {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
    }


    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActRuTaskVo>
     * @Description :待办事务的查询
     * @Param [procdefVo, page]
     * <AUTHOR>
     */
    @Override
    public List<ActRuTaskVo> getToDoSelect(ActReProcdefDto procdefVo, Page page) {

        //当前登录人
//        StaffVo staff = StaffContext.getStaff();
//        String username = String.valueOf(staff.getId());
        String username = String.valueOf(GetAuthInfo.getStaffId());
        if (page.getShowCount() == 0 || page.getShowCount() == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("Paging_error"));
        }

        List<Long> staffIdByNames = null;
        List<ActRuTaskVo> actRuTaskVos = new ArrayList<>();
        if (GeneralTool.isNotEmpty(procdefVo.getStartStaffName())) {
//            staffIdByNames = permissionCenterClient.getStaffIdByName(procdefVo.getStartStaffName());
            Result<List<Long>> result = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(procdefVo.getStartStaffName());
            if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
                staffIdByNames = result.getData();
            }
            //名字条件没找到就直接结束
            if (GeneralTool.isEmpty(staffIdByNames)) {
//                page.restPage(actRuTaskVos);//这里需要测试 111111
                return null;
            }
        }

        String pname = "";
        if (GeneralTool.isNotEmpty(procdefVo.getName())) {
            pname = "%" + procdefVo.getName() + "%";
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        actRuTaskVos = ruTaskMapper.getToDoSelectListByNormalUser(procdefVo.getCategory(), pname, username, staffIdByNames);
//        PageInfo<ActRuTaskVo> pageInfo = new PageInfo<>(actRuTaskVos);
        IPage<ActRuTaskVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        actRuTaskVos = ruTaskMapper.getToDoSelectListByNormalUser(pages, procdefVo.getCategory(), pname, username, staffIdByNames);
        page.setAll((int) pages.getTotal());

        if (actRuTaskVos != null && actRuTaskVos.size() > 0) {
            Set<Long> collect = actRuTaskVos.stream().filter(data -> data.getFkStaffName() != null).map(data -> Long.valueOf(data.getFkStaffName())).collect(Collectors.toSet());
//            Map<Long, String> staffNameMap = permissionCenterClient.getStaffNameMap(collect);
            Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(collect);
            Map<Long, String> staffNameMap = new HashMap<>();
            if (result.isSuccess() && result.getData() != null) {
                staffNameMap = result.getData();
            }
            //111111 这个逻辑要重点测试，分页总数减1是什么原因
            for (int i = 0; i < actRuTaskVos.size(); i++) {
                ActRuTaskVo actRuTaskVo = actRuTaskVos.get(i);
                Object statusToDo = taskService.getVariable(actRuTaskVo.getId(), "statusToDo");
                if (GeneralTool.isNotEmpty(statusToDo)) {
                    if (statusToDo.equals(0)) {
                        actRuTaskVos.remove(i);
                        //回显数据显示-1
                        page.setAll((int) pages.getTotal()-1);//111111
                    }
                }
                if (actRuTaskVo.getFkStaffName() != null) {
                    actRuTaskVo.setFkStaffName(staffNameMap.get(Long.valueOf(actRuTaskVo.getFkStaffName())));
                }
            }
        }
//        page.setAll(actRuTaskVos.size());
        return actRuTaskVos;
    }

    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActRuTaskVo>
     * @Description :待签事务的查询
     * @Param [procdefVo, page]
     * <AUTHOR>
     */
    @Override
    public List<ActRuTaskVo> getSignedSelect(ActReProcdefDto procdefVo, Page page) {

        if (page.getShowCount() == 0 || page.getShowCount() == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("Paging_error"));
        }
        //当前登录人
//        StaffVo staff = StaffContext.getStaff();
        String username = String.valueOf(GetAuthInfo.getStaffId());
        List<ActRuTaskVo> actRuTaskDtolist = new ArrayList<>();
        Map<Long, String> staffNameMap = null;
        List<Long> staffIdByNames = null;

        String pname = "";
        if (GeneralTool.isNotEmpty(procdefVo.getStartStaffName())) {
//            staffIdByNames = permissionCenterClient.getStaffIdByName(procdefVo.getStartStaffName());
            Result<List<Long>> result = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(procdefVo.getStartStaffName());
            if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
                staffIdByNames = result.getData();
                if (CollectionUtil.isEmpty(staffIdByNames)) {
                    return null;
                }
            }
//            //名字条件没找到就直接结束
//            if (GeneralTool.isEmpty(staffIdByNames)) {
//                page.restPage(actRuTaskDtolist);
//                return null;
//            }
        }

        if (GeneralTool.isNotEmpty(procdefVo.getName())) {
            pname = "%" + procdefVo.getName() + "%";
        }
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        actRuTaskDtolist = ruTaskMapper.getSignedListByNormalUser(procdefVo.getCategory(), pname, username, staffIdByNames);
//        page.restPage(actRuTaskDtolist);
//        if (actRuTaskDtolist.size() > 0 && actRuTaskDtolist != null) {
//            staffNameMap = permissionCenterClient.getStaffNameMap(actRuTaskDtolist.stream().filter(data -> data.getFkStaffName() != null)
//                    .map(data -> Long.valueOf(data.getFkStaffName()))
//                    .collect(Collectors.toSet()));
//            for (ActRuTaskVo hpi : actRuTaskDtolist) {
//                if (hpi.getFkStaffName() != null) {
//                    hpi.setFkStaffName(staffNameMap.get(Long.valueOf(hpi.getFkStaffName())));
//                }
//            }
//        }
        IPage<ActRuTaskVo> actRuTaskDtoIPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        actRuTaskDtolist = ruTaskMapper.getSignedListByNormalUser(actRuTaskDtoIPage, procdefVo.getCategory(), pname, username, staffIdByNames);
        if (actRuTaskDtolist.size() > 0 && actRuTaskDtolist != null) {
            Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(actRuTaskDtolist.stream().filter(data -> data.getFkStaffName() != null)
                    .map(data -> Long.valueOf(data.getFkStaffName()))
                    .collect(Collectors.toSet()));
            if (result.isSuccess() && result.getData() != null) {
                staffNameMap = result.getData();
                for (ActRuTaskVo hpi : actRuTaskDtolist) {
                    if (hpi.getFkStaffName() != null) {
                        hpi.setFkStaffName(staffNameMap.get(Long.valueOf(hpi.getFkStaffName())));
                    }
                }
            }
        }
        page.setAll((int) actRuTaskDtoIPage.getTotal());
        return actRuTaskDtolist;
    }


    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActHiActinstDto>
     * @Description :办结事务查询和搜索
     * @Param [procdefVo, page]
     * <AUTHOR>
     */
    @Override
    public List<ActHiProcinstVo> getCompleteTask(ActHiProcinstDto hiProcinstVo, Page page) {

//        StaffVo staff = StaffContext.getStaff();
        UserInfo staff = GetAuthInfo.getUser();
        if (staff == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("get_userInfo_fail"));
        }

        String username = String.valueOf(staff.getStaffId());
        if (page.getShowCount() == 0 || page.getShowCount() == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("Paging_error"));
        }

        List<ActHiProcinstVo> actHiProcinstVoList = new ArrayList<>();
        List<Long> staffIdByNames = null;
        //超级管理员能看到全部
        if (staff.getIsAdmin()) {
            String pname = "";
            if (GeneralTool.isNotEmpty(hiProcinstVo.getProcName())) {
                pname = "%" + hiProcinstVo.getProcName() + "%";
            }

            if (GeneralTool.isNotEmpty(hiProcinstVo.getStartStaffName())) {
//                staffIdByNames = permissionCenterClient.getStaffIdByName(hiProcinstVo.getStartStaffName());
//                //名字条件没找到就直接结束
//                if (GeneralTool.isEmpty(staffIdByNames)) {
//                    page.restPage(actHiProcinstVoList);
//                    return null;
//                }
                Result<List<Long>> result = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(hiProcinstVo.getStartStaffName());
                if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
                    staffIdByNames = result.getData();
                    if (CollectionUtil.isEmpty(staffIdByNames)) {
                        return null;
                    }
                }
            }

//            PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//            actHiProcinstVoList = actHiProcinstMapper.getCompleteTaskList(hiProcinstVo.getCategory(), pname, staffIdByNames);
//            page.restPage(actHiProcinstVoList);

            IPage<ActHiProcinstVo> actHiProcinstDtoIPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            actHiProcinstVoList = actHiProcinstMapper.getCompleteTaskList(actHiProcinstDtoIPage, hiProcinstVo.getCategory(), pname, staffIdByNames);
            page.setAll((int) actHiProcinstDtoIPage.getTotal());

        } else {
            if (GeneralTool.isNotEmpty(hiProcinstVo.getStartStaffName())) {
//                staffIdByNames = permissionCenterClient.getStaffIdByName(hiProcinstVo.getStartStaffName());
//                //名字条件没找到就直接结束
//                if (GeneralTool.isEmpty(staffIdByNames)) {
//                    page.restPage(actHiProcinstVoList);
//                    return null;
//                }
                Result<List<Long>> result = permissionCenterClient.getStaffIdsByNameKeyOrEnNameKey(hiProcinstVo.getStartStaffName());
                if (result.isSuccess() && CollectionUtil.isNotEmpty(result.getData())) {
                    staffIdByNames = result.getData();
                    if (CollectionUtil.isEmpty(staffIdByNames)) {
                        return null;
                    }
                }
            }
            //普通用户
            String pname = "";
            if (GeneralTool.isNotEmpty(hiProcinstVo.getProcName())) {
                pname = "%" + hiProcinstVo.getProcName() + "%";
            }
//            PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//            actHiProcinstVoList = actHiProcinstMapper.getCompleteTaskListByNormalUser(hiProcinstVo.getCategory(), pname, username, staffIdByNames);
//            page.restPage(actHiProcinstVoList);

            IPage<ActHiProcinstVo> actHiProcinstDtoIPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            actHiProcinstVoList = actHiProcinstMapper.getCompleteTaskListByNormalUser(actHiProcinstDtoIPage, hiProcinstVo.getCategory(), pname, username, staffIdByNames);
            page.setAll((int) actHiProcinstDtoIPage.getTotal());
        }
        if (actHiProcinstVoList != null && actHiProcinstVoList.size() > 0) {
            Set<Long> collect = actHiProcinstVoList.stream().filter(data -> data.getStartByName() != null).map(data -> Long.parseLong(data.getStartByName()))
                    .collect(Collectors.toSet());
            Result<Map<Long, String>> result = permissionCenterClient.getStaffNameMap(collect);
            Map<Long, String> staffNameMap = new HashMap<>();
            if (result.isSuccess() && result.getData() != null) {
                staffNameMap = result.getData();
            }
            for (ActHiProcinstVo hpi : actHiProcinstVoList) {
                if (GeneralTool.isEmpty(hpi.getDeleteReason())) {
                    hpi.setActName(hpi.getActName());
                } else {
                    hpi.setActName(hpi.getDeleteReason());
                }
                if (hpi.getStartByName() != null) {
                    hpi.setStartByName(staffNameMap.get(Long.parseLong(hpi.getStartByName())));
                }
            }
        }
        return actHiProcinstVoList;
    }


    /**
     * @return java.util.List<com.get.workflowcenter.vo.ActHiProcinstVo>
     * @Description :
     * @Param [hiProcinstVo, page]
     * <AUTHOR>
     * @Description :已发事务查询
     */
    @Override
    public List<ActHiProcinstVo> getRunningTask(ActHiProcinstDto hiProcinstVo, Page page) {

//        StaffVo staff = StaffContext.getStaff();
        UserInfo staff = GetAuthInfo.getUser();
        if (staff == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("get_userInfo_fail"));
        }

        String username = String.valueOf(staff.getStaffId());


        if (page.getShowCount() == 0 || page.getShowCount() == null) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("Paging_error"));
        }
        List<ActHiProcinstVo> historicProcessInstanceList = new ArrayList<>();

        if (staff.getIsAdmin()) {
            String pname = "";
            if (GeneralTool.isNotEmpty(hiProcinstVo.getProcName())) {
                pname = "%" + hiProcinstVo.getProcName() + "%";
            }
//            PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//            historicProcessInstanceList = actHiProcinstMapper.getRunningTaskList(hiProcinstVo.getCategory(), pname);
//            page.restPage(historicProcessInstanceList);
            IPage<ActHiProcinstVo> actHiProcinstDtoIPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            historicProcessInstanceList = actHiProcinstMapper.getRunningTaskList(actHiProcinstDtoIPage, hiProcinstVo.getCategory(), pname);
            page.setAll((int) actHiProcinstDtoIPage.getTotal());
        } else {
            //非管理员用户

            String pname = "";
            if (GeneralTool.isNotEmpty(hiProcinstVo.getProcName())) {
                pname = "%" + hiProcinstVo.getProcName() + "%";
            }
//            PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//            historicProcessInstanceList = actHiProcinstMapper.getRunningTaskListByNormalUser(hiProcinstVo.getCategory(), pname, username);
//            page.restPage(historicProcessInstanceList);

            IPage<ActHiProcinstVo> actHiProcinstDtoIPage = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
            historicProcessInstanceList = actHiProcinstMapper.getRunningTaskListByNormalUser(actHiProcinstDtoIPage, hiProcinstVo.getCategory(), pname, username);
            page.setAll((int) actHiProcinstDtoIPage.getTotal());
        }

        return historicProcessInstanceList;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveCommentAndComplete(String taskId, String comment, String outcome) {
        Authentication.setAuthenticatedUserId(String.valueOf(GetAuthInfo.getStaffId()));
        // 根据任务id查出任务
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();

        if (GeneralTool.isEmpty(task)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("task_obj_null"));
        }
        // 得到任务中的流程实例id
        String pId = task.getProcessInstanceId();

        String businessKey = "";
        LeaveApplicationForm leaveApplicationForm = null;
        if (task.getProcessDefinitionId().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)&&"1".equals(outcome)){
            //如果是工休单流程
            List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery().processInstanceId(pId).list();
            for (ProcessInstance processInstance : processInstances) {
                if (GeneralTool.isNotEmpty(processInstance.getBusinessKey())){
                    businessKey = processInstance.getBusinessKey();
                    break;
                }
            }
            leaveApplicationForm = officeCenterClient.getLeaveApplicationForm(Long.valueOf(businessKey)).getData();
            //上个节点的审批时间 （用下个节点的创建时间表示上个节点的审批时间）
            Date lastApprovalTime = task.getCreateTime();
            //验证能否审批
            validateDelay(leaveApplicationForm,lastApprovalTime,comment);
        }

        if (GeneralTool.isEmpty(comment)) {
            if ("1".equals(outcome)) {
                //同意
                comment = LocaleMessageUtils.getMessage("default_agreement_message");

            } else if ("0".equals(outcome)) {
                //拒绝
                comment = LocaleMessageUtils.getMessage("default_disagreement_message");
            }
        }
        // 添加批注
        taskService.addComment(taskId, pId, comment);
        // 流程推进 没有条件信息表示正常推进，有则是按条件走分支
            Map<String, Object> map = new HashMap<>();
            if (GeneralTool.isEmpty(outcome)) {
                taskService.complete(taskId);
                try {
                    //完成了任务就把提醒删除
                    reminderCenterClient.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(taskId));
                } catch (Exception e) {
                    log.error("提醒任务删除异常----------------" + e.getMessage());
                }
            } else {
                // 新增公司类型判断逻辑
                StaffVo staff = workFlowHelper.getStaffDto((DelegateTask) task);
                Long companyId = staff.getFkCompanyId();
                Result<String> result = permissionCenterClient.getCompanyNameById(companyId);
                if (result.isSuccess() && result.getData() != null){
                    map.put("fkCompanyName",result.getData());
                }
//                Long fkCompanyId = leaveApplicationForm.getFkCompanyId();


                map.put("sequenceFlowsStatus", outcome);
                if ("0".equals(outcome)) {
                    taskService.setVariableLocal(taskId, "approvalAction", 0);
                } else {
                    if (GeneralTool.isNotEmpty(task.getProcessDefinitionId())&&task.getProcessDefinitionId().contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)){
                        //判断是否进入资料审批员
//                    String businessKey = "";
//                    List<ProcessInstance> processInstances = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).list();
//                    for (ProcessInstance processInstance : processInstances) {
//                        if (GeneralTool.isNotEmpty(processInstance.getBusinessKey())){
//                            businessKey = processInstance.getBusinessKey();
//                            break;
//                        }
//                    }
//                    leaveApplicationForm = officeCenterClient.getLeaveApplicationForm(Long.valueOf(businessKey)).getData();
                        String typeKey = officeCenterClient.getLeaveTypeKeyById(leaveApplicationForm.getId());
                        if (ProjectKeyEnum.DISEASE_VACATION.key.equals(typeKey)){
                            Boolean b = officeCenterClient.hasMediaAndAttach(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key,leaveApplicationForm.getId()).getData();
                            if (!b){
                                //进入节点
                                map.put("isHavingAttachments", 0);

                            }else {
                                map.put("isHavingAttachments", 1);
                            }
                        }else {
                            map.put("isHavingAttachments", 1);
                        }
                    }

                    taskService.setVariableLocal(taskId, "approvalAction", 1);
                }
                taskService.complete(taskId, map);
                try {
                    //完成了任务就把提醒删除
                    reminderCenterClient.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(taskId));
                } catch (Exception e) {
                    log.error("提醒任务删除异常----------------" + e.getMessage());
                }
                if ("0".equals(outcome)) {
                    //审批拒绝也应该在审批人的待办列表中 所以"0"-->"1"
                    iWorkFlowService.getStatusToDoSingle("1", pId);
                }
            }

        try {
            HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(taskId).singleResult();
            if (GeneralTool.isNotEmpty(historicTaskInstance)){
                ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                        .processDefinitionId(historicTaskInstance.getProcessDefinitionId())
                        .singleResult();

                if (TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key.equals(processDefinition.getKey())) {
                    List<HistoricIdentityLink> identityLinkList = historyService.getHistoricIdentityLinksForProcessInstance(pId);
                    String starterId = null;
                    for (HistoricIdentityLink identityLink : identityLinkList) {
                        if (Objects.equals("starter", identityLink.getType())) {
                            starterId = identityLink.getUserId();
                            break;
                        }
                    }
                    if (GeneralTool.isNotEmpty(starterId)){
                        //更新缓存
                        CacheUtil.evict(CacheNames.TASK_CACHE, starterId, cache_key);
                    }
                }
            }
        }catch (Exception e) {
            log.error(e.getMessage());
        }


    }

    /**
     * 验证能否审批
     * @param leaveApplicationForm
     */
    private void validateDelay(LeaveApplicationForm leaveApplicationForm,Date lastApprovalTime,String comment) {
        if (GeneralTool.isEmpty(SecureUtil.getStaffId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("not_logged_in"));
        }
        //白名单审批 跳过
        ConfigVo staffWhiteListConfig = permissionCenterClient.getConfigByKey(ProjectKeyEnum.LEAVE_APPLICATION_FORM_SUBMIT_WHITE_LIST.key).getData();
        String value1 = staffWhiteListConfig.getValue1();
        List<Long> staffIds = JSONObject.parseArray(value1, Long.class);
        if (staffIds.contains(SecureUtil.getStaffId())){
            return;
        }

//        ConfigVo configDto = permissionCenterClient.getConfigByKey(ProjectKeyEnum.LEAVE_APPLICATION_FORM_SUBMIT_LIMIT.key).getData();
//        String configJson = configDto.getValue1();
//        JSONObject configJsonObject = JSON.parseObject(configJson);
//        JSONArray configArray = null;
//        if (GeneralTool.isEmpty(SecureUtil.getFkCompanyId())||(!SecureUtil.getFkCompanyId().equals(2L)&&!SecureUtil.getFkCompanyId().equals(3L))){
//            configArray = configJsonObject.getJSONArray("OTHER");
//        }
//        if(SecureUtil.getFkCompanyId().equals(2L)){
//            configArray = configJsonObject.getJSONArray("GEA");
//        }else if (SecureUtil.getFkCompanyId().equals(3L)){
//            configArray = configJsonObject.getJSONArray("IAE");
//        }
//        List<Double> configList = configArray.toJavaList(Double.class);
        Map<Long, String> companyConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.LEAVE_APPLICATION_FORM_SUBMIT_LIMIT.key, 1).getData();
        String configValue1 = companyConfigMap.get(SecureUtil.getFkCompanyId());
        List<Double> configList = new ArrayList<>(JSON.parseArray(configValue1, Double.class));
        //审批人的延迟时间
        Double delayHours = configList.get(1);

        SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (delayHours == -1){
            return;
        }

//        System.out.println("sf.format(lastApprovalTime) = " + sf.format(lastApprovalTime));
//        System.out.println("sf.format(leaveApplicationForm.getStartTime()) = " + sf.format(leaveApplicationForm.getStartTime()));
        if (lastApprovalTime.compareTo(leaveApplicationForm.getStartTime())<0){
            return;
        }

        Date applyDate = new Date();
//        Date advanceDateByHour = GetDateUtil.getAdvanceDateByHour(applyDate, -1L * delayHours);
        Date advanceDateByHour = GetDateUtil.getDateAfterMs(lastApprovalTime, (long)(delayHours * GetDateUtil.ONE_HOUR_MILLISECOND));
//        System.out.println("sf.format(applyDate) = " + sf.format(applyDate));
//        System.out.println("sf.format(advanceDateByHour) = " + sf.format(advanceDateByHour));
        if (advanceDateByHour.compareTo(applyDate)>0){
            return;
        }

        if (GeneralTool.isEmpty(comment)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("approval_exception"));
        }
//        StaffVo staffDto = permissionCenterClient.getStaffById(SecureUtil.getStaffId()).getData();
//        String loginId = staffDto.getLoginId();
//        List<OfficeMediaAndAttached> officeMediaAndAttacheds = officeCenterClient.getOfficeMediaAndAttacheds(leaveApplicationForm.getId(),loginId).getData();
//
//        if(GeneralTool.isEmpty(officeMediaAndAttacheds)){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("startup_exception"));
//        }

    }

    /**
     * feign调用 通过员工id查找操作过的对应业务表单id以及当前审批状态
     * @param staffId
     * @param key
     * @return
     */
    @Override
    public Map<Long, Integer> getFromIdsByStaffId(Long staffId, String key) {
        //key：流程实例id value：业务id(工休单id)
        Map<String, String> result = new HashMap<>();
        //key：业务id value：状态（0待签/1待办/2历史记录）
        Map<Long, Integer> map = new HashMap();
        //通过候选人条件查找可能要处理的流程实例
        List<HistoricTaskInstance> historicTaskInstanceList = historyService.createHistoricTaskInstanceQuery()
                .processDefinitionKey(key)
                .taskCandidateUser(String.valueOf(staffId)).list();
        //通过处理人条件查找可能要处理的流程实例
        List<HistoricTaskInstance> historicTaskInstanceList2 = historyService.createHistoricTaskInstanceQuery()
                .processDefinitionKey(key)
                .taskAssignee(String.valueOf(staffId)).list();
        //result.put 表示result里面就是这个人涉及到的全部流程实例（业务表单）
        Set<String> processInstanceIds = Sets.newHashSet();
        Map<String, HistoricProcessInstance> historicProcessInstanceMap = Maps.newHashMap();
        if (GeneralTool.isNotEmpty(historicTaskInstanceList2)){
            processInstanceIds = historicTaskInstanceList2.stream().map(HistoricTaskInstance::getProcessInstanceId).filter(Objects::nonNull).collect(Collectors.toSet());
        }
        if (GeneralTool.isNotEmpty(processInstanceIds)){
            List<HistoricProcessInstance> historicProcessInstances = historyService.createHistoricProcessInstanceQuery().processInstanceIds(processInstanceIds).list();
            historicProcessInstanceMap= historicProcessInstances.stream().collect(HashMap::new, (m, v) -> m.put(v.getId(), v), HashMap::putAll);
        }
        for (HistoricTaskInstance historicTaskInstance : historicTaskInstanceList) {
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(historicTaskInstance.getProcessInstanceId()).singleResult();
            if (GeneralTool.isNotEmpty(historicProcessInstance)) {
                result.put(historicProcessInstance.getId(), historicProcessInstance.getBusinessKey());
            }
        }
        for (HistoricTaskInstance historicTaskInstance : historicTaskInstanceList2) {
//            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(historicTaskInstance.getProcessInstanceId()).singleResult();
            if (GeneralTool.isNotEmpty(historicProcessInstanceMap)){
                HistoricProcessInstance historicProcessInstance = historicProcessInstanceMap.get(historicTaskInstance.getProcessInstanceId());
                if (GeneralTool.isNotEmpty(historicProcessInstance)) {
                    result.put(historicProcessInstance.getId(), historicProcessInstance.getBusinessKey());
                }
            }
        }
        /*
            收集全部的流程实例id（业务表单id）
            当流程实例为空，表示流程运行完毕，该表单状态为历史记录2
                      不为空，需要判断是否有处理人
                        有处理人，表示状态待办1
                        没有处理人，需要判断登录人是否是在该任务的候选人列表中
                            在，表示待签0
                            不在，表示历史记录2
         */
        //判断后设置状态 0待签/1待办/2历史记录
        if (GeneralTool.isEmpty(result)){
            return map;
        }
        Map<String, List<Task>> tasksMap = Maps.newHashMap();
        List<Task> taskList = taskService.createTaskQuery().processInstanceIdIn(Lists.newArrayList(result.keySet())).orderByTaskCreateTime().desc().list();
        if (GeneralTool.isNotEmpty(taskList)){
            tasksMap = taskList.stream().collect(Collectors.groupingBy(Task::getProcessInstanceId));
        }
        for (Map.Entry<String, String> entry : result.entrySet()) {
            //当前流程实例是否正在运行（是否有任务在进行）
    //        List<Task> tasks = taskService.createTaskQuery().processInstanceId(entry.getKey()).orderByTaskCreateTime().desc().list();
            if (GeneralTool.isEmpty(tasksMap)) {
                //没有任务，表示流程实例已经结束，该表单为历史状态2
                map.put(Long.valueOf(entry.getValue()), 2);
                continue;
            }
            List<Task> tasks = tasksMap.get(entry.getKey());
            if (GeneralTool.isEmpty(tasks))  {
                //没有任务，表示流程实例已经结束，该表单为历史状态2
                map.put(Long.valueOf(entry.getValue()), 2);
                continue;
            }
            Task task = tasks.get(0);
            String assignee = task.getAssignee();
            //判断当前任务是否有处理人
            if (GeneralTool.isEmpty(assignee)) {
                //任务候选人列表
                List<IdentityLink> identityLinkList = taskService.getIdentityLinksForTask(task.getId());
                List<String> candidateUserList = identityLinkList.stream().map(IdentityLink::getUserId).collect(Collectors.toList());
                if (GeneralTool.isEmpty(candidateUserList)) {
                    //不在，表示是历史记录2
                    map.put(Long.valueOf(entry.getValue()), 2);
                    continue;
                }
                //判断是否在候选人列表中
                if (candidateUserList.contains(String.valueOf(staffId))) {
                    //在，表示该任务为待签状态0
                    map.put(Long.valueOf(entry.getValue()), 0);
                } else {
                    //不在，表示是历史记录2
                    map.put(Long.valueOf(entry.getValue()), 2);
                }
            } else {
                //判断该处理人是不是登录人
                if (assignee.equals(String.valueOf(staffId))) {
                    //表示待办状态1
                    map.put(Long.valueOf(entry.getValue()), 1);
                } else {
                    //表示这个流程实例已经过了自己的审批，为历史状态2
                    map.put(Long.valueOf(entry.getValue()), 2);
                }
            }
        }
        return map;
    }

    @Override
    public Map<Long, ActRuTaskVo> getActRuTaskDtosByBusinessKey(List<Long> businessIds, String procdefKey) {
        Map<Long, ActRuTaskVo> map = new HashMap<>();
        ProcessInstanceQuery processInstanceQuery = runtimeService.createProcessInstanceQuery();
        HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery();
        for (Long id : businessIds) {
            ActRuTaskVo actRuTaskVo = new ActRuTaskVo();
//            ProcessInstance processInstance = processInstanceQuery.processInstanceBusinessKey(String.valueOf(id)).processDefinitionKey(procdefKey).singleResult();
//            List<ProcessInstance> processInstanceList = processInstanceQuery.processInstanceBusinessKey(String.valueOf(id)).processDefinitionKey(procdefKey).list();
//            if (GeneralTool.isNotEmpty(processInstanceList)) {
//                Task task = taskService.createTaskQuery().processInstanceId(processInstanceList.get(0).getId()).singleResult();//这里只取一个，待确定是否有问题
//                if (GeneralTool.isNotEmpty(task)) {
//                    actRuTaskVo.setId(task.getId());
//                    //根据任务id查找rev
//                    Integer version = ruTaskMapper.getVersionById(task.getId());
//                    actRuTaskVo.setTaskVersion(version);
//                    actRuTaskVo.setAssignee(task.getAssignee());
//                }
//            }

            ProcessInstance processInstance = processInstanceQuery.processInstanceBusinessKey(String.valueOf(id)).processDefinitionKey(procdefKey).singleResult();
            if (GeneralTool.isNotEmpty(processInstance)) {
                List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstance.getId()).orderByTaskCreateTime().desc().list();
                if (GeneralTool.isNotEmpty(tasks)) {
                    Task task = tasks.get(0);
                    actRuTaskVo.setId(task.getId());
                    //根据任务id查找rev
                    Integer version = ruTaskMapper.getVersionById(task.getId());
                    actRuTaskVo.setTaskVersion(version);
                    actRuTaskVo.setAssignee(task.getAssignee());
                }
            }
//            HistoricProcessInstance historicProcessInstance = historicProcessInstanceQuery.processInstanceBusinessKey(String.valueOf(id)).processDefinitionKey(procdefKey).singleResult();
//            List<HistoricProcessInstance> historicProcessInstanceList = historicProcessInstanceQuery.processInstanceBusinessKey(String.valueOf(id)).processDefinitionKey(procdefKey).list();
//            if (GeneralTool.isNotEmpty(historicProcessInstanceList)) {
//                actRuTaskVo.setProcInstId(historicProcessInstanceList.get(0).getId());
//            }
            HistoricProcessInstance historicProcessInstance = historicProcessInstanceQuery.processInstanceBusinessKey(String.valueOf(id)).processDefinitionKey(procdefKey).singleResult();
            if (GeneralTool.isNotEmpty(historicProcessInstance)) {
                actRuTaskVo.setProcInstId(historicProcessInstance.getId());
            }
            map.put(id, actRuTaskVo);
        }
        return map;
    }

    @Override
    public HiCommentVo viewHisCommentByBusinessKey(Long businessKey, String procdefKey) {
        List<ActHiCommentVo> actHiCommentVoList = new ArrayList<>();
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processDefinitionKey(procdefKey).processInstanceBusinessKey(String.valueOf(businessKey)).singleResult();
        if (GeneralTool.isEmpty(historicProcessInstance)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("process_already_delete"));
        }
        HiCommentVo hiCommentVo = new HiCommentVo();
        Task task = taskService.createTaskQuery().processInstanceId(historicProcessInstance.getId()).singleResult();
        if (GeneralTool.isEmpty(task)) {
            hiCommentVo.setAgreeButtonType(false);
            hiCommentVo.setRefuseButtonType(false);
        } else {
            if (GeneralTool.isEmpty(task.getAssignee())) {
                hiCommentVo.setAgreeButtonType(false);
                hiCommentVo.setRefuseButtonType(false);
            } else {
                if (task.getAssignee().contains(",")) {
                    List<Long> assigneeIds = Stream.of(task.getAssignee().split(",")).map(Long::valueOf).collect(Collectors.toList());
                    if (assigneeIds.contains(GetAuthInfo.getStaffId())) {
                        hiCommentVo.setAgreeButtonType(true);
                        hiCommentVo.setRefuseButtonType(true);
                    } else {
                        hiCommentVo.setAgreeButtonType(false);
                        hiCommentVo.setRefuseButtonType(false);
                    }
                } else {
                    if (Long.valueOf(task.getAssignee()).equals(GetAuthInfo.getStaffId())) {
                        hiCommentVo.setAgreeButtonType(true);
                        hiCommentVo.setRefuseButtonType(true);
                    } else {
                        hiCommentVo.setAgreeButtonType(false);
                        hiCommentVo.setRefuseButtonType(false);
                    }
                }
            }
        }
        //历史活动集合
        List<HistoricActivityInstance> historicActivityInstanceList =
                historyService.createHistoricActivityInstanceQuery().processInstanceId(historicProcessInstance.getId()).orderByHistoricActivityInstanceStartTime().asc().list();

        Set<String> taskIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(historicActivityInstanceList)) {
            taskIds = historicActivityInstanceList.stream().map(HistoricActivityInstance::getTaskId).collect(Collectors.toSet());
            taskIds.removeIf(Objects::isNull);
        }
        if (GeneralTool.isEmpty(taskIds)) {
            taskIds.add("0");
        }
        List<HistoricVariableInstance> approvalAction = historyService.createHistoricVariableInstanceQuery()
                .taskIds(taskIds)
                .variableName("approvalAction")
                .list();

        Map<String, Object> approvalActionMap = new HashMap<>();
        if (GeneralTool.isNotEmpty(approvalAction)) {
            approvalActionMap = approvalAction.stream().collect(Collectors.toMap(HistoricVariableInstance::getTaskId, HistoricVariableInstance::getValue));
        }

        for (HistoricActivityInstance historicActivityInstance : historicActivityInstanceList) {
            ActHiCommentVo actHiCommentVo = new ActHiCommentVo();
            //审批流程（流程名称）
            actHiCommentVo.setCurrentFlow(historicProcessInstance.getProcessDefinitionName());
            String activityName = historicActivityInstance.getActivityName();
            String activityType = historicActivityInstance.getActivityType();

            if ("startEvent".equals(activityType)) {
                Object buttonType = getVariableByHisInstanceAndName(historicActivityInstance.getProcessInstanceId(), "buttonType");
                if (GeneralTool.isNotEmpty(buttonType)) {
                    String buttonTypeString = (String) buttonType;
                    activityName = "0".equals(buttonTypeString) ? "申请终止流程" : "申请结案流程";
                } else {
                    activityName = "开始";
                }
                Result<String> result = permissionCenterClient.getStaffName(Long.valueOf(Long.valueOf(historicProcessInstance.getStartUserId())));
                if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                    actHiCommentVo.setAssignee(result.getData());
                }

            }
            if ("endEvent".equals(activityType)) {
                activityName = "流程结束";
            }
            if (GeneralTool.isNotEmpty(activityName) && GeneralTool.isNotEmpty(historicActivityInstance.getEndTime())) {
                //当前节点（任务名称）
                actHiCommentVo.setCurrentNode(activityName);
                //通过任务id查找该任务审批意见
                List<Comment> taskComments = taskService.getTaskComments(historicActivityInstance.getTaskId());
                actHiCommentVo.setTaskComments(taskComments);
                //开始时间
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                Date startTime = historicActivityInstance.getStartTime();
                actHiCommentVo.setHandlingStartTime(startTime);
                //审批时间（结束时间）
                Date endTime = historicActivityInstance.getEndTime();
                actHiCommentVo.setHandlingEndTime(endTime);
                String endformat = simpleDateFormat.format(endTime);
                String startformat = simpleDateFormat.format(startTime);
                String durationTime = dateDiff(startformat, endformat, simpleDateFormat.toPattern());
                actHiCommentVo.setTotalTime(durationTime);
                //审批人
                if (GeneralTool.isNotEmpty(historicActivityInstance.getAssignee()) && GeneralTool.isEmpty(actHiCommentVo.getAssignee())) {
//                    actHiCommentVo.setAssignee(permissionCenterClient.getStaffName(Long.valueOf(historicActivityInstance.getAssignee())));
                    Result<String> result = permissionCenterClient.getStaffName(Long.valueOf(Long.valueOf(historicActivityInstance.getAssignee())));
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                        actHiCommentVo.setAssignee(result.getData());
                    }
                }
                if (GeneralTool.isNotEmpty(approvalActionMap.get(historicActivityInstance.getTaskId()))) {
                    actHiCommentVo.setApprovalAction((Integer) approvalActionMap.get(historicActivityInstance.getTaskId()));
                    if (Integer.valueOf(0).equals(approvalActionMap.get(historicActivityInstance.getTaskId()))) {
                        actHiCommentVo.setApprovalActionName(LocaleMessageUtils.getMessage("default_disagreement_message"));
                    } else {
                        actHiCommentVo.setApprovalActionName(LocaleMessageUtils.getMessage("default_agreement_message"));
                    }
                }
                actHiCommentVoList.add(actHiCommentVo);
            }
        }
        hiCommentVo.setActHiCommentDs(actHiCommentVoList);
        return hiCommentVo;
    }


    /**
     * @Description: 根据流程历史实例以及名称获取变量值
     * @Author: Jerry
     * @Date:11:48 2021/11/9
     */
    @Override
    public Object getVariableByHisInstanceAndName(String processInstanceId, String name) {
        HistoricVariableInstance historicVariableInstance = historyService.createHistoricVariableInstanceQuery().processInstanceId(processInstanceId).variableName(name).singleResult();
        if (GeneralTool.isEmpty(historicVariableInstance) || GeneralTool.isEmpty(historicVariableInstance.getValue())) {
            return null;
        }
        //String value = JSON.toJSONString(historicVariableInstance.getValue());
        return historicVariableInstance.getValue();
    }

    @Override
    public String getStartUserId(String processInstId) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstId).singleResult();
        if (GeneralTool.isNotEmpty(historicProcessInstance.getStartUserId())) {
            return historicProcessInstance.getStartUserId();
        }
        return null;
    }

    @Override
    public void getStatusToDoSingle(String status, String procInstId) {
        if ("0".equals(status)) {
            Task task = taskService.createTaskQuery().processInstanceId(procInstId).singleResult();
            if (GeneralTool.isNotEmpty(task)) {
                taskService.setVariableLocal(task.getId(), "statusToDo", 0);
            }
        } else {
            Task task = taskService.createTaskQuery().processInstanceId(procInstId).singleResult();
            if (GeneralTool.isNotEmpty(task)) {
                taskService.setVariable(task.getId(), "statusToDo", 1);
            }
        }

    }


    @Override
    public Boolean StopExecution(String processInstId, String msg, String procdefKey, String businessKey) {
        if (GeneralTool.isEmpty(procdefKey) || GeneralTool.isEmpty(procdefKey) || GeneralTool.isEmpty(businessKey) || GeneralTool.isEmpty(msg)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        boolean status = true;
        LeaveApplicationForm leaveApplicationForm = null;
        Integer formStatus = null;
        switch (procdefKey) {
            case "m_leave_application_form":
                leaveApplicationForm = officeCenterClient.getLeaveApplicationForm(Long.valueOf(businessKey)).getData();
                if (GeneralTool.isNotEmpty(leaveApplicationForm.getFkLeaveApplicationFormIdRevoke())){
                    //撤单不给终止
                    throw new GetServiceException(LocaleMessageUtils.getMessage("leave_form_cannot_be_void"));
                }
                if (GeneralTool.isNotEmpty(leaveApplicationForm)){
                    formStatus = leaveApplicationForm.getStatus();
                    if (GeneralTool.isNotEmpty(leaveApplicationForm.getFkStaffId())){
                        CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(leaveApplicationForm.getFkStaffId()), cache_key);
                    }
                }
                officeCenterClient.changeStatus(5, procdefKey, Long.valueOf(businessKey));
                break;
            case "m_payment_application_form":
            case "m_prepay_application_form":
            case "m_expense_claim_form":
            case "m_travel_claim_form":
                financeCenterClient.changeStatus(5, procdefKey, Long.valueOf(businessKey));
                break;
            case "m_agent_contract":
                saleCenterClient.changeStatus(5, procdefKey, Long.valueOf(businessKey));
                break;
            case "m_institution_provider":
                institutionCenterClient.changeStatus(5, "m_contract", Long.valueOf(businessKey));
                break;
            default:
                status = false;
        }

        if (status) {
            //删除提醒任务
            Task task = taskService.createTaskQuery().processInstanceId(processInstId).singleResult();
            if (GeneralTool.isNotEmpty(task)) {
                try {
                    reminderCenterClient.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(task.getId()));
                } catch (Exception e) {
                    log.error("提醒任务删除异常：" + e.getMessage());
                }
            }
            ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstId).singleResult();
            if (processInstance != null) {
                runtimeService.deleteProcessInstance(processInstId, msg);
                return true;
            } else if (GeneralTool.isNotEmpty(leaveApplicationForm)&&Objects.equals(formStatus,ProjectExtraEnum.APPROVAL_FINISHED.key)){
                HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processInstId).singleResult();
                if (historicProcessInstance != null) {
                    historyService.deleteHistoricProcessInstance(processInstId);
                    return true;
                }
            } else {
                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
            }
        }
        return false;
    }

    /**
     * 开启流程
     * @param businessKey 例如：工休单id
     * @param procdefKey 例如：工休单表名 m_leave_application_form
     * @param companyId 租户
     * @param map 流程实例参数
     * @return
     */
    @Override
    public Boolean startProcess(String businessKey, String procdefKey, String companyId, Map<String, Object> map) {
        if (procdefKey.equals(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)){
            LeaveApplicationForm leaveApplicationForm = officeCenterClient.getLeaveApplicationForm(Long.valueOf(businessKey)).getData();
            if (GeneralTool.isNotEmpty(leaveApplicationForm)&&GeneralTool.isNotEmpty(leaveApplicationForm.getFkStaffId())){
                //更新提醒缓存
                CacheUtil.evict(CacheNames.TASK_CACHE, String.valueOf(leaveApplicationForm.getFkStaffId()), cache_key);
            }
        }

        Authentication.setAuthenticatedUserId(String.valueOf(GetAuthInfo.getStaffId()));

        List<ProcessInstance> processInstanceList = runtimeService.createProcessInstanceQuery()
                .processInstanceBusinessKey(String.valueOf(businessKey))
                .processDefinitionKey(procdefKey)
                .list();

        List<HistoricProcessInstance> historicProcessInstances = historyService.createHistoricProcessInstanceQuery()
                .processInstanceBusinessKey(String.valueOf(businessKey))
                .processDefinitionKey(procdefKey)
                .list();

        List<Execution> executionList = runtimeService.createExecutionQuery()
                .processInstanceBusinessKey(String.valueOf(businessKey))
                .processDefinitionKey(procdefKey)
                .list();
        //流程实例已存在，表单状态不对，修改即可
        if (GeneralTool.isNotEmpty(processInstanceList) || GeneralTool.isNotEmpty(historicProcessInstances) || GeneralTool.isNotEmpty(executionList)) {
            return false;
        }
        try {
            //定制流程开启
            runtimeService.startProcessInstanceByKeyAndTenantId(procdefKey, businessKey, map, companyId);
        } catch (ActivitiObjectNotFoundException e) {
            //通用流程开启
            runtimeService.startProcessInstanceByKey(procdefKey, businessKey, map);
            e.printStackTrace();
        }
        return true;
    }

    @Override
    public Boolean getUserSubmit(String taskId, String status) {
        Map<String, Object> map = new HashMap<>();
        map.put("sequenceFlowsStatus", status);
        taskService.setVariable(taskId, "statusToDo", 1);
        taskService.complete(taskId, map);
        return true;
    }

    @Override
    public HiCommentFeignVo getHiComment(Long businessKey, String procdefKey) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processDefinitionKey(procdefKey).processInstanceBusinessKey(String.valueOf(businessKey)).singleResult();
        if (GeneralTool.isEmpty(historicProcessInstance)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("process_already_delete"));
            return null;
        }
        HiCommentFeignVo hiCommentFeignVo = new HiCommentFeignVo();
        Task task = taskService.createTaskQuery().processInstanceId(historicProcessInstance.getId()).singleResult();
        if (GeneralTool.isEmpty(task)) {
            hiCommentFeignVo.setAgreeButtonType(false);
            hiCommentFeignVo.setRefuseButtonType(false);
        } else {
            if (GeneralTool.isEmpty(task.getAssignee())) {
                hiCommentFeignVo.setAgreeButtonType(false);
                hiCommentFeignVo.setRefuseButtonType(false);
            } else {
                if (task.getAssignee().contains(",")) {
                    List<Long> assigneeIds = Stream.of(task.getAssignee().split(",")).map(Long::valueOf).collect(Collectors.toList());
                    if (assigneeIds.contains(SecureUtil.getStaffId())) {
                        hiCommentFeignVo.setAgreeButtonType(true);
                        hiCommentFeignVo.setRefuseButtonType(true);
                    } else {
                        hiCommentFeignVo.setAgreeButtonType(false);
                        hiCommentFeignVo.setRefuseButtonType(false);
                    }
                } else {
                    if (Long.valueOf(task.getAssignee()).equals(SecureUtil.getStaffId())) {
                        hiCommentFeignVo.setAgreeButtonType(true);
                        hiCommentFeignVo.setRefuseButtonType(true);
                    } else {
                        hiCommentFeignVo.setAgreeButtonType(false);
                        hiCommentFeignVo.setRefuseButtonType(false);
                    }
                }
            }
        }
        return hiCommentFeignVo;
    }

    @Override
    public ActHiTaskInstVo getActHiTaskInstDtoAndLeaveFormMessage(Long fkTableId) {
        ActHiTaskInstVo actHiTaskInstVo = new ActHiTaskInstVo();
        HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(String.valueOf(fkTableId)).singleResult();
        List<HistoricProcessInstance> list = historyService.createHistoricProcessInstanceQuery().processInstanceId(historicTaskInstance.getProcessInstanceId()).list();


        //流程key
        String processDefinitionId = historicTaskInstance.getProcessDefinitionId();
        if (processDefinitionId.contains(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key)){
            actHiTaskInstVo.setWorkFlowType(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key);
            //工休单
            Long formId = null;
            if (GeneralTool.isNotEmpty(list)){
                formId = Long.valueOf(list.get(0).getBusinessKey());
            }
            if (GeneralTool.isNotEmpty(formId)){
                Result<LeaveApplicationForm> leaveForm = officeCenterClient.getLeaveApplicationForm(formId);
                if (leaveForm.isSuccess() && GeneralTool.isNotEmpty(leaveForm.getData())){
                    LeaveApplicationForm leaveApplicationForm = leaveForm.getData();
                    String reason = leaveApplicationForm.getReason();
                    BigDecimal days = leaveApplicationForm.getDays().abs();
                    String typeName = officeCenterClient.getLeaveTypeNameById(leaveApplicationForm.getId()).getData();

                    actHiTaskInstVo.setLeaveReason(reason);
                    actHiTaskInstVo.setLeaveDays(days.toString());
                    actHiTaskInstVo.setLeaveType(typeName);
                    actHiTaskInstVo.setStartDay(leaveApplicationForm.getStartTime());
                    actHiTaskInstVo.setEndDay(leaveApplicationForm.getEndTime());
                }


            }
        }


        List<HistoricIdentityLink> identityLinkList = historyService.getHistoricIdentityLinksForProcessInstance(historicTaskInstance.getProcessInstanceId());
        //员工id
        String starterId = null;
        for (HistoricIdentityLink identityLink : identityLinkList) {
            if (Objects.equals("starter", identityLink.getType())) {
                starterId = identityLink.getUserId();
                break;
            }
        }
        if (GeneralTool.isNotEmpty(starterId)){

            Result<StaffVo> result = permissionCenterClient.getCompanyIdByStaffId(Long.valueOf(starterId));
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                StaffVo staffVo = result.getData();

                Result<String> officeNameById = permissionCenterClient.getOfficeNameById(staffVo.getFkOfficeId());
                if (officeNameById.isSuccess() && GeneralTool.isNotEmpty(officeNameById.getData())) {
                    String offerName = officeNameById.getData();
                    actHiTaskInstVo.setOfficeName(offerName);
                }

                Result<String> departmentNameById = permissionCenterClient.getDepartmentNameById(staffVo.getFkDepartmentId());
                if (departmentNameById.isSuccess() && GeneralTool.isNotEmpty(departmentNameById.getData())) {
                    String departmentName = departmentNameById.getData();
                    actHiTaskInstVo.setDepartmentName(departmentName);
                }
            }
        }

        return actHiTaskInstVo;
    }

    @Override
    public List<ActHiTaskInstVo> getActHiTaskInstDtosByBusinessKey(String businessKey, String procdefKey) {
//        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
//                .processDefinitionKey(procdefKey).processInstanceBusinessKey(String.valueOf(businessKey)).singleResult();

        List<ActHiTaskInstVo> actHiTaskInstVos = new ArrayList<>();
        List<HistoricTaskInstance> historicTaskInstances = historyService.createHistoricTaskInstanceQuery()
                .processDefinitionKey(procdefKey)
                .processInstanceBusinessKey(businessKey)
                .list();

        if (GeneralTool.isEmpty(historicTaskInstances)){
            return null;
        }

        for (HistoricTaskInstance historicTaskInstance : historicTaskInstances) {
            ActHiTaskInstVo actHiTaskInstVo = new ActHiTaskInstVo();
            actHiTaskInstVo.setId(historicTaskInstance.getId());
            actHiTaskInstVo.setProcInstId(historicTaskInstance.getProcessInstanceId());
            actHiTaskInstVos.add(actHiTaskInstVo);
        }
        return actHiTaskInstVos;
    }

    @Override
    public Set<String> getToDoByStaffIdAndTableName(Long staffId, String key) {

        Set<String> businessKeys = new HashSet<>();
        List<Task> taskList = taskService.createTaskQuery().taskAssignee(String.valueOf(staffId)).processDefinitionKey(key).list();
        if (GeneralTool.isNotEmpty(taskList)){
            Set<String> processInstanceIds = taskList.stream().map(Task::getProcessInstanceId).filter(Objects::nonNull).collect(Collectors.toSet());
            List<ProcessInstance> processInstanceList = runtimeService.createProcessInstanceQuery().processInstanceIds(processInstanceIds).list();
            if (GeneralTool.isNotEmpty(processInstanceList)){
                businessKeys = processInstanceList.stream().map(ProcessInstance::getBusinessKey).collect(Collectors.toSet());
            }
        }
        return businessKeys;
    }

    @Override
    public Set<String> getToSignByStaffIdAndTableName(Long staffId, String key) {
        Set<String> businessKeys = new HashSet<>();
        List<Task> taskList = taskService.createTaskQuery().taskCandidateUser(String.valueOf(staffId)).processDefinitionKey(key).list();
        if (GeneralTool.isNotEmpty(taskList)){
            Set<String> processInstanceIds = taskList.stream().map(Task::getProcessInstanceId).filter(Objects::nonNull).collect(Collectors.toSet());
            List<ProcessInstance> processInstanceList = runtimeService.createProcessInstanceQuery().processInstanceIds(processInstanceIds).list();
            if (GeneralTool.isNotEmpty(processInstanceList)){
                businessKeys = processInstanceList.stream().map(ProcessInstance::getBusinessKey).collect(Collectors.toSet());
            }
        }
        return businessKeys;
    }

    @Override
    public void doSendRemind(LeaveApplicationForm leaveApplicationForm, String title) {

        Task task = taskService.createTaskQuery().processInstanceBusinessKey(String.valueOf(leaveApplicationForm.getId())).singleResult();
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionId(task.getProcessDefinitionId()).singleResult();

        StaffVo staffVo = permissionCenterClient.getCompanyIdByStaffId(leaveApplicationForm.getFkStaffId()).getData();
        //构造行政专员ids
        List<String> ids = new ArrayList<>();
        Set<String> nums = new HashSet<>(1);
//        if (staffVo.getFkCompanyId().equals(1L)){
//            nums.add("HKHR01");
//        }else if (staffVo.getFkCompanyId().equals(2L)){
//            nums.add("XZZL02");
//        }
      if(staffVo.getFkCompanyId().equals(3L)){
            nums.add("IPZJB05");
        }
        List<Long> staffIds = permissionCenterClient.getStaffIdsByPositionNums(nums);
        ids = staffIds.stream().map(String::valueOf).collect(Collectors.toList());


        //workFlowHelper.sendMessage(staffVo,ids,processDefinition,title,task,null);
    }

    @Override
    public void doSendEmail(LeaveApplicationForm leaveApplicationForm, String title,Long superiorId) {
        Task task = taskService.createTaskQuery().processInstanceBusinessKey(String.valueOf(leaveApplicationForm.getId())).singleResult();
        StaffVo staffVo = permissionCenterClient.getCompanyIdByStaffId(leaveApplicationForm.getFkStaffId()).getData();
        //模版文件
        //TODO HTI_WORKFLOW_LEAVE_FORM
        EmailTemplate emailTemplate = reminderCenterClient.getEmailTemplateByTypeKey(EmailTemplateEnum.WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey()).getData();
        if(GeneralTool.isEmpty(emailTemplate)){
            throw new GetServiceException(ErrorCodeEnum.SERVER_EXCEPTION.getCode());
        }
        Map<String,String> param = new HashMap();
        if(GeneralTool.isNotEmpty(superiorId)){
            param.put("superiorId",String.valueOf(superiorId));
        }else {
            param.put("superiorId","");
        }

        String  map = null;
        ObjectMapper mapper = new ObjectMapper();
        try {
             map = mapper.writeValueAsString(param);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        String title1 = null;
        //获取中英文配置
        Map<Long, String>  versionConfigMap = permissionCenterClient.getCompanyConfigMap(ProjectKeyEnum.REMINDER_EMAIL_LANGUAGE_VERSION.key, 1).getData();
        String versionValue2 = versionConfigMap.get(staffVo.getFkCompanyId());
        if("zh".equals(versionValue2)){
            title1 = "工休申请流程-"+ staffVo.getName()+"("+ staffVo.getNameEn()+")" +" -已通过审批";
        }else {
            title1 = "Leave Application Approved -" + staffVo.getName() +" -Approved";
        }
        EmailSenderQueue emailSenderQueue = new EmailSenderQueue();
        emailSenderQueue.setFkEmailTypeKey(EmailTemplateEnum.WORK_LEAVE_WORKFLOW_REMINDER.getEmailTemplateKey());
        emailSenderQueue.setEmailTitle(title1);
        emailSenderQueue.setEmailParameter(map);
        emailSenderQueue.setFkDbName(ProjectKeyEnum.WORKFLOW_CENTER.key);
        emailSenderQueue.setFkTableName(TableEnum.ACT_HI_TASKINST.key);
        emailSenderQueue.setFkTableId(Long.valueOf(task.getId()));
        emailSenderQueue.setOperationTime(now());
        List<EmailSenderQueue> emailSenderQueueList = Lists.newArrayList(emailSenderQueue);
        Result<Boolean> booleanResult = reminderCenterClient.batchAddEmailQueue(emailSenderQueueList);
        if (!booleanResult.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(booleanResult.getMessage()));
        }
        //邮件发送内容
//        StaffVo staffVo = permissionCenterClient.getCompanyIdByStaffId(leaveApplicationForm.getFkStaffId()).getData();
//        String departmentName = permissionCenterClient.getDepartmentNameById(staffVo.getFkDepartmentId()).getData();
//        String office = permissionCenterClient.getOfficeNameById(staffVo.getFkOfficeId()).getData();
//
//        SimpleDateFormat date = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        String leaveTypeKey = officeCenterClient.getLeaveTypeNameById(leaveApplicationForm.getId()).getData();
//
//        String emailTemplate = remindTemplate.getEmailTemplate();
        //TODO 邮件模板id=5
//        emailTemplate = emailTemplate.replace("#{taskTitle}","工休申请流程-"+ staffVo.getName()+"("+ staffVo.getNameEn()+")" +" -已通过审批");
//        emailTemplate = emailTemplate.replace("#{officeName}",office);
//        emailTemplate = emailTemplate.replace("#{departmentName}",departmentName);
//        emailTemplate = emailTemplate.replace("#{leaveType}",leaveTypeKey);
//        emailTemplate = emailTemplate.replace("#{leaveDays}",leaveApplicationForm.getDays().toString());
//        emailTemplate = emailTemplate.replace("#{startTime}",date.format(leaveApplicationForm.getStartTime()));
//        emailTemplate = emailTemplate.replace("#{endTime}",date.format(leaveApplicationForm.getEndTime()));
//        emailTemplate = emailTemplate.replace("#{leaveReason}",leaveApplicationForm.getReason());

        //获取直属上司的直属上司的邮箱
        //StaffVo superopr = permissionCenterClient.getCompanyIdByStaffId(superiorId).getData();
//       String to="<EMAIL>";
//        String emailTemplateTest = Base64.getEncoder().encodeToString((emailTemplate).getBytes(StandardCharsets.UTF_8));
//        reminderCenterClient.sendMail(title,emailTemplateTest,superopr.getEmail(),null);
//        sendMessageUtils.sendMail(title,emailTemplate,superopr.getEmail());
    }

    @Override
    public ActHiTaskInstVo getActHiTaskInstDtoAndStudentOffer(Long fkTableId) {
        ActHiTaskInstVo actHiTaskInstVo = new ActHiTaskInstVo();
        HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(String.valueOf(fkTableId)).singleResult();
        List<HistoricProcessInstance> list = historyService.createHistoricProcessInstanceQuery().processInstanceId(historicTaskInstance.getProcessInstanceId()).list();

        //流程key
        String processDefinitionId = historicTaskInstance.getProcessDefinitionId();
        if (processDefinitionId.contains(TableEnum.SALE_STUDENT_OFFER.key)){
            //工休单
            Long formId = null;
            if (GeneralTool.isNotEmpty(list)){
                formId = Long.valueOf(list.get(0).getBusinessKey());
            }
            if (GeneralTool.isNotEmpty(formId)){
                Result<StudentOfferVo> studentOfferForWorkFlow = saleCenterClient.getStudentOfferForWorkFlow(formId);
                if (studentOfferForWorkFlow.isSuccess() && GeneralTool.isNotEmpty(studentOfferForWorkFlow.getData())){
                    SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    Date startTime = historicTaskInstance.getStartTime();
                    String submitTime = sf.format(startTime);
                    StudentOfferVo studentOfferVo = studentOfferForWorkFlow.getData();
                    Map<String,String> map = Maps.newHashMap();
                    map.put("num", studentOfferVo.getNum());
                    map.put("fkStudentName", studentOfferVo.getStudentName());
                    map.put("fkCountryName", studentOfferVo.getFkAreaCountryName());
                    map.put("fkSchoolName", studentOfferVo.getInstitutionFullName());
                    map.put("fkCancelOfferReasonName", studentOfferVo.getFkCancelOfferReasonName());
                    map.put("submitTime",submitTime);
                    actHiTaskInstVo.setMap(map);
                }
            }
        }
        return actHiTaskInstVo;

    }

    @Override
    public Boolean stopStudentOfferWorkFlow(Long id) {
        //删除提醒任务
        Task task = taskService.createTaskQuery().processInstanceBusinessKey(String.valueOf(id)).processDefinitionKey(TableEnum.SALE_STUDENT_OFFER.key).singleResult();
        if (GeneralTool.isNotEmpty(task)) {
            try {
                reminderCenterClient.batchUpdate(new ArrayList<>(), TableEnum.ACT_HI_TASKINST.key, Long.valueOf(task.getId()));
            } catch (Exception e) {
                log.error("提醒任务删除异常：" + e.getMessage());
            }
        }
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        if (processInstance != null) {
            runtimeService.deleteProcessInstance(task.getProcessInstanceId(), "终止流程");
            historyService.deleteHistoricProcessInstance(task.getProcessInstanceId());
            return true;
        } else {
            return false;
        }
    }

    @Override
    public Long getStartUserIdByIdAndProcdefKey(Long id, String procdefKey) {
        Task task = taskService.createTaskQuery().processInstanceBusinessKey(String.valueOf(id)).processDefinitionKey(TableEnum.SALE_STUDENT_OFFER.key).singleResult();
        if (GeneralTool.isEmpty(task)){
            return null;
        }
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(task.getProcessInstanceId()).singleResult();
        if (GeneralTool.isNotEmpty(historicProcessInstance.getStartUserId())) {
            return Long.valueOf(historicProcessInstance.getStartUserId());
        }
        return null;
    }

    /**
     * 获取审核人id
     * @param businessKey
     * @return
     */
    @Override
    public Long getAssigneeStaffId(String businessKey) {
        Task task = taskService.createTaskQuery().processInstanceBusinessKey(String.valueOf(businessKey))
                .processDefinitionKey(TableEnum.OFFICE_LEAVE_APPLICATION_FORM.key).singleResult();
        if (GeneralTool.isEmpty(task)){
            return null;
        }
        String assignee = task.getAssignee();
        return GeneralTool.isNotEmpty(assignee)?Long.valueOf(assignee):null;
    }

    @Override
    public List<Map<String, Object>> approvalStatusSelected() {
        return ProjectExtraEnum.enumsTranslation2Arrays(ProjectExtraEnum.STATUS_OF_APPROVE);
    }

    /**
     * 申请表单流程发起
     * @param tableId
     * @param tableName
     * @param companyId
     */
    @Override
    @Transactional
    public void applicationFormStartProcess(Long tableId, String tableName, String companyId) {
        if (GeneralTool.isEmpty(tableId) || GeneralTool.isEmpty(tableName) || GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        Map<String, Object> map = new HashMap<>();
        ExpenseClaimForm expenseClaimForm = null;
        PrepayApplicationForm prepayApplicationForm = null;
        TravelClaimForm travelClaimForm = null;
        PaymentApplicationForm paymentApplicationForm = null;
        if (TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key.equals(tableName)) {
            expenseClaimForm = financeCenterClient.getExpenseClaimFormById(tableId).getData();
            if (expenseClaimForm == null || expenseClaimForm.getStatus() != 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("start_process_fail_status"));
            }
            Result<BigDecimal> result = financeCenterClient.getExpenseClaimFormTotalAmount(expenseClaimForm.getId());
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            map.put("businessKey", expenseClaimForm.getId());
            map.put("prepay", expenseClaimForm);
            map.put("amount", result.getData());
        } else if (TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key.equals(tableName)) {
            prepayApplicationForm = financeCenterClient.getPrepayApplicationFormById(tableId).getData();
            if (prepayApplicationForm == null || prepayApplicationForm.getStatus() != 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("start_process_fail_status"));
            }
            map.put("businessKey", prepayApplicationForm.getId());
            map.put("prepay", prepayApplicationForm);
            map.put("amount", prepayApplicationForm.getAmount());
        } else if (TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key.equals(tableName)) {
            travelClaimForm = financeCenterClient.getTravelClaimFormById(tableId).getData();
            if (travelClaimForm == null || travelClaimForm.getStatus() != 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("start_process_fail_status"));
            }
            Result<BigDecimal> result = financeCenterClient.getTravelClaimFormTotalAmount(travelClaimForm.getId());
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            map.put("businessKey", travelClaimForm.getId());
            map.put("prepay", travelClaimForm);
            map.put("amount", result.getData());
        } else if (TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key.equals(tableName)) {
            paymentApplicationForm = financeCenterClient.getPaymentApplicationFormById(tableId).getData();
            if (paymentApplicationForm == null || paymentApplicationForm.getStatus() != 0) {
                throw new GetServiceException(LocaleMessageUtils.getMessage("start_process_fail_status"));
            }
            Result<BigDecimal> result = financeCenterClient.getPaymentApplicationFormTotalAmount(paymentApplicationForm.getId());
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
            map.put("businessKey", paymentApplicationForm.getId());
            map.put("prepay", paymentApplicationForm);
            map.put("amount", result.getData());
        }

        String username = String.valueOf(GetAuthInfo.getStaffId());
        Result<StaffVo> result1 = permissionCenterClient.getStaffById(GetAuthInfo.getStaffId());
        if (!result1.isSuccess()) {
            throw new GetServiceException(result1.getMessage());
        }
        StaffVo staffVo = result1.getData();
        map.put("userid", username);
        map.put("companyId", companyId);
        map.put("tableName", tableName);
        map.put("staff", staffVo);
        map.put("applyUser", GetAuthInfo.getStaffId());

        //表名 +公司id
        List<ProcessDefinition> processDefinition =
                repositoryService.createProcessDefinitionQuery().processDefinitionKey(tableName).processDefinitionTenantId(companyId).orderByProcessDefinitionVersion().desc().list();
        String id = "";
        for (ProcessDefinition pdid : processDefinition) {
            id = pdid.getId();
            break;
        }
        Authentication.setAuthenticatedUserId(username);
        ProcessInstance processInstance = runtimeService.startProcessInstanceById(id, String.valueOf(tableId), map);

        //更新状态
        if (TableEnum.FINANCE_EXPENSE_CLAIM_FORM.key.equals(tableName)) {
            expenseClaimForm.setStatus(2);
            Result result = financeCenterClient.updateExpenseClaimFormStatus(expenseClaimForm);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
        } else if (TableEnum.FINANCE_PREPAY_APPLICATION_FORM.key.equals(tableName)) {
            prepayApplicationForm.setStatus(2);
            Result<Boolean> result = financeCenterClient.updateBorrowMoneyStatus(prepayApplicationForm);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
        } else if (TableEnum.FINANCE_TRAVEL_CLAIM_FORM.key.equals(tableName)) {
            travelClaimForm.setStatus(2);
            Result<Boolean> result = financeCenterClient.updateTravelClaimFormStatus(travelClaimForm);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
        } else if (TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key.equals(tableName)) {
            paymentApplicationForm.setStatus(2);
            Result<Boolean> result = financeCenterClient.updatePaymentApplicationFormStatus(paymentApplicationForm);
            if (!result.isSuccess()) {
                throw new GetServiceException(result.getMessage());
            }
        }

    }

}




