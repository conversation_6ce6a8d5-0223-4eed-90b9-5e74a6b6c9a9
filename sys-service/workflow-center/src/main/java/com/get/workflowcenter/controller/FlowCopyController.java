package com.get.workflowcenter.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.activiti.editor.constants.ModelDataJsonConstants;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.repository.Model;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Service
@RestController
@RequestMapping("/workflow")
public class FlowCopyController {

    @Autowired
    private RepositoryService repositoryService;
    @Resource
    private ObjectMapper objectMapper;

    /**
     * 流程复制业务实现
     *
     * @param
     */
    @GetMapping("/flowCopy")
    public void flowCopy() {

        // 获取复制源流程的模型信息
        byte[] bytes = repositoryService.getModelEditorSource("302558");
        if (bytes == null || bytes.length < 0) {
            // 源流程信息为空,无效复制
        }
        try {
            // 获取流程设计节点信息
            ObjectNode sourceObjectNode = (ObjectNode) new ObjectMapper().readTree(bytes);
            // 创建复制的目标流程模型, 填充部分默认信息
            Model newModel = repositoryService.newModel();
            ObjectNode modelNode = objectMapper.createObjectNode();
            newModel.setKey("m_payment_application_form");
            // 填充模型名称, 其中在activiti7.1.M6版本中该name值必须定义为SpringAutoDeployment
            newModel.setName("支付申请流程");
            newModel.setVersion(1);
            // 其余的信息填充可选填
            modelNode.put(ModelDataJsonConstants.MODEL_NAME, "支付申请流程");
            modelNode.put(ModelDataJsonConstants.MODEL_REVISION, 1);
            modelNode.put(ModelDataJsonConstants.MODEL_DESCRIPTION, "");
            newModel.setMetaInfo(modelNode.toString());
            // 保存模型
            repositoryService.saveModel(newModel);
            // 对源流程信息进行深拷贝
            ObjectNode editorNode = sourceObjectNode.deepCopy();
            // 以下信息可以通过debugger查看源流程信息, 定义新的流程信息后进行覆盖
            ObjectNode properties = objectMapper.createObjectNode();
            // 流程key的唯一性, 复制后的流程key需要更新, 可定义为源key_copy
            properties.put("process_id", "m_payment_application_form");
            properties.put("name", "支付申请流程");
            properties.put("author", "summer");
            // 根据源流程复制后的属性信息覆盖
            editorNode.set("properties", properties);
            // 保存新的流程模型的设计信息
            repositoryService.addModelEditorSource(newModel.getId(), editorNode.toString().getBytes(StandardCharsets.UTF_8));
            // 复制成功, 根据新的流程模型设计流程信息发布, 启动实例验证即可


            // 获取流程设计节点信息
            sourceObjectNode = (ObjectNode) new ObjectMapper().readTree(bytes);
            // 创建复制的目标流程模型, 填充部分默认信息
            newModel = repositoryService.newModel();
            modelNode = objectMapper.createObjectNode();
            newModel.setKey("m_expense_claim_form");
            // 填充模型名称, 其中在activiti7.1.M6版本中该name值必须定义为SpringAutoDeployment
            newModel.setName("费用报销流程");
            newModel.setVersion(1);
            // 其余的信息填充可选填
            modelNode.put(ModelDataJsonConstants.MODEL_NAME, "费用报销流程");
            modelNode.put(ModelDataJsonConstants.MODEL_REVISION, 1);
            modelNode.put(ModelDataJsonConstants.MODEL_DESCRIPTION, "");
            newModel.setMetaInfo(modelNode.toString());
            // 保存模型
            repositoryService.saveModel(newModel);
            // 对源流程信息进行深拷贝
            editorNode = sourceObjectNode.deepCopy();
            // 以下信息可以通过debugger查看源流程信息, 定义新的流程信息后进行覆盖
            properties = objectMapper.createObjectNode();
            // 流程key的唯一性, 复制后的流程key需要更新, 可定义为源key_copy
            properties.put("process_id", "m_expense_claim_form");
            properties.put("name", "费用报销流程");
            properties.put("author", "summer");
            // 根据源流程复制后的属性信息覆盖
            editorNode.set("properties", properties);
            // 保存新的流程模型的设计信息
            repositoryService.addModelEditorSource(newModel.getId(), editorNode.toString().getBytes(StandardCharsets.UTF_8));
            // 复制成功, 根据新的流程模型设计流程信息发布, 启动实例验证即可



            // 获取流程设计节点信息
            sourceObjectNode = (ObjectNode) new ObjectMapper().readTree(bytes);
            // 创建复制的目标流程模型, 填充部分默认信息
            newModel = repositoryService.newModel();
            modelNode = objectMapper.createObjectNode();
            newModel.setKey("m_travel_claim_form");
            // 填充模型名称, 其中在activiti7.1.M6版本中该name值必须定义为SpringAutoDeployment
            newModel.setName("差旅报销流程");
            newModel.setVersion(1);
            // 其余的信息填充可选填
            modelNode.put(ModelDataJsonConstants.MODEL_NAME, "差旅报销流程");
            modelNode.put(ModelDataJsonConstants.MODEL_REVISION, 1);
            modelNode.put(ModelDataJsonConstants.MODEL_DESCRIPTION, "");
            newModel.setMetaInfo(modelNode.toString());
            // 保存模型
            repositoryService.saveModel(newModel);
            // 对源流程信息进行深拷贝
            editorNode = sourceObjectNode.deepCopy();
            // 以下信息可以通过debugger查看源流程信息, 定义新的流程信息后进行覆盖
            properties = objectMapper.createObjectNode();
            // 流程key的唯一性, 复制后的流程key需要更新, 可定义为源key_copy
            properties.put("process_id", "m_travel_claim_form");
            properties.put("name", "差旅报销流程");
            properties.put("author", "summer");
            // 根据源流程复制后的属性信息覆盖
            editorNode.set("properties", properties);
            // 保存新的流程模型的设计信息
            repositoryService.addModelEditorSource(newModel.getId(), editorNode.toString().getBytes(StandardCharsets.UTF_8));
            // 复制成功, 根据新的流程模型设计流程信息发布, 启动实例验证即可
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}
