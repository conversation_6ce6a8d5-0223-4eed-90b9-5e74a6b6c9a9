package com.get.workflowcenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.tool.utils.GeneralTool;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.PrepayApplicationFormVo;
import com.get.workflowcenter.service.IWorkFlowService;
import com.get.workflowcenter.service.ImBorrowMoneyServiec;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.impl.identity.Authentication;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/1 10:33
 */

@Api(tags = "借款流程")
@RestController
@RequestMapping("workflow/borrowFlow")
@Slf4j
public class MborrowMoneyController {

    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private HistoryService historyService;
    //    @Autowired
//    private FeginFinanceService feginFinanceService;
    @Autowired
    private ImBorrowMoneyServiec imBorrowMoneyServiec;
    @Autowired
    private IWorkFlowService iWorkFlowService;


    /**
     * @ Description :借款流程开始
     * @ Param [businessKey, procdefKey, companyId]
     * @ return java.lang.Boolean
     * @ author LEO
     */
    @ApiIgnore
    @ApiOperation("借款流程开始")
    @PostMapping("startBorrowFlow")
    public Boolean startBorrowFlow(@RequestParam("businessKey") String businessKey,
                                   @RequestParam("procdefKey") String procdefKey,
                                   @RequestParam("companyId") String companyId) {
//        if (GeneralTool.isEmpty(businessKey) || GeneralTool.isEmpty(procdefKey) || GeneralTool.isEmpty(companyId)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
//        String username = String.valueOf(GetAuthInfo.getStaffId());
//        PrepayApplicationFormVo mborrowMoneyDto = feginFinanceService.getBorrowById(Long.valueOf(businessKey));
//        if (mborrowMoneyDto == null || mborrowMoneyDto.getStatus() != 0 || mborrowMoneyDto.getStatus() == 5) {
//            return false;
//        }
//        PrepayApplicationForm payForm = BeanCopyUtils.objClone(mborrowMoneyDto, PrepayApplicationForm::new);
//        Map<String, Object> map = new HashMap<>();
//        map.put("userid", username);
//        map.put("prepay", payForm);
//
//
//        try {
//
//            //表名 +公司id
//            List<ProcessDefinition> processDefinition =
//                    repositoryService.createProcessDefinitionQuery().processDefinitionKey(procdefKey).processDefinitionTenantId(companyId).orderByProcessDefinitionVersion().desc().list();
//            String id = "";
//            for (ProcessDefinition pdid : processDefinition) {
//                id = pdid.getId();
//                break;
//            }
//
//            ProcessInstance processInstance = runtimeService.startProcessInstanceById(id, String.valueOf(payForm.getId()), map);
//            payForm.setStatus(2);
//            feginFinanceService.updateBorrowMoneyStatus(payForm);
//            return true;
//        } catch (Exception e) {
//            e.printStackTrace();
//            return false;
//        }
//        return false;

        return imBorrowMoneyServiec.startBorrowFlow(businessKey, procdefKey, companyId);
    }

    /**
     * @ Description :审批
     * @ Param [status, taskId, procInstId, msg]
     * @ return com.get.common.result.ResponseBo
     * @ author LEO
     */
    @ApiOperation("审批")
    @GetMapping("/getExamineAndApprove")
    public ResponseBo getExamineAndApprove(@RequestParam("status") String status, @RequestParam("taskId") String taskId,
                                           @RequestParam("procInstId") String procInstId, @RequestParam("msg") String msg) {

        if (GeneralTool.isEmpty(status) || GeneralTool.isEmpty(taskId) || GeneralTool.isEmpty(msg) || GeneralTool.isEmpty(procInstId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
//        StaffVo staff = StaffContext.getStaff();
        String username = String.valueOf(GetAuthInfo.getStaffId());
        Authentication.setAuthenticatedUserId(username);
        taskService.addComment(taskId, procInstId, msg);
        Map<String, Object> map = new HashMap<>();
        map.put("sequenceFlowsStatus", status);
        if ("0".equals(status)) {
            taskService.setVariableLocal(taskId, "approvalAction", 0);
        } else {
            taskService.setVariableLocal(taskId, "approvalAction", 1);
        }
        taskService.complete(taskId, map);
        iWorkFlowService.getStatusToDoSingle(status, procInstId);

        return ResponseBo.ok();
    }

    /**
     * @ Description : 查询表单所有任务task版本
     * @ Param [businessKey, key]
     * @ return com.get.workflowcenter.vo.ActRuTaskVo
     * @ author LEO
     */
    @ApiIgnore
    @ApiOperation("查询表单所有任务task版本")
    @GetMapping("getPrepayTaskDataByBusinessKey")
    public ActRuTaskVo getPrepayTaskDataByBusinessKey(@RequestParam("businessKey") String businessKey, @RequestParam("procdefKey") String key) {
        return imBorrowMoneyServiec.getTaskDataByBusinessKey(businessKey, key);
    }

    /**
     * @ Description : 显示借款流程流转信息
     * @ Param [businessKey, key]
     * @ return com.get.common.result.ResponseBo<com.get.workflowcenter.vo.PrepayApplicationFormVo>
     * @ author LEO
     */
    @ApiOperation("显示借款流程流转信息")
    @GetMapping("/getPrepayFlowData")
    public ResponseBo<PrepayApplicationFormVo> getPayFlowData(@RequestParam("businessKey") String businessKey,
                                                              @RequestParam("procdefKey") String key) throws GetServiceException {
        List<PrepayApplicationFormVo> mpayDtos = imBorrowMoneyServiec.getPrepayFlowData(businessKey, key);
        ListResponseBo<PrepayApplicationFormVo> mpayDtoListResponseBo = new ListResponseBo<>(mpayDtos);
        return mpayDtoListResponseBo;
    }

    /**
     * @ Description : 判断去待签还是代表页面，0待签，1待办
     * @ Param [taskId, version]
     * @ return int
     * @ author LEO
     */
    @ApiOperation("判断去待签还是代表页面，0待签，1待办")
    @GetMapping("getSignOrGet")
    public int getSignOrGet(@RequestParam(required = false, value = "taskId") String taskId,
                            @RequestParam(required = false, value = "version") Integer version) {
        int signOrGet = imBorrowMoneyServiec.getSignOrGet(taskId, version);
        return signOrGet;
    }

}
