package com.get.workflowcenter.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.get.common.result.Page;
import com.get.workflowcenter.vo.ActReModelVo;
import com.get.workflowcenter.entity.ActReModel;
import com.get.workflowcenter.dto.ActReModelDto;
import com.get.workflowcenter.dto.ActReProcdefDto;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/11/20 16:29
 * @verison: 1.0
 * @description:
 */
public interface IModelService extends IService<ActReModel> {
    /**
     * @return java.lang.String
     * @Description :创建模型
     * @Param [name, key]
     * <AUTHOR>
     */
    String create(String name, String key);

    /**
     * @return java.lang.String
     * @Description :上传模型
     * @Param [modelFile]
     * <AUTHOR>
     */
    String uploadModel(MultipartFile modelFile);

    /**
     * @return void
     * @Description :删除
     * @Param [modelId]
     * <AUTHOR>
     */
    void delete(String modelId);

    /**
     * @return java.util.List<org.activiti.engine.repository.Model>
     * @Description :列表
     * @Param [actReModelDto, page]
     * <AUTHOR>
     */
    List<ActReModelVo> getModels(ActReModelDto actReModelDto, Page page);

    /**
     * @return void
     * @Description :下载模型
     * @Param [modelId, response]
     * <AUTHOR>
     */
    void export(String modelId, HttpServletResponse response);

    /**
     * @return java.lang.String
     * @Description :部署模型
     * @Param [modelId, actReProcdefDto]
     * <AUTHOR>
     */
    void deployModel(ActReProcdefDto actReProcdefDto);

    /**
     * @return void
     * @Description :部署流程
     * @Param [file, tenantId, category]
     * <AUTHOR>
     */
    void deployProcess(MultipartFile file, String tenantId, String category);
}
