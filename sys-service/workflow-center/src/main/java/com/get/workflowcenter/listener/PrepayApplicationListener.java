package com.get.workflowcenter.listener;

import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.financecenter.vo.PrepayApplicationFormVo;
import com.get.financecenter.entity.PrepayApplicationForm;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.workflowcenter.entity.WorkFlowPrepayApplicationForm;
import org.activiti.engine.HistoryService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.history.HistoricActivityInstance;

import java.io.Serializable;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/2 17:31
 */
public class PrepayApplicationListener implements Serializable, ExecutionListener {
    @Override
    public void notify(DelegateExecution delegateExecution) {
        String processInstanceBusinessKey = delegateExecution.getProcessInstanceBusinessKey();
        String processInstanceId = delegateExecution.getProcessInstanceId();
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        IFinanceCenterClient financeCenterClient = SpringUtil.getBean(IFinanceCenterClient.class);
        UtilService utilService = SpringUtil.getBean(UtilService.class);
        if ("end".equals(delegateExecution.getEventName())) {
            List<HistoricActivityInstance> list = historyService.createHistoricActivityInstanceQuery().processInstanceId(processInstanceId).orderByHistoricActivityInstanceStartTime().desc().list();

            Result<PrepayApplicationFormVo> prepayApplicationFormVoResult = financeCenterClient.getBorrowMoneyById(Long.valueOf(processInstanceBusinessKey));
            if (prepayApplicationFormVoResult.isSuccess() && GeneralTool.isNotEmpty(prepayApplicationFormVoResult.getData())) {
                PrepayApplicationFormVo prepayApplicationFormVo = prepayApplicationFormVoResult.getData();
                WorkFlowPrepayApplicationForm prepayApplicationForm = BeanCopyUtils.objClone(prepayApplicationFormVo, WorkFlowPrepayApplicationForm::new);
//                for (HistoricActivityInstance hti : list) {
//                    if (hti.getActivityName() != null && "资料有误重新申请".equals(hti.getActivityName())) {
//                        utilService.updateUserInfoToEntity(prepayApplicationForm);
//                        prepayApplicationForm.setStatus(4);
//                        break;
//                    } else {
//                        utilService.updateUserInfoToEntity(prepayApplicationForm);
//                        prepayApplicationForm.setStatus(1);
//                        break;
//                    }
//                }
                prepayApplicationForm.setStatus(1);
                PrepayApplicationForm prepayApplicationForm_ = BeanCopyUtils.objClone(prepayApplicationForm, PrepayApplicationForm::new);
                financeCenterClient.updateBorrowMoneyStatus(prepayApplicationForm_);
            }

        } else {
            Object sequenceFlowsStatus = delegateExecution.getVariable("sequenceFlowsStatus");
            Result<PrepayApplicationFormVo> prepayApplicationFormVoResult = financeCenterClient.getBorrowMoneyById(Long.valueOf(processInstanceBusinessKey));
            if (prepayApplicationFormVoResult.isSuccess() && GeneralTool.isNotEmpty(prepayApplicationFormVoResult.getData())) {
                PrepayApplicationFormVo prepayApplicationFormVo = prepayApplicationFormVoResult.getData();
                WorkFlowPrepayApplicationForm prepayApplicationForm = BeanCopyUtils.objClone(prepayApplicationFormVo, WorkFlowPrepayApplicationForm::new);
                if ("0".equals(sequenceFlowsStatus)) {
                    utilService.updateUserInfoToEntity(prepayApplicationForm);
                    prepayApplicationForm.setStatus(3);
                } else {
                    utilService.updateUserInfoToEntity(prepayApplicationForm);
                    prepayApplicationForm.setStatus(2);
                }
                PrepayApplicationForm prepayApplicationForm_ = BeanCopyUtils.objClone(prepayApplicationForm, PrepayApplicationForm::new);
                financeCenterClient.updateBorrowMoneyStatus(prepayApplicationForm_);
            }
        }
    }
}
