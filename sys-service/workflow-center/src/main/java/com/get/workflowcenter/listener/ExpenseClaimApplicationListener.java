package com.get.workflowcenter.listener;

import com.get.common.utils.BeanCopyUtils;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.core.tool.utils.SpringUtil;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.entity.PrepayApplicationForm;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.financecenter.vo.PrepayApplicationFormVo;
import com.get.workflowcenter.entity.WorkFlowPrepayApplicationForm;
import org.activiti.engine.HistoryService;
import org.activiti.engine.delegate.DelegateExecution;
import org.activiti.engine.delegate.ExecutionListener;
import org.activiti.engine.history.HistoricActivityInstance;

import java.io.Serializable;
import java.util.List;

/**
 * 费用报销单监听器
 */
public class ExpenseClaimApplicationListener implements Serializable, ExecutionListener {
    @Override
    public void notify(DelegateExecution delegateExecution) {
        String processInstanceBusinessKey = delegateExecution.getProcessInstanceBusinessKey();
        String processInstanceId = delegateExecution.getProcessInstanceId();
        HistoryService historyService = SpringUtil.getBean(HistoryService.class);
        IFinanceCenterClient financeCenterClient = SpringUtil.getBean(IFinanceCenterClient.class);
        UtilService utilService = SpringUtil.getBean(UtilService.class);
        if ("end".equals(delegateExecution.getEventName())) {
            Result<ExpenseClaimForm> result = financeCenterClient.getExpenseClaimFormById(Long.valueOf(processInstanceBusinessKey));
            if (!result.isSuccess()) {
                return;
            }
            ExpenseClaimForm expenseClaimForm = result.getData();
            expenseClaimForm.setStatus(1);
            Result<Boolean> result1 = financeCenterClient.updateExpenseClaimFormStatus(expenseClaimForm);
            if (!result1.isSuccess()) {
                return;
            }
        } else {
            Object sequenceFlowsStatus = delegateExecution.getVariable("sequenceFlowsStatus");
            Result<PrepayApplicationFormVo> prepayApplicationFormVoResult = financeCenterClient.getBorrowMoneyById(Long.valueOf(processInstanceBusinessKey));
            if (prepayApplicationFormVoResult.isSuccess() && GeneralTool.isNotEmpty(prepayApplicationFormVoResult.getData())) {
                PrepayApplicationFormVo prepayApplicationFormVo = prepayApplicationFormVoResult.getData();
                WorkFlowPrepayApplicationForm prepayApplicationForm = BeanCopyUtils.objClone(prepayApplicationFormVo, WorkFlowPrepayApplicationForm::new);
                if ("0".equals(sequenceFlowsStatus)) {
                    utilService.updateUserInfoToEntity(prepayApplicationForm);
                    prepayApplicationForm.setStatus(3);
                } else {
                    utilService.updateUserInfoToEntity(prepayApplicationForm);
                    prepayApplicationForm.setStatus(2);
                }
                PrepayApplicationForm prepayApplicationForm_ = BeanCopyUtils.objClone(prepayApplicationForm, PrepayApplicationForm::new);
                financeCenterClient.updateBorrowMoneyStatus(prepayApplicationForm_);
            }
        }
    }
}
