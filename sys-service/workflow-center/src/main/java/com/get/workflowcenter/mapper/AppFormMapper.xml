<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.workflowcenter.mapper.AppFormMapper">
    <select id="findFormIdsByGeaUserId">
        select a.formId from ApplForm a
        <where>
            <if test="geaUserId!=null and geaUserId !=''" >
                and  a.geaUserId = #{geaUserId}
            </if>
        </where>
    </select>
</mapper>