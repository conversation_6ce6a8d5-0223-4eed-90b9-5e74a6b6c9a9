package com.get.workflowcenter.service.impl;

import com.get.common.eunms.TableEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.GetAuthInfo;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.financecenter.feign.IFinanceCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.permissioncenter.vo.StaffVo;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.PaymentApplicationFormVo;
import com.get.workflowcenter.entity.WorkFlowPaymentApplicationForm;
import com.get.workflowcenter.mapper.ActRuTaskMapper;
import com.get.workflowcenter.service.MpayService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricActivityInstance;
import org.activiti.engine.history.HistoricIdentityLink;
import org.activiti.engine.history.HistoricProcessInstance;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.repository.ProcessDefinition;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Comment;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.get.common.utils.GetDateUtil.dateDiff;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/3/18 10:34
 */
@Service
@Slf4j
public class MpayServiceImpl implements MpayService {

    @Autowired
    private RepositoryService repositoryService;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private IFinanceCenterClient financeCenterClient;
    @Resource
    private ActRuTaskMapper actRuTaskMapper;
    @Resource
    private IPermissionCenterClient permissionCenterClient;

    /**
     * @ Description :详情
     * @ Param [businessKey]
     * @ return java.util.List<com.get.workflowcenter.vo.PaymentApplicationFormVo>
     * @ author LEO
     */
    @Override
    public List<PaymentApplicationFormVo> getPayFlowData(String businessKey, String key) {
        if (StringUtils.isBlank(businessKey)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("parameter_missing"));
        }
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        com.get.financecenter.vo.PaymentApplicationFormVo result_ = financeCenterClient.getMpayById(Long.valueOf(businessKey)).getData();
        PaymentApplicationFormVo mpay = BeanCopyUtils.objClone(result_, PaymentApplicationFormVo::new);
        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceBusinessKey(businessKey).processDefinitionKey(key).singleResult();


        List<PaymentApplicationFormVo> mpayDtoList = new ArrayList<>();
        List<HistoricActivityInstance> list =
                historyService.createHistoricActivityInstanceQuery().processInstanceId(historicProcessInstance.getId()).orderByHistoricActivityInstanceStartTime().asc().list();

        for (HistoricActivityInstance hti : list) {
            PaymentApplicationFormVo newMpayDto = new PaymentApplicationFormVo();
            if ("startEvent".equals(hti.getActivityType())) {
                newMpayDto.setActName("开始流程");
                newMpayDto.setAssignee(mpay.getGmtCreateUser());
                Date startTime = hti.getStartTime();
                newMpayDto.setStartTimeDto(startTime);
                mpayDtoList.add(newMpayDto);

            } else if ("endEvent".equals(hti.getActivityType())) {
                newMpayDto.setActName("流程结束");
                Date startTime = hti.getStartTime();
                newMpayDto.setStartTimeDto(startTime);
                newMpayDto.setEndTimeDto(hti.getEndTime());
                mpayDtoList.add(newMpayDto);
            }
            if (hti.getActivityName() != null) {
                Date startTime = hti.getStartTime();
                String startformat = simpleDateFormat.format(startTime);
                newMpayDto.setStartTimeDto(startTime);
                newMpayDto.setActName(hti.getActivityName());
                if (null != hti.getAssignee()) {
//                    String staffName = permissionCenterClient.getStaffName(Long.valueOf(hti.getAssignee()));
                    Result<String> result = permissionCenterClient.getStaffName(Long.valueOf(hti.getAssignee()));
                    if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
//                        institutionProviderDto.setAssignee(result.getData());
                        newMpayDto.setAssignee(result.getData());
                    }
                }
                if (hti.getEndTime() != null) {
                    Date endTime = hti.getEndTime();
                    String endformat = simpleDateFormat.format(endTime);
                    String durationTime = dateDiff(startformat, endformat, simpleDateFormat.toPattern());
                    newMpayDto.setTotalTime(durationTime);
                    newMpayDto.setEndTimeDto(endTime);
                }
                List<Comment> taskComments = taskService.getTaskComments(hti.getTaskId());
                for (Comment comlist : taskComments) {
                    newMpayDto.setMsg(comlist.getFullMessage());
                }
                mpayDtoList.add(newMpayDto);
            }

        }

        return mpayDtoList;

    }


    @Override
    public List<Long> getPersonalHistoryTasks(String key) {
//        StaffVo staff = StaffContext.getStaff();
        String userid = String.valueOf(GetAuthInfo.getStaffId());

        List<Long> personalHistoryTasks = new ArrayList<>();

        List<HistoricProcessInstance> list =
                historyService.createHistoricProcessInstanceQuery().processDefinitionKey(key).orderByProcessInstanceId().desc().list();
        StringBuffer stringBuffer = new StringBuffer();
        String signe = "";
        for (HistoricProcessInstance historicProcessInstance : list) {
            if (StringUtils.isBlank(stringBuffer)) {
                signe = historicProcessInstance.getId();
                stringBuffer.append(signe);

            }
            if (!historicProcessInstance.getId().equals(signe)) {
                signe = historicProcessInstance.getId();
                stringBuffer.append("," + historicProcessInstance.getId());
            }
        }
        String newid = stringBuffer.toString();
        String[] split = newid.split(",");
        //都是查历史数据 包括正在进行
        for (String st : split) {
            HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(st).singleResult();
            if (historicProcessInstance != null) {
                List<HistoricTaskInstance> taskList = historyService.createHistoricTaskInstanceQuery().processInstanceId(historicProcessInstance.getId()).orderByHistoricTaskInstanceStartTime().desc().list();
                if (taskList.size() != 0) {
                    for (HistoricTaskInstance task : taskList) {
                        if (task.getAssignee() != null) {
                            if (task.getAssignee().equals(userid)) {
                                personalHistoryTasks.add(Long.valueOf(historicProcessInstance.getBusinessKey()));
                                break;
                            }
                        } else {
                            List<HistoricIdentityLink> identityLinksForTask = historyService.getHistoricIdentityLinksForTask(task.getId());
                            if (identityLinksForTask != null && identityLinksForTask.size() != 0) {
                                for (HistoricIdentityLink identityLink : identityLinksForTask) {
                                    if (identityLink.getUserId().equals(userid)) {
                                        personalHistoryTasks.add(Long.valueOf(historicProcessInstance.getBusinessKey()));
                                        break;
                                    }

                                }
                            }
                        }

                    }

                }
            }


        }


        return personalHistoryTasks;
    }

    @Override
    public int getSignOrGet(String taskId, Integer version) {
//        StaffVo staff = StaffContext.getStaff();
        String userid = String.valueOf(GetAuthInfo.getStaffId());

        if (GeneralTool.isNotEmpty(taskId) && GeneralTool.isNotEmpty(version)) {
            Task taskQuery = taskService.createTaskQuery().taskId(taskId).taskAssignee(userid).singleResult();
            if (taskQuery == null) {
                Task task = taskService.createTaskQuery().taskCandidateUser(userid).taskId(taskId).singleResult();
                if (task != null) {
                    ActRuTaskVo actRuTaskVo = actRuTaskMapper.comparisonVersion(task.getId(), version);
                    if (actRuTaskVo != null) {
                        //签收
                        return 0;
                    }
                } else {
                    //什么都不用干
                    return 2;
                }
            } else {
                //1待办
                return 1;
            }
        }
        return 2;
    }


    @Override
    public ActRuTaskVo getTaskDataByBusinessKey(String businessKey, String key) {
        ActRuTaskVo actRuTaskVo = new ActRuTaskVo();

        HistoricProcessInstance historicProcessInstance =
                historyService.createHistoricProcessInstanceQuery().processInstanceBusinessKey(businessKey).processDefinitionKey(key).singleResult();
        if (historicProcessInstance != null) {
            actRuTaskVo.setProcInstId(historicProcessInstance.getId());

            ActRuTaskVo taskVersionByProcessInId = actRuTaskMapper.getTaskVersionByProcessInId(historicProcessInstance.getId());
            if (taskVersionByProcessInId != null) {
                ProcessDefinition processDefinitionQuery = repositoryService.createProcessDefinitionQuery().processDefinitionId(historicProcessInstance.getProcessDefinitionId()).singleResult();
                actRuTaskVo.setId(taskVersionByProcessInId.getId());
                actRuTaskVo.setDeployId(processDefinitionQuery.getDeploymentId());
                actRuTaskVo.setRev(taskVersionByProcessInId.getRev());
                return actRuTaskVo;

            }

        }
        return actRuTaskVo;
    }

    @Override
    public Boolean startPayFlow(String businessKey, String procdefKey, String companyId) {
        if (GeneralTool.isEmpty(businessKey) || GeneralTool.isEmpty(procdefKey) || GeneralTool.isEmpty(companyId)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        String username = String.valueOf(GetAuthInfo.getStaffId());
        Authentication.setAuthenticatedUserId(username);
        try {
            PaymentApplicationFormVo mpayById = null;
            Result<com.get.financecenter.vo.PaymentApplicationFormVo> result = financeCenterClient.getMpayById(Long.valueOf(businessKey));
            if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
                com.get.financecenter.vo.PaymentApplicationFormVo paymentApplicationFormVo = result.getData();
                mpayById = BeanCopyUtils.objClone(paymentApplicationFormVo, PaymentApplicationFormVo::new);
            }
            if (mpayById == null || mpayById.getStatus() != 0 || mpayById.getStatus() == 5) {
                return false;
            }
            Result<StaffVo> result1 = permissionCenterClient.getStaffById(GetAuthInfo.getStaffId());
            if (!result1.isSuccess()) {
                throw new GetServiceException(result1.getMessage());
            }
            StaffVo staffVo = result1.getData();
            WorkFlowPaymentApplicationForm paymentApplicationForm = BeanCopyUtils.objClone(mpayById, WorkFlowPaymentApplicationForm::new);
            Map<String, Object> map = new HashMap<>();
            map.put("userid", username);
            map.put("companyId", companyId);
            map.put("tableName", TableEnum.FINANCE_PAYMENT_APPLICATION_FORM.key);
            map.put("prepay", paymentApplicationForm);
            map.put("staff", staffVo);
            map.put("applyUser", GetAuthInfo.getStaffId());
            //表名 +公司id b
            List<ProcessDefinition> processDefinition = repositoryService.createProcessDefinitionQuery().processDefinitionKey(procdefKey).processDefinitionTenantId(companyId).orderByProcessDefinitionVersion().desc().list();
            String id = "";
            for (ProcessDefinition pdid : processDefinition) {
                id = pdid.getId();
                break;
            }
            Authentication.setAuthenticatedUserId(username);
            ProcessInstance processInstance = runtimeService.startProcessInstanceById(id, String.valueOf(paymentApplicationForm.getId()), map);
            mpayById.setStatus(2);
            com.get.financecenter.entity.PaymentApplicationForm paymentApplicationForm_ = BeanCopyUtils.objClone(mpayById, com.get.financecenter.entity.PaymentApplicationForm::new);
            financeCenterClient.updateMpay(paymentApplicationForm_);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
