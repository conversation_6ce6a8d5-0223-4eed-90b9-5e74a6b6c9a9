package com.get.workflowcenter.service.impl;

import com.get.workflowcenter.entity.ActRuIdentitylink;
import com.get.workflowcenter.mapper.ActRuIdentitylinkMapper;
import com.get.workflowcenter.service.IActRuIdentitylinkService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: Hardy
 * @create: 2021/7/23 17:59
 * @verison: 1.0
 * @description:
 */
@Service
public class ActRuIdentitylinkServiceImpl implements IActRuIdentitylinkService {

    @Resource
    private ActRuIdentitylinkMapper actRuIdentitylinkMapper;

    @Override
    public ActRuIdentitylink getStarterId(String procInstId) {
        return actRuIdentitylinkMapper.getStarterId(procInstId);
    }
}
