package com.get.workflowcenter.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.workflowcenter.entity.WorkFlowType;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface WorkFlowTypeMapper extends BaseMapper<WorkFlowType> {

    /**
     * @return int
     * @Description :新增
     * @Param [record]
     * <AUTHOR>
     */
    int insertSelective(WorkFlowType record);

    /**
     * @return java.lang.String
     * @Description :通过id查找流程类型名称
     * @Param [workFlowTypeId]
     * <AUTHOR>
     */
    String getWorkFlowTypeNameById(@Param("workFlowTypeId") Long workFlowTypeId);

    /**
     * @return java.lang.Integer
     * @Description :获取最大排序值
     * @Param []
     * <AUTHOR>
     */
    Integer getMaxViewOrder();
}