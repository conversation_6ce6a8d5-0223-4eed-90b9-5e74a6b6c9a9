package com.get.platformconfigcenter.service;


/**
 * MSO菜单管理逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/7/27 14:18
 */
public interface SitemapService {
    /**
     * 菜单树
     *
     * @Date 14:28 2021/7/27
     * <AUTHOR>
     */
//    List<SitemapVo> getSitemapTree();


    /**
     * 新增菜单
     *
     * @param sitemapVo
     * @return
     */
//    Long addSitemap(SitemapDto sitemapVo);

    /**
     * 更新菜单
     *
     * @param sitemapVo
     * @return
     */
//    void updateSitemap(SitemapDto sitemapVo);

    /**
     * 菜单详情
     *
     * @param id
     * @return
     */
//    SitemapVo findSitemapById(Long id);

    /**
     * 删除菜单
     *
     * @param id
     * @
     */
//    void delete(Long id);


    /**
     * @Description: 根据父id获取子菜单
     * @Author: Jerry
     * @Date:12:45 2021/8/9
     */
//    List<SitemapVo> getSiteMapByParentId(Long id, String keyWord);


    /**
     * @Description: 上移下移
     * @Author: Jerry
     * @Date:12:49 2021/8/9
     */
//    void movingOrder(List<SitemapDto> sitemapVo);

    /**
     * 模板页面下拉
     *
     * @return
     */
//    List<SitemapPageTemplate> sitemapPageTemplate();
}
