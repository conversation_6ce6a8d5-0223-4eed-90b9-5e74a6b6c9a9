package com.get.platformconfigcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.platformconfigcenter.vo.IssueToRpaFieldMappingVo;
import com.get.platformconfigcenter.service.IssueToRpaFieldMappingService;
import com.get.platformconfigcenter.dto.IssueToRpaFieldMappingDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 10:49
 * Date: 2021/11/8
 * Description:数据字段映射管理
 */
@Api(tags = "数据字段映射管理")
@RestController
@RequestMapping("platform/ISSUE/issueToRpaFieldMapping")
public class IssueToRpaFieldMappingController {
    //TODO 注释ISSUE相关功能 lucky  2024/12/23

//    @Resource
//    private IssueToRpaFieldMappingService issueToRpaFieldMappingService;
//
//    /**
//     * @Description: 数据字段映射列表数据
//     * @Author: Jerry
//     * @Date:11:04 2021/11/8
//     */
//    @ApiOperation(value = "数据字段映射列表数据", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/数据字段映射管理/数据字段映射列表数据")
//    @PostMapping("datas")
//    public ResponseBo<IssueToRpaFieldMappingVo> datas(@RequestBody SearchBean<IssueToRpaFieldMappingDto> page) {
//        List<IssueToRpaFieldMappingVo> datas = issueToRpaFieldMappingService.datas(page.getData(), page);
//        Page p = BeanCopyUtils.objClone(page, Page::new);
//        return new ListResponseBo<>(datas, p);
//    }
//
//    /**
//     * @Description: 新增数据字段映射
//     * @Author: Jerry
//     * @Date:11:04 2021/11/8
//     */
//    @ApiOperation(value = "新增数据字段映射", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/数据字段映射管理/新增数据字段映射")
//    @PostMapping("add")
//    public ResponseBo add(@RequestBody @Validated(IssueToRpaFieldMappingDto.Add.class) IssueToRpaFieldMappingDto issueToRpaFieldMappingDto) {
//        issueToRpaFieldMappingService.add(issueToRpaFieldMappingDto);
//        return SaveResponseBo.ok();
//    }
//
//
//    /**
//     * @Description: 更新数据字段映射
//     * @Author: Jerry
//     * @Date:11:04 2021/11/8
//     */
//    @ApiOperation(value = "更新数据字段映射", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/数据字段映射管理/更新数据字段映射")
//    @PostMapping("update")
//    public ResponseBo update(@RequestBody IssueToRpaFieldMappingDto issueToRpaFieldMappingDto) {
//        issueToRpaFieldMappingService.update(issueToRpaFieldMappingDto);
//        return UpdateResponseBo.ok();
//    }
//
//
//    /**
//     * @Description: 数据字段映射详情
//     * @Author: Jerry
//     * @Date:11:04 2021/11/8
//     */
//    @ApiOperation(value = "数据字段映射详情", notes = "id为此条数据id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/数据字段映射管理/数据字段映射详情")
//    @GetMapping("/{id}")
//    public ResponseBo<IssueToRpaFieldMappingVo> detail(@PathVariable("id") Long id) {
//        return new ResponseBo<>(issueToRpaFieldMappingService.detail(id));
//    }
//
//
//    /**
//     * @Description: 删除数据字段映射
//     * @Author: Jerry
//     * @Date:11:04 2021/11/8
//     */
//    @ApiOperation(value = "删除数据字段映射", notes = "id为此条数据的id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/数据字段映射管理/删除数据字段映射")
//    @PostMapping("delete/{id}")
//    public ResponseBo delete(@PathVariable("id") Long id) {
//        issueToRpaFieldMappingService.delete(id);
//        return DeleteResponseBo.ok();
//    }
}
