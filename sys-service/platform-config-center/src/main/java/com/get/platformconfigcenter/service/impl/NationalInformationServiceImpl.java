package com.get.platformconfigcenter.service.impl;

import com.get.common.eunms.ProjectKeyEnum;
import com.get.core.mybatis.base.UtilService;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.platformconfigcenter.service.DeleteService;
import com.get.platformconfigcenter.service.MediaAndAttachedMsoService;
import com.get.platformconfigcenter.service.NationalInformationService;
import com.get.salecenter.feign.ISaleCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 国家资讯配置模块业务处理类
 *
 * <AUTHOR>
 * @date 2021/5/11 17:43
 */
@Service
public class NationalInformationServiceImpl implements NationalInformationService {
//    @Resource
//    private AgentModuleInfoMapper agentModuleInfoMapper;
//    @Resource
//    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private MediaAndAttachedMsoService mediaAndAttachedMsoService;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    private DeleteService deleteService;
//    @Resource
//    private TranslationMapper translationMapper;

    /**
     * @Description:国家资讯配置列表数据
     * @Param
     * @Date 17:56 2021/5/11
     * <AUTHOR>
     */
//    @Override
//    public List<AgentModuleVo> getAgentModuleList(AgentModuleDto agentModuleVo, Page page) {
////        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
////        Example example = new Example(AgentModuleInfo.class);
////        Example.Criteria criteria = example.createCriteria();
////        if (GeneralTool.isNotEmpty(agentModuleVo.getFkAreaCountryId())) {
////            criteria.andEqualTo("fkAreaCountryId", agentModuleVo.getFkAreaCountryId());
////        }
////        if (GeneralTool.isNotEmpty(agentModuleVo.getColumnTitle())) {
////            criteria.andLike("columnTitle", "%" + agentModuleVo.getColumnTitle() + "%");
////        }
////        if (GeneralTool.isNotEmpty(agentModuleVo.getModuleKey())) {
////            criteria.andEqualTo("moduleKey", agentModuleVo.getModuleKey());
////        }
////        example.orderBy("viewOrder").desc();
////        List<AgentModuleInfo> agentModuleInfos = agentModuleInfoMapper.selectByExample(example);
////        Set<Long> countryIds = agentModuleInfos.stream().map(AgentModuleInfo::getFkAreaCountryId).filter(Objects::nonNull).collect(Collectors.toSet());
////        countryIds.removeIf(Objects::isNull);
//
////        page.restPage(agentModuleInfos);
//        LambdaQueryWrapper<AgentModuleInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        if (GeneralTool.isNotEmpty(agentModuleVo.getFkAreaCountryId())) {
////            criteria.andEqualTo("fkAreaCountryId", agentModuleVo.getFkAreaCountryId());
//            lambdaQueryWrapper.eq(AgentModuleInfo::getFkAreaCountryId, agentModuleVo.getFkAreaCountryId());
//        }
//        if (GeneralTool.isNotEmpty(agentModuleVo.getColumnTitle())) {
//            lambdaQueryWrapper.like(AgentModuleInfo::getColumnTitle, agentModuleVo.getColumnTitle());
//        }
//        if (GeneralTool.isNotEmpty(agentModuleVo.getModuleKey())) {
//            lambdaQueryWrapper.eq(AgentModuleInfo::getModuleKey, agentModuleVo.getModuleKey());
//        }
//        lambdaQueryWrapper.orderByDesc(AgentModuleInfo::getViewOrder);
//        IPage<AgentModuleInfo> pages = agentModuleInfoMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
//        List<AgentModuleInfo> agentModuleInfos = pages.getRecords();
//        page.setAll((int) pages.getTotal());
//        Set<Long> countryIds = agentModuleInfos.stream().map(AgentModuleInfo::getFkAreaCountryId).filter(Objects::nonNull).collect(Collectors.toSet());
//        Result<Map<Long, String>> resultcountryNameMap = institutionCenterClient.getCountryNamesByIds(countryIds);
//        Map<Long, String> countryNameMap = new HashMap<>();
//        if (resultcountryNameMap.isSuccess()) {
//            countryNameMap = resultcountryNameMap.getData();
//        }
//        Set<Long> agentIdSet = agentModuleInfos.stream().map(AgentModuleInfo::getFkAgentId).collect(Collectors.toSet());
//        agentIdSet.removeIf(Objects::isNull);
//        List<AgentModuleVo> agentModuleDtoList = new ArrayList<>();
//        Map<Long, String> agentNameMap = new HashMap<>();
//        //111111
//        Result<Map<Long, String>> resultagentNameMap = saleCenterClient.getAgentNamesByIds(agentIdSet);
//        if (resultagentNameMap.isSuccess()) {
//            agentNameMap = resultagentNameMap.getData();
//        }
//        for (AgentModuleInfo agentModuleInfo : agentModuleInfos) {
//            AgentModuleVo agentModuleDto = BeanCopyUtils.objClone(agentModuleInfo, AgentModuleVo::new);
//            agentModuleDto.setModuleName(ProjectKeyEnum.getValue(agentModuleInfo.getModuleKey()));
//            agentModuleDto.setCountryName(countryNameMap.get(agentModuleInfo.getFkAreaCountryId()));
//            agentModuleDto.setFkTableName(TableEnum.AGENT_MODULE_INFO.key);
//            agentModuleDto.setFkAgentName(agentNameMap.get(agentModuleInfo.getFkAgentId()));
//            agentModuleDtoList.add(agentModuleDto);
//        }
//        return agentModuleDtoList;
//    }

    /**
     * @Description:新增国家资讯
     * @Param
     * @Date 10:30 2021/5/12
     * <AUTHOR>
     */
//    @Override
//    public Long addAgentModule(AgentModuleDto agentModuleVo) {
//        //新闻/资讯类型”与“页面组件名称”必填其一
//        if (GeneralTool.isEmpty(agentModuleVo.getFkInfoTypeId()) && GeneralTool.isEmpty(agentModuleVo.getComponentName())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("info_type_or_component_name_least_one"));
//        }
//        if (GeneralTool.isEmpty(agentModuleVo.getMediaAttachedVos())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
//        AgentModuleInfo agentModuleInfo = BeanCopyUtils.objClone(agentModuleVo, AgentModuleInfo::new);
//        utilService.updateUserInfoToEntity(agentModuleInfo);
//        //获取最大排序值
//        agentModuleInfo.setViewOrder(agentModuleInfoMapper.getMaxViewOrder());
//        agentModuleInfoMapper.insert(agentModuleInfo);
//        //保存图片
//        List<MediaAndAttachedVo> mediaAndAttachedVoList = agentModuleVo.getMediaAttachedVos();
//        for (MediaAndAttachedVo mediaAndAttachedVo : mediaAndAttachedVoList) {
//            mediaAndAttachedVo.setTypeKey(FileTypeEnum.PLATFORM_MSO_COLUMN_PIC.key);
//            mediaAndAttachedVo.setFkTableId(agentModuleInfo.getId());
//            mediaAndAttachedVo.setFkTableName(TableEnum.AGENT_MODULE_INFO.key);
//            mediaAndAttachedMsoService.addMediaAndAttached(mediaAndAttachedVo);
//        }
//        return agentModuleInfo.getId();
//    }

    /**
     * @Description:更新国家资讯
     * @Param
     * @Date 14:52 2021/5/12
     * <AUTHOR>
     */
//    @Override
//    public AgentModuleVo updateAgentModule(AgentModuleDto agentModuleVo) {
//        if (GeneralTool.isEmpty(agentModuleVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//        //新闻/资讯类型”与“页面组件名称”必填其一
//        if (GeneralTool.isEmpty(agentModuleVo.getFkInfoTypeId()) && GeneralTool.isEmpty(agentModuleVo.getComponentName())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("info_type_or_component_name_least_one"));
//        }
//        AgentModuleInfo agentModuleInfo = agentModuleInfoMapper.selectById(agentModuleVo.getId());
//        if (GeneralTool.isEmpty(agentModuleInfo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        AgentModuleInfo agentModule = BeanCopyUtils.objClone(agentModuleVo, AgentModuleInfo::new);
//        utilService.updateUserInfoToEntity(agentModule);
//        agentModuleInfoMapper.updateByPrimaryKeySelective(agentModule);
//        List<MediaAndAttachedVo> mediaAttachedVos = agentModuleVo.getMediaAttachedVos();
////        Example example = new Example(MediaAndAttached.class);
////        example.createCriteria().andEqualTo("fkTableName", TableEnum.AGENT_MODULE_INFO.key)
////                .andEqualTo("fkTableId", agentModuleVo.getId()).andEqualTo("typeKey", FileTypeEnum.PLATFORM_MSO_COLUMN_PIC.key);
////        mediaAndAttachedMapper.deleteByExample(example);
//
//        mediaAndAttachedMapper.delete(Wrappers.<ConfigMediaAndAttached>lambdaQuery()
//                .eq(ConfigMediaAndAttached::getFkTableName, TableEnum.AGENT_MODULE_INFO.key)
//                .eq(ConfigMediaAndAttached::getFkTableId, agentModuleVo.getId())
//                .eq(ConfigMediaAndAttached::getTypeKey, FileTypeEnum.PLATFORM_MSO_COLUMN_PIC.key));
//
//        for (MediaAndAttachedVo mediaAndAttachedVo : mediaAttachedVos) {
//            mediaAndAttachedVo.setTypeKey(FileTypeEnum.PLATFORM_MSO_COLUMN_PIC.key);
//            mediaAndAttachedVo.setFkTableId(agentModuleInfo.getId());
//            mediaAndAttachedVo.setFkTableName(TableEnum.AGENT_MODULE_INFO.key);
//            mediaAndAttachedMsoService.addMediaAndAttached(mediaAndAttachedVo);
//        }
//        return findAgentModuleById(agentModule.getId());
//    }

    /**
     * @Description:国家资讯配置详情
     * @Param
     * @Date 11:31 2021/5/12
     * <AUTHOR>
     */
//    @Override
//    public AgentModuleVo findAgentModuleById(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        AgentModuleInfo agentModuleInfo = agentModuleInfoMapper.selectById(id);
//        if (GeneralTool.isEmpty(agentModuleInfo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        AgentModuleVo agentModuleDto = BeanCopyUtils.objClone(agentModuleInfo, AgentModuleVo::new);
//        MediaAndAttachedVo attachedVo = new MediaAndAttachedVo();
//        attachedVo.setFkTableName(TableEnum.AGENT_MODULE_INFO.key);
//        attachedVo.setFkTableId(id);
//        List<MediaAndAttachedDto> mediaAndAttachedDtos = mediaAndAttachedMsoService.getMediaAndAttachedVo(attachedVo);
//        agentModuleDto.setMediaAndAttachedDtoList(mediaAndAttachedDtos);
//        return agentModuleDto;
//    }

    /**
     * @Description:国家资讯配置删除
     * @Param
     * @Date 11:31 2021/5/12
     * <AUTHOR>
     */
//    @Override
//    public void delete(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        boolean succuee = deleteService.deleteValidateAgentModuleInfo(id);
//        if (succuee) {
//            agentModuleInfoMapper.deleteById(id);
//        }
//
//        //删除翻译内容
//        translationMapper.deleteTranslations(TableEnum.AGENT_MODULE_INFO.key, id);
//    }

    /**
     * @Description:上移下移
     * @Param
     * @Date 12:49 2021/5/12
     * <AUTHOR>
     */
//    @Override
//    public void movingOrder(List<AgentModuleDto> agentModuleVoList) {
//        if (GeneralTool.isEmpty(agentModuleVoList)) {
//            throw new GetServiceException(ResultCode.INVALID_PARAM, LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//        AgentModuleInfo ro = BeanCopyUtils.objClone(agentModuleVoList.get(0), AgentModuleInfo::new);
//        Integer oneorder = ro.getViewOrder();
//        AgentModuleInfo rt = BeanCopyUtils.objClone(agentModuleVoList.get(1), AgentModuleInfo::new);
//        Integer twoorder = rt.getViewOrder();
//        ro.setViewOrder(twoorder);
//        utilService.updateUserInfoToEntity(ro);
//        rt.setViewOrder(oneorder);
//        utilService.updateUserInfoToEntity(rt);
//        agentModuleInfoMapper.updateByPrimaryKey(ro);
//        agentModuleInfoMapper.updateByPrimaryKey(rt);
//    }

    /**
     * @Description:国家资讯模块类型下拉框数据
     * @Param
     * @Date 15:49 2021/5/12
     * <AUTHOR>
     */
    @Override
    public List<Map<String, Object>> getModuleKey() {
        return ProjectKeyEnum.enumsTranslation2Arrays(ProjectKeyEnum.AGENT_MODULE);
    }


}
