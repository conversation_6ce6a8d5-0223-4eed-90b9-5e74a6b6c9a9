package com.get.platformconfigcenter.service.impl;

import com.get.core.mybatis.base.UtilService;
import com.get.platformconfigcenter.service.MediaAndAttachedMsoService;
import com.get.platformconfigcenter.service.SitemapPageTemplateService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @DATE: 2021/12/3
 * @TIME: 16:55
 * @Description:
 **/
@Service
public class SitemapPageTemplateServiceImpl implements SitemapPageTemplateService {

    @Resource
    private UtilService utilService;
//    @Resource
//    private SitemapPageTemplateMapper sitemapPageTemplateMapper;
//    @Resource
//    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private MediaAndAttachedMsoService mediaAndAttachedMsoService;

//    @Override
//    public List<SitemapPageTemplateVo> getSitemapPageTemplateList(SitemapPageTemplateDto sitemapPageTemplateVo, Page page) {
//        LambdaQueryWrapper<SitemapPageTemplate> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(SitemapPageTemplate::getName, sitemapPageTemplateVo.getName());
//        IPage<SitemapPageTemplate> iPage = sitemapPageTemplateMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
//        page.setAll((int) iPage.getTotal());
//        List<SitemapPageTemplate> sitemapPageTemplates = iPage.getRecords();
//        List<SitemapPageTemplateVo> sitemapPageTemplateDtos = sitemapPageTemplates.stream()
//                .map(sitemapPageTemplate -> BeanCopyUtils.objClone(sitemapPageTemplate, SitemapPageTemplateVo::new))
//                .collect(Collectors.toList());
//        return sitemapPageTemplateDtos;
//    }
//
//    @Override
//    public Long addSitemapPageTemplate(SitemapPageTemplateDto sitemapPageTemplateVo) {
//        if (GeneralTool.isEmpty(sitemapPageTemplateVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
//        SitemapPageTemplate sitemapPageTemplate = BeanCopyUtils.objClone(sitemapPageTemplateVo, SitemapPageTemplate::new);
//        utilService.updateUserInfoToEntity(sitemapPageTemplate);
//        sitemapPageTemplateMapper.insert(sitemapPageTemplate);
//        //保存图片
//        List<MediaAndAttachedVo> mediaAndAttachedVoList = sitemapPageTemplateVo.getMediaAttachedVos();
//        if (GeneralTool.isNotEmpty(mediaAndAttachedVoList)) {
//            for (MediaAndAttachedVo mediaAndAttachedVo : mediaAndAttachedVoList) {
//                mediaAndAttachedVo.setTypeKey(FileTypeEnum.PLATFORM_MSO_AGENT_SITEMAP_PAGE_TEMPLATE_PIC.key);
//                mediaAndAttachedVo.setFkTableId(sitemapPageTemplate.getId());
//                mediaAndAttachedVo.setFkTableName(TableEnum.M_SITEMAP_PAGE_TEMPLATE.key);
//                mediaAndAttachedMsoService.addMediaAndAttached(mediaAndAttachedVo);
//            }
//        }
//        return sitemapPageTemplate.getId();
//    }
//
//    @Override
//    public SitemapPageTemplateVo updateSitemapPageTemplate(SitemapPageTemplateDto sitemapPageTemplateVo) {
//        if (sitemapPageTemplateVo == null) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//        SitemapPageTemplate result = sitemapPageTemplateMapper.selectById(sitemapPageTemplateVo.getId());
//        if (result == null) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        SitemapPageTemplate sitemapPageTemplate = BeanCopyUtils.objClone(sitemapPageTemplateVo, SitemapPageTemplateVo::new);
//        utilService.updateUserInfoToEntity(sitemapPageTemplate);
//        sitemapPageTemplateMapper.updateById(sitemapPageTemplate);
//        //删除图片
//        LambdaQueryWrapper<ConfigMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(ConfigMediaAndAttached::getFkTableId, sitemapPageTemplate.getId());
//        lambdaQueryWrapper.eq(ConfigMediaAndAttached::getTypeKey, FileTypeEnum.PLATFORM_MSO_AGENT_SITEMAP_PAGE_TEMPLATE_PIC.key);
//        mediaAndAttachedMapper.delete(lambdaQueryWrapper);
//
//        //保存图片
//        List<MediaAndAttachedVo> mediaAndAttachedVoList = sitemapPageTemplateVo.getMediaAttachedVos();
//        if (GeneralTool.isNotEmpty(mediaAndAttachedVoList)) {
//            for (MediaAndAttachedVo mediaAndAttachedVo : mediaAndAttachedVoList) {
//                mediaAndAttachedVo.setTypeKey(FileTypeEnum.PLATFORM_MSO_AGENT_SITEMAP_PAGE_TEMPLATE_PIC.key);
//                mediaAndAttachedVo.setFkTableId(sitemapPageTemplate.getId());
//                mediaAndAttachedVo.setFkTableName(TableEnum.M_SITEMAP_PAGE_TEMPLATE.key);
//                mediaAndAttachedMsoService.addMediaAndAttached(mediaAndAttachedVo);
//            }
//        }
//        return findSitemapPageTemplateById(sitemapPageTemplate.getId());
//    }
//
//    @Override
//    public SitemapPageTemplateVo findSitemapPageTemplateById(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        SitemapPageTemplate sitemapPageTemplate = sitemapPageTemplateMapper.selectById(id);
//        if (GeneralTool.isEmpty(sitemapPageTemplate)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        SitemapPageTemplateVo sitemapPageTemplateDto = BeanCopyUtils.objClone(sitemapPageTemplate, SitemapPageTemplateVo::new);
//
//        //查询图片
//        MediaAndAttachedVo attachedVo = new MediaAndAttachedVo();
//        attachedVo.setFkTableName(TableEnum.M_SITEMAP_PAGE_TEMPLATE.key);
//        attachedVo.setFkTableId(id);
//        List<MediaAndAttachedDto> mediaAndAttachedDtos = mediaAndAttachedMsoService.getMediaAndAttachedVo(attachedVo);
//        sitemapPageTemplateDto.setMediaAndAttachedDtoList(mediaAndAttachedDtos);
//        return sitemapPageTemplateDto;
//    }
//
//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void delete(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        if (sitemapPageTemplateMapper.selectById(id) == null) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_obj_null"));
//        }
//        //同时删除该表id下的所有媒体附件
////        Example example = new Example(MediaAndAttached.class);
////        example.createCriteria().andEqualTo("fkTableName", TableEnum.M_SITEMAP_PAGE_TEMPLATE.key)
////                .andEqualTo("fkTableId", id).andEqualTo("typeKey", FileTypeEnum.PLATFORM_MSO_AGENT_SITEMAP_PAGE_TEMPLATE_PIC.key);
////        mediaAndAttachedMapper.deleteByExample(example);
//
//        LambdaQueryWrapper<ConfigMediaAndAttached> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(ConfigMediaAndAttached::getFkTableName, TableEnum.M_SITEMAP_PAGE_TEMPLATE.key);
//        lambdaQueryWrapper.eq(ConfigMediaAndAttached::getFkTableId, id);
//        lambdaQueryWrapper.eq(ConfigMediaAndAttached::getTypeKey, FileTypeEnum.PLATFORM_MSO_AGENT_SITEMAP_PAGE_TEMPLATE_PIC.key);
//        mediaAndAttachedMapper.delete(lambdaQueryWrapper);
//        sitemapPageTemplateMapper.deleteById(id);
//    }
}
