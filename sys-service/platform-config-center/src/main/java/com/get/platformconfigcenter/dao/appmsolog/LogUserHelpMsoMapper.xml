<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appmsolog.LogUserHelpMsoMapper">
    <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.LogUserHelpMso">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="fk_user_id" jdbcType="BIGINT" property="fkUserId"/>
        <result column="fk_help_id" jdbcType="BIGINT" property="fkHelpId"/>
        <result column="help_title" jdbcType="VARCHAR" property="helpTitle"/>
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate"/>
        <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser"/>
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified"/>
        <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser"/>
    </resultMap>
    <resultMap extends="BaseResultMap" id="JoinResultMap" type="com.get.platformconfigcenter.vo.LogUserHelpMsoVo">
        <result column="lastGmtCreate" jdbcType="VARCHAR" property="lastGmtCreate"/>
        <result column="firstGmtCreate" jdbcType="VARCHAR" property="firstGmtCreate"/>
    </resultMap>
  <!--  <insert id="insert" parameterType="com.get.platformconfigcenter.entity.LogUserHelpMso">
        insert into log_user_help_mso (id, fk_user_id, fk_help_id,
                                       help_title, gmt_create, gmt_create_user,
                                       gmt_modified, gmt_modified_user)
        values (#{id,jdbcType=BIGINT}, #{fkUserId,jdbcType=BIGINT}, #{fkHelpId,jdbcType=BIGINT},
                #{helpTitle,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR},
                #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})
    </insert>-->
    <!--<insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.LogUserHelpMso">
        insert into log_user_help_mso
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="fkUserId != null">
                fk_user_id,
            </if>
            <if test="fkHelpId != null">
                fk_help_id,
            </if>
            <if test="helpTitle != null">
                help_title,
            </if>
            <if test="gmtCreate != null">
                gmt_create,
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user,
            </if>
            <if test="gmtModified != null">
                gmt_modified,
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="fkUserId != null">
                #{fkUserId,jdbcType=BIGINT},
            </if>
            <if test="fkHelpId != null">
                #{fkHelpId,jdbcType=BIGINT},
            </if>
            <if test="helpTitle != null">
                #{helpTitle,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>-->
    <!--<update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.LogUserHelpMso">
        update log_user_help_mso
        <set>
            <if test="fkUserId != null">
                fk_user_id = #{fkUserId,jdbcType=BIGINT},
            </if>
            <if test="fkHelpId != null">
                fk_help_id = #{fkHelpId,jdbcType=BIGINT},
            </if>
            <if test="helpTitle != null">
                help_title = #{helpTitle,jdbcType=VARCHAR},
            </if>
            <if test="gmtCreate != null">
                gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtCreateUser != null">
                gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
            </if>
            <if test="gmtModified != null">
                gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
            </if>
            <if test="gmtModifiedUser != null">
                gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.LogUserHelpMso">
        update log_user_help_mso
        set fk_user_id        = #{fkUserId,jdbcType=BIGINT},
            fk_help_id        = #{fkHelpId,jdbcType=BIGINT},
            help_title        = #{helpTitle,jdbcType=VARCHAR},
            gmt_create        = #{gmtCreate,jdbcType=TIMESTAMP},
            gmt_create_user   = #{gmtCreateUser,jdbcType=VARCHAR},
            gmt_modified      = #{gmtModified,jdbcType=TIMESTAMP},
            gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>-->
    <!--<select id="selectUserIntentionRecordList" resultMap="JoinResultMap">
        &#45;&#45;         SELECT
&#45;&#45;             luhm.*
&#45;&#45;         FROM
&#45;&#45;             log_user_help_mso AS luhm
&#45;&#45;                 INNER JOIN ( SELECT fk_user_id, MAX( gmt_create ) AS lastGmtCreate FROM log_user_help_mso GROUP BY fk_user_id ) AS m ON m.fk_user_id = luhm.fk_user_id
&#45;&#45;                 AND m.lastGmtCreate = luhm.gmt_create
&#45;&#45;                 LEFT JOIN ( SELECT fk_user_id, MIN( gmt_create ) AS firstGmtCreate FROM log_user_help_mso GROUP BY fk_user_id ) AS pm ON pm.fk_user_id = luhm.fk_user_id
&#45;&#45;         ORDER BY
&#45;&#45;             luhm.gmt_create DESC
        SELECT b.fk_user_id,
               b.lastGmtCreate,
               pm.firstGmtCreate
        FROM (
                 SELECT *
                 FROM (
                          SELECT luhm.fk_user_id,
                                 m.lastGmtCreate
                          FROM log_user_help_mso AS luhm
                                   INNER JOIN (SELECT fk_user_id, MAX(gmt_create) AS lastGmtCreate
                                               FROM log_user_help_mso
                                               GROUP BY fk_user_id) AS m ON m.fk_user_id = luhm.fk_user_id
                              AND m.lastGmtCreate = luhm.gmt_create
                      ) a
                 GROUP BY a.fk_user_id
             ) b
                 LEFT JOIN (SELECT fk_user_id, MIN(gmt_create) AS firstGmtCreate
                            FROM log_user_help_mso
                            GROUP BY fk_user_id) AS pm ON pm.fk_user_id = b.fk_user_id
        ORDER BY b.lastGmtCreate DESC
    </select>-->


</mapper>