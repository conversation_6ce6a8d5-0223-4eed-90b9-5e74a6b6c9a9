package com.get.platformconfigcenter.service.impl;


import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.utils.LocaleMessageUtils;
import com.get.common.utils.SymmetricEncoder;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
//import com.get.platformconfigcenter.dao.appissue.StudentInstitutionCourseMapper;
import com.get.platformconfigcenter.dao.appissue.StudentMapper;
import com.get.platformconfigcenter.entity.PlatFormStudent;
import com.get.platformconfigcenter.service.IIssueStudentService;
import com.get.remindercenter.feign.IReminderCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/7/6 17:57
 */
@Service
public class IssueStudentServiceImpl implements IIssueStudentService {

    @Resource
    private StudentMapper studentMapper;
//    @Resource
//    private StudentInstitutionCourseMapper studentInstitutionCourseMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private IReminderCenterClient iReminderCenterClient;

    //TODO 注释ISSUE相关功能 lucky  2024/12/23
//    @Override
//    public List<StudentInstitutionCourseVo> getIssueStudentCourseByStuId(Long fkStudentId) {
//
//        if (ObjectUtils.isEmpty(fkStudentId)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        ArrayList<StudentInstitutionCourseVo> coursesList = new ArrayList<>();
//
//        //根据代理id 找全部属于这个代理的学生和对应学生的课程
////        Example courseSelect = new Example(StudentInstitutionCourse.class);
////        courseSelect.createCriteria().andEqualTo("fkStudentId", fkStudentId);
////        List<StudentInstitutionCourse> studentInstitutionCourses = studentInstitutionCourseMapper.selectByExample(courseSelect);
//        List<StudentInstitutionCourse> studentInstitutionCourses = studentInstitutionCourseMapper.selectList(Wrappers.<StudentInstitutionCourse>lambdaQuery().eq(StudentInstitutionCourse::getFkStudentId, fkStudentId));
//        if (GeneralTool.isNotEmpty(studentInstitutionCourses)) {
//
//            //查找学校名字，课程名字，等级名字。 111111
//            studentInstitutionCourses.stream().filter(Objects::nonNull).forEach(studentInstitutionCourse -> {
//                StudentInstitutionCourseVo studentInstitutionCourseDto = BeanCopyUtils.objClone(studentInstitutionCourse, StudentInstitutionCourseVo::new);
//                Result<String> resultInstitutionNames = institutionCenterClient.getInstitutionNamesById(studentInstitutionCourse.getFkInstitutionId());
//                if (resultInstitutionNames.isSuccess()) {
//                    studentInstitutionCourseDto.setSchoolName(resultInstitutionNames.getData());
//                }
//                if (GeneralTool.isNotEmpty(studentInstitutionCourse.getFkInstitutionCourseId())) {
//                    Result<String> resultInstitutionCourseName = institutionCenterClient.getInstitutionCourseNameById(studentInstitutionCourse.getFkInstitutionCourseId());
//                    if (resultInstitutionCourseName.isSuccess()) {
//                        studentInstitutionCourseDto.setCourseName(resultInstitutionCourseName.getData());
//                    }
//                } else {
//                    studentInstitutionCourseDto.setInstitutionCourseName(studentInstitutionCourse.getInstitutionCourseName());
//                }
//                Result<String> resultMajorLevelNames = institutionCenterClient.getMajorLevelNamesById(studentInstitutionCourse.getFkMajorLevelId());
//                if (resultMajorLevelNames.isSuccess()) {
//                    studentInstitutionCourseDto.setMajorLeveName(resultMajorLevelNames.getData());
//                }
//
//                coursesList.add(studentInstitutionCourseDto);
//            });
//        }
//
//
//        return coursesList;
//    }

//TODO 注释ISSUE相关功能 lucky  2024/12/23

//    @Override
//    public void updateIssueCourseStatus(Integer statusStep, Long courseId) {
//        StudentInstitutionCourse studentInstitutionCourse = studentInstitutionCourseMapper.selectById(courseId);
//        if (studentInstitutionCourse == null) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        studentInstitutionCourse.setStatusStep(statusStep);
//        utilService.updateUserInfoToEntity(studentInstitutionCourse);
//        studentInstitutionCourseMapper.updateById(studentInstitutionCourse);
//
//    }

    @Override
    public String getCipherText() {
        String encode = SymmetricEncoder.AESEncode("AES", "get_bms");
        return encode;
    }

    @Override
    public void updateStudentAgent(Long fkSutdentId, Long fkAgentId) {
        PlatFormStudent platFormStudent = studentMapper.selectById(fkSutdentId);
        if (GeneralTool.isEmpty(platFormStudent)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        platFormStudent.setFkAgentId(fkAgentId);
        utilService.setUpdateInfo(platFormStudent);
        studentMapper.updateById(platFormStudent);
    }

//    @Override
//    public void getChangeIssueStudentSataus(Long fkIssueStudentsId, Integer status) {
//        PlatFormStudent platFormStudent = studentMapper.selectById(fkIssueStudentsId);
//        if (GeneralTool.isEmpty(platFormStudent)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        StringBuilder stringBuilder = new StringBuilder();
//        stringBuilder.append("您issue账号下的学生").append(platFormStudent.getNameZh()).append("提交状态已变更");
//        platFormStudent.setStatus(status);
//        utilService.setUpdateInfo(platFormStudent);
//        studentMapper.updateById(platFormStudent);
//        iReminderCenterClient.customSendMail("utf-8",
//                "smtp.exmail.qq.com",
//                465,
//                "smtps",
//                "<EMAIL>",
//                "GZEtLhkf6WzpcPd9",
//                "Issue学生状态变更",
//                platFormStudent.getFollowUpEmail1(), "<EMAIL>", stringBuilder.toString(), false);
//    }

//    @Override
//    public List<Long> getIssueStuIdsByAgentId(Long fkAgentId) {
//        List<PlatFormStudent> platFormStudents = studentMapper.selectList(Wrappers.<PlatFormStudent>lambdaQuery().eq(PlatFormStudent::getFkAgentId, fkAgentId));
//        return platFormStudents.stream().map(PlatFormStudent::getId).collect(Collectors.toList());
//    }

//    @Override
//    public Boolean updateIssueStudentInstitutionCourse(StudentInstitutionCourse studentInstitutionCourse) {
//        if(GeneralTool.isEmpty(studentInstitutionCourse.getId()))
//        {
//            return false;
//        }
//        studentInstitutionCourseMapper.updateById(studentInstitutionCourse);
//        return true;
//    }

//    @Override
//    public Boolean updateIssueStudentInstitutionCourseWithNull(StudentInstitutionCourse studentInstitutionCourse) {
//        if(GeneralTool.isEmpty(studentInstitutionCourse.getId()))
//        {
//            return false;
//        }
//        studentInstitutionCourseMapper.updateByIdWithNull(studentInstitutionCourse);
//        return true;
//    }

//    @Override
//    public StudentInstitutionCourseVo getIssueStudentInstitutionCourseById(Long fkIssueCourseId) {
//        StudentInstitutionCourse studentInstitutionCourse = studentInstitutionCourseMapper.selectById(fkIssueCourseId);
//        if (GeneralTool.isEmpty(studentInstitutionCourse)) {
//            return null;
//        }
//        return BeanCopyUtils.objClone(studentInstitutionCourse, StudentInstitutionCourseVo::new);
//    }

}
