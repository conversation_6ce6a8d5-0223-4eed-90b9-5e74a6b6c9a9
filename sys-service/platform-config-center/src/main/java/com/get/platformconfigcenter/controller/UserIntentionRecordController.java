package com.get.platformconfigcenter.controller;

import com.get.platformconfigcenter.service.UserIntentionRecordService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 平台用户意向记录管理
 *
 * <AUTHOR>
 * @date 2021/9/7 15:27
 */
@Api(tags = "平台用户意向记录管理")
@RestController
@RequestMapping("/platform/intentionRecord")
public class UserIntentionRecordController {

    @Resource
    private UserIntentionRecordService userIntentionRecordService;

    /**
     * 平台用户意向记录列表
     *
     * @Date 15:32 2021/9/7
     * <AUTHOR>
     */
 /*   @ApiOperation(value = "平台用户意向记录列表", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/平台用户意向记录列表")
    @PostMapping("datas")
    public ResponseBo<LogUserHelpMsoVo> datas(@RequestBody SearchBean<LogUserHelpMsoListDto> page) {
        List<LogUserHelpMsoVo> datas = userIntentionRecordService.getUserIntentionRecordList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }*/

    /**
     * 明细意向记录
     *
     * @Date 17:45 2021/9/7
     * <AUTHOR>
     */
/*    @ApiOperation(value = "明细意向记录", notes = "id为用户id")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/平台用户意向记录/明细意向记录")
    @GetMapping("/detail/{userId}")
    public ResponseBo<LogUserHelpMsoVo> detail(@PathVariable("userId") Long id) {
        List<LogUserHelpMsoVo> data = userIntentionRecordService.detail(id);
        return new ListResponseBo<>(data);
    }*/

    /**
     * 意向摘要
     *
     * @Date 10:23 2021/9/8
     * <AUTHOR>
     */
/*    @ApiOperation(value = "意向摘要", notes = "id为用户id")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/平台用户意向记录/意向摘要")
    @GetMapping("/intention/{userId}")
    public ResponseBo<HelpDto1> intention(@PathVariable("userId") Long id) {
        List<HelpDto1> data = userIntentionRecordService.intention(id);
        return new ListResponseBo<>(data);
    }*/

}
