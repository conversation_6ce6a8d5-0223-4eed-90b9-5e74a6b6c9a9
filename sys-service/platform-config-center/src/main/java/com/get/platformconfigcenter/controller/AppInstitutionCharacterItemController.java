package com.get.platformconfigcenter.controller;

import com.get.platformconfigcenter.service.AppInstitutionCharacterItemService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 课程动态表单详情配置控制类
 *
 * <AUTHOR>
 * @date 2021/5/20 12:27
 */
@Api(tags = "课程动态表单详情配置")
@RestController
@RequestMapping("/platform/ISSUE/institutionCharacter/item")
public class AppInstitutionCharacterItemController {
    @Resource
    private AppInstitutionCharacterItemService appInstitutionCharacterItemService;

//    @ApiOperation(value = "Item-课程动态表单配置详情列表数据", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/ISSUE/课程动态表单配置/详情/查询")
//    @GetMapping("/item/datas/{characterId}")
//    public ResponseBo<AppInstitutionCharacterItemVo> itemDatas(@PathVariable("characterId") Long characterId) {
//        List<AppInstitutionCharacterItemVo> agentInfoDtos = appInstitutionCharacterItemService.getInstitutionCharacterItemList(characterId);
//        return new ListResponseBo<>(agentInfoDtos);
//    }

//    @ApiOperation(value = "Item-删除接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/ISSUE/课程动态表单配置/详情/删除课程动态表单详情配置")
//    @PostMapping("/item/delete")
//    public ResponseBo itemDelete(@RequestBody List<Long> ids) {
//        appInstitutionCharacterItemService.delete(ids);
//        return DeleteResponseBo.ok();
//    }

//    @ApiOperation(value = "Item-新增课程动态表单详情配置接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/ISSUE/课程动态表单配置/新增课程动态表单配置")
//    @PostMapping("/item/add")
//    public ResponseBo itemAdd(@RequestBody @Validated(AppInstitutionCharacterItemDto.Add.class) AppInstitutionCharacterItemDto appInstitutionCharacterItemVo) {
//        appInstitutionCharacterItemService.addInstitutionCharacterItem(appInstitutionCharacterItemVo);
//        return SaveResponseBo.ok();
//    }

//    @ApiOperation(value = "Item-修改接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/ISSUE/课程动态表单配置/更新课程动态表单配置")
//    @PostMapping("/item/update")
//    public ResponseBo itemUpdate(@RequestBody @Validated(AppInstitutionCharacterItemDto.Update.class) AppInstitutionCharacterItemDto appInstitutionCharacterItemVo) {
//        appInstitutionCharacterItemService.updateInstitutionCharacterItem(appInstitutionCharacterItemVo);
//        return UpdateResponseBo.ok();
//    }

//    @ApiOperation(value = "Item-配置详情接口", notes = "id为此条数据id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/ISSUE/课程动态表单配置/详情")
//    @GetMapping("/item/{id}")
//    public ResponseBo<AppInstitutionCharacterItemVo> itemDetail(@PathVariable("id") Long id) {
//        AppInstitutionCharacterItemVo data = appInstitutionCharacterItemService.findInstitutionCharacterItemById(id);
//        return new ResponseBo<>(data);
//    }

    /**
     * @Description:上移下移
     * @Param
     * @Date 12:48 2021/5/12
     * <AUTHOR>
     */
//    @ApiOperation(value = "上移下移", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/MSO/国家资讯配置/移动顺序")
//    @PostMapping("movingOrder")
//    public ResponseBo movingOrder(@RequestBody List<AppInstitutionCharacterItemDto> appInstitutionCharacterItemVos) {
//        appInstitutionCharacterItemService.movingOrder(appInstitutionCharacterItemVos);
//        return ResponseBo.ok();
//    }
}
