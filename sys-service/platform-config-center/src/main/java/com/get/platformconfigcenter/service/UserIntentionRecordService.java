package com.get.platformconfigcenter.service;

/**
 * 平台用户意向记录业务逻辑类
 *
 * <AUTHOR>
 * @date 2021/9/7 15:58
 */
public interface UserIntentionRecordService {

    /**
     * 平台用户意向记录列表
     *
     * @Date 15:32 2021/9/7
     * <AUTHOR>
     */
//    List<LogUserHelpMsoVo> getUserIntentionRecordList(LogUserHelpMsoListDto logUserHelpMsoListVo, SearchBean<LogUserHelpMsoListDto> page);

    /**
     * 明细意向记录
     *
     * @Date 17:45 2021/9/7
     * <AUTHOR>
     */
//    List<LogUserHelpMsoVo> detail(Long id);

    /**
     * 意向摘要
     *
     * @return
     * @Date 10:30 2021/9/8
     * <AUTHOR>
     */
//    List<HelpDto1> intention(Long id);
}
