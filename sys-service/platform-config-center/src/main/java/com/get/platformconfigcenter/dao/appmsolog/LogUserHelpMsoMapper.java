package com.get.platformconfigcenter.dao.appmsolog;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.LogUserHelpMso;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("appmsologdb")
public interface LogUserHelpMsoMapper extends BaseMapper<LogUserHelpMso> {
//    int insert(LogUserHelpMso record);

//    int insertSelective(LogUserHelpMso record);

//    int updateByPrimaryKeySelective(LogUserHelpMso record);

//    int updateByPrimaryKey(LogUserHelpMso record);

    /**
     * 平台用户意向记录列表
     *
     * @return
     * @Date 16:17 2021/9/7
     * <AUTHOR>
     */
//    List<LogUserHelpMsoVo> selectUserIntentionRecordList(IPage<LogUserHelpMsoVo> iPage, @Param("logUserHelpMsoListVo") LogUserHelpMsoListDto logUserHelpMsoListVo);
}