package com.get.platformconfigcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.platformconfigcenter.vo.AppInstitutionCharacteVo;
import com.get.platformconfigcenter.service.AppInstitutionCharacterService;
import com.get.platformconfigcenter.dto.AppInstitutionCharacterDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 课程动态表单配置控制类
 *
 * <AUTHOR>
 * @date 2021/5/20 12:27
 */
@Api(tags = "课程动态表单配置")
@RestController
@RequestMapping("/platform/ISSUE/institutionCharacter")
public class AppInstitutionCharacterController {
    @Resource
    private AppInstitutionCharacterService appInstitutionCharacterService;

    @ApiOperation(value = "课程动态表单配置列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/ISSUE/课程动态表单配置/查询")
    @PostMapping("datas")
    public ResponseBo<AppInstitutionCharacteVo> datas(@RequestBody SearchBean<AppInstitutionCharacterDto> page) {
        List<AppInstitutionCharacteVo> agentInfoDtos = appInstitutionCharacterService.getInstitutionCharacterList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(agentInfoDtos, p);
    }

    @ApiOperation(value = "新增课程动态表单配置接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/ISSUE/课程动态表单配置/新增课程动态表单配置")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(AppInstitutionCharacterDto.Add.class) AppInstitutionCharacterDto appInstitutionCharacterDto) {
        return SaveResponseBo.ok(appInstitutionCharacterService.addInstitutionCharacter(appInstitutionCharacterDto));
    }

//    @ApiOperation(value = "修改接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/ISSUE/课程动态表单配置/更新课程动态表单配置")
//    @PostMapping("update")
//    public ResponseBo<AppInstitutionCharacteVo> update(@RequestBody @Validated(AppInstitutionCharacterDto.Update.class) AppInstitutionCharacterDto appInstitutionCharacterVo) {
//        return UpdateResponseBo.ok(appInstitutionCharacterService.updateInstitutionCharacter(appInstitutionCharacterVo));
//    }

    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/ISSUE/课程动态表单配置/详情")
    @GetMapping("/{id}")
    public ResponseBo<AppInstitutionCharacteVo> detail(@PathVariable("id") Long id) {
        AppInstitutionCharacteVo data = appInstitutionCharacterService.findInstitutionCharacterById(id);
        return new ResponseBo<>(data);
    }

//    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/ISSUE/课程动态表单配置/删除课程动态表单配置")
//    @PostMapping("delete/{id}")
//    public ResponseBo delete(@PathVariable("id") Long id) {
//        appInstitutionCharacterService.delete(id);
//        return DeleteResponseBo.ok();
//    }


}
