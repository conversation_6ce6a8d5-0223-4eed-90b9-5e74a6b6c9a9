package com.get.platformconfigcenter.service.impl;

import com.get.core.mybatis.base.UtilService;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.platformconfigcenter.dao.appissue.AppFormConfigAttachmentMapper;
import com.get.platformconfigcenter.dao.appissue.AppFormConfigDivisionMapper;
import com.get.platformconfigcenter.dao.appissue.AppFormConfigMapper;
import com.get.platformconfigcenter.service.AppFormConfigService;
import com.get.platformconfigcenter.service.DeleteService;
import com.get.salecenter.feign.ISaleCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 信息收集内容配置
 *
 * <AUTHOR>
 * @date 2021/5/21 11:28
 */
@Service
public class AppFormConfigServiceImpl implements AppFormConfigService {
    @Resource
    private AppFormConfigAttachmentMapper appFormConfigAttachmentMapper;
//    @Resource
//    private AppFormConfigDivisionMapper appFormConfigDivisionMapper;
//    @Resource
//    private AppFormConfigMapper appFormConfigMapper;
    @Resource
    private AppFormConfigDivisionMapper appFormConfigDivisionMapper;
    @Resource
    private AppFormConfigMapper appFormConfigMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private DeleteService deleteService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;

    /**
     * 信息收集内容列表数据
     *
     * @return
     * @Date 15:36 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public List<AppFormConfigVo> getConfigurationList(AppFormConfigDto appFormConfigVo, Page page) {
////        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
////        Example example = new Example(AppFormConfig.class);
////        Example.Criteria criteria = example.createCriteria();
////        if (GeneralTool.isNotEmpty(appFormConfigVo.getFkAreaCountryId())) {
////            criteria.andEqualTo("fkAreaCountryId", appFormConfigVo.getFkAreaCountryId());
////        }
////        if (GeneralTool.isNotEmpty(appFormConfigVo.getFkMajorLevelId())) {
////            criteria.andEqualTo("fkMajorLevelId", appFormConfigVo.getFkMajorLevelId());
////        }
////        if (GeneralTool.isNotEmpty(appFormConfigVo.getFkInstitutionId())) {
////            criteria.andEqualTo("fkInstitutionId", appFormConfigVo.getFkInstitutionId());
////        }
////        if (GeneralTool.isNotEmpty(appFormConfigVo.getIsActive())) {
////            criteria.andEqualTo("isActive", appFormConfigVo.getIsActive());
////        }
////        List<AppFormConfig> appFormConfigs = appFormConfigMapper.selectByExample(example);
////        page.restPage(appFormConfigs);
//        LambdaQueryWrapper<AppFormConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        if (GeneralTool.isNotEmpty(appFormConfigVo.getFkAreaCountryId())) {
//            lambdaQueryWrapper.eq(AppFormConfig::getFkAreaCountryId, appFormConfigVo.getFkAreaCountryId());
//        }
//        if (GeneralTool.isNotEmpty(appFormConfigVo.getFkMajorLevelId())) {
//            lambdaQueryWrapper.eq(AppFormConfig::getFkMajorLevelId, appFormConfigVo.getFkMajorLevelId());
//        }
//        if (GeneralTool.isNotEmpty(appFormConfigVo.getFkInstitutionId())) {
//            lambdaQueryWrapper.eq(AppFormConfig::getFkInstitutionId, appFormConfigVo.getFkInstitutionId());
//        }
//        if (GeneralTool.isNotEmpty(appFormConfigVo.getIsActive())) {
//            lambdaQueryWrapper.eq(AppFormConfig::getIsActive, appFormConfigVo.getIsActive());
//        }

//        IPage<AppFormConfig> pages = appFormConfigMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
//        page.setAll((int) pages.getTotal());
//        List<AppFormConfig> appFormConfigs = pages.getRecords();
//
//        //111111
//        Result<Map<Long, String>> resultcountryNameMap = institutionCenterClient.getCountryNamesByIds(appFormConfigs.stream().map(AppFormConfig::getFkAreaCountryId).collect(Collectors.toSet()));
//        Set<Long> collect = appFormConfigs.stream().map(AppFormConfig::getFkInstitutionId).collect(Collectors.toSet());
//        collect.removeIf(Objects::isNull);
//        Result<Map<Long, String>> resultinstitutionNameMap = institutionCenterClient.getInstitutionNamesByIds(collect);
//        collect = appFormConfigs.stream().map(AppFormConfig::getFkMajorLevelId).collect(Collectors.toSet());
//        collect.removeIf(Objects::isNull);
////        Result<Map<Long, String>> resultmajorLevelNameMap = institutionCenterClient.getMajorLevelNamesByIds(collect);
//        Result<Map<Long, MajorLevel>> resultmajorLevelMap = institutionCenterClient.getMajorLevelByIds(collect);
//        Map<Long, String> countryNameMap = new HashMap<>();
//        Map<Long, String> majorLevelNameMap = new HashMap<>();
//        Map<Long, MajorLevel> majorLevelMap = new HashMap<>();
//        Map<Long, String> institutionNameMap = new HashMap<>();
//        if (resultcountryNameMap.isSuccess()) {
//            countryNameMap = resultcountryNameMap.getData();
//        }
//        if (resultinstitutionNameMap.isSuccess()) {
//            institutionNameMap = resultinstitutionNameMap.getData();
//        }
////        if (resultmajorLevelNameMap.isSuccess()) {
////            majorLevelNameMap = resultmajorLevelNameMap.getData();
////        }
//        if (resultmajorLevelMap.isSuccess()) {
//            majorLevelMap = resultmajorLevelMap.getData();
//        }
//        List<AppFormConfigVo> appFormConfigDtos = new ArrayList<>();
//        //111111
//        for (AppFormConfig appFormConfig : appFormConfigs) {
//            AppFormConfigVo appFormConfigDto = BeanCopyUtils.objClone(appFormConfig, AppFormConfigVo::new);
//            appFormConfigDto.setFkAreaCountryName(countryNameMap.get(appFormConfigDto.getFkAreaCountryId()));
//            if (GeneralTool.isNotEmpty(appFormConfig.getFkMajorLevelId())) {
//                MajorLevel majorLevel = majorLevelMap.get(appFormConfig.getFkMajorLevelId());
//                if (GeneralTool.isNotEmpty(majorLevel)){
//                    appFormConfigDto.setFkMajorLevelName(majorLevel.getLevelName() + "（"+ majorLevel.getLevelNameChn()+"）");
//                }
//            }
//            if (GeneralTool.isNotEmpty(appFormConfig.getFkInstitutionId())) {
//                appFormConfigDto.setFkInstitutionName(institutionNameMap.get(appFormConfig.getFkInstitutionId()));
//            }
//            appFormConfigDtos.add(appFormConfigDto);
//        }
//        return appFormConfigDtos;
//    }

    /**
     * 新增信息收集内容
     *
     * @Date 16:13 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public Long addConfiguration(AppFormConfigDto appFormConfigVo) {
//        if (GeneralTool.isEmpty(appFormConfigVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
////        Example example = new Example(AppFormConfig.class);
////        Example.Criteria criteria = example.createCriteria().andEqualTo("fkAreaCountryId", appFormConfigVo.getFkAreaCountryId());
////        if (GeneralTool.isEmpty(appFormConfigVo.getFkMajorLevelId())) {
////            criteria.andIsNull("fkMajorLevelId");
////        } else {
////            criteria.andEqualTo("fkMajorLevelId", appFormConfigVo.getFkMajorLevelId());
////        }
////        if (GeneralTool.isEmpty(appFormConfigVo.getFkInstitutionId())) {
////            criteria.andIsNull("fkInstitutionId");
////        } else {
////            criteria.andEqualTo("fkInstitutionId", appFormConfigVo.getFkInstitutionId());
////        }
////        List<AppFormConfig> appFormDivisions = appFormConfigMapper.selectByExample(example);
//        LambdaQueryWrapper<AppFormConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(AppFormConfig::getFkAreaCountryId, appFormConfigVo.getFkAreaCountryId());
//        if (GeneralTool.isEmpty(appFormConfigVo.getFkMajorLevelId())) {
////            criteria.andIsNull("fkMajorLevelId"); 111111需要測試
//            lambdaQueryWrapper.isNull(AppFormConfig::getFkMajorLevelId);
//        } else {
//            lambdaQueryWrapper.eq(AppFormConfig::getFkMajorLevelId, appFormConfigVo.getFkMajorLevelId());
//        }
//        if (GeneralTool.isEmpty(appFormConfigVo.getFkInstitutionId())) {
////            criteria.andIsNull("fkInstitutionId");
//            lambdaQueryWrapper.isNull(AppFormConfig::getFkInstitutionId);
//        } else {
//            lambdaQueryWrapper.eq(AppFormConfig::getFkInstitutionId, appFormConfigVo.getFkInstitutionId());
//        }
//        List<AppFormConfig> appFormDivisions = appFormConfigMapper.selectList(lambdaQueryWrapper);
//        if (GeneralTool.isNotEmpty(appFormDivisions)) {
//            throw new GetServiceException(ResultCode.INVALID_PARAM, LocaleMessageUtils.getMessage("type_name_exists"));
//        }
//        AppFormConfig appFormConfig = BeanCopyUtils.objClone(appFormConfigVo, AppFormConfig::new);
//        utilService.updateUserInfoToEntity(appFormConfig);
//        appFormConfigMapper.insert(appFormConfig);
//        if (GeneralTool.isNotEmpty(appFormConfigVo.getAppFormConfigDivisionVoList())) {
//            for (AppFormConfigDivisionDto appFormConfigDivisionVo : appFormConfigVo.getAppFormConfigDivisionVoList()) {
//                AppFormConfigDivision appFormConfigDivision = BeanCopyUtils.objClone(appFormConfigDivisionVo, AppFormConfigDivision::new);
//                appFormConfigDivision.setFkAppFormConfigId(appFormConfig.getId());
//                utilService.updateUserInfoToEntity(appFormConfigDivision);
//                appFormConfigDivisionMapper.insert(appFormConfigDivision);
//            }
//        }
//        if (GeneralTool.isNotEmpty(appFormConfigVo.getAppFormConfigAttachmentVoList())) {
//            for (AppFormConfigAttachmentDto appFormConfigAttachmentVo : appFormConfigVo.getAppFormConfigAttachmentVoList()) {
//                AppFormConfigAttachment appFormConfigAttachment = BeanCopyUtils.objClone(appFormConfigAttachmentVo, AppFormConfigAttachment::new);
//                appFormConfigAttachment.setFkAppFormConfigId(appFormConfig.getId());
//                utilService.updateUserInfoToEntity(appFormConfigAttachment);
//                appFormConfigAttachmentMapper.insert(appFormConfigAttachment);
//            }
//        }
//        return appFormConfig.getId();
//    }

    /**
     * 修改信息收集内容
     *
     * @Date 16:29 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public AppFormConfigVo updateConfiguration(AppFormConfigDto appFormConfigVo) {
//        if (GeneralTool.isEmpty(appFormConfigVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
////        Example example = new Example(AppFormConfig.class);
////        Example.Criteria criteria = example.createCriteria().andEqualTo("fkAreaCountryId", appFormConfigVo.getFkAreaCountryId()).andNotEqualTo("id", appFormConfigVo.getId());
////        if (GeneralTool.isEmpty(appFormConfigVo.getFkMajorLevelId())) {
////            criteria.andIsNull("fkMajorLevelId");
////        } else {
////            criteria.andEqualTo("fkMajorLevelId", appFormConfigVo.getFkMajorLevelId());
////        }
////        if (GeneralTool.isEmpty(appFormConfigVo.getFkInstitutionId())) {
////            criteria.andIsNull("fkInstitutionId");
////        } else {
////            criteria.andEqualTo("fkInstitutionId", appFormConfigVo.getFkInstitutionId());
////        }
////        List<AppFormConfig> appFormDivisions = appFormConfigMapper.selectByExample(example);
////        Example example = new Example(AppFormConfig.class);
////        Example.Criteria criteria = example.createCriteria().andEqualTo("fkAreaCountryId", appFormConfigVo.getFkAreaCountryId()).andNotEqualTo("id", appFormConfigVo.getId());
//        LambdaQueryWrapper<AppFormConfig> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(AppFormConfig::getFkAreaCountryId, appFormConfigVo.getFkAreaCountryId());
//        lambdaQueryWrapper.ne(AppFormConfig::getId, appFormConfigVo.getId());
//        if (GeneralTool.isEmpty(appFormConfigVo.getFkMajorLevelId())) {
//            lambdaQueryWrapper.isNull(AppFormConfig::getFkMajorLevelId);
//        } else {
//            lambdaQueryWrapper.eq(AppFormConfig::getFkMajorLevelId, appFormConfigVo.getFkMajorLevelId());
//        }
//        if (GeneralTool.isEmpty(appFormConfigVo.getFkInstitutionId())) {
//            lambdaQueryWrapper.isNull(AppFormConfig::getFkInstitutionId);
//        } else {
//            lambdaQueryWrapper.eq(AppFormConfig::getFkInstitutionId, appFormConfigVo.getFkInstitutionId());
//        }
//        List<AppFormConfig> appFormDivisions = appFormConfigMapper.selectList(lambdaQueryWrapper);
//        if (GeneralTool.isNotEmpty(appFormDivisions)) {
//            throw new GetServiceException(ResultCode.INVALID_PARAM, LocaleMessageUtils.getMessage("type_name_exists"));
//        }
//
//        AppFormConfig appFormDivision = appFormConfigMapper.selectById(appFormConfigVo.getId());
//        if (GeneralTool.isEmpty(appFormDivision)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        appFormDivision.setFkMajorLevelId(appFormConfigVo.getFkMajorLevelId());
//        appFormDivision.setFkAreaCountryId(appFormConfigVo.getFkAreaCountryId());
//        appFormDivision.setFkInstitutionId(appFormConfigVo.getFkInstitutionId());
//        appFormDivision.setIsActive(appFormConfigVo.getIsActive());
//        utilService.updateUserInfoToEntity(appFormDivision);
//        appFormConfigMapper.updateByPrimaryKey(appFormDivision);
////        example = new Example(AppFormConfigDivision.class);
////        example.createCriteria().andEqualTo("fkAppFormConfigId", appFormConfigVo.getId());
////        appFormConfigDivisionMapper.deleteByExample(example);
////        example = new Example(AppFormConfigAttachment.class);
////        example.createCriteria().andEqualTo("fkAppFormConfigId", appFormConfigVo.getId());
////        appFormConfigAttachmentMapper.deleteByExample(example);
////        example = new Example(AppFormConfigDivision.class);
////        example.createCriteria().andEqualTo("fkAppFormConfigId", appFormConfigVo.getId());
//        appFormConfigDivisionMapper.delete(Wrappers.<AppFormConfigDivision>query().lambda().eq(AppFormConfigDivision::getFkAppFormConfigId, appFormConfigVo.getId()));
//        appFormConfigAttachmentMapper.delete(Wrappers.<AppFormConfigAttachment>query().lambda().eq(AppFormConfigAttachment::getFkAppFormConfigId, appFormConfigVo.getId()));
//
//        if (GeneralTool.isNotEmpty(appFormConfigVo.getAppFormConfigDivisionVoList())) {
//            for (AppFormConfigDivisionDto appFormConfigDivisionVo : appFormConfigVo.getAppFormConfigDivisionVoList()) {
//                AppFormConfigDivision appFormConfigDivision = BeanCopyUtils.objClone(appFormConfigDivisionVo, AppFormConfigDivision::new);
//                appFormConfigDivision.setFkAppFormConfigId(appFormDivision.getId());
//                utilService.updateUserInfoToEntity(appFormConfigDivision);
//                utilService.updateUserInfoToEntity(appFormConfigDivision);
//                appFormConfigDivisionMapper.insert(appFormConfigDivision);
//            }
//        }
//        if (GeneralTool.isNotEmpty(appFormConfigVo.getAppFormConfigAttachmentVoList())) {
//            for (AppFormConfigAttachmentDto appFormConfigAttachmentVo : appFormConfigVo.getAppFormConfigAttachmentVoList()) {
//                AppFormConfigAttachment appFormConfigAttachment = BeanCopyUtils.objClone(appFormConfigAttachmentVo, AppFormConfigAttachment::new);
//                appFormConfigAttachment.setFkAppFormConfigId(appFormDivision.getId());
//                utilService.updateUserInfoToEntity(appFormConfigAttachment);
//                utilService.updateUserInfoToEntity(appFormConfigAttachment);
//               appFormConfigAttachmentMapper.insert(appFormConfigAttachment);
//            }
//        }
//        return findConfigurationById(appFormDivision.getId());
//    }

    /**
     * 信息收集内容详情
     *
     * @Date 16:34 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public AppFormConfigVo findConfigurationById(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        AppFormConfig appFormDivision = appFormConfigMapper.selectById(id);
//        if (GeneralTool.isEmpty(appFormDivision)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        AppFormConfigVo appFormConfigDto = BeanCopyUtils.objClone(appFormDivision, AppFormConfigVo::new);
////        Example example = new Example(AppFormConfigDivision.class);
////        example.createCriteria().andEqualTo("fkAppFormConfigId", id);
////        List<AppFormConfigDivision> appFormConfigDivisions = appFormConfigDivisionMapper.selectByExample(example);
//        List<AppFormConfigDivision> appFormConfigDivisions = appFormConfigDivisionMapper.selectList(Wrappers.<AppFormConfigDivision>query().lambda().eq(AppFormConfigDivision::getFkAppFormConfigId, id));
//        appFormConfigDto.setAppFormConfigDivisionDtoList(appFormConfigDivisions.stream().map(appFormConfigDivision -> BeanCopyUtils.objClone(appFormConfigDivision, AppFormConfigDivisionVo::new)).collect(Collectors.toList()));
////        example = new Example(AppFormConfigAttachment.class);
////        example.createCriteria().andEqualTo("fkAppFormConfigId", id);
////        List<AppFormConfigAttachment> appFormConfigAttachments = appFormConfigAttachmentMapper.selectByExample(example);
//        List<AppFormConfigAttachment> appFormConfigAttachments = appFormConfigAttachmentMapper.selectList(Wrappers.<AppFormConfigAttachment>query().lambda().eq(AppFormConfigAttachment::getFkAppFormConfigId, id));
//
//        appFormConfigDto.setAppFormConfigAttachmentDtoList(appFormConfigAttachments.stream().map(appFormConfigAttachment -> BeanCopyUtils.objClone(appFormConfigAttachment, AppFormConfigAttachmentVo::new)).collect(Collectors.toList()));
//        return appFormConfigDto;
//    }

    /**
     * 删除信息收集内容
     *
     * @Date 16:38 2021/5/20
     * <AUTHOR>
     */
//    @Override
//    public void delete(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        deleteService.deleteValidateAppFormConfig(id);
//        appFormConfigMapper.deleteById(id);
//    }
}
