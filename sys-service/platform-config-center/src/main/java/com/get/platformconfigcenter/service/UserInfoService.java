package com.get.platformconfigcenter.service;

import com.get.common.result.Page;
import com.get.platformconfigcenter.vo.UserInfoVo;
import com.get.platformconfigcenter.entity.UserInfo;
import com.get.platformconfigcenter.dto.UserInfoDto;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON>.
 * Time: 17:31
 * Date: 2021/7/8
 * Description:用户信息业务类
 */
public interface UserInfoService {

    /**
     * 用户信息管理列表数据
     *
     * @param userInfoDto
     * @param page
     * @return
     */
    List<UserInfoVo> getUserInfoList(UserInfoDto userInfoDto, Page page);

    /**
     * 新增用户信息管理
     *
     * @param userInfoDto
     * @return
     */
    Long addUserInfo(UserInfoDto userInfoDto);

    /**
     * 更新用户信息管理
     *
     * @param userInfoDto
     * @return
     */
    void updateUserInfo(UserInfoDto userInfoDto);

    /**
     * 用户信息管理详情
     *
     * @param id
     * @return
     */
    UserInfoVo findUserInfoById(Long id);

    /**
     * 删除用户信息管理
     *
     * @param id
     * @
     */
    void delete(Long id);

    /**
     * @Description: feign调用 根据userid获取名称
     * @Author: Jerry
     * @Date:14:20 2021/8/23
     */
    Map<Long, String> getUserNamesByUserIds(Set<Long> userIds);

    /**
     * @Description: feign调用 根据userid获取名称（微信昵称）
     * @Author: Jerry
     * @Date:14:20 2021/8/23
     */
    Map<Long, String> getUserNickNamesByUserIds(Set<Long> userIds);

    /**
     * @Description: feign调用 根据userid获取手机号
     * @Author: Jerry
     * @Date:10:30 2021/10/15
     */
    Map<Long, String> getMobileByUserIds(Set<Long> userIds);

    /**
     * @Description: feign调用 根据userid获取对象
     * @Author: Jerry
     * @Date:16:51 2021/8/26
     */
    Map<Long, UserInfoVo> getUserInfoDtoByIds(Set<Long> userIds);

    /**
     * @Description: 根据名称模糊搜索用户ids
     * @Author: Jerry
     * @Date:14:46 2021/8/23
     */
    Set<Long> getUserIdsByParam(String userName, Long fkAreaCityId, String bdName);

    /**
     * @Description: 根据名称模糊或者手机号搜索用户ids
     * @Author: Jerry
     * @Date:11:14 2021/8/27
     */
    Set<Long> getUserIdsByNameOrMobile(String userName, String phoneNumber);

    /**
     * @Description: 根据ids获取人员对应的城市名称
     * @Author: Jerry
     * @Date:10:39 2021/8/30
     */
    Map<Long, String> getCityNamesByUserIds(Set<Long> userIds);

    /**
     * 条件查询用户
     *
     * @param userInfoDto
     * @return
     */
    List<UserInfo> getUserInfoBySearch(UserInfoDto userInfoDto);

    /**
     * 根据代理获取ISSUE用户
     *
     * @param fkAgentId
     * @param fkCompanyId
     * @return
     */
   // List<UserInfoVo> getUserByAgentId(Long fkAgentId, Long fkCompanyId);

    //List<UserAgentVo> getUsersByAgentId(Long fkAgentId);
    /**
     * 根据代理获取用户信息
     *
     * @param fkAgentIds
     * @return
     */
   // Map<Long,List<UserAgentVo>> getUsersByAgentIds(Set<Long> fkAgentIds);

    /**
     * 根据学生ids获取用户信息
     * @param studentIds
     * @return
     */
    Map<Long,UserInfo> getUserByStudentIds(Set<Long> studentIds);

    void getUpdateUserPw(String pw,Long fkUserId);

    //void getSetIssueRoot(UserAgentDto userAgentVo);

    void updateUserVip(Long id, Boolean isVip);

    //void updateIsShowAppStatusPermission(Long id, Boolean isShowAppStatusPermission);

   // Boolean insertIssueAgentUser(Long fkAgentId, Long fkCompanyId, Long fkUserId);

    //Map<Long, UserInfo> getUserByofferItemIssueCourseIds(Set<Long> offerItemIssueCourseIds);

   // Boolean removeIssueRelationByAgentId(Long fkAgentId);
    //Boolean updateIssueUserContactPersonTypeKeyByPersonId(Long fkContactPersonId,String fkContactPersonTypeKey);
}
