package com.get.platformconfigcenter.service;

import com.get.common.result.Page;
import com.get.platformconfigcenter.vo.AppInstitutionCharacteVo;
import com.get.platformconfigcenter.dto.AppInstitutionCharacterDto;

import java.util.List;

/**
 * 课程动态表单配置业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/5/20 15:33
 */
public interface AppInstitutionCharacterService {
    /**
     * 课程动态表单配置列表数据
     *
     * @return
     * @Date 15:36 2021/5/20
     * <AUTHOR>
     */
    List<AppInstitutionCharacteVo> getInstitutionCharacterList(AppInstitutionCharacterDto appInstitutionCharacterDto, Page page);

    /**
     * 新增课程动态表单配置
     *
     * @Date 16:13 2021/5/20
     * <AUTHOR>
     */
    Long addInstitutionCharacter(AppInstitutionCharacterDto appInstitutionCharacterDto);

    /**
     * 修改课程动态表单配置
     *
     * @Date 16:29 2021/5/20
     * <AUTHOR>
     */
   // AppInstitutionCharacteVo updateInstitutionCharacter(AppInstitutionCharacterDto appInstitutionCharacterVo);

    /**
     * 课程动态表单配置详情
     *
     * @Date 16:34 2021/5/20
     * <AUTHOR>
     */
    AppInstitutionCharacteVo findInstitutionCharacterById(Long id);

    /**
     * 删除课程动态表单配置
     *
     * @Date 16:37 2021/5/20
     * <AUTHOR>
     */
    //void delete(Long id);

}
