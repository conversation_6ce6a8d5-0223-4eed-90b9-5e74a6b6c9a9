package com.get.platformconfigcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.platformconfigcenter.vo.UserInfoVo;
import com.get.platformconfigcenter.service.UserInfoService;
import com.get.platformconfigcenter.dto.UserInfoDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * Created by Jerry.
 * Time: 16:59
 * Date: 2021/7/8
 * Description:用户信息管理
 */
@Api(tags = "用户信息管理")
@RestController
@RequestMapping("/platform/userinfo")
public class UserInfoController {
    @Resource
    private UserInfoService userInfoService;

    /**
     * 用户信息管理列表数据
     *
     * @param page
     * @return
     * @
     */
    @ApiOperation(value = "用户信息管理列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/用户信息管理/查询")
    @PostMapping("datas")
    public ResponseBo<UserInfoVo> datas(@RequestBody SearchBean<UserInfoDto> page) {
        List<UserInfoVo> userInfoList = userInfoService.getUserInfoList(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(userInfoList, p);
    }

    /**
     * 新增用户信息管理
     *
     * @param userInfoDto
     * @return
     * @
     */
    @ApiOperation(value = "新增用户信息管理", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/用户信息管理/新增用户信息管理")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(UserInfoDto.Add.class) UserInfoDto userInfoDto) {
        return SaveResponseBo.ok(userInfoService.addUserInfo(userInfoDto));
    }

    /**
     * 更新用户信息管理
     *
     * @param userInfoDto
     * @return
     * @
     */
    @ApiOperation(value = "更新用户信息管理", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/用户信息管理/更新用户信息管理")
    @PostMapping("update")
    public ResponseBo<UserInfoVo> update(@RequestBody @Validated(UserInfoDto.Update.class) UserInfoDto userInfoDto) {
        userInfoService.updateUserInfo(userInfoDto);
        return UpdateResponseBo.ok();
    }

    /**
     * 用户信息管理详情
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "用户信息管理详情", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/用户信息管理/用户信息管理详情")
    @GetMapping("/{id}")
    public ResponseBo<UserInfoVo> detail(@PathVariable("id") Long id) {
        return new ResponseBo<>(userInfoService.findUserInfoById(id));
    }

    /**
     * 删除用户信息管理
     *
     * @param id
     * @return
     * @
     */
    @ApiOperation(value = "删除用户信息管理", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/用户信息管理/删除用户信息管理")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        userInfoService.delete(id);
        return DeleteResponseBo.ok();
    }


    /**
     * 获取所属平台下拉框数据
     *
     * @param
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "获取所属平台下拉框数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, description = "业务平台配置中心/用户信息管理/获取所属平台下拉框数据")
    @PostMapping("getPlatformSelect")
    public ResponseBo<Map<String, Object>> getPlatformSelect() {
        return new ListResponseBo<>(ProjectKeyEnum.enumsTranslation2Arrays(ProjectKeyEnum.PLATFORM_TYPE));
    }


    /**
     * @Description: feign调用 根据userid获取名称
     * @Author: Jerry
     * @Date:14:19 2021/8/23
     */
    @ApiIgnore
    @PostMapping("getUserNamesByUserIds")
    public Map<Long, String> getUserNamesByUserIds(@RequestBody Set<Long> userIds) {
        return userInfoService.getUserNamesByUserIds(userIds);
    }

    /**
     * @Description: feign调用 根据userid获取名称(微信昵称)
     * @Author: Jerry
     * @Date:14:19 2021/8/23
     */
    @ApiIgnore
    @PostMapping("getUserNickNamesByUserIds")
    public Map<Long, String> getUserNickNamesByUserIds(@RequestBody Set<Long> userIds) {
        return userInfoService.getUserNickNamesByUserIds(userIds);
    }


    /**
     * @Description: feign调用 根据userid获取手机号
     * @Author: Jerry
     * @Date:10:29 2021/10/15
     */
    @ApiIgnore
    @PostMapping("getMobileByUserIds")
    public Map<Long, String> getMobileByUserIds(@RequestBody Set<Long> userIds) {
        return userInfoService.getMobileByUserIds(userIds);
    }

    /**
     * @Description: feign调用 根据userid获取对象
     * @Author: Jerry
     * @Date:16:51 2021/8/26
     */
    @ApiIgnore
    @PostMapping("getUserInfoDtoByIds")
    public Map<Long, UserInfoVo> getUserInfoDtoByIds(@RequestBody Set<Long> userIds) {
        return userInfoService.getUserInfoDtoByIds(userIds);
    }

    /**
     * @Description: 根据名称模糊搜索用户ids
     * @Author: Jerry
     * @Date:14:46 2021/8/23
     */
    @ApiIgnore
    @PostMapping("getUserIdsByParam")
    public Set<Long> getUserIdsByParam(@RequestParam(value = "userName", required = false) String userName,
                                       @RequestParam(value = "fkAreaCityId", required = false) Long fkAreaCityId,
                                       @RequestParam(value = "bdName", required = false) String bdName) {
        return userInfoService.getUserIdsByParam(userName, fkAreaCityId, bdName);
    }

    /**
     * @Description: 根据名称模糊或者手机号搜索用户ids
     * @Author: Jerry
     * @Date:11:13 2021/8/27
     */
    @ApiIgnore
    @PostMapping("getUserIdsByNameOrMobile")
    public Set<Long> getUserIdsByNameOrMobile(@RequestParam(value = "userName", required = false) String userName,
                                              @RequestParam(value = "phoneNumber", required = false) String phoneNumber) {
        return userInfoService.getUserIdsByNameOrMobile(userName, phoneNumber);
    }

    /**
     * @Description: 根据ids获取人员对应的城市名称
     * @Author: Jerry
     * @Date:10:38 2021/8/30
     */
    @ApiIgnore
    @PostMapping("getCityNamesByUserIds")
    public Map<Long, String> getCityNamesByUserIds(@RequestBody Set<Long> userIds) {
        return userInfoService.getCityNamesByUserIds(userIds);
    }

    /**
     * feign调用,条件查询用户信息
     *
     * @param userInfoDto
     * @return
     * @
     */
    @ApiIgnore
    @PostMapping("getUserInfoBySearch")
    public ListResponseBo getUserInfoBySearch(@RequestBody UserInfoDto userInfoDto) {
        return new ListResponseBo(userInfoService.getUserInfoBySearch(userInfoDto));
    }




//    @ApiOperation("根据代理获取ISSUE用户")
//    @GetMapping("getUserByAgentIdVo")
//    public ListResponseBo getUserByAgentIdVo(@RequestParam("fkAgentId") Long fkAgentId,
//                                             @RequestParam("fkCompanyId")Long fkCompanyId) {
//        return new ListResponseBo(userInfoService.getUserByAgentId(fkAgentId,fkCompanyId));
//    }


    /**
     * @ Description :根据代理获取用户s
     * @ Param [fkAgentId]
     * @ return java.util.List<com.get.platformconfigcenter.vo.UserInfoVo>
     * @ author LEO
     */
//    @ApiOperation("根据代理获取用户")
//    @GetMapping("getUsersByAgentId")
//    public ListResponseBo getUsersByAgentId(@RequestParam("fkAgentId") Long fkAgentId) {
//        return new ListResponseBo(userInfoService.getUsersByAgentId(fkAgentId));
//    }

    @ApiOperation("修改用户密码")
    @GetMapping("getUpdateUserPw")
    public ResponseBo getUpdateUserPw(@RequestParam("pw") String pw, @RequestParam("fkUserId") Long fkUserId) {
        userInfoService.getUpdateUserPw(pw, fkUserId);
        return ResponseBo.ok();
    }

//    @PostMapping("getSetIssueRoot")
//    @ApiOperation("设置issue用户权限")
//    public ResponseBo getSetIssueRoot(@RequestBody UserAgentDto userAgentVo) {
//        userInfoService.getSetIssueRoot(userAgentVo);
//        return ResponseBo.ok();
//    }

    @PostMapping("updateUserVip")
    @ApiOperation("设置用户vip状态")
    public ResponseBo updateUserVip(@RequestParam("id") Long id, @RequestParam("isVip") Boolean isVip){
        userInfoService.updateUserVip(id,isVip);
        return ResponseBo.ok();
    }

//    @ApiOperation("申请状态显示设置")
//    @PostMapping("updateIsShowAppStatusPermission")
//    public ResponseBo updateIsShowAppStatusPermission(@RequestParam("id") Long id, @RequestParam("isShowAppStatusPermission") Boolean isShowAppStatusPermission){
//        userInfoService.updateIsShowAppStatusPermission(id,isShowAppStatusPermission);
//        return ResponseBo.ok();
//    }
}
