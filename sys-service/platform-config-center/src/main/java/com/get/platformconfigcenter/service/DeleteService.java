package com.get.platformconfigcenter.service;


/**
 * 删除业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/5/12 12:40
 */
public interface DeleteService {
    /**
     * @Description:删除国家资讯配置逻辑验证
     * @Param
     * @Date 12:42 2021/5/12
     * <AUTHOR>
     */
//    boolean deleteValidateAgentModuleInfo(Long agentModuleId);

    /**
     * @Description: 菜单删除逻辑验证
     * @Author: Jerry
     * @Date:12:15 2021/8/10
     */
//    boolean deleteValidateSiteMap(Long siteMapId);

    /**
     * @Description:删除代理配置逻辑验证
     * @Param
     * @Date 17:07 2021/5/13
     * <AUTHOR>
     */
//    boolean deleteValidateAgentInfo(Long agentInfoId);

    /**
     * 删除学生附件类型逻辑验证
     *
     * @Date 16:42 2021/5/20
     * <AUTHOR>
     */
    boolean deleteValidateStudentAttachment(Long studentAttachmentId);

    /**
     * 删除申请表单板块逻辑验证
     *
     * @Date 16:42 2021/5/20
     * <AUTHOR>
     */
   // boolean deleteValidateAppFormDivision(Long appFormDivisionId);

    /**
     * 删除课程动态表单配置逻辑验证
     *
     * @Date 16:19 2021/5/21
     * <AUTHOR>
     */
    //boolean deleteValidateAppInstitutionCharacter(Long appInstitutionCharacterId);

    /**
     * 删除信息收集内容配置逻辑验证
     *
     * @Date 18:04 2021/5/21
     * <AUTHOR>
     */
   // boolean deleteValidateAppFormConfig(Long appFormConfigId);

    /**
     * 验证课程删除逻辑验证
     *
     * @Date 18:04 2021/5/21
     * <AUTHOR>
     */
    //boolean deleteValidateCourse(Long courseId);
}
