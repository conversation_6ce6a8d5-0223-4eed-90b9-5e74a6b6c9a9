package com.get.platformconfigcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.platformconfigcenter.vo.HubsUserVo;
import com.get.platformconfigcenter.vo.SubmitOrderVo;
import com.get.platformconfigcenter.entity.CountryButler;
import com.get.platformconfigcenter.service.AppHubsService;
import com.get.platformconfigcenter.dto.AppHubsSearchOrderDto;
import com.get.platformconfigcenter.dto.AppHubsUpdateOrderDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @Author: Eric
 * @Date: 2024/9/19
 * @Description:
 * @Version 1.0
 */
@Api(tags = "Hubs销售管理")
@RestController
@RequestMapping("/platform/hubs")
public class AppHubsController {

    @Resource
    private AppHubsService appHubsService;

    @GetMapping("/getAppHubsCountry")
    @ApiOperation(value = "获取国家管家下拉", notes = "")
    public ResponseBo getAppHubsCountry(){
        List<CountryButler> list = appHubsService.getAppHubsCountry();
        return new ResponseBo<>(list);

    }

    @PostMapping("/getAppHubsOrders")
    @ApiOperation(value = "hubs订单管理查询", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/hubs/订单管理/查询")
    public ListResponseBo<SubmitOrderVo> getAppHubsOrders(@RequestBody SearchBean<AppHubsSearchOrderDto> searchBean){

        List<SubmitOrderVo> list = appHubsService.getAppHubsOrders(searchBean.getData(),searchBean);

        Page page = BeanCopyUtils.objClone(searchBean, Page::new);

        return new ListResponseBo<>(list,page);

    }

    @ApiOperation(value = "hubs订单管理详情", notes = "")
    @GetMapping("/getAppHubsOrdersDetail")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/hubs/订单管理/详情")
    public ResponseBo getAppHubsOrdersDetail(@RequestParam("fkOrderId") Long fkOrderId){

        Map<String,Object> map = appHubsService.getAppHubsOrdersDetail(fkOrderId);

        return new ResponseBo<>(map);
    }

    @PostMapping("/updateSalqOrder")
    @ApiOperation(value = "修改订单", notes = "只修改订单价格")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/hubs/订单管理/修改订单")
    public ResponseBo updateSalqOrder(@RequestBody List<AppHubsUpdateOrderDto> data){

        appHubsService.updateSalqOrder(data);

        return ResponseBo.ok();
    }

    @PostMapping("/getRegisteredUser")
    @ApiOperation(value = "hubs订单管理查询", notes = "")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/hubs")
    public ListResponseBo<HubsUserVo> getRegisteredUser(@RequestBody SearchBean<AppHubsSearchOrderDto> searchBean){
        List<HubsUserVo> list = appHubsService.getRegisteredUser(searchBean.getData(), searchBean);
        Page page = BeanCopyUtils.objClone(searchBean, Page::new);
        return new ListResponseBo<>(list,page);

    }


}
