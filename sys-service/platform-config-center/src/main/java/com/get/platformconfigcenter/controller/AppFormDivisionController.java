package com.get.platformconfigcenter.controller;

import com.get.platformconfigcenter.service.AppFormDivisionService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 申请表单板块管理控制类
 *
 * <AUTHOR>
 * @date 2021/5/20 12:27
 */
@Api(tags = "申请表单板块管理")
@RestController
@RequestMapping("/platform/ISSUE/appFormDivision")
public class AppFormDivisionController {
    @Resource
    private AppFormDivisionService appFormDivisionService;

//    @ApiOperation(value = "申请表单板块列表数据", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/ISSUE/申请表单板块管理/查询")
//    @PostMapping("datas")
//    public ResponseBo<AppFormDivisionVo> datas(@RequestBody SearchBean<AppFormDivisionDto> page) {
//        List<AppFormDivisionVo> agentInfoDtos = appFormDivisionService.getAppFormDivisionList(page.getData(), page);
//        Page p = BeanCopyUtils.objClone(page, Page::new);
//        return new ListResponseBo<>(agentInfoDtos, p);
//    }

//    @ApiOperation(value = "新增申请表单板块接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/ISSUE/申请表单板块管理/新增申请表单板块")
//    @PostMapping("add")
//    public ResponseBo add(@RequestBody @Validated(AppFormDivisionDto.Add.class) AppFormDivisionDto appFormDivisionVo) {
//        return SaveResponseBo.ok(appFormDivisionService.addAppFormDivision(appFormDivisionVo));
//    }

//    @ApiOperation(value = "修改接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/ISSUE/申请表单板块管理/更新申请表单板块")
//    @PostMapping("update")
//    public ResponseBo<AppFormDivisionVo> update(@RequestBody @Validated(AppFormDivisionDto.Update.class) AppFormDivisionDto appFormDivisionVo) {
//        return UpdateResponseBo.ok(appFormDivisionService.updateAppFormDivision(appFormDivisionVo));
//    }

//    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/ISSUE/申请表单板块管理/详情")
//    @GetMapping("/{id}")
//    public ResponseBo<AppFormDivisionVo> detail(@PathVariable("id") Long id) {
//        AppFormDivisionVo data = appFormDivisionService.findAppFormDivisionById(id);
//        return new ResponseBo<>(data);
//    }

//    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/ISSUE/申请表单板块管理/删除申请表单板块")
//    @PostMapping("delete/{id}")
//    public ResponseBo delete(@PathVariable("id") Long id) {
//        appFormDivisionService.delete(id);
//        return DeleteResponseBo.ok();
//    }

//    @ApiOperation(value = "上移下移", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/ISSUE/申请表单板块管理/移动顺序")
//    @PostMapping("movingOrder")
//    public ResponseBo movingOrder(@RequestBody List<AppFormDivisionDto> appFormDivisionVos) {
//        appFormDivisionService.movingOrder(appFormDivisionVos);
//        return ResponseBo.ok();
//    }

    /**
     * 资料板块下拉框数据
     *
     * @return
     * @
     */
//    @VerifyPermission(IsVerify = false)
//    @ApiOperation(value = "资料板块下拉框数据", notes = "")
//    @GetMapping("getAppFormDivisionSelect")
//    public ResponseBo<AppFormDivisionVo> getAttachmentTypeSelect() {
//        List<AppFormDivisionVo> datas = appFormDivisionService.getAppFormDivisionSelect();
//        return new ListResponseBo<>(datas);
//    }
}
