<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.appissue.AppInstitutionCharacterItemMapper">
  <resultMap id="BaseResultMap" type="com.get.platformconfigcenter.entity.AppInstitutionCharacterItem">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="fk_app_institution_character_id" jdbcType="BIGINT" property="fkAppInstitutionCharacterId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="input_type" jdbcType="INTEGER" property="inputType" />
    <result column="data_type" jdbcType="INTEGER" property="dataType" />
    <result column="data_source" jdbcType="VARCHAR" property="dataSource" />
    <result column="max_length" jdbcType="INTEGER" property="maxLength" />
    <result column="is_required" jdbcType="BIT" property="isRequired" />
    <result column="view_order" jdbcType="INTEGER" property="viewOrder" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_create_user" jdbcType="VARCHAR" property="gmtCreateUser" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="gmt_modified_user" jdbcType="VARCHAR" property="gmtModifiedUser" />
  </resultMap>
  <sql id="Base_Column_List">
    id, fk_app_institution_character_id, title, field_name, input_type, data_type, data_source,
    max_length, is_required, view_order, gmt_create, gmt_create_user, gmt_modified, gmt_modified_user
  </sql>
  <!--TODO 注释sql-->
<!--  <insert id="insert" parameterType="com.get.platformconfigcenter.entity.AppInstitutionCharacterItem" keyProperty="id" useGeneratedKeys="true">-->
<!--    insert into u_app_institution_character_item (id, fk_app_institution_character_id, title,-->
<!--      field_name, input_type, data_type,-->
<!--      data_source, max_length, is_required,-->
<!--      view_order, gmt_create, gmt_create_user,-->
<!--      gmt_modified, gmt_modified_user)-->
<!--    values (#{id,jdbcType=BIGINT}, #{fkAppInstitutionCharacterId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR},-->
<!--      #{fieldName,jdbcType=VARCHAR}, #{inputType,jdbcType=INTEGER}, #{dataType,jdbcType=INTEGER},-->
<!--      #{dataSource,jdbcType=VARCHAR}, #{maxLength,jdbcType=INTEGER}, #{isRequired,jdbcType=BIT},-->
<!--      #{viewOrder,jdbcType=INTEGER}, #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      #{gmtModified,jdbcType=TIMESTAMP}, #{gmtModifiedUser,jdbcType=VARCHAR})-->
<!--  </insert>-->
<!--  <insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.AppInstitutionCharacterItem">-->
<!--    insert into u_app_institution_character_item-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="fkAppInstitutionCharacterId != null">-->
<!--        fk_app_institution_character_id,-->
<!--      </if>-->
<!--      <if test="title != null">-->
<!--        title,-->
<!--      </if>-->
<!--      <if test="fieldName != null">-->
<!--        field_name,-->
<!--      </if>-->
<!--      <if test="inputType != null">-->
<!--        input_type,-->
<!--      </if>-->
<!--      <if test="dataType != null">-->
<!--        data_type,-->
<!--      </if>-->
<!--      <if test="dataSource != null">-->
<!--        data_source,-->
<!--      </if>-->
<!--      <if test="maxLength != null">-->
<!--        max_length,-->
<!--      </if>-->
<!--      <if test="isRequired != null">-->
<!--        is_required,-->
<!--      </if>-->
<!--      <if test="viewOrder != null">-->
<!--        view_order,-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create,-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user,-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified,-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="fkAppInstitutionCharacterId != null">-->
<!--        #{fkAppInstitutionCharacterId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="title != null">-->
<!--        #{title,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="fieldName != null">-->
<!--        #{fieldName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="inputType != null">-->
<!--        #{inputType,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="dataType != null">-->
<!--        #{dataType,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="dataSource != null">-->
<!--        #{dataSource,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="maxLength != null">-->
<!--        #{maxLength,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="isRequired != null">-->
<!--        #{isRequired,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="viewOrder != null">-->
<!--        #{viewOrder,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
<!--  <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.AppInstitutionCharacterItem">-->
<!--    update u_app_institution_character_item-->
<!--    <set>-->
<!--      <if test="fkAppInstitutionCharacterId != null">-->
<!--        fk_app_institution_character_id = #{fkAppInstitutionCharacterId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="title != null">-->
<!--        title = #{title,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="fieldName != null">-->
<!--        field_name = #{fieldName,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="inputType != null">-->
<!--        input_type = #{inputType,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="dataType != null">-->
<!--        data_type = #{dataType,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="dataSource != null">-->
<!--        data_source = #{dataSource,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="maxLength != null">-->
<!--        max_length = #{maxLength,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="isRequired != null">-->
<!--        is_required = #{isRequired,jdbcType=BIT},-->
<!--      </if>-->
<!--      <if test="viewOrder != null">-->
<!--        view_order = #{viewOrder,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="gmtCreate != null">-->
<!--        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtCreateUser != null">-->
<!--        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="gmtModified != null">-->
<!--        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="gmtModifiedUser != null">-->
<!--        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--    </set>-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </update>-->
<!--  <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.AppInstitutionCharacterItem">-->
<!--    update u_app_institution_character_item-->
<!--    set fk_app_institution_character_id = #{fkAppInstitutionCharacterId,jdbcType=BIGINT},-->
<!--      title = #{title,jdbcType=VARCHAR},-->
<!--      field_name = #{fieldName,jdbcType=VARCHAR},-->
<!--      input_type = #{inputType,jdbcType=INTEGER},-->
<!--      data_type = #{dataType,jdbcType=INTEGER},-->
<!--      data_source = #{dataSource,jdbcType=VARCHAR},-->
<!--      max_length = #{maxLength,jdbcType=INTEGER},-->
<!--      is_required = #{isRequired,jdbcType=BIT},-->
<!--      view_order = #{viewOrder,jdbcType=INTEGER},-->
<!--      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},-->
<!--      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},-->
<!--      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},-->
<!--      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}-->
<!--    where id = #{id,jdbcType=BIGINT}-->
<!--  </update>-->
<!--    <delete id="deleteByIds" parameterType="java.util.List">-->
<!--      delete from u_app_institution_character_item-->
<!--      where id IN-->
<!--      <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">-->
<!--        #{item}-->
<!--      </foreach>-->
<!--    </delete>-->
<!--  <select id="getMaxViewOrder" resultType="java.lang.Integer">-->
<!--    select-->
<!--      IFNULL(max(view_order)+1,0) view_order-->
<!--    from-->
<!--      u_app_institution_character_item-->
<!--  </select>-->
</mapper>