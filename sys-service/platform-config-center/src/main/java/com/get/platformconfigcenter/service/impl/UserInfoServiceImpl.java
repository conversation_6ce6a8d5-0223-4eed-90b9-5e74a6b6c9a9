package com.get.platformconfigcenter.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectKeyEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.common.utils.MD5Utils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.examcenter.feign.IExamCenterClient;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.permissioncenter.feign.IPermissionCenterClient;
import com.get.platformconfigcenter.dao.appissue.*;
import com.get.platformconfigcenter.dao.appmso.UserTypeMapper;
import com.get.platformconfigcenter.dao.registration.UserInfoMapper;
import com.get.platformconfigcenter.vo.UserInfoVo;
import com.get.platformconfigcenter.entity.*;
import com.get.platformconfigcenter.service.UserInfoService;
import com.get.platformconfigcenter.dto.UserInfoDto;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by Jerry.
 * Time: 17:35
 * Date: 2021/7/8
 * Description:用户信息管理实现类
 */
@Service
public class UserInfoServiceImpl implements UserInfoService {
    @Resource
    private UserInfoMapper userInfoMapper;
    @Resource
    private MUserInfoMapper mUserInfoMapper;
    @Resource
    private RPermissionRoleTypeUserMapper permissionRoleTypeUserMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient feignInstitutionService;
    @Resource
    private IPermissionCenterClient feignPermissionService;
    @Resource
    private IExamCenterClient feignExamService;
    @Resource
    private UserAgentMapper userAgentMapper;
    @Resource
    private IssueUserContactPersonMapper userContactPersonMapper;
    @Resource
    private StudentInstitutionCourseMapper studentInstitutionCourseMapper;
    @Resource
    private StudentInfoMapper studentInfoMapper;
    @Resource
    private UserTypeMapper userTypeMapper;

    /**
     * 用户信息管理列表数据
     *
     * @param userInfoDto
     * @param page
     * @return
     */
    @Override
    public List<UserInfoVo> getUserInfoList(UserInfoDto userInfoDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        Example example = new Example(UserInfo.class);
//        Example.Criteria criteria = example.createCriteria();
//        if(GeneralTool.isNotEmpty(userInfoDto.getFkPlatformType())){
//            criteria.andEqualTo("fkPlatformType",userInfoDto.getFkPlatformType());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getNickname())){
//            criteria.andLike("nickname","%"+userInfoDto.getNickname()+"%");
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getFamilyNamePy())){
//            criteria.andLike("familyNamePy","%"+userInfoDto.getFamilyNamePy()+"%");
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getFirstNamePy())){
//            criteria.andLike("firstNamePy","%"+userInfoDto.getFirstNamePy()+"%");
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getMobile())){
//            criteria.andEqualTo("mobile",userInfoDto.getMobile());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getGender())){
//            criteria.andEqualTo("gender",userInfoDto.getGender());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getIdentityCard())){
//            criteria.andEqualTo("identityCard",userInfoDto.getIdentityCard());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getEmail())){
//            criteria.andEqualTo("email",userInfoDto.getEmail());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getFkAreaCountryId())){
//            criteria.andEqualTo("fkAreaCountryId",userInfoDto.getFkAreaCountryId());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getFkAreaStateId())){
//            criteria.andEqualTo("fkAreaStateId",userInfoDto.getFkAreaStateId());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getFkAreaCityId())){
//            criteria.andEqualTo("fkAreaCityId",userInfoDto.getFkAreaCityId());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getMobileAreaCode())){
//            criteria.andEqualTo("mobileAreaCode",userInfoDto.getMobileAreaCode());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getCreateBeginDate())){
//            criteria.andGreaterThanOrEqualTo("gmtCreate", userInfoDto.getCreateBeginDate());
//        }
//        if(GeneralTool.isNotEmpty(userInfoDto.getCreateEndDate())){
//            criteria.andLessThanOrEqualTo("gmtCreate", userInfoDto.getCreateEndDate());
//        }
//        example.setOrderByClause("gmt_create desc");
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
//        page.restPage(userInfos);

//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        Example example = new Example(UserInfo.class);
//        Example.Criteria criteria = example.createCriteria();
        LambdaQueryWrapper<UserInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(userInfoDto.getFkPlatformType())) {
            lambdaQueryWrapper.eq(UserInfo::getFkPlatformType, userInfoDto.getFkPlatformType());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getNickname())) {
            lambdaQueryWrapper.like(UserInfo::getNickname, userInfoDto.getNickname());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getFamilyNamePy())) {
            lambdaQueryWrapper.like(UserInfo::getFamilyNamePy, userInfoDto.getFamilyNamePy());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getFirstNamePy())) {
            lambdaQueryWrapper.like(UserInfo::getFirstNamePy, userInfoDto.getFirstNamePy());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getMobile())) {
            lambdaQueryWrapper.eq(UserInfo::getMobile, userInfoDto.getMobile());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getGender())) {
            lambdaQueryWrapper.eq(UserInfo::getGender, userInfoDto.getGender());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getIdentityCard())) {
            lambdaQueryWrapper.eq(UserInfo::getIdentityCard, userInfoDto.getIdentityCard());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getEmail())) {
            lambdaQueryWrapper.eq(UserInfo::getEmail, userInfoDto.getEmail());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getFkAreaCountryId())) {
            lambdaQueryWrapper.eq(UserInfo::getFkAreaCountryId, userInfoDto.getFkAreaCountryId());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getFkAreaStateId())) {
            lambdaQueryWrapper.eq(UserInfo::getFkAreaStateId, userInfoDto.getFkAreaStateId());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getFkAreaCityId())) {
            lambdaQueryWrapper.eq(UserInfo::getFkAreaCityId, userInfoDto.getFkAreaCityId());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getMobileAreaCode())) {
            lambdaQueryWrapper.eq(UserInfo::getMobileAreaCode, userInfoDto.getMobileAreaCode());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getCreateBeginDate())) {
            lambdaQueryWrapper.ge(UserInfo::getGmtCreate, userInfoDto.getCreateBeginDate());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getCreateEndDate())) {
            lambdaQueryWrapper.le(UserInfo::getGmtCreate, userInfoDto.getCreateEndDate());
        }
        lambdaQueryWrapper.orderByDesc(UserInfo::getGmtCreate);
        IPage<UserInfo> pages = userInfoMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
        List<UserInfo> userInfos = pages.getRecords();
        page.setAll((int) pages.getTotal());

        Set<Long> userTypeIds = new HashSet<>();
        Set<Long> ids = userInfos.stream().map(UserInfo::getId).collect(Collectors.toSet());
        if (GeneralTool.isNotEmpty(ids)){
            List<UserType> userTypes = userTypeMapper.selectList(Wrappers.<UserType>lambdaQuery().eq(UserType::getType, 1).in(UserType::getFkUserId, ids));
            if (GeneralTool.isNotEmpty(userTypes)){
                userTypeIds = userTypes.stream().map(UserType::getFkUserId).collect(Collectors.toSet());
            }
        }
        List<UserInfoVo> userInfoVos = new ArrayList<>();
        for (UserInfo userInfo : userInfos) {
            UserInfoVo userInfoVo = BeanCopyUtils.objClone(userInfo, UserInfoVo::new);
            if ("0".equals(userInfo.getGender())) {
                userInfoVo.setGender("女");
            } else if ("1".equals(userInfo.getGender())) {
                userInfoVo.setGender("男");
            }
            if (userTypeIds.contains(userInfo.getId())){
                userInfoVo.setIsVipUser(true);
            }
//            userInfoVo.setFkPlatformTypeName(ProjectKeyEnum.getValue(userInfo.getFkPlatformType()));
            userInfoVo.setFkPlatformTypeName(ProjectKeyEnum.getInitialValue(userInfo.getFkPlatformType()));
            userInfoVos.add(userInfoVo);
        }
        return userInfoVos;
    }

    /**
     * 新增用户信息管理
     *
     * @param userInfoDto
     * @return
     */
    @Override
    public Long addUserInfo(UserInfoDto userInfoDto) {
        if (GeneralTool.isEmpty(userInfoDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
        }
        UserInfo userInfo = BeanCopyUtils.objClone(userInfoDto, UserInfo::new);
        utilService.updateUserInfoToEntity(userInfo);
        //设置加密密码
        userInfo.setLoginPs(MD5Utils.encrypt(userInfoDto.getLoginPs()));
        userInfoMapper.insert(userInfo);
        return userInfo.getId();
    }

    /**
     * 更新用户信息管理
     *
     * @param userInfoDto
     * @return
     */
    @Override
    public void updateUserInfo(UserInfoDto userInfoDto) {
        if (GeneralTool.isEmpty(userInfoDto)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
        }
        UserInfo checkUserInfo = userInfoMapper.selectById(userInfoDto.getId());
        if (GeneralTool.isEmpty(checkUserInfo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        UserInfo userInfo = BeanCopyUtils.objClone(userInfoDto, UserInfo::new);
        utilService.updateUserInfoToEntity(userInfo);
        //设置加密密码
        if (GeneralTool.isNotEmpty(userInfoDto.getLoginPs())) {
            userInfo.setLoginPs(MD5Utils.encrypt(userInfoDto.getLoginPs()));
        } else {
            userInfo.setLoginPs(checkUserInfo.getLoginPs());
        }
        userInfoMapper.updateByPrimaryKey(userInfo);
    }

    /**
     * 用户信息管理详情
     *
     * @param id
     * @return
     */
    @Override
    public UserInfoVo findUserInfoById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        UserInfo userInfo = userInfoMapper.selectById(id);
        if (GeneralTool.isEmpty(userInfo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        UserInfoVo userInfoVo = BeanCopyUtils.objClone(userInfo, UserInfoVo::new);
        /*if("0".equals(userInfo.getGender())){
            userInfoVo.setGender("女");
        }else if("1".equals(userInfo.getGender())){
            userInfoVo.setGender("男");
        }*/
        userInfoVo.setFkPlatformTypeName(ProjectKeyEnum.getValue(userInfo.getFkPlatformType()));
        return userInfoVo;
    }

    /**
     * 删除用户信息管理
     *
     * @param id
     * @
     */
    @Override
    public void delete(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        userInfoMapper.deleteById(id);
    }

    /**
     * @Description: feign调用 根据userid获取名称
     * @Author: Jerry
     * @Date:14:23 2021/8/23
     */
    @Override
    public Map<Long, String> getUserNamesByUserIds(Set<Long> userIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(userIds)) {
            return map;
        }
//        Example example = new Example(UserInfo.class);
//        example.createCriteria().andIn("id",userIds);
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        List<UserInfo> userInfos = userInfoMapper.selectBatchIds(userIds);
        if (GeneralTool.isEmpty(userInfos)) {
            return map;
        }
        for (UserInfo userInfo : userInfos) {
            map.put(userInfo.getId(), userInfo.getName());
        }
        return map;
    }


    /**
     * @Description: feign调用 根据userid获取名称（微信昵称）
     * @Author: Jerry
     * @Date:14:23 2021/8/23
     */
    @Override
    public Map<Long, String> getUserNickNamesByUserIds(Set<Long> userIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(userIds)) {
            return map;
        }
//        Example example = new Example(UserInfo.class);
//        example.createCriteria().andIn("id",userIds);
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        List<UserInfo> userInfos = userInfoMapper.selectBatchIds(userIds);
        if (GeneralTool.isEmpty(userInfos)) {
            return map;
        }
        for (UserInfo userInfo : userInfos) {
            map.put(userInfo.getId(), userInfo.getWechatNickname());
        }
        return map;
    }


    /**
     * @Description: feign调用 根据userid获取手机号
     * @Author: Jerry
     * @Date:10:30 2021/10/15
     */
    @Override
    public Map<Long, String> getMobileByUserIds(Set<Long> userIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(userIds)) {
            return map;
        }
//        Example example = new Example(UserInfo.class);
//        example.createCriteria().andIn("id",userIds);
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);

        List<UserInfo> userInfos = userInfoMapper.selectBatchIds(userIds);
        if (GeneralTool.isEmpty(userInfos)) {
            return map;
        }
        for (UserInfo userInfo : userInfos) {
            if (GeneralTool.isNotEmpty(userInfo.getMobile())) {
                map.put(userInfo.getId(), userInfo.getMobile());
            }
        }
        return map;
    }

    /**
     * @Description: feign调用 根据userid获取对象
     * @Author: Jerry
     * @Date:17:04 2021/8/26
     */
    @Override
    public Map<Long, UserInfoVo> getUserInfoDtoByIds(Set<Long> userIds) {
        Map<Long, UserInfoVo> map = new HashMap<>();
        if (GeneralTool.isEmpty(userIds)) {
            return map;
        }
//        Example example = new Example(UserInfo.class);
//        example.createCriteria().andIn("id",userIds);
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        List<UserInfo> userInfos = userInfoMapper.selectBatchIds(userIds);
        if (GeneralTool.isEmpty(userInfos)) {
            return map;
        }
        for (UserInfo userInfo : userInfos) {
            UserInfoVo userInfoVo = BeanCopyUtils.objClone(userInfo, UserInfoVo::new);
            map.put(userInfo.getId(), userInfoVo);
        }
        return map;
    }

    /**
     * @Description: 根据名称模糊搜索用户ids
     * @Author: Jerry
     * @Date:14:47 2021/8/23
     */
    @Override
    public Set<Long> getUserIdsByParam(String userName, Long fkAreaCityId, String bdName) {
//        Example example = new Example(UserInfo.class);
//        Example.Criteria criteria = example.createCriteria();
//        if(GeneralTool.isNotEmpty(userName)){
//            criteria.andLike("wechatNickname","%"+userName+"%");
//        }
//        if(GeneralTool.isNotEmpty(fkAreaCityId)){
//            criteria.andEqualTo("fkAreaCityId",fkAreaCityId);
//        }
//        if(GeneralTool.isNotEmpty(bdName)){
//            //根据bd名称获取员工ids
//            List<Long> staffIdsByNameKey = feignPermissionService.getStaffIdsByNameKey(bdName);
//            if(GeneralTool.isNotEmpty(staffIdsByNameKey)){
//                Set<Long> fkStaffIds = new HashSet<>(staffIdsByNameKey);
//                //根据员工ids（BD）获取用户ids
//                Set<Long> userIdsByStaffIds = feignExamService.getUserIdsByStaffIds(fkStaffIds);
//                if(GeneralTool.isEmpty(userIdsByStaffIds)){
//                    return null;
//                }
//                criteria.andIn("id",userIdsByStaffIds);
//            }else{
//                //查询不出用户
//                return null;
//            }
//        }
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        LambdaQueryWrapper<UserInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(userName)) {
            lambdaQueryWrapper.like(UserInfo::getWechatNickname, userName);
        }
        if (GeneralTool.isNotEmpty(fkAreaCityId)) {
            lambdaQueryWrapper.eq(UserInfo::getFkAreaCityId, fkAreaCityId);
        }
        if (GeneralTool.isNotEmpty(bdName)) {
            //根据bd名称获取员工ids 111111
            Result<List<Long>> resultstaffIdsByNameKey = feignPermissionService.getStaffIdsByNameKeyOrEnNameKey(bdName);
            if (!resultstaffIdsByNameKey.isSuccess()) {
                throw new GetServiceException(resultstaffIdsByNameKey.getMessage());
            }
            List<Long> staffIdsByNameKey = resultstaffIdsByNameKey.getData();
            if (GeneralTool.isNotEmpty(staffIdsByNameKey)) {
                Set<Long> fkStaffIds = new HashSet<>(staffIdsByNameKey);
                Set<Long> userIdsByStaffIds = new HashSet<>();
                //根据员工ids（BD）获取用户ids
                Result<Set<Long>> resultuserIdsByStaffIds = feignExamService.getUserIdsByStaffIds(fkStaffIds);
                if (resultuserIdsByStaffIds.isSuccess()) {
                    userIdsByStaffIds = resultuserIdsByStaffIds.getData();
                    if (GeneralTool.isEmpty(userIdsByStaffIds)) {
                        return null;
                    }
                    lambdaQueryWrapper.in(UserInfo::getId, userIdsByStaffIds);
                }

            } else {
                //查询不出用户
                return null;
            }
        }
        List<UserInfo> userInfos = userInfoMapper.selectList(lambdaQueryWrapper);


        if (GeneralTool.isEmpty(userInfos)) {
            return null;
        }
        Set<Long> userIds = userInfos.stream().map(UserInfo::getId).collect(Collectors.toSet());
        return userIds;
    }

    /**
     * @Description: 根据名称模糊或者手机号搜索用户ids
     * @Author: Jerry
     * @Date:11:14 2021/8/27
     */
    @Override
    public Set<Long> getUserIdsByNameOrMobile(String userName, String phoneNumber) {
//        Example example = new Example(UserInfo.class);
//        Example.Criteria criteria = example.createCriteria();
//        if(GeneralTool.isNotEmpty(userName)){
//            criteria.andLike("wechatNickname","%"+userName+"%");
//        }
//        if(GeneralTool.isNotEmpty(phoneNumber)){
//            criteria.andEqualTo("mobile",phoneNumber);
//        }
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        LambdaQueryWrapper<UserInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(userName)) {
            lambdaQueryWrapper.like(UserInfo::getWechatNickname, userName);
        }
        if (GeneralTool.isNotEmpty(phoneNumber)) {
            lambdaQueryWrapper.eq(UserInfo::getMobile, phoneNumber);
        }
        List<UserInfo> userInfos = userInfoMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(userInfos)) {
            return null;
        }
        Set<Long> userIds = userInfos.stream().map(UserInfo::getId).collect(Collectors.toSet());
        return userIds;
    }

    /**
     * @Description: 根据ids获取人员对应的城市名称
     * @Author: Jerry
     * @Date:10:39 2021/8/30
     */
    @Override
    public Map<Long, String> getCityNamesByUserIds(Set<Long> userIds) {
        Map<Long, String> map = new HashMap<>();
        if (GeneralTool.isEmpty(userIds)) {
            return map;
        }
//        Example example = new Example(UserInfo.class);
//        example.createCriteria().andIn("id",userIds);
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        List<UserInfo> userInfos = userInfoMapper.selectBatchIds(userIds);
        if (GeneralTool.isEmpty(userInfos)) {
            return map;
        }
        //获取所有的城市ids
        Set<Long> fkAreaCityIds = userInfos.stream().map(UserInfo::getFkAreaCityId).collect(Collectors.toSet());
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(fkAreaCityIds)) {
            //通过城市ids 查找对应的城市名称map 111111
            Result<Map<Long, String>> resultcityNamesByIds = feignInstitutionService.getCityNameChnsByIds(fkAreaCityIds);
            if (resultcityNamesByIds.isSuccess()) {
                cityNamesByIds = resultcityNamesByIds.getData();
            }
        }
        for (UserInfo userInfo : userInfos) {
            map.put(userInfo.getId(), cityNamesByIds.get(userInfo.getFkAreaCityId()));
        }
        return map;
    }

    @Override
    public List<UserInfo> getUserInfoBySearch(UserInfoDto userInfoDto) {
//        Example example = new Example(UserInfo.class);
//        Example.Criteria criteria = example.createCriteria().andEqualTo("id", userInfoDto.getUserIds());
//        if (GeneralTool.isNotEmpty(userInfoDto.getName())) {
//            criteria.andEqualTo("name", userInfoDto.getName());
//        }
//        if (GeneralTool.isNotEmpty(userInfoDto.getNameEn())) {
//            criteria.andEqualTo("nameEn", userInfoDto.getNameEn());
//        }
//        if (GeneralTool.isNotEmpty(userInfoDto.getNickname())) {
//            criteria.andEqualTo("nickname", userInfoDto.getNickname());
//        }
//        if (GeneralTool.isNotEmpty(userInfoDto.getMobile())) {
//            criteria.andEqualTo("mobile", userInfoDto.getMobile());
//        }
//        if (GeneralTool.isNotEmpty(userInfoDto.getMobileAreaCode())) {
//            criteria.andEqualTo("mobileAreaCode", userInfoDto.getMobileAreaCode());
//        }
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        LambdaQueryWrapper<UserInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (GeneralTool.isNotEmpty(userInfoDto.getName())) {
            lambdaQueryWrapper.eq(UserInfo::getName, userInfoDto.getName());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getNameEn())) {
            lambdaQueryWrapper.eq(UserInfo::getNameEn, userInfoDto.getNameEn());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getNickname())) {
            lambdaQueryWrapper.eq(UserInfo::getNickname, userInfoDto.getNickname());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getMobile())) {
            lambdaQueryWrapper.eq(UserInfo::getMobile, userInfoDto.getMobile());
        }
        if (GeneralTool.isNotEmpty(userInfoDto.getMobileAreaCode())) {
            lambdaQueryWrapper.eq(UserInfo::getMobileAreaCode, userInfoDto.getMobileAreaCode());
        }
        List<UserInfo> userInfos = userInfoMapper.selectList(lambdaQueryWrapper);
        return userInfos;
    }



//    @Override
//    public List<UserInfoVo> getUserByAgentId(Long fkAgentId, Long fkCompanyId) {
//        List<UserAgent> fkUserIds = userAgentMapper.getUserIdsByAgent(fkAgentId,fkCompanyId);
//        if (GeneralTool.isEmpty(fkUserIds)) {
//            return null;
//        }
//        Map<Long, UserAgent> collect = fkUserIds.stream().collect(Collectors.toMap(UserAgent::getFkUserId, Function.identity()));
//        List<UserInfo> userInfos = userInfoMapper.getUserInfoByIds(fkUserIds.stream().map(UserAgent::getFkUserId).collect(Collectors.toList()));
//        List<UserInfoVo> userInfoDtos = BeanCopyUtils.copyListProperties(userInfos, UserInfoVo::new);
//        userInfoDtos.stream().forEach(d->{
//            UserAgent userAgent = collect.get(d.getId());
//            d.setIsBmsPermission(userAgent.getIsBmsPermission());
//            d.setIsSubagentPermission(userAgent.getIsSubagentPermission());
//            d.setRoleType(userAgent.getRoleType());
//        });
//        return userInfoDtos;
//    }

//    @Override
//    public List<UserAgentVo> getUsersByAgentId(Long fkAgentId) {
//        LambdaQueryWrapper<UserAgent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(UserAgent::getFkAgentId, fkAgentId);
//        List<UserAgent> userAgents = userAgentMapper.selectList(lambdaQueryWrapper);
//        if (GeneralTool.isEmpty(userAgents)) {
//            return null;
//        }
//        List<UserAgentVo> userAgentDtos = BeanCopyUtils.copyListProperties(userAgents, UserAgentVo::new);
//        List<Long> userIds = userAgentDtos.stream().map(UserAgent::getFkUserId).collect(Collectors.toList());
//
//        //Map<Long, String> collect = userInfoMapper.getUserInfoByIds(userIds).stream().collect(HashMap::new,(m,v) -> m.put(v.getId(), v.getName()),HashMap::putAll);
//        Map<Long, List<UserInfo>> userInfoMap = userInfoMapper.getUserInfoByIds(userIds).stream().collect(Collectors.groupingBy(UserInfo::getId));
//        for (UserAgentVo userAgentDto : userAgentDtos) {
//            if (GeneralTool.isNotEmpty(userInfoMap)) {
//                List<UserInfo> userInfos = userInfoMap.get(userAgentDto.getFkUserId());
//                if (GeneralTool.isNotEmpty(userInfos)) {
//                    userAgentDto.setUserName(userInfos.get(0).getName());
//                    userAgentDto.setMobile(userInfos.get(0).getMobile());
//                    userAgentDto.setFkPlatformType(userInfos.get(0).getFkPlatformType());
//                }
//            }
//            if (GeneralTool.isNotEmpty(userAgentDto.getFkPlatformType())) {
//                if (userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_BMS.key)) {
//                    userAgentDto.setFkPlatformType("AIS注册");
//                } else if (userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_ISSUE.key)) {
//                    userAgentDto.setFkPlatformType("ISSUE注册");
//                } else if (userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_MSO.key)) {
//                    userAgentDto.setFkPlatformType("MSO注册");
//                } else if (userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_IB.key)) {
//                    userAgentDto.setFkPlatformType("IBS注册");
//                } else if (userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_MP_EXAM.key)) {
//                    userAgentDto.setFkPlatformType("GET_MP_EXAM注册");
//                } else if (userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_HKISO.key)) {
//                    userAgentDto.setFkPlatformType("GTE_HKISO");
//                }
//            }
//        }
//
//        return userAgentDtos;
//    }
//    @Override
//    public List<UserAgentVo> getUsersByAgentId(Long fkAgentId) {
//        LambdaQueryWrapper<UserAgent>lambdaQueryWrapper=new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(UserAgent::getFkAgentId, fkAgentId);
//        List<UserAgent> userAgents = userAgentMapper.selectList(lambdaQueryWrapper);
//        if (GeneralTool.isEmpty(userAgents)){
//            return null;
//        }
//        List<UserAgentVo> userAgentDtos = BeanCopyUtils.copyListProperties(userAgents, UserAgentVo::new);
//        List<Long> userIds = userAgentDtos.stream().map(UserAgent::getFkUserId).collect(Collectors.toList());
//
//        //Map<Long, String> collect = userInfoMapper.getUserInfoByIds(userIds).stream().collect(HashMap::new,(m,v) -> m.put(v.getId(), v.getName()),HashMap::putAll);
//        Map<Long, List<UserInfo>> userInfoMap = userInfoMapper.getUserInfoByIds(userIds).stream().collect(Collectors.groupingBy(UserInfo::getId));
//        for (UserAgentVo userAgentDto : userAgentDtos) {
//            if (GeneralTool.isNotEmpty(userInfoMap)){
//                List<UserInfo> userInfos = userInfoMap.get(userAgentDto.getFkUserId());
//                if (GeneralTool.isNotEmpty(userInfos)){
//                    userAgentDto.setUserName(userInfos.get(0).getName());
//                    userAgentDto.setMobile(userInfos.get(0).getMobile());
//                    userAgentDto.setFkPlatformType(userInfos.get(0).getFkPlatformType());
//                }
//            }
//            if (GeneralTool.isNotEmpty(userAgentDto.getFkPlatformType())){
//                if (userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_BMS.key)){
//                    userAgentDto.setFkPlatformType("AIS注册");
//                }else if(userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_ISSUE.key)){
//                    userAgentDto.setFkPlatformType("ISSUE注册");
//                }else if(userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_MSO.key)){
//                    userAgentDto.setFkPlatformType("MSO注册");
//                }else if(userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_IB.key)){
//                    userAgentDto.setFkPlatformType("IBS注册");
//                }else if(userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_MP_EXAM.key)){
//                    userAgentDto.setFkPlatformType("GET_MP_EXAM注册");
//                }else if(userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_HKISO.key)){
//                    userAgentDto.setFkPlatformType("GTE_HKISO");
//                }
//            }
//        }
//
//        return userAgentDtos;
//
//    }

//    @Override
//    public Map<Long, List<UserAgentVo>> getUsersByAgentIds(Set<Long> fkAgentIds) {
//        HashMap<Long, List<UserAgentVo>> map = new HashMap<>();
//        List<UserAgent> userAgents = userAgentMapper.getUsersAgentList(fkAgentIds);
//        if (GeneralTool.isEmpty(userAgents)){
//            return null;
//        }
//        List<UserAgentVo> userAgentDtos = BeanCopyUtils.copyListProperties(userAgents, UserAgentVo::new);
//        List<Long> userIds = userAgentDtos.stream().map(UserAgent::getFkUserId).collect(Collectors.toList());
//
//        //后期优化
//        Map<Long, List<UserInfo>> userInfoMap = userInfoMapper.getUserInfoByIds(userIds).stream().collect(Collectors.groupingBy(UserInfo::getId));
////        if(GeneralTool.isNotEmpty(userInfoMap)){
////            Map<Long, String> collect = userInfoMapper.getUserInfoByIds(userIds).stream().collect(HashMap::new,(m,v) -> m.put(v.getId(), v.getName()),HashMap::putAll);
////            userAgentDtos.stream().forEach(d->d.setUserName(userInfoMap.get(d.getFkUserId()).get(0).getName()));
////            userAgentDtos.stream().forEach(d->d.setMobile(userInfoMap.get(d.getFkUserId()).get(0).getMobile()));
////            userAgentDtos.stream().forEach(d->d.setFkPlatformType(userInfoMap.get(d.getFkUserId()).get(0).getFkPlatformType()));
////            Map<Long, String> mobileCollect = userInfoMapper.getUserInfoByIds(userIds).stream().collect(HashMap::new,(m,v) -> m.put(v.getId(), v.getMobile()),
////                    HashMap::putAll);
////            userAgentDtos.stream().forEach(d->d.setMobile(mobileCollect.get(d.getFkUserId())));
////
////            Map<Long, String> platformTypecollect = userInfoMapper.getUserInfoByIds(userIds).stream().collect(HashMap::new,(m,v) -> m.put(v.getId(),
////                    v.getFkPlatformType()),
////                    HashMap::putAll);
////            userAgentDtos.stream().forEach(d->d.setFkPlatformType(platformTypecollect.get(d.getFkUserId())));
////        }
////        Map<Long, String> collect = userInfoMapper.getUserInfoByIds(userIds).stream().collect(HashMap::new,(m,v) -> m.put(v.getId(), v.getName()),HashMap::putAll);
////        userAgentDtos.stream().forEach(d->d.setUserName(collect.get(d.getFkUserId())));
////
////        Map<Long, String> mobileCollect = userInfoMapper.getUserInfoByIds(userIds).stream().collect(HashMap::new,(m,v) -> m.put(v.getId(), v.getMobile()),
////                HashMap::putAll);
////        userAgentDtos.stream().forEach(d->d.setMobile(mobileCollect.get(d.getFkUserId())));
////
////        Map<Long, String> platformTypecollect = userInfoMapper.getUserInfoByIds(userIds).stream().collect(HashMap::new,(m,v) -> m.put(v.getId(),
////                v.getFkPlatformType()),
////                HashMap::putAll);
////        userAgentDtos.stream().forEach(d->d.setFkPlatformType(platformTypecollect.get(d.getFkUserId())));
//        for (UserAgentVo userAgentDto : userAgentDtos) {
//            if (GeneralTool.isNotEmpty(userInfoMap)){
//                List<UserInfo> userInfos = userInfoMap.get(userAgentDto.getFkUserId());
//                if (GeneralTool.isNotEmpty(userInfos)){
//                    userAgentDto.setUserName(userInfos.get(0).getName());
//                    userAgentDto.setMobile(userInfos.get(0).getMobile());
//                    userAgentDto.setFkPlatformType(userInfos.get(0).getFkPlatformType());
//                }
//            }
//            if (GeneralTool.isNotEmpty(userAgentDto.getFkPlatformType())){
//                if (userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_BMS.key)){
//                    userAgentDto.setFkPlatformType("AIS注册");
//                }else if(userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_ISSUE.key)){
//                    userAgentDto.setFkPlatformType("ISSUE注册");
//                }else if(userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_MSO.key)){
//                    userAgentDto.setFkPlatformType("MSO注册");
//                }else if(userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_IB.key)){
//                    userAgentDto.setFkPlatformType("IBS注册");
//                }else if(userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_MP_EXAM.key)){
//                    userAgentDto.setFkPlatformType("GET_MP_EXAM注册");
//                }else if(userAgentDto.getFkPlatformType().equals(ProjectKeyEnum.GET_HKISO.key)){
//                    userAgentDto.setFkPlatformType("GTE_HKISO");
//                }
//            }
//            if(GeneralTool.isNotEmpty(mUserInfos) && mUserInfos.size()>0)
//            {
//                MUserInfo mUserInfo = mUserInfos.stream().filter(mUserInfo_ -> mUserInfo_.getFkUserId().equals(userAgentDto.getFkUserId())).findFirst().orElse(null);
//                if(mUserInfo!=null)
//                {
//                    userAgentDto.setIsModifiedPs(false);//默认false
//                    if(GeneralTool.isNotEmpty(mUserInfo.getIsModifiedPs()))
//                    {
//                        userAgentDto.setIsModifiedPs(mUserInfo.getIsModifiedPs());
//                    }
//                }
//            }
//            if (map.containsKey(userAgentDto.getFkAgentId())){
//                List<UserAgentVo> agentDtos = map.get(userAgentDto.getFkAgentId());
//                agentDtos.add(userAgentDto);
//                map.put(userAgentDto.getFkAgentId(), agentDtos);
//                continue;
//            }
//            List<UserAgentVo> userAgentDtolist = new ArrayList<>();
//            userAgentDtolist.add(userAgentDto);
//            map.put(userAgentDto.getFkAgentId(), userAgentDtolist);
//        }
//        return map;
//    }

    @Override
    public Map<Long, UserInfo> getUserByStudentIds(Set<Long> studentIds) {
        HashMap<Long, UserInfo> map = new HashMap<>();

        LambdaQueryWrapper<StudentInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(StudentInfo::getId,studentIds);
        List<StudentInfo> studentInfos = studentInfoMapper.selectList(lambdaQueryWrapper);
        if (GeneralTool.isEmpty(studentInfos)){
            return null;
        }
        for (StudentInfo studentInfo : studentInfos) {
            if (GeneralTool.isNotEmpty(studentInfo.getGmtCreateUserId())){
                UserInfo userInfo = userInfoMapper.selectById(studentInfo.getGmtCreateUserId());
                if (GeneralTool.isNotEmpty(userInfo)){
                    map.put(studentInfo.getId(),userInfo);
                }
            }
        }
        return map;
    }


    @Override
    public void getUpdateUserPw(String pw,Long fkUserId) {
        String encrypt = MD5Utils.encrypt(pw);
        UserInfo userInfo = userInfoMapper.selectById(fkUserId);
        if (GeneralTool.isEmpty(userInfo)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        userInfo.setLoginPs(encrypt);
        utilService.setUpdateInfo(userInfo);
        userInfoMapper.updateByPrimaryKey(userInfo);


    }

//    @Override
//    public void getSetIssueRoot(UserAgentDto userAgentVo) {
//        LambdaQueryWrapper<UserAgent>lambdaQueryWrapper=new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.eq(UserAgent::getFkUserId, userAgentVo.getFkUserId());
//        lambdaQueryWrapper.eq(UserAgent::getFkCompanyId, userAgentVo.getFkCompanyId());
//        UserAgent userAgent1 = userAgentMapper.selectOne(lambdaQueryWrapper);
//        if (GeneralTool.isEmpty(userAgent1))throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        userAgent1.setIsBmsPermission(userAgentVo.getIsBmsPermission());
//        userAgent1.setRoleType(userAgentVo.getRoleType());
//        userAgent1.setIsSubagentPermission(userAgentVo.getIsSubagentPermission());
//        utilService.setUpdateInfo(userAgent1);
//        userAgentMapper.updateById(userAgent1);
//    }

    @Override
    public void updateUserVip(Long id, Boolean isVip) {
        if (isVip){
            UserType userType = new UserType();
            userType.setFkUserId(id);
            userType.setType(1);
            utilService.setCreateInfo(userType);
            userTypeMapper.insert(userType);
        }else {
            LambdaQueryWrapper<UserType> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserType::getFkUserId,id).eq(UserType::getType,1);
            userTypeMapper.delete(wrapper);
        }
    }




//    @Override
//    public void updateIsShowAppStatusPermission(Long id, Boolean isShowAppStatusPermission) {
//        UserAgent userAgent = userAgentMapper.selectById(id);
//        if (GeneralTool.isEmpty(userAgent)){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        userAgent.setIsShowAppStatusPermission(isShowAppStatusPermission);
//        utilService.setUpdateInfo(userAgent);
//        userAgentMapper.updateById(userAgent);
//    }

//    @Override
//    public Boolean insertIssueAgentUser(Long fkAgentId, Long fkCompanyId, Long fkUserId) {
//        if (GeneralTool.isEmpty(fkAgentId) || GeneralTool.isEmpty(fkCompanyId) || GeneralTool.isEmpty(fkUserId) ){
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
//        UserAgent userAgent = new UserAgent();
//        userAgent.setRoleType(0);
//        userAgent.setIsSubagentPermission(true);
//        userAgent.setIsBmsPermission(true);
//        userAgent.setIsShowAppStatusPermission(true);
//        userAgent.setFkCompanyId(fkCompanyId);
//        userAgent.setFkUserId(fkUserId);
//        userAgent.setFkAgentId(fkAgentId);
//        utilService.setCreateInfo(userAgent);
//        userAgentMapper.insert(userAgent);
//        return true;
//    }

//    @Override
//    public Map<Long, UserInfo> getUserByofferItemIssueCourseIds(Set<Long> offerItemIssueCourseIds) {
//        HashMap<Long, UserInfo> map = new HashMap<>();
//
//        LambdaQueryWrapper<StudentInstitutionCourse> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.in(StudentInstitutionCourse::getId,offerItemIssueCourseIds);
//        List<StudentInstitutionCourse> list = studentInstitutionCourseMapper.selectList(lambdaQueryWrapper);
//        if (GeneralTool.isEmpty(list)){
//            return null;
//        }
//        for (StudentInstitutionCourse course : list) {
//            if (GeneralTool.isNotEmpty(course.getGmtCreateUserId())){
//                UserInfo userInfo = userInfoMapper.selectById(course.getGmtCreateUserId());
//                if (GeneralTool.isNotEmpty(userInfo)){
//                    map.put(course.getId(),userInfo);
//                }
//            }
//        }
//        return map;
//    }

//    @Override
////    @Transactional(rollbackFor = Exception.class) //加上事务有问题，暂时没有找到原因
//    public Boolean removeIssueRelationByAgentId(Long fkAgentId) {
//        LambdaQueryWrapper<UserAgent> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        lambdaQueryWrapper.in(UserAgent::getFkAgentId,fkAgentId);
//        List<UserAgent> userAgent = userAgentMapper.selectList(lambdaQueryWrapper);
//        if(GeneralTool.isNotEmpty(userAgent) && userAgent.size()>0)
//        {
//            for (UserAgent userAgent1 : userAgent) {
//                userAgentMapper.deleteById(userAgent1.getId());
//                userInfoMapper.deleteById(userAgent1.getFkUserId());
//            }
//    @Override
//    @DSTransactional
//    public Boolean updateIssueUserContactPersonTypeKeyByPersonId(Long fkContactPersonId,String fkContactPersonTypeKey) {
//        //判断是否拥有user
//        //如user存在，则更新ContactPersonTypeKey
//        //	r_user_contact_person
//        //	m_user_info
//        //	r_permission_role_type_user
//        IssueUserContactPerson userContactPerson = userContactPersonMapper.selectOne(Wrappers.<IssueUserContactPerson>lambdaQuery().eq(IssueUserContactPerson::getFkContactPersonId, fkContactPersonId).last("limit 1"));
//        if(GeneralTool.isNotEmpty(userContactPerson))
//        {
//            MUserInfo mUserInfo = mUserInfoMapper.selectOne(Wrappers.<MUserInfo>lambdaQuery().eq(MUserInfo::getFkUserId, userContactPerson.getFkUserId()).last("limit 1"));
//            if(GeneralTool.isNotEmpty(mUserInfo))
//            {
//                mUserInfo.setFkContactPersonTypeKey(fkContactPersonTypeKey);
//                mUserInfoMapper.updateById(mUserInfo);
//            }
//
//            IssueRPermissionRoleTypeUser issueRPermissionRoleTypeUser = permissionRoleTypeUserMapper.selectOne(Wrappers.<IssueRPermissionRoleTypeUser>lambdaQuery().eq(IssueRPermissionRoleTypeUser::getFkUserId, userContactPerson.getFkUserId()).last("limit 1"));
//            if(GeneralTool.isNotEmpty(issueRPermissionRoleTypeUser))
//            {
//                issueRPermissionRoleTypeUser.setFkContactPersonTypeKey(fkContactPersonTypeKey);
//                permissionRoleTypeUserMapper.updateById(issueRPermissionRoleTypeUser);
//            }
//        }
//        return true;
//    }
}
