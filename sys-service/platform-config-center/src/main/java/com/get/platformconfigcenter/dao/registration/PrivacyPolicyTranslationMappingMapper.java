package com.get.platformconfigcenter.dao.registration;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.TranslationMapping;
import org.apache.ibatis.annotations.Mapper;

@Mapper
@DS("registrationdb")
public interface PrivacyPolicyTranslationMappingMapper extends BaseMapper<TranslationMapping> {

    int insert(TranslationMapping record);

    int insertSelective(TranslationMapping record);

    int updateByPrimaryKeySelective(TranslationMapping record);

    int updateByPrimaryKey(TranslationMapping record);
}