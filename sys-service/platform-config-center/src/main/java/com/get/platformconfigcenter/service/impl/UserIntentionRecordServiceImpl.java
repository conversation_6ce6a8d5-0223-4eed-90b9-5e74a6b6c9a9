package com.get.platformconfigcenter.service.impl;

import com.get.helpcenter.feign.IHelpCenterClient;
import com.get.platformconfigcenter.dao.appmsolog.LogUserHelpMsoMapper;
import com.get.platformconfigcenter.dao.registration.UserInfoMapper;
import com.get.platformconfigcenter.service.UserIntentionRecordService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 平台用户意向记录业务逻辑类
 *
 * <AUTHOR>
 * @date 2021/9/7 15:58
 */
@Service
public class UserIntentionRecordServiceImpl implements UserIntentionRecordService {
    @Resource
    private LogUserHelpMsoMapper logUserHelpMsoMapper;
    @Resource
    private UserInfoMapper userInfoMapper;
    @Resource
    private IHelpCenterClient helpCenterClient;

    /**
     * 平台用户意向记录列表
     *
     * @Date 15:32 2021/9/7
     * <AUTHOR>
     */
   /* @Override
    public List<LogUserHelpMsoVo> getUserIntentionRecordList(LogUserHelpMsoListDto logUserHelpMsoListVo, SearchBean<LogUserHelpMsoListDto> page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        //要按照最新记录排序
//        List<LogUserHelpMsoVo> logUserHelpMsoDtos = logUserHelpMsoMapper.selectUserIntentionRecordList(logUserHelpMsoListVo);
//        page.restPage(logUserHelpMsoDtos);
//
//        List<Long> userIds = logUserHelpMsoDtos.stream().map(LogUserHelpMso::getFkUserId).collect(Collectors.toList());
//        Example example = new Example(UserInfo.class);
//        Example.Criteria criteria = example.createCriteria().andEqualTo("id", userIds);
//        if (GeneralTool.isNotEmpty(logUserHelpMsoListVo.getName())) {
//            criteria.andEqualTo("name", logUserHelpMsoListVo.getName());
//        }
//        if (GeneralTool.isNotEmpty(logUserHelpMsoListVo.getNameEn())) {
//            criteria.andEqualTo("nameEn", logUserHelpMsoListVo.getNameEn());
//        }
//        if (GeneralTool.isNotEmpty(logUserHelpMsoListVo.getNickname())) {
//            criteria.andEqualTo("nickname", logUserHelpMsoListVo.getNickname());
//        }
//        if (GeneralTool.isNotEmpty(logUserHelpMsoListVo.getMobile())) {
//            criteria.andEqualTo("mobile", logUserHelpMsoListVo.getMobile());
//        }
//        if (GeneralTool.isNotEmpty(logUserHelpMsoListVo.getMobileAreaCode())) {
//            criteria.andEqualTo("mobileAreaCode", logUserHelpMsoListVo.getMobileAreaCode());
//        }
//        List<UserInfo> userInfos = userInfoMapper.selectByExample(example);
        IPage<LogUserHelpMsoVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        //要按照最新记录排序
        List<LogUserHelpMsoVo> logUserHelpMsoDtos = logUserHelpMsoMapper.selectUserIntentionRecordList(pages, logUserHelpMsoListVo);
        page.setAll((int) pages.getTotal());

        List<Long> userIds = logUserHelpMsoDtos.stream().map(LogUserHelpMso::getFkUserId).collect(Collectors.toList());
        List<LogUserHelpMsoVo> dates = new ArrayList<>();
        if (GeneralTool.isNotEmpty(userIds)) {
//        Example example = new Example(UserInfo.class);
//        Example.Criteria criteria = example.createCriteria().andEqualTo("id", userIds);
            LambdaQueryWrapper<UserInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
            lambdaQueryWrapper.in(UserInfo::getId, userIds);
            if (GeneralTool.isNotEmpty(logUserHelpMsoListVo.getName())) {
                lambdaQueryWrapper.eq(UserInfo::getName, logUserHelpMsoListVo.getName());
            }
            if (GeneralTool.isNotEmpty(logUserHelpMsoListVo.getNameEn())) {
                lambdaQueryWrapper.eq(UserInfo::getNameEn, logUserHelpMsoListVo.getNameEn());
            }
            if (GeneralTool.isNotEmpty(logUserHelpMsoListVo.getNickname())) {
                lambdaQueryWrapper.eq(UserInfo::getNickname, logUserHelpMsoListVo.getNickname());
            }
            if (GeneralTool.isNotEmpty(logUserHelpMsoListVo.getMobile())) {
                lambdaQueryWrapper.eq(UserInfo::getMobile, logUserHelpMsoListVo.getMobile());
            }
            if (GeneralTool.isNotEmpty(logUserHelpMsoListVo.getMobileAreaCode())) {
                lambdaQueryWrapper.eq(UserInfo::getMobileAreaCode, logUserHelpMsoListVo.getMobileAreaCode());
            }
            List<UserInfo> userInfos = userInfoMapper.selectList(lambdaQueryWrapper);

            for (LogUserHelpMsoVo logUserHelpMsoDto : logUserHelpMsoDtos) {
                for (UserInfo userInfo : userInfos) {
                    if (userInfo.getId().equals(logUserHelpMsoDto.getFkUserId())) {
                        logUserHelpMsoDto.setName(userInfo.getName());
                        logUserHelpMsoDto.setNameEn(userInfo.getNameEn());
                        logUserHelpMsoDto.setFamilyNamePy(userInfo.getFamilyNamePy());
                        logUserHelpMsoDto.setFirstNamePy(userInfo.getFirstNamePy());
                        logUserHelpMsoDto.setNickname(userInfo.getNickname());
                        logUserHelpMsoDto.setMobileAreaCode(userInfo.getMobileAreaCode());
                        logUserHelpMsoDto.setMobile(userInfo.getMobile());
                        logUserHelpMsoDto.setGender(userInfo.getGender());
                        dates.add(logUserHelpMsoDto);
                        break;
                    }
                }
            }
        }


        return dates;
    }*/


    /**
     * 明细意向记录
     *
     * @Date 17:45 2021/9/7
     * <AUTHOR>
     */
/*    @Override
    public List<LogUserHelpMsoVo> detail(Long id) {
//        Example example = new Example(LogUserHelpMso.class);
//        example.createCriteria().andEqualTo("fkUserId", id);
//        example.orderBy("gmtCreate").desc();
//        List<LogUserHelpMso> logUserHelpMoses = logUserHelpMsoMapper.selectByExample(example);

        List<LogUserHelpMso> logUserHelpMoses = logUserHelpMsoMapper.selectList(Wrappers.<LogUserHelpMso>lambdaQuery().eq(LogUserHelpMso::getFkUserId, id).orderByDesc(LogUserHelpMso::getGmtCreate));
        return logUserHelpMoses.stream().map(logUserHelpMso -> BeanCopyUtils.objClone(logUserHelpMso, LogUserHelpMsoVo::new)).collect(Collectors.toList());
    }*/

    /**
     * 意向摘要
     *
     * @return
     * @Date 10:30 2021/9/8
     * <AUTHOR>
     */
/*    @Override
    public List<HelpDto1> intention(Long id) {
//        Example example = new Example(LogUserHelpMso.class);
//        example.createCriteria().andEqualTo("fkUserId", id);
//        List<LogUserHelpMso> logUserHelpMoses = logUserHelpMsoMapper.selectByExample(example);
        List<LogUserHelpMso> logUserHelpMoses = logUserHelpMsoMapper.selectList(Wrappers.<LogUserHelpMso>lambdaQuery().eq(LogUserHelpMso::getFkUserId, id));
        Set<Long> helpIds = logUserHelpMoses.stream().map(LogUserHelpMso::getFkHelpId).collect(Collectors.toSet());
        List<HelpDto1> list = new ArrayList<>();
        Result<List<HelpDto1>> result = helpCenterClient.getHelpDtoByHelpId(helpIds);
        if (result.isSuccess() && GeneralTool.isNotEmpty(result.getData())) {
            list = result.getData();
        }
        return list;
    }*/

}
