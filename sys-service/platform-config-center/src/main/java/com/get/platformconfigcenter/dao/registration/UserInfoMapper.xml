<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.get.platformconfigcenter.dao.registration.UserInfoMapper">
  <insert id="insert" parameterType="com.get.platformconfigcenter.entity.UserInfo">
    insert into m_user (id, fk_platform_type, login_id, 
      login_ps, num, name, 
      name_en, family_name_py, first_name_py, 
      nickname, gender, birthday, 
      identity_card, mobile_area_code, mobile, 
      email, company, position, 
      fk_area_country_id, fk_area_state_id, fk_area_city_id, 
      zip_code, address, qq, 
      wechat, whatsapp, wechat_nickname, wechat_icon_url, wechat_openid, is_active,
      gmt_create, gmt_create_user, gmt_modified, 
      gmt_modified_user)
    values (#{id,jdbcType=BIGINT}, #{fkPlatformType,jdbcType=VARCHAR}, #{loginId,jdbcType=VARCHAR}, 
      #{loginPs,jdbcType=VARCHAR}, #{num,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, 
      #{nameEn,jdbcType=VARCHAR}, #{familyNamePy,jdbcType=VARCHAR}, #{firstNamePy,jdbcType=VARCHAR}, 
      #{nickname,jdbcType=VARCHAR}, #{gender,jdbcType=INTEGER}, #{birthday,jdbcType=DATE}, 
      #{identityCard,jdbcType=VARCHAR}, #{mobileAreaCode,jdbcType=VARCHAR}, #{mobile,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{company,jdbcType=VARCHAR}, #{position,jdbcType=VARCHAR}, 
      #{fkAreaCountryId,jdbcType=BIGINT}, #{fkAreaStateId,jdbcType=BIGINT}, #{fkAreaCityId,jdbcType=BIGINT}, 
      #{zipCode,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, #{qq,jdbcType=VARCHAR}, 
      #{wechat,jdbcType=VARCHAR}, #{whatsapp,jdbcType=VARCHAR},#{wechatNickname,jdbcType=VARCHAR},#{wechatIconUrl,jdbcType=VARCHAR},
      #{wechatOpenid,jdbcType=VARCHAR},#{isActive,jdbcType=BIT},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{gmtCreateUser,jdbcType=VARCHAR}, #{gmtModified,jdbcType=TIMESTAMP}, 
      #{gmtModifiedUser,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.get.platformconfigcenter.entity.UserInfo">
    insert into m_user
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="fkPlatformType != null">
        fk_platform_type,
      </if>
      <if test="loginId != null">
        login_id,
      </if>
      <if test="loginPs != null">
        login_ps,
      </if>
      <if test="num != null">
        num,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="nameEn != null">
        name_en,
      </if>
      <if test="familyNamePy != null">
        family_name_py,
      </if>
      <if test="firstNamePy != null">
        first_name_py,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="gender != null">
        gender,
      </if>
      <if test="birthday != null">
        birthday,
      </if>
      <if test="identityCard != null">
        identity_card,
      </if>
      <if test="mobileAreaCode != null">
        mobile_area_code,
      </if>
      <if test="mobile != null">
        mobile,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="company != null">
        company,
      </if>
      <if test="position != null">
        position,
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id,
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id,
      </if>
      <if test="fkAreaCityId != null">
        fk_area_city_id,
      </if>
      <if test="zipCode != null">
        zip_code,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="qq != null">
        qq,
      </if>
      <if test="wechat != null">
        wechat,
      </if>
      <if test="whatsapp != null">
        whatsapp,
      </if>
      <if test="wechatNickname != null">
        wechat_nickname,
      </if>
      <if test="wechatIconUrl != null">
        wechat_icon_url,
      </if>
      <if test="wechatOpenid != null">
        wechat_openid,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="fkPlatformType != null">
        #{fkPlatformType,jdbcType=VARCHAR},
      </if>
      <if test="loginId != null">
        #{loginId,jdbcType=VARCHAR},
      </if>
      <if test="loginPs != null">
        #{loginPs,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="familyNamePy != null">
        #{familyNamePy,jdbcType=VARCHAR},
      </if>
      <if test="firstNamePy != null">
        #{firstNamePy,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        #{gender,jdbcType=INTEGER},
      </if>
      <if test="birthday != null">
        #{birthday,jdbcType=DATE},
      </if>
      <if test="identityCard != null">
        #{identityCard,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        #{company,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        #{position,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryId != null">
        #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityId != null">
        #{fkAreaCityId,jdbcType=BIGINT},
      </if>
      <if test="zipCode != null">
        #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="qq != null">
        #{qq,jdbcType=VARCHAR},
      </if>
      <if test="wechat != null">
        #{wechat,jdbcType=VARCHAR},
      </if>
      <if test="whatsapp != null">
        #{whatsapp,jdbcType=VARCHAR},
      </if>
      <if test="wechatNickname != null">
        #{wechatNickname,jdbcType=VARCHAR},
      </if>
      <if test="wechatIconUrl != null">
        #{wechatIconUrl,jdbcType=VARCHAR},
      </if>
      <if test="wechatOpenid != null">
        #{wechatOpenid,jdbcType=VARCHAR},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.get.platformconfigcenter.entity.UserInfo">
    update m_user
    <set>
      <if test="fkPlatformType != null">
        fk_platform_type = #{fkPlatformType,jdbcType=VARCHAR},
      </if>
      <if test="loginId != null">
        login_id = #{loginId,jdbcType=VARCHAR},
      </if>
      <if test="loginPs != null">
        login_ps = #{loginPs,jdbcType=VARCHAR},
      </if>
      <if test="num != null">
        num = #{num,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="nameEn != null">
        name_en = #{nameEn,jdbcType=VARCHAR},
      </if>
      <if test="familyNamePy != null">
        family_name_py = #{familyNamePy,jdbcType=VARCHAR},
      </if>
      <if test="firstNamePy != null">
        first_name_py = #{firstNamePy,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="gender != null">
        gender = #{gender,jdbcType=INTEGER},
      </if>
      <if test="birthday != null">
        birthday = #{birthday,jdbcType=DATE},
      </if>
      <if test="identityCard != null">
        identity_card = #{identityCard,jdbcType=VARCHAR},
      </if>
      <if test="mobileAreaCode != null">
        mobile_area_code = #{mobileAreaCode,jdbcType=VARCHAR},
      </if>
      <if test="mobile != null">
        mobile = #{mobile,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="company != null">
        company = #{company,jdbcType=VARCHAR},
      </if>
      <if test="position != null">
        position = #{position,jdbcType=VARCHAR},
      </if>
      <if test="fkAreaCountryId != null">
        fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaStateId != null">
        fk_area_state_id = #{fkAreaStateId,jdbcType=BIGINT},
      </if>
      <if test="fkAreaCityId != null">
        fk_area_city_id = #{fkAreaCityId,jdbcType=BIGINT},
      </if>
      <if test="zipCode != null">
        zip_code = #{zipCode,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="qq != null">
        qq = #{qq,jdbcType=VARCHAR},
      </if>
      <if test="wechat != null">
        wechat = #{wechat,jdbcType=VARCHAR},
      </if>
      <if test="whatsapp != null">
        whatsapp = #{whatsapp,jdbcType=VARCHAR},
      </if>
      <if test="wechatNickname != null">
        wechat_nickname = #{wechatNickname,jdbcType=VARCHAR},
      </if>
      <if test="wechatIconUrl != null">
        wechat_icon_url = #{wechatIconUrl,jdbcType=VARCHAR},
      </if>
      <if test="wechatOpenid != null">
        wechat_openid = #{wechatOpenid,jdbcType=VARCHAR},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=BIT},
      </if>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtCreateUser != null">
        gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModifiedUser != null">
        gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.get.platformconfigcenter.entity.UserInfo">
    update m_user
    set fk_platform_type = #{fkPlatformType,jdbcType=VARCHAR},
      login_id = #{loginId,jdbcType=VARCHAR},
      login_ps = #{loginPs,jdbcType=VARCHAR},
      num = #{num,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      name_en = #{nameEn,jdbcType=VARCHAR},
      family_name_py = #{familyNamePy,jdbcType=VARCHAR},
      first_name_py = #{firstNamePy,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      gender = #{gender,jdbcType=INTEGER},
      birthday = #{birthday,jdbcType=DATE},
      identity_card = #{identityCard,jdbcType=VARCHAR},
      mobile_area_code = #{mobileAreaCode,jdbcType=VARCHAR},
      mobile = #{mobile,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      company = #{company,jdbcType=VARCHAR},
      position = #{position,jdbcType=VARCHAR},
      fk_area_country_id = #{fkAreaCountryId,jdbcType=BIGINT},
      fk_area_state_id = #{fkAreaStateId,jdbcType=BIGINT},
      fk_area_city_id = #{fkAreaCityId,jdbcType=BIGINT},
      zip_code = #{zipCode,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      qq = #{qq,jdbcType=VARCHAR},
      wechat = #{wechat,jdbcType=VARCHAR},
      whatsapp = #{whatsapp,jdbcType=VARCHAR},
      wechat_nickname = #{wechatNickname,jdbcType=VARCHAR},
      wechat_icon_url = #{wechatIconUrl,jdbcType=VARCHAR},
      wechat_openid = #{wechatOpenid,jdbcType=VARCHAR},
      is_active = #{isActive,jdbcType=BIT},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_create_user = #{gmtCreateUser,jdbcType=VARCHAR},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      gmt_modified_user = #{gmtModifiedUser,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getUserInfoByIds" resultType="com.get.platformconfigcenter.entity.UserInfo">
    SELECT id,fk_platform_type,name,nickname,login_id as mobile FROM m_user WHERE id IN
    <foreach collection="fkUserIds" item="fkUserId" open="(" separator="," close=")">
      #{fkUserId}
    </foreach>
  </select>
</mapper>