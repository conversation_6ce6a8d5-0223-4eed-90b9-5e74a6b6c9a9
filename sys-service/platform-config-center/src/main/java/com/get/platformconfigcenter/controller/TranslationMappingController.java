package com.get.platformconfigcenter.controller;

import com.get.platformconfigcenter.service.TranslationMappingService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @Description:语言翻译管理
 * @Param
 * @Date 16:50 2021/5/12
 * <AUTHOR>
 */
@Api(tags = "语言翻译管理")
@RestController
@RequestMapping("/platform/translationMapping")
public class TranslationMappingController {
    @Resource
    private TranslationMappingService translationMappingService;


    /**
     * @return com.get.common.result.ResponseBo<com.get.institutioncenter.vo.InstitutionProviderVo>
     * @Description: 列表数据
     * @Param [page]
     * <AUTHOR>
     */
//    @ApiOperation(value = "列表数据")
//    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/MSO/国家资讯配置/查询翻译")
//    @PostMapping("getTranslationMappingDtos")
//    public ResponseBo<TranslationMappingVo> getTranslationMappingDtos(@RequestBody TranslationDto translationVo) {
//        List<TranslationMappingVo> datas = translationMappingService.getTranslationMappingDtos(translationVo);
//        return new ListResponseBo<>(datas);
//    }

    /**
     * 保存翻译配置
     *
     * @param translationVos
     * @return
     * @
     */

//    @ApiOperation(value = "保存翻译接口")
//    @OperationLogger(module = LoggerModulesConsts.INSTITUTIONCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/MSO/国家资讯配置/保存翻译")
//    @PostMapping("updateTranslations")
//    public ResponseBo updateTranslations(@RequestBody @Validated(TranslationDto.Add.class) List<TranslationDto> translationVos) {
//        translationMappingService.updateTranslations(translationVos);
//        return ResponseBo.ok();
//    }


}
