package com.get.platformconfigcenter.controller;

import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 代理配置管理类
 *
 * <AUTHOR>
 * @date 2021/5/13 9:44
 */
@Api(tags = "代理配置")
@RestController
@RequestMapping("platform/agent")
public class AgentController {
//    @Resource
//    private AgentInfoService agentInfoService;
//
//    @ApiOperation(value = "代理配置列表数据", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/MSO/代理配置/查询")
//    @PostMapping("datas")
//    public ResponseBo<AgentInfoVo> datas(@RequestBody SearchBean<AgentInfoDto> page) {
//        List<AgentInfoVo> agentInfoDtos = agentInfoService.getAgentInfoList(page.getData(), page);
//        Page p = BeanCopyUtils.objClone(page, Page::new);
//        return new ListResponseBo<>(agentInfoDtos, p);
//    }
//
//    /**
//     * 新增代理配置
//     *
//     * @param agentInfoVo
//     * @return
//     * @
//     */
//    @ApiOperation(value = "新增代理配置接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/MSO/代理配置/新增代理配置")
//    @PostMapping("add")
//    public ResponseBo add(@RequestBody @Validated(AgentInfoDto.Add.class) AgentInfoDto agentInfoVo) {
//        return SaveResponseBo.ok(agentInfoService.addAgentInfo(agentInfoVo));
//    }
//
//    /**
//     * @Description:更新代理配置
//     * @Param
//     * @Date 16:27 2021/5/13
//     * <AUTHOR>
//     */
//    @ApiOperation(value = "修改接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/MSO/代理配置/更新代理配置")
//    @PostMapping("update")
//    public ResponseBo<AgentInfoVo> update(@RequestBody @Validated(AgentInfoDto.Update.class) AgentInfoDto agentInfoVo) {
//        return UpdateResponseBo.ok(agentInfoService.updateAgentInfo(agentInfoVo));
//    }
//
//    /**
//     * @Description:代理配置详情
//     * @Param
//     * @Date 12:52 2021/5/13
//     * <AUTHOR>
//     */
//    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/MSO/代理配置/详情")
//    @GetMapping("/{id}")
//    public ResponseBo<AgentInfoVo> detail(@PathVariable("id") Long id) {
//        AgentInfoVo data = agentInfoService.findAgentInfoById(id);
//        return new ResponseBo<>(data);
//    }
//
//    /**
//     * @Description:删除代理配置
//     * @Param
//     * @Date 16:56 2021/5/13
//     * <AUTHOR>
//     */
//    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/MSO/代理配置/删除代理配置")
//    @PostMapping("delete/{id}")
//    public ResponseBo delete(@PathVariable("id") Long id) {
//        agentInfoService.delete(id);
//        return DeleteResponseBo.ok();
//    }
//
//
//    /**
//     * @Description: 分配siteMap
//     * @Author: Jerry
//     * @Date:18:22 2021/8/13
//     */
//    @ApiOperation(value = "分配siteMap", notes = "链接上的id为代理id,sitemapVoIds为菜单id,格式为：[1,2,3]")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/MSO/代理配置/分配siteMap")
//    @PostMapping("allocationSitemap/{id}")
//    public ResponseBo allocationSitemap(@PathVariable("id") Long id, @RequestBody ValidList<String> sitemapVoIds) {
//        agentInfoService.allocationSitemap(id, sitemapVoIds);
//        return SaveResponseBo.ok();
//    }
//
//    /**
//     * @Description: 回显代理菜单功能
//     * @Author: Jerry
//     * @Date:10:01 2021/8/16
//     */
//     @ApiOperation(value = "回显代理菜单功能", notes = "链接上的id为代理id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/MSO/代理配置/回显代理菜单功能")
//    @PostMapping("selectSitemap/{id}")
//    public ResponseBo<AgentSiteMapVo> selectSitemap(@PathVariable("id") Long id) {
//        return new ListResponseBo(agentInfoService.selectSitemap(id));
//    }
//
//    @ApiOperation(value = "新增代理的大类推荐学校，新闻",notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER,type = LoggerOptTypeConst.ADD,description = "配置代理的大类推荐学校，新闻")
//    @PostMapping("configCourseGroupRecommendations")
//    public ResponseBo configCourseGroupRecommendations(@RequestBody @Validated(AgentCourseGroupRecommendDto.Add.class) AgentCourseGroupRecommendDto agentCourseGroupRecommendVo){
//        agentInfoService.configCourseGroupRecommendations(agentCourseGroupRecommendVo);
//        return SaveResponseBo.ok();
//    }
//
//    @ApiOperation(value = "代理推荐大类绑定的学校，新闻列表")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER,type = LoggerOptTypeConst.ADD,description = "代理推荐大类绑定的学校，新闻列表查询")
//    @PostMapping("courseGroupRecommendationsList")
//    public ListResponseBo courseGroupRecommendationsList(@RequestBody SearchBean<AgentCourseGroupRecommendListDto> voSearchBean){
//          List<AgentCourseGroupRecommendVo> datas =  agentInfoService.getCourseGroupRecommendationsList(voSearchBean.getData(),voSearchBean);
//         Page page = BeanCopyUtils.objClone(voSearchBean,Page::new);
//
//        return new ListResponseBo<>(datas,page);
//    }
//
//    @ApiOperation(value = "删除代理推荐课程大类相关内容")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER,type = LoggerOptTypeConst.DELETE,description = "删除代理推荐课程大类相关内容")
//    @PostMapping("deleteCourseGroupRecommendations/{id}")
//    public ResponseBo deleteCourseGroupRecommendations(@PathVariable("id") Long id ){
//        agentInfoService.deleteCourseGroupRecommendations(id);
//        return ResponseBo.ok();
//    }
//
//
//
//    @ApiOperation(value = "回显课程大类相关的推荐新闻，学校",notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER,type = LoggerOptTypeConst.LIST,description = "业务平台配置中心/MSO/代理配置/回显课程大类相关的推荐新闻，学校")
//    @GetMapping("selectCourseGroupRecommendations/{id}")
//    public ResponseBo<AgentCourseGroupRecommendVo> selectCourseGroupRecommendations(@PathVariable("id") Long id){
//        return new ResponseBo(agentInfoService.selectCourseGroupRecommendations(id));
//    }


}
