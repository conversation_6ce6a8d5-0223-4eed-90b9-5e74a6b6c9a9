package com.get.platformconfigcenter.service.impl;

import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;
import com.get.platformconfigcenter.dao.appissue.AplOrderMapper;
import com.get.platformconfigcenter.service.AplOrderService;
import com.get.platformconfigcenter.vo.AplOrderVo;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2021/11/24
 * @TIME: 14:42
 * @Description:
 **/
@Service
public class AplOrderServiceImpl implements AplOrderService {
//    @Resource
//    private AplOrderMapper aplOrderMapper;
//
//    @Override
//    public String getRobotStatusById(Long orderId) {
//        if (GeneralTool.isEmpty(orderId)) {
//            throw new GetServiceException("order_id_null");
//        }
//        return aplOrderMapper.getRobotStatusById(orderId);
//    }
//
//    @Override
//    public Map<Long, String> getRobotStatusByIds(Set<Long> orderIds) {
//        Map<Long, String> map = new HashMap<>();
//        if (GeneralTool.isEmpty(orderIds)) {
//            return map;
//        }
//        List<AplOrderVo> robotStatusByIds = aplOrderMapper.getRobotStatusByIds(orderIds);
//        if (GeneralTool.isNotEmpty(robotStatusByIds)) {
//            for (AplOrderVo robotStatusById : robotStatusByIds) {
//                map.put(robotStatusById.getOrderId(), robotStatusById.getRobotStatus());
//            }
//        }
//        return map;
//    }
//    @Override
//    public Map<Long, AplOrderVo> getRobotStatusAndMsgByIds(Set<Long> orderIds) {
//        Map<Long, AplOrderVo> map = new HashMap<>();
//        if (GeneralTool.isEmpty(orderIds)) {
//            return map;
//        }
//        List<AplOrderVo> robotStatusByIds = aplOrderMapper.getRobotStatusByIds(orderIds);
//        if (GeneralTool.isNotEmpty(robotStatusByIds)) {
//            for (AplOrderVo robotStatusById : robotStatusByIds) {
//                map.put(robotStatusById.getOrderId(), robotStatusById);
//            }
//        }
//        return map;
//    }
}
