package com.get.platformconfigcenter.service.impl;

import com.get.common.eunms.ProjectExtraEnum;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.platformconfigcenter.dao.appmso.*;
import com.get.platformconfigcenter.vo.*;
import com.get.platformconfigcenter.service.AgentInfoService;
import com.get.platformconfigcenter.service.DeleteService;
import com.get.platformconfigcenter.service.IPrivacyPolicyService;
import com.get.platformconfigcenter.service.MediaAndAttachedMsoService;
import com.get.salecenter.feign.ISaleCenterClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 代理配置逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/5/13 9:53
 */
@Service
public class AgentInfoServiceImpl implements AgentInfoService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
//    @Resource
//    private AgentInfoMapper agentInfoMapper;
//    @Resource
//    private AgentModuleInfoMapper agentModuleInfoMapper;
//    @Resource
//    private AgentRecommendMapper agentRecommendMapper;
    @Resource
    private AgentInfoMapper agentInfoMapper;
    @Resource
    private AgentModuleInfoMapper agentModuleInfoMapper;
    @Resource
    private AgentRecommendMapper agentRecommendMapper;
    @Resource
    private MediaAndAttachedMsoService mediaAndAttachedMsoService;
    @Resource
    private ISaleCenterClient saleCenterClient;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private UtilService utilService;
    @Resource
    private DeleteService deleteService;
    @Resource
    private IPrivacyPolicyService iPrivacyPolicyService;
//    @Resource
//    private AgentSiteMapMapper agentSiteMapMapper;
//    @Resource
//    private TranslationMapper translationMapper;

//    @Resource
//    private AgentCourseGroupRecommendMapper agentCourseGroupRecommendMapper;

    /**
     * @Description:查询代理配置列表
     * @Param
     * @Date 18:54 2021/5/13
     * <AUTHOR>
     */
//    @Override
//    public List<AgentInfoVo> getAgentInfoList(AgentInfoDto agentInfoVo, Page page) {
////        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
////        Example example = new Example(AgentInfo.class);
////        Example.Criteria criteria = example.createCriteria();
////        if (GeneralTool.isNotEmpty(agentInfoVo.getFkAgentName())) {
////            List<Long> agentIds = feignSaleService.getAgentIds(agentInfoVo.getFkAgentName());
////            if (GeneralTool.isEmpty(agentIds)) {
////                agentIds.add(0L);
////            }
////            criteria.andIn("fkAgentId", agentIds);
////        }
////        if (GeneralTool.isNotEmpty(agentInfoVo.getPublicLevel())) {
////            criteria.andLike("publicLevel", "%" + agentInfoVo.getPublicLevel() + "%");
////        }
////        List<AgentInfo> agentInfos = agentInfoMapper.selectByExample(example);
//
//        LambdaQueryWrapper<AgentInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        if (GeneralTool.isNotEmpty(agentInfoVo.getFkAgentName())) {
//            //111111
//            Result<List<Long>> result = saleCenterClient.getAgentIds(agentInfoVo.getFkAgentName());
//            if (result.isSuccess()) {
//                List<Long> agentIds = result.getData();
//                if (GeneralTool.isEmpty(agentIds)) {
//                    agentIds.add(0L);
//                }
//                lambdaQueryWrapper.in(AgentInfo::getFkAgentId, agentIds);
//            }
//        }
//        if (GeneralTool.isNotEmpty(agentInfoVo.getPublicLevel())) {
//            lambdaQueryWrapper.like(AgentInfo::getPublicLevel, agentInfoVo.getPublicLevel());
//        }
//        IPage<AgentInfo> pages = agentInfoMapper.selectPage(GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount())), lambdaQueryWrapper);
//        List<AgentInfo> agentInfos = pages.getRecords();
//        page.setAll((int) pages.getTotal());
//
//        Map<Long, String> agentNamesMap = new HashMap<>();
//        Result<Map<Long, String>> agentNamesMapResult = saleCenterClient.getAgentNamesByIds(agentInfos.stream().map(AgentInfo::getFkAgentId).filter(Objects::nonNull).collect(Collectors.toSet()));
//        if (agentNamesMapResult.isSuccess() && GeneralTool.isNotEmpty(agentNamesMapResult.getData())) {
//            agentNamesMap = agentNamesMapResult.getData();
//        }
//        logger.info("agentNamesMap返回值：{}", JSONObject.toJSONString(agentNamesMapResult));
//        List<AgentInfoVo> agentInfoDtoArrayList = new ArrayList<>();
//        for (AgentInfo agentInfo : agentInfos) {
//            AgentInfoVo agentInfoDto = BeanCopyUtils.objClone(agentInfo, AgentInfoVo::new);
//            agentInfoDto.setFkAgentName(agentNamesMap.get(agentInfo.getFkAgentId()));
//            agentInfoDto.setFkTableName(TableEnum.AGENT_INFO.key);
//            //设置公开对象名称
//            setPublicLevelName(agentInfoDto);
//            agentInfoDtoArrayList.add(agentInfoDto);
//        }
//        return agentInfoDtoArrayList;
//    }

    /**
     * @Description:新增代理配置
     * @Param
     * @Date 10:15 2021/5/13
     * <AUTHOR>
     */
//    @Override
//    public Long addAgentInfo(AgentInfoDto agentInfoVo) {
//        if (GeneralTool.isEmpty(agentInfoVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
////        Example example = new Example(AgentInfo.class);
////        example.createCriteria().andEqualTo("fkAgentId", agentInfoVo.getFkAgentId());
////        List<AgentInfo> agentInfoList = agentInfoMapper.selectByExample(example);
//        List<AgentInfo> agentInfoList = agentInfoMapper.selectList(Wrappers.<AgentInfo>query().lambda().eq(AgentInfo::getFkAgentId, agentInfoVo.getFkAgentId()));
//        if (GeneralTool.isNotEmpty(agentInfoList)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("AGENT_INFO_EXITS"));
//        }
//        //唯一keycode
//        AgentInfo agentInfo = BeanCopyUtils.objClone(agentInfoVo, AgentInfo::new);
////        example.createCriteria().andEqualTo("keyCode", agentInfo.getKeyCode());
////        List<AgentInfo> agentInfos = agentInfoMapper.selectByExample(example);
//
//        List<AgentInfo> agentInfos = agentInfoMapper.selectList(Wrappers.<AgentInfo>query().lambda().eq(AgentInfo::getKeyCode, agentInfo.getKeyCode()));
//        if (GeneralTool.isNotEmpty(agentInfos)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("KEY_CODE_INFO_EXITS"));
//        }
//        utilService.updateUserInfoToEntity(agentInfo);
//        agentInfoMapper.insert(agentInfo);
//        //保存图片
//        saveMediaAndAttached(agentInfoVo.getTopLogoMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_TOP_LOGO.key, TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//        saveMediaAndAttached(agentInfoVo.getBottomLogoMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_BOTTOM_LOGO.key, TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//        //资讯轮播图
//        saveMediaAndAttached(agentInfoVo.getNewsBannerMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_NEWS_BANNER.key, TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//        saveMediaAndAttached(agentInfoVo.getMobileNewsBannerMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_MOBILE_NEWS_BANNER.key, TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//        saveMediaAndAttached(agentInfoVo.getPadNewsBannerMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_PAD_NEWS_BANNER.key, TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//
//        //DSE广告轮播图
//        saveMediaAndAttached(agentInfoVo.getAdvertsBannerMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_DSE_ADVERTS_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        saveMediaAndAttached(agentInfoVo.getMobileDseAdvertsMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_MOBILE_DSE_ADVERTS_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        saveMediaAndAttached(agentInfoVo.getPadDseAdvertsMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_PAD_DSE_ADVERTS_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        //DSE轮播图
//        saveMediaAndAttached(agentInfoVo.getMobileDseMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_MOBILE_DSE_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        saveMediaAndAttached(agentInfoVo.getDseBannerMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_DSE_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        saveMediaAndAttached(agentInfoVo.getPadDseMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_DSE_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//
//        //首页轮播图
//        saveMediaAndAttached(agentInfoVo.getIndexBannerMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_INDEX_BANNER.key, TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//        saveMediaAndAttached(agentInfoVo.getMobileIndexMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_MOBILE_INDEX_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        saveMediaAndAttached(agentInfoVo.getPadIndexMediaAttachedVos(),FileTypeEnum.PLATFORM_MSO_AGENT_PAD_INDEX_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        //插入推荐学校、dse推荐学校、dse推荐新闻、首页推荐新闻
//        insertRecommendInfo(agentInfoVo, agentInfo);
//        return agentInfo.getId();
//    }

    /**
     * 插入代理推荐信息
     *
     * @Date 12:06 2021/7/28
     * <AUTHOR>
     */
//    private void insertAgentRecommend(List<Long> ids, Long fkAgentId, String targetType) {
//        for (Long id : ids) {
//            AgentRecommend agentRecommend = new AgentRecommend();
//            agentRecommend.setFkAgentId(fkAgentId);
//            agentRecommend.setTargetType(targetType);
//            agentRecommend.setTargetId(id);
//            agentRecommend.setViewOrder(agentRecommendMapper.getMaxViewOrder());
//            utilService.updateUserInfoToEntity(agentRecommend);
//            agentRecommendMapper.insert(agentRecommend);
//        }
//    }

    /**
     * @Description:更新代理配置
     * @Param
     * @Date 16:29 2021/5/13
     * <AUTHOR>
     */
//    @Override
//    public AgentInfoVo updateAgentInfo(AgentInfoDto agentInfoVo) {
//        if (GeneralTool.isEmpty(agentInfoVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
////        Example example = new Example(AgentInfo.class);
////        example.createCriteria().andEqualTo("fkAgentId", agentInfoVo.getFkAgentId()).andNotEqualTo("id", agentInfoVo.getId());
////        List<AgentInfo> agentInfoList = agentInfoMapper.selectByExample(example);
//        List<AgentInfo> agentInfoList = agentInfoMapper.selectList(Wrappers.<AgentInfo>query().lambda()
//                .eq(AgentInfo::getFkAgentId, agentInfoVo.getFkAgentId())
//                .ne(AgentInfo::getId, agentInfoVo.getId()));
//
//        if (GeneralTool.isNotEmpty(agentInfoList)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("AGENT_INFO_EXITS"));
//        }
//        AgentInfo agentInfo = agentInfoMapper.selectById(agentInfoVo.getId());
//        if (GeneralTool.isEmpty(agentInfo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        //唯一keycode
//        agentInfo = BeanCopyUtils.objClone(agentInfoVo, AgentInfo::new);
////        example.createCriteria().andEqualTo("keyCode", agentInfo.getKeyCode());
////        List<AgentInfo> agentInfos = agentInfoMapper.selectByExample(example);
//        List<AgentInfo> agentInfos = agentInfoMapper.selectList(Wrappers.<AgentInfo>query().lambda()
//                .eq(AgentInfo::getKeyCode, agentInfo.getKeyCode()).ne(AgentInfo::getId,agentInfoVo.getId()));
//        if (GeneralTool.isNotEmpty(agentInfos)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("KEY_CODE_INFO_EXITS"));
//        }
//        utilService.updateUserInfoToEntity(agentInfo);
//        agentInfoMapper.updateByPrimaryKeySelective(agentInfo);
//        //删除旧图片
//        mediaAndAttachedMsoService.deleteMediaAndAttached(TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//        //保存图片
//        saveMediaAndAttached(agentInfoVo.getTopLogoMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_TOP_LOGO.key, TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//        saveMediaAndAttached(agentInfoVo.getBottomLogoMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_BOTTOM_LOGO.key, TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//        //资讯轮播图
//        saveMediaAndAttached(agentInfoVo.getNewsBannerMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_NEWS_BANNER.key, TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//        saveMediaAndAttached(agentInfoVo.getMobileNewsBannerMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_MOBILE_NEWS_BANNER.key, TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//        saveMediaAndAttached(agentInfoVo.getPadNewsBannerMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_PAD_NEWS_BANNER.key, TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//
//        //DSE广告轮播图
//        saveMediaAndAttached(agentInfoVo.getAdvertsBannerMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_DSE_ADVERTS_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        saveMediaAndAttached(agentInfoVo.getMobileDseAdvertsMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_MOBILE_DSE_ADVERTS_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        saveMediaAndAttached(agentInfoVo.getPadDseAdvertsMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_PAD_DSE_ADVERTS_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        //DSE轮播图
//        saveMediaAndAttached(agentInfoVo.getMobileDseMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_MOBILE_DSE_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        saveMediaAndAttached(agentInfoVo.getDseBannerMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_DSE_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        saveMediaAndAttached(agentInfoVo.getPadDseMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_PAD_DSE_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//
//        //首页轮播图
//        saveMediaAndAttached(agentInfoVo.getIndexBannerMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_INDEX_BANNER.key, TableEnum.AGENT_INFO.key, agentInfoVo.getId());
//        saveMediaAndAttached(agentInfoVo.getMobileIndexMediaAttachedVos(), FileTypeEnum.PLATFORM_MSO_AGENT_MOBILE_INDEX_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//        saveMediaAndAttached(agentInfoVo.getPadIndexMediaAttachedVos(),FileTypeEnum.PLATFORM_MSO_AGENT_PAD_INDEX_BANNER.key, TableEnum.AGENT_INFO.key, agentInfo.getId());
//
//        //删除旧推荐
////        example = new Example(AgentRecommend.class);
////        example.createCriteria().andEqualTo("fkAgentId", agentInfo.getFkAgentId());
////        agentRecommendMapper.deleteByExample(example);
//
//        agentRecommendMapper.delete(Wrappers.<AgentRecommend>query().lambda()
//                .eq(AgentRecommend::getFkAgentId, agentInfo.getFkAgentId()));
//
//        //插入推荐学校、dse推荐学校、dse推荐新闻
//        insertRecommendInfo(agentInfoVo, agentInfo);
//        return findAgentInfoById(agentInfo.getId());
//    }

    /**
     * 插入推荐学校、dse推荐学校、dse推荐新闻
     *
     * @Date 12:14 2021/7/28
     * <AUTHOR>
     */
//    private void insertRecommendInfo(AgentInfoDto agentInfoVo, AgentInfo agentInfo) {
//        //推荐学校
//        List<Long> institutionIds = agentInfoVo.getInstitutionIdList();
//        Long fkAgentId = agentInfo.getFkAgentId();
//        if (GeneralTool.isNotEmpty(institutionIds)) {
//            insertAgentRecommend(institutionIds, fkAgentId, ProjectKeyEnum.INDEX_INSTITUTION.key);
//        }
//        //DSE推荐学校
//        List<Long> dseInstitutionIdList = agentInfoVo.getDseInstitutionIdList();
//        if (GeneralTool.isNotEmpty(dseInstitutionIdList)) {
//            insertAgentRecommend(dseInstitutionIdList, fkAgentId, ProjectKeyEnum.DSE_INSTITUTION.key);
//        }
//        //DSE推荐新闻
//        List<Long> dseNewsIdList = agentInfoVo.getDseNewsIdList();
//        if (GeneralTool.isNotEmpty(dseNewsIdList)) {
//            insertAgentRecommend(dseNewsIdList, fkAgentId, ProjectKeyEnum.DSE_NEWS.key);
//        }
//        //首页推荐新闻
//        List<Long> indexNewsIdList = agentInfoVo.getIndexNewsIdList();
//        if (GeneralTool.isNotEmpty(indexNewsIdList)) {
//            insertAgentRecommend(indexNewsIdList, fkAgentId, ProjectKeyEnum.INDEX_NEWS.key);
//        }
//        //网站推荐新闻
//        List<Long> websiteNewsIdList = agentInfoVo.getWebsiteNewsIdList();
//        if (GeneralTool.isNotEmpty(websiteNewsIdList)) {
//            insertAgentRecommend(websiteNewsIdList, fkAgentId, ProjectKeyEnum.WEBSITE_NEWS.key);
//        }
//        //推荐桥梁学校
//        List<AgentInfoDto.BridgeInstitutionVo> bridgeInstitutionVoList = agentInfoVo.getBridgeInstitutionVo();
//        if (GeneralTool.isNotEmpty(bridgeInstitutionVoList)){
//            for (AgentInfoDto.BridgeInstitutionVo bridgeInstitutionVo : bridgeInstitutionVoList) {
//                Long bridgeInstitutionId = bridgeInstitutionVo.getBridgeInstitutionId();
//                if (GeneralTool.isNotEmpty(bridgeInstitutionId)){
//                    AgentRecommend agentRecommend = new AgentRecommend();
//                    agentRecommend.setFkAgentId(fkAgentId);
//                    agentRecommend.setTargetType(ProjectKeyEnum.BRIDGE_INSTITUTION.key);
//                    agentRecommend.setTargetId(bridgeInstitutionId);
//                    if (GeneralTool.isNotEmpty(bridgeInstitutionVo.getVerticalAscentInstitutionIdList())){
//                        String verticalAscentInstitutions = StringUtils.join(bridgeInstitutionVo.getVerticalAscentInstitutionIdList(), ",");
//                        agentRecommend.setTargetValue(verticalAscentInstitutions);
//                    }
//                    agentRecommend.setViewOrder(agentRecommendMapper.getMaxViewOrder());
//                    utilService.updateUserInfoToEntity(agentRecommend);
//                    agentRecommendMapper.insert(agentRecommend);
//                }
//            }
//        }
//
//    }

    /**
     * @Description:代理配置详情
     * @Param
     * @Date 12:52 2021/5/13
     * <AUTHOR>
     */
//    @Override
//    public AgentInfoVo findAgentInfoById(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        AgentInfo agentInfo = agentInfoMapper.selectById(id);
//        if (GeneralTool.isEmpty(agentInfo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        AgentInfoVo agentInfoDto = BeanCopyUtils.objClone(agentInfo, AgentInfoVo::new);
//        //111111
////        agentInfoDto.setFkAgentName(feignSaleService.getAgentNameById(agentInfo.getFkAgentId()));
//        //隐私条例
//        if (GeneralTool.isNotEmpty(agentInfo.getFkPrivacyPolicyId())) {
//            Set<Long> idSet = new HashSet<>();
//            idSet.add(agentInfo.getFkPrivacyPolicyId());
//            Map<Long, String> privacyPolicyTitlesByIds = iPrivacyPolicyService.getPrivacyPolicyTitlesByIds(idSet);
//            agentInfoDto.setFkPrivacyPolicyName(privacyPolicyTitlesByIds.get(agentInfo.getFkPrivacyPolicyId()));
//        }
//        agentInfoDto.setTopLogoMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_TOP_LOGO.key));
//        agentInfoDto.setBottomLogoMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_BOTTOM_LOGO.key));
//
//        agentInfoDto.setIndexBannerMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_INDEX_BANNER.key));
//        agentInfoDto.setPadIndexMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_PAD_INDEX_BANNER.key));
//        agentInfoDto.setMobileIndexMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_MOBILE_INDEX_BANNER.key));
//
//        agentInfoDto.setNewsBannerMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_NEWS_BANNER.key));
//        agentInfoDto.setMobileNewsBannerMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_MOBILE_NEWS_BANNER.key));
//        agentInfoDto.setPadNewsBannerMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_PAD_NEWS_BANNER.key));
//
//        agentInfoDto.setAdvertsBannerMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_DSE_ADVERTS_BANNER.key));
//        agentInfoDto.setMobileDseAdvertsMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_MOBILE_DSE_ADVERTS_BANNER.key));
//        agentInfoDto.setPadDseAdvertsMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_PAD_DSE_ADVERTS_BANNER.key));
//
//        agentInfoDto.setDseBannerMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_DSE_BANNER.key));
//        agentInfoDto.setMobileDseMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_MOBILE_DSE_BANNER.key));
//        agentInfoDto.setPadDseMediaAttachedVos(getIconImg(agentInfo.getId(), FileTypeEnum.PLATFORM_MSO_AGENT_PAD_DSE_BANNER.key));
//
//        if (agentInfoDto.getFkAgentId()!=null) {
//            Result<String> agentName = saleCenterClient.getAgentNameById(agentInfoDto.getFkAgentId());
//            if (agentName.isSuccess() && GeneralTool.isNotEmpty(agentName.getData())) {
//                agentInfoDto.setFkAgentName(agentName.getData());
//            }
//        }
//
//
//
//
//
//        agentInfoDto.setFkTableName(TableEnum.AGENT_INFO.key);
//
//        //查询推荐信息
////        Example example = new Example(AgentRecommend.class);
////        example.createCriteria().andEqualTo("fkAgentId", agentInfo.getFkAgentId());
////        List<AgentRecommend> agentRecommends = agentRecommendMapper.selectByExample(example);
//        List<AgentRecommend> agentRecommends = agentRecommendMapper.selectList(Wrappers.<AgentRecommend>query().lambda()
//                .eq(AgentRecommend::getFkAgentId, agentInfo.getFkAgentId()));
//        if (GeneralTool.isNotEmpty(agentRecommends)) {
//            //首页推荐学校信息
//            Set<Long> institutionIdSet = agentRecommends.stream().filter(agentRecommend -> agentRecommend.getTargetType().equals(ProjectKeyEnum.INDEX_INSTITUTION.key))
//                    .map(AgentRecommend::getTargetId).collect(Collectors.toSet());
//            if (GeneralTool.isNotEmpty(institutionIdSet)) {
//                agentInfoDto.setInstitutionIdList(new ArrayList<>(institutionIdSet));
//                //111111
//                Result<Map<Long, String>> resultinstitutionNameMap = institutionCenterClient.getInstitutionNamesByIds(institutionIdSet);
//                Map<Long, String> institutionNameMap = null;
//                if (resultinstitutionNameMap.isSuccess()) {
//                    institutionNameMap = resultinstitutionNameMap.getData();
//                }
//                Collection<String> institutionNames = institutionNameMap.values();
//                String institutionNameStr = StringUtils.join(institutionNames, "，");
//                agentInfoDto.setInstitutionNameStr(institutionNameStr);
//            }
//            //DSE推荐学校
//            Set<Long> dseInstitutionIdSet = agentRecommends.stream().filter(agentRecommend -> agentRecommend.getTargetType().equals(ProjectKeyEnum.DSE_INSTITUTION.key))
//                    .map(AgentRecommend::getTargetId).collect(Collectors.toSet());
//            if (GeneralTool.isNotEmpty(dseInstitutionIdSet)) {
//                agentInfoDto.setDseInstitutionIdList(new ArrayList<>(dseInstitutionIdSet));
//                //111111
//                Result<Map<Long, String>> resultinstitutionNameMap = institutionCenterClient.getInstitutionNamesByIds(dseInstitutionIdSet);
//                Map<Long, String> institutionNameMap = new HashMap<>();
//                if (resultinstitutionNameMap.isSuccess()) {
//                    institutionNameMap = resultinstitutionNameMap.getData();
//                }
//                Collection<String> institutionNames = institutionNameMap.values();
//                String institutionNameStr = StringUtils.join(institutionNames, "，");
//                agentInfoDto.setDseInstitutionNameStr(institutionNameStr);
//            }
//            //DSE推荐新闻
//            Set<Long> dseNewsIdSet = agentRecommends.stream().filter(agentRecommend -> agentRecommend.getTargetType().equals(ProjectKeyEnum.DSE_NEWS.key))
//                    .map(AgentRecommend::getTargetId).collect(Collectors.toSet());
//            if (GeneralTool.isNotEmpty(dseNewsIdSet)) {
//                agentInfoDto.setDseNewsIdList(new ArrayList<>(dseNewsIdSet));
//                //111111
//                Result<Map<Long, String>> resultnewsTitles = institutionCenterClient.getNewsTitlesByIds(dseNewsIdSet);
//                Map<Long, String> newsTitles = new HashMap<>();
//                if (resultnewsTitles.isSuccess()) {
//                    newsTitles = resultnewsTitles.getData();
//                }
//                Collection<String> titles = newsTitles.values();
//                String titleStr = StringUtils.join(titles, "，");
//                agentInfoDto.setDseNewsNameStr(titleStr);
//            }
//            //网站推荐新闻
//            Set<Long> websiteNewsIds =agentRecommends.stream().filter(agentRecommend -> agentRecommend.getTargetType().equals(ProjectKeyEnum.WEBSITE_NEWS.key))
//                    .map(AgentRecommend::getTargetId).collect(Collectors.toSet());
//            if (GeneralTool.isNotEmpty(websiteNewsIds)) {
//                agentInfoDto.setWebsiteNewsIdList(new ArrayList<>(websiteNewsIds));
//                Result<Map<Long, String>> resultnewsTitles = institutionCenterClient.getNewsTitlesByIds(websiteNewsIds);
//                Map<Long, String> newsTitles = new HashMap<>();
//                if (resultnewsTitles.isSuccess()) {
//                    newsTitles = resultnewsTitles.getData();
//                }
//                Collection<String> titles = newsTitles.values();
//                String titleStr = StringUtils.join(titles, "，");
//                agentInfoDto.setWebsiteNewsNameStr(titleStr);
//            }
//            //首页推荐新闻
//            Set<Long> indexNewsIdSet = agentRecommends.stream().filter(agentRecommend -> agentRecommend.getTargetType().equals(ProjectKeyEnum.INDEX_NEWS.key))
//                    .map(AgentRecommend::getTargetId).collect(Collectors.toSet());
//            if (GeneralTool.isNotEmpty(indexNewsIdSet)) {
//                agentInfoDto.setIndexNewsIdList(new ArrayList<>(indexNewsIdSet));
//                //111111
//                Result<Map<Long, String>> resultindexNewsTitlesMap = institutionCenterClient.getNewsTitlesByIds(indexNewsIdSet);
//                Map<Long, String> indexNewsTitlesMap = new HashMap<>();
//                if (resultindexNewsTitlesMap.isSuccess()) {
//                    indexNewsTitlesMap = resultindexNewsTitlesMap.getData();
//                }
//                Collection<String> indexNewsTitles = indexNewsTitlesMap.values();
//                String indexNewsTitleStr = StringUtils.join(indexNewsTitles, "，");
//                agentInfoDto.setIndexNewsNameStr(indexNewsTitleStr);
//            }
//            //推荐桥梁学校
//            Set<Long> bridgeInstitutionIdSet = agentRecommends.stream().filter(agentRecommend -> agentRecommend.getTargetType().equals(ProjectKeyEnum.BRIDGE_INSTITUTION.key))
//                    .map(AgentRecommend::getTargetId).collect(Collectors.toSet());
//            if (GeneralTool.isNotEmpty(bridgeInstitutionIdSet)) {
//                List<BridgeInstitutionVo> bridgeInstitutionDtoList = new ArrayList<>();
//                for (Long bridgeInstitutionId : bridgeInstitutionIdSet) {
//                    BridgeInstitutionVo bridgeInstitutionDto = new BridgeInstitutionVo();
//                    bridgeInstitutionDto.setBridgeInstitutionId(bridgeInstitutionId);
//                    Set<Long> bridgeIds = new HashSet<>();
//                    bridgeIds.add(bridgeInstitutionId);
//                    Result<Map<Long, String>> resultinstitutionNameMap = institutionCenterClient.getInstitutionNamesByIds(bridgeIds);
//                    Map<Long, String> bridgeInstitutionNameMap = null;
//                    if (resultinstitutionNameMap.isSuccess()) {
//                        bridgeInstitutionNameMap = resultinstitutionNameMap.getData();
//                    }
//                    Collection<String> institutionNames = bridgeInstitutionNameMap.values();
//                    String institutionNameStr = StringUtils.join(institutionNames, "，");
//                    bridgeInstitutionDto.setBridgeInstitutionStr(institutionNameStr);
//                    //直升学校
//                    List<String> verticalAscentInstitutionList = agentRecommends.stream().filter(agentRecommend -> agentRecommend.getTargetType().equals(ProjectKeyEnum.BRIDGE_INSTITUTION.key) && agentRecommend.getTargetId().equals(bridgeInstitutionId))
//                            .map(AgentRecommend::getTargetValue).collect(Collectors.toList());
//                    verticalAscentInstitutionList.removeIf(Objects::isNull);
//                    if (GeneralTool.isNotEmpty(verticalAscentInstitutionList)){
//                        List<String> verticalAscentInstitution = Arrays.asList(verticalAscentInstitutionList.get(0).split(","));
//                        Set<Long> ids = verticalAscentInstitution.stream().map(i -> Long.valueOf(i)).collect(Collectors.toSet());
//                        bridgeInstitutionDto.setVerticalAscentInstitutionIdList(new ArrayList<>(ids));
//                        Result<Map<Long, String>> verticalAscentInstitutionMap = institutionCenterClient.getInstitutionNamesByIds(ids);
//                        Map<Long, String> verticalAsceMap = new HashMap<>();
//                        if (verticalAscentInstitutionMap.isSuccess()) {
//                            verticalAsceMap = verticalAscentInstitutionMap.getData();
//                        }
//                        Collection<String> verticalAsce = verticalAsceMap.values();
//                        String verticalAsceStr = StringUtils.join(verticalAsce, "，");
//                        bridgeInstitutionDto.setVerticalAscentInstitutionIdStr(verticalAsceStr);
//                    }
//                    bridgeInstitutionDtoList.add(bridgeInstitutionDto);
//                }
//                agentInfoDto.setBridgeInstitutionDtoList(bridgeInstitutionDtoList);
//
//
//            }
//
//
//        }
//        if (GeneralTool.isNotEmpty(agentInfo.getPublicLevel())) {
//            StringJoiner str = new StringJoiner(",");
//            String[] split = agentInfo.getPublicLevel().split(",");
//            for (String pubLevelStr : split) {
//                str.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(pubLevelStr), ProjectExtraEnum.PUBLIC_OBJECTS));
//            }
//            agentInfoDto.setPublicLevelName(str.toString());
//        }
//        return agentInfoDto;
//    }

    /**
     * @Description:删除代理配置
     * @Param
     * @Date 16:56 2021/5/13
     * <AUTHOR>
     */
//    @Override
//    public void delete(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        boolean succuee = deleteService.deleteValidateAgentInfo(id);
//        if (succuee) {
//            agentInfoMapper.deleteById(id);
//        }
//
//        //删除翻译内容
//        translationMapper.deleteTranslations(TableEnum.AGENT_INFO.key, id);
//    }

    /**
     * @Description: 分配siteMap
     * @Author: Jerry
     * @Date:18:28 2021/8/13
     */
//    @Override
//    public void allocationSitemap(Long id, List<String> sitemapVoIds) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        //先删除原来的数据
////        Example example = new Example(AgentSiteMap.class);
////        example.createCriteria().andEqualTo("fkAgentId",id);
////        agentSiteMapMapper.deleteByExample(example);
//
//        agentSiteMapMapper.delete(Wrappers.<AgentSiteMap>query().lambda()
//                .eq(AgentSiteMap::getFkAgentId, id));
//
//        //新增新数据
//        if (GeneralTool.isNotEmpty(sitemapVoIds)) {
//            for (String sitemapVoId : sitemapVoIds) {
//                AgentSiteMap agentSiteMap = new AgentSiteMap();
//                agentSiteMap.setFkAgentId(id);
//                agentSiteMap.setFkMenuKey(sitemapVoId);
//                utilService.updateUserInfoToEntity(agentSiteMap);
//                agentSiteMapMapper.insert(agentSiteMap);
//            }
//        }
//    }

    /**
     * @Description: 回显代理菜单功能
     * @Author: Jerry
     * @Date:10:04 2021/8/16
     */
   /* @Override
    public List<AgentSiteMapVo> selectSitemap(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
//        Example example = new Example(AgentSiteMap.class);
//        example.createCriteria().andEqualTo("fkAgentId",id);
//        List<AgentSiteMap> agentSiteMaps = agentSiteMapMapper.selectByExample(example);

        List<AgentSiteMap> agentSiteMaps = agentSiteMapMapper.selectList(Wrappers.<AgentSiteMap>query().lambda()
                .eq(AgentSiteMap::getFkAgentId, id));

        if (GeneralTool.isEmpty(agentSiteMaps)) {
            return new ArrayList<>();
        }
        List<AgentSiteMapVo> agentSiteMapDtos = new ArrayList<>();
        for (AgentSiteMap agentSiteMap : agentSiteMaps) {
            AgentSiteMapVo agentSiteMapDto = BeanCopyUtils.objClone(agentSiteMap, AgentSiteMapVo::new);
            agentSiteMapDtos.add(agentSiteMapDto);
        }
        return agentSiteMapDtos;
    }*/

   /* @Override
    @Transactional(rollbackFor = Exception.class)
    public void configCourseGroupRecommendations(AgentCourseGroupRecommendDto agentCourseGroupRecommendVo) {
        if(GeneralTool.isEmpty(agentCourseGroupRecommendVo.getFkAgentId())){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        //清空数据
        agentCourseGroupRecommendMapper.delete(Wrappers.<AgentCourseGroupRecommend>query().lambda().
                eq(AgentCourseGroupRecommend::getFkAgentId, agentCourseGroupRecommendVo.getFkAgentId())
                .eq(AgentCourseGroupRecommend::getFkCourseTypeGroupId, agentCourseGroupRecommendVo.getFkCourseTypeGroupId())
                .and(wrapper -> wrapper.eq(AgentCourseGroupRecommend::getTargetType, ProjectKeyEnum.COURSE_GROUP_RECOMMEND_INSTITUTIONS.key)
                        .or().eq(AgentCourseGroupRecommend::getTargetType, ProjectKeyEnum.COURSE_GROUP_RECOMMEND_NEWS.key)
                        .or().eq(AgentCourseGroupRecommend::getTargetType, ProjectKeyEnum.PROXY_EXAM_RECOMMENDATION.key)
                        .or().eq(AgentCourseGroupRecommend::getTargetType,ProjectKeyEnum.AGENCY_INFORMATION_RECOMMENDATION.key)
                        .or().eq(AgentCourseGroupRecommend::getTargetType,ProjectKeyEnum.INSTITUTION_COURSE.key)
                ));

        if (GeneralTool.isNotEmpty(agentCourseGroupRecommendVo.getInstitutionIdList())){
            //如果有重复的学校，则删除
//            agentCourseGroupRecommendMapper.delete(Wrappers.<AgentCourseGroupRecommend>query().lambda().
//                    eq(AgentCourseGroupRecommend::getFkAgentId,agentCourseGroupRecommendVo.getFkAgentId())
//                    .eq(AgentCourseGroupRecommend::getTargetType,ProjectKeyEnum.COURSE_GROUP_RECOMMEND_INSTITUTIONS.key)
//                    .eq(AgentCourseGroupRecommend::getFkCourseTypeGroupId,agentCourseGroupRecommendVo.getFkCourseTypeGroupId())
//                    .in(AgentCourseGroupRecommend::getTargetId,agentCourseGroupRecommendVo.getInstitutionIdList()));

            //添加推荐学校
            insertAgentCourseGroupRecommendations(agentCourseGroupRecommendVo.getFkAgentId(),
                    agentCourseGroupRecommendVo.getFkCourseTypeGroupId(),agentCourseGroupRecommendVo.getInstitutionIdList(),ProjectKeyEnum.COURSE_GROUP_RECOMMEND_INSTITUTIONS.key);
        }


        if (GeneralTool.isNotEmpty(agentCourseGroupRecommendVo.getNewsIdList())){
            //删除重复的新闻
//            agentCourseGroupRecommendMapper.delete(Wrappers.<AgentCourseGroupRecommend>query().lambda().
//                    eq(AgentCourseGroupRecommend::getFkAgentId,agentCourseGroupRecommendVo.getFkAgentId())
//                    .eq(AgentCourseGroupRecommend::getTargetType,ProjectKeyEnum.COURSE_GROUP_RECOMMEND_NEWS.key)
//                    .eq(AgentCourseGroupRecommend::getFkCourseTypeGroupId,agentCourseGroupRecommendVo.getFkCourseTypeGroupId())
//                    .in(AgentCourseGroupRecommend::getTargetId,agentCourseGroupRecommendVo.getNewsIdList())
//            );

            //推荐新闻
            insertAgentCourseGroupRecommendations(agentCourseGroupRecommendVo.getFkAgentId(),
                    agentCourseGroupRecommendVo.getFkCourseTypeGroupId(),agentCourseGroupRecommendVo.getNewsIdList(),ProjectKeyEnum.COURSE_GROUP_RECOMMEND_NEWS.key);

        }

        if (GeneralTool.isNotEmpty(agentCourseGroupRecommendVo.getCourseIdList())){
            //删除重复的新闻
//            if (GeneralTool.isNotEmpty(agentCourseGroupRecommendVo.getCourseIdList())){
//                agentCourseGroupRecommendMapper.delete(Wrappers.<AgentCourseGroupRecommend>query().lambda().
//                        eq(AgentCourseGroupRecommend::getFkAgentId,agentCourseGroupRecommendVo.getFkAgentId())
//                        .eq(AgentCourseGroupRecommend::getTargetType,ProjectKeyEnum.INSTITUTION_COURSE.key)
//                        .eq(AgentCourseGroupRecommend::getFkCourseTypeGroupId,agentCourseGroupRecommendVo.getFkCourseTypeGroupId())
//                        .in(AgentCourseGroupRecommend::getTargetId,agentCourseGroupRecommendVo.getCourseIdList())
//                );
//            }

            //推荐新闻
            insertAgentCourseGroupRecommendations(agentCourseGroupRecommendVo.getFkAgentId(),
                    agentCourseGroupRecommendVo.getFkCourseTypeGroupId(),agentCourseGroupRecommendVo.getCourseIdList(),ProjectKeyEnum.INSTITUTION_COURSE.key);
        }


        //考卷推荐
        if(GeneralTool.isNotEmpty(agentCourseGroupRecommendVo.getExamList())){
            insertAgentCourseGroupRecommendations(agentCourseGroupRecommendVo.getFkAgentId(),
                    agentCourseGroupRecommendVo.getFkCourseTypeGroupId(),agentCourseGroupRecommendVo.getExamList(),ProjectKeyEnum.PROXY_EXAM_RECOMMENDATION.key
            );
        }

        //咨询推荐
        if(GeneralTool.isNotEmpty(agentCourseGroupRecommendVo.getInformationList())){
            insertAgentCourseGroupRecommendations(agentCourseGroupRecommendVo.getFkAgentId(),
                    agentCourseGroupRecommendVo.getFkCourseTypeGroupId(),agentCourseGroupRecommendVo.getInformationList(),ProjectKeyEnum.AGENCY_INFORMATION_RECOMMENDATION.key
                    );
        }



    }*/

    /**
     * @param fkagentId
     * <AUTHOR>
     * @Description:
     * @Return java.util.Collection
     * @date 2022/11/15 17:52
     */
   /* @Override
    public AgentCourseGroupRecommendVo selectCourseGroupRecommendations(Long fkagentId) {
        //获取所有推荐表
        List<AgentCourseGroupRecommend> courseGroupIntRecommendList = agentCourseGroupRecommendMapper.selectList(Wrappers.<AgentCourseGroupRecommend>query().lambda()
                .eq(AgentCourseGroupRecommend::getFkAgentId,fkagentId)
        );

        AgentCourseGroupRecommendVo agentCourseGroupRecommendDto = new AgentCourseGroupRecommendVo();

//        if(GeneralTool.isNotEmpty(courseGroupIntRecommendList)){
//        //获取所有课程组
//        Set<Long> courseGroupIdList = courseGroupIntRecommendList.stream().map(AgentCourseGroupRecommend::getFkCourseTypeGroupId).collect(Collectors.toSet());
//        //获取所有学校类型
//       Set<Long> institutionIds = courseGroupIntRecommendList.stream().filter(agentCourseGroupRecommend -> agentCourseGroupRecommend.
//       getTargetType().equals(ProjectKeyEnum.COURSE_GROUP_RECOMMEND_INSTITUTIONS.key)
//        ).map(AgentCourseGroupRecommend::getTargetId).collect(Collectors.toSet());
//        //查询所有推荐新闻
//       Set<Long> newsIds = courseGroupIntRecommendList.stream().filter(agentCourseGroupRecommend -> agentCourseGroupRecommend.
//       getTargetType().equals(ProjectKeyEnum.COURSE_GROUP_RECOMMEND_NEWS.key))
//                .map(AgentCourseGroupRecommend::getTargetId ).collect(Collectors.toSet());
//
//
//       //学校学校名字
//        if(GeneralTool.isNotEmpty(institutionIds)){
//            agentCourseGroupRecommendDto.setInstitutionIdList(new ArrayList<>(institutionIds));
//            Result<Map<Long,String>> resultInstitutionNameMap = institutionCenterClient.getInstitutionNamesByIds(institutionIds);
//            Map<Long,String> institutionNameMap = new HashMap<>();
//            if(resultInstitutionNameMap.isSuccess()){
//                institutionNameMap = resultInstitutionNameMap.getData();
//            }
//            Collection<String> institutionNames = institutionNameMap.values();
//            String institutionNameStr = StringUtils.join(institutionNames,",");
//            agentCourseGroupRecommendDto.setInstitutionName(institutionNameStr);
//        }
//
//        //新闻信息
//        if(GeneralTool.isNotEmpty(newsIds)){
//            agentCourseGroupRecommendDto.setNewsIdList(new ArrayList<>(newsIds));
//          Result<Map<Long,String>> resultNeswNameMap = institutionCenterClient.getNewsTitlesByIds(newsIds);
//            Map<Long,String> newsTitles = new HashMap<>();
//           if(resultNeswNameMap.isSuccess()){
//               newsTitles =  resultNeswNameMap.getData();
//           }
//
//          Collection<String> titles = newsTitles.values();
//          String titleStr  = StringUtils.join(titles,",");
//          agentCourseGroupRecommendDto.setNewName(titleStr);
//        }
//
//        //课程组信息
//        if(GeneralTool.isNotEmpty(courseGroupIdList)){
//          Result<Map<Long,String>> courseGroupTypeNameMap =  institutionCenterClient.getCourseGroupTypeNameByIds(courseGroupIdList);
//            Map<Long,String> courseTypeName = new HashMap<>();
//          if(courseGroupTypeNameMap.isSuccess()){
//              courseTypeName =  courseGroupTypeNameMap.getData();
//          }
//            agentCourseGroupRecommendDto.setCourseGroupIdList((courseGroupIdList);
//          Collection<String> courseTypeNames = courseTypeName.values();
//          String courseTypeNameStr = StringUtils.join(courseTypeNames,",");
//          agentCourseGroupRecommendDto.setCourseGroupName(courseTypeNameStr);
//
//        }
//        }

        return agentCourseGroupRecommendDto;
    }*/

    /*@Override
    public List<AgentCourseGroupRecommendVo> getCourseGroupRecommendationsList(AgentCourseGroupRecommendListDto data, Page page) {

          IPage<AgentCourseGroupRecommend> iPage =  GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(),page.getShowCount()));
        List<AgentCourseGroupRecommend> agentCourseGroupRecommends =  agentCourseGroupRecommendMapper.getCourseGroupRecommendationsList(iPage,data);
           List<AgentCourseGroupRecommendVo> agentCourseGroupRecommendDtos = new ArrayList<>();

           for(AgentCourseGroupRecommend agentCourseGroupRecommend:agentCourseGroupRecommends){
             AgentCourseGroupRecommendVo agentCourseGroupRecommendDto =  BeanCopyUtils.objClone(agentCourseGroupRecommend,AgentCourseGroupRecommendVo::new);

               Result<Map<Long,String>> courseGroupTypeNameMap = institutionCenterClient.getCourseGroupTypeNameByIds(Collections.singleton(agentCourseGroupRecommendDto.getFkCourseTypeGroupId()));
               Map<Long,String> courseTypeName = new HashMap<>();
               if(courseGroupTypeNameMap.isSuccess()){
                   courseTypeName =  courseGroupTypeNameMap.getData();
               }
               agentCourseGroupRecommendDto.setCourseGroupName(courseTypeName.get(agentCourseGroupRecommendDto.getFkCourseTypeGroupId()));

               LambdaQueryWrapper<AgentCourseGroupRecommend> wrapper = new LambdaQueryWrapper<>();
               wrapper.eq(AgentCourseGroupRecommend::getFkAgentId,agentCourseGroupRecommendDto.getFkAgentId())
                       .eq(AgentCourseGroupRecommend::getFkCourseTypeGroupId,agentCourseGroupRecommendDto.getFkCourseTypeGroupId());
               List<AgentCourseGroupRecommend> agentCourseGroupRecommendsInfos =  agentCourseGroupRecommendMapper.selectList(wrapper);

             Set<Long> insMaps =  agentCourseGroupRecommendsInfos.stream().filter(agentCourseGroupRecommendsInfo ->
                       ProjectKeyEnum.COURSE_GROUP_RECOMMEND_INSTITUTIONS.key.equals(agentCourseGroupRecommendsInfo.getTargetType())).
                     map(AgentCourseGroupRecommend::getTargetId).collect(Collectors.toSet());

               Set<Long> newsMaps =  agentCourseGroupRecommendsInfos.stream().filter(agentCourseGroupRecommendsInfo ->
                               ProjectKeyEnum.COURSE_GROUP_RECOMMEND_NEWS.key.equals(agentCourseGroupRecommendsInfo.getTargetType())).
                       map(AgentCourseGroupRecommend::getTargetId).collect(Collectors.toSet());

               Set<Long> courseMaps =  agentCourseGroupRecommendsInfos.stream().filter(agentCourseGroupRecommendsInfo ->
                               ProjectKeyEnum.INSTITUTION_COURSE.key.equals(agentCourseGroupRecommendsInfo.getTargetType())).
                       map(AgentCourseGroupRecommend::getTargetId).collect(Collectors.toSet());

               Set<Long> examMaps = agentCourseGroupRecommendsInfos.stream().filter(agentCourseGroupRecommendsInfo ->
                       ProjectKeyEnum.PROXY_EXAM_RECOMMENDATION.key.equals(agentCourseGroupRecommendsInfo.getTargetType())).
                        map(AgentCourseGroupRecommend::getTargetId).collect(Collectors.toSet());

               Set<Long>  informationMaps = agentCourseGroupRecommendsInfos.stream().filter(agentCourseGroupRecommendsInfo ->
                               ProjectKeyEnum.AGENCY_INFORMATION_RECOMMENDATION.key.equals(agentCourseGroupRecommendsInfo.getTargetType())).
                                map(AgentCourseGroupRecommend::getTargetId).collect(Collectors.toSet());

               agentCourseGroupRecommendDto.setInstitutionIdList(insMaps);

               agentCourseGroupRecommendDto.setNewsIdList(newsMaps);

               agentCourseGroupRecommendDto.setCourseIdList(courseMaps);

               agentCourseGroupRecommendDto.setExamList(examMaps);

               agentCourseGroupRecommendDto.setInformationList(informationMaps);

               agentCourseGroupRecommendDtos.add(agentCourseGroupRecommendDto);

           }

         page.setAll((int) iPage.getTotal());

        return agentCourseGroupRecommendDtos;
    }*/

    /*@Override
    public void deleteCourseGroupRecommendations(Long id) {
        if(GeneralTool.isEmpty(id) ){
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }

        LambdaQueryWrapper<AgentCourseGroupRecommend> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AgentCourseGroupRecommend::getId,id);
        AgentCourseGroupRecommend agentCourseGroupRecommend =  agentCourseGroupRecommendMapper.selectByPrimaryKey(id);
        if(GeneralTool.isEmpty(agentCourseGroupRecommend)){
            throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
        }
        //如果有重复的学校，则删除
        agentCourseGroupRecommendMapper.delete(Wrappers.<AgentCourseGroupRecommend>query().lambda().
                eq(AgentCourseGroupRecommend::getFkAgentId,agentCourseGroupRecommend.getFkAgentId())
                .eq(AgentCourseGroupRecommend::getFkCourseTypeGroupId,agentCourseGroupRecommend.getFkCourseTypeGroupId()));
    }

    private void  insertAgentCourseGroupRecommendations(Long fkAgent, Long courseGroupIds, List<Long> targetIds, String targetType){
            for(Long targetId : targetIds){
                AgentCourseGroupRecommend agentCourseGroupRecommend = new AgentCourseGroupRecommend();
                agentCourseGroupRecommend.setFkAgentId(fkAgent);
                agentCourseGroupRecommend.setFkCourseTypeGroupId(courseGroupIds);
                agentCourseGroupRecommend.setTargetId(targetId);
                agentCourseGroupRecommend.setViewOrder(agentCourseGroupRecommendMapper.getMaxViewOrder());
                agentCourseGroupRecommend.setTargetType(targetType);
                utilService.updateUserInfoToEntity(agentCourseGroupRecommend);
                agentCourseGroupRecommendMapper.insert(agentCourseGroupRecommend);

            }


    }*/


    /**
     * @return
     * @Description:获取图片
     * @Param
     * @Date 14:28 2021/5/13
     * <AUTHOR>
     */
//    private List<MediaAndAttachedDto> getIconImg(Long id, String typeKey) {
//        MediaAndAttachedVo attachedVo = new MediaAndAttachedVo();
//        attachedVo.setFkTableName(TableEnum.AGENT_INFO.key);
//        attachedVo.setFkTableId(id);
//        attachedVo.setTypeKey(typeKey);
//        return mediaAndAttachedMsoService.getMediaAndAttachedVo(attachedVo);
//    }

    /**
     * @Description:保存图片
     * @Param
     * @Date 12:18 2021/5/13
     * <AUTHOR>
     */
//    private void saveMediaAndAttached(List<MediaAndAttachedVo> topLogoMediaAttachedVos, String typeKey, String fkTableName, Long fkTableId) {
//        if (GeneralTool.isNotEmpty(topLogoMediaAttachedVos)) {
//            for (MediaAndAttachedVo mediaAndAttachedVo : topLogoMediaAttachedVos) {
//                mediaAndAttachedVo.setTypeKey(typeKey);
//                mediaAndAttachedVo.setFkTableId(fkTableId);
//                mediaAndAttachedVo.setFkTableName(fkTableName);
//                mediaAndAttachedVo.setEffectiveStartTime(mediaAndAttachedVo.getEffectiveStartTime());
//                mediaAndAttachedVo.setEffectiveEndTime(mediaAndAttachedVo.getEffectiveEndTime());
//                mediaAndAttachedMsoService.addMediaAndAttached(mediaAndAttachedVo);
//            }
//        }
//    }

    //设置公开对象名称
    private void setPublicLevelName(AgentInfoVo agentInfoVo) {
        StringJoiner stringJoiner = new StringJoiner(",");
        if (GeneralTool.isNotEmpty(agentInfoVo.getPublicLevel())) {
            //把公开对象的序号用逗号割开
            List<String> result = Arrays.asList(agentInfoVo.getPublicLevel().split(","));
            for (String name : result) {
                stringJoiner.add(ProjectExtraEnum.getValueByKey(Integer.valueOf(name), ProjectExtraEnum.PUBLIC_OBJECTS));
            }
            agentInfoVo.setPublicLevelName(stringJoiner.toString());
        }
    }

    /**
     * @Description: 遍历菜单是否被选中
     * @Author: Jerry
     * @Date:11:49 2021/8/16
     */
    /*private void setCheck(List<SitemapVo> sitemapTree,Set<Long> fkMenuIds){
        for (SitemapVo sitemapDto : sitemapTree) {
            //如果菜单被选中,则将该菜单标记为true
            if(fkMenuIds.contains(sitemapDto.getId())){
                sitemapDto.setCheckAgentSiteMap(true);
            }
            if(GeneralTool.isNotEmpty(sitemapDto.getChildSitemapDtoList())){
                setCheck(sitemapDto.getChildSitemapDtoList(),fkMenuIds);
            }
        }
    }*/
}
