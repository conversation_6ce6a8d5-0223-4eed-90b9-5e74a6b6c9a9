package com.get.platformconfigcenter.service.impl;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.eunms.FileTypeEnum;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.feign.IFileCenterClient;
import com.get.platformconfigcenter.vo.MediaAndAttachedVo;
import com.get.platformconfigcenter.entity.ConfigMediaAndAttached;
import com.get.platformconfigcenter.service.MediaAndAttachedMsoService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 媒体附件业务逻辑类
 *
 * <AUTHOR>
 * @date 2021/5/12 11:42
 */
@Service
public class MediaAndAttachedMsoServiceImpl implements MediaAndAttachedMsoService {
//    @Resource
//    private MediaAndAttachedMapper mediaAndAttachedMapper;
    @Resource
    private IFileCenterClient fileCenterClient;
    @Resource
    private UtilService utilService;

//    @Override
//    public List<MediaAndAttachedDto> getMediaAndAttachedVo(MediaAndAttachedVo attachedVo) {
//        List<ConfigMediaAndAttached> mediaAndAttacheds = getMediaAndAttacheds(attachedVo);
//        return getFileMedia(mediaAndAttacheds);
//    }

    /**
     * @Description:上传文件
     * @Param
     * @Date 12:14 2021/5/12
     * <AUTHOR>
     */
    @Override
    public List<FileDto> upload(MultipartFile[] multipartFiles, String type) {
        if (GeneralTool.isEmpty(multipartFiles)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_file_null"));
        }
        List<FileDto> fileDtos = null;
        Result<List<FileDto>> result = fileCenterClient.upload(multipartFiles, type);
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        fileDtos = result.getData();
        return fileDtos;
    }

    /**
     * 上传附件
     *
     * @Date 11:44 2021/7/5
     * <AUTHOR>
     */
    @Override
    public List<FileDto> uploadAppendix(MultipartFile[] multipartFiles, String type) {

        List<FileDto> fileDtos = null;
        Result<List<FileDto>> result = fileCenterClient.uploadAppendix(multipartFiles, type);
        if (!result.isSuccess()) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_upload_fail"));
        }
        fileDtos = result.getData();
        return fileDtos;
    }

    /**
     * 获取媒体附件
     *
     * @param
     * @return
     * @throws GetServiceException
     */
//    private List<ConfigMediaAndAttached> getMediaAndAttacheds(MediaAndAttachedVo attachedVo) {
//        if (GeneralTool.isEmpty(attachedVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
//        }
////        Example example = new Example(MediaAndAttached.class);
////        Example.Criteria criteria = example.createCriteria();
////        if (GeneralTool.isNotEmpty(attachedVo.getTypeKey())) {
////            criteria.andEqualTo("typeKey", attachedVo.getTypeKey());
////        }
////        criteria.andEqualTo("fkTableName", attachedVo.getFkTableName());
////        criteria.andEqualTo("fkTableId", attachedVo.getFkTableId());
////        example.orderBy("indexKey").desc();
////        return mediaAndAttachedMapper.selectByExample(example);
//
//        return mediaAndAttachedMapper.selectList(Wrappers.<ConfigMediaAndAttached>lambdaQuery()
//                .eq(ConfigMediaAndAttached::getTypeKey, attachedVo.getTypeKey())
//                .eq(ConfigMediaAndAttached::getFkTableName, attachedVo.getFkTableName())
//                .eq(ConfigMediaAndAttached::getFkTableId, attachedVo.getFkTableId())
//                .orderByAsc(ConfigMediaAndAttached::getIndexKey).orderByAsc(ConfigMediaAndAttached::getEffectiveStartTime));
//    }

    private List<MediaAndAttachedVo> getFileMedia(List<ConfigMediaAndAttached> mediaAndAttachedList) {
        if (GeneralTool.isEmpty(mediaAndAttachedList)) {
            return null;
        }
        //获取guid集合
        List<String> guidList = mediaAndAttachedList.stream().map(ConfigMediaAndAttached::getFkFileGuid).collect(Collectors.toList());
        //转换成DTO
        List<MediaAndAttachedVo> mediaAndAttachedVos = mediaAndAttachedList.stream()
                .map(mediaAndAttached -> BeanCopyUtils.objClone(mediaAndAttached, MediaAndAttachedVo::new)).collect(Collectors.toList());
        //根据GUID服务调用查询
        Map<String, List<String>> guidListWithTypeMap = new HashMap<>();
        guidListWithTypeMap.put(LoggerModulesConsts.PLATFORMCENTER_MSO, guidList);
        Result<List<FileDto>> result = fileCenterClient.findFileByGuid(guidListWithTypeMap);
        List<FileDto> fileDtos = result.getData();
        //返回结果不为空时
        List<MediaAndAttachedVo> collect = null;
        if (GeneralTool.isNotEmpty(fileDtos)) {
            //遍历查询GUID是否一致
            collect = mediaAndAttachedVos.stream().map(mediaAndAttachedDto -> fileDtos
                    .stream()
                    .filter(fileDto -> fileDto.getFileGuid().equals(mediaAndAttachedDto.getFkFileGuid()))
                    .findFirst()
                    .map(fileDto -> {
                        mediaAndAttachedDto.setFilePath(fileDto.getFilePath());
                        mediaAndAttachedDto.setFileNameOrc(fileDto.getFileNameOrc());
                        mediaAndAttachedDto.setFileKey(fileDto.getFileKey());
                        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAndAttachedDto.getTypeKey()));
                        /*mediaAndAttachedDto.setFkTableName(null);*/
                        return mediaAndAttachedDto;
                    }).orElse(null)
            ).collect(Collectors.toList());
        }
        return collect;
    }

    /**
     * @Description:保存文件
     * @Param
     * @Date 12:28 2021/5/12
     * <AUTHOR>
     */
//    @Override
//    public MediaAndAttachedDto addMediaAndAttached(MediaAndAttachedVo mediaAttachedVo) {
//        if (GeneralTool.isEmpty(mediaAttachedVo.getFkTableName())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("tableName_null"));
//        }
//        ConfigMediaAndAttached andAttached = BeanCopyUtils.objClone(mediaAttachedVo, ConfigMediaAndAttached::new);
//        Integer nextIndexKey = null;
//        if(GeneralTool.isEmpty(mediaAttachedVo.getIndexKey())){
//            nextIndexKey = mediaAndAttachedMapper.getNextIndexKey(andAttached.getFkTableId(), mediaAttachedVo.getFkTableName());
//        }else {
//            nextIndexKey = mediaAttachedVo.getIndexKey();
//        }
//
//
//        nextIndexKey = GeneralTool.isNotEmpty(nextIndexKey) ? nextIndexKey : 0;
//        //实例化对象
//        andAttached.setIndexKey(nextIndexKey);
//        utilService.updateUserInfoToEntity(andAttached);
//        mediaAndAttachedMapper.insertSelective(andAttached);
//        MediaAndAttachedDto mediaAndAttachedDto = BeanCopyUtils.objClone(andAttached, MediaAndAttachedDto::new);
//        mediaAndAttachedDto.setFilePath(mediaAttachedVo.getFilePath());
//        mediaAndAttachedDto.setFileNameOrc(mediaAttachedVo.getFileNameOrc());
//        mediaAndAttachedDto.setTypeValue(FileTypeEnum.getValue(mediaAttachedVo.getTypeKey()));
//        mediaAndAttachedDto.setId(andAttached.getId());
//        mediaAndAttachedDto.setFileKey(mediaAttachedVo.getTypeKey());
//        return mediaAndAttachedDto;
//    }

//    @Override
//    public void deleteMediaAndAttached(String fkTableName, Long fkTableId) {
////        Example example = new Example(MediaAndAttached.class);
////        example.createCriteria().andEqualTo("fkTableName", fkTableName).andEqualTo("fkTableId", fkTableId);
////        mediaAndAttachedMapper.deleteByExample(example);
//        mediaAndAttachedMapper.delete(Wrappers.<ConfigMediaAndAttached>lambdaQuery().eq(ConfigMediaAndAttached::getFkTableName, fkTableName).eq(ConfigMediaAndAttached::getFkTableId, fkTableId));
//    }
}
