package com.get.platformconfigcenter.service;

import java.util.List;
import java.util.Map;

/**
 * 国家资讯配置模块业务处理类
 *
 * <AUTHOR>
 * @date 2021/5/11 17:42
 */
public interface NationalInformationService {

    /**
     * @Description:国家资讯配置列表数据
     * @Param
     * @Date 17:56 2021/5/11
     * <AUTHOR>
     */
//    List<AgentModuleVo> getAgentModuleList(AgentModuleDto agentModuleVo, Page page);

    /**
     * @Description:新增国家资讯
     * @Param
     * @Date 10:30 2021/5/12
     * <AUTHOR>
     */
//    Long addAgentModule(AgentModuleDto agentModuleVo);

    /**
     * @Description:更新国家资讯
     * @Param
     * @Date 14:52 2021/5/12
     * <AUTHOR>
     */
//    AgentModuleVo updateAgentModule(AgentModuleDto agentModuleVo);


    /**
     * @Description:国家资讯配置详情
     * @Param
     * @Date 11:31 2021/5/12
     * <AUTHOR>
     */
//    AgentModuleVo findAgentModuleById(Long id);

    /**
     * @Description:国家资讯配置删除
     * @Param
     * @Date 11:31 2021/5/12
     * <AUTHOR>
     */
//    void delete(Long id);

    /**
     * @Description:上移下移
     * @Param
     * @Date 12:49 2021/5/12
     * <AUTHOR>
     */
//    void movingOrder(List<AgentModuleDto> agentModuleVoList);

    /**
     * @Description:国家资讯模块类型下拉框数据
     * @Param
     * @Date 15:49 2021/5/12
     * <AUTHOR>
     */
    List<Map<String, Object>> getModuleKey();

}
