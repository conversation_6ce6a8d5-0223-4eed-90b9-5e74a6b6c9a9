package com.get.platformconfigcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.DeleteResponseBo;
import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import com.get.common.result.ResponseBo;
import com.get.common.result.SaveResponseBo;
import com.get.common.result.SearchBean;
import com.get.common.result.UpdateResponseBo;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.platformconfigcenter.vo.PrivacyPolicyVo;
import com.get.platformconfigcenter.service.IPrivacyPolicyService;
import com.get.platformconfigcenter.dto.PrivacyPolicyDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * @author: Hardy
 * @create: 2021/5/13 15:53
 * @verison: 1.0
 * @description:
 */
@Api(tags = "私隐条例管理")
@RestController
@RequestMapping("/platform/privacypolicy")
public class PrivacyPolicyController {

    @Resource
    private IPrivacyPolicyService privacyPolicyService;


    /**
     * @return com.get.common.result.ResponseBo<com.get.registrationcenter.vo.PrivacyPolicyVo>
     * @Description :列表数据
     * @Param [page]
     * <AUTHOR>
     */
    @ApiOperation(value = "列表数据", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REGISTRATIONCENTER, type = LoggerOptTypeConst.LIST, description = "注册中心/私隐条例管理")
    @PostMapping("datas")
    public ListResponseBo<PrivacyPolicyVo> datas(@RequestBody SearchBean<PrivacyPolicyDto> page) {
        List<PrivacyPolicyVo> datas = privacyPolicyService.getPrivacyPolicys(page.getData(), page);
        Page p = BeanCopyUtils.objClone(page, Page::new);
        return new ListResponseBo<>(datas, p);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description : 新增接口
     * @Param [helpVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "新增接口", notes = "")
    @OperationLogger(module = LoggerModulesConsts.REGISTRATIONCENTER, type = LoggerOptTypeConst.ADD, description = "注册中心/私隐条例管理/新增")
    @PostMapping("add")
    public ResponseBo add(@RequestBody @Validated(PrivacyPolicyDto.Add.class) PrivacyPolicyDto privacyPolicyDto) {
        return SaveResponseBo.ok(privacyPolicyService.addPrivacyPolicy(privacyPolicyDto));
    }

    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [helpVo]
     * <AUTHOR>
     */
    @ApiOperation(value = "修改接口")
    @OperationLogger(module = LoggerModulesConsts.REGISTRATIONCENTER, type = LoggerOptTypeConst.EDIT, description = "注册中心/私隐条例管理/修改")
    @PostMapping("update")
    public ResponseBo<PrivacyPolicyDto> update(@RequestBody @Validated(PrivacyPolicyDto.Update.class) PrivacyPolicyDto privacyPolicyDto) {
        return UpdateResponseBo.ok(privacyPolicyService.updatePrivacyPolicyVo(privacyPolicyDto));
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "删除", notes = "id为此条数据的id")
    @OperationLogger(module = LoggerModulesConsts.REGISTRATIONCENTER, type = LoggerOptTypeConst.DELETE, description = "注册中心/私隐条例管理/删除")
    @PostMapping("delete/{id}")
    public ResponseBo delete(@PathVariable("id") Long id) {
        privacyPolicyService.deletePrivacyPolicy(id);
        return DeleteResponseBo.ok();
    }


    /**
     * @return com.get.common.result.ResponseBo<com.get.registrationcenter.vo.PrivacyPolicyVo>
     * @Description :详情
     * @Param [id]
     * <AUTHOR>
     */
    @ApiOperation(value = "私隐条例详情接口", notes = "id为此条数据id")
    @OperationLogger(module = LoggerModulesConsts.REGISTRATIONCENTER, type = LoggerOptTypeConst.DETAIL, description = "注册中心/私隐条例管理/详情")
    @GetMapping("/{id}")
    public ResponseBo<PrivacyPolicyVo> detail(@PathVariable("id") Long id) {
        PrivacyPolicyVo data = privacyPolicyService.findPrivacyPolicyById(id);
        return new ResponseBo<>(data);
    }


    /**
     * @return com.get.common.result.ResponseBo
     * @Description :私隐条例管理下拉框数据
     * @Param []
     * <AUTHOR>
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "私隐条例管理下拉框数据", notes = "")
    @GetMapping("getPrivacyPolicyList")
    public ResponseBo<PrivacyPolicyVo> getPrivacyPolicyList() {
        List<PrivacyPolicyVo> datas = privacyPolicyService.getPrivacyPolicyList();
        return new ListResponseBo<>(datas);
    }


    /**
     * @Description :feign调用 通过城市ids 查找对应的城市名称map
     * @Param [ids]
     * <AUTHOR>
     */
    @ApiIgnore
    @PostMapping(value = "getPrivacyPolicyTitlesByIds")
    public Map<Long, String> getPrivacyPolicyTitlesByIds(@RequestBody Set<Long> ids) {
        return privacyPolicyService.getPrivacyPolicyTitlesByIds(ids);
    }
}
