package com.get.platformconfigcenter.controller;

import com.get.platformconfigcenter.service.AppFormConfigService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 信息收集内容配置
 *
 * <AUTHOR>
 * @date 2021/5/21 11:22
 */
@Api(tags = "信息收集内容配置管理")
@RestController
@RequestMapping("/platform/ISSUE/informationCollectionConfiguration")
public class AppFormConfigController {
    @Resource
    private AppFormConfigService appFormConfigService;

//    @ApiOperation(value = "信息收集内容配置列表数据", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/ISSUE/信息收集内容配置管理/查询")
//    @PostMapping("datas")
//    public ResponseBo<AppFormConfigVo> datas(@RequestBody SearchBean<AppFormConfigDto> page) {
//        List<AppFormConfigVo> appFormConfigDtos = appFormConfigService.getConfigurationList(page.getData(), page);
//        Page p = BeanCopyUtils.objClone(page, Page::new);
//        return new ListResponseBo<>(appFormConfigDtos, p);
//    }

//    @ApiOperation(value = "新增信息收集内容配置接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/ISSUE/信息收集内容配置管理/新增信息收集内容配置")
//    @PostMapping("add")
//    public ResponseBo add(@RequestBody @Validated(AppFormConfigDto.Add.class) AppFormConfigDto appFormConfigVo) {
//        return SaveResponseBo.ok(appFormConfigService.addConfiguration(appFormConfigVo));
//    }

//    @ApiOperation(value = "修改接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/ISSUE/信息收集内容配置管理/更新信息收集内容配置")
//    @PostMapping("update")
//    public ResponseBo<AppFormConfigVo> update(@RequestBody @Validated(AppFormConfigDto.Update.class) AppFormConfigDto appFormConfigVo) {
//        return UpdateResponseBo.ok(appFormConfigService.updateConfiguration(appFormConfigVo));
//    }

//    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/ISSUE/信息收集内容配置管理/详情")
//    @GetMapping("/{id}")
//    public ResponseBo<AppFormConfigVo> detail(@PathVariable("id") Long id) {
//        AppFormConfigVo data = appFormConfigService.findConfigurationById(id);
//        return new ResponseBo<>(data);
//    }

//    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/ISSUE/信息收集内容配置管理/删除信息收集内容配置")
//    @PostMapping("delete/{id}")
//    public ResponseBo delete(@PathVariable("id") Long id) {
//        appFormConfigService.delete(id);
//        return DeleteResponseBo.ok();
//    }
}
