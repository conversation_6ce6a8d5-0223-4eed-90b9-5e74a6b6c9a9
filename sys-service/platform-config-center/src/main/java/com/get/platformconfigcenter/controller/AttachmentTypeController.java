package com.get.platformconfigcenter.controller;

import com.get.common.result.ListResponseBo;
import com.get.common.result.ResponseBo;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.platformconfigcenter.service.AttachmentTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 附件类型管理控制类
 *
 * <AUTHOR>
 * @date 2021/5/20 12:27
 */
@Api(tags = "附件类型配置")
@RestController
@RequestMapping("/platform/ISSUE/attachmentType")
public class AttachmentTypeController {
    @Resource
    private AttachmentTypeService attachmentTypeService;

//    @ApiOperation(value = "附件类型列表数据", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.LIST, description = "业务平台配置中心/ISSUE/附件类型管理/查询")
//    @PostMapping("datas")
//    public ResponseBo<StudentAttachmentVo> datas(@RequestBody SearchBean<StudentAttachmentDto> page) {
//        List<StudentAttachmentVo> agentInfoDtos = attachmentTypeService.getAttachmentTypeList(page.getData(), page);
//        Page p = BeanCopyUtils.objClone(page, Page::new);
//        return new ListResponseBo<>(agentInfoDtos, p);
//    }

//    @ApiOperation(value = "新增附件类型接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.ADD, description = "业务平台配置中心/ISSUE/附件类型管理/新增附件类型")
//    @PostMapping("add")
//    public ResponseBo add(@RequestBody @Validated(StudentAttachmentDto.Add.class) StudentAttachmentDto studentAttachmentVo) {
//        return SaveResponseBo.ok(attachmentTypeService.addAttachmentType(studentAttachmentVo));
//    }

//    @ApiOperation(value = "修改接口", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/ISSUE/附件类型管理/更新附件类型")
//    @PostMapping("update")
//    public ResponseBo<StudentAttachmentVo> update(@RequestBody @Validated(StudentAttachmentDto.Update.class) StudentAttachmentDto studentAttachmentVo) {
//        return UpdateResponseBo.ok(attachmentTypeService.updateAttachmentType(studentAttachmentVo));
//    }

//    @ApiOperation(value = "配置详情接口", notes = "id为此条数据id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/ISSUE/附件类型管理/详情")
//    @GetMapping("/{id}")
//    public ResponseBo<StudentAttachmentVo> detail(@PathVariable("id") Long id) {
//        StudentAttachmentVo data = attachmentTypeService.findAttachmentTypeById(id);
//        return new ResponseBo<>(data);
//    }

//    @ApiOperation(value = "删除接口", notes = "id为此条数据的id")
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DELETE, description = "业务平台配置中心/ISSUE/附件类型管理/删除附件类型")
//    @PostMapping("delete/{id}")
//    public ResponseBo delete(@PathVariable("id") Long id) {
//        attachmentTypeService.delete(id);
//        return DeleteResponseBo.ok();
//    }

//    @ApiOperation(value = "上移下移", notes = "")
//    @OperationLogger(module = LoggerModulesConsts.FINANCECENTER, type = LoggerOptTypeConst.EDIT, description = "业务平台配置中心/ISSUE/附件类型管理/移动顺序")
//    @PostMapping("movingOrder")
//    public ResponseBo movingOrder(@RequestBody List<StudentAttachmentDto> studentAttachmentVos) {
//        attachmentTypeService.movingOrder(studentAttachmentVos);
//        return ResponseBo.ok();
//    }

    /**
     * 附件类型下拉框数据
     *
     * @return
     * @
     */
//    @VerifyPermission(IsVerify = false)
//    @ApiOperation(value = "附件类型下拉框数据", notes = "")
//    @GetMapping("getAttachmentTypeSelect")
//    public ResponseBo<StudentAttachmentVo> getAttachmentTypeSelect() {
//        List<StudentAttachmentVo> datas = attachmentTypeService.getAttachmentTypeSelect();
//        return new ListResponseBo<>(datas);
//    }

    /**
     * 附件文件类型下拉框数据
     *
     * @return
     * @
     */
    @VerifyPermission(IsVerify = false)
    @ApiOperation(value = "附件文件类型下拉框数据", notes = "")
    @GetMapping("getFileExtensionSelect")
    public ResponseBo getFileExtensionSelect() {
        List<Map<String, Object>> datas = attachmentTypeService.getFileExtensionSelect();
        return new ListResponseBo<>(datas);
    }


}
