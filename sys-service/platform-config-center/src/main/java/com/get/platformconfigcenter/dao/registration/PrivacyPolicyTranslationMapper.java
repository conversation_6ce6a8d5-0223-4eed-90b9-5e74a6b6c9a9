package com.get.platformconfigcenter.dao.registration;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.get.platformconfigcenter.entity.Translation;
import com.get.platformconfigcenter.dto.TranslationDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
@DS("registrationdb")
public interface PrivacyPolicyTranslationMapper extends BaseMapper<Translation> {

    int insert(Translation record);

    int insertSelective(Translation record);

    int updateByPrimaryKeySelective(Translation record);

    int updateByPrimaryKeyWithBLOBs(Translation record);

    int updateByPrimaryKey(Translation record);

    String getTranslation(TranslationDto translationDto);

    /**
     * 删除翻译内容
     *
     * @param id
     */
    void deleteTranslations(@Param("tableName") String tableName, @Param("id") Long id);
}