package com.get.platformconfigcenter.controller;

import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.core.log.annotation.OperationLogger;
import com.get.platformconfigcenter.service.AplOrderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2021/11/24
 * @TIME: 14:41
 * @Description:
 **/
@Api(tags = "Issue一键网申")
@RestController
@RequestMapping("platform/robot")
public class AplOrderController {
    @Resource
    private AplOrderService aplOrderService;

//    @GetMapping("getRobotStatus")
//    @ApiOperation(value = "获取申请状态", notes = "")
//    @ApiIgnore
//    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER, type = LoggerOptTypeConst.DETAIL, description = "业务平台配置中心/ISSUE/ISSUE一键网申/获取申请状态")
//    public String getRobotStatusById(@RequestParam("orderId") Long orderId) {
//        return aplOrderService.getRobotStatusById(orderId);
//    }

    /**
     * 获取申请状态
     *
     * @param orderIds
     * @return
     */
//    @ApiIgnore
//    @GetMapping("getRobotStatusByIds")
//    public Map<Long, String> getRobotStatusByIds(@RequestParam("orderIds") Set<Long> orderIds) {
//        return aplOrderService.getRobotStatusByIds(orderIds);
//    }
}
