package com.get.platformconfigcenter.controller;


import com.get.common.consts.LoggerModulesConsts;
import com.get.common.consts.LoggerOptTypeConst;
import com.get.common.result.*;
import com.get.common.utils.BeanCopyUtils;
import com.get.core.log.annotation.OperationLogger;
import com.get.platformconfigcenter.entity.MsoEventRegistration;
import com.get.platformconfigcenter.service.EventRegistrationService;
import com.get.platformconfigcenter.dto.EventRegistrationDto;
import com.get.platformconfigcenter.dto.IsAttendDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @ClassName: EventRegistrationController
 * @Author: Eric
 * @Date: 2023/5/23 17:53
 * @Version: 1.0
 */
@Api(tags = "展会登记管理")
@RestController
@RequestMapping("platform/appMso/eventRegistration")
public class EventRegistrationController {

    @Resource
    private EventRegistrationService eventRegistrationService;

    @PostMapping("getEventRegistrationList")
    @ApiOperation("MSO活动登记列表")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER,type = LoggerOptTypeConst.ADD,description = "MSO活动登记列表")
    public ResponseBo getEventRegistrationList(@RequestBody SearchBean<EventRegistrationDto> eventRegistrationVo){
      List<MsoEventRegistration> eventRegistrations = eventRegistrationService.getEventRegistrationList(eventRegistrationVo.getData(),eventRegistrationVo);
       Page page = BeanCopyUtils.objClone(eventRegistrationVo, Page::new);

        return new ListResponseBo(eventRegistrations,page);

    }

    @PostMapping("batchUpdateIsAttend")
    @ApiOperation("MSO活动批量登记-1是登记0是取消")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER,type = LoggerOptTypeConst.EDIT,description = "MSO活动批量登记-1是登记0是取消")
    public ResponseBo batchUpdateIsAttend(@RequestBody IsAttendDto isAttendDto){

        eventRegistrationService.batchUpdateIsAttend(isAttendDto);

        return SaveResponseBo.ok();
    }


    @PostMapping("batchDeleteEventRegistration")
    @ApiOperation("批量删除活动登记人员")
    @OperationLogger(module = LoggerModulesConsts.PLATFORMCENTER,type = LoggerOptTypeConst.DELETE,description = "批量删除活动登记人员")
    public ResponseBo batchDeleteEventRegistration(@RequestBody List<Long> ids){
        eventRegistrationService.batchDeleteEventRegistration(ids);

        return DeleteResponseBo.ok();
    }




}
