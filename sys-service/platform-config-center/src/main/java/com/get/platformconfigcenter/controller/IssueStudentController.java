package com.get.platformconfigcenter.controller;


import com.get.common.result.ResponseBo;
import com.get.platformconfigcenter.service.DeleteService;
import com.get.platformconfigcenter.service.IIssueStudentService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/7/6 17:48
 */
@Api(tags = "Issue学生")
@RestController
@RequestMapping("platform/ISSUE/student")
public class IssueStudentController {
    //TODO 注释ISSUE相关功能 lucky  2024/12/23

//    @Resource
//    private IIssueStudentService iIssueStudentService;
//    @Resource
//    private DeleteService deleteService;
//
//
////    @ApiOperation("查找issue学生课程")
////    @GetMapping("getIssueStudentCourseByStuId")
////    public ResponseBo getIssueStudentCourseByStuId(@RequestParam("fkStudentId") Long fkStudentId) {
////        List<StudentInstitutionCourseVo> issueStudentAndCourseByFkStaffId =
////                iIssueStudentService.getIssueStudentCourseByStuId(fkStudentId);
////        return new ListResponseBo<>(issueStudentAndCourseByFkStaffId);
////    }
//
////    @ApiOperation("修改课程状态")
////    @PostMapping("updateIssueCourseStatus")
////    public ResponseBo updateIssueCourseStatus(@RequestParam("statusStep") Integer statusStep,
////                                              @RequestParam("courseId") Long courseId) {
////        iIssueStudentService.updateIssueCourseStatus(statusStep, courseId);
////        return ResponseBo.ok();
////
////    }
//
//    @ApiOperation("获取访问issue的密钥")
//    @GetMapping("getCipherText")
//    public ResponseBo getCipherText() {
//        String cipherText = iIssueStudentService.getCipherText();
//        return new ResponseBo<>(cipherText);
//    }
//
//    /**
//     * @Description: 验证课程逻辑删除
//     * @Author: jack
//     * @Date:11:13 2021/8/27
//     */
////    @ApiIgnore
////    @GetMapping("deleteValidateCourse")
////    public Boolean deleteValidateCourse(@RequestParam(value = "courseId") Long courseId) {
////        return deleteService.deleteValidateCourse(courseId);
////    }
//
//
//    @ApiOperation("更新学生代理")
//    @GetMapping("updateStudentAgent")
//    public ResponseBo updateStudentAgent(@RequestParam("fkSutdentId") Long fkSutdentId, @RequestParam("fkAgentId") Long fkAgentId) {
//        iIssueStudentService.updateStudentAgent(fkSutdentId, fkAgentId);
//        return ResponseBo.ok();
//    }
//
//    @ApiOperation("修改issue学生提交状态")
//    @GetMapping("getChangeIssueStudentSataus")
//    public ResponseBo getChangeIssueStudentSataus(@RequestParam("fkIssueStudentsId") Long fkIssueStudentsId,
//                                                  @RequestParam("status") Integer status) {
//        iIssueStudentService.getChangeIssueStudentSataus(fkIssueStudentsId, status);
//        return ResponseBo.ok();
//    }

}
