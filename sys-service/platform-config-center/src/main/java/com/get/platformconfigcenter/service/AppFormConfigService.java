package com.get.platformconfigcenter.service;

/**
 * 信息收集内容配置
 *
 * <AUTHOR>
 * @date 2021/5/21 11:28
 */
public interface AppFormConfigService {
    /**
     * 申请表单板块列表数据
     *
     * @return
     * @Date 15:36 2021/5/20
     * <AUTHOR>
     */
   // List<AppFormConfigVo> getConfigurationList(AppFormConfigDto appFormConfigVo, Page page);

    /**
     * 新增申请表单板块
     *
     * @Date 16:13 2021/5/20
     * <AUTHOR>
     */
   // Long addConfiguration(AppFormConfigDto appFormConfigVo);

    /**
     * 修改申请表单板块
     *
     * @Date 16:29 2021/5/20
     * <AUTHOR>
     */
    //AppFormConfigVo updateConfiguration(AppFormConfigDto appFormConfigVo);

    /**
     * 申请表单板块详情
     *
     * @Date 16:34 2021/5/20
     * <AUTHOR>
     */
    //AppFormConfigVo findConfigurationById(Long id);

    /**
     * 删除申请表单板块
     *
     * @Date 16:37 2021/5/20
     * <AUTHOR>
     */
   // void delete(Long id);
}
