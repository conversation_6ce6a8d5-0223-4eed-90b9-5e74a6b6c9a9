package com.get.platformconfigcenter.service.impl;

import com.get.core.mybatis.base.UtilService;
import com.get.platformconfigcenter.dao.appissue.AppInstitutionCharacterItemMapper;
import com.get.platformconfigcenter.dao.appissue.IssueTranslationMapper;
import com.get.platformconfigcenter.service.AppInstitutionCharacterItemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 课程动态表单详情配置业务逻辑处理类
 *
 * <AUTHOR>
 * @date 2021/5/25 10:13
 */
@Service
public class AppInstitutionCharacterItemServiceImpl implements AppInstitutionCharacterItemService {
//    @Resource
//    private AppInstitutionCharacterItemMapper appInstitutionCharacterItemMapper;
    @Resource
    private AppInstitutionCharacterItemMapper appInstitutionCharacterItemMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IssueTranslationMapper issueTranslationMapper;

    /**
     * Item-课程动态表单配置详情列表数据
     *
     * @Date 10:24 2021/5/24
     * <AUTHOR>
     */
//    @Override
//    public List<AppInstitutionCharacterItemVo> getInstitutionCharacterItemList(Long characterId) {
////        Example example = new Example(AppInstitutionCharacterItem.class);
////        example.createCriteria().andEqualTo("fkAppInstitutionCharacterId", characterId);
////        example.orderBy("viewOrder").desc();
////        List<AppInstitutionCharacterItem> appInstitutionCharacterItems = appInstitutionCharacterItemMapper.selectByExample(example);
//
//        List<AppInstitutionCharacterItem> appInstitutionCharacterItems = appInstitutionCharacterItemMapper.selectList(Wrappers.<AppInstitutionCharacterItem>query().lambda()
//                .eq(AppInstitutionCharacterItem::getFkAppInstitutionCharacterId, characterId)
//                .orderByDesc(AppInstitutionCharacterItem::getViewOrder));
//        List<AppInstitutionCharacterItemVo> institutionCharacterItemDtoList = new ArrayList<>();
//        for (AppInstitutionCharacterItem appInstitutionCharacterItem : appInstitutionCharacterItems) {
//            AppInstitutionCharacterItemVo appInstitutionCharacterItemDto = BeanCopyUtils.objClone(appInstitutionCharacterItem, AppInstitutionCharacterItemVo::new);
//            appInstitutionCharacterItemDto.setFkTableName(TableEnum.APP_INSTITUTION_CHARACTER_ITEM.key);
//            institutionCharacterItemDtoList.add(appInstitutionCharacterItemDto);
//        }
//        return institutionCharacterItemDtoList;
//    }

    /**
     * Item-删除课程动态表单详情配置
     *
     * @Date 11:49 2021/5/24
     * <AUTHOR>
     */
//    @Override
//    public void delete(List<Long> ids) {
//        appInstitutionCharacterItemMapper.deleteByIds(ids);
//        for (Long id : ids) {
//            //删除翻译内容
//            issueTranslationMapper.deleteTranslations(TableEnum.APP_INSTITUTION_CHARACTER_ITEM.key, id);
//        }
//    }

    /**
     * Item-新增课程动态表单详情配置
     *
     * @Date 12:07 2021/5/24
     * <AUTHOR>
     */
//    @Override
//    public void addInstitutionCharacterItem(AppInstitutionCharacterItemDto appInstitutionCharacterItemVo) {
//        if (GeneralTool.isEmpty(appInstitutionCharacterItemVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("insert_vo_null"));
//        }
//        AppInstitutionCharacterItem appInstitutionCharacterItem = BeanCopyUtils.objClone(appInstitutionCharacterItemVo, AppInstitutionCharacterItem::new);
//        appInstitutionCharacterItem.setViewOrder(appInstitutionCharacterItemMapper.getMaxViewOrder());
//        utilService.updateUserInfoToEntity(appInstitutionCharacterItem);
//        appInstitutionCharacterItemMapper.insert(appInstitutionCharacterItem);
//    }

    /**
     * Item-修改课程动态表单详情配置
     *
     * @Date 12:51 2021/5/24
     * <AUTHOR>
     */
//    @Override
//    public void updateInstitutionCharacterItem(AppInstitutionCharacterItemDto appInstitutionCharacterItemVo) {
//        if (GeneralTool.isEmpty(appInstitutionCharacterItemVo)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//        AppInstitutionCharacterItem appInstitutionCharacterItem = BeanCopyUtils.objClone(appInstitutionCharacterItemVo, AppInstitutionCharacterItem::new);
//        utilService.updateUserInfoToEntity(appInstitutionCharacterItem);
//        appInstitutionCharacterItemMapper.updateByPrimaryKeySelective(appInstitutionCharacterItem);
//    }

    /**
     * 课程动态表单Item详情
     *
     * @Date 17:50 2021/5/24
     * <AUTHOR>
     */
//    @Override
//    public AppInstitutionCharacterItemVo findInstitutionCharacterItemById(Long id) {
//        if (GeneralTool.isEmpty(id)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
//        }
//        AppInstitutionCharacterItem appInstitutionCharacterItem = appInstitutionCharacterItemMapper.selectById(id);
//        if (GeneralTool.isEmpty(appInstitutionCharacterItem)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//        }
//        return BeanCopyUtils.objClone(appInstitutionCharacterItem, AppInstitutionCharacterItemVo::new);
//    }

    /**
     * 上移下移
     *
     * @Date 14:19 2021/6/2
     * <AUTHOR>
     */
//    @Override
//    public void movingOrder(List<AppInstitutionCharacterItemDto> appInstitutionCharacterItemVos) {
//        if (GeneralTool.isEmpty(appInstitutionCharacterItemVos)) {
//            throw new GetServiceException(ResultCode.INVALID_PARAM, LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//        AppInstitutionCharacterItem ro = BeanCopyUtils.objClone(appInstitutionCharacterItemVos.get(0), AppInstitutionCharacterItem::new);
//        Integer oneorder = ro.getViewOrder();
//        AppInstitutionCharacterItem rt = BeanCopyUtils.objClone(appInstitutionCharacterItemVos.get(1), AppInstitutionCharacterItem::new);
//        Integer twoorder = rt.getViewOrder();
//        ro.setViewOrder(twoorder);
//        utilService.updateUserInfoToEntity(ro);
//        rt.setViewOrder(oneorder);
//        utilService.updateUserInfoToEntity(rt);
//        appInstitutionCharacterItemMapper.updateByPrimaryKey(ro);
//        appInstitutionCharacterItemMapper.updateByPrimaryKey(rt);
//    }
}
