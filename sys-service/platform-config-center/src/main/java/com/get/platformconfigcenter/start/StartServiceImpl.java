package com.get.platformconfigcenter.start;

import com.get.core.auto.service.AutoService;
import com.get.core.start.service.StartService;
import org.springframework.boot.builder.SpringApplicationBuilder;

/**
 * 如有多数据源则需要配置该参数
 */
@AutoService(StartService.class)
public class StartServiceImpl implements StartService {
    @Override
    public void launcher(SpringApplicationBuilder builder, String appName, String profile, boolean isLocalDev) {
//		Properties props = System.getProperties();
//		PropsUtil.setProperty(props, "spring.datasource.dynamic.enabled", "true");
//		//seata地址
//		PropsUtil.setProperty(props, "seata.service.grouplist.default", LauncherStart.seataAddr(profile));
//		//seata配置group
//		PropsUtil.setProperty(props, "seata.service.vgroup-mapping.".concat(LauncherStart.seataServiceGroup(appName)), LauncherStart.DEFAULT_MODE);
    }

}
