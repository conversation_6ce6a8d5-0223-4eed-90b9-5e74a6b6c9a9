package com.get.platformconfigcenter.service.impl;

import com.get.core.mybatis.base.UtilService;
import com.get.platformconfigcenter.dao.appissue.AppInstitutionCharacterItemMapper;
import com.get.platformconfigcenter.dao.appissue.IssueTranslationMapper;
import com.get.platformconfigcenter.dao.appissue.IssueTranslationMappingMapper;
import com.get.platformconfigcenter.dao.appissue.StudentAttachmentMapper;
import com.get.platformconfigcenter.dao.registration.PrivacyPolicyMapper;
import com.get.platformconfigcenter.dao.registration.PrivacyPolicyTranslationMapper;
import com.get.platformconfigcenter.dao.registration.PrivacyPolicyTranslationMappingMapper;
import com.get.platformconfigcenter.service.TranslationMappingService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: Hardy
 * @create: 2021/5/14 17:21
 * @verison: 1.0
 * @description:
 */
@Service
public class TranslationMappingServiceImpl implements TranslationMappingService {
//    @Resource
//    private TranslationMappingMapper translationMappingMapper;
//    @Resource
//    private TranslationMapper translationMapper;
    @Resource
    private PrivacyPolicyTranslationMappingMapper privacyPolicyTranslationMappingMapper;
    @Resource
    private PrivacyPolicyTranslationMapper privacyPolicyTranslationMapper;
//    @Resource
//    private AgentModuleInfoMapper agentModuleInfoMapper;
//    @Resource
//    private AgentInfoMapper agentInfoMapper;
    @Resource
    private AppInstitutionCharacterItemMapper appInstitutionCharacterItemMapper;
    @Resource
    private PrivacyPolicyMapper privacyPolicyMapper;
    @Resource
    private IssueTranslationMappingMapper issueTranslationMappingMapper;
    @Resource
    private IssueTranslationMapper issueTranslationMapper;
    @Resource
    private StudentAttachmentMapper studentAttachmentMapper;
//    @Resource
//    private AppFormDivisionMapper appFormDivisionMapper;
    @Resource
    private UtilService utilService;
//    @Resource
//    private SitemapMapper sitemapMapper;


//    @Override
//    public List<TranslationMappingVo> getTranslationMappingDtos(TranslationDto translationVo) {
//        if (GeneralTool.isEmpty(translationVo.getType()) || GeneralTool.isEmpty(translationVo.getLanguageCode())) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
//        }
////        Example example = new Example(TranslationMapping.class);
////        Example.Criteria criteria = example.createCriteria();
////        if (GeneralTool.isNotEmpty(translationVo)) {
////            if (GeneralTool.isNotEmpty(translationVo.getFkTableName())) {
////                criteria.andEqualTo("fkTableName", translationVo.getFkTableName());
////            }
////        }
//
//        LambdaQueryWrapper<TranslationMapping> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        if (GeneralTool.isNotEmpty(translationVo)) {
//            if (GeneralTool.isNotEmpty(translationVo.getFkTableName())) {
//                criteria.andEqualTo("fkTableName", translationVo.getFkTableName());
//            }
//        }

//        LambdaQueryWrapper<TranslationMapping> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        if (GeneralTool.isNotEmpty(translationVo)) {
//            if (GeneralTool.isNotEmpty(translationVo.getFkTableName())) {
//                lambdaQueryWrapper.eq(TranslationMapping::getFkTableName, translationVo.getFkTableName());
//            }
//        }
//
//
//        //ISSUE语言判断
//        boolean issueFlag = translationVo.getFkTableName().equals(TableEnum.APP_INSTITUTION_CHARACTER_ITEM.key) ||
//                translationVo.getFkTableName().equals(TableEnum.U_STUDENT_ATTACHMENT.key) ||
//                translationVo.getFkTableName().equals(TableEnum.U_APP_FORM_DIVISION.key);
//
//        Map<String, Object> map = fromJavaBean(translationVo.getFkTableName(), translationVo.getFkTableId());
//        List<TranslationMappingVo> translationMappingDtos = null;
//        if (translationVo.getFkTableName().equals(TableEnum.PRIVATE_POLICY.key)) {
//            List<TranslationMapping> translationMappings = privacyPolicyTranslationMappingMapper.selectList(lambdaQueryWrapper);
//            translationMappingDtos = translationMappings.stream().map(TranslationMapping -> BeanCopyUtils.objClone(TranslationMapping, TranslationMappingVo::new)).collect(Collectors.toList());
//        } else if (issueFlag) {
//            List<TranslationMapping> translationMappings = issueTranslationMappingMapper.selectList(lambdaQueryWrapper);
//            translationMappingDtos = translationMappings.stream().map(TranslationMapping -> BeanCopyUtils.objClone(TranslationMapping, TranslationMappingVo::new)).collect(Collectors.toList());
//        } else {
////            List<TranslationMapping> translationMappings = translationMappingMapper.selectList(lambdaQueryWrapper);
////            translationMappingDtos = translationMappings.stream().map(TranslationMapping -> BeanCopyUtils.objClone(TranslationMapping, TranslationMappingVo::new)).collect(Collectors.toList());
//        }
//        if (translationVo.getType() == 1 && translationVo.getLanguageCode().equals(ProjectKeyEnum.ZH_CN.key)) {
//            for (TranslationMappingVo translationMappingDto : translationMappingDtos) {
//                for (String key : map.keySet()) {
//                    if (key.equals(translationMappingDto.getFkColumnName())) {
//                        translationVo.setFkTranslationMappingId(translationMappingDto.getId());
//                        if (GeneralTool.isNotEmpty(map.get(key))) {
//                            translationMappingDto.setTranslationContent(map.get(key).toString());
//                        }
//                    }
//                }
//            }
//        } else {
//            for (TranslationMappingVo translationMappingDto : translationMappingDtos) {
//                for (String key : map.keySet()) {
//                    if (key.equals(translationMappingDto.getFkColumnName())) {
//                        translationVo.setFkTranslationMappingId(translationMappingDto.getId());
//                        if (translationVo.getFkTableName().equals(TableEnum.PRIVATE_POLICY.key)) {
//                            translationMappingDto.setTranslationContent(privacyPolicyTranslationMapper.getTranslation(translationVo));
//                        } else if (issueFlag) {
//                            translationMappingDto.setTranslationContent(issueTranslationMapper.getTranslation(translationVo));
//                        } else {
////                            translationMappingDto.setTranslationContent(translationMapper.getTranslation(translationVo));
//                        }
//                    }
//                }
//            }
//        }
//
//        return translationMappingDtos;
//    }

//    @Override
//    public void updateTranslations(List<TranslationDto> translationVos) {
//        if (GeneralTool.isEmpty(translationVos)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("update_vo_null"));
//        }
//        if (translationVos.get(0).getLanguageCode().equals(ProjectKeyEnum.ZH_CN.key)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("Simplified_Chinese_not_translation"));
//        }
//        for (TranslationDto translationVo : translationVos) {
////            Example example = new Example(Translation.class);
////            Example.Criteria criteria = example.createCriteria();
////            if (GeneralTool.isNotEmpty(translationVo)) {
////                if (GeneralTool.isNotEmpty(translationVo.getFkTableName())) {
////                    criteria.andEqualTo("fkTableName", translationVo.getFkTableName());
////                }
////                if (GeneralTool.isNotEmpty(translationVo.getFkTableId())) {
////                    criteria.andEqualTo("fkTableId", translationVo.getFkTableId());
////                }
////                if (GeneralTool.isNotEmpty(translationVo.getLanguageCode())) {
////                    criteria.andEqualTo("languageCode", translationVo.getLanguageCode());
////                }
////                if (GeneralTool.isNotEmpty(translationVo.getFkTranslationMappingId())) {
////                    criteria.andEqualTo("fkTranslationMappingId", translationVo.getFkTranslationMappingId());
////                }
////            }
//            LambdaQueryWrapper<Translation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//            if (GeneralTool.isNotEmpty(translationVo)) {
//                if (GeneralTool.isNotEmpty(translationVo.getFkTableName())) {
//                    lambdaQueryWrapper.eq(Translation::getFkTableName, translationVo.getFkTableName());
//                }
//                if (GeneralTool.isNotEmpty(translationVo.getFkTableId())) {
//                    lambdaQueryWrapper.eq(Translation::getFkTableId, translationVo.getFkTableId());
//                }
//                if (GeneralTool.isNotEmpty(translationVo.getLanguageCode())) {
//                    lambdaQueryWrapper.eq(Translation::getLanguageCode, translationVo.getLanguageCode());
//                }
//                if (GeneralTool.isNotEmpty(translationVo.getFkTranslationMappingId())) {
//                    lambdaQueryWrapper.eq(Translation::getFkTranslationMappingId, translationVo.getFkTranslationMappingId());
//                }
//            }
//            LambdaQueryWrapper<Translation> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//            if (GeneralTool.isNotEmpty(translationVo)) {
//                if (GeneralTool.isNotEmpty(translationVo.getFkTableName())) {
//                    lambdaQueryWrapper.eq(Translation::getFkTableName, translationVo.getFkTableName());
//                }
//                if (GeneralTool.isNotEmpty(translationVo.getFkTableId())) {
//                    lambdaQueryWrapper.eq(Translation::getFkTableId, translationVo.getFkTableId());
//                }
//                if (GeneralTool.isNotEmpty(translationVo.getLanguageCode())) {
//                    lambdaQueryWrapper.eq(Translation::getLanguageCode, translationVo.getLanguageCode());
//                }
//                if (GeneralTool.isNotEmpty(translationVo.getFkTranslationMappingId())) {
//                    lambdaQueryWrapper.eq(Translation::getFkTranslationMappingId, translationVo.getFkTranslationMappingId());
//                }
//            }
//            int i = 0;
//            if (translationVo.getFkTableName().equals(TableEnum.PRIVATE_POLICY.key)) {
//                i = privacyPolicyTranslationMapper.delete(lambdaQueryWrapper);
//            } else if (translationVo.getFkTableName().equals(TableEnum.APP_INSTITUTION_CHARACTER_ITEM.key) ||
//                    translationVo.getFkTableName().equals(TableEnum.U_STUDENT_ATTACHMENT.key) ||
//                    translationVo.getFkTableName().equals(TableEnum.U_APP_FORM_DIVISION.key)) {
//                i = issueTranslationMapper.delete(lambdaQueryWrapper);
//            }
////            else {
////                i = translationMapper.delete(lambdaQueryWrapper);
////            }
//            if (i < 0) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("delete_fail"));
//            }
//            //ISSUE语言判断
//            boolean issueFlag = translationVo.getFkTableName().equals(TableEnum.APP_INSTITUTION_CHARACTER_ITEM.key) ||
//                    translationVo.getFkTableName().equals(TableEnum.U_STUDENT_ATTACHMENT.key) ||
//                    translationVo.getFkTableName().equals(TableEnum.U_APP_FORM_DIVISION.key);
//
//            Translation translation = BeanCopyUtils.objClone(translationVo, Translation::new);
//            utilService.updateUserInfoToEntity(translation);
//            if (translationVo.getFkTableName().equals(TableEnum.PRIVATE_POLICY.key)) {
//                privacyPolicyTranslationMapper.insert(translation);
//            } else if (issueFlag) {
//                issueTranslationMapper.insert(translation);
//            } else {
////                translationMapper.insert(translation);
//            }
//        }
//    }

//    private Map<String, Object> fromJavaBean(String fkTableName, Long fkTableId) {
//        if (GeneralTool.isEmpty(fkTableName) || GeneralTool.isEmpty(fkTableId)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("search_vo_null"));
//        }
//        Map<String, Object> map = null;
//        //111111待修改，原来是根据clomu类获取字段名称的
//        if (fkTableName.equals(TableEnum.AGENT_MODULE_INFO.key)) {
//            AgentModuleInfo agentModuleInfo = agentModuleInfoMapper.selectById(fkTableId);
//            if (GeneralTool.isEmpty(agentModuleInfo)) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//            }
//            map = BeanCopyUtils.setConditionMap(agentModuleInfo);
//        } else if (fkTableName.equals(TableEnum.AGENT_INFO.key)) {
//            AgentInfo agentInfo = agentInfoMapper.selectById(fkTableId);
//            if (GeneralTool.isEmpty(agentInfo)) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//            }
//            map = BeanCopyUtils.setConditionMap(agentInfo);
//        } else if (fkTableName.equals(TableEnum.APP_INSTITUTION_CHARACTER_ITEM.key)) {
//            AppInstitutionCharacterItem appInstitutionCharacterItem = appInstitutionCharacterItemMapper.selectById(fkTableId);
//            if (GeneralTool.isEmpty(appInstitutionCharacterItem)) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//            }
//            map = BeanCopyUtils.setConditionMap(appInstitutionCharacterItem);
//        } else if (fkTableName.equals(TableEnum.PRIVATE_POLICY.key)) {
//            PrivacyPolicy privacyPolicy = privacyPolicyMapper.selectById(fkTableId);
//            if (GeneralTool.isEmpty(privacyPolicy)) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//            }
//            map = BeanCopyUtils.setConditionMap(privacyPolicy);
//        } else if (fkTableName.equals(TableEnum.U_STUDENT_ATTACHMENT.key)) {
//            StudentAttachment studentAttachment = studentAttachmentMapper.selectById(fkTableId);
//            if (GeneralTool.isEmpty(studentAttachment)) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//            }
//            map = BeanCopyUtils.setConditionMap(studentAttachment);
//        } else if (fkTableName.equals(TableEnum.U_APP_FORM_DIVISION.key)) {
//            AppFormDivision appFormDivision = appFormDivisionMapper.selectById(fkTableId);
//            if (GeneralTool.isEmpty(appFormDivision)) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//            }
//            map = BeanCopyUtils.setConditionMap(appFormDivision);
//        } else if (fkTableName.equals(TableEnum.M_SITEMAP.key)) {
//            Sitemap sitemap = sitemapMapper.selectById(fkTableId);
//            if (GeneralTool.isEmpty(sitemap)) {
//                throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
//            }
//            map = BeanCopyUtils.setConditionMap(sitemap);
//        }
//        return map;
//    }
}
