package com.get.platformconfigcenter.controller;

import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.tool.api.Result;
import com.get.platformconfigcenter.entity.Notice;
import com.get.platformconfigcenter.service.IDynamicService;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 多数据源测试
 */
@RestController
@AllArgsConstructor
@RequestMapping("dynamic")
@Api(value = "多数据源测试", tags = "多数据源测试")
public class DemoDynamicController {

    private IDynamicService DemodynamicService;

    /**
     * master列表
     */
    @GetMapping("/master-list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "master测试列表", notes = "master列表")
    @VerifyLogin(IsVerify = false)
    public Result<List<Notice>> masterList() {
        List<Notice> list = DemodynamicService.masterList();
        return Result.data(list);
    }

    /**
     * 其他数据源列表
     */
    @GetMapping("/slave-list")
    @ApiOperationSupport(order = 1)
    @ApiOperation(value = "其他数据源测试列表", notes = "其他数据源列表")
    public Result<List<Notice>> slaveList() {
        List<Notice> list = DemodynamicService.slaveList();
        return Result.data(list);
    }

}
