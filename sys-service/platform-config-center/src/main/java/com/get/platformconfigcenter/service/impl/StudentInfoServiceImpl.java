package com.get.platformconfigcenter.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.get.common.eunms.ProjectExtraEnum;
import com.get.common.result.Page;
import com.get.common.utils.BeanCopyUtils;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.mybatis.base.UtilService;
import com.get.core.mybatis.support.GetCondition;
import com.get.core.mybatis.utils.PageUtil;
import com.get.core.tool.api.Result;
import com.get.core.tool.utils.GeneralTool;
import com.get.institutioncenter.feign.IInstitutionCenterClient;
import com.get.platformconfigcenter.dao.appissue.StudentInfoMapper;
import com.get.platformconfigcenter.dto.StudentInfoDto;
import com.get.platformconfigcenter.entity.StudentInfo;
import com.get.platformconfigcenter.service.StudentInfoService;
import com.get.platformconfigcenter.vo.StudentInfoVo;
import com.get.salecenter.feign.ISaleCenterClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by Jerry.
 * Time: 17:27
 * Date: 2021/7/9
 * Description:学生信息管理实现类
 */
@Service
public class StudentInfoServiceImpl implements StudentInfoService {
    @Resource
    private StudentInfoMapper studentInfoMapper;
    @Resource
    private UtilService utilService;
    @Resource
    private IInstitutionCenterClient institutionCenterClient;
    @Resource
    private ISaleCenterClient saleCenterClient;

    @Override
    public List<StudentInfoVo> getStudentInfoList(StudentInfoDto studentInfoDto, Page page) {
//        PageHelper.startPage(page.getCurrentPage(), page.getShowCount());
//        List<StudentInfoVo> studentInfos = studentInfoMapper.getStudentInfo(studentInfoDto);
//        page.restPage(studentInfos);
        IPage<StudentInfoVo> pages = GetCondition.getPage(PageUtil.convertToQuery(page.getCurrentPage(), page.getShowCount()));
        List<StudentInfoVo> studentInfos = studentInfoMapper.getStudentInfo(pages, studentInfoDto);
        page.setAll((int) pages.getTotal());//111111 这里需要测试能不能返回total
        List<StudentInfoVo> studentInfoVos = new ArrayList<>();

        if (GeneralTool.isEmpty(studentInfos)) {
            return studentInfoVos;
        }

        //代理ids
        Set<Long> agentIds = studentInfos.stream().map(StudentInfoVo::getFkAgentId).collect(Collectors.toSet());
        //根据代理ids获取名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentIds)) {
            //111111
            Result<Map<Long, String>> resultagentNamesByIds = saleCenterClient.getAgentNamesByIds(agentIds);
            if (resultagentNamesByIds.isSuccess()) {
                agentNamesByIds = resultagentNamesByIds.getData();
            }
        }

        //国家ids
        Set<Long> countryIds = new HashSet<>();
        //州省ids
        Set<Long> stateIds = new HashSet<>();
        //城市ids
        Set<Long> cityIds = new HashSet<>();
        //目标国家ids
        Set<Long> targetCountryIds = new HashSet<>();
        for (StudentInfoVo studentInfo : studentInfos) {
            if (GeneralTool.isNotEmpty(studentInfo.getFkStudentAppCountryIdFrom())) {
                //有可能存在不是id的情况
                try {
                    countryIds.add(Long.valueOf(studentInfo.getFkStudentAppCountryIdFrom()));
                } catch (Exception e) {
                }
            }
            if (GeneralTool.isNotEmpty(studentInfo.getFkAreaStateIdFrom())) {
                //有可能存在不是id的情况
                try {
                    stateIds.add(Long.valueOf(studentInfo.getFkAreaStateIdFrom()));
                } catch (Exception e) {
                }
            }
            if (GeneralTool.isNotEmpty(studentInfo.getFkAreaCityIdFrom())) {
                //有可能存在不是id的情况
                try {
                    cityIds.add(Long.valueOf(studentInfo.getFkAreaCityIdFrom()));
                } catch (Exception e) {
                }
            }
            if (GeneralTool.isNotEmpty(studentInfo.getTargetCountryId())) {
                String[] split = studentInfo.getTargetCountryId().split(",");
                for (String s : split) {
                    targetCountryIds.add(Long.valueOf(s));
                }
            }
        }

        //111111
        //根据国家ids获取名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> resultcountryNamesByIds = saleCenterClient.getNamesByStudentAppCountryIds(countryIds);
            if (resultcountryNamesByIds.isSuccess()) {
                countryNamesByIds = resultcountryNamesByIds.getData();
            }
        }
        //根据州省ids获取名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> resultstateNamesByIds = institutionCenterClient.getStateNamesByIds(stateIds);
            if (resultstateNamesByIds.isSuccess()) {
                stateNamesByIds = resultstateNamesByIds.getData();
            }
        }
        //根据城市ids获取名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> resultcityNamesByIds = institutionCenterClient.getCityNamesByIds(cityIds);
            if (resultcityNamesByIds.isSuccess()) {
                cityNamesByIds = resultcityNamesByIds.getData();
            }
        }
        //根据目标国家国家ids获取名称
        Map<Long, String> targetCountryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(targetCountryIds)) {
            Result<Map<Long, String>> resulttargetCountryNamesByIds = institutionCenterClient.getCountryNamesByIds(targetCountryIds);
            if (resulttargetCountryNamesByIds.isSuccess()) {
                targetCountryNamesByIds = resulttargetCountryNamesByIds.getData();
            }
        }

        for (StudentInfoVo studentInfoVo : studentInfos) {
            setStudentDtoValue(studentInfoVo, agentNamesByIds, countryNamesByIds, stateNamesByIds, cityNamesByIds, targetCountryNamesByIds);
            studentInfoVos.add(studentInfoVo);
        }
        return studentInfoVos;
    }

    @Override
    public void updateStudentInfoStatus(StudentInfoDto studentInfoDto) {
        if (GeneralTool.isEmpty(studentInfoDto.getId())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        if (GeneralTool.isEmpty(studentInfoDto.getStatus())) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("the_status_cannot_be_empty"));
        }
        StudentInfo checkStudentInfo = studentInfoMapper.selectById(studentInfoDto.getId());
        if (GeneralTool.isEmpty(checkStudentInfo)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        StudentInfo studentInfo = BeanCopyUtils.objClone(studentInfoDto, StudentInfo::new);
        utilService.updateUserInfoToEntity(studentInfo);
        studentInfoMapper.updateStudentStatusByStudentId(studentInfo);
    }

    @Override
    public StudentInfoVo findStudentInfoById(Long id) {
        if (GeneralTool.isEmpty(id)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("id_null"));
        }
        StudentInfoDto studentInfoDto = new StudentInfoDto();
        //只根据学生ID查询
        studentInfoDto.setId(id);
        List<StudentInfoVo> studentInfoVos = studentInfoMapper.getStudentInfo(null, studentInfoDto);
        if (GeneralTool.isEmpty(studentInfoVos)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        StudentInfoVo studentInfoVo = studentInfoVos.get(0);

        //代理ids
        Set<Long> agentIds = new HashSet<>();
        agentIds.add(studentInfoVo.getFkAgentId());
        //根据代理ids获取名称
        Map<Long, String> agentNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(agentIds)) {
            Result<Map<Long, String>> resultagentNamesByIds = saleCenterClient.getAgentNamesByIds(agentIds);//111111
            if (resultagentNamesByIds.isSuccess()) {
                agentNamesByIds = resultagentNamesByIds.getData();
            }
        }

        //国家ids
        Set<Long> countryIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(studentInfoVo.getFkStudentAppCountryIdFrom())) {
            //有可能存在不是id的情况
            try {
                countryIds.add(Long.valueOf(studentInfoVo.getFkStudentAppCountryIdFrom()));
            } catch (Exception e) {
            }
        }
        //州省ids
        Set<Long> stateIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(studentInfoVo.getFkAreaStateIdFrom())) {
            //有可能存在不是id的情况
            try {
                stateIds.add(Long.valueOf(studentInfoVo.getFkAreaStateIdFrom()));
            } catch (Exception e) {
            }
        }
        //城市ids
        Set<Long> cityIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(studentInfoVo.getFkAreaCityIdFrom())) {
            //有可能存在不是id的情况
            try {
                cityIds.add(Long.valueOf(studentInfoVo.getFkAreaCityIdFrom()));
            } catch (Exception e) {
            }
        }
        //目标国家ids
        Set<Long> targetCountryIds = new HashSet<>();
        if (GeneralTool.isNotEmpty(studentInfoVo.getTargetCountryId())) {
            String[] split = studentInfoVo.getTargetCountryId().split(",");
            for (String s : split) {
                targetCountryIds.add(Long.valueOf(s));
            }
        }
        //111111

        //根据国家ids获取名称
        Map<Long, String> countryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(countryIds)) {
            Result<Map<Long, String>> resultcountryNamesByIds = institutionCenterClient.getCountryNamesByIds(countryIds);
            if (resultcountryNamesByIds.isSuccess()) {
                countryNamesByIds = resultcountryNamesByIds.getData();
            }
        }
        //根据州省ids获取名称
        Map<Long, String> stateNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(stateIds)) {
            Result<Map<Long, String>> resultstateNamesByIds = institutionCenterClient.getStateNamesByIds(stateIds);
            if (resultstateNamesByIds.isSuccess()) {
                stateNamesByIds = resultstateNamesByIds.getData();
            }
        }
        //根据城市ids获取名称
        Map<Long, String> cityNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(cityIds)) {
            Result<Map<Long, String>> resultcityNamesByIds = institutionCenterClient.getCityNamesByIds(cityIds);
            if (resultcityNamesByIds.isSuccess()) {
                cityNamesByIds = resultcityNamesByIds.getData();
            }
        }
        //根据目标国家国家ids获取名称
        Map<Long, String> targetCountryNamesByIds = new HashMap<>();
        if (GeneralTool.isNotEmpty(targetCountryIds)) {
            Result<Map<Long, String>> resulttargetCountryNamesByIds = institutionCenterClient.getCountryNamesByIds(targetCountryIds);
            if (resulttargetCountryNamesByIds.isSuccess()) {
                targetCountryNamesByIds = resulttargetCountryNamesByIds.getData();
            }
        }

        setStudentDtoValue(studentInfoVo, agentNamesByIds, countryNamesByIds, stateNamesByIds, cityNamesByIds, targetCountryNamesByIds);
        return studentInfoVo;
    }

    @Override
    public List<Map<String, Object>> agentSelectDatas() {
//        Example example = new Example(StudentInfo.class);
//        //查询代理ID不为空的数据
//        example.createCriteria().andIsNotNull("fkAgentId");
//        //查询出符合条件的所有学生信息
//        List<StudentInfo> studentInfos = studentInfoMapper.selectByExample(example);

        List<StudentInfo> studentInfos = studentInfoMapper.selectList(Wrappers.<StudentInfo>lambdaQuery().isNotNull(StudentInfo::getFkAgentId));
        //将学生关联的代理ID全部取出
        Set<Long> agentIds = studentInfos.stream().map(StudentInfo::getFkAgentId).filter(Objects::nonNull).collect(Collectors.toSet());
        //111111
        Map<Long, String> agentNamesByIds = new HashMap<>();
        Result<Map<Long, String>> resultagentNamesByIds = saleCenterClient.getAgentNamesByIds(agentIds);
        if (resultagentNamesByIds.isSuccess()) {
            agentNamesByIds = resultagentNamesByIds.getData();
        }
        //去掉key值或value值为空的数据
        agentNamesByIds.keySet().removeIf(GeneralTool::isEmpty);
        agentNamesByIds.values().removeIf(GeneralTool::isEmpty);
        List<Map<String, Object>> list = new ArrayList<>();
        for (Map.Entry<Long, String> longStringEntry : agentNamesByIds.entrySet()) {
            Map<String, Object> map = new HashMap();
            map.put("id", longStringEntry.getKey());
            map.put("name", longStringEntry.getValue());
            list.add(map);
        }
        return list;
    }


    /**
     * 根据目标国家ID获取目标国家名称
     *
     * @param targetCountryIds
     * @return
     */
    private String getTargetCountryNames(String targetCountryIds, Map<Long, String> targetCountryNamesByIds) {
        if (GeneralTool.isEmpty(targetCountryIds) || GeneralTool.isEmpty(targetCountryNamesByIds)) {
            return null;
        }
        String[] split = targetCountryIds.split(",");
        StringJoiner sj = new StringJoiner(",");
        for (String s : split) {
            if (GeneralTool.isNotEmpty(targetCountryNamesByIds.get(Long.valueOf(s)))) {
                sj.add(targetCountryNamesByIds.get(Long.valueOf(s)));
            }
        }
        return sj.toString();
    }


    /**
     * 处理学生信息返回类的返回值
     *
     * @param studentInfoVo
     */
    private void setStudentDtoValue(StudentInfoVo studentInfoVo, Map<Long, String> agentNamesByIds,
                                    Map<Long, String> countryNamesByIds, Map<Long, String> stateNamesByIds,
                                    Map<Long, String> cityNamesByIds, Map<Long, String> targetCountryNamesByIds) {
        if (GeneralTool.isNotEmpty(studentInfoVo.getFirstName()) && GeneralTool.isNotEmpty(studentInfoVo.getLastName())) {
            studentInfoVo.setNameEn(studentInfoVo.getFirstName() + studentInfoVo.getLastName());
        } else if (GeneralTool.isNotEmpty(studentInfoVo.getFirstName())) {
            studentInfoVo.setNameEn(studentInfoVo.getFirstName());
        } else if (GeneralTool.isNotEmpty(studentInfoVo.getLastName())) {
            studentInfoVo.setNameEn(studentInfoVo.getLastName());
        }

        //获取目标国家-根据国家ids查找国家名称map
        String targetCountryNames = getTargetCountryNames(studentInfoVo.getTargetCountryId(), targetCountryNamesByIds);
        studentInfoVo.setTargetCountryName(targetCountryNames);
        //处理代理名称
        if (GeneralTool.isNotEmpty(studentInfoVo.getFkAgentId())) {
            String agentNameById = agentNamesByIds.get(studentInfoVo.getFkAgentId());
            studentInfoVo.setFkAgentName(agentNameById);
        }
        //处理学生所属国家
        if (GeneralTool.isNotEmpty(studentInfoVo.getFkStudentAppCountryIdFrom())) {
            //有可能存在不是id的情况
            try {
                String countryNameById = countryNamesByIds.get(Long.valueOf(studentInfoVo.getFkStudentAppCountryIdFrom()));
                studentInfoVo.setFkStudentAppCountryIdFromName(countryNameById);
            } catch (Exception e) {
                studentInfoVo.setFkStudentAppCountryIdFromName(studentInfoVo.getFkStudentAppCountryIdFrom());
            }
        }
        //处理学生所属州省
        if (GeneralTool.isNotEmpty(studentInfoVo.getFkAreaStateIdFrom())) {
            //有可能存在不是id的情况
            try {
                String stateNameById = stateNamesByIds.get(Long.valueOf(studentInfoVo.getFkAreaStateIdFrom()));
                studentInfoVo.setFkAreaStateIdFromName(stateNameById);
            } catch (Exception e) {
                studentInfoVo.setFkAreaStateIdFromName(studentInfoVo.getFkAreaStateIdFrom());
            }
        }
        //处理学生所属城市
        if (GeneralTool.isNotEmpty(studentInfoVo.getFkAreaCityIdFrom())) {
            //有可能存在不是id的情况
            try {
                String cityNameById = cityNamesByIds.get(Long.valueOf(studentInfoVo.getFkAreaCityIdFrom()));
                studentInfoVo.setFkAreaCityIdFromName(cityNameById);
            } catch (Exception e) {
                studentInfoVo.setFkAreaCityIdFromName(studentInfoVo.getFkAreaCityIdFrom());
            }
        }
        //处理性别
        if (GeneralTool.isNotEmpty(studentInfoVo.getGender()) && studentInfoVo.getGender() == 0) {
            studentInfoVo.setGenderName("女");
        } else if (GeneralTool.isNotEmpty(studentInfoVo.getGender()) && studentInfoVo.getGender() == 1) {
            studentInfoVo.setGenderName("男");
        }
        //处理状态值
        if (GeneralTool.isNotEmpty(studentInfoVo.getStatus())) {
            studentInfoVo.setStatusName(ProjectExtraEnum.getValueByKey(studentInfoVo.getStatus(), ProjectExtraEnum.STUDENT_INFO_STATUS));
        }
    }
}
