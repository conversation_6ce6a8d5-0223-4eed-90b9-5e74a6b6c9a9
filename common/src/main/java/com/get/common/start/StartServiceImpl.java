package com.get.common.start;

import com.get.common.constant.LauncherStart;
import com.get.core.auto.service.AutoService;
import com.get.core.start.constant.AppConstant;
import com.get.core.start.constant.NacosConstant;
import com.get.core.start.service.StartService;
import com.get.core.start.utils.PropsUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.builder.SpringApplicationBuilder;

import java.io.InputStream;
import java.util.Optional;
import java.util.Properties;

@Slf4j
@AutoService(StartService.class)
public class StartServiceImpl implements StartService {

    private String profile;

    private String nacosServerAddr;

    private String nacosConfigServerAddr;


    {
        try {
            Properties props = System.getProperties();
            InputStream in = this.getClass().getClassLoader().getResourceAsStream("von.properties");
            if (in != null) {
                props.load(in);
                in.close();

                profile = props.getProperty("von.local.profile");
                nacosServerAddr = props.getProperty("von.nacos.discovery.server-addr");
                nacosConfigServerAddr = props.getProperty("von.nacos.config.server-addr");

                log.info("成功从von.properties加载配置");

                props.setProperty("spring.profiles.active", profile);
                props.setProperty("get.env", profile);
                if (!profile.equals("prod") && !profile.equals("gray") && !profile.equals("iae") && !profile.equals("tw")) {
                    props.setProperty("get.dev-mode", "true");
                } else {
                    props.setProperty("get.dev-mode", "false");
                }
            } else {
                log.warn("未找到von.properties文件");
            }
        } catch (Exception e) {
            log.error("加载von.properties失败", e);
        }

        Properties props = System.getProperties();
        String nacosAddr = Optional.ofNullable(nacosServerAddr).orElse(LauncherStart.nacosAddr(profile));
        PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.server-addr", nacosAddr);
        PropsUtil.setProperty(props, "spring.cloud.nacos.config.server-addr", Optional.of(nacosConfigServerAddr).orElse(LauncherStart.nacosAddr(profile)));
        PropsUtil.setProperty(props, "mybatis-plus.configuration.log-impl", "org.apache.ibatis.logging.stdout.StdOutImpl");
    }

    @Override
    public void launcher(SpringApplicationBuilder builder, String appName, String profile, boolean isLocalDev) {
        Properties defaultProperties = new Properties();
        profile = Optional.ofNullable(this.profile).orElse(profile);

        builder.properties(defaultProperties);
        defaultProperties.setProperty("spring.cloud.nacos.config.shared-configs[1].data-id", NacosConstant.sharedDataId(this.profile));
        builder.properties(defaultProperties);
        Properties props = System.getProperties();

        // 通用注册
        PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.server-addr", LauncherStart.nacosAddr(profile));
        PropsUtil.setProperty(props, "spring.cloud.nacos.config.server-addr", LauncherStart.nacosAddr(profile));
        PropsUtil.setProperty(props, "spring.cloud.sentinel.transport.dashboard", LauncherStart.sentinelAddr(profile));
        PropsUtil.setProperty(props, "spring.zipkin.base-url", LauncherStart.zipkinAddr(profile));
//        PropsUtil.setProperty(props, "spring.cloud.nacos.username", "nacos");
//        PropsUtil.setProperty(props, "spring.cloud.nacos.password", "nacos");

        PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[0].data-id", NacosConstant.sharedDataId());
        PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[0].refresh", NacosConstant.NACOS_CONFIG_REFRESH);
        PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[1].data-id", NacosConstant.sharedDataId(profile));
        PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[1].refresh", NacosConstant.NACOS_CONFIG_REFRESH);
        PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[0].group", NacosConstant.NACOS_CONFIG_DEFAULT_GROUP);
        PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[1].group", NacosConstant.NACOS_CONFIG_DEFAULT_GROUP);
        PropsUtil.setProperty(props,"spring.cloud.nacos.config.ext-config[0].data-id",NacosConstant.dataId(appName,profile));

        if (profile.equals(AppConstant.TEST_CODE)) {
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.namespace", NacosConstant.NACOS_NAMESPACE_TEST);
            PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.namespace", NacosConstant.NACOS_NAMESPACE_TEST);
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.group", NacosConstant.NACOS_NAMESPACE_TEST);
            PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.group", NacosConstant.NACOS_NAMESPACE_TEST);
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[0].group", NacosConstant.NACOS_NAMESPACE_TEST);
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[1].group", NacosConstant.NACOS_NAMESPACE_TEST);
        }else if (profile.equals(AppConstant.GRAY_CODE)) {
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.namespace", NacosConstant.NACOS_NAMESPACE_GRAY);
            PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.namespace", NacosConstant.NACOS_NAMESPACE_GRAY);
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.group", NacosConstant.NACOS_NAMESPACE_GRAY);
            PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.group", NacosConstant.NACOS_NAMESPACE_GRAY);
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[0].group", NacosConstant.NACOS_NAMESPACE_GRAY);
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[1].group", NacosConstant.NACOS_NAMESPACE_GRAY);
        }else if (profile.equals(AppConstant.LOCAL_CODE)) {
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.namespace", NacosConstant.NACOS_NAMESPACE_LOCAL);
            PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.namespace", NacosConstant.NACOS_NAMESPACE_LOCAL);
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.group", NacosConstant.NACOS_NAMESPACE_LOCAL);
            PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.group", NacosConstant.NACOS_NAMESPACE_LOCAL);
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[0].group", NacosConstant.NACOS_NAMESPACE_LOCAL);
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[1].group", NacosConstant.NACOS_NAMESPACE_LOCAL);
        }else{
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[0].group", NacosConstant.NACOS_NAMESPACE_DEV);
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[1].group", NacosConstant.NACOS_NAMESPACE_DEV);
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.namespace", NacosConstant.NACOS_NAMESPACE_DEV);
            PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.namespace", NacosConstant.NACOS_NAMESPACE_DEV);
            PropsUtil.setProperty(props, "spring.cloud.nacos.config.group", NacosConstant.NACOS_NAMESPACE_DEV);
            PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.group", NacosConstant.NACOS_NAMESPACE_DEV);
        }



//        //如为灰度环境则使用灰度分组
//        if (profile.equals(AppConstant.GRAY_CODE)) {
//            PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[0].group", NacosConstant.NACOS_CONFIG_GRAY_GROUP);
//            PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[1].group", NacosConstant.NACOS_CONFIG_GRAY_GROUP);
//
////			PropsUtil.setProperty(props,"spring.cloud.nacos.config.ext-config[0].data-id",NacosConstant.dataId("get",profile));
////			PropsUtil.setProperty(props,"spring.cloud.nacos.config.ext-config[0].group",NacosConstant.NACOS_CONFIG_GRAY_GROUP);
////			PropsUtil.setProperty(props,"spring.cloud.nacos.config.ext-config[0].refresh",NacosConstant.NACOS_CONFIG_REFRESH);
//
//            PropsUtil.setProperty(props, "spring.cloud.nacos.config.namespace", "efcd69b7-ddab-4ca4-8f24-76ce976e135a");
//            PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.namespace", "efcd69b7-ddab-4ca4-8f24-76ce976e135a");
//            PropsUtil.setProperty(props, "spring.cloud.nacos.config.group", NacosConstant.NACOS_CONFIG_GRAY_GROUP);
//            PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.group", NacosConstant.NACOS_CONFIG_GRAY_GROUP);
//        } else {
//            PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[0].group", NacosConstant.NACOS_CONFIG_DEFAULT_GROUP);
//            PropsUtil.setProperty(props, "spring.cloud.nacos.config.shared-configs[1].group", NacosConstant.NACOS_CONFIG_DEFAULT_GROUP);
//        }
        System.out.println("================nacos配置地址：================>" + LauncherStart.nacosAddr(profile));
        System.out.println("================配置信息：================>" + props.toString());


        //生产环境和IAE环境elk写入日志
        if (profile.equals(AppConstant.PROD_CODE) || profile.equals(AppConstant.IAE_CODE)) {
            // 开启elk日志
            PropsUtil.setProperty(props, "get.log.elk.destination", LauncherStart.elkAddr(profile));
        } else {
            //内网使用简单的file模式，只需要启动seata服务即可
            PropsUtil.setProperty(props, "seata.registry.type", "file");
            PropsUtil.setProperty(props, "seata.config.type", "file");
            // seata注册地址
            PropsUtil.setProperty(props, "seata.service.grouplist.default", LauncherStart.seataAddr(profile));
            PropsUtil.setProperty(props, "seata.tx-service-group", "get-tx-group");
            PropsUtil.setProperty(props, "seata.service.vgroup-mapping.get-tx-group", "default");
        }
    }

}
