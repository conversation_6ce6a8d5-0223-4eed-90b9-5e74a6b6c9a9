package com.get.common.cache;

/**
 * 通用缓存名
 */
public interface CacheNames {

    String SYS_CACHE = "get:sys";//系统缓存

    String BIZ_CACHE = "get:biz";//业务缓存

    String MENU_CACHE = "get:menu";//菜单缓存

    String USER_CACHE = "get:user";//用户缓存

    String USER_AUTH = "get:auth";//认证缓存

    String TASK_CACHE = "get:task"; //任务缓存

    String STAFF_FOLLOWER_IDS_CACHE = "get:staffFollowerIds";//员工及下属关系缓存

    String STAFF_NAME_CACHE = "get:staffName";//员工姓名

    String INVOICE_ADD = "get:invoiceAdd";

    String ALL_STAFF_IDS_CACHE = "get:allStaffIds";//所有员工的ID集合

    /**
     * 验证码key
     */
    String CAPTCHA_KEY = USER_AUTH + "::get:captcha:";

    /**
     * 登录失败key
     */
    String USER_FAIL_KEY = USER_CACHE + "::get:fail:";
    String API_KEYS_CODE = "apiKeys:code:";
    String API_RESOURCE_KEYS_CODE = "apiResourceKeys:code:";
    String STAFF_COMPANY_IDS_CODE = "staffCompanyIds:code:";
    String STAFF_COUNTRY_IDS_CODE = "staffCountryIds:code:";
    String STAFF_INFO_CODE = "staffInfo:code:";
    String TRANSLATION_CODE = "translation:code:";

    /**
     * 合同续期配置
     */
    String AGENT_CONTRACT_CONFIG_CODE = "agentContractConfig:code:";
    /**
     * 参会流程配置缓存key
     */
    String CONVENTION_PROCEDURE_CONFIGURATION = "get:conventionConfiguration";

    /**
     * 微信推送ticket
     */
   String SUITE_TICKET_KEY = "wx:cptp:suite_ticket";

    /**
     * 第三方应用凭证 2h （key按应用区分）
     */
    String SUITE_ACCESS_TOKEN_KEY = "wx:cptp:suite_access_token";

    /**
     * H5应用调用企业微信JS接口的临时票据 2h (跟着商家企业Id走)
     */
    String JS_API_TICKET_KEY = "wx:cptp:jsapi_ticket:";

    /**
     * 企业access_token
     */
    String ACCESS_TOKEN_KEY = "wx:cptp:access_token";

    /**
     * 奖励策略缓存名
     */
    String INCENTIVE_POLICY_KEY = BIZ_CACHE+"incentivePolicy:key";

    /**
     * 邮件统计缓存名
     */
    String EMAIL_STATISTICS_KEY = BIZ_CACHE+"emailStatistics:key";

    /**
     * 批量匹配邮件缓存名
     */
    String BATCH_MATCH_EMAIL_KEY = BIZ_CACHE+"batchMatchEmail:key";

    /**
     * 返回拼接后的key
     *
     * @param cacheKey      缓存key
     * @param cacheKeyValue 缓存key值
     * @return tenantKey
     */
    static String cacheKey(String cacheKey, String cacheKeyValue) {
        return cacheKey.concat(cacheKeyValue);
    }

}
