package com.get.common.query.exception;

import lombok.Getter;

/**
 * 动态查询基础异常类
 * 所有动态查询相关异常的父类
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Getter
public class DynamicQueryException extends RuntimeException {

    private final String errorCode;
    private final Object[] params;

    public DynamicQueryException(String message) {
        super(message);
        this.errorCode = "DYNAMIC_QUERY_ERROR";
        this.params = new Object[0];
    }

    public DynamicQueryException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "DYNAMIC_QUERY_ERROR";
        this.params = new Object[0];
    }

    public DynamicQueryException(String errorCode, String message, Object... params) {
        super(message);
        this.errorCode = errorCode;
        this.params = params != null ? params : new Object[0];
    }

    public DynamicQueryException(String errorCode, String message, Throwable cause, Object... params) {
        super(message, cause);
        this.errorCode = errorCode;
        this.params = params != null ? params : new Object[0];
    }

    /**
     * 获取详细的错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("[").append(errorCode).append("] ").append(getMessage());
        
        if (params.length > 0) {
            sb.append(" - 参数: ");
            for (int i = 0; i < params.length; i++) {
                if (i > 0) sb.append(", ");
                sb.append(params[i]);
            }
        }
        
        return sb.toString();
    }
}