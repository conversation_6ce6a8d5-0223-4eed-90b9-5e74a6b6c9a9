package com.get.common.query;

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(
    name = "dynamic.query.enabled", 
    havingValue = "true", 
    matchIfMissing = false
)
@ComponentScan(basePackages = "com.get.common.query")
public class DynamicQueryConfiguration {
}
