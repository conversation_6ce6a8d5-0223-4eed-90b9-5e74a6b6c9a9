package com.get.common.query.enums;

/**
 * 查询类型枚举
 * 定义支持的查询条件类型
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
public enum QueryType {
    
    /**
     * 等于查询
     * SQL: column = value
     */
    EQ,
    
    /**
     * 不等于查询
     * SQL: column != value
     */
    NE,
    
    /**
     * 模糊查询（两边模糊）
     * SQL: column LIKE '%value%'
     */
    LIKE,
    
    /**
     * 左模糊查询
     * SQL: column LIKE '%value'
     */
    LIKE_LEFT,
    
    /**
     * 右模糊查询
     * SQL: column LIKE 'value%'
     */
    LIKE_RIGHT,
    
    /**
     * 大于查询
     * SQL: column > value
     */
    GT,
    
    /**
     * 大于等于查询
     * SQL: column >= value
     */
    GE,
    
    /**
     * 小于查询
     * SQL: column < value
     */
    LT,
    
    /**
     * 小于等于查询
     * SQL: column <= value
     */
    LE,
    
    /**
     * IN查询
     * SQL: column IN (value1, value2, ...)
     * 适用于Collection类型的字段
     */
    IN,
    
    /**
     * NOT IN查询
     * SQL: column NOT IN (value1, value2, ...)
     * 适用于Collection类型的字段
     */
    NOT_IN,
    
    /**
     * 为空查询
     * SQL: column IS NULL
     */
    IS_NULL,
    
    /**
     * 不为空查询
     * SQL: column IS NOT NULL
     */
    IS_NOT_NULL,
    
    /**
     * 范围查询
     * SQL: column BETWEEN value1 AND value2
     * 适用于List类型的字段，List必须包含两个元素
     */
    BETWEEN,
    
    /**
     * 不在范围内查询
     * SQL: column NOT BETWEEN value1 AND value2
     */
    NOT_BETWEEN,
    
    /**
     * 排序字段（不生成WHERE条件，用于ORDER BY）
     */
    ORDER_BY,
    
    /**
     * 忽略该字段（不生成任何SQL条件）
     */
    IGNORE
}