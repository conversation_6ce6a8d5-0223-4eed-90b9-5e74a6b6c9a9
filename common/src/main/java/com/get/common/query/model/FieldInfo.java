package com.get.common.query.model;

import com.get.common.query.annotation.QueryField;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.lang.reflect.Field;

/**
 * 字段信息封装类
 * 用于缓存反射获取的字段信息和注解信息
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Data
@AllArgsConstructor
public class FieldInfo {
    
    /**
     * 反射字段对象
     */
    private Field field;
    
    /**
     * 查询字段注解
     */
    private QueryField queryField;
    
    /**
     * 数据库字段名（已处理过的）
     */
    private String columnName;
    
    /**
     * 字段类型
     */
    private Class<?> fieldType;
    
    /**
     * 是否为集合类型
     */
    private boolean isCollection;
    
    /**
     * 构造函数
     */
    public FieldInfo(Field field, QueryField queryField) {
        this.field = field;
        this.queryField = queryField;
        this.fieldType = field.getType();
        this.isCollection = java.util.Collection.class.isAssignableFrom(fieldType);
        
        // 处理数据库字段名
        if (queryField.column().isEmpty()) {
            // 将驼峰命名转换为下划线命名
            this.columnName = camelToUnderscore(field.getName());
        } else {
            this.columnName = queryField.column();
        }
    }
    
    /**
     * 驼峰命名转下划线命名
     * 正确处理连续大写字母，如：XMLHttpRequest -> xml_http_request
     * 
     * @param camelCase 驼峰命名字符串
     * @return 下划线命名字符串
     */
    private String camelToUnderscore(String camelCase) {
        if (camelCase == null || camelCase.isEmpty()) {
            return camelCase;
        }
        
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < camelCase.length(); i++) {
            char current = camelCase.charAt(i);
            
            // 第一个字符，直接添加小写形式
            if (i == 0) {
                result.append(Character.toLowerCase(current));
                continue;
            }
            
            char previous = camelCase.charAt(i - 1);
            
            if (Character.isUpperCase(current)) {
                // 当前字符是大写字母
                boolean needUnderscore = false;
                
                if (Character.isLowerCase(previous)) {
                    // 前一个字符是小写，需要添加下划线（如：userId -> user_id）
                    needUnderscore = true;
                } else if (Character.isUpperCase(previous) && i + 1 < camelCase.length()) {
                    // 前一个字符也是大写，检查下一个字符
                    char next = camelCase.charAt(i + 1);
                    if (Character.isLowerCase(next)) {
                        // 连续大写字母后跟小写字母，当前大写字母是新单词的开始
                        // 如：XMLHttp -> xml_http（在H前添加下划线）
                        needUnderscore = true;
                    }
                }
                
                if (needUnderscore) {
                    result.append('_');
                }
                result.append(Character.toLowerCase(current));
            } else {
                // 当前字符不是大写字母，直接添加
                result.append(current);
            }
        }
        
        return result.toString();
    }
}