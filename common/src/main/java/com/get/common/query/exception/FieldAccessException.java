package com.get.common.query.exception;

import java.lang.reflect.Field;

/**
 * 字段访问异常
 * 在通过反射访问字段时发生的异常
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
public class FieldAccessException extends DynamicQueryException {

    public FieldAccessException(String message) {
        super("FIELD_ACCESS_ERROR", message);
    }

    public FieldAccessException(String message, Throwable cause) {
        super("FIELD_ACCESS_ERROR", message, cause);
    }

    public FieldAccessException(String message, Field field, Throwable cause) {
        super("FIELD_ACCESS_ERROR", message, cause, field.getName(), field.getType().getSimpleName());
    }

    /**
     * 创建字段访问失败异常
     */
    public static FieldAccessException accessFailed(Field field, Throwable cause) {
        String message = String.format("访问字段失败 - 字段: %s, 类型: %s", 
            field.getName(), field.getType().getSimpleName());
        return new FieldAccessException(message, field, cause);
    }

    /**
     * 创建字段访问失败异常（带上下文信息）
     */
    public static FieldAccessException accessFailed(Field field, String context, Throwable cause) {
        String message = String.format("访问字段失败 - 字段: %s, 类型: %s, 上下文: %s", 
            field.getName(), field.getType().getSimpleName(), context);
        return new FieldAccessException(message, field, cause);
    }

    /**
     * 创建分组字段访问失败异常
     */
    public static FieldAccessException groupAccessFailed(Field field, String groupName, Throwable cause) {
        String message = String.format("访问分组字段失败 - 字段: %s, 分组: %s", 
            field.getName(), groupName);
        return new FieldAccessException(message, field, cause);
    }
}