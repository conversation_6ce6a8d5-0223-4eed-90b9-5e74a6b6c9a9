package com.get.common.query.exception;

import com.get.common.query.enums.QueryType;

/**
 * 参数验证异常
 * 在验证查询参数时发生的异常
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
public class ParameterValidationException extends DynamicQueryException {

    public ParameterValidationException(String message) {
        super("PARAMETER_VALIDATION_ERROR", message);
    }

    public ParameterValidationException(String message, Object... params) {
        super("PARAMETER_VALIDATION_ERROR", message, params);
    }

    /**
     * 创建集合参数类型错误异常
     */
    public static ParameterValidationException invalidCollectionType(QueryType queryType, Object value) {
        String message = String.format("%s查询参数不是集合类型", queryType);
        return new ParameterValidationException(message, queryType, value.getClass().getSimpleName());
    }

    /**
     * 创建集合参数为空异常
     */
    public static ParameterValidationException emptyCollection(QueryType queryType) {
        String message = String.format("%s查询的集合参数为空", queryType);
        return new ParameterValidationException(message, queryType);
    }

    /**
     * 创建范围参数类型错误异常
     */
    public static ParameterValidationException invalidRangeType(QueryType queryType, Object value) {
        String message = String.format("%s查询参数不是List类型", queryType);
        return new ParameterValidationException(message, queryType, value.getClass().getSimpleName());
    }

    /**
     * 创建范围参数大小错误异常
     */
    public static ParameterValidationException invalidRangeSize(QueryType queryType, int actualSize) {
        String message = String.format("%s查询参数List大小不为2", queryType);
        return new ParameterValidationException(message, queryType, actualSize);
    }

    /**
     * 创建范围参数包含null值异常
     */
    public static ParameterValidationException rangeContainsNull(QueryType queryType, Object start, Object end) {
        String message = String.format("%s查询参数包含null值", queryType);
        return new ParameterValidationException(message, queryType, start, end);
    }

    /**
     * 创建范围值类型不兼容异常
     */
    public static ParameterValidationException incompatibleRangeTypes(Object start, Object end) {
        String message = "范围值类型不兼容，无法比较大小";
        return new ParameterValidationException(message, 
            start.getClass().getSimpleName(), end.getClass().getSimpleName());
    }
}