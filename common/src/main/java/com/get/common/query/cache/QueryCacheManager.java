package com.get.common.query.cache;

import com.get.common.query.model.FieldInfo;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 查询缓存管理器
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
@Slf4j
@Component
@ConditionalOnProperty(
    name = "dynamic.query.cache.enabled",
    havingValue = "true",
    matchIfMissing = true
)
@RequiredArgsConstructor
public class QueryCacheManager {
    
    private final CacheKeyGenerator keyGenerator;
    
    /**
     * 字段信息缓存（使用字符串key代替Class key）
     */
    private final Map<String, CachedFieldInfo> fieldCache = new ConcurrentHashMap<>();
    
    /**
     * 缓存统计信息
     */
    private final Map<String, AtomicLong> stats = new ConcurrentHashMap<>();
    
    /**
     * 定时清理任务执行器
     */
    private ScheduledExecutorService cleanupExecutor;
    
    /**
     * 缓存最大大小
     */
    @Value("${dynamic.query.cache.max-size:1000}")
    private int maxCacheSize;
    
    /**
     * 缓存过期时间（小时）
     */
    @Value("${dynamic.query.cache.expire-hours:24}")
    private int expireHours;
    
    /**
     * 清理任务执行间隔（分钟）
     */
    @Value("${dynamic.query.cache.cleanup-interval:60}")
    private int cleanupIntervalMinutes;
    
    @PostConstruct
    public void init() {
        // 初始化统计信息
        initStats();
        
        // 启动定时清理任务
        startCleanupTask();
        
        log.info("查询缓存管理器初始化完成 - 最大缓存数: {}, 过期时间: {}小时, 清理间隔: {}分钟", 
            maxCacheSize, expireHours, cleanupIntervalMinutes);
    }
    
    @PreDestroy
    public void destroy() {
        if (cleanupExecutor != null && !cleanupExecutor.isShutdown()) {
            cleanupExecutor.shutdown();
            try {
                if (!cleanupExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                    cleanupExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                cleanupExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("查询缓存管理器清理任务已停止");
        }
    }
    
    /**
     * 获取字段信息
     */
    public List<FieldInfo> getFieldInfos(Class<?> clazz) {
        String cacheKey = keyGenerator.generateFieldCacheKey(clazz);
        CachedFieldInfo cachedInfo = fieldCache.get(cacheKey);
        
        if (cachedInfo == null) {
            incrementStat("cacheMiss");
            return null;
        }
        
        // 检查是否过期
        if (isExpired(cachedInfo)) {
            fieldCache.remove(cacheKey);
            incrementStat("cacheExpired");
            incrementStat("cacheMiss");
            log.debug("缓存已过期并移除 - 类: {}, key: {}", clazz.getSimpleName(), cacheKey);
            return null;
        }
        
        // 更新访问时间和访问次数
        cachedInfo.updateAccess();
        incrementStat("cacheHit");
        
        return cachedInfo.getFieldInfos();
    }
    
    /**
     * 缓存字段信息
     */
    public void cacheFieldInfos(Class<?> clazz, List<FieldInfo> fieldInfos) {
        // 检查缓存大小限制
        if (fieldCache.size() >= maxCacheSize) {
            evictLeastRecentlyUsed();
        }
        
        String cacheKey = keyGenerator.generateFieldCacheKey(clazz);
        CachedFieldInfo cachedInfo = new CachedFieldInfo(fieldInfos);
        fieldCache.put(cacheKey, cachedInfo);
        incrementStat("cachePut");
        
        log.debug("缓存字段信息 - 类: {}, key: {}, 字段数: {}", 
            clazz.getSimpleName(), cacheKey, fieldInfos.size());
    }
    
    /**
     * 清空所有缓存
     */
    public void clearAll() {
        int size = fieldCache.size();
        fieldCache.clear();
        incrementStat("cacheCleared");
        log.info("清空所有缓存 - 清理数量: {}", size);
    }
    
    /**
     * 清空指定类的缓存
     */
    public void clearClass(Class<?> clazz) {
        String cacheKey = keyGenerator.generateFieldCacheKey(clazz);
        CachedFieldInfo removed = fieldCache.remove(cacheKey);
        if (removed != null) {
            incrementStat("cacheRemoved");
            log.debug("清空指定类缓存 - 类: {}, key: {}", clazz.getSimpleName(), cacheKey);
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public Map<String, Object> getCacheStats() {
        Map<String, Object> result = new HashMap<>();
        
        // 基础统计
        result.put("cacheSize", fieldCache.size());
        result.put("maxCacheSize", maxCacheSize);
        result.put("expireHours", expireHours);
        
        // 访问统计
        stats.forEach((key, value) -> result.put(key, value.get()));
        
        // 命中率计算
        long hits = stats.getOrDefault("cacheHit", new AtomicLong(0)).get();
        long misses = stats.getOrDefault("cacheMiss", new AtomicLong(0)).get();
        long total = hits + misses;
        if (total > 0) {
            result.put("hitRate", String.format("%.2f%%", (double) hits / total * 100));
        } else {
            result.put("hitRate", "0.00%");
        }
        
        // 缓存详情
        Map<String, Object> cacheDetails = new HashMap<>();
        fieldCache.forEach((cacheKey, cachedInfo) -> {
            Map<String, Object> detail = new HashMap<>();
            detail.put("fieldCount", cachedInfo.getFieldInfos().size());
            detail.put("createTime", cachedInfo.getCreateTime());
            detail.put("lastAccessTime", cachedInfo.getLastAccessTime());
            detail.put("accessCount", cachedInfo.getAccessCount());
            detail.put("cacheKey", cacheKey);
            
            // 从缓存key中提取类名作为显示名
            try {
                String className = keyGenerator.extractDtoClassNameFromCacheKey(cacheKey);
                String simpleName = className.substring(className.lastIndexOf('.') + 1);
                cacheDetails.put(simpleName, detail);
            } catch (Exception e) {
                // 如果提取失败，使用原始key
                cacheDetails.put(cacheKey, detail);
            }
        });
        result.put("cacheDetails", cacheDetails);
        
        return result;
    }
    
    /**
     * 获取缓存健康状态
     */
    public Map<String, Object> getHealthStatus() {
        Map<String, Object> health = new HashMap<>();
        
        long hits = stats.getOrDefault("cacheHit", new AtomicLong(0)).get();
        long misses = stats.getOrDefault("cacheMiss", new AtomicLong(0)).get();
        long total = hits + misses;
        
        // 健康状态判断
        String status;
        if (total == 0) {
            status = "UNKNOWN";
        } else {
            double hitRate = (double) hits / total;
            if (hitRate >= 0.8) {
                status = "EXCELLENT";
            } else if (hitRate >= 0.6) {
                status = "GOOD";
            } else if (hitRate >= 0.4) {
                status = "FAIR";
            } else {
                status = "POOR";
            }
        }
        
        health.put("status", status);
        health.put("cacheSize", fieldCache.size());
        health.put("maxSize", maxCacheSize);
        health.put("utilization", String.format("%.2f%%", (double) fieldCache.size() / maxCacheSize * 100));
        
        return health;
    }
    
    /**
     * 初始化统计信息
     */
    private void initStats() {
        stats.put("cacheHit", new AtomicLong(0));
        stats.put("cacheMiss", new AtomicLong(0));
        stats.put("cachePut", new AtomicLong(0));
        stats.put("cacheExpired", new AtomicLong(0));
        stats.put("cacheEvicted", new AtomicLong(0));
        stats.put("cacheRemoved", new AtomicLong(0));
        stats.put("cacheCleared", new AtomicLong(0));
    }
    
    /**
     * 递增统计计数
     */
    private void incrementStat(String key) {
        stats.computeIfAbsent(key, k -> new AtomicLong(0)).incrementAndGet();
    }
    
    /**
     * 启动定时清理任务
     */
    private void startCleanupTask() {
        cleanupExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "QueryCacheCleanup");
            thread.setDaemon(true);
            return thread;
        });
        
        cleanupExecutor.scheduleWithFixedDelay(
            this::cleanupExpiredCache,
            cleanupIntervalMinutes,
            cleanupIntervalMinutes,
            TimeUnit.MINUTES
        );
    }
    
    /**
     * 清理过期缓存
     */
    private void cleanupExpiredCache() {
        int cleanedCount = 0;
        
        Iterator<Map.Entry<String, CachedFieldInfo>> iterator = fieldCache.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, CachedFieldInfo> entry = iterator.next();
            if (isExpired(entry.getValue())) {
                iterator.remove();
                cleanedCount++;
                incrementStat("cacheExpired");
            }
        }
        
        if (cleanedCount > 0) {
            log.debug("定时清理过期缓存 - 清理数量: {}, 剩余数量: {}", cleanedCount, fieldCache.size());
        }
    }
    
    /**
     * 检查缓存是否过期
     */
    private boolean isExpired(CachedFieldInfo cachedInfo) {
        return cachedInfo.getCreateTime().plusHours(expireHours).isBefore(LocalDateTime.now());
    }
    
    /**
     * 驱逐最近最少使用的缓存项
     */
    private void evictLeastRecentlyUsed() {
        if (fieldCache.isEmpty()) {
            return;
        }
        
        // 找到最久未访问的缓存项
        String lruKey = null;
        LocalDateTime oldestAccess = LocalDateTime.now();
        
        for (Map.Entry<String, CachedFieldInfo> entry : fieldCache.entrySet()) {
            LocalDateTime lastAccess = entry.getValue().getLastAccessTime();
            if (lastAccess.isBefore(oldestAccess)) {
                oldestAccess = lastAccess;
                lruKey = entry.getKey();
            }
        }
        
        if (lruKey != null) {
            fieldCache.remove(lruKey);
            incrementStat("cacheEvicted");
            
            // 尝试从缓存key中提取类名用于日志
            try {
                String className = keyGenerator.extractDtoClassNameFromCacheKey(lruKey);
                String simpleName = className.substring(className.lastIndexOf('.') + 1);
                log.debug("驱逐LRU缓存项 - 类: {}, key: {}, 最后访问时间: {}", 
                    simpleName, lruKey, oldestAccess);
            } catch (Exception e) {
                log.debug("驱逐LRU缓存项 - key: {}, 最后访问时间: {}", lruKey, oldestAccess);
            }
        }
    }
    
    /**
     * 缓存字段信息封装类
     */
    @Getter
    private static class CachedFieldInfo {
        private final List<FieldInfo> fieldInfos;
        private final LocalDateTime createTime;
        private volatile LocalDateTime lastAccessTime;
        private final AtomicLong accessCount;
        
        public CachedFieldInfo(List<FieldInfo> fieldInfos) {
            this.fieldInfos = new ArrayList<>(fieldInfos);
            this.createTime = LocalDateTime.now();
            this.lastAccessTime = this.createTime;
            this.accessCount = new AtomicLong(1);
        }
        
        public long getAccessCount() {
            return accessCount.get();
        }
        
        public void updateAccess() {
            this.lastAccessTime = LocalDateTime.now();
            this.accessCount.incrementAndGet();
        }
    }
}