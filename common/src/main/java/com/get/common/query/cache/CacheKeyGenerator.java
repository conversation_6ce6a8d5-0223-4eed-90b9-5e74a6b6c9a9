package com.get.common.query.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * 缓存键生成器
 * 统一管理动态查询框架中所有缓存和监控的key生成逻辑
 * 
 * <AUTHOR>
 * @since 2025-01-30
 */
@Slf4j
@Component
@ConditionalOnProperty(
    name = "dynamic.query.cache.enabled",
    havingValue = "true",
    matchIfMissing = true
)
public class CacheKeyGenerator {
    
    /**
     * 字段缓存key分隔符
     */
    private static final String FIELD_CACHE_PREFIX = "field_cache:";
    
    /**
     * 监控指标key分隔符
     */
    private static final String METRICS_SEPARATOR = "->";
    
    /**
     * 生成字段信息缓存的key
     * 
     * @param dtoClass 查询DTO类
     * @return 缓存key
     */
    public String generateFieldCacheKey(Class<?> dtoClass) {
        if (dtoClass == null) {
            throw new IllegalArgumentException("DTO类不能为null");
        }
        
        String key = FIELD_CACHE_PREFIX + dtoClass.getName();
        log.debug("生成字段缓存key: {}", key);
        return key;
    }
    
    /**
     * 生成性能监控指标的key
     * 
     * @param dtoClass 查询DTO类
     * @param entityClass 实体类（可以为null）
     * @return 监控指标key
     */
    public String generateMetricsKey(Class<?> dtoClass, Class<?> entityClass) {
        if (dtoClass == null) {
            throw new IllegalArgumentException("DTO类不能为null");
        }
        
        String key;
        if (entityClass != null) {
            key = dtoClass.getSimpleName() + METRICS_SEPARATOR + entityClass.getSimpleName();
        } else {
            key = dtoClass.getSimpleName();
        }
        
        log.debug("生成监控指标key: {}", key);
        return key;
    }
    
    /**
     * 生成完整的查询上下文key
     * 包含DTO类和实体类的完整信息
     * 
     * @param dtoClass 查询DTO类
     * @param entityClass 实体类
     * @return 查询上下文key
     */
    public String generateQueryContextKey(Class<?> dtoClass, Class<?> entityClass) {
        if (dtoClass == null || entityClass == null) {
            throw new IllegalArgumentException("DTO类和实体类都不能为null");
        }
        
        String key = dtoClass.getName() + METRICS_SEPARATOR + entityClass.getName();
        log.debug("生成查询上下文key: {}", key);
        return key;
    }
    
    /**
     * 从字段缓存key中提取DTO类名
     * 
     * @param cacheKey 缓存key
     * @return DTO类名
     */
    public String extractDtoClassNameFromCacheKey(String cacheKey) {
        if (cacheKey == null || !cacheKey.startsWith(FIELD_CACHE_PREFIX)) {
            throw new IllegalArgumentException("无效的缓存key格式: " + cacheKey);
        }
        
        return cacheKey.substring(FIELD_CACHE_PREFIX.length());
    }
    
    /**
     * 从监控指标key中提取DTO类名
     * 
     * @param metricsKey 监控指标key
     * @return DTO类名
     */
    public String extractDtoClassNameFromMetricsKey(String metricsKey) {
        if (metricsKey == null || metricsKey.isEmpty()) {
            throw new IllegalArgumentException("监控指标key不能为空");
        }
        
        int separatorIndex = metricsKey.indexOf(METRICS_SEPARATOR);
        if (separatorIndex > 0) {
            return metricsKey.substring(0, separatorIndex);
        } else {
            return metricsKey;
        }
    }
    
    /**
     * 从监控指标key中提取实体类名
     * 
     * @param metricsKey 监控指标key
     * @return 实体类名，如果不存在则返回null
     */
    public String extractEntityClassNameFromMetricsKey(String metricsKey) {
        if (metricsKey == null || metricsKey.isEmpty()) {
            return null;
        }
        
        int separatorIndex = metricsKey.indexOf(METRICS_SEPARATOR);
        if (separatorIndex > 0 && separatorIndex < metricsKey.length() - METRICS_SEPARATOR.length()) {
            return metricsKey.substring(separatorIndex + METRICS_SEPARATOR.length());
        } else {
            return null;
        }
    }
    
    /**
     * 验证缓存key的格式
     * 
     * @param cacheKey 缓存key
     * @return 是否为有效格式
     */
    public boolean isValidCacheKey(String cacheKey) {
        return cacheKey != null && 
               cacheKey.startsWith(FIELD_CACHE_PREFIX) && 
               cacheKey.length() > FIELD_CACHE_PREFIX.length();
    }
    
    /**
     * 验证监控指标key的格式
     * 
     * @param metricsKey 监控指标key
     * @return 是否为有效格式
     */
    public boolean isValidMetricsKey(String metricsKey) {
        return metricsKey != null && !metricsKey.trim().isEmpty();
    }
    
    /**
     * 获取key生成器的配置信息
     */
    public java.util.Map<String, Object> getKeyGeneratorInfo() {
        java.util.Map<String, Object> info = new java.util.HashMap<>();
        info.put("fieldCachePrefix", FIELD_CACHE_PREFIX);
        info.put("metricsSeparator", METRICS_SEPARATOR);
        info.put("supportedKeyTypes", java.util.Arrays.asList("fieldCache", "metrics", "queryContext"));
        return info;
    }
}