package com.get.common.query.exception;

/**
 * 查询构建异常
 * 在构建查询条件过程中发生的异常
 *
 * <AUTHOR>
 * @since 2025-01-30
 */
public class QueryBuildException extends DynamicQueryException {

    public QueryBuildException(String message) {
        super("QUERY_BUILD_ERROR", message);
    }

    public QueryBuildException(String message, Throwable cause) {
        super("QUERY_BUILD_ERROR", message, cause);
    }

    public QueryBuildException(String message, Class<?> dtoClass, Class<?> entityClass) {
        super("QUERY_BUILD_ERROR", message, dtoClass.getSimpleName(), entityClass.getSimpleName());
    }

    public QueryBuildException(String message, Throwable cause, Class<?> dtoClass, Class<?> entityClass) {
        super("QUERY_BUILD_ERROR", message, cause, dtoClass.getSimpleName(), entityClass.getSimpleName());
    }

    /**
     * 创建查询构建失败异常
     */
    public static QueryBuildException buildFailed(Class<?> dtoClass, Class<?> entityClass, Throwable cause) {
        String message = String.format("构建查询条件失败 - 查询DTO: %s, 实体类: %s", 
            dtoClass.getSimpleName(), entityClass.getSimpleName());
        return new QueryBuildException(message, cause, dtoClass, entityClass);
    }

    /**
     * 创建分组查询构建失败异常
     */
    public static QueryBuildException groupBuildFailed(String groupName, Throwable cause) {
        String message = String.format("构建分组查询条件失败 - 分组: %s", groupName);
        return new QueryBuildException(message, cause);
    }
}