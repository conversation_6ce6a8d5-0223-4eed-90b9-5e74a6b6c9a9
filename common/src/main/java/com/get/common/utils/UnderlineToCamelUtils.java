package com.get.common.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 驼峰法-下划线互转
 *
 * <AUTHOR>
 * @version 1.0.0
 * @since 2015.07.04
 */
public class UnderlineToCamelUtils {
    /**
     * 下划线转驼峰法
     *
     * @param line       源字符串
     * @param smallCamel 大小驼峰,是否为小驼峰(首字母小写)
     * @return 转换后的字符串
     */
    public static String underlineToCamel(String line, boolean smallCamel) {
        if (line == null || "".equals(line)) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        Pattern pattern = Pattern.compile("([A-Za-z\\d]+)(_)?");
        Matcher matcher = pattern.matcher(line);
        while (matcher.find()) {
            String word = matcher.group();
            sb.append(smallCamel && matcher.start() == 0 ? Character.toLowerCase(word.charAt(0)) : Character.toUpperCase(word.charAt(0)));
            int index = word.lastIndexOf('_');
            if (index > 0) {
                sb.append(word.substring(1, index).toLowerCase());
            } else {
                sb.append(word.substring(1).toLowerCase());
            }
        }
        return sb.toString();
    }

    /**
     * 驼峰法转下划线
     *
     * @param line 源字符串
     * @return 转换后的字符串
     */
    public static String camelToUnderline(String line) {
        if (line == null || "".equals(line)) {
            return "";
        }
        line = String.valueOf(line.charAt(0)).toUpperCase().concat(line.substring(1));
        StringBuffer sb = new StringBuffer();
        Pattern pattern = Pattern.compile("[A-Z]([a-z\\d]+)?");
        Matcher matcher = pattern.matcher(line);
        while (matcher.find()) {
            String word = matcher.group();
            sb.append(word.toLowerCase());
            sb.append(matcher.end() == line.length() ? "" : "_");
        }
        return sb.toString();
    }

    /**
     * 驼峰法转下划线
     *
     * @param line 源字符串
     * @return 转换后的字符串
     */
    public static String camelToUnderline(String line, boolean smallCamel) {
        if (line == null || "".equals(line)) {
            return "";
        }
        line = String.valueOf(line.charAt(0)).toUpperCase().concat(line.substring(1));
        StringBuffer sb = new StringBuffer();
        Pattern pattern = Pattern.compile("[A-Z]([a-z\\d]+)?");
        Matcher matcher = pattern.matcher(line);
        while (matcher.find()) {
            String word = matcher.group();
            if (smallCamel) {
                sb.append(word.toLowerCase());
                sb.append(matcher.end() == line.length() ? "" : "-");
            } else {
                sb.append(word.toUpperCase());
                sb.append(matcher.end() == line.length() ? "" : "_");
            }
        }
        return sb.toString();
    }

    public static void change(String[] strs) {
        strs[0] = "12";
        strs[1] = "13";
    }

    public static void main(String[] args) {
//        System.out.println(underlineToCamel("tea_abc",true));
//        System.out.println(camelToUnderline("teaAbc"));
//        System.out.println(camelToUnderline("TeaAbcAAA"));
        System.out.println(camelToUnderline("checkReceiptInvoiceMappingExist", true));
        System.out.println(camelToUnderline("checkReceiptInvoiceMappingExist", false));
//        System.out.println(camelToUnderline("sendSms", true));
//        getAgentContractByAgentIds
//
//
//        String tets = "Handler dispatch failed; nested exception is java.lang.AssertionError: com.get.core.cloud.exception.GetFeignServiceException: com.netflix.client.ClientException: Load balancer does not have available server for client: institution-center";
//        String[] strings = tets.split(": ");
//        if(tets.contains("GetFeignServiceException") && strings.length >=3 )
//        {
//            tets = strings[strings.length-2]+":"+strings[strings.length-1];
//            System.out.println(tets);
//        }
    }
}