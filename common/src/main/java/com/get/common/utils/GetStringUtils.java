package com.get.common.utils;

import lombok.Data;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 公共字符串处理类
 *
 * <AUTHOR>
 * @date 2022/2/23 16:38
 */
public class GetStringUtils {

    /**
     * 学生编号
     *
     * @param num
     * @return
     */
    public static String getStudentNum(Long num) {
        String code = String.valueOf(num);
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy");
        String year = formatter.format(calendar.getTime());

        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        return "ST" + year + code;
    }

    /**
     * 客户编号
     *
     * @param num
     * @return
     */
    public static String getClientNum(Long num) {
        String code = String.valueOf(num);
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy");
        String year = formatter.format(calendar.getTime());

        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        return "CL" + year + code;
    }

    /**
     * 学习申请方案编号
     *
     * @param num
     * @return
     */
    public static String getStudentOfferNum(Long num) {
        String code = String.valueOf(num);
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String year = formatter.format(calendar.getTime());

        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        return "OFF" + year + code;
    }

    /**
     * 学习计划编号
     *
     * @param num
     * @return
     */
    public static String getStudentOfferItemNum(Long num) {
        String code = String.valueOf(num);
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String year = formatter.format(calendar.getTime());

        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        return "OFI" + year + code;
    }

    /**
     * 收款单编号
     *
     * @param id
     * @return
     */
    public static String getReceiptFormNum(Long id) {
        String code = String.valueOf(id);
        if (String.valueOf(id).length() < 9) {
            code = String.format("%08d", id);
        }
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        return "RCF" + formatter.format(calendar.getTime()) + code;
    }

    /**
     * 付款单编号
     *
     * @param num
     * @return
     */
    public static String getPayFormNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 9) {
            code = String.format("%08d", num);
        }
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        return "PMF" + formatter.format(calendar.getTime()) + code;
    }

    /**
     * 财务结算批次号
     *
     * @param
     * @return
     */
    public static String geFinancialSettlementNum() {
        Random random = new Random();
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return formatter.format(calendar.getTime()) + "_" + (random.nextInt(900) + 100);
    }

    /**
     * 将集合拼接成一个string（in语句）
     *
     * @param collection
     * @return
     */
    public static <E> String getSqlString(Collection<E> collection) {
        if (CollectionUtils.isEmpty(collection)) {
            return "";
        }
        StringBuffer sb = new StringBuffer();
        sb.append("(");
        for (E e : collection) {
            if (e == null) {
                continue;
            }
            if (!Number.class.equals(e.getClass().getSuperclass())) {
                sb.append("\"").append(String.valueOf(e)).append("\"").append(",");
            } else {
                sb.append(String.valueOf(e)).append(",");
            }
        }
        sb.deleteCharAt(sb.toString().length() - 1);
        sb.append(")");
        return sb.toString();
    }

    public static boolean isChinese(String string) {
        //通过字节码进行判断
        if (string.matches("[\u4E00-\u9FA5]+")) {
            return true;
        }
        return false;
    }


    /**
     * @return java.lang.String
     * @Description :住宿编号
     * @Param [num]
     * <AUTHOR>
     */
    public static String getAccommodationNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "ACC" + code;
    }

    /**
     * @return java.lang.String
     * @Description :保险编号
     * @Param [num]
     * <AUTHOR>
     */
    public static String getInsuranceNum(Long num) {
        String code = String.valueOf(num);
        if (String.valueOf(num).length() < 7) {
            code = String.format("%06d", num);
        }
        return "INS" + code;
    }


    public static String getAmount(String amount) {
        char[] b = amount.toCharArray();
        String result = "";
        for (int i = 0; i < b.length; i++) {
            if (("0123456789.").contains(b[i] + "")) {
                result += b[i];
            }
        }
        return result;
    }

    /**
     * 通过反射将类字符串类型的值统一转成小写，慎用，可能导致精确匹配的字符串查询失败
     * @param obj
     * @return
     */
    public static Object convertStringToLower(Object obj) {
        Object pojo1 = obj;
        try {
            // 通过反射获取类的类类型及字段属性
            Class clazz = pojo1.getClass();
            Field[] fields = clazz.getDeclaredFields();
            for (Field field : fields) {
                String name = field.getName(); // 获取属性的名字
                // 排除序列化属性
                if ("serialVersionUID".equals(name)) {
                    continue;
                }
                PropertyDescriptor pd = new PropertyDescriptor(field.getName(), clazz);
                // 获取对应属性值
                Method getMethod = pd.getReadMethod();
                Object o1 = getMethod.invoke(pojo1);
                if (o1 == null ) {
                    continue;
                }
                //字符串类型
                if (o1 instanceof String) {
                    if(StringUtils.isEmpty((String)o1))
                    {
                        continue;
                    }
                    o1 = ((String) o1).toLowerCase();
                    getMethod = obj.getClass().getMethod("set"+name.substring(0, 1).toUpperCase() + name.substring(1),String.class);
                    getMethod.invoke(obj, o1);
                }
                //集合内部字符串类型,这里只取一层，如有对象内部嵌套集合则需要手工处理
                if (o1 instanceof Collection) {
                    List<String> list = new ArrayList<>();
                    for (Object o:(Collection) o1)
                    {
                        //字符串类型
                        if (o instanceof String) {
                            o = ((String) o).toLowerCase();
                            list.add((String)o);
                        }
                    }
                    if(list.size()>0)
                    {
                        getMethod = obj.getClass().getMethod("set"+name.substring(0, 1).toUpperCase() + name.substring(1),List.class);
                        getMethod.invoke(obj, list);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return pojo1;
    }


    public static String getReminderTemplate(Map<String, String> map, String htmlText) {
        if (map!=null) {
            for (String s : map.keySet()) {
                if (map.get(s)!=null) {
                    htmlText = htmlText.replace("${" + s + "}", map.get(s));
                } else {
                    htmlText = htmlText.replace("${" + s + "}", "");
                }
            }
        }
        return htmlText;
    }
}
