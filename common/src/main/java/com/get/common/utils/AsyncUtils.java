package com.get.common.utils;

import com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.util.concurrent.DefaultThreadFactory;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 *    private static final ThreadPoolExecutor executor= new ThreadPoolExecutor(8,
 * //            16,
 * //            10,
 * //            TimeUnit.SECONDS,
 * //            new LinkedBlockingQueue<>(128),
 * //            new DefaultThreadFactory("Export"),
 * //            new ThreadPoolExecutor.AbortPolicy());
 * //
 * //    public static void run(Runnable runnable){
 * //        executor.execute(runnable);
 * //    }
 * <AUTHOR>
 */
public class AsyncUtils {
}
