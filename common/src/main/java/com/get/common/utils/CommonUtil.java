package com.get.common.utils;

import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.get.common.annotion.ExportInternationalization;
import com.get.common.result.FocExportVo;
import com.get.common.result.SaveResponseBo;
import com.get.core.secure.utils.SecureUtil;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang.StringUtils;
import org.springframework.lang.Nullable;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;
import java.io.PrintWriter;
import java.lang.reflect.Field;
import java.lang.reflect.Type;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 通用工具类
 */
public class CommonUtil<T> {

    public static final String INLAND_AREA_CODE = "86";

    public static final List<String> HMT = new ArrayList<String>(){{
       add("852");
       add("853");
       add("886");
    }};


    private static final int MAX_LEN = 50;


    public static String getTokenSessionId(){
        return UUID.randomUUID().toString().replaceAll("-","");
    }

    public static String formatSeconds(long seconds) {
        if (seconds==0) {
            return null;
        }
        long day = seconds / (24 * 3600);
        long hour = (seconds % (24 * 3600)) / 3600;
        long minute = (seconds % 3600) / 60;
        long second = seconds % 60;

        StringBuilder sb = new StringBuilder();
        if (day > 0) {
            sb.append(day).append("天");
        }
        if (hour > 0) {
            sb.append(hour).append("时");
        }
        if (minute > 0) {
            sb.append(minute).append("分");
        }
        if (second > 0 || sb.toString().isEmpty()) {
            sb.append(second).append("秒");
        }
        return sb.toString();
    }

    public static String getVerifyCode(int len){
        if (len > MAX_LEN) {
            return null;
        }
        StringBuilder builder = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < len; i++) {
            builder.append(random.nextInt(10));
        }
        return builder.toString();
    }

    public static Integer getOffset(Integer pageNumber, Integer pageSize) {
        if (pageSize == null || pageSize < 0) {
            pageSize = 0;
        }
        if (pageNumber != null && pageNumber > 0) {
            return (pageNumber - 1) * pageSize;
        }
        return 0;
    }
    public static <T> List<T> deepCopy(List<T> src) throws IOException, ClassNotFoundException {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        ObjectOutputStream out = new ObjectOutputStream(byteOut);
        out.writeObject(src);

        ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
        ObjectInputStream in = new ObjectInputStream(byteIn);
        @SuppressWarnings("unchecked")
        List<T> dest = (List<T>) in.readObject();
        return dest;
    }

    //嵌套map深拷贝
    public static <K,R,V> Map<K, Map<R,V>> deepCopyMap(Map<K, Map<R, V>> map) throws IOException, ClassNotFoundException {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        ObjectOutputStream out = new ObjectOutputStream(byteOut);
        out.writeObject(map);

        ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
        ObjectInputStream in = new ObjectInputStream(byteIn);
        @SuppressWarnings("unchecked")
        Map<K, Map<R,V>> dest = (Map<K, Map<R,V>>) in.readObject();
        return dest;
    }

    /**
     * 自定义类型深拷贝
     *
     * @Date 14:35 2023/3/22
     * <AUTHOR>
     */
    @Nullable
    public static <T> T copy(Type type, @Nullable T target){
        Gson gson = new Gson();
        String jsonString = gson.toJson(target);
        return gson.fromJson(jsonString, type);
    }

    public static void ok(HttpServletResponse response) {
        PrintWriter writer;
        try {
            writer = response.getWriter();
            writer.write(new ObjectMapper().writeValueAsString(SaveResponseBo.ok()));
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public List<FocExportVo> getOptions(Class<T> cls) {
        Field[] fields = cls.getDeclaredFields();
        List<FocExportVo> result = new ArrayList<>();
        int i = 1;
        for (Field field : fields) {
            field.setAccessible(true);
            boolean present = field.isAnnotationPresent(ApiModelProperty.class);
            if (present) {
//                ApiModelProperty property = field.getAnnotation(ApiModelProperty.class);
                FocExportVo focExportVo = new FocExportVo();
                focExportVo.setKey("k" + i)
                        .setFiledName(field.getName())
                        .setDescription(getExportInternationalizationValue(field));
                result.add(focExportVo);
                i++;
            }
        }
        return result;
    }

    public List<FocExportVo> getOptions(Field[] fields) {
//        Field[] fields = cls.getDeclaredFields();
        List<FocExportVo> result = new ArrayList<>();
        int i = 1;
        for (Field field : fields) {
            field.setAccessible(true);
            boolean present = field.isAnnotationPresent(ApiModelProperty.class);
            if (present) {
//                ApiModelProperty property = field.getAnnotation(ApiModelProperty.class);
                FocExportVo focExportVo = new FocExportVo();
                focExportVo.setKey("k" + i)
                        .setFiledName(field.getName())
                        .setDescription(getExportInternationalizationValue(field));
                result.add(focExportVo);
                i++;
            }
        }
        return result;
    }

    public List<FocExportVo> getOptions(Class<T> cls, String keys) {
        Field[] fields = cls.getDeclaredFields();
        List<FocExportVo> result = new ArrayList<>();
        List<String> list = Arrays.asList(keys.split(","));
        int i = 1;
        for (Field field : fields) {
            field.setAccessible(true);
            boolean present = field.isAnnotationPresent(ApiModelProperty.class);
            if (present) {
//                ApiModelProperty property = field.getAnnotation(ApiModelProperty.class);
                FocExportVo focExportVo = new FocExportVo();
                focExportVo.setKey("k" + i)
                        .setFiledName(field.getName())
                        .setDescription(getExportInternationalizationValue(field));
                focExportVo.setSelected(list.contains(focExportVo.getKey()));
                result.add(focExportVo);
                i++;
            }
        }
        return result;
    }

    public List<FocExportVo> getOptions(Field[] fields, String keys) {
//        Field[] fields = cls.getDeclaredFields();
        List<FocExportVo> result = new ArrayList<>();
        List<String> list = Arrays.asList(keys.split(","));
        int i = 1;
        for (Field field : fields) {
            field.setAccessible(true);
            boolean present = field.isAnnotationPresent(ApiModelProperty.class);
            if (present) {
//                ApiModelProperty property = field.getAnnotation(ApiModelProperty.class);
                FocExportVo focExportVo = new FocExportVo();
                focExportVo.setKey("k" + i)
                        .setFiledName(field.getName())
                        .setDescription(getExportInternationalizationValue(field));
                focExportVo.setSelected(list.contains(focExportVo.getKey()));
                result.add(focExportVo);
                i++;
            }
        }
        return result;
    }

    /**
     * 有使用@ExportInternationalization注解，返回对应的国际化值
     * 否则返回@ApiModelProperty注解的值
     *
     * @param field 字段属性
     * @return
     */
    public static String getExportInternationalizationValue(Field field) {
        String value = null;
        // 有使用@ExportInternationalization注解，则返回对应的国际化值
        if (field.isAnnotationPresent(ExportInternationalization.class)) {
//            String locale = "zh";
            String locale = SecureUtil.getLocale();
            ExportInternationalization exportInternationalization = field.getAnnotation(ExportInternationalization.class);
            switch (locale) {
                case "zh":
                    value = exportInternationalization.valueZh();
                    break;
                case "en":
                    value = exportInternationalization.valueEn();
                    break;
                default:
                    // 默认返回中文
                    value = exportInternationalization.valueZh();
                    break;
            }
        } else if (field.isAnnotationPresent(ApiModelProperty.class)) { // 否则返回@ApiModelProperty注解的值
            ApiModelProperty apiModelProperty = field.getAnnotation(ApiModelProperty.class);
            value = apiModelProperty.value();
        }
        return value;
    }

    public static void sort(List<String> list){
        list.sort(new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                return extractInt(o1)-extractInt(o2);
            }
            int extractInt(String str){
                str = str.replaceAll("\\D","");
                return str.isEmpty()? 0 : Integer.parseInt(str);
            }
        });
    }

    public static void sortFoc(List<FocExportVo> list){
        list.sort(new Comparator<FocExportVo>() {
            @Override
            public int compare(FocExportVo o1, FocExportVo o2) {
                return extractInt(o1.getKey())-extractInt(o2.getKey());
            }
            int extractInt(String str){
                str = str.replaceAll("\\D","");
                return str.isEmpty()? 0 : Integer.parseInt(str);
            }
        });
    }

    public Map<Long, Object> convertItem(List<T> data) {
        Map<Long, Object> convertMap = new HashMap<>(data.size());
        Field id;
        Long idVal;
        for (T t : data) {
            try {
                id = t.getClass().getDeclaredField("id");
                idVal = (Long) id.get(t);
                convertMap.put(idVal, t);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return convertMap;
    }
    /**
     * Map根据value排序;
     *
     * @param map
     * @return map
     */
    public static <K, V extends Comparable<? super V>> Map<K, V> sortByValue(Map<K, V> map) {
        if (map.isEmpty()) {
            return Collections.emptyMap();
        }
        List<Map.Entry<K, V>> list = new LinkedList<>(map.entrySet());

        list.sort((o1, o2) -> (o2.getValue()).compareTo(o1.getValue()));

        Map<K, V> result = new LinkedHashMap<>();
        for (Map.Entry<K, V> entry : list) {
            result.put(entry.getKey(), entry.getValue());
        }
        return result;
    }

    /**
     * map根据key排序
     * @param map
     * @return
     */
    public static Map<String, String> sortMapByKey(Map<String, String> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        Map<String, String> liquidLinkedMap = new LinkedHashMap<>();
        map.entrySet().stream()
                .sorted(Map.Entry.<String, String>comparingByKey()).forEachOrdered(e -> liquidLinkedMap.put(e.getKey(), e.getValue()));
        return liquidLinkedMap;
    }


    public static <K, V extends Comparable<? super V>> List<K> sortByValue2(Map<K, V> map) {
        if (map.isEmpty()) {
            return Collections.emptyList();
        }
        List<Map.Entry<K, V>> list = new LinkedList<>(map.entrySet());
        list.sort((o1, o2) -> (o2.getValue()).compareTo(o1.getValue()));
        List<K> result = new LinkedList<>();
        for (Map.Entry<K, V> entry : list) {
            result.add(entry.getKey());
        }
        return result;
    }

    public Map<Long, Object> convertSupperItem(List<T> data) {
        Map<Long, Object> convertMap = new HashMap<>(data.size());
        Field id;
        Long idVal;
        for (T t : data) {
            try {
                Class<?> superclass = t.getClass().getSuperclass();
                id = superclass.getDeclaredField("id");
                id.setAccessible(true);
                idVal = (Long) id.get(t);
                convertMap.put(idVal, t);
            } catch (NoSuchFieldException | IllegalAccessException e) {
                e.printStackTrace();
            }
        }
        return convertMap;
    }

    /**
     * 校验中文
     * @param str
     * @return
     */
    public static boolean isContainsChinese(String str) {
        if (str == null) { return false; }
        Pattern p = Pattern.compile("[\u4e00-\u9fa5]");
        Matcher m = p.matcher(str);
        return m.find();
    }

    private final static String SEPARATOR = "，";

    public String getFiledValue(Object obj, Class<T> cls) {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            T t = objectMapper.convertValue(obj, cls);
            Field[] fields = obj.getClass().getDeclaredFields();
            if (fields.length > 0) {
                StringBuilder builder = new StringBuilder(SEPARATOR);
                boolean present;
                Object val;
                String filedName;
                for (Field field : fields) {
                    field.setAccessible(true);
                    filedName = field.getName();
                    if ("fkCompanyId".equals(filedName)
                            || "fkCompanyIds".equals(filedName)
                            || "pageNumber".equals(filedName)
                            || "pageSize".equals(filedName)) {
                        continue;
                    }
                    present = field.isAnnotationPresent(ApiModelProperty.class);
                    if (present) {
                        val = field.get(t);
                        if (val == null) {
                            continue;
                        }
                        if (StringUtils.isBlank(val.toString().replace("[", "").replace("]", ""))) {
                            continue;
                        }
                        builder.append(field.getAnnotation(ApiModelProperty.class).value()).append("=");
                        if (field.getType() == Date.class) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            builder.append(sdf.format(val)).append(SEPARATOR);
                        } else {
                            builder.append(val).append(SEPARATOR);
                        }
                    }
                }
                String str = builder.toString();
                if (str.endsWith(SEPARATOR)) {
                    str = str.substring(0, str.length() - 1);
                }
                return str;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }
    public Map<String, String> getTemplateFieldMap(T object) {
        Map<String, String> map = new HashMap<>(8);
        try{
            Field[] fields = object.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                Object val = field.get(object);
                if (val!=null) {
                    //如果是date类型
                    if (field.getType() == Date.class) {
                        SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd");
                        String format = fmt.format(val);
                        map.put(field.getName(), format);
                    } else {
                        map.put(field.getName(), val.toString());
                    }
                } else {
                    map.put(field.getName(), "");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return map;
    }

    /**
     * @return java.lang.String
     * @Description: 随机生成数
     * @Param []
     * <AUTHOR>
     */
    public static String getRandomCodeAndChar(int length) {
        String digits = "0123456789"; // 数字集合
        String chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"; // 英文字母集合
        String allChars = digits + chars; // 数字和英文字母集合
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        // 随机生成字符串
        for (int i = 0; i < length; i++) {
            int index = random.nextInt(allChars.length());
            sb.append(allChars.charAt(index));
        }
        return sb.toString();
    }
}
