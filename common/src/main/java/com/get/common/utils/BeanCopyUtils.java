package com.get.common.utils;

import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

import static com.get.common.utils.UnderlineToCamelUtils.camelToUnderline;

public class BeanCopyUtils extends BeanUtils {

    /**
     * 将一个对象克隆到另外一个对象中
     */
    public static <T, K> T objClone(K k, Supplier<T> target) {
        T t = target.get();
        if (k == null) {
            return null;
        }
        copyProperties(k, t);
        return t;
    }

    /**
     * 集合数据的拷贝，用法参考下面
     *
     * @param sources: 数据源类
     * @param target:  目标类::new(eg: UserVO::new)
     * @return
     */
    public static <S, T> List<T> copyListProperties(List<S> sources, Supplier<T> target) {
        return copyListProperties(sources, target, null);
    }


    /**
     * 带回调函数的集合数据的拷贝（可自定义字段拷贝规则），用法参考下面
     *
     * @param sources:  数据源类
     * @param target:   目标类::new(eg: UserVO::new)
     * @param callBack: 回调函数
     * @return
     */
    public static <S, T> List<T> copyListProperties(List<S> sources, Supplier<T> target, BeanCopyUtilCallBack<S, T> callBack) {
        List<T> list = new ArrayList<>(sources.size());
        for (S source : sources) {
            T t = target.get();
            copyProperties(source, t);
            list.add(t);
            if (callBack != null) {
                // 回调
                callBack.callBack(source, t);
            }
        }
        return list;
    }

    /**
     * Object 对象转 List<T></>
     */
    public static <T> List<T> castList(Object obj, Class<T> clazz) {
        List<T> result = new ArrayList<T>();
        if (obj instanceof List<?>) {
            for (Object o : (List<?>) obj) {
                result.add(clazz.cast(o));
            }
            return result;
        }
        return null;
    }

    /**
     * 将一个类查询方式加入map（属性值为int型时，0时不加入，
     * 属性值为String型或Long时为null和“”不加入）
     * 注：需要转换的必须是对象，即有属性
     */
    public static Map<String, Object> setConditionMap(Object obj) {
        Map<String, Object> map = new HashMap<>();
        if (obj == null) {
            return null;
        }
        Field[] fields = obj.getClass().getDeclaredFields();//获取类的各个属性值
        for (Field field : fields) {
            field.setAccessible(true);
            //旧的方法，获取带斜划线的表字段名
//            if(field.isAnnotationPresent(Column.class)){
//                String fieldName =  field.getDeclaredAnnotation(Column.class).name();//获取类的属性名称
//                map.put(fieldName,  getValueByFieldName(field.getName(),obj));
//            }
            //新的方法，根据字段名把驼峰转换为下划线
            String fieldName = field.getName();
            if (StringUtils.isNotEmpty(fieldName)) {
//                //转为下划线字符串
                 String fieldName_ = camelToUnderline(fieldName);
//                map.put(fieldName, fieldName_);
                map.put(fieldName_,getValueByFieldName(field.getName(),obj));
            }
        }
        return map;
    }
    /**
     * 根据属性名获取该类此属性的值
     * @param fieldName
     * @param object
     * @return
     */
    public static Object getValueByFieldName(String fieldName,Object object){
        String firstLetter=fieldName.substring(0,1).toUpperCase();
        String getter = "get"+firstLetter+fieldName.substring(1);
        try {
            Method method = object.getClass().getMethod(getter, new Class[]{});
            Object value = method.invoke(object, new Object[] {});
            return value;
        } catch (Exception e) {
            return null;
        }
    }

    //用法示例：：：
//    public static void main(String[] args) {
    //普通list的copy
//        List<UserDO> userDOList = new ArrayList();
//        userDOList.add(new UserDO(1L, "Van", 18, 1));
//        userDOList.add(new UserDO(2L, "VanVan", 20, 2));
//        List<UserVO> userVOList = BeanCopyUtil.copyListProperties(userDOList, UserVO::new);
//        log.info("userVOList:{}",userVOList);
    //处理属性不同的字段的拷贝：
//        ist<UserDO> userDOList = new ArrayList();
//        userDOList.add(new UserDO(1L, "Van", 18, 1));
//        userDOList.add(new UserDO(2L, "VanVan", 20, 2));
//        List<UserVO> userVOList = BeanCopyUtil.copyListProperties(userDOList, UserVO::new, (userDO, userVO) ->{
//            // 这里可以定义特定的转换规则
//            userVO.setSex(SexEnum.getDescByCode(userDO.getSex()).getDesc());
//        });
//        log.info("userVOList:{}",userVOList);
//    }
}
