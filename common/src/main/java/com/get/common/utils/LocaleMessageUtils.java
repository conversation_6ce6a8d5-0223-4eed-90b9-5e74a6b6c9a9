//package com.get.common.utils;
//
//import org.apache.commons.lang.StringUtils;
//import org.springframework.stereotype.Component;
//import java.io.Serializable;
//import java.util.ResourceBundle;
//
///**
// * @Description: 国际化工具类
// * <AUTHOR>
// */
//@Component
//public class LocaleMessageUtils implements Serializable {
//    private static final long serialVersionUID = -7657898714983901418L;
//
//    /**
//     * 根据传入的locale展示message
//     * @param locale
//     * @param code
//     * @return
//     */
//    public static String getMessage(String locale,String code) {
//        return getMessage(locale,code, "");
//    }
//
//    /**
//     * 该方法获取默认的message
//     * @param code
//     * @return
//     */
//    public static String getMessage(String code) {
//        return getMessage("",code, "");
//    }
//
//    /**
//     * @param code           ：对应messages配置的key.
//     * @param defaultMessage : 没有设置key的时候的默认值.
//     * @return
//     */
//    public static String getMessage(String locale,String code, String defaultMessage) {
//        String message = "";
//        try {
//            String builder = getLocaleFileName(locale);
//            ResourceBundle rb = ResourceBundle.getBundle(builder);
//            message = new String(rb.getString(code).getBytes("ISO-8859-1"), "UTF8");
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//        return message;
//    }
//
//    private static String getLocaleFileName(String locale) {
//        StringBuilder builder = new StringBuilder();
//        builder.append("i18n/messages");
//        if (StringUtils.isNotEmpty(locale)) {
//            builder.append("_");
//            builder.append(locale);
//        }
////        builder.append(".properties");
//        return builder.toString();
//    }
//
//    public static void main(String[] args) {
//        System.out.println(getMessage("en","id_null"));
//    }
//
//
//
//}
