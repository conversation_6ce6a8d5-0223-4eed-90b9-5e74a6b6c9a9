package com.get.common.utils;

import com.get.common.consts.AESConstant;
import org.apache.commons.lang.StringUtils;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * Created by <PERSON>.
 * User: 14:51
 * Date: 2021/6/11
 * Description:AES工具类
 */
public class AESUtils {

    // 加密
    public static String Encrypt(String sSrc, String sKey) throws Exception {
        // 判断Key是否正确
        if (StringUtils.isEmpty(sSrc) || StringUtils.isEmpty(sKey)) {
            throw new RuntimeException("参数不能为空");
        }
        // 判断Key是否为16位
        if (sKey.length() != 16) {
            throw new RuntimeException("key需要16位长度");
        }
        byte[] raw = sKey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        //"算法/模式/补码方式"
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));
        //此处使用BASE64做转码功能，同时能起到2次加密的作用。
        return new BASE64Encoder().encode(encrypted);
    }

    // 解密
    public static String Decrypt(String sSrc, String sKey) throws Exception {
        // 判断Key是否正确
        if (StringUtils.isEmpty(sSrc) || StringUtils.isEmpty(sKey)) {
            throw new RuntimeException("参数不能为空");
        }
        // 判断Key是否为16位
        if (sKey.length() != 16) {
            throw new RuntimeException("key需要16位长度");
        }
        byte[] raw = sKey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);
        //先用base64解密
        byte[] encrypted1 = new BASE64Decoder().decodeBuffer(sSrc);
        byte[] original = cipher.doFinal(encrypted1);
        String originalString = new String(original, "utf-8");
        return originalString;
    }


    public static String EncryptURL(String sSrc, String sKey) throws Exception {
        if (StringUtils.isEmpty(sSrc) || StringUtils.isEmpty(sKey)) {
            throw new RuntimeException("参数不能为空");
        }
        if (sKey.length() != 16) {
            throw new RuntimeException("key需要16位长度");
        }

        byte[] raw = sKey.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");

        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);

        byte[] encrypted = cipher.doFinal(sSrc.getBytes(StandardCharsets.UTF_8));

        // 使用 Base64 URL 安全编码，避免出现 + / = 等特殊字符
        String base64Str = Base64.getUrlEncoder().withoutPadding().encodeToString(encrypted);

        return base64Str;
    }

    public static String DecryptURL(String sSrc, String sKey) throws Exception {
        if (StringUtils.isEmpty(sSrc) || StringUtils.isEmpty(sKey)) {
            throw new RuntimeException("参数不能为空");
        }
        if (sKey.length() != 16) {
            throw new RuntimeException("key需要16位长度");
        }

        byte[] raw = sKey.getBytes(StandardCharsets.UTF_8);
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");

        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(Cipher.DECRYPT_MODE, skeySpec);

        // 使用 URL 安全的 Base64 解码
        byte[] decoded = Base64.getUrlDecoder().decode(sSrc);

        byte[] original = cipher.doFinal(decoded);
        return new String(original, StandardCharsets.UTF_8);
    }


//    public static void main(String[] args) {
//        try {
//            String password = AESUtils.Encrypt("123456", AESConstant.AES_LOGIN_KEY);
//            System.out.println(password);
//            System.out.println(AESUtils.Decrypt(password, AESConstant.AES_LOGIN_KEY));
//        }catch (Exception e)
//        {
//            e.printStackTrace();
//        }
//    }
}
