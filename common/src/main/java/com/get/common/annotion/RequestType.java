package com.get.common.annotion;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @DATE: 2021/5/17
 * @TIME: 15:27
 * @Description:
 **/
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface RequestType {
    /**
     * 是否请求
     *
     * @return
     */
    public boolean IsRequest();
}
