package com.get.common.annotion;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @author:Eric
 * @date: 04/06/2021
 * description:
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.FIELD})
public @interface TableDto {
    /**
     * Dto表名字
     *
     * @return
     */
    String tableName() default "";

    /**
     * Dto表字段
     *
     * @return
     */
    String columnDto() default "";

    /**
     * 实体类属性，用于返回值判断
     *
     * @return
     */
    String entityColumnDto() default "";

    /**
     * Dto(当前实体类)关联表id
     *
     * @return
     */
    String columnDtoMainId() default "";

    String catalog() default "";

    String schema() default "";

//    UniqueConstraint[] uniqueConstraints() default {};
}
