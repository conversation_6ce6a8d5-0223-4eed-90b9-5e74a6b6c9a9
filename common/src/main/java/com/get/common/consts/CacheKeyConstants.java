package com.get.common.consts;


/**
 * 缓存key
 *
 * <AUTHOR> @version 0.0.1
 */
public class CacheKeyConstants {

    /**
     * 缓存生命周期5s
     */
    public static final int REDIS_KEY_EXPIRES_FIVE_SECOND = 5;
    /**
     * 缓存生命周期20s
     */
    public static final int REDIS_KEY_EXPIRES_TWENTY_SECOND = 20;
    /**
     * 缓存生命周期五分钟
     */
    public static final int REDIS_KEY_EXPIRES_FIVE_MINUTES = 60 * 5;

    /**
     * 缓存生命周期1小时
     */
    public static final int REDIS_KEY_EXPIRE_ONE_HOUR = 60 * 60;

    /**
     * 缓存生命周期2小时
     */
    public static final int REDIS_KEY_EXPIRE_TWO_HOUR = 60 * 60 * 2;
    /**
     * 缓存生命周期一天
     */
    public static final int REDIS_KEY_EXPIRE_ONE_DAY = 60 * 60 * 24;
    /**
     * 定时任务中心缓存key前缀
     */
    public static final String SCHEDULE_CACHE_KEY_ROOT = "schedule_admin_";

    /**
     * 销售中心缓存key前缀
     */
    public static final String SALE_CACHE_KEY_ROOT = "sale_admin_";
    /**
     * 峰会桌子已入座数量缓存key
     */
    public static final String CONVENTION_SEAT_COUNT_KEY = SALE_CACHE_KEY_ROOT + "convention_seat_count_key_";

    /**
     * 峰会桌子已入座数量缓存key
     */
    public static final String CONVENTION_SEATED_SEAT_COUNT_KEY = SALE_CACHE_KEY_ROOT + "convention_seated_seat_count_key_";

    /**
     * 峰会桌子已入座数量缓存锁
     */
    public static final String CONVENTION_TABLE_LOCK_KEY = SALE_CACHE_KEY_ROOT + "convention_table_lock_key_";

    /**
     * 活动地点报名缓存锁
     */
    public static final String GOPRO_PLACE_LOCK_KEY = SALE_CACHE_KEY_ROOT + "gopro_place_lock_key_";

    /**
     * 活动地点报名缓存锁
     */
    public static final String SEND_EMAIL_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "send_email_lock_key_";

    /**
     * 定时任务发送缓存锁
     */
    public static final String TASKS_EMAIL_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "tasks_email_lock_key_";

    /**
     * 邮件定时任务发送缓存锁
     */
    public static final String NEW_TASKS_EMAIL_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "new_tasks_email_lock_key_";


    /**
     * 发新闻给所有代理 邮件推送缓存锁
     */
    public static final String AGENT_NEWS_EMAIL_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "agent_news_email_lock_key";

    /**
     *  定时任务发送新闻邮件缓存锁
     */
    public static final String NEWS_TASKS_EMAIL_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "news_tasks_email_lock_key_";

    /**
     * offer解析 缓存锁
     */
    public static final String STUDENT_ANALYZE_OFFER_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "student_analyze_offer_lock_key";

    /**
     * 活动地点报名缓存锁
     */
    public static final String APP_AGENT_APPROVE_COMMENT_SEND_EMAIL_LOCK_KEY = SALE_CACHE_KEY_ROOT + "app_agent_approve_comment_send_email_lock_key_";


    /**
     * 定时任务 课程数据同步缓存锁
     */
    public static final String AUTO_UPDATE_INSTITUTION_COURSE_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "auto_update_institution_course_lock_cache_key";
    /**
     * 定时任务 学习计划数据同步缓存锁
     */
    public static final String AUTO_UPDATE_STUDENT_OFFER_ITEM_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "auto_update_student_offer_item_lock_cache_key";

    /**
     * 定时任务 留学保险数据同步缓存锁
     */
    public static final String AUTO_UPDATE_STUDENT_INSURANCE_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "auto_update_student_insurance_lock_cache_key";
    /**
     * 定时任务 留学保险数据同步缓存锁
     */
    public static final String AUTO_UPDATE_STUDENT_CASITA_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "auto_update_student_casita_lock_cache_key";


    /**
     * 定时任务 不存在新旧系统学校映射缓存key
     */
    public static final String NOT_EXIST_INSTITUTION_MAPPING_CACHE_KEY = SCHEDULE_CACHE_KEY_ROOT + "not_exist_course_institution_mapping_cache_key_";

    /**
     * 定时任务 学生信息锁定key
     */
    public static final String STUDENT_INFO_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "student_info_lock_key_";

    /**
     * 定时任务 代理联系人锁定key
     */
    public static final String AGENT_PERSON_INFO_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "agent_person_info_lock_key_";

    /**
     * 定时任务 紧急中止定时任务缓存锁
     */
    public static final String EMERGENCY_STOP_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "emergency_stop_lock_key";

    /**
     * 定时任务 紧急中止留学申请定时任务缓存锁
     */
    public static final String EMERGENCY_STOP_INSURANCE_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "emergency_stop_insurance_lock_key";
    /**
     * 定时任务 紧急中止留学申请定时任务缓存锁
     */
    public static final String EMERGENCY_STOP_CASITA_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "emergency_stop_casita_lock_key";


    /**
     * 工作流缓存key前缀
     */
    public static final String WORK_CACHE_KEY_ROOT = "work_admin_";
    /**
     * 工作流缓存key前缀
     */
    public static final String LEAVE_FORM_SUBMIT_LOCK_KEY = WORK_CACHE_KEY_ROOT + "leave_form_submit_lock_key_";


    public static final String STUDENT_OFFER_LOCK_KEY = WORK_CACHE_KEY_ROOT + "student_offer_lock_key_";

    /**
     * 定时任务发送队列提醒缓存锁
     */
    public static final String MESSAGE_TASK_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "message_task_lock_cache_key";
    /**
     * 定时任务更新年假提醒缓存锁
     */
    public static final String ANNUAL_LEAVE_UPDATE_TASK_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "annual_leave_update_task_lock_key";
    /**
     * 定时任务更新年假提醒缓存锁
     */
    public static final String DISEASE_VACATION_UPDATE_TASK_LOCK_KEY = SCHEDULE_CACHE_KEY_ROOT + "disease_vacation_update_task_lock_key";

    /**
     * 定时任务 不存同步aplorder状态key
     */
    public static final String NOT_EXIST_SALE_STUDENT_OFFER_CACHE_KEY = SCHEDULE_CACHE_KEY_ROOT + "not_exist_course_sale_student_offer_cache_key_";

    /**
     * 峰会报名缓存锁
     */
    public static final String CONVENTION_REGISTRATION_CACHE_KEY = SALE_CACHE_KEY_ROOT + "convention_registration_cache_key";
    /**
     * 床位安排缓存锁
     */
    public static final String CONFIGURATION_BED_CACHE_KEY = SALE_CACHE_KEY_ROOT + "configuration_bed_cache_key_";

    /**
     * 学校缓存key前缀
     */
    public static final String INSTITUTION_CACHE_KEY_ROOT = "institution_admin_";

    /**
     * 批量翻译学校课程缓存key
     */
    public static final String BATCH_TRANSLATION_INSTITUTION_AND_COURSE_INFO_CACHE_KEY = INSTITUTION_CACHE_KEY_ROOT + "batch_translation_institution_and_course_info_cache_key";

    /**
     * KPI方案统计缓存key前缀
     */
    public static final String KPI_PLAN_STATISTICS_KEY = "kpi_plan_statistics_key_";

    /**
     * 续约申请TOKEN前缀
     */
    public static final String SALE_AGENT_RENEWAL_PREFIX = "sale:agent:renewal:";

}

