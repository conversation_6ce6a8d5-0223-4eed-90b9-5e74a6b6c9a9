package com.get.common.constant;

/**
 * 系统常量：微服务名称管理
 */
public interface AppCenterConstant {
    /**
     * 前缀
     */
//	String APPLICATION_NAME_PREFIX = "get-";
    String APPLICATION_NAME_PREFIX = "";

    /**
     * 邮件模块名称
     */
    String APPLICATION_AIS_MAIL = APPLICATION_NAME_PREFIX + "ais-mail";

    /**
     * 获取邮件rocketmq
     */
    String APPLICATION_AIS_MAIL_FETCH_ROCKETMQ = APPLICATION_NAME_PREFIX + "ais-mail-fetch-rocketmq";

    /**
     * 网关模块名称
     */
    String APPLICATION_GATEWAY_NAME = APPLICATION_NAME_PREFIX + "gateway";
    /**
     * 授权模块名称
     */
    String APPLICATION_AUTHENTICATION_NAME = APPLICATION_NAME_PREFIX + "authentication";

    /**
     * 权限中心模块名称
     */
    String APPLICATION_PERMISSION_CENTER = APPLICATION_NAME_PREFIX + "permission-center";

    /**
     * 销售中心模块名称
     */
    String APPLICATION_SALE_CENTER = APPLICATION_NAME_PREFIX + "sale-center";

    /**
     * 会议中心模块名称
     */
    String APPLICATION_CONVENTION_CENTER = APPLICATION_NAME_PREFIX + "app-convention-center";

    /**
     * 财务中心模块名称
     */
    String APPLICATION_FINANCE_CENTER = APPLICATION_NAME_PREFIX + "finance-center";

    /**
     * 工作流中心模块名称
     */
    String APPLICATION_WORKFLOW_CENTER = APPLICATION_NAME_PREFIX + "workflow-center";

    /**
     * 办公中心模块名称
     */
    String APPLICATION_OFFICE_CENTER = APPLICATION_NAME_PREFIX + "office-center";
    /**
     * 学校中心模块名称
     */
    String APPLICATION_INSTITUTION_CENTER = APPLICATION_NAME_PREFIX + "institution-center";

    /**
     * 配置中心模块名称
     */
    String APPLICATION_PLATFORM_CONFIG_CENTER = APPLICATION_NAME_PREFIX + "platform-center";

    /**
     * 文件模块名称
     */
    String APPLICATION_FILE_CENTER = APPLICATION_NAME_PREFIX + "file-center";

    /**
     * 文件测试模块名称
     */
    String APPLICATION_FILES_CENTER = APPLICATION_NAME_PREFIX + "files-center";

    /**
     * 投票模块名称
     */
    String APPLICATION_VOTING_CENTER = APPLICATION_NAME_PREFIX + "voting-center";

    /**
     * 简历模块名称
     */
    String APPLICATION_RESUME_CENTER = APPLICATION_NAME_PREFIX + "resume-center";

    /**
     * 日志模块名称
     */
    String APPLICATION_LOG_NAME = APPLICATION_NAME_PREFIX + "log-center";

    /**
     * 帮助模块名称
     */
    String APPLICATION_HELP_CENTER = APPLICATION_NAME_PREFIX + "help-center";

    /**
     * 报表模块名称
     */
    String APPLICATION_REPORT_CENTER = APPLICATION_NAME_PREFIX + "report-center";

    /**
     * 提醒消息模块名称
     */
    String APPLICATION_REMINDER_CENTER = APPLICATION_NAME_PREFIX + "reminder-center";
    /**
     * 提醒消息模块名称
     */
    String APPLICATION_PARTNER_CENTER = APPLICATION_NAME_PREFIX + "partner-center";
    String APPLICATION_PLATFORM_CENTER = APPLICATION_NAME_PREFIX + "platform-center";
    /**
     * 翻译配置模块名称
     */
    String APPLICATION_REGISTRATION_CENTER = APPLICATION_NAME_PREFIX + "registration-center";

    /**
     * 考试模块名称
     */
    String APPLICATION_EXAM_CENTER = APPLICATION_NAME_PREFIX + "exam-center";

    /**
     * websocket推送模块名称
     */
    String APPLICATION_WEBSOCKET_NAME = APPLICATION_NAME_PREFIX + "websocket-center";

    /**
     * 接口文档模块名称
     */
    String APPLICATION_SWAGGER_NAME = APPLICATION_NAME_PREFIX + "swagger";

    /**
     * 调度任务模块名称
     */
    String APPLICATION_XXLJOB_NAME = APPLICATION_NAME_PREFIX + "xxljob";

    /**
     * 调度任务管理后台模块名称
     */
    String APPLICATION_XXLJOB_ADMIN_NAME = APPLICATION_NAME_PREFIX + "xxljob-admin";

    /**
     * springBootAdmin管理后台模块名称
     */
    String APPLICATION_BOOT_ADMIN_NAME = APPLICATION_NAME_PREFIX + "boot-admin";

    /**
     * 测试模块名称
     */
    String APPLICATION_SEATA_DEMO = APPLICATION_NAME_PREFIX + "seata-demo";

    /**
     * 学习档案模块
     */
    String APPLICATION_SCHOOL_GATE_CENTER = APPLICATION_NAME_PREFIX + "school-gate-center";

    /**
     * 竞赛中心模块
     */
    String APPLICATION_COMPETITION_CENTER = APPLICATION_NAME_PREFIX + "competition-center";

    /**
     * mps模块
     */
    String APPLICATION_MPS_CENTER = APPLICATION_NAME_PREFIX + "mps-center";

    /**
     * pmp模块
     */
    String APPLICATION_PMP_CENTER = APPLICATION_NAME_PREFIX + "pmp-center";

    /**
     * rocketmq 模块
     */

    String APPLICATION_MIDDLE_CENTER = APPLICATION_NAME_PREFIX + "middle-center";

    String APPLICATION_ROCKETMQ_CENTER = APPLICATION_NAME_PREFIX + "rocketmq-center";

    String APPLICATION_INSURANCE_CENTER = APPLICATION_NAME_PREFIX + "insurance-center";
}
