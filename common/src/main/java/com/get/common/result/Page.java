package com.get.common.result;

//import com.get.core.mybatis.support.Query;

import lombok.Data;

/**
 * @author: jack
 * @create: 2020-04-16
 * @verison: 1.0
 * @description: 数据分页类
 */
@Data
public class Page<T> {
    /**
     * 每页显示记录数
     */
    private Integer showCount; //每页显示记录数
    /**
     * 总页数
     */
    private int totalPage;        //总页数
    /**
     * 总记录数
     */
    private int totalResult;    //总记录数
    /**
     * 当前页数
     */

    private Integer currentPage;    //当前页
    /**
     * 当前记录起始索引
     */
    private int currentResult;    //当前记录起始索引

//    private T data;  //数据

    public Page() {
        this.currentPage = 1;//默认查询第一页
        this.showCount = 20;//默认查询20条数据
    }

    public void setAll(Integer totalResult) {
        this.totalResult = totalResult;
        if (this.showCount == 0) {
            this.showCount = 20;
//            this.totalPage = 0;
//            this.currentResult = 0;
        }
        this.totalPage = (this.totalResult + this.showCount - 1) / this.showCount;
        this.currentResult = (this.getCurrentPage() - 1) * this.showCount;
    }

    public void setCurrentPage(int currentPage) {
        this.currentPage = currentPage <= 0 ? 1 : currentPage;
    }

//    /**
//     * 将PageHelper分页后的list转为分页信息
//     */
//    public void restPage(List<T> list) {
//        PageInfo<T> pageInfo = new PageInfo<T>(list);
//        //总页数
//        this.setTotalPage(pageInfo.getPages());
//        //当前页
//        this.setCurrentPage(pageInfo.getPageNum());
//        //当前页的数量
////        this.setShowCount(pageInfo.getSize());
//        //总记录数
//        this.setTotalResult(new Long(pageInfo.getTotal()).intValue());
//        //当前记录起始索引
//        this.setCurrentResult((pageInfo.getPageNum() - 1) * pageInfo.getPageSize());
//    }

//    public Query convertToQuery(Page page)
//    {
//        Query query = new Query();
//        if(GeneralTool.isNotEmpty(page))
//        {
//            query.setCurrent(page.getCurrentPage());
//            query.setSize(page.getTotalResult());
//        }
//        return query;
//    }

}

