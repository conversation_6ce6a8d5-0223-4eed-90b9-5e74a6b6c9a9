Index: biz-service-ap/ais-platform-center-ap/src/main/java/com/get/aisplatformcenterap/vo/MLiveVo.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.get.aisplatformcenterap.vo;\r\n\r\nimport com.get.aisplatformcenterap.entity.MLiveEntity;\r\nimport io.swagger.annotations.ApiModelProperty;\r\nimport lombok.Data;\r\n\r\nimport java.util.Date;\r\nimport java.util.List;\r\n\r\n@Data\r\npublic class MLiveVo extends MLiveEntity {\r\n\r\n    @ApiModelProperty(\"文件guid(文档中心)\")\r\n    private String fileGuid;\r\n\r\n\r\n    @ApiModelProperty(\"主图-桶地址\")\r\n    private String fileKey;\r\n\r\n    @ApiModelProperty(\"直播附件-桶地址\")\r\n    private List<FileArray> liveFile;\r\n\r\n\r\n    @ApiModelProperty(\"公司编码\")\r\n    private String companyNum;\r\n\r\n\r\n    @ApiModelProperty(\"上架状态 0已下架/1待上架/2已上架\")\r\n    private String statusName;\r\n\r\n    @ApiModelProperty(\"状态：0未开始/1直播中/2已结束（有回放）/3已结束（无回放）/5已上架/6已下架\")\r\n    private String liveStatusName;\r\n    @ApiModelProperty(\"类型中文名称\")\r\n    private String typeNameChn;\r\n\r\n    @ApiModelProperty(\"直播类型\")\r\n    private String typeKey;\r\n\r\n    @ApiModelProperty(\"观看人数\")\r\n    private int liveNum;\r\n    @ApiModelProperty(\"订阅人数\")\r\n    private int appointmentNum;\r\n\r\n\r\n\r\n    public String getStatusName() {\r\n        if(getStatus()!=null){\r\n            if(getStatus().intValue()==0){\r\n                statusName=\"已下架\";\r\n            } else if (getStatus().intValue()==1) {\r\n                statusName=\"已上架\";\r\n            }\r\n        }\r\n        return statusName;\r\n    }\r\n\r\n    public void setStatusName(String statusName) {\r\n        this.statusName = statusName;\r\n    }\r\n\r\n    public String getLiveStatusName() {\r\n\r\n        if(getLiveStatus()!=null){\r\n            Date now = new Date();\r\n            Date startTime=getLiveTimeStart();\r\n            Date endTime=getLiveTimeEnd();\r\n            boolean resultFlag0=false;\r\n            if(startTime!=null){\r\n                int result0=startTime.compareTo(now);\r\n                if(result0>0){//startTime大\r\n                    resultFlag0=true;\r\n                }\r\n            }\r\n            boolean resultFlag1=false;\r\n            if(endTime!=null){\r\n                int result1=endTime.compareTo(now);\r\n                if(result1>0){//endTime 大\r\n                    resultFlag1=true;\r\n                }\r\n            }\r\n            if(getLiveStatus()!=null){\r\n                if(getLiveStatus().intValue()==0 && getTypeKey()!=null && getTypeKey().equals(\"live\") && resultFlag0 ){\r\n                    liveStatusName=\"未开始\";\r\n                } else if ( getLiveStatus().intValue()==1 || (\r\n                        getLiveStatus().intValue()==0 && getTypeKey()!=null && getTypeKey().equals(\"live\") && !resultFlag0 && resultFlag1)) {\r\n                    liveStatusName=\"直播中\";\r\n                } else if (getLiveStatus().intValue()==2 || (\r\n                        getLiveStatus().intValue()==0 && getTypeKey()!=null && getTypeKey().equals(\"live\")&& !resultFlag1 && (getIsLoop()!=null && getIsLoop() ))) {\r\n                    liveStatusName=\"已结束（有回放）\";\r\n                }else if (getLiveStatus().intValue()==3 || (\r\n                        getLiveStatus().intValue()==0 && getTypeKey()!=null && getTypeKey().equals(\"live\") && !resultFlag1)) {\r\n                    liveStatusName=\"已结束（无回放）\";\r\n                }\r\n            }\r\n\r\n        }\r\n\r\n\r\n        return liveStatusName;\r\n    }\r\n\r\n    public void setLiveStatusName(String liveStatusName) {\r\n        this.liveStatusName = liveStatusName;\r\n    }\r\n\r\n\r\n}\r\n
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service-ap/ais-platform-center-ap/src/main/java/com/get/aisplatformcenterap/vo/MLiveVo.java b/biz-service-ap/ais-platform-center-ap/src/main/java/com/get/aisplatformcenterap/vo/MLiveVo.java
--- a/biz-service-ap/ais-platform-center-ap/src/main/java/com/get/aisplatformcenterap/vo/MLiveVo.java	(revision 224f4735142aff2e7c99cc1b8c2683ae658f3f7e)
+++ b/biz-service-ap/ais-platform-center-ap/src/main/java/com/get/aisplatformcenterap/vo/MLiveVo.java	(date 1755164508330)
@@ -1,5 +1,7 @@
 package com.get.aisplatformcenterap.vo;
 
+import com.baomidou.mybatisplus.annotation.TableField;
+import com.fasterxml.jackson.annotation.JsonProperty;
 import com.get.aisplatformcenterap.entity.MLiveEntity;
 import io.swagger.annotations.ApiModelProperty;
 import lombok.Data;
@@ -41,6 +43,28 @@
     @ApiModelProperty("订阅人数")
     private int appointmentNum;
 
+    /**
+     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
+     * <p> 数据库类型:varchar(200) &nbsp;&nbsp;&nbsp;&nbsp;
+     * <p> 注释:适用相关学校 &nbsp;&nbsp;&nbsp;&nbsp;
+     */
+    @ApiModelProperty("适用相关学校")
+    @JsonProperty("targetInstitutions")
+    private String targetInstitutions;
+
+    /**
+     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
+     * <p> 数据库类型:varchar(100) &nbsp;&nbsp;&nbsp;&nbsp;
+     * <p> 注释:适用相关国家地区 &nbsp;&nbsp;&nbsp;&nbsp;
+     */
+    @ApiModelProperty("适用相关国家地区")
+    @JsonProperty("targetAreas")
+    private String targetAreas;
+
+    @ApiModelProperty("讲师职位")
+    private String teacherJob;
+
+
 
 
     public String getStatusName() {
Index: biz-service-ap/ais-platform-center-ap/src/main/java/com/get/aisplatformcenterap/entity/MLiveEntity.java
IDEA additional info:
Subsystem: com.intellij.openapi.diff.impl.patch.BaseRevisionTextPatchEP
<+>package com.get.aisplatformcenterap.entity;\r\n\r\nimport com.baomidou.mybatisplus.annotation.TableName;\r\nimport com.get.core.mybatis.base.BaseEntity;\r\nimport com.get.core.mybatis.base.BaseVoEntity;\r\nimport io.swagger.annotations.ApiModelProperty;\r\nimport lombok.Data;\r\n\r\nimport javax.validation.constraints.NotNull;\r\nimport java.io.Serializable;\r\nimport java.util.Date;\r\n\r\n/**\r\n * \r\n * @TableName m_live\r\n */\r\n@Data\r\n@TableName(\"m_live\")\r\npublic class MLiveEntity extends BaseEntity implements Serializable {\r\n    @ApiModelProperty(\"公司Id\")\r\n    private Long fkCompanyId;\r\n\r\n    @ApiModelProperty(\"平台ID\")\r\n    private Long fkPlatformId;\r\n\r\n    @ApiModelProperty(\"平台应用CODE\")\r\n    private String fkPlatformCode;\r\n\r\n\r\n    @ApiModelProperty(\"直播类型Id\")\r\n    private Long fkLiveTypeId;\r\n\r\n    @ApiModelProperty(\"培训标题\")\r\n    private String title;\r\n\r\n\r\n    @ApiModelProperty(\"课程编码\")\r\n    private String num;\r\n\r\n\r\n    @ApiModelProperty(\"老师英文(拼音)名\")\r\n    private String teacherName;\r\n\r\n\r\n    @ApiModelProperty(\"讲师中文名\")\r\n    private String teacherNameChn;\r\n\r\n\r\n    @ApiModelProperty(\"性别:0女/1男\")\r\n    private String teacherGender;\r\n\r\n\r\n    @ApiModelProperty(\"讲师职位\")\r\n    private String teacherJob;\r\n\r\n\r\n    @ApiModelProperty(\"讲师简介\")\r\n    private String teacherBrief;\r\n\r\n\r\n    @ApiModelProperty(\"直播开始时间\")\r\n    @NotNull(message = \"直播开始时间不能为空\",groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})\r\n    private Date liveTimeStart;\r\n\r\n\r\n    @ApiModelProperty(\"直播结束时间\")\r\n    private Date liveTimeEnd;\r\n\r\n\r\n\r\n    @ApiModelProperty(\"直播链接\")\r\n    private String liveUrl;\r\n\r\n\r\n    @ApiModelProperty(\"回播链接\")\r\n    private String loopUrl;\r\n\r\n    @ApiModelProperty(\"(保留备用)上架状态 0已下架/1已上架\")\r\n    private Integer status;\r\n\r\n    @ApiModelProperty(\"状态：0未开始/1直播中/2已结束（有回放）/3已结束（无回放）\")\r\n    private Integer liveStatus;\r\n\r\n    @ApiModelProperty(value = \"是否回播：0否/1是\")\r\n    private Boolean  isLoop;\r\n\r\n    private String description;\r\n\r\n}
Subsystem: com.intellij.openapi.diff.impl.patch.CharsetEP
<+>UTF-8
===================================================================
diff --git a/biz-service-ap/ais-platform-center-ap/src/main/java/com/get/aisplatformcenterap/entity/MLiveEntity.java b/biz-service-ap/ais-platform-center-ap/src/main/java/com/get/aisplatformcenterap/entity/MLiveEntity.java
--- a/biz-service-ap/ais-platform-center-ap/src/main/java/com/get/aisplatformcenterap/entity/MLiveEntity.java	(revision 224f4735142aff2e7c99cc1b8c2683ae658f3f7e)
+++ b/biz-service-ap/ais-platform-center-ap/src/main/java/com/get/aisplatformcenterap/entity/MLiveEntity.java	(date 1755163660369)
@@ -1,6 +1,8 @@
 package com.get.aisplatformcenterap.entity;
 
+import com.baomidou.mybatisplus.annotation.TableField;
 import com.baomidou.mybatisplus.annotation.TableName;
+import com.fasterxml.jackson.annotation.JsonProperty;
 import com.get.core.mybatis.base.BaseEntity;
 import com.get.core.mybatis.base.BaseVoEntity;
 import io.swagger.annotations.ApiModelProperty;
@@ -34,7 +36,7 @@
     private String title;
 
 
-    @ApiModelProperty("课程编码")
+    @ApiModelProperty("直播编号（暂无用）")
     private String num;
 
 
@@ -86,4 +88,25 @@
 
     private String description;
 
+
+    /**
+     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
+     * <p> 数据库类型:varchar(200) &nbsp;&nbsp;&nbsp;&nbsp;
+     * <p> 注释:适用相关学校 &nbsp;&nbsp;&nbsp;&nbsp;
+     */
+    @ApiModelProperty("适用相关学校")
+    @JsonProperty("targetInstitutions")
+    @TableField("target_institutions")
+    private String targetInstitutions;
+
+    /**
+     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
+     * <p> 数据库类型:varchar(100) &nbsp;&nbsp;&nbsp;&nbsp;
+     * <p> 注释:适用相关国家地区 &nbsp;&nbsp;&nbsp;&nbsp;
+     */
+    @ApiModelProperty("适用相关国家地区")
+    @JsonProperty("targetAreas")
+    @TableField("target_areas")
+    private String targetAreas;
+
 }
\ No newline at end of file
