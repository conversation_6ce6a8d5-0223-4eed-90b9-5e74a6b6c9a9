package com.get.insurancecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @Author:Oliver
 * @Date: 2025/7/25
 * @Version 1.0
 * @apiNote:
 */
@Data
@TableName("m_credit_card_statement")
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CreditCardStatement extends BaseEntity {

    @ApiModelProperty("信用卡Id")
    private Long fkCreditCardId;

    @ApiModelProperty("交易流水号")
    private String num;

    @ApiModelProperty("业务类型：0调整/1支出/2收取/3退款")
    private Integer businessType;

    @ApiModelProperty("关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty("关联类型Id（目标类型表对应记录项Id）")
    private Long relationTargetId;

    @ApiModelProperty("交易币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty("交易金额")
    private BigDecimal amount;

    @ApiModelProperty("公式汇率（微信收取费用）")
    private BigDecimal exchangeRateFormula;

    @ApiModelProperty("公式汇率系数（微信收取费用）")
    private BigDecimal exchangeRateFactorFormula;

    @ApiModelProperty("公式手续费（如：100）（微信收取费用）")
    private BigDecimal serviceFeeFormula;

    @ApiModelProperty("公式手续费系数（如：1.006）（微信收取费用）")
    private BigDecimal serviceFeeFactorFormula;

    @ApiModelProperty("公式说明（微信收取费用）")
    private String noteFormula;

    @ApiModelProperty("公式金额（微信收取费用）-实收金额")
    private BigDecimal amountRmbFormula;

    @ApiModelProperty("汇率（信用卡支付费用）-实付的汇率")
    private BigDecimal exchangeRateRmb;

    @ApiModelProperty("交易金额（信用卡支付费用）-实付金额")
    private BigDecimal amountRmb;

    @ApiModelProperty("交易状态：0失败/1成功")
    private Integer status;

    @ApiModelProperty("备注")
    private String remark;
}
