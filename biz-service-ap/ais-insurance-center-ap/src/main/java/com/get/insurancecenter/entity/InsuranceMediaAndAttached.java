package com.get.insurancecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @Author:Oliver
 * @Date: 2025/6/19
 * @Version 1.0
 * @apiNote:
 */
@Data
@TableName("s_media_and_attached ")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InsuranceMediaAndAttached extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "文件guid(文档中心)")
    private String fkFileGuid;

    @ApiModelProperty(value = "表名")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    @ApiModelProperty(value = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    private String typeKey;

    @ApiModelProperty(value = "索引值(默认从0开始，同一类型下值唯一)")
    private Integer indexKey;

    @ApiModelProperty(value = "链接")
    private String link;

    @ApiModelProperty(value = "备注")
    private String remark;
}
