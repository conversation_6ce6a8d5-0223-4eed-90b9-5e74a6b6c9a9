package com.get.aismail.dto;


import com.get.aismail.entity.AreaRegion;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 业务区域返回类
 *
 * <AUTHOR>
 * @date 2021/7/28 14:59
 */
@Data
public class AreaRegionDto extends AreaRegion {

    /**
     * 州省id
     */
    @ApiModelProperty(value = "州省id")
    private Long fkAreaStateId;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;
}
