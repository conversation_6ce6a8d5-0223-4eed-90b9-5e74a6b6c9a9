package com.get.aismail.dto;


import com.get.aismail.entity.AreaState;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/27 12:26
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "州省返回类")
public class AreaStateDto extends AreaState {
    /**
     * 州省对应的城市
     */
    @ApiModelProperty(value = "州省对应的城市")
    List<AreaCityDto> areaCityDtos;
    /**
     * 表示州省类型
     */
    @ApiModelProperty(value = "表示州省类型")
    private String type;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;


    /**
     * 业务区域
     */
    @ApiModelProperty(value = "业务区域")
    private List<AreaRegionDto> areaRegion;
}
