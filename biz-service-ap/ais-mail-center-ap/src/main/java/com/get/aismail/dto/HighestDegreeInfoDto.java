package com.get.aismail.dto;

import com.get.core.mybatis.base.BaseSelectEntity;
import lombok.Data;

@Data
public class HighestDegreeInfoDto {
    // 学历等级类型
    private BaseSelectEntity educationLevelType;
    // 在读/毕业学习名称
    private String fkInstitutionNameEducation;
    // 毕业学校国家
    private AreaCountryDto fkAreaCountryIdEducation;
    // 毕业学校省份
    private AreaStateDto fkAreaStateIdEducation;
    // 毕业学校市
    private AreaCityDto fkAreaCityIdEducation;
    // 毕业专业
    private String educationMajor;
    // 毕业学习类型
    private String institutionTypeEducation;
    public HighestDegreeInfoDto(){}
    public HighestDegreeInfoDto(BaseSelectEntity educationLevelType, String fkInstitutionNameEducation, AreaCountryDto fkAreaCountryIdEducation, AreaStateDto fkAreaStateIdEducation, AreaCityDto fkAreaCityIdEducation, String educationMajor, String institutionTypeEducation) {
        this.educationLevelType = educationLevelType;
        this.fkInstitutionNameEducation = fkInstitutionNameEducation;
        this.fkAreaCountryIdEducation = fkAreaCountryIdEducation;
        this.fkAreaStateIdEducation = fkAreaStateIdEducation;
        this.fkAreaCityIdEducation = fkAreaCityIdEducation;
        this.educationMajor = educationMajor;
        this.institutionTypeEducation = institutionTypeEducation;
    }
}
