package com.get.aismail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.time.LocalDateTime;

@Data
@TableName("m_file_sale")
public class MFileSale {
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "文件Id")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "文件guid")
    @Column(name = "file_guid")
    private String fileGuid;

    @ApiModelProperty(value = "源文件类型")
    @Column(name = "file_type_orc")
    private String fileTypeOrc;

    @ApiModelProperty(value = "源文件名")
    @Column(name = "file_name_orc")
    private String fileNameOrc;

    @ApiModelProperty(value = "目标文件名")
    @Column(name = "file_name")
    private String fileName;

    @ApiModelProperty(value = "目标文件路径")
    @Column(name = "file_path")
    private String filePath;

    @ApiModelProperty(value = "文件外部存储Key（如：腾讯云COS）")
    @Column(name = "file_key")
    private String fileKey;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "gmt_create")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "创建用户(登录账号)")
    @Column(name = "gmt_create_user")
    private String gmtCreateUser;

    @ApiModelProperty(value = "修改时间")
    @Column(name = "gmt_modified")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "修改用户(登录账号)")
    @Column(name = "gmt_modified_user")
    private String gmtModifiedUser;
}
