package com.get.aismail.dto;


import com.get.aismail.entity.AreaCity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/7/28 11:00
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "城市返回类")
public class AreaCityDto extends AreaCity {
    /**
     * 表示城市类型
     */
    @ApiModelProperty(value = "表示城市类型")
    private String type;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;

    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;

}
