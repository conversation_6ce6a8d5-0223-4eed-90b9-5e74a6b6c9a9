package com.get.aismail.dto;

import lombok.Data;

import java.util.Map;

@Data
public class GradesInfoDto {
    // 高中成绩
    private Map<String, Object> highSchoolTestType;
    // 高中评分成绩
    private String highSchoolTestScore;
    // 本科成绩
    private Map<String, Object> standardTestType;
    // 本科评分成绩
    private String standardTestScore;
    // 研究生成绩
    private Map<String, Object> masterTestType;
    // 研究生评分成绩
    private String masterTestScore;
    // 英语测试类型
    private Map<String, Object> englishTestType;
    // 英语测试成绩
    private String englishTestScore;
    public GradesInfoDto(){}
    public GradesInfoDto(Map<String, Object> highSchoolTestType, String highSchoolTestScore, Map<String, Object> standardTestType, String standardTestScore, Map<String, Object> masterTestType, String masterTestScore, Map<String, Object> englishTestType, String englishTestScore) {
        this.highSchoolTestType = highSchoolTestType;
        this.highSchoolTestScore = highSchoolTestScore;
        this.standardTestType = standardTestType;
        this.standardTestScore = standardTestScore;
        this.masterTestType = masterTestType;
        this.masterTestScore = masterTestScore;
        this.englishTestType = englishTestType;
        this.englishTestScore = englishTestScore;
    }
}
