package com.get.aismail.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.time.LocalDateTime;

@Data
@TableName("M_MAIL_ACCOUNT")
public class MMailAccount {

    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "唯一主键")
    @Column(name = "id")
    private Long id;

    @ApiModelProperty(value = "平台应用CODE，AIS / PARTNER")
    @Column(name = "fk_platform_code")
    private String fkPlatformCode;

    @ApiModelProperty(value = "平台应用对应的创建用户Id，AIS=fk_staff_id / PARTNER=fk_partner_user_id")
    @Column(name = "fk_platform_user_id")
    private Long fkPlatformUserId;

    @ApiModelProperty(value = "email账户")
    @Column(name = "email_account")
    private String emailAccount;

    @ApiModelProperty(value = "email密码")
    @Column(name = "email_password")
    private String emailPassword;

    @ApiModelProperty(value = "邮箱类型Key，系统定义")
    @Column(name = "email_type")
    private String emailType;

    @ApiModelProperty(value = "是否默认邮箱，0否/1是")
    @Column(name = "is_default")
    private Boolean isDefault;

    @ApiModelProperty(value = "创建时间")
    @Column(name = "gmt_create")
    private LocalDateTime gmtCreate;

    @ApiModelProperty(value = "创建用户(登录账号)")
    @Column(name = "gmt_create_user")
    private String gmtCreateUser;

    @ApiModelProperty(value = "修改时间")
    @Column(name = "gmt_modified")
    private LocalDateTime gmtModified;

    @ApiModelProperty(value = "修改用户(登录账号)")
    @Column(name = "gmt_modified_user")
    private String gmtModifiedUser;
}
