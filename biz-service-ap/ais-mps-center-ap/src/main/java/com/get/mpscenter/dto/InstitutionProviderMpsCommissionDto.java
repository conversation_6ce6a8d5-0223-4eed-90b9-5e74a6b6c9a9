package com.get.mpscenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

@Data
public class InstitutionProviderMpsCommissionDto extends BaseVoEntity {

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("学校id(提供商列表搜索使用)")
    private Long fkInstitutionCommissionId;

    @ApiModelProperty("关键字搜索")
    private String keyWord;

    @ApiModelProperty("提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty("学校id(学校列表使用)")
    private Long fkInstitutionId;

    @ApiModelProperty("适用学校提供商（前端忽略）")
    private Set<Long> suitInstitutionProviderIds;

}
