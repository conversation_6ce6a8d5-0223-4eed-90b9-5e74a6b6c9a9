package com.get.mpscenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@TableName("u_mps_key_word")
@Data
public class MpsKeyWord extends BaseEntity implements Serializable {

    @ApiModelProperty("类型：INPUT")
    private String typeKey;

    @ApiModelProperty("关键字")
    private String keyWord;

    @ApiModelProperty("排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
