package com.get.mpscenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@TableName("m_institution_provider_mps_commission")
@Data
public class InstitutionProviderMpsCommission extends BaseEntity implements Serializable {

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("合同编号")
    private String contractNum;

    @ApiModelProperty("佣金")
    private String commission;

    @ApiModelProperty("佣金说明")
    private String commissionNote;

    @ApiModelProperty("代理佣金")
    private String agentCommission;

    @ApiModelProperty("代理佣金说明")
    private String agentCommissionNote;

    @ApiModelProperty("后续佣金")
    private String followCommission;

    @ApiModelProperty("后续佣金说明")
    private String followCommissionNote;

    @ApiModelProperty("代理后续佣金")
    private String agentFollowCommission;

    @ApiModelProperty("代理后续佣金说明")
    private String agentFollowCommissionNote;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty("对应MPS Id（GEA），多个用英文逗号隔开")
    private String idGea;

    @ApiModelProperty("是否激活：0否/1是")
    private Boolean isActive;

    @ApiModelProperty("课程模式,0=Push/1=Direct")
    private Integer programmesMode;

    @ApiModelProperty("学校提供商佣金Id（复制/同步来源）")
    private Long fkInstitutionProviderMpsCommissionIdFrom;

    @ApiModelProperty("是否适用：0不适用/1适用/2待确认")
    private Integer useStatus;
}
