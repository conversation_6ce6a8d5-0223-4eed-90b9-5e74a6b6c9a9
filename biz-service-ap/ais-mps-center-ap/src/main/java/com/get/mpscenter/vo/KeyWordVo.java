package com.get.mpscenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class KeyWordVo extends BaseVoEntity {

    private Set<Long> majorLevelIds;

    private List<String> majorLevelNames;

    @ApiModelProperty("类型：INPUT")
    private String typeKey;

    @ApiModelProperty("关键字")
    private String keyWord;

    @ApiModelProperty("排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
