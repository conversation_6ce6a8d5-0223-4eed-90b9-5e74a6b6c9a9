package com.get.mpscenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InstitutionProviderCommissionVo extends BaseVoEntity {

    @ApiModelProperty("提供商名字")
    private String fkInstitutionProviderName;

    @ApiModelProperty("提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty("佣金条件")
    private String title;

    @ApiModelProperty("代理后续佣金")
    private String agentFollowCommission;

    @ApiModelProperty("代理后续佣金说明")
    private String agentFollowCommissionNote;

    @ApiModelProperty("代理佣金")
    private String agentCommission;

    @ApiModelProperty("代理佣金说明")
    private String agentCommissionNote;
}
