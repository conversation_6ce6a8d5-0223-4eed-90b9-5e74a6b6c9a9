package com.get.mpscenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Set;

@Data
public class InstitutionProviderMpsCommissionVo extends BaseVoEntity {

    @ApiModelProperty("公司名称")
    private String fkCompanyNames;

    @ApiModelProperty("公司Ids")
    private Set<Long> fkCompanyIds;

    @ApiModelProperty("学校Ids")
    private Set<Long> fkInstitutionIds;

    @ApiModelProperty("学校名称")
    private List<String> fkInstitutionNames;

    @ApiModelProperty("课程等级ids")
    private Set<Long> majorLevels;

    @ApiModelProperty("合约覆盖国家Ids")
    private Set<Long> fkAreaCountryIds;

    @ApiModelProperty("课程等级名称")
    private List<String> majorLevelNames;

    @ApiModelProperty("合约覆盖国家")
    private List<String> areaCountryNames;

    @ApiModelProperty("合约覆盖国家Ids")
    private Set<Long> excludeAreaCountryIds;

    @ApiModelProperty("合约除外国家")
    private List<String> excludeAreaCountryNames;

    @ApiModelProperty("学校提供商名字")
    private String fkInstitutionProviderName;

    @ApiModelProperty("课程模式名称")
    private String programmesModeName;

    @ApiModelProperty("年份")
    private Integer year;

    @ApiModelProperty("学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("合同编号")
    private String contractNum;

    @ApiModelProperty("佣金")
    private String commission;

    @ApiModelProperty("佣金说明")
    private String commissionNote;

    @ApiModelProperty("代理佣金")
    private String agentCommission;

    @ApiModelProperty("代理佣金说明")
    private String agentCommissionNote;

    @ApiModelProperty("后续佣金")
    private String followCommission;

    @ApiModelProperty("后续佣金说明")
    private String followCommissionNote;

    @ApiModelProperty("代理后续佣金")
    private String agentFollowCommission;

    @ApiModelProperty("代理后续佣金说明")
    private String agentFollowCommissionNote;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty("对应MPS Id（GEA），多个用英文逗号隔开")
    private String idGea;

    @ApiModelProperty("是否激活：0否/1是")
    private Boolean isActive;

    @ApiModelProperty("课程模式,0=Push/1=Direct")
    private Integer programmesMode;

    @ApiModelProperty("学校提供商佣金Id（复制/同步来源）")
    private Long fkInstitutionProviderMpsCommissionIdFrom;

    @ApiModelProperty("是否适用：0不适用/1适用/2待确认")
    private Integer useStatus;
}
