package com.get.mpscenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Set;

@Data
public class MpsCommissionDto extends BaseVoEntity implements Serializable {

    @ApiModelProperty("年份")
    @NotNull(message = "年份", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Integer year;

    @ApiModelProperty("学校提供商Id")
    @NotNull(message = "学校提供商Id", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Long fkInstitutionProviderId;

    @ApiModelProperty("标题")
    @NotNull(message = "标题title", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String title;

    @ApiModelProperty("合同编号")
    private String contractNum;

    @ApiModelProperty("佣金")
    private String commission;

    @ApiModelProperty("佣金说明")
    private String commissionNote;

    @ApiModelProperty("代理佣金")
    private String agentCommission;

    @ApiModelProperty("代理佣金说明")
    private String agentCommissionNote;

    @ApiModelProperty("后续佣金")
    private String followCommission;

    @ApiModelProperty("后续佣金说明")
    private String followCommissionNote;

    @ApiModelProperty("代理后续佣金")
    private String agentFollowCommission;

    @ApiModelProperty("代理后续佣金说明")
    private String agentFollowCommissionNote;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty("对应MPS Id（GEA），多个用英文逗号隔开")
    private String idGea;

    @ApiModelProperty("是否激活：0否/1是")
    @NotNull(message = "是否激活：0否/1是", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Boolean isActive;

    @ApiModelProperty("公司ids")
    @NotNull(message = "公司ids", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Set<Long> fkCompanyIds;

    @ApiModelProperty("业务国家Id")
    private Set<Long> fkAreaCountryIds;

    @ApiModelProperty("是否包括：1包括/-1除外")
    private Boolean isInclude;

    @ApiModelProperty("学校ids")
    private Set<Long> fkInstitutionIds;

    @ApiModelProperty("专业等级ids")
    private Set<Long> fkMajorLevelIds;

    @ApiModelProperty("课程模式：0=Push/1=Direct")
    private Integer programmesMode;

    @ApiModelProperty("学校提供商佣金Id（复制/同步来源）")
    private Long fkInstitutionProviderMpsCommissionIdFrom;
}
