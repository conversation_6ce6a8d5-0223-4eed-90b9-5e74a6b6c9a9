package com.get.mpscenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName("m_institution_provider")
@Alias("mpsInstitutionProvider")
public class InstitutionProvider extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 国家Id（所在地）
     */
    @ApiModelProperty(value = "国家Id（所在地）")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 州省Id（所在地）
     */
    @ApiModelProperty(value = "州省Id（所在地）")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 城市Id（所在地）
     */
    @ApiModelProperty(value = "城市Id（所在地）")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 学校提供商类型Id：渠道=1，集团=2，学校=3
     */
    @ApiModelProperty(value = "学校提供商类型Id：渠道=1，集团=2，学校=3")
    @Column(name = "fk_institution_provider_type_id")
    private Long fkInstitutionProviderTypeId;
    /**
     * 所属集团Id
     */
    @ApiModelProperty(value = "所属集团Id")
    @Column(name = "fk_institution_group_id")
    @UpdateWithNull
    private Long fkInstitutionGroupId;
    /**
     * 渠道来源Id
     */
    @ApiModelProperty(value = "渠道来源Id")
    @Column(name = "fk_institution_channel_id")
    private Long fkInstitutionChannelId;
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @Column(name = "num")
    private String num;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;
    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zip_code")
    private String zipCode;
    /**
     * 电邮
     */
    @ApiModelProperty(value = "电邮")
    @Column(name = "email")
    private String email;
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    @Column(name = "address")
    private String address;
    /**
     * 申请佣金截止时间
     */
    @ApiModelProperty(value = "申请佣金截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "app_commission_deadline")
    private Date appCommissionDeadline;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;
}