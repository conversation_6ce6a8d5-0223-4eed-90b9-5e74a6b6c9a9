package com.get.mpscenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("COLLEGE6")
public class College {

    @ApiModelProperty(value = "isview")
    private Long isview;

    @ApiModelProperty(value = "tubs")
    private String tubs;

    @ApiModelProperty("主键id")
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;

    private String name;


}
