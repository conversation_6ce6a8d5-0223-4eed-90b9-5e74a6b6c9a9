package com.get.mpscenter.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@TableName("COMMISSION_RATE6")
public class CommissionRate {

    @ApiModelProperty("主键id")
    @TableId(
            value = "id",
            type = IdType.AUTO
    )
    private Long id;

    private String course;

    private String remark;

    private String rate;

    private String rateRemark;

    private String geaAsf;

    private String geaAsfRemark;

    private String followAsf;

    private String followAsfRemark;

    private String geafollowAsf;

    private String geafollowAsfRemark;

    private String sourcegea;

    private String sourcehk;

    private String sourceiae;

    private String sourcetw;

    private Long collegeId;
}
