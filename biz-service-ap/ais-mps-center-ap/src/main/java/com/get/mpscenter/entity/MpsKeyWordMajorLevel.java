package com.get.mpscenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@TableName("r_mps_key_word_major_level")
@Data
public class MpsKeyWordMajorLevel extends BaseEntity implements Serializable {

    @ApiModelProperty("常用关键字库Id")
    private Long fkMpsKeyWordId;


    @ApiModelProperty("专业等级Id")
    private Long fkMajorLevelId;
}
