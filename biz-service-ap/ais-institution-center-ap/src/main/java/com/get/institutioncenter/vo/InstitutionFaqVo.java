package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 11:56
 * @Description:
 **/
@Data
@ApiModel("常见问题返回类")
public class InstitutionFaqVo extends BaseEntity {
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    //===========实体类InstitutionFaq==================
    private static final long serialVersionUID = 1L;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 问题
     */
    @ApiModelProperty(value = "问题")
    @Column(name = "question")
    private String question;
    /**
     * 回答
     */
    @ApiModelProperty(value = "回答")
    @Column(name = "answer")
    private String answer;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkInstitutionId=").append(fkInstitutionId);
        sb.append(", question=").append(question);
        sb.append(", answer=").append(answer);
        sb.append(", viewOrder=").append(viewOrder);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
