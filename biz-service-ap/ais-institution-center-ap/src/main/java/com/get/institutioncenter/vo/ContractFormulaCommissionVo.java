package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * @author: Sea
 * @create: 2021/4/23 17:36
 * @verison: 1.0
 * @description:
 */
@Data
public class ContractFormulaCommissionVo extends BaseEntity {

    //===============实体类ContractFormulaCommission================
    private static final long serialVersionUID = 1L;
    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    @Column(name = "fk_contract_formula_id")
    private Long fkContractFormulaId;
    /**
     * 期数，从1开始，按顺序生成，并按顺序排序
     */
    @ApiModelProperty(value = "期数，从1开始，按顺序生成，并按顺序排序")
    @Column(name = "step")
    private Integer step;
    /**
     * 佣金比例
     */
    @ApiModelProperty(value = "佣金比例")
    @Column(name = "commission_rate")
    private BigDecimal commissionRate;
    /**
     * 代理佣金比例
     */
    @ApiModelProperty(value = "代理佣金比例")
    @Column(name = "commission_rate_ag")
    private BigDecimal commissionRateAg;
    /**
     * 固定金额
     */
    @ApiModelProperty(value = "固定金额")
    @Column(name = "fixed_amount")
    private BigDecimal fixedAmount;
    /**
     * 代理固定金额
     */
    @ApiModelProperty(value = "代理固定金额")
    @Column(name = "fixed_amount_ag")
    private BigDecimal fixedAmountAg;
    /**
     * 佣金上限
     */
    @ApiModelProperty(value = "佣金上限")
    @Column(name = "limit_amount")
    private BigDecimal limitAmount;
    /**
     * 代理佣金上限
     */
    @ApiModelProperty(value = "代理佣金上限")
    @Column(name = "limit_amount_ag")
    private BigDecimal limitAmountAg;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
}
