package com.get.institutioncenter.po;

import com.get.institutioncenter.entity.AreaCityInfoType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2021/3/1 17:07
 * @verison: 1.0
 * @description:
 */
@Data
public class AreaCityInfoTypePo extends AreaCityInfoType {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    //============实体类AreaCityInfoType===============
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

}
