package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2020/12/7
 * @TIME: 16:09
 * @Description:
 **/
public class InstitutionCourseEngScoreVo extends BaseEntity {

    //===============实体类InstitutionCourseEngScore=================
    private static final long serialVersionUID = 1L;
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;
    /**
     * 条件类型，枚举：IELTS/TOEFL-IBT/TOEFL-PBT/PTE
     */
    @ApiModelProperty(value = "条件类型，枚举：IELTS/TOEFL-IBT/TOEFL-PBT/PTE")
    @Column(name = "condition_type")
    private Integer conditionType;
    /**
     * 总分
     */
    @ApiModelProperty(value = "总分")
    @Column(name = "overall")
    private BigDecimal overall;
    /**
     * 听
     */
    @ApiModelProperty(value = "听")
    @Column(name = "listening")
    private BigDecimal listening;
    /**
     * 讲
     */
    @ApiModelProperty(value = "讲")
    @Column(name = "speaking")
    private BigDecimal speaking;
    /**
     * 读
     */
    @ApiModelProperty(value = "读")
    @Column(name = "reading")
    private BigDecimal reading;
    /**
     * 写
     */
    @ApiModelProperty(value = "写")
    @Column(name = "writing")
    private BigDecimal writing;
    /**
     * 成绩描述
     */
    @ApiModelProperty(value = "成绩描述")
    @Column(name = "description")
    private String description;

    @ApiModelProperty(value = "成绩描述")
    @Column(name = "description_source")
    private String descriptionSource;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkInstitutionCourseId=").append(fkInstitutionCourseId);
        sb.append(", conditionType=").append(conditionType);
        sb.append(", overall=").append(overall);
        sb.append(", listening=").append(listening);
        sb.append(", speaking=").append(speaking);
        sb.append(", reading=").append(reading);
        sb.append(", writing=").append(writing);
        sb.append(", description=").append(description);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }

}
