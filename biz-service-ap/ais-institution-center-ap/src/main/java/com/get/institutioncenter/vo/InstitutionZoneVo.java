package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2021/1/15
 * @TIME: 15:25
 * @Description:
 **/
@Data
@ApiModel("学校校区返回类")
public class InstitutionZoneVo extends BaseEntity {
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;
    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String areaCountryName;

    /**
     * 州省名称
     */
    @ApiModelProperty(value = "州省名称")
    private String areaStateName;

    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String areaCityName;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id")
    private Long fkInstitutionCourseId;

    //===============实体类InstitutionZone====================
    private static final long serialVersionUID = 1L;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 校区名称
     */
    @ApiModelProperty(value = "校区名称")
    @Column(name = "name")
    private String name;
    /**
     * 校区中文名称
     */
    @ApiModelProperty(value = "校区中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    @Column(name = "description")
    private String description;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
}
