package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/27 12:26
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "州省返回类")
public class AreaStateVo extends BaseEntity {
    /**
     * 州省对应的城市
     */
    @ApiModelProperty(value = "州省对应的城市")
    List<AreaCityVo> areaCityDtos;
    /**
     * 表示州省类型
     */
    @ApiModelProperty(value = "表示州省类型")
    private String type;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;


    /**
     * 业务区域
     */
    @ApiModelProperty(value = "业务区域")
    private List<AreaRegionVo> areaRegion;

    //================实体类AreaState=======================
    private static final long serialVersionUID = 1L;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 州省编号
     */
    @ApiModelProperty(value = "州省编号")
    @Column(name = "num")
    private String num;
    /**
     * 州省名称
     */
    @ApiModelProperty(value = "州省名称")
    @Column(name = "name")
    private String name;
    /**
     * 州省中文名称
     */
    @ApiModelProperty(value = "州省中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;



}
