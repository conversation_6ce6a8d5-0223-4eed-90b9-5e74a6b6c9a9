package com.get.institutioncenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 15:35
 */
@Data
public class InstitutionDeadlineInfoVo2 extends BaseEntity {
    @ApiModelProperty("学校名")
    private String fkInstitutionName;

    @ApiModelProperty("公开对象名称")
    private String publicLevelName;

    @ApiModelProperty("学校封面url")
    private String coversUrl;

    @ApiModelProperty("学校中文名")
    private String fkInstitutionNameZh;

    @ApiModelProperty("学校英文名")
    private String fkInstitutionNameEn;

    @ApiModelProperty("最新创建时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date newGmtCreate;

    //==============实体类InstitutionDeadlineInfo2===================
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "title")
    private String title;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Column(name = "description")
    private String description;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @Column(name = "publish_time")
    private Date publishTime;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
}
