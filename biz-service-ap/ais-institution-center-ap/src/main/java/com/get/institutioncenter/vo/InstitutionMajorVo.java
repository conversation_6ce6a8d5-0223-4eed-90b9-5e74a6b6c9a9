package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/6
 * @TIME: 11:45
 * @Description:
 **/
@Data
@ApiModel("专业学科返回类")
public class InstitutionMajorVo extends BaseEntity {
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;
    /**
     * 学院名称
     */
    @ApiModelProperty(value = "学院名称")
    private String institutionFacultyName;
    /**
     * 专业等级名称
     */
    @ApiModelProperty(value = "专业等级名称")
    private String majorLevelName;
    /**
     * 文件下载链接
     */
    @ApiModelProperty(value = "专业学科附件")
    private List<MediaAndAttachedVo> mediaAndAttachedDtos;

    //===============实体类InstitutionMajor=================
    private static final long serialVersionUID = 1L;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 学校学院Id
     */
    @ApiModelProperty(value = "学校学院Id")
    @Column(name = "fk_institution_faculty_id")
    private Long fkInstitutionFacultyId;
    /**
     * 专业等级Id
     */
    @ApiModelProperty(value = "专业等级Id")
    @Column(name = "fk_major_level_id")
    private Long fkMajorLevelId;
    /**
     * 专业编号
     */
    @ApiModelProperty(value = "专业编号")
    @Column(name = "num")
    private String num;
    /**
     * 专业名称
     */
    @ApiModelProperty(value = "专业名称")
    @Column(name = "name")
    private String name;
    /**
     * 专业中文名称
     */
    @ApiModelProperty(value = "专业中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 排名信息
     */
    @ApiModelProperty(value = "排名信息")
    @Column(name = "ranking")
    private String ranking;
    /**
     * 学费信息
     */
    @ApiModelProperty(value = "学费信息")
    @Column(name = "fee")
    private String fee;
    /**
     * 总学期时长信息
     */
    @ApiModelProperty(value = "总学期时长信息")
    @Column(name = "duration")
    private String duration;
    /**
     * 核心课程
     */
    @ApiModelProperty(value = "核心课程")
    @Column(name = "core_course")
    private String coreCourse;
    /**
     * 录取标准
     */
    @ApiModelProperty(value = "录取标准")
    @Column(name = "entry_standards")
    private String entryStandards;
    /**
     * 职业发展
     */
    @ApiModelProperty(value = "职业发展")
    @Column(name = "occupation_development")
    private String occupationDevelopment;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkInstitutionId=").append(fkInstitutionId);
        sb.append(", fkInstitutionFacultyId=").append(fkInstitutionFacultyId);
        sb.append(", fkMajorLevelId=").append(fkMajorLevelId);
        sb.append(", num=").append(num);
        sb.append(", name=").append(name);
        sb.append(", nameChn=").append(nameChn);
        sb.append(", ranking=").append(ranking);
        sb.append(", fee=").append(fee);
        sb.append(", duration=").append(duration);
        sb.append(", coreCourse=").append(coreCourse);
        sb.append(", entryStandards=").append(entryStandards);
        sb.append(", occupationDevelopment=").append(occupationDevelopment);
        sb.append(", isActive=").append(isActive);
        sb.append(", viewOrder=").append(viewOrder);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
