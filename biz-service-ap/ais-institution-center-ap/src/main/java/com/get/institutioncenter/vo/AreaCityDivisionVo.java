package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2021/3/3 14:48
 * @verison: 1.0
 * @description:
 */
@Data
public class AreaCityDivisionVo extends BaseEntity {
    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String areaCityName;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;

    //=============实体类AreaCityDivision===============
    private static final long serialVersionUID = 1L;
    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 区域名称
     */
    @ApiModelProperty(value = "区域名称")
    @Column(name = "name")
    private String name;
    /**
     * 街道名称，逗号隔开，如：洪桥街道,北京街道,六榕街道
     */
    @ApiModelProperty(value = "街道名称，逗号隔开，如：洪桥街道,北京街道,六榕街道")
    @Column(name = "streets_name")
    private String streetsName;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
