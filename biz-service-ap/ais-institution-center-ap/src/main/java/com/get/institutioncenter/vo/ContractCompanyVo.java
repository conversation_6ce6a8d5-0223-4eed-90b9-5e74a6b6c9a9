package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/10/29
 * @TIME: 10:20
 * @Description:
 **/
@Data
public class ContractCompanyVo extends BaseEntity {

    //==============实体类ContractCompany===============
    private static final long serialVersionUID = 1L;
    /**
     * 合同Id
     */
    @ApiModelProperty(value = "合同Id")
    @Column(name = "fk_contract_id")
    private Long fkContractId;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
}
