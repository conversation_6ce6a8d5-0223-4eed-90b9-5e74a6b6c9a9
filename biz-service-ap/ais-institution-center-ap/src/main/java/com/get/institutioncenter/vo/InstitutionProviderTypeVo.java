package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2020/8/12 11:04
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel("学校提供商类型返回类")
public class InstitutionProviderTypeVo extends BaseEntity {

    //=============实体类InstitutionProviderType========================
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 类型key（系统枚举定义）
     */
    @ApiModelProperty(value = "类型key（系统枚举定义）")
    @Column(name = "type_key")
    private String typeKey;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
