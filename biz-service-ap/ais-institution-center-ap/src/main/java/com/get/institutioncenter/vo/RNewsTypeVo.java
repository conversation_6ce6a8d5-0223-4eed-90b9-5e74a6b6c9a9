package com.get.institutioncenter.vo;

import com.get.common.annotion.TableDto;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2020/8/6 11:42
 * @verison: 1.0
 * @description:
 */
@Data
public class RNewsTypeVo extends BaseEntity {
    /**
     * 新闻类型名称
     */
    @ApiModelProperty(value = "新闻类型名称")
    @TableDto(tableName = "u_news_type", columnDto = "type_name", entityColumnDto = "newsTypeName", columnDtoMainId = "fk_news_type_id")
    private String newsTypeName;
    /**
     * 目标类型名称
     */
    @ApiModelProperty(value = "目标类型名称")
    private String targetTypeName;
    /**
     * 目标名称
     */
    @ApiModelProperty(value = "目标名称")
    private String targetName;

    //===================实体类RNewsType=======================
    private static final long serialVersionUID = 1L;
    /**
     * 新闻Id
     */
    @ApiModelProperty(value = "新闻Id")
    @Column(name = "fk_news_id")
    private Long fkNewsId;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;
    /**
     * 新闻类型Id
     */
    @ApiModelProperty(value = "新闻类型Id")
    @Column(name = "fk_news_type_id")
    private Long fkNewsTypeId;
}
