package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/3
 * @TIME: 15:30
 * @Description:学院dto
 **/
@Data
@ApiModel("学校学院返回类")
public class InstitutionFacultyVo extends BaseEntity {
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;
    /**
     * 文件下载链接
     */
    @ApiModelProperty(value = "学院附件")
    private List<MediaAndAttachedVo> mediaAndAttachedDtos;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id")
    private Long fkInstitutionCourseId;

    //=============实体类InstitutionFaculty=================
    private static final long serialVersionUID = 1L;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 学院名称
     */
    @ApiModelProperty(value = "学院名称")
    @Column(name = "name")
    private String name;
    /**
     * 学院中文名称
     */
    @ApiModelProperty(value = "学院中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    @Column(name = "description")
    private String description;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkInstitutionId=").append(fkInstitutionId);
        sb.append(", name=").append(name);
        sb.append(", nameChn=").append(nameChn);
        sb.append(", description=").append(description);
        sb.append(", viewOrder=").append(viewOrder);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }

}
