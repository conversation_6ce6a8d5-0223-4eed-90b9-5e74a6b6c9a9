package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/10
 * @TIME: 14:54
 * @Description:知名校友dto
 **/
@Data
@ApiModel("学校知名校友返回类")
public class InstitutionAlumnusVo extends BaseEntity {
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;
    /**
     * 文件下载链接
     */
    @ApiModelProperty(value = "校友附件信息")
    private List<MediaAndAttachedVo> mediaAndAttachedDtos;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;


    //============实体类InstitutionAlumnus=================
    private static final long serialVersionUID = 1L;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Column(name = "name")
    private String name;
    /**
     * 简历
     */
    @ApiModelProperty(value = "简历")
    @Column(name = "resume")
    private String resume;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkInstitutionId=").append(fkInstitutionId);
        sb.append(", name=").append(name);
        sb.append(", resume=").append(resume);
        sb.append(", viewOrder=").append(viewOrder);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }

}
