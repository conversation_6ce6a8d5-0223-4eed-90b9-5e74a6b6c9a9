package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 16:13
 * @Description:
 **/
@Data
@ApiModel("学校课程返回类")
public class InstitutionCourseVo extends BaseEntity {
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;
    /**
     * 学校类型名称
     */
    @ApiModelProperty(value = "学校类型名称")
    private String fkInstitutionTypeName;
    /**
     * 学院名称
     */
    @ApiModelProperty(value = "学院名称")
    private String institutionFacultyName;
    /**
     * 文件下载链接
     */
    @ApiModelProperty(value = "课程附件信息")
    private List<MediaAndAttachedVo> mediaAndAttachedDtos;
    /**
     * 新闻
     */
    @ApiModelProperty(value = "学校新闻")
    private List<NewsVo> newsDtos;
    /**
     * 专业等级名称
     */
    @ApiModelProperty(value = "专业等级名称")
    private String majorLevelName;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;

    /**
     * 学校学院数组
     */
    @ApiModelProperty(value = "学校学院数组")
    private List<Long> fkInstitutionFacultyIds;

    /**
     * 专业等级数组
     */
    @ApiModelProperty(value = "专业等级数组")
    private List<Long> fkMajorLevelIds;

    /**
     * 课程类型数组
     */
    @ApiModelProperty(value = "课程类型数组")
    private List<Long> fkCourseTypeIds;
    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;

    /**
     * 数据等级名称
     */
    @ApiModelProperty(value = "数据等级名称")
    private String dataLevelName;

    /**
     * 校区名称
     */
    @ApiModelProperty(value = "校区名称")
    private String zoneName;

    /**
     * 官网
     */
    @ApiModelProperty(value = "官网")
    private String webSite;

    /**
     * 校区id数组
     */
    @ApiModelProperty(value = "校区id数组")
    @NotNull(message = "校区id数组不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private List<Long> fkInstitutionZoneIds;

    /**
     * 币种类型
     */
    @ApiModelProperty(value = "币种类型")
    private String fkCurrencyTypeNum;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeNumName;
    /**
     * 类型Key
     */
    @ApiModelProperty(value = "学校类型key")
    private String fkInstitutionTypeKey;

    private String typeValue;

    //===============实体类InstitutionCourse======================
    private static final long serialVersionUID = 1L;
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;
    /**
     * 课程code（校方编号）
     */
    @ApiModelProperty(value = "课程code（校方编号）")
    @Column(name = "code")
    private String code;
    /**
     * 显示名称
     */
    @ApiModelProperty(value = "显示名称")
    @Column(name = "name_display")
    private String nameDisplay;
    /**
     * 课程编号
     */
    @ApiModelProperty(value = "课程编号")
    @Column(name = "num")
    private String num;
    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    @Column(name = "name")
    private String name;
    /**
     * 课程中文名称
     */
    @ApiModelProperty(value = "课程中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 开学月份
     */
    @ApiModelProperty(value = "开学月份")
    @Column(name = "start_month")
    private String startMonth;

    /**
     * 申请开始时间
     */
  /*  @ApiModelProperty(value = "申请开始时间")
    @Column(name = "apply_start_date")
    private Date applyStartDate;*/

    /**
     * 申请截止时间
     */
  /*  @ApiModelProperty(value = "申请截止时间")
    @Column(name = "apply_end_date")
    private Date applyEndDate;*/
    /**
     * 开学时间描述
     */
    @ApiModelProperty(value = "开学时间描述")
    @Column(name = "start_date_note")
    private String startDateNote;
    /**
     * 申请月份，多月逗号分隔：9
     */
    @ApiModelProperty(value = "申请月份，多月逗号分隔：9")
    @Column(name = "apply_month")
    private String applyMonth;
    /**
     * 申请时间描述
     */
    @ApiModelProperty(value = "申请时间描述")
    @Column(name = "apply_date_note")
    private String applyDateNote;
    /**
     * 课程学费
     */
    @ApiModelProperty(value = "课程学费")
    @Column(name = "fee")
    private BigDecimal fee;
    /**
     * 课程学费（最高）
     */
    @ApiModelProperty(value = "课程学费（最高）")
    @Column(name = "fee_max")
    private BigDecimal feeMax;
    /**
     * 课程学费说明
     */
    @ApiModelProperty(value = "课程学费说明")
    @Column(name = "fee_note")
    private String feeNote;
    /**
     * 课程总时长（年）
     */
    @ApiModelProperty(value = "课程总时长（年）")
    @Column(name = "duration_year")
    private BigDecimal durationYear;
    /**
     * 课程总时长说明
     */
    @ApiModelProperty(value = "课程总时长说明")
    @Column(name = "duration_note")
    private String durationNote;
    /**
     * 简介
     */
    @ApiModelProperty(value = "简介")
    @Column(name = "introduction")
    private String introduction;

    @ApiModelProperty(value = "是否直录：0否/1是")
    @Column(name = "is_direct")
    private Boolean isDirect;

    /**
     * 核心课程
     */
    @ApiModelProperty(value = "核心课程")
    @Column(name = "core_course")
    private String coreCourse;
    /**
     * 录取标准
     */
    @ApiModelProperty(value = "录取标准")
    @Column(name = "entry_standards")
    private String entryStandards;
    /**
     * 职业发展
     */
    @ApiModelProperty(value = "职业发展")
    @Column(name = "occupation_development")
    private String occupationDevelopment;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;
    /**
     * 数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整
     */
    @ApiModelProperty(value = "数据等级：0旧数据导入/1官方数据导入/2需要补充/3采集完整")
    @Column(name = "data_level")
    private Integer dataLevel;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;
    /**
     * 课程学费（统一为人民币，主要是学费筛选使用）
     */
    @ApiModelProperty(value = "课程学费（统一为人民币，主要是学费筛选使用）")
    @Column(name = "fee_cny")
    private BigDecimal feeCny;
}
