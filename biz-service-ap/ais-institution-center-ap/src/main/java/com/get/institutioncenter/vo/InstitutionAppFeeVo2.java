package com.get.institutioncenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/6 9:51
 */
@Data
@ApiModel("学校奖学金信息返回类")
public class InstitutionAppFeeVo2  {
    @ApiModelProperty("学校名称")
    private String fkInstitutionName;

    @ApiModelProperty("公开对象名称")
    private String publicLevelName;

    @ApiModelProperty("学校封面url")
    private String coversUrl;

    @ApiModelProperty("学校中文名")
    private String fkInstitutionNameZh;

    @ApiModelProperty("学校英文名")
    private String fkInstitutionNameEn;

    @ApiModelProperty
    private String levelTypeName;

    //===================实体类InstitutionAppFee2======================
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;

    @ApiModelProperty("等级类型：0本科/1硕士/2社区")
    @Column(name = "level_type")
    private Integer levelType;

    /**
     * 标题
     */
    @ApiModelProperty(value = "标题")
    @Column(name = "title")
    private String title;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    @Column(name = "description")
    private String description;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    @Column(name = "public_level")
    private String publicLevel;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间")
    @Column(name = "publish_time")
    private Date publishTime;

    @ApiModelProperty(value = "是否免申请费，0否/1是")
    @Column(name = "is_free")
    private Boolean isFree;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;

    @ApiModelProperty(value = "缴纳时间信息")
    @Column(name = "payment_time_info")
    private String paymentTimeInfo;

    @ApiModelProperty(value = "链接信息")
    @Column(name = "url_info")
    private String urlInfo;


    private static final long serialVersionUID = 1L;

}
