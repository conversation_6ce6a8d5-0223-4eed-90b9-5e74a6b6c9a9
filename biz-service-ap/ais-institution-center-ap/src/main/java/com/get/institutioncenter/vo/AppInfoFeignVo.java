package com.get.institutioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/10
 * @TIME: 14:47
 * @Description:
 **/
@Data
public class AppInfoFeignVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 表Id集合
     */
    @ApiModelProperty(value = "表Id集合")
    private List<Long> fkTableIds;
    /**
     * 类型关键字，如：app_course_website
     */
    @ApiModelProperty(value = "类型关键字，如：app_course_website")
    @Column(name = "type_key")
    private String typeKey;
}
