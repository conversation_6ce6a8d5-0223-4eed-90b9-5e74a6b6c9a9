package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Hardy
 * @create: 2023/12/15 12:43
 * @verison: 1.0
 * @description:
 *
 *
 * 
 */
@Data
public class InstitutionCourseSubjectVo extends BaseEntity {

    //==============实体类InstitutionCourseSubject====================
    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    @Column(name = "fk_institution_course_id")
    private Long fkInstitutionCourseId;

    /**
     * 科目名称
     */
    @ApiModelProperty(value = "科目名称")
    @Column(name = "subject_name")
    private String subjectName;

    /**
     * 科目名称（中文）
     */
    @ApiModelProperty(value = "科目名称（中文）")
    @Column(name = "subject_name_chn")
    private String subjectNameChn;

    /**
     * 类型模式
     */
    @ApiModelProperty(value = "类型模式")
    @Column(name = "mode")
    private String mode;

    /**
     * 排序，顺序
     */
    @ApiModelProperty(value = "排序，顺序")
    @Column(name = "view_order")
    private Integer viewOrder;

    private static final long serialVersionUID = 1L;
}
