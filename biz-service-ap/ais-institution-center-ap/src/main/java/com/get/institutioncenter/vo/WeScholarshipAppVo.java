package com.get.institutioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
public class WeScholarshipAppVo {

    @ApiModelProperty(value = "国家id")
    @NotNull(message = "国家id不能为空")
    private Long countryId;

    @ApiModelProperty(value = "学校")
    @NotNull(message = "学校id不能为空")
    private Long institutionId;

    @ApiModelProperty(value = "学院id")
    private Long facultyId;


    @ApiModelProperty(value = "课程组别id")
    private Long courseGroupId;

    @ApiModelProperty(value = "课程类型id")
    private Long courseTypeId;


    @ApiModelProperty(value = "课程级别id")
    private Long courseLevelId;


    @ApiModelProperty(value = "课程id")
    @NotNull(message = "课程id不能为空")
    private Long courseId;
}
