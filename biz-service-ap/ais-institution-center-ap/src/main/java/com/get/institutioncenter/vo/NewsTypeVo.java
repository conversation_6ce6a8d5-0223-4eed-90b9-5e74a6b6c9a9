package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2020/8/6 11:42
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "新闻类型返回类")
public class NewsTypeVo extends BaseEntity {

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    //===============实体类NewsType===============
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
