package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/7
 * @TIME: 11:05
 * @Description:
 **/
@Data
@ApiModel("课程类型返回类")
public class CourseTypeVo extends BaseEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 课程类型组别ids
     */
    @ApiModelProperty(value = "课程类型组别ids")
    private List<Long> courseTypeGroupIds;

    /**
     * 课程类型组别名字
     */
    @ApiModelProperty(value = "课程类型组别名字")
    private String fkCourseTypeGroupName;

    /**
     * 学校课程id
     */
    @ApiModelProperty(value = "学校课程id")
    private Long fkInstitutionCourseId;

    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;

    //================实体类CourseType================
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称中文")
    @Column(name = "type_name_chn")
    private String typeNameChn;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    @ApiModelProperty(value = "匹配优先序，1/2/3，数字越小越优先")
    @Column(name = "matching_priority")
    private Integer matchingPriority;

    @ApiModelProperty(value = "公开对象")
    @Column(name = "public_level")
    private String publicLevel;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", typeName=").append(typeName);
        sb.append(", viewOrder=").append(viewOrder);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
