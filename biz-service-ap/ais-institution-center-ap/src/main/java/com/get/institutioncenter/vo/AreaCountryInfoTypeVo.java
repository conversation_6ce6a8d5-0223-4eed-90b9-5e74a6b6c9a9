package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2021/1/13 16:41
 * @verison: 1.0
 * @description:
 */
@Data
public class AreaCountryInfoTypeVo extends BaseEntity {

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    //===========实体类AreaCountryInfoType==================
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
