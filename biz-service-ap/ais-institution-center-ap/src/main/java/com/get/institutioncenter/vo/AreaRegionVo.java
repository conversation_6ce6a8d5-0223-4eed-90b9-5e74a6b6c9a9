package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;


/**
 * 业务区域返回类
 *
 * <AUTHOR>
 * @date 2021/7/28 14:59
 */
@Data
public class AreaRegionVo extends BaseEntity {

    /**
     * 州省id
     */
    @ApiModelProperty(value = "州省id")
    private Long fkAreaStateId;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    //=================实体类AreaRegion=======================
    private static final long serialVersionUID = 1L;
    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 大区编号
     */
    @ApiModelProperty(value = "大区编号")
    @Column(name = "num")
    private String num;
    /**
     * 大区名称
     */
    @ApiModelProperty(value = "大区名称")
    @Column(name = "name")
    private String name;
    /**
     * 大区中文名称
     */
    @ApiModelProperty(value = "大区中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

}
