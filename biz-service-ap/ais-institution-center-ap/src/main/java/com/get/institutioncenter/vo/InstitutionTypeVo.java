package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2020/7/30 11:12
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel("学校类型返回类")
public class InstitutionTypeVo extends BaseEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    //============实体类InstitutionType===================
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称中文")
    @Column(name = "type_name_chn")
    private String typeNameChn;
    /**
     * 类型Key
     */
    @ApiModelProperty(value = "类型Key")
    @Column(name = "type_key")
    private String typeKey;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    @Column(name = "view_order")
    private Integer viewOrder;

}
