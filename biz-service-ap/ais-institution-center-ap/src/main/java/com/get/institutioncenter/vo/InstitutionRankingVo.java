package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.ArrayList;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/5/25 11:17
 */
@Data
public class InstitutionRankingVo extends BaseEntity {


    @ApiModelProperty("国家ids")
    private String fkCountryIds;

    @ApiModelProperty("多专业大类名称")
    private String groupNameChn;

    @ApiModelProperty("标题")
    private String title;

    @ApiModelProperty("性质")
    private String nature;

    @ApiModelProperty("州")
    private String stateName;

    @ApiModelProperty("专业小类集合")
    private List<InstitutionRankingVo> institutionRankingDtos =new ArrayList<>();

    //=================实体类InstitutionRanking=====================
    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    @Column(name = "year")
    private Integer year;

    /**
     * 排名类型：QS=0/TIMES=1/THE=2/USNews=3/Macleans=4/ARWU=5/QS专业排名=6
     */
    @ApiModelProperty(value = "排名类型：QS=0/TIMES=1/THE=2/USNews=3/Macleans=4/ARWU=5/QS专业排名=6")
    @Column(name = "ranking_type")
    private Integer rankingType;

    /**
     * 排名范围（下限）
     */
    @ApiModelProperty(value = "排名范围（下限）")
    @Column(name = "ranking_min")
    private Integer rankingMin;

    /**
     * 排名范围（上限）
     */
    @ApiModelProperty(value = "排名范围（上限）")
    @Column(name = "ranking_max")
    private Integer rankingMax;

    /**
     * 排名描述
     */
    @ApiModelProperty(value = "排名描述")
    @Column(name = "ranking_note")
    private String rankingNote;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;

    /**
     * 院校名称
     */
    @ApiModelProperty(value = "院校名称")
    @Column(name = "institution_name")
    private String institutionName;

    /**
     * 院校名称（中文）
     */
    @ApiModelProperty(value = "院校名称（中文）")
    @Column(name = "institution_name_chn")
    private String institutionNameChn;

    /**
     * 学校性质
     */
    @ApiModelProperty(value = "学校性质")
    @Column(name = "institution_nature")
    private String institutionNature;


    /**
     * 国家id
     */
    @ApiModelProperty(value = "国家id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    @Column(name = "area_country_name")
    private String areaCountryName;

    /**
     * 专业大类id
     */
    @ApiModelProperty(value = "专业大类id")
    @Column(name = "fk_course_type_group_id")
    private Long fkCourseTypeGroupId;

    /**
     * 专业大类名称
     */
    @ApiModelProperty(value = "专业大类名称")
    @Column(name = "course_type_group_name")
    private String courseTypeGroupName;

    /**
     * 专业大类名称（中文）
     */
    @ApiModelProperty(value = "专业大类名称（中文）")
    @Column(name = "course_type_group_name_chn")
    private String courseTypeGroupNameChn;

    /**
     * 专业小类id
     */
    @ApiModelProperty(value = "专业小类id")
    @Column(name = "fk_course_type_id")
    private Long fkCourseTypeId;

    /**
     * 专业小类名称
     */
    @ApiModelProperty(value = "专业小类名称")
    @Column(name = "course_type_name")
    private String courseTypeName;

    /**
     * 专业小类名称（中文）
     */
    @ApiModelProperty(value = "专业小类名称（中文）")
    @Column(name = "course_type_name_chn")
    private String courseTypeNameChn;

    private static final long serialVersionUID = 1L;
}
