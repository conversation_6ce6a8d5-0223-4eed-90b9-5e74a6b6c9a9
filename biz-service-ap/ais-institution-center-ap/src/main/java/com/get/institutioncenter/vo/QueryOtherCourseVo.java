package com.get.institutioncenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author:cream
 * @Date: 2023/7/18  9:53
 */
@Data
public class QueryOtherCourseVo {

    @NotNull(message = "id不能为null")
    @ApiModelProperty("主键id")
    private Long fkTableId;

    @NotBlank(message = "请传递搜索类型")
    @ApiModelProperty("区分奖学金，申请费，截止信息")
    private String fkTypeName;

    @ApiModelProperty("课程id")
    private Long courseId;
}
