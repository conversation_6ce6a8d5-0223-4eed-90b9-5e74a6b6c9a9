package com.get.institutioncenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @Description：课程类型组别返回类
 * @Param
 * @return
 * @Date 11:02 2021/4/27
 * <AUTHOR>
 */
@Data
@ApiModel("课程类型组别返回类")
public class CourseTypeGroupVo extends BaseEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;


    /**
     * 表名
     */
    @ApiModelProperty(value = "分类模式名称")
    private String modeName;

    @ApiModelProperty(value = "公开对象名字")
    private String publicLevelName;

    //================实体类CourseTypeGroup=========================
    private static final long serialVersionUID = 1L;
    /**
     * 类型组别名称
     */
    @ApiModelProperty(value = "类型组别名称")
    @Column(name = "type_group_name")
    private String typeGroupName;
    /**
     * 类型组别名称
     */
    @ApiModelProperty(value = "类型组别名称中文")
    @Column(name = "type_group_name_chn")
    private String typeGroupNameChn;
    /**
     * 分类模式
     */
    @ApiModelProperty(value = "分类模式")
    @Column(name = "mode")
    private Integer mode;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    @ApiModelProperty(value = "公开对象")
    @Column(name = "public_level")
    private String publicLevel;


}
