package com.get.institutioncenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2021/6/4 12:43
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "批量翻译结果返回类")
public class BatchTranslationResultVo {

    /**
     * 翻译数量
     */
    @ApiModelProperty(value = "翻译数量")
    Integer translationCount;

    /**
     * 预计时间
     */
    @ApiModelProperty(value = "预计时间")
    Integer estimatedTime;

    /**
     * 完成数量
     */
    @ApiModelProperty(value = "完成数量")
    Integer completeCount;

    /**
     * 失败数量
     */
    @ApiModelProperty(value = "失败数量")
    Integer errorCount;
}
