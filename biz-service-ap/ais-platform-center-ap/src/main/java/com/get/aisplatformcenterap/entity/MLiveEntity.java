package com.get.aisplatformcenterap.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 
 * @TableName m_live
 */
@Data
@TableName("m_live")
public class MLiveEntity extends BaseEntity implements Serializable {
    @ApiModelProperty("公司Id")
    private Long fkCompanyId;

    @ApiModelProperty("平台ID")
    private Long fkPlatformId;

    @ApiModelProperty("平台应用CODE")
    private String fkPlatformCode;


    @ApiModelProperty("直播类型Id")
    private Long fkLiveTypeId;

    @ApiModelProperty("培训标题")
    private String title;


    @ApiModelProperty("直播编号（暂无用）")
    private String num;


    @ApiModelProperty("老师英文(拼音)名")
    private String teacherName;


    @ApiModelProperty("讲师中文名")
    private String teacherNameChn;


    @ApiModelProperty("性别:0女/1男")
    private String teacherGender;


    @ApiModelProperty("讲师职位")
    private String teacherJob;


    @ApiModelProperty("讲师简介")
    private String teacherBrief;


    @ApiModelProperty("直播开始时间")
    @NotNull(message = "直播开始时间不能为空",groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private Date liveTimeStart;


    @ApiModelProperty("直播结束时间")
    private Date liveTimeEnd;



    @ApiModelProperty("直播链接")
    private String liveUrl;


    @ApiModelProperty("回播链接")
    private String loopUrl;

    @ApiModelProperty("(保留备用)上架状态 0已下架/1已上架")
    private Integer status;

    @ApiModelProperty("状态：0未开始/1直播中/2已结束（有回放）/3已结束（无回放）")
    private Integer liveStatus;

    @ApiModelProperty(value = "是否回播：0否/1是")
    private Boolean  isLoop;

    private String description;


    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(200) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:适用相关学校 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("适用相关学校")
    @JsonProperty("targetInstitutions")
    @TableField("target_institutions")
    private String targetInstitutions;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(100) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:适用相关国家地区 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("适用相关国家地区")
    @JsonProperty("targetAreas")
    @TableField("target_areas")
    private String targetAreas;

}