package com.get.aisplatformcenterap.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.aisplatformcenterap.entity.MLiveEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class MLiveVo extends MLiveEntity {

    @ApiModelProperty("文件guid(文档中心)")
    private String fileGuid;


    @ApiModelProperty("主图-桶地址")
    private String fileKey;

    @ApiModelProperty("直播附件-桶地址")
    private List<FileArray> liveFile;


    @ApiModelProperty("公司编码")
    private String companyNum;


    @ApiModelProperty("上架状态 0已下架/1待上架/2已上架")
    private String statusName;

    @ApiModelProperty("状态：0未开始/1直播中/2已结束（有回放）/3已结束（无回放）/5已上架/6已下架")
    private String liveStatusName;
    @ApiModelProperty("类型中文名称")
    private String typeNameChn;

    @ApiModelProperty("直播类型")
    private String typeKey;

    @ApiModelProperty("观看人数")
    private int liveNum;
    @ApiModelProperty("订阅人数")
    private int appointmentNum;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(200) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:适用相关学校 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("适用相关学校")
    @JsonProperty("targetInstitutions")
    private String targetInstitutions;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:varchar(100) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:适用相关国家地区 &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty("适用相关国家地区")
    @JsonProperty("targetAreas")
    private String targetAreas;

    @ApiModelProperty("讲师职位")
    private String teacherJob;




    public String getStatusName() {
        if(getStatus()!=null){
            if(getStatus().intValue()==0){
                statusName="已下架";
            } else if (getStatus().intValue()==1) {
                statusName="已上架";
            }
        }
        return statusName;
    }

    public void setStatusName(String statusName) {
        this.statusName = statusName;
    }

    public String getLiveStatusName() {

        if(getLiveStatus()!=null){
            Date now = new Date();
            Date startTime=getLiveTimeStart();
            Date endTime=getLiveTimeEnd();
            boolean resultFlag0=false;
            if(startTime!=null){
                int result0=startTime.compareTo(now);
                if(result0>0){//startTime大
                    resultFlag0=true;
                }
            }
            boolean resultFlag1=false;
            if(endTime!=null){
                int result1=endTime.compareTo(now);
                if(result1>0){//endTime 大
                    resultFlag1=true;
                }
            }
            if(getLiveStatus()!=null){
                if(getLiveStatus().intValue()==0 && getTypeKey()!=null && getTypeKey().equals("live") && resultFlag0 ){
                    liveStatusName="未开始";
                } else if ( getLiveStatus().intValue()==1 || (
                        getLiveStatus().intValue()==0 && getTypeKey()!=null && getTypeKey().equals("live") && !resultFlag0 && resultFlag1)) {
                    liveStatusName="直播中";
                } else if (getLiveStatus().intValue()==2 || (
                        getLiveStatus().intValue()==0 && getTypeKey()!=null && getTypeKey().equals("live")&& !resultFlag1 && (getIsLoop()!=null && getIsLoop() ))) {
                    liveStatusName="已结束（有回放）";
                }else if (getLiveStatus().intValue()==3 || (
                        getLiveStatus().intValue()==0 && getTypeKey()!=null && getTypeKey().equals("live") && !resultFlag1)) {
                    liveStatusName="已结束（无回放）";
                }
            }

        }


        return liveStatusName;
    }

    public void setLiveStatusName(String liveStatusName) {
        this.liveStatusName = liveStatusName;
    }


}
