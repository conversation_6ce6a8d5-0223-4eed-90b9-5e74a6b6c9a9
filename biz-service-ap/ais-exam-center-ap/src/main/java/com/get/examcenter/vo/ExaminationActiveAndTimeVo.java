package com.get.examcenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * Created by <PERSON>.
 * Time: 9:58
 * Date: 2021/8/24
 * Description:激活状态以及考试时间返回类
 */
@Data
@ApiModel("激活状态以及考试时间返回类")
public class ExaminationActiveAndTimeVo {
    /**
     * 考卷是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean examinationPaperActive;

    /**
     * 考卷开放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "开放时间")
    private Date examinationPaperStartTime;

    /**
     * 考卷结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date examinationPaperEndTime;

    /**
     * 考试是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean examinationActive;


    /**
     * 考试开放时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "开放时间")
    private Date examinationStartTime;

    /**
     * 考试结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date examinationEndTime;

    /**
     * 考卷是否允许重考：0否/1是
     */
    @ApiModelProperty(value = "是否允许重考：0否/1是")
    private Boolean isRetest;

    /**
     * 考卷Id
     */
    @ApiModelProperty(value = "考卷Id")
    private Long fkExaminationPaperId;

    /**
     * 考试Id
     */
    @ApiModelProperty(value = "考试Id")
    private Long fkExaminationId;

    /**
     * 题目数量
     */
    @ApiModelProperty(value = "题目数量")
    private Integer questionCount;
}
