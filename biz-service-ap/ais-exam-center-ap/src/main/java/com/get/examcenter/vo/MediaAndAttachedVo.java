package com.get.examcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * Created by <PERSON>.
 * Time: 18:03
 * Date: 2021/8/27
 * Description:媒体附件
 */
@Data
public class MediaAndAttachedVo extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    /**
     * 源文件名
     */
    @ApiModelProperty(value = "源文件名")
    private String fileNameOrc;


    /**
     * 文件类型
     */
    @ApiModelProperty(value = "文件类型")
    private String typeValue;
    /**
     * 文件key
     */
    @ApiModelProperty(value = "文件key")
    private String fileKey;

    //==========实体类===============
    private static final long serialVersionUID = 1L;
    /**
     * 文件guid(文档中心)
     */
    @ApiModelProperty(value = "文件guid(文档中心)")
    private String fkFileGuid;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    private Long fkTableId;
    /**
     * 类型关键字，如：institution_mov/institution_pic/alumnus_head_icon
     */
    @ApiModelProperty(value = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    private String typeKey;
    /**
     * 索引值(默认从0开始，同一类型下值唯一)
     */
    @ApiModelProperty(value = "索引值(默认从0开始，同一类型下值唯一)")
    private Integer indexKey;
    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String link;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

}
