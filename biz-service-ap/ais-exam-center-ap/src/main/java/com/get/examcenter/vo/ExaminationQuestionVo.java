package com.get.examcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/8/23 16:12
 */
@Data
public class ExaminationQuestionVo extends BaseEntity implements Serializable {

    /**
     * 操作guid
     */
    @ApiModelProperty(value = "操作guid")
    private String optGuid;

    /**
     * 答案选项
     */
    @ApiModelProperty(value = "答案选项")
    private List<ExaminationAnswerVo> examinationAnswers;

    /**
     * 考卷Id
     */
    @ApiModelProperty(value = "考卷Id")
    private Long fkExaminationPaperId;

    /**
     * 是否复习题名称
     */
    @ApiModelProperty(value = "是否允许重考名称")
    private String isReviewName;

    /**
     * 是否允许重考名称
     */
    @ApiModelProperty(value = "是否允许重考名称")
    private String isRetestName;

    /**
     * 是否激活名称
     */
    @ApiModelProperty(value = "是否激活名称")
    private String isActiveName;

    /**
     * 题目类型名称
     */
    @ApiModelProperty(value = "题目类型名称")
    private String fkQuestionTypeName;

    /**
     * 题型名称(单选题、多选题、判断题)
     */
    @ApiModelProperty(value = "题型名称(单选题、多选题、判断题)")
    private String questionTypeListName;

    /**
     * 当前记录数
     */
    @ApiModelProperty(value = "当前记录数")
    private Integer nowCurrentRecord;

    /**
     * 考题总数
     */
    @ApiModelProperty(value = "考题总数")
    private Integer questionSumCount;

    /**
     * 是否有答题记录
     */
    @ApiModelProperty(value = "是否有答题记录")
    private Boolean isHaveQuestionScore;

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    //========实体类=============
    private static final long serialVersionUID = 1L;
    /**
     * 题目类型Id
     */
    @ApiModelProperty(value = "题目类型Id")
    private Long fkQuestionTypeId;
    /**
     * 考题编号
     */
    @ApiModelProperty(value = "考题编号")
    private String num;
    /**
     * 题型：枚举(单选题0/多选题1/判断题2)
     */
    @ApiModelProperty(value = "题型：枚举(单选题0/多选题1/判断题2)")
    private Integer questionType;
    /**
     * 问题内容
     */
    @ApiModelProperty(value = "问题内容")
    private String question;
    /**
     * 得分
     */
    @ApiModelProperty(value = "得分")
    private Integer score;
    /**
     * 答题时间限制（分钟）
     */
    @ApiModelProperty(value = "答题时间限制（分钟）")
    private Integer timeLimit;
    /**
     * 是否复习题：0否/1是
     */
    @ApiModelProperty(value = "是否复习题：0否/1是")
    private Boolean isReview;
    /**
     * 是否允许重考：0否/1是
     */
    @ApiModelProperty(value = "是否允许重考：0否/1是")
    private Boolean isRetest;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;
    /**
     * 默认为0，为0时随机排序，排序为从大到小顺序排列
     */
    @ApiModelProperty(value = "默认为0，为0时随机排序，排序为从大到小顺序排列")
    private Integer viewOrder;

    /**
     * 是否必填：0否/1是
     */
    @ApiModelProperty(value = "是否必填：0否/1是")
    private Boolean isRequired;

    /**
     * 考卷Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
}
