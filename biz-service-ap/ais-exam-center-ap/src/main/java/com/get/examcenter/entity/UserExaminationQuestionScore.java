package com.get.examcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_user_examination_question_score")
public class UserExaminationQuestionScore extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 操作guid
     */
    @ApiModelProperty(value = "操作guid")
    private String optGuid;
    /**
     * 注册中心的user id
     */
    @ApiModelProperty(value = "注册中心的user id")
    private Long fkUserId;
    /**
     * 考卷Id
     */
    @ApiModelProperty(value = "考卷Id")
    private Long fkExaminationPaperId;
    /**
     * 问题Id
     */
    @ApiModelProperty(value = "问题Id")
    private Long fkExaminationQuestionId;
    /**
     * 答案Ids
     */
    @ApiModelProperty(value = "答案Ids")
    private String fkExaminationAnswerIds;
    /**
     * 获得分数
     */
    @ApiModelProperty(value = "获得分数")
    private Integer score;
    /**
     * 用时（秒）
     */
    @ApiModelProperty(value = "用时（秒）")
    private Integer useTime;
}