package com.get.examcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("u_score_title")
public class ScoreTitle extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 称号名称
     */
    @ApiModelProperty(value = "称号名称")
    private String title;
    /**
     * 分值范围（小）
     */
    @ApiModelProperty(value = "分值范围（小）")
    private Integer scoreMin;
    /**
     * 分值范围（大）
     */
    @ApiModelProperty(value = "分值范围（大）")
    private Integer scoreMax;
    /**
     * 排名范围（小）
     */
    @ApiModelProperty(value = "排名范围（小）")
    private Integer rankingMin;
    /**
     * 排名范围（大）
     */
    @ApiModelProperty(value = "排名范围（大）")
    private Integer rankingMax;
    /**
     * 颜色编码，用于突显，可不填
     */
    @ApiModelProperty(value = "颜色编码，用于突显，可不填")
    private String colorCode;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 考试Id
     */
    @ApiModelProperty(value = "考试Id")
    private Long fkExaminationId;

    /**
     * 所属公司Id
     */
    @ApiModelProperty(value = "所属公司Id")
    private Long fkCompanyId;

    /**
     * 参数配置
     */
    @ApiModelProperty(value = "参数配置")
    private String paramJson;
}