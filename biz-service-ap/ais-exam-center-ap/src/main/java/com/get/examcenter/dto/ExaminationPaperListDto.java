package com.get.examcenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * Created by <PERSON>.
 * Time: 9:43
 * Date: 2021/8/23
 * Description:考场管理列表Vo
 */
@Data
public class ExaminationPaperListDto extends BaseVoEntity implements Serializable {

    /**
     * 考试id
     */
    @ApiModelProperty(value = "考试id")
    private Long fkExaminationId;

    /**
     * 考试名称
     */
    @ApiModelProperty(value = "考试名称")
    private String fkExaminationName;

    /**
     * 考卷编号
     */
    @ApiModelProperty(value = "考卷编号")
    private String num;

    /**
     * 考卷名称
     */
    @ApiModelProperty(value = "考卷名称")
    private String name;

    /**
     * 开放时间
     */
    @ApiModelProperty(value = "开放时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;
}
