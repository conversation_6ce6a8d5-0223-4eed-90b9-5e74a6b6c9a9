package com.get.examcenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/8/23 17:51
 */
@Data
public class ExaminationAnswerDto extends BaseVoEntity implements Serializable {


    /**
     * 问题Id
     *
     *
     */
    @ApiModelProperty(value = "问题Id")
    private Long fkExaminationQuestionId;

    /**
     * 答案内容
     */
    @ApiModelProperty(value = "答案内容")
    @NotBlank(message = "答案内容不能为空", groups = {Add.class, Update.class})
    private String answer;

    /**
     * 是否正确答案：0否/1是
     */
    @ApiModelProperty(value = "是否正确答案：0否/1是")
    @NotNull(message = "是否正确答案不能为空", groups = {Add.class, Update.class})
    private Boolean isRightAnswer;

    /**
     * 答案得分
     */
    @ApiModelProperty(value = "得分")
    private Integer score;

    /**
     * 默认为0，为0时随机排序，排序为从大到小顺序排列
     */
    @ApiModelProperty(value = "默认为0，为0时随机排序，排序为从大到小顺序排列")
    @NotNull(message = "排序不能为空", groups = {Add.class, Update.class})
    private Integer viewOrder;

}
