package com.get.examcenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * Created by <PERSON>.
 * Time: 14:04
 * Date: 2021/8/23
 * Description:排行榜返回类
 */
@Data
public class UserPaperScoreVo extends BaseEntity implements Serializable {


    @ApiModelProperty(value = "名字（中）")
    private String weChatNickName;
    @ApiModelProperty(value = "姓名(英)")
    private String nameEn;
    @ApiModelProperty(value = "手机号")
    private String mobile;
    @ApiModelProperty(value = "邮箱")
    private String email;

    //================实体类=======================
    private static final long serialVersionUID = 1L;
    /**
     * 操作guid
     */
    @ApiModelProperty(value = "操作guid")
    private String optGuid;
    /**
     * 注册中心的user id
     */
    @ApiModelProperty(value = "注册中心的user id")
    private Long fkUserId;
    /**
     * 考试Id
     */
    @ApiModelProperty(value = "考试Id")
    private Long fkExaminationId;
    /**
     * 考卷Id
     */
    @ApiModelProperty(value = "考卷Id")
    private Long fkExaminationPaperId;
    /**
     * 获得分数
     */
    @ApiModelProperty(value = "获得分数")
    private Integer score;
    /**
     * 用时（秒）
     */
    @ApiModelProperty(value = "用时（秒）")
    private Integer useTime;




}
