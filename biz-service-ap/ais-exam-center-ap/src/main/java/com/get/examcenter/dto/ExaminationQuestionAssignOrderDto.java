package com.get.examcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
/** 
* @Description: 考题排序vo
* @Param: 
* @return: 
* @Author: Walker
* @Date: 2022/7/28
*/
@Data
public class ExaminationQuestionAssignOrderDto implements Serializable {
    /**
     * 绑定id
     */
    @ApiModelProperty(value = "绑定id")
    private Long id;

    /**
     * 排序值
     */
    /*@ApiModelProperty(value = "排序值")
    private Integer viewOrder;*/
}
