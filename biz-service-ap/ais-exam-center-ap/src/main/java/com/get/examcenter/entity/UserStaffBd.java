package com.get.examcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("r_user_staff_bd")
public class UserStaffBd extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 注册中心的user id
     */
    @ApiModelProperty(value = "注册中心的user id")
    private Long fkUserId;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
    /**
     * 大区Id
     */
    @ApiModelProperty(value = "大区Id")
    private Long fkAreaRegionId;
}