package com.get.examcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("m_examination_question_assign")
public class ExaminationQuestionAssign extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 考卷Id
     */
    @ApiModelProperty(value = "考卷Id")
    private Long fkExaminationPaperId;
    /**
     * 分配目标类型：0考题类型/1考题
     */
    @ApiModelProperty(value = "分配目标类型：0考题类型/1考题")
    private Integer targetType;
    /**
     * 分配目标id
     */
    @ApiModelProperty(value = "分配目标id")
    private Long targetId;

    /**
     * 排序为从大到小顺序排列
     */
    @ApiModelProperty(value = "排序为从大到小顺序排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是，默认都激活，才能在试题上正常显示")
    private Boolean isActive;
}