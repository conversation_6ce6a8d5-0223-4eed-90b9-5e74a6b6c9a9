//package com.get.registrationcenter.feign;
//
//import com.get.common.entity.fegin.LogOperation;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//
///**
// * @author: Hardy
// * @create: 2021/5/17 16:04
// * @verison: 1.0
// * @description:
// */
//@Component
//@FeignClient(name="log-center")
//public interface FeginLogOperationService {
//    /**
//     *   这里是Feign,不是业务接口
//     *
//     *   这里的接口名称和方法名可随意定义，但方法上的注解和方法参数必须与提供者对应方法相同
//     * */
//    @PostMapping(value = "log/operation/add")
//    Long save(@RequestBody LogOperation logOperation);
//}
