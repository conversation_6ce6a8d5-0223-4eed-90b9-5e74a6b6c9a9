package com.get.registrationcenter.feign;

import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.registrationcenter.entity.User;
import com.get.salecenter.entity.Agent;
import com.get.salecenter.dto.ContactPersonDto;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class IRegistrationCenterFailBack implements IRegistrationCenterClient {

    @Override
    public Result<List<User>> getUsersByIds(List<Long> userIds) {
        return null;
    }

    @Override
    public Result<Boolean> updateIssueContactPerson(ContactPersonDto contactPersonVo) {
        return Result.fail("同步数据失败");
    }

    @Override
    public Result<Long> insertAgentUser(Agent agent) {
        return null;
    }

    @Override
    public Result<User> getUserByAgentId(Long agentId) {
        return Result.fail("获取数据失败");
    }
}
