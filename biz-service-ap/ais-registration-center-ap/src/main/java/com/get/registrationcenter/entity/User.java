package com.get.registrationcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR> Kevin
 * Date: 12/17/2020 2:24 PM
 * Description:
 */
@Data
@TableName("m_user")
public class User extends BaseEntity implements Serializable {
    /**
     * 来自平台类型：get_mso
     */
    @ApiModelProperty(value = "来自平台类型：get_mso")
    private String fkPlatformType;

    /**
     * 登陆用户Id
     */
    @ApiModelProperty(value = "登陆用户Id")
    private String loginId;

    /**
     * 登陆用户密码
     */
    @ApiModelProperty(value = "登陆用户密码")
    private String loginPs;

    /**
     * 会员编号
     */
    @ApiModelProperty(value = "会员编号")
    private String num;

    /**
     * 姓名（中文）
     */
    @ApiModelProperty(value = "姓名（中文）")
    private String name;

    /**
     * 姓名（英文）
     */
    @ApiModelProperty(value = "姓名（英文）")
    private String nameEn;

    /**
     * 姓（拼音）
     */
    @ApiModelProperty(value = "姓（拼音）")
    private String familyNamePy;

    /**
     * 名（拼音）
     */
    @ApiModelProperty(value = "名（拼音）")
    private String firstNamePy;

    /**
     * 昵称
     */
    @ApiModelProperty(value = "昵称")
    private String nickname;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    private Integer gender;

    /**
     * 生日
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "生日,格式：yyyy-MM-dd")
    private Date birthday;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String identityCard;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    private String mobile;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "电话区号")
    private String mobileAreaCode;


    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    private String email;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    private Long fkAreaStateId;

    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    private Long fkAreaCityId;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String zipCode;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;

    /**
     * QQ号
     */
    @ApiModelProperty(value = "QQ号")
    private String qq;

    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    private String wechat;

    /**
     * whatsapp号
     */
    @ApiModelProperty(value = "whatsapp号")
    private String whatsapp;

    /**
     * 微信昵称
     */
    @ApiModelProperty(value = "微信昵称")
    private String wechatNickname;

    /**
     * 微信头像URL
     */
    @ApiModelProperty(value = "微信头像URL")
    private String wechatIconUrl;

    /**
     * 微信openid
     */
    @ApiModelProperty(value = "微信openid")
    private String wechatOpenid;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String company;

    /**
     * 职位
     */
    @ApiModelProperty(value = "职位")
    private String position;

    /**
     * 短信验证码
     */
    @ApiModelProperty(value = "手机验证码")
    @TableField(exist = false)
    private String verification;

    private static final long serialVersionUID = 1L;
}