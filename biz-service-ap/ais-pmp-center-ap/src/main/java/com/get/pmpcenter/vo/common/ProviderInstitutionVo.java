package com.get.pmpcenter.vo.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/2/13  12:05
 * @Version 1.0
 * 学校列表
 */
@Data
public class ProviderInstitutionVo {

    @ApiModelProperty(value = "学校Id")
    private Long institutionId;

    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty(value = "学校提供商Id")
    private Long providerId;

    @ApiModelProperty(value = "方案ID")
    private Long planId;

    @ApiModelProperty(value = "关系所属公司Id")
    private String companyIds;

    @ApiModelProperty(value = "代理方案关联的分公司")
    private Long companyId;
}
