package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.pmpcenter.vo.common.MediaVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/10  11:37
 * @Version 1.0
 */
@Data
@TableName("m_institution_provider_contract")
public class InstitutionProviderContract extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "学校佣金合同方Id")
    private Long fkContractPartyId;

    @ApiModelProperty(value = "合同类型Id")
    private Long fkContractTypeId;

    @ApiModelProperty(value = "合同编号")
    private String contractNum;

    @ApiModelProperty(value = "合同名称")
    private String contractTitle;

    @ApiModelProperty(value = "合同开始时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "合同结束时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制：0否/1是")
    private Integer isTimeless;

    @ApiModelProperty(value = "合同备注")
    private String remark;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "是否锁定：0否/1是")
    private Integer isLocked;

    @ApiModelProperty(value = "审批状态：0未提交/1待审批/2通过/3拒绝")
    private Integer approvalStatus;

    @ApiModelProperty(value = "合同附件文件集合")
    @TableField(exist = false)
    private List<MediaVo> mediaList;

    @ApiModelProperty(value = "合同KPI描述")
    private String contractKpi;
}
