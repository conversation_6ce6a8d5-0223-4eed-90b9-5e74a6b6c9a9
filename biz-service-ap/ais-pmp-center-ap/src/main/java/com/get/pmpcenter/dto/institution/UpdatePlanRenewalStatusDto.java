package com.get.pmpcenter.dto.institution;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author:Oliver
 * @Date: 2025/3/18
 * @Version 1.0
 * @apiNote:更新方案续约中状态参数
 */
@Data
public class UpdatePlanRenewalStatusDto {

    @ApiModelProperty(value = "是否续约中：0否/1是")
    @NotNull(message = "续约中状态不能为空")
    private Integer isRenewal;

    @ApiModelProperty(value = "方案ID")
    @NotNull(message = "方案ID不能为空")
    private Long planId;
}
