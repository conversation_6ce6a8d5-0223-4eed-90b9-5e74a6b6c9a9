package com.get.pmpcenter.vo.institution;

import com.get.pmpcenter.enums.TerritoryRuleEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Objects;

/**
 * @Author:Oliver
 * @Date: 2025/3/13
 * @Version 1.0
 * @apiNote:佣金方案适用地区
 */
@Data
public class PlanTerritoryVo {

    @ApiModelProperty(value = "规则描述-英文")
    private String description;

    @ApiModelProperty(value = "国家名称(逗号隔开)-英文")
    private String territories;

    @ApiModelProperty(value = "规则")
    private Integer isInclude;

    @ApiModelProperty(value = "国家ID集合")
    private List<Long> territoryIds;

    @ApiModelProperty(value = "规则描述-中文")
    private String descriptionChn;

    @ApiModelProperty(value = "国家名称(逗号隔开)-中文")
    private String territoriesChn;

    @ApiModelProperty(value = "大区名称(逗号隔开)-英文")
    private String regions;

    @ApiModelProperty(value = "大区名称(逗号隔开)-中文")
    private String regionsChn;

    @ApiModelProperty(value = "大区ID集合")
    private List<Long> regionIds;

    public static PlanTerritoryVo createGlobalTerritory() {
        PlanTerritoryVo planTerritoryVo = new PlanTerritoryVo();
        planTerritoryVo.setDescription(Objects.nonNull(TerritoryRuleEnum.getEnumByCode(5)) ? TerritoryRuleEnum.getEnumByCode(5).getMsg() : StringUtils.EMPTY);
        planTerritoryVo.setTerritories(StringUtils.EMPTY);
        planTerritoryVo.setDescriptionChn(Objects.nonNull(TerritoryRuleEnum.getEnumByCode(5)) ? TerritoryRuleEnum.getEnumByCode(5).getMsgChn() : StringUtils.EMPTY);
        planTerritoryVo.setTerritoriesChn(StringUtils.EMPTY);
        planTerritoryVo.setRegions(StringUtils.EMPTY);
        planTerritoryVo.setRegionsChn(StringUtils.EMPTY);
        return planTerritoryVo;
    }
}
