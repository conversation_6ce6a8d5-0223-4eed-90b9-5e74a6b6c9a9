package com.get.pmpcenter.vo.common;

import com.get.pmpcenter.vo.institution.InstitutionLabelVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/2/13  12:05
 * @Version 1.0
 * 学校列表
 */
@Data
public class InstitutionVo {

    @ApiModelProperty(value = "学校Id")
    private Long institutionId;

    @ApiModelProperty(value = "学校名称")
    private String name;

    @ApiModelProperty(value = "学校中文名称")
    private String nameChn;

    @ApiModelProperty(value = "显示名称")
    private String nameDisplay;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "国家中文名称")
    private String countryNameChn;

    @ApiModelProperty(value = "学校关联的代理佣金方案列表")
    private List<AgentPlanInstitutionVo> defPlans;

    @ApiModelProperty(value = "国家Id")
    private Long countryId;

    @ApiModelProperty(value = "学校标签")
    private InstitutionLabelVo institutionLabel;

    @ApiModelProperty(value = "学校关联的佣金方案数量")
    private Integer planCount;

    @ApiModelProperty(value = "州省名称")
    private String areaStateName;

    @ApiModelProperty(value = "州省中文名称")
    private String areaStateNameChn;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "城市中文名称")
    private String cityNameChn;

    @ApiModelProperty(value = "是否存在官网：0否/1是")
    private Integer isExistWebsite;

    @ApiModelProperty(value = "学校官网地址")
    private String website;

    @ApiModelProperty(value = "是否激活0否1是")
    private Integer isActive;

}
