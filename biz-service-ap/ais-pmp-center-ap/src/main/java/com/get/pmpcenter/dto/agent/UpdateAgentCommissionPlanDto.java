package com.get.pmpcenter.dto.agent;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/2/25  17:04
 * @Version 1.0
 * @Description: 保存代理佣金计划-非模板
 */
@Data
public class UpdateAgentCommissionPlanDto {

    @ApiModelProperty(value = "代理佣金方案Id-编辑传")
    @NotNull(message = "代理佣金方案Id不能为空")
    private Long id;

    @ApiModelProperty(value = "分公司ID")
    @NotNull(message = "分公司ID不能为空")
    private Long companyId;

    @ApiModelProperty(value = "学校提供商Id")
    @NotNull(message = "学校提供商Id不能为空")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "代理商等级Id")
    private Long fkAgentCommissionTypeId;

    @ApiModelProperty(value = "学校提供商佣金方案Id")
    private Long fkInstitutionProviderCommissionPlanId;

    @ApiModelProperty(value = "方案名称")
    @NotBlank(message = "方案名称不能为空")
    private String name;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "方案有效时间（开始）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "方案开始时间不能为空")
    private Date startTime;

    @ApiModelProperty(value = "方案有效时间（结束）")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "有效时间是否无时间限制：0否/1是")
    @NotNull(message = "有效时间是否无时间限制不能为空")
    private Integer isTimeless;

    @ApiModelProperty(value = "是否全局模板：0否/1是")
    private Integer isGobal;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "是否主方案：0否/1是")
    private Integer isMain;

    @ApiModelProperty(value = "合同KPI描述")
    private String contractKpi;

    @ApiModelProperty(value = "合同备注")
    private String contractRemark;

}
