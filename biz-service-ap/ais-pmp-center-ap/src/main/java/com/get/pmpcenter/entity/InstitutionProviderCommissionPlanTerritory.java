package com.get.pmpcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@TableName("r_institution_provider_commission_plan_territory")
public class InstitutionProviderCommissionPlanTerritory extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "学校提供商佣金方案Id")
    private Long fkInstitutionProviderCommissionPlanId;

    @ApiModelProperty(value = "业务国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "大区Id")
    private Long fkAreaRegionId;

    @ApiModelProperty(value = "是否包括：1包括/2casebycase/3AU onshore/-1除外")
    private Integer isInclude;
}
