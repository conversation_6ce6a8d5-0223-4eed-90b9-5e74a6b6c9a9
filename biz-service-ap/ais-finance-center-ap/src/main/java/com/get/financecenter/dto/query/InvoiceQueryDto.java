package com.get.financecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/16 17:11
 * @verison: 1.0
 * @description:
 */
@Data
public class InvoiceQueryDto {
    /**
     * 公司Id
     */
    @NotNull(message = "公司不能为空")
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 应付类型关键字，枚举，如：m_student_offer_item
     */
    @ApiModelProperty(value = "业务类型关键字，枚举，如：m_student_offer_item留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿")
    private String fkTypeKey;

    /**
     * 对应记录Id
     */
    @ApiModelProperty(value = "对应记录Id")
    private Long fkTypeTargetId;

    @ApiModelProperty(value = "业务国家id")
    private Long businessCountryId;

    @ApiModelProperty("学生名称")
    private String studentName;


    /**
     * 发票编号
     */
    @NotBlank(message = "发票编号不能为空")
    @ApiModelProperty(value = "发票编号")
    private String num;

    /**
     * 开票日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开票日期")
    private Date invoiceDate;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;


    /**
     * 开始日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始日期")
    private Date invoiceDateBeg;

    /**
     * 结束日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束日期")
    private Date invoiceDateEnd;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;


    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;



    @ApiModelProperty(value = "是否到账")
    private Boolean isAccount = null;

    @ApiModelProperty(value = "创建人")
    private String createUser;
}
