package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: Sea
 * @create: 2020/12/18 10:27
 * @verison: 1.0
 * @description:
 */
@Data
public class ProviderTypeDto extends BaseVoEntity{
    /**
     * 供应商类型名称
     */
    @NotBlank(message = "供应商类型名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "供应商类型名称")
    private String typeName;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
