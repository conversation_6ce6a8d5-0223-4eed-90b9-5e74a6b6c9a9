package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 代理佣金结算Dto
 *
 * <AUTHOR>
 * @date 2021/12/21 11:20
 */
@Data
public class AgentSettlementVo extends BaseVoEntity implements Serializable {

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "代理编号")
    private String num;

    @ApiModelProperty(value = "代理名称")
    private String name;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "州省Id")
    private Long fkAreaStateId;

    @ApiModelProperty(value = "城市Id")
    private Long fkAreaCityId;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "州省名称")
    private String stateName;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "应付计划数")
    private Integer planCount;

    @ApiModelProperty(value = "代理银行账户")
    private List<FinAgentContractAccountVo> agentContractAccountList;

}
