package com.get.financecenter.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量下载对账单
 *
 * <AUTHOR>
 * @date 2022/3/16 18:11
 */
@Data
@ApiModel(value = "批量下载对账单vo")
public class BatchDownloadAgentReconciliationDto {


    @NotNull(message = "代理id不能为空")
    @ApiModelProperty(value = "代理id")
    private Long fkAgentId;

    @ApiModelProperty(value = "结算口代理Id")
    private Long settlementPortAgentId;

    @ApiModelProperty(value = "同代理的应收计划信息List")
    @NotEmpty(message = "同代理的应收计划信息List不能为空")
    private List<AgentSettlementBatchExportDto> agentSettlementOfferItemDtoList;

    @NotNull(message = "下载对账单flag不能为空")
    @ApiModelProperty(value = "下载对账单flag true:下载并跳到下一步骤  false:只下载对账单")
    private Boolean flag;

//    @ApiModelProperty(value = "业务类型Key")
//    private String fkTypeKey;
//
//
//    @NotNull(message = "学生代理合同账户Id不能为空")
//    @ApiModelProperty(value = "学生代理合同账户Id")
//    private Long fkAgentContractAccountId;
//
//
//    @NotNull(message = "应付计划Id不能为空")
//    @ApiModelProperty(value = "应付计划Ids")
//    private List<Long> payablePlanIdList;

////    @NotBlank(message = "导出类型不能为空")
////    @ApiModelProperty(value = "导出类型 人民币中介结算汇出表:INTERMEDIARY_SETTLEMENT 人民币易思汇：EASY_TRANSFER 外币中介结算汇出表：FOREIGN_CURRENCY  留学保险：INSURANCE 留学住宿：ACCOMMODATION")
////    private String excelType;
//
//    @ApiModelProperty(value = "结算币种")
//    private String agentAccountCurrencyTypeNum;

}
