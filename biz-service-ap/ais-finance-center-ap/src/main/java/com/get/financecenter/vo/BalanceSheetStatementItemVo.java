package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 资产负债表 子Vo
 */
@Data
public class BalanceSheetStatementItemVo {

    @ApiModelProperty(value = "项目名称")
    private String title;

    @ApiModelProperty(value = "期初数")
    private BigDecimal amountOpeningBalance;

    @ApiModelProperty(value = "借方发生额")
    private BigDecimal amountDr;

    @ApiModelProperty(value = "贷方发生额")
    private BigDecimal amountCr;

    @ApiModelProperty(value = "期末数")
    private BigDecimal amountClosingBalance;

}
