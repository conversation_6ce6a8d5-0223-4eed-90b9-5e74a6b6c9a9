package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 公司科目
 */
@Data
public class CompanyAccountingVo {
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "已设科目数")
    private Long fkAccountingItemCount;

    @ApiModelProperty(value = "编辑人")
    private String gmtModifiedUser;

    @ApiModelProperty(value = "编辑时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date gmtModified;

}

