package com.get.financecenter.vo;

import com.get.financecenter.entity.PaymentForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2020/12/23
 * @TIME: 14:47
 * @Description:
 **/
@Data
public class PaymentFormVo extends PaymentForm {
    private static final long serialVersionUID = 1L;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    /**
     * 目标类型名称，枚举：m_agent代理
     */
    @ApiModelProperty(value = "目标类型名称，枚举：m_agent代理")
    private String fkTypeName;

    /**
     * 对应记录Id
     */
    @ApiModelProperty(value = "对应记录Id")
    private String fkTypeTargetName;

    /**
     * 目标类型名称
     */
    @ApiModelProperty(value = "目标类型名称")
    private String fkTypeKeyName;
    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;
    /**
     * 银行帐号名称（代理/供应商）
     */
    @ApiModelProperty(value = "银行帐号名称（代理/供应商）")
    private String fkBankAccountName;

    /**
     * 银行帐号名称（公司）
     */
    @ApiModelProperty(value = "银行帐号名称（公司）")
    private String fkBankAccountCompanyName;

    /**
     * 目标名称
     */
    @ApiModelProperty(value = "目标名称")
    private String targetName;

    /**
     * 付款费用类型名称
     */
    @ApiModelProperty(value = "付款费用类型名称")
    private String fkPaymentFeeTypeName;

    /**
     * 付款金额（拆分/总）
     */
    @ApiModelProperty(value = "付款金额（拆分/总）")
    private BigDecimal amountPayment;


    /**
     * 付款金额（折合应付币种金额）
     */
    @ApiModelProperty(value = "付款金额（折合应付币种金额）")
    private BigDecimal amountPayable;

    /**
     * 汇率调整（可正可负，为平衡计算应付金额）
     */
    @ApiModelProperty(value = "汇率调整（可正可负，为平衡计算应付金额）")
    private BigDecimal amountExchangeRate;


    @ApiModelProperty(value = "子单汇率调整（可正可负，为平衡计算应付金额）")
    private BigDecimal childAmountExchangeRate;


    @ApiModelProperty(value = "应付id")
    private Long fkPayablePlanId;

    /**
     * 差额
     */
    @ApiModelProperty(value = "差额")
    private BigDecimal diffAmount;

    @ApiModelProperty(value = "付款单手续费")
    private BigDecimal paymentSlipFee;

    @ApiModelProperty(value = "绑定手续费")
    private BigDecimal bindingFee;
}
