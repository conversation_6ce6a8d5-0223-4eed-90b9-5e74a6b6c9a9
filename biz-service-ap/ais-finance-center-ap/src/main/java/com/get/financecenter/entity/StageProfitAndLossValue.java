package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_stage_profit_and_loss_value")
public class StageProfitAndLossValue extends BaseEntity implements Serializable {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private Integer year;

    /**
     * 月
     */
    @ApiModelProperty(value = "月")
    private Integer month;

    /**
     * 损益表项排序，从小到大，顺序排列
     */
    @ApiModelProperty(value = "损益表项排序，从小到大，顺序排列")
    private Integer itemIndex;

    /**
     * 公司损益表项套账Id
     */
    @ApiModelProperty(value = "公司损益表项套账Id")
    private Long fkCompanyProfitAndLossItemId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String title;

    /**
     * 加减方向：1/-1
     */
    @ApiModelProperty(value = "加减方向：1/-1")
    private Integer directionValue;

    /**
     * 期初数
     */
    @ApiModelProperty(value = "期初数")
    private BigDecimal amountOpeningBalance;

    /**
     * 借方发生额
     */
    @ApiModelProperty(value = "借方发生额")
    private BigDecimal amountDr;

    /**
     * 贷方发生额
     */
    @ApiModelProperty(value = "贷方发生额")
    private BigDecimal amountCr;

    /**
     * 期末数
     */
    @ApiModelProperty(value = "期末数")
    private BigDecimal amountClosingBalance;

    /**
     * 发生额
     */
    @ApiModelProperty(value = "发生额")
    private BigDecimal amount;

    /**
     * 是否累加：0否/1是
     */
    @ApiModelProperty(value = "是否累加：0否/1是")
    private Boolean isSum;

}
