package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/1 18:03
 */
@Data
public class PrepayApplicationFormDto  extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 公司Ids
     */
    @ApiModelProperty(value = "公司Ids")
    private List<Long> fkCompanyIds;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;


    /**
     * 申请人Id
     */
    @ApiModelProperty(value = "申请人Id")
    private Long fkStaffId;

    /**
     * 借款申请单编号（系统生成）
     */
    @ApiModelProperty(value = "借款申请单编号（系统生成）")
    private String num;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 借款金额
     */
    @ApiModelProperty(value = "借款金额")
    private BigDecimal amount;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废
     */
    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    private Integer status;
    @ApiModelProperty(value = "附件")
    private MediaAndAttachedDto mediaAndAttachedVo;
    @ApiModelProperty(value = "1查询全部，0查询个人，2我的审批")
    private String selectStatus;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("流程key")
    private String procdkey;
    @ApiModelProperty("借款表单父id")
    private Long fkPrepayApplicationFormIdRevoke;
}
