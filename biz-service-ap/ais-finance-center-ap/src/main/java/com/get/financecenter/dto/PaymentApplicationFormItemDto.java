package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/3/31 11:36
 */
@Data
public class PaymentApplicationFormItemDto   extends BaseVoEntity {

    @ApiModelProperty(value = "支付申请单Id")
    private Long fkPaymentApplicationFormId;

    @ApiModelProperty(value = "付款费用类型Id")
    private Long fkPaymentFeeTypeId;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty(value = "关联类型Id（目标类型表对应记录项Id）")
    private Long relationTargetId;

}
