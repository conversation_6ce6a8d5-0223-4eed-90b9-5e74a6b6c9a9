package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.financecenter.entity.FComment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2021/1/5 11:39
 * @verison: 1.0
 * @description:
 */
@Data
public class FCommentVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    //=============实体类FComment=================
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    /**
     * 评论
     */
    @ApiModelProperty(value = "评论")
    private String comment;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkTableName=").append(fkTableName);
        sb.append(", fkTableId=").append(fkTableId);
        sb.append(", comment=").append(comment);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}
