package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 批量创建应付计划（可选）、付款单参数
 */
@Data
public class ServiceFeePaymentFormDto {

    @ApiModelProperty("远程调用方法所需参数")
    private List<PaymentFormParamDto> paymentFormParamVoList;

    @ApiModelProperty("服务费ids")
    @NotEmpty(message = "请选择要实付的记录")
    private Set<Long> serviceFeeIds;

    @ApiModelProperty("是否已创建应付计划，与服务费id对应")
    @NotEmpty(message = "请选择要实付的记录")
    private List<Boolean> payableFlagList;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司id不能为空")
    private Long fkCompanyId;

    /**
     * 实收类型：1/按服务费金额比率实付 2/按应付计划进行实付 3/自定义金额实付
     */
    @ApiModelProperty(value = "实付类型：1/按服务费金额比率实付 2/按应付计划进行实付 3/自定义金额实付")
    @NotBlank(message = "实付类型不能为空")
    private String payableType;

    @ApiModelProperty("应付币种编号")
    private String fkPayableCurrencyNum;

    @ApiModelProperty("应付金额比率（代理佣金费率）")
    private BigDecimal payableAmountRate;

    @ApiModelProperty("应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty("付款银行帐号Id")
    @NotNull(message = "付款银行帐号Id（公司）不能为空")
    private Long fkBankAccountId;

    @ApiModelProperty("付款日期")
    @NotNull(message = "请填写付款日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date paymentDate;

    @ApiModelProperty("应付计划ids")
    private Set<Long> payablePlanIds;

}
