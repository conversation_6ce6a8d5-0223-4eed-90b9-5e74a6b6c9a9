package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("u_bank")
public class Bank extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    private String bankName;
    /**
     * 银行国际化名称，语言按需要进行翻译
     */
    @ApiModelProperty(value = "银行国际化名称，语言按需要进行翻译")
    private String bankNameInternational;
}