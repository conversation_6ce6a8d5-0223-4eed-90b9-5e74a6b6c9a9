package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 批量创建应收计划（可选）、收款单参数
 */
@Data
public class ServiceFeeReceiptFormDto {

    @ApiModelProperty("远程调用方法所需参数")
    private List<ReceiptFormParamDto> receiptFormParamVoList;

    @ApiModelProperty("服务费ids")
    @NotEmpty(message = "请选择要实收的记录")
    private Set<Long> serviceFeeIds;

    @ApiModelProperty("是否已创建应收计划，与服务费id对应")
    @NotEmpty(message = "请选择要实收的记录")
    private List<Boolean> receivableFlagList;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司id不能为空")
    private Long fkCompanyId;

    /**
     * 实收类型：1/按服务费金额全额实收 2/按应收计划进行实收 3/自定义金额实收
     */
    @ApiModelProperty(value = "实收类型：1/按服务费金额全额实收 2/按应收计划进行实收 3/自定义金额实收")
    @NotBlank(message = "实收类型不能为空")
    private String receivableType;

    @ApiModelProperty("应收币种编号")
    private String fkReceivableCurrencyNum;

    @ApiModelProperty("应收金额")
    private BigDecimal receivableAmount;

    @ApiModelProperty("收款银行帐号Id")
    @NotNull(message = "收款银行帐号Id（公司）不能为空")
    private Long fkBankAccountId;

    @ApiModelProperty("收款日期")
    @NotNull(message = "请填写收款日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date recipientDate;

//    @ApiModelProperty("发票Id")
//    @NotNull(message = "发票Id不能为空")
//    private Long fkInvoiceId;
}
