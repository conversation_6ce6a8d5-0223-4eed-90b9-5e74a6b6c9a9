package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.AssertTrue;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 凭证明细
 */
@Data
public class VouchItemDto extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "凭证Id")
    private Long fkVouchId;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "科目Id")
    @NotNull(message = "科目Id不能为空")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "币种编号")
    @NotBlank(message = "币种编号不能为空")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "借方发生额")
    private BigDecimal amountDr;

    @ApiModelProperty(value = "贷方发生额")
    private BigDecimal amountCr;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty(value = "关联类型Id（目标类型表对应记录项Id）")
    private Long relationTargetId;

    @AssertTrue(message = "借方或贷方发生额必须有一个不为空")
    public boolean isAtLeastOneAmountPresent() {
        return amountDr != null || amountCr != null;
    }

}