package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.financecenter.entity.FDepartment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2020/6/28
 * @TIME: 16:46
 **/
@Data
public class FDepartmentVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    //==============实体类FDepartment==================
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 部门编号
     */
    @ApiModelProperty(value = "部门编号")
    private String num;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;
    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

}
