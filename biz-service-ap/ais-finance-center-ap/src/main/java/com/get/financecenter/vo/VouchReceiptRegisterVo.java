package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 凭证和收款登记记录
 */
@Data
public class VouchReceiptRegisterVo extends BaseEntity {

    @ApiModelProperty(value = "凭证Id")
    private Long fkVouchId;

    @ApiModelProperty(value = "凭证编号")
    private String vouchNumber;

    @ApiModelProperty(value = "收款方式类型Id")
    private Long fkReceiptMethodTypeId;

    @ApiModelProperty(value = "收款方式名称")
    private String receiptMethodTypeName;

    @ApiModelProperty(value = "科目Id（收款方式对应科目）")
    private Long fkAccountingItemIdReceiptMethod;

    @ApiModelProperty(value = "科目名称")
    private String accountingItemName;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）（收款方式对应科目）")
    private String relationTargetKeyReceiptMethod;

    @ApiModelProperty(value = "关联类型Key名称")
    private String relationTargetKeyReceiptMethodName;

    @ApiModelProperty(value = "关联项借方公司Id")
    private Long relationTargetDrCompanyId;

    @ApiModelProperty(value = "关联类型Id（目标类型表对应记录项Id）（收款方式对应科目）")
    private Long relationTargetIdReceiptMethod;

    @ApiModelProperty(value = "关联类型Id名称")
    private String relationTargetIdReceiptMethodName;

    @ApiModelProperty(value = "关联项贷方公司Id")
    private Long relationTargetCrCompanyId;

    @ApiModelProperty(value = "收款费用类型Id")
    private Long fkReceiptFeeTypeId;

    @ApiModelProperty(value = "收款费用类型名称")
    private String receiptFeeTypeName;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "公司Id（归口）")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "部门Id（归口）")
    private Long fkDepartmentId;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）（收款费用类型对应）")
    private String relationTargetKeyReceiptFee;

    @ApiModelProperty(value = "关联类型Key名称")
    private String relationTargetKeyReceiptFeeName;

    @ApiModelProperty(value = "关联类型Id（目标类型表对应记录项Id）（收款费用类型对应）")
    private Long relationTargetIdReceiptFee;
    @ApiModelProperty(value = "关联类型Id名称")
    private String relationTargetIdReceiptFeeName;

    @ApiModelProperty(value = "状态：0取消/1生效")
    private Integer status;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;

    @ApiModelProperty(value = "收款金额")
    private BigDecimal receiptAmount;

    @ApiModelProperty(value = "收款日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date receiptDate;


}