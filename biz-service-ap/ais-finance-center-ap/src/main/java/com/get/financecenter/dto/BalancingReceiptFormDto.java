package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2023/10/10 11:06
 * @verison: 1.0
 * @description:
 */
@Data
public class BalancingReceiptFormDto {

    @NotNull(message = "应收计划id")
    @ApiModelProperty(value = "应收计划id",required = true)
    private Long id;

    @NotBlank(message = "币种")
    @ApiModelProperty(value = "币种",required = true)
    private String fkCurrencyTypeNum;

    @NotNull(message = "应收未收")
    @ApiModelProperty(value = "应收未收",required = true)
    private BigDecimal diffReceivableAmount;

    @NotBlank(message = "应收类型关键字")
    @ApiModelProperty(value = "应收类型关键字，枚举，如：student_offer_item",required = true)
    private String fkTypeKey;

    @NotNull(message = "应收类型Id")
    @ApiModelProperty(value = "应收类型Id",required = true)
    private Long fkTypeTargetId;

}
