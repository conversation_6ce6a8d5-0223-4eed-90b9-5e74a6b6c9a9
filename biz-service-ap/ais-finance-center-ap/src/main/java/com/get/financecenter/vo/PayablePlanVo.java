package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/12/22
 * @TIME: 10:55
 * @Description:
 **/
@Data
public class PayablePlanVo extends BaseVoEntity {

    /**
     * 应付类型关键字，枚举，如：m_student_offer_item
     */
    @ApiModelProperty(value = "应付类型关键字，枚举，如：m_student_offer_item")
    private String fkTypeKey;

    /**
     * 应付类型对应记录Id，如：m_student_offer_item.id
     */
    @ApiModelProperty(value = "应付类型对应记录Id，如：m_student_offer_item.id")
    private Long fkTypeTargetId;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;

    /**
     * 费率%(代理)
     */
    @ApiModelProperty(value = "费率%(代理)")
    private BigDecimal commissionRate;

    /**
     * 代理分成比率%
     */
    @ApiModelProperty(value = "代理分成比率%")
    private BigDecimal splitRate;

    /**
     * 佣金金额(代理)
     */
    @ApiModelProperty(value = "佣金金额(代理)")
    private BigDecimal commissionAmount;

    /**
     * 定额金额
     */
    @ApiModelProperty(value = "定额金额")
    private BigDecimal fixedAmount;

    /**
     * 奖励金额
     */
    @ApiModelProperty(value = "奖励金额")
    private BigDecimal bonusAmount;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    /**
     * 计划付款时间
     */
    @ApiModelProperty(value = "计划付款时间")
    private Date payablePlanDate;

    /**
     * 状态：0关闭/1打开/2完成
     */
    @ApiModelProperty(value = "状态：0关闭/1打开/2完成")
    private Integer status;

    /**
     * 结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总
     */
    @ApiModelProperty(value = "结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
    private Integer statusSettlement;

    /**
     * 财务结算汇总批次号
     */
    @ApiModelProperty(value = "财务结算汇总批次号")
    private String numSettlementBatch;

    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    private String idGeaFinance;


    @ApiModelProperty(value = "应付类型名称")
    private String fkTypeName;

    @ApiModelProperty(value = "应付类型对应记录名称")
    private String fkTypeTargetName;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;


    /**
     * 提供商名称
     */
    @ApiModelProperty(value = "提供商名称")
    private String fkInstitutionProviderName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String fkCourseName;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;

    /**
     * 记录总名称
     */
    @ApiModelProperty(value = "记录总名称")
    private String targetNames;

    @ApiModelProperty(value = "学生信息")
    private String studentInformation;

    @ApiModelProperty(value = "业务信息")
    private String businessInformation;

    @ApiModelProperty(value = "渠道信息")
    private String channelInformation;
}
