package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2021/1/5 11:38
 * @verison: 1.0
 * @description:
 */
@Data
public class CommentDto  extends BaseVoEntity {
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    /**
     * 评论
     */
    @ApiModelProperty(value = "评论")
    private String comment;
}
