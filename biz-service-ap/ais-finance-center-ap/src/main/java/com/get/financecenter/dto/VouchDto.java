package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class VouchDto {

    @ApiModelProperty(value = "凭证号：银付-202504-000279")
    private String vouchNum;

    @ApiModelProperty(value = "凭证类型：转/现收/现付/银收/银付")
    private String vouchType;

    @ApiModelProperty(value = "业务时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessDate;

    @ApiModelProperty(value = "参考信息")
    private String remark;

    @ApiModelProperty(value = "归口公司")
    private Long fkCompanyId;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    List<VouchItemDto> vouchItemList;

}
