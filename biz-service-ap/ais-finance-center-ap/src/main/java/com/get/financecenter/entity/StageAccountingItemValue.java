package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_stage_accounting_item_value")
public class StageAccountingItemValue extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "年")
    private Integer year;

    @ApiModelProperty(value = "月")
    private Integer month;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "加减方向：1/-1")
    private Integer directionValue;

    @ApiModelProperty(value = "借方期初数")
    private BigDecimal amountDrOpeningBalance;

    @ApiModelProperty(value = "贷方期初数")
    private BigDecimal amountCrOpeningBalance;

    @ApiModelProperty(value = "借方本期发生额")
    private BigDecimal amountDr;

    @ApiModelProperty(value = "贷方本期发生额")
    private BigDecimal amountCr;

    @ApiModelProperty(value = "借方期末数")
    private BigDecimal amountDrClosingBalance;

    @ApiModelProperty(value = "贷方期末数")
    private BigDecimal amountCrClosingBalance;

    @ApiModelProperty(value = "借方本年累计发生额")
    private BigDecimal amountDrSum;

    @ApiModelProperty(value = "贷方本年累计发生额")
    private BigDecimal amountCrSum;

}
