package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2021/12/31
 * @TIME: 15:13
 * @Description:付款单导出类
 **/
@Data
public class PaymentFormExportVo implements Serializable {
    /**
     * 所属公司
     */
    @ApiModelProperty(value = "所属公司")
    private String fkCompanyName;
    /**
     * 目标类型名称，枚举：m_agent代理
     */
    @ApiModelProperty(value = "目标类型名称，枚举：m_agent代理")
    private String fkTypeName;
    /**
     * 目标名称
     */
    @ApiModelProperty(value = "目标名称")
    private String fkTypeTargetName;
    /**
     * 系统编号
     */
    @ApiModelProperty(value = "系统编号")
    private String numSystem;
    /**
     * 银行编号
     */
    @ApiModelProperty(value = "银行编号")
    private String numBank;
    /**
     * 币种
     */
    @ApiModelProperty(value = "币种")
    private String fkCurrencyTypeName;
    /**
     * 付款金额
     */
    @ApiModelProperty(value = "付款金额")
    private BigDecimal amount;
    /**
     * 绑定应付金额
     */
    @ApiModelProperty(value = "绑定应付金额")
    private BigDecimal amountPayment;
    /**
     * 差额
     */
    @ApiModelProperty(value = "差额")
    private BigDecimal diffAmount;
    /**
     * 付款日期
     */
    @ApiModelProperty(value = "付款日期")
    private String paymentDate;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private String status;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private String gmtCreateDate;
}
