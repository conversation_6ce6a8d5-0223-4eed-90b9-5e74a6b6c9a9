package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 费用报销单Dto
 *
 * @author: Sea
 * @create: 2021/4/7 10:48
 * @verison: 1.0
 * @description:
 */
@Data
public class ExpenseClaimFormDto extends BaseVoEntity {


    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司不能为空")
    private Long fkCompanyId;

    @ApiModelProperty(value = "票据张数")
    private Integer billCount;

    @ApiModelProperty(value = "部门Id")
    @NotNull(message = "部门Id不能为空")
    private Long fkDepartmentId;

    @NotBlank(message = "币种编号不能为空")
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "摘要")
    @NotBlank(message = "摘要不能为空")
    private String summary;

    /**
     * 状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废
     */
    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    private Integer status;

    /**
     * 关联撤销表单Id
     */
    @ApiModelProperty(value = "关联撤销表单Id")
    private Long fkExpenseClaimFormIdRevoke;

    //自定义内容

    /**
     * 1查询全部，0查询个人,2 查询我的审批
     */
    @ApiModelProperty(value = "1查询全部，0查询个人,2 查询我的审批")
    private String selectStatus;

    /**
     * 开始日期
     */
    @ApiModelProperty(value = "开始日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束日期
     */
    @ApiModelProperty(value = "结束日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 表单明细对象集合
     */
    @ApiModelProperty(value = "表单明细对象集合")
    private List<ExpenseClaimFormItemDto> expenseClaimFormItemVos;



}
