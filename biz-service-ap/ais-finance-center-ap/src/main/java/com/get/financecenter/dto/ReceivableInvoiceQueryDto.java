package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ReceivableInvoiceQueryDto {

    @ApiModelProperty(value = "应收id")
    @NotEmpty(message = "应收id不能为空")
    private List<Long> planIds;

    @NotNull(message = "公司id不能为空")
    @Min(value = 1, message = "缺少公司id参数")
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;
}
