package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 科目余额汇总表Vo
 */
@Data
public class StageAccountingItemVo {

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "科目类型枚举：1=资产/2=负债/3=权益/4=成本/5=损益/6=共同")
    private Integer type;

    @ApiModelProperty(value = "科目类型名")
    private String typeName;

    @ApiModelProperty(value = "科目名")
    private String accountingItemName;

    @ApiModelProperty(value = "科目等级")
    private Integer grade;

    @ApiModelProperty(value = "余额方向：0借/1贷。资产、费用【借增贷减】。负债、权益、收入【贷增借减】。")
    private Integer direction;

    @ApiModelProperty(value = "借方期初数")
    private BigDecimal amountDrOpeningBalance;

    @ApiModelProperty(value = "贷方期初数")
    private BigDecimal amountCrOpeningBalance;

    @ApiModelProperty(value = "借方本期发生额")
    private BigDecimal amountDr;

    @ApiModelProperty(value = "贷方本期发生额")
    private BigDecimal amountCr;

    @ApiModelProperty(value = "借方期末数")
    private BigDecimal amountDrClosingBalance;

    @ApiModelProperty(value = "贷方期末数")
    private BigDecimal amountCrClosingBalance;

    @ApiModelProperty(value = "借方本年累计发生额")
    private BigDecimal amountDrSum;

    @ApiModelProperty(value = "贷方本年累计发生额")
    private BigDecimal amountCrSum;


}
