package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ReceiptMethodTypeDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "科目id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "关键字")
    private String keyWord;

}

