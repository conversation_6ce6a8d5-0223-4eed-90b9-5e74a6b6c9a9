package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @author: Sea
 * @create: 2020/12/18 16:12
 * @verison: 1.0
 * @description:
 */
@Data
public class CurrencyTypeDto  extends BaseVoEntity  {
    /**
     * 货币类型编号
     */
    @NotBlank(message = "货币类型编号不能不为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "货币类型编号")
    private String num;

    /**
     * 货币类型名称
     */
    @NotBlank(message = "货币类型名称不能不为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "货币类型名称")
    private String typeName;

    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    //自定义内容
    /**
     * 编号/币种名称 搜索关键字
     */
    @ApiModelProperty(value = "编号/币种名称 搜索关键字")
    private String keyWord;
}
