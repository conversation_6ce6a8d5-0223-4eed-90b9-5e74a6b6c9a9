package com.get.financecenter.feign;

import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.financecenter.entity.ExpenseClaimForm;
import com.get.financecenter.entity.TravelClaimForm;
import com.get.financecenter.vo.*;
import com.get.financecenter.vo.AlreadyPayVo;
import com.get.financecenter.entity.PrepayApplicationForm;
import com.get.financecenter.entity.Invoice;
import com.get.financecenter.entity.InvoiceReceivablePlan;
import com.get.financecenter.entity.PaymentApplicationForm;
import com.get.financecenter.entity.PaymentForm;
import com.get.financecenter.dto.*;
import com.get.financecenter.dto.InvoiceDto;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class IFinanceCenterClientFallBack implements IFinanceCenterClient {
    @Override
    public Result<String> getCurrencyNameByNum(String fkCurrencyTypeNum) {
        return Result.fail("获取数据失败");
    }

    @Override
    public List<AlreadyPayVo> getAlreadyPayByPlanIds(Set<Long> planIds) {
        return null;
    }

    @Override
    public Result<Map<String, String>> getCurrencyNamesByNums(Set<String> fkCurrencyTypeNums) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<String, String>> getNewCurrencyNamesByNums(Set<String> nums) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<PaymentFormVo>> getPayFormList(Long planId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<ReceiptFormVo>> getReceiptFormList(Long planId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<BigDecimal> getExchangeRate(String fromCurrency, String toCurrency) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> batchAddAuTo(List<ExchangeRateDto> exchangeRateDtos) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<BigDecimal> getLastExchangeRate(Boolean last, String fromCurrency, String toCurrency) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<PaymentApplicationFormVo> getMpayById(Long id) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> updateMpay(PaymentApplicationForm payForm) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> updateBorrowMoneyStatus(PrepayApplicationForm prepayApplicationForm) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<PrepayApplicationFormVo> getBorrowMoneyById(Long id) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> changeStatus(Integer status, String tableName, Long businessKey) {
        return Result.fail("操作失败");
    }

    @Override
    public List<ReceiptFormVo> getReceiptFormListFeignByPlanIds(Set<Long> planIds) {
        return null;
    }

    @Override
    public List<ReceiptFormItemVo> getReceiptFormItemListFeignByPlanIds(Set<Long> planIds) {
        return null;
    }

    @Override
    public List<ReceiptFormItemVo> getReceiptFormItemListFeignByFormItemIds(Set<Long> receiptFormItemIds) {
        return null;
    }

    @Override
    public List<PaymentFormVo> getPayFormListFeignByPlanIds(Set<Long> planIds) {
        return null;
    }

    @Override
    public ReceiptFormVo getReceiptFormByFormId(Long id) {
        return null;
    }

    @Override
    public Map<Long, String> getFkInvoiceNum(Set<Long> planIds) {
        return null;
    }



    @Override
    public Result<List<ReceiptFormVo>> getReceiptFormListFeign(Long planId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public BigDecimal getAmountPaidByPayablePlanId(Long payablePlanId) {
        return null;
    }

    @Override
    public List<ReceiptFormItemVo> getSettlementReceiptFormItemListByPlanId(Long receivablePlanId) {
        return null;
    }

    @Override
    public Result<BigDecimal> importReGea(List<OccVo> receivedOcList, Long receivablePlanId, Long targetId, String currency) {
        return null;
    }

    @Override
    public void importPayGea(List<OccVo> hiPaidInfoList, Long payablePlanId, String currency, Long agentId, Double payableAmount, Double receivableFee) {

    }

    @Override
    public void importPaymentNewRecord(OccVo occVo, Long payablePlanId, String currency, Long agentId, Double payableAmount, Long backId) {

    }

//    @Override
//    public Result isPayInAdvanceInsertSettlementInstallment(Long fkReceivablePlanId) {
//        return null;
//    }

    @Override
    public Result<Long> generateInvoice(InvoiceDto invoiceDto) {
        return Result.fail("操作失败");
    }

    @Override
    public Result generateInvoiceReceivablePlan(InvoiceReceivablePlanDto invoiceReceivablePlanDto) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> isReceivablePlanBound(Long fkReceivablePlanId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result updateInvoice(InvoiceDto invoiceDto) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<Invoice>> getInvoiceByNum(String invoiceNum) {
        return Result.fail("操作失败");
    }

    @Override
    public List<Long> getReceivablePlanIdsFeign(Long invoiceId) {
        return null;
    }

    @Override
    public void createInvoiceAndTargetMapping(String typeKey, Long invoiceId, Long targetId) {

    }

    @Override
    public void updateInvoiceAndTargetMapping(String typeKey, Long invoiceId, Long targetId) {

    }

    @Override
    public Result<List<ReceiptFormItemVo>> getReceiptFormItemsByFormIds(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<List<InvoiceVo>> getInvoiceByNumList(Set<String> numSet) {
        return null;
    }

    @Override
    public Result<Integer> getMaxPoNum(String num) {
        return null;
    }

    @Override
    public Result<Invoice> isExistNum(Integer poNum, String invoiceNum) {
        return Result.fail("操作失败");
    }

    @Override
    public void updateFee(Set<Long> payItemIds) {
        throw new RuntimeException("服务调用回滚");
    }

    @Override
    public Result<Integer> getPayFormItemCount(Set<Long> payPlanIds) {
        return Result.fail("服务调用降级");
    }

    @Override
    public Map<Long, String> getInvoiceNumByReceivableId(Set<Long> ids) {
        return Collections.emptyMap();
    }

    @Override
    public Result<Map<String, BigDecimal>> getLastExchangeRateHkd(Set<String> toCurrency) {
        return null;
    }

    @Override
    public void receivableBindingInvoice(Long receivablePlanId, BigDecimal amount, Long invoiceId) {

    }

    @Override
    public PaymentForm getPayFormById(Long paymentFormId) {
        return null;
    }

    @Override
    public Map<Long, String> getInvoiceCommissionNotice(Long fkInvoiceId) {
        return null;
    }

    @Override
    public List<ReceiptFormItemVo> getReceiptFormByInvoiceId(Long fkInvoiceId) {
        return null;
    }

    @Override
    public Boolean updateReceiptAndPaymentFormStatus(Set<Long> paymentFormIds, Set<Long> receiptFormIds) {
        return null;
    }

    @Override
    public Boolean checkReceiptInvoiceMappingExist(Long invoiceId) {
        return null;
    }

    @Override
    public Boolean unBindInvoiceByReceivablePlanIds(List<Long> receivablePlanIdset) {
        return false;
    }

    @Override
    public Result<List<CurrencyTypeVo>> getCurrencyByPublicLevel(Integer key) {
        return Result.fail("操作失败");
    }

    @Override
    public Result updateInvoiceReceivablePlan(Long invoiceId, Long receivablePlanId, BigDecimal amount) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<Long>> getInvoiceIdsByReceivablePlanId(Long receivablePlanId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result prepaymentButtonHti(List<PrepaymentButtonHtiVo> prepaymentButtonHtiDtoList) {
        return null;
    }

    @Override
    public Result<Map<Long, InvoiceReceivablePlan>> getInvoiceAmountByIds(List<Long> fkInvoiceReceivablePlanIds) {
        return null;
    }

    @Override
    public Result updateInvoiceReceivablePlanAmount(Map<Long, BigDecimal> map) {
        return null;
    }

    @Override
    public Result<Map<Long, Integer>> getPaymentFormItemList(List<String> keys, Set<Long> studentIds) {
        return null;
    }

    @Override
    public Result saveBatchReceiptForms(ServiceFeeReceiptFormDto serviceFeeReceiptFormDto) {
        return null;
    }

    @Override
    public Result saveBatchPaymentForms(ServiceFeePaymentFormDto serviceFeePaymentFormDto) {
        return null;
    }

    @Override
    public Result<Map<String, BigDecimal>> getBatchLastExchangeRate(Set<String> fromCurrencySet, String toCurrency) {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getBankNameByIds(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<Map<Long, List<ReceiptFormVo>>> getReceiptFormsByTargetIds(String fkTypeKey, Set<Long> fkTypeTargetIds) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> activateCommissionSettlementByReceivablePlanIds(Set<Long> receivablePlanIds) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> activateCommissionSettlement(Set<Long> receiptFormItemIds) {
        return null;
    }

    @Override
    public Result<InvoiceReceivablePlan> getInvoiceReceivablePlanById(Long fkInvoiceReceivablePlanId) {
        return null;
    }

    @Override
    public Result<Map<Long, List<ReceiptFormVo>>> getReceiptFormsByFeeIds(List<Long> feeIds) {
        return null;
    }

    @Override
    public Result<Integer> getAccountCommissionSettlementStatus(Long accountId) {
        return null;
    }

    @Override
    public Result updateCommissionSettlementAccountCurrencyTypeNum(Long accountId, String fkCurrencyTypeNum) {
        return null;
    }

    @Override
    public Result<Boolean> getCommissionSettlementAccountInfo(Long accountId) {
        return null;
    }

    @Override
    public Result<Boolean> checkExistAccountCommissionSettlement(Long agentAccountId) {
        return null;
    }

    @Override
    public Result<Boolean> settlementInstallmentUpdate(SettlementInstallmentUpdateDto settlementInstallmentUpdateDto) {
        return null;
    }

    @Override
    public Result<Boolean> checkPayableInfo(Set<Long> payablePlanIds) {
        return null;
    }

    @Override
    public Result insertSettlementInstallmentBatch(List<SettlementInstallmentBatchUpdateDto> settlementInstallmentBatchUpdateDtos) {
        return null;
    }

    @Override
    public Result<Boolean> deleteSettlementCommissionByPayablePlanId(Long payablePlanId) {
        return null;
    }

    @Override
    public Result<Boolean> oneClickSettlement(OneClickSettlementDto oneClickSettlementDto) {
        return null;
    }

    @Override
    public Result<List<Long>> getSettlementFlagAgentIds() {
        return null;
    }

    @Override
    public Result<Boolean> deleteUnsettledCommissionByAdmissionFailure(List<Long> payablePlanIdSet) {
        return null;
    }

    @Override
    public Result<TravelClaimForm> getTravelClaimFormById(Long targetId) {
        return null;
    }

    @Override
    public Result<ExpenseClaimForm> getExpenseClaimFormById(Long targetId) {
        return null;
    }

    @Override
    public Result<PrepayApplicationForm> getPrepayApplicationFormById(Long targetId) {
        return null;
    }

    @Override
    public Result<PaymentApplicationForm> getPaymentApplicationFormById(Long targetId) {
        return null;
    }

    @Override
    public Result<Boolean> updateExpenseClaimFormStatus(ExpenseClaimForm expenseClaimForm) {
        return null;
    }

    @Override
    public Result<Boolean> updateTravelClaimFormStatus(TravelClaimForm travelClaimForm) {
        return null;
    }

    @Override
    public Result<Boolean> updatePaymentApplicationFormStatus(PaymentApplicationForm paymentApplicationForm) {
        return null;
    }

    @Override
    public Result<BigDecimal> getExpenseClaimFormTotalAmount(Long id) {
        return null;
    }

    @Override
    public Result<BigDecimal> getTravelClaimFormTotalAmount(Long id) {
        return null;
    }

    @Override
    public Result<BigDecimal> getPaymentApplicationFormTotalAmount(Long id) {
        return null;
    }

    @Override
    public Result<List<InsurancePaymentFormVo>> createInsurancePaymentForm(InsurancePaymentFormDto insurancePaymentFormDto) {
        return null;
    }

    @Override
    public Result<FormInformationVo> getFormInformationByFormId(String fkTableName, Long fkTableId) {
                return null;
    }
}
