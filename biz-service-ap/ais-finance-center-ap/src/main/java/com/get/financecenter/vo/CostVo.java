package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class CostVo {

    @ApiModelProperty(value = "报销单类型")
    private String claimType;

    @ApiModelProperty(value = "报销单类型名称")
    private String claimTypeName;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "报销单编号")
    private String num;

    @ApiModelProperty(value = "报销业务")
    private String claimBusiness;

    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "报销金额")
    private BigDecimal claimAmount;

    @ApiModelProperty(value = "发票金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty(value = "关联活动类型")
    private String fkEventTableName;

    @ApiModelProperty(value = "关联活动名称")
    private String fkEventTableNameName;

    @ApiModelProperty(value = "关联活动Id")
    private Long fkEventTableId;

    @ApiModelProperty(value = "关联活动标题")
    private String fkEventTableTitle;

    @ApiModelProperty(value = "关联活动公司Id")
    private  Long eventTableCompanyId;

}
