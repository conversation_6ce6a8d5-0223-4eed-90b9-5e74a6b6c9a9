package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2021/1/18
 * @TIME: 14:18
 * @Description:
 **/
@Data
public class AlreadyPayVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 已收金额
     */
    @ApiModelProperty(value = "已付金额")
    private BigDecimal alreadyPayAmount;

    /**
     * 已收币种
     */
    @ApiModelProperty(value = "已付币种")
    private String alreadyPayCurrency;

    /**
     * 已收币种名
     */
    @ApiModelProperty(value = "已收币种名")
    private String alreadyPayCurrencyName;


    /**
     * 汇率调整（可正可负，为平衡计算应付金额）
     */
    @ApiModelProperty(value = "汇率调整（可正可负，为平衡计算应付金额）")
    private BigDecimal alreadyExchangeRate;

    /**
     * 记录总名称
     */
    @ApiModelProperty(value = "记录总名称")
    private String targetNames;

    /**
     * 付款金额（折合应付币种金额）
     */
    @ApiModelProperty(value = "付款金额（折合应付币种金额）")
    private BigDecimal amountPayable;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    private Long fkPayablePlanId;


}
