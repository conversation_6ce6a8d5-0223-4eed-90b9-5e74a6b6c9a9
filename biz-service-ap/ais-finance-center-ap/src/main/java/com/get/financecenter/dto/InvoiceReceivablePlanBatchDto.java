package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class InvoiceReceivablePlanBatchDto  extends BaseEntity {

    /**
     * 发票Id
     */
    @NotNull(message = "发票Id不能为空")
    @ApiModelProperty(value = "发票Id")
    private Long fkInvoiceId;


    /**
     * 应收计划Ids
     */
    @NotEmpty(message = "请选择要绑定的应收计划")
    @ApiModelProperty(value = "应收计划Ids")
    private List<Long> fkReceivablePlanIds;

}
