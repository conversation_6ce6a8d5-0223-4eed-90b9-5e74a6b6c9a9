package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/21 9:58
 * @verison: 1.0
 * @description:
 */
@Data
public class BankAccountDto  extends BaseVoEntity{

    /**
     * 公司Id
     */
    @NotNull(message = "公司Id不能为空", groups = {Add.class, Update.class})
    @Min(value = 1, message = "缺少公司id参数", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private List<Long> fkCompanyIds;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 银行账户名称
     */
    @NotBlank(message = "银行账户名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "银行账户名称")
    private String bankAccount;

    /**
     * 银行账号
     */
    @NotBlank(message = "银行账号不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "银行账号")
    private String bankAccountNum;

    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /**
     * 银行支行名称
     */
    @ApiModelProperty(value = "银行支行名称")
    private String bankBranchName;

    /**
     * 银行地址国家Id
     */
    @ApiModelProperty(value = "银行地址国家Id")
    private Long fkAreaCountryId;

    /**
     * 银行地址州省Id
     */
    @ApiModelProperty(value = "银行地址州省Id")
    private Long fkAreaStateId;

    /**
     * 银行地址城市Id
     */
    @ApiModelProperty(value = "银行地址城市Id")
    private Long fkAreaCityId;

    /**
     * 银行地址城市区域Id
     */
    @ApiModelProperty(value = "银行地址城市区域Id")
    private Long fkAreaCityDivisionId;

    /**
     * 银行地址
     */
    @ApiModelProperty(value = "银行地址")
    private String bankAddress;

    /**
     * Swift Code
     */
    @ApiModelProperty(value = "Swift Code")
    private String swiftCode;

    /**
     * 其他转账编码
     */
    @ApiModelProperty(value = "其他转账编码")
    private String otherCode;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    //自定义内容
    /**
     * 银行名称/账号名称/银行账号 搜索关键字
     */
    @ApiModelProperty(value = "银行名称/账号名称/银行账号 搜索关键字")
    private String keyWord;
}
