package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Hardy
 * @create: 2021/6/29 16:44
 * @verison: 1.0
 * @description:
 */
@Data
public class GetExchangeRateVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "status")
    private String status;

    @ApiModelProperty(value = "msg")
    private String msg;

    @ApiModelProperty(value = "result")
    private GetExchangeRateResultVo result;


}
