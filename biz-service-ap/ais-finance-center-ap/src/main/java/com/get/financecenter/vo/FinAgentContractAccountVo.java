package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 18:45
 * @Description:
 **/
@Data
public class FinAgentContractAccountVo {

    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    private Long fkAgentId;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 银行账户名称
     */
    @ApiModelProperty(value = "银行账户名称")
    private String bankAccount;

    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    private String bankAccountNum;

    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    private String bankName;

    /**
     * 银行支行名称
     */
    @ApiModelProperty(value = "银行支行名称")
    private String bankBranchName;

    /**
     * 银行地址国家Id
     */
    @ApiModelProperty(value = "银行地址国家Id")
    private Long fkAreaCountryId;

    /**
     * 银行地址州省Id
     */
    @ApiModelProperty(value = "银行地址州省Id")
    private Long fkAreaStateId;

    /**
     * 银行地址城市Id
     */
    @ApiModelProperty(value = "银行地址城市Id")
    private Long fkAreaCityId;

    /**
     * 银行地址城市区域Id
     */
    @ApiModelProperty(value = "银行地址城市区域Id")
    private Long fkAreaCityDivisionId;

    /**
     * 银行地址
     */
    @ApiModelProperty(value = "银行地址")
    private String bankAddress;

    /**
     * Swift Code
     */
    @ApiModelProperty(value = "Swift Code")
    private String swiftCode;

    /**
     * 其他转账编码
     */
    @ApiModelProperty(value = "其他转账编码")
    private String otherCode;

    /**
     * 是否默认首选：0否/1是
     */
    @ApiModelProperty(value = "是否默认首选：0否/1是")
    private Boolean isDefault;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;
}
