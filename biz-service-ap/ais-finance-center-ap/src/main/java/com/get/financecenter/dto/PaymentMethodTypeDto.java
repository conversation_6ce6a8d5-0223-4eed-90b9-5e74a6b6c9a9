package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 付款方式类型
 */
@Data
public class PaymentMethodTypeDto {
    @ApiModelProperty(value = "付款方式类型id")
    private Long id;

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "对应凭证类型：转/现收/现付/银收/银付")
    private String vouchType;

    @ApiModelProperty(value = "关键字")
    private String keyWord;


}