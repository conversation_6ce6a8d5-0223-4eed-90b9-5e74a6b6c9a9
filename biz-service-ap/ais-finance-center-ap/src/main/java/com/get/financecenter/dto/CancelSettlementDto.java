package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/4/28 16:31
 */
@Data
public class CancelSettlementDto {

//    @NotEmpty(message = "代理id不能为空")
//    @ApiModelProperty(value = "代理id")
//    private Long agentId;

    @NotNull(message = "学生代理合同账户Id不能为空")
    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;

    @NotNull(message = "应付计划Id不能为空")
    @ApiModelProperty(value = "应付计划Id")
    private Long payablePlanId;
}
