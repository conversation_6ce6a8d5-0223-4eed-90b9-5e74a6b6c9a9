package com.get.financecenter.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class InvoiceBindDto {

    @NotEmpty(message = "fkTypeTargetIds不能为空")
    private List<Long> fkTypeTargetIds;

    @NotBlank(message = "fkTypeKey不能为空")
    private String fkTypeKey;

    @NotNull(message = "公司id")
    private Long fkCompanyId;

    private Long id;
}
