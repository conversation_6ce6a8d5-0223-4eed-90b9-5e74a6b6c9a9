package com.get.financecenter.dto;

import com.get.financecenter.entity.VouchApplicationFormPayment;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 新增付款记录Dto
 */
@Data
public class  VouchApplicationFormPaymentAddDto extends VouchApplicationFormPayment {

    @ApiModelProperty(value = "财务单据表名")
    @NotBlank(message = "财务单据表名不能为空")
    private String fkTableName;

    @ApiModelProperty(value = "财务单据表记录Id")
    @NotNull(message = "财务单据表记录Id不能为空")
    private Long fkTableId;


    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "摘要")
    private String remark;


}
