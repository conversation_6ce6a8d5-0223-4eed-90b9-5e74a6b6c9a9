package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 澳小保创建付款单
 */
@Data
public class InsurancePaymentFormDto {

    @ApiModelProperty("代理id")
    private Long agentId;

    @ApiModelProperty(value = "银行帐号Id（代理/供应商）")
    private Long fkBankAccountId;

    @ApiModelProperty(value = "银行帐号Id（公司）")
    private Long fkBankAccountIdCompany;

    @ApiModelProperty(value = "付款日期")
    private Date paymentDate;

    @ApiModelProperty(value = "付款单编号（凭证号）")
    private String numBank;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "应付计划Ids")
    private Set<Long> fkPayablePlanIds;

    @ApiModelProperty(value = "附件")
    private List<MediaAndAttachedDto> mediaAndAttachedDtoList;

}
