package com.get.financecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class AgentSettlementQueryDto {

    @ApiModelProperty(value = "业务类型")
    private String businessType;

    @ApiModelProperty(value = "代理名称或编号")
    private String agentNameOrNum;

    @ApiModelProperty(value = "代理州省Id")
    private Long agentAreaStateId;

    @ApiModelProperty(value = "学生名字")
    private String studentName;

    @ApiModelProperty(value = "申请开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applyStartTime;

    @ApiModelProperty(value = "申请结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date applyEndTime;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingStartTime;

    @ApiModelProperty(value = "结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingEndTime;

    @ApiModelProperty(value = "结算状态：0未结算/1结算中/2代理确认/3财务确认")
    @NotNull(message = "结算状态不能为空")
    private Integer statusSettlement;

    @ApiModelProperty(value = "大区Id")
    private Long fkAreaRegionId;

    @ApiModelProperty(value = "代理绑定BD名称或编号")
    private String bdNameOrCode;

    @ApiModelProperty(value = "到账时间（开始时间）")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiptStartTime;

    @ApiModelProperty(value = "到账时间（结束时间）")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiptEndTime;

    @ApiModelProperty(value = "步骤提交时间（开始时间）")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepSubmissionStartTime;

    @ApiModelProperty(value = "步骤提交时间（开始时间）")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepSubmissionEndTime;

    @ApiModelProperty(value = "预付标记 ture:查询预付 false:查询非预付")
    private Boolean prepaidMark;

    @ApiModelProperty(value = "学生佣金结算标记关键字")
    private String commissionMark;


    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    @ApiModelProperty(value = "公司ids")
    private Long fkCompanyId;

    @ApiModelProperty(value = "负数应付金额搜索  true:搜索负数应付金额 false:不搜索")
    private Boolean negativeFlag;

    @ApiModelProperty(value = "待结算标记  true：有标记  false:，没标记")
    private Boolean settlementFlag;


    @ApiModelProperty(value = "代理id (查询子项列表必传)")
    private Long agentId;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;



}
