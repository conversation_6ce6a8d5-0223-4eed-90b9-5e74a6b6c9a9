package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.financecenter.entity.PaymentFormItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/21
 * @TIME: 16:55
 * @Description:
 **/
@Data
public class PaymentFormItemVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "应付类型名称")
    private String fkTypeName;

    private String fkAreaCountryName;
    /**
     * 代理名称
     */
    @ApiModelProperty(value = "应付计划列表代理名称")
    private String fkAgentName;

    /**
     * 记录总名称
     */
    @ApiModelProperty(value = "记录总名称")
    private String targetNames;


    /**
     * 延迟入学标记
     */
    @ApiModelProperty(value = "延迟入学标记")
    private Boolean isDeferEntrance;

    /**
     * 延迟入学时间
     */
    @ApiModelProperty(value = "延迟入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date maxDeferEntranceTimes;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private BigDecimal payablePlanAmount;

    /**
     * 应付币种
     */
    @ApiModelProperty(value = "应付币种")
    private String payablePlanCurrency;

    /**
     * 应付币种名
     */
    @ApiModelProperty(value = "应付币种名")
    private String payablePlanCurrencyName;

    /**
     * 实付币种
     */
    @ApiModelProperty(value = "实付币种")
    private String payFormCurrency;


    /**
     * 已付
     */
    @ApiModelProperty(value = "已付")
    private List<AlreadyPayVo> alreadyPayDtos;


    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "学生信息")
    private String studentInformation;

    @ApiModelProperty(value = "业务信息")
    private String businessInformation;

    @ApiModelProperty(value = "渠道信息")
    private String channelInformation;

    //=============实体类PaymentFormItem=======================
    /**
     * 付款单Id
     */
    @ApiModelProperty(value = "付款单Id")
    private Long fkPaymentFormId;

    /**
     * 应付计划Id
     */
    @ApiModelProperty(value = "应付计划Id")
    private Long fkPayablePlanId;

    /**
     * 付款金额（拆分/总）
     */
    @ApiModelProperty(value = "付款金额（拆分/总）")
    private BigDecimal amountPayment;

    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;

    /**
     * 汇率（折合应付币种汇率）
     */
    @ApiModelProperty(value = "汇率（折合应付币种汇率）")
    private BigDecimal exchangeRatePayable;

    /**
     * 付款金额（折合应付币种金额，含手续费）
     */
    @ApiModelProperty(value = "付款金额（折合应付币种金额，含手续费）")
    private BigDecimal amountPayable;

    /**
     * 汇率调整金额（可正可负，为平衡计算应付金额）
     */
    @ApiModelProperty(value = "汇率调整金额（可正可负，为平衡计算应付金额）")
    private BigDecimal amountExchangeRate;

    /**
     * 汇率（折合港币）
     */
    @ApiModelProperty(value = "汇率（折合港币）")
    private BigDecimal exchangeRateHkd;

    /**
     * 付款金额（折合港币）
     */
    @ApiModelProperty(value = "付款金额（折合港币）")
    private BigDecimal amountHkd;

    /**
     * 汇率（折合人民币）
     */
    @ApiModelProperty(value = "汇率（折合人民币）")
    private BigDecimal exchangeRateRmb;

    /**
     * 付款金额（折合人民币）
     */
    @ApiModelProperty(value = "付款金额（折合人民币）")
    private BigDecimal amountRmb;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;


}
