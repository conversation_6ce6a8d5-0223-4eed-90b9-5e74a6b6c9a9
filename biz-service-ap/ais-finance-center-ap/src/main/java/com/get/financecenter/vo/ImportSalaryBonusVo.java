package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ImportSalaryBonusVo extends BaseEntity {

    @ApiModelProperty(value = "总数")
    private Integer totalNum;

    @ApiModelProperty(value = "成功数")
    private Integer successNum;

    @ApiModelProperty(value = "失败数")
    private Integer failNum;

    @ApiModelProperty(value = "工号无效")
    private List<String> invalidNum;

    @ApiModelProperty(value = "姓名无效")
    private List<String> invalidName;

    @ApiModelProperty(value = "其他原因")
    private List<String> invalidOther;


}
