package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TravelClaimFormAndItemVo extends TravelClaimFormItemVo {
    @ApiModelProperty(value = "差旅报销Id")
    private Long id;

    @ApiModelProperty(value = "费用报销申请单Id")
    private Long fktravelClaimFormId;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;

    @ApiModelProperty(value = "部门名称")
    private String departmentName;

    @ApiModelProperty(value = "费用报销申请单编号（系统生成）")
    private String num;

    @ApiModelProperty(value = "状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private Integer status;

    /**
     * 报销人名称
     */
    @ApiModelProperty(value = "报销人名称")
    private String staffName;

    @ApiModelProperty(value = "申请人Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "关联撤销表单Id")
    private Long fktravelClaimFormIdRevoke;

    @ApiModelProperty(value = "通知员工Ids，支持多选（英文逗号分隔：1,2,3）")
    private String fkStaffIdsNotice;
    /**
     * 报销单状态 0待签1代办2无
     */
    @ApiModelProperty(value = "报销单状态 0待签1代办2无")
    private Integer travelClaimFormStatus;


}
