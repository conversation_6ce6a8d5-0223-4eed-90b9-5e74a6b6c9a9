package com.get.financecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 公司科目
 */
@Data
@TableName("m_company_accounting_item")
public class CompanyAccountingItem extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "公司科目套账Id")
    private Long id;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "资产负债表类型Id")
    private Long fkBalanceSheetTypeId;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "借方期初数")
    private BigDecimal amountDrOpeningBalance;

    @ApiModelProperty(value = "贷方期初数")
    private BigDecimal amountCrOpeningBalance;

}

