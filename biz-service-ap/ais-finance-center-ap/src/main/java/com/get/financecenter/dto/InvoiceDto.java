package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/12/16 17:11
 * @verison: 1.0
 * @description:
 */
@Data
public class InvoiceDto   extends BaseVoEntity {
    /**
     * 公司Id
     */
    @NotNull(message = "公司不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 应付类型关键字，枚举，如：m_student_offer_item
     */
    @ApiModelProperty(value = "业务类型关键字，枚举，如：m_student_offer_item留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿")
    private String fkTypeKey;
    /**
     * 付款方实付单号
     */
    @ApiModelProperty(value = "付款方实付单号")
    private String typeTargetPaymentNum;
    /**
     * 对应记录Id
     */
    @ApiModelProperty(value = "对应记录Id")
    private Long fkTypeTargetId;

    @ApiModelProperty(value = "业务国家id")
    private Long businessCountryId;

    @ApiModelProperty("学生名称")
    private String studentName;

    /**
     * 学校提供商Id（开票对象）
     */
    @ApiModelProperty(value = "学校提供商Id（开票对象）")
    private Long fkInstitutionProviderId;

    /**
     * 发票编号
     */
    @NotBlank(message = "发票编号不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "发票编号")
    private String num;

    /**
     * 开票日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开票日期")
    private Date invoiceDate;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;

    /**
     * 实收日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "实收日期")
    private Date receiptDate;

    /**
     * 实收币种
     */
    @ApiModelProperty(value = "实收币种")
    private String fkCurrencyTypeNumReceipt;

    /**
     * 实收金额
     */
    @ApiModelProperty("实收金额")
    private BigDecimal receiptAmount;

    /**
     * 国内发票编号
     */
    @ApiModelProperty(value = "国内发票编号")
    private String localInvoiceNum;

    /**
     * po号码
     */
    @ApiModelProperty(value = "po号码")
    private String poNum;

    /**
     * 原币种
     */
    @ApiModelProperty("原币种")
    private String fkCurrencyTypeNumOrc;
    /**
     * 付款方实付单号
     */
    @ApiModelProperty("付款方实付单号")
    private String institutionProviderPaymentNum;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;

    //自定义内容
    /**
     * 学校提供商名称关键字
     */
    @ApiModelProperty(value = "学校提供商名称关键字")
    private String institutionProviderNameKey;

    /**
     * 开始日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开始日期")
    private Date invoiceDateBeg;

    /**
     * 结束日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "结束日期")
    private Date invoiceDateEnd;

    /**
     * 发票id数组
     */
    @ApiModelProperty(value = "发票id数组")
    private List<Long> invoiceIds;

    /**
     * 应收计划Ids
     */
    @ApiModelProperty(value = "应收计划Ids")
    private List<Long> fkReceivablePlanIds;

    /**
     * 目标名称
     */
    @ApiModelProperty(value = "目标名称")
    private String targetName;

    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    @NotEmpty(message = "目标对象不能为空")
    @ApiModelProperty(value = "目标Id集合")
    private Set<Long> fkTypeTargetIds;

    @ApiModelProperty(value = "是否到账")
    private Boolean isAccount = null;

    @ApiModelProperty(value = "创建人")
    private String createUser;
}
