package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 重新统计损益DTO
 */
@Data
public class RecomputeProfitLossStatementDto {

    @ApiModelProperty(value = "重新统计开始时间")
    @NotNull(message = "重新统计开始时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "公司Ids")
    @NotEmpty(message = "公司Ids不能为空")
    private List<Long> companyIds;

}
