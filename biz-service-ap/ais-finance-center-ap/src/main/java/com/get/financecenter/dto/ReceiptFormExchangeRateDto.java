package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ReceiptFormExchangeRateDto {

    /**
     * 发票ID列表
     */
    @ApiModelProperty(value = "发票ID列表")
    @NotEmpty(message = "请选择发票")
    private List<Long> fkInvoiceIdList;

    /**
     * 收款总金额（到账金额）
     */
    @ApiModelProperty(value = "收款总金额（到账金额）")
    @NotNull(message = "收款总金额（到账金额）不能为空")
    private BigDecimal amount;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @NotBlank(message = "币种编号不能为空")
    private String fkCurrencyTypeNum;
}
