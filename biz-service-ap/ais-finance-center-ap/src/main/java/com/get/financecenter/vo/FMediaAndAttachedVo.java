package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.financecenter.entity.FMediaAndAttached;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/8/14 10:10
 * @verison: 1.0
 * @description:
 */
@Data
public class FMediaAndAttachedVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "文件路径")
    private String filePath;

    /**
     * 源文件名
     */
    @ApiModelProperty(value = "源文件名")
    private String fileNameOrc;

    private String typeValue;

    /**
     * 文件key
     */
    @ApiModelProperty(value = "文件key")
    private String fileKey;

    //===============实体类FMediaAndAttached=================
    /**
     * 文件guid(文档中心)
     */
    @ApiModelProperty(value = "文件guid(文档中心)")
    private String fkFileGuid;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    private Long fkTableId;
    /**
     * 类型关键字，如：institution_mov/institution_pic/alumnus_head_icon
     */
    @ApiModelProperty(value = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    private String typeKey;
    /**
     * 索引值(默认从0开始，同一类型下值唯一)
     */
    @ApiModelProperty(value = "索引值(默认从0开始，同一类型下值唯一)")
    private Integer indexKey;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    private String link;

}
