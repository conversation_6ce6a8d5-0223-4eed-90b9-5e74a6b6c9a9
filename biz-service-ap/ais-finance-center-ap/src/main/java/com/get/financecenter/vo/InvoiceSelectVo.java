package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2022/4/18 12:17
 * @verison: 1.0
 * @description:
 */
@Data
public class InvoiceSelectVo {

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;
    /**
     * 发票编号
     */
    @ApiModelProperty("发票编号")
    private String num;
    /**
     * 发票编号
     */
    @ApiModelProperty("币种")
    private String fkCurrencyTypeNum;
    /**
     * 发票编号
     */
    @ApiModelProperty("金额")
    private BigDecimal invoiceAmount;

    @ApiModelProperty("权重")
    private BigDecimal weight;

    private String fkTypeKey;

    private Integer isActivityType;


    private Date gmtCreate;
}
