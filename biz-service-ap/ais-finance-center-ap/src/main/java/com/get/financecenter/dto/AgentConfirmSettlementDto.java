package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 提交代理确认结算 (第二步提交按钮 hti专用)Vo
 *
 * <AUTHOR>
 * @date 2024/2/21 10:27
 */
@Data
public class AgentConfirmSettlementDto {

    @NotNull(message = "代理id不能为空")
    @ApiModelProperty(value = "代理id")
    private Long agentId;

    @NotNull(message = "应付计划id不能为空")
    @ApiModelProperty(value = "应付计划id")
    private Long payablePlanId;

    @NotNull(message = "学生代理合同账户Id不能为空")
    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;


}
