package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Map;

@Data
public class ProfitAndLossStatementItemVo {

    @ApiModelProperty(value = "项目名称")
    private String title;

    @ApiModelProperty("颜色代码RGB")
    private String colorCode;

    @ApiModelProperty(value = "月份金额Map")
    private Map<Integer, ProfitAndLossStatementValueVo> monthsAmountMap;

    @ApiModelProperty(value = "合计金额")
    private BigDecimal totalBalance;

}
