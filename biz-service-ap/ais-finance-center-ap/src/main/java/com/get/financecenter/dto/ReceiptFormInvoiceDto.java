package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2021/1/6
 * @TIME: 17:55
 * @Description:
 **/
@Data
public class ReceiptFormInvoiceDto extends BaseVoEntity{
    /**
     * 收款单Id
     */
    @ApiModelProperty(value = "收款单Id")
    @NotNull(message = "收款单Id不能为空", groups = {Add.class, Update.class})
    private Long fkReceiptFormId;

    /**
     * 发票Id
     */
    @ApiModelProperty(value = "发票Id")
    @NotNull(message = "发票Id不能为空", groups = {Add.class, Update.class})
    private Long fkInvoiceId;
}
