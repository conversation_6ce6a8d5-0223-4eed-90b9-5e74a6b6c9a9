package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 第三步佣金结算总额表导出实体类
 */
@Data
public class AgentSettlementGrossAmountExport {

    /**
     * 枚举：TableEnum.SALE_COMMISSION_PAYABLE_TARGET_TYPE
     */
    @ApiModelProperty(value = "应付计划类型")
    private String fkTypeName;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    @ApiModelProperty(value = "结算货币标记")
    private String currencyTypeName;

    @ApiModelProperty(value = "结算银行名称")
    private String bankName;

    @ApiModelProperty(value = "应付币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "汇率")
    private String exchangeRate;

    @ApiModelProperty(value = "港币")
    private String payableAmountHkd;

    @ApiModelProperty(value = "BD")
    private String bdName;

    /**
     * 区域：countryName + cityName + stateName
     */
    @ApiModelProperty(value = "区域")
    private String region;

    @ApiModelProperty(value = "账户导出时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date accountExportTime;

    @ApiModelProperty(value = "是否预付")
    private String prepaidMark;
}
