package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/18 10:13
 * @verison: 1.0
 * @description:
 */
@Data
public class ProviderDto   extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 公司Ids
     */
    @ApiModelProperty(value = "公司Ids")
    private List<Long> fkCompanyIds;

    /**
     * 供应商类型Id
     */
    @ApiModelProperty(value = "供应商类型Id")
    private Long fkProviderTypeId;

    /**
     * 供应商编号
     */
    @ApiModelProperty(value = "供应商编号")
    private String num;

    /**
     * 供应商名称
     */
    @ApiModelProperty(value = "供应商名称")
    private String name;

    /**
     * 区域所在国家Id
     */
    @ApiModelProperty(value = "区域所在国家Id")
    private Long fkAreaCountryId;

    /**
     * 区域所在州省Id
     */
    @ApiModelProperty(value = "区域所在州省Id")
    private Long fkAreaStateId;

    /**
     * 区域所在城市Id
     */
    @ApiModelProperty(value = "区域所在城市Id")
    private Long fkAreaCityId;

    /**
     * 性质：0个人/1公司
     */
    @ApiModelProperty(value = "性质：0个人/1公司")
    private Integer natureType;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    private String legalPerson;

    /**
     * 税号/统一社会信用代码（公司）
     */
    @ApiModelProperty(value = "税号/统一社会信用代码（公司）")
    private String taxCode;

    /**
     * 身份证号（个人）
     */
    @ApiModelProperty(value = "身份证号（个人）")
    private String idCardNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    //自定义内容
    /**
     * 供应商名称/编号搜索关键字
     */
    @ApiModelProperty(value = "供应商名称/编号搜索关键字")
    private String keyWord;

    /**
     * 创建时间(开始)
     */
    @ApiModelProperty("创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    /**
     * 创建时间(结束)
     */
    @ApiModelProperty("创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
}
