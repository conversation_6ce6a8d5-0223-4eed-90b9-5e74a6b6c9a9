package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class AgencyCommissionSettlementServiceExport {

    @ApiModelProperty("代理所属公司")
    private String companyName;

    @ApiModelProperty("学生编号")
    private String studentNum;

    @ApiModelProperty("到账月")
    private String receiptMonth;

    @ApiModelProperty("到账日期")
    private String receiptTime;

    @ApiModelProperty("是否预付")
    private String paymentInAdvance;

    @ApiModelProperty("机构编号")
    private String agentNum;

    @ApiModelProperty("机构名称")
    private String agentName;

    @ApiModelProperty("学校提供商")
    private String institutionProviderName;

    @ApiModelProperty("学校提供商中文")
    private String institutionProviderNameChn;


    @ApiModelProperty("国家")
    private String countryName;

    @ApiModelProperty("国家中文")
    private String countryNameChn;

    @ApiModelProperty("学校")
    private String institutionName;

    @ApiModelProperty("学校中文")
    private String institutionNameChn;

    @ApiModelProperty("学生姓名")
    private String studentName;

    @ApiModelProperty(value = "佣金结算标记")
    private String commissionMark;

    @ApiModelProperty("课程")
    private String courseName;

    @ApiModelProperty("开课时间")
    private String openTime;

    @ApiModelProperty("应收币种")
    private String fkReceivableCurrencyNum;

    @ApiModelProperty("应收币种中文")
    private String fkReceivableCurrencyNumChn;

    @ApiModelProperty("应收学费")
    private BigDecimal tuitionAmount;

    @ApiModelProperty("佣金费率")
    private String receivableCommissionRate;

    @ApiModelProperty("应收金额")
    private BigDecimal receivableAmount;

    @ApiModelProperty("实际到账")
    private BigDecimal receiptAmount;

    @ApiModelProperty("实收人民币汇率")
    private String rmbExchangeRate;

    @ApiModelProperty("实到折合人民币")
    private BigDecimal receivableConvertedAmount;

    @ApiModelProperty("应付币种")
    private String payableCurrencyNum;

    @ApiModelProperty("应付币种中文")
    private String payableCurrencyNumChn;

    @ApiModelProperty("合作方佣金费率")
    private String payableCommissionRate;

    @ApiModelProperty("合作方占佣比")
    private String pods;

    @ApiModelProperty("合作方应付佣金")
    private BigDecimal payableAmount;

    @ApiModelProperty("合作方实际支付佣金")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "应付未付")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal differenceAmount;

    @ApiModelProperty("实付人民币汇率")
    private String payableRmbExchangeRate;

    @ApiModelProperty("实付折合人民币")
    private String payableConvertAmount;



//    @ApiModelProperty("渠道费率")
//    private String receivableNatRate;
//
//    @ApiModelProperty("学校佣金")
//    private BigDecimal commissionAmount;





    @ApiModelProperty("申请创建时间")
    private String itemCreateTime;




//    @ApiModelProperty("合作方分成比率")
//    private String payableSpiltNate;

    @ApiModelProperty(value = "应付摘要")
    private String summary;

}
