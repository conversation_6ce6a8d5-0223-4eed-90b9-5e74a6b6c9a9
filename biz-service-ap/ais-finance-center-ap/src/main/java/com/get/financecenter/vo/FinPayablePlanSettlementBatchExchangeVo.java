package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class FinPayablePlanSettlementBatchExchangeVo {
    /**
     * 财务结算汇总批次号
     */
    @ApiModelProperty(value = "财务结算汇总批次号")
    private String numSettlementBatch;

    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    private Long fkAgentId;

    /**
     * 币种编号（原币种）
     */
    @ApiModelProperty(value = "币种编号（原币种）")
    private String fkCurrencyTypeNum;

    /**
     * 币种编号（兑换币种）
     */
    @ApiModelProperty(value = "币种编号（兑换币种）")
    private String fkCurrencyTypeNumExchange;

    /**
     * 兑换汇率
     */
    @ApiModelProperty(value = "兑换汇率")
    private BigDecimal exchangeRate;

    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;

    @ApiModelProperty(value = "代理名")
    private String agentName;

    @ApiModelProperty(value = "应付类型关键字，枚举，如：m_student_offer_item")
    private String fkTypeKey;

    @ApiModelProperty(value = "应付类型名称")
    private String fkTypeName;

    @ApiModelProperty(value = "外币弹框专用字段 true:为编辑过的币种 false:为未编辑币种")
    private boolean flag;
}