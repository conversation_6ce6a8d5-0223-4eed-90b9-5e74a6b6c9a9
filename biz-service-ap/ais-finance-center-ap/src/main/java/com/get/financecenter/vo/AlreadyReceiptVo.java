package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2021/1/15
 * @TIME: 18:30
 * @Description:
 **/
@Data
public class AlreadyReceiptVo implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 已收金额
     */
    @ApiModelProperty(value = "已收金额")
    private BigDecimal alreadyReceiptAmount;

    /**
     * 已收币种
     */
    @ApiModelProperty(value = "已收币种")
    private String alreadyReceiptCurrency;

    /**
     * 已收币种名
     */
    @ApiModelProperty(value = "已收币种名")
    private String alreadyReceiptCurrencyName;

    /**
     * 汇率调整（可正可负，为平衡计算应付金额）
     */
    @ApiModelProperty(value = "汇率调整（可正可负，为平衡计算应付金额）")
    private BigDecimal alreadyExchangeRate;

    /**
     * 记录总名称
     */
    @ApiModelProperty(value = "记录总名称")
    private String targetNames;

    /**
     * 收款金额（折合应收币种金额）
     */
    @ApiModelProperty(value = "收款金额（折合应收币种金额）")
    private BigDecimal amountReceivable;

    /**
     * 应收计划id
     */
    @ApiModelProperty(value = "应收计划id")
    private Long fkReceivablePlanId;

    /**
     * 收款单id
     */
    @ApiModelProperty(value = "收款单id")
    private Long fkReceiptFormId;
}
