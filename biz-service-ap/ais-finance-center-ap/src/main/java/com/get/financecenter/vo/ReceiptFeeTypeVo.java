package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2021/12/6
 * @TIME: 15:45
 * @Description:
 **/
@Data
public class ReceiptFeeTypeVo extends BaseEntity {

    @ApiModelProperty(value = "类型组别Key")
    private String typeGroupKey;

    @ApiModelProperty(value = "类型组别名称")
    private String typeGroupKeyName;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "科目名称")
    private String accountingItemName;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty(value = "关联类型Key（目标类型表名）名称")
    private String relationTargetKeyName;

    @ApiModelProperty(value = "凭证摘要设定")
    private String vouchSummary;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

}
