package com.get.financecenter.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class BankAccountQueryDto {
    /**
     * 银行名称/账号名称/银行账号 搜索关键字
     */
    @ApiModelProperty(value = "银行名称/账号名称/银行账号 搜索关键字")
    private String keyWord;

    /**
     * 公司Id
     */
    @NotNull(message = "公司Id不能为空")
    @Min(value = 1, message = "缺少公司id参数")
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    //=========================
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private List<Long> fkCompanyIds;

}
