package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 付款单参数VO
 */
@Data
public class PaymentFormParamDto {

    @ApiModelProperty("应付计划Id")
    private Long fkPayablePlanId;

    @ApiModelProperty("代理Id")
    private Long fkAgentId;

    @ApiModelProperty("应付币种编号")
    private String fkPayableCurrencyNum;

    @ApiModelProperty("应付金额")
    private BigDecimal payableAmount;

    /**
     * 银行帐号Id（代理/供应商）
     */
    @ApiModelProperty(value = "银行帐号Id（代理/供应商）")
    private Long fkBankAccountId;
}
