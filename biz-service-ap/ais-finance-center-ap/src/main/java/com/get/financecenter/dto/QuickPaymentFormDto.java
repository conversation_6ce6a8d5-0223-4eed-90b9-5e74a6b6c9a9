package com.get.financecenter.dto;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class QuickPaymentFormDto  extends BaseEntity {

    @NotNull(message = "应付计划Id不能为空")
    @ApiModelProperty(value = "应付计划Id")
    private Long payablePlanId;

    @NotBlank(message = "应付类型关键字不能为空")
    @ApiModelProperty(value = "应付类型关键字，枚举，如：student_offer_item")
    private String fkTypeKey;

    @NotNull(message = "目标银行账户id")
    @ApiModelProperty(value = "目标银行账户id")
    private Long targetBankAccountId;

    @NotNull(message = "付款银行账户id")
    @ApiModelProperty(value = "付款银行账户id")
    private Long paymentBankAccountId;

    @NotBlank(message = "付款银行流水号")
    @ApiModelProperty(value = "流水号")
    private String paymentSerialNumber;

    @NotBlank(message = "请选择币种")
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;


    @NotNull(message = "请填写收款金额")
    @ApiModelProperty(value = "收款金额")
    private BigDecimal paymentAmount=BigDecimal.ZERO;

    @NotNull(message = "请填写手续费")
    @ApiModelProperty(value = "手续费")
    private BigDecimal paymentFee=BigDecimal.ZERO;

    @ApiModelProperty(value = "摘要")
    private String summary;

}
