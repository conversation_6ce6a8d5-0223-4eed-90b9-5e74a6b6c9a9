package com.get.financecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 第三步佣金结算总额表DTO
 */
@Data
public class AgentSettlementGrossAmountVo {

    @ApiModelProperty(value = "代理id (查询子项列表必传)")
    private Long agentId;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "州省名称")
    private String stateName;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "BD名称")
    private String bdName;
}
