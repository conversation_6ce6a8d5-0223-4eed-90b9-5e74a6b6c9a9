package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class AccountingItemDto {

    @ApiModelProperty(value = "科目id")
    private Long id;

    @ApiModelProperty(value = "父科目Id")
    private Long fkParentAccountingItemId;

    @ApiModelProperty(value = "科目类型枚举：1=资产/2=负债/3=权益/4=成本/5=损益/6=共同")
    private Integer type;

    @ApiModelProperty(value = "科目等级")
    private Integer grade;

    @ApiModelProperty(value = "科目编号")
    private String code;

    @ApiModelProperty(value = "科目名称")
    private String codeName;

    @ApiModelProperty(value = "余额方向：0借/1贷。资产、费用【借增贷减】。负债、权益、收入【贷增借减】。")
    private Integer direction;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "关键字")
    private String keyWord;
}
