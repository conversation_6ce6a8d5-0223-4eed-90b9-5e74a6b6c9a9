package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 公司科目
 */
@Data
public class CompanyAccountingItemVo extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "公司科目套账Id")
    private Long id;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "资产负债表类型Id")
    private Long fkBalanceSheetTypeId;

    @ApiModelProperty(value = "资产负债表类型名称")
    private String balanceSheetTypeName;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "科目编码")
    private String accountingItemCode;

    @ApiModelProperty(value = "科目名称")
    private String accountingItemName;

    @ApiModelProperty(value = "余额方向：0借/1贷。资产、费用【借增贷减】。负债、权益、收入【贷增借减】。")
    private String direction;

    @ApiModelProperty(value = "余额方向：0借/1贷。资产、费用【借增贷减】。负债、权益、收入【贷增借减】。")
    private String directionName;

    @ApiModelProperty(value = "借方期初数")
    private BigDecimal amountDrOpeningBalance;

    @ApiModelProperty(value = "贷方期初数")
    private BigDecimal amountCrOpeningBalance;

}

