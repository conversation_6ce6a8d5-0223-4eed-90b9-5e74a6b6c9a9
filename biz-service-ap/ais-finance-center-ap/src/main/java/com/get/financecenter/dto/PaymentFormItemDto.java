package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/12/21
 * @TIME: 16:55
 * @Description:
 **/
@Data
public class PaymentFormItemDto extends BaseVoEntity{

    /**
     * 付款单Id
     */
    @NotNull(message = "付款单Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "付款单Id", required = true)
    private Long fkPaymentFormId;

    /**
     * 应付计划Id
     */
    @NotNull(message = "应付计划Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "应付计划Id", required = true)
    private Long fkPayablePlanId;

    /**
     * 付款金额（拆分/总）
     */
    @ApiModelProperty(value = "付款金额（拆分/总）")
    private BigDecimal amountPayment;

    /**
     * 汇率（折合应付币种汇率）
     */
    @ApiModelProperty(value = "汇率（折合应付币种汇率）")
    private BigDecimal exchangeRatePayable;

    /**
     * 付款金额（折合应付币种金额）
     */
    @ApiModelProperty(value = "付款金额（折合应付币种金额）")
    private BigDecimal amountPayable;

    /**
     * 汇率调整（可正可负，为平衡计算应付金额）
     */
    @ApiModelProperty(value = "汇率调整（可正可负，为平衡计算应付金额）")
    private BigDecimal amountExchangeRate;

    /**
     * 汇率（折合港币）
     */
    @ApiModelProperty(value = "汇率（折合港币）")
    private BigDecimal exchangeRateHkd;

    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;

    /**
     * 付款金额（折合港币）
     */
    @ApiModelProperty(value = "付款金额（折合港币）")
    private BigDecimal amountHkd;

    /**
     * 汇率（折合人民币）
     */
    @ApiModelProperty(value = "汇率（折合人民币）")
    private BigDecimal exchangeRateRmb;

    /**
     * 付款金额（折合人民币）
     */
    @ApiModelProperty(value = "付款金额（折合人民币）")
    private BigDecimal amountRmb;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;


    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 创建时间(开始)
     */
    @ApiModelProperty("创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    /**
     * 创建时间(结束)
     */
    @ApiModelProperty("创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;


    /**
     * 目标类型关键字，枚举：m_student_offer_item
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_student_offer_item")
    private String fkTypeKey;

    /**
     * 对应记录Id
     */
    @ApiModelProperty(value = "对应记录id")
    private Long fkTypeTargetId;

    @ApiModelProperty(value = "付款单Id列表")
    private List<Long> fkPaymentFormIds;
}
