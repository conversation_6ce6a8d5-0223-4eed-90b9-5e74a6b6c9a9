package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
public class ReceiptReDto {

    @ApiModelProperty("收款单id")
    private Long formId;

    @ApiModelProperty("发票金额排序")
    private Boolean invoiceAmountSort;

    @ApiModelProperty("实收金额排序")
    private Boolean receiptAmountSort;

    public Boolean getInvoiceAmountSort() {
        if (Objects.isNull(invoiceAmountSort)) {
            return false;
        }
        return invoiceAmountSort;
    }

    public Boolean getReceiptAmountSort() {
        if (Objects.isNull(receiptAmountSort)) {
            return false;
        }
        return receiptAmountSort;
    }

    @ApiModelProperty("学生名称")
    private String studentName;
}
