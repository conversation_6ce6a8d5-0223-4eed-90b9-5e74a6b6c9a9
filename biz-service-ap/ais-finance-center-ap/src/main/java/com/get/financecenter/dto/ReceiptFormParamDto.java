package com.get.financecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 收款单参数VO
 */
@Data
public class ReceiptFormParamDto {

    @ApiModelProperty("应收计划Id")
    private Long fkReceivablePlanId;

    @ApiModelProperty("学生Id")
    private Long fkStudentId;

    @ApiModelProperty("应收币种编号")
    private String fkReceivableCurrencyNum;

    @ApiModelProperty("应收金额")
    private BigDecimal receivableAmount;
}
