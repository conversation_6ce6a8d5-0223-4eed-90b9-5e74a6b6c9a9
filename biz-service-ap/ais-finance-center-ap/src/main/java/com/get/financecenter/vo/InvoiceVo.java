package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.financecenter.entity.Invoice;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/12/16 17:11
 * @verison: 1.0
 * @description:
 */
@Data
public class InvoiceVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 学校提供商名称
     */
    @ApiModelProperty(value = "学校提供商名称")
    private String institutionProviderName;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 中间表id
     */
    @ApiModelProperty(value = "中间表id")
    private Long pid;

    /**
     * 原币种名称
     */
    @ApiModelProperty(value = "原币种名称")
    private String fkCurrencyTypeNumOrcName;
    /**
     * 实收币种名称
     */
    @ApiModelProperty(value = "实收币种名称")
    private String fkCurrencyTypeNumReceiptName;

    /**
     * 计划id
     */
    @ApiModelProperty(value = "计划id")
    private Long fkReceivablePlanId;


    @ApiModelProperty("收款单id")
    private Long receiptFormId;
    /**
     * 目标类型名称
     */
    @ApiModelProperty(value = "目标类型名称")
    private String fkTypeKeyName;

    /**
     * 目标名称
     */
    @ApiModelProperty(value = "目标名称")
    private String targetName;

    /**
     * 金额小计
     */
    @ApiModelProperty(value = "金额小计")
    private BigDecimal subtotal;


    @ApiModelProperty(value = "目标Id集合")
    private Set<Long> fkTypeTargetIds;

    @ApiModelProperty(value = "是否到账")
    private Boolean isAccount = false;

    @ApiModelProperty(value = "true前端需要高亮显示，false则不处理")
    private Boolean mark = false;

    //================实体类Invoice======================

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    private String num;
    /**
     * 国内发票编号
     */
    @ApiModelProperty(value = "国内发票编号")
    private String localInvoiceNum;

    /**
     * po号码
     */
    @ApiModelProperty(value = "po号码")
    private String poNum;

    /**
     * 应付类型关键字，枚举，如：m_student_offer_item
     */
    @ApiModelProperty(value = "业务类型关键字，枚举，如：m_student_offer_item留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿")
    private String fkTypeKey;
    /**
     * 开票日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "开票日期")
    private Date invoiceDate;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
    /**
     * 付款方实付单号
     */
    @ApiModelProperty(value = "付款方实付单号")
    private String typeTargetPaymentNum;
    /**
     * 开票金额
     */
    @ApiModelProperty(value = "开票金额")
    private BigDecimal invoiceAmount;
    /**
     * 实收日期
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "实收日期")
    private Date receiptDate;
    /**
     * 实收币种
     */
    @ApiModelProperty(value = "实收币种")
    private String fkCurrencyTypeNumReceipt;
    /**
     * 实收金额
     */
    @ApiModelProperty("实收金额")
    private BigDecimal receiptAmount;
    /**
     * 原币种
     */
    @ApiModelProperty("原币种")
    private String fkCurrencyTypeNumOrc;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    private Integer status;

}

