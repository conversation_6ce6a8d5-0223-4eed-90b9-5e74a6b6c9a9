package com.get.financecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PaymentApplicationFormQueryDto {

    @ApiModelProperty(value = "支付申请单编号（系统生成）")
    private String num;

    @ApiModelProperty(value = "流程key")
    private String procdkey;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "办公室Id")
    private Long fkOfficeId;

    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "1查询全部，0查询个人,2 查询我的审批")
    private String selectStatus;

    @ApiModelProperty(value = "公司id")
    private List<Long> fkCompanyIds;

    @ApiModelProperty(value = "审批状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private Integer status;

    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;

}
