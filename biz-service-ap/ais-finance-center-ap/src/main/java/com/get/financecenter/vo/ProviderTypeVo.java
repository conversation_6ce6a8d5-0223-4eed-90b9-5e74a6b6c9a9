package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.financecenter.entity.ProviderType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/12/18 10:28
 * @verison: 1.0
 * @description:
 */
@Data
public class ProviderTypeVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    //===================实体类ProviderType======================
    /**
     * 供应商类型名称
     */
    @ApiModelProperty(value = "供应商类型名称")
    private String typeName;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
