package com.get.financecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/3/30 16:17
 */
@Data
public class PaymentApplicationFormDto extends BaseVoEntity {


    /**
     * 申请日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "申请日期")
    private Date applicationDate;

    @ApiModelProperty(value = "1查询全部，0查询个人,2 查询我的审批")
    private String selectStatus;
    @ApiModelProperty(value = "附件")
    private List<MediaAndAttachedDto> mediaAndAttachedVos;
    @ApiModelProperty("任务版本号")
    private Integer taskVersion;
    @ApiModelProperty("任务id")
    private String taskId;

    /**
     * 公司Ids
     */
    @ApiModelProperty(value = "公司Ids")
    private List<Long> fkCompanyIds;

    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("流程key")
    private String procdkey;

    @ApiModelProperty(value = "支付明细")
    private List<PaymentApplicationFormItemDto> paymentApplicationFormItemVos;

    //------------------表字段
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;

    @ApiModelProperty(value = "申请人Id")
    private Long fkStaffId;

    @ApiModelProperty("关联撤销表单Id")
    private Long fkPaymentApplicationFormIdRevoke;

    @ApiModelProperty(value = "支付申请单编号（系统生成）")
    private String num;

    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "摘要")
    private String summary;

    @ApiModelProperty(value = "凭证Id")
    private Long fkVouchId;

    @ApiModelProperty(value = "是否创建凭证：0否/1是")
    private Boolean isVouchCreated;

    @ApiModelProperty(value = "创建凭证时间")
    private Date dateVouchCreated;

    @ApiModelProperty(value = "创建凭证人")
    private Integer fkStaffIdVouchCreated;

    @ApiModelProperty(value = "通知员工Ids，支持多选（英文逗号分隔：1,2,3）")
    private String fkStaffIdsNotice;

    @ApiModelProperty(value = "状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private Integer status;

}
