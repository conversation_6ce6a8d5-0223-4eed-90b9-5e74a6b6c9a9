package com.get.financecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.financecenter.entity.CurrencyType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/9/17 11:53
 * @verison: 1.0
 * @description:
 */
@Data
public class CurrencyTypeVo extends BaseEntity {
    private static final long serialVersionUID = 1L;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;

    /**
     * 公开对象名称
     */
    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;

    //=============实体类CurrencyType=================
    /**
     * 货币类型编号
     */
    @ApiModelProperty(value = "货币类型编号")
    private String num;
    /**
     * 货币类型名称
     */
    @ApiModelProperty(value = "货币类型名称")
    private String typeName;
    /**
     * 公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2
     */
    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
