package com.get.financecenter.dto.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class PrepayApplicationFormQueryDto {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty("流程key")
    private String procdkey;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;

    @ApiModelProperty(value = "审批状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    private Integer status;

    /**
     * 借款申请单编号（系统生成）
     */
    @ApiModelProperty(value = "借款申请单编号（系统生成）")
    private String num;

    @ApiModelProperty("开始时间")
    private Date startTime;

    @ApiModelProperty("结束时间")
    private Date endTime;

    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;

    @ApiModelProperty(value = "1查询全部，0查询个人，2我的审批")
    private String selectStatus;

    //===========================
    /**
     * 公司Ids
     */
    @ApiModelProperty(value = "公司Ids")
    private List<Long> fkCompanyIds;

}
