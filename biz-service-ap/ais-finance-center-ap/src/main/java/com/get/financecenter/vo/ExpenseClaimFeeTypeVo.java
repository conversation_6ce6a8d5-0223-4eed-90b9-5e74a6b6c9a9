package com.get.financecenter.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2021/4/6 16:56
 * @verison: 1.0
 * @description:
 */
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ExpenseClaimFeeTypeVo extends BaseEntity {

    @ApiModelProperty(value = "关联项关联类型Key（目标类型表名）")
    private String relationTargetKey;

    @ApiModelProperty(value = "关联项关联类型名")
    private String relationTargetKeyName;

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "科目Id")
    private Long fkAccountingItemId;

    @ApiModelProperty(value = "科目名称")
    private String accountingItemName;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

}
