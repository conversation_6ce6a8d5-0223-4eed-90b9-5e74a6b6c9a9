package com.get.reportcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_staff")
public class ReportStaff extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;
    /**
     * 职位Id
     */
    @ApiModelProperty(value = "职位Id")
    private Long fkPositionId;
    /**
     * 办公室Id
     */
    @ApiModelProperty(value = "办公室Id")
    private Long fkOfficeId;
    /**
     * 简历guid(人才中心)
     */
    @ApiModelProperty(value = "简历guid(人才中心)")
    private String fkResumeGuid;
    /**
     * 登陆用户Id
     */
    @ApiModelProperty(value = "登陆用户Id")
    private String loginId;
    /**
     * 登陆用户密码
     */
    @ApiModelProperty(value = "登陆用户密码")
    private String loginPs;
    /**
     * 员工编号
     */
    @ApiModelProperty(value = "员工编号")
    private String num;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String name;
    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    private String nameEn;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    private Integer gender;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    private Date birthday;
    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String identityCard;
    /**
     * 住址电话
     */
    @ApiModelProperty(value = "住址电话")
    private String homeTel;
    /**
     * 工作电话
     */
    @ApiModelProperty(value = "工作电话")
    private String workTel;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    private String mobile;
    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    private String email;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    private String zipCode;
    /**
     * 地址
     */
    @ApiModelProperty(value = "地址")
    private String address;
    /**
     * QQ号
     */
    @ApiModelProperty(value = "QQ号")
    private String qq;
    /**
     * 微信号
     */
    @ApiModelProperty(value = "微信号")
    private String wechat;
    /**
     * whatsapp号
     */
    @ApiModelProperty(value = "whatsapp号")
    private String whatsapp;
    /**
     * 紧急联系方式
     */
    @ApiModelProperty(value = "紧急联系方式")
    private String emergencyContact;
    /**
     * 职责
     */
    @ApiModelProperty(value = "职责")
    private String jobDescription;
    /**
     * 工资生效日期
     */
    @ApiModelProperty(value = "工资生效日期")
    private Date salaryEffectiveDate;
    /**
     * 基本工资
     */
    @ApiModelProperty(value = "基本工资")
    private BigDecimal salaryBase;
    /**
     * 绩效工资
     */
    @ApiModelProperty(value = "绩效工资")
    private BigDecimal salaryPerformance;
    /**
     * 岗位津贴
     */
    @ApiModelProperty(value = "岗位津贴")
    private BigDecimal allowancePosition;
    /**
     * 餐饮津贴
     */
    @ApiModelProperty(value = "餐饮津贴")
    private BigDecimal allowanceCatering;
    /**
     * 交通津贴
     */
    @ApiModelProperty(value = "交通津贴")
    private BigDecimal allowanceTransportation;
    /**
     * 通讯津贴
     */
    @ApiModelProperty(value = "通讯津贴")
    private BigDecimal allowanceTelecom;
    /**
     * 其他津贴
     */
    @ApiModelProperty(value = "其他津贴")
    private BigDecimal allowanceOther;
    /**
     * 入职时间
     */
    @ApiModelProperty(value = "入职时间")
    private Date entryDate;
    /**
     * 转正时间
     */
    @ApiModelProperty(value = "转正时间")
    private Date passProbationDate;
    /**
     * 离职时间
     */
    @ApiModelProperty(value = "离职时间")
    private Date leaveDate;
    /**
     * 是否在职，0否/1是
     */
    @ApiModelProperty(value = "是否在职，0否/1是")
    private Boolean isOnDuty;
    /**
     * 是否领取离职证明，0否/1是
     */
    @ApiModelProperty(value = "是否领取离职证明，0否/1是")
    private Boolean isGetLeavingCertificate;
    /**
     * 是否已经停保，0否/1是
     */
    @ApiModelProperty(value = "是否已经停保，0否/1是")
    private Boolean isStopSocialInsurance;
    /**
     * 停保年月
     */
    @ApiModelProperty(value = "停保年月")
    private String stopSocialInsuranceMonth;
    /**
     * 是否强制修改密码，0否/1是
     */
    @ApiModelProperty(value = "是否强制修改密码，0否/1是")
    private Boolean isModifiedPs;
    /**
     * 是否可编辑简历，0否/1是
     */
    @ApiModelProperty(value = "是否可编辑简历，0否/1是")
    private Boolean isModifiedResume;
    /**
     * 是否超级管理员：0否/1是
     */
    @ApiModelProperty(value = "是否超级管理员：0否/1是")
    private Boolean isAdmin;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;
    /**
     * 用户当前登录会话id
     */
    @ApiModelProperty(value = "用户当前登录会话id")
    private String sessionId;
}