package com.get.reportcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("m_adv_search_config")
public class AdvSearchConfig extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 高级搜索脚本key
     */
    @ApiModelProperty(value = "高级搜索脚本key")
    private String queryKey;
    /**
     * 脚本标题
     */
    @ApiModelProperty(value = "脚本标题")
    private String queryTitle;
    /**
     * 结果表格长度
     */
    @ApiModelProperty(value = "结果表格长度")
    private Integer queryWidth;
    /**
     * Sql脚本
     */
    @ApiModelProperty(value = "Sql脚本")
    private String querySql;
    /**
     * 搜索参数
     */
    @ApiModelProperty(value = "搜索参数")
    private String queryParamter;
}