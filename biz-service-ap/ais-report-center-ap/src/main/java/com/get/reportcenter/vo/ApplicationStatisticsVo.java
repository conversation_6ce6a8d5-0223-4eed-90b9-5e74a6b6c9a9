package com.get.reportcenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2022/3/29
 * @TIME: 19:30
 * @Description:
 **/
@Data
public class ApplicationStatisticsVo {
    /**
     * 统计标题名
     */
    @ApiModelProperty(value = "统计标题名")
    private String title;

    /**
     * 跳转类型
     */
    @ApiModelProperty(value = "跳转类型：1-日期类型；2-日期时间类型（周报周统计跳转）；3-业绩统计：【定校量（按学生）】、【成功入学量（按学生）】跳转;5-业绩统计按学生+日期时间类型")
    private Integer jumpState;

    /**
     * 结果1
     */
    @ApiModelProperty(value = "结果1")
    private BigDecimal resultOne;

    /**
     * 结果2
     */
    @ApiModelProperty(value = "结果2")
    private BigDecimal resultTwo;

    /**
     * 变化比例（结果1与结果2的对比）
     */
    @ApiModelProperty(value = "变化比例（结果1与结果2的对比）")
    private String changeRatio;


    /**
     * 变化状态：-1（降）；0（无变化）；1（升）
     */
    @ApiModelProperty(value = "变化状态：-1（降）；0（无变化）；1（升）")
    private int changeStatus;

}
