package com.get.reportcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_payment_form_item")
public class ReportPaymentFormItem extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 付款单Id
     */
    @ApiModelProperty(value = "付款单Id")
    private Long fkPaymentFormId;
    /**
     * 应付计划Id
     */
    @ApiModelProperty(value = "应付计划Id")
    private Long fkPayablePlanId;
    /**
     * 付款金额（拆分/总）
     */
    @ApiModelProperty(value = "付款金额（拆分/总）")
    private BigDecimal amountPayment;
    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;
    /**
     * 汇率（折合应付币种汇率）
     */
    @ApiModelProperty(value = "汇率（折合应付币种汇率）")
    private BigDecimal exchangeRatePayable;
    /**
     * 付款金额（折合应付币种金额）
     */
    @ApiModelProperty(value = "付款金额（折合应付币种金额）")
    private BigDecimal amountPayable;
    /**
     * 汇率调整金额（可正可负，为平衡计算应付金额）
     */
    @ApiModelProperty(value = "汇率调整金额（可正可负，为平衡计算应付金额）")
    private BigDecimal amountExchangeRate;
    /**
     * 汇率（折合港币）
     */
    @ApiModelProperty(value = "汇率（折合港币）")
    private BigDecimal exchangeRateHkd;
    /**
     * 付款金额（折合港币）
     */
    @ApiModelProperty(value = "付款金额（折合港币）")
    private BigDecimal amountHkd;
    /**
     * 汇率（折合人民币）
     */
    @ApiModelProperty(value = "汇率（折合人民币）")
    private BigDecimal exchangeRateRmb;
    /**
     * 付款金额（折合人民币）
     */
    @ApiModelProperty(value = "付款金额（折合人民币）")
    private BigDecimal amountRmb;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
}