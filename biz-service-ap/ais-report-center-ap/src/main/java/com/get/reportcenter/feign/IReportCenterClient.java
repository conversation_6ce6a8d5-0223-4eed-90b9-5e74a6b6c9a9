package com.get.reportcenter.feign;

import com.alibaba.fastjson.JSONObject;
import com.get.common.constant.AppCenterConstant;
import com.get.common.result.ResponseBo;
import com.get.core.tool.api.Result;
import com.get.reportcenter.entity.ReportSale;
import com.get.reportcenter.dto.ReportSaleDto;
import com.get.salecenter.vo.PeriodicStatisticsVo;
import com.get.salecenter.dto.StudentApplicationStatisticsDto;
import org.apache.ibatis.annotations.Param;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * Feign接口类
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_REPORT_CENTER
)
public interface IReportCenterClient {

	String API_PREFIX = "/feign";
    /**
     * 查询最近一次周报统计报表记录
     */
	String GET_LAST_REPORT_SALE =  API_PREFIX + "/get-last-report-sale";

    /**
     * 查询周报统计报表
     */
    String GET_REPORT_SALE = API_PREFIX + "/get-report-sale";
    /**
     * 修改周报统计报表状态
     */
    String UPDATE_REPORT_SALE_STATUS = API_PREFIX + "/update-report-sale-status";
    /**
     * 新增周报统计报表
     */
    String ADD_REPORT_SALE = API_PREFIX + "/add-report-sale";
    /**
     * 新增周报统计报表
     */
    String ADD_REPORT_SALE_COMMON = API_PREFIX + "/add-report-sale-common";
    /**
     * 修改周报统计报表
     */
    String UPDATE_REPORT_SALE = API_PREFIX + "/update-report-sale";

    String GET_REPORT_SALE_STATUS_BY_ID = API_PREFIX + "/get-report-sale-status-by-id";

    String GET_REPORT_SALE_BY_ID = API_PREFIX + "/get-report-sale-by-id";

    String GET_LAST_REPORT_SALE_VO_BY_REPORT_NAME_AND_USER_ID = API_PREFIX + "/get-last-report-sale-vo-by-report-name-and-user-id";

//	String SUCCESSFULLY_EXPORTED_CUSTOMER_LIST = API_PREFIX + "/successfully-exported-customer-list";
//	@PostMapping(SUCCESSFULLY_EXPORTED_CUSTOMER_LIST)
//	Result<List<StudentOfferItemReportModel>> successfullyExportedCustomerList(@RequestBody ReportStudentOfferItemVo studentOfferItemVo);


    /**
     * 查询最近一次周报统计报表记录
     */
    @PostMapping(GET_LAST_REPORT_SALE)
    Result<ReportSale> getLastReportSale(@RequestParam("fkStaffId") Long fkStaffId);

    /**
     * 查询周报统计报表记录
     */
    @PostMapping(GET_REPORT_SALE)
    Result<ReportSale> getReportSale(@RequestBody StudentApplicationStatisticsDto studentApplicationStatisticsVo);

    /**
     * 修改周报统计报表记录状态
     */
    @PostMapping(UPDATE_REPORT_SALE_STATUS)
    void updateReportSaleStatus(@RequestParam("fkReportSaleId") Long fkReportSaleId, @RequestParam("reportStatus") Integer reportStatus);

    /**
     * 新增周报统计报表记录
     * <AUTHOR>
     * @DateTime 2022/12/20 12:20
     */
    @PostMapping(ADD_REPORT_SALE)
    Result<Long> addReportSale(@RequestBody StudentApplicationStatisticsDto studentApplicationStatisticsVo);

    /**
     * 新增报表记录
     * <AUTHOR>
     * @DateTime 2022/12/20 12:20
     */
    @PostMapping(ADD_REPORT_SALE_COMMON)
    Result<Long> addReportSaleCommon(@RequestBody ReportSaleDto reportSaleVo);

    /**
     * 修改周报统计报表记录
     * <AUTHOR>
     * @DateTime 2022/12/20 15:50
     */
    @PostMapping(UPDATE_REPORT_SALE)
    void updateReportSale(@RequestBody PeriodicStatisticsVo periodicStatisticsDto,@RequestParam("fkReportSaleId") Long fkReportSaleId);

    /**
     * 修改周报统计报表记录
     * <AUTHOR>
     * @DateTime 2022/12/20 15:50
     */
    @PostMapping(GET_REPORT_SALE_STATUS_BY_ID)
    Result<Integer> getReportSaleStatusById(@RequestParam("fkReportSaleId") Long fkReportSaleId);

    /**
     *
     * @param fkReportSaleId
     * @return
     */
    @PostMapping(GET_REPORT_SALE_BY_ID)
    Result<ReportSaleDto> getReportSaleById(@RequestParam("fkReportSaleId")Long fkReportSaleId);

    @PostMapping(GET_LAST_REPORT_SALE_VO_BY_REPORT_NAME_AND_USER_ID)
    Result<ReportSaleDto> getLastReportSaleVoByReportNameAndUserId(@RequestParam("key")String key, @RequestParam("staffId")Long staffId);
}
