package com.get.reportcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_receipt_form_item")
public class ReportReceiptFormItem extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 收款单Id
     */
    @ApiModelProperty(value = "收款单Id")
    private Long fkReceiptFormId;
    /**
     * 应收计划Id
     */
    @ApiModelProperty(value = "应收计划Id")
    private Long fkReceivablePlanId;
    /**
     * 收款金额（拆分/总）
     */
    @ApiModelProperty(value = "收款金额（拆分/总）")
    private BigDecimal amountReceipt;
    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    private BigDecimal serviceFee;
    /**
     * 汇率（折合应收币种汇率）
     */
    @ApiModelProperty(value = "汇率（折合应收币种汇率）")
    private BigDecimal exchangeRateReceivable;
    /**
     * 收款金额（折合应收币种金额）
     */
    @ApiModelProperty(value = "收款金额（折合应收币种金额）")
    private BigDecimal amountReceivable;
    /**
     * 汇率调整金额（可正可负，为平衡计算应收金额）
     */
    @ApiModelProperty(value = "汇率调整金额（可正可负，为平衡计算应收金额）")
    private BigDecimal amountExchangeRate;
    /**
     * 汇率（折合港币）
     */
    @ApiModelProperty(value = "汇率（折合港币）")
    private BigDecimal exchangeRateHkd;
    /**
     * 收款金额（折合港币）
     */
    @ApiModelProperty(value = "收款金额（折合港币）")
    private BigDecimal amountHkd;
    /**
     * 汇率（折合人民币）
     */
    @ApiModelProperty(value = "汇率（折合人民币）")
    private BigDecimal exchangeRateRmb;
    /**
     * 收款金额（折合人民币）
     */
    @ApiModelProperty(value = "收款金额（折合人民币）")
    private BigDecimal amountRmb;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;
}