package com.get.reportcenter.feign;

import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.reportcenter.dto.ReportSaleDto;
import com.get.reportcenter.entity.ReportSale;
//import com.get.reportcenter.vo.ReportSaleVo;
import com.get.salecenter.vo.PeriodicStatisticsVo;
import com.get.salecenter.dto.StudentApplicationStatisticsDto;
import com.get.salecenter.vo.StudentApplicationStatisticsVo;
import org.springframework.stereotype.Component;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class IReportCenterClientFallBack implements IReportCenterClient {
    @Override
    public Result<ReportSale> getLastReportSale(Long fkStaffId) {
        return null;
    }

    @Override
    public Result<ReportSale> getReportSale(StudentApplicationStatisticsDto studentApplicationStatisticsDto) {
        return null;
    }

    @Override
    public void updateReportSaleStatus(Long fkReportSaleId, Integer reportStatus){
    }

    @Override
    public Result<Long> addReportSale(StudentApplicationStatisticsDto studentApplicationStatisticsDto) {
        return null;
    }

    @Override
    public Result<Long> addReportSaleCommon(ReportSaleDto reportSaleVo) {
        return null;
    }

    @Override
    public void updateReportSale(PeriodicStatisticsVo periodicStatisticsVo, Long fkReportSaleId) {

    }

    @Override
    public Result<Integer> getReportSaleStatusById(Long fkReportSaleId) {
        return null;
    }

    @Override
    public Result<ReportSaleDto> getReportSaleById(Long fkReportSaleId) {
        return Result.fail("没数据返回！");
    }

    @Override
    public Result<ReportSaleDto> getLastReportSaleVoByReportNameAndUserId(String key, Long staffId) {
        return Result.fail("没数据返回！");
    }

//    @Override
//    public Result<List<StudentOfferItemReportModel>> successfullyExportedCustomerList(ReportStudentOfferItemVo studentOfferItemVo) {
//        return Result.fail("获取数据失败");
//    }
}
