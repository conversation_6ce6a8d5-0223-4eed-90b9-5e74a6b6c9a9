package com.get.reportcenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 成功客户导出Excel 模板类
 *
 * @Date 10:00 2022/3/3
 * <AUTHOR>
 */
@Data
public class StudentOfferItemReportModel {

//    @ApiModelProperty(value = "旧数据财务id(gea)")
//    private String idGeaFinance;

    /**
     * 学习计划id
     */
    @ApiModelProperty(value = "appId")
    private Long appId;

    /**
     * 成功年份
     */
    @ApiModelProperty(value = "year")
    private String year;

    @ApiModelProperty(value = "no.")
    private String no;

    /**
     * 成功月份
     */
    @ApiModelProperty(value = "Succ. Date")
    private String successTime;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "申请时间")
    private Date applicationTime;

    /**
     * 成功季度
     */
    @ApiModelProperty(value = "Succ. Q")
    private String succQ;

    /**
     * bd名
     */
    @ApiModelProperty(value = "RR")
    private String rr;

    /**
     * 代理名
     */
    @ApiModelProperty(value = "Agent Source")
    private String agentSource;

    /**
     * 代理省份
     */
    @ApiModelProperty(value = "Agent Region")
    private String agentRegion;

    /**
     * 学生名
     */
    @ApiModelProperty(value = "Student")
    private String studentName;

    /**
     * cpp学生编号
     */
    @ApiModelProperty(value = "Our ID.")
    private String ourId;

    /**
     * 学生名pinyin
     */
    @ApiModelProperty(value = "studentPinYin")
    private String studentPinYin;

    /**
     * 报考国家编号
     */
    @ApiModelProperty(value = "Country")
    private String countryNum;

    /**
     * 学校name
     */
    @ApiModelProperty(value = "School")
    private String schoolName;

    /**
     * 学校类型
     */
    @ApiModelProperty(value = "School Type")
    private String schoolType;

    /**
     * CPP学校name
     */
    @ApiModelProperty(value = "CPP School")
    private String oldInstitutionName;

    /**
     * 学校集团
     */
    @ApiModelProperty(value = "Group")
    private String group;

    /**
     * 报考课程name
     */
    @ApiModelProperty(value = "Course")
    private String course;

    /**
     * cpp报考课程name
     */
    @ApiModelProperty(value = "CPP Course")
    private String oldCourseCustomName;

    /**
     * 报考课程等级
     */
    @ApiModelProperty(value = "Major Type")
    private String majorType;

    /**
     * 报考课程类型
     */
    @ApiModelProperty(value = "Course Type")
    private String courseType;

    /**
     * 学生自己id
     */
    @ApiModelProperty(value = "STD ID")
    private String stdId;

    @ApiModelProperty(value = "DOB")
    private String dob;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "CHOP")
    private String chop;

    /**
     * 入学年份
     */
    @ApiModelProperty(value = "Intake Year")
    private String intakeYear;

    /**
     * 入学月份
     */
    @ApiModelProperty(value = "Intake Mth")
    private String intakeMth;

    /**
     * 入学季度
     */
    @ApiModelProperty(value = "Intake Q")
    private String intakeQ;

    @ApiModelProperty(value = "Source CCY")
    private String sourceCcy;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "School Fee")
    private BigDecimal tuitionAmount;

    /**
     * 费率%
     */
    @ApiModelProperty(value = "Rate")
    private BigDecimal commissionRate;

    /**
     * 渠道费率%
     */
    @ApiModelProperty(value = "Net%")
    private BigDecimal netRate;

    /**
     * 总佣金金额
     */
    @ApiModelProperty(value = "Amount")
    private BigDecimal amount;

    /**
     * 总奖励金额
     */
    @ApiModelProperty(value = "Additional Amount")
    private BigDecimal additionalAmount;

    /**
     * 总应收金额
     */
    @ApiModelProperty(value = "Total Amt in $")
    private BigDecimal totalAmt;

    @ApiModelProperty(value = "Rev CCY")
    private String revCyy;

//    /**
//     * 第一次部分收款币种
//     */
//    @ApiModelProperty(value = "")
//    private String revCyy1;

    /**
     * 第一部分实收金额
     */
    @ApiModelProperty(value = "1st term")
    private BigDecimal stTerm1;

    /**
     * 第一次部分收款时间
     */
    @ApiModelProperty(value = "1st Date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stDate1;

    /**
     * 银行
     */
    @ApiModelProperty(value = "1st Bank")
    private String stBank1;

//    /**
//     *第二次部分收款币种
//     */
//    @ApiModelProperty(value = "")
//    private String revCyy2;

    /**
     * 第二部分实收金额
     */
    @ApiModelProperty(value = "2nd term")
    private BigDecimal stTerm2;

    /**
     * 第二部分收款时间
     */
    @ApiModelProperty(value = "2nd Date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stDate2;

    /**
     * 银行
     */
    @ApiModelProperty(value = "2nd Bank")
    private String stBank2;

//    /**
//     * 第三次部分收款币种
//     */
//    @ApiModelProperty(value = "")
//    private String revCyy3;

    /**
     * 第三部分收款金额
     */
    @ApiModelProperty(value = "3rd term")
    private BigDecimal stTerm3;

    /**
     * 第三部分实收时间
     */
    @ApiModelProperty(value = "3rd Date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stDate3;

    /**
     * 银行
     */
    @ApiModelProperty(value = "3rd Bank")
    private String stBank3;

//    /**
//     *第四次部分收款币种
//     */
//    @ApiModelProperty(value = "")
//    private String revCyy4;

    /**
     * 第四部分收款金额
     */
    @ApiModelProperty(value = "4th term")
    private BigDecimal stTerm4;

    /**
     * 第四部分收款时间
     */
    @ApiModelProperty(value = "4th Date")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stDate4;

    /**
     * 银行
     */
    @ApiModelProperty(value = "4th Bank")
    private String stBank4;

    /**
     * 总收款金额
     */
    @ApiModelProperty(value = "Total Received")
    private BigDecimal totalReceiptAmount;

    @ApiModelProperty(value = "Source CCY = Rev CCY?")
    private BigDecimal sourceRevCcy;

    @ApiModelProperty(value = "bankCharges")
    private BigDecimal bankCharges;

    @ApiModelProperty(value = "AR outstanding")
    private BigDecimal arOutstanding;

    @ApiModelProperty(value = "AR Status\n" +
            "(Processing/COMPLETED/WITHDRAW/Written off)")
    private String arStatus;

    /**
     * 代理费率%
     */
    @ApiModelProperty(value = "Agent Rate")
    private BigDecimal agCommissionRate;

    /**
     * 代理分成比率
     */
    @ApiModelProperty(value = "Split Rate")
    private BigDecimal agSplitRate;

    /**
     * 代理总佣金金额
     */
    @ApiModelProperty(value = "Agent Comm.")
    private BigDecimal agCommissionAmount;

    /**
     * 代理总奖励金额
     */
    @ApiModelProperty(value = "Additional Agent Comm")
    private BigDecimal agBonusAmount;

    /**
     * 代理总应付金额
     */
    @ApiModelProperty(value = "Total Agent Comm in $")
    private BigDecimal totalAgentAmt;


    /**
     * 第一部分付款金额
     */
    @ApiModelProperty(value = "1st payment")
    private BigDecimal stPayment1;

    /**
     * 第一部分付款时间
     */
    @ApiModelProperty(value = "1st PayDate")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stPayDate1;

    /**
     * 第二部分付款金额
     */
    @ApiModelProperty(value = "2nd payment")
    private BigDecimal stPayment2;

    /**
     * 第二部分付款时间
     */
    @ApiModelProperty(value = "2nd PayDate")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stPayDate2;

    /**
     * 第三部分付款金额
     */
    @ApiModelProperty(value = "3rd payment")
    private BigDecimal stPayment3;

    /**
     * 第三部分付款时间
     */
    @ApiModelProperty(value = "3rd PayDate")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stPayDate3;

    /**
     * 第四部分付款金额
     */
    @ApiModelProperty(value = "4th payment")
    private BigDecimal stPayment4;

    /**
     * 第四部分付款时间
     */
    @ApiModelProperty(value = "4th PayDate")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stPayDate4;

    /**
     * 总付款金额
     */
    @ApiModelProperty(value = "Total Paid")
    private BigDecimal totalPaymentAmount;

    @ApiModelProperty(value = "Agent Bonus")
    private String agentBonus;

    @ApiModelProperty(value = "Bank Charges")
    private String agentBankCharges;

    @ApiModelProperty(value = "AP Outstanding")
    private String apOutstanding;

    @ApiModelProperty(value = "AP Status\n" +
            "(Processing/COMPLETED/withdraw/Written off)")
    private String apStatus;

    @ApiModelProperty(value = "GEA Processing Fee")
    private String geaProcessingFee;

    @ApiModelProperty(value = "RO Comm")
    private String roComm;

    @ApiModelProperty(value = "Gross Amount in $")
    private BigDecimal grossAmountIn;

    @ApiModelProperty(value = "Gross Profit in $")
    private BigDecimal grossProfitIn;

    @ApiModelProperty(value = "Gross Profit %")
    private String grossProfit;

    @ApiModelProperty(value = "Net Profit in $")
    private BigDecimal netProfitIn;

    @ApiModelProperty(value = "Net Profit %")
    private String netProfit;

    /**
     * rrid
     */
    @ApiModelProperty(value = "rrId")
    private Long rrId;


    /**
     * 代理id
     */
    @ApiModelProperty(value = "agentId")
    private Long agentId;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "studentId")
    private Long studentId;


    /**
     * 报考学校id
     */
    @ApiModelProperty(value = "schoolId")
    private Long schoolId;


    /**
     * 报考课程id
     */
    @ApiModelProperty(value = "courseId")
    private Long courseId;

    //    @ApiModelProperty(value = "收款信息")
//    private List<ReceiptFormItemDto> receiptFormItemDtoList;
//
//    @ApiModelProperty(value = "付款信息")
//    private List<PaymentFormItemDto> paymentFormItemDtoList;

}
