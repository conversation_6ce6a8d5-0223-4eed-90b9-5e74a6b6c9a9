package com.get.reportcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @DATE: 2021/3/4
 * @TIME: 15:06
 * @Description:
 **/
@Data
public class AdvSearchRunDto extends BaseVoEntity {
    /**
     * 高级搜索脚本key
     */
    @ApiModelProperty(value = "高级搜索脚本key")
    @NotBlank(message = "queryKey", groups = {Add.class, Update.class})
    private String queryKey;

    /**
     * 脚本运行GUID
     */
    @ApiModelProperty(value = "脚本运行GUID")
    private String queryRunGuid;

    /**
     * 搜索参数条件
     */
    @ApiModelProperty(value = "搜索参数条件")
    @NotBlank(message = "paramCondition", groups = {Add.class, Update.class})
    private String paramCondition;

    /**
     * 搜索参数名称
     */
    @ApiModelProperty(value = "搜索参数名称")
    @NotBlank(message = "paramName", groups = {Add.class, Update.class})
    private String paramName;

    /**
     * 搜索参数数据类型
     */
    @ApiModelProperty(value = "搜索参数数据类型")
    @NotBlank(message = "paramType", groups = {Add.class, Update.class})
    private String paramType;

    /**
     * 搜索参数运算符
     */
    @ApiModelProperty(value = "搜索参数运算符")
    @NotBlank(message = "paramOperation", groups = {Add.class, Update.class})
    private String paramOperation;

    /**
     * 搜索参数数值
     */
    @ApiModelProperty(value = "搜索参数数值")
    @NotBlank(message = "paramValue", groups = {Add.class, Update.class})
    private String paramValue;

    /**
     * 搜索参数数值列表文本字段
     */
    @ApiModelProperty(value = "搜索参数数值列表文本字段")
    private String paramSqlText;

    /**
     * 搜索参数数值列表数值字段
     */
    @ApiModelProperty(value = "搜索参数数值列表数值字段")
    private String paramSqlValue;

    /**
     * 搜索参数数值列表SQL
     */
    @ApiModelProperty(value = "搜索参数数值列表SQL")
    private String paramSql;

}
