package com.get.reportcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Hardy
 * @create: 2022/12/2 1:07
 * @verison: 1.0
 * @description:
 */
@Data
public class CompetitionRegistrationListDto {
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Column(name = "name")
    private Long fkCompetitionItemId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Column(name = "name")
    private String name;

    /**
     * 年级
     */
    @ApiModelProperty(value = "年级")
    @Column(name = "grade")
    private String grade;

    /**
     * 学号
     */
    @ApiModelProperty(value = "学号")
    @Column(name = "student_no")
    private String studentNo;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    @Column(name = "school_name")
    private String schoolName;
}
