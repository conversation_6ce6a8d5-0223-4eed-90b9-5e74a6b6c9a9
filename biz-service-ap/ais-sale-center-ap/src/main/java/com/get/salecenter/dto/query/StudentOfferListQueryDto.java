package com.get.salecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.salecenter.dto.StudentProjectRoleStaffDto;
import com.get.salecenter.vo.StudentProjectRoleStaffVo;
import com.get.salecenter.vo.StudentVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class StudentOfferListQueryDto extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "状态列表")
    private List<Integer> statusList;

    @ApiModelProperty(value = "编号")
    private String num;

    @ApiModelProperty(value = "创建开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createBeginDate;

    @ApiModelProperty(value = "教育机构ID列表")
    private List<Long> fkInstitutionIdsEducation;

    @ApiModelProperty(value = "BD ID列表")
    private List<Long> fkBdIds;

    @ApiModelProperty(value = "教育机构名称")
    private String fkInstitutionNameEducation;

    @ApiModelProperty(value = "教育机构ID列表2")
    private List<Long> fkInstitutionIdsEducation2;

    @ApiModelProperty(value = "旧课程专业等级名称列表")
    private List<String> oldCourseMajorLevelNames;

    @ApiModelProperty(value = "教育机构名称2")
    private String fkInstitutionNameEducations2;

    @ApiModelProperty(value = "代理ID列表")
    private List<Long> fkAgentIds;

    @ApiModelProperty(value = "教育机构集团ID列表")
    private List<Long> fkInstitutionGroupIds;

    @ApiModelProperty(value = "项目代理ID列表")
    private List<Long> itemAgentIds;

    @ApiModelProperty(value = "项目角色ID列表")
    private List<Long> fkProjectRoleIds;

    @ApiModelProperty(value = "创建结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createEndDate;

    @ApiModelProperty(value = "步骤顺序列表")
    private List<Integer> stepOrderList;

    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    @ApiModelProperty(value = "每页大小")
    private Integer pageSize;

    @ApiModelProperty(value = "页码")
    private Integer pageNumber;

    @ApiModelProperty(value = "学生所在国家ID")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "目标国家ID列表")
    private List<Long> targetCountryIdList;

    @ApiModelProperty(value = "教育机构ID列表")
    private List<Long> institutionIds;

    @ApiModelProperty(value = "学校名称")
    private String schoolName;

    @ApiModelProperty(value = "渠道名称列表")
    private List<String> channelNames;

    @ApiModelProperty(value = "课程ID列表")
    private List<Long> courseIds;

    @ApiModelProperty(value = "课程名称")
    private String courseName;

    @ApiModelProperty(value = "公司ID")
    private Long fkCompanyId;

    @ApiModelProperty(value = "学生编号")
    private String studentNum;

    @ApiModelProperty(value = "学生国籍所在国家ID")
    private Long studentNationalityCountryId;

    @ApiModelProperty(value = "电话")
    private String tel;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "开学时间开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTimeStart;

    @ApiModelProperty(value = "开学时间结束")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTimeEnd;

    @ApiModelProperty(value = "项目创建开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date itemCreateBeginDate;

    @ApiModelProperty(value = "项目创建结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date itemCreateEndDate;

    @ApiModelProperty(value = "是否延期入学")
    private Boolean isDeferEntrance;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "BD名称")
    private String bdName;

    @ApiModelProperty(value = "代理所在国家ID")
    private Long fkAreaCountryIdAgent;

    @ApiModelProperty(value = "代理所在州ID列表")
    private List<Long> fkAreaStateIdAgentList;

    @ApiModelProperty(value = "失败原因ID")
    private Long failureReasonId;

    @ApiModelProperty(value = "步骤变更开始日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepChangeBeginDate;

    @ApiModelProperty(value = "步骤变更结束日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepChangeEndDate;

    @ApiModelProperty(value = "工作流状态")
    private Integer statusWorkflow;

    @ApiModelProperty(value = "课程等级ID")
    private Long majorLevelId;

    @ApiModelProperty(value = "集团名称")
    private String groupName;

    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "变更步骤ID列表")
    private List<Long> changeStepId;

    @ApiModelProperty(value = "地区ID")
    private Long fkAreaRegionId;

    @ApiModelProperty(value = "报名失败原因ID列表")
    private List<Long> fkEnrolFailureReasonIds;

    @ApiModelProperty(value = "旧课程专业等级名称")
    private String oldCourseMajorLevelName;

    @ApiModelProperty(value = "新申请状态")
    private Integer newAppStatus;

    @ApiModelProperty(value = "角色ID列表")
    private Set<Long> fkRoleIds;

    @ApiModelProperty(value = "课程类型组ID列表")
    private List<Long> courseTypeGroupIds;

    @ApiModelProperty(value = "旧课程类型组名称列表")
    private List<String> oldCourseTypeGroupNames;

    @ApiModelProperty(value = "教育机构提供者ID")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "课程等级ID列表")
    private List<Long> majorLevelIds;

    //========================
    @ApiModelProperty(value = "是否批量修改项目成员")
    private Boolean isProjectUpate;

    /**
     * 学生Id
     */
    //@NotNull(message = "学生Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "学生Id", required = true)
    private Long fkStudentId;

    //=========path=========


    @ApiModelProperty(value = "偏移量")
    private Integer offset;

    /**
     * 代理Id（业绩绑定）
     */
    @NotNull(message = "代理Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "代理Id（业绩绑定）", required = true)
    private Long fkAgentId;

    /**
     * 员工Id（业绩绑定，BD）
     */
    @NotNull(message = "员工Id不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）", required = true)
    private Long fkStaffId;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @NotNull(message = "备注不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    private String remark;

    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    private Integer status;

    /**
     * 1查询全部，0查询个人,2 查询我的审批
     */
    @ApiModelProperty(value = "1查询全部，0查询个人,2 查询我的审批")
    private String selectStatus;


    /**
     * 发起人id
     */
    @ApiModelProperty(value = "发起人id")
    private Long fkStaffIdWorkflow;


    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;

    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;

    /**
     * 发起人ids
     */
    @ApiModelProperty(value = "发起人ids")
    private String fkStaffIdWorkflowIds;

    /**
     * 学生ids
     */
    @ApiModelProperty(value = "学生ids")
    private String studentIds;

    /**
     * 可查看所有学生的ids
     */
    @ApiModelProperty(value = "可查看所有学生的ids")
    private String allStudentIds;

    /**
     * ids
     */
    @ApiModelProperty(value = "ids")
    private String ids;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String courseFullName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionFullName;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id")
    private Long fkInstitutionCourseId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 学生查询条件
     */
    @ApiModelProperty(value = "学生查询条件")
    private StudentVo studentVo;

    /**
     * 学生国籍所在国家Id
     */
    @ApiModelProperty(value = "学生国籍所在国家Id")
    private Long fkAreaCountryIdNationality;


    @ApiModelProperty("bd编号")
    private String bdCode;

    @ApiModelProperty(value = "是否全局匹配课程名称 枚举：0百度式搜索/1全局匹配")
    private Boolean isCourseGlobalMatching = false;


    @ApiModelProperty("项目成员名称")
    private String projectRoleStaffName;


    @ApiModelProperty("批量分配申请方案项目成员条件")
    private StudentProjectRoleStaffDto studentProjectRoleStaff;

    /**
     * 集团名称（多选）
     */
    @ApiModelProperty(value = "集团名称（多选）")
    private List<String> institutionGroupNames;









}
