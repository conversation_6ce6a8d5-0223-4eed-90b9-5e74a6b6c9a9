package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.salecenter.dto.query.AgentQueryDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @author: Sea
 * @create: 2020/10/20 14:56
 * @verison: 1.0
 * @description:
 */
@Data
public class AgentStaffDto extends BaseVoEntity{

    @ApiModelProperty(value = "搜索条件")
    private AgentQueryDto agentVo;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    private Long fkAgentId;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 绑定时间
     */
    @ApiModelProperty(value = "绑定时间")
    private Date activeDate;

    /**
     * 取消绑定时间（下次绑定时，需要重新建立记录）
     */
    @ApiModelProperty(value = "取消绑定时间（下次绑定时，需要重新建立记录）")
    private Date unactiveDate;
}
