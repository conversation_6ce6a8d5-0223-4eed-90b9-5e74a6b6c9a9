package com.get.salecenter.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.FastjsonTypeHandler;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.IncentivePolicy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
public class IncentivePolicyVo extends BaseEntity {
    @ApiModelProperty("公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "公开对象名称")
    private String publicLevelName;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "学校供应商名称")
    private String institutionProviderName;

    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty(value = "已标记结算")
    private Integer commissionNum;

    @ApiModelProperty(value = "附件集合")
    List<MediaAndAttachedVo> mediaAndAttachedDtos;

    //=========实体类IncentivePolicy==============
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "国家Id")
    @UpdateWithNull
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "学校提供商Id")
    @UpdateWithNull
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "学校Ids，逗号隔开：1,2,3")
    private String fkInstitutionIds;

    @ApiModelProperty(value = "奖励政策编号，编号规则：系统生成，ICP+6位ID数字，例：ICP000001")
    private String num;

    @ApiModelProperty(value = "年度")
    private Integer year;

    @ApiModelProperty(value = "活动时间说明")
    private String activityTimeNote;

    @ApiModelProperty(value = "提交申请时间说明")
    private String submittedTimeNote;

    @ApiModelProperty(value = "押金缴费时间说明")
    private String depositPaidTimeNote;

    @ApiModelProperty(value = "学费缴费时间说明")
    private String tuitionTimeNote;

    @ApiModelProperty(value = "入学时间说明")
    private String enrolledTimeNote;

    @ApiModelProperty(value = "适用课程说明")
    private String conditionNote;

    @ApiModelProperty(value = "奖励内容说明")
    private String rewardNote;

    @ApiModelProperty(value = "适用课程json")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private IncentivePolicyConditionVo conditionJson;

    @ApiModelProperty(value = "奖励内容json")
    @TableField(typeHandler = FastjsonTypeHandler.class)
    private IncentivePolicyRewardVo rewardJson;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
    private String publicLevel;

    @ApiModelProperty(value = "状态：0作废/1进行中/2已结算")
    private Integer status;

}
