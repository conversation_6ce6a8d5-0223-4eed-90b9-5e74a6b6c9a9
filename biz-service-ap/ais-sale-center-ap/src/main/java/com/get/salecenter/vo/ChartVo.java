package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/10/29 16:06
 * @verison: 1.0
 * @description:
 */
@Data
public class ChartVo {

    List<TableChartVo> tableChartDtos;

    /**
     * 共多少桌
     */
    @ApiModelProperty(value = "共多少桌")
    private Integer tableCount;

    /**
     * 可坐多少人
     */
    @ApiModelProperty(value = "可坐多少人")
    private Integer personCount;

    /**
     * 已经安排多少人
     */
    @ApiModelProperty(value = "已经安排多少人")
    private Integer arrangedPersonCount;

    /**
     * 还剩多少人
     */
    @ApiModelProperty(value = "还剩多少人")
    private Integer notArrangedPersonCount;
}
