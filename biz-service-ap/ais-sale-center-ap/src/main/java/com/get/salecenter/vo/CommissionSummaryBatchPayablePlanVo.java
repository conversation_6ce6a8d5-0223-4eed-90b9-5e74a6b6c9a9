package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.PayablePlan;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/5/8 12:24
 */
@Data
public class CommissionSummaryBatchPayablePlanVo extends BaseEntity {

    @ApiModelProperty(value = "学生代理合同账户Id")
    private Long fkAgentContractAccountId;

    @ApiModelProperty(value = "币种编号（代理账户）")
    private String agentCurrencyTypeNum;

    //==============实体类=================
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 应付类型关键字，枚举，如：m_student_offer_item
     */
    @ApiModelProperty(value = "应付类型关键字，枚举，如：m_student_offer_item")
    @Column(name = "fk_type_key")
    private String fkTypeKey;

    /**
     * 应付类型对应记录Id，如：m_student_offer_item.id
     */
    @ApiModelProperty(value = "应付类型对应记录Id，如：m_student_offer_item.id")
    @Column(name = "fk_type_target_id")
    private Long fkTypeTargetId;

    /**
     * 应收计划Id
     */
    @ApiModelProperty(value = "应收计划Id")
    @Column(name = "fk_receivable_plan_id")
    private Long fkReceivablePlanId;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    @Column(name = "summary")
    private String summary;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    @Column(name = "tuition_amount")
    private BigDecimal tuitionAmount;

    /**
     * 费率%(代理)
     */
    @ApiModelProperty(value = "费率%(代理)")
    @Column(name = "commission_rate")
    private BigDecimal commissionRate;

    /**
     * 代理分成比率%
     */
    @ApiModelProperty(value = "代理分成比率%")
    @Column(name = "split_rate")
    private BigDecimal splitRate;

    /**
     * 佣金金额(代理)
     */
    @ApiModelProperty(value = "佣金金额(代理)")
    @Column(name = "commission_amount")
    private BigDecimal commissionAmount;

    /**
     * 定额金额
     */
    @ApiModelProperty(value = "定额金额")
    @Column(name = "fixed_amount")
    private BigDecimal fixedAmount;

    /**
     * 其他金额
     */
    @ApiModelProperty(value = "其他金额")
    @Column(name = "bonus_amount")
    private BigDecimal bonusAmount;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    @Column(name = "payable_amount")
    private BigDecimal payableAmount;

    /**
     * 计划付款时间
     */
    @ApiModelProperty(value = "计划付款时间")
    @Column(name = "payable_plan_date")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date payablePlanDate;

    /**
     * 是否预付，0否/1是
     */
    @ApiModelProperty(value = "是否预付，0否/1是")
    @Column(name = "is_pay_in_advance")
    private Boolean isPayInAdvance;

    @ApiModelProperty(value = "预付百分比：50, 100")
    @UpdateWithNull
    private Integer payInAdvancePercent;

    /**
     * 状态：0关闭/1打开/2完成
     */
    @ApiModelProperty(value = "状态：0关闭/1打开/2完成")
    @Column(name = "status")
    private Integer status;

    /**
     * 结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总
     */
    @ApiModelProperty(value = "结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
    @Column(name = "status_settlement")
    private Integer statusSettlement;

    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    @Column(name = "id_gea_finance")
    private String idGeaFinance;

    private static final long serialVersionUID = 1L;

}
