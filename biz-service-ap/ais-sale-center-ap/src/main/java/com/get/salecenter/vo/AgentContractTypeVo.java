package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AgentContractType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 14:51
 * @Description:
 **/
@Data
public class AgentContractTypeVo extends BaseEntity implements Serializable {

    //========实体类AgentContractType==========
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 类型key
     */
    @ApiModelProperty(value = "类型key")
    @Column(name = "type_key")
    private String typeKey;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
