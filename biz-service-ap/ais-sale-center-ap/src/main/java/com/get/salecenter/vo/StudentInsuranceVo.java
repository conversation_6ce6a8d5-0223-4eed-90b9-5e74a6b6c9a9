package com.get.salecenter.vo;

import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/1/10
 * @TIME: 12:16
 * @Description:
 **/
@Data
public class StudentInsuranceVo extends BaseEntity {
    /**
     * 绑定的项目成员
     */
    List<StudentProjectRoleStaffVo> studentProjectRoleStaffDtos;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;
    /**
     * 性别：0女/1男
     */
    private Integer gender;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别")
    private String genderName;
    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    private Long fkCompanyId;
    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;
    /**
     * 姓（英/拼音）
     */
    @ApiModelProperty(value = "姓（英/拼音）")
    private String lastName;
    /**
     * 名（英/拼音）
     */
    @ApiModelProperty(value = "名（英/拼音）")
    private String firstName;
    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    private String englishName;
    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;
    /**
     * 护照编号（保险业务必填）
     */
    @ApiModelProperty(value = "护照编号（保险业务必填）")
    private String passportNum;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;
    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private String staffName;
    /**
     * 国家Name
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;
    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String channelName;
    /**
     * 收取佣金金额
     */
    @ApiModelProperty(value = "收取佣金金额")
    private BigDecimal amountReceivable;
    /**
     * 支付佣金金额（代理）
     */
    @ApiModelProperty(value = "支付佣金金额（代理）")
    private BigDecimal amountPayable;
    /**
     * 保险费币种
     */
    @ApiModelProperty(value = "保险费币种名称")
    private String fkCurrencyTypeNumInsuranceName;


    /**
     * 佣金币种
     */
    @ApiModelProperty(value = "佣金币种名称")
    private String fkCurrencyTypeNumCommissionName;

    /**
     * 应收应付IDS
     */
    @ApiModelProperty(value = "应收应付IDS")
    private List<Long> aRAPIds;

    /**
     * 状态：0关闭/1打开
     */
    private String statusName;
    @ApiModelProperty(value = "绑定的项目成员")
    private String projectRoleName;

    @ApiModelProperty(value = "币种")
    private String payableCurrencyTypeName;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal actualPayableAmount;

    @ApiModelProperty(value = "差额")
    private BigDecimal diffPayableAmount;

    /**
     * 付款情况信息显示
     */
    @ApiModelProperty(value = "付款情况信息显示（币种=payableCurrencyTypeName，实付金额=actualPayableAmount，付款时间=gmtCreate）")
    private JSONArray payDetailList;
    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态")
    private Integer arStatus;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态")
    private Integer apStatus;

    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态名称")
    private String arStatusName;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态名称")
    private String apStatusName;

    /**
     * 结算状态
     */
    @ApiModelProperty(value = "结算状态")
    private Integer settlementStatus;
    /**
     * 结算状态
     */
    @ApiModelProperty(value = "结算状态名称")
    private String settlementStatusName;

    @ApiModelProperty(value = "是否是无需结算状态,true表示无需结算")
    private Boolean isNosettlement=false;

    @ApiModelProperty(value = "应收币种")
    private String receivableCurrencyTypeName;

    @ApiModelProperty(value = "服务提供商名称")
    private String fkBusinessProviderName;

    @ApiModelProperty(value = "服务提供商/产品")
    private String businessProviderAndProductName;

    @ApiModelProperty(value = "支付方式名称")
    private String paymentMethodName;

    //============实体类StudentInsurance================
    private static final long serialVersionUID = 1L;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * 代理Id（业绩绑定）
     */
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 业务渠道Id/佣金合同方Id
     */
    @ApiModelProperty(value = "业务渠道Id/佣金合同方Id")
    @Column(name = "fk_business_channel_id")
    private Long fkBusinessChannelId;
    /**
     * 业务提供商Id
     */
    @ApiModelProperty(value = "业务提供商Id")
    @Column(name = "fk_business_provider_id")
    private Long fkBusinessProviderId;
    /**
     * 购买产品
     */
    @ApiModelProperty(value = "购买产品")
    @Column(name = "business_provider_product")
    private String businessProviderProduct;
    /**
     * 员工Id（业绩绑定，BD）
     */
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    @ApiModelProperty(value = "分类，枚举：0学生保险/1陪读人保险")
    @Column(name = "type")
    private Integer type;
    /**
     * 留学保险编号
     */
    @ApiModelProperty(value = "留学保险编号")
    @Column(name = "num")
    private String num;



    @ApiModelProperty(value = "受保人姓名（中）")
    @Column(name = "insurant_name")
    private String insurantName;

    @ApiModelProperty(value = "受保人姓（英/拼音）")
    @Column(name = "insurant_last_name")
    private String insurantLastName;


    @ApiModelProperty(value = "受保人名（英/拼音）")
    @Column(name = "insurant_first_name")
    private String insurantFirstName;

    @ApiModelProperty(value = "受保人性别")
    @Column(name = "insurant_gender")
    private Integer insurantGender;

    @ApiModelProperty(value = "受保人生日")
    @Column(name = "insurant_birthday")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insurantBirthday;

    @ApiModelProperty(value = "受保人护照编号")
    @Column(name = "insurant_passport_num")
    private String insurantPassportNum;

    /**
     * 前往国家Id
     */
    @ApiModelProperty(value = "前往国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 保险单号
     */
    @ApiModelProperty(value = "保险单号")
    @Column(name = "insurance_num")
    private String insuranceNum;
    /**
     * 保单开始时间
     */
    @ApiModelProperty(value = "保单开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "insurance_start_time")
    private Date insuranceStartTime;
    /**
     * 保单结束时间
     */
    @ApiModelProperty(value = "保单结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "insurance_end_time")
    private Date insuranceEndTime;
    /**
     * 保险购买时间
     */
    @ApiModelProperty(value = "保险购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "insurance_buy_time")
    private Date insuranceBuyTime;
    /**
     * 购买账户
     */
    @ApiModelProperty(value = "购买账户")
    @Column(name = "buy_account")
    private String buyAccount;
    /**
     * 保险费币种
     */
    @ApiModelProperty(value = "保险费币种")
    @Column(name = "fk_currency_type_num_insurance")
    private String fkCurrencyTypeNumInsurance;
    /**
     * 保险金额
     */
    @ApiModelProperty(value = "保险金额")
    @Column(name = "insurance_amount")
    private BigDecimal insuranceAmount;
    /**
     * 保险金额说明
     */
    @ApiModelProperty(value = "保险金额说明")
    @Column(name = "insurance_amount_note")
    private String insuranceAmountNote;
    /**
     * 支付方式枚举：0飞汇/1易思汇/4信用卡
     */
    @ApiModelProperty(value = "支付方式枚举：0飞汇/1易思汇/4信用卡")
    @Column(name = "payment_method")
    private Integer paymentMethod;
    /**
     * 佣金币种
     */
    @ApiModelProperty(value = "佣金币种")
    @Column(name = "fk_currency_type_num_commission")
    private String fkCurrencyTypeNumCommission;
    /**
     * 收取佣金比例%
     */
    @ApiModelProperty(value = "收取佣金比例%")
    @Column(name = "commission_rate_receivable")
    private BigDecimal commissionRateReceivable;
    /**
     * 支付佣金比例%（代理）
     */
    @ApiModelProperty(value = "支付佣金比例%（代理）")
    @Column(name = "commission_rate_payable")
    private BigDecimal commissionRatePayable;
    /**
     * 固定收取佣金金额
     */
    @ApiModelProperty(value = "固定收取佣金金额")
    @Column(name = "fixed_amount_receivable")
    private BigDecimal fixedAmountReceivable;
    /**
     * 固定支付佣金金额（代理）
     */
    @ApiModelProperty(value = "固定支付佣金金额（代理）")
    @Column(name = "fixed_amount_payable")
    private BigDecimal fixedAmountPayable;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 备注
     */
    @ApiModelProperty(value = "是否财务专用：0否/1是")
    @Column(name = "is_hidden")
    private Boolean isHidden;
    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    @Column(name = "status")
    private Integer status;


    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentLabelVos;
}
