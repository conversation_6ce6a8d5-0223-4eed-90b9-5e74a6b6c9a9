package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.SaleContactPersonCompany;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 16:13
 * @Description: 代理联系人-公司安全配置DTO
 **/
@Data
public class ContactPersonCompanyVo extends BaseEntity {

    //============实体类SaleContactPersonCompany================
    private static final long serialVersionUID = 1L;
    /**
     * 联系人Id
     */
    @ApiModelProperty(value = "联系人Id")
    @Column(name = "fk_contact_person_id")
    private Long fkContactPersonId;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

}
