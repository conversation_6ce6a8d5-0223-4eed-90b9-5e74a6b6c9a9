package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AgentContractFormula;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/1/6 12:37
 * @verison: 1.0
 * @description:
 */
@Data
public class AgentContractFormulaVo extends BaseEntity implements Serializable {
    /**
     * 国家区域id
     */
    @ApiModelProperty(value = "国家区域id")
    private List<Long> countryIdList;

    /**
     * 国家区域name
     */
    @ApiModelProperty(value = "国家区域name")
    private String countryName;

    /**
     * 课程类型id
     */
    @ApiModelProperty(value = "课程类型id")
    private List<Long> courseTypeIdList;

    /**
     * 课程类型name
     */
    @ApiModelProperty(value = "课程类型name")
    private String courseTypeName;

    /**
     * 课程等级id
     */
    @ApiModelProperty(value = "课程等级id")
    private List<Long> majorLevelIdList;

    /**
     * 课程等级name
     */
    @ApiModelProperty(value = "课程等级name")
    private String majorLevelName;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private List<Long> companyIdList;

    /**
     * 公司name
     */
    @ApiModelProperty(value = "公司name")
    private String companyName;

    /**
     * 学校提供商名称
     */
    @ApiModelProperty(value = "学校提供商名称")
    private String institutionProviderName;

    /**
     * 合同公式
     */
    @ApiModelProperty(value = "合同公式")
    private String contractFormula;

    /**
     * 学校id
     */
    @ApiModelProperty(value = "学校id")
    private List<Long> institutionIdList;

    /**
     * 学校name
     */
    @ApiModelProperty(value = "学校name")
    private String institutionName;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id")
    private List<Long> courseIdList;

    /**
     * 课程name
     */
    @ApiModelProperty(value = "课程name")
    private String courseName;

    /**
     * 学生代理合同公式佣金配置
     */
    @ApiModelProperty(value = "学生代理合同公式佣金配置")
    private List<AgentContractFormulaCommissionVo> agentContractFormulaCommissionDtos;

    //==============实体类AgentContractFormula====================
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;
    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    @Column(name = "fk_contract_formula_id")
    private Long fkContractFormulaId;
    /**
     * 生效开始时间
     */
    @ApiModelProperty(value = "生效开始时间")
    @Column(name = "start_time")
    private Date startTime;
    /**
     * 生效结束时间
     */
    @ApiModelProperty(value = "生效结束时间")
    @Column(name = "end_time")
    private Date endTime;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 代理佣金上限(总)
     */
    @ApiModelProperty(value = "代理佣金上限(总)")
    @Column(name = "limit_amount_ag")
    private BigDecimal limitAmountAg;
    /**
     * 公式备注
     */
    @ApiModelProperty(value = "公式备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 排序（倒序），数字由大到小排列
     */
    @ApiModelProperty(value = "排序（倒序），数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;


}
