package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ClientListInfoDto {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "学生推荐来源")
    private String studentSource;

    @ApiModelProperty(value = "推荐来源代理名称")
    private String sourceAgentName;

    @ApiModelProperty(value = "推荐来源跟进bd名称")
    private String sourceBdName;

    @ApiModelProperty(value = "推荐来源本地市场来源名称")
    private String sourceBusinessProviderName;

    @ApiModelProperty(value = "中文姓名")
    private String name;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "出生日期")
    private Date birthday;

    @ApiModelProperty(value = "英文姓")
    private String firstName;

    @ApiModelProperty(value = "英文名")
    private String lastName;

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "客户星级")
    private Integer starLevel;

    @ApiModelProperty(value = "预计签约时间")
    private String expectSigningTime;

    @ApiModelProperty(value = "步骤状态")
    private String fkClientOfferStepName;

    @ApiModelProperty(value = "在读/毕业学校")
    private String institutionName;

    @ApiModelProperty(value = "在读/毕业专业")
    private String majorName;

    @ApiModelProperty(value = "入读时间")
    private String startTimeEducation;

    @ApiModelProperty(value = "毕业时间")
    private String endTimeEducation;

    @ApiModelProperty(value = "是否入境")
    private String isEnterCountryName;

//    @ApiModelProperty(value = "跟进状态名称")
//    private String followUpStatus;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "微信")
    private String wechat;

    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    @ApiModelProperty(value = "BD")
    private String bdName;

    @ApiModelProperty(value = "项目成员名称")
    private String projectRoleName;

    @ApiModelProperty(value = "申请国家")
    private String offerCountryName;

    @ApiModelProperty(value = "申请id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "咨询备注")
    private String remark;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "最近回访时间")
    private Date lastVisitTime;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "预约回访时间")
    private Date followUpTime;

    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    @ApiModelProperty(value = "部门名")
    private String departmentName;

    @ApiModelProperty(value = "是否Bms学生")
    private Boolean isBmsStudent;

    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    @ApiModelProperty(value = "推荐来源类型")
    private String fkTableName;

    @ApiModelProperty(value = "推荐来源类型名称")
    private String fkTableNameValue;

    @ApiModelProperty(value = "客户编号")
    private String num;

    @ApiModelProperty(value = "最新事件")
    private String latestEvent;

    @ApiModelProperty(value = "是否激活 ")
    private Boolean isActive;

    @ApiModelProperty(value = "学生负责人名称")
    private String fkStudentStaffNames;


}
