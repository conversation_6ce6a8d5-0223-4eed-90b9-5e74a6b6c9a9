package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.salecenter.dto.ApplicationStatisticsDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/3/29
 * @TIME: 19:54
 * @Description:
 **/
@Data
public class StatisticsVo {
    /**
     * 国家ID
     */
    @ApiModelProperty("国家ID")
    private Long fkAreaCountryId;
    /**
     * 其他国家ID列表
     */
    @ApiModelProperty("其他国家ID列表")
    private List<Long> fkAreaCountryIdList;
    /**
     * 小计国家ID列表
     */
    @ApiModelProperty("小计国家ID列表")
    private List<Long> fkAreaCountryIds;
    /**
     * 国家名称
     */
    @ApiModelProperty("国家名称")
    private String fkAreaCountryName;
    /**
     * 开始日期1
     */
    @ApiModelProperty("日期1")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dateStartOne;
    /**
     * 结束日期1
     */
    @ApiModelProperty("日期1")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dateEndOne;

    /**
     * 开始日期2
     */
    @ApiModelProperty("日期2")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dateStartTwo;

    /**
     * 结束日期2
     */
    @ApiModelProperty("日期2")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dateEndTwo;
    /**
     * 年累计开始时间
     */
    @ApiModelProperty("年累计开始时间1")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date cumulativeDateStartOne;
    /**
     * 年累计结束时间
     */
    @ApiModelProperty("年累计结束时间1")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date cumulativeDateEndOne;
    /**
     * 年累计开始时间2
     */
    @ApiModelProperty("年累计开始时间2")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date cumulativeDateStartTwo;
    /**
     * 年累计结束时间2
     */
    @ApiModelProperty("年累计结束时间2")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date cumulativeDateEndTwo;

    /**
     * 周统计开始时间
     */
    @ApiModelProperty(value = "周统计开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date weekDateStart;

    /**
     * 周统计结束时间
     */
    @ApiModelProperty(value = "周统计结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8" )
    private Date weekDateEnd;
    /**
     * 变化类型：升降
     */
    @ApiModelProperty("变化类型：升降")
    private String sort;
    /**
     * 进行统计的相关列表
     */
    @ApiModelProperty("进行统计的相关列表")
    private List<ApplicationStatisticsDto> applicationStatisticsDtoList;
}
