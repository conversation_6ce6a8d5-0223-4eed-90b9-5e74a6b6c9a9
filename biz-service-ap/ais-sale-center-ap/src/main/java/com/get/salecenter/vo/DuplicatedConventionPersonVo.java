package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Hardy
 * @create: 2023/5/24 16:08
 * @verison: 1.0
 * @description:
 */
@Data
public class DuplicatedConventionPersonVo {



    @ApiModelProperty(value = "峰会Id")
    private Long fkConventionId;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty("全名")
    private String fullName;

    /*基本信息*/

    /**
     * 参加人姓名
     */
    @ApiModelProperty(value = "参加人姓名")
    @Column(name = "name")
    private String name;
    /**
     * 参加人姓名（中文）
     */
    @ApiModelProperty(value = "参加人姓名（中文）")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;
    /**
     * 参加人职位
     */
    @ApiModelProperty(value = "参加人职位")
    @Column(name = "title")
    private String title;
    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    @Column(name = "company")
    private String company;
    /**
     * 参加人电邮
     */
    @ApiModelProperty(value = "参加人电邮")
    @Column(name = "email")
    private String email;
    /**
     * 参加人电话
     */
    @ApiModelProperty(value = "参加人电话")
    @Column(name = "tel")
    private String tel;
    /**
     * 护照号
     */
    @ApiModelProperty(value = "护照号")
    @Column(name = "passport_num")
    private String passportNum;
    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    @Column(name = "id_card_num")
    private String idCardNum;

    /**
     * 是否VIP：0否/1是
     */
    @ApiModelProperty(value = "是否VIP：0否/1是")
    @Column(name = "is_vip")
    private Boolean isVip;

}
