package com.get.salecenter.vo;



import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SettlementCommissionNoticeVo extends BaseEntity {


    @ApiModelProperty("附件")
    private List<MediaAndAttachedVo> mediaAndAttachedDtos;

    //=========实体类SettlementCommissionNotice=================
    @ApiModelProperty("批次号")
    private String numSettlementBatch;

    @ApiModelProperty("应付id")
    private Long fkPayablePlanId;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("佣金通知")
    private String commissionNotice;
}
