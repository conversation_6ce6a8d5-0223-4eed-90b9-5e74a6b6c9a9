package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2021/2/1
 * @TIME: 18:42
 * @Description:
 **/
@Data
public class WorldHistogramVo {
    private String countryName;
    private String countryNameChn;
    private String regionName;
    private String regionNameChn;
    @ApiModelProperty(value = "学习计划数")
    private Integer studentItemNum;
//    @ApiModelProperty(value = "学习计划数")
//    private Integer studentItemNum;
//    @ApiModelProperty(value = "学生数")
//    private Integer studentNum;

    @ApiModelProperty(value = "数量")
    private Integer num;
    private Integer agentItemNum;
}
