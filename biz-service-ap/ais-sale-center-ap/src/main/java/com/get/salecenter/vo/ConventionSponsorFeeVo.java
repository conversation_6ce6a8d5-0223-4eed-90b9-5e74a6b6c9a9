package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionSponsorFee;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * @author: Sea
 * @create: 2021/5/8 11:08
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionSponsorFeeVo extends BaseEntity {
    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    /**
     * 是否剩余 false未售完/true售完
     */
    @ApiModelProperty(value = "是否剩余 false未售完/true售完")
    private Boolean soldOut;

    /**
     * 是否绑定 false未绑定/true绑定
     */
    @ApiModelProperty(value = "是否与峰会赞助商存在绑定关系")
    private Boolean isBinding = false;

    @ApiModelProperty(value = "备注")
    private String remark;

    //===============实体类ConventionSponsorFee================
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;
    /**
     * 费用名称
     */
    @ApiModelProperty(value = "费用名称")
    @Column(name = "title")
    private String title;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 费用金额
     */
    @ApiModelProperty(value = "费用金额")
    @Column(name = "fee")
    private BigDecimal fee;
    /**
     * 日期描述
     */
    @ApiModelProperty(value = "日期描述")
    @Column(name = "date_note")
    private String dateNote;
    /**
     * 时间描述
     */
    @ApiModelProperty(value = "时间描述")
    @Column(name = "time_note")
    private String timeNote;
    /**
     * 图片路径
     */
    @ApiModelProperty(value = "图片路径")
    @Column(name = "img_url")
    private String imgUrl;
    /**
     * 费用摘要
     */
    @ApiModelProperty(value = "费用摘要")
    @Column(name = "summary")
    private String summary;
    /**
     * 数量限制，不填或<=0为不限制
     */
    @ApiModelProperty(value = "数量限制，不填或<=0为不限制")
    @Column(name = "count_limit")
    private Integer countLimit;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    /**
     * 费用金额（折合人民币）
     */
    @ApiModelProperty(value = "费用金额（折合人民币）")
    @Column(name = "fee_cny")
    private BigDecimal feeCny;

}
