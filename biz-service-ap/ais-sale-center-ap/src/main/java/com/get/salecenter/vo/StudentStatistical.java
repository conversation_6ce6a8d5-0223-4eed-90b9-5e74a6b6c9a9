package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 学生数统计类
 */
@Data
public class StudentStatistical {
    @ApiModelProperty(value = "目标名称")
    private String targetName;

    @ApiModelProperty(value = "目标id")
    private Long targetId;

    @ApiModelProperty(value = "员工id")
    private Long fkStaffId;

    @ApiModelProperty(value = "业务国家名称")
    private String areaCountryName;

    @ApiModelProperty(value = "业务国家id")
    private String fkAreaCountryId;

    @ApiModelProperty(value = "学生数（首次）")
    private Integer totalStudentNum;

    @ApiModelProperty(value = "旧学生数")
    private Integer oldTotalStudentNum;

    @ApiModelProperty(value = "新学生数")
    private Integer newTotalStudentNum;

    @ApiModelProperty(value = "旧学生数（首次）")
    private Integer oldFirstStudentNum;

    @ApiModelProperty(value = "新学生数（首次）")
    private Integer newFirstStudentNum;

    @ApiModelProperty(value = "缺资料数")
    private Integer appOptStudentNum;

    @ApiModelProperty(value = "提交数")
    private Integer itemNum;

    @ApiModelProperty(value = "延迟提交数")
    private Integer delayItemNum;

    @ApiModelProperty(value = "申请反馈数")
    private Integer admittedNum;

    @ApiModelProperty(value = "延迟申请反馈数")
    private Integer delayAdmittedNum;

    @ApiModelProperty(value = "os数")
    private Integer osNum;

    @ApiModelProperty(value = "os重复学生数")
    private Integer repeatOsStudentNum;

    @ApiModelProperty(value = "签证数")
    private Integer visaNum;

    @ApiModelProperty(value = "入学数")
    private Integer enrolledNum;

    @ApiModelProperty(value = "后补签证数")
    private Integer enrolledVisaNum;

    @ApiModelProperty(value = "已结算提交数")
    private Integer commissionActionItemNum;

    @ApiModelProperty(value = "已结算延迟提交数")
    private Integer commissionActionDelayItemNum;

    @ApiModelProperty(value = "已结算申请反馈数")
    private Integer commissionActionAdmittedNum;

    @ApiModelProperty(value = "已结算申请反馈数")
    private Integer commissionActionAdmittedDelayNum;

    @ApiModelProperty(value = "已结算os数")
    private Integer commissionActionOsNum;

    @ApiModelProperty(value = "已结算签证数")
    private Integer commissionActionVisaNum;

    @ApiModelProperty(value = "已结算入学数")
    private Integer commissionActionEnrolledNum;

    @ApiModelProperty(value = "已结算后补签证数")
    private Integer commissionActionEnrolledVisaNum;

    @ApiModelProperty(value = "未结算提交数")
    private Integer unsettledCommissionActionItemNum;

    @ApiModelProperty(value = "未结算延迟提交数")
    private Integer unsettledCommissionActionDelayItemNum;

    @ApiModelProperty(value = "未结算申请反馈数")
    private Integer unsettledCommissionActionAdmittedNum;

    @ApiModelProperty(value = "未结算延迟申请反馈数")
    private Integer unsettledCommissionActionAdmittedDelayNum;

    @ApiModelProperty(value = "未结算os数")
    private Integer unsettledCommissionActionOsNum;

    @ApiModelProperty(value = "未结算签证数")
    private Integer unsettledCommissionActionVisaNum;

    @ApiModelProperty(value = "未结算入学数")
    private Integer unsettledCommissionActionEnrolledNum;

    @ApiModelProperty(value = "未结算后补签证数")
    private Integer unsettledCommissionActionEnrolledVisaNum;

    @ApiModelProperty(value = "结算提交数str")
    private String commissionActionItemNumStr;

    @ApiModelProperty(value = "结算延迟提交数str")
    private String commissionActionDelayItemNumStr;

    @ApiModelProperty(value = "结算申请反馈数str")
    private String commissionActionAdmittedNumStr;

    @ApiModelProperty(value = "结算延迟申请反馈数str")
    private String commissionActionAdmittedDelayNumStr;

    @ApiModelProperty(value = "结算os数str")
    private String commissionActionOsNumStr;

    @ApiModelProperty(value = "结算签证数str")
    private String commissionActionVisaNumStr;

    @ApiModelProperty(value = "结算入学数str")
    private String commissionActionEnrolledNumStr;

    @ApiModelProperty(value = "结算后补签证数str")
    private String commissionActionEnrolledVisaNumStr;

    @ApiModelProperty(value = "结算金额")
    private String commissionActionAmount;

    @ApiModelProperty(value = "未结算申请金额")
    private BigDecimal unsettledCommissionActionItemAmount;

    @ApiModelProperty(value = "未结算os金额")
    private BigDecimal unsettledCommissionActionOsAmount;

    @ApiModelProperty(value = "未结算签证金额")
    private BigDecimal unsettledCommissionActionVisaAmount;

    @ApiModelProperty(value = "未结算入学金额")
    private BigDecimal unsettledCommissionActionEnrolledAmount;

    @ApiModelProperty(value = "未结算补签证金额")
    private BigDecimal unsettledCommissionActionEnrolledVisaAmount;

    @ApiModelProperty(value = "bd编号 + bd名")
    private String bdName;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty(value = "代理昵称")
    private String nickName;

    @ApiModelProperty(value = "所在区域")
    private String region;

    @ApiModelProperty(value = "学生项目角色Id")
    private Long fkStudentProjectRoleId;

    @ApiModelProperty(value = "代理标签")
    private String agentLabelNames;
}
