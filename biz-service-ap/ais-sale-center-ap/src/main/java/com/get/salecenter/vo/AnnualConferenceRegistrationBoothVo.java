package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AnnualConferenceRegistrationBooth;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * @author: Sea
 * @create: 2021/4/29 14:47
 * @verison: 1.0
 * @description:
 */
@Data
public class AnnualConferenceRegistrationBoothVo extends BaseEntity implements Serializable {

    //=============实体类AnnualConferenceRegistrationBooth================
    private static final long serialVersionUID = 1L;
    /**
     * 年度会议注册id
     */
    @ApiModelProperty(value = "年度会议注册id")
    @Column(name = "fk_annual_conference_registration_id")
    private Long fkAnnualConferenceRegistrationId;
    /**
     * 展位名称
     */
    @ApiModelProperty(value = "展位名称")
    @Column(name = "booth_name")
    private String boothName;
    /**
     * 国家/地区(多选)
     */
    @ApiModelProperty(value = "国家/地区(多选)")
    @Column(name = "country_region")
    private String countryRegion;
    /**
     * 展位号
     */
    @ApiModelProperty(value = "展位号")
    @Column(name = "booth_index")
    private Integer boothIndex;
}
