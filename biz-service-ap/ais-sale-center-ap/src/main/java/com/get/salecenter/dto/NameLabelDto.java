package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2024/3/25
 * @TIME: 16:29
 * @Description:业务标记Vo
 **/
@Data
public class NameLabelDto extends BaseVoEntity  {
    @NotBlank(message = "表名不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "表名：m_institution学校")
    private String fkTableName;

    @NotNull(message = "表ID不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    @ApiModelProperty(value = "0名字前面/1名字后面")
    private Integer positionType;

    @ApiModelProperty(value = "标签")
    private String label;

    @ApiModelProperty(value = "排序")
    private Integer viewOrder;

   
}
