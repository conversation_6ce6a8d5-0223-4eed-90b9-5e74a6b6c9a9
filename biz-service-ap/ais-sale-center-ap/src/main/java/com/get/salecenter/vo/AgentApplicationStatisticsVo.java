package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2022/11/29
 * @TIME: 16:19
 * @Description:
 **/
@Data
public class AgentApplicationStatisticsVo {
    /**
     * 新建学生量
     */
    @ApiModelProperty(value = "新建学生量")
    private BigDecimal createCount;

    /**
     * 定校量（按学生）
     */
    @ApiModelProperty(value = "定校量（按学生）")
    private BigDecimal confirmationCountByStudent;

    /**
     * 成功入学量（按学生）
     */
    @ApiModelProperty(value = "成功入学量（按学生）")
    private BigDecimal successCountByStudent;

    /**
     * 定校量转化率
     */
    @ApiModelProperty(value = "定校量转化率）")
    private String confirmationConversionRate;

    /**
     * 成功入学量转化率
     */
    @ApiModelProperty(value = "成功入学量转化率")
    private String successConversionRate;

}
