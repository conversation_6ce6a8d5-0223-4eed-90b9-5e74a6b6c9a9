package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class StudentCourseStatisticsVo {

    @ApiModelProperty(value = "学生名称")
    private String name;

    @ApiModelProperty(value = "学历")
    private String educationName;

    private String educationType;

    @ApiModelProperty(value = "专业成绩")
    private String professionalScore;

    @ApiModelProperty(value = "英语成绩")
    private String englishScore;

    @ApiModelProperty(value = "主修专业")
    private String major;

    @ApiModelProperty(value = "毕业院校")
    private String graduateSchoolName;

    @ApiModelProperty(value = "申请国家")
    private String applyCountryName;

    @ApiModelProperty(value = "申请院校")
    private String applySchoolName;

    @ApiModelProperty(value = "申请课程")
    private String applyCourseName;

    @ApiModelProperty(value = "是否获得offer")
    private String offer;

    @ApiModelProperty("成绩类型")
    private Integer professionalScoreType;

    @ApiModelProperty("成绩类型名称")
    private String professionalScoreTypeName;

    @ApiModelProperty("英语成绩类型")
    private Integer englishTestType;

    @ApiModelProperty("英语成绩类型名称")
    private String englishTestTypeName;

}
