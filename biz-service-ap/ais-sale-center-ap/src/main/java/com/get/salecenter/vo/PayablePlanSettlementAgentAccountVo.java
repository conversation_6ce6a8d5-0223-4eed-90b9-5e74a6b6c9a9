package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @date 2022/1/11 12:03
 */
@Data
public class PayablePlanSettlementAgentAccountVo extends BaseEntity {
    @ApiModelProperty(value = "币种名称（代理账户）")
    private String currencyTypeName;

    //===========实体类PayablePlanSettlementAgentAccount==============
    private static final long serialVersionUID = 1L;
    /**
     * 财务结算汇总批次号
     */
    @ApiModelProperty(value = "财务结算汇总批次号")
    @Column(name = "num_settlement_batch")
    private String numSettlementBatch;
    /**
     * 应付计划Id
     */
    @ApiModelProperty(value = "应付计划Id")
    @Column(name = "fk_payable_plan_id")
    private Long fkPayablePlanId;
    /**
     * 学生代理合同账户Id
     */
    @ApiModelProperty(value = "学生代理合同账户Id")
    @Column(name = "fk_agent_contract_account_id")
    private Long fkAgentContractAccountId;
    /**
     * 币种编号（代理账户）
     */
    @ApiModelProperty(value = "币种编号（代理账户）")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
}
