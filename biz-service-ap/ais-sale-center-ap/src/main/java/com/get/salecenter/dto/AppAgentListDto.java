package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/11/18 17:35
 * @verison: 1.0
 * @description: 列表搜索vo
 */
@Data
public class AppAgentListDto {

    @ApiModelProperty("公司id")
    private List<Long> fkCompanyId;

    @ApiModelProperty("申请状态")
    private Integer appStatus;

    @ApiModelProperty("代理名称")
    private String name;

    @ApiModelProperty("代理查询开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createBeginTime;

    @ApiModelProperty("代理查询结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createEndTime;

    @ApiModelProperty("代理统计跳转")
    private AgentAnnualSummaryDto agentAnnualSummaryVo;

    @ApiModelProperty("bd的id")
    private Long fkStaffId;

    @ApiModelProperty(value = "申请类型：1新申请/2续签申请")
    private Integer appType;

    @ApiModelProperty(value = "申请来源：0=网页申请1.0/1=网页申请2.0/2=小程序申请2.0")
    @JsonProperty("appFrom")
    private Integer appFrom;

}

