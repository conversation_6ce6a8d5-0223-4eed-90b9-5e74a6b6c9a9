package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 12:37
 * @Description: 学生代理合同
 **/
@Data
public class AgentContractDto extends BaseVoEntity implements Serializable {


    private static final long serialVersionUID = 1L;
    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id", required = true)
    @NotNull(message = "学生代理id不能为空", groups = {Add.class, Update.class})
    private Long fkAgentId;
    /**
     * 学生代理合同类型Id
     */
    @ApiModelProperty(value = "学生代理合同类型Id", required = true)
    @NotNull(message = "学生代理合同类型不能为空", groups = {Add.class, Update.class})
    private Long fkAgentContractTypeId;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNum;
    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;
    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "合同模板：0=MPS主合同/1=PMP主合同/2=PMP附加合同")
    private Integer contractTemplateMode;

    @ApiModelProperty(value = "附加协议内容")
    private String additional;

    /**
     * 返佣比例备注
     */
    @ApiModelProperty(value = "返佣比例备注")
    private String returnCommissionRateNote;
    /**
     * 合同备注
     */
    @ApiModelProperty(value = "合同备注")
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;
    @ApiModelProperty(value = "查询关键字")
    private String keyWord;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;
    @ApiModelProperty(value = "1查询全部，0查询个人，2我的审批")
    private String selectStatus;

    @ApiModelProperty(value = "合同审批模式：0普通/1特殊")
    private Integer contractApprovalMode;

    @ApiModelProperty("流程key")
    private String procdkey;

    @ApiModelProperty("代理合同父id")
    private Long fkAgentContractIdRevoke;

    @ApiModelProperty("我的审批表单ids")
    private List<Long> formIds;


    @ApiModelProperty("代理合同查询开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createBeginTime;

    @ApiModelProperty("代理合同查询结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createEndTime;

    @ApiModelProperty("BD的id")
    private Long bdId;

    @ApiModelProperty("代理激活状态")
    private Boolean agentIsActive;
}
