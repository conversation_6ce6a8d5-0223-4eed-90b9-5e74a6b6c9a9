package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionHotelRoom;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;
import java.util.TreeMap;

/**
 * @author: Sea
 * @create: 2020/8/18 11:49
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "酒店房间返回类")
public class ConventionHotelRoomVo extends BaseEntity {

    /**
     * 酒店名称
     */
    @ApiModelProperty(value = "酒店名称")
    private String hotel;

    /**
     * 房型
     */
    @ApiModelProperty(value = "房型")
    private String roomType;

    /**
     * 住房人员
     */
    @ApiModelProperty(value = "住房人员")
    private List<ConventionHotelRoomPersonVo> conventionHotelRoomPersonList;

    /**
     * 住房人员
     */
    @ApiModelProperty(value = "住房人员")
    private TreeMap<String, List<ConventionPersonListVo>> conventionPersonMap;

    /**
     * 房型
     */
    @ApiModelProperty(value = "房型")
    private Integer bedCount;

    /**
     * 是否签到
     */
    @ApiModelProperty(value = "是否签到")
    private Boolean isAttend;

    //============实体类ConventionHotelRoom==================
    private static final long serialVersionUID = 1L;
    /**
     * 酒店房型Id
     */
    @ApiModelProperty(value = "酒店房型Id")
    @Column(name = "fk_convention_hotel_id")
    private Long fkConventionHotelId;
    /**
     * 系统房间编号
     */
    @ApiModelProperty(value = "系统房间编号")
    @Column(name = "system_room_num")
    private String systemRoomNum;
    /**
     * 酒店房间编号
     */
    @ApiModelProperty(value = "酒店房间编号")
    @Column(name = "hotel_room_num")
    private String hotelRoomNum;
    /**
     * 住店日期
     */
    @ApiModelProperty(value = "住店日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "stay_date")
    private Date stayDate;
}
