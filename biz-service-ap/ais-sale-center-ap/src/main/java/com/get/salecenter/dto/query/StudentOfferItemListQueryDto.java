package com.get.salecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.permissioncenter.dto.ConfigRemindDto;
import com.get.salecenter.dto.StudentQueryDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
public class StudentOfferItemListQueryDto extends BaseVoEntity {

    @ApiModelProperty(value = "特殊申请步骤状态（默认传4）")
    private List<Long> specialCurrentState;


    @ApiModelProperty(value = "是否已经创建应收应付")
    private String statusKey;

    @ApiModelProperty(value = "学生国籍所在国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "学校id列表")
    private List<Long> institutionIds;

    @ApiModelProperty(value = "学校名")
    private String schoolName;

    @ApiModelProperty(value = "课程id列表")
    private List<Long> courseIds;

    @ApiModelProperty(value = "申请课程名")
    private String courseName;

    @ApiModelProperty(value = "开学时间（开始范围）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginOpeningTime;

    @ApiModelProperty(value = "开学时间（结束范围）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endOpeningTime;

    @ApiModelProperty(value = "申请创建时间（开始范围）")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createStartTime;

    @ApiModelProperty(value = "页面大小")
    private Integer pageSize;

    @ApiModelProperty(value = "页码")
    private Integer pageNumber;

    @ApiModelProperty(value = "申请创建时间（结束范围）")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createEndTime;

    @ApiModelProperty(value = "课程等级id")
    private Long majorLevelId;

    @ApiModelProperty(value = "申请步骤状态")
    private List<Long> currentState;

    @ApiModelProperty(value = "变更的步骤id")
    private List<Long> changeStepId;

    @ApiModelProperty(value = "集团名称")
    private String groupName;

    @ApiModelProperty(value = "渠道Ids")
    private List<Long> channelIds;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "步骤状态变更时间（开始范围）")
    private Date offerStepStartTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "步骤状态变更时间（结束范围）")
    private Date offerStepEndTime;

    @ApiModelProperty(value = "bd名")
    private String bdName;

    @ApiModelProperty(value = "代理名")
    private String agentName;

    @ApiModelProperty(value = "项目成员过滤")
    private List<Long> fkProjectMemberId;

    @ApiModelProperty(value = "旧系统课程专业等级名称")
    private String oldCourseMajorLevelName;

    @ApiModelProperty(value = "提供商下拉框副id（渠道id）")
    private List<Long> fkInstitutionProviderIds;

    @ApiModelProperty(value = "提供商名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty(value = "排序，0申请创建时间倒序/1学生名称正序")
    private String selectionSort;

    @ApiModelProperty(value = "持续类型")
    private Integer durationType;

    @ApiModelProperty(value = "持续时间")
    private BigDecimal duration;

    @ApiModelProperty(value = "导出列表的Id")
    private List<Long> exportIds;

    @ApiModelProperty(value = "学生佣金结算标记关键字")
    private String commissionMark;

    @ApiModelProperty(value = "邮箱")
    private String email;
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;
    @ApiModelProperty(value = "渠道名称")
    private String channelName;
    @ApiModelProperty(value = "偏移量")
    private Integer offset;
    @ApiModelProperty(value = "是否成功")
    private Boolean isSuccess;
    @ApiModelProperty(value = "多选步骤")
    private List<Integer> stepOrderList;
    //=========path=========
    @ApiModelProperty(value = "提醒时间vo")
    //private ConfigRemindDto configRemindDto;
    private ConfigRemindDto configRemindVo;
    @ApiModelProperty(value = "导出列表的国家ids")
    private List<Long> exportCountryIds;
    @ApiModelProperty(value = "导出列表的staffId")
    private Long exportStaffId;
    @ApiModelProperty(value = "学生名字")
    private String studentName;
    @ApiModelProperty(value = "导出列表的语言")
    private String exportLocale;
    @ApiModelProperty(value = "公司Id", required = true)
    private Long fkCompanyId;

    @ApiModelProperty(value = "项目成员id列表")
    private List<Long> fkProjectRoleIds;
    @ApiModelProperty(value = "付款状态：0/1/2：未付/已付部分/已付齐")
    private Integer payableStatus;
    @ApiModelProperty("收款状态：0未收/1部分已收/2已收齐")
    private Integer receiveStatus;
    @ApiModelProperty(value = "学生查询条件")
    private StudentQueryDto studentVo;
    @ApiModelProperty("学校Ids")
    private Set<Long> fkInstitutionIds;
    @ApiModelProperty(value = "代理佣金结算状态：0待结算/1可结算，默认为0")
    private Integer settlementStatus;
    @ApiModelProperty("学生编号")
    private String studentNum;
    @ApiModelProperty(value = "电话")
    private String tel;
    @ApiModelProperty(value = "学生国籍所在国家Id")
    private Long fkAreaCountryIdNationality;

    //==================path=================
    /**
     * 服务提供商/产品
     */
    @ApiModelProperty(value = "服务提供商/产品")
    private String businessProviderAndProductName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "是否财务专用：0否/1是")
    @Column(name = "is_hidden")
    private Boolean isHidden;

    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态：0/未收 1/部分已收 2/已收齐")
    private Integer arStatus;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态：0/未付 1/部分已付 2/已付清")
    private Integer apStatus;


    /**
     * bd名称
     */
    @ApiModelProperty(value = "bd名称")
    private String staffName;

    /**
     * 项目成员名称
     */
    @ApiModelProperty(value = "项目成员名称")
    private String memberName;

    /**
     * 保险单号
     */
    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    private Integer status;

    /**
     * 保单开始时间
     */
    @ApiModelProperty(value = "保单开始时间-开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceStartTimeStart;

    /**
     * 保单开始时间
     */
    @ApiModelProperty(value = "保单开始时间-结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceStartTimeEnd;
    /**
     * 保单结束时间
     */
    @ApiModelProperty(value = "保单结束时间-开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTimeStart;
    /**
     * 保单结束时间
     */
    @ApiModelProperty(value = "保单结束时间-结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTimeEnd;

    /**
     * 保险购买时间
     */
    @ApiModelProperty(value = "保险购买时间-开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceBuyTimeStart;

    /**
     * 保险购买时间
     */
    @ApiModelProperty(value = "保险购买时间-结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceBuyTimeEnd;

    @ApiModelProperty(value = "保险创建时间范围起")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date gmtCreateBeginTime;

    @ApiModelProperty(value = "保险创建时间范围末")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date gmtCreateEndTime;

    /**
     * 业务渠道Id
     */
    @ApiModelProperty(value = "业务渠道Id")
    private Long fkBusinessChannelId;



}
