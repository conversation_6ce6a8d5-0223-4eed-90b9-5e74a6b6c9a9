package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.common.annotion.ExportInternationalization;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2022/3/17 12:10
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentOfferItemSummaryExportVo {

    @ExportInternationalization(valueZh = "学生编号", valueEn = "Student Number")
    @ApiModelProperty(value = "学生编号")
    private String studentNum;

    @ExportInternationalization(valueZh = "申请计划编号", valueEn = "Application Plan Number")
    @ApiModelProperty(value = "申请计划编号")
    private String offerItemNum;

    /**
     * student表
     */
    @ExportInternationalization(valueZh = "英文姓名", valueEn = "English Name")
    @ApiModelProperty(value = "英文姓名")
    private String name;

    /**
     * student表
     */
    @ExportInternationalization(valueZh = "姓名", valueEn = "Name")
    @ApiModelProperty(value = "姓名")
    private String nameChn;

    /**
     * student表
     * 项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生
     */
    @ExportInternationalization(valueZh = "学位项目说明（如3+1/2+2/联合培养等）", valueEn = "Degree Project Description")
    @ApiModelProperty(value = "学位项目说明（如3+1/2+2/联合培养等）")
    private String educationProjectName;

    /**
     * student表
     */
    @ExportInternationalization(valueZh = "生日", valueEn = "Birthday")
    @ApiModelProperty(value = "生日")
    private String birthday;

    @ExportInternationalization(valueZh = "学生创建时间", valueEn = "Student Creation Time")
    @ApiModelProperty(value = "学生创建时间")
    private Date studentGmtCreate;

    /**
     * student表
     */
    @ExportInternationalization(valueZh = "性别", valueEn = "Sex")
    @ApiModelProperty(value = "性別")
    private String gender;

    /**
     * student表
     */
    @ExportInternationalization(valueZh = "手机号码", valueEn = "Mobile Number")
    @ApiModelProperty(value = "手机号码")
    private String mobile;

    /**
     * student表
     */
    @ExportInternationalization(valueZh = "E-mail", valueEn = "E-mail")
    @ApiModelProperty(value = "E-mail")
    private String email;

    /**
     * studentId
     */
    @ExportInternationalization(valueZh = "Offer Student ID", valueEn = "Offer Student ID")
    @ApiModelProperty(value = "Offer Student ID")
    private String studentId;



    /**
     * offer表agentId查出
     */
    @ExportInternationalization(valueZh = "中介来源", valueEn = "Agent Source")
    @ApiModelProperty(value = "中介来源")
    private String agentName;

    /**
     * BD大区
     */
    @ExportInternationalization(valueZh = "BD大区", valueEn = "BD Region")
    @ApiModelProperty(value = "BD大区")
    private String bdRegion;

    /**
     * 申请绑定BD
     */
    @ExportInternationalization(valueZh = "申请绑定BD", valueEn = "Apply Binding BD")
    @ApiModelProperty(value = "申请绑定BD")
    private String bdName;


//    /**
//     * 区域总监可多个 offer的BD
//     */
//    @ApiModelProperty("PRD")
//    private String prdName;
//
//    /**
//     * 区域经理可多个 offer的BD
//     */
//    @ApiModelProperty("SDD")
//    private String sddName;
//
//    /**
//     * 区域支持经理 offer的BD
//     */
//    @ApiModelProperty("SDM")
//    private String sdmName;


    /**
     * item 创建时间
     */
    @ExportInternationalization(valueZh = "录入时间", valueEn = "Entry Time")
    @ApiModelProperty(value = "录入时间")
    private Date gmtCreate;

    /**
     * stepName
     */
    @ExportInternationalization(valueZh = "跟进状态", valueEn = "Follow-up Status")
    @ApiModelProperty(value = "跟进状态")
    private String stepName;

    //最高状态
    @ExportInternationalization(valueZh = "最高状态", valueEn = "Highest Status")
    @ApiModelProperty(value = "最高状态")
    private String maxStepName;

    @ExportInternationalization(valueZh = "支付类型（最新）", valueEn = "Payment Type（latest）")
    @ApiModelProperty(value = "支付类型（最新）")
    private String payLogLastPaymentTypeName;

    @ExportInternationalization(valueZh = "支付状态（最新）", valueEn = "Payment Status（latest）")
    @ApiModelProperty(value = "支付状态（最新）")
    private String payLogLastPaymentStatusName;

    @ExportInternationalization(valueZh = "支付时间（最新）", valueEn = "Payment Time（latest）")
    @ApiModelProperty(value = "支付时间（最新）")
    private Date payLogLastCreateTime;

    @ExportInternationalization(valueZh = "支付备注（最新）", valueEn = "Payment Remark（latest）")
    @ApiModelProperty(value = "支付类型（最新）")
    private String payLogLastRemark;

    /**
     * 学校等级 暂无
     */
    @ExportInternationalization(valueZh = "学校等级", valueEn = "School Level")
    @ApiModelProperty(value = "学校等级")
    private String institutionLevl;

    /**
     * 申请国家
     */
    @ExportInternationalization(valueZh = "申请国家", valueEn = "Application Country")
    @ApiModelProperty(value = "申请国家")
    private String countryName;

    /**
     * 学校名称
     */
    @ExportInternationalization(valueZh = "学校名称", valueEn = "School Name")
    @ApiModelProperty(value = "学校名称")
    private String institutionName;

//    /**
//     * 学校全称
//     */
//    @ApiModelProperty(value = "学校全称")
//    private String institutionFullName;

    /**
     * 学校全称
     */
    @ExportInternationalization(valueZh = "旧学校名称", valueEn = "Old School Name")
    @ApiModelProperty(value = "旧学校名称")
    private String oldInstitutionName;

    /**
     * 课程名字
     */
    @ExportInternationalization(valueZh = "课程名字", valueEn = "Course Name")
    @ApiModelProperty(value = "课程名字")
    private String courseName;

    /**
     * 课程输入方式
     */
    @ExportInternationalization(valueZh = "课程输入方式", valueEn = "Course Input Method: Select/Customize")
    @ApiModelProperty(value = "课程输入方式：选择/自定义")
    private String oldCourseName;

    /**
     * 课程状态
     */
    @ExportInternationalization(valueZh = "课程状态", valueEn = "Course Status")
    @ApiModelProperty(value = "课程状态")
    private String courseStatus;

    @ExportInternationalization(valueZh = "是否为主课", valueEn = "Main Class Or Not")
    @ApiModelProperty(value = "是否为主课")
    private String isMainCourse;

    /**
     * 递交申请日期
     */
    @ExportInternationalization(valueZh = "递交申请日期", valueEn = "Submission Application Date")
    @ApiModelProperty(value = "递交申请日期")
    private String submitTime;

    /**
     * 入读日期 开学时间
     */
    @ExportInternationalization(valueZh = "入读日期", valueEn = "Entrance Date")
    @ApiModelProperty(value = "入读日期")
    private String openingTime;

    /**
     * 延迟入学时间
     */
    @ExportInternationalization(valueZh = "延迟入学时间", valueEn = "Defer Entrance Time")
    @ApiModelProperty(value = "延迟入学时间")
    private String maxDeferEntranceTimes;

    /**
     * 顾问名称
     */
    @ExportInternationalization(valueZh = "顾问", valueEn = "Consultant")
    @ApiModelProperty(value = "顾问")
    private String rcName;

    /**
     * ARC（跟进顾问）
     */
    @ExportInternationalization(valueZh = "跟进顾问", valueEn = "Follow-up Consultant")
    @ApiModelProperty(value = "跟进顾问")
    private String arcName;



    /**
     * 外联
     */
    @ExportInternationalization(valueZh = "外联", valueEn = "Outreach")
    @ApiModelProperty(value = "外联")
    private String outreachName;

    /**
     * ad（文案）
     */
    @ExportInternationalization(valueZh = "文案", valueEn = "Advertising")
    @ApiModelProperty(value = "文案")
    private String adName;

    @ExportInternationalization(valueZh = "咨询支持", valueEn = "Consulting Support")
    @ApiModelProperty(value = "咨询支持", notes = "key29")
    private String consultingSupport;

    @ExportInternationalization(valueZh = "咨询主管", valueEn = "Consulting Director")
    @ApiModelProperty(value = "咨询主管")
    private String consultingDirector;

    @ExportInternationalization(valueZh = "Consultant", valueEn = "Consultant")
    @ApiModelProperty(value = "Consultant")
    private String consultant;

    @ExportInternationalization(valueZh = "咨询主管", valueEn = "Consulting Manager")
    @ApiModelProperty(value = "consultantManager")
    private String consultantManager;

    /**
     * 失败原因
     */
    @ExportInternationalization(valueZh = "失败原因", valueEn = "Failure Reason")
    @ApiModelProperty(value = "失败原因")
    private String failureReason;

    @ExportInternationalization(valueZh = "提供商名称", valueEn = "Provider Name")
    @ApiModelProperty(value = "提供商名称")
    private String fkInstitutionProviderName;

    /**
     * 渠道
     */
    @ExportInternationalization(valueZh = "渠道", valueEn = "Channel")
    @ApiModelProperty(value = "渠道")
    private String fkInstitutionChannelName;

    @ExportInternationalization(valueZh = "集团", valueEn = "Group")
    @ApiModelProperty(value = "集团")
    private String fkInstitutionGroupName;

    /**
     * 学生来源
     */
    @ExportInternationalization(valueZh = "学生来源", valueEn = "Student Source")
    @ApiModelProperty(value = "学生来源")
    private String studentFrom;

    /**
     * 课程资料情况
     */
    @ExportInternationalization(valueZh = "课程资料情况", valueEn = "Course Material Information")
    @ApiModelProperty(value = "课程资料情况")
    private String courseInfoStatus;

    /**
     * 国籍
     */
    @ExportInternationalization(valueZh = "国籍", valueEn = "Nationality")
    @ApiModelProperty(value = "国籍")
    private String nationalityName;

    /**
     * 课程Level
     */
    @ExportInternationalization(valueZh = "课程Level", valueEn = "Course Level")
    @ApiModelProperty(value = "课程Level")
    private String courseMajorLevelNames;

    @ExportInternationalization(valueZh = "课程类型", valueEn = "Course Type")
    @ApiModelProperty(value = "课程类型")
    private String courseType;

    /**
     * 代理邮箱 可多个
     */
    @ExportInternationalization(valueZh = "代理邮箱", valueEn = "Agent Email")
    @ApiModelProperty(value = "代理邮箱")
    private String agentEmail;

    /**
     * 代理邮箱
     */
    @ExportInternationalization(valueZh = "备注", valueEn = "Remark")
    @ApiModelProperty(value = "备注")
    private String appRemark;

    @ExportInternationalization(valueZh = "最新步骤备注", valueEn = "Latest Step Remark")
    @ApiModelProperty(value = "最新步骤备注")
    private String stepRemark;

    @ExportInternationalization(valueZh = "最新备注建议", valueEn = "Latest Remark Suggestion")
    @ApiModelProperty(value = "最新备注建议")
    private String comment;

    @ExportInternationalization(valueZh = "最新备注建议时间", valueEn = "Latest Remark Suggestion Time")
    @ApiModelProperty(value = "最新备注建议时间")
    private String commentTime;

    /**
     * 交押金截止时间
     */
    @ExportInternationalization(valueZh = "交押金截止时间", valueEn = "Deposit Deadline")
    @ApiModelProperty(value = "交押金截止时间")
    private String depositDeadlineStr;

    /**
     * 接受offer截止时间
     */
    @ExportInternationalization(valueZh = "接受offer截止时间", valueEn = "Accept Offer Deadline")
    @ApiModelProperty(value = "接受offer截止时间")
    private String acceptOfferDeadlineStr;

    @ExportInternationalization(valueZh = "支付押金时间", valueEn = "Pay Deposit Time")
    @ApiModelProperty("支付押金时间")
    private String depositTimeStr;

    @ExportInternationalization(valueZh = "支付学费时间", valueEn = "Pay Tuition Time")
    @ApiModelProperty(value = "支付学费时间")
    private String tuitionTimeStr;

    @ExportInternationalization(valueZh = "新申请状态", valueEn = "New Application Status")
    @ApiModelProperty(value = "新申请状态")
    private String newAppStatusName;

    @ExportInternationalization(valueZh = "变更步骤时间", valueEn = "Change Step Time")
    @ApiModelProperty(value = "变更步骤时间")
    private String changeStepName;

    @ExportInternationalization(valueZh = "最后状态变更人", valueEn = "Final Status Change Person")
    @ApiModelProperty(value = "最后状态变更人")
    private String finalCreateUserName;

    /**
     * 学历等级类型：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ExportInternationalization(valueZh = "学历等级", valueEn = "Education Level")
    @ApiModelProperty(value = "学历等级")
    private String educationLevelType;

    /**
     * 毕业院校名称（下拉）
     */
    @ExportInternationalization(valueZh = "在读/毕业学校", valueEn = "In/Out Of School")
    @ApiModelProperty(value = "在读/毕业学校")
    private String fkInstitutionNameEducation;

    /**
     * 毕业专业
     */
    @ExportInternationalization(valueZh = "毕业专业", valueEn = "Education Major")
    @ApiModelProperty(value = "毕业专业")
    private String educationMajor;

    /**
     * 毕业大学国家名称
     */
    @ExportInternationalization(valueZh = "所在国家", valueEn = "Country Name")
    @ApiModelProperty(value = "所在国家")
    private String countryNameEducation;

    /**
     * 毕业大学州省名称
     */
    @ExportInternationalization(valueZh = "所在州省", valueEn = "State Name")
    @ApiModelProperty(value = "所在州省")
    private String stateNameEducation;

    /**
     * 毕业大学城市名称
     */
    @ExportInternationalization(valueZh = "所在城市", valueEn = "City Name")
    @ApiModelProperty(value = "所在城市")
    private String cityNameEducation;

    /**
     * 毕业学校类型：985/211/其他，默认选项：其他
     */
    @ExportInternationalization(valueZh = "毕业学校类型", valueEn = "Education Institution Type")
    @ApiModelProperty(value = "毕业学校类型")
    private String institutionTypeEducation;


    /**
     * 学历等级类型（国际）：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ExportInternationalization(valueZh = "学历等级(国际)", valueEn = "Education Level(International)")
    @ApiModelProperty(value = "学历等级(国际)")
    private String educationLevelType2;

    /**
     * 毕业院校名称国际(下拉)
     */
    @ExportInternationalization(valueZh = "在读/毕业学校(国际)", valueEn = "In/Out Of School(International)")
    @ApiModelProperty(value = "在读/毕业学校(国际)")
    private String fkInstitutionNameEducation2;

    /**
     * 毕业专业（国际）
     */
    @ExportInternationalization(valueZh = "毕业专业(国际)", valueEn = "Education Major(International)")
    @ApiModelProperty(value = "毕业专业（国际）")
    private String educationMajor2;

    /**
     * 毕业国家名称国际
     */
    @ExportInternationalization(valueZh = "所在国家(国际)", valueEn = "Country Name(International)")
    @ApiModelProperty(value = "所在国家(国际)")
    private String countryNameEducation2;

    /**
     * 毕业州省名称国际
     */
    @ExportInternationalization(valueZh = "所在州省(国际)", valueEn = "State Name(International)")
    @ApiModelProperty(value = "所在州省(国际)")
    private String stateNameEducation2;

    /**
     * 毕业城市名称国际
     */
    @ExportInternationalization(valueZh = "所在城市(国际)", valueEn = "City Name(International)")
    @ApiModelProperty(value = "所在城市(国际)")
    private String cityNameEducation2;

//    /**
//     * 毕业院校名称国际
//     */
//    @ApiModelProperty(value = "毕业院校名称国际（手填）")
//    private String fkInstitutionNameEducation2;


    /**
     * 高中成绩类型名称
     */
    @ExportInternationalization(valueZh = "高中成绩类型", valueEn = "High School Grade Type")
    @ApiModelProperty(value = "高中成绩类型")
    private String highSchoolTestTypeName;

    /**
     * 高中成绩
     */
    @ExportInternationalization(valueZh = "高中成绩", valueEn = "High School Grade")
    @ApiModelProperty(value = "高中成绩")
    private String highSchoolTestScore;

    /**
     * 本科成绩类型名称
     */
    @ExportInternationalization(valueZh = "本科成绩类型", valueEn = "Undergraduate Grade Type")
    @ApiModelProperty(value = "本科成绩类型")
    private String standardTestTypeName;

    /**
     * 本科成绩
     */
    @ExportInternationalization(valueZh = "本科成绩", valueEn = "Undergraduate Grade")
    @ApiModelProperty(value = "本科成绩")
    @Column(name = "standard_test_score")
    private String standardTestScore;

    @ExportInternationalization(valueZh = "研究生成绩类型", valueEn = "Master Grade Type")
    @ApiModelProperty(value = "研究生成绩类型")
    private String masterTestType;

    @ExportInternationalization(valueZh = "研究生成绩", valueEn = "Master Grade")
    @ApiModelProperty(value = "研究生成绩")
    private String masterTestScore;

    /**
     * 英语测试类型
     */
    @ExportInternationalization(valueZh = "英语成绩类型", valueEn = "English Grade Type")
    @ApiModelProperty(value = "英语成绩类型")
    private String englishTestTypeName;

    /**
     * 英语测试成绩
     */
    @ExportInternationalization(valueZh = "英语成绩", valueEn = "English Grade")
    @ApiModelProperty(value = "英语成绩")
    @Column(name = "english_test_score")
    private BigDecimal englishTestScore;

    @ExportInternationalization(valueZh = "最终状态日期", valueEn = "Final Status Date")
    @ApiModelProperty(value = "最终状态日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date finalProcessingTime;

    @ExportInternationalization(valueZh = "最终处理日", valueEn = "Final Processing Day")
    @ApiModelProperty(value = "最终处理日")
    private Integer finalProcessingDate;

    @ExportInternationalization(valueZh = "提交申请处理日", valueEn = "Submit Application Processing Day")
    @ApiModelProperty(value = "提交申请处理日")
    private Integer processingDate;

    @ExportInternationalization(valueZh = "代理标签", valueEn = "Agent Label")
    @ApiModelProperty(value = "代理标签")
    private String agentLabelNames;

    @ExportInternationalization(valueZh = "代理电邮标签", valueEn = "Agent Email Label")
    @ApiModelProperty(value = "代理电邮标签")
    private String agentEmailLabelNames;

}
