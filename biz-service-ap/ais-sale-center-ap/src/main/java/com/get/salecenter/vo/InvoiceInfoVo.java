package com.get.salecenter.vo;

import com.get.salecenter.dto.InvoiceInfoDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/8/18 16:21
 * @verison: 1.0
 * @description:
 */
@Data
public class InvoiceInfoVo implements Serializable {

    @ApiModelProperty("参会人id")
    private Long fkConventionPersonId;

    @ApiModelProperty("发票信息")
    private List<InvoiceInfoDto> invoiceInfoVos;

    private static final long serialVersionUID = 1L;
}
