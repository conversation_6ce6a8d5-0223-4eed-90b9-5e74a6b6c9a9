package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/12/24 15:43
 */
@Data
public class CommissionSummaryVo {

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "代理id")
    private Long id;

    @ApiModelProperty(value = "bd名")
    private String bdName;

    @ApiModelProperty(value = "业务类型Key")
    private String fkTypeKey;

    @ApiModelProperty(value = "业务类型")
    private String fkTypeKeyName;

    @ApiModelProperty(value = "代理名")
    private String agentName;

    @ApiModelProperty(value = "支付币种")
    private String planCurrencyNum;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "人民币汇率")
    private BigDecimal rmbRate;

    @ApiModelProperty(value = "折合人民币金额")
    private BigDecimal rmbAmount;

    @ApiModelProperty(value = "已付金额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "应付差额")
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    private BigDecimal differenceAmount;

    @ApiModelProperty(value = "结算币种")
    private String accountCurrencyNum;

    @ApiModelProperty(value = "结算币种名称")
    private String accountCurrencyNumName;

    @ApiModelProperty(value = "银行账户id")
    private Long agentContractAccountId;

    @ApiModelProperty(value = "收款开户银行")
    private String bankName;

    @ApiModelProperty(value = "银行支行")
    private String bankBranchName;

    @ApiModelProperty(value = "收款账户名")
    private String bankAccount;

    @ApiModelProperty(value = "收款账户号")
    private String bankAccountNum;

    @ApiModelProperty(value = "银行地址")
    private String bankAddress;

    @ApiModelProperty(value = "Swift Code")
    private String swiftCode;

    @ApiModelProperty(value = "银行国家编码")
    private String areaCountryCode;

    @ApiModelProperty(value = "学生姓名")
    private String studentName;

    @ApiModelProperty(value = "转账用途")
    private String transferPurpose;

    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal amountActual;

    @ApiModelProperty(value = "实际手续费金额")
    private BigDecimal serviceFeeActual;

    @ApiModelProperty(value = "锁定标记 true：已锁定代理  false:未锁定代理")
    private Boolean lockFlag;

    @ApiModelProperty(value = "代理身份证号码")
    private String agentIdCard;

    @ApiModelProperty(value = "结算ids")
    private String settlementIds;

}
