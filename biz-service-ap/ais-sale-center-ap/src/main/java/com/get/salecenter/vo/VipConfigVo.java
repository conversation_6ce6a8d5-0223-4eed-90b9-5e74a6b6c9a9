package com.get.salecenter.vo;


import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.VipConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2022/11/9
 * @TIME: 16:44
 * @Description:VIP配置Dto
 **/
@Data
public class VipConfigVo extends BaseEntity {

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "统计类型名称")
    private String typeName;

    @ApiModelProperty(value = "统计项名称")
    private String statisticsName;

    //=============实体类================
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
