package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ClientEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/11/10
 * @TIME: 16:07
 * @Description:
 **/
@Data
public class ClientEventVo extends BaseEntity {

    @ApiModelProperty(value = "咨询客户事件类型名称")
    private String fkStudentEventTypeName;

    //==========实体类ClientEvent===========
    @ApiModelProperty(value = "客户Id")
    @Column(name = "fk_client_id")
    private Long fkClientId;

    @ApiModelProperty(value = "客户事件类型Id（学生共用）")
    @Column(name = "fk_student_event_type_id")
    private Long fkStudentEventTypeId;

    @ApiModelProperty(value = "预约回访时间")
    @Column(name = "follow_up_time")
    private Date followUpTime;

    @ApiModelProperty(value = "时间内容")
    @Column(name = "description")
    private String description;
}
