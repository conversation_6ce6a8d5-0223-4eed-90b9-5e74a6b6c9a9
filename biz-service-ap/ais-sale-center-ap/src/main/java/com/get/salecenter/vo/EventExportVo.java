package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2021/3/11
 * @TIME: 15:11
 * @Description: 导出excel对象
 **/
@Data
public class EventExportVo {
    @ApiModelProperty(value = "Id")
    private Long id;
    /**
     * 所属公司名称
     */
    @ApiModelProperty(value = "所属公司名称")
    private String companyName;

    /**
     * 状态：0计划/1结束/2取消
     */
    @ApiModelProperty(value = "状态")
    private String statusName;

    /**
     * 活动类型名称
     */
    @ApiModelProperty(value = "活动类型名称")
    private String eventTypeName;

    /**
     * 时间
     */
    @ApiModelProperty(value = "时间")
    private String time;

    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号")
    private String num;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    private String eventTimeName;
    /**
     * 星期
     */
    @ApiModelProperty(value = "星期")
    private String week;

    /**
     * 活动举办国家Name
     */
    @ApiModelProperty(value = "活动举办国家名称")
    private String areaCountryNameHold;


    /**
     * 活动举办州省Name
     */
    @ApiModelProperty(value = "活动举办州省名称")
    private String areaStateNameHold;

    /**
     * 活动举办城市Name
     */
    @ApiModelProperty(value = "活动举办城市名称")
    private String areaCityNameHold;

    /**
     * 活动主题
     */
    @ApiModelProperty(value = "活动主题")
    private String eventTheme;

    /**
     * 活动目标对象国家名称
     */
    @ApiModelProperty(value = "活动目标对象国家名称")
    private String eventTargetCountryName;

    /**
     * 活动目标对象
     */
    @ApiModelProperty(value = "活动目标对象")
    private String eventTarget;

    /**
     * 活动目标对象负责人
     */
    @ApiModelProperty(value = "活动目标对象负责人")
    private String eventTargetLeader;

    /**
     * 负责人名字
     */
    @ApiModelProperty(value = "负责人名字")
    private String staffNameLeader1;

    /**
     * 第二负责人名字
     */
    @ApiModelProperty(value = "第二负责人名字")
    private String staffNameLeader2;

    @ApiModelProperty(value = "报名学校数")
    private Long registerCount;

    /**
     * 预算人数
     */
    @ApiModelProperty(value = "预算人数")
    private Integer budgetCount;

    /**
     * 预算金额
     */
    @ApiModelProperty(value = "预算金额")
    private BigDecimal budgetAmount;


    /**
     * 实际金额
     */
    @ApiModelProperty(value = "实际金额")
    private BigDecimal actualAmount;

//    /**
//     * 费用收入
//     */
//    @ApiModelProperty(value = "费用收入")
//    private BigDecimal amountIncome;

    @ApiModelProperty("费用收入（带币种）")
    private String amountIncomeCurrency;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;

    /**
     * BD预算场地费用
     */
    @ApiModelProperty(value = "BD预算场地费用")
    private BigDecimal bdVenueAmount;

    /**
     * BD预算餐饮费用
     */
    @ApiModelProperty(value = "BD预算餐饮费用")
    private BigDecimal bdFoodAmount;

    /**
     * BD预算奖品费用
     */
    @ApiModelProperty(value = "BD预算奖品费用")
    private BigDecimal bdPrizeAmount;

    /**
     * BD预算其他费用
     */
    @ApiModelProperty(value = "BD预算其他费用")
    private BigDecimal bdOtherAmount;


    @ApiModelProperty(value = "BD预算总费用")
    private BigDecimal bdSumAmount;

    /**
     * 参加人数
     */
    @ApiModelProperty(value = "参加人数")
    private Integer attendedCount;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;
    @ApiModelProperty("修改时间")
    private Date gmtModified;


}
