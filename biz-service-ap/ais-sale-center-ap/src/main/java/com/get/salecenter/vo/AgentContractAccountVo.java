package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AgentContractAccount;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 18:45
 * @Description:
 **/
@Data
public class AgentContractAccountVo extends BaseEntity implements Serializable {
    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNum;

    @ApiModelProperty(value = "国家名")
    private String countryName;
    //    @ApiModelProperty(value = "结算口代理Id")
    private Long settlementPortAgentId;
    /**
     * 代理名称
     */
    @ApiModelProperty("代理名称")
    private String fkAgentName;

    @ApiModelProperty("佣金结算中状态 1：不在佣金结算中，可编辑  2：佣金结算中 编辑币种需要同时更新佣金结算币种标记 3：已进行最终佣金结算 不可编辑")
    private Integer commissionSettlementStatus;

    @ApiModelProperty("账户卡类型名称")
    private String accountCardTypeName;

    @ApiModelProperty("是否绑定付款单")
    private Boolean isBindingPayment = false;

    private String fkAreaCountryName;
    private String fkAreaStateName;
    private String fkAreaCityName;
    private String fkAreaCityDivisionName;


    @ApiModelProperty(value = "附件")
    List<MediaAndAttachedVo> mediaAndAttachedDtos;

    @ApiModelProperty(value = "佣金结算标记 true:已有佣金结算 false:无佣金结算数据")
    private Boolean settlementFlag;

    //================实体类AgentContractAccount=========================
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    /**
     * 账户卡类型
     */
    @ApiModelProperty(value = "账户卡类型，枚举：借记卡1,存折2,信用卡3,准贷记卡4,预付卡费5,境外卡6")
    @Column(name = "account_card_type")
    private Integer accountCardType;
    /**
     * 银行账户名称
     */
    @ApiModelProperty(value = "银行账户名称")
    @Column(name = "bank_account")
    private String bankAccount;
    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    @Column(name = "bank_account_num")
    private String bankAccountNum;
    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    @Column(name = "bank_name")
    private String bankName;
    /**
     * 银行支行名称
     */
    @ApiModelProperty(value = "银行支行名称")
    @Column(name = "bank_branch_name")
    private String bankBranchName;
    /**
     * 银行地址国家Id
     */
    @ApiModelProperty(value = "银行地址国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 银行地址州省Id
     */
    @ApiModelProperty(value = "银行地址州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 银行地址城市Id
     */
    @ApiModelProperty(value = "银行地址城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 银行地址城市区域Id
     */
    @ApiModelProperty(value = "银行地址城市区域Id")
    @Column(name = "fk_area_city_division_id")
    private Long fkAreaCityDivisionId;
    /**
     * 银行地址
     */
    @ApiModelProperty(value = "银行地址")
    @Column(name = "bank_address")
    private String bankAddress;
    /**
     * 银行编号类型：SwiftCode/BSB
     */
    @ApiModelProperty(value = "银行编号类型：SwiftCode/BSB")
    @Column(name = "bank_code_type")
    private String bankCodeType;
    /**
     * 银行编号
     */
    @ApiModelProperty(value = "银行编号")
    @Column(name = "bank_code")
    private String bankCode;
    /**
     * 国家编码
     */
    @ApiModelProperty(value = "国家编码")
    @Column(name = "area_country_code")
    private String areaCountryCode;
    /**
     * 是否默认首选：0否/1是
     */
    @ApiModelProperty(value = "是否默认首选：0否/1是")
    @Column(name = "is_default")
    private Boolean isDefault;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


}
