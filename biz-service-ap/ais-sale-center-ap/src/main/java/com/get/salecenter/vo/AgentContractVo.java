package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 12:36
 * @Description: 代理合同DTO
 **/
@Data
//@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class AgentContractVo extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "公司ids")
    private List<Long> companyIds;

    @ApiModelProperty("任务版本号")
    private Integer taskVersion;

    @ApiModelProperty("任务id")
    private String taskId;

    @ApiModelProperty("待签或代表")
    private int signOrGetStatus;

    @ApiModelProperty("流程实例id")
    private String procInstId;

    @ApiModelProperty("创建人公司id")
    private Long fkcompanyId;

    @ApiModelProperty("发起人id")
    private Long fkStaffId;

    /**
     * 同表父id
     */
    @ApiModelProperty("同表父id")
    private Long fkTableParentId;

    /**
     * 同意按钮状态
     */
    @ApiModelProperty(value = "同意按钮状态")
    private Boolean agreeButtonType;

    /**
     * 拒绝按钮状态
     */
    @ApiModelProperty(value = "拒绝按钮状态")
    private Boolean refuseButtonType;

    @ApiModelProperty(value = "合同附件")
    List<MediaAndAttachedVo> mediaAndAttachedDtos;

    @ApiModelProperty("是否续期:0否/1是")
    private Integer isRenewal;

    //========实体类AgentContract==============
    private static final long serialVersionUID = 1L;
    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 学生代理合同类型Id
     */
    @ApiModelProperty(value = "学生代理合同类型Id")
    @Column(name = "fk_agent_contract_type_id")
    private Long fkAgentContractTypeId;
    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    @Column(name = "contract_num")
    private String contractNum;
    /**
     * 合同开始时间
     */
    @ApiModelProperty(value = "合同开始时间")
    @Column(name = "start_time")
    private Date startTime;
    /**
     * 合同结束时间
     */
    @ApiModelProperty(value = "合同结束时间")
    @Column(name = "end_time")
    private Date endTime;

    @ApiModelProperty(value = "合同模板：0=MPS主合同/1=PMP主合同/2=PMP附加合同")
    private Integer contractTemplateMode;

    @ApiModelProperty(value = "附加协议内容")
    private String additional;

    /**
     * 返佣比例备注
     */
    @ApiModelProperty(value = "返佣比例备注")
    @Column(name = "return_commission_rate_note")
    private String returnCommissionRateNote;
    /**
     * 合同备注
     */
    @ApiModelProperty(value = "合同备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    @Column(name = "status")
    private Integer status;
    @ApiModelProperty(value = "合同审批模式：0普通/1特殊")
    @Column(name = "contract_approval_mode")
    private Integer contractApprovalMode;

    @ApiModelProperty(value = "关联撤销合同Id")
    @Column(name = "fk_agent_contract_id_revoke")
    private Long fkAgentContractIdRevoke;

    /**
     * <p> Java类型:Integer &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 数据库类型:int(10) &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:合同审批状态：0无合同/1有合同/2未签署/3待审核/4审核通过/-4审核驳回/5续约中/6生效中/7已过期（6和7为逻辑生成，非数据库字段） &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(name = "合同审批状态：0无合同/1有合同/2未签署/3待审核/4审核通过/-4审核驳回/5续约中")
    @JsonProperty("contractApprovalStatus")
    private Integer contractApprovalStatus;

}
