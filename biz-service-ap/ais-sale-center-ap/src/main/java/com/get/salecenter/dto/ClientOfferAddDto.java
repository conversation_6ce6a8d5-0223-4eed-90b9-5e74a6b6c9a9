package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ClientOfferAddDto extends BaseVoEntity{

    @ApiModelProperty(value = "国家Id")
    @NotNull(message = "国家Id不能为空", groups = {Add.class, Update.class})
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "代理联系电话区号")
    private String agentContactTelAreaCode;

    @ApiModelProperty(value = "代理联系电话，多个用【;+空格】隔开")
    private String agentContactTel;

    @ApiModelProperty(value = "代理联系电邮")
    private String agentContactEmail;

    @NotNull(message = "客户Id不能为空", groups = {Add.class})
    @ApiModelProperty(value = "客户Id")
    private Long fkClientId;

    @ApiModelProperty(value = "代理Id")
    private Long fkAgentId;

    /**
     * 邮箱
     */
    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 员工Id（业绩绑定，BD）
     */
//    @NotNull(message = "员工Id不能为空", groups = {Add.class, Update.class})
//    @ApiModelProperty(value = "员工Id（业绩绑定，BD）", required = true)
//    private Long fkStaffId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;


    @ApiModelProperty(value = "角色员工id")
    @NotNull(message = "角色员工不能为空", groups = {Add.class})
    private List<ProjectRoleStaffDto> roleStaffVo;

   
}
