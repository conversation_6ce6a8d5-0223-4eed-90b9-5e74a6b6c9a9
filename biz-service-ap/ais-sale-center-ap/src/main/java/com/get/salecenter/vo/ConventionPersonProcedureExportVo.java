package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2022/10/8 15:51
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionPersonProcedureExportVo {
    /**
     * 参展人员类型（枚举定义）：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @ApiModelProperty(value = "类型")
    private String typeName;

    /**
     * 参加人姓名
     */
    @ApiModelProperty(value = "姓名（英）")
    private String name;

    /**
     * 参加人姓名（中文）
     */
    @ApiModelProperty(value = "姓名（中）")
    private String nameChn;

    /**
     * 公司
     */
    @ApiModelProperty(value = "公司")
    private String company;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别")
    private String genderName;

    /**
     * 晚宴桌桌子编号
     */
    @ApiModelProperty(value = "晚宴桌")
    private String dinnerNum;

    /**
     * 培训桌桌子编号
     */
    @ApiModelProperty(value = "培训桌")
    private String trainingNum;

    /**
     * BD名称
     */
    @ApiModelProperty(value = "BD名称")
    private String bdName;
}
