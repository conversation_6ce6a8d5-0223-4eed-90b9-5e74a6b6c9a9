package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2022/12/2
 * @TIME: 12:27
 * @Description:
 **/
@Data
public class CountryApplicationStatisticsVo {
    /**
     * 国家ID
     */
    @ApiModelProperty(value = "国家ID")
    private Long fkAreaCountryId;
    /**
     *国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;
    /**
     * 申请数
     */
    @ApiModelProperty(value = "申请数")
    private BigDecimal applicationCount;
}
