package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2023/12/19
 * @TIME: 14:57
 * @Description:
 **/
@Data
public class EventPlanRegistrationVo extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;


    @ApiModelProperty(value = "活动年度计划Id")
    private Long fkEventPlanId;

    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "学校提供商名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty(value = "发票建议币种")
    private String fkCurrencyTypeNumInvoice;

    @ApiModelProperty(value = "年份")
    private String year;

    @ApiModelProperty(value = "联系人资料")
    private String personName;

    @ApiModelProperty(value = "联系人资料")
    private String personInformation;

    @ApiModelProperty(value = "活动主题")
    private String mainTitle;

    @ApiModelProperty(value = "活动项目")
    private String activityName;

    @ApiModelProperty(value = "报名名册项目ID")
    private Long fkEventPlanRegistrationEventId;

    @ApiModelProperty(value = "费用" )
    private BigDecimal amount;

    @ApiModelProperty(value = "币种" )
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "报名费用")
    private String fee;

    @ApiModelProperty(value = "小计报名费用")
    private String subtotalFee;

    @ApiModelProperty(value = "日期")
    private String Date;

    @ApiModelProperty(value = "是否取消：0否/1是")
    private Boolean isCancel;

}
