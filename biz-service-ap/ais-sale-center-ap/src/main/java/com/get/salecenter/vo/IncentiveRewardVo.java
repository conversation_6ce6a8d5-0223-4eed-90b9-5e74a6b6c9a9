package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.IncentiveReward;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2023-03-13
 */
@Data
public class IncentiveRewardVo extends BaseEntity {
    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String fkCompanyName;

    //==========实体类IncentiveReward===========
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "奖品名称")
    private String rewardName;

    @ApiModelProperty(value = "奖品关键字")
    private String rewardKey;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
