package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppAgentChangeDataVo {

    @ApiModelProperty(value = "名称")
    private String natureNote;

    @ApiModelProperty(value = "协议编号")
    private String contractNum;

    /**
     * 申请状态：0新申请/1审核中/2通过/3拒绝
     */
    @ApiModelProperty(value = "申请状态：0新申请/1审核中/2通过/3拒绝")
    private Integer appStatus;

}
