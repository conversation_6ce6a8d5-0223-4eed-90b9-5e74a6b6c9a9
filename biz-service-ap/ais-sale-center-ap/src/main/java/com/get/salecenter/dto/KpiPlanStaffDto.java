package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.salecenter.vo.KpiPlanStaffVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/4/18
 * @TIME: 16:23
 * @Description:
 **/
@Data
public class KpiPlanStaffDto  extends BaseVoEntity  {
    @ApiModelProperty(value = "KPI方案Id")
    @NotNull(message = "KPI方案Id不能为空", groups = {KpiPlanStaffDto.Add.class})
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "员工Id集合")
    @NotNull(message = "员工Id不能为空",groups = {KpiPlanStaffDto.Add.class})
    private List<Long> fkStaffIdList;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "目标设置人Id")
    @NotNull(message = "目标设置人不能为空", groups = {KpiPlanStaffDto.Add.class})
    private Long rootFkStaffId;

    @ApiModelProperty(value = "统计角色，枚举：BD=1/项目成员=2/PM=3")
    @NotNull(message = "统计角色不能为空", groups = {KpiPlanStaffDto.Add.class})
    private Integer countRole;

    @ApiModelProperty(value = "统计方式，枚举：个人=1/团队(含业务下属)=2")
    @NotNull(message = "统计方式不能为空", groups = {KpiPlanStaffDto.Add.class})
    private Integer countMode;

    @ApiModelProperty(value = "所属公司")
    private Long companyId;
   
}
