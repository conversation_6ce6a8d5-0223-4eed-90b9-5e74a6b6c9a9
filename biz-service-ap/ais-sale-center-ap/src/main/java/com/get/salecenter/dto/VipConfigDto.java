package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2022/11/9
 * @TIME: 16:42
 * @Description:VIP配置VO
 **/
@Data
public class VipConfigDto  extends BaseVoEntity implements Serializable {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司Id不能为空", groups = {Add.class, Update.class})
    @Min(value = 1, message = "缺少公司id参数", groups = {Add.class, Update.class})
    private Long fkCompanyId;
    /**
     * 统计项名称
     */
    @ApiModelProperty(value = "统计项名称")
    private String statisticsName;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @NotNull(message = "表名不能为空", groups = {Add.class, Update.class})
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @NotNull(message = "表Id不能为空", groups = {Add.class, Update.class})
    private Long fkTableId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

   
}
