package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class StudentQueryDto {
    @ApiModelProperty(value = "学生姓名")
    private String name;

    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    @ApiModelProperty(value = "公司Id", required = true)
    private Long fkCompanyId;

    @ApiModelProperty(value = "学生编号")
    private String num;

    @ApiModelProperty(value = "学生国籍所在国家Id")
    private Long fkAreaCountryIdNationality;

    @ApiModelProperty(value = "电话")
    private String tel;

    @ApiModelProperty(value = "邮箱")
    private String email;
}
