package com.get.salecenter.vo;

import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionRegistration;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/3 10:52
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "峰会报名返回类")
public class ConventionRegistrationVo extends BaseEntity {

    /**
     * 类别
     */
    @ApiModelProperty(value = "类别")
    private String type;

    /**
     * 学校提供商名称
     */
    @ApiModelProperty(value = "学校提供商名称")
    private String institutionProviderName;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private List<String> country;

    /**
     * 报名包含的国家编号
     */
    @ApiModelProperty(value = "报名包含的国家编号")
    private List<String> cNums;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    /**
     * 参会人id
     */
    @ApiModelProperty(value = "参会人id")
    private Long fkConventionPersonId;

    @ApiModelProperty(value = "归口金额")
    private BigDecimal eventCostAmount;

    @ApiModelProperty(value = "归口金额币种编号")
    private String eventCostAmountCurrencyNum;

    @ApiModelProperty(value = "归口金额币种编号名称")
    private String eventCostAmountCurrencyNumName;

    @ApiModelProperty(value = "状态名称")
    private String statusName;


    //======实体类ConventionRegistration=========
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 学校提供商Id（费用归口）
     */
    @ApiModelProperty(value = "学校提供商Id（费用归口）")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    /**
     * 活动费用绑定Id
     */
    @UpdateWithNull
    @ApiModelProperty(value = "活动费用绑定Id")
    @Column(name = "fk_event_cost_id")
    private Long fkEventCostId;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 报名费
     */
    @ApiModelProperty(value = "报名费")
    @Column(name = "registration_fee")
    private BigDecimal registrationFee;
    /**
     * 费用摘要
     */
    @ApiModelProperty(value = "费用摘要")
    @Column(name = "summary_fee")
    private String summaryFee;
    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称")
    @Column(name = "provider_name")
    private String providerName;
    /**
     * 展位编号
     */
    @ApiModelProperty(value = "展位编号")
    @Column(name = "booth_num")
    private String boothNum;
    /**
     * 展位名称
     */
    @ApiModelProperty(value = "展位名称")
    @Column(name = "booth_name")
    private String boothName;
    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    @Column(name = "contact_person_name")
    private String contactPersonName;
    /**
     * 联系人姓名（中文）
     */
    @ApiModelProperty(value = "联系人姓名（中文）")
    @Column(name = "contact_person_name_chn")
    private String contactPersonNameChn;
    /**
     * 联系人电邮
     */
    @ApiModelProperty(value = "联系人电邮")
    @Column(name = "contact_email")
    private String contactEmail;
    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    @Column(name = "contact_tel")
    private String contactTel;
    /**
     * 快递信息
     */
    @ApiModelProperty(value = "快递信息,允许多个快递信息，格式：[中通快递][xxxxxxxxxxx][5];[中通快递][xxxxxxxxxxx][2];")
    @Column(name = "express_info")
    private String expressInfo;
    /**
     * 回执码，8位数字随机数
     */
    @ApiModelProperty(value = "回执码，8位数字随机数")
    @Column(name = "receipt_code")
    private String receiptCode;
    /**
     * 状态：0新建/1已确认/2已收款
     */
    @ApiModelProperty(value = "状态：0新建/1已确认/2已收款")
    @Column(name = "status")
    private Integer status;
    /**
     * 报名费（折合人民币）
     */
    @ApiModelProperty(value = "报名费（折合人民币）")
    @Column(name = "registration_fee_cny")
    private BigDecimal registrationFeeCny;


    @ApiModelProperty(value = "是否已验证：0否/1是")
    @Column(name = "is_verified")
    private Integer  isVerified;
}

