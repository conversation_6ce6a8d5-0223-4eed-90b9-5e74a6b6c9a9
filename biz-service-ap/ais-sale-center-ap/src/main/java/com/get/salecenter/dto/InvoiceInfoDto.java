package com.get.salecenter.dto;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2023/8/18 15:38
 * @verison: 1.0
 * @description:
 */
@Data
public class InvoiceInfoDto implements Serializable {

    @JSONField(ordinal = 1)
    @NotBlank(message = "发票类型")
    @ApiModelProperty("发票类型")
    private String invoiceType;

    @JSONField(ordinal = 2)
    @NotBlank(message = "公司名")
    @ApiModelProperty("公司名")
    private String companyName;

    @JSONField(ordinal = 3)
    @ApiModelProperty("事件")
    private String event;

//    @ApiModelProperty("币种id")
//    private Long currencyTypeId;

    @JSONField(ordinal = 4)
    @ApiModelProperty("币种类型")
    private String currencyType;


    @JSONField(ordinal = 5)
    @ApiModelProperty("纳税人识别号")
    private String taxpayerIdNum;

    @JSONField(ordinal = 6)
    @ApiModelProperty("公司地址")
    private String companyAddress;

    @JSONField(ordinal = 7)
    @ApiModelProperty("公司电话")
    private String companyTel;

    @JSONField(ordinal = 8)
    @ApiModelProperty("公司账号")
    private String companyAccount;

    @JSONField(ordinal = 9)
    @ApiModelProperty("开户行")
    private String bankName;

    @JSONField(ordinal = 10)
    @NotNull(message = "金额")
    @ApiModelProperty("金额")
    private BigDecimal amount;

    @JSONField(ordinal = 11)
    @ApiModelProperty("开票内容：服务费 / 咨询费（下拉选择）")
    private String invoiceContent;

    @JSONField(ordinal = 12)
    @ApiModelProperty("开票后回复邮箱")
    private String email;

}
