package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AgentRoleStaff;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * 代理项目成员返回类
 *
 * <AUTHOR>
 * @date 2021/8/2 11:32
 */
@Data
public class AgentRoleStaffVo extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "学生项目角色")
    private String fkStudentProjectRoleName;

    @ApiModelProperty(value = "员工名字")
    private String fkStaffName;

    /**
     * 国家名字
     */
    @ApiModelProperty(value = "国家名字")
    private String areaCountryName;

    /**
     * 业务类型名称
     */
    @ApiModelProperty(value = "业务类型名称")
    private String fkTypeKeyName;

    /**
     * 角色viewOrder
     */
    @ApiModelProperty(value = "角色viewOrder")
    private Integer roleViewOrder;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    /**
     * 代理所在国家名字
     */
    @ApiModelProperty(value = "代理所在国家名字")
    private String fkAreaCountryIdAgentName;

    /**
     * 代理所在州省名字
     */
    @ApiModelProperty(value = "代理所在州省名字")
    private String fkAreaStateIdAgentName;

    //=============实体类AgentRoleStaff==============
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 目标类型关键字，枚举：m_student_offer留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_student_offer留学申请/m_student_insurance留学保险/m_student_accommodation留学住宿")
    @Column(name = "fk_type_key")
    private String fkTypeKey;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 代理所在国家Id
     */
    @ApiModelProperty(value = "代理所在国家Id")
    @Column(name = "fk_area_country_id_agent")
    private Long fkAreaCountryIdAgent;
    /**
     * 代理所在州省Id
     */
    @ApiModelProperty(value = "代理所在州省Id")
    @Column(name = "fk_area_state_id_agent")
    private Long fkAreaStateIdAgent;
    /**
     * 学生项目角色Id
     */
    @ApiModelProperty(value = "学生项目角色Id")
    @Column(name = "fk_student_project_role_id")
    private Long fkStudentProjectRoleId;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 国家Id，可不选，不选为适合所有国家线
     */
    @ApiModelProperty(value = "国家Id，可不选，不选为适合所有国家线")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;

}
