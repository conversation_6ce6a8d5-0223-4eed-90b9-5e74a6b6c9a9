package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * author:Neil
 * Time: 17:38
 * Date: 2022/6/20
 * Description:
 */
@Data
public class BusinessProviderVo extends BaseEntity {
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "部门名称List")
    private List<String> departmentNameList;

    @ApiModelProperty(value = "公司Id")
    private List<Long> fkCompanyId;


    /**
     * 业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "业务类型名称")
    private String fkTypeKeyName;

    /**
     * 业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "业务类型名称")
    private List<String> fkTypeKeyNameList;

    @ApiModelProperty(value = "业务类型名称")
    private String fkBusinessProviderTypeName;


    @ApiModelProperty(value = "计划id")
    private Long planId;

    //========实体类BusinessProvider===========
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "部门Ids，支持多选，1,2,3逗号分隔")
    @Column(name = "fk_department_ids")
    @UpdateWithNull
    private String fkDepartmentIds;

    /**
     * 业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿")
    @Column(name = "fk_type_key")
    private String fkTypeKey;

    @ApiModelProperty(value = "业务类型Id")
    @Column(name = "fk_business_provider_type_id")
    private Long fkBusinessProviderTypeId;
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @Column(name = "num")
    private String num;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;
    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    @Column(name = "name_chn")
    private String nameChn;

    @ApiModelProperty(value = "法定代表人")
    @Column(name = "legal_person")
    private String legalPerson;

    @ApiModelProperty(value = "产品名录，英文分号分隔")
    @Column(name = "product_info")
    private String productInfo;


    @ApiModelProperty(value = "合作协议起止时间（开始）")
    @Column(name = "contract_start_time ")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractStartTime;

    @ApiModelProperty(value = "合作协议起止时间（结束）")
    @Column(name = "contract_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date contractEndTime;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;


}
