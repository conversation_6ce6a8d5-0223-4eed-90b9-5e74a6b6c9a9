package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/8/12 17:17
 */
@Data
public class StudentOfferItemUploadVo {
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;
    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;
    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTime;
    @ApiModelProperty(value = "学生申请方案项目Id")
    private Long id;
    @ApiModelProperty(value = "学生Id")
    private Long fkStudentId;
    @ApiModelProperty(value = "国家Id")
    private Long fkInstitutionCourseId;
    @ApiModelProperty(value = "课程名称")
    private String courseName;
    @ApiModelProperty(value = "旧系统课程名称")
    private String oldCourseCustomName;
    @ApiModelProperty(value = "类型关键字")
    private String typeKey;
    @ApiModelProperty(value = "源文件名")
    private String fileNameOrc;
    @ApiModelProperty(value = "目标文件名")
    private String fileName;
    @ApiModelProperty(value = "目标文件路径")
    private String filePath;
    @ApiModelProperty(value = "文件外部存储Key")
    private String fileKey;
    @ApiModelProperty(value = "guid")
    @Column(name = "file_guid")
    private String fileGuid;
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;
}
