package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.RStudentOfferItemStep;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/18
 * @TIME: 16:28
 * @Description:
 **/
@Data
public class RStudentOfferItemStepVo extends BaseEntity {

    /**
     * 申请步骤名
     */
    @ApiModelProperty(value = "申请步骤名")
    private String stepName;


    /**
     * 申请步骤排序，由0开始按顺序排列
     */
    @ApiModelProperty(value = "申请步骤排序，由0开始按顺序排列")
    private Integer stepOrder;

    //附件列表
    @ApiModelProperty(value ="附件列表")
    private List<MediaAndAttachedVo> mediaAndAttachedDto;

    @ApiModelProperty(value = "附件类型所对应的key")
    private String keyByStep;

    //===========实体类RStudentOfferItemStep===============
    private static final long serialVersionUID = 1L;
    /**
     * 学生申请方案项目Id
     */
    @ApiModelProperty(value = "学生申请方案项目Id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;
    /**
     * 学生申请方案项目状态步骤Id
     */
    @ApiModelProperty(value = "学生申请方案项目状态步骤Id")
    @Column(name = "fk_student_offer_item_step_id")
    private Long fkStudentOfferItemStepId;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    //reassign_time
    @ApiModelProperty(value = " reassign_time")
    @Column(name = "reassign_time")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date reassignTime;
}
