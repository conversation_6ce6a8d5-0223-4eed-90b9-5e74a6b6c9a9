package com.get.salecenter.vo;

import com.get.salecenter.entity.EventItemConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/7/7 18:22
 */
@Data
public class EventItemConfigVo {

    @ApiModelProperty("剩余数量")
    private int remainingQuantity;

    //========实体类EventItemConfig===========
    /**
     * 活动配置Id
     */
    @ApiModelProperty(value = "活动配置Id")
    private Long id;

    /**
     * 活动类型Key:2023Gopro/2023CanadaConference
     */
    @ApiModelProperty(value = "活动类型Key:2023Gopro/2023CanadaConference")
    private String eventType;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String itemName;

    /**
     * 项目限制
     */
    @ApiModelProperty(value = "项目限制")
    private Integer quota;

    /**
     * 其他参数1
     */
    @ApiModelProperty(value = "其他参数1")
    private String value1;

    /**
     * 其他参数2
     */
    @ApiModelProperty(value = "其他参数2")
    private String value2;

    /**
     * 其他参数3
     */
    @ApiModelProperty(value = "其他参数3")
    private String value3;

    /**
     * 其他参数4
     */
    @ApiModelProperty(value = "其他参数4")
    private String value4;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date gmtCreate;

    /**
     * 创建用户(登录账号)
     */
    @ApiModelProperty(value = "创建用户(登录账号)")
    private String gmtCreateUser;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Date gmtModified;

    /**
     * 修改用户(登录账号)
     */
    @ApiModelProperty(value = "修改用户(登录账号)")
    private String gmtModifiedUser;
}
