package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 活动时间日志VO
 */
@Data
public class EventDeferTimeDto extends BaseVoEntity {
    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
    @NotNull(message = "活动ID不能为空", groups = {Add.class})
    private Long fkEventId;

    /**
     * 活动开始时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "活动开始时间")
    private Date eventTime;

    /**
     * 活动结束时间
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "活动结束时间")
    private Date eventTimeEnd;

}
