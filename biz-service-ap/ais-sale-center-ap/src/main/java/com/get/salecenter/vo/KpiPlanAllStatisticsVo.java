package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class KpiPlanAllStatisticsVo implements Serializable {

    @ApiModelProperty(value = "KPI小计-总计")
    private String kpiAllTotal;

    @ApiModelProperty(value = "考核小计-总计")
    private String assessmentAllTotal;

    @ApiModelProperty(value = "总进度-总计，考核总计/KPI总计")
    private String allScheduleTotal;

    @ApiModelProperty(value = "KPI总占比-总计")
    private String kpiAllRatioTotal;

    @ApiModelProperty(value = "考核总占比-总计")
    private String assessmentAllRatioTotal;

    @ApiModelProperty(value = "申请数-总计")
    private String applicationCountTotal;

    @ApiModelProperty(value = "学生数-总计")
    private String studentCountTotal;

    @ApiModelProperty(value = "交押数/定校数-总计")
    private String confirmationCountTotal;

    @ApiModelProperty(value = "代理数-总计")
    private String agentCountTotal;

    @ApiModelProperty(value = "所有考核人员kpi目标列表-总计")
    private List<KpiPlanAllTargetStatisticsVo> kpiPlanAllTargetStatisticsDtoList;
}
