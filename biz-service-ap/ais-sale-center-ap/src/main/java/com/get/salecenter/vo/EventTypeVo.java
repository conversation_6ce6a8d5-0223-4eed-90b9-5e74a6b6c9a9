package com.get.salecenter.vo;

import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.EventType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/12/7 11:28
 * @verison: 1.0
 * @description:
 */
@Data
public class EventTypeVo extends BaseEntity {
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "专属部门ids")
    private List<Long> fkDepartmentIdList;

    @ApiModelProperty(value = "专属部门名称")
    private String fkDepartmentNames;

    //=========实体类EventType==========
    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    @Column(name = "type_name")
    private String typeName;

    /**
     * 类型名称
     */
    @UpdateWithNull
    @ApiModelProperty(value = "专属部门，没有设置表示无限制，支持多部门，逗号分隔：1,2,3")
    @Column(name = "fk_department_ids")
    private String fkDepartmentIds;


    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
}
