package com.get.salecenter.vo;

import com.get.salecenter.entity.AppAgentContactPerson;
import com.get.salecenter.entity.AppAgentContractAccount;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/11/24 12:45
 * @verison: 1.0
 * @description:
 */
@Data
public class AppAgentSetAgreeContext {

    private Long agentId;

    private Long fkCompanyId;

    private AppAgentVo appAgentDto;

    private List<AppAgentContractAccount> appAgentContractAccounts;

    private List<AppAgentContactPerson> appAgentContactPersons;

    private Boolean isRenewal;

}
