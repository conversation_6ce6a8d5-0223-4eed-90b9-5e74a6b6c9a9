package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;

/**
 * @author: Hardy
 * @create: 2023/2/9 16:11
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionDatasVo<T> {

    @ApiModelProperty("表头")
    private String title;

    @ApiModelProperty("表头中文")
    private String titleChn;

    @ApiModelProperty("值")
    private T value;

    @ApiModelProperty("标识")
    private String key;

    @ApiModelProperty("排序")
    private Integer viewOrder;

    @ApiModelProperty("类型")
    private Integer type;

    public void setValue(Collection<T> data) {
        this.value = (T) data;
    }

    public void setValue(T data) {
        this.value = (T) data;
    }
}
