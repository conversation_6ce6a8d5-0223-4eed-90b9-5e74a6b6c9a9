package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StudentOfferItemFailure;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * 学习计划生成失败返回类
 *
 * <AUTHOR>
 * @date 2021/7/8 17:37
 */
@Data
public class StudentOfferItemFailureVo extends BaseEntity {
    @ApiModelProperty(value = "学习计划编号")
    private String num;

    @ApiModelProperty(value = "目标国家id")
    private Long areaCountryId;

    @ApiModelProperty(value = "目标国家名字")
    private String areaCountryName;

    @ApiModelProperty(value = "学生中文名")
    private String studentName;

    @ApiModelProperty(value = "学生英文名")
    private String studentNameEng;

    @ApiModelProperty(value = "代理名字")
    private String agentName;

    @ApiModelProperty(value = "学校提供商id")
    private Long institutionProviderId;

    @ApiModelProperty(value = "学校提供商名字")
    private String institutionProviderName;

    @ApiModelProperty(value = "学校id")
    private Long institutionId;

    @ApiModelProperty(value = "学校名字")
    private String institutionName;

    @ApiModelProperty(value = "课程id")
    private Long institutionCourseId;

    @ApiModelProperty(value = "课程名字")
    private String institutionCourseName;

    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTime;

    @ApiModelProperty(value = "是否延迟入学")
    private Boolean isDeferEntrance;

    @ApiModelProperty("延迟入学时间（最终开学时间）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;


    @ApiModelProperty(value = "备注")
    private String remark;

    //===========实体类StudentOfferItemFailure================
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * 学生申请方案项目Id
     */
    @ApiModelProperty(value = "学生申请方案项目Id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;
    /**
     * 状态：0执行失败/1执行成功
     */
    @ApiModelProperty(value = "状态：0执行失败/1执行成功")
    @Column(name = "status")
    private Integer status;

}
