package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.common.result.FocExportVo;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class ClientDto extends BaseVoEntity {

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "客户姓名")
    private String name;

    @ApiModelProperty(value = "手机号码")
    private String phone;

    @ApiModelProperty(value = "微信号")
    private String weChatNumber;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "国家id")
    private List<Long> fkAreaCountryId;

    @ApiModelProperty(value = "升学方案")
    private String remark;

    @ApiModelProperty(value = "预约开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginTime;

    @ApiModelProperty(value = "预约结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    @ApiModelProperty(value = "资源创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reBeginCreateTime;

    @ApiModelProperty(value = "资源创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reEndCreateTime;

    @ApiModelProperty(value = "预约回访开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date beginFollowUpTime;

    @ApiModelProperty(value = "预约回访结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endFollowUpTime;

    @ApiModelProperty(value = "星级")
    private Integer star;

    @ApiModelProperty(value = "跟进状态")
    private Integer status;

//    @ApiModelProperty(value = "代理id")
//    private Long fkAgentId;

    @ApiModelProperty(value = "项目成员id")
    private Long projectRoleStaffId;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty("部门id")
    private Long fkDepartmentId;

    List<FocExportVo> focExportVos;

    @ApiModelProperty(value = "推荐来源类型")
    private String fkTableName;

    @ApiModelProperty(value = "推荐来源关键字")
    private String fkTableValue;

    @ApiModelProperty(value = "资源是否激活 1：激活 0：作废")
    private Boolean isActive;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("学生负责人")
    private Long fkStudentStaffId;

    @ApiModelProperty(value = "学生负责人ids")
    private List<Long> fkStudentStaffIds;

    @ApiModelProperty(value = "公司ids")
    private List<Long> companyIds;

}
