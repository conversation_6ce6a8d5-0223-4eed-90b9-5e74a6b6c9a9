package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用于学生资源详情中 绑定业务学生按钮返回数据的DTO
 */
@Data
public class ExistStudentByBoundVo {

    @ApiModelProperty("学生Id")
    private Long fkStudentId;

    /**
     * fk_client_id属性
     */
    @ApiModelProperty("学生资源Id")
    private Long fkClientId;

    @ApiModelProperty("学生信息")
    private String studentInfo;

    /**
     * 是否存在业务学生绑定数据
     */
    @ApiModelProperty("是否存在业务学生绑定数据 true/存在 false/不存在")
     private Boolean isExist;
}
