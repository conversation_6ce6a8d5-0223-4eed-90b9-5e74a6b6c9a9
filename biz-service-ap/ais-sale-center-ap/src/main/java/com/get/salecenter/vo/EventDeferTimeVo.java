package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.EventDeferTime;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

@Data
public class EventDeferTimeVo extends BaseEntity {
    /**
     * 显示活动时间
     */
    @ApiModelProperty(value = "显示活动时间")
    private String eventTimeName;

    //===========实体类EventDeferTime==============
    private static final long serialVersionUID = 1L;
    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
    @Column(name = "fk_event_id")
    private Long fkEventId;
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @Column(name = "event_time")
    private Date eventTime;
    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @Column(name = "event_time_end")
    private Date eventTimeEnd;

}
