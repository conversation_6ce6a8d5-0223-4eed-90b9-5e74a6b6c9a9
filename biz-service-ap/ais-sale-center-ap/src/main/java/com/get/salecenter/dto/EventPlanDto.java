package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2023/12/13
 * @TIME: 15:49
 * @Description:
 **/
@Data
public class EventPlanDto extends BaseVoEntity implements Serializable  {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司Id不能为空", groups = {Add.class, Update.class})
    private Long fkCompanyId;

    @ApiModelProperty(value = "年份")
    @NotNull(message = "年度不能为空", groups = {Add.class, Update.class})
    private Integer year;

    @ApiModelProperty(value = "标题")
    @NotBlank(message = "标题不能为空", groups = {Add.class, Update.class})
    private String title;

    @ApiModelProperty(value = "描述")
    private String description;

   
}
