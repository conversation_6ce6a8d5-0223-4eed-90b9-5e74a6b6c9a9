package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2022/3/29
 * @TIME: 11:22
 * @Description:学生申请计划统计DTO
 **/
@Data
public class StudentApplicationStatisticsVo {
    @ApiModelProperty(value = "国家ID")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "代理省份")
    private Long fkAreaStateId;

    @ApiModelProperty(value = "代理省份名称")
    private String fkAreaStateName;


    @ApiModelProperty(value = "新建学生")
    private BigDecimal createCount;


    @ApiModelProperty(value = "处理申请（含加申计划）")
    private BigDecimal applicationCount;


    @ApiModelProperty(value = "定校量（按申请学校）")
    private BigDecimal confirmationCount;


    @ApiModelProperty(value = "成功入学量（按申请学校）")
    private BigDecimal successCount;

    @ApiModelProperty(value = "定校量（按学生）")
    private BigDecimal confirmationCountByStudent;

    @ApiModelProperty(value = "成功入学量（按学生）")
    private BigDecimal successCountByStudent;

    /**
     * 定校量转化率
     */
    @ApiModelProperty(value = "定校量转化率）")
    private String confirmationConversionRate;
    /**
     * 成功入学量转化率
     */
    @ApiModelProperty(value = "成功入学量转化率")
    private String successConversionRate;
}
