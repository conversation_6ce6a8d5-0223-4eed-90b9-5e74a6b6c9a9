package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.BusinessProviderContactPerson;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BusinessProviderContactPersonVo extends BaseEntity {

    //=======实体类BusinessProviderContactPerson=======
    @ApiModelProperty("业务提供商Id")
    private Long fkBusinessProviderId;

    @ApiModelProperty("联系人姓名")
    private String name;

    @ApiModelProperty("性别：0女/1男")
    private Integer gender;

    @ApiModelProperty("部门")
    private String department;

    @ApiModelProperty("职称")
    private String title;

    @ApiModelProperty("手机号区号")
    private String mobileAreaCode;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("备注")
    private String remark;
}
