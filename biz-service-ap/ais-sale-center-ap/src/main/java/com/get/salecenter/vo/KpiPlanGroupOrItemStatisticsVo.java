package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2024/4/24
 * @TIME: 11:21
 * @Description:
 **/
@Data
public class KpiPlanGroupOrItemStatisticsVo extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "申请计划id列表，用于kpi方案统计考核小计跳转到申请计划汇总")
    private Set<Long> kpiAssessmentOfferItemIds;

    @ApiModelProperty(value = "申请计划id列表，用于kpi方案统计申请数、学生数跳转到申请计划汇总")
    private Set<Long> kpiApplicationOfferItemIds;

    @ApiModelProperty(value = "申请计划id列表，用于kpi方案统计定校数跳转到申请计划汇总")
    private Set<Long> kpiConfirmationOfferItemIds;

    @ApiModelProperty(value = "专业等级Id列表（跳转传递）")
    private List<Long> fkMajorLevelIdList;

    @ApiModelProperty(value = "申请国家Id列表（跳转传递）")
    private List<Long> fkAreaCountryIdList;

    @ApiModelProperty(value = "剔除学校Id列表（跳转传递）")
    private List<Long> fkInstitutionIdList;

    @ApiModelProperty(value = "KPI方案ID")
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "组别名称")
    private String groupName;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "KPI方案组别Id")
    private Long fkKpiPlanGroupId;

    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "学校提供商名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty(value = "专业等级Ids(多选)，格式：1,2,3")
    private String fkMajorLevelIds;

    @ApiModelProperty(value = "专业等级名称")
    private String fkMajorLevelNames;

    @ApiModelProperty(value = "申请国家Ids(多选)，格式：1,2,3")
    private String fkAreaCountryIds;

    @ApiModelProperty(value = "申请国家名称")
    private String fkAreaCountryNames;

    @ApiModelProperty(value = "国家包含类型：null没设置/0不包含/1包含")
    private Integer countryIncludeType;

    @ApiModelProperty(value = "剔除提供商下的学校Ids(多选)，格式：1,2,3")
    private String fkInstitutionIds;

    @ApiModelProperty(value = "剔除提供商下的学校名称")
    private String fkInstitutionIdsExcludingNames;

    @ApiModelProperty(value = "时间设定")
    private String timeSet;

    @ApiModelProperty(value = "是否统计后续课程：0否/1是，默认0不做统计")
    private Boolean  isFollow;

    @ApiModelProperty(value = "学校包含类型：null没设置/0不包含/1包含")
    private Integer institutionIncludeType;

    @ApiModelProperty(value = "学生创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentCreateTimeStart;

    @ApiModelProperty(value = "学生创建时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentCreateTimeEnd;

    @ApiModelProperty(value = "入学时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeStart;

    @ApiModelProperty(value = "入学时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date intakeTimeEnd;

    @ApiModelProperty(value = "步骤登记时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepTimeStart;

    @ApiModelProperty(value = "步骤登记时间（结束）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepTimeEnd;

    @ApiModelProperty(value = "目标设置（成功入学）")
    private Integer targetEnrolled;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "KPI小计")
    private String kpiSubtotal;

    @ApiModelProperty(value = "考核小计")
    private String assessmentSubtotal;

    @ApiModelProperty(value = "总进度，考核小计/KPI小计")
    private String allSchedule;

    @ApiModelProperty(value = "KPI总占比")
    private String kpiAllRatio;

    @ApiModelProperty(value = "考核总占比")
    private String assessmentAllRatio;

    @ApiModelProperty(value = "统计角色，枚举：BD=1/项目成员=2/PM=3（KPI统计跳转参数）")
    private Integer countRole;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "员工KPI目标设置和统计量列表")
    private List<KpiPlanTargetStatisticsVo> kpiPlanTargetStatisticsDtoList;

    @ApiModelProperty(value = "KPI总申请数")
    private Integer kpiApplicationCount;

    @ApiModelProperty(value = "KPI总学生数")
    private Integer kpiStudentCount;

    @ApiModelProperty(value = "KPI总定校数")
    private Integer kpiConfirmationCount;

    @ApiModelProperty(value = "KPI总代理数")
    private Integer kpiAgentCount;

    @ApiModelProperty(value = "代理id列表，用于kpi方案统计代理数跳转到代理管理")
    private Set<Long> kpiFkAgentIds;
}
