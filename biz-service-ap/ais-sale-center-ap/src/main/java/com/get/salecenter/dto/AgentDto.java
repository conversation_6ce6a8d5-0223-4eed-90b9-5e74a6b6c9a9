package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * @author: Sea
 * @create: 2020/7/7 11:51
 * @verison: 1.0
 * @description:
 */
@Data
public class AgentDto extends BaseVoEntity {
    /**
     * 学生代理父Id
     */
    @ApiModelProperty(value = "学生代理父Id")
    private Long fkParentAgentId;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @NotNull(message = "国家Id", groups = {Add.class, Update.class})
    private Long fkAreaCountryId;

    /**
     * 国家列表
     */
    @ApiModelProperty(value = "国家列表")
    private List<Long> fkCountryIds;

    /**
     * 州省Id
     */
    @ApiModelProperty(value = "州省Id")
    @NotNull(message = "州省Id", groups = {Add.class, Update.class})
    private Long fkAreaStateId;

    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    private Long fkAreaCityId;

    /**
     * 代理编号
     */
    @ApiModelProperty(value = "代理编号")
    private String num;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    @NotBlank(message = "代理名称不能为空", groups = {Add.class, Update.class})
    private String name;

    /**
     * 名称备注
     */
    @ApiModelProperty(value = "名称备注")
    private String nameNote;

    /**
     * 代理昵称
     */
    @ApiModelProperty(value = "代理昵称")
    private String nickName;

    /**
     * 性质：公司/个人/工作室/国际学校/其他
     */
    @ApiModelProperty(value = "性质：公司/个人/工作室/国际学校/其他")
    private String nature;

    /**
     * 性质列表
     */
    @ApiModelProperty(value = "性质列表")
    private List<String> natureList;

    /**
     * 性质备注
     */
    @ApiModelProperty(value = "性质备注")
    private String natureNote;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    private String legalPerson;

    /**
     * 税号/统一社会信用代码（公司）
     */
    @ApiModelProperty(value = "税号/统一社会信用代码（公司）")
    private String taxCode;

    /**
     * 身份证号（个人）
     */
    @ApiModelProperty(value = "身份证号（个人）")
    private String idCardNum;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String address;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 邀请码，8位数字或字母随机数
     */
    @ApiModelProperty(value = "邀请码，8位数字或字母随机数")
    private String invitationCode;

    /**
     * 是否结算口，0否/1是
     */
    @ApiModelProperty(value = "是否结算口，0否/1是")
    private Boolean isSettlementPort;

    /**
     * 是否关键代理：0否/1是
     */
    @NotNull(message = "请选择是否关键代理")
    @ApiModelProperty(value = "是否关键代理：0否/1是")
    private Boolean isKeyAgent;

    /**
     * 关键代理失效时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "关键代理失效时间")
    private Date keyAgentFailureTime;

    @ApiModelProperty(value = "是否渠道代理：false否/true是")
    private Boolean isCustomerChannel;

    @ApiModelProperty(value = "是否拒收系统邮件：0否/1是")
    @NotNull(message = "是否拒收系统邮件不能为空", groups = {Add.class, Update.class})
    private Boolean isRejectEmail;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    private String idGea;

    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    private String idIae;

    //自定义内容
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;

    /**
     * BD编号
     */
    @ApiModelProperty(value = "BD编号")
    private String bdCode;

    /**
     * BD名称
     */
    @ApiModelProperty(value = "BD名称")
    private String bdName;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;


    /**
     * BD绑定的大区ID
     */
    @ApiModelProperty("BD绑定的大区ID")
    private Long fkAreaRegionId;

    @ApiModelProperty(value = "是否导出")
    private Boolean isExport;

    @ApiModelProperty("个人姓名")
    private String personalName;

    @ApiModelProperty("代理查询开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createBeginTime;

    @ApiModelProperty("代理查询结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date createEndTime;

    @ApiModelProperty("代理统计跳转")
    private AgentAnnualSummaryDto agentAnnualSummaryVo;

    @ApiModelProperty("是否无合同附件代理:false否/true是")
    private Boolean isHasContractAttachment;

    @ApiModelProperty("代理是否存在备注 true：有 false:没有")
    private Boolean existsRemark;

    @ApiModelProperty("代理id集合，用于KPI统计中代理数的跳转")
    private Set<Long> agentIds;

}
