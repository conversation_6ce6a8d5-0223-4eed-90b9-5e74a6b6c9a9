package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class KpiPlanAllTargetStatisticsVo {

    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "员工姓名")
    private String staffName;

    @ApiModelProperty(value = "目标设置（成功入学）")
    private Integer targetEnrolled;

    @ApiModelProperty(value = "成功入学数")
    private Integer successCount;

    @ApiModelProperty(value = "进度")
    private String schedule;

    @ApiModelProperty(value = "KPI方案考核人员Id")
//    @Column(name = "fk_kpi_plan_staff_id")
    private Long fkKpiPlanStaffId;
}
