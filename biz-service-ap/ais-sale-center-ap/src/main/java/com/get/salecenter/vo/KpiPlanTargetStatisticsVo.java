package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2024/4/19
 * @TIME: 15:07
 * @Description:
 **/
@Data
public class KpiPlanTargetStatisticsVo {
    @ApiModelProperty(value = "KPI方案组别子项Id")
    private Long fkKpiPlanGroupItemId;

    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "员工姓名")
    private String staffName;

    @ApiModelProperty(value = "目标设置（成功入学）")
    private Integer targetEnrolled;

    @ApiModelProperty(value = "成功入学数")
    private Integer successCount;

    @ApiModelProperty(value = "申请计划id列表，用于kpi方案统计单个考核人员成功入学数跳转到申请计划汇总")
    private Set<Long> kpiSuccessOfferItemIds;

    @ApiModelProperty(value = "进度")
    private String schedule;

    @ApiModelProperty(value = "KPI方案考核人员Id")
//    @Column(name = "fk_kpi_plan_staff_id")
    private Long fkKpiPlanStaffId;
}

