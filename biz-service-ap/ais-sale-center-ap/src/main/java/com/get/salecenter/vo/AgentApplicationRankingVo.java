package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.institutioncenter.vo.AreaRegionVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/5/25
 * @TIME: 14:56
 * @Description:
 **/
@Data
public class AgentApplicationRankingVo extends BaseEntity implements Serializable {
    /**
     * 代理ID
     */
    @ApiModelProperty(value = "代理Id")
    private Long fkAgentId;
    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String agentName;

    /**
     * 性质：公司/个人/工作室/国际学校/其他
     */
    @ApiModelProperty(value = "性质：公司/个人/工作室/国际学校/其他")
    private String nature;

    /**
     * 代理所在国家ID
     */
    @ApiModelProperty(value = "代理所在国家ID")
    private Long fkAreaCountryId;

    /**
     * 代理所在国家
     */
    @ApiModelProperty(value = "代理所在国家")
    private String fkAreaCountryName;

    /**
     * 代理所在州省ID
     */
    @ApiModelProperty(value = "代理所在州省ID")
    private Long fkAreaStateId;

    /**
     * 代理所在州省
     */
    @ApiModelProperty(value = "代理所在州省")
    private String fkAreaStateName;
    /**
     * 代理区域名
     */
    @ApiModelProperty(value = "代理区域名")
    private String countryAndStateName;
    /**
     * BD名称
     */
    @ApiModelProperty(value = "BD名称")
    private String bdName;
    /**
     * BD绑定的大区
     */
    @ApiModelProperty("BD绑定的大区")
    private List<AreaRegionVo> areaRegionVos;
    /**
     * 新建学生
     */
    @ApiModelProperty(value = "新建学生")
    private BigDecimal createCount;

    /**
     * 处理申请（含加申）
     */
    @ApiModelProperty(value = "处理申请（含加申）")
    private BigDecimal applicationCount;

    /**
     * 学生确认数
     */
    @ApiModelProperty(value = "学生定校量（按学校）")
    private BigDecimal confirmationCount;

    /**
     * 成功入学量
     */
    @ApiModelProperty(value = "成功入学量（按学校）")
    private BigDecimal successCount;

    /**
     * 申请成功比例
     */
    @ApiModelProperty(value = "申请成功比例")
    private String successRatio;

    /**
     * 定校量（按学生）
     */
    @ApiModelProperty(value = "定校量（按学生）")
    private BigDecimal confirmationCountByStudent;

    /**
     * 成功入学量（按学生）
     */
    @ApiModelProperty(value = "成功入学量（按学生）")
    private BigDecimal successCountByStudent;
    /**
     * 定校量转化率
     */
    @ApiModelProperty(value = "定校量转化率）")
    private String confirmationConversionRate;
    /**
     * 成功入学量转化率
     */
    @ApiModelProperty(value = "成功入学量转化率")
    private String successConversionRate;

}
