package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/19
 * @TIME: 14:30
 * @Description: 学生学习计划回显dto
 **/
@Data
public class StudentItemAndStepVo {

    @ApiModelProperty(value = "Id")
    private Long id;

    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    private Long fkInstitutionCourseId;

    /**
     * 自定义课程Id
     */
    @ApiModelProperty(value = "自定义课程Id")
    private Long fkInstitutionCourseCustomId;

    /**
     * 是否后续课程隐藏：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程隐藏：0否/1是")
    private Boolean isFollowHidden;
    /**
     * 申请步骤名
     */
    @ApiModelProperty(value = "申请步骤名")
    private String stepName;


    @ApiModelProperty(value = "申请步骤id")
    private Long fkStudentOfferItemStepId;


    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String institutionCourseName;

    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    @Column(name = "old_institution_name")
    private String oldInstitutionName;

    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    @Column(name = "old_institution_full_name")
    private String oldInstitutionFullName;

    /**
     * 课程长度类型(0周、1月、2年、3学期)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1月、2年、3学期)")
    private Integer durationType;

    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private BigDecimal duration;

    /**
     * 学校id
     */
    @ApiModelProperty(value = "学校ID")
    private Long fkInstitutionId;
    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTime;

    @ApiModelProperty("延迟入学时间（最终开学时间）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;


    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date gmtCreate;

    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    private String oldCourseCustomName;

    /**
     * 学习及计划父id
     */
    @ApiModelProperty(value = "学习及计划父id")
    private Long fkParentStudentOfferItemId;

    /**
     * 是否后续课程：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程：0否/1是")
    @Column(name = "is_follow")
    private Boolean isFollow;

    @ApiModelProperty(value = "是否加申，0否/1是")
    private Boolean isAddApp;

    /**
     * 申请备注（网申信息）
     */
    @ApiModelProperty(value = "申请备注（网申信息）")
    @Column(name = "app_remark")
    private String appRemark;

    /**
     * 次级课程
     */
    @ApiModelProperty(value = "次级课程")
    private List<StudentItemAndStepVo> childItemAndStepDtos;

    /**
     * fkStudentOfferId
     */
    @ApiModelProperty(value = "fkStudentOfferId")
    private Long fkStudentOfferId;


    @ApiModelProperty(value = "最大延迟入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date maxDeferEntranceTimes;

    @ApiModelProperty(value = "是否延迟入学标记：0否/1是")
    private Boolean isDeferEntrance;


    @ApiModelProperty(value = "入学失败原因")
    private String enrolFailureReason;

    @ApiModelProperty(value = "2022年后续 true:有后续 false:没有后续")
    private Boolean existsFollow;

    @ApiModelProperty(value = "是否步骤更随主课，0否/1是")
    private Boolean isStepFollow;

    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    private Integer status;

    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    @ApiModelProperty(value = "课程名称")
    private String fkCourseName;

    @ApiModelProperty(value = "国家id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "学生名称")
    private String fkStudentName;

    @ApiModelProperty(value = "学生Id")
    private Long fkStudentId;
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

}
