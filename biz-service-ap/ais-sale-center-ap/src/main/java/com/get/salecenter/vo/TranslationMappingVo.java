package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.TranslationMapping;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2020/12/16
 * @TIME: 15:11
 * @Description:
 **/
@Data
public class TranslationMappingVo extends BaseEntity {
    /**
     * 标准版内容
     */
    @ApiModelProperty(value = "标准版内容")
    private String standardContent;
    /**
     * 翻译内容
     */
    @ApiModelProperty(value = "翻译内容")
    private String translationContent;

    //==========实体类TranslationMapping=============
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "表名")
    private String fkTableName;

    @ApiModelProperty(value = "字段名")
    private String fkColumnName;

    @ApiModelProperty(value = "字段标题名")
    private String inputTitle;

    @ApiModelProperty(value = "输入类型：0单行/1多行/2富文本")
    private Integer inputType;

    @ApiModelProperty(value = "最大字符限制数")
    private Integer maxLength;

    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

}
