package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StaffBdCode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/10/21 15:31
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffBdCodeVo extends BaseEntity {
    /**
     * 代理ID
     */
    @ApiModelProperty(value = "代理ID")
    private Long fkAgentId;
    /**
     * 名字
     */
    @ApiModelProperty(value = "名字")
    private String bdName;

    /**
     * 负责代理数
     */
    @ApiModelProperty(value = "负责代理数")
    private Integer bdCount;

    /**
     * 公司名字
     */
    @ApiModelProperty(value = "公司名字")
    private String companyName;

    @ApiModelProperty(value = "公司编号")
    private String companyNum;

    /**
     * 业务区域
     */
    @ApiModelProperty(value = "业务区域")
    private List<SaleAreaRegionVo> areaRegionDto;

    /**
     * BD编号（4位）
     */
    @ApiModelProperty(value = "BD编号（4位）")
    private String bdCode;

    /**
     * 有效代理
     */
    @ApiModelProperty(value = "有效代理")
    private Integer effectiveAgentCount;

    /**
     * 无效代理
     */
    @ApiModelProperty(value = "无效代理")
    private Integer invalidAgentCount;

    /**
     * 是否在职，false否/true是是
     */
    @ApiModelProperty(value = "是否在职，false否/true是")
    private Boolean isOnDuty;

    //==========实体类StaffBdCode=============
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    /**
     * 颜色编码，用于突显，可不填
     */
    @ApiModelProperty(value = "颜色编码，用于突显，可不填")
    @Column(name = "color_code")
    private String colorCode;
    /**
     * 大区Id，支持多选（格式为：1,2,3），暂为属性字段，可不填
     */
    @ApiModelProperty(value = "大区Id，支持多选（格式为：1,2,3），暂为属性字段，可不填")
    @Column(name = "fk_area_region_id")
    private String fkAreaRegionId;
}
