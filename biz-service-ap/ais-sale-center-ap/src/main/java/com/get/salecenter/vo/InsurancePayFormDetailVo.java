package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class InsurancePayFormDetailVo {

    @ApiModelProperty("币种名称")
    private String payableCurrencyTypeName;
    @ApiModelProperty("实收金额")
    private BigDecimal actualPayableAmount;
    @ApiModelProperty("付款时间")
    private Date actualPayTime;
    @ApiModelProperty("留学保险id")
    private Long insuranceId;
}
