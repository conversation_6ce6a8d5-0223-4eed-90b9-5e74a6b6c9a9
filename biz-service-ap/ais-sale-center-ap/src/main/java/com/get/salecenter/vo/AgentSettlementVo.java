package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 代理佣金结算Dto
 *
 * <AUTHOR>
 * @date 2021/12/21 11:20
 */
@Data
public class AgentSettlementVo extends BaseVoEntity {

    @ApiModelProperty(value = "公司Id")
    private String fkCompanyId;

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "代理编号")
    private String num;

    @ApiModelProperty(value = "代理激活状态 生效 or 未生效")
    private Boolean isActive;

    @ApiModelProperty(value = "代理名称")
    private String name;

    @ApiModelProperty(value = "名称备注")
    private String nameNote;

    @ApiModelProperty(value = "性质：公司/个人/工作室/国际学校/其他")
    private String nature;

    @ApiModelProperty(value = "结算代理性质：公司/个人/工作室/国际学校/其他")
    private String portNature;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "州省Id")
    private Long fkAreaStateId;

    @ApiModelProperty(value = "城市Id")
    private Long fkAreaCityId;

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "州省名称")
    private String stateName;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "应付计划数")
    private Integer planCount;
    @ApiModelProperty(value = "结算口代理Id")
    private Long settlementPortAgentId;
    @ApiModelProperty(value = "代理银行账户")
    private List<AgentContractAccountVo> agentContractAccountList;

    @ApiModelProperty(value = "是否有身份证  true:有  false:没有 null:不显示")
    private Boolean idCardFlag;

    @ApiModelProperty(value = "是否有营业执照  true:有  false:没有 null:不显示")
    private Boolean businessLicenseFlag;

    @ApiModelProperty(value = "合同是否没过期  true:是  false:已过期")
    private Boolean contractFlag;

    @ApiModelProperty(value = "是否有首选账号  true:有  false:没有")
    private Boolean preferredAccountFlag;


    @ApiModelProperty(value = "是否首次合作  true:是  false:不是")
    private Boolean firstFlag = true;

    @ApiModelProperty(value = "待结算标记  true：有标记  false:，没标记")
    private Boolean settlementFlag;

}
