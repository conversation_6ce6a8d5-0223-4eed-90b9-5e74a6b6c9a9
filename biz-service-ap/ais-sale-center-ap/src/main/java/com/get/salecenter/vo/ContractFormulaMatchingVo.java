package com.get.salecenter.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/1/24 10:53
 */
@Data
@ApiModel(value = "获取匹配的合同公式返回类")
public class ContractFormulaMatchingVo {

    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    /**
     * 公式类型：0常规/1奖励/2一次性奖励
     */
    @ApiModelProperty(value = "公式类型：0常规/1奖励/2一次性奖励")
    @Column(name = "formula_type")
    private Integer formulaType;

    /**
     * 公式名称
     */
    @ApiModelProperty(value = "公式名称")
    @Column(name = "title")
    private String title;

    /**
     * 统计类型：0学生数，1学费，2课程长度(周)，3课程长度(月)，4课程长度(年)
     */
    @ApiModelProperty(value = "统计类型：0学生数，1学费，2课程长度(周)，3课程长度(月)，4课程长度(年)")
    @Column(name = "count_type")
    private Integer countType;

    /**
     * 统计目标值(小)
     */
    @ApiModelProperty(value = "统计目标值(小)")
    @Column(name = "count_vale_min")
    private BigDecimal countValeMin;

    /**
     * 统计目标值(大)
     */
    @ApiModelProperty(value = "统计目标值(大)")
    @Column(name = "count_vale_max")
    private BigDecimal countValeMax;

    /**
     * 公式条件类型(多选)：0转代理学生 / 1学生获得奖学金占学费的60%以上 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段
     */
    @ApiModelProperty(value = "公式条件类型(多选)：0转代理学生 / 1学生获得奖学金占学费的60%以上 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段")
    @Column(name = "condition_type")
    private String conditionType;

    /**
     * 生效开始时间
     */
    @ApiModelProperty(value = "生效开始时间")
    @Column(name = "start_time")
    private Date startTime;

    /**
     * 生效结束时间
     */
    @ApiModelProperty(value = "生效结束时间")
    @Column(name = "end_time")
    private Date endTime;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    /**
     * 代理佣金币种编号（不同佣金币种时才需要填写）
     */
    @ApiModelProperty(value = "代理佣金币种编号（不同佣金币种时才需要填写）")
    @Column(name = "fk_currency_type_num_ag")
    private String fkCurrencyTypeNumAg;

    /**
     * 佣金上限(总)
     */
    @ApiModelProperty(value = "佣金上限(总)")
    @Column(name = "limit_amount")
    private BigDecimal limitAmount;

    /**
     * 代理佣金上限(总)
     */
    @ApiModelProperty(value = "代理佣金上限(总)")
    @Column(name = "limit_amount_ag")
    private BigDecimal limitAmountAg;

    /**
     * 公式备注
     */
    @ApiModelProperty(value = "公式备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 排序（倒序），数字由大到小排列
     */
    @ApiModelProperty(value = "排序（倒序），数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;

    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;

    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;

    /**
     * 合同公式Id
     */
    @ApiModelProperty(value = "合同公式Id")
    private Long fkContractFormulaId;

    /**
     * 学习计划id
     */
    @ApiModelProperty(value = "学习计划id")
    private Long offerItemId;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;

    /**
     * 代理佣金币种名称
     */
    @ApiModelProperty(value = "代理佣金币种名称")
    private String currencyTypeNameAg;

    /**
     * 期数，从1开始，按顺序生成，并按顺序排序
     */
    @ApiModelProperty(value = "期数，从1开始，按顺序生成，并按顺序排序")
    @Column(name = "step")
    private Integer step;

    /**
     * 佣金比例
     */
    @ApiModelProperty(value = "佣金比例")
    @Column(name = "commission_rate")
    private BigDecimal commissionRate;

    /**
     * 代理佣金比例
     */
    @ApiModelProperty(value = "代理佣金比例")
    @Column(name = "commission_rate_ag")
    private BigDecimal commissionRateAg;

    /**
     * 固定金额
     */
    @ApiModelProperty(value = "固定金额")
    @Column(name = "fixed_amount")
    private BigDecimal fixedAmount;

    /**
     * 代理固定金额
     */
    @ApiModelProperty(value = "代理固定金额")
    @Column(name = "fixed_amount_ag")
    private BigDecimal fixedAmountAg;

    /**
     * 阶段备注
     */
    @ApiModelProperty(value = "阶段备注")
    private String stageRemark;
}
