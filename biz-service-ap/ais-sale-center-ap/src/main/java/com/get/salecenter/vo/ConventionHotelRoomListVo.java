package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.util.List;
import java.util.TreeMap;

/**
 * @author: Hardy
 * @create: 2021/10/12 15:58
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionHotelRoomListVo {

    @ApiModelProperty("id，主键")
    @Id
    private Long id;

    /**
     * 酒店房型Id
     */
    @ApiModelProperty(value = "酒店房型Id")
    private Long fkConventionHotelId;

    /**
     * 系统房间编号
     */
    @ApiModelProperty(value = "系统房间编号")
    @Column(name = "system_room_num")
    private String systemRoomNum;

    /**
     * 酒店名称
     */
    @ApiModelProperty(value = "酒店名称")
    private String hotel;

    /**
     * 房型
     */
    @ApiModelProperty(value = "房型")
    private String roomType;


    /**
     * 住房人员
     */
    @ApiModelProperty(value = "住房人员")
    private TreeMap<String, List<ConventionPersonListVo>> conventionPersonMap;

    /**
     * 房型
     */
    @ApiModelProperty(value = "房型")
    private Integer bedCount;

    /**
     * 酒店房间编号
     */
    @ApiModelProperty(value = "酒店房间编号")
    private String hotelRoomNum;
}
