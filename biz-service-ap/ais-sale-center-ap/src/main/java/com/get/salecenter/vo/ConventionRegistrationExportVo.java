package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/5/26 12:09
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionRegistrationExportVo {
    /**
     * 回执码
     */
    @ApiModelProperty(value = "回执码")
    private String receiptCode;

    /**
     * 状态：0新建/1已确认/2已收款
     */
    @ApiModelProperty(value = "状态：0待发invoice/1已发invoice/2已收款/3已从mkt fee扣")
    private String status;

    /**
     * 院校名称
     */
    @ApiModelProperty(value = "院校名称")
    private String providerName;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private List<String> country;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String currencyName;

    /**
     * 报名费
     */
    @ApiModelProperty(value = "报名费")
    private BigDecimal registrationFee;

    /**
     * 折合人民币
     */
    @ApiModelProperty(value = "折合人民币")
    private BigDecimal registrationFeeCny;

    /**
     * 费用摘要
     */
    @ApiModelProperty(value = "费用摘要")
    private String summaryFee;

    /**
     * 展位编号
     */
    @ApiModelProperty(value = "展位编号")
    private String boothNum;

    /**
     * 展位名称
     */
    @ApiModelProperty(value = "展位名称")
    private String boothName;

    /**
     * 联系人姓名
     */
    @ApiModelProperty(value = "联系人姓名")
    @Column(name = "contact_person_name")
    private String contactPersonName;

    /**
     * 联系人电邮
     */
    @ApiModelProperty(value = "联系人电邮")
    @Column(name = "contact_email")
    private String contactEmail;

    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String contactTel;

    @ApiModelProperty(value = "邮箱验证")
    private String isVerified;

}
