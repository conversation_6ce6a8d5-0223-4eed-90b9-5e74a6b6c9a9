package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/7/8 17:51
 * @verison: 1.0
 * @description:
 */
@Data
public class AnnualReservationBoothVo {

    /**
     * 报名名册id
     */
    @ApiModelProperty(value = "报名名册id")
    private Long conventionRegistrationId;

    /**
     * 机构名
     */
    @ApiModelProperty(value = "机构名")
    private String institutionName;

    /**
     * 展位名
     */
    @ApiModelProperty(value = "展位名")
    private String boothName;

    /**
     * 快递信息
     */
    @ApiModelProperty(value = "快递信息")
    private List<ExpressInfoVo> expressInfoDtoList;

    /**
     * 提供晚宴抽奖礼品：0否/1是
     */
    @ApiModelProperty("提供晚宴抽奖礼品：0否/1是")
    private Integer provideGifts;
}
