package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

@Data
public class UEventThemeVo extends BaseEntity {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 活动主题
     */
    @ApiModelProperty(value = "活动主题")
    @Column(name = "event_theme")
    private String eventTheme;

}
