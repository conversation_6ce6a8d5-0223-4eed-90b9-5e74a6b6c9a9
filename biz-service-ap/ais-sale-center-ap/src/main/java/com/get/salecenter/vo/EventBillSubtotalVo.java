package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2024/1/3 17:19
 * @verison: 1.0
 * @description:
 */
@Data
public class EventBillSubtotalVo {

    @ApiModelProperty("已分金额")
    private BigDecimal sumAssignedAmount;

    @ApiModelProperty("可分金额")
    private BigDecimal sumDifferenceEventAmount;

    @ApiModelProperty("活动费用")
    private BigDecimal sumEventAmount;

    @ApiModelProperty("币种编号（三个金额同一币种）")
    private String fkCurrencyTypeNumEvent;

}
