package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/6/25 17:01
 * @desciption:
 */
@Data
public class EnrolFailureCheckItemVo extends BaseEntity {

    @ApiModelProperty(value = "国家名称")
    private String countryName;

    @ApiModelProperty(value = "大学")
    private String institutionName;

    @ApiModelProperty(value = "课程")
    private String courseName;
}
