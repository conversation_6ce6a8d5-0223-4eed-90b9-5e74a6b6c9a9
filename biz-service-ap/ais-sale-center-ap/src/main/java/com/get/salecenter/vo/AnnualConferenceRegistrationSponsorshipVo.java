package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AnnualConferenceRegistrationSponsorship;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * @author: Sea
 * @create: 2021/4/29 14:48
 * @verison: 1.0
 * @description:
 */
@Data
public class AnnualConferenceRegistrationSponsorshipVo extends BaseEntity implements Serializable {
    /**
     * 赞助类型
     */
    @ApiModelProperty(value = "赞助类型")
    private String type;

    //============实体类AnnualConferenceRegistrationSponsorship=================
    private static final long serialVersionUID = 1L;
    /**
     * 年度会议注册id
     */
    @ApiModelProperty(value = "年度会议注册id")
    @Column(name = "fk_annual_conference_registration_id")
    private Long fkAnnualConferenceRegistrationId;
    /**
     * 赞助id
     */
    @ApiModelProperty(value = "赞助id")
    @Column(name = "fk_sponsorship_config_id")
    private Long fkSponsorshipConfigId;


}
