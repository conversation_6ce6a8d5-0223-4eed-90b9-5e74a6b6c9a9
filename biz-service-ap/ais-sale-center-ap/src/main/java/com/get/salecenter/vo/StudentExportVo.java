package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

/**
 * Created by <PERSON>.
 * User: 15:55
 * Date: 2021/7/7
 * Description:学生导出excel实体类
 */
@Data
public class StudentExportVo {

    /**
     * 所属公司
     */
    @ApiModelProperty(value = "所属公司")
    private String fkCompanyName;

    /**
     * 学生编号
     */
    @ApiModelProperty(value = "学生编号")
    private String num;

    @ApiModelProperty(value = "最终申请状态")
    private String maxStepOrderName;

    @ApiModelProperty(value = "最初申请状态")
    private String minStepOrderName;

    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String reasonName;

    /**
     * 姓名(中)
     */
    @ApiModelProperty(value = "姓名(中)")
    private String name;

    /**
     * 姓名(英)
     */
    @ApiModelProperty(value = "姓名(英)")
    private String nameEng;


    /**
     * 多个申请国家
     */
    @ApiModelProperty(value = "申请国家")
    private String areaCountryNames;

    /**
     * 成功入读国家
     */
    @ApiModelProperty(value = "成功入读国家")
    private String areaCountrySuccessfulNames;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String gender;

    /**
     * 所在区域
     */
    @ApiModelProperty(value = "所在区域")
    private String inArea;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private Integer age;

    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    private String mobile;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    private String tel;

    /**
     * Email
     */
    @ApiModelProperty(value = "Email")
    private String email;

    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zipcode")
    private String zipcode;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @Column(name = "contact_address")
    private String contactAddress;


    /**
     * 当前绑定代理
     */
    @ApiModelProperty(value = "学生绑定代理")
    private String staffNameList;
    @ApiModelProperty(value = "学生绑定代理标签")
    private String staffLabelNames;
    /**
     * 当前绑定代理编号
     */
    @ApiModelProperty(value = "学生绑定代理编号")
    private String currentAgentNumList;

    /**
     * 当前绑定BD
     */
    @ApiModelProperty(value = "学生绑定BD")
    private String bdNameList;

    @ApiModelProperty(value = "申请方案绑定代理")
    private String studentOfferStaffNameList;

    @ApiModelProperty(value = "申请方案绑定代理标签")
    private String studentOfferStaffLabelNames;

    @ApiModelProperty(value = "申请方案绑定代理编号")
    private String studentOfferAgentNumList;

    @ApiModelProperty(value = "申请方案绑定BD")
    private String studentOfferBdNamList;

    /**
     * 项目角色 / 员工
     */
    @ApiModelProperty(value = "项目角色 / 员工")
    private Set<String> projectRoleStaffList;

    /**
     * 状态变更最新时间
     */
    @ApiModelProperty(value = "状态变更最新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date statusChangeLastTime;

    /**
     * 学历等级类型：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ApiModelProperty(value = "学历等级")
    private String educationLevelType;

    /**
     * 毕业院校名称（下拉）
     */
    @ApiModelProperty(value = "在读/毕业学校")
    private String educationInstitutionName;

    /**
     * 毕业专业
     */
    @ApiModelProperty(value = "毕业专业")
    private String educationMajor;

    /**
     * 毕业大学国家名称
     */
    @ApiModelProperty(value = "所在国家")
    private String countryNameEducation;

    /**
     * 毕业大学州省名称
     */
    @ApiModelProperty(value = "所在州省")
    private String stateNameEducation;

    /**
     * 毕业大学城市名称
     */
    @ApiModelProperty(value = "所在城市")
    private String cityNameEducation;

    /**
     * 毕业学校类型：985/211/其他，默认选项：其他
     */
    @ApiModelProperty(value = "毕业学校类型")
    private String institutionTypeEducation;


    /**
     * 学历等级类型（国际）：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ApiModelProperty(value = "学历等级(国际)")
    private String educationLevelType2;

    /**
     * 毕业院校名称国际(下拉)
     */
    @ApiModelProperty(value = "在读/毕业学校(国际)")
    private String educationInstitutionName2;

    /**
     * 毕业专业（国际）
     */
    @ApiModelProperty(value = "毕业专业（国际）")
    private String educationMajor2;

    /**
     * 毕业国家名称国际
     */
    @ApiModelProperty(value = "所在国家(国际)")
    private String countryNameEducation2;

    /**
     * 毕业州省名称国际
     */
    @ApiModelProperty(value = "所在州省(国际)")
    private String stateNameEducation2;

    /**
     * 毕业城市名称国际
     */
    @ApiModelProperty(value = "所在城市(国际)")
    private String cityNameEducation2;


    /**
     * 高中成绩类型名称
     */
    @ApiModelProperty(value = "高中成绩类型")
    private String highSchoolTestTypeName;

    /**
     * 高中成绩
     */
    @ApiModelProperty(value = "高中成绩")
    private String highSchoolTestScore;

    /**
     * 本科成绩类型名称
     */
    @ApiModelProperty(value = "本科成绩类型")
    private String standardTestTypeName;

    /**
     * 本科成绩
     */
    @ApiModelProperty(value = "本科成绩")
    @Column(name = "standard_test_score")
    private String standardTestScore;

    @ApiModelProperty(value = "研究生成绩类型")
    private String masterTestTypeName;


    @ApiModelProperty(value = "研究生成绩")
    private String masterTestScore;

    /**
     * 英语测试类型
     */
    @ApiModelProperty(value = "英语成绩类型")
    private String englishTestTypeName;

    /**
     * 英语测试成绩
     */
    @ApiModelProperty(value = "英语成绩")
    @Column(name = "english_test_score")
    private BigDecimal englishTestScore;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "学生申请资料收取时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receivedApplicationDataDate;


}
