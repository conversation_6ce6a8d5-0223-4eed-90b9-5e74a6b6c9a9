package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;


/**
 * @author: Neil
 * @description: 查询学生业务代理绑定DTO
 * @date: 2022/5/9 16:56
 * @return
 */
@Data
public class AgentsBindingVo {

    @ApiModelProperty(value = "申请编号")
    private String num;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "bd名称")
    private String bdName;

    @ApiModelProperty(value = "邮箱")
    private Set<String> emails;
}