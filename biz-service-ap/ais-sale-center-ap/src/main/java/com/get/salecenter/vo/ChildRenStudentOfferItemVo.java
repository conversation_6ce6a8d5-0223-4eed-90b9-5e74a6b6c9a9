package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/11/18
 * @TIME: 12:38
 * @Description:
 **/
@Data
public class ChildRenStudentOfferItemVo extends BaseEntity {

    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    private String oldCourseCustomName;
    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String fkCourseName;

    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTime;

    @ApiModelProperty("延迟入学时间（最终开学时间）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;


    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    private Long fkInstitutionCourseId;


    /**
     * 课程官网URL
     */
    @ApiModelProperty(value = "课程官网URL")
    private String courseWebsite;


    /**
     * 是否后续课程隐藏：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程隐藏：0否/1是")
    private Boolean isFollowHidden;

    /**
     * 是否可以删除
     */
    @ApiModelProperty(value = "是否可以删除，true不能删除 false可以删除")
    private Boolean isDelete;

    /**
     * 提供商名称
     */
    @ApiModelProperty(value = "提供商名称")
    private String fkInstitutionProviderName;

    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    private Long fkInstitutionProviderId;
    /**
     * 渠道来源Id
     */
    @ApiModelProperty(value = "渠道来源Id")
    private Long fkInstitutionChannelId;

    @ApiModelProperty(value = "渠道名称")
    private String fkChannelName;

    @ApiModelProperty(value = "学生id")
    private String studentId;

    @ApiModelProperty(value = "延迟入学标记")
    private Boolean isDeferEntrance;

    @ApiModelProperty(value = "延迟入学时间")
    private Date deferEntranceTime;

    /**
     * 是否后续课程：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程：0否/1是")
    private Boolean isFollow;

    @ApiModelProperty(value = "申请步骤")
    private Long stepId;

    @ApiModelProperty(value = "申请步骤名")
    private String stepName;

    @ApiModelProperty(value = "2022年后续 true:有后续 false:没有后续")
    private Boolean existsFollow;

    @ApiModelProperty(value = "申请计划编号")
    private String num;

    @ApiModelProperty(value = "课程等级名称（手填）")
    private String oldCourseMajorLevelName;

    @ApiModelProperty(value = "课程等级对应的ids，多个用逗号分隔")
    private String fkInstitutionCourseMajorLevelIds;

    @ApiModelProperty(value = "课程类型名称（手填）")
    private String oldCourseTypeName;

    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;
}
