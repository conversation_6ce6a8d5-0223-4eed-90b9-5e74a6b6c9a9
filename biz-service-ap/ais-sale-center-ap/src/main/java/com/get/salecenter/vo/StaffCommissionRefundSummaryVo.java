package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: neil
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionRefundSummaryVo {

    @ApiModelProperty("统计数据")
    private List<StaffCommissionRefundDetailVo> staffCommissionRefundList;

    @ApiModelProperty("结算总金额")
    private StaffCommissionRefundTotal staffCommissionRefundSummary;

    @ApiModelProperty("报表创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date refundSummaryCreate;


    @Data
    public static class StaffCommissionRefundTotal{

        @ApiModelProperty("岗位名称")
        private String position;

        @ApiModelProperty("员工名称")
        private String fkStaffName;

        @ApiModelProperty("OS金额(已结算)")
        private BigDecimal osSettlementAmount;

        @ApiModelProperty("OS金额(未结算)")
        private BigDecimal osUnSettlementAmount;

        @ApiModelProperty("签证金额（已结算）")
        private BigDecimal vgSettlementAmount;

        @ApiModelProperty("签证金额（未结算）")
        private BigDecimal vgUnSettlementAmount;

        @ApiModelProperty("入学金额（已结算）")
        private BigDecimal seSettlementAmount;

        @ApiModelProperty("入学金额（未结算）")
        private BigDecimal seUnSettlementAmount;

        @ApiModelProperty("后补签证金额（已结算）")
        private BigDecimal vgbSettlementAmount;

        @ApiModelProperty("后补签证金额（未结算）")
        private BigDecimal vgbUnSettlementAmount;

        @ApiModelProperty("结算金额（已结算）")
        private BigDecimal commissionAmount;

        @ApiModelProperty("结算金额(未结算)")
        private BigDecimal unCommissionAmount;


    }
}
