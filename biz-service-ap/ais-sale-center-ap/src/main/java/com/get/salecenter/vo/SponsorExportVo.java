package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Sea
 * @create: 2021/5/12 10:07
 * @verison: 1.0
 * @description:
 */
@Data
public class SponsorExportVo {


    /**
     * 回执码，8位数字随机数(和报名一致)
     */
    @ApiModelProperty(value = "回执码")
    private String receiptCode;

    /**
     * 赞助商名称
     */
    @ApiModelProperty(value = "报名赞助商")
    private String sponsorName;

    @ApiModelProperty(value = "绑定学校提供商")
    private String institutionProviderName;

    /**
     * 类型名称
     */
    @ApiModelProperty(value = "赞助费用类型")
    private String typeName;

//    /**
//     * 币种名称
//     */
//    @ApiModelProperty(value = "币种名称")
//    private String currencyName;

//    /**
//     * 赞助费用
//     */
//    @ApiModelProperty(value = "赞助费用")
//    private BigDecimal sponsorFee;
//
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "其他费用币种")
    private String currencyTypeName;

    /**
     * 费用金额
     */
    @ApiModelProperty(value = "其他费用金额")
    private BigDecimal feeOther;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "总费用币种")
    private String fkCurrencyTypeNumFee;

    /**
     * 费用金额
     */
    @ApiModelProperty(value = "总费用金额")
    private BigDecimal sumFee;

    @ApiModelProperty(value = "折合人民币金额")
    private BigDecimal sumFeeCny;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("创建时间")
    private Date gmtCreate;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @ApiModelProperty("修改时间")
    private Date gmtModified;

    @ApiModelProperty("修改人")
    private String gmtModifiedUser;
}
