package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * 业务提供商下拉框DTO
 */
@Data
public class BusinessProviderSelectVo {
    @ApiModelProperty("业务提供商Id")
    private Long id;

    @ApiModelProperty("全称")
    private String fullName;

    @ApiModelProperty(value = "产品名录，英文分号分隔")
    @Column(name = "product_info")
    private String productInfo;

    /**
     * 产品名录集合，product_info字段按英文分号分隔
     */
    @ApiModelProperty(value = "产品名录集合")
    private List<String> productList;
}
