package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/3/7 15:14
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffSettlementStatisticsItemVo {

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("员工名称")
    private String staffName;

    @ApiModelProperty("员工名称")
    private Long fkStaffId;

    @ApiModelProperty("学生名称")
    private String studentName;

    @ApiModelProperty("名")
    private String firstName;

    @ApiModelProperty("姓")
    private String lastName;

    @ApiModelProperty("学生Id")
    private Long fkStudentId;

    @ApiModelProperty("入学年份")
    private String openingTimes;

    @ApiModelProperty("代理名称")
    private String agentName;

    @ApiModelProperty("代理id")
    private String agentIds;

    @ApiModelProperty("国家名称")
    private String fkAreaCountryIds;

    @ApiModelProperty("国家名称")
    private String fkAreaCountryNames;

    @ApiModelProperty("课程等级ids")
    private String fkInstitutionCourseMajorLevelIds;

    /**
     * 目标学历
     */
    @ApiModelProperty("课程等级名称")
    private String fkInstitutionCourseMajorLevelNames;

    /**
     * 申请大类
     */
    @ApiModelProperty("课程大类名称")
    private String fkInstitutionCourseTypeGroupNames;

    @ApiModelProperty("课程大类名称")
    private String fkInstitutionCourseTypeGroupIds;

    @ApiModelProperty("收款时间")
    private String receiptDate;

    @ApiModelProperty("结算时间集合")
    private String settlementDates;

    @ApiModelProperty("结案状态")
    private Integer status;

    @ApiModelProperty("收款金额")
    private String receiptAmount;

    @ApiModelProperty("总计")
    private BigDecimal totalCommissionAmount;

    /**
     * 动态数据
     */
    private List<StaffCommissionDatasVo<StaffCommissionStatisticsDateAndAmountVo>> dynamicDatas;

}
