package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/11/21
 * @TIME: 10:27
 * @Description:代理业绩统计Dto
 **/
@Data
public class AgentPerformanceStatisticsVo {
    @ApiModelProperty(value = " 国家申请分布图")
    private List<CountryApplicationStatisticsVo> countryApplicationStatistics;

    @ApiModelProperty(value = "申请分布图")
    private AgentApplicationStatisticsVo agentApplicationStatistics;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;


    @ApiModelProperty(value = "结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
}
