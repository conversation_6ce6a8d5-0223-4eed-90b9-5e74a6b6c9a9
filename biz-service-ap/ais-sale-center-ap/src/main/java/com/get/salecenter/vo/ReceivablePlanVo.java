package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/19
 * @TIME: 15:54
 * @Description:
 **/
@Data
public class ReceivablePlanVo extends BaseEntity {

    @ApiModelProperty(value = "应收类型名称")
    private String fkTypeName;

    @ApiModelProperty(value = "应收类型对应记录名称")
    private String fkTypeTargetName;
    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    @ApiModelProperty("申请步骤状态")
    private String stepName;
    /**
     * 提供商名称
     */
    @ApiModelProperty(value = "提供商名称")
    private String fkInstitutionProviderName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;
    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "备注详情")
    private PlanRemarkDetailVo planRemarkDetailDto;
    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String fkCourseName;

    @ApiModelProperty(value = "学生名称")
    private String studentName;

    @ApiModelProperty(value = "名（英/拼音）")
    private String studentFirstName;

    @ApiModelProperty(value = "姓（英/拼音）")
    private String studentLastName;

    @ApiModelProperty(value = "学生生日")
    private String studentBirthday;
    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private BigDecimal duration;


    /**
     * 延迟入学标记
     */
    @ApiModelProperty(value = "延迟入学标记")
    private Boolean isDeferEntrance;


    /**
     * 课程长度类型(0周、1学期、2年)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1学期、2年)")
    private Integer durationType;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;

    /**
     * 折合币种名称
     */
    @ApiModelProperty(value = "折合币种名称")
    private String equivalentCurrencyTypeName;

    /**
     * 应收计划额外原因Id
     */
    @ApiModelProperty(value = "应收计划额外原因名称")
    private String fkReceivableReasonName;

    /**
     * 记录总名称
     */
    @ApiModelProperty(value = "记录总名称")
    private String targetNames;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;


    /**
     * 目标公司名称
     */
    @ApiModelProperty(value = "目标公司名称")
    private String targetCompanyName;

    /**
     * 目标公司Id
     */
    @ApiModelProperty(value = "目标公司Id")
    private List<Long> targetCompanyNameId;

    /**
     * 收款金额（折合应收币种金额）
     */
    @ApiModelProperty(value = "收款金额（折合应收币种金额）")
    private BigDecimal amountReceivable;

    /**
     * 差额
     */
    @ApiModelProperty(value = "差额")
    private BigDecimal difference;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    @ApiModelProperty(value = "学生号")
    private String studentId;

    @ApiModelProperty(value = "学生编号")
    private String studentNum;

    @ApiModelProperty(value = "性别")
    private String genderName;

    /**
     * 收齐状态：0未收/1部分已收/2已收齐
     */
    @ApiModelProperty(value = "收齐状态：0未收/1部分已收/2已收齐")
    private Integer receiveStatus;

    /**
     * 实收金额
     */
    @ApiModelProperty(value = "实收金额")
    private BigDecimal actualReceivableAmount;

    /**
     * 差额
     */
    @ApiModelProperty(value = "差额")
    private BigDecimal diffReceivableAmount;

    /**
     * 提供商id
     */
    @ApiModelProperty(value = "提供商id")
    private Long fkInstitutionProviderId;


    /**
     * 学生申请方案Id
     */
    @ApiModelProperty(value = "学生申请方案Id")
    private Long fkStudentOfferId;

    /**
     * 发票编号
     */
    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNum;

    @ApiModelProperty(value = "学生信息")
    private String studentInformation;

    @ApiModelProperty(value = "业务信息")
    private String businessInformation;

    @ApiModelProperty(value = "渠道信息")
    private String channelInformation;

    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    private String oldInstitutionName;

    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    private String oldInstitutionFullName;

    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    private String oldCourseCustomName;


    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 课程Id
     */
    @ApiModelProperty(value = "课程Id")
    private Long fkInstitutionCourseId;

    /**
     * 渠道来源Id
     */
    @ApiModelProperty(value = "渠道来源Id")
    private Long fkInstitutionChannelId;
    /**
     * 渠道来源Id
     */
    @ApiModelProperty(value = "渠道来源Id")
    private Long fkBusinessChannelId;
    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String fkInstitutionChannelName;

    /**
     * 其他金额类型名称
     */
    @ApiModelProperty(value = "其他金额类型名称")
    private String bonusTypeName;

    /**
     * 是否后续课程：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程：0否/1是")
    private Boolean isFollow;

    /**
     * 父计划id
     */
    @ApiModelProperty(value = "父计划id")
    private Long fkParentStudentOfferItemId;

    /**
     * 发票绑定金额
     */
    @ApiModelProperty(value = "发票绑定金额")
    private BigDecimal amount;

    @ApiModelProperty(value = "自增记录id")
    private Long recordId;

    @ApiModelProperty(value = "是否同步到应付计划")
    private Boolean syn = false;

    @ApiModelProperty(value = "申请备注")
    private String appRemark;

    @ApiModelProperty(value = "绑定应付计划Id")
    private Long fkPayPlanId;

    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTime;

    @ApiModelProperty("申请计划延时入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;

    @ApiModelProperty(value = "学校名称/保险开始时间/公寓名称")
    private String istOrApmName;

    @ApiModelProperty(value = "学校简称/保险结束时间/公寓开始时间")
    private String shortNameOrApaStartTime;

    @ApiModelProperty(value = "实收信息")
    private List<PublicReceiptFormDetailVo> receiptFormDetailDtos;

    @ApiModelProperty(value = "计划收款时间串")
    private String receivablePlanDateStr;

    @ApiModelProperty(value = "发票ids")
    private List<Long> fkInvoiceIds;

    @ApiModelProperty("收款方类型名")
    private String fkTypeKeyReceivableName;

    @ApiModelProperty("收款方名")
    private String receivableName;

    @ApiModelProperty(value = "保险业务信息")
    private String insuranceInfo;

    //============实体类ReceivablePlan==============
    private static final long serialVersionUID = 1L;


    /**
     * 应收类型关键字，枚举，如：m_student_offer_item
     */
    @ApiModelProperty(value = "应收类型关键字，枚举，如：m_student_offer_item")
    @Column(name = "fk_type_key")
    private String fkTypeKey;
    /**
     * 应收类型对应记录Id，如：m_student_offer_item.id
     */
    @ApiModelProperty(value = "应收类型对应记录Id，如：m_student_offer_item.id")
    @Column(name = "fk_type_target_id")
    private Long fkTypeTargetId;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    @Column(name = "summary")
    private String summary;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    @Column(name = "tuition_amount")
    private BigDecimal tuitionAmount;
    /**
     * 费率%
     */
    @ApiModelProperty(value = "费率%")
    @Column(name = "commission_rate")
    private BigDecimal commissionRate;
    /**
     * 渠道费率%
     */
    @ApiModelProperty(value = "渠道费率%")
    @Column(name = "net_rate")
    private BigDecimal netRate;
    /**
     * 佣金金额
     */
    @ApiModelProperty(value = "佣金金额")
    @Column(name = "commission_amount")
    private BigDecimal commissionAmount;
    /**
     * 定额金额
     */
    @UpdateWithNull
    @ApiModelProperty(value = "定额金额")
    @Column(name = "fixed_amount")
    private BigDecimal fixedAmount;
    /**
     * 其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入
     */
    @ApiModelProperty(value = "其他金额类型：0=奖励金额/1=Incentive奖励/2=保险金额/3=活动费用/4=其他收入")
    @Column(name = "bonus_type")
    private Integer bonusType;
    /**
     * 其他金额
     */
    @ApiModelProperty(value = "其他金额")
    @Column(name = "bonus_amount")
    private BigDecimal bonusAmount;
    /**
     * 应收金额
     */
    @ApiModelProperty(value = "应收金额")
    @Column(name = "receivable_amount")
    private BigDecimal receivableAmount;
    /**
     * 计划收款时间
     */
    @ApiModelProperty(value = "计划收款时间")
    @Column(name = "receivable_plan_date")
    @JsonFormat(pattern="yyyy-MM-dd")
    @UpdateWithNull
    private Date receivablePlanDate;
    /**
     * 应收计划额外原因Id
     */
    @ApiModelProperty(value = "应收计划额外原因Id")
    @Column(name = "fk_receivable_reason_id")
    private Integer fkReceivableReasonId;
    /**
     * 状态：0关闭/1打开/2完成
     */
    @ApiModelProperty(value = "状态：0关闭/1打开/2完成")
    @Column(name = "status")
    private Integer status;
    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    @Column(name = "id_gea_finance")
    private String idGeaFinance;

    //费率%
    @ApiModelProperty(value = "奖金比例")
    private BigDecimal bonusCommissionRate;

    //定额金额
    @ApiModelProperty(value = "奖金固定金额")
    private BigDecimal bonusFixedAmount;
}
