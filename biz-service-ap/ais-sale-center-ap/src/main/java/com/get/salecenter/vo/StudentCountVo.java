package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2021/2/2
 * @TIME: 10:08
 * @Description:
 **/
@Data
public class StudentCountVo {

    private Long id;


    private Long companyId;
    /**
     * 学生课程记录
     */
    @ApiModelProperty(value = "学生课程记录")
    private String studentItemRecord;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    /**
     * 学生课程记录
     */
    @ApiModelProperty(value = "学生课程记录")
    private String studentName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    private Long studentCountryId;

}
