package com.get.salecenter.vo;

import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.permissioncenter.vo.DepartmentAndStaffVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/5/9 17:04
 * @verison: 1.0
 * @description:
 */
@Data
public class EventBillVo extends BaseEntity {

    @ApiModelProperty("判断应收部分字段是否能编辑标志")
    private Boolean enableUpdate;

    @ApiModelProperty("部门和员工信息")
    private List<DepartmentAndStaffVo> departmentAndStaffDtos;

    @ApiModelProperty("国家ids")
    private List<Long> fkAreaCountryIdList;

    @ApiModelProperty("活动金额（带币种）")
    private String eventAmountCurrency;

    @ApiModelProperty("发票金额（带币种）")
    private String invoiceAmountCurrency;

    @ApiModelProperty("提供商名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty("国家名称")
    private String fkAreaCountryName;


    @ApiModelProperty("摘要ids")
    private List<Long> fkEventSummaryIdList;

    @ApiModelProperty("摘要名称")
    private String fkEventSummaryName;

    @ApiModelProperty(value = "活动费用币种名称")
    private String fkCurrencyTypeNumEventName;

    @ApiModelProperty(value = "发起invoice金额币种名称")
    private String fkCurrencyTypeNumInvoiceName;

    @ApiModelProperty(value = "费用归口下拉名称")
    private String selectName;

    @ApiModelProperty(value = "活动实收金额")
    private BigDecimal amountReceiptEvent;

    @ApiModelProperty(value = "活动余额")
    private BigDecimal diffAmountEvent;

    @ApiModelProperty(value = "活动费用余额")
    private BigDecimal eventBillBalance;

    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;

    //============实体类EventBill=================
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 学校供应商Id
     */
    @ApiModelProperty(value = "学校供应商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    /**
     * 活动年份
     */
    @ApiModelProperty(value = "活动年份")
    @Column(name = "event_year")
    private Integer eventYear;

    /**
     * invoice币种
     */
    @ApiModelProperty(value = "invoice币种")
    @Column(name = "fk_currency_type_num_invoice")
    private String fkCurrencyTypeNumInvoice;

    /**
     * invoice金额
     */
    @UpdateWithNull
    @ApiModelProperty(value = "invoice金额")
    @Column(name = "invoice_amount")
    private BigDecimal invoiceAmount;

    /**
     * 活动费币种
     */
    @ApiModelProperty(value = "活动费币种")
    @Column(name = "fk_currency_type_num_event")
    private String fkCurrencyTypeNumEvent;

    /**
     * 活动费金额
     */
    @ApiModelProperty(value = "活动费金额")
    @Column(name = "event_amount")
    private BigDecimal eventAmount;

    /**
     * invoice名目
     */
    @ApiModelProperty(value = "invoice名目")
    @Column(name = "invoice_summary")
    private String invoiceSummary;

    /**
     * invoice收件人
     */
    @ApiModelProperty(value = "invoice收件人")
    @Column(name = "invoice_contact_person")
    private String invoiceContactPerson;

    /**
     * invoice收件人Email
     */
    @ApiModelProperty(value = "invoice收件人Email")
    @Column(name = "invoice_contact_email")
    private String invoiceContactEmail;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 应收计划Id
     */
//    @UpdateWithNull
//    @ApiModelProperty(value = "应收计划Id")
//    @Column(name = "fk_receivable_plan_id")
//    private Long fkReceivablePlanId;

    /**
     * 发票编号
     */
    @UpdateWithNull
    @ApiModelProperty(value = "发票编号")
    @Column(name = "fk_invoice_num")
    private String fkInvoiceNum;

    /**
     * 状态：0作废/1打开
     */
    @ApiModelProperty(value = "状态：0作废/1打开")
    @Column(name = "status")
    private Integer status;

    private static final long serialVersionUID = 1L;

}
