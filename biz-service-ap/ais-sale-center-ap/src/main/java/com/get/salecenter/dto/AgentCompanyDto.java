package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/9/15
 * @TIME: 14:29
 * @Description:
 **/
@Data
public class AgentCompanyDto extends BaseVoEntity{

    /**
     * 学生代理Id
     */
    @NotNull(message = "fkAgentId", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "学生代理Id", required = true)
    private Long fkAgentId;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

 
}
