package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/4/16
 * @TIME: 16:02
 * @Description:KPI方案组别DTO
 **/
@Data
public class KpiPlanGroupVo extends BaseEntity implements Serializable {
    @ApiModelProperty(value = "KPI方案ID")
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "组别名称")
    private String groupName;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "KPI方案组别子项列表")
    private List<KpiPlanGroupItemVo> kpiPlanGroupItemDtoList;
}
