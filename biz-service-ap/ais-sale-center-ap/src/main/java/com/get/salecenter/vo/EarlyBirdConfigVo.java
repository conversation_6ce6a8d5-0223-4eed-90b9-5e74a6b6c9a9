package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 早鸟价配置
 *
 * @Date 10:06 2023/10/25
 * <AUTHOR>
 */
@Data
public class EarlyBirdConfigVo {

    @ApiModelProperty(value = "早鸟时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date earlyBirdTime;

    @ApiModelProperty(value = "是否为早鸟时间  true:是  false:不是")
    private boolean isEarlyBird;

    @ApiModelProperty(value = "早鸟折扣")
    private BigDecimal isEarlyBirdDiscount;

}
