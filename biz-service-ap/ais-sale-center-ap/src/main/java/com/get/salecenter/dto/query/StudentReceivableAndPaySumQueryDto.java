package com.get.salecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class StudentReceivableAndPaySumQueryDto {

    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "学生名字")
    private String studentName;

    @ApiModelProperty(value = "方案开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    @ApiModelProperty(value = "方案结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    @ApiModelProperty(value = "课程名称")
    private String institutionCourseName;

    @ApiModelProperty(value = "学校id")
    private String fkInstitutionId;

    @ApiModelProperty(value = "提供商id")
    private Long fkProviderId;

    @ApiModelProperty(value = "开学年份开始")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTimeStart;

    @ApiModelProperty(value = "开学年份结束")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date openingTimeEnd;

    @ApiModelProperty(value = "收齐状态：0未收/1部分已收/2已收齐/3未收齐（未收+部分已收）")
    private Integer receiveStatus;

    @ApiModelProperty(value = "付款状态：0/1/2/3：未付/已付部分/已付齐/未付齐（未付+已付部分）")
    private Integer payableStatus;

    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    @ApiModelProperty(value = "大区Id")
    private Long fkAreaRegionId;

    @ApiModelProperty(value = "状态开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date statusBeginTime;

    @ApiModelProperty(value = "状态结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date statusEndTime;

    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNums;

    @ApiModelProperty(value = "代理名称/代理编号")
    private String agentNameNum;

    @ApiModelProperty("变更的步骤id")
    private List<Long> changeStepId;

    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

}
