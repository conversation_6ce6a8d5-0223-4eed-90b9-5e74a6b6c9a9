package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/11/17 16:22
 * @verison: 1.0
 * @description:
 */
@Data
public class AppAgentAddDto extends BaseVoEntity {

    @ApiModelProperty(value = "奖学金userId")
    private Long userId;

    /**
     * 公司Id
     */
    @NotNull(message = "公司Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * BD员工Id
     */
    @NotNull(message = "BD员工Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "BD员工Id")
    private Long fkStaffId;

    /**
     * 国家Id
     */
    @NotNull(message = "国家Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    /**
     * 州省Id
     */
    @NotNull(message = "州省Id", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "州省Id")
    private Long fkAreaStateId;

    /**
     * 城市Id
     */
    @ApiModelProperty(value = "城市Id")
    private Long fkAreaCityId;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String name;

    /**
     * 名称备注
     */
    @ApiModelProperty(value = "名称备注")
    private String nameNote;

    /**
     * 个人姓名
     */
    @ApiModelProperty(value = "个人姓名")
    private String personalName;

    /**
     * 代理昵称
     */
    @ApiModelProperty(value = "代理昵称")
    private String nickName;

    /**
     * 性质：公司/个人/工作室/国际学校/其他
     */
    @NotNull(message = "合作伙伴性质", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "性质：0公司/1个人")
    private Integer natureType;

    /**
     * 性质：公司/个人/工作室/国际学校/其他
     */
    @ApiModelProperty(value = "性质：公司/个人/工作室/国际学校/其他")
    private String nature;

    /**
     * 合作方式
     */
    @ApiModelProperty(value = "合作方式：0以公司名义/1以个人名义")
    private Integer cooperationType;

    /**
     * 性质备注
     */
    @ApiModelProperty(value = "性质备注")
    private String natureNote;

    /**
     * 法定代表人
     */
    @ApiModelProperty(value = "法定代表人")
    private String legalPerson;

    /**
     * 税号/统一社会信用代码（公司）
     */
    @ApiModelProperty(value = "税号/统一社会信用代码（公司）")
    private String taxCode;

    /**
     * 身份证号（个人）
     */
    @ApiModelProperty(value = "身份证号（个人）")
    private String idCardNum;

    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    private String address;

    /**
     * 合同开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "合同开始时间")
    private Date contractStartTime;

    /**
     * 合同结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "合同结束时间")
    private Date contractEndTime;

    /**
     * 合同佣金联系邮箱
     */
    @ApiModelProperty(value = "合同佣金联系邮箱")
    private String email1;

    /**
     * 业务新闻接收邮箱
     */
//    @NotBlank(message = "业务新闻接收邮箱", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "业务新闻接收邮箱")
    private String email2;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 申请状态：0新申请/1审核中/2通过/3拒绝
     */
    @ApiModelProperty(value = "申请状态：0新申请/1审核中/2通过/3拒绝")
    private Integer appStatus;

    /**
     * 申请状态修改时间
     */
    @ApiModelProperty(value = "申请状态修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date appStatusModifyTime;

    /**
     * 申请状态修改人(登录账号)
     */
    @ApiModelProperty(value = "申请状态修改人(登录账号)")
    private String appStatusModifyUser;

    /**
     * 转化到代理Id
     */
    @ApiModelProperty(value = "转化到代理Id")
    private Long fkAgentId;

    @ApiModelProperty("代理联系人")
    @NotEmpty(message = "代理联系人",groups = {BaseVoEntity.Add.class})
    @Valid
    private List<AppAgentContactPersonAddDto> appAgentContactPersonAddVos;

    @ApiModelProperty("代理申请合同账户")
    @NotEmpty(message = "代理申请合同账户",groups = {BaseVoEntity.Add.class})
    @Valid
    private List<AppAgentContractAccountAddDto> appAgentContractAccountAddVos;

    @ApiModelProperty("附件vo：营业执照/身份证正反面")
    private List<MediaAndAttachedDto> mediaAndAttachedVos;

    @ApiModelProperty(value = "申请来源：0=网页申请1.0/1=网页申请2.0/2=小程序申请2.0")
    @JsonProperty("appFrom")
    @NotNull(message = "申请来源", groups = {Add.class})
    private Integer appFrom;

    @ApiModelProperty(value = "变更声明内容")
    private String changeStatement;

    /**
     * <p> Java类型:String &nbsp;&nbsp;&nbsp;&nbsp;
     * <p> 注释:续约token &nbsp;&nbsp;&nbsp;&nbsp;
     */
    @ApiModelProperty(value = "续约token")
    @JsonProperty("renewalToken")
    private String renewalToken;

}
