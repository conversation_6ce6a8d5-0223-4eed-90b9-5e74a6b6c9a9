package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StaffCommissionInstitution;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Hardy
 * @create: 2023/3/10 10:03
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionInstitutionVo extends BaseEntity {

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    //==========实体类StaffCommissionInstitution============
    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;

    /**
     * 状态，枚举：1激活提成
     */
    @ApiModelProperty(value = "状态，枚举：1激活提成")
    @Column(name = "status")
    private Integer status;

    private static final long serialVersionUID = 1L;
}
