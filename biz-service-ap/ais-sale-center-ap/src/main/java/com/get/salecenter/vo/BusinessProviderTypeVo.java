package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.BusinessProviderType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BusinessProviderTypeVo extends BaseEntity {

    //======实体类BusinessProviderType=====
    @ApiModelProperty("业务提供商类型名称")
    private String typeName;

    @ApiModelProperty("排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
