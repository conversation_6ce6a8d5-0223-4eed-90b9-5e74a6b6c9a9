package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2023/12/14
 * @TIME: 10:41
 * @Description:
 **/
@Data
public class EventPlanThemeVo extends BaseEntity implements Serializable {
    @ApiModelProperty(value = "活动年度计划Id")
    private Long fkEventPlanId;

    @ApiModelProperty(value = "展示类型：线上活动1/线下活动2/线下专坊3")
    private Integer displayType;

    @ApiModelProperty(value = "展示类型名")
    private String displayTypeName;

    @ApiModelProperty(value = "主标题")
    private String mainTitle;

    @ApiModelProperty(value = "副标题")
    private String subTitle;

    @ApiModelProperty(value = "描述")
    private String description;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是，若否需要灰掉整个主题")
    private Boolean isActive;

    @ApiModelProperty(value = "报名名册合计")
    private Integer registrationCount;
}
