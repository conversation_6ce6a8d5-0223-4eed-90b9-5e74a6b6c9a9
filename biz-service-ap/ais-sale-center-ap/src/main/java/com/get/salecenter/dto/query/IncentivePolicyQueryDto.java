package com.get.salecenter.dto.query;

import com.get.core.mybatis.annotation.UpdateWithNull;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class IncentivePolicyQueryDto {


 @NotNull(message = "公司Id")
 @ApiModelProperty(value = "公司Id", required = true)
 private Long fkCompanyId;

 @ApiModelProperty(value = "状态：0作废/1打开")
 private Integer status;

 @NotNull(message = "公司Id")
 @ApiModelProperty(value = "公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2")
 private String publicLevel;

 @NotNull(message = "公司Id")
 @ApiModelProperty(value = "年度")
 private Integer year;

 @ApiModelProperty(value = "国家Id")
 @UpdateWithNull
 private Long fkAreaCountryId;

 @ApiModelProperty(value = "学校提供商Id")
 @UpdateWithNull
 private Long fkInstitutionProviderId;

 @ApiModelProperty(value = "学校Ids，逗号隔开：1,2,3")
 private String fkInstitutionIds;

 @ApiModelProperty(value = "适用课程说明")
 private String conditionNote;

 @ApiModelProperty(value = "备注")
 private String remark;
}