package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.AppAgentContractAccount;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2022/11/23 11:23
 * @verison: 1.0
 * @description:
 */
@Data
public class AppAgentContractAccountVo extends BaseEntity {

    @ApiModelProperty(value = "币种名称")
    private String currencyTypeName;

    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "州省名称")
    private String fkAreaStateName;

    @ApiModelProperty(value = "城市名称")
    private String fkAreaCityName;

    @ApiModelProperty(value = "行政区名称")
    private String fkAreaCityDivisionName;

    @ApiModelProperty("附件")
    private List<MediaAndAttachedVo> mediaAndAttachedDtos;

    //===============实体类AppAgentContractAccount========================
    /**
     * 学生代理申请Id
     */
    @ApiModelProperty(value = "学生代理申请Id")
    @Column(name = "fk_app_agent_id")
    private Long fkAppAgentId;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    /**
     * 银行账户名称
     */
    @ApiModelProperty(value = "银行账户名称")
    @Column(name = "bank_account")
    private String bankAccount;

    /**
     * 银行账号
     */
    @ApiModelProperty(value = "银行账号")
    @Column(name = "bank_account_num")
    private String bankAccountNum;

    /**
     * 银行名称
     */
    @ApiModelProperty(value = "银行名称")
    @Column(name = "bank_name")
    private String bankName;

    /**
     * 银行支行名称
     */
    @ApiModelProperty(value = "银行支行名称")
    @Column(name = "bank_branch_name")
    private String bankBranchName;

    /**
     * 银行地址国家Id
     */
    @ApiModelProperty(value = "银行地址国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;

    /**
     * 银行地址州省Id
     */
    @ApiModelProperty(value = "银行地址州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;

    /**
     * 银行地址城市Id
     */
    @ApiModelProperty(value = "银行地址城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;

    /**
     * 银行地址城市区域Id
     */
    @ApiModelProperty(value = "银行地址城市区域Id")
    @Column(name = "fk_area_city_division_id")
    private Long fkAreaCityDivisionId;

    /**
     * 银行地址
     */
    @ApiModelProperty(value = "银行地址")
    @Column(name = "bank_address")
    private String bankAddress;

    /**
     * 银行编号类型：SwiftCode/BSB
     */
    @ApiModelProperty(value = "银行编号类型：SwiftCode/BSB")
    @Column(name = "bank_code_type")
    private String bankCodeType;

    /**
     * 银行编号
     */
    @ApiModelProperty(value = "银行编号")
    @Column(name = "bank_code")
    private String bankCode;

    /**
     * 国家编码
     */
    @ApiModelProperty(value = "国家编码")
    @Column(name = "area_country_code")
    private String areaCountryCode;

    /**
     * 是否默认首选：0否/1是
     */
    @ApiModelProperty(value = "是否默认首选：0否/1是")
    @Column(name = "is_default")
    private Boolean isDefault;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    private static final long serialVersionUID = 1L;
}
