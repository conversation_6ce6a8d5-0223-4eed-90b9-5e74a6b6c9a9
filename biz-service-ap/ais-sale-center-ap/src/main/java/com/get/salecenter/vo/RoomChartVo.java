package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Sea
 * @create: 2020/10/27 15:06
 * @verison: 1.0
 * @description:
 */
@Data
public class RoomChartVo {
    /**
     * 每一条柱状图的名字
     */
    @ApiModelProperty(value = "每一条柱状图的名字")
    private String name;

    /**
     * 统计集合list
     */
    @ApiModelProperty(value = "统计集合list")
    private List<ChartCountListVo> chartCountList;
//    /**
//     * 统计集合
//     */
//    @ApiModelProperty(value = "统计集合")
//    private List<Integer> countList;
}
