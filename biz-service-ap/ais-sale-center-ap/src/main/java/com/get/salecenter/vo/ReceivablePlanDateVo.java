package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ReceivablePlanDate;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @Author:cream
 * @Date: 2023/7/26  15:45
 */
@Data
public class ReceivablePlanDateVo extends BaseEntity {

    //===========实体类ReceivablePlanDate=============
    /**
     * 应收计划Id
     */
    @ApiModelProperty(value = "应收计划Id")
    @Column(name = "fk_receivable_plan_id")
    private Long fkReceivablePlanId;

    /**
     * 计划收款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty(value = "计划收款时间")
    @Column(name = "receivable_plan_date")
    private Date receivablePlanDate;
}
