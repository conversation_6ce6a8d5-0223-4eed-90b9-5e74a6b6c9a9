package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AgentUpdateDto extends BaseVoEntity {

    @Valid
    private AgentDto agentVo;

    private List<AgentIdCardDto> agentIdCardVos;

    @Valid
    private MediaAndAttachedDto mediaAndAttachedVo;

 

}
