package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2022/1/10
 * @TIME: 12:17
 * @Description:
 **/
@Data
public class StudentAccommodationVo extends BaseEntity {
    /**
     * 绑定的项目成员
     */
    List<StudentProjectRoleStaffVo> studentProjectRoleStaffDtos;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;
    /**
     * 性别：0女/1男
     */
    private Integer gender;
    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    private Long fkCompanyId;
    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;
    /**
     * 姓（英/拼音）
     */
    @ApiModelProperty(value = "姓（英/拼音）")
    private String lastName;
    /**
     * 名（英/拼音）
     */
    @ApiModelProperty(value = "名（英/拼音）")
    private String firstName;
    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别")
    private String genderName;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;
    /**
     * 英文名
     */
    @ApiModelProperty(value = "英文名")
    private String englishName;
    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;
    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private String staffName;
    /**
     * 国家Name
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;
    /**
     * 州省名称
     */
    @ApiModelProperty(value = "州省名称")
    private String areaStateName;
    /**
     * 城市名称
     */
    @ApiModelProperty(value = "城市名称")
    private String areaCityName;
    /**
     * 渠道名称
     */
    @ApiModelProperty(value = "渠道名称")
    private String channelName;

    @ApiModelProperty(value = "提供商名称")
    private String providerName;
    /**
     * 收取佣金金额
     */
    @ApiModelProperty(value = "收取佣金金额")
    private BigDecimal amountReceivable;
    /**
     * 支付佣金金额（代理）
     */
    @ApiModelProperty(value = "支付佣金金额（代理）")
    private BigDecimal amountPayable;
    /**
     * 住宿费币种
     */
    @ApiModelProperty(value = "住宿费币种名称")
    private String fkCurrencyTypeNumAccommodationName;


    /**
     * 佣金币种
     */
    @ApiModelProperty(value = "佣金币种名称")
    private String fkCurrencyTypeNumCommissionName;
    /**
     * 应收应付IDS
     */
    @ApiModelProperty(value = "应收应付IDS")
    private List<Long> aRAPIds;

    /**
     * 状态：0关闭/1打开
     */
    private String statusName;
    @ApiModelProperty(value = "绑定的项目成员")
    private String projectRoleName;


    @ApiModelProperty(value = "币种")
    private String payableCurrencyTypeName;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal actualPayableAmount;

    @ApiModelProperty(value = "差额")
    private BigDecimal diffPayableAmount;

    /**
     * 付款情况信息显示
     */
    @ApiModelProperty(value = "付款情况信息显示（币种=payableCurrencyTypeName，实付金额=actualPayableAmount，付款时间=gmtCreate）")
    private List<Map<String,Object>> payDetailList;
    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态")
    private Integer arStatus;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态")
    private Integer apStatus;

    /**
     * 应收状态
     */
    @ApiModelProperty(value = "应收状态名称")
    private String arStatusName;

    /**
     * 应付状态
     */
    @ApiModelProperty(value = "应付状态名称")
    private String apStatusName;

    /**
     * 结算状态
     */
    @ApiModelProperty(value = "结算状态")
    private Integer settlementStatus;
    /**
     * 结算状态
     */
    @ApiModelProperty(value = "结算状态名称")
    private String settlementStatusName;


    @ApiModelProperty(value = "应收币种")
    private String receivableCurrencyTypeName;

    //=========实体类StudentAccommodation==========
    private static final long serialVersionUID = 1L;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * 代理Id（业绩绑定）
     */
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 员工Id（业绩绑定，BD）
     */
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 留学住宿编号
     */
    @ApiModelProperty(value = "留学住宿编号")
    @Column(name = "num")
    private String num;
    /**
     * 前往国家Id
     */
    @ApiModelProperty(value = "前往国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 前往州省Id
     */
    @ApiModelProperty(value = "前往州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 前往城市Id
     */
    @ApiModelProperty(value = "前往城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 公寓名称
     */
    @ApiModelProperty(value = "公寓名称")
    @Column(name = "apartment_name")
    private String apartmentName;
    /**
     * 入住日期
     */
    @ApiModelProperty(value = "入住日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "check_in_date")
    private Date checkInDate;
    /**
     * 退房日期
     */
    @ApiModelProperty(value = "退房日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "check_out_date")
    private Date checkOutDate;
    /**
     * 住宿天数
     */
    @ApiModelProperty(value = "住宿天数")
    @Column(name = "duration")
    private Integer duration;
    /**
     * 业务渠道Id/佣金合同方Id
     */
    @ApiModelProperty(value = "业务渠道Id/佣金合同方Id")
    @Column(name = "fk_business_channel_id")
    private Long fkBusinessChannelId;

    @ApiModelProperty(value = "业务提供商Id")
    @Column(name = "fk_business_provider_id")
    private Long fkBusinessProviderId;
    /**
     * 押金支付日期
     */
    @ApiModelProperty(value = "押金支付日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @Column(name = "deposit_date")
    private Date depositDate;
    /**
     * 住宿费币种
     */
    @ApiModelProperty(value = "住宿费币种")
    @Column(name = "fk_currency_type_num_accommodation")
    private String fkCurrencyTypeNumAccommodation;
    /**
     * （每周/每月）住宿金额
     */
    @ApiModelProperty(value = "（每周/每月）住宿金额")
    @Column(name = "accommodation_amount_per")
    private BigDecimal accommodationAmountPer;
    /**
     * 枚举：1每周/2每月
     */
    @ApiModelProperty(value = "枚举：1每周/2每月")
    @Column(name = "accommodation_amount_per_unit")
    private Integer accommodationAmountPerUnit;
    /**
     * 总住宿金额
     */
    @ApiModelProperty(value = "总住宿金额")
    @Column(name = "accommodation_amount")
    private BigDecimal accommodationAmount;
    /**
     * 住宿金额说明
     */
    @ApiModelProperty(value = "住宿金额说明")
    @Column(name = "accommodation_amount_note")
    private String accommodationAmountNote;
    /**
     * 佣金币种
     */
    @ApiModelProperty(value = "佣金币种")
    @Column(name = "fk_currency_type_num_commission")
    private String fkCurrencyTypeNumCommission;
    /**
     * 收取佣金比例%
     */
    @ApiModelProperty(value = "收取佣金比例%")
    @Column(name = "commission_rate_receivable")
    private BigDecimal commissionRateReceivable;
    /**
     * 支付佣金比例%（代理）
     */
    @ApiModelProperty(value = "支付佣金比例%（代理）")
    @Column(name = "commission_rate_payable")
    private BigDecimal commissionRatePayable;
    /**
     * 固定收取佣金金额
     */
    @ApiModelProperty(value = "固定收取佣金金额")
    @Column(name = "fixed_amount_receivable")
    private BigDecimal fixedAmountReceivable;
    /**
     * 固定支付佣金金额（代理）
     */
    @ApiModelProperty(value = "固定支付佣金金额（代理）")
    @Column(name = "fixed_amount_payable")
    private BigDecimal fixedAmountPayable;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentLabelVos;
}
