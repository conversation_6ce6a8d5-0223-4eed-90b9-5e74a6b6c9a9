package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class SettlementPayablePlanVo {

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    @Column(name = "summary")
    private String summary;

    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    @Column(name = "tuition_amount")
    private BigDecimal tuitionAmount;

    /**
     * 费率%(代理)
     */
    @ApiModelProperty(value = "费率%(代理)")
    @Column(name = "commission_rate")
    private BigDecimal commissionRate;

    /**
     * 代理分成比率%
     */
    @ApiModelProperty(value = "代理分成比率%")
    @Column(name = "split_rate")
    private BigDecimal splitRate;

    /**
     * 佣金金额(代理)
     */
    @ApiModelProperty(value = "佣金金额(代理)")
    @Column(name = "commission_amount")
    private BigDecimal commissionAmount;

    /**
     * 定额金额
     */
    @ApiModelProperty(value = "定额金额")
    @Column(name = "fixed_amount")
    private BigDecimal fixedAmount;

    /**
     * 其他金额
     */
    @ApiModelProperty(value = "其他金额")
    @Column(name = "bonus_amount")
    private BigDecimal bonusAmount;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    @Column(name = "payable_amount")
    private BigDecimal payableAmount;

    /**
     * 计划付款时间
     */
    @ApiModelProperty(value = "计划付款时间")
    @Column(name = "payable_plan_date")
    private Date payablePlanDate;

    /**
     * 是否预付，0否/1是
     */
    @ApiModelProperty(value = "是否预付，0否/1是")
    @Column(name = "is_pay_in_advance")
    private Boolean isPayInAdvance;
}
