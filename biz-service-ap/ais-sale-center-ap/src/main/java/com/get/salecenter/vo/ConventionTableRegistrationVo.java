package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionTableRegistration;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2020/8/31 11:34
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "峰会桌台配置-培训桌返回类")
public class ConventionTableRegistrationVo extends BaseEntity {

    /**
     * 峰会报名详细信息
     */
    @ApiModelProperty(value = "峰会报名详细信息")
    private ConventionRegistrationVo conventionRegistrationDto;

    //============实体类ConventionTableRegistration=============
    private static final long serialVersionUID = 1L;
    /**
     * 峰会桌子Id
     */
    @ApiModelProperty(value = "峰会桌子Id")
    @Column(name = "fk_convention_table_id")
    private Long fkConventionTableId;
    /**
     * 峰会报名Id
     */
    @ApiModelProperty(value = "峰会报名Id")
    @Column(name = "fk_convention_registration_id")
    private Long fkConventionRegistrationId;
}
