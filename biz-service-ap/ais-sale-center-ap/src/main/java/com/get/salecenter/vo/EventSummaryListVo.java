package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.EventSummary;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Hardy
 * @create: 2022/6/7 15:35
 * @verison: 1.0
 * @description:
 */
@Data
public class EventSummaryListVo extends BaseEntity {
    @ApiModelProperty("公司名称")
    private String fkCompanyName;

    //============实体类EventSummary=============
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    @Column(name = "event_summary")
    private String eventSummary;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

    private static final long serialVersionUID = 1L;

}
