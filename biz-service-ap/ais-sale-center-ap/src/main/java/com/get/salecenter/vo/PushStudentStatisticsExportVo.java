package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2022/4/12
 * @TIME: 11:01
 * @Description:
 **/
@Data
public class PushStudentStatisticsExportVo {
    /**
     * 代理省份名称
     */
    @ApiModelProperty(value = "代理省份名称")
    private String fkAreaStateName;
    /**
     * 新建学生
     */
    @ApiModelProperty(value = "新建学生")
    private Integer createCount;


    /**
     * 定校量（按学生）
     */
    @ApiModelProperty(value = "定校量（按学生）")
    private Integer confirmationCountByStudent;
    /**
     * 成功入学量（按学生）
     */
    @ApiModelProperty(value = "成功入学量（按学生）")
    private Integer successCountByStudent;

    /**
     * 定校量转化率
     */
    @ApiModelProperty(value = "定校量转化率）")
    private String confirmationConversionRate;
    /**
     * 成功入学量转化率
     */
    @ApiModelProperty(value = "成功入学量转化率")
    private String successConversionRate;
}
