package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2023/8/10 16:44
 * @verison: 1.0
 * @description:
 */
@Data
public class AmountPayNoticeDto implements Serializable {

    @NotNull(message = "房型id")
    @ApiModelProperty(value = "房型id",required = true)
    private Long id;

    @ApiModelProperty(value = "参会人id")
    private Long fkConventionPersonId;

    /**
     * 到店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "到店日期")
    @ApiModelProperty(value = "到店日期",required = true)
    private Date checkInTime;

    /**
     * 离店日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @NotNull(message = "离店日期")
    @ApiModelProperty(value = "离店日期",required = true)
    private Date checkOutTime;

}
