package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Hardy
 * @create: 2022/4/8 10:22
 * @verison: 1.0
 * @description:
 */
@Data
public class AgentExportVo {

    /**
     * 所属公司
     */
    @ApiModelProperty(value = "所属公司")
    private String companyName;

    /**
     * 代理编号
     */
    @ApiModelProperty(value = "编号")
    @Column(name = "num")
    private String num;

    /**
     * BD编号/名称  需要拼接
     */
    @ApiModelProperty(value = "BD编号/名字")
    private String bdCodeAndName;

    /**
     * 父编号和名  需要拼接
     */
    @ApiModelProperty(value = "父编号/名称")
    private String parentAgentNumAndName;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String name;

    /**
     * 名称备注
     */
    @ApiModelProperty(value = "名称备注/原公司名")
    private String nameNote;

    /**
     * 大区名称  areaRegionDtos拼接
     */
    @ApiModelProperty(value = "大区")
    private String areaRegionName;

    /**
     * 大区名称  countryName/stateName 拼接
     */
    @ApiModelProperty(value = "区域")
    private String countryAndStateName;

    @ApiModelProperty("代理学生数")
    private Integer studentNum;

    @ApiModelProperty("首次合同创建时间")
    private String firstContractTimeStr;

    /**
     * 是否生效
     */
    @ApiModelProperty(value = "是否生效")
    private String isActiveName;

    /**
     * 是否渠道代理
     */
    @ApiModelProperty(value = "是否渠道代理")
    private String isCustomerChannelName;

    @ApiModelProperty("创建时间")
    private String gmtCreateStr;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    /**
     * 返佣比例备注，换行符拼接
     */
    @ApiModelProperty(value = "返佣比例备注")
    private String returnCommissionRateNote;

    @ApiModelProperty(value = "代理标签")
    private String agentLabelNames;

}
