package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/2/15 12:18
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionSummaryVo {

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("公司名称")
    private String fkCompanyName;

    @ApiModelProperty("公司id")
    private Long fkStudentId;

    @ApiModelProperty("名称")
    private String studentName;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("姓")
    private String lastName;

    @ApiModelProperty("名")
    private String firstName;

    @ApiModelProperty("提成状态")
    private Integer commissionStatus = 1;

    @ApiModelProperty("提成状态")
    private String commissionStatusName;

    @ApiModelProperty("收款状态")
    private Integer receiveStatus;

    @ApiModelProperty("收款状态")
    private String receiveStatusName;

    @ApiModelProperty("结算状态")
    private Integer settlementStatus;

    @ApiModelProperty("有无加申")
    private Integer addAppStatus;

    @ApiModelProperty("有无佣金")
    private Integer institutionStatus;

    @ApiModelProperty("有无学习计划")
    private Integer offerItemStatus;

    @ApiModelProperty("结案按钮标志")
    private Boolean finishedButtonType = false;

    @ApiModelProperty("取消结算按钮标志")
    private Boolean cancelSettlementButtonType = false;

    @ApiModelProperty("提成步骤key")
    private String fkStaffCommissionStepKey;

    @ApiModelProperty("是否匹配规则")
    private Integer policyStatus;

    private List<StaffCommissionDatasVo<Integer>> staffCommissionSteps;
}
