package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/11/16
 * @TIME: 10:30
 * @Description: 绑定项目成员
 **/

@Data
public class ClientProjectRoleStaffVo extends BaseEntity {

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String projectName;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目目标名称")
    private String projectTargetName;

    /**
     * 角色名称
     */
    @ApiModelProperty(value = "角色名称")
    private String roleName;
    /**
     * 角色key
     */
    @ApiModelProperty(value = "角色key")
    private String roleKey;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private String staffName;

    /**
     * 员工id串
     */
    private String staffIdStr;

    /**
     * 绑定员工名
     */
    @ApiModelProperty(value = "绑定员工名，逗号分隔")
    private String staffNameStr;

    /**
     * 员工id
     */
    private Long fkStaffId;

    //==========实体类StudentProjectRoleStaff===========
    private static final long serialVersionUID = 1L;
    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    @Column(name = "fk_table_name")
    private String fkTableName;
    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;
    /**
     * 学生项目角色Id
     */
    @ApiModelProperty(value = "学生项目角色Id")
    @Column(name = "fk_student_project_role_id")
    private Long fkStudentProjectRoleId;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
    /**
     * 绑定时间
     */
    @ApiModelProperty(value = "绑定时间")
    @Column(name = "active_date")
    private Date activeDate;
    /**
     * 取消绑定时间（下次绑定时，需要重新建立记录）
     */
    @ApiModelProperty(value = "取消绑定时间（下次绑定时，需要重新建立记录）")
    @Column(name = "unactive_date")
    private Date unactiveDate;

}
