package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/2/2
 * @TIME: 16:30
 * @Description:
 **/
@Data
public class SystemPageVo1 {

    @ApiModelProperty(value = "指令")
    private Byte command;

    @ApiModelProperty(value = "学生总数")
    private Long studentTotalSum;

//    @ApiModelProperty(value = "学生总申请数")
//    private Long studentTotalItemSum;

    @ApiModelProperty(value = "学生申请记录")
    private List<StudentCountVo> studentCountRecords;

    @ApiModelProperty(value = "国家学生数")
    private List<WorldHistogramVo> countryStudentCount;

//    @ApiModelProperty(value = "国家学生申请数")
//    private List<WorldHistogramDto> countryStudentItemCount;

    @ApiModelProperty(value = "州省学生数")
    private List<WorldHistogramVo> stateStudentCount;

    @ApiModelProperty(value = "世界地图")
    private List<WorldMapVo> worldMapDtos;
    //    @ApiModelProperty(value = "世界地图申请数")
//    private List<WorldMapDto> worldMapItemDtos;
    @ApiModelProperty(value = "国家经纬度")
    private JSONArray nationallonlat;

    @ApiModelProperty(value = "省份经纬度")
    private JSONObject provinceslonlat;
}
