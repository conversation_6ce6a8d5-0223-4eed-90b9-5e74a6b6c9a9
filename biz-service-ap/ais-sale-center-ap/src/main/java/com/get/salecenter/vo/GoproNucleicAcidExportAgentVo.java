package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2022/6/6 11:09
 * @verison: 1.0
 * @description:
 */
@Data
public class GoproNucleicAcidExportAgentVo {

    @ApiModelProperty(value = "序号")
    private Integer orderId;

    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty(value = "性别：0女/1男")
    private String genderName;

    @ApiModelProperty("所在机构")
    private String institution;

    @ApiModelProperty("联系电话")
    private String mobile;

    /**
     * 证件类型：身份证/护照
     */
    @ApiModelProperty(value = "证件类型：身份证/护照")
    private String identityType;

    @ApiModelProperty("身份证号")
    private String identityCard;

    @ApiModelProperty("对接的GEA区域经理")
    private String bdName;

    @ApiModelProperty("出发国家")
    private String areaCountryName;

    @ApiModelProperty("出发省份")
    private String areaStateName;

    @ApiModelProperty("出发城市")
    private String areaCityName;

    /**
     * 到达日期
     */
//    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "到达日期")
    private String arrivalDate;

    /**
     * 交通班次编号
     */
    @ApiModelProperty(value = "交通班次编号")
    private String transportationCode;

    /**
     * 行程安排
     */
    @ApiModelProperty(value = "行程安排")
    private String scheduling;
}
