package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2023/8/18 16:21
 * @verison: 1.0
 * @description:
 */
@Data
public class InvoiceInfoSaveDto implements Serializable {

    @NotNull
    @ApiModelProperty("参会人id")
    private Long fkConventionPersonId;

    @Valid
    @NotEmpty
    @ApiModelProperty("发票信息")
    private List<InvoiceInfoDto> invoiceInfoVos;
}
