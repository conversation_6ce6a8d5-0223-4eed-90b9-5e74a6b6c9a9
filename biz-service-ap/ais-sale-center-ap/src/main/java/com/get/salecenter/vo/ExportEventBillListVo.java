package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

@Data
public class ExportEventBillListVo {
    @ApiModelProperty("公司名称")
    private String fkCompanyName;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("可分配余额")
    private String differenceEventAmountCurrency;

    @ApiModelProperty("学校提供商(收款方)")
    private String fkInstitutionProviderName;

    @ApiModelProperty("业务国家/地区")
    private String fkAreaCountryName;

    @ApiModelProperty("活动费用币种")
    private String eventCurrencyTypeNum;

    @ApiModelProperty("活动费用金额")
    private String eventAmountCurrency;

    @ApiModelProperty("发起invoice币种")
    private String invoiceCurrencyTypeNum;

    @ApiModelProperty("发起invoice金额")
    private String invoiceAmountCurrency;

    @ApiModelProperty("invoice名目")
    private String invoiceSummary;

    @ApiModelProperty("invoice收件人")
    private String invoiceContactPerson;

    @ApiModelProperty("invoice收件电子邮箱")
    private String invoiceContactEmail;

    @ApiModelProperty(value = "活动年份")
    private Integer eventYear;

    @ApiModelProperty("活动摘要")
    private String fkEventSummaryName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty("应收币种")
    private String receivableCurrencyTypeNum;

    @ApiModelProperty("应收金额")
    private String receivableAmountCurrency;

    @ApiModelProperty("实收金额")
    private String actualReceivableAmountCurrency;

    @ApiModelProperty("应收未收金额")
    private String differenceAmountCurrency;

    @ApiModelProperty(value = "发票号")
    private String fkInvoiceNum;

    @ApiModelProperty("通知人")
    private String fkStaffNoticeName;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date gmtCreate;

    @ApiModelProperty("更新人")
    private String gmtModifiedUser;

    @ApiModelProperty("更新时间")
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    private Date gmtModified;

}

