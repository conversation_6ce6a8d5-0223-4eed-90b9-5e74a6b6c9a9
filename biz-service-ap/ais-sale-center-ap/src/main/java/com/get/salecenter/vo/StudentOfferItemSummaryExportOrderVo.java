package com.get.salecenter.vo;

import com.get.common.annotion.ExportInternationalization;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @author: Hardy
 * @create: 2022/12/5 18:23
 * @verison: 1.0
 * @description:
 */
@Data
public class StudentOfferItemSummaryExportOrderVo {

    @ExportInternationalization(valueZh= "按申请计划创建时间（最新排最前）", valueEn = "By Application Plan Creation Time(Latest List First)")
    @ApiModelProperty(value = "按申请计划创建时间（最新排最前）")
    private Date gmtCreate;

    @ExportInternationalization(valueZh = "按学生id（最新排最前）", valueEn = "By Student ID(Latest List First)")
    @ApiModelProperty(value = "按学生id（最新排最前）")
    private Long fkStudentId;

}
