package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2022/3/14 17:09
 * @verison: 1.0
 * @description:
 */
@Data
public class PayablePlanExportVo {

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "应付类型名称")
    private String fkTypeName;

    @ApiModelProperty(value = "申请计划编号")
    private String offerItemNum;

    @ApiModelProperty(value = "学生信息")
    private String studentNameInfo;

    @ApiModelProperty(value = "学生名称")
    private String studentName;

    @ApiModelProperty(value = "佣金结算标记")
    private String commissionMark;

    @ApiModelProperty(value = "学生名称(英文)")
    private String studentLastName;

    @ApiModelProperty(value = "学生生日")
    private String studentBirthday;

    @ApiModelProperty(value = "性别")
    private String genderName;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "BD+代理编号")
    private String bdAgentNum;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    @ApiModelProperty(value = "业务信息")
    private String businessInformation;

    @ApiModelProperty(value = "学校")
    private String fkInstitutionName;

    @ApiModelProperty(value = "课程名称")
    private String fkCourseName;

    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String openingTime;


    @ApiModelProperty("申请步骤状态")
    private String stepName;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 币种名称
     */
    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;
    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;

    @ApiModelProperty(value = "代理佣金费率")
    private BigDecimal commissionRate;

    @ApiModelProperty(value = "代理分成比率%")
    private BigDecimal splitRate;

    @ApiModelProperty(value = "代理佣金金额")
    private BigDecimal commissionAmount;

    @ApiModelProperty(value = "定额金额")
    private BigDecimal fixedAmount;

    @ApiModelProperty(value = "奖励金额")
    private BigDecimal bonusAmount;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    /**
     * 实付金额
     */
    @ApiModelProperty(value = "实付金额")
    private BigDecimal actualPayableAmount;

    /**
     * 差额
     */
    @ApiModelProperty(value = "差额")
    private BigDecimal diffPayableAmount;

    /**
     * 计划付款时间
     */
    @ApiModelProperty(value = "计划付款时间")
    private Date payablePlanDate;

    /**
     * 状态：0关闭/1打开/2完成
     */
    @ApiModelProperty(value = "状态")
    private String statusName;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @ApiModelProperty(value = "代理标签")
    private String agentLabelNames;
}
