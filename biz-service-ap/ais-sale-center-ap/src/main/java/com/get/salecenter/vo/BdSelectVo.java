package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 代理在线申请表单bd下拉框DTO
 */
@Data
public class BdSelectVo {
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 全称
     */
    @ApiModelProperty(value = "全称")
    private String fullName;
    /**
     * BD编号（4位）
     */
    @ApiModelProperty(value = "BD编号（4位）")
    private String bdCode;
    /**
     * 大区Id，支持多选（格式为：1,2,3），暂为属性字段，可不填
     */
    @ApiModelProperty(value = "大区Id，支持多选（格式为：1,2,3），暂为属性字段，可不填")
    private String fkAreaRegionId;
    /**
     * 是否为国内代理
     */
    @ApiModelProperty(value = "是否为国内代理")
    private Boolean isChinaAgent;

}
