package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: Sea
 * @create: 2020/8/27 11:35
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionTableDto extends BaseVoEntity {

    /**
     * * 峰会Id
     */
    @NotNull(message = "峰会Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会Id", required = true)
    private Long fkConventionId;

    /**
     * 桌子类型key
     */
    @NotBlank(message = "桌子类型key不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "桌子类型key", required = true)
    private String fkTableTypeKey;

    /**
     * 前序号
     */
    @NotBlank(message = "前序号不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "前序号", required = true)
    private String preNum;

    /**
     * 桌子编号
     */
    @ApiModelProperty(value = "桌子编号")
    private String tableNum;

    /**
     * 座位数
     */
    @NotNull(message = "座位数不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "座位数", required = true)
    private Integer seatCount;

    /**
     * 是否主席台：0否/1是，默认否
     */
    @NotNull(message = "是否主席台不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "是否主席台：0否/1是，默认否", required = true)
    private Integer isVip;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

    //-----自定义
    /**
     * 桌台数
     */
    @ApiModelProperty(value = "桌台数")
    private Integer tableCount;

    /**
     * 1:已满/0:未满
     */
    @ApiModelProperty(value = "1:已满/0:未满")
    private Integer isFull;

    /**
     * 参会人员名称
     */
    @ApiModelProperty(value = "参会人员名称")
    private String personName;

    /**
     * 展位名称
     */
    @ApiModelProperty(value = "展位名称")
    private String boothName;

    /**
     * 查询重复人员
     */
    @ApiModelProperty(value = "0否/1是")
    private Integer searchRepeatPerson;

    /**
     * 剩余座位数
     */
    @ApiModelProperty(value = "剩余座位数")
    private Integer remainingSeatCount;

   
}

