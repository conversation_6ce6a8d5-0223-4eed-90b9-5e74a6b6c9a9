package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/23
 * @TIME: 12:39
 * @Description:
 **/
@Data
public class PayablePlanNewDto extends BaseVoEntity  {

    /**
     * 应收类型关键字，枚举，如：m_student_offer_item
     */
//    @NotNull(message = "应收类型不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "应收计划类型")
    private String fkTypeKey;

    /**
     * 国家Id
     */
    @ApiModelProperty(value="国家Id")
    private Long fkAreaCountryIds;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生信息（中英名称/生日）")
    private String studentName;

    /**
     * 提供商名称
     */
    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "渠道信息（渠道/学校提供商）")
    private String bzoName;

    @ApiModelProperty(value = "业务信息（学校/课程/保险/住宿）")
    private String bziName;

    @ApiModelProperty(value = "业务信息补充（学校/课程）")
    private String bziNameSupplement;

    @ApiModelProperty(value = "发票编号")
    private String fkInvoiceNums;

    @ApiModelProperty(value = "收齐状态：0未付/1已付部分/2已付齐/3未付齐")
    private Integer payableStatus;

    @ApiModelProperty("创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 是否预付，0否/1是
     */
    @ApiModelProperty(value = "是否预付，0否/1是")
    private Integer isPayInAdvance;

    @ApiModelProperty(value = "学生Id")
    private Long fkStudentId;

    @ApiModelProperty(value = "是否为发票详情调用flag true:发票调用保证实时性  false：非发票调用 使用数据仓库")
    private Boolean isInvoiceFlag = false;

    @ApiModelProperty(value = "导出ids")
    private List<Long> exportIds;

    @ApiModelProperty(value = "学生佣金结算标记关键字")
    private String commissionMark;

    @ApiModelProperty(value = "收款状态  0:未收 1：部分已收 2：已收齐")
    private Integer tradeStatus;

    @ApiModelProperty(value = "应付金额不为0开关")
    private Boolean payableAmountUnequalZeroFlag;

  

}
