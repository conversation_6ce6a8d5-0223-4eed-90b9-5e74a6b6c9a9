package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 更新Issue order Id Vo类
 *
 * <AUTHOR>
 * @date 2022/3/12 11:17
 */
@Data
public class IssueRpaDto {

    @NotNull(message = "学习计划id不能为空")
    @ApiModelProperty(value = "学习计划id")
    private Long studentItemId;

    @NotNull(message = "Issue order Id不能为空")
    @ApiModelProperty(value = "Issue order Id")
    private Long fkIssueRpaOrderId;

}
