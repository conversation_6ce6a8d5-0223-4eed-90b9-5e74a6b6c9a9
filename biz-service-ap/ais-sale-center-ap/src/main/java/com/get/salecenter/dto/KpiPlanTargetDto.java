package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2024/4/18
 * @TIME: 16:52
 * @Description:
 **/
@Data
public class KpiPlanTargetDto extends BaseVoEntity {
    @ApiModelProperty(value = "KPI方案组别子项Id")
    @NotNull(message = "KPI方案组别子项Id不能为空", groups = {Add.class})
    private Long fkKpiPlanGroupItemId;

//    @ApiModelProperty(value = "员工Id")
//    @NotNull(message = "员工Id不能为空", groups = {Add.class})
//    private Long fkStaffId;

    @ApiModelProperty(value = "目标设置（成功入学）")
    private Integer targetEnrolled;

    @ApiModelProperty(value = "KPI方案考核人员Id")
    @NotNull(message = "KPI方案考核人员Id不能为空", groups = {Add.class})
    private Long fkKpiPlanStaffId;

  
}
