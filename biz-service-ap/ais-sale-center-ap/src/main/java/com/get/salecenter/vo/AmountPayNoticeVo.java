package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @author: Hardy
 * @create: 2023/8/11 10:26
 * @verison: 1.0
 * @description:
 */
@Data
public class AmountPayNoticeVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("提示状态:0新增/1编辑未支付/2编辑已支付")
    private Integer noticeType;

    @ApiModelProperty("价格：x/晚")
    private String amount;

    @ApiModelProperty("总价")
    private String totalAmount;

    @ApiModelProperty("数量")
    private String count;

}
