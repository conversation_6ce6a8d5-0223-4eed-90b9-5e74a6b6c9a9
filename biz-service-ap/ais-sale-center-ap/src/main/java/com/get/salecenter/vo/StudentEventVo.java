package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StudentEvent;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2020/11/10
 * @TIME: 16:07
 * @Description:
 **/
@Data
public class StudentEventVo extends BaseEntity {

    @ApiModelProperty(value = "学生事件类型名称")
    private String fkStudentEventTypeName;

    //==========实体类StudentEvent==============
    private static final long serialVersionUID = 1L;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * 学生事件类型Id
     */
    @ApiModelProperty(value = "学生事件类型Id")
    @Column(name = "fk_student_event_type_id")
    private Long fkStudentEventTypeId;
    /**
     * 事件内容
     */
    @ApiModelProperty(value = "事件内容")
    @Column(name = "description")
    private String description;
}
