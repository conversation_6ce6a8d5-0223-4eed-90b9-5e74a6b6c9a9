package com.get.salecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/3/29
 * @TIME: 11:31
 * @Description:学生申请计划统计VO
 **/
@Data
public class StatisticsDto {
    /**
     * 学生创建开始时间
     */
    @ApiModelProperty(value = "学生创建开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;
    /**
     * 学生创建结束时间
     */
    @ApiModelProperty(value = "学生创建结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 开始时间(副)
     */
    @ApiModelProperty(value = "开始时间(副)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTimeDeputy;
    /**
     * 结束时间(副)
     */
    @ApiModelProperty(value = "结束时间(副)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTimeDeputy;
    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startOpeningTime;
    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endOpeningTime;

    /**
     * 大区Id
     */
    @ApiModelProperty(value = "大区Id")
    private Long fkAreaRegionId;
    /**
     * 申请学校Id
     */
    @ApiModelProperty(value = "申请学校Id")
    private Long fkInstitutionId;
    /**
     * 申请国家ID列表，搜索过滤条件
     */
    @ApiModelProperty(value = "申请国家ID列表,搜索过滤条件")
    private List<Long> fkAreaCountryIds;

    /**
     * 代理国家ID
     */
    @ApiModelProperty("代理国家ID")
    private Long fkAreaCountryIdAgent;

    /**
     * 代理省份Id列表
     */
    @ApiModelProperty("代理省份Id列表")
    private List<Long> fkAreaStateIdAgentList;

    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private String agentName;

    /**
     * 国家ID列表,员工（登录人）业务国家
     */
    @ApiModelProperty(value = "国家ID列表,员工（登录人）业务国家")
    private List<Long> fkAreaCountryIdList;

    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    private Long fkCompanyId;

    /**
     * 公司ID列表
     */
    @ApiModelProperty(value = "公司列表")
    private List<Long> fkCompanyIdList;

    /**
     * 年份一
     */
    @ApiModelProperty(value = "年份一")
    private Integer yearOne;
    /**
     * 月份一
     */
    @ApiModelProperty(value = "月份一")
    private Integer monthOne;
    /**
     * 旬一
     */
    @ApiModelProperty(value = "旬一")
    private Integer tenDaysOne;

    /**
     * 年份二
     */
    @ApiModelProperty(value = "年份二")
    private Integer yearTwo;

    /**
     * 月份二
     */
    @ApiModelProperty(value = "月份二")
    private Integer monthTwo;

    /**
     * 旬二
     */
    @ApiModelProperty(value = "旬二")
    private Integer tenDaysTwo;

    /**
     * bd名称
     */
    @ApiModelProperty(value = "bd名称")
    private String bdName;

    /**
     * BD编号
     */
    @ApiModelProperty(value = "BD编号")
    private String bdCode;

    /**
     * 绑定BD员工ids
     */
    @ApiModelProperty(value = "绑定BD员工ids")
    private List<Long> fkStaffIds;


    /**
     * 员工以及旗下员工所创建的代理ids
     */
    @ApiModelProperty(value = "员工以及旗下员工所创建的代理ids")
    private List<Long> staffFollowerIds;


    /**
     * 查询类型：1-查全部，范围时间格式化至年月日（月报小计、季报小计、年报小计）；
     *          2-按申请国家分组，范围时间格式化至年月日（月报、季报、年报、国家业绩）；
     *          3-按申请国家分组，范围时间格式化至时分秒（周报）；
     *          4-查全部，范围时间格式化至时分秒（周报小计）；
     *          5-业绩统计总计；
     *          6-按代理省份分组（代理省份送生表）；
     */
    @ApiModelProperty(value="查询类型：1-查全部，范围时间格式化至年月日（月报小计、季报小计、年报小计）；" +
            "2-按申请国家分组，范围时间格式化至年月日（月报、季报、年报、业绩统计）；" +
            "3-按申请国家分组，范围时间格式化至时分秒（周报）；" +
            "4-查全部，范围时间格式化至时分秒（周报小计）；" +
            "5-业绩统计总计；" +
            "6-按代理省份分组（代理省份送生表）；")
    private Integer queryType;

    /**
     *是否需要进行延迟执行，用于周报延迟执行，减缓服务器cpu过高问题
     */
    @ApiModelProperty(value = "是否需要进行延迟执行，用于周报延迟执行，减缓服务器cpu过高问题")
    private Boolean isDelay;

    /**
     * 是否为跨月查询
     */
    @ApiModelProperty(value="是否为跨月查询")
    private Boolean isSpanQuery;

    /**
     * 所属集团Id
     */
    @ApiModelProperty(value = "所属集团Id")
    private List<Long> fkInstitutionGroupIds;

    /**
     * 业绩统计GEA定校量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计GEA定校量相关步骤列表枚举")
    private List<String> geaConfirmationStatisticsStepList;

    /**
     * 业绩统计IAE定校量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计IAE定校量相关步骤列表枚举")
    private List<String> iaeConfirmationStatisticsStepList;

    /**
     * 业绩统计GEA成功量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计GEA成功量相关步骤列表枚举")
    private List<String> geaSuccessStatisticsStepList;

    /**
     * 业绩统计IAE成功量相关步骤列表枚举
     */
    @ApiModelProperty(value = "业绩统计IAE成功量相关步骤列表枚举")
    private List<String> iaeSuccessStatisticsStepList;

}
