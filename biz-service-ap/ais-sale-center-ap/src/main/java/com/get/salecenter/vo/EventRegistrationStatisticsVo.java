package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.EventRegistration;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2024/3/19 15:41
 * @verison: 1.0
 * @description:
 */
@Data
public class EventRegistrationStatisticsVo extends BaseEntity {

    @ApiModelProperty(value = "学校提供商名称")
    private String fkInstitutionProviderName;

    @ApiModelProperty(value = "活动主题")
    private String eventTheme;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "活动时间")
    private Date eventTime;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "活动结束时间")
    private Date eventTimeEnd;

    @ApiModelProperty(value = "状态：0计划/1结束/2取消")
    private Integer eventStatus;

    @ApiModelProperty(value = "活动举办城市Id")
    private Long fkAreaCityIdHold;

    @ApiModelProperty(value = "活动举办城市Name")
    private String areaCityNameHold;

    //============实体类EventRegistration================
    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
    @Column(name = "fk_event_id")
    private Long fkEventId;

    @ApiModelProperty(value = "枚举状态：0不参加/1参加/2待定")
    @Column(name = "status")
    private Integer status;

    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @Column(name = "fk_institution_provider_id")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    private static final long serialVersionUID = 1L;

}
