package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:cream
 * @Date: 2023/8/23  12:36
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentBusinessInfoVo {

    @ApiModelProperty("激活代理数量")
    private Long activeAgentCount;

    @ApiModelProperty("未激活代理数量")
    private Long unActiveAgentCount;

    @ApiModelProperty("已过期合同数量")
    private Long contractExpiredCount;

    /**
     * 兼容旧版本的构造函数
     */
    public AgentBusinessInfoVo(Long activeAgentCount, Long unActiveAgentCount) {
        this.activeAgentCount = activeAgentCount;
        this.unActiveAgentCount = unActiveAgentCount;
        this.contractExpiredCount = 0L;
    }
}
