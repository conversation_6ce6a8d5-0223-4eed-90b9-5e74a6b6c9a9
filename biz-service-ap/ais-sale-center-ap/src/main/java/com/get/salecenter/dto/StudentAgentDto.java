package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/11/9
 * @TIME: 16:17
 * @Description:
 **/
@Data
public class StudentAgentDto extends BaseVoEntity {

    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @NotNull(message = "学生Id不能为空", groups = {Add.class, Update.class})
    private Long fkStudentId;

    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    @NotNull(message = "代理Id不能为空", groups = {Add.class, Update.class})
    private Long fkAgentId;

    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    private Boolean isActive;

    /**
     * 绑定时间
     */
    @ApiModelProperty(value = "绑定时间")
    private Date activeDate;

    /**
     * 取消绑定时间（下次绑定时，需要重新建立记录）
     */
    @ApiModelProperty(value = "取消绑定时间（下次绑定时，需要重新建立记录）")
    private Date unactiveDate;

    
}
