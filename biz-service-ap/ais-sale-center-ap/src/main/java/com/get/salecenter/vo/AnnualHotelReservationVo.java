package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * @author: Hardy
 * @create: 2021/6/25 14:22
 * @verison: 1.0
 * @description:
 */
@Data
public class AnnualHotelReservationVo {
    /**
     * 回执码
     */
    @ApiModelProperty(value = "回执码")
    private String receiptCode;

//    /**
//     * 中文姓
//     */
//    @ApiModelProperty(value = "中文姓")
//    private String lastNameChn;
//
//    /**
//     * 中文名
//     */
//    @ApiModelProperty(value = "中文名")
//    private String firstNameChn;

    /**
     * 中文名
     */
    @ApiModelProperty(value = "中文名")
    private String nameChn;

//    /**
//     * 英文姓/拼音姓
//     */
//    @ApiModelProperty(value = "英文姓/拼音姓")
//    private String lastName;
//
//    /**
//     * 英文名/拼音名
//     */
//    @ApiModelProperty(value = "英文名/拼音名")
//    private String firstName;

    /**
     * 英文名/拼音名
     */
    @ApiModelProperty(value = "英文名/拼音名")
    private String name;

    /**
     * 入住时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "入住时间")
    private Date checkInDate;

    /**
     * 退房时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "退房时间")
    private Date checkOutDate;

    /**
     * 房间类型名称
     */
    @ApiModelProperty(value = "房间类型名称")
    private String roomTypeName;

    @ApiModelProperty(value = "酒店房型Id")
    @Column(name = "fk_convention_hotel_id")
    private Long fkConventionHotelId;

    /**
     * 机构名
     */
    @ApiModelProperty(value = "机构名")
    private String institutionName;

    /**
     * 展位名
     */
    @ApiModelProperty(value = "展位名")
    private String boothName;

    /**
     * 展位报名Id
     */
    @ApiModelProperty(value = "展位报名Id")
    private Long conventionRegistrationId;


    /**
     * 参加人电邮
     */
    @ApiModelProperty(value = "参加人电邮")
    private String email;

    /**
     * 参加人电话
     */
    @NotBlank(message = "参加人电话不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "参加人电话", required = true)
    private String tel;

    /**
     * 性别：0女/1男
     */
    @NotNull(message = "性别不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "性别：0女/1男", required = true)
    private Integer gender;

    /**
     * 参加人职位
     */
    @ApiModelProperty(value = "参加人职位")
    private String title;

    /**
     * 参加人id
     */
    @ApiModelProperty(value = "参加人id")
    private Long id;

    /**
     * 护照号
     */
    @ApiModelProperty(value = "护照号")
    private String passportNum;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    /**
     * 州省名
     */
    @ApiModelProperty(value = "州省名")
    private String fkAreaStateName;

    /**
     * 城市名
     */
    @ApiModelProperty(value = "城市名")
    private String fkAreaCityName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 是否出席：0否/1是
     */
    @ApiModelProperty(value = "是否参加晚宴：0否/1是")
    @Column(name = "is_attend_dinner")
    private Boolean isAttendDinner;
    /**
     * 是否出席：0否/1是
     */
    @ApiModelProperty(value = "是否代订酒店：0否/1是")
    @Column(name = "is_book_hotel")
    private Boolean isBookHotel;
}
