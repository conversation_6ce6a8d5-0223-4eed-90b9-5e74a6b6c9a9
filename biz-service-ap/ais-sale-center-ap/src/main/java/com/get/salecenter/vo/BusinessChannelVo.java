package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.BusinessChannel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2022/1/7
 * @TIME: 15:58
 * @Description:
 **/
@Data
public class BusinessChannelVo extends BaseEntity {
    @ApiModelProperty(value = "公司名称")
    private String companyName;


    /**
     * 业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "业务类型名称")
    private String fkTypeKeyName;


    @ApiModelProperty(value = "计划id")
    private Long planId;

    @ApiModelProperty(value = "全称")
    private String fullName;

    //===========实体类BusinessChannel============
    private static final long serialVersionUID = 1L;

    /**
     * 业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "业务类型关键字，枚举：m_student_insurance留学保险/m_student_accommodation留学住宿")
    @Column(name = "fk_type_key")
    private String fkTypeKey;
    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    @Column(name = "num")
    private String num;
    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    @Column(name = "name")
    private String name;
    /**
     * 中文名称
     */
    @ApiModelProperty(value = "中文名称")
    @Column(name = "name_chn")
    private String nameChn;
    /**
     * 是否激活：0否/1是
     */
    @ApiModelProperty(value = "是否激活：0否/1是")
    @Column(name = "is_active")
    private Boolean isActive;
}
