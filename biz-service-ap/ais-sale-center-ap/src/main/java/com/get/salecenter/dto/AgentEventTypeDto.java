package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @DATE: 2020/10/20
 * @TIME: 10:52
 * @Description:
 **/
@Data
public class AgentEventTypeDto extends BaseVoEntity {

    private static final long serialVersionUID = 1L;
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称", required = true)
    @NotBlank(message = "类型名称不能为空", groups = {Add.class, Update.class})
    @Column(name = "type_name")
    private String typeName;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;
    /**
     * 关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;
}
