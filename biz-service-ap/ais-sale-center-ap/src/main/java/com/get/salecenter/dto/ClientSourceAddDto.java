package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @author: Hardy
 * @create: 2023/12/12 17:29
 * @verison: 1.0
 * @description:
 */
@Data
public class ClientSourceAddDto extends BaseVoEntity {

    /**
     * 客户Id
     */
    @NotNull(message = "客户Id", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "客户Id",required = true)
    @Column(name = "fk_client_id")
    private Long fkClientId;

    /**
     * 表名：crm_contract_num/bms_student_num
     */
    @NotBlank(message = "来源类型", groups = {Add.class,Update.class})
    @ApiModelProperty(value = "表名：crm_contract_num/bms_student_num")
    @Column(name = "fk_table_name")
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;

    /**
     * 表值
     */
    @ApiModelProperty(value = "表值")
    @Column(name = "fk_table_value")
    private String fkTableValue;

    /**
     * 渠道代理Id
     */
    @ApiModelProperty(value = "渠道代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;

    /**
     * 学生编号
     */
    @ApiModelProperty(value = "学生编号")
    private String studentNum;

    private static final long serialVersionUID = 1L;
}
