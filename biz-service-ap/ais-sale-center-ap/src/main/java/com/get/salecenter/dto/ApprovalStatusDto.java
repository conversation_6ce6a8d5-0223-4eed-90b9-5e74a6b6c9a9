package com.get.salecenter.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ApprovalStatusDto extends BaseEntity {

    @ApiModelProperty("学生Id")
    private List<Long> fkStudentIdList;

    @ApiModelProperty("学生Id")
    private Long fkStudentId;

    @ApiModelProperty("审批意见")
    private String approvalOpinion;

    @ApiModelProperty("审批状态：0新申请/1通过/2拒绝/-1删除")
    private Integer approvalStatus;

    @ApiModelProperty(value = "申请国家")
    private Long countryId;
}
