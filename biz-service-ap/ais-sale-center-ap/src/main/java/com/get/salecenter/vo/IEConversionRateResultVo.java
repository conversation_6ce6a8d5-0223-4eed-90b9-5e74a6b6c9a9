package com.get.salecenter.vo;

import com.get.common.result.ListResponseBo;
import com.get.common.result.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/1/19
 * @TIME: 17:03
 * @Description:
 **/
@Data
public class IEConversionRateResultVo<T> extends ListResponseBo<T> {
    @ApiModelProperty(value = "动态字段（动态表头）")
    private List<DynamicFieldVo> fieldDtos;

    public IEConversionRateResultVo(Collection<T> list, Page page , List<DynamicFieldVo> fieldDtos) {
        super(list,page);
        this.fieldDtos = fieldDtos;
    }
}
