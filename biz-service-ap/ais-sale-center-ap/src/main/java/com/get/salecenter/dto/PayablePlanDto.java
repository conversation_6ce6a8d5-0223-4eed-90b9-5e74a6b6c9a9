package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/11/23
 * @TIME: 12:39
 * @Description:
 **/
@Data
public class PayablePlanDto extends BaseVoEntity {

    /**
     * 应付类型关键字，枚举，如：m_student_offer_item
     */
    @NotNull(message = "应收类型不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "应付类型关键字，枚举，如：student_offer_item")
    private String fkTypeKey;

    /**
     * 应付类型对应记录Id，如：m_student_offer_item.id
     */
    @NotNull(message = "应收类型对应记录Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "应付类型对应记录Id，如：m_student_offer_item.id")
    private Long fkTypeTargetId;

    /**
     * 应收计划Id
     */
    @ApiModelProperty(value = "应收计划Id")
    private Long fkReceivablePlanId;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    private String summary;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @NotBlank(message = "应收类型对应记录Id不能为空", groups = {Add.class, Update.class})
    private String fkCurrencyTypeNum;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    private BigDecimal tuitionAmount;

    /**
     * 费率%(代理)
     */
    @ApiModelProperty(value = "费率%(代理)")
    private BigDecimal commissionRate;

    /**
     * 代理分成比率%
     */
    @ApiModelProperty(value = "代理分成比率%")
    private BigDecimal splitRate;

    /**
     * 佣金金额(代理)
     */
    @ApiModelProperty(value = "佣金金额(代理)")
    private BigDecimal commissionAmount;

    /**
     * 定额金额
     */
    @ApiModelProperty(value = "定额金额")
    private BigDecimal fixedAmount;

    /**
     * 奖励金额
     */
    @ApiModelProperty(value = "奖励金额")
    private BigDecimal bonusAmount;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    /**
     * 计划付款时间
     */
    @ApiModelProperty(value = "计划付款时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date payablePlanDate;

    /**
     * 是否预付，0否/1是
     */
    @ApiModelProperty(value = "是否预付，0否/1是")
    private Boolean isPayInAdvance;

    /**
     * 状态：0关闭/1打开/2完成
     */
    @ApiModelProperty(value = "状态：0关闭/1打开/2完成")
    private Integer status;


    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    /**
     * 公司id
     */
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    /**
     * 公司ids
     */
    @ApiModelProperty(value = "公司ids")
    private List<Long> fkCompanyIds;

    /**
     * num
     */
    @ApiModelProperty(value = "num")
    private String offerItemNum;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;

    /**
     * 提供商名称
     */
    @ApiModelProperty(value = "代理名称")
    private String agentName;

    /**
     * 学校名称
     */
    @ApiModelProperty(value = "学校名称")
    private String institutionName;

    /**
     * 课程名称
     */
    @ApiModelProperty(value = "课程名称")
    private String courseName;


    @ApiModelProperty("创建时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("创建时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 查询用ids
     */
    @ApiModelProperty(value = "查询用ids")
    private List<Long> ids;


    /**
     * 付款状态：0/1/2：未付/已付部分/已付齐
     */
    @ApiModelProperty(value = "付款状态：0/1/2：未付/已付部分/已付齐")
    private Integer payableStatus;

    @ApiModelProperty(value = "付款单id")
    private Long paymentFormId;

    
}
