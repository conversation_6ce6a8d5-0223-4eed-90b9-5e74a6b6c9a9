package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.SaleMediaAndAttached;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @description:附件dto
 */
@Data
public class MediaAppdixVo extends BaseEntity {

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    @ApiModelProperty(value = "文件guid(文档中心)")
    private String fkFileGuid;

    @ApiModelProperty(value = "表名")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    @ApiModelProperty(value = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    private String typeKey;

    @ApiModelProperty(value = "索引值(默认从0开始，同一类型下值唯一)")
    private int indexkey;

    //=============实体类SaleMediaAndAttached===============
    private static final long serialVersionUID = 1L;



    /**
     * 索引值(默认从0开始，同一类型下值唯一)
     */
    @ApiModelProperty(value = "索引值(默认从0开始，同一类型下值唯一)")
    @Column(name = "index_key")
    private Integer indexKey;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 链接
     */
    @ApiModelProperty(value = "链接")
    @Column(name = "link")
    private String link;
}
