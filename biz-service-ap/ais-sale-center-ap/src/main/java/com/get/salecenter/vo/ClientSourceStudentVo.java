package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ClientSource;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Hardy
 * @create: 2023/12/11 16:28
 * @verison: 1.0
 * @description:
 */
@Data
public class ClientSourceStudentVo extends BaseEntity {


    //========实体类ClientSource==========
    /**
     * 客户Id
     */
    @ApiModelProperty(value = "客户Id")
    @Column(name = "fk_client_id")
    private Long fkClientId;

    /**
     * 表名：crm_contract_num/bms_student_num
     */
    @ApiModelProperty(value = "表名：crm_contract_num/bms_student_num")
    @Column(name = "fk_table_name")
    private String fkTableName;

    /**
     * 表Id
     */
    @ApiModelProperty(value = "表Id")
    @Column(name = "fk_table_id")
    private Long fkTableId;

    /**
     * 表值
     */
    @ApiModelProperty(value = "表值")
    @Column(name = "fk_table_value")
    private String fkTableValue;

    /**
     * 代理Id（业绩绑定）
     */
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;

    /**
     * 员工Id（BD）
     */
    @ApiModelProperty(value = "员工Id（BD）")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    private static final long serialVersionUID = 1L;

}
