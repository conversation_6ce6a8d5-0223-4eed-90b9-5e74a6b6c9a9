package com.get.salecenter.vo;

import com.get.salecenter.entity.StaffCommissionAction;
import com.get.salecenter.entity.StudentProjectRole;
import com.get.salecenter.entity.StudentProjectRoleStaff;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @author: Hardy
 * @create: 2023/3/6 17:16
 * @verison: 1.0
 * @description:
 */
@Data
@Accessors(chain = true)
public class GetStaffCommissionSpecificDtoAndSetNameContext {

    /**
     * 角色key对应的总结算金额
     */
    private Map<String, BigDecimal> sumSettlementAmountMap;

    /**
     * 角色key对应的结算申请计划id
     */
    private Map<String, List<Long>> settlementItemIdMap;

    /**
     * 角色key对应实体
     */
    private Map<String, StudentProjectRole> roleByRoleKeyMap;

    /**
     * 申请计划id对应方案id
     */
    private Map<Long, Long> itemToOfferIdMap;

    /**
     * 员工id对应项目成员和员工关系实体
     */
    private Map<Long, List<StudentProjectRoleStaff>> studentProjectRoleStaffMap;

    /**
     * 员工名称映射
     */
    private Map<Long, String> staffNamesMap;

    /**
     * 项目角色key
     */
    private String studentProjectRoleKey;

    /**
     * 当前提成步骤对应的结算记录列表
     */
    private List<StaffCommissionAction> currentStepActions;

    /**
     * 未结算/已结算 0/1
     */
    private Integer type;
}
