package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Data
public class StudentServiceFeeVo extends BaseEntity {

    @ApiModelProperty("服务费类型名称")
    private String serviceTypeName;

    @ApiModelProperty("bd名称")
    private String bdName;

    @ApiModelProperty("代理名称")
    private String agentName;

    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("学生名称")
    private String fkStudentName;

    @ApiModelProperty("国家名称")
    private String fkAreaCountryName;

    @ApiModelProperty("国家id集合")
    private Set<Long> areaCountryIds;

    @ApiModelProperty("币种名称")
    private String fkCurrencyNumName;

    @ApiModelProperty("应收信息")
    private String receivableAmountInfo;

    @ApiModelProperty("应付信息")
    private String payableAmountInfo;
    /**
     * 绑定的项目成员
     */
    List<StudentProjectRoleStaffVo> studentProjectRoleStaffDtos;

    @ApiModelProperty("收款方类型名")
    private String fkTypeKeyReceivableName;

    @ApiModelProperty("收款方名")
    private String receivableName;

    /**
     * 已经创建了应收应付，则显示作废应收应付按钮
     */
    @ApiModelProperty("是否显示作废应收应付按钮")
    private Boolean showCancelRePy;

    //===========实体类StudentServiceFee===============
    @ApiModelProperty("学生id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;

    @ApiModelProperty("代理Id（业绩绑定）")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;

    @ApiModelProperty("员工Id（业绩绑定，BD）")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    @ApiModelProperty("学生留学服务费编号")
    @Column(name = "num")
    private String num;

    @ApiModelProperty("业务国家Ids（多选用英文逗号隔开，如：1,2,3）")
    @Column(name = "fk_area_country_ids")
    private String fkAreaCountryIds;

    @ApiModelProperty("学生服务费类型Id")
    @Column(name = "fk_student_service_fee_type_id")
    private Long fkStudentServiceFeeTypeId;

    @ApiModelProperty("收款方类型，枚举，如：m_student/m_institution_provider")
    @Column(name = "fk_type_key_receivable")
    private String fkTypeKeyReceivable;

    @ApiModelProperty("收款方类型Id，如：m_student.id")
    @Column(name = "fk_type_target_id_receivable")
    private Long fkTypeTargetIdReceivable;

    @ApiModelProperty("币种")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    @ApiModelProperty("金额")
    @Column(name = "amount")
    private BigDecimal amount;

    @ApiModelProperty("税金")
    @Column(name = "taxes")
    private BigDecimal taxes;

    @ApiModelProperty("业务发生开始时间")
    @Column(name = "business_start_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessStartTime;

    @ApiModelProperty("业务发生结束时间")
    @Column(name = "business_end_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date businessEndTime;

    @ApiModelProperty("备注")
    @Column(name = "remark")
    private String remark;

    @ApiModelProperty("业务状态：0未完成/1已完成")
    @Column(name = "business_status")
    private Integer businessStatus;

    @ApiModelProperty("结算状态审批：0未审批/1已审批")
    @Column(name = "approve_status")
    private Integer approveStatus;

    @ApiModelProperty("状态 0作废/1生效")
    @Column(name = "status")
    private Integer status;

    @ApiModelProperty("销售时间")
    @Column(name = "sales_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date salesTime;

    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentLabelVos;

}
