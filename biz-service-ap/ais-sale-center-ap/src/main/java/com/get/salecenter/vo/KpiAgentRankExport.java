package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * KPI代理排名导出实体类
 */
@Data
public class KpiAgentRankExport {

    @ApiModelProperty(value = "排名")
    private String rankNum;

    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    @ApiModelProperty(value = "BD名称")
    private String bdName;

    /**
     * BD绑定的大区
     */
    @ApiModelProperty("大区")
    private String areaRegion;

    @ApiModelProperty(value = "区域")
    private String region;

    @ApiModelProperty(value = "学生数")
    private Integer studentCount;

}
