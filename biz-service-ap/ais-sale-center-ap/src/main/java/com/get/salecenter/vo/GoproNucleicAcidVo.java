package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.GoproNucleicAcid;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Hardy
 * @create: 2022/5/25 12:12
 * @verison: 1.0
 * @description:
 */
@Data
public class GoproNucleicAcidVo extends BaseEntity {

    //==========实体类GoproNucleicAcid=============
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 活动场地：敦煌/西双版纳/呼伦贝尔
     */
    @ApiModelProperty(value = "活动场地：敦煌/西双版纳/呼伦贝尔")
    @Column(name = "retreat_type_name")
    private String retreatTypeName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Column(name = "name")
    private String name;

    /**
     * 性别：0女/1男
     */
    @ApiModelProperty(value = "性别：0女/1男")
    @Column(name = "gender")
    private Integer gender;

    /**
     * 所在机构/院校
     */
    @ApiModelProperty(value = "所在机构/院校")
    @Column(name = "institution")
    private String institution;

    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;

    /**
     * 证件类型：身份证/护照
     */
    @ApiModelProperty(value = "证件类型：身份证/护照")
    @Column(name = "identity_type")
    private String identityType;

    /**
     * 身份证/护照号
     */
    @ApiModelProperty(value = "身份证/护照号")
    @Column(name = "identity_card")
    private String identityCard;

    /**
     * 出发国家/地区
     */
    @ApiModelProperty(value = "出发国家/地区")
    @Column(name = "area_country_name")
    private String areaCountryName;

    /**
     * 出发州省
     */
    @ApiModelProperty(value = "出发州省")
    @Column(name = "area_state_name")
    private String areaStateName;

    /**
     * 出发城市
     */
    @ApiModelProperty(value = "出发城市")
    @Column(name = "area_city_name")
    private String areaCityName;

    /**
     * 到达日期
     */
    @ApiModelProperty(value = "到达日期")
    @Column(name = "arrival_date")
    private String arrivalDate;

    /**
     * 交通班次编号
     */
    @ApiModelProperty(value = "交通班次编号")
    @Column(name = "transportation_code")
    private String transportationCode;

    /**
     * 行程安排
     */
    @ApiModelProperty(value = "行程安排")
    @Column(name = "scheduling")
    private String scheduling;

    /**
     * bd区域
     */
    @ApiModelProperty(value = "bd区域")
    @Column(name = "bd_region")
    private String bdRegion;

    /**
     * bd姓名
     */
    @ApiModelProperty(value = "bd姓名")
    @Column(name = "bd_name")
    private String bdName;

    /**
     * 排序id
     */
    @ApiModelProperty(value = "排序id")
    @Column(name = "order_id")
    private Integer orderId;

    private static final long serialVersionUID = 1L;
}
