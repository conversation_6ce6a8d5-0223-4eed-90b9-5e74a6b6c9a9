package com.get.salecenter.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class ClientStaffVo extends BaseEntity {

    @ApiModelProperty(value = "学生资源Id")
    private Long fkClientId;

    @ApiModelProperty(value = "负责人Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "负责人姓名")
    private String fkStaffName;

    @ApiModelProperty(value = "是否激活：0否/1是")
    private Integer isActive;

    @ApiModelProperty(value = "绑定时间")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date activeDate;

    @ApiModelProperty(value = "取消绑定时间（下次绑定时，需要重新建立记录）")
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    private Date unactiveDate;


}
