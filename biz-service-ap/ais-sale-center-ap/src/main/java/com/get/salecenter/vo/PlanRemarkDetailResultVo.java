package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Hardy
 * @create: 2022/11/3 10:16
 * @verison: 1.0
 * @description: 备注信息
 */
@Data
public class PlanRemarkDetailResultVo {

    @ApiModelProperty("id")
    private Long id;

    /**
     * 项目说明名称
     */
    @ApiModelProperty(value = "项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生")
    private Integer educationProject;

    /**
     * 学位情况名称
     */
    @ApiModelProperty(value = "学位情况名称")
    private Integer educationDegree;

    /**
     * 成绩备注
     */
    @ApiModelProperty(value = "成绩备注")
    private String scoreRemark;

    /**
     * 成绩备注
     */
    @ApiModelProperty(value = "学生备注/建议")
    private String studentCommentIds;

    /**
     * 成绩备注
     */
    @ApiModelProperty(value = "申请计划备注/建议")
    private String studentOfferItemCommentIds;

    /**
     * 方案备注
     */
    @ApiModelProperty(value = "方案备注")
    private String studentOfferRemark;

    /**
     * 步骤历史备注
     */
    @ApiModelProperty(value = "步骤历史备注")
    private String stepRemarks;

    /**
     * 保险备注
     */
    @ApiModelProperty(value = "保险备注")
    private String insuranceRemark;

    /**
     * 住宿备注
     */
    @ApiModelProperty(value = "住宿备注")
    private String accommodationRemark;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;
    /**
     * 申请计划id
     */
    @ApiModelProperty(value = "申请计划id")
    private Long fkStudentOfferItemId;


}
