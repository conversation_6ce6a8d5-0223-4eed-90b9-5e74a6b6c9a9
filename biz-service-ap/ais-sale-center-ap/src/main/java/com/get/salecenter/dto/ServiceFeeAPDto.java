package com.get.salecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Set;

/**
 * 批量创建应付计划
 */
@Data
public class ServiceFeeAPDto {

    @ApiModelProperty("服务费ids")
    @NotEmpty(message = "请选择要创建应付的对象")
    private Set<Long> serviceFeeIds;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司id不能为空")
    private Long fkCompanyId;

    @ApiModelProperty("应付比率")
    @NotNull(message = "应付比率不能为空")
    private BigDecimal payableRatio;

}
