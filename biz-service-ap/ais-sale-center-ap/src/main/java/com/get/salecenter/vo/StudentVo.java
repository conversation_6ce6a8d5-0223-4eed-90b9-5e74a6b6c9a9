package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.dto.StudentClientSourceDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/4
 * @TIME: 11:25
 * @Description:
 **/
@Data
public class StudentVo extends BaseEntity {

    @ApiModelProperty(value = "绑定的项目成员")
    List<StudentProjectRoleStaffVo> studentProjectRoleStaffDtos;
    @ApiModelProperty(value = "绑定的代理")
    List<StudentAgentVo> studentAgentDtoList;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;
    /**
     * 学校名称List
     */
    @ApiModelProperty(value = "学校名称List")
    private Set<String> institutionNameList;
    /**
     * 学生出生所在国家名
     */
    @ApiModelProperty(value = "学生出生所在国家名")
    private String fkAreaCountryBirthName;
    /**
     * 学生出生所在州省名
     */
    @ApiModelProperty(value = "学生出生所在州省名")
    private String fkAreaStateBirthName;
    /**
     * 学生出生所在城市名
     */
    @ApiModelProperty(value = "学生出生所在城市名")
    private String fkAreaCityBirthName;
    /**
     * 学生国籍所在国家Id联查到的对应国家名称
     */
    @ApiModelProperty(value = "学生国籍所在国家名称（下拉）")
    private String countryNameNationality;

    @ApiModelProperty(value = "国内学历名称")
    private String domesticEducationName;

    @ApiModelProperty(value = "国际学历名称")
    private String internationalEducationName;

    /**
     * 绿卡国家名称
     */
    @ApiModelProperty(value = "绿卡国家名称（下拉）")
    private String countryNameGreenCard;
    /**
     * 学生出生所在国家Id联查到的对应国家名称
     */
    @ApiModelProperty(value = "学生出生所在国家名称（下拉）")
    private String countryNameBirth;
    /**
     * 学生出生所在州省Id联查到的对应州省名称
     */
    @ApiModelProperty("学生出生所在州省名称（下拉）")
    private String stateNameBirth;
    /**
     * 学生出生所在城市Id联查到的对应州省名称
     */
    @ApiModelProperty("学生出生所在城市名称（下拉）")
    private String cityNameBirth;
    /**
     * 学生现居所在国家Id联查到的对应国家名称
     */
    @ApiModelProperty(value = "学生现居所在国家（下拉）")
    private String countryName;
    /**
     * 学生现居所在州省Id联查到的州省名称
     */
    @ApiModelProperty(value = "学生现居所在州省名称（下拉）")
    private String stateName;
    /**
     * 学生现居所在城市Id联查到的城市名称
     */
    @ApiModelProperty(value = "学生现居所在城市名称（下拉）")
    private String cityName;
    /**
     * 毕业国家名称
     */
    @ApiModelProperty(value = "毕业国家名称（下拉）")
    private String countryNameEducation;
    /**
     * 毕业州省名称
     */
    @ApiModelProperty(value = "毕业州省名称（下拉）")
    private String stateNameEducation;
    /**
     * 毕业城市名称
     */
    @ApiModelProperty(value = "毕业城市名称（下拉）")
    private String cityNameEducation;
    /**
     * 毕业国家名称国际
     */
    @ApiModelProperty(value = "毕业国家名称国际（下拉）")
    private String countryNameEducation2;
    /**
     * 毕业州省名称国际
     */
    @ApiModelProperty(value = "毕业州省名称国际（下拉）")
    private String stateNameEducation2;
    /**
     * 毕业城市名称国际
     */
    @ApiModelProperty(value = "毕业城市名称国际（下拉）")
    private String cityNameEducation2;
    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄")
    private Integer age;
    /**
     * 代理名称
     */
    @ApiModelProperty(value = "代理名称")
    private List<String> fkAgentName;
    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private List<String> fkStaffName;
    /**
     * 学生业务状态名
     */
    @ApiModelProperty(value = "学生业务状态名(多选)")
    private String conditionTypeName;
    /**
     * 当前申请状态
     */
    @ApiModelProperty(value = "当前申请状态")
    private String stepName;
    /**
     * 失败原因
     */
    @ApiModelProperty(value = "失败原因")
    private String reasonName;
    /**
     * 代理Id
     */
    @ApiModelProperty(value = "代理Id")
    private Long fkAgentId;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
    /**
     * 绑定代理 / BD
     */
    @ApiModelProperty(value = "绑定代理 / BD")
    private Set<String> bdNameAndStaffNameList;

    /**
     * 当前绑定代理
     */
    @ApiModelProperty(value = "当前绑定代理")
    private Set<String> currentStaffNameList;

    @ApiModelProperty(value = "当前绑定代理信息")
    private List<AgentAndAgentLabelVo> currentStaffNameAndAgentLabelList;

    /**
     * 当前绑定代理编号
     */
    @ApiModelProperty(value = "当前绑定代理编号")
    private Set<String> currentAgentNumList;

    /**
     * 当前绑定BD
     */
    @ApiModelProperty(value = "当前绑定BD")
    private Set<String> currentBdNameList;

    /**
     * 当前绑定代理 / BD
     */
    @ApiModelProperty(value = "当前绑定代理 / BD")
    private Set<String> currentBdNameAndStaffNameList;

    @ApiModelProperty(value = "申请方案绑定代理")
    private Set<String> studentOfferStaffNameList;

    @ApiModelProperty(value = "申请方案绑定代理")
    private List<AgentAndAgentLabelVo> studentOfferStaffNameAndAgentLabelList;

    @ApiModelProperty(value = "申请方案绑定代理编号")
    private Set<String> studentOfferAgentNumList;

    @ApiModelProperty(value = "申请方案绑定BD")
    private Set<String> studentOfferBdNamList;

    @ApiModelProperty(value = "申请方案绑定代理/BD")
    private Set<String> studentOfferBdNameAndStaffNameList;
    /**
     * 项目角色 / 员工
     */
    @ApiModelProperty(value = "项目角色 / 员工")
    private Set<String> projectRoleStaffList;
    /**
     * 状态变更最新时间
     */
    @ApiModelProperty(value = "状态变更最新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date statusChangeLastTime;
    /**
     * 是否有学习申请方案 true:有 false：没有
     */
    @ApiModelProperty(value = "是否有学习申请方案 true:有 false：没有")
    private boolean studentOfferFlag;

    /**
     * 是否有留学住宿 true:有 false：没有
     */
    @ApiModelProperty(value = "是否有留学住宿 true:有 false：没有")
    private boolean studentAccommodationFlag;

    /**
     * 是否有留学保险 true:有 false：没有
     */
    @ApiModelProperty(value = "是否有留学保险 true:有 false：没有")
    private boolean studentInsurancesFlag;

    /**
     * 是否有留学保险 true:有 false：没有
     */
    @ApiModelProperty(value = "是否有留学服务费 true:有 false：没有")
    private boolean studentServiceFeeFlag;

    /**
     * 毕业院校名称（下拉）
     */
    @ApiModelProperty(value = "毕业院校名称（下拉）")
    private String educationInstitutionName;

    /**
     * 毕业院校名称国际（下拉）
     */
    @ApiModelProperty(value = "毕业院校名称国际（下拉）")
    private String educationInstitutionName2;

    /**
     * 学习计划id
     */
    @ApiModelProperty(value = "学习计划id")
    private Long offerItemId;
    /**
     * 多个申请国家
     */
    @ApiModelProperty(value = "多个申请国家")
    private String areaCountryNames;

    /**
     * 成功入读国家
     */
    @ApiModelProperty(value = "成功入读国家")
    private String areaCountrySuccessfulNames;

//    /**
//     * 应收应付信息
//     */
//    @ApiModelProperty(value = "应收应付信息")
//    private List<StudentReceivableAndPaySumVo> studentReceivableAndPayInfo;

    /**
     * 项目说明名称
     */
    @ApiModelProperty(value = "项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生")
    private String educationProjectName;

    /**
     * 学位情况名称
     */
    @ApiModelProperty(value = "学位情况名称")
    private String educationDegreeName;

    @ApiModelProperty(value = "学习计划List")
    private List<StudentOfferItemVo> studentOfferItemDtoList;

    @ApiModelProperty(value = "学生中英文名")
    private String fullName;

    @ApiModelProperty(value = "邮箱")
    private String email;

    /**
     * 高中成绩类型名称
     */
    @ApiModelProperty(value = "高中成绩类型名称")
    private String highSchoolTestTypeName;


    /**
     * 本科成绩类型名称
     */
    @ApiModelProperty(value = "本科成绩类型名称")
    private String standardTestTypeName;
    /**
     * 英语测试类型
     */
    @ApiModelProperty(value = "英语测试类型")
    private String englishTestTypeName;

    /**
     * 硕士成绩类型，枚举Key
     */
    @ApiModelProperty(value = "硕士成绩类型，枚举Key")
    private String masterTestTypeName;

    /**
     * 硕士成绩
     */
    @ApiModelProperty(value = "本科成绩")
    private String masterTestScore;

    @ApiModelProperty(value = "学生最高状态")
    private Integer maxStepOrder;

    @ApiModelProperty(value = "学生最高状态名")
    private String maxStepOrderName;

    @ApiModelProperty(value = "学生最低状态")
    private Integer minStepOrder;

    @ApiModelProperty(value = "学生最低状态名")
    private String minStepOrderName;

    /**
     * 总记录数
     */
    @ApiModelProperty(value = "总记录数")
    private Integer totalCount;

    /**
     * 学生Id（ISSUEv2版申请）
     */
    @ApiModelProperty(value = "学生Id（ISSUEv2版申请）")
    private Long fkStudentIdIssue2;

    /**
     * 学生Id（ISSUEv2版申请）
     */
    @ApiModelProperty(value = "学生Id（ISSUE申请）")
    private Long fkStudentIdIssue;

    /**
     * 是否激活提成
     */
    @ApiModelProperty(value = "是否激活提成：false否/true是")
    private Boolean isActiveCommission = false;

    /**
     * 是否激活提成
     */
    @ApiModelProperty(value = "结算状态list")
    private List<Map<String,Object>> settlementStatusList;

    @ApiModelProperty(value = "护照签发地名称")
    private String fkAreaCountryNamePassport;

    @ApiModelProperty(value = "推荐来源")
    private StudentClientSourceDto studentClientSourceDto;

    //================实体类Student=====================
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 学生编号
     */
    @ApiModelProperty(value = "学生编号")
    @Column(name = "num")
    private String num;
    /**
     * 学生姓名（中）
     */
    @ApiModelProperty(value = "学生姓名（中）")
    @Column(name = "name")
    private String name;
    /**
     * 姓（英/拼音）
     */
    @ApiModelProperty(value = "姓（英/拼音）")
    @Column(name = "last_name")
    private String lastName;
    /**
     * 名（英/拼音）
     */
    @ApiModelProperty(value = "名（英/拼音）")
    @Column(name = "first_name")
    private String firstName;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @Column(name = "gender")
    private String gender;
    /**
     * 生日
     */
    @ApiModelProperty(value = "生日")
    @Column(name = "birthday")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;
    /**
     * 学生出生所在国家Id
     */
    @ApiModelProperty(value = "学生出生所在国家Id")
    @Column(name = "fk_area_country_id_birth")
    @UpdateWithNull
    private Long fkAreaCountryIdBirth;
    /**
     * 学生出生所在州省Id
     */
    @ApiModelProperty(value = "学生出生所在州省Id")
    @Column(name = "fk_area_state_id_birth")
    @UpdateWithNull
    private Long fkAreaStateIdBirth;
    /**
     * 学生出生所在城市Id
     */
    @ApiModelProperty(value = "学生出生所在城市Id")
    @Column(name = "fk_area_city_id_birth")
    @UpdateWithNull
    private Long fkAreaCityIdBirth;
    /**
     * 学生出生所在国家名称
     */
    @ApiModelProperty(value = "学生出生所在国家名称")
    @Column(name = "fk_area_country_name_birth")
    private String fkAreaCountryNameBirth;
    /**
     * 学生出生所在州省名称
     */
    @ApiModelProperty(value = "学生出生所在州省名称")
    @Column(name = "fk_area_state_name_birth")
    private String fkAreaStateNameBirth;
    /**
     * 学生出生所在城市名称
     */
    @ApiModelProperty(value = "学生出生所在城市名称")
    @Column(name = "fk_area_city_name_birth")
    private String fkAreaCityNameBirth;
    /**
     * 手机区号
     */
    @ApiModelProperty(value = "手机区号")
    @Column(name = "mobile_area_code")
    private String mobileAreaCode;
    /**
     * 移动电话
     */
    @ApiModelProperty(value = "移动电话")
    @Column(name = "mobile")
    private String mobile;
    /**
     * 电话区号
     */
    @ApiModelProperty(value = "电话区号")
    @Column(name = "tel_area_code")
    private String telAreaCode;
    /**
     * 电话
     */
    @ApiModelProperty(value = "电话")
    @Column(name = "tel")
    private String tel;

    /**
     * 学生现居所在国家Id
     */
    @ApiModelProperty(value = "学生现居所在国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 学生现居所在州省Id
     */
    @ApiModelProperty(value = "学生现居所在州省Id")
    @Column(name = "fk_area_state_id")
    private Long fkAreaStateId;
    /**
     * 学生现居所在城市Id
     */
    @ApiModelProperty(value = "学生现居所在城市Id")
    @Column(name = "fk_area_city_id")
    private Long fkAreaCityId;
    /**
     * 学生现居所在国家名称
     */
    @ApiModelProperty(value = "学生现居所在国家名称")
    @Column(name = "fk_area_country_name")
    private String fkAreaCountryName;
    /**
     * 学生现居所在州省名称
     */
    @ApiModelProperty(value = "学生现居所在州省名称")
    @Column(name = "fk_area_state_name")
    private String fkAreaStateName;
    /**
     * 学生现居所在城市名称
     */
    @ApiModelProperty(value = "学生现居所在城市名称")
    @Column(name = "fk_area_city_name")
    private String fkAreaCityName;
    /**
     * 邮编
     */
    @ApiModelProperty(value = "邮编")
    @Column(name = "zipcode")
    private String zipcode;

//    /**
//     * 学历区域类型：国内/国际
//     */
//    @ApiModelProperty(value = "学历区域类型：国内/国际")
//    @Column(name = "education_area_type")
//    private String educationAreaType;
    /**
     * 联系地址
     */
    @ApiModelProperty(value = "联系地址")
    @Column(name = "contact_address")
    private String contactAddress;
    /**
     * 学历等级类型：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ApiModelProperty(value = "学历等级类型：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND")
    @Column(name = "education_level_type")
    private String educationLevelType;
    /**
     * 毕业专业
     */
    @ApiModelProperty(value = "毕业专业")
    @Column(name = "education_major")
    private String educationMajor;
    /**
     * 毕业大学国家Id
     */
    @ApiModelProperty(value = "毕业大学国家Id")
    @Column(name = "fk_area_country_id_education")
    private Long fkAreaCountryIdEducation;
    /**
     * 毕业大学州省Id
     */
    @ApiModelProperty(value = "毕业大学州省Id")
    @Column(name = "fk_area_state_id_education")
    private Long fkAreaStateIdEducation;
    /**
     * 毕业大学城市Id
     */
    @ApiModelProperty(value = "毕业大学城市Id")
    @Column(name = "fk_area_city_id_education")
    private Long fkAreaCityIdEducation;
    /**
     * 毕业大学国家名称
     */
    @ApiModelProperty(value = "毕业大学国家名称")
    @Column(name = "fk_area_country_name_education")
    private String fkAreaCountryNameEducation;
    /**
     * 毕业大学州省名称
     */
    @ApiModelProperty(value = "毕业大学州省名称")
    @Column(name = "fk_area_state_name_education")
    private String fkAreaStateNameEducation;
    /**
     * 毕业大学城市名称
     */
    @ApiModelProperty(value = "毕业大学城市名称")
    @Column(name = "fk_area_city_name_education")
    private String fkAreaCityNameEducation;
    /**
     * 毕业院校Id
     */
    @ApiModelProperty(value = "毕业院校Id")
    @Column(name = "fk_institution_id_education")
    @UpdateWithNull
    private Long fkInstitutionIdEducation;
    /**
     * 毕业院校名称
     */
    @ApiModelProperty(value = "毕业院校名称")
    @Column(name = "fk_institution_name_education")
    @UpdateWithNull
    private String fkInstitutionNameEducation;
    /**
     * 毕业学校类型：985/211/其他，默认选项：其他
     */
    @ApiModelProperty(value = "毕业学校类型：985/211/其他，默认选项：其他")
    @Column(name = "institution_type_education")
    private String institutionTypeEducation;
    /**
     * 学历等级类型（国际）：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND
     */
    @ApiModelProperty(value = "学历等级类型（国际）：中专/高中/大学/大专/专升本/本科/研究生/硕士/博士在读/博士/博士后/HND")
    @Column(name = "education_level_type2")
    private String educationLevelType2;
    /**
     * 毕业专业（国际）
     */
    @ApiModelProperty(value = "毕业专业（国际）")
    @Column(name = "education_major2")
    private String educationMajor2;
    /**
     * 毕业大学国家Id（国际）
     */
    @ApiModelProperty(value = "毕业大学国家Id（国际）")
    @Column(name = "fk_area_country_id_education2")
    private Long fkAreaCountryIdEducation2;
    /**
     * 毕业大学州省Id（国际）
     */
    @ApiModelProperty(value = "毕业大学州省Id（国际）")
    @Column(name = "fk_area_state_id_education2")
    private Long fkAreaStateIdEducation2;
    /**
     * 毕业大学城市Id（国际）
     */
    @ApiModelProperty(value = "毕业大学城市Id（国际）")
    @Column(name = "fk_area_city_id_education2")
    private Long fkAreaCityIdEducation2;
    /**
     * 毕业大学国家名称（国际）
     */
    @ApiModelProperty(value = "毕业大学国家名称（国际）")
    @Column(name = "fk_area_country_name_education2")
    private String fkAreaCountryNameEducation2;
    /**
     * 毕业大学州省名称（国际）
     */
    @ApiModelProperty(value = "毕业大学州省名称（国际）")
    @Column(name = "fk_area_state_name_education2")
    private String fkAreaStateNameEducation2;
    /**
     * 毕业大学城市名称（国际）
     */
    @ApiModelProperty(value = "毕业大学城市名称（国际）")
    @Column(name = "fk_area_city_name_education2")
    private String fkAreaCityNameEducation2;
    /**
     * 毕业院校Id（国际）
     */
    @ApiModelProperty(value = "毕业院校Id（国际）")
    @Column(name = "fk_institution_id_education2")
    @UpdateWithNull
    private Long fkInstitutionIdEducation2;
    /**
     * 毕业院校名称（国际）
     */
    @ApiModelProperty(value = "毕业院校名称（国际）")
    @Column(name = "fk_institution_name_education2")
    @UpdateWithNull
    private String fkInstitutionNameEducation2;
    /**
     * 项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生
     */
    @ApiModelProperty(value = "项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生")
    @Column(name = "education_project")
    private Integer educationProject;
    /**
     * 学位情况，枚举：获得双学位/获得国际学位/获得国内学位
     */
    @ApiModelProperty(value = "学位情况，枚举：获得双学位/获得国际学位/获得国内学位")
    @Column(name = "education_degree")
    private Integer educationDegree;

    /**
     * 是否复杂教育背景：0否/1是
     */
    @ApiModelProperty(value = "是否复杂教育背景：0否/1是")
    @Column(name = "is_complex_education")
    private Boolean isComplexEducation;

    /**
     * '复杂教育背景备注'
     */
    @ApiModelProperty(value = "'复杂教育背景备注'")
    @Column(name = "complex_education_remark")
    private String complexEducationRemark;

    /**
     * 高中成绩类型，枚举Key
     */
    @ApiModelProperty(value = "高中成绩类型，枚举Key")
    @Column(name = "high_school_test_type")
    private String highSchoolTestType;
    /**
     * 高中成绩
     */
    @ApiModelProperty(value = "高中成绩")
    @Column(name = "high_school_test_score")
    private String highSchoolTestScore;
    /**
     * 本科成绩类型，枚举Key
     */
    @ApiModelProperty(value = "本科成绩类型，枚举Key")
    @Column(name = "standard_test_type")
    private String standardTestType;
    /**
     * 本科成绩
     */
    @ApiModelProperty(value = "本科成绩")
    @Column(name = "standard_test_score")
    private String standardTestScore;
    /**
     * 硕士成绩类型，枚举Key
     */
    @ApiModelProperty(value = "硕士成绩类型，枚举Key")
    @Column(name = "master_test_type")
    private String masterTestType;

    /**
     * 英语测试类型
     */
    @ApiModelProperty(value = "英语测试类型")
    @Column(name = "english_test_type")
    private String englishTestType;
    /**
     * 英语测试成绩
     */
    @ApiModelProperty(value = "英语测试成绩")
    @Column(name = "english_test_score")
    private BigDecimal englishTestScore;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 共享路径
     */
    @ApiModelProperty(value = "共享路径")
    @Column(name = "shared_path")
    private String sharedPath;
    /**
     * 学生业务状态(多选)：0转代理学生/1奖学金占学费的60%以上
     */
    @ApiModelProperty(value = "学生业务状态(多选)：0转代理学生/1奖学金占学费的60%以上")
    @Column(name = "condition_type")
    private String conditionType;
    /**
     * 旧数据num(gea)
     */
    @ApiModelProperty(value = "旧数据num(gea)")
    @Column(name = "num_gea")
    private String numGea;
    /**
     * 旧数据num(iae)
     */
    @ApiModelProperty(value = "旧数据num(iae)")
    @Column(name = "num_iae")
    private String numIae;
    /**
     * 旧数据id(issue学生Id)
     */
    @ApiModelProperty(value = "旧数据id(issue学生Id)")
    @Column(name = "id_issue")
    private String idIssue;
    /**
     * 旧数据id(issue学生信息Id)
     */
    @ApiModelProperty(value = "旧数据id(issue学生信息Id)")
    @Column(name = "id_issue_info")
    private String idIssueInfo;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;
    /**
     * 学生国籍所在国家Id
     */
    @ApiModelProperty(value = "学生国籍所在国家Id")
    @Column(name = "fk_area_country_id_nationality")
    private Long fkAreaCountryIdNationality;
    /**
     * 学生国籍所在国家名称
     */
    @ApiModelProperty(value = "学生国籍所在国家名称")
    @Column(name = "fk_area_country_name_nationality")
    private String fkAreaCountryNameNationality;
    /**
     * 绿卡国家Id
     */
    @ApiModelProperty(value = "绿卡国家Id")
    @Column(name = "fk_area_country_id_green_card")
    private Long fkAreaCountryIdGreenCard;
    /**
     * 护照编号（保险业务必填）
     */
    @ApiModelProperty(value = "护照编号（保险业务必填）")
    @Column(name = "passport_num")
    private String passportNum;

    @ApiModelProperty(value = "护照签发地Id")
    @Column(name = "fk_area_country_id_passport")
    private Long fkAreaCountryIdPassport;

    @ApiModelProperty(value = "收到申请资料时间")
    @Column(name = "received_application_data_date")
    @UpdateWithNull
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date receivedApplicationDataDate;


    @ApiModelProperty(value = "学生资源Id")
    @Column(name = "fk_client_id")
    private Long fkClientId;

    @ApiModelProperty(value = "学生申请方案数量")
    private Integer studentOfferItemNum;


}
