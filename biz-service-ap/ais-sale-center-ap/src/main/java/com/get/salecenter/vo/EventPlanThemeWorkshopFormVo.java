package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2023/12/13
 * @TIME: 9:33
 * @Description:
 **/
@Data
public class EventPlanThemeWorkshopFormVo extends BaseEntity implements Serializable {
    @ApiModelProperty(value = "活动计划主题Id")
    private Long fkEventPlanThemeId;

    @ApiModelProperty(value = "地点")
    private String location;

    @ApiModelProperty(value = "日期")
    private String date;

    @ApiModelProperty(value = "时长")
    private String duration;

    @ApiModelProperty(value = "规模")
    private String scale;

    @ApiModelProperty(value = "说明")
    private String note;

    @ApiModelProperty(value = "人数限制")
    private Integer quota;

    @ApiModelProperty(value = "报名币种")
    private String fkCurrencyTypeNum;

    @ApiModelProperty(value = "报名费用")
    private BigDecimal amount;

    @ApiModelProperty(value = "费用单位")
    private String unit;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "是否激活：0否/1是，若否需要灰掉活动项目")
    private Boolean isActive;
}
