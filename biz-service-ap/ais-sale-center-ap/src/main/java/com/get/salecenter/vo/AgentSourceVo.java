package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 代理积分Dto
 * @return:
 * @Author: Walker
 * @Date: 2022/3/15
 */
@Data
public class AgentSourceVo {
    @ApiModelProperty(value = "代理Id")
    private String fkAgentIds;

    @ApiModelProperty(value = "代理排名")
    private Integer rank;
    @ApiModelProperty(value = "代理排名名称")
    private String rankName;

    @ApiModelProperty(value = "代理名(简写：跳转)")
    private String agName;

    @ApiModelProperty(value = "代理名")
    private String agentName;

    @ApiModelProperty(value = "总积分")
    private String ttlScore;

    @ApiModelProperty("新申请积分")
    private String crtScore;

    @ApiModelProperty("确定课程积分")
    private String admittedScore;

    @ApiModelProperty("KPI加分")
    private String kpiScore;

    @ApiModelProperty("KPI加分")
    private String kpiScoreOne;

    @ApiModelProperty("KPI加分")
    private String kpiScoreTwo;

    @ApiModelProperty("KPI加分(3分)")
    private String kpiScoreThree;

    @ApiModelProperty("KPI加分(4分)")
    private String kpiScoreFour;

    @ApiModelProperty("未付费学生数")
    private String noAdmittedCount;

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "bd名字")
    private String bdNames;

    @ApiModelProperty(value = "开始时间")
    private String beginTime;

    @ApiModelProperty(value = "结束时间")
    private String endTime;

    @ApiModelProperty(value = "开始时间")
    private String openingStartTime;

    @ApiModelProperty(value = "结束时间")
    private String openingEndTime;
}
