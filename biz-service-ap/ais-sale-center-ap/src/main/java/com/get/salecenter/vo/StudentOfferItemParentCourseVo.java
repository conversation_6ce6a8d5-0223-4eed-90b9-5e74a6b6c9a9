package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * author:Neil
 * Time: 9:48
 * Date: 2022/6/27
 * Description:
 */
@Data
public class StudentOfferItemParentCourseVo extends BaseEntity {
    /**
     * 父课程名称
     */
    @ApiModelProperty(value = "父课程名称")
    private String parentCourseFullName;

    /**
     * 父课程申请步骤名
     */
    @ApiModelProperty(value = "父课程申请步骤名")
    private String parentStepName;

    /**
     * 课程id
     */
    @ApiModelProperty(value = "课程id")
    private Long fkInstitutionCourseId;

    @ApiModelProperty(value = "旧课程名")
    private String oldCourseCustomName;
}
