package com.get.salecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AccommodationSummaryQueryDto {

    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "学生名称编号")
    private String studentName;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "bd名称")
    private String staffName;

    @ApiModelProperty(value = "项目成员名称")
    private String memberName;

    @ApiModelProperty(value = "业务渠道")
    private Long fkBusinessChannelId;

    @ApiModelProperty(value = "业务提供商Id")
    private Long fkBusinessProviderId;

    @ApiModelProperty(value = "状态：0关闭/1打开")
    private Integer status;

    @ApiModelProperty(value = "前往国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "前往州省Id")
    private Long fkAreaStateId;

    @ApiModelProperty(value = "前往城市Id")
    private Long fkAreaCityId;

    @ApiModelProperty(value = "公寓名称")
    private String apartmentName;

    @ApiModelProperty(value = "入住日期开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkInDateStart;

    @ApiModelProperty(value = "入住日期结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkInDateEnd;

    @ApiModelProperty(value = "退房日期开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkOutDateStart;

    @ApiModelProperty(value = "退房日期结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date checkOutDateEnd;

    @ApiModelProperty(value = "应收状态：0/未收 1/部分已收 2/已收齐")
    private Integer arStatus;

    @ApiModelProperty(value = "应付状态：0/未付 1/部分已付 2/已付清")
    private Integer apStatus;

    //====================
    private List<Long> staffFollowerIds;

}
