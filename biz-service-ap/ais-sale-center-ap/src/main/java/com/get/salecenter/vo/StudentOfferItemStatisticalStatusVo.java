package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 学习计划统计状态栏
 *
 * @Date 9:45 2021/7/22
 * <AUTHOR>
 */
@Data
public class StudentOfferItemStatisticalStatusVo extends BaseEntity {

    @ApiModelProperty(value = "名字")
    private String name;

    @ApiModelProperty(value = "数量")
    private Long itemNum;

    @ApiModelProperty(value = "学生数量")
    private int studentNum;

    @ApiModelProperty(value = "申请步骤ID")
    private Long fkStudentOfferItemStepId;

    @ApiModelProperty(value = "申请步骤排序，由0开始按顺序排列")
    private Integer stepOrder;

    @ApiModelProperty(value = "步骤KEY")
    private String stepKey;

    @ApiModelProperty(value = "失败原因id")
    private Long failureReasonId;

    @ApiModelProperty(value = "是否延迟入学标记：0否/1是")
    private Boolean isDeferEntrance;

    @ApiModelProperty("修改时间(开始)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date beginTime;

    @ApiModelProperty("修改时间(结束)")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
}
