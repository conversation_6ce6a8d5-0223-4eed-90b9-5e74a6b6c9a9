package com.get.salecenter.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: Sea
 * @create: 2020/7/6 11:25
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionHotelDto extends BaseVoEntity{

    /**
     * 峰会Id
     */
    @NotNull(message = "峰会Id不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会Id", required = true)
    private Long fkConventionId;

    /**
     * 酒店名称
     */
    @NotBlank(message = "酒店名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "酒店名称", required = true)
    private String hotel;

    /**
     * 房型
     */
    @NotBlank(message = "房型不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "房型", required = true)
    private String roomType;

    /**
     * 床位数
     */
    @NotNull(message = "床位数不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "床位数", required = true)
    private Integer bedCount;

    /**
     * 系统编号前序号（批量创建房间使用）
     */
    @NotBlank(message = "系统编号前序号不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "系统编号前序号（批量创建房间使用）", required = true)
    private String preNum;

    /**
     * 价格币种编号
     */
    @ApiModelProperty(value = "价格币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 价格（参考）
     */
    @ApiModelProperty(value = "价格（参考）")
    private BigDecimal price;

    /**
     * 排序，数字由小到大排列
     */
    @ApiModelProperty(value = "排序，数字由小到大排列")
    private Integer viewOrder;

    //自定义内容
    /**
     * 关键字搜索
     */
    @ApiModelProperty(value = "关键字搜索")
    private String keyWord;
}
