package com.get.salecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * @author: Sea
 * @create: 2020/12/9 18:06
 * @verison: 1.0
 * @description:
 */
@Data
public class EventCostDto extends BaseVoEntity  {
    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
//    @NotNull(message = "活动Id不能为空", groups = {Add.class, Update.class})
    private Long fkEventId;

    /**
     * 学校提供商Id
     */
    @ApiModelProperty(value = "学校提供商Id")
    @NotNull(message = "学校提供商Id不能为空", groups = {Add.class, Update.class})
    private Long fkInstitutionProviderId;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    private String fkCurrencyTypeNum;

    /**
     * 活动费用
     */
    @ApiModelProperty(value = "活动费用")
    private BigDecimal amount;

    /**
     * 活动费用账单Id
     */
    @ApiModelProperty(value = "活动费用账单Id")
    private Long fkEventBillId;

    /**
     * 收款单Id（暂时无用）
     */
    @ApiModelProperty(value = "收款单Id（暂时无用）")
    private Long fkReceiptFormId;

    /**
     * 折合收款汇率
     */
    @ApiModelProperty(value = "折合收款汇率")
    @Column(name = "exchange_rate_receivable")
    private BigDecimal exchangeRateReceivable;

    /**
     * 折合收款金额
     */
    @ApiModelProperty(value = "折合收款金额")
    @Column(name = "amount_receivable")
    private BigDecimal amountReceivable;

    /**
     * 汇率（折合港币）
     */
    @ApiModelProperty(value = "汇率（折合港币）")
    @Column(name = "exchange_rate_hkd")
    private BigDecimal exchangeRateHkd;

    /**
     * 收款金额（折合港币）
     */
    @ApiModelProperty(value = "收款金额（折合港币）")
    @Column(name = "amount_hkd")
    private BigDecimal amountHkd;

    /**
     * 汇率（折合人民币）
     */
    @ApiModelProperty(value = "汇率（折合人民币）")
    @Column(name = "exchange_rate_rmb")
    private BigDecimal exchangeRateRmb;

    /**
     * 收款金额（折合人民币）
     */
    @ApiModelProperty(value = "收款金额（折合人民币）")
    @Column(name = "amount_rmb")
    private BigDecimal amountRmb;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;

    /**
     * 报名名册id
     */
    @ApiModelProperty(value = "报名名册id")
    private Long fkConventionRegistrationId;

    /**
     * 赞助商名册id
     */
    @ApiModelProperty(value = "赞助商名册id")
    private Long fkConventionSponsorId;

}
