package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StudentOfferItemDeferEntranceTime;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * 延迟入学返回类
 *
 * <AUTHOR>
 * @date 2021/7/22 11:06
 */
@Data
public class StudentOfferItemDeferEntranceTimeVo extends BaseEntity {
    /**
     * 数量
     */
    @ApiModelProperty(value = "学习计划数量")
    private Long itemNum;

    //===========实体类StudentOfferItemDeferEntranceTime===========
    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "学生申请方案Id")
    @Column(name = "fk_student_offer_item_id")
    private Long fkStudentOfferItemId;
    @ApiModelProperty(value = "延迟入学时间")
    @Column(name = "defer_entrance_time")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferEntranceTime;
}
