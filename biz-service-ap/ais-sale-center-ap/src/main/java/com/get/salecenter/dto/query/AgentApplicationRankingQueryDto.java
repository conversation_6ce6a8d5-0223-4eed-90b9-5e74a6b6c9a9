package com.get.salecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class AgentApplicationRankingQueryDto {

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;
    /**
     * 大区Id
     */
    @ApiModelProperty(value = "大区Id")
    private Long fkAreaRegionId;
    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;
    /**
     * 性质列表：公司/个人/工作室/国际学校/其他
     */
    @ApiModelProperty(value = "性质列表：公司/个人/工作室/国际学校/其他")
    private List<String> natureList;

    /**
     * 国家列表
     */
    @ApiModelProperty(value = "国家列表")
    private List<Long> fkCountryIds;

    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startOpeningTime;

    /**
     * 开学时间
     */
    @ApiModelProperty(value = "开学时间" )
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endOpeningTime;


    /**
     * bd名称
     */
    @ApiModelProperty(value = "bd名称")
    private String bdName;

    /**
     * 排序类型
     */
    @ApiModelProperty(value = "排序类型：1-按新建学生数排序；2-按定校量排序；3-按成功入学量排序")
    private Integer sortType;

    @ApiModelProperty(value = "代理名称/编号")
    private String agentNameOrNum;


}
