package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2020/11/23
 * @TIME: 12:38
 * @Description:
 **/
@Data
public class PayablePlanNewVo extends BaseEntity {

    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty("申请步骤状态")
    private String stepName;
    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String companyName;

    @ApiModelProperty(value = "应付类型名称")
    private String fkTypeName;

    @ApiModelProperty(value = "实付折合金额（这个金额币种和应付一致，因为已经是折合了）")
    private BigDecimal sumAmountPayable;

    @ApiModelProperty(value = "汇率调整金额")
    private BigDecimal sumAmountExchangeRate;

    @ApiModelProperty(value = "港币金额")
    private BigDecimal sumAmountHkd;

    /**
     * 人民币金额
     */
    @ApiModelProperty(value = "人民币金额")
    private BigDecimal sumAmountRmb;

    @ApiModelProperty(value = "实付")
    private BigDecimal actualPayableAmount;

    @ApiModelProperty(value = "差额")
    private BigDecimal diffPayableAmount;

    /**
     * 学校提供商国家Id(所在地)
     */
    @ApiModelProperty(value = "学校提供商国家Id(所在地)")
    private String fkAreaCountryIds;

    /**
     * 国家名称
     */
    @ApiModelProperty(value = "国家名称")
    private String fkAreaCountryName;

    /**
     * 学生id
     */
    @ApiModelProperty(value = "学生id")
    private Long fkStudentId;

    @ApiModelProperty(value = "学生号")
    private String studentId;

    /**
     * 学生名称
     */
    @ApiModelProperty(value = "学生名称")
    private String studentName;

    @ApiModelProperty(value = "名（英/拼音）")
    private String studentFirstName;

    @ApiModelProperty(value = "姓（英/拼音）")
    private String studentLastName;

    @ApiModelProperty(value = "学生生日")
    private String studentBirthday;

    @ApiModelProperty(value = "学校Id")
    private String institutionId;

    @ApiModelProperty(value = "学校名称/保险开始时间/公寓名称")
    private String istOrApmName;

    @ApiModelProperty(value = "学校简称/保险结束时间/公寓开始时间")
    private String shortNameOrApaStartTime;

    @ApiModelProperty(value = "学校中文名称/护照编号/公寓结束时间")
    private String stuNamePassportApaEnd;

    @ApiModelProperty(value = "学校中文名称简称/公寓天数")
    private String stuCnApaDay;

    @ApiModelProperty(value = "课程Id")
    private Long fkInstitutionCourseId;

    @ApiModelProperty(value = "课程名称")
    private String fkCourseName;

    /**
     * 延迟入学标记
     */
    @ApiModelProperty(value = "延迟入学标记")
    private Boolean isDeferEntrance;

    /**
     * 延迟入学时间
     */
    @ApiModelProperty(value = "延迟入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date maxDeferEntranceTimes;

    /**
     * 课程长度
     */
    @ApiModelProperty(value = "课程长度")
    private BigDecimal duration;

    /**
     * 课程长度类型(0周、1学期、2年)
     */
    @ApiModelProperty(value = "课程长度类型(0周、1学期、2年)")
    private Integer durationType;


    @ApiModelProperty(value = "课程中文名字")
    private String fkCourseNameCn;

    @ApiModelProperty(value = "开学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private String openingTime;

    @ApiModelProperty("延迟入学时间（最终开学时间）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;

    private String bziName24;

    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    @ApiModelProperty(value = "名称备注")
    private String agentNameNote;

    @ApiModelProperty(value = "币种名称")
    private String fkCurrencyTypeName;

    /**
     * 旧系统学校名称
     */
    @ApiModelProperty(value = "旧系统学校名称")
    @Column(name = "old_institution_name")
    private String oldInstitutionName;

    /**
     * 旧系统学校全称
     */
    @ApiModelProperty(value = "旧系统学校全称")
    @Column(name = "old_institution_full_name")
    private String oldInstitutionFullName;

    /**
     * 旧系统课程名称
     */
    @ApiModelProperty(value = "旧系统课程名称")
    @Column(name = "old_course_custom_name")
    private String oldCourseCustomName;

    /**
     * 应付计划的目标对象对应的应收计划列表（receivableAmount-应收金额、actualReceivableAmount-实收金额、diffReceivableAmount-收款差额）
     */
    @ApiModelProperty(value = "应付计划的目标对象对应的应收计划列表（receivableAmount-应收金额、actualReceivableAmount-实收金额、diffReceivableAmount-收款金额）")
    private List<Map<String,Object>> receivablePlanList;

    /**
     * 是否后续课程：0否/1是
     */
    @ApiModelProperty(value = "是否后续课程")
    private Boolean isFollow;

    /**
     * 父计划id
     */
    @ApiModelProperty(value = "父计划id")
    private Long fkParentStudentOfferItemId;

    /**
     * 申请方案Id
     */
    @ApiModelProperty(value = "申请方案Id")
    private Long fkStudentOfferId;

    /**
     * 付款状态：0/1/2：未付/已付部分/已付齐
     */
    @ApiModelProperty(value = "付款状态：0/1/2：未付/已付部分/已付齐")
    private Integer payableStatus;

    @ApiModelProperty(value = "申请备注")
    private String appRemark;

    @ApiModelProperty(value = "备注详情")
    private PlanRemarkDetailVo planRemarkDetailDto;

    @ApiModelProperty("bd编号")
    private String bdCode;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty("代理id")
    private Long fkAgentId;


    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    @ApiModelProperty("BD Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "性别")
    private String genderName;

    @ApiModelProperty(value = "申请计划编号")
    private String offerItemNum;

    @ApiModelProperty(value = "学生佣金结算标记关键字")
    private String commissionMark;

    @ApiModelProperty(value = "收款状态  0:未收 1：部分已收 2：已收齐")
    private Integer tradeStatus;

    @ApiModelProperty(value = "收款状态名  0:未收 1：部分已收 2：已收齐")
    private String tradeStatusName;

    @ApiModelProperty(value = "受保人护照编号")
    private String insurantPassportNum;

    @ApiModelProperty(value = "保险业务信息")
    private String insuranceInfo;

    //==================实体类PayablePlan=====================


    /**
     * 应付类型关键字，枚举，如：m_student_offer_item
     */
    @ApiModelProperty(value = "应付类型关键字，枚举，如：m_student_offer_item")
    @Column(name = "fk_type_key")
    private String fkTypeKey;

    /**
     * 应付类型对应记录Id，如：m_student_offer_item.id
     */
    @ApiModelProperty(value = "应付类型对应记录Id，如：m_student_offer_item.id")
    @Column(name = "fk_type_target_id")
    private Long fkTypeTargetId;

    /**
     * 应收计划Id
     */
    @ApiModelProperty(value = "应收计划Id")
    @Column(name = "fk_receivable_plan_id")
    private Long fkReceivablePlanId;

    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    @Column(name = "summary")
    private String summary;

    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;

    /**
     * 学费金额
     */
    @ApiModelProperty(value = "学费金额")
    @Column(name = "tuition_amount")
    private BigDecimal tuitionAmount;

    /**
     * 费率%(代理)
     */
    @ApiModelProperty(value = "费率%(代理)")
    @Column(name = "commission_rate")
    private BigDecimal commissionRate;

    /**
     * 代理分成比率%
     */
    @ApiModelProperty(value = "代理分成比率%")
    @Column(name = "split_rate")
    private BigDecimal splitRate;

    /**
     * 佣金金额(代理)
     */
    @ApiModelProperty(value = "佣金金额(代理)")
    @Column(name = "commission_amount")
    private BigDecimal commissionAmount;

    /**
     * 定额金额
     */
    @ApiModelProperty(value = "定额金额")
    @Column(name = "fixed_amount")
    private BigDecimal fixedAmount;

    /**
     * 其他金额
     */
    @ApiModelProperty(value = "其他金额")
    @Column(name = "bonus_amount")
    private BigDecimal bonusAmount;

    /**
     * 应付金额
     */
    @ApiModelProperty(value = "应付金额")
    @Column(name = "payable_amount")
    private BigDecimal payableAmount;

    /**
     * 计划付款时间
     */
    @ApiModelProperty(value = "计划付款时间")
    @Column(name = "payable_plan_date")
    @JsonFormat(pattern="yyyy-MM-dd")
    private Date payablePlanDate;

    /**
     * 是否预付，0否/1是
     */
    @ApiModelProperty(value = "是否预付，0否/1是")
    @Column(name = "is_pay_in_advance")
    private Boolean isPayInAdvance;

    @ApiModelProperty(value = "预付百分比：50, 100")
    @UpdateWithNull
    private Integer payInAdvancePercent;

    /**
     * 状态：0关闭/1打开/2完成
     */
    @ApiModelProperty(value = "状态：0关闭/1打开/2完成")
    @Column(name = "status")
    private Integer status;

    /**
     * 结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总
     */
    @ApiModelProperty(value = "结算状态：0未结算/1结算中/2代理确认/3财务确认/4财务汇总")
    @Column(name = "status_settlement")
    private Integer statusSettlement;

    /**
     * 旧数据财务id(gea)
     */
    @ApiModelProperty(value = "旧数据财务id(gea)")
    @Column(name = "id_gea_finance")
    private String idGeaFinance;

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("代理标签")
    private List<AgentLabelVo> agentLabelVos;

}
