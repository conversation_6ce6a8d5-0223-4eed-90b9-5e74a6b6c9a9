package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.ConventionProcedure;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/7/2 11:22
 * @verison: 1.0
 * @description:
 */
@Data
@ApiModel(value = "峰会流程返回类")
public class ConventionProcedureVo extends BaseEntity {
    /**
     * 参加峰会流程人数
     */
    @ApiModelProperty(value = "参加峰会流程人数")
    private Long personProcedureCount;

    /**
     * 参加流程人员信息(中间表)
     */
    @ApiModelProperty(value = "参加流程人员和流程关联信息")
    private List<ConventionPersonProcedureVo> conventionPersonProcedureDtos;

    /**
     * 是否参加
     */
    @ApiModelProperty(value = "是否参加")
    private boolean join;
    /**
     * 参会人员id
     */
    @ApiModelProperty(value = "参会人员id")
    private Long personId;


    /**
     * 媒体附件
     */
    @ApiModelProperty(value = "媒体附件")
    private List<MediaAndAttachedVo> mediaAndAttachedDtos;

    //==============实体类ConventionProcedure================
    private static final long serialVersionUID = 1L;
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @Column(name = "fk_convention_id")
    private Long fkConventionId;
    /**
     * 流程涉及分配桌子类型key(可无)
     */
    @ApiModelProperty(value = "流程涉及分配桌子类型key(可无)")
    @Column(name = "fk_table_type_key")
    private String fkTableTypeKey;
    /**
     * 流程主题
     */
    @ApiModelProperty(value = "流程主题")
    @Column(name = "subject")
    private String subject;
    /**
     * 举行地点
     */
    @ApiModelProperty(value = "举行地点")
    @Column(name = "venue")
    private String venue;
    /**
     * 流程描述
     */
    @ApiModelProperty(value = "流程描述")
    @Column(name = "description")
    private String description;
    /**
     * 计划开始时间
     */
    @ApiModelProperty(value = "计划开始时间")
    @Column(name = "start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date startTime;
    /**
     * 计划结束时间
     */
    @ApiModelProperty(value = "计划结束时间")
    @Column(name = "end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date endTime;
    /**
     * 步骤索引
     */
    @ApiModelProperty(value = "步骤索引")
    @Column(name = "step_index")
    private Integer stepIndex;
}
