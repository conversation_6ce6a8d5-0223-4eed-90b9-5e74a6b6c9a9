package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.platformconfigcenter.vo.UserAgentVo;
import com.get.platformconfigcenter.vo.UserInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/10/25 14:33
 */
@Data
public class NewIssueUserSuperiorVo extends BaseEntity {
    /**
     * 用户Id
     */
    @ApiModelProperty(value = "用户Id")
    private Long fkUserId;

    /**
     * 上司Id
     */
    @ApiModelProperty(value = "上司Id")
    private Long fkUserSuperiorId;

    @ApiModelProperty("已经绑定")
    private List<UserInfoVo> isExistDatas;

    @ApiModelProperty("未绑定")
    private List<UserInfoVo> notExistDatas;

}
