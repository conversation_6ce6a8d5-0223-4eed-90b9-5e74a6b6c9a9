package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2022/3/16 11:05
 */
@Data
public class AplOrderVo {

    private Long id;

    @ApiModelProperty("中文名字")
    private String nameZh;

    @ApiModelProperty("英文名字")
    private String nameEn;

    @ApiModelProperty("ai表id")
    private Integer orderId;

    @ApiModelProperty("项目成员名字")
    private String projeckName;

    @ApiModelProperty("国家名字")
    private String countryName;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("rpa账号")
    private String rpaAccount;

    @ApiModelProperty("rpa密码")
    private String rpaPassword;

    @ApiModelProperty("员工名字")
    private String bdName;

    @ApiModelProperty("代理名字")
    private String agentName;

    @ApiModelProperty("学校名字")
    private String schoolName;

    @ApiModelProperty("课程名字")
    private String courseName;

    @ApiModelProperty("新系统课程id")
    private Long fkInstitutionCourseId;

    @ApiModelProperty("新系统学校id")
    private Long fkInstitutionId;

    @ApiModelProperty("旧系统学校名称")
    private String oldInstitutionName;

    @ApiModelProperty("旧系统课程名称")
    private String oldCourseCustomName;


    @ApiModelProperty("rpa状态")
    private String rpaState;

    @ApiModelProperty("rpa创建时间")
    private Date rpaCreateTime;

    @ApiModelProperty("rpa完成时间")
    private Date rpafinishTime;

    @ApiModelProperty("rpa反馈信息")
    private String rpaRobotReturnMsg;

    @ApiModelProperty("rpa备注")
    private String peopleMsg;

    @ApiModelProperty("新系统学校id")
    private Long issueSchoolId;

    @ApiModelProperty("旧系统学校名称（有就传）")
    private String oldIssueSchoolName;

    @ApiModelProperty("新系统课程id")
    private Long issueCourseId;

    @ApiModelProperty("旧系统课程名称（有就传）")
    private String oldIssueCourseNmae;

    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("公司id")
    private Long fkCompanyId;

    @ApiModelProperty("代理id")
    private Long fkAgentId;

    @ApiModelProperty("备注")
    private String nameNote;

    @ApiModelProperty("方案id")
    private Long fkStudentOfferId;

    @ApiModelProperty("方案联系人邮箱")
    private String fkContactPersonEmail;

    @ApiModelProperty("bms学生id")
    private Long fkBmsStudentId;

}
