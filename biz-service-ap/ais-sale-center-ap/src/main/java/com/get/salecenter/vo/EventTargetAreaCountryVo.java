package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.EventTargetAreaCountry;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Sea
 * @create: 2020/12/7 18:31
 * @verison: 1.0
 * @description:
 */
@Data
public class EventTargetAreaCountryVo extends BaseEntity {

    //=========实体类EventTargetAreaCountry============
    private static final long serialVersionUID = 1L;
    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
    @Column(name = "fk_event_id")
    private Long fkEventId;
    /**
     * 活动目标对象国家Id
     */
    @ApiModelProperty(value = "活动目标对象国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
}
