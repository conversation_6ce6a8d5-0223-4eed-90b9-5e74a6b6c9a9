package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2024/1/19
 * @TIME: 15:50
 * @Description:
 **/
@Data
public class EnrolledConversionRateStatisticsVo {
    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    @ApiModelProperty(value = "入学年份")
    private Integer year;


    @ApiModelProperty(value = "对应字段（表头）")
    private String field;


    @ApiModelProperty(value = "申请数")
    private BigDecimal applicationCount;


    @ApiModelProperty(value = "成功入学数")
    private BigDecimal successCount;


    @ApiModelProperty(value = "转化率")
    private String conversionRate;


    @ApiModelProperty(value = "合并")
    private String concat;
}
