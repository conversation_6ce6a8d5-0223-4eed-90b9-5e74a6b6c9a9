package com.get.salecenter.vo;


import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.Translation;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2020/12/16
 * @TIME: 12:59
 * @Description:
 **/
@Data
public class TranslationVo extends BaseEntity {


    //========实体类Translation===========
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "表名")
    private String fkTableName;

    @ApiModelProperty(value = "表Id")
    private Long fkTableId;

    @ApiModelProperty(value = "翻译配置Id")
    private Long fkTranslationMappingId;

    @ApiModelProperty(value = "语言枚举code：zh-hk/en-us")
    private String languageCode;

    @ApiModelProperty(value = "翻译内容")
    private String translation;
}
