package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2021/9/9
 * @TIME: 11:37
 * @Description:
 **/
@Data
public class ConventionAwardDto extends BaseVoEntity implements Serializable  {
    /**
     * 峰会Id
     */
    @ApiModelProperty(value = "峰会Id")
    @NotNull(message = "峰会Id不能为空", groups = {Add.class, Update.class})
    private Long fkConventionId;

    /**
     * 奖品名称
     */
    @ApiModelProperty(value = "奖品名称")
    @NotBlank(message = "奖品名称不能为空", groups = {Add.class, Update.class})
    private String awardName;

    /**
     * 奖品描述
     */
    @ApiModelProperty(value = "奖品描述")
    private String awardDescription;

    /**
     * 奖品数量
     */
    @ApiModelProperty(value = "奖品数量")
    @NotNull(message = "峰会Id不能为空", groups = {Add.class, Update.class})
    private Integer awardCount;

    /**
     * 奖品提供机构名称
     */
    @ApiModelProperty(value = "奖品提供机构名称")
    @NotBlank(message = "奖品提供机构名称不能为空", groups = {Add.class, Update.class})
    private String providerName;

    /**
     * 奖品提供机构副名称
     */
    @ApiModelProperty(value = "奖品提供机构副名称")
    private String providerNameSub;

    /**
     * 可获得角色，可多选，逗号分割：0校方/1代理/2嘉宾/3员工/4工作人员
     */
    @ApiModelProperty(value = "可获得角色，可多选，逗号分割：0校方/1代理/2嘉宾/3员工/4工作人员")
    @NotBlank(message = "角色不能为空", groups = {Add.class, Update.class})
    private String getRole;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
    /**
     * 奖品图片
     */
    @ApiModelProperty(value = "奖品图片")
    private List<MediaAndAttachedDto> mediaAttachedVos;
    /**
     * 可获得用户id，多个用逗号隔开，如：1,2
     */
    @ApiModelProperty(value = "可获得用户id，多个用逗号隔开，如：1,2")
    private String getFkConventionPersonId;
}
