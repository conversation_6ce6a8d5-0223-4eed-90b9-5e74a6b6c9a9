package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * author:Neil
 * Time: 14:58
 * Date: 2023/1/17
 * Description:
 */
@Data
public class StudentReceivableAndPaySumDetailVo {

    @ApiModelProperty(value = "应收金额")
    private BigDecimal receivableAmount;


    @ApiModelProperty(value = "实收金额")
    private BigDecimal actualReceivableAmount;

    @ApiModelProperty(value = "应收未收")
    private BigDecimal diffReceivableAmount;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal actualPayableAmount;

    @ApiModelProperty(value = "应付未付")
    private BigDecimal diffPayableAmount;

    @ApiModelProperty(value = "应收币种")
    private String receivableCurrencyTypeNum;

    @ApiModelProperty(value = "应付币种")
    private String payCurrencyTypeNum;
}
