package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StudentOffer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * @author: Neil
 * @description: 查询学生业务代理绑定DTO
 * @date: 2022/5/9 16:04
 * @return
 */
@Data
public class StudentOfferBindingVo extends BaseEntity {

    @ApiModelProperty(value = "学生id")
    private Long studentId;

    @ApiModelProperty(value = "学生编号")
    private String studentNum;

    @ApiModelProperty(value = "学生名称")
    private String studentName;

    @ApiModelProperty(value = "学生名称（英文）")
    private String studentNameEng;

    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    @ApiModelProperty(value = "BD名称")
    private String bdName;

    @ApiModelProperty(value = "国家名字")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "是否issue创建")
    private Boolean isIssueCreat = false;

    @ApiModelProperty(value = "是否存在实付佣金绑定数据")
    private Boolean existPaymentForm = false;

    //==========实体类StudentOffer============
    private static final long serialVersionUID = 1L;
    /**
     * 学生Id
     */
    @ApiModelProperty(value = "学生Id")
    @Column(name = "fk_student_id")
    private Long fkStudentId;
    /**
     * 代理Id（业绩绑定）
     */
    @ApiModelProperty(value = "代理Id（业绩绑定）")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 代理联系人Id
     */
//    @ApiModelProperty(value = "代理联系人Id")
//    @Column(name = "fk_contact_person_id")
//    private Long fkContactPersonId;
    /**
     * 员工Id（业绩绑定，BD）
     */
    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;
    /**
     * 国家Id
     */
    @ApiModelProperty(value = "申请方案终止作废原因Id")
    @Column(name = "fk_cancel_offer_reason_id")
    private Long fkCancelOfferReasonId;
    /**
     * 申请人员工Id
     */
    @ApiModelProperty(value = "申请人员工Id")
    @Column(name = "fk_staff_id_workflow")
    private Long fkStaffIdWorkflow;
    /**
     * 申请方案编号
     */
    @ApiModelProperty(value = "申请方案编号")
    @Column(name = "num")
    private String num;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Column(name = "remark")
    private String remark;
    /**
     * 状态：0关闭/1打开
     */
    @ApiModelProperty(value = "状态：0关闭/1打开")
    @Column(name = "status")
    private Integer status;
    /**
     * 状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销
     */
    @ApiModelProperty(value = "状态：0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废/6撤销")
    @Column(name = "status_workflow")
    private Integer statusWorkflow;
    /**
     * 旧数据id(gea)
     */
    @ApiModelProperty(value = "旧数据id(gea)")
    @Column(name = "id_gea")
    private String idGea;
    /**
     * 旧数据id(iae)
     */
    @ApiModelProperty(value = "旧数据id(iae)")
    @Column(name = "id_iae")
    private String idIae;

    @ApiModelProperty(value = "联系人邮箱")
    @Column(name = "agent_contact_email")
    private String agentContactEmail;
}
