package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.EnrolFailureReason;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;

/**
 * <AUTHOR>
 * @DATE: 2021/6/16
 * @TIME: 15:01
 * @Description:
 **/
@Data
public class EnrolFailureReasonVo extends BaseEntity {

    /**
     * 数量
     */
    @ApiModelProperty(value = "学习计划数量")
    private Long itemNum;

    //==========实体类EnrolFailureReason=============
    private static final long serialVersionUID = 1L;
    /**
     * 入学失败原因名称
     */
    @ApiModelProperty(value = "入学失败原因名称")
    @Column(name = "reason_name")
    private String reasonName;
    /**
     * 原因Key
     */
    @ApiModelProperty(value = "原因Key")
    @Column(name = "reason_key")
    private String reasonKey;
    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    @Column(name = "view_order")
    private Integer viewOrder;

}
