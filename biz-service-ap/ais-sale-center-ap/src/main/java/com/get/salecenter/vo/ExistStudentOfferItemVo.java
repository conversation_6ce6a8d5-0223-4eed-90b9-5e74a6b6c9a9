package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ExistStudentOfferItemVo {

    @ApiModelProperty("类型")
    private String type;

    @ApiModelProperty("申请计划是否有重复：  true 重复  false 不重复")
    private boolean isExistsStudentOfferItem;

    @ApiModelProperty("申请方案id：提示信息")
    private Map<String, String> maps;

}
