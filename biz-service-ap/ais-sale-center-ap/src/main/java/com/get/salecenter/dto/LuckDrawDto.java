package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 峰会抽奖Vo
 *
 * <AUTHOR>
 * @date 2023/2/2 16:53
 */
@Data
public class LuckDrawDto extends BaseVoEntity {
    /**
     * 峰会id
     */
    @ApiModelProperty(value = "峰会id")
    @NotNull(message = "峰会id不能为空", groups = {Add.class, Update.class})
    private Long fkConventionId;

    @ApiModelProperty(value = "重新抽奖code")
    private List<String> awardCodeList;


    /**
     * 峰会奖品Id
     */
    @ApiModelProperty(value = "峰会奖品Id")
    @NotNull(message = "峰会奖品Id不能为空", groups = {Add.class, Update.class})
    private Long fkConventionAwardId;

    @ApiModelProperty(value = "抽奖次数")
    private Integer lotteryCount = 1;

   
}
