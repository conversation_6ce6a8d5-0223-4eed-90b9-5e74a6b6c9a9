package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

@Data
public class PayablePlanSettlementBatchExchangeVo extends BaseEntity {
    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "代理名")
    private String agentName;

    @ApiModelProperty(value = "应付类型名称")
    private String fkTypeName;

    @ApiModelProperty(value = "外币弹框专用字段 true:为编辑过的币种 false:为未编辑币种")
    private boolean flag;

    //=============实体类PayablePlanSettlementBatchExchange================


    /**
     * 目标类型关键字，枚举：m_student_offer留学申请方案/m_student_insurance留学保险/m_student_accommodation留学住宿
     */
    @ApiModelProperty(value = "目标类型关键字，枚举：m_student_offer留学申请方案/m_student_insurance留学保险/m_student_accommodation留学住宿")
    @Column(name = "fk_type_key")
    private String fkTypeKey;
    /**
     * 财务结算汇总批次号
     */
    @ApiModelProperty(value = "财务结算汇总批次号")
    @Column(name = "num_settlement_batch")
    private String numSettlementBatch;
    /**
     * 学生代理Id
     */
    @ApiModelProperty(value = "学生代理Id")
    @Column(name = "fk_agent_id")
    private Long fkAgentId;
    /**
     * 币种编号（原币种）
     */
    @ApiModelProperty(value = "币种编号（原币种）")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 币种编号（兑换币种）
     */
    @ApiModelProperty(value = "币种编号（兑换币种）")
    @Column(name = "fk_currency_type_num_exchange")
    private String fkCurrencyTypeNumExchange;
    /**
     * 兑换汇率
     */
    @ApiModelProperty(value = "兑换汇率")
    @Column(name = "exchange_rate")
    private BigDecimal exchangeRate;
    /**
     * 手续费
     */
    @ApiModelProperty(value = "手续费")
    @Column(name = "service_fee")
    private BigDecimal serviceFee;
}