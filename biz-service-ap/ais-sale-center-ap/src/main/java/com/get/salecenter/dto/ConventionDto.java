package com.get.salecenter.dto;

import com.baomidou.mybatisplus.core.conditions.update.Update;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2020/6/30 17:41
 * @verison: 1.0
 * @description:
 */
@Data
public class ConventionDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 活动Id
     */
    @ApiModelProperty(value = "活动Id")
    private Long fkEventId;

    /**
     * 主题名称
     */
    @NotBlank(message = "主题名称不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "主题名称", required = true)
    private String themeName;

    /**
     * 峰会开始时间
     */
    @NotNull(message = "峰会开始时间不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会开始时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date startTime;

    /**
     * 峰会结束时间
     */
    @NotNull(message = "峰会结束时间不能为空", groups = {Add.class, Update.class})
    @ApiModelProperty(value = "峰会结束时间", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date endTime;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String description;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    private String year;

    /**
     * 媒体附件id
     */
    @ApiModelProperty(value = "媒体附件id")
    private List<Long> fkId;
}
