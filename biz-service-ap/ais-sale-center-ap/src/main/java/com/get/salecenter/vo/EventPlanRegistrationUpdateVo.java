package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.EventPlanRegistrationContactPerson;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2024/1/16
 * @TIME: 14:19
 * @Description:
 **/
@Data
public class EventPlanRegistrationUpdateVo extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "发票建议币种")
    private String fkCurrencyTypeNumInvoice;

    @ApiModelProperty(value = "联系人信息")
    private List<EventPlanRegistrationContactPerson> EventPlanRegistrationContactPersonList;
}
