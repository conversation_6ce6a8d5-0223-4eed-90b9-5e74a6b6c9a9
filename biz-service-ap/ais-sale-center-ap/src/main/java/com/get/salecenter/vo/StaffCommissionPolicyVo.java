package com.get.salecenter.vo;

import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseEntity;
import com.get.salecenter.entity.StaffCommissionPolicy;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * @author: Hardy
 * @create: 2023/2/6 17:02
 * @verison: 1.0
 * @description:
 */
@Data
public class StaffCommissionPolicyVo extends BaseEntity {

    @ApiModelProperty("项目成员角色")
    private String fkStudentProjectRoleKeyName;

    @ApiModelProperty("结算步骤")
    private String fkStaffCommissionStepKeyName;

    @ApiModelProperty("项目成员角色")
    private Long fkStudentProjectRoleId;

    @ApiModelProperty("结算步骤")
    private Long fkStaffCommissionStepKeyId;

    @ApiModelProperty("公司名称")
    private String fkCompanyName;

    @ApiModelProperty("学校名称")
    private String fkInstitutionName;

    @ApiModelProperty("课程等级名称")
    private String fkMajorLevelName;

    @ApiModelProperty("国家名称")
    private String fkAreaCountryName;

    @ApiModelProperty("金额类型")
    private Integer amountType;

    @ApiModelProperty("金额类型")
    private String amountTypeName;

    @ApiModelProperty("提成额")
    private BigDecimal commissionAmount;

    //==========实体类StaffCommissionPolicy============
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    /**
     * 学生项目角色key
     */
    @ApiModelProperty(value = "学生项目角色key")
    @Column(name = "fk_student_project_role_key")
    private String fkStudentProjectRoleKey;

    /**
     * 员工提成业务步骤key
     */
    @ApiModelProperty(value = "员工提成业务步骤key")
    @Column(name = "fk_staff_commission_step_key")
    private String fkStaffCommissionStepKey;

    /**
     * 国家Id
     */
    @ApiModelProperty(value = "国家Id")
    @Column(name = "fk_area_country_id")
    private Long fkAreaCountryId;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校Id")
    @Column(name = "fk_institution_id")
    private Long fkInstitutionId;

    /**
     * 专业等级Id
     */
    @ApiModelProperty(value = "专业等级Id")
    @Column(name = "fk_major_level_id")
    private Long fkMajorLevelId;

    /**
     * 定额金额
     */
    @UpdateWithNull
    @ApiModelProperty(value = "定额金额")
    @Column(name = "fixed_amount")
    private BigDecimal fixedAmount;

    /**
     * 学费比率%
     */
    @UpdateWithNull
    @ApiModelProperty(value = "学费比率%")
    @Column(name = "commission_rate")
    private BigDecimal commissionRate;

    /**
     * 优先级，数字越大越优先，优先匹配对应的提成金额
     */
    @ApiModelProperty(value = "优先级，数字越大越优先，优先匹配对应的提成金额")
    @Column(name = "priority")
    private Integer priority;

    private static final long serialVersionUID = 1L;
}
