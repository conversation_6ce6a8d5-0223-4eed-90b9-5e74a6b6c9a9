package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * KPI考核人员数
 */
@Data
public class KpiPlanStaffTreeVo {
    @ApiModelProperty(value = "KPI方案考核人员主键id")
    private Long id;

    @ApiModelProperty(value = "KPI方案Id")
    private Long fkKpiPlanId;

    @ApiModelProperty(value = "目标设置人Id")
    private Long fkStaffIdAdd;

    @ApiModelProperty(value = "统计角色，枚举：BD=1/项目成员=2/PM=3")
    private Integer countRole;

    @ApiModelProperty(value = "统计角色，枚举：BD=1/项目成员=2/PM=3")
    private String countRoleName;

    @ApiModelProperty(value = "统计方式，枚举：个人=1/团队(含业务下属)=2")
    private Integer countMode;

    @ApiModelProperty(value = "统计方式，枚举：个人=1/团队(含业务下属)=2")
    private String countModeName;

    @ApiModelProperty(value = "考核人员Id")
    private Long fkStaffId;

    @ApiModelProperty(value = "考核人员姓名")
    private String staffName;

    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    @ApiModelProperty(value = "考核人员标签集合")
    private List<KpiPlanStaffLabelVo> KpiPlanStaffLabelDtoList;

    @ApiModelProperty(value = "层级")
    private Integer level;

    @ApiModelProperty(value = "子节点")
    private List<KpiPlanStaffTreeVo> children;
}
