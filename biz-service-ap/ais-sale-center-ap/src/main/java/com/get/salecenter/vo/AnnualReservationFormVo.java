package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @author: Hardy
 * @create: 2021/7/7 16:20
 * @verison: 1.0
 * @description:
 */
@Data
public class AnnualReservationFormVo {

    /**
     * 参加人id
     */
    @ApiModelProperty(value = "参加人id")
    private Long id;

    /**
     * 展位报名Id
     */
    @ApiModelProperty(value = "展位报名Id")
    private Long conventionRegistrationId;
    /**
     * 回执码
     */
    @ApiModelProperty(value = "回执码")
    private String receiptCode;

    /**
     * 机构名
     */
    @ApiModelProperty(value = "机构名")
    private String institutionName;

    /**
     * 展位名
     */
    @ApiModelProperty(value = "展位名")
    private String boothName;

//    /**
//     * 中文姓
//     */
//    @ApiModelProperty(value = "中文姓")
//    private String lastNameChn;
//
//    /**
//     * 中文名
//     */
//    @ApiModelProperty(value = "中文名")
//    private String firstNameChn;

    /**
     * 中文名
     */
    @ApiModelProperty(value = "中文名")
    private String nameChn;

//    /**
//     * 英文姓/拼音姓
//     */
//    @ApiModelProperty(value = "英文姓/拼音姓")
//    private String lastName;
//
//    /**
//     * 英文名/拼音名
//     */
//    @ApiModelProperty(value = "英文名/拼音名")
//    private String firstName;

    /**
     * 英文名/拼音名
     */
    @ApiModelProperty(value = "英文名/拼音名")
    private String name;

    /**
     * 性别：0女/1男
     */
    @NotNull(message = "性别不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "性别：0女/1男", required = true)
    private Integer gender;

    /**
     * 参加人职位
     */
    @ApiModelProperty(value = "参加人职位")
    private String title;

    /**
     * 参加人电邮
     */
    @ApiModelProperty(value = "参加人电邮")
    private String email;

    /**
     * 参加人电话
     */
    @NotBlank(message = "参加人电话不能为空", groups = {BaseVoEntity.Add.class, BaseVoEntity.Update.class})
    @ApiModelProperty(value = "参加人电话", required = true)
    private String tel;

    /**
     * 到达时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "到达时间")
    private Date arrivalTime;

    /**
     * 到达交通类型
     */
    @ApiModelProperty(value = "到达交通类型")
    private Integer arrivalTransportation;

    /**
     * 到达交通编号
     */
    @ApiModelProperty(value = "到达交通编号")
    private String arrivalTransportationCode;

    /**
     * 离开时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty("离开时间")
    private Date leaveTime;

    /**
     * 离开交通类型
     */
    @ApiModelProperty(value = "离开交通类型")
    private Integer leaveTransportation;

    /**
     * 离开交通编号
     */
    @ApiModelProperty(value = "离开交通编号")
    private String leaveTransportationCode;

    /**
     * 入住时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "入住时间")
    private Date checkInTime;

    /**
     * 退房时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "退房时间")
    private Date checkOutTime;

    /**
     * 房间类型名称
     */
    @ApiModelProperty(value = "房间类型名称")
    private String roomTypeName;

    /**
     * 酒店房型Id
     */
    @ApiModelProperty(value = "酒店房型Id")
    private Long fkConventionHotelId;

//    /**
//     * 峰会流程
//     */
//    @ApiModelProperty("峰会流程")
//    private List<Long> conventionProcedureIds;

    /**
     * 护照号
     */
    @ApiModelProperty(value = "护照号")
    private String passportNum;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号")
    private String idCardNum;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 峰会流程
     */
    @ApiModelProperty(value = "峰会流程")
    private List<Long> conventionProcedureIds;

    /**
     * 证件内容
     */
    @ApiModelProperty(value = "证件内容")
    private String documentContent;

    /**
     * 证件类型
     */
    @ApiModelProperty(value = "证件类型")
    private Integer documentType;

    /**
     * 支付状态：0未支付/1完成支付
     */
    @ApiModelProperty(value = "支付状态：0未支付/1完成支付")
    private Integer payType;

//    @ApiModelProperty(value = "同住人id")
//    private Long residentId;

    @ApiModelProperty(value = "同住人姓名")
    private String residentName;

    @ApiModelProperty(value = "同住人姓名（中文）")
    private String residentNChn;
}
