package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class StudentInsuranceExportVo {

    @ApiModelProperty(value = "所属公司")
    private String fkCompanyName;

    @ApiModelProperty(value = "状态")
    private String statusName;

    @ApiModelProperty(value = "结算状态")
    private String settlementStatusName;

    @ApiModelProperty(value = "应收币种")
    private String receivableCurrencyTypeName;

    @ApiModelProperty(value = "应收金额")
    private BigDecimal amountReceivable;

    @ApiModelProperty(value = "应付币种")
    private String payableCurrencyTypeName;

    @ApiModelProperty(value = "应付金额")
    private BigDecimal payableAmount;

    @ApiModelProperty(value = "实付金额")
    private BigDecimal actualPayableAmount;

    @ApiModelProperty(value = "应付未付")
    private BigDecimal diffPayableAmount;

    @ApiModelProperty(value = "收款状态")
    private String arStatusName;

    @ApiModelProperty(value = "付款状态")
    private String apStatusName;

    /**
     * 付款情况信息显示（币种=payableCurrencyTypeName，实付金额=actualPayableAmount，付款时间=gmtCreate）
     */
    @ApiModelProperty(value = "付款情况")
    private String payDetail;

    @ApiModelProperty(value = "留学保险编号")
    private String num;

    @ApiModelProperty(value = "受保人姓名")
    private String studentName;

    @ApiModelProperty(value = "受保人英文名")
    private String englishName;

    @ApiModelProperty(value = "生日")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date birthday;

    @ApiModelProperty(value = "性别")
    private String genderName;

    @ApiModelProperty(value = "护照编号")
    private String passportNum;

    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    @ApiModelProperty(value = "负责BD")
    private String staffName;

    @ApiModelProperty(value = "佣金合同方")
    private String channelName;

    @ApiModelProperty(value = "服务提供商/产品")
    private String businessProviderAndProductName;

    @ApiModelProperty(value = "前往国家/地区")
    private String fkAreaCountryName;

    @ApiModelProperty(value = "保险单号")
    private String insuranceNum;

    @ApiModelProperty(value = "保单开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceStartTime;

    @ApiModelProperty(value = "保单结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceEndTime;

    @ApiModelProperty(value = "保险购买时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date insuranceBuyTime;

    @ApiModelProperty(value = "购买账户")
    private String buyAccount;

    @ApiModelProperty(value = "保险费币种")
    private String fkCurrencyTypeNumInsurance;

    @ApiModelProperty(value = "保险金额")
    private BigDecimal insuranceAmount;

    @ApiModelProperty(value = "保险费金额说明")
    private String insuranceAmountNote;

    @ApiModelProperty(value = "支付方式")
    private String paymentMethodName;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "绑定项目成员")
    private String projectRoleName;

    @ApiModelProperty("创建人")
    private String gmtCreateUser;

    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date gmtCreate;

    @ApiModelProperty(value = "代理标签")
    private String agentLabelNames;
}
