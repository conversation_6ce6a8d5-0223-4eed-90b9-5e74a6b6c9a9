package com.get.salecenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Description: 代理积分导出Dto
 * @return:
 * @Author: Walker
 * @Date: 2022/4/24
 */
@Data
public class AgentSourceExcelVo {

    @ApiModelProperty(value = "代理名")
    private String agentName;

    @ApiModelProperty(value = "总积分")
    private String ttlScore;

    @ApiModelProperty("1分")
    private String kpiScoreOne;

    @ApiModelProperty("KPI 2分")
    private String kpiScoreTwo;

    @ApiModelProperty("KPI 3分")
    private String kpiScoreThree;

    @ApiModelProperty("未付费学生数(2024-01-01至2025-02-29)")
    private String noAdmittedCount;

    @ApiModelProperty("BD")
    private String bdNames;
}
