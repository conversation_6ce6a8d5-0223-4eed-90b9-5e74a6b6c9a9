package com.get.salecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2024/4/23
 * @TIME: 12:32
 * @Description:kpi统计申请计划
 **/
@Data
public class KpiStudentOfferItemVo extends BaseEntity implements Serializable {

    @ApiModelProperty(value = "学生ID")
    private Long fkStudentId;

    @ApiModelProperty(value = "员工Id（业绩绑定，BD）")
    private Long fkStaffId;

    @ApiModelProperty(value = "国家Id")
    private Long fkAreaCountryId;

    @ApiModelProperty(value = "学校Id")
    private Long fkInstitutionId;

    @ApiModelProperty("申请计划延时入学时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date deferOpeningTime;

    @ApiModelProperty(value = "学校提供商")
    private Long fkInstitutionProviderId;

    @ApiModelProperty(value = "课程等级对应的ids，多个用逗号分隔")
    private String fkInstitutionCourseMajorLevelIds;

    @ApiModelProperty(value = "是否有子课程，0否/1是")
    private Boolean isDirect;

    @ApiModelProperty(value = "是否后续课程：0否/1是")
    private Boolean isFollow;

    @ApiModelProperty("学生创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date studentCreateTime;

    @ApiModelProperty("业务步骤登记时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date stepTime;

    /**
     * 申请创建开始时间
     */
    @ApiModelProperty(value = "申请计划创建时间（开始）")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date offerItemCreateTimeStart;


    @ApiModelProperty(value = "旧系统课程专业等级名称，用于过滤手动添加的课程等级")
    private String oldCourseMajorLevelName;

    @ApiModelProperty(value = "代理Id")
    private Long fkAgentId;
}
