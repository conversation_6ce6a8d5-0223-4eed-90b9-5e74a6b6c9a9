package com.get.salecenter.vo;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.util.Date;

/**
 * 合同审批VO
 *
 * <AUTHOR>
 * @since 2025-07-14
 */
@Data
public class AgentContractApprovalVo extends BaseEntity implements Serializable {

    @ApiModelProperty
    private Boolean sendFlag = false;

    // ==============实体类AgentContractApprovalEntity=====================
    /**
     * 学生代理合同Id
     */
    @ApiModelProperty(value = "学生代理合同Id")
    @Column(name = "fk_agent_contract_id")
    private Long fkAgentContractId;

    /**
     * 审批人Id
     */
    @ApiModelProperty(value = "审批人Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;

    /**
     * 审批意见
     */
    @ApiModelProperty(value = "审批意见")
    @Column(name = "approval_comment")
    private String approvalComment;

    /**
     * 审批状态：4审核通过/-4审核驳回
     */
    @ApiModelProperty(value = "审批状态：4审核通过/-4审核驳回")
    @Column(name = "approval_status")
    private Integer approvalStatus;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    @Column(name = "approval_time")
    private Date approvalTime;

    // ==============扩展字段=====================

    /**
     * 审批人姓名
     */
    @ApiModelProperty(value = "审批人姓名")
    private String staffName;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号")
    private String contractNum;

    private static final long serialVersionUID = 1L;
}