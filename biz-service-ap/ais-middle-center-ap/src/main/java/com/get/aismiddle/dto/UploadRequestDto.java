package com.get.aismiddle.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 文件上传DTO
 */
@Data
public class UploadRequestDto {

    @ApiModelProperty(value = "文件")
    @NotNull(message = "文件不能为空")
    private MultipartFile[] files;

    @ApiModelProperty(value = "是否存放私密桶")
    @NotNull(message = "是否存放私密桶不能为空")
    private Boolean isPrivateBucket;

    @ApiModelProperty(value = "文件对应的微服务名称, 如：销售中心,华通伙伴中心")
    @NotBlank(message = "文件对应的微服务名称不能为空")
    private String serviceName;

    @ApiModelProperty(value = "类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
    @NotBlank(message = "类型关键字不能为空")
    private String typeKey;

    @ApiModelProperty(value = "创建人")
    @NotBlank(message = "创建人不能为空")
    private String gmtCreateUser;

    @ApiModelProperty(value = "文件桶前缀,如/appendix")
    @NotBlank(message = "文件桶前缀不能为空")
    private String prefix;

}
