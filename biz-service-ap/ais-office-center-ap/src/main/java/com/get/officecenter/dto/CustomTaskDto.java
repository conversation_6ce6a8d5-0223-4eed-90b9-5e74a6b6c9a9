package com.get.officecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * @Author:cream
 * @Date: 2023/5/30  9:42
 */
@Data
public class CustomTaskDto extends BaseVoEntity {


    @ApiModelProperty("任务id")
    private Long taskId;

    @NotNull(message = "接收人id不能为空",groups = {Add.class,Update.class})
    @ApiModelProperty("接收人员工Id")
    private Long fkStaffIdTo;

    @ApiModelProperty("任务描述")
    private String taskDescription;


    @ApiModelProperty("多人任务json")
    private List<String> taskJsonList;

    @ApiModelProperty(value = "任务截止时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskDeadline;

    @ApiModelProperty(value = "查看人员Ids，逗号分隔：1,2,3")
    private  String  fkStaffIdsView;
}
