package com.get.officecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class TaskItemDto {

    @ApiModelProperty("用户自定义任务Id（主任务ID）")
    @NotNull(message = "主任务ID不能为空")
    private Long fkTaskId;

    @ApiModelProperty("我的任务")
    private Boolean isMyTask;

    @ApiModelProperty("下属任务")
    private Boolean isSubordinateTask;

    @ApiModelProperty("任务状态")
    private Integer taskStatus;

    @ApiModelProperty("任务关联项关键字")
    private String taskRelatedKeyword;

    @ApiModelProperty("代理名称")
    private String agentName;

    @ApiModelProperty("接收任务人名称")
    private String recipientStaffName;
}
