package com.get.officecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

/**
 * @Author:cream
 * @Date: 2023/5/30  12:28
 */
@Data
@Alias("office_comment")
@TableName("s_comment")
public class Comment extends BaseEntity {

    @ApiModelProperty("表名称")
    private String fkTableName;

    @ApiModelProperty("表id")
    private Long fkTableId;

    @ApiModelProperty("备注")
    private String comment;
}
