package com.get.officecenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2022/5/10
 * @TIME: 12:15
 * @Description:新增补卡VO
 **/
@Data
public class ClockInDataDto implements Serializable {
    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    private Long fkCompanyId;
    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
    /**
     * 打卡时间
     */
    @ApiModelProperty("打卡时间")
    private Date clockInTime;

}
