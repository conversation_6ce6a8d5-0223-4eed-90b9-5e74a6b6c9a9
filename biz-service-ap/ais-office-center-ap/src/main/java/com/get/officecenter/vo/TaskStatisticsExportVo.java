package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class TaskStatisticsExportVo {
    @ApiModelProperty("接收人名称")
    private String recipientStaffName;

    @ApiModelProperty("个人任务完成状态数量")
    private Integer taskItemFinishedCount;

    @ApiModelProperty("个人任务未能完成状态数量")
    private Integer taskItemUnfinishedCount;

    @ApiModelProperty("个人任务待处理状态数量")
    private Integer taskItemTodoCount;

    @ApiModelProperty("个人任务总数")
    private Integer taskItemTotal;

    @ApiModelProperty("第一次完成时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date firstTime;

    @ApiModelProperty("最后一次完成时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date lastTime;
}
