//package com.get.officecenter.feign;
//
//import com.get.common.entity.fegin.ActRuTaskDto;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestBody;
//import org.springframework.web.bind.annotation.RequestParam;
//
//import java.util.List;
//import java.util.Map;
//
///**
// * @version 1.0
// * @Author: LEO
// * @Date: 2021/3/30 11:31
// */
//@Component
//@FeignClient(name = "workflow-center")
//public interface FeignWorkFlowService {
//
//    /**
//     * @return com.get.common.result.ResponseBo
//     * @Description :feign调用 开启流程
//     * @Param [procdefKey, businessKey, companyId, map]
//     * <AUTHOR>
//     */
//    @PostMapping("/workflow/startProcess")
//    Boolean startProcess(@RequestParam("procdefKey") String procdefKey,
//                            @RequestParam("businessKey") String businessKey,
//                            @RequestParam("companyId") String companyId,
//                            @RequestBody Map<String, Object> map);
//
//    /**
//     * @Description :feign调用 通过员工id查找对应业务表单id
//     * @Param [staffId, key]
//     * <AUTHOR>
//     */
//    @PostMapping("/workflow/getFromIdsByStaffId")
//    Map<Long, Integer> getFromIdsByStaffId(@RequestParam("staffId") Long staffId, @RequestParam("key") String key);
//
//    /**
//     * @Description :
//     * @Param [expenseClaimFormIds]
//     * <AUTHOR>
//     */
//    @PostMapping("/workflow/getActRuTaskDtosByBusinessKey")
//    Map<Long, ActRuTaskDto> getActRuTaskDtosByBusinessKey(@RequestBody List<Long> expenseClaimFormIds, @RequestParam("procdefKey") String procdefKey);
//}
