package com.get.officecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2022/1/11
 * @TIME: 9:57
 * @Description:工休库存管理
 **/
@Data
@TableName("m_leave_stock")
public class LeaveStock extends BaseEntity implements Serializable {
    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
    /**
     * 公休类型关键字，枚举，同公休单类型一致
     */
    @ApiModelProperty(value = "工休类型关键字，枚举，同工休单类型一致")
    private String leaveTypeKey;
    /**
     * 结余时长
     */
    @ApiModelProperty(value = "结余时长（小时）")
    private BigDecimal leaveStock;
    /**
     * 有效截至时间
     */
    @ApiModelProperty(value = "有效截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveDeadline;
}
