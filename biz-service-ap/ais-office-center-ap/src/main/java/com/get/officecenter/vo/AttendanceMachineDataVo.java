package com.get.officecenter.vo;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @DATE: 2022/4/29
 * @TIME: 17:38
 * @Description:
 **/
@Data
public class AttendanceMachineDataVo extends BaseVoEntity {
    /**
     * 部门
     */
    @ApiModelProperty("部门")
    private String departmentName;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long staffId;

    /**
     * 员工名称
     */
    @ApiModelProperty(value = "员工名称")
    private String staffName;

    /**
     * 考勤号
     */
    @ApiModelProperty(value = "考勤号")
    private String attendanceNum;

    /**
     * 打卡时间
     */
    @ApiModelProperty(value = "打卡时间")
    private String clockInTime;

    /**
     * 机器号
     */
    @ApiModelProperty(value = "机器号")
    private String machineNum;

    /**
     * 编号
     */
    @ApiModelProperty(value = "编号")
    private String number;
    /**
     * 对比方式
     */
    @ApiModelProperty(value = "对比方式")
    private String contrastType;
    /**
     * 卡号
     */
    @ApiModelProperty(value = "卡号")
    private String cardNumber;
}
