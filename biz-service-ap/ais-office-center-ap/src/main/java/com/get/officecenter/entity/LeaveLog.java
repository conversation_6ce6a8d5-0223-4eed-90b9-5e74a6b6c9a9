package com.get.officecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2022/1/11
 * @TIME: 10:07
 * @Description:工休日志
 **/
@Data
@TableName("m_leave_log")
public class LeaveLog extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
    /**
     * 工休类型关键字，枚举，同工休单类型一致
     */
    @ApiModelProperty(value = "工休类型关键字，枚举，同工休单类型一致")
    private String leaveTypeKey;
    /**
     * 操作类型关键字，枚举
     */
    @ApiModelProperty(value = "操作类型关键字，枚举")
    private String optTypeKey;
    /**
     * 时长（小时）
     */
    @ApiModelProperty(value = "时长（小时）")
    private BigDecimal duration;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;
    /**
     * 关联工休库存Id
     */
    @ApiModelProperty(value = "关联工休库存Id")
    private Long fkLeaveStockId;
    /**
     * 工休申请单Id
     */
    @ApiModelProperty(value = "工休申请单Id")
    private Long fkLeaveApplicationFormId;
}