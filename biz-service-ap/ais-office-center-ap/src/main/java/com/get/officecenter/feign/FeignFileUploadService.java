//package com.get.officecenter.feign;
//
//import com.get.common.exception.YException;
//import com.get.common.result.ListResponseBo;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.http.MediaType;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//import org.springframework.web.bind.annotation.RequestPart;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.validation.constraints.NotBlank;
//import javax.validation.constraints.NotNull;
//import java.io.IOException;
//import java.util.List;
//
///**
// * <AUTHOR>
// * @DATE: 2020/7/7
// * @TIME: 17:47
// * @Description: 服务调用接口, 调用文件中心上传文件接口
// **/
//
//
//@Component
//@FeignClient("file-center")
//public interface FeignFileUploadService {
//
//    @PostMapping(value = "file/upload", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    ListResponseBo upload(@RequestPart("files") MultipartFile[] files, @RequestParam("type") String type) throws YException, IOException;
//
//
//    @PostMapping(value = "file/getFileByGuid")
//    ListResponseBo getFile(@RequestParam("guidList") @NotNull(message = "guid不能为空") List<String> guidList,
//                           @NotBlank(message = "type不能为空") @RequestParam("type") String type);
//
//    @PostMapping(value = "file/uploadAppendix", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    ListResponseBo uploadAppendix(@RequestPart("files") MultipartFile[] files, @RequestParam("type") String type) throws YException, IOException;
//
//}
