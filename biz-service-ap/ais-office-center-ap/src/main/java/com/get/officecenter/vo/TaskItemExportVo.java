package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class TaskItemExportVo {

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("接收任务")
    private String recipientStaffName;

    @ApiModelProperty("任务关联类型")
    private String taskItemRelatedTypeName;

    @ApiModelProperty("代理")
    private String agentName;

    @ApiModelProperty("任务关联项")
    private String taskRelatedContent;

    @ApiModelProperty("最新反馈")
    private String lastComment;

    @ApiModelProperty("状态修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statusModifiedTime;
}
