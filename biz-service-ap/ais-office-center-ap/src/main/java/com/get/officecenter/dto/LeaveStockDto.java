package com.get.officecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2022/1/13
 * @TIME: 15:45
 * @Description:工休库存VO
 **/
@Data
public class LeaveStockDto extends BaseVoEntity {
    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
    /**
     * 公休类型关键字，枚举，同公休单类型一致
     */
    @ApiModelProperty(value = "工休类型关键字，枚举，同工休单类型一致")
    private String leaveTypeKey;
    /**
     * 结余时长
     */
    @ApiModelProperty(value = "结余时长（小时）")
    private BigDecimal leaveStock;
    /**
     * 有效截至时间
     */
    @ApiModelProperty(value = "有效截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveDeadline;
    /**
     * 搜索关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;

    /**
     * 调整时长类型：0增加/1减少
     */
    @ApiModelProperty(value = "调整时长类型：0增加/1减少")
    private Integer status;
    /**
     * 调整时长（小时）
     */
    @ApiModelProperty(value = "调整时长（小时）")
    private BigDecimal changeLeaveStock;
    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 操作类型关键字，枚举
     */
    @ApiModelProperty(value = "操作类型关键字，枚举")
    private String optTypeKey;

    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工Id")
    private List<Long> fkStaffIds;
}
