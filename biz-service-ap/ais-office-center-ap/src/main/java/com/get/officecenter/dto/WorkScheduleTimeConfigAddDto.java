package com.get.officecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2022/11/7 12:47
 */
@Data
@ApiModel(value = "工作时间设置VO")
public class WorkScheduleTimeConfigAddDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @NotNull(message = "公司Id不能为空", groups = {Add.class, Update.class})
    @Min(value = 1, message = "缺少公司id参数", groups = {Add.class, Update.class})
    private Long fkCompanyId;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;

    /**
     * 工作开始时间
     */
    @ApiModelProperty(value = "工作开始时间")
    @NotBlank(message = "工作开始时间不能为空", groups = {Add.class, Update.class})
    private String workingStartTime;

    /**
     * 工作结束时间
     */
    @ApiModelProperty(value = "工作结束时间")
    @NotBlank(message = "工作结束时间不能为空", groups = {Add.class, Update.class})
    private String workingEndTime;

    /**
     * 午休开始时间
     */
    @ApiModelProperty(value = "午休开始时间")
    @NotBlank(message = "午休开始时间不能为空", groups = {Add.class, Update.class})
    private String noonBreakStartTime;

    /**
     * 午休结束时间
     */
    @ApiModelProperty(value = "午休结束时间")
    @NotBlank(message = "午休结束时间不能为空", groups = {Add.class, Update.class})
    private String noonBreakEndTime;

    /**
     * 工作时长（小时）
     */
    @ApiModelProperty(value = "工作时长（小时）")
    private BigDecimal workingDuration;

    /**
     * 每周工作日：如：1,2,3,4,5
     */
    @ApiModelProperty(value = "每周工作日：如：1,2,3,4,5")
    @NotBlank(message = "每周工作日不能为空", groups = {Add.class, Update.class})
    private String workingWeekCycle;
}
