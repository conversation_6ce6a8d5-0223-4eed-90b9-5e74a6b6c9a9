package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.annotation.UpdateWithNull;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.officecenter.entity.WorkScheduleStaffConfig;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 特殊人员工作时间
 *
 * <AUTHOR>
 * @date 2022/11/7 16:35
 */
@Data
public class WorkScheduleStaffConfigVo extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 员工Id
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;

    /**
     * 工作开始时间
     */
    @ApiModelProperty(value = "工作开始时间")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private String workingStartTime;

    /**
     * 工作结束时间
     */
    @ApiModelProperty(value = "工作结束时间")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    private String workingEndTime;

    /**
     * 午休开始时间
     */
    @ApiModelProperty(value = "午休开始时间")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    @UpdateWithNull
    private String noonBreakStartTime;

    /**
     * 午休结束时间
     */
    @ApiModelProperty(value = "午休结束时间")
    @JsonFormat(pattern = "HH:mm", timezone = "GMT+8")
    @UpdateWithNull
    private String noonBreakEndTime;

    /**
     * 工作时长（小时）
     */
    @ApiModelProperty(value = "工作时长（小时）")
    private BigDecimal workingDuration;

    /**
     * 每周工作日：如：1,2,3,4,5
     */
    @ApiModelProperty(value = "每周工作日：如：1,2,3,4,5")
    private String workingWeekCycle;

    /**
     * 生效开始时间
     */
    @ApiModelProperty(value = "生效开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveStartTime;

    /**
     * 生效结束时间
     */
    @ApiModelProperty(value = "生效结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveEndTime;

    @ApiModelProperty("公司名称")
    private String fkCompanyName;

    @ApiModelProperty(value = "考勤号")
    private String attendanceNum;

    @ApiModelProperty("员工名称")
    private String fkStaffName;

}
