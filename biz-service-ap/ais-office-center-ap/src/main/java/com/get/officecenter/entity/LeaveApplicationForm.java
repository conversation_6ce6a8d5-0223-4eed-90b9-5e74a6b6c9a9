package com.get.officecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName("m_leave_application_form")
public class LeaveApplicationForm extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;

    /**
     * 办公室Id
     */
    @ApiModelProperty(value = "办公室Id")
    private Long fkOfficeId;

    /**
     * 申请人Id
     */
    @ApiModelProperty(value = "申请人Id")
    private Long fkStaffId;

    /**
     * 工休申请单类型Id
     */
    @ApiModelProperty(value = "工休申请单类型Id")
    private Long fkLeaveApplicationFormTypeId;

    /**
     * 工休申请单编号（系统生成）
     */
    @ApiModelProperty(value = "工休申请单编号（系统生成）")
    private String num;

    /**
     * 开始时间
     */
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 天数，2小时0.25, 4小时0.5, 8小时1天
     */
    @ApiModelProperty(value = "天数，2小时0.25, 4小时0.5, 8小时1天")
    private BigDecimal days;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因")
    private String reason;

    /**
     * 是否直属上司口头同意，0否/1是
     */
    @ApiModelProperty(value = "是否直属上司口头同意，0否/1是")
    private Boolean isAgree;

    /**
     * 状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废
     */
    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    private Integer status;

    /**
     * 关联撤销表单Id
     */
    @ApiModelProperty(value = "关联撤销表单Id")
    private Long fkLeaveApplicationFormIdRevoke;

    /**
     * 组别guid
     */
    @ApiModelProperty(value = "组别guid")
    private String groupGuid;

}