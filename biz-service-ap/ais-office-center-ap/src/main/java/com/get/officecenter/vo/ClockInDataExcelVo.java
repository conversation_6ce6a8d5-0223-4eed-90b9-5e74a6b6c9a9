package com.get.officecenter.vo;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @DATE: 2022/5/12
 * @TIME: 16:24
 * @Description:
 **/
@Data
public class ClockInDataExcelVo {

    /**
     * 员工名称
     */
    @ApiModelProperty("员工名称")
    private String fkStaffName;

    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String departmentName;

    /**
     * 考勤时间
     */
    @ApiModelProperty(value = "考勤时间")
    private String attendanceTime;

    /**
     * 打卡时间
     */
    @ApiModelProperty("打卡时间")
    private String clockInTime;

    /**
     * 打卡结果：正常；迟到；缺卡
     */
    @ApiModelProperty(value = "打卡结果")
    private String clockInStateName;

    /**
     * 数据来源
     */
    @ApiModelProperty("数据来源")
    private String dataSourceName;

    /**
     * 是否激活：是；否
     */
    @ApiModelProperty("是否激活")
    private String isActiveName;
    /**
     * 创建人
     */
    @ApiModelProperty("创建人")
    private String gmtCreateUser;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private String gmtCreate;

}
