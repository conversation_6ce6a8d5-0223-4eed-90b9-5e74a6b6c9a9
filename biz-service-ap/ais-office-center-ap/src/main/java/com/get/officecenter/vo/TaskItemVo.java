package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.officecenter.entity.TaskItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class TaskItemVo extends BaseVoEntity {
    @ApiModelProperty("用户自定义任务Id")
    private Long fkTaskId;

    @ApiModelProperty("接收人员工Id")
    private Long fkStaffIdTo;

    @ApiModelProperty("任务关联项表名")
    private String fkTableName;

    @ApiModelProperty("任务关联项表Id")
    private Long fkTableId;

    @ApiModelProperty("状态枚举：0待解决/1已完成/2未能完成/3超时任务")
    private Integer status;

    @ApiModelProperty("状态修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statusModifiedTime;

    @ApiModelProperty("任务类型 {2:我的任务,3:下属任务}")
    private Integer taskItemType;

    @ApiModelProperty("接收任务人名称")
    private String recipientStaffName;

    @ApiModelProperty("任务关联类型")
    private String taskItemRelatedTypeName;

    @ApiModelProperty("最新反馈")
    private String lastComment;

    @ApiModelProperty("任务关联项")
    private String taskRelatedContent;

    @ApiModelProperty("申请计划ID，用于申请计划详情页的跳转")
    private Long offerItemId;

    @ApiModelProperty("学生申请方案ID，用于申请计划详情页的跳转")
    private Long fkStudentOfferId;

    @ApiModelProperty("学生Id，用于申请计划详情页的跳转")
    private Long fkStudentId;

    @ApiModelProperty("代理名称")
    private String agentName;

    @ApiModelProperty("是否可见")
    private Boolean isVisible;

    @ApiModelProperty("接收人员工Id集合")
    private String fkStaffIdTos;
}
