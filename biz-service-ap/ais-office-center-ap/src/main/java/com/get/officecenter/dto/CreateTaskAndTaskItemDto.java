package com.get.officecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.officecenter.entity.TaskItem;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CreateTaskAndTaskItemDto {

    @ApiModelProperty(value = "主任务的任务描述")
    private String taskDescription;

    @ApiModelProperty("主任务的搜索条件备注")
    private String remark;

    @ApiModelProperty("多人任务json")
    private String taskJson;

    @ApiModelProperty(value = "查看人员Ids，逗号分隔：1,2,3")
    private  String  fkStaffIdsView;

    @ApiModelProperty("子任务集合")
    private List<TaskItem> taskItemList;

    @ApiModelProperty(value = "任务截止时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskDeadline;
}
