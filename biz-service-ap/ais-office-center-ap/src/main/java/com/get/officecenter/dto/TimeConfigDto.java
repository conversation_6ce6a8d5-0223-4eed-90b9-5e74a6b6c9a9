package com.get.officecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2023/2/28
 * @TIME: 11:20
 * @Description: 获取时间配置VO
 **/
@Data
public class TimeConfigDto extends BaseVoEntity {
    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    private Long fkCompanyId;

    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工ID")
    private Long fkStaffId;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Long fkDepartmentId;

    /**
     * 星期
     */
    @ApiModelProperty(value = "星期")
    private Integer week;

    /**
     * 工休申请单类型Id
     */
    @ApiModelProperty(value = "工休申请单类型Id")
    private Long fkLeaveApplicationFormTypeId;

    /**
     * 类型：1-开始时间段；2-结束时间段
     */
    @ApiModelProperty(value = "类型：1-开始时间段；2-结束时间段")
    private Integer type;
    /**
     * 申请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value = "申请时间")
    private Date applicationTime;
}
