package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2022/1/13
 * @TIME: 15:43
 * @Description:
 **/
@Data
public class LeaveStockVo extends BaseVoEntity {

    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;
    /**
     * 员工ID
     */
    @ApiModelProperty(value = "员工Id")
    private Long fkStaffId;
    /**
     * 公休类型关键字，枚举，同公休单类型一致
     */
    @ApiModelProperty(value = "工休类型关键字，枚举，同工休单类型一致")
    private String leaveTypeKey;
    /**
     * 结余时长
     */
    @ApiModelProperty(value = "结余时长（小时）")
    private BigDecimal leaveStock;
    /**
     * 有效截至时间
     */
    @ApiModelProperty(value = "有效截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveDeadline;
    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String fkCompanyName;
    /**
     * 员工名称
     */
    @ApiModelProperty("员工名称")
    private String fkStaffName;
    /**
     * 工休类型名称
     */
    @ApiModelProperty("工休类型名称")
    private String leaveTypeName;
    /**
     * 工休库存合计
     */
    @ApiModelProperty("工休库存合计")
    private BigDecimal leaveStockSum;
}
