package com.get.officecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2022/5/10
 * @TIME: 10:20
 * @Description:打卡记录列表查询VO
 **/
@Data
public class ClockInDataListDto implements Serializable {
    /**
     * 员工名搜索key
     */
    @ApiModelProperty(value = "员工名搜索key")
    private String keyWord;
    /**
     * 月份时间
     */
    @ApiModelProperty(value = "月份时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date date;
    /**
     * 打卡开始时间
     */
    @ApiModelProperty(value = "打卡开始时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dateStart;
    /**
     * 打卡结束时间
     */
    @ApiModelProperty(value = "打卡结束时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date dateEnd;
    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司ID")
    private Long fkCompanyId;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    private Long departmentId;

    /**
     * 打卡结果：1-正常；2-迟到；3-缺卡
     */
    @ApiModelProperty(value = "打卡结果：1-正常；2-迟到；3-缺卡")
    private Integer clockInState;

    /**
     * 数据来源
     */
    @ApiModelProperty(value = "数据来源")
    private Integer dataSource;

    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效")
    private Boolean isActive;
}
