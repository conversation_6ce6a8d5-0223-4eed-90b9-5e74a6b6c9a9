package com.get.officecenter.dto.query;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class LeaveStockQueryDto {

    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 搜索关键字
     */
    @ApiModelProperty(value = "关键字")
    private String keyWord;

    /**
     * 公休类型关键字，枚举，同公休单类型一致
     */
    @ApiModelProperty(value = "工休类型关键字，枚举，同工休单类型一致")
    private String leaveTypeKey;

    /**
     * 有效截至时间
     */
    @ApiModelProperty(value = "有效截止时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date effectiveDeadline;

    /**
     * 公司ID
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

}
