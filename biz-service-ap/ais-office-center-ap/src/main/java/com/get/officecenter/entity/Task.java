package com.get.officecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author:cream
 * @Date: 2023/5/29  16:52
 */
@Data
@TableName("m_task")
public class Task extends BaseEntity {

    @ApiModelProperty("委派人员工Id")
    private Long fkStaffIdFrom;

    @ApiModelProperty("接收人员工Id，-1为群组任务")
    private Long fkStaffIdTo;

    @ApiModelProperty("任务编号")
    private String num;

    @ApiModelProperty("任务描述")
    private String taskDescription;

    @ApiModelProperty("0待解决/1已完成")
    private Integer status;

    @ApiModelProperty("状态修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statusModifiedTime;

    @ApiModelProperty("搜索条件备注")
    private String remark;

    @ApiModelProperty("多人任务json")
    private String taskJson;

    @ApiModelProperty(value = "任务截止时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskDeadline;

    @ApiModelProperty(value = "查看人员Ids，逗号分隔：1,2,3")
    private  String  fkStaffIdsView;
}
