package com.get.officecenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.common.result.ResponseBo;
import com.get.core.tool.api.Result;
import com.get.officecenter.entity.Task;
import com.get.officecenter.entity.TaskItem;
import com.get.officecenter.vo.*;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.officecenter.entity.OfficeMediaAndAttached;
import com.get.officecenter.dto.CreateTaskAndTaskItemDto;
import com.get.officecenter.dto.LeaveLogDto;
import com.get.officecenter.dto.LeaveStockDto;
import com.get.officecenter.dto.query.LeaveApplicationFormQueryDto;
import com.get.permissioncenter.vo.WxCpUserVo;
import com.get.remindercenter.vo.ReminderTaskCountVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 文件 Feign接口类  待测试跨微服务的文件上传功能
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_OFFICE_CENTER
)
public interface IOfficeCenterClient {

    String API_PREFIX = "/feign";


    /**
     * 修改流程表单状态
     */
    String CHANGE_STATUS = API_PREFIX + "/change-status";
    /**
     * 根据工休单id获取请假类型
     */
    String GET_LEAVE_TYPE_NAME_BY_ID = API_PREFIX + "/get-leave-type-name-by-id";
    /**
     * 根据被撤单id回显撤销单的内容
     */
    String GET_LEAVE_APPLICATION_FORM = API_PREFIX + "/get-leave-application-form";
    /**
     * 添加工休日志
     */
    String ADD_SYSTEM_LEAVE_LOG = API_PREFIX + "/add-system-leave-log";
    /**
     * 新增信息(工作流feign调用）
     */
    String ADD_SYSTEM = API_PREFIX + "/add-system";
    /**
     * 调整时长(工作流feign调用)
     */
    String UPDATE_SYSTEM_LEAVETOCK = API_PREFIX + "/update-system-leavetock";
    /**
     * 获取库存
     */
    String GET_LEAVE_STOCK_DTOS = API_PREFIX + "/get-leave-stock-dtos";

    /**
     * 获取库存
     */
    String GET_EFFICIENT_LEAVE_STOCK_DTOS = API_PREFIX + "/get-efficient-leave-stock-dtos";
    /**
     * @Description :根据表单id获取typeKey
     */
    String GET_LEAVE_TYPE_KEY_BY_ID = API_PREFIX + "/get-leave-type-key-by-id";
    /**
     * 列表数据
     */
    String GET_SYSTEM_LEAVE_LOG = API_PREFIX + "/get-system-leave-log";
    String GET_APPLICATION_AND_APPROVAL_COUNT = API_PREFIX + "/get-application-and-approval-count";
    String HAS_MEDIA_AND_ATTACH = API_PREFIX + "/has-media-and-attach";
    String GET_WX_CP_USER_ID_BY_CODE = API_PREFIX + "/get-wx-cp-user-id-by-code";
    String GET_OFFICE_MEDIAANDATTACHEDS = API_PREFIX + "/get-office-mediaandattacheds";
    String GET_LEAVEAPPLICATIONFORM_EXPORTDTOS = API_PREFIX + "/get-leaveapplicationform-exportdtos";
    String ADD_TASK_AND_TASK_ITEM = API_PREFIX + "/add-task-and-task-item";

    String GET_TASK = API_PREFIX + "/get-task";

    String GET_TASK_ITEM = API_PREFIX + "/get-task-item";

    String GET_TASK_ITEM_IDS = API_PREFIX + "/get-task-item-ids";

    String GET_UNFINISHED_TASK_ITEM_RECEIVER = API_PREFIX + "/get-unfinished-task-item-receiver";

    String GET_TASK_ITEM_BY_ID = API_PREFIX + "/get-task-item-by-id";

    String GET_TASK_BY_END_TIME = API_PREFIX + "/get-task-by-end-time";

    /**
     * 修改流程表单状态
     * @param status
     * @param tableName
     * @param businessKey
     * @return
     */
    @PostMapping(CHANGE_STATUS)
    Result<Boolean> changeStatus(@RequestParam("status") Integer status, @RequestParam("tableName") String tableName, @RequestParam("businessKey") Long businessKey);

    /**
     * 根据工休单id获取请假类型
     * @param id
     * @return
     */
    @GetMapping(GET_LEAVE_TYPE_NAME_BY_ID)
    Result<String> getLeaveTypeNameById(@RequestParam("id") Long id);

    /**
     * 根据被撤单id回显撤销单的内容
     * @param id
     * @return
     */
    @GetMapping(GET_LEAVE_APPLICATION_FORM)
    Result<LeaveApplicationForm> getLeaveApplicationForm(@RequestParam("id") Long id);

    /**
     * 新增公休日志(工作流)
     * @param leaveLogDto
     * @return
     */
    @PostMapping(ADD_SYSTEM_LEAVE_LOG)
    Result<Boolean> addSystemLeaveLog(@RequestBody LeaveLogDto leaveLogDto);

    /**
     * 新增信息(工作流feign调用）
     * @param leaveStockDto
     * @return
     */
    @PostMapping(ADD_SYSTEM)
    Result<Long> addSystem(@RequestBody LeaveStockDto leaveStockDto);

    /**
     * 调整时长(工作流feign调用)
     * @param leaveStockDto
     * @return
     */
    @PostMapping(UPDATE_SYSTEM_LEAVETOCK)
    Result<Boolean> updateSystemLeavetock(@RequestBody LeaveStockDto leaveStockDto);

    /**
     * 获取库存信息
     * @param leaveStockDto
     * @return
     */
    @PostMapping(GET_LEAVE_STOCK_DTOS)
    List<LeaveStockVo> getLeaveStockDtos(@RequestBody LeaveStockDto leaveStockDto);

    /**
     * 根据工休单id获取请假类型key
     * @param id
     * @return
     */
    @PostMapping(GET_LEAVE_TYPE_KEY_BY_ID)
    String getLeaveTypeKeyById(@RequestParam("id") Long id);

    /**
     * 获取系统日志
     * @param leaveLogDto
     * @return
     */
    @PostMapping(GET_SYSTEM_LEAVE_LOG)
    List<LeaveLogVo> getSystemLeaveLog(@RequestBody LeaveLogDto leaveLogDto);

    @PostMapping(GET_APPLICATION_AND_APPROVAL_COUNT)
    Result<List<ReminderTaskCountVo>> getApplicationAndApprovalCount(@RequestParam("staffId")Long staffId);

    /**
     * 是否有附件
     *
     * @param key
     * @param id
     * @return
     */
    @PostMapping(HAS_MEDIA_AND_ATTACH)
    Result<Boolean> hasMediaAndAttach(@RequestParam("key")String key, @RequestParam("id")Long id);

    @PostMapping(GET_WX_CP_USER_ID_BY_CODE)
    Result<WxCpUserVo> getWxCpUserIdByCode(@RequestParam("code") String code);

    @PostMapping(GET_EFFICIENT_LEAVE_STOCK_DTOS)
    List<LeaveStockVo> getEfficientLeaveStockDtos(@RequestBody LeaveStockDto leaveStockDto);

    @PostMapping(GET_OFFICE_MEDIAANDATTACHEDS)
    Result<List<OfficeMediaAndAttached>> getOfficeMediaAndAttacheds(@RequestParam("id")Long id,@RequestParam("loginId") String loginId);

    @PostMapping(GET_LEAVEAPPLICATIONFORM_EXPORTDTOS)
    Result<List<LeaveApplicationFormExportVo>>  getLeaveApplicationFormExportDtos(@RequestBody LeaveApplicationFormQueryDto leaveApplicationFormVo);

    /**
     * 添加主任务和子任务
     *
     * @param createTaskAndTaskItemDto
     * @return
     */
    @PostMapping(ADD_TASK_AND_TASK_ITEM)
    Result<Boolean> addTaskAndTaskItem(@RequestBody CreateTaskAndTaskItemDto createTaskAndTaskItemDto);

    /**
     * 根据任务id获取任务
     */
    @PostMapping(GET_TASK)
    Result<CustomTaskVo> getTask(@RequestParam("taskId") Long taskId);

    /**
     * 根据任务id获取子任务
     */
    @PostMapping(GET_TASK_ITEM)
    Result<List<TaskItem>> getTaskList(@RequestParam("taskId") Long taskId);

    /**
     * 根据任务id获取子任务id列表
     */
    @PostMapping(GET_TASK_ITEM_IDS)
    Result<List<Long>> getTaskItemIds(@RequestParam("taskId") Long taskId);

    /**
     * 查询未完成子任务的接收人
     */
    @PostMapping(GET_UNFINISHED_TASK_ITEM_RECEIVER)
    Result<List<Long>> getUnfinishedTaskItemReceiver(@RequestParam("taskId") Long taskId);

    /**
     * 获取任务子项信息
     */
    @PostMapping(GET_TASK_ITEM_BY_ID)
    Result<TaskItem> getTaskItem(Long taskItemId);

    /**
     * 获取当天截止的任务
     */
    @PostMapping(GET_TASK_BY_END_TIME)
    Result<List<Task>> getTaskByEndTime();
}
