//package com.get.officecenter.feign;
//
//import com.get.common.entity.fegin.StaffVo;
//import com.get.common.result.ListResponseBo;
//import com.get.common.result.ResponseBo;
//import org.springframework.cloud.openfeign.FeignClient;
//import org.springframework.stereotype.Component;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.Map;
//import java.util.Set;
//
///**
// * @author: Sea
// * @create: 2020/7/15 11:25
// * @verison: 1.0
// * @description:
// */
//@Component
//@FeignClient(name = "permission-center")
//public interface FeignPermissionService {
//    //员工
//
//    /**
//     * @Description :feign调用 通过员工ids 查找对应的员工姓名map
//     * @Param [ids]
//     * <AUTHOR>
//     */
//    @PostMapping(value = "permission/staff/getStaffNamesByIds")
//    Map<Long, String> getStaffNamesByIds(@RequestBody Set<Long> ids);
//
//    /**
//     * @return com.get.common.result.ResponseBo<com.get.common.entity.fegin.StaffVo>
//     * @Description :获取员工dto
//     * @Param [id]
//     * <AUTHOR>
//     */
//    @GetMapping("permission/staff/{id}")
//    ResponseBo<StaffVo> detail(@PathVariable("id") Long id);
//
//    /**
//     * @return java.lang.String
//     * @Description :feign调用 通过员工id 查找对应的员工姓名
//     * @Param [id]
//     * <AUTHOR>
//     */
//    @GetMapping(value = "permission/staff/getStaffName")
//    String getStaffName(@RequestParam(required = false) Long id);
//
//    /**
//     * @return com.get.common.result.ListResponseBo
//     * @Description :获取对应部门最高职位人
//     * @Param [companyId, departmentId]
//     * <AUTHOR>
//     */
//    @PostMapping(value = "permission/staff/getTopPositionStaffIds")
//    ListResponseBo getTopPositionStaffIds(@RequestParam(value = "companyId") Long companyId, @RequestParam(value = "departmentId") Long departmentId);
//
//    //公司
//
//    /**
//     * @Description :feign调用 根据ids查询公司名称map
//     * @Param [companyIds]
//     * <AUTHOR>
//     */
//    @PostMapping("permission/company/getCompanyNamesByIds")
//    Map<Long, String> getCompanyNamesByIds(@RequestBody Set<Long> companyIds);
//
//    /**
//     * @return java.lang.String
//     * @Description :feign调用 根据id查询公司名称
//     * @Param [companyId]
//     * <AUTHOR>
//     */
//    @PostMapping("permission/company/getCompanyName")
//    String getCompanyNameById(@RequestParam(value = "companyId") Long companyId);
//
//    //部门
//
//    /**
//     * @return java.lang.String
//     * @Description :根据部门id查找部门名称
//     * @Param [departmentId]
//     * <AUTHOR>
//     */
//    @PostMapping("permission/department/getDepartmentNameById")
//    String getDepartmentNameById(@RequestParam(required = false, value = "departmentId") Long departmentId);
//
//    //办公室
//
//    /**
//     * @return java.lang.String
//     * @Description :根据办公室id查找对应办公室名称
//     * @Param [officeId]
//     * <AUTHOR>
//     */
//    @PostMapping("permission/office/getOfficeNameById")
//    String getOfficeNameById(@RequestParam(required = false, value = "officeId") Long officeId);
//
//    /**
//     * 获取员工信息
//     * @param staffId
//     * @return
//     */
//    @GetMapping(value = "permission/staff/getCompanyIdByStaffId")
//    StaffVo getCompanyIdByStaffId(@RequestParam("StaffId") Long staffId);
//
//    /**
//     * 获取登录人的直属上司id
//     * @param staffId
//     * @return
//     */
//    @PostMapping("permission/staff/getStaffSupervisorIdByStaffId")
//    Long getStaffSupervisorIdByStaffId(@RequestParam("staffId") Long staffId);
//
////    /**
////     * fegin调用，更新年假
////     *
////     * @param id
////     * @param annualLeaveBase
////     * @return ResponseBo
////     * @throws YException
////     */
////    @PostMapping("permission/staff/updateAnnualLeaveBase")
////    ResponseBo updateAnnualLeaveBase(@RequestParam("id") Long id, @RequestParam("annualLeaveBase") BigDecimal annualLeaveBase);
////
////    /**
////     * fegin调用，更新补休
////     *
////     * @param id
////     * @param compensatoryLeaveBase
////     * @return ResponseBo
////     * @throws YException
////     */
////    @PostMapping("permission/staff/updateCompensatoryLeaveBase")
////    ResponseBo updateCompensatoryLeaveBase(@RequestParam("id") Long id, @RequestParam("compensatoryLeaveBase") BigDecimal compensatoryLeaveBase);
//
//}
