package com.get.officecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
@TableName("r_leave_application_form_type_company")
public class LeaveApplicationFormTypeCompany extends BaseEntity implements Serializable {
    /**
     * 工休申请单类型Id
     */
    @ApiModelProperty(value = "工休申请单类型Id")
    @Column(name = "fk_leave_application_form_type_id")
    private Long fkLeaveApplicationFormTypeId;

    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;

    private static final long serialVersionUID = 1L;
}