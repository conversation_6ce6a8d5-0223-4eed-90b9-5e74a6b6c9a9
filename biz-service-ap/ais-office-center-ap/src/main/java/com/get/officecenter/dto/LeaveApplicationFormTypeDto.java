package com.get.officecenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/12 16:04
 * @verison: 1.0
 * @description:
 */
@Data
public class LeaveApplicationFormTypeDto extends BaseVoEntity {
    /**
     * 类型名称
     */
    @ApiModelProperty(value = "类型名称")
    private String typeName;

    /**
     * 类型关键字
     */
    @ApiModelProperty(value = "类型关键字")
    private String typeKey;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;

    //自定义内容
    /**
     * 搜索关键字
     */
    @ApiModelProperty(value = "搜索关键字")
    private String keyWord;

    @NotNull(message = "公司id", groups = {BaseVoEntity.Update.class})
    @ApiModelProperty(value = "公司id")
    private Long fkCompanyId;

    @ApiModelProperty(value = "公司")
    private List<Long> fkCompanyIdList;

}
