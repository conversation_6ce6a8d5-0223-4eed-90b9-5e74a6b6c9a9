package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.officecenter.entity.Task;
import com.get.permissioncenter.vo.StaffVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @Author:cream
 * @Date: 2023/5/30  10:22
 */
@Data
public class CustomTaskVo extends BaseVoEntity {

    @ApiModelProperty("委派人员工Id")
    private Long fkStaffIdFrom;

    @ApiModelProperty("接收人员工Id，-1为群组任务")
    private Long fkStaffIdTo;

    @ApiModelProperty("任务编号")
    private String num;

    @ApiModelProperty("任务描述")
    private String taskDescription;

    @ApiModelProperty("0待解决/1已完成/2超时任务")
    private Integer status;

    @ApiModelProperty("状态修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statusModifiedTime;

    @ApiModelProperty("搜索条件备注")
    private String remark;


    @ApiModelProperty("委托人部门名称")
    private String delegationDepartmentName;

    @ApiModelProperty("接收人部门名称")
    private String recipientDepartmentName;

    @ApiModelProperty("委托人名称")
    private String delegation;

    @ApiModelProperty("接收人名称")
    private String recipient;

    @ApiModelProperty("评论")
    private String comment;

    @ApiModelProperty("任务类型 {1： 我的委派。2：我的任务 , 3：下属任务}")
    private Integer taskType;

    @ApiModelProperty("子任务完成状态数量")
    private Integer taskItemFinishedCount;

    @ApiModelProperty("子任务未能完成状态数量")
    private Integer taskItemUnfinishedCount;

    @ApiModelProperty("子任务总数")
    private Integer taskItemTotal;

    @ApiModelProperty("子任务待处理状态数量")
    private Integer taskItemTodoCount;

    @ApiModelProperty("多人任务json")
    private List<String> taskJsonList;

    @ApiModelProperty(value = "任务截止时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date taskDeadline;

    @ApiModelProperty(value = "子任务最新状态")
    private Integer taskItemStatus;

    @ApiModelProperty(value = "查看人员Ids，逗号分隔：1,2,3")
    private  String  fkStaffIdsView;


    @ApiModelProperty("查看登录人的子任务已完成数量")
    private Integer subTaskCompletedCount;

    @ApiModelProperty("查看登录人的子任务未完成数量")
    private Integer subTaskUncompletedCount;

    @ApiModelProperty("查看登录人的子任务总数")
    private Integer subTaskTotal;

    @ApiModelProperty("查看登录人的子任务待处理数量")
    private Integer subTaskPendingCount;


}
