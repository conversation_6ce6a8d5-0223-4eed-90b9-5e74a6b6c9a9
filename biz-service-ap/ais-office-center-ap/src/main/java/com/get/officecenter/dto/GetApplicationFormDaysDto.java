package com.get.officecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2023/2/28
 * @TIME: 12:27
 * @Description:获取申请时长VO
 **/
@Data
public class GetApplicationFormDaysDto {

    /**
     * 工休申请单类型Id
     */
    @ApiModelProperty(value = "工休申请单类型Id")
    private Long fkLeaveApplicationFormTypeId;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;
    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;
}
