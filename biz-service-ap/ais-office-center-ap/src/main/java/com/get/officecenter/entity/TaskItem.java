package com.get.officecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@TableName("m_task_item")
public class TaskItem extends BaseEntity {

    @ApiModelProperty("用户自定义任务Id")
    private Long fkTaskId;

    @ApiModelProperty("接收人员工Id")
    private Long fkStaffIdTo;

    @ApiModelProperty("任务关联项表名")
    private String fkTableName;

    @ApiModelProperty("任务关联项表Id")
    private Long fkTableId;

    @ApiModelProperty("状态枚举：0待解决/1已完成/2未能完成")
    private Integer status;

    @ApiModelProperty("状态修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date statusModifiedTime;

    @ApiModelProperty("角色Key")
    private String fkRoleKey;

}
