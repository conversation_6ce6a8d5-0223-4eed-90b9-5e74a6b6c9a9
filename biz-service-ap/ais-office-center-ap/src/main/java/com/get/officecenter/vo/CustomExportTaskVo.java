package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.util.Date;

@Data
public class CustomExportTaskVo {

    @ApiModelProperty("类型")
    private String taskType;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("委派")
    private String delegation;

    @ApiModelProperty("接收(个人任务完成情况)")
    private String recipient;

    @ApiModelProperty("个人子任务已完成数量")
    private Integer subTaskCompletedCount;

    @ApiModelProperty("个人子任务未完成数量")
    private Integer subTaskUncompletedCount;

    @ApiModelProperty("个人子任务待处理数量")
    private Integer subTaskPendingCount;

    @ApiModelProperty("个人子任务总数")
    private Integer subTaskTotal;


    @ApiModelProperty("任务编号")
    private String num;

    @ApiModelProperty("任务内容")
    private String taskDescription;

    @ApiModelProperty("子任务完成状态数量")
    private Integer taskItemFinishedCount;

    @ApiModelProperty("子任务未能完成状态数量")
    private Integer taskItemUnfinishedCount;

    @ApiModelProperty("子任务待处理状态数量")
    private Integer taskItemTodoCount;

    @ApiModelProperty("子任务总数")
    private Integer taskItemTotal;



    @ApiModelProperty("最新反馈")
    private String comment;

    @ApiModelProperty("截止时间")
    private String taskDeadline;

    @ApiModelProperty("最新状态变更时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String statusModifiedTime;

    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty("创建人")
    private String gmtCreateUser;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @ApiModelProperty("创建时间")
    private String gmtCreate;
    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty("编辑人")
    private String gmtModifiedUser;
    @DateTimeFormat(
            pattern = "yyyy-MM-dd HH:mm:ss"
    )
    @JsonFormat(
            pattern = "yyyy-MM-dd HH:mm:ss",
            timezone = "GMT+8"
    )
    @ApiModelProperty("编辑时间")
    private String gmtModified;




}
