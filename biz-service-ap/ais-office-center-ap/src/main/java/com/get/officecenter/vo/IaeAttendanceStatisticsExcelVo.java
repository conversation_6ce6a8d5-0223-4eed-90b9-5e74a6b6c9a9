package com.get.officecenter.vo;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @DATE: 2022/10/25
 * @TIME: 17:40
 * @Description:IAE考勤统计Dto
 **/
@Data
public class IaeAttendanceStatisticsExcelVo extends BaseVoEntity {
    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "姓名（英）")
    private String staffName;
    /**
     * 员工姓名
     */
    @ApiModelProperty(value = "姓名（中）")
    private String staffNameChn;

    /**
     * 办公室
     */
    @ApiModelProperty(value = "办公室")
    private String officeName;

    /**
     * 应出勤天数
     */
    @ApiModelProperty(value = "应出勤天数")
    private BigDecimal attendanceDays;

    /**
     * 实际出勤天数
     */
    @ApiModelProperty(value = "实际出勤天数")
    private BigDecimal actualAttendanceDays;

    /**
     * 补休天数
     */
    @ApiModelProperty(value = "补休天数")
    private BigDecimal takeDeferredHolidays;

    /**
     * 加班天数
     */
    @ApiModelProperty(value = "加班天数")
    private BigDecimal overtime;

    /**
     * 出差天数
     */
    @ApiModelProperty(value = "出差天数")
    private BigDecimal evection;

    /**
     * 出差天数
     */
    @ApiModelProperty(value = "考勤扣款")
    private BigDecimal attendanceDeduction;

    /**
     * 额度外迟到次数
     */
    @ApiModelProperty(value = "额度外迟到次数")
    private Integer overLateNumber;

    /**
     * 事假天数
     */
    @ApiModelProperty(value = "事假天数")
    private BigDecimal leaveVacation;

    /**
     * 病假天数
     */
    @ApiModelProperty(value = "病假天数")
    private BigDecimal diseaseVacation;

    /**
     * 本月使用年假天数
     */
    @ApiModelProperty(value = "本月使用年假天数")
    private BigDecimal annualLeave;

    /**
     * 婚假天数
     */
    @ApiModelProperty(value = "婚假")
    private BigDecimal marriageVacation;

    /**
     * 婚前体检
     */
    @ApiModelProperty(value = "婚前体检")
    private BigDecimal premaritalExamination;

    /**
     * 产假
     */
    @ApiModelProperty(value = "产假")
    private BigDecimal maternityVacation;

    /**
     * 产检假
     */
    @ApiModelProperty(value = "产检假")
    private BigDecimal maternityCheckVacation;

    /**
     * 丧假
     */
    @ApiModelProperty(value = "丧假")
    private BigDecimal funeralVacation;

    /**
     * 工伤假
     */
    @ApiModelProperty(value = "工伤假")
    private BigDecimal workRelatedVacation;

    /**
     * 剩余补休时长
     */
    @ApiModelProperty(value = "剩余补休天数")
    private BigDecimal remainingTakeDeferredHolidays;

    /**
     * 剩余年假天数
     */
    @ApiModelProperty(value = "剩余年假天数")
    private BigDecimal remainingAnnualLeave;
}
