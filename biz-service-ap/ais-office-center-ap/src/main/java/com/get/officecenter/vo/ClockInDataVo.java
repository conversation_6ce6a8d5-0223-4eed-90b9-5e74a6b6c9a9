package com.get.officecenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.officecenter.entity.ClockInData;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2022/5/10
 * @TIME: 11:34
 * @Description:
 **/
@Data
public class ClockInDataVo extends BaseVoEntity {

    /**
     * 公司ID
     */
    @ApiModelProperty("公司ID")
    private Long fkCompanyId;
    /**
     * 员工ID
     */
    @ApiModelProperty("员工Id")
    private Long fkStaffId;
    /**
     * 打卡时间
     */
    @ApiModelProperty("打卡时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date clockInTime;
    /**
     * 数据来源
     */
    @ApiModelProperty("数据来源:0-打卡机；1-钉钉；2-系统录入")
    private Integer dataSource;
    /**
     * 是否激活：1-是；0-否
     */
    @ApiModelProperty("是否激活")
    private Boolean isActive;
    /**
     * 员工名称
     */
    @ApiModelProperty("员工名称")
    private String fkStaffName;
    /**
     * 公司名称
     */
    @ApiModelProperty("公司名称")
    private String fkCompanyName;
    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String departmentName;

    /**
     * 打卡结果：1-正常；2-迟到；3-缺卡
     */
    @ApiModelProperty(value = "打卡结果：1-正常；2-迟到；3-缺卡")
    private Integer clockInState;

    /**
     * 考勤时间
     */
    @ApiModelProperty(value = "考勤时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date attendanceTime;
}
