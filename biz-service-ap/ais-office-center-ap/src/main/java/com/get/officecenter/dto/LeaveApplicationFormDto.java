package com.get.officecenter.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: Sea
 * @create: 2021/4/12 19:16
 * @verison: 1.0
 * @description:
 */
@Data
public class LeaveApplicationFormDto extends BaseVoEntity {
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    private Long fkCompanyId;

    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    private Long fkDepartmentId;

    /**
     * 办公室Id
     */
    @ApiModelProperty(value = "办公室Id")
    private Long fkOfficeId;

    /**
     * 申请人Id
     */
    @ApiModelProperty(value = "申请人Id")
    private Long fkStaffId;

    /**
     * 工休申请单类型Id
     */
    @ApiModelProperty(value = "工休申请单类型Id")
    private Long fkLeaveApplicationFormTypeId;

    /**
     * 工休申请单编号（系统生成）
     */
    @ApiModelProperty(value = "工休申请单编号（系统生成）")
    private String num;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空！", groups = {Add.class})
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空！", groups = {Add.class})
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    /**
     * 天数，2小时0.25, 4小时0.5, 8小时1天
     */
    @ApiModelProperty(value = "天数，2小时0.25, 4小时0.5, 8小时1天")
    private BigDecimal days;

    /**
     * 申请原因
     */
    @ApiModelProperty(value = "申请原因")
    private String reason;

    /**
     * 是否直属上司口头同意，0否/1是
     */
    @ApiModelProperty(value = "是否直属上司口头同意，0否/1是")
    private Boolean isAgree;

    /**
     * 状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废
     */
    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    private Integer status;

    /**
     * 工休申请单父Id
     */
    @ApiModelProperty(value = "工休申请单父Id")
    private Long fkLeaveApplicationFormIdRevoke;

    //自定义内容
    /**
     * 1查询全部，0查询个人,2 查询我的审批
     */
    @ApiModelProperty(value = "1查询全部，0查询个人,2 查询我的审批")
    private String selectStatus;

    /**
     * 表单创建日期-开始
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "表单创建日期-开始")
    private String createStartTime;

    /**
     * 表单创建日期-结束
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(value = "表单创建日期-结束")
    private String createEndTime;


    /**
     * bms移动版 审批中，已完成 bigType: 'examining' | 'completed'
     */
    @ApiModelProperty(value = "bms移动版审批类型：审批中/已完成")
    private String bigType;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件")
    private List<MediaAndAttachedDto> mediaAndAttachedVos;
}
