package com.get.officecenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2022/5/9
 * @TIME: 14:01
 * @Description:打卡数据
 **/
@Data
@TableName("m_clock_in_data")
public class ClockInData extends BaseEntity implements Serializable {
    /**
     * 公司ID
     */
    @ApiModelProperty("公司ID")
    private Long fkCompanyId;
    /**
     * 员工ID
     */
    @ApiModelProperty("员工Id")
    private Long fkStaffId;
    /**
     * 打卡时间
     */
    @ApiModelProperty("打卡时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date clockInTime;
    /**
     * 数据来源
     */
    @ApiModelProperty("数据来源:0-打卡机；1-钉钉；2-系统录入")
    private Integer dataSource;
    /**
     * 是否激活：1-是；0-否
     */
    @ApiModelProperty("是否激活")
    private Boolean isActive;
}
