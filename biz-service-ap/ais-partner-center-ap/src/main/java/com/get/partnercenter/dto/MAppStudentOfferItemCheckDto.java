package com.get.partnercenter.dto;


import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class MAppStudentOfferItemCheckDto {

    @ApiModelProperty("生效学生ID")
    @NotNull(message = "生效学生ID不能为空")
    private Long fkStudentId;

    @ApiModelProperty("草稿学生ID")
    @NotNull(message = "草稿学生ID不能为空")
    private Long fkAppStudentId;


    @ApiModelProperty("审核记录")
    private List<MAppStudentOfferItemCheck> listSub;
}
