package com.get.partnercenter.entity;


import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-06-09 09:46:59
 */

@Data
@TableName("m_institution_high_commission")
public class MInstitutionHighCommissionEntity  extends BaseEntity {

  @ApiModelProperty("学校高佣Id")
  private Long id;
 

  @ApiModelProperty("公司Id")
  private Long fkCompanyId;
 

  @ApiModelProperty("平台应用Id")
  private Long fkPlatformId;
 

  @ApiModelProperty("平台应用CODE")
  private String fkPlatformCode;
 

  @ApiModelProperty("学校Id")
  private Long fkInstitutionId;
 

  @ApiModelProperty("代理佣金分类Id")
  private Long fkAgentCommissionTypeId;
 

  @ApiModelProperty("推荐信息")
  private String recommendInfo;
 

  @ApiModelProperty("有效时间（开始）")
  private LocalDateTime startTime;
 

  @ApiModelProperty("有效时间（结束）")
  private LocalDateTime endTime;
 

  @ApiModelProperty("有效时间是否无时间限制：0否/1是")
  private Boolean isTimeless;
 

  @ApiModelProperty("权重")
  private Integer weight;
 

  @ApiModelProperty("上架状态：0已下架/1已上架")
  private Integer status;
 

 

}
