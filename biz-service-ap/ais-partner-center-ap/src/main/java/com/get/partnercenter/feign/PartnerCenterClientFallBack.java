package com.get.partnercenter.feign;

import com.alibaba.fastjson.JSONObject;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.partnercenter.dto.MediaAndAttachedDto;
import com.get.partnercenter.dto.RegisterPartnerUserDto;
import com.get.partnercenter.dto.wechat.LinkAndQrCodeVo;
import com.get.partnercenter.dto.wechat.MinProgramQrCodeDto;
import com.get.partnercenter.vo.MediaAndAttachedVo;
import com.get.partnercenter.vo.RegisterPartnerUserVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote:
 */
@Component
@VerifyPermission(IsVerify = false)
@Slf4j
public class PartnerCenterClientFallBack implements IPartnerCenterClient {
    @Override
    public Result<List<RegisterPartnerUserVo>> registerPartnerUser(List<RegisterPartnerUserDto> list) {
        log.error("注册用户失败:{}", JSONObject.toJSONString(list));
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<MediaAndAttachedVo>> addMediaAndAttachedList(List<MediaAndAttachedDto> mediaAttachedVos) {
        return null;
    }

    @Override
    public Result<Object> getQrCode(MinProgramQrCodeDto minProgramQrCodeDto) {
        log.error("获取小程序码失败:{}", JSONObject.toJSONString(minProgramQrCodeDto));
        return null;
    }

    @Override
    public Result<Object> getUrlLink(MinProgramQrCodeDto minProgramQrCodeDto) {
        log.error("获取小程序短链接失败:{}", JSONObject.toJSONString(minProgramQrCodeDto));
        return null;
    }

    @Override
    public Result<LinkAndQrCodeVo> getLinkAndQrCode(MinProgramQrCodeDto minProgramQrCodeDto) {
        log.error("获取小程序短链接和小程序失败:{}", JSONObject.toJSONString(minProgramQrCodeDto));
        return null;
    }
}
