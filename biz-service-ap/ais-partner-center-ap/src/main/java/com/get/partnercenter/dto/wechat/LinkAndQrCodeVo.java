package com.get.partnercenter.dto.wechat;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:Oliver
 * @Date: 2025/8/4
 * @Version 1.0
 * @apiNote:微信链接和二维码信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class LinkAndQrCodeVo {

    @ApiModelProperty("微信链接")
    private String url;

    @ApiModelProperty("微信二维码")
    private String qrCode;
}
