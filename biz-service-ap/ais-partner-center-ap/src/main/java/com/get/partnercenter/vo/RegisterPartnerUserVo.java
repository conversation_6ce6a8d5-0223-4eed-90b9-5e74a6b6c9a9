package com.get.partnercenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @Author:Oliver
 * @Date: 2025/7/7
 * @Version 1.0
 * @apiNote:注册伙伴用户返回类
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RegisterPartnerUserVo {

    @ApiModelProperty("发件人名称")
    @NotBlank(message = "发件人名称不能为空")
    private String account;

    @ApiModelProperty("发件人电邮地址")
    @NotBlank(message = "发件人电邮地址不能为空")
    private String password;
}
