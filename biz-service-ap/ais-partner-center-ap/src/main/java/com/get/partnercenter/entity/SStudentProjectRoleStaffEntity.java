package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName; 
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-05-19 15:33:34
 */

@Data
@TableName("s_student_project_role_staff")
public class SStudentProjectRoleStaffEntity extends Model<SStudentProjectRoleStaffEntity>{

  @ApiModelProperty("关系Id")
  private Long id;
 

  @ApiModelProperty("表名")
  private String fkTableName;
 

  @ApiModelProperty("表Id")
  private Long fkTableId;
 

  @ApiModelProperty("学生项目角色Id")
  private Long fkStudentProjectRoleId;
 

  @ApiModelProperty("员工Id")
  private Long fkStaffId;
 

  @ApiModelProperty("是否激活：0否/1是")
  private Boolean isActive;
 

  @ApiModelProperty("绑定时间")
  private LocalDateTime activeDate;
 

  @ApiModelProperty("取消绑定时间（下次绑定时，需要重新建立记录）")
  private LocalDateTime unactiveDate;
 

  @ApiModelProperty("创建时间")
  private LocalDateTime gmtCreate;
 

  @ApiModelProperty("创建用户(登录账号)")
  private String gmtCreateUser;
 

  @ApiModelProperty("修改时间")
  private LocalDateTime gmtModified;
 

  @ApiModelProperty("修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
