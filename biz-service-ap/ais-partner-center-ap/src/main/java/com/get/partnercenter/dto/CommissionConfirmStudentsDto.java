package com.get.partnercenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CommissionConfirmStudentsDto {

    @ApiModelProperty( "代理ID")
    @NotNull(message = "代理ID不能为空")
    private Long agentId;

    @ApiModelProperty( "单位ID")
    private Long companyId;


    @ApiModelProperty( "年份")
    @NotNull(message = "年份不能为空")
    private Integer year;


    @ApiModelProperty( "月份")
    @NotNull(message = "月份不能为空")
    private Integer month;


}
