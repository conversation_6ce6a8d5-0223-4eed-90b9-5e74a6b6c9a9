package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName m_app_student
 */
@TableName(value ="m_app_student")
@Data
public class MAppStudentEntity extends BaseEntity implements Serializable {
    /**
     * 学生代理Id
     */
    private Long fkAgentId;

    /**
     * 公司Id
     */
    private Long fkCompanyId;

    /**
     * 学生编号
     */
    private String num;

    /**
     * 学生姓名（中）
     */
    private String name;

    /**
     * 姓（英/拼音）
     */
    private String lastName;

    /**
     * 名（英/拼音）
     */
    private String firstName;

    /**
     * 性别
     */
    private String gender;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 学生国籍所在国家Id
     */
    private Long fkAreaCountryIdNationality;

    /**
     * 学生国籍所在国家名称
     */
    private String fkAreaCountryNameNationality;

    /**
     * 绿卡国家Id
     */
    private Long fkAreaCountryIdGreenCard;

    /**
     * 护照编号（保险业务必填）
     */
    private String passportNum;

    /**
     * 护照签发地Id
     */
    private Long fkAreaCountryIdPassport;

    /**
     * 学生出生所在国家Id
     */
    private Long fkAreaCountryIdBirth;

    /**
     * 学生出生所在州省Id
     */
    private Long fkAreaStateIdBirth;

    /**
     * 学生出生所在城市Id
     */
    private Long fkAreaCityIdBirth;

    /**
     * 学生出生所在国家名称
     */
    private String fkAreaCountryNameBirth;

    /**
     * 学生出生所在州省名称
     */
    private String fkAreaStateNameBirth;

    /**
     * 学生出生所在城市名称
     */
    private String fkAreaCityNameBirth;

    /**
     * 手机区号
     */
    private String mobileAreaCode;

    /**
     * 移动电话
     */
    private String mobile;

    /**
     * 电话区号
     */
    private String telAreaCode;

    /**
     * 电话
     */
    private String tel;

    /**
     * Email
     */
    private String email;

    /**
     * 学生现居所在国家Id
     */
    private Long fkAreaCountryId;

    /**
     * 学生现居所在州省Id
     */
    private Long fkAreaStateId;

    /**
     * 学生现居所在城市Id
     */
    private Long fkAreaCityId;

    /**
     * 学生现居所在国家名称
     */
    private String fkAreaCountryName;

    /**
     * 学生现居所在州省名称
     */
    private String fkAreaStateName;

    /**
     * 学生现居所在城市名称
     */
    private String fkAreaCityName;

    /**
     * 邮编
     */
    private String zipcode;

    /**
     * 联系地址
     */
    private String contactAddress;

    /**
     * 学历等级类型：高中/大学/本科/研究生/硕士/博士在读/博士/博士后
     */
    private String educationLevelType;

    /**
     * 毕业专业
     */
    private String educationMajor;

    /**
     * 毕业大学国家Id
     */
    private Long fkAreaCountryIdEducation;

    /**
     * 毕业大学州省Id
     */
    private Long fkAreaStateIdEducation;

    /**
     * 毕业大学城市Id
     */
    private Long fkAreaCityIdEducation;

    /**
     * 毕业大学国家名称
     */
    private String fkAreaCountryNameEducation;

    /**
     * 毕业大学州省名称
     */
    private String fkAreaStateNameEducation;

    /**
     * 毕业大学城市名称
     */
    private String fkAreaCityNameEducation;

    /**
     * 毕业院校Id
     */
    private Long fkInstitutionIdEducation;

    /**
     * 毕业院校名称
     */
    private String fkInstitutionNameEducation;

    /**
     * 毕业学校类型：985/211/其他，默认选项：其他
     */
    private String institutionTypeEducation;

    /**
     * 学历等级类型（国际）：高中/大学/本科/研究生/硕士/博士在读/博士/博士后
     */
    private String educationLevelType2;

    /**
     * 毕业专业（国际）
     */
    private String educationMajor2;

    /**
     * 毕业大学国家Id（国际）
     */
    private Long fkAreaCountryIdEducation2;

    /**
     * 毕业大学州省Id（国际）
     */
    private Long fkAreaStateIdEducation2;

    /**
     * 毕业大学城市Id（国际）
     */
    private Long fkAreaCityIdEducation2;

    /**
     * 毕业大学国家名称（国际）
     */
    private String fkAreaCountryNameEducation2;

    /**
     * 毕业大学州省名称（国际）
     */
    private String fkAreaStateNameEducation2;

    /**
     * 毕业大学城市名称（国际）
     */
    private String fkAreaCityNameEducation2;

    /**
     * 毕业院校Id（国际）
     */
    private Long fkInstitutionIdEducation2;

    /**
     * 毕业院校名称（国际）
     */
    private String fkInstitutionNameEducation2;

    /**
     * 项目说明，枚举：0=3+1/1=2+2/2=4+0/3=交换生
     */
    private Integer educationProject;

    /**
     * 学位情况，枚举：获得双学位/获得国际学位/获得国内学位
     */
    private Integer educationDegree;

    /**
     * 是否复杂教育背景：0否/1是
     */
    private Boolean isComplexEducation;

    /**
     * 复杂教育背景备注
     */
    private String complexEducationRemark;

    /**
     * 高中成绩类型，枚举Key
     */
    private String highSchoolTestType;

    /**
     * 高中成绩
     */
    private String highSchoolTestScore;

    /**
     * 本科成绩类型，枚举Key
     */
    private String standardTestType;

    /**
     * 本科成绩
     */
    private String standardTestScore;

    /**
     * 硕士成绩类型，枚举Key
     */
    private String masterTestType;

    /**
     * 硕士成绩
     */
    private String masterTestScore;

    /**
     * 英语测试类型
     */
    private String englishTestType;

    /**
     * 英语测试成绩
     */
    private BigDecimal englishTestScore;

    /**
     * 备注
     */
    private String remark;

    private String conditionType;


    /**
     * 学生Id（审批通过后学生Id）
     */
    private Long fkStudentId;

    /**
     * 平台应用Id
     */
    private Long fkPlatformId;

    /**
     * 平台应用CODE
     */
    private String fkPlatformCode;

    /**
     * 平台应用对应的创建用户Id，PARTNER=fk_partner_user_id（审批后绑定学生分配）
     */
    private Long fkPlatformCreateUserId;

    /**
     * 状态：-1作废/0未提交(草稿)/1已提交(待确认处理)/2正式生效
     */
    private Integer status;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}