package com.get.partnercenter.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * @Author:Oliver
 * @Date: 2025/2/12  10:46
 * @Version 1.0
 * 小程序用户代理Dto
 */
@Data
public class AgentAccountVo {

    @ApiModelProperty(value = "代理状态1启用0禁用")
    private Integer status;

    @ApiModelProperty(value = "代理ID")
    private Long agentId;

    @ApiModelProperty(value = "代理编号")
    private String agentNum;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "BD编号")
    private String bdCode;

    @ApiModelProperty(value = "BD名称")
    private String bdName;

    @ApiModelProperty(value = "BD名称-中文")
    private String bdNameChn;

    @ApiModelProperty(value = "代理关联的所属大区ID")
    private String areaRegionId;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty(value = "代理的州省名称")
    private String areaName;

    @ApiModelProperty(value = "代理的州省名称-中文")
    private String areaNameChn;

    @ApiModelProperty(value = "所属大区名称")
    private String areaRegionName;

    @ApiModelProperty(value = "所属大区名称-中文")
    private String areaRegionNameChn;

    @ApiModelProperty(value = "创建人")
    private String gmtCreateUser;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty("创建时间")
    private Date gmtCreate;

}
