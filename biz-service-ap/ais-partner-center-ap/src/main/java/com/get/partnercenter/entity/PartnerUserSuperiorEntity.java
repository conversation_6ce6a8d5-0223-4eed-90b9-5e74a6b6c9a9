package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-14 20:58:46
 */

@Data
@TableName("r_partner_user_superior")
@EqualsAndHashCode(callSuper = true)
public class PartnerUserSuperiorEntity  extends BaseEntity implements Serializable {

  private static final long serialVersionUID = 1L;

  @ApiModelProperty( "租户Id")
  private Long fkTenantId;
 

  @ApiModelProperty( "伙伴用户Id")
  private Long fkPartnerUserId;
 

  @ApiModelProperty( "伙伴用户业务上司Id")
  private Long fkPartnerUserIdSuperior;
 

}
