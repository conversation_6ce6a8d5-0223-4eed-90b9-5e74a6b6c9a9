package com.get.partnercenter.vo;

import com.get.partnercenter.entity.MEventEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


@Data
public class MEventRegistrationAgentVo extends MEventEntity {

    @ApiModelProperty("学生代理Id")
    private Long fkAgentId;
    @ApiModelProperty("学生代理")
    private String agentName;
    @ApiModelProperty("伙伴用户Id")
    private Long fkPartnerUserId;
    @ApiModelProperty("伙伴用户")
    private String partnerUserName;
    @ApiModelProperty("登陆用户")
    private String fkUserLoginId;


    @ApiModelProperty("枚举状态：0待定/1参加/2不参加")
    private Integer status;
    @ApiModelProperty("状态名称")
    private String statusName;

    @ApiModelProperty("角色名称")
    private String roleName;
    @ApiModelProperty("角色英文名称")
    private String roleNameEn;
    @ApiModelProperty("公司简称")
    private String companyName;



    @ApiModelProperty("代理BD")
    private String agentBD;

    @ApiModelProperty("代理编号")
    private String mStaffNum;
    @ApiModelProperty("代理名称")
    private String ASmStaffName;

    @ApiModelProperty("姓名")
    private String name;
    @ApiModelProperty("移动电话")
    private String mobile;
    @ApiModelProperty("参加人数")
    private Integer peopleCount;


    public String getStatusName() {
        if(status!=null){
            if(status==0){
                statusName="待定";
            }else if(status==1){
                statusName="参加";
            } else if (status==2) {
                statusName="取消参加";
            }
        }

        return statusName;
    }

    public String getAgentBD() {
        if( mStaffNum!=null && mStaffNum.length()>0){
            agentBD=mStaffNum;
        }
        if(ASmStaffName!=null && ASmStaffName.length()>0){
            if(agentBD!=null && agentBD.length()>0){
                agentBD=agentBD+"/"+ASmStaffName;
            }else {
                agentBD=ASmStaffName;
            }

        }
        return agentBD;
    }
}
