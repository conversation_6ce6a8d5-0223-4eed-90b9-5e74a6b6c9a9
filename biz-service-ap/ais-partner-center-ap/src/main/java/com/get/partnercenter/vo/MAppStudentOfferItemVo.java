package com.get.partnercenter.vo;

import com.get.partnercenter.entity.MAppStudentOfferItemEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class MAppStudentOfferItemVo extends MAppStudentOfferItemEntity {

    @ApiModelProperty("国家名称")
    private String fkAreaCountryName;
    private String fkAreaCountryNameChn;

    @ApiModelProperty("学校名称")
    private String institutionName;
    private String institutionNameChn;

    @ApiModelProperty("课程名称")
    private String fkInstitutionCourseName;
    private String fkInstitutionCourseNameChn;


    public String getInstitutionName() {
        if(institutionName==null || institutionName.length()==0){
            institutionName=institutionNameChn;
        }
        return institutionName;
    }

    public String getFkAreaCountryName() {
        if(fkAreaCountryName==null || fkAreaCountryName.length()==0){
            fkAreaCountryName=fkAreaCountryNameChn;
        }
        return fkAreaCountryName;
    }

    public String getFkInstitutionCourseName() {
        if(fkInstitutionCourseName==null || fkInstitutionCourseName.length()==0){
            fkInstitutionCourseName=fkInstitutionCourseNameChn;
        }
        return fkInstitutionCourseName;
    }
}
