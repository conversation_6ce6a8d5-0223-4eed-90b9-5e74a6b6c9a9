package com.get.partnercenter.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.get.core.mybatis.base.BaseVoEntity;
import com.get.partnercenter.entity.MInstitutionHighCommissionEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class HighCommissionVo extends MInstitutionHighCommissionEntity {


    @JsonSerialize(
            using = ToStringSerializer.class
    )
    @ApiModelProperty("主键id")
    private @NotNull(
            message = "主键不能为空！",
            groups = {BaseVoEntity.Update.class}
    ) Long id;

    @ApiModelProperty("学校名称")
    private String institutionName;

    /**
     * 文件guid(文档中心)
     */
    private String fileGuid;


    @ApiModelProperty("文件路径")
    private String fileKey;

    @ApiModelProperty("代理类别名称")
    private String agentTypeName;
    @ApiModelProperty("公司名称")
    private String companyName;

    @ApiModelProperty("平台名称")
    private String platFormName;

}
