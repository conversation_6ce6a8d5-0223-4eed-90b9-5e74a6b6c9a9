package com.get.partnercenter.vo;

import com.get.partnercenter.entity.MAppStudentCheckEntity;
import com.get.partnercenter.entity.MAppStudentEntity;
import com.get.partnercenter.enums.TypeEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class MAppStudentVo extends MAppStudentEntity {

    @ApiModelProperty("代理名字")
    private String agentName;

    private String agentPersonalName;


    @ApiModelProperty("公司名称")
    private String fkCompanyName;

    @ApiModelProperty("护照签发国家名称")
    private String fkAreaCountryNamePassport;

    @ApiModelProperty("绿卡国家名称")
    private String countryNameGreenCard;

    @ApiModelProperty("共享路径")
    private String sharedPath;

    @ApiModelProperty("学历等级类型(国内)")
    private String domesticEducationName;
    @ApiModelProperty("学历等级类型(国际)")
    private String internationalEducationName;

    @ApiModelProperty("项目说明")
    private String educationProjectName;

    @ApiModelProperty("学位情况")
    private String educationDegreeName;
    @ApiModelProperty("高中成绩类型名称")
    private String highSchoolTestTypeName;
    @ApiModelProperty("本科成绩类型名称")
    private String standardTestTypeName;

    @ApiModelProperty("研究生成绩类型名称")
    private String masterTestTypeName;

    @ApiModelProperty("英语测试类型名称")
    private String englishTestTypeName;

    @ApiModelProperty("申请计划")
    private List<MAppStudentOfferItemVo> mAppStudentOfferItemlist;

    @ApiModelProperty("申请计划-加申-未审批的数据")
    private List<MAppStudentOfferItemVo> mAppAddCheckStudentOfferItemlist;

    @ApiModelProperty("审核记录")
    private List<MAppStudentCheckEntity> mAppStudentCheckList;

    @ApiModelProperty("审批类型:0学生审批/1 加申审批")
    private Integer isAddItemStatus=0;
    @ApiModelProperty("加申ITEM明细数量")
    private int   jiashenCheckItemNUm;

    @ApiModelProperty("申请计划加申状态:0审核中/1完成")
    private Integer offerItemStatus=1;

    @ApiModelProperty("待申审核ITEM明细数量")
    private int   checkItemNUm;

    @ApiModelProperty( "学生附件")
    List<FileArray> fileArray;

    public String getEducationProjectName() {
        if(getEducationProject()!=null){
            if(getEducationProject().intValue()==0){
                educationProjectName= TypeEnum.THREE_ONE.value;
            }else if(getEducationProject().intValue()==1){
                educationProjectName= TypeEnum.TWO_TWO.value;
            }else if(getEducationProject().intValue()==2){
                educationProjectName= TypeEnum.FOUR_ZERO.value;
            }else if(getEducationProject().intValue()==3){
                educationProjectName= TypeEnum.EXCHANGE_STUDENTS.value;
            }
        }
        return educationProjectName;
    }

    public String getEducationDegreeName() {
        if(getEducationDegree()!=null){
            if(getEducationDegree().intValue()==0){
                educationDegreeName=TypeEnum.DOUBLE_DEGREE.value;
            }else if(getEducationDegree().intValue()==1){
                educationDegreeName=TypeEnum.INTERNATIONAL_DEGREE.value;
            }else if(getEducationDegree().intValue()==2){
                educationDegreeName=TypeEnum.DOMESTIC_DEGREE.value;
            }

        }
        return educationDegreeName;
    }

    public String getHighSchoolTestTypeName() {
        if(getHighSchoolTestType()!=null && !"".equals(getHighSchoolTestType())){
            highSchoolTestTypeName=TypeEnum.getBaseCombox(getHighSchoolTestType(),TypeEnum.HIGH_SCHOOL_GRADES);
        }
        return highSchoolTestTypeName;
    }

    public String getStandardTestTypeName() {
        if(getStandardTestType()!=null && !"".equals(getStandardTestType())){
            standardTestTypeName=TypeEnum.getBaseCombox(getStandardTestType(),TypeEnum.UNDERGRADUATE_ACHIEVEMENT);
        }
        return standardTestTypeName;
    }

    public String getMasterTestTypeName() {
        if(getMasterTestType()!=null && !"".equals(getMasterTestType())){
            masterTestTypeName=TypeEnum.getBaseCombox(getMasterTestType(),TypeEnum.UNDERGRADUATE_ACHIEVEMENT);
        }
        return masterTestTypeName;
    }

    public String getEnglishTestTypeName() {
        if(getEnglishTestType()!=null && !"".equals(getEnglishTestType())){
            englishTestTypeName=TypeEnum.getBaseCombox(getEnglishTestType(),TypeEnum.English_subtype);
        }

        return englishTestTypeName;
    }


    public Integer getIsAddItemStatus() {
        if(getJiashenCheckItemNUm()>0){
            isAddItemStatus=1;//加申审批
        }
        return isAddItemStatus;
    }

    public Integer getOfferItemStatus() {
        if(getIsAddItemStatus()!=null && getIsAddItemStatus().intValue()==1 && getCheckItemNUm()>0){
            offerItemStatus=0;
        }

        return offerItemStatus;
    }



}
