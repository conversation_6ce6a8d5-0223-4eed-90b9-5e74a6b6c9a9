package com.get.partnercenter.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.get.core.mybatis.base.BaseEntity;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-04-21 16:40:16
 */

@Data
@TableName("m_event")
@EqualsAndHashCode(callSuper = true) 
public class MEventEntity extends BaseEntity implements Serializable {

  @ApiModelProperty("活动Id")
  private Long id;
 

  @ApiModelProperty("公司Id")
  private Long fkCompanyId;
 

  @ApiModelProperty("活动类型Id")
  private Long fkEventTypeId;
 

  @ApiModelProperty("活动编号")
  private String num;
 

  @ApiModelProperty("活动时间")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date eventTime;
 

  @ApiModelProperty("活动结束时间")
  @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
  private Date eventTimeEnd;
 

  @ApiModelProperty("活动主题")
  private String eventTheme;
 

  @ApiModelProperty("活动目标对象")
  private String eventTarget;
 

  @ApiModelProperty("活动目标对象负责人")
  private String eventTargetLeader;
 

  @ApiModelProperty("活动举办国家Id")
  private Long fkAreaCountryIdHold;
 

  @ApiModelProperty("活动举办州省Id")
  private Long fkAreaStateIdHold;
 

  @ApiModelProperty("活动举办城市Id")
  private Long fkAreaCityIdHold;
 

  @ApiModelProperty("员工Id（负责人1）")
  private Long fkStaffIdLeader1;
 

  @ApiModelProperty("员工Id（负责人2）")
  private Long fkStaffIdLeader2;
 

  @ApiModelProperty("币种编号")
  private String fkCurrencyTypeNum;
 

  @ApiModelProperty("BD预算场地费用")
  private BigDecimal bdVenueAmount;
 

  @ApiModelProperty("BD预算餐饮费用")
  private BigDecimal bdFoodAmount;
 

  @ApiModelProperty("BD预算奖品费用")
  private BigDecimal bdPrizeAmount;
 

  @ApiModelProperty("BD预算其他费用")
  private BigDecimal bdOtherAmount;
 

  @ApiModelProperty("预算金额")
  private BigDecimal budgetAmount;
 

  @ApiModelProperty("实际金额")
  private BigDecimal actualAmount;
 

  @ApiModelProperty("预算人数")
  private Integer budgetCount;
 

  @ApiModelProperty("参加人数")
  private Integer attendedCount;
 

  @ApiModelProperty("描述（富文本）")
  private String description;
 

  @ApiModelProperty("是否激活：0否/1是")
  private Boolean isActive;
 

  @ApiModelProperty("公开对象：0不公开/1公开/2学生/3代理，支持多选，保存格式为1,2,PARTNER为13")
  private String publicLevel;
 

  @ApiModelProperty("备注")
  private String remark;
 

  @ApiModelProperty("状态：0计划/1结束/2取消")
  private Integer status;
 

 

}
