package com.get.partnercenter.dto;

import com.get.partnercenter.entity.MInstitutionHighCommissionEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class HighCommissionPutAwayParamsDto extends MInstitutionHighCommissionEntity {
    @NotNull(message = "上下架ID不能为空")
    @ApiModelProperty("上下架ID")
    private Long[] ids;
    @NotNull(message = "上架/下架 类型不能为空")
    @ApiModelProperty("1下架/2上架")
    private int type;




}
