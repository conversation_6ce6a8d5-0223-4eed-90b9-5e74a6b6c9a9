package com.get.partnercenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class CommissionParamsDto {

    @ApiModelProperty( "年份")
    @NotNull(message = "年份不能为空")
    private Integer year;

    
    @ApiModelProperty( "月份")
    @NotNull(message = "月份不能为空")
    private Integer month;

    @ApiModelProperty( "代理名称")
    private String agentName;

    @ApiModelProperty("国家ID")
    private Long areaCountryId;
    @ApiModelProperty("学校名称")
    private String institutionName;
    @ApiModelProperty("课程名字")
    private String courseName;


    @ApiModelProperty("课程等级ID")
    private Long courseLevelId;

    @ApiModelProperty("公司ID")
    private Long companyId;



}
