package com.get.partnercenter.entity;

import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-04-29 18:26:39
 */

@Data
@TableName("m_event_registration_agent")
@EqualsAndHashCode(callSuper = true)
public class MEventRegistrationAgentEntity  extends BaseEntity implements Serializable {

  @ApiModelProperty("活动汇总代理报名名册Id")
  private Long id;
 

  @ApiModelProperty("活动Id")
  private Long fkEventId;
 

  @ApiModelProperty("学生代理Id")
  private Long fkAgentId;
 

  @ApiModelProperty("伙伴用户Id")
  private Long fkPartnerUserId;
 

  @ApiModelProperty("枚举状态：0待定/1参加/2不参加")
  private Integer status;
 

  @ApiModelProperty("备注")
  private String remark;


}
