package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@TableName("m_partner_role")
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PartnerRole extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty( "租户Id")
    private Long fkTenantId;

    @ApiModelProperty( "公司Id")
    private Long fkCompanyId;

    @ApiModelProperty( "学生代理Id")
    private Long fkAgentId;

    @ApiModelProperty( "角色名称")
    private String roleName;

    @ApiModelProperty( "角色名称（英文）")
    private String roleNameEn;

    @ApiModelProperty( "角色CODE")
    private String roleCode;

    @ApiModelProperty( "角色描述")
    private String roleDesc;

    @ApiModelProperty( "是否激活：0否/1是")
    private Integer isActive;

}
