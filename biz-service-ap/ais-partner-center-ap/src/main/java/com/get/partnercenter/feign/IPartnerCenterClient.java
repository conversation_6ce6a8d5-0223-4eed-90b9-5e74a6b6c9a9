package com.get.partnercenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.core.tool.api.Result;
import com.get.partnercenter.dto.MediaAndAttachedDto;
import com.get.partnercenter.dto.RegisterPartnerUserDto;
import com.get.partnercenter.dto.wechat.LinkAndQrCodeVo;
import com.get.partnercenter.dto.wechat.MinProgramQrCodeDto;
import com.get.partnercenter.vo.MediaAndAttachedVo;
import com.get.partnercenter.vo.RegisterPartnerUserVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * @Author:Oliver
 * @Date: 2025/3/24
 * @Version 1.0
 * @apiNote: pmp-feign
 */
@FeignClient(value = AppCenterConstant.APPLICATION_PARTNER_CENTER)
public interface IPartnerCenterClient {

    /**
     * 注册伙伴用户
     *
     * @param list
     * @return
     */
    @PostMapping("/partnerUser/feign/registerPartnerUser")
    Result<List<RegisterPartnerUserVo>> registerPartnerUser(@RequestBody @Valid List<RegisterPartnerUserDto> list);

    /**
     * 添加媒体附件
     *
     * @param mediaAttachedVos
     * @return
     */
    @PostMapping("/mediaAndAttached/feign/addMediaAndAttachedList")
    Result<List<MediaAndAttachedVo>> addMediaAndAttachedList(@RequestBody List<MediaAndAttachedDto> mediaAttachedVos);

    /**
     * 获取小程序码-华通伙伴
     *
     * @param minProgramQrCodeDto
     * @return
     */
    @PostMapping("/wx/feign/getQrCode")
    Result<Object> getQrCode(@RequestBody @Valid MinProgramQrCodeDto minProgramQrCodeDto);


    /**
     * 获取小程序短链接-华通伙伴
     *
     * @param minProgramQrCodeDto
     * @return
     */
    @PostMapping("/wx/feign/getUrlLink")
    Result<Object> getUrlLink(@RequestBody @Valid MinProgramQrCodeDto minProgramQrCodeDto);

    /**
     * 获取小程序短链接和小程序-华通伙伴
     *
     * @param minProgramQrCodeDto
     * @return
     */
    @PostMapping("/wx/feign/getLinkAndQrCode")
    Result<LinkAndQrCodeVo> getLinkAndQrCode(@RequestBody @Valid MinProgramQrCodeDto minProgramQrCodeDto);

}
