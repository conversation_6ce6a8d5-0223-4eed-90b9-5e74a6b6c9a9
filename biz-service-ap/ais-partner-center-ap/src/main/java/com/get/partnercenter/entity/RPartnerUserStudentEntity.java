package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 
 * @TableName r_partner_user_student
 */
@TableName(value ="r_partner_user_student")
@Data
public class RPartnerUserStudentEntity extends BaseEntity implements Serializable {
    /**
     * 租户Id
     */
    private Long fkTenantId;

    /**
     * 伙伴用户Id
     */
    private Long fkPartnerUserId;

    /**
     * 学生Id
     */
    private Long fkStudentId;

    /**
     * 是否激活：0否/1是
     */
    private Boolean isActive;

    /**
     * 绑定时间
     */
    private Date activeDate;

    /**
     * 取消绑定时间（下次绑定时，需要重新建立记录）
     */
    private Date unactiveDate;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}