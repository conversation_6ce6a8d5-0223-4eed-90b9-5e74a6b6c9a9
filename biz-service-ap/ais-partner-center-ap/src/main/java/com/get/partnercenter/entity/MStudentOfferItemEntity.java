package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-18 11:40:29
 */

@Data
@TableName("m_student_offer_item") 
public class MStudentOfferItemEntity extends Model<MStudentOfferItemEntity>{

  @ApiModelProperty("学生申请方案项目Id")
  private Long id;


  @ApiModelProperty("学生申请方案项目父Id")
  private Long fkParentStudentOfferItemId;
 

  @ApiModelProperty("学生Id")
  private Long fkStudentId;
 

  @ApiModelProperty("代理Id（业绩绑定）")
  private Long fkAgentId;
 

  @ApiModelProperty("员工Id（业绩绑定，BD）")
  private Long fkStaffId;
 

  @ApiModelProperty("学生申请方案Id")
  private Long fkStudentOfferId;
 

  @ApiModelProperty("国家Id")
  private Long fkAreaCountryId;
 

  @ApiModelProperty("学校Id")
  private Long fkInstitutionId;
 

  @ApiModelProperty("学校学院Id")
  private Long fkInstitutionFacultyId;
 

  @ApiModelProperty("学校校区Id")
  private Long fkInstitutionZoneId;
 

  @ApiModelProperty("课程Id")
  private Long fkInstitutionCourseId;
 

  @ApiModelProperty("自定义课程Id（存储系统匹配的课程id）")
  private Long fkInstitutionCourseCustomId;
 

  @ApiModelProperty("学校提供商Id")
  private Long fkInstitutionProviderId;
 

  @ApiModelProperty("渠道来源Id")
  private Long fkInstitutionChannelId;
 

  @ApiModelProperty("学生申请方案项目状态步骤Id")
  private Long fkStudentOfferItemStepId;
 

  @ApiModelProperty("学生申请方案项目状态步骤时间")
  private LocalDateTime studentOfferItemStepTime;
 

  @ApiModelProperty("rpa order id（一键申请对应的Order id）")
  private Long fkIssueRpaOrderId;
 

  @ApiModelProperty("课程等级对应的ids，多个用逗号分隔")
  private String fkInstitutionCourseMajorLevelIds;
 

  @ApiModelProperty("课程类型对应的ids，多个用逗号分隔")
  private String fkInstitutionCourseTypeIds;
 

  @ApiModelProperty("课程类型组别对应的ids，多个用逗号分隔")
  private String fkInstitutionCourseTypeGroupIds;
 

  @ApiModelProperty("申请方案项目编号")
  private String num;
 

  @ApiModelProperty("学生ID（Offer ID）")
  private String studentId;
 

  @ApiModelProperty("课程长度类型(0周、1学期、2年)")
  private Integer durationType;
 

  @ApiModelProperty("课程长度")
  private BigDecimal duration;
 

  @ApiModelProperty("开学时间")
  private LocalDate openingTime;
 

  @ApiModelProperty("延迟入学时间（最终开学时间）")
  private LocalDate deferOpeningTime;
 

  @ApiModelProperty("结束时间")
  private LocalDate closingTime;
 

  @ApiModelProperty("币种编号")
  private String fkCurrencyTypeNum;
 

  @ApiModelProperty("学费金额")
  private BigDecimal tuitionAmount;
 

  @ApiModelProperty("学费付费状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)")
  private Integer tuitionStatus;
 

  @ApiModelProperty("申请费币种")
  private String fkAppFeeCurrencyTypeNum;
 

  @ApiModelProperty("申请费金额")
  private BigDecimal appFeeAmount;
 

  @ApiModelProperty("申请费付费状态：0.付费失败 1.已发送代付邮件 2.已付费(支付渠道) 3.已付费(学校)")
  private Integer appFeeStatus;
 

  @ApiModelProperty("课程官网URL")
  private String courseWebsite;
 

  @ApiModelProperty("课程开放时间")
  private LocalDate courseOpenTime;
 

  @ApiModelProperty("是否主课程：0否/1是（一套方案，只有一个主课程）")
  private Boolean isMain;
 

  @ApiModelProperty("是否后续课程：0否/1是")
  private Boolean isFollow;
 

  @ApiModelProperty("是否财务专用：0否/1是")
  private Boolean isFollowHidden;
 

  @ApiModelProperty("是否减免学分：0否/1是")
  private Boolean isCreditExemption;
 

  @ApiModelProperty("是否加申，0否/1是")
  private Boolean isAddApp;
 

  @ApiModelProperty("是否步骤更随主课，0否/1是")
  private Boolean isStepFollow;
 

  @ApiModelProperty("是否无佣金：0否/1是")
  private Boolean isNoCommission;
 

  @ApiModelProperty("新申请状态：枚举：0缺资料/1未开放")
  private Integer newAppStatus;
 

  @ApiModelProperty("新申请状态操作时间")
  private LocalDateTime newAppOptTime;
 

  @ApiModelProperty("申请方式：0网申/1扫描/2原件邮递/3其他")
  private Integer appMethod;
 

  @ApiModelProperty("申请备注（网申信息）")
  private String appRemark;
 

  @ApiModelProperty("备注")
  private String remark;
 

  @ApiModelProperty("学习计划业务状态(多选)：0获得第一阶段佣金、1获得第二阶段佣金、2获得第三阶段佣金、3获得第四阶段佣金 / 4只读第一阶段、5只读第二阶段、6只读第三阶段、7只读第四阶段")
  private String conditionType;
 

  @ApiModelProperty("入学失败原因Id")
  private Long fkEnrolFailureReasonId;
 

  @ApiModelProperty("其他入学失败原因")
  private String otherFailureReason;
 

  @ApiModelProperty("是否延迟入学标记：0否/1是")
  private Boolean isDeferEntrance;
 

  @ApiModelProperty("学习模式：枚举定义：0未定/1面授/2网课")
  private Integer learningMode;
 

  @ApiModelProperty("提交申请时间")
  private LocalDate submitAppTime;
 

  @ApiModelProperty("支付押金时间")
  private LocalDate depositTime;
 

  @ApiModelProperty("支付押金截止时间")
  private LocalDate depositDeadline;
 

  @ApiModelProperty("接受Offer截止时间")
  private LocalDate acceptOfferDeadline;
 

  @ApiModelProperty("支付学费时间")
  private LocalDate tuitionTime;
 

  @ApiModelProperty("保险购买方式枚举：0买学校保险/1通过Hti买保险/2通过其他机构买保险")
  private Integer insuranceBuyMethod;
 

  @ApiModelProperty("押金支付方式枚举：0=飞汇/1=易思汇/2=支付宝/3=银行/4=信用卡/5=其他汇款平台")
  private Integer depositPaymentMethod;
 

  @ApiModelProperty("学费支付方式枚举：0=飞汇/1=易思汇/2=支付宝/3=银行/4=信用卡/5=其他汇款平台")
  private Integer tuitionPaymentMethod;
 

  @ApiModelProperty("状态：0关闭/1打开")
  private Integer status;
 

  @ApiModelProperty("一键操作时间")
  private LocalDateTime rpaOptTime;
 

  @ApiModelProperty("一键完成时间")
  private LocalDateTime rpaFinishTime;
 

  @ApiModelProperty("一键备注")
  private String rpaRemark;
 

  @ApiModelProperty("一键处理状态：枚举")
  private String statusRpa;
 

  @ApiModelProperty("来自平台类型：get_mso")
  private String fkPlatformType;
 

  @ApiModelProperty("旧系统学校名称")
  private String oldInstitutionName;
 

  @ApiModelProperty("旧系统学校全称")
  private String oldInstitutionFullName;
 

  @ApiModelProperty("旧系统课程名称")
  private String oldCourseCustomName;
 

  @ApiModelProperty("旧系统课程专业等级名称")
  private String oldCourseMajorLevelName;
 

  @ApiModelProperty("旧系统课程类型名称")
  private String oldCourseTypeName;
 

  @ApiModelProperty("ISSUE课程输入标记：input/select")
  private String issueCourseInputFlag;
 

  @ApiModelProperty("旧数据财务id(gea)")
  private String idGeaFinance;
 

  @ApiModelProperty("旧数据id(issue)")
  private String idIssue;
 

  @ApiModelProperty("旧数据id(gea)")
  private String idGea;
 

  @ApiModelProperty("旧数据id(iae)")
  private String idIae;
 

  @ApiModelProperty("创建时间")
  private LocalDateTime gmtCreate;
 

  @ApiModelProperty("创建用户(登录账号)")
  private String gmtCreateUser;
 

  @ApiModelProperty("修改时间")
  private LocalDateTime gmtModified;
 

  @ApiModelProperty("修改用户(登录账号)")
  private String gmtModifiedUser;
 

}
