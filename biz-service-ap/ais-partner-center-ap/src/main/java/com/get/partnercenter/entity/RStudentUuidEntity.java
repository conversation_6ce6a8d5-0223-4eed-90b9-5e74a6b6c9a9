package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import java.io.Serializable;
import lombok.Data;

/**
 * 
 * @TableName r_student_uuid
 */
@TableName(value ="r_student_uuid")
@Data
public class RStudentUuidEntity extends BaseEntity implements Serializable {
    /**
     * 学生Id
     */
    private Long fkStudentId;

    /**
     * 学生UUID
     */
    private String fkStudentUuid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}