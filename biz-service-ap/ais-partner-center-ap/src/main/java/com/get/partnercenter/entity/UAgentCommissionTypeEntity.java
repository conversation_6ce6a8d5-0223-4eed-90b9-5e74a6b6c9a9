package com.get.partnercenter.entity;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-03-24 16:48:24
 */

@Data
@TableName("u_agent_commission_type")
public class UAgentCommissionTypeEntity extends BaseEntity implements Serializable {

  @ApiModelProperty("代理佣金分类Id")
  private Long id;
 

  @ApiModelProperty("公司Id（每个区域分公司都有自己的分类及命名）")
  private Long fkCompanyId;
 

  @ApiModelProperty("分类名称，如：1级代理，2级代理")
  private String typeName;
 

  @ApiModelProperty("排序，倒序：数字由大到小排列")
  private Integer viewOrder;
 

  @ApiModelProperty("是否激活：0否/1是，当设置屏蔽后，其绑定对应的佣金模板也会失效")
  private Boolean isActive;

  @TableField(exist = false)
  private static final long serialVersionUID = 1L;
 

}
