package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.Alias;

import java.io.Serializable;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-01-06 14:44:03
 */

@Data
@TableName("s_media_and_attached")
@Alias("partnerSMediaAndAttachedEntity")
public class SMediaAndAttachedEntity extends BaseEntity implements Serializable {

  @ApiModelProperty("媒体附件Id")
  private Long id;
 

  @ApiModelProperty("文件guid(文档中心)")
  private String fkFileGuid;
 

  @ApiModelProperty("表名")
  private String fkTableName;
 

  @ApiModelProperty("表Id")
  private Long fkTableId;
 

  @ApiModelProperty("类型关键字，如：institution_mov/institution_pic/alumnus_head_icon")
  private String typeKey;
 

  @ApiModelProperty("索引值(默认从0开始，同一类型下值唯一)")
  private Integer indexKey;
 

  @ApiModelProperty("链接")
  private String link;
 

  @ApiModelProperty("备注")
  private String remark;
 

 

}
