package com.get.partnercenter.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
@AllArgsConstructor
public enum MailTemplateTypeEnum {

    INVITE_TO_REGISTER("INVITE_TO_REGISTER", "邀请邮件模板", 1),
    RESET_PASSWORD("RESET_PASSWORD", "密码邮件模板", 2),
    RESET_PASSWORD_APP("RESET_PASSWORD_APP", "密码邮件模板-小程序", 3),
    CONFIRM_COMMISSION("CONFIRM_COMMISSION", "确认签证函学生名单", 4),
    REGISTER_PARTNER_USER("REGISTER_PARTNER_USER", "REGISTER_PARTNER_USER", 5);

    private String code;
    private String msg;
    private Integer type;

    public static MailTemplateTypeEnum getEnumByType(Integer type) {
        for (MailTemplateTypeEnum value : MailTemplateTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    public static MailTemplateTypeEnum getEnumByCode(String code) {
        for (MailTemplateTypeEnum value : MailTemplateTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
