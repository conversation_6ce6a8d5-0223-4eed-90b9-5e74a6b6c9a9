package com.get.partnercenter.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import java.io.Serializable;
import java.util.Date;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 * @TableName m_app_student_offer_item
 */
@TableName(value ="m_app_student_offer_item")
@Data
public class MAppStudentOfferItemEntity extends BaseEntity implements Serializable {
    /**
     * 申请学生Id
     */
    private Long fkAppStudentId;

    /**
     * 国家Id
     */
    private Long fkAreaCountryId;

    /**
     * 学校Id
     */
    private Long fkInstitutionId;

    /**
     * 课程Id
     */
    private Long fkInstitutionCourseId;

    /**
     * 开学时间
     */
    private Date openingTime;



    @ApiModelProperty("学生申请方案项目Id（审批通过后需要回填Id，建立关系）")
    private Long fkStudentOfferItemId;

    @ApiModelProperty("是否加申：0否/1是")
    private Boolean isAdditional;

    @ApiModelProperty("加申状态：-1作废/0未提交(草稿)/1已提交(待确认处理)/2正式生效")
    private Integer statusAdditional;


    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}