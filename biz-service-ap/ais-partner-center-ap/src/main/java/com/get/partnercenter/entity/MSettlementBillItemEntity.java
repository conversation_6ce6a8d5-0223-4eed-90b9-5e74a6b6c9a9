package com.get.partnercenter.entity;


import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Description 
 * <AUTHOR>
 * @Date: 2025-02-14 17:39:29
 */

@Data
@TableName("m_settlement_bill_item")
public class MSettlementBillItemEntity extends BaseEntity implements Serializable {

  @ApiModelProperty("结算账单明细项Id")
  private Long id;

 

  @ApiModelProperty("结算账单Id")
  private Long fkSettlementBillId;
 

  @ApiModelProperty("学生Id")
  private Long fkStudentId;
 

  @ApiModelProperty("应付计划Id")
  private Long fkPayablePlanId;
 

  @ApiModelProperty("应付币种")
  private String fkCurrencyTypeNum;
 

  @ApiModelProperty("实际支付金额（合并小计=实收+预付）")
  private BigDecimal amountActual;
 

  @ApiModelProperty("实际手续费金额（合并小计=实收+预付）")
  private BigDecimal serviceFeeActual;
 

  @ApiModelProperty("兑换汇率")
  private BigDecimal exchangeRate;
 

  @ApiModelProperty("兑换支付金额")
  private BigDecimal amountExchange;
 

  @ApiModelProperty("兑换手续费金额")
  private BigDecimal serviceFeeExchange;

 

}
