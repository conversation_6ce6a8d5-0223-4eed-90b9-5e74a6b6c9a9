package com.get.partnercenter.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author:<PERSON>
 * @Date: 2025/2/24  10:43
 * @Version 1.0
 */
@Data
public class BdVo {

    @ApiModelProperty("BD ID")
    private Long staffId;

    @ApiModelProperty("BD名称")
    private String name;

    @ApiModelProperty("BD编号")
    private String num;

    @ApiModelProperty("邮箱")
    private String email;

    @ApiModelProperty("邮箱密码-为空代表不能发邮箱")
    private String emailPassword;

    @ApiModelProperty("代理ID")
    private Long agentId;

    @ApiModelProperty("代理公司ID")
    private Long companyId;

    @ApiModelProperty("代理名称")
    private String agentName;

    @ApiModelProperty("BD登录账号")
    private String loginId;
}
