package com.get.gateway.filter;


import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.get.core.jwt.JwtUtil;
import com.get.gateway.provider.AuthProvider;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.TimeUnit;


import static org.springframework.cloud.gateway.support.ServerWebExchangeUtils.addOriginalRequestUrl;

/**
 * 接口鉴权认证
 *
 * @author: David.xie
 */
@Slf4j
@Component
@RequiredArgsConstructor
@RefreshScope
public class InterfaceServerFilter implements GlobalFilter, Ordered {

    @Value("${defautauth.username}")
    private String username;
    @Value("${defautauth.password}")
    private String password;
    @Value("${defautauth.clientid}")
    private String clientId;
    @Value("${defautauth.clientsecret}")
    private String clientSecret;
    @Value("${defautauth.authUrl}")
    private String authUrl;
    @Value("${defautauth.userId}")
    private String baseUid;

    private String tokenKey;

    private String sessionIdKey;

    @PostConstruct
    public void init() {
        this.tokenKey = "get:token".concat("::").concat("token:sign:").concat(baseUid);
        this.sessionIdKey = "get:token".concat("::").concat("token:session:").concat(baseUid);
    }

    private String token;
    private Long expires_in = 3600L;

    private Long expires_mills;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String nonceStr = "";
        if (exchange.getRequest().getHeaders().get("X-Nonce") != null && !exchange.getRequest().getHeaders().get("X-Nonce").isEmpty()) {
            nonceStr = exchange.getRequest().getHeaders().get("X-Nonce").get(0);
            String nonceKey = "SignatureVerificationFilter:nonce:" + nonceStr;
            String s = this.redisTemplate.opsForValue().get(nonceKey);
            if (s != null) {
                String token = getToken();
                log.info("sign-token:" + token);

                request = exchange.getRequest().mutate()
                        .headers(httpHeaders -> httpHeaders.set(AuthProvider.AUTH_KEY, token))
                        .build();

                addOriginalRequestUrl(exchange, request.getURI());

                String rawPath = request.getURI().getRawPath();
                ServerHttpRequest newRequest = request.mutate()
                        .path(rawPath)
                        .build();

                return chain.filter(exchange.mutate()
                        .request(newRequest.mutate()
                                .build()).build());

            } else {
                return chain.filter(exchange);
            }
        }
        return chain.filter(exchange);
    }

    private String getToken() {

        String getTokenStr = this.redisTemplate.opsForValue().get(tokenKey);

        String getSessionIdStr = this.redisTemplate.opsForValue().get(sessionIdKey);

        String key = "";

        String getSessionValue = "";

        String accessToken = "";

        if (getSessionIdStr != null && getTokenStr != null) {
            key = "get:token".concat("::").concat("token:state:").concat(baseUid + ":").concat(getSessionIdStr);
            getSessionValue = this.redisTemplate.opsForValue().get(key);
            accessToken = JwtUtil.getAccessToken(baseUid, getSessionIdStr, getTokenStr);
        }

        if (StrUtil.isNotEmpty(accessToken) && accessToken != null && !accessToken.equals("null") && getSessionValue != null) {
            log.info("sign-getTokenStr:" + getTokenStr);
            return accessToken;
        } else {
            this.redisTemplate.delete(tokenKey);
            this.redisTemplate.delete(sessionIdKey);
            CloseableHttpClient httpClient = HttpClients.createDefault();
            long current_time = System.currentTimeMillis();
            String plainClientCredentials = clientId + ":" + clientSecret;
            String base64ClientCredentials = new String(Base64.encode(plainClientCredentials.getBytes()));
            String url = authUrl;
            log.info("Cache url:{}", url);
            RequestConfig config = RequestConfig.custom().setConnectTimeout(20000).setConnectionRequestTimeout(1000).setSocketTimeout(20000)
                    .build();
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Authorization", "Basic " + base64ClientCredentials);
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
            httpPost.setConfig(config);
            HashMap<String, String> param = new HashMap<String, String>();
            param.put("grant_type", password);
            param.put("username", username);
            param.put("password", password);
            List<NameValuePair> paramList = new ArrayList<>();
            for (String key1 : param.keySet()) {
                paramList.add(new BasicNameValuePair(key1, param.get(key1)));
            }

            UrlEncodedFormEntity entity = null;
            try {
                entity = new UrlEncodedFormEntity(paramList);
            } catch (UnsupportedEncodingException e1) {
                e1.printStackTrace();
            }
            httpPost.setEntity(entity);
            try {
                log.info("sign-HttpResponse httpPost:{}", httpPost);
                HttpResponse response = httpClient.execute(httpPost);
                log.info("sign-HttpResponse response:{}", response);
                String responseJson = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.info("sign-getToken return:" + responseJson);

                if (response.getStatusLine().getStatusCode() == 200) {
                    JSONObject jsonObject = JSONObject.parseObject(responseJson);
                    token = (String) jsonObject.get("access_token");
                    String getSessionId = (String) jsonObject.get("session_id");
                    expires_mills = expires_in + current_time;
                    this.redisTemplate.opsForValue().set(tokenKey, token, expires_mills, TimeUnit.SECONDS);
                    this.redisTemplate.opsForValue().set(sessionIdKey, getSessionId, expires_mills, TimeUnit.SECONDS);
                    return token;
                }
            } catch (Exception e) {
                log.error("sign-getToken error:{}", e.getMessage());
                e.printStackTrace();
            }
        }

        return "";
    }

    @Override
    public int getOrder() {
        return -2000;
    }

}
