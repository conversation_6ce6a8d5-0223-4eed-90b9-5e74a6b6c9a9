package com.get.gateway.filter;

import com.get.core.jwt.JwtUtil;
import com.get.gateway.provider.AuthProvider;
import com.get.gateway.provider.RequestProvider;
import io.jsonwebtoken.Claims;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.boot.actuate.autoconfigure.endpoint.web.WebEndpointProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.http.HttpHeaders;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.ArrayList;
import java.util.List;

/**
 * webflux 日志请求记录，方便开发时调试。
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
@ConditionalOnProperty(value = "get.log.request.enabled", havingValue = "true", matchIfMissing = true)
public class GlobalRequestLogFilter implements GlobalFilter, Ordered {
    private final WebEndpointProperties endpointProperties;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        // 打印请求路径
        String path = request.getPath().pathWithinApplication().value();

        // 忽略 endpoint 请求
        String endpointBasePath = endpointProperties.getBasePath();
        if (StringUtils.isNotBlank(endpointBasePath) && path.startsWith(endpointBasePath)) {
            return chain.filter(exchange);
        }

        String requestUrl = RequestProvider.getOriginalRequestUrl(exchange);

        // 构建成一条长 日志，避免并发下日志错乱
        StringBuilder beforeReqLog = new StringBuilder(300);
        // 日志参数
        List<Object> beforeReqArgs = new ArrayList<>();
        beforeReqLog.append("\n\n================ Gateway Request Start  ================\n");
        // 打印路由
        beforeReqLog.append("===> {}: {}\n");
        // 参数
        String requestMethod = request.getMethodValue();
        beforeReqArgs.add(requestMethod);
        beforeReqArgs.add(requestUrl);

        // 打印请求头
        HttpHeaders headers = request.getHeaders();
        headers.forEach((headerName, headerValue) -> {
            beforeReqLog.append("===Headers===  {}: {}\n");
            beforeReqArgs.add(headerName);
            if (AuthProvider.AUTH_KEY.toLowerCase().equals(headerName)) {
                String value = headerValue.get(0);
                String token = JwtUtil.getToken(value);
                Claims claims = JwtUtil.parseJWT(token);
                beforeReqArgs.add((claims == null) ? "" : claims.toString());
                beforeReqLog.append("===Headers===  {}: {}\n");
                beforeReqArgs.add(headerName.concat("-original"));
                beforeReqArgs.add(StringUtils.join(headerValue.toArray()));
            } else {
                beforeReqArgs.add(StringUtils.join(headerValue.toArray()));
            }
        });

        beforeReqLog.append("================  Gateway Request End  =================\n");
        // 打印执行时间
        log.info(beforeReqLog.toString(), beforeReqArgs.toArray());
        return chain.filter(exchange);
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE;
    }
}
