package com.get.workflowcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@TableName("m_payment_application_form_item")
public class WorkFlowPaymentApplicationFormItem extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 支付申请单Id
     */
    @ApiModelProperty(value = "支付申请单Id")
    @Column(name = "fk_payment_application_form_id")
    private Long fkPaymentApplicationFormId;
    /**
     * 付款费用类型Id
     */
    @ApiModelProperty(value = "付款费用类型Id")
    @Column(name = "fk_payment_fee_type_id")
    private Long fkPaymentFeeTypeId;
    /**
     * 支付金额
     */
    @ApiModelProperty(value = "支付金额")
    @Column(name = "amount")
    private BigDecimal amount;
    /**
     * 摘要
     */
    @ApiModelProperty(value = "摘要")
    @Column(name = "summary")
    private String summary;
    /**
     * 供应商Id
     */
    @ApiModelProperty(value = "供应商Id")
    @Column(name = "fk_provider_id")
    private Long fkProviderId;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkPaymentApplicationFormId=").append(fkPaymentApplicationFormId);
        sb.append(", fkPaymentFeeTypeId=").append(fkPaymentFeeTypeId);
        sb.append(", amount=").append(amount);
        sb.append(", summary=").append(summary);
        sb.append(", fkProviderId=").append(fkProviderId);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}