package com.get.workflowcenter.vo;

import com.get.workflowcenter.entity.ActReProcdef;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2020/11/12 16:23
 */
@Data
public class ActReProcdefVo extends ActReProcdef {

    @ApiModelProperty("部署时间")
    private Date deployTime;

    /**
     * 流程类型名称
     */
    @ApiModelProperty(value = "流程类型名称")
    private String workFlowTypeName;

    @ApiModelProperty("公司名称")
    private String companyName;
}
