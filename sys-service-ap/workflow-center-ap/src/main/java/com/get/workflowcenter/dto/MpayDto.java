package com.get.workflowcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/3/18 10:20
 */
@Data
public class MpayDto extends BaseVoEntity {


    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    @Column(name = "fk_department_id")
    private Long fkDepartmentId;

    /**
     * 部门主管id
     */
    @ApiModelProperty(value = "部门主管id")
    @Column(name = "fk_department_manager_id")
    private Long fkDepartmentManagerId;

    /**
     * 会计主管
     */
    @ApiModelProperty(value = "会计主管")
    @Column(name = "fk_accounting_supervisor_id")
    private Long fkAccountingSupervisorId;

    /**
     * 出纳人
     */
    @ApiModelProperty(value = "出纳人")
    @Column(name = "fk_cashier_id")
    private Long fkCashierId;

    /**
     * 复核人id
     */
    @ApiModelProperty(value = "复核人id")
    @Column(name = "fk_reviewer_id")
    private Long fkReviewerId;

    private Long fkCertifierId;

    /**
     * 经手人
     */
    @ApiModelProperty(value = "经手人")
    @Column(name = "fk_handled_by_id")
    private Long fkHandledById;

    /**
     * 事由或品名
     */
    @ApiModelProperty(value = "事由或品名")
    @Column(name = "reason")
    private String reason;

    /**
     * 数量
     */
    @ApiModelProperty(value = "数量")
    @Column(name = "number")
    private String number;

    /**
     * 单位
     */
    @ApiModelProperty(value = "单位")
    @Column(name = "unit")
    private String unit;

    /**
     * 单价
     */
    @ApiModelProperty(value = "单价")
    @Column(name = "unit_price")
    private Long unitPrice;

    /**
     * 总金额
     */
    @ApiModelProperty(value = "总金额")
    @Column(name = "total_money")
    private Long totalMoney;

    /**
     * 受款人
     */
    @ApiModelProperty(value = "受款人")
    @Column(name = "payee_name")
    private String payeeName;

    /**
     * 受款人地址
     */
    @ApiModelProperty(value = "受款人地址")
    @Column(name = "payee_address")
    private String payeeAddress;

    /**
     * 未能取得单据原因
     */
    @ApiModelProperty(value = "未能取得单据原因")
    @Column(name = "reason_for_refusal")
    private String reasonForRefusal;

    /**
     * 日期
     */
    @ApiModelProperty(value = "日期")
    @Column(name = "date")
    private Date date;


}
