package com.get.workflowcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("act_hi_taskinst")
public class ActHiTaskInst implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(
            value = "ID_",
            type = IdType.NONE
    )
    private String id;
    @TableField("PROC_DEF_ID_")
    private String procDefId;
    @TableField("TASK_DEF_KEY_")
    private String taskDefKey;
    @TableField("PROC_INST_ID_")
    private String procInstId;
    @TableField("EXECUTION_ID_")
    private String executionId;
    @TableField("NAME_")
    private String name;
    @TableField("PARENT_TASK_ID_")
    private String parentTaskId;
    @TableField("DESCRIPTION_")
    private String description;
    @TableField("OWNER_")
    private String owner;
    @TableField("ASSIGNEE_")
    private String assignee;
    @TableField("START_TIME_")
    private Date startTime;
    @TableField("CLAIM_TIME_")
    private Date claimTime;
    @TableField("END_TIME_")
    private Date endTime;
    @TableField("DURATION_")
    private Long duration;
    @TableField("DELETE_REASON_")
    private String deleteReason;
    @TableField("PRIORITY_")
    private Integer priority;
    @TableField("DUE_DATE_")
    private Date dueDate;
    @TableField("FORM_KEY_")
    private String formKey;
    @TableField("CATEGORY_")
    private String category;
    @TableField("TENANT_ID_")
    private String tenantId;
}