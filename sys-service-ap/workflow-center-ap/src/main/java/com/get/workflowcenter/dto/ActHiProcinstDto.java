package com.get.workflowcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2020/12/18 16:47
 */
@Data
public class ActHiProcinstDto extends BaseVoEntity {


    @ApiModelProperty(value = "流程名称")
    private String procName;


    //自定义内容
    /**
     * 列表类型（1：通用列表/2：定制列表）
     */
    @ApiModelProperty(value = "列表类型（1：通用列表/2：定制列表）")
    private Integer mode;


    @ApiModelProperty(value = "流程分类")
    private String category;


    @ApiModelProperty("申请人名字")
    private String startStaffName;


}
