package com.get.workflowcenter.vo;

import com.get.workflowcenter.entity.ActHiTaskInst;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/7 0:55
 */
@Data
public class ActHiTaskInstVo extends ActHiTaskInst {

    @ApiModelProperty("请假理由")
    private String leaveReason;
    @ApiModelProperty("请假类型")
    private String leaveType;
    @ApiModelProperty("请假时间")
    private String leaveDays;
    @ApiModelProperty("部门名称")
    private String departmentName;
    @ApiModelProperty("办公室名称")
    private String officeName;
    @ApiModelProperty("工作流类型")
    private String workFlowType;
    @ApiModelProperty("开始时间")
    private Date startDay;
    @ApiModelProperty("结束时间")
    private Date endDay;
    @ApiModelProperty("参数map")
    private Map<String, String> map;

}
