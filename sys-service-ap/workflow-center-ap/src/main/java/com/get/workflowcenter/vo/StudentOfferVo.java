package com.get.workflowcenter.vo;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.workflowcenter.entity.WorkFlowStudentOffer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Set;

/**
 * <AUTHOR>
 * @DATE: 2020/11/13
 * @TIME: 15:44
 * @Description:
 **/
@Data
public class StudentOfferVo extends WorkFlowStudentOffer {

    @ApiModelProperty(value = "代理名称")
    private String fkAgentName;

    @ApiModelProperty(value = "BD名称")
    private String bdName;

    @ApiModelProperty(value = "国家名字")
    private String fkAreaCountryName;

    /**
     * @Description: 学习计划学校
     * @Author: Jerry
     * @Date:12:59 2021/8/17
     */
    @ApiModelProperty(value = "学习计划学校")
    private Set<BaseSelectEntity> institutionTabs;
}
