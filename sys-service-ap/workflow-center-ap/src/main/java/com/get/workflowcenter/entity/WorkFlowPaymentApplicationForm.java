package com.get.workflowcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.get.core.mybatis.base.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

/**
 * 支付申请单
 */
@Data
@TableName("m_payment_application_form")
public class WorkFlowPaymentApplicationForm extends BaseEntity implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 公司Id
     */
    @ApiModelProperty(value = "公司Id")
    @Column(name = "fk_company_id")
    private Long fkCompanyId;
    /**
     * 部门Id
     */
    @ApiModelProperty(value = "部门Id")
    @Column(name = "fk_department_id")
    private Long fkDepartmentId;
    /**
     * 办公室Id
     */
    @ApiModelProperty(value = "办公室Id")
    @Column(name = "fk_office_id")
    private Long fkOfficeId;
    /**
     * 申请人Id
     */
    @ApiModelProperty(value = "申请人Id")
    @Column(name = "fk_staff_id")
    private Long fkStaffId;
    /**
     * 支付申请单编号（系统生成）
     */
    @ApiModelProperty(value = "支付申请单编号（系统生成）")
    @Column(name = "num")
    private String num;
    /**
     * 币种编号
     */
    @ApiModelProperty(value = "币种编号")
    @Column(name = "fk_currency_type_num")
    private String fkCurrencyTypeNum;
    /**
     * 状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废
     */
    @ApiModelProperty(value = "状态： 0待发起/1审批结束/2审批中/3审批拒绝/4申请放弃/5作废")
    @Column(name = "status")
    private Integer status;

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", fkCompanyId=").append(fkCompanyId);
        sb.append(", fkDepartmentId=").append(fkDepartmentId);
        sb.append(", fkOfficeId=").append(fkOfficeId);
        sb.append(", fkStaffId=").append(fkStaffId);
        sb.append(", num=").append(num);
        sb.append(", fkCurrencyTypeNum=").append(fkCurrencyTypeNum);
        sb.append(", status=").append(status);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}