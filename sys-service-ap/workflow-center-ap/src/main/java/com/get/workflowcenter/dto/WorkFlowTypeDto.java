package com.get.workflowcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2021/1/22 16:03
 * @verison: 1.0
 * @description:
 */
@Data
public class WorkFlowTypeDto extends BaseVoEntity {
    /**
     * 流程类型名称
     */
    @ApiModelProperty(value = "流程类型名称")
    private String typeName;

    /**
     * 排序，倒序：数字由大到小排列
     */
    @ApiModelProperty(value = "排序，倒序：数字由大到小排列")
    private Integer viewOrder;
}
