package com.get.workflowcenter.vo;

import com.get.workflowcenter.entity.WorkFlowMediaAndAttached;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/8/14 10:10
 * @verison: 1.0
 * @description:
 */
@Data
public class WorkFlowMediaAndAttachedVo extends WorkFlowMediaAndAttached {

    @ApiModelProperty(value = "文件路径")
    private String filePath;

    /**
     * 源文件名
     */
    @ApiModelProperty(value = "源文件名")
    private String fileNameOrc;

    private String typeValue;
}
