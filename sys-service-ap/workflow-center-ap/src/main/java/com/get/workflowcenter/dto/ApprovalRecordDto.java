package com.get.workflowcenter.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ApprovalRecordDto {

    @ApiModelProperty(value = "公司ids")
    private List<Long> companyIds;

    @ApiModelProperty(value = "审批类型")
    private String approvalType;

    @ApiModelProperty(value = "审批状态")
    private Integer approvalStatus;

    @ApiModelProperty(value = "标题")
    private String title;

    @ApiModelProperty(value = "选项Tab, myApplication: '我的申请'   myApproval: '我的审批'   allApprovals: '所有审批'")
    private String approvalTab;

}
