package com.get.workflowcenter.vo;

import com.get.workflowcenter.entity.WorkFlowPrepayApplicationForm;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2021/4/1 11:26
 */
@Data
public class PrepayApplicationFormVo extends WorkFlowPrepayApplicationForm {
    @ApiModelProperty("发起人")
    private String startByName;
    @ApiModelProperty("执行人/审批人")
    private String assignee;
    @ApiModelProperty("开始时间")
    private Date startTimeDto;
    @ApiModelProperty("任务耗时")
    private String totalTime;
    @ApiModelProperty("当前步骤")
    private String actName;
    @ApiModelProperty("结束时间")
    private Date EndTimeDto;
    @ApiModelProperty("MSG")
    private String Msg;
    @ApiModelProperty("任务id")
    private String taskId;
    @ApiModelProperty(value = "事物或理由，金额等")
    private PaymentApplicationFormItemVo mpayItemDtos = null;
    @ApiModelProperty(value = "公司名称")
    private String DepartmentName;

    /**
     * 审批动作:0/1:驳回/同意
     */
    @ApiModelProperty(value = "审批动作:0/1:驳回/同意")
    private Integer approvalAction;

    /**
     * 审批动作名称：驳回/同意
     */
    @ApiModelProperty(value = "审批动作名称：驳回/同意")
    private String approvalActionName;

}
