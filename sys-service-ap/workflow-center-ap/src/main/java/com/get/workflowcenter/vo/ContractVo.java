package com.get.workflowcenter.vo;

import com.get.workflowcenter.entity.WorkFlowContract;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @DATE: 2020/8/20
 * @TIME: 17:48
 * @Description: 合同管理DTO
 **/
@Data
public class ContractVo extends WorkFlowContract implements Serializable {

    @ApiModelProperty(value = "学校提供商名称")
    private String fkInstitutionProviderName;

    /**
     * 学校Id
     */
    @ApiModelProperty(value = "学校名称")
    private String fkInstitutionName;

    /**
     * 学校课程Id
     */
    @ApiModelProperty(value = "学校课程名称")
    private String fkInstitutionCourseName;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型")
    private String fkContractTypeName;

    /**
     * 公司名称
     */
    @ApiModelProperty(value = "公司名称")
    private String fkCompanyName;
    @ApiModelProperty(value = "流程状态")
    private Integer status;
    /**
     * 定义下拉枚举：0新签/1续签
     */
    @ApiModelProperty(value = "定义下拉枚举：0新签/1续签")
    private List<String> contractApprovalModeEnum;
    /**
     * 任务版本号
     */
    @ApiModelProperty("任务版本号")
    private Integer taskVersion;
    /**
     * 任务id
     */
    @ApiModelProperty("任务id")
    private String taskId;
    /**
     * 待签或代表
     */
    @ApiModelProperty("待签或代表")
    private int signOrGetStatus;
    /**
     * 流程实例id
     */
    @ApiModelProperty("流程实例id")
    private String procInstId;
    /**
     * 发起人
     */
    @ApiModelProperty("发起人")
    private String startByName;
    /**
     * 执行人/审批人
     */
    @ApiModelProperty("执行人/审批人")
    private String assignee;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Date startTimeDto;
    /**
     * 任务耗时
     */
    @ApiModelProperty("任务耗时")
    private String totalTime;
    /**
     * 当前步骤
     */
    @ApiModelProperty("当前步骤")
    private String actName;
    /**
     * 结束时间
     */
    @ApiModelProperty("结束时间")
    private Date EndTimeDto;
    /**
     * MSG
     */
    @ApiModelProperty("MSG")
    private String Msg;

    /**
     * 审批动作:0/1:驳回/同意
     */
    @ApiModelProperty(value = "审批动作:0/1:驳回/同意")
    private Integer approvalAction;

    /**
     * 审批动作名称：驳回/同意
     */
    @ApiModelProperty(value = "审批动作名称：驳回/同意")
    private String approvalActionName;

}
