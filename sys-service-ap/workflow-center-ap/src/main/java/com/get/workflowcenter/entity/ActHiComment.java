package com.get.workflowcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("act_hi_comment")
public class ActHiComment implements Serializable {
    private static final long serialVersionUID = 1L;
    @TableId(
            value = "ID_",
            type = IdType.NONE
    )
    private String id;
    @TableField("TYPE_")
    private String type;
    @TableField("TIME_")
    private Date time;
    @TableField("USER_ID_")
    private String userId;
    @TableField("TASK_ID_")
    private String taskId;
    @TableField("PROC_INST_ID_")
    private String procInstId;
    @TableField("ACTION_")
    private String action;
    @TableField("MESSAGE_")
    private String message;
    @TableField("FULL_MSG_")
    private byte[] fullMsg;
}