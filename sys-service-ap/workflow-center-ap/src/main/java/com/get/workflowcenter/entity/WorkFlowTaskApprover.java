package com.get.workflowcenter.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Id;
import java.io.Serializable;

/**
 * <AUTHOR> by Ron
 * @date 2020/5/18 0:26
 */
@Data
@TableName("task_approver")
public class WorkFlowTaskApprover implements Serializable {

    private static final long serialVersionUID = 3981027707801654562L;

    @Id
    private int taId;
    @Column(name = "gea_user_id")
    private Integer geaUserId;
    @Column(name = "task_id")
    private String taskId;


    @Override
    public String toString() {
        return "TaskApprover{" +
                "taId=" + taId +
                ", geaUserId=" + geaUserId +
                ", taskId='" + taskId + '\'' +
                '}';
    }
}
