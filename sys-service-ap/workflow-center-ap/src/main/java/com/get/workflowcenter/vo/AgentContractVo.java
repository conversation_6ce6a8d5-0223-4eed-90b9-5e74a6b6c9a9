package com.get.workflowcenter.vo;

import com.get.workflowcenter.entity.WorkFlowAgentContract;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @DATE: 2020/9/14
 * @TIME: 12:36
 * @Description: 代理合同DTO
 **/
@Data
public class AgentContractVo extends WorkFlowAgentContract {

    @ApiModelProperty(value = "类型名称")
    private String typeName;

    @ApiModelProperty(value = "代理名称")
    private String agentName;

    @ApiModelProperty(value = "公司名称")
    private String companyName;
    @ApiModelProperty("发起人")
    private String startByName;
    @ApiModelProperty("执行人/审批人")
    private String assignee;
    @ApiModelProperty("开始时间")
    private Date startTimeDto;
    @ApiModelProperty("任务耗时")
    private String totalTime;
    @ApiModelProperty("当前步骤")
    private String actName;
    @ApiModelProperty("结束时间")
    private Date EndTimeDto;
    @ApiModelProperty("MSG")
    private String Msg;
    @ApiModelProperty("任务id")
    private String taskId;

    /**
     * 审批动作:0/1:驳回/同意
     */
    @ApiModelProperty(value = "审批动作:0/1:驳回/同意")
    private Integer approvalAction;

    /**
     * 审批动作名称：驳回/同意
     */
    @ApiModelProperty(value = "审批动作名称：驳回/同意")
    private String approvalActionName;

}
