package com.get.workflowcenter.vo;


import com.get.workflowcenter.entity.ActRuTask;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2020/11/27 11:09
 */
@Data
public class ActRuTaskVo extends ActRuTask {

    @ApiModelProperty("当前步骤名称")
    private String taskName;
    @ApiModelProperty("版本")
    private int procdefVersion;
    @ApiModelProperty("申请时间")
    private Date procdefStartDate;
    @ApiModelProperty("流程名字")
    private String procdefName;
    @ApiModelProperty("实例id")
    private String procInstId;
    @ApiModelProperty("业务表id")
    private String businessKey;
    @ApiModelProperty("流程实例id")
    private String deployId;
    @ApiModelProperty("流程key")
    private String procdefKey;
    @ApiModelProperty("任务版本")
    private Integer taskVersion;
    @ApiModelProperty("申请人名字")
    private String fkStaffName;

}
