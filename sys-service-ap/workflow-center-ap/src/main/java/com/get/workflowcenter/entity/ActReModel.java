package com.get.workflowcenter.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("ACT_RE_MODEL")
public class ActReModel implements Serializable {
    private static final long serialVersionUID = 1L;
    //    @TableField("ID_")
    @TableId(
            value = "ID_",
            type = IdType.NONE
    )
    private String id;
    @TableField("REV_")
    private Integer rev;
    @TableField("NAME_")
    private String name;
    @TableField("KEY_")
    private String key;
    @TableField("CATEGORY_")
    private String category;
    @TableField("CREATE_TIME_")
    private Date createTime;
    @TableField("LAST_UPDATE_TIME_")
    private Date lastUpdateTime;
    @TableField("VERSION_")
    private Integer version;
    @TableField("META_INFO_")
    private String metaInfo;
    @TableField("DEPLOYMENT_ID_")
    private String deploymentId;
    @TableField("EDITOR_SOURCE_VALUE_ID_")
    private String editorSourceValueId;
    @TableField("EDITOR_SOURCE_EXTRA_VALUE_ID_")
    private String editorSourceExtraValueId;
    @TableField("TENANT_ID_")
    private String tenantId;

}