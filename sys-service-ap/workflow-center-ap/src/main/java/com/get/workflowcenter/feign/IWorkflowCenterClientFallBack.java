package com.get.workflowcenter.feign;

import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.workflowcenter.vo.ActHiTaskInstVo;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.HiCommentFeignVo;
import com.get.workflowcenter.dto.ActReProcdefDto;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class IWorkflowCenterClientFallBack implements IWorkflowCenterClient {
    @Override
    public Result<List<Long>> getPersonalHistoryTasks(String procdkey) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<ActRuTaskVo> getContractTaskDataByBusinessKey(String businessKey, String procdefKey) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Integer> getSignOrGet(String taskId, Integer version) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> startContractFlow(String businessKey, String procdefKey, String companyId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> startPayFlow(String businessKey, String procdefKey, String companyId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> startBorrowFlow(String businessKey, String procdefKey, String companyId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Map<String, String>> getCurrencyNamesByNums(Set<String> fkCurrencyTypeNums) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, Integer>> getFromIdsByStaffId(Long staffId, String key) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> getContractUserSubmit(String taskId, String status) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Map<Long, ActRuTaskVo>> getActRuTaskDtosByBusinessKey(Map<String, List<Long>> businessIdsWithProcdefKey) {
        return Result.fail("获取数据失败");
    }

//    @Override
////    public Result<Map<Long, ActRuTaskVo>> getActRuTaskDtosByBusinessKey(List<Long> businessIds, String procdefKey) {
//    public Result<Map<Long, ActRuTaskVo>> getActRuTaskDtosByBusinessKey(Map<String, List<Long>> businessIdsWithProcdefKey) {
//        return Result.fail("获取数据失败");
//    }

    @Override
    public Result<Boolean> startProcess(String businessKey, String procdefKey, String companyId, Map<String, Object> map) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> startInstitutionContractFlow(String businessKey, String procdefKey, String companyId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> startStudentOfferFlow(String businessKey, String procdefKey, String companyId, String buttonType, String submitReason, Long fkCancelOfferReasonId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Object> getVariableByHisInstanceAndName(String processInstanceId, String name) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> getUserSubmit(String taskId, String status) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<ActRuTaskVo> getTaskDataByBusinessKey(String businessKey, String key) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<ActRuTaskVo> getPrepayTaskDataByBusinessKey(String businessKey, String key) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<HiCommentFeignVo> getHiComment(Long businessKey, String procdefKey) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<ActHiTaskInstVo> getActHiTaskInstDtoAndLeaveFormMessage(Long fkTableId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<ActHiTaskInstVo>> getActHiTaskInstDtosByBusinessKey(String businessKey, String procdefKey) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Set<String>> getToDoByStaffIdAndTableName(Long staffId, String key) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Set<String>> getToSignByStaffIdAndTableName(Long staffId, String key) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result doSendRemind(LeaveApplicationForm leaveApplicationForm, String title) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<ResponseBo<ActRuTaskVo>> getToDoMatter(SearchBean<ActReProcdefDto> page) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<ResponseBo<ActRuTaskVo>> getToSignedMatter(SearchBean<ActReProcdefDto> page) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<ActHiTaskInstVo> getActHiTaskInstDtoAndStudentOffer(Long fkTableId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> stopStudentOfferWorkFlow(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Long> getStartUserIdByIdAndProcdefKey(Long id, String procdefKey) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> stopExecution(String processInstId, String msg, String procdefKey, String businessKey) {
        return Result.data(false);
    }

    @Override
    public Result<Long> getAssigneeStaffId(String businessKey) {
        return Result.fail("获取数据失败");
    }
}
