package com.get.workflowcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2020/11/12 16:30
 */
@Data
public class ActReProcdefDto extends BaseVoEntity {


    private Long id;

    private Integer rev;

    @ApiModelProperty(value = "流程分类")
    private String category;

    private String name;

    @ApiModelProperty(value = "流程标识")
    private String key;

    private Integer version;

    private String deploymentId;

    private String resourceName;

    private String dgrmResourceName;

    private String description;

    private Byte hasStartFormKey;

    private Byte hasGraphicalNotation;

    private Integer suspensionState;

    private String tenantId;

    private String engineVersion;

    //自定义内容
    /**
     * 列表类型（1：通用列表/2：定制列表）
     */
    @ApiModelProperty(value = "列表类型（1：通用列表/2：定制列表）")
    private Integer mode;

    /**
     * 模型id
     */
    @ApiModelProperty(value = "模型id")
    private String modelId;

    /**
     * 部署ids
     */
    @ApiModelProperty(value = "部署ids")
    private List<String> deploymentIds;


    @ApiModelProperty("申请人名字")
    private String startStaffName;
}
