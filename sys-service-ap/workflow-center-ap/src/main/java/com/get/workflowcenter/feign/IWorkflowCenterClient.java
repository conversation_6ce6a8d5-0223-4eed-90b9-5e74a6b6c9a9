package com.get.workflowcenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.common.result.ResponseBo;
import com.get.common.result.SearchBean;
import com.get.core.tool.api.Result;
import com.get.officecenter.entity.LeaveApplicationForm;
import com.get.workflowcenter.vo.ActHiTaskInstVo;
import com.get.workflowcenter.vo.ActRuTaskVo;
import com.get.workflowcenter.vo.HiCommentFeignVo;
import com.get.workflowcenter.dto.ActReProcdefDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Feign接口类
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_WORKFLOW_CENTER
)
public interface IWorkflowCenterClient {

    String API_PREFIX = "/feign";

    /**
     * 查找他是否有历史审批和候选人
     */
    String GET_PERSONAL_HISTORY_TASKS = API_PREFIX + "/get-personal-history-tasks";
    /**
     * 查询表单所有任务task版本
     */
    String GET_CONTRACT_TASK_DATA_BY_KEY = API_PREFIX + "/get-contract-task-data-by-key";
    /**
     * 判断去待签还是代表页面，0待签，1待办
     */
    String GET_SIGN_OR_GET = API_PREFIX + "/get-sign-or-get";
    /**
     * 合同流程开始
     */
    String START_CONTRACT_FLOW = API_PREFIX + "/start-contract-flow";
    /**
     * feign调用 开启流程
     */
    String START_PAY_FLOW = API_PREFIX + "/start-pay-flow";
    /**
     * 借款流程开始
     */
    String START_BORROW_FLOW = API_PREFIX + "/start-borrow-flow";
    String GET_CURRENCY_NAMES_BY_NUMS = API_PREFIX + "/get-currency-names-by-nums";
    /**
     * feign调用 通过员工id查找对应业务表单id
     */
    String GET_FROM_IDS_BY_STAFF_ID = API_PREFIX + "/get-from-ids-by-staff-id";
    /**
     * 学校供应商审批
     */
    String GET_CONTRACT_USER_SUBMIT = API_PREFIX + "/get-contract-user-submit";
    /**
     * 根据业务id查找正在执行的任务id
     */
    String GET_ACT_RU_TASK_DTOS_BY_BUSINESS_KEY = API_PREFIX + "/get-act-ru-task-dtos-by-business-key";
    /**
     * 开启流程
     */
    String START_PROCESS = API_PREFIX + "/start-process";
    /**
     * 学校提供商流程开始
     */
    String START_INSTITUTION_CONTRACT_FLOW = API_PREFIX + "/start-institution-contract-flow";
    /**
     * 发起学生申请方案流程
     */
    String START_STUDENT_OFFER_FLOW = API_PREFIX + "/start-student-offer-flow";
    /**
     * 根据流程历史实例以及名称获取变量值
     */
    String GET_VARIABLE_BY_HIS_INSTANCE_AND_NAME = API_PREFIX + "/get-variable-by-his-instance-and-name";
    /**
     * 修改节点单独提交(重新申请或放弃)
     */
    String GET_USER_SUBMIT = API_PREFIX + "/get-user-submit";
    /**
     * 查询表单所有任务task版本
     */
    String GET_TASK_DATA_BY_BUSINESS_KEY = API_PREFIX + "/get-task-data-by-business-key";
    /**
     * 查询表单所有任务task版本
     */
    String GET_PREPAY_TASK_DATA_BY_BUSINESS_KEY = API_PREFIX + "/get-prepay-task-data-by-business-key";
    /**
     * 获取审批按钮状态
     */
    String GET_HI_COMMENT = API_PREFIX + "/get-hi-comment";
    String GET_ACT_HI_TASK_INST_DTO_AND_LEAVE_FORM_MESSAGE = API_PREFIX + "/get-act-hi-task-inst-dto-and-leave-form-message";
    String GET_ACT_HI_TASK_INST_DTOS_BY_BUSINESS_KEY = API_PREFIX + "/get-act-hi-task-inst-dtos-by-business-key";
    String GET_TO_DO_BY_STAFFID_AND_TABLE_NAME = API_PREFIX + "/get-to-do-by-staffId-and-table-name";
    String GET_TO_SIGN_BY_STAFFID_AND_TABLE_NAME = API_PREFIX + "/get-to-sign-by-staffId-and-table-name";
    String DO_SEND_REMIND = API_PREFIX + "/do-send-remind";
    String GET_TO_DO_MATTER = API_PREFIX + "/get-to-do-matter";
    String GET_TO_SIGNED_MATTER = API_PREFIX + "/get-to-signed-matter";
    String GET_ACT_HI_TASK_INST_DTO_AND_STUDENT_OFFER = API_PREFIX + "/get-act-hi-task-inst-dto-and-student-offer";
    String STOP_STUDENT_OFFER_WORK_FLOW = API_PREFIX + "/stop-student-offer-work-flow";
    String GET_START_USER_ID_BY_ID_AND_PROCDEF_KEY = API_PREFIX + "/get-start-user-id-by-id-and-procdef-key";
    String STOP_EXECUTION = API_PREFIX + "/stop-execution";
    String GET_ASSIGNEE_STAFF_ID = API_PREFIX + "/get-assignee-staff-id";

    /**
     * 查找他是否有历史审批和候选人
     * @param procdkey
     * @return
     */
    @GetMapping(GET_PERSONAL_HISTORY_TASKS)
    Result<List<Long>> getPersonalHistoryTasks(@RequestParam("procdkey") String procdkey);

    /**
     * 查询表单所有任务task版本
     * @param businessKey
     * @param procdefKey
     * @return
     */
    @GetMapping(GET_CONTRACT_TASK_DATA_BY_KEY)
    Result<ActRuTaskVo> getContractTaskDataByBusinessKey(@RequestParam("businessKey") String businessKey, @RequestParam("procdefKey") String procdefKey);

    /**
     * 判断去待签还是代表页面，0待签，1待办
     * @param taskId
     * @param version
     * @return
     */
    @GetMapping(GET_SIGN_OR_GET)
    Result<Integer> getSignOrGet(@RequestParam(value = "taskId", required = false) String taskId, @RequestParam(value = "version", required = false) Integer version);

    /**
     * 合同流程开始
     * @param businessKey
     * @param procdefKey
     * @param companyId
     * @return
     */
    @GetMapping(START_CONTRACT_FLOW)
    Result<Boolean> startContractFlow(@RequestParam("businessKey") String businessKey,
                                      @RequestParam("procdefKey") String procdefKey,
                                      @RequestParam("companyId") String companyId);

    /**
     * feign调用 开启流程
     * @param businessKey
     * @param procdefKey
     * @param companyId
     * @return
     */
    @GetMapping(START_PAY_FLOW)
    Result<Boolean> startPayFlow(@RequestParam("businessKey") String businessKey,
                                 @RequestParam("procdefKey") String procdefKey,
                                 @RequestParam("companyId") String companyId);

    /**
     * 借款流程开始
     * @param businessKey
     * @param procdefKey
     * @param companyId
     * @return
     */
    @GetMapping(START_BORROW_FLOW)
    Result<Boolean> startBorrowFlow(@RequestParam("businessKey") String businessKey,
                                    @RequestParam("procdefKey") String procdefKey,
                                    @RequestParam("companyId") String companyId);

    @PostMapping(GET_CURRENCY_NAMES_BY_NUMS)
    Result<Map<String, String>> getCurrencyNamesByNums(@RequestBody Set<String> fkCurrencyTypeNums);

    /**
     * feign调用 通过员工id查找操作过的对应业务表单id以及当前审批状态
     * @param staffId
     * @param key
     * @return
     */
    @GetMapping(GET_FROM_IDS_BY_STAFF_ID)
    Result<Map<Long, Integer>> getFromIdsByStaffId(@RequestParam("staffId") Long staffId, @RequestParam("key") String key);

    /**
     * 学校供应商审批
     * @param taskId
     * @param status
     * @return
     */
    @PostMapping(GET_CONTRACT_USER_SUBMIT)
    Result<Boolean> getContractUserSubmit(@RequestParam("taskId") String taskId, @RequestParam("status") String status);

    /**
     * 根据业务id查找正在执行的任务id
     * @param businessIdsWithProcdefKey
     * @return
     */
    @PostMapping(GET_ACT_RU_TASK_DTOS_BY_BUSINESS_KEY)
//	Result<Map<Long, ActRuTaskVo>> getActRuTaskDtosByBusinessKey(@RequestBody List<Long> businessIds, @RequestParam("procdefKey") String procdefKey);
    Result<Map<Long, ActRuTaskVo>> getActRuTaskDtosByBusinessKey(@RequestBody Map<String, List<Long>> businessIdsWithProcdefKey);

    /**
     * 开启流程
     * @param businessKey
     * @param procdefKey
     * @param companyId
     * @param map
     * @return
     */
    @PostMapping(START_PROCESS)
    Result<Boolean> startProcess(@RequestParam("businessKey") String businessKey,
                                 @RequestParam("procdefKey") String procdefKey,
                                 @RequestParam("companyId") String companyId,
                                 @RequestBody Map<String, Object> map);

    /**
     * 学校提供商流程开始
     * @param businessKey
     * @param procdefKey
     * @param companyId
     * @return
     */
    @PostMapping(START_INSTITUTION_CONTRACT_FLOW)
    Result<Boolean> startInstitutionContractFlow(@RequestParam("businessKey") String businessKey,
                                                 @RequestParam("procdefKey") String procdefKey, @RequestParam("companyId") String companyId);

    /**
     * 发起学生申请方案流程
     * @param businessKey
     * @param procdefKey
     * @param companyId
     * @param buttonType
     * @return
     */
    @PostMapping(START_STUDENT_OFFER_FLOW)
    Result<Boolean> startStudentOfferFlow(@RequestParam("businessKey") String businessKey,
                                          @RequestParam("procdefKey") String procdefKey,
                                          @RequestParam("companyId") String companyId,
                                          @RequestParam("buttonType") String buttonType,
                                          @RequestParam(value = "submitReason",required = false) String submitReason,
                                          @RequestParam(value = "fkCancelOfferReasonId",required = false) Long fkCancelOfferReasonId);

    /**
     * 根据流程历史实例以及名称获取变量值
     * @param processInstanceId
     * @param name
     * @return
     */
    @PostMapping(GET_VARIABLE_BY_HIS_INSTANCE_AND_NAME)
    Result<Object> getVariableByHisInstanceAndName(@RequestParam("processInstanceId") String processInstanceId,
                                                   @RequestParam("name") String name);

    /**
     * 修改节点单独提交(重新申请或放弃)
     * @param taskId
     * @param status
     * @return
     */
    @PostMapping(GET_USER_SUBMIT)
    Result<Boolean> getUserSubmit(@RequestParam("taskId") String taskId, @RequestParam("status") String status);

    /**
     * 查询表单所有任务task版本
     * @param businessKey
     * @param key
     * @return
     */
    @PostMapping(GET_TASK_DATA_BY_BUSINESS_KEY)
    Result<ActRuTaskVo> getTaskDataByBusinessKey(@RequestParam("businessKey") String businessKey, @RequestParam("procdefKey") String key);

    /**
     * 查询表单所有任务task版本
     * @param businessKey
     * @param key
     * @return
     */
    @PostMapping(GET_PREPAY_TASK_DATA_BY_BUSINESS_KEY)
    Result<ActRuTaskVo> getPrepayTaskDataByBusinessKey(@RequestParam("businessKey") String businessKey, @RequestParam("procdefKey") String key);

    /**
     * 获取审批按钮状态
     * @param businessKey
     * @param procdefKey
     * @return
     */
    @GetMapping(GET_HI_COMMENT)
    Result<HiCommentFeignVo> getHiComment(@RequestParam("businessKey") Long businessKey, @RequestParam("procdefKey") String procdefKey);


    @GetMapping(GET_ACT_HI_TASK_INST_DTO_AND_LEAVE_FORM_MESSAGE)
    Result<ActHiTaskInstVo> getActHiTaskInstDtoAndLeaveFormMessage(@RequestParam("fkTableId")Long fkTableId);

    @GetMapping(GET_ACT_HI_TASK_INST_DTOS_BY_BUSINESS_KEY)
    Result<List<ActHiTaskInstVo>> getActHiTaskInstDtosByBusinessKey(@RequestParam("businessKey") String businessKey, @RequestParam("procdefKey") String procdefKey);

    @GetMapping(GET_TO_DO_BY_STAFFID_AND_TABLE_NAME)
    Result<Set<String>> getToDoByStaffIdAndTableName(@RequestParam("staffId")Long staffId, @RequestParam("key")String key);

    @GetMapping(GET_TO_SIGN_BY_STAFFID_AND_TABLE_NAME)
    Result<Set<String>> getToSignByStaffIdAndTableName(@RequestParam("staffId")Long staffId, @RequestParam("key")String key);

    @PostMapping(DO_SEND_REMIND)
    Result doSendRemind(@RequestBody LeaveApplicationForm leaveApplicationForm, @RequestParam("title") String title);
//	String GET_HI_COMMENT = API_PREFIX + "/get-hi-comment";
//	@GetMapping(GET_HI_COMMENT)
//	Result<HiCommentFeignVo> getHiComment(@RequestParam("businessKey") Long businessKey, @RequestParam("procdefKey") String procdefKey) {
//		return null;
//	}

    @PostMapping(GET_TO_DO_MATTER)
    Result<ResponseBo<ActRuTaskVo>> getToDoMatter(@RequestBody SearchBean<ActReProcdefDto> page);

    @PostMapping(GET_TO_SIGNED_MATTER)
    Result<ResponseBo<ActRuTaskVo>> getToSignedMatter(@RequestBody SearchBean<ActReProcdefDto> page);

    @GetMapping(GET_ACT_HI_TASK_INST_DTO_AND_STUDENT_OFFER)
    Result<ActHiTaskInstVo> getActHiTaskInstDtoAndStudentOffer(@RequestParam("fkTableId")Long fkTableId);

    @PostMapping(STOP_STUDENT_OFFER_WORK_FLOW)
    Result<Boolean> stopStudentOfferWorkFlow(@RequestParam("id")Long id);

    @PostMapping(GET_START_USER_ID_BY_ID_AND_PROCDEF_KEY)
    Result<Long> getStartUserIdByIdAndProcdefKey(@RequestParam("id")Long id,@RequestParam("procdefKey")String procdefKey);

    @PostMapping(STOP_EXECUTION)
    Result<Boolean> stopExecution(@RequestParam("processInstId")String processInstId,@RequestParam("msg")String msg,@RequestParam("procdefKey")String procdefKey,@RequestParam("businessKey")String businessKey);

    @PostMapping(GET_ASSIGNEE_STAFF_ID)
    Result<Long> getAssigneeStaffId(@RequestParam("businessKey")String businessKey);
}
