package com.get.workflowcenter.dto;

import com.get.core.mybatis.base.BaseVoEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @author: Sea
 * @create: 2020/11/20 16:40
 * @verison: 1.0
 * @description:
 */
@Data
public class ActReModelDto extends BaseVoEntity {
    /**
     * 模型key
     */
    @ApiModelProperty(value = "模型key")
    private String key;
    /**
     * 模型name
     */
    @ApiModelProperty(value = "模型name")
    private String name;
    /**
     * 模型xml文件
     */
    @ApiModelProperty(value = "模型xml文件")
    private String jsonXml;
    /**
     * 模型流程图文件
     */
    @ApiModelProperty(value = "模型流程图文件")
    private String svgXml;
    /**
     * 模型id
     */
    @ApiModelProperty(value = "模型id")
    private String modelId;
    /**
     * 模型描述
     */
    @ApiModelProperty(value = "模型描述")
    private String description;
}
