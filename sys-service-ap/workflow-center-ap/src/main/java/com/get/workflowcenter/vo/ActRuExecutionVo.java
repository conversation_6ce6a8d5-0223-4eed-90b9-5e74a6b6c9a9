package com.get.workflowcenter.vo;

import com.get.workflowcenter.entity.ActRuExecution;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @version 1.0
 * @Author: LEO
 * @Date: 2020/11/30 14:50
 */
@Data
public class ActRuExecutionVo extends ActRuExecution {

    @ApiModelProperty("流程定义的key")
    private String procdefKey;
    @ApiModelProperty("发起人")
    private String startByName;
    @ApiModelProperty("开始时间")
    private String startTimeDto;

}
