package com.get.permissioncenter.feign;

import com.get.core.mybatis.base.BaseSelectEntity;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.permissioncenter.dto.BatchModifyConfigDto;
import com.get.permissioncenter.dto.CompanyConfigInfoDto;
import com.get.permissioncenter.dto.StaffByIdsAndCompanyIdsDto;
import com.get.permissioncenter.entity.*;
import com.get.permissioncenter.vo.*;
import com.get.permissioncenter.vo.tree.CompanyTreeVo;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestBody;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Feign失败配置
 */
@Component
@VerifyPermission(IsVerify = false)
public class IPermissionCenterClientFallBack implements IPermissionCenterClient {
    @Override
    public Result<List<Staff>> getAllStaff() {
        return null;
    }

    @Override
    public Result<List<CompanyTreeVo>> getCompanyTree(Long companyId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getHtiAndChildCompany() {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<CompanyVo> getCompanyVo() {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<StaffInfoVo> staffInfo(String userName, String password, String isAvatarLogin, String avatarLogin) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getStaffName(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> updateLoginStaffInfoByStaffId(Long staffId) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> updateAnnualLeaveBase(Long staffId, BigDecimal annualLeaveBase) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<Boolean> updateCompensatoryLeaveBase(Long staffId, BigDecimal compensatoryLeaveBase) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<Long>> getStaffIdsByNameKeyOrEnNameKey(String name) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<String, Long>> getStaffIdsByAttendanceNum(Set<String> attendanceNums,Long fkCompanyId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<String, BaseSelectEntity>> getStaffIdsByNameAndEnName(List<BaseSelectEntity> entities) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getStaffIdsByCompanyId(Long companyId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getStaffIdsByCompanyIds(List<Long> companyIds) {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getStaffLoginIdByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> deleteGuid(String guid) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<String> getCompanyNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getStaffNameMap(Set<Long> staffIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getStaffNameDepartMentMap(Set<Long> staffIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, Staff>> getStaffMapByStaffIds(Set<Long> staffIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public List<StaffVo> getStaffByIds(Set<Long> staffIds) {
        return null;
    }

    @Override
    public Result<StaffVo> getStaffById(Long staffId) {
        return null;
    }

    @Override
    public Result<List<BaseSelectEntity>> getStaffByDepartmentIds(List<Long> departmentIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<BaseSelectEntity>> getStaffByDepartmentIdsAndCountryNum(List<Long> departmentIds, String fkCountryNum) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<BaseSelectEntity>> getStaffByStaffIds(Set<Long> staffIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public List<BaseSelectEntity> getStaffByCompanyId(Long fkCompanyId) {
        return null;
    }

    @Override
    public List<Staff> getStaffs(Long fkCompanyId) {
        return null;
    }

    @Override
    public List<StaffVo> getStaffDtoByFkCompanyId(Long fkCompanyId) {
        return null;
    }

    @Override
    public List<StaffVo> getStaffDtos(Long fkCompanyId, Long fkDepartmentId, String staffNameKeyOrEnNameKey) {
        return null;
    }

    @Override
    public List<Long> getAllSubordinateIds(Long staffId){
        return null;
    }

    @Override
    public Result<List<StaffVo>> getStaffByCreateUsers(Set<String> staffIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, Long>> getCompanyIdByStaffIds(Set<Long> staffIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<CompanyTreeVo>> getAllCompanyDto() {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getCompanyNamesByIds(Set<Long> companyIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<String>> getCompanyNamesByIdsDESC(Set<Long> companyIds) {
        return Result.fail("获取数据失败");
    }
    @Override
    public Result<Map<Long, String>> getCompanyNamesByStaffIds(Set<Long> staffIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<StaffVo> getStaffByCreateUser(String createUser) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<StaffVo> getCompanyIdByStaffId(Long staffId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Map<Long, String>> getofficeNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public List<Long> getCompanyIdByName(String keyWord) {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getDepartmentNamesByIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<String>> getDepartmentNameList(String[] departmentNumList) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getDepartmentNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getAllDepartmentStaffIds(Long companyId, Long departmentId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Long> getDepartmentIdByNum(String num) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getDepartmentNumById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getPositionByNum(List<String> num) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getTopPositionStaffIds(Long companyId, Long departmentId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getAllPositionStaffIds(Long companyId, List<Long> positionIdList) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<DepartmentVo> getDepartmentById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<OfficeVo> getOfficeById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getOfficeNameById(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Long> getStaffSupervisorIdByStaffId(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Set<Long>> getStaffSupervisorIds(Set<Long> ids) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getStaffFollowerIds(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getBusinessSubordinatesIds(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<Long>> getObtainDirectSubordinatesIds(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<List<String>> getStaffAreaCountryKeysByfkStaffId(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Set<Long>> getStaffAreaCountryIdsByfkStaffId(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> getGroupGradeResourcesByResource(String resourceKey) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<MediaAndAttachedVo> getCompanyIcon(Long fkCompanyId) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<Boolean> saveResumeGuid(String guid) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<String> getConfigValueByConfigKey(String key) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<ConfigVo> getConfigByKey(String key) {
        return Result.fail("获取数据失败");
    }

    @Override
    public CompanyConfigVo getCompanyConfigInfo(String key, Integer column) {
        return null;
    }

    @Override
    public Result<ConfigVo> getConfigValueByConfigKeyAndValue(String key, Long value1) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Set<Long> getAllStaffIds() {
        return null;
    }

    @Override
    public List<Long> getStaffIdsByNameKey(String staffNameKey) {
        return null;
    }

    @Override
    public Map<Long, String> getStaffNamesByIds(Set<Long> ids) {
        return null;
    }

    @Override
    public Result<List<BatchModifyConfigVo>> getBatchUpdateItems(BatchModifyConfigDto batchModifyConfigDto) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Result<BatchModifyConfigVo> detail(Long id) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Map<Long, String> getPositionNumByIds(Set<Long> ids) {
        return null;
    }

    @Override
    public List<Long> getStaffIdsByPositionNums(Set<String> positionNums) {
        return null;
    }

    @Override
    public Map<String, List<Long>> getPositionNumAndStaffIdMap(Set<Long> ids) {
        return null;
    }

    @Override
    public Map<Long, List<Long>> getStaffSuperiorByIds(Set<Long> staffIds) {
        return null;
    }

    @Override
    public void getDayOfStaffBirthday() {

    }

    @Override
    public List<Long> getStaffListByStaffName(String staffName) {
        return null;
    }

    @Override
    public Result<Map<Long, Boolean>> getStaffIsOnDuty(Set<Long> staffIds) {
        return null;
    }

    @Override
    public Map<Long, String> getStaffEnNameByIds(Set<Long> ids) {
        return null;

    }

    @Override
    public Map<Long, String> getStaffChnNameByIds(@RequestBody Set<Long> ids){
        return null;
    }

    @Override
    public List<Long> getChildCompanyIds(Long companyId) {
        return null;
    }

    @Override
    public Result<List<BaseSelectEntity>> fuzzySearchCompanyName(String keyword, Long companyId) {
        return null;
    }

    @Override
    public Result<List<DepartmentAndStaffVo>> getDepartmentAndStaffDtoByStaffIds(Set<Long> staffIds) {
        return Result.fail("获取数据失败");
    }

    @Override
    public List<StaffVo> getStaffDtoByIds(Set<Long> staffIds){
        return null;
    }

    @Override
    public Result<Set<Long>> getStaffDepartmentsById(Long staffId) {
        return null;
    }

    @Override
    public Long addDownloadRecord(StaffDownload staffDownload) {
        return null;
    }

    @Override
    public void updateDownload(StaffDownload staffDownload) {

    }

    @Override
    public void saveStaffConfigByType(String type, List<String> keys) {

    }

    @Override
    public StaffConfig getStaffConfigByType(String type) {
        return null;
    }

    @Override
    public List<Long> getReStaffIdByKey(String key, List<Long> staffIds) {
        return Collections.emptyList();
    }

    @Override
    public List<PermissionMediaAndAttached> getMediaAndAttachedByIaeCrm(List<String> fkTableNames) {
        return null;
    }

    @Override
    public Result<StaffInfoVo> wxCpLogin(String code, String platformType) {
        return Result.fail("获取数据失败");
    }

    @Override
    public Boolean updateMediaAndAttachedById(PermissionMediaAndAttached permissionMediaAndAttached) {
        return null;
    }

    @Override
    public Result<List<StaffVo>> getStaffDtosByDepartmentNums(Long countryId, Set<String> nums) {
        return Result.fail("获取数据失败");
    }

    @Override
    public StaffVo getStaffByLoginId(String loginId) {
        return null;
    }

    @Override
    public Result<CompanyConfigValueVo> getProjectLimitConfigKey(String configKey) {
        return null;
    }

    @Override
    public Result<List<Long>> getStaffIdsByLikeCondition(String gmtCreateUser) {
        return Result.fail("获取数据失败");
    }

    @Override
    public List<Staff> getStaffByLoginIds(List<String> staffGmtCreate) {
        return null;
    }

    @Override
    public List<CompanyConfigInfoDto> getCompanySettlementConfigInfo(String configKey) {
        return null;
    }

    @Override
    public Map<Long, Integer> getCompanySettlementConfigInfoMap(String configKey) {
        return null;
    }

    @Override
    public List<Long> getStaffIdsByResourceKey(String resourceKey, Boolean isContainAdmin) {
        return null;
    }

    @Override
    public List<StaffCourseLevelTypeConfig> getStaffCourseLevelTypeConfig(Long fkStaffId,Integer type){
        return null;
    }

    @Override
    public  Result<List<StaffAreaCountry>> getStaffAreaCountryByStaffIds(List<Long> fkStaffIds){
        return null;
    }

    @Override
    public Result<Boolean> getIsStaffBusiness(Long fkStaffId) {
        return null;
    }

    @Override
    public Result<Map<String, StaffVo>> getStaffDtoMapByLoginIds(Set<String> loginIds) {
        return null;
    }

    @Override
    public Result<Map<Long, Set<Long>>> getAllStaffSuperiorByStaffIds(Set<Long> staffIds) {
        return null;
    }

    @Override
    public Result<List<String>> getPushDepartmentStaffEmail(Long fkCountryId, Long fkInstitutionId, List<String> departmentList) {
        return null;
    }

    @Override
    public Result<List<Long>> getAuthorizedStaffIdsByResourceKey(String resourceKey) {
        return Result.fail("获取数据失败");
    }

    /**
     * 根据ids和公司ids获取员工列表
     * @param staffByIdsAndCompanyIdsDto
     * @return
     */
    @Override
    public Map<Long, String> getStaffListByIdsAndCompanyIds(StaffByIdsAndCompanyIdsDto staffByIdsAndCompanyIdsDto) {
        return Collections.emptyMap();
    }

    @Override
    public Result<List<ResourceVo>> getResourceTree() {
        return null;
    }

    @Override
    public Result<Map<Long, String>> getCompanyConfigMap(String configKey, int value) {
        return null;
    }

    /**
     * 获取公司配置
     * @param configKey
     * @return
     */
    @Override
    public Result<Map<Long, CompanyConfigAnalysisVo>> getCompanyConfigAnalysis(String configKey) {
        return null;
    }
}
