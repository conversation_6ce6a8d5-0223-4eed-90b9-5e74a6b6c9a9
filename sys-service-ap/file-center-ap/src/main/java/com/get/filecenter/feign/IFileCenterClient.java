package com.get.filecenter.feign;

import com.get.common.constant.AppCenterConstant;
import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.tool.api.Result;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.vo.FileVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 文件 Feign接口类  待测试跨微服务的文件上传功能
 */
@FeignClient(
        value = AppCenterConstant.APPLICATION_FILE_CENTER
)
public interface IFileCenterClient {

    String API_PREFIX = "/feign";

    /**
     * 上传文件
     */
    String UPLOAD = API_PREFIX + "/upload";
    /**
     * 上传附件
     */
    String UPLOAD_APPENDIX = API_PREFIX + "/upload-appendix";
    /**
     * 上传私有桶文件
     */
    String UPLOAD_PRIVATE_BUCKET_FILE = API_PREFIX + "/upload-private-bucket-file";

    /**
     * 上传hti附件
     * uploadHtiPublicFile
     */
    String UPLOAD_HTI_PUBLIC_FILE = API_PREFIX + "/upload-hti-public-file";

//    String UPLOAD_APPENDIX = API_PREFIX + "/upload-appendix";
//    @PostMapping(UPLOAD_APPENDIX)
//    Result<List<FileDto>> uploadAppendix(@RequestParam("files") MultipartFile[] files, @RequestParam("type") String type);
    /**
     * 根据GUID查询
     */
    String FIND_FILE_BY_GUID = API_PREFIX + "/find-file-by-guid";
    String GET_FILE = API_PREFIX + "/get-file";

//    String FIND_FILE_BY_GUID = API_PREFIX + "/find-file-by-guid";
//    @PostMapping(FIND_FILE_BY_GUID)
//    Result<List<FileDto>> findFileByGuid(@RequestParam("guidList") @NotNull(message = "guid不能为空") List<String> guidList,
//                                         @NotBlank(message = "type不能为空") @RequestParam("type") String type);
    /**
     * 删除文件
     */
    String DELETE = API_PREFIX + "/delete";

    String DELETE_ALL = API_PREFIX + "delete-all";
    /**
     * 上传附件
     */
    String FEIGN_UPLOAD_APPENDIX = API_PREFIX + "/feignUploadAppendix";
    String GET_FILE_PATH_BY_GUIDS= API_PREFIX+"/get-file-path-by-guids";
    String GET_DOWNLOAD_FILE= API_PREFIX+"/get-download-file";
    String GET_SALE_FILE_NAME_ORC_BY_GUIDS = API_PREFIX+"/get-sale-file-name-orc-by-guids";
    String GET_SALE_FILE_KEY_BY_GUIDS = API_PREFIX+"/get-sale-file-key-by-guids";
    String UPLOAD_OBJECT_BY_XXL = API_PREFIX+"/upload-object-by-xxl";

    /**
     * 上传文件
     * @param files
     * @param type
     * @return
     */
    @PostMapping(value = UPLOAD, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<List<FileDto>> upload(@RequestPart("files") MultipartFile[] files, @RequestParam("type") String type);



    /**
     * 上传附件
     * @param files
     * @param type
     * @return
     */
    @PostMapping(value = UPLOAD_APPENDIX, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<List<FileDto>> uploadAppendix(@RequestPart("files") MultipartFile[] files, @RequestParam("type") String type);

    /**
     * 上传私有桶文件
     *
     * @param files
     * @param type
     * @param prefix
     * @return
     */
    @PostMapping(value = UPLOAD_PRIVATE_BUCKET_FILE, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<List<FileDto>> uploadPrivateBucketFile(@RequestPart("files") MultipartFile[] files, @RequestParam("type") String type, @RequestParam(value = "gmtCreateUser", required = false) String gmtCreateUser, @RequestParam(value = "prefix", required = false) String prefix);

    @PostMapping(value = UPLOAD_HTI_PUBLIC_FILE, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<List<FileDto>> uploadHtiPublicFile(@RequestPart("files") MultipartFile[] files, @RequestParam("type") String type, @RequestParam(value = "gmtCreateUser", required = false) String gmtCreateUser, @RequestParam(value = "prefix", required = false) String prefix);

    /**
     * 根据GUID查询
     * @param guidListWithType
     * @return
     */
    @PostMapping(FIND_FILE_BY_GUID)
    Result<List<FileDto>> findFileByGuid(@RequestBody Map<String, List<String>> guidListWithType);

    /**
     * 根据GUID查询
     * @param guidList
     * @param type
     * @return
     */
    @PostMapping(GET_FILE)
    Result<List<FileDto>> getFile(@RequestParam("guidList") @NotNull(message = "guid不能为空") List<String> guidList,
                                  @NotBlank(message = "type不能为空") @RequestParam("type") String type);

    /**
     * 删除文件
     * @param guid
     * @param type
     * @return
     */
    @PostMapping(DELETE)
    Result<Boolean> delete(@RequestParam("guid") String guid, @RequestParam("type") String type);


    @PostMapping(DELETE_ALL)
    void deleteAll(Set<String> guids);
    /**
     * 上传附件
     * @param files
     * @param type
     * @return
     */
    @PostMapping(value = FEIGN_UPLOAD_APPENDIX,consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    Result<List<FileDto>> feignUploadAppendix(@RequestPart("files") MultipartFile[] files, @RequestParam("type") String type);

    @PostMapping(GET_FILE_PATH_BY_GUIDS)
    @VerifyLogin(IsVerify = false)
    Result<Map<String,String>>getFilePathByGuids(@RequestBody List<String>guId);

    @PostMapping(GET_DOWNLOAD_FILE)
    Result<SaleFileDto> getDownloadFile(@RequestBody FileVo fileVo);

    @PostMapping(GET_SALE_FILE_NAME_ORC_BY_GUIDS)
    @VerifyLogin(IsVerify = false)
    Result<Map<String,String>> getSaleFileNameOrcByGuids(@RequestBody List<String> guids);

    @PostMapping(GET_SALE_FILE_KEY_BY_GUIDS)
    @VerifyLogin(IsVerify = false)
    Result<Map<String,String>> getSaleFileKeyByGuids(@RequestBody List<String> guids);

    @PostMapping(value = UPLOAD_OBJECT_BY_XXL, consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @VerifyLogin(IsVerify = false)
    Result<Boolean> uploadObjectByXxl(@RequestPart("file") MultipartFile file,@RequestParam("isPub") Boolean isPub, @RequestParam("bucketName") String bucketName, @RequestParam("ossPath") String ossPath);
}
