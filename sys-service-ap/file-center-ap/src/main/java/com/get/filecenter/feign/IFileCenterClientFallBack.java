package com.get.filecenter.feign;

import com.get.core.secure.annotation.VerifyLogin;
import com.get.core.secure.annotation.VerifyPermission;
import com.get.core.tool.api.Result;
import com.get.filecenter.dto.FileDto;
import com.get.filecenter.dto.SaleFileDto;
import com.get.filecenter.vo.FileVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Feign失败配置
 */
@Component
@Slf4j
@VerifyPermission(IsVerify = false)
public class IFileCenterClientFallBack implements IFileCenterClient {


    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<FileDto>> upload(MultipartFile[] files, String type) {
        return Result.fail("操作失败");
    }

    @Override
    @VerifyLogin(IsVerify = false)
    public Result<List<FileDto>> uploadAppendix(MultipartFile[] files, String type) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<FileDto>> uploadPrivateBucketFile(MultipartFile[] files, String type, String gmtCreateUser, String prefix) {
        return null;
    }

    @Override
    public Result<List<FileDto>> uploadHtiPublicFile(MultipartFile[] files, String type, String gmtCreateUser, String prefix) {
        return Result.fail("操作失败");
    }

    @Override
    public Result<List<FileDto>> findFileByGuid(Map<String, List<String>> guidListWithType) {
        return Result.fail("查询数据失败");
    }

    @Override
    public Result<List<FileDto>> getFile(@NotNull(message = "guid不能为空") List<String> guidList, @NotBlank(message = "type不能为空") String type) {
        return null;
    }

    @Override
    public Result<Boolean> delete(String guid, String type) {
        return Result.fail("查询数据失败");
    }

    @Override
    public void deleteAll(Set<String> guids) {
        log.error("删除失败");
    }

    @Override
    public Result<List<FileDto>> feignUploadAppendix(MultipartFile[] files, String type) {
        return Result.fail("查询数据失败");
    }

    @Override
    public Result<Map<String, String>> getFilePathByGuids(List<String> guId) {
        return null;
    }

    @Override
    public Result<SaleFileDto> getDownloadFile(FileVo fileVo) {
        return null;
    }

    @Override
    public Result<Map<String, String>> getSaleFileNameOrcByGuids(List<String> guids) {
        return null;
    }

    @Override
    public Result<Map<String, String>> getSaleFileKeyByGuids(List<String> guids) {
        return null;
    }

    @Override
    public Result<Boolean> uploadObjectByXxl(MultipartFile file, Boolean isPub, String bucketName, String ossPath) {
        return null;
    }


//    @Override
//    public Result<List<FileDto>> findFileByGuid(@NotNull(message = "guid不能为空") List<String> guidList, @NotBlank(message = "type不能为空") String type) {
//        return Result.fail("查询数据失败");
//    }
}
