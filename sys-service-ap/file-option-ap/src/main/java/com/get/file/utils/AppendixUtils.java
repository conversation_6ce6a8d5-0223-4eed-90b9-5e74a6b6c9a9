package com.get.file.utils;

import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;
import java.util.UUID;

public class AppendixUtils {
    private static String FILE_ROOT_PATH = "/appendix";

    public static String saveFile(MultipartFile file) throws IOException {
        String fileFileName = file.getOriginalFilename();
        /*String path = getPath();*/
        String path = "/data/project/get/file-center/target";
        String url = "/appendix";
        // 生成一个带日期的路径字符串，例如：/2016/01/25/
        String dateString = getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        // 文件的绝对路径，例如：C:\abc\XXX.jpg
        String filePath = path + FILE_ROOT_PATH + dateString + fileName;
        String osName = getOSName();
        // 如果是 Windows 操作系统；
        if (osName.startsWith("Windows")) {
            // 替换路径；
            filePath = filePath.replace("/", "\\");
            System.out.println(filePath);
        }
        // 判断文件路径是否存在，若不存在，创建该路径；
        File f = new File(path + FILE_ROOT_PATH + dateString);
        if (!f.isDirectory()) {
            f.mkdirs();
        }
        File destFile = new File(filePath);
        // 复制上传的图片到destFile；
        /*FileUtils.copyFile(file, destFile);*/
        file.transferTo(destFile);
        String fileurl = FILE_ROOT_PATH + dateString + fileName;
        return fileurl;
    }

    public static String getFilePath(MultipartFile file, String prefix) {
        String fileFileName = file.getOriginalFilename();
        String dateString = getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        if (prefix == null) {
            prefix = FILE_ROOT_PATH;
        }
        return prefix + dateString + fileName;
    }


    private static String getOSName() {
        //获得系统属性集
        Properties props = System.getProperties();
        String osName = props.getProperty("os.name");
        return osName;
    }


    private static String getPath() throws IOException {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        ServletContext servletContext = request.getSession().getServletContext();
        /*ServletContext servletContext = ServletActionContext.getServletContext();*/
        // RealPath: /home/<USER>/workspace/.metadata/.plugins/org.eclipse.wst.server.core/tmp0/wtpwebapps/hccx
        /* String realPath = servletContext.getRealPath("");*/
        // 参数为空
        File directory = new File("");
        String realPath = directory.getCanonicalPath();
        String path = realPath.replace("/get-platform", "");
        path = path.replace("\\get-platform", "");
        return path;
    }

    private static String getDateString() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("/yyyy/MM/dd/");
        Date date = new Date();
        String dateString = simpleDateFormat.format(date);
        return dateString;
    }

    public static String newFileName(String oldFileName) {
        // 获取最后一个"."出现的位置下标
        int index = oldFileName.lastIndexOf(".");
        // 截取最后一个"."之前的内容
        String frontStr = oldFileName.substring(0, index);
        // 获取最后一个"."之后的内容
        String behindStr = oldFileName.substring(index);
        // 重新拼接文件的名称
        String newFileName = frontStr + System.currentTimeMillis() + "_"
                + behindStr;
        return newFileName;
    }
}
