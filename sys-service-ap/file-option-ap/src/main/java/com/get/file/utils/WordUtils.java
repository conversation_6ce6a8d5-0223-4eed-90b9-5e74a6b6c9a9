//package com.get.file.utils;
//
//import com.deepoove.poi.XWPFTemplate;
//import lombok.extern.slf4j.Slf4j;
//
//import javax.servlet.http.HttpServletResponse;
//import java.io.InputStream;
//import java.net.URLEncoder;
//import java.util.Map;
//
//@Slf4j
//public class WordUtils {
//    /**
//     * Author Cream
//     * Description : //填充docx模板
//     * Date 2022/11/18 9:42
//     * Params:
//     * Return
//     */
//    public static void fillDocx(Map<String, Object> dataMap, HttpServletResponse response){
//        try(InputStream is = WordUtils.class.getClassLoader().getResourceAsStream("template/contract.docx")) {
//            //将数据对象转为HashMap 或者自己组装
//            XWPFTemplate xwpfTemplate;
//            //实际使用代码-----
//            //数据替换
//            xwpfTemplate = XWPFTemplate
//                    .compile(is)
//                    .render(dataMap);
//            response.setCharacterEncoding("utf-8");
//            response.setContentType("application/msword");
//            response.setHeader("Content-Disposition", "attachment;filename="
//                    .concat(String.valueOf(URLEncoder.encode("代理合同.docx", "UTF-8"))));
//            xwpfTemplate.write(response.getOutputStream());
//        }catch (Exception e){
//            log.info("docx填充失败");
//            e.printStackTrace();
//        }
//    }
//}
