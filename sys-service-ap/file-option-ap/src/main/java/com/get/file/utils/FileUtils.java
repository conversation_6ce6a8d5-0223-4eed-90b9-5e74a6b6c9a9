package com.get.file.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.StyleSet;
import com.get.common.result.FocExportVo;
import com.get.common.utils.CommonUtil;
import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;
import sun.net.www.protocol.http.HttpURLConnection;

import javax.net.ssl.HttpsURLConnection;
import javax.servlet.ServletContext;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URL;
import java.net.URLConnection;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @DATE: 2021/3/3
 * @TIME: 12:43
 * @Description: 文件工具类
 **/
@Slf4j
public class FileUtils {
    private static String FILE_ROOT_PATH = "/files";

    private static String FILE_HTI_ROOT_PATH = "/htipubfiles";

    public static String saveFile(MultipartFile file) throws IOException {
        String fileFileName = file.getOriginalFilename();
        String path = getPath();
        String url = "/files";
        // 生成一个带日期的路径字符串，例如：/2016/01/25/
        String dateString = getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        // 文件的绝对路径，例如：C:\abc\XXX.jpg
        String filePath = path + FILE_ROOT_PATH + dateString + fileName;
        String osName = getOSName();
        // 如果是 Windows 操作系统；
        if (osName.startsWith("Windows")) {
            // 替换路径；
            filePath = filePath.replace("/", "\\");
            System.out.println(filePath);
        }
        // 判断文件路径是否存在，若不存在，创建该路径；
        File f = new File(path + FILE_ROOT_PATH + dateString);
        if (!f.isDirectory()) {
            f.mkdirs();
        }
        File destFile = new File(filePath);
        // 复制上传的图片到destFile；
        /*FileUtils.copyFile(file, destFile);*/
        file.transferTo(destFile);
        String fileurl = FILE_ROOT_PATH + dateString + fileName;
        return fileurl;
    }


    public static String getFilePath(MultipartFile file) {
        String fileFileName = file.getOriginalFilename();
        String dateString = getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        String fileurl = FILE_ROOT_PATH + dateString + fileName;
        return fileurl;
    }

    public static String getFileHtiPath(MultipartFile file, String prefix) {
        String fileFileName = file.getOriginalFilename();
        String dateString = getDateString();
        // 生成一个随机的文件名，例如：XXX.jpg
        String fileName = UUID.randomUUID() + fileFileName.substring(fileFileName.lastIndexOf("."));
        prefix = prefix == null ? FILE_HTI_ROOT_PATH : prefix;
        String fileurl = prefix + dateString + fileName;
        return fileurl;
    }

    private static String getOSName() {
        Properties props = System.getProperties(); //获得系统属性集
        String osName = props.getProperty("os.name");
        return osName;
    }


    private static String getPath() throws IOException {
        ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        HttpServletRequest request = attributes.getRequest();
        ServletContext servletContext = request.getSession().getServletContext();
        /*ServletContext servletContext = ServletActionContext.getServletContext();*/
        // RealPath: /home/<USER>/workspace/.metadata/.plugins/org.eclipse.wst.server.core/tmp0/wtpwebapps/hccx
        /* String realPath = servletContext.getRealPath("");*/
        File directory = new File("");// 参数为空
        String realPath = directory.getCanonicalPath();
        String path = realPath.replace("/get-platform", "");
        path = path.replace("\\get-platform", "");
        return path;
    }

    private static String getDateString() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("/yyyy/MM/dd/");
        Date date = new Date();
        String dateString = simpleDateFormat.format(date);
        return dateString;
    }

    public static String newFileName(String oldFileName) {
        // 获取最后一个"."出现的位置下标
        int index = oldFileName.lastIndexOf(".");
        // 截取最后一个"."之前的内容
        String frontStr = oldFileName.substring(0, index);
        // 获取最后一个"."之后的内容
        String behindStr = oldFileName.substring(index);
        // 重新拼接文件的名称
        String newFileName = frontStr + System.currentTimeMillis() + "_"
                + behindStr;
        return newFileName;
    }

    public static void exportExcel(HttpServletResponse response, List<LinkedHashMap<String, String>> exportData, String fileName) {
        //大文件导出
        BigExcelWriter writer = setExcelStyle(fileName, exportData.get(0).size());

        // 一次性写出内容，使用默认样式，强制输出标题
        writer.write(exportData, true);
        //大文件导出
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
        }
    }

    public static void exportExcel(HttpServletResponse response, List<String> headers, List<List<String>> rows, String fileName) {
        BigExcelWriter writer = setExcelStyle(fileName, headers.size());

        // 写入表头
        writer.writeHeadRow(headers);

        // 写入数据行
        writer.write(rows);

        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
        }
    }




    public static <T> void exportExcel(HttpServletResponse response, List<T> exportData, String fileName, Class<T> beanType) {
        if (CollectionUtil.isEmpty(exportData) || StrUtil.isEmpty(fileName)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
            log.info("filename is null!!!");
        }
        BigExcelWriter writer = getExcelWhithHeaderStyle(exportData,fileName, beanType, SecureUtil.getLocale());

//        //设置字段
//        for (Map.Entry<String, String> field : fileMap.entrySet()) {
//            writer.addHeaderAlias(field.getValue(), field.getKey());
//        }
//        writer.write(exportData, true);
        //大文件导出
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {
//            throw new YServerException(LocaleMessageUtils.getMessage("file_export_fail"));
            e.printStackTrace();
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
        }
    }


    public static <T> void exportExcel(HttpServletResponse response, List<T> exportData, String fileName, Map<String, String> fileMap) {
        if (CollectionUtil.isEmpty(exportData) || StrUtil.isEmpty(fileName)) {
//            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
            log.info("filename is null!!!");
        }
//        Map<String, String> fileMap = getFileMap(beanType);
        BigExcelWriter writer = setExcelStyleNotWrapText(fileName, fileMap.size());

        //设置字段
        for (Map.Entry<String, String> field : fileMap.entrySet()) {
            writer.addHeaderAlias(field.getValue(), field.getKey());
        }
        writer.setOnlyAlias(true);
        writer.write(exportData, true);
        //大文件导出
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {
//            throw new YServerException(LocaleMessageUtils.getMessage("file_export_fail"));
            e.printStackTrace();
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
        }
    }

    /**
     * @Description: 导出excel，不自动换行
     * @Author: Jerry
     * @Date:15:56 2021/12/20
     */
    public static <T> void exportExcelNotWrapText(HttpServletResponse response, List<T> exportData, String fileName, Class<T> beanType) {
        if (GeneralTool.isEmpty(exportData) || GeneralTool.isEmpty(fileName)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Map<String, String> fileMap = getFileMap(beanType);
        BigExcelWriter writer = setExcelStyleNotWrapText(fileName, fileMap.size());

        //设置字段
        for (Map.Entry<String, String> field : fileMap.entrySet()) {
            writer.addHeaderAlias(field.getValue(), field.getKey());
        }
        writer.write(exportData, true);
        //大文件导出
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
        }
    }





    /**
     * 导出Excel（支持HTML图片标签+保持原有样式）
     */
    public static <T> void exportExcelWithImages(HttpServletResponse response,
                                                 List<T> exportData,
                                                 String fileName,
                                                 Class<T> beanType,
                                                 List<String> htmlImageFields) {
        // 1. 参数校验（保持原有校验方式）
        if (GeneralTool.isEmpty(exportData) || GeneralTool.isEmpty(fileName)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }

        // 2. 初始化Excel（复用原有样式设置）
        Map<String, String> fileMap = getFileMap(beanType);
        BigExcelWriter writer = setExcelStyleNotWrapText(fileName, fileMap.size());
        SXSSFWorkbook workbook = (SXSSFWorkbook) writer.getWorkbook();

        // 获取 sheet
        SXSSFSheet sheet = (SXSSFSheet) writer.getSheet();

        // ============= 新增1：创建居中样式 =============
        CellStyle centerStyle = workbook.createCellStyle();
        centerStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        centerStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

// ============= 新增2：创建表头样式 =============
        CellStyle headerStyle = workbook.createCellStyle();
        Font headerFont = workbook.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short)14);
        headerStyle.setFont(headerFont);
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

// 只创建表头（不创建数据行）
        Row headerRow = sheet.createRow(0);
        headerRow.setHeightInPoints(60); // 设置表头高度为60磅


        //  设置字段（保持原有逻辑）
        for (Map.Entry<String, String> field : fileMap.entrySet()) {
            writer.addHeaderAlias(field.getValue(), field.getKey());
        }

        //  仅写入表头
        writer.writeHeadRow(new ArrayList<>(fileMap.keySet()));

        // 仅写入表头（应用表头样式）
        List<String> headers = new ArrayList<>(fileMap.keySet());
        for(int i=0; i<headers.size(); i++){
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers.get(i));
            cell.setCellStyle(headerStyle); // 应用表头样式
        }


        // 6. 手动逐行写入数据（避免自动创建行）
        for (int i = 0; i < exportData.size(); i++) {
            // 创建行对象
            Row row = sheet.createRow(i + 1);
            row.setHeightInPoints(40);

            // 获取当前数据项
            T item = exportData.get(i);

            // 手动填充数据
            Map<String, Object> beanMap = BeanUtil.beanToMap(item);
            int colIndex = 0;
            for (String fieldName : fileMap.values()) {
                Object value = beanMap.get(fieldName);
                Cell cell = row.createCell(colIndex++);
                if (value != null) {
                    // 修改点：检查是否包含HTML标签
                    String strValue = value.toString();
                    if (strValue.contains("<") && strValue.contains(">")) {  // 判断是否包含HTML标签
                        // 使用removeHtmlTags方法去除HTML标签，保留纯文本
                        String cleanText = removeHtmlTags(strValue);
                        System.out.println("Original HTML: " + strValue);
                        System.out.println("Cleaned text: " + cleanText);
                        cell.setCellValue(cleanText);
                    } else {
                        cell.setCellValue(strValue);  // 不包含则正常设置
                    }
                }
                cell.setCellStyle(centerStyle); // 应用居中样式
            }

            // 设置列宽自适应内容长度
//            if (sheet instanceof SXSSFSheet) {
//                // 对于SXSSFSheet，需要先刷新到磁盘再设置自适应列宽
//                sheet.trackAllColumnsForAutoSizing();
//                for (int a = 0; a < fileMap.size(); a++) {
//                    sheet.autoSizeColumn(a);
//                }
//            }
            // 7. 处理图片（在行创建后立即处理）
//            for (String imageField : htmlImageFields) {
//                try {
//                    processImageField(workbook, sheet, fileMap, item, beanType, imageField, i);
//                } catch (Exception e) {
//                    throw new RuntimeException("处理图片字段[" + imageField + "]失败: " + e.getMessage(), e);
//                }
//            }
        }


// 4. 写入数据
//        writer.write(exportData, true);
        // 6. 输出文件（保持原有逻辑）
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            workbook.write(out);
//            writer.flush(out, true);
        } catch (IOException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
            workbook.dispose(); // 新增：清理临时文件
        }
    }


    /**
     * 处理图片字段（私有方法）
     */
    private static <T> void processImageField(SXSSFWorkbook workbook,
                                              Sheet sheet,
                                              Map<String, String> fileMap,
                                              T item,
                                              Class<T> beanType,
                                              String imageFieldName,
                                              int rowIndex) throws Exception {
        Field field = ReflectUtil.getField(beanType, imageFieldName);
        if (field == null) return;

        field.setAccessible(true);
        Object fieldValue = field.get(item);
        if (fieldValue == null) return;

        String content = String.valueOf(fieldValue);
        // 添加额外检查确保是图片内容
        if (!isImageContent(content)) {
            return; // 如果不是图片内容，直接返回
        }

        String imageUrl = extractImageUrl(content); // 直接提取URL
        // 判断是否是图片（包含<img>标签或图片URL）
        if (imageUrl != null) {
                byte[] imageBytes = downloadImage(imageUrl);
                if (imageBytes != null && imageBytes.length > 0) {
                    int colIndex = new ArrayList<>(fileMap.values()).indexOf(imageFieldName);
                    if (colIndex >= 0) {
                        insertImageToCell(workbook, sheet, imageBytes, rowIndex + 1, colIndex);
                    }
                }
        }

    }


    // 新增HTML标签去除方法
    private static String removeHtmlTags(String html) {
        if (html == null || html.trim().isEmpty()) {
            return html;
        }
        // 先替换HTML实体
        html = html.replace("&nbsp;", " ")
                .replace("&amp;", "&")
                .replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&quot;", "\"")
                .replace("&#39;", "'");

        // 先去除HTML注释
        html = html.replaceAll("<!--.*?-->", "");

        // 去除script和style标签及其内容
        html = html.replaceAll("(?i)<script[^>]*>.*?</script>", "");
        html = html.replaceAll("(?i)<style[^>]*>.*?</style>", "");

        // 去除所有HTML标签
        html = html.replaceAll("<[^>]*>", "");

        // 清理多余的空白字符
        html = html.replaceAll("\\s+", " ");

        return html.trim();
    }




    /**
     * 判断内容是否是图片（包含<img>标签或图片URL）
     */
    private static boolean isImageContent(String content) {
        if (StrUtil.isBlank(content)) {
            return false;
        }
        // 包含<img>标签
        if (content.contains("<img") && content.contains("src=")) {
            return true;
        }
        // 是图片URL
        return content.matches("https?://.*\\.(png|jpg|jpeg|gif)(\\?.*)?");
    }


    /**
     * 从HTML/字符串中提取图片URL（优化版）
     */
    private static String extractImageUrl(String content) {
        if (StrUtil.isBlank(content)) {
            return null;
        }

        // 如果是纯URL直接返回
        if (content.matches("https?://.*\\.(png|jpg|jpeg|gif)(\\?.*)?")) {
            return content;
        }

        // 从HTML中提取
        Pattern pattern = Pattern.compile("src\\s*=\\s*['\"](.*?\\.(?:png|jpg|jpeg|gif)(?:\\?.*?)?)['\"]");
        Matcher matcher = pattern.matcher(content);
        return matcher.find() ? matcher.group(1) : null;
    }


    /**
     * 下载图片（简化版）
     */
    private static byte[] downloadImage(String imageUrl) throws IOException {
        URL url = new URL(imageUrl);
        URLConnection conn = url.openConnection(); // 使用通用URLConnection接口

        // 统一设置超时
        conn.setConnectTimeout(5000);
        conn.setReadTimeout(10000);

        // 针对HTTPS的特殊处理
        if (conn instanceof HttpsURLConnection) {
            HttpsURLConnection httpsConn = (HttpsURLConnection) conn;
            // 可添加SSL证书验证相关设置（如需跳过验证见下方补充）
        }

        try (InputStream in = conn.getInputStream();
             ByteArrayOutputStream out = new ByteArrayOutputStream()) {
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = in.read(buffer)) != -1) {
                out.write(buffer, 0, bytesRead);
            }
            return out.toByteArray();
        } finally {
            if (conn instanceof HttpURLConnection) {
                ((HttpURLConnection)conn).disconnect();
            }
        }
    }


    /**
     * 插入图片到单元格（保持原有样式）
     */
    private static void insertImageToCell(SXSSFWorkbook workbook,
                                          Sheet sheet,
                                          byte[] imageBytes,
                                          int rowNum,
                                          int colNum) throws IOException {
        int pictureIdx = workbook.addPicture(imageBytes, Workbook.PICTURE_TYPE_PNG);
        Drawing<?> drawing = sheet.createDrawingPatriarch();
        ClientAnchor anchor = workbook.getCreationHelper().createClientAnchor();
        anchor.setCol1(colNum);
        anchor.setRow1(rowNum);
        anchor.setCol2(colNum + 1);
        anchor.setRow2(rowNum + 1);
        drawing.createPicture(anchor, pictureIdx);

        // 保持原有列宽设置逻辑
        sheet.setColumnWidth(colNum, 50 * 256);
        Row row = sheet.getRow(rowNum);
        if (row != null) {
            row.setHeightInPoints(120);
        }
    }




    public static <T> void exportExcelNotWrapText(HttpServletResponse response, List<T> exportData, String fileName, Map<String, String> fileMap) {
        if (GeneralTool.isEmpty(exportData) || GeneralTool.isEmpty(fileName)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        BigExcelWriter writer = setExcelStyleNotWrapText(fileName, fileMap.size());

        //设置字段
        for (Map.Entry<String, String> field : fileMap.entrySet()) {
            writer.addHeaderAlias(field.getValue(), field.getKey());
        }
        writer.setOnlyAlias(true);
        writer.write(exportData, true);
        //大文件导出
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
        }
    }

    /**
     * @Description: 导出excel，不自动换行
     * @Author: Jerry
     * @Date:15:56 2021/12/20
     */
    public static <T> void exportExcelWhithHeaderStyle(HttpServletResponse response, List<T> exportData, String fileName, Class<T> beanType) {
        if (GeneralTool.isEmpty(exportData) || GeneralTool.isEmpty(fileName)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("upload_vo_null"));
        }
        Map<String, String> fileMap = getFileMap(beanType);
        BigExcelWriter writer = setExcelStyleNotWrapText(fileName, fileMap.size());
        writer = setHeaderStyle(writer);
        //设置字段
        for (Map.Entry<String, String> field : fileMap.entrySet()) {
            writer.addHeaderAlias(field.getValue(), field.getKey());
        }
        writer.write(exportData, true);
//        writer.autoSizeColumnAll();
        //大文件导出
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
        }
    }

    /**
     * @Description: 导出excel，不自动换行
     * @Author: Jerry
     * @Date:15:56 2021/12/20
     */
    public static void exportExcelNotWrapText(HttpServletResponse response, List<LinkedHashMap<String, String>> exportData, String fileName) {
        //大文件导出
        BigExcelWriter writer = setExcelStyleNotWrapText(fileName, exportData.get(0).size());

        // 一次性写出内容，使用默认样式，强制输出标题
        writer.write(exportData, true);
        writer.autoSizeColumnAll();
        //大文件导出
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, true);
        } catch (IOException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
        }
    }

    /**
     * @return java.util.List<T>
     * @Description: 导入excel
     * @Param [file 传入的文件, beanType 转出对应的实体class]
     * <AUTHOR>
     */
    public static <T> List<T> importExcel(MultipartFile file, Class<T> beanType) throws IOException {
        try {
            InputStream inputStream = file.getInputStream();
            ExcelReader reader = ExcelUtil.getReader(inputStream);
            //获取excel数据
            List<Map<String, Object>> excelResultList = reader.readAll();
            //获取字段map
            Map<String, String> fileNameMap = getFileMap(beanType);
            List<T> resultList = new ArrayList<>(excelResultList.size());
            for (Map<String, Object> result : excelResultList) {
                //移除空key
                removeNullKey(result);
                for (Map.Entry<String, String> fileMap : fileNameMap.entrySet()) {
                    result.put(fileMap.getValue(), result.remove(fileMap.getKey()));
                }
                resultList.add(BeanUtil.mapToBean(result, beanType, true));
            }
            return resultList;
        } catch (IOException e) {
//            throw new YServerException(LocaleMessageUtils.getMessage("file_upload_fail"));
            throw new IOException();
        }
    }

    /**
     * @return cn.hutool.poi.excel.BigExcelWriter
     * @Description: 设置导出的excel的样式
     * @Param [fileName]
     * <AUTHOR>
     */
    private static BigExcelWriter setExcelStyle(String fileName, int size) {
        //大文件导出
        BigExcelWriter writer = (BigExcelWriter) ExcelUtil.getBigWriter();
        writer.renameSheet(fileName);

        Sheet sheet = writer.getSheet();
//        setSizeColumn(sheet, size);

        //样式
        StyleSet styleSet = writer.getStyleSet();
        CellStyle headCellStyle = styleSet.getHeadCellStyle();
        //字体样式
        Font font = writer.createFont();
        font.setBold(true);
        font.setFontName("微软雅黑");
        headCellStyle.setFont(font);
        headCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());

        CellStyle cellStyle = styleSet.getCellStyle();
        cellStyle.setWrapText(true);
        return writer;
    }

    /**
     * @Description: 设置导出的excel的样式，自动换行,设置列宽
     * @Author: Jerry
     * @Date:15:56 2021/12/20
     */
    public static BigExcelWriter setExcelStyleWrapText(String fileName, int size) {
        //大文件导出
        BigExcelWriter writer = (BigExcelWriter) ExcelUtil.getBigWriter();
        writer.renameSheet(fileName);

        SXSSFSheet sheet = (SXSSFSheet) writer.getSheet();
        //窗口行数不进行限制
        sheet.setRandomAccessWindowSize(-1);
        setSizeColumn(sheet, size);
        //样式
        StyleSet styleSet = writer.getStyleSet();
        CellStyle headCellStyle = styleSet.getHeadCellStyle();
        //字体样式
        Font font = writer.createFont();
        font.setBold(true);
        font.setFontName("微软雅黑");
        headCellStyle.setFont(font);
        headCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        CellStyle cellStyle = styleSet.getCellStyle();
        //自动换行
        cellStyle.setWrapText(true);
        return writer;
    }

    /**
     * @Description: 设置导出的excel的样式，不自动换行
     * @Author: Jerry
     * @Date:15:56 2021/12/20
     */
    public static BigExcelWriter setExcelStyleNotWrapText(String fileName, int size) {
        //大文件导出
        BigExcelWriter writer = (BigExcelWriter) ExcelUtil.getBigWriter();
        writer.renameSheet(fileName);

        SXSSFSheet sheet = (SXSSFSheet) writer.getSheet();
        setSizeColumn(sheet, size);
//        sheet.trackAllColumnsForAutoSizing();
        //样式
        StyleSet styleSet = writer.getStyleSet();
        CellStyle headCellStyle = styleSet.getHeadCellStyle();
        //字体样式
        Font font = writer.createFont();
        font.setBold(true);
        font.setFontName("微软雅黑");
        headCellStyle.setFont(font);
        headCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        CellStyle cellStyle = styleSet.getCellStyle();
        //不自动换行
        cellStyle.setWrapText(false);
        return writer;
    }

    private static BigExcelWriter setHeaderStyle(BigExcelWriter writer) {
        //头部样式
        StyleSet style = writer.getStyleSet();
        CellStyle headerCellStyle = style.getHeadCellStyle();
        //字体样式
        Font font = writer.createFont();
        font.setBold(true);
        font.setFontName("Arial");
        headerCellStyle.setFont(font);
        headerCellStyle.setFillForegroundColor(IndexedColors.LIGHT_GREEN.getIndex());
        return writer;
    }

    /**
     * @return void
     * @Description: 自适应宽度(中文支持)
     * @Param [sheet, size]size值为 列数-1
     * <AUTHOR>
     */
    public static void setSizeColumn(Sheet sheet, int size) {
        for (int columnNum = 0; columnNum < size; columnNum++) {
            int columnWidth = sheet.getColumnWidth(columnNum) / 256;
            for (int rowNum = 0; rowNum < sheet.getLastRowNum(); rowNum++) {
                Row currentRow;
                //当前行未被使用过
                if (sheet.getRow(rowNum) == null) {
                    currentRow = sheet.createRow(rowNum);
                } else {
                    currentRow = sheet.getRow(rowNum);
                }
                if (currentRow.getCell(columnNum) != null) {
                    Cell currentCell = currentRow.getCell(columnNum);
                    if (currentCell.getCellType() == CellType.STRING) {
                        int length = currentCell.getStringCellValue().getBytes().length;
                        if (columnWidth < length) {
                            columnWidth = length;
                        }
                    }
                }
            }
            sheet.setColumnWidth(columnNum, columnWidth * 600);
        }
    }

    public static <T> Map<String, String> getFileMap(Class<T> beanType) {
        Map<String, String> fileMap = new LinkedHashMap<>(7);
        Field[] fields = ReflectUtil.getFields(beanType);
        for (Field field : fields) {
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
            if (annotation != null) {
                fileMap.put(CommonUtil.getExportInternationalizationValue(field), field.getName());
            }
        }
        return fileMap;
    }

    public static <T> Map<String, String> getFileMapIgnoreSomeField(Class<T> beanType, Collection collection) {
        Map<String, String> fileMap = new LinkedHashMap<>(7);
        Field[] fields = ReflectUtil.getFields(beanType);
        for (Field field : fields) {
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
            if (annotation != null&&!collection.contains(field.getName())) {
                fileMap.put(annotation.value(), field.getName());
            }
        }
        return fileMap;
    }

    public static void removeNullKey(Map map) {
        Set set = map.keySet();
        for (Iterator iterator = set.iterator(); iterator.hasNext(); ) {
            Object obj = iterator.next();
            String str = (String) obj;
            if (StrUtil.isEmpty(str)) {
                iterator.remove();
            }
        }
    }

    /**
     * 添加多一个sheet
     *
     * @param exportData
     * @param beanType
     * @param writer
     * @param sheetName
     * @param <T>
     * @return
     */
    public static <T> BigExcelWriter addSheet(List<T> exportData, Class<T> beanType, BigExcelWriter writer, String sheetName) {
        writer.setSheet(sheetName);
        Map<String, String> fileMap = getFileMap(beanType);
        //设置字段
        for (Map.Entry<String, String> field : fileMap.entrySet()) {
            writer.addHeaderAlias(field.getValue(), field.getKey());
        }
        writer.write(exportData, true);
        return writer;
    }

    /**
     * 执行导出动作
     *
     * @param response
     * @param writer
     * @param fileName
     */
    public static void doExportExcel(HttpServletResponse response, BigExcelWriter writer, String fileName) {
        //大文件导出
        response.setContentType("application/vnd.ms-excel;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            writer.flush(out, true);
            out.flush();
        } catch (IOException e) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("file_export_fail"));
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
        }
    }

    public static MultipartFile getFile(BigExcelWriter writer, String fileName) {
        ByteArrayOutputStream outputStream = null;
        try {
            outputStream =  new ByteArrayOutputStream();
            writer.flush(outputStream,true);
        } finally {
            IoUtil.close(writer);
            IoUtil.close(outputStream);
        }
        byte[] bytes = outputStream.toByteArray();
        return new ConvertToMultipartFile(bytes,"newFile",fileName,"xlsx",bytes.length);
    }

    public static MultipartFile getFile(ByteArrayOutputStream outputStream, String fileName,String contentType) {
//        ByteArrayOutputStream outputStream = null;
//        try {
//            outputStream =  new ByteArrayOutputStream();
//            writer.flush(outputStream,true);
//        } finally {
//            IoUtil.close(writer);
//            IoUtil.close(outputStream);
//        }
        byte[] bytes = outputStream.toByteArray();
        return new ConvertToMultipartFile(bytes,"newFile",fileName,contentType,bytes.length);
    }

    /**
     * @Description: 导出excel，不自动换行
     * @Author: Jerry
     * @Date:15:56 2021/12/20
     */
    public static <T> BigExcelWriter getExcelWhithHeaderStyle(List<T> exportData, String fileName, Class<T> beanType,String locale) {
        if (GeneralTool.isEmpty(exportData) || GeneralTool.isEmpty(fileName)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(locale,"upload_vo_null"));
        }
        Map<String, String> fileMap = getFileMap(beanType);
        BigExcelWriter writer = setExcelStyleNotWrapText(fileName, fileMap.size());
        writer = setHeaderStyle(writer);
        //设置字段
        for (Map.Entry<String, String> field : fileMap.entrySet()) {
            writer.addHeaderAlias(field.getValue(), field.getKey());
        }
        writer.write(exportData, true);
        return writer;
    }

    public static <T> BigExcelWriter getExcelWhithHeaderStyle(List<T> exportData, List<FocExportVo> focExportVos, String fileName, String locale) {
        if (GeneralTool.isEmpty(exportData) || GeneralTool.isEmpty(fileName)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage(locale,"upload_vo_null"));
        }
        BigExcelWriter writer = setExcelStyleNotWrapText(fileName, focExportVos.size());
        setHeaderStyle(writer);
        //设置字段
        focExportVos = focExportVos.stream().filter(FocExportVo::getSelected).filter(focExportVo->!focExportVo.getSorted()).collect(Collectors.toList());
        CommonUtil.sortFoc(focExportVos);
        for (FocExportVo exportVo : focExportVos) {
            writer.addHeaderAlias(exportVo.getFiledName(),exportVo.getDescription());
        }
        writer.setOnlyAlias(true);
        writer.write(exportData, true);
        return writer;
    }


    /**
     * @Description: 导出excel，不自动换行
     * @Author: Jerry
     * @Date:15:56 2021/12/20
     */
    public static <T> void getExcelNotWrapTextStream(OutputStream out,List<T> exportData, String fileName, Class<T> beanType) {
        if (GeneralTool.isEmpty(exportData) || GeneralTool.isEmpty(fileName)) {
            throw new GetServiceException(LocaleMessageUtils.getMessage("search_result_null"));
        }
        Map<String, String> fileMap = getFileMap(beanType);
        BigExcelWriter writer = setExcelStyleNotWrapText(fileName, fileMap.size());

        //设置字段
        for (Map.Entry<String, String> field : fileMap.entrySet()) {
            writer.addHeaderAlias(field.getValue(), field.getKey());
        }
        writer.write(exportData, true);
        //大文件导出
//        response.setContentType("application/vnd.ms-excel;charset=utf-8");
//        response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");
//        ServletOutputStream out = null;
        try {
//            out = response.getOutputStream();
            writer.flush(out, true);
        } finally {
            IoUtil.close(writer);
            IoUtil.close(out);
        }
    }
}
