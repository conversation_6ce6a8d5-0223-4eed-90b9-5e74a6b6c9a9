package com.get.file.utils;

import com.get.common.utils.LocaleMessageUtils;
import com.get.core.log.exception.GetServiceException;
import lombok.extern.slf4j.Slf4j;
import net.sf.jxls.transformer.XLSTransformer;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　　　◆　　　　　　　　　　　　◆　　　　　　　　　　　　　　◆　　　　　　　　　　　　　◆◆　　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆◆◆◆　　　　　　　　　◆◆◆　　◆◆
 * 　　　　　　　　　　◆◆　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆　　　　　　　　　　◆◆　◆◆
 * 　　　◆◆　　　　　◆◆　　　　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆◆◆◆
 * 　　　◆◆◆　　　◆◆◆　　　　　　　◆◆　　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆◆
 * 　　　　◆◆◆　◆◆◆　　　　　　　　◆◆◆　◆◆◆　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　◆◆◆
 * 　　　　◆◆◆◆◆◆◆　　　　　　　　　◆◆◆◆◆　　　　　　　　　　◆◆　　　　　　　　　　　　　◆◆　　　　　　　　　　　　　　　◆◆
 * 　　　　　　◆◆◆　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆
 * 　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　　◆◆◆◆
 * <p>
 * Time: 11:45
 * Date: 2021/12/15
 * Description:
 */
@Slf4j
@Component
public class TemplateExcelUtils {

    /**
     * 根据模板导出数据
     *
     * @param fileName
     * @param sourcePath resource/template文件夹下路径
     * @param beanParams
     * @param response
     * @throws Exception
     */
    public static void downLoadExcel(String fileName, String sourcePath, Map<String, Object> beanParams, HttpServletResponse response) throws Exception {
        OutputStream os = getOutputStream(fileName, response);
        //读取模板
        InputStream is = TemplateExcelUtils.class.getClassLoader().getResourceAsStream("template/" + sourcePath);
        XLSTransformer transformer = new XLSTransformer();
        //向模板中写入内容
        Workbook workbook = transformer.transformXLS(is, beanParams);
        //写入成功后转化为输出流
        workbook.write(os);


    }

    public static void downLoadExcelZip(String fileName, String sourcePath, Map<String, Object> beanParams, ZipOutputStream zos) {
//        fileName = URLEncoder.encode(fileName , "UTF-8").replaceAll("\\+", "%20");
        try {
            //读取模板
            InputStream is = TemplateExcelUtils.class.getClassLoader().getResourceAsStream("template/" + sourcePath);
            XLSTransformer transformer = new XLSTransformer();
            //向模板中写入内容
            Workbook workbook = transformer.transformXLS(is, beanParams);

            //把excel写进压缩文件流
            byte[] bufs = new byte[1024 * 10];
            zos.putNextEntry(new ZipEntry(fileName + ".xlsx"));

            ByteArrayOutputStream tempos = new ByteArrayOutputStream();
            workbook.write(tempos);
            ByteArrayInputStream swapStream = new ByteArrayInputStream(tempos.toByteArray());
            BufferedInputStream bis = new BufferedInputStream(swapStream, 1024 * 10);
            int read;
            while ((read = bis.read(bufs, 0, 1024 * 10)) != -1) {
                zos.write(bufs, 0, read);
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("文件下载失败：" + e.getMessage());
            throw new GetServiceException(LocaleMessageUtils.getMessage("download_failed") + e.getMessage());
        }
    }

    /**
     * 导出文件时为Writer生成OutputStream.
     *
     * @param fileName 文件名
     * @param response response
     * @return ""
     */
    private static OutputStream getOutputStream(String fileName,
                                                HttpServletResponse response) throws Exception {
        try {
//            fileName = URLEncoder.encode(fileName, "UTF-8");
            fileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
//            response.setCharacterEncoding("utf8");
            response.setCharacterEncoding("utf-8");
//            response.setHeader("Content-Disposition", "attachment; filename=" + fileName + ".xlsx");
            response.setHeader("Content-Disposition", "attachment; filename*=utf-8''" + fileName + ".xlsx");
            response.setHeader("Pragma", "public");
            response.setHeader("Cache-Control", "no-store");
            response.addHeader("Cache-Control", "max-age=0");
            return response.getOutputStream();
        } catch (IOException e) {
            log.error("导出excel表格失败:" + e.getMessage(), e);
            throw new Exception("导出excel表格失败!", e);
        }
    }
}
