package com.get.file.utils;

import com.get.common.consts.CommonsConstants;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.FillPatternType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;

import java.math.BigDecimal;
import java.util.Date;


public class ExcelUtil {

    /**
     * 生成字体
     *
     * @param wb
     * @return
     */
    public static Font createFont(Workbook wb) {
        Font font = wb.createFont();
        font.setFontName(CommonsConstants.EXCEL_FONT_NAME);
        return font;
    }

    /**
     * 生成Excel表头
     *
     * @param wb
     * @param sheet
     * @param excelHeader 每一列的集合
     */
    public static void createExcelHeader(Workbook wb, Sheet sheet, String[] excelHeader) {
        CellStyle cellStyle = wb.createCellStyle();
        Font font = ExcelUtil.createFont(wb);
        font.setBold(true);
        cellStyle.setFont(font);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        Row row = sheet.createRow(0);
        for (int i = 0; i < excelHeader.length; i++) {
            createCell(cellStyle, row, i, excelHeader[i]);
            sheet.setColumnWidth(i, CommonsConstants.EXCEL_SET_COLUMN_WIDTH);
        }
    }

    /**
     * 生成单元格
     *
     * @param style
     * @param row
     * @param column
     * @param value
     */
    public static void createCell(CellStyle style, Row row, int column, Object value) {
        Cell cell = row.createCell(column);
        if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        }
        if (value instanceof Date) {
            cell.setCellValue((Date) value);
        }
        if (value instanceof String) {
            cell.setCellValue((String) value);
        }
        if (value instanceof Float) {
            cell.setCellValue((Float) value);
        }
        if (value instanceof Double) {
            cell.setCellValue((Double) value);
        }
        if (value instanceof Long) {
            cell.setCellValue((Long) value);
        }
        if (value instanceof BigDecimal) {
            cell.setCellValue(new BigDecimal(value.toString()).doubleValue());
        }
        cell.setCellStyle(style);
    }
}
