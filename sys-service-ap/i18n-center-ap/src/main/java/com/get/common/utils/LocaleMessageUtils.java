package com.get.common.utils;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.setting.dialect.Props;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.text.MessageFormat;
import java.util.ResourceBundle;

/**
 * @Description: 国际化工具类
 * <AUTHOR>
 */
@Slf4j
@Component
public class LocaleMessageUtils implements Serializable {
    private static final long serialVersionUID = -7657898714983901418L;

    /**
     * 根据传入的locale展示message
     *
     * @param locale
     * @param code
     * @return
     */
    public static String getMessage(String locale, String code) {
        return getMessage(locale, code, "");
    }

    /**
     * 该方法获取默认的message
     *
     * @param code
     * @return
     */
    public static String getMessage(String code) {
        //111111  这里是测试默认使用zh
//        SecureUtil.putStaffTranslationLocaleByStaffId(GetAuthInfo.getStaffId(),"zh");
        return getMessage(SecureUtil.getLocale(), code, "");
    }

    /**
     * @param code           ：对应messages配置的key.
     * @param defaultMessage : 没有设置key的时候的默认值.
     * @return
     */
    public static String getMessage(String locale, String code, String defaultMessage) {
        String message = defaultMessage;
        try {
            if (StringUtils.isEmpty(locale)) {
                locale = "zh";//默认使用中文
            }
            String builder = getLocaleFileName(locale);
            ResourceBundle rb = ResourceBundle.getBundle(builder);
            message = new String(rb.getString(code).getBytes("ISO-8859-1"), "UTF8");
        } catch (Exception e) {
//            e.printStackTrace();
            //如找不到则默认为空
            log.error("找不到I18N配置属性code:" + code);
        }
        return message;
    }

    private static String getLocaleFileName(String locale) {
        StringBuilder builder = new StringBuilder();
        builder.append("i18n/messages");
        if (StringUtils.isNotEmpty(locale)) {
            builder.append("_");
            builder.append(locale);
        }
//        builder.append(".properties");
        return builder.toString();
    }

//    public static Optional<List<String>> getNamesFromDB() {
//        boolean hasName = true;
//        if (hasName) {
//            String [] names = {"沉默王二", "一枚有趣的程序员", "牛逼牛逼"};
//            return Optional.of(Arrays.asList(names));
//        }
//        return Optional.empty();
//    }
//
//    public static void main(String[] args) {
////        System.out.println(getNamesFromDB());
//        System.out.println(getMessage("en","id_null"));
//    }

    /**
     * 占位符
     *
     * @Date 16:34 2021/12/2
     * <AUTHOR>
     */
    public static String getFormatMessage(String code, Object... arguments) {
        return getMessageFormat(code, "", arguments);
    }

    /**
     * @param arguments      占位符
     * @param code           ：对应messages配置的key.
     * @param defaultMessage : 没有设置key的时候的默认值.
     * @return
     */
    public static String getMessageFormat(String code, String defaultMessage, Object... arguments) {
        //获取选中的语言
        String locale = SecureUtil.getLocale();
        String builder = getLocaleFileName(locale);
        builder = builder + ".properties";
        Props props = new Props(builder, CharsetUtil.CHARSET_UTF_8);
        String translateMsg = props.getProperty(code);
        return GeneralTool.isNotEmpty(translateMsg) ? MessageFormat.format(translateMsg, arguments) : defaultMessage;
    }


}
