package com.get.common.eunms;

import com.get.common.utils.LocaleMessageUtils;
import com.get.core.tool.utils.GeneralTool;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

public enum ClientEnum {

    /**
     * 跟进状态 -》 新建 => 跟进中 =》 已签约 =》 长期跟进
     */
    NEW(0,"新建"),

    FOLLOW_UP(1,"跟进中"),

    SIGNED(2, "已签约"),

    LT_FOLLOW_UP(3,"长期跟进")
    ;

    /**
     * 跟进状态列表
     */
    public static final ClientEnum[] FUS = {NEW,FOLLOW_UP,SIGNED,LT_FOLLOW_UP};

    public Integer key;

    public String val;


    ClientEnum(Integer key, String val) {
        this.key = key;
        this.val = val;
    }

    public static String getValue(Integer key) {
        ClientEnum[] values = values();
        for (ClientEnum value : values) {
            if (value.key.equals(key)) {
                return value.val;
            }
        }
        return null;
    }

    public static List<Map<String,Object>> enumsTranslation2Arrays(ClientEnum[] clientEnums){
        if (clientEnums == null || clientEnums.length == 0) {
            return Collections.emptyList();
        }
        List<Map<String,Object>> result = new ArrayList<>();
        for (ClientEnum anEnum : clientEnums) {
            Map<String,Object> map = new HashMap<>(1);
            map.put("key",anEnum.key);
            map.put("value",anEnum.val);
            result.add(map);
        }
        return result;
    }
    public static String getValueByKey(Integer key, ClientEnum[] enums) {
        List<ClientEnum> list = Arrays.stream(enums).filter(e -> e.key.equals(key)).collect(Collectors.toList());
        return GeneralTool.isNotEmpty(list) ? list.get(0).val: "";
    }

}
