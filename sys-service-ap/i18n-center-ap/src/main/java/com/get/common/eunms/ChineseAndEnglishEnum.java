package com.get.common.eunms;

/**
 * <AUTHOR>
 * @DATE: 2021/2/22
 * @TIME: 17:50
 * @Description:
 **/
public enum ChineseAndEnglishEnum {
    //异常中文
    PARAMETER_EMPTY_CHINESE("parameter_empty_chinese", "参数为空"),
    PARAMETER_ERROR_CHINESE("parameter_error_chinese", "参数错误"),
    VALIDATION_ERROR_CHINESE("validation_error_chinese", "验证错误"),
    SERVER_ERROR_CHINESE("server_error_chinese", "服务器错误"),
    DELETE_ERROR_CHINESE("delete_error_chinese", "有关联数据，不能删除"),
    ACCOUNT_NOT_ACTIVATED_CHINESE("account_not_activated_chinese", "账户未激活"),
    DUPLICATE_ATTRIBUTE_CHINESE("duplicate_attribute_chinese", "属性唯一,不允许重复"),
    OLD_PASSWORD_ERROR_CHINESE(" old_password_error_chinese", "旧密码错误"),
    USER_NOT_LOGGED_IN_CHINESE("user_not_logged_in_chinese", "用户未登陆"),
    NO_PERMISSION_CHINESE("no_permission_chinese", "没有权限查看此条数据"),

    //异常英文
    PARAMETER_EMPTY_ENGLISH("parameter_empty_english", "The parameter is empty"),
    PARAMETER_ERROR_ENGLISH("parameter_error_english", "Parameter error"),
    VALIDATION_ERROR_ENGLISH("validation_error_english", "Validation error"),
    SERVER_ERROR_ENGLISH("server_error_english", "Server error"),
    DELETE_ERROR_ENGLISH("delete_error_english", "Associated data cannot be deleted"),
    ACCOUNT_NOT_ACTIVATED_ENGLISH("account_not_activated_english", "Account not activated"),
    DUPLICATE_ATTRIBUTE_ENGLISH("duplicate_attribute_english", "Property is unique and cannot be duplicated"),
    OLD_PASSWORD_ERROR_ENGLISH(" old_password_error_english", "Old password error"),
    USER_NOT_LOGGED_IN_ENGLISH("user_not_logged_in_english", "User not logged in"),
    NO_PERMISSION_ENGLISH("no_permission_english", "You don't have permission to view this data"),


    ZH_CN("zh","zh-cn"),
    ZH_EN("en","en-us")
    ;


    public String key;
    public String value;


    ChineseAndEnglishEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }


    public static String getValue(String key) {
        ChineseAndEnglishEnum[] ChineseAndEnglishEnum = values();
        for (com.get.common.eunms.ChineseAndEnglishEnum projectKeyEnum : ChineseAndEnglishEnum) {
            if (projectKeyEnum.key.equals(key)) {
                return projectKeyEnum.value();
            }
        }
        return null;
    }

    public static String getKey(String value) {
        ChineseAndEnglishEnum[] projectKeyEnums = values();
        for (ChineseAndEnglishEnum projectKeyEnum : projectKeyEnums) {
            if (projectKeyEnum.value().equals(value)) {
                return projectKeyEnum.key();
            }
        }
        return null;
    }

    private String key() {
        return this.key;
    }

    private String value() {
        return this.value;
    }

}
