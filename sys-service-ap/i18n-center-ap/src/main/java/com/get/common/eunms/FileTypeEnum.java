package com.get.common.eunms;

import com.get.common.utils.LocaleMessageUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 16:27
 * @Description: 文件类型枚举
 **/
public enum FileTypeEnum {
    /**
     * 权限中心文件中文
     */
    PERMISSION_CONTRACT_FILE("permission_contract_file", "员工合同资料"),
    PERMISSION_HREVENT_FILE("permission_hrevent_file", "员工人事事件资料"),
    PERMISSION_STAFF_FILE("permission_staff_file", "员工资料"),
    PERMISSION_HEAD_ICON("permission_head_icon", "员工头像"),
    PERMISSION_COMPANY_ICON("permission_company_icon", "公司图标"),
    /**
     * 学校文件中文
     */
    INSTITUTION_LOGO("institution_logo", "学校logo"),
    INSTITUTION_PIC("institution_pic", "学校环境图"),
    INSTITUTION_MOV("institution_mov", "学校介绍视频"),
    INSTITUTION_MSO_PDF("institution_mso_pdf", "MSO-PDF"),
    INSTITUTION_COVER("institution_cover", "学校封面图"),
    INSTITUTION_MAP_GG("institution_map_gg", "google地图"),
    INSTITUTION_MAP_BD("institution_map_bd", "百度地图"),
    INSTITUTION_PROVIDER_FILE("institution_provider_file", "学校提供商资料"),

    INSTITUTION_CONTRACT_FILE("institution_contract_file", "学校合同资料"),
    //国家文件-国徽中文
    INSTITUTION_COUNTRY_EMBLEM_PIC("institution_country_emblem_pic", "国徽"),
    //国家文件-国旗中文
    INSTITUTION_COUNTRY_FLAG_PIC("institution_country_flag_pic", "国旗"),
    //国家文件-附件中文
    INSTITUTION_COUNTRY_MSO_BANNER("institution_country_mso_banner", "MSO-Banner"),
    //城市文件中文
    INSTITUTION_CITY_PIC("institution_city_pic", "城市配图"),
    //学院文件中文
    INSTITUTION_FACULTY_PIC("institution_faculty_pic", "学院环境图"),
    INSTITUTION_FACULTY_MOV("institution_faculty_mov", "学院介绍视频"),
    //校区文件中文
    INSTITUTION_ZONE_PIC("institution_zone_pic", "校区环境图"),
    INSTITUTION_ZONE_MOV("institution_zone_mov", "校区介绍视频"),
    //专业文件中文
    INSTITUTION_MAJOR_PIC("institution_major_pic", "专业图"),
    INSTITUTION_MAJOR_MOV("institution_major_mov", "专业介绍视频"),
    //课程文件中文
    INSTITUTION_COURSE_PIC("institution_course_pic", "课程图"),
    INSTITUTION_COURSE_MOV("institution_course_mov", "课程介绍视频"),
    INSTITUTION_COURSE_FILE("institution_course_file", "课程资料"),
    //知名校友文件中文
    INSTITUTION_ALUMNUS_PIC("institution_alumnus_pic", "知名校友图"),
    INSTITUTION_ALUMNUS_MOV("institution_alumnus_mov", "知名校友介绍视频"),
    INSTITUTION_ALUMNUS_FILE("institution_alumnus_file", "知名校友资料"),
    //资讯文件中文
    INSTITUTION_INFO_PIC("institution_info_pic", "资讯图"),
    INSTITUTION_INFO_MOV("institution_info_mov", "资讯介绍视频"),
    INSTITUTION_INFO_FILE("institution_info_file", "资讯附件"),
    INSTITUTION_INFO_COVER("institution_info_cover", "资讯封面图"),
    //新闻文件中文
    INSTITUTION_NEWS_FILE("institution_news_file", "新闻文件"),
    INSTITUTION_NEWS_TITLE_PIC("institution_news_title_pic", "新闻标题图片"),
    INSTITUTION_NEWS_COVER("institution_news_cover", "新闻封面图"),
    INSTITUTION_NEWS_APPENDIX("institution_news_appendix", "新闻附件"),
    INSTITUTION_NEWS_MAIL_APPENDIX("institution_news_mail_appendix", "邮件附件"),
    //国家资讯文件中文
    INSTITUTION_COUNTRY_INFO_DESCRIPTION("institution_country_info_description,", "国家资讯富文本"),
    INSTITUTION_COUNTRY_INFO_PIC("institution_country_info_pic", "国家资讯配图"),
    INSTITUTION_COUNTRY_INFO_COVER("institution_country_info_cover", "国家资讯封面图"),
    //城市资讯文件中文
    INSTITUTION_CITY_INFO_DESCRIPTION("institution_city_info_description,", "城市资讯富文本"),
    INSTITUTION_CITY_INFO_PIC("institution_city_info_pic", "城市资讯配图"),
    INSTITUTION_CITY_INFO_COVER("institution_city_info_cover", "城市资讯封面图"),

    /**
     * 人才中心文件中文
     */
    RESUME_ICON("resume_icon", "简历头像"),
    RESUME_FILE("resume_file", "简历资料"),
    /**
     * 财务中心文件中文
     */
    FINANCE_INVOICE_FILE("finance_invoice_file", "发票资料"),
    FINANCE_PROVIDER_FILE("finance_provider_file", "供应商资料"),
    FINANCE_RECEIPT_FILE("finance_receipt_file", "收款单资料"),
    FINANCE_PAYMENT_FILE("finance_payment_file", "付款单资料"),
    FINANCE_RECEIPT_ITEM_FILE("finance_receipt_item_file", "应收计划资料"),
    FINANCE_PAYMENT_ITEM_FILE("finance_payment_item_file", "应付计划资料"),
    FINANCE_EXPENSE_CLAIM_FORM_FILE("finance_expense_claim_form_file", "费用报销单凭证票据"),
    FINANCE_PREPAY_APPLICATION_FORM_FILE("finance_prepay_application_form_file", "借款申请单凭证票据"),
    FINANCE_TRAVEL_CLAIM_FORM_FILE("finance_travel_claim_form_file", "差旅报销单凭证票据"),
    FINANCE_PAYMENT_APPLICATION_FORM_FILE("finance_payment_application_form_file", "支付申请单凭证票据"),

    /**
     * 办公中心文件中文
     */
    OFFICE_LEAVE_APPLICATION_FORM_FILE("office_leave_application_form_file", "工休单凭据"),
    OFFICE_TASK_ITEM_EXPORT ("office_task_item_export", "子任务列表导出"),

    /**
     * 销售中心文件中文
     */
    SALE_CONVENTION_DESCRIPTION("sale_convention_description", "峰会富文本"),
    SALE_CONVENTION_PROCEDURE_DESCRIPTION("sale_convention_procedure_description", "峰会流程富文本"),

    SALE_AGENT_FILE("sale_agent_file", "代理资料"),
    SALE_APP_AGENT_CHANGE_FILE("sale_app_agent_change_file", "信息变更"),
    SALE_CONTRACT_FILE("sale_contract_file", "代理合同资料"),
    SALE_CONTRACT_SEAL_FILE("sale_contract_seal_file", "公司盖章合同"),
    BUSINESS_LICENSE("agent_business_license","代理营业执照"),
    SALE_AGENT_ID_CARD_FRONT("sale_agent_id_card_front","身份证国微面"),
    SALE_AGENT_ID_CARD_BACK("sale_agent_id_card_back","身份证人像面"),
    SALE_STUDENT_FILE("sale_student_file", "学生资料"),
    SALE_APP_STUDENT_FILE("m_app_student_file", "partner学生资料"),
    SALE_STUDENT_FILE_FROM_REC("sale_student_file_from_rec", "识别资料"),

    SALE_CLIENT_FILE("sale_client_file", "客户资料"),
    SALE_OFFER_ITEM_FILE("sale_offer_item_file", "学习计划资料"),
    SALE_PAYABLE_FILE("sale_payable_file", "应付计划资料"),
    SALE_RECEIVABLE_FILE("sale_receivable_file", "应收计划资料"),
    SALE_EVENT_BILL_FILE("sale_event_bill_file", "活动汇总费用资料"),
    SALE_BUSINESS_PROVIDER_AGREEMENT("sale_business_provider_agreement","业务提供商合作协议"),
    SALE_BUSINESS_PROVIDER_AGREEMENT_FILE("sale_business_provider_agreement_file", "附件文件"),
    SALE_EVENT_INCENTIVE_FILE("sale_event_incentive_file", "奖励推广活动资料"),
    CONVENTION_GOPRO_NUCLEIC_ACID_FILE("convention_gopro_nucleic_acid_file", "GoPro核酸资料"),

    INCENTIVE_POLICY_FILE("incentive_policy_file", "奖励政策资料"),

    APP_AGENT_CONTRACT_ACCOUNT_STATEMENT_FILE("app_agent_contract_account_statement_file","声明文件"),
    APP_AGENT_BUSINESS_LICENSE("app_agent_business_license","营业执照"),
    APP_AGENT_ID_CARD("app_agent_id_card","个人身份证"),

    COMMENT_COMMENT("comment_comment", "评论文件"),
    AWARD_PIC("award_pic", "奖品图片"),
    VOTING_LIKE_PIC("voting_like_pic", "峰会点赞截图"),
    //活动附件中文
    SALE_EVENT_FILE("sale_event_file", "活动资料"),

    // 留学服务费附件类型
    M_STUDENT_SERVICE_FEE_INVOICE("m_student_service_fee_invoice", "invoice"),
    M_STUDENT_SERVICE_FEE_MEDIA("m_student_service_fee_media", "留学服务费附件"),

    // 留学服务费成本支出附件类型
    M_STUDENT_SERVICE_FEE_COST_INVOICE("m_student_service_fee_cost_invoice", "成本支出发票"),

    /**
     * 工作流中心文件中文
     */
    WORKFLOW_PAY_FILE("workflow_pay_file", "支付申请单文件"),

    /**
     * 平台配置中心文件中文
     */
    PLATFORM_MSO_COLUMN_PIC("platform_mso_column_pic", "MSO平台栏目图片"),
    PLATFORM_MSO_AGENT_TOP_LOGO("platform_mso_agent_top_logo", "MSO平台代理顶部Logo"),
    PLATFORM_MSO_AGENT_BOTTOM_LOGO("platform_mso_agent_bottom_logo", "MSO平台代理底部Logo"),

    PLATFORM_ISSUE_ATTACHMENT_TYPE_TEMPLATE("student_attachment_template", "ISSUE文件类型文件模板"),

    PLATFORM_MSO_AGENT_SITEMAP_COLUMN_PIC("platform_mso_agent_sitemap_column_pic", "MSO平台代理SITEMAP栏目图片"),
    PLATFORM_MSO_AGENT_SITEMAP_PAGE_TEMPLATE_PIC("platform_mso_agent_sitemap_page_template_pic", "MSO平台代理SITEMAP页面模板图片"),

    PLATFORM_MSO_AGENT_NEWS_BANNER("platform_mso_agent_news_banner", "MSO平台代理资讯新闻Banner"),
    PLATFORM_MSO_AGENT_MOBILE_NEWS_BANNER("platform_mso_agent_mobile_news_banner", "MSO平台移动端代理资讯新闻Banner"),
    PLATFORM_MSO_AGENT_PAD_NEWS_BANNER("platform_mso_agent_pad_news_banner", "MSO平台平板端代理资讯新闻Banner"),

    PLATFORM_MSO_AGENT_DSE_BANNER("platform_mso_agent_dse_banner", "MSO平台代理DSE模块Banner"),
    PLATFORM_MSO_AGENT_MOBILE_DSE_BANNER("platform_mso_agent_mobile_dse_banner", "MSO平台代理移动端DSE模块Banner"),
    PLATFORM_MSO_AGENT_PAD_DSE_BANNER("platform_mso_agent_pad_dse_banner", "MSO平台代理平板端DSE模块Banner"),

    PLATFORM_MSO_AGENT_DSE_ADVERTS_BANNER("platform_mso_agent_dse_adverts_banner", "MSO平台代理DSE模块广告Banner"),
    PLATFORM_MSO_AGENT_MOBILE_DSE_ADVERTS_BANNER("platform_mso_agent_mobile_dse_adverts_banner", "MSO平台代理移动端DSE模块广告Banner"),
    PLATFORM_MSO_AGENT_PAD_DSE_ADVERTS_BANNER("platform_mso_agent_pad_dse_adverts_banner", "MSO平台代理平板端端DSE模块广告Banner"),

    PLATFORM_MSO_AGENT_INDEX_BANNER("platform_mso_agent_index_banner", "MSO平台代理首页Banner"),
    PLATFORM_MSO_AGENT_MOBILE_INDEX_BANNER("platform_mso_agent_mobile_index_banner", "MSO平台代理移动端首页Banner"),
    PLATFORM_MSO_AGENT_PAD_INDEX_BANNER("platform_mso_agent_pad_index_banner", "MSO平台代理移动端平板首页Banner"),

    /**
     * 帮助中心文件中心
     */
    HELP_FILE("help_file", "帮助信息资料"),
    /**
     * 考试中心
     */
    EXAM_SCORE_TITLE_PIC("exam_score_title", "考试中心称号图片"),
    //留学住宿文件
    STUDENT_ACCOMMODATION_PIC("student_accommodation_pic", "留学住宿图"),
    STUDENT_ACCOMMODATION_MOV("student_accommodation_mov", "留学住宿介绍视频"),
    STUDENT_ACCOMMODATION_FILE("student_accommodation_file", "留学住宿资料"),

    //留学保险文件
    STUDENT_INSURANCE_PIC("student_insurance_pic", "留学保险图"),
    STUDENT_INSURANCE_MOV("student_insurance_mov", "留学保险介绍视频"),
    STUDENT_INSURANCE_FILE("student_insurance_file", "留学保险资料"),


    COMPETITION_ITEM_LOGO("competition_item_logo_file", "竞赛项目Logo"),
    COMPETITION_ITEM_BANNER("competition_item_banner_file", "竞赛项目横幅"),
    COMPETITION_ITEM_JUDGER_IMAGE_FILE("competition_item_judger_image_file", "竞赛项目横幅"),


    /**
     * 文件操作
     */
    D_LIST_EXPORT("d_list_export","列表导出"),


    /**
     * 状态
     */
    D_FAILURE("0","失败"),

    D_PROCESSING("1","进行中"),

    D_FINISHED("2","完成"),

    /**
     * 投票中心
     */
    VOTING_ITEM_OPTION_PIC("voting_item_option_pic", "投票选项图片"),


    //投票中心投票项管理图片
    VOTING_ITEM_PIC("voting_item_pic", "投票项管理图片"),

    TEMPLATE_PIC("template_pic","简历模板图片"),

    SG_TRANSCRIPT_PIC("sg_transcript","SG简历成绩单"),
    SG_USER_AVATAR("sg_user_avatar","SG普通用户头像"),
    SG_REPORT_PIC("sg_report_pic","SG简历活动单"),
    SG_PERSONAL_ALBUM("sg_personal_album","SG个人相册/个人相册标志" );

    /**
     * 学校文件组中文
     */
    public static final FileTypeEnum[] SCHOOLGATE = new FileTypeEnum[]{TEMPLATE_PIC,SG_TRANSCRIPT_PIC,SG_PERSONAL_ALBUM};

    /**
     * 学校文件组中文
     */
    public static final FileTypeEnum[] INSTITUTION = new FileTypeEnum[]{INSTITUTION_PIC, INSTITUTION_MOV, INSTITUTION_MSO_PDF, INSTITUTION_COVER, INSTITUTION_MAP_GG,
            INSTITUTION_MAP_BD};

    /**
     * 操作方式
     */
    public static final FileTypeEnum[] OPERATION = {D_LIST_EXPORT};

    /**
     * 文件状态
     */
    public static final FileTypeEnum[] STATUS = {D_FAILURE,D_PROCESSING,D_FINISHED};
    /**
     * 学校文件组中文
     */
    public static final FileTypeEnum[] INSTITUTIONPROVIDER = new FileTypeEnum[]{INSTITUTION_PROVIDER_FILE};
    /**
     * 学校文件组中文
     */
    public static final FileTypeEnum[] INSTITUTIONCONTRACT = new FileTypeEnum[]{INSTITUTION_CONTRACT_FILE};
    /**
     * 学院文件组中文
     */
    public static final FileTypeEnum[] INSTITUTIONFACULTY = new FileTypeEnum[]{INSTITUTION_FACULTY_PIC, INSTITUTION_FACULTY_MOV};
    /**
     * 专业文件组中文
     */
    public static final FileTypeEnum[] INSTITUTIONMAJOR = new FileTypeEnum[]{INSTITUTION_MAJOR_PIC, INSTITUTION_MAJOR_MOV};
    /**
     * 课程文件组中文
     */
    public static final FileTypeEnum[] INSTITUTIONCOURSE = new FileTypeEnum[]{INSTITUTION_COURSE_PIC, INSTITUTION_COURSE_MOV, INSTITUTION_COURSE_FILE};
    /**
     * 知名校友文件组中文
     */
    public static final FileTypeEnum[] INSTITUTIONALUMNUS = new FileTypeEnum[]{INSTITUTION_ALUMNUS_PIC, INSTITUTION_ALUMNUS_MOV, INSTITUTION_ALUMNUS_FILE};
    /**
     * 资讯文件组中文
     */
    public static final FileTypeEnum[] INSTITUTIONINFO = new FileTypeEnum[]{INSTITUTION_INFO_PIC, INSTITUTION_COURSE_MOV, INSTITUTION_INFO_FILE, INSTITUTION_INFO_COVER};
    /**
     * 新闻文件组中文
     */
    public static final FileTypeEnum[] NEWS = new FileTypeEnum[]{INSTITUTION_NEWS_FILE, INSTITUTION_NEWS_TITLE_PIC, INSTITUTION_NEWS_COVER, INSTITUTION_NEWS_APPENDIX,INSTITUTION_NEWS_MAIL_APPENDIX};
    /**
     * 权限中心组中文
     */
    public static final FileTypeEnum[] PERMISSIONSFATT = new FileTypeEnum[]{PERMISSION_STAFF_FILE};
    /**
     * 权限中心组中文
     */
    public static final FileTypeEnum[] PERMISSIONHREVENT = new FileTypeEnum[]{PERMISSION_HREVENT_FILE};
    /**
     * 权限中心组中文
     */
    public static final FileTypeEnum[] PERMISSIONCONTRACT = new FileTypeEnum[]{PERMISSION_CONTRACT_FILE};
    /**
     * 销售代理组中文
     */
    public static final FileTypeEnum[] SALEAGENT = new FileTypeEnum[]{SALE_AGENT_FILE};
    /**
     * 销售合同组中文
     */
    public static final FileTypeEnum[] SALECONTRACT = new FileTypeEnum[]{SALE_CONTRACT_FILE,SALE_CONTRACT_SEAL_FILE};
    /**
     * 国家文件组中文
     */
    public static final FileTypeEnum[] COUNTRY = new FileTypeEnum[]{INSTITUTION_COUNTRY_EMBLEM_PIC, INSTITUTION_COUNTRY_FLAG_PIC, INSTITUTION_COUNTRY_MSO_BANNER};
    /**
     * 峰会文件组中文
     */
    public static final FileTypeEnum[] CONVENTION = new FileTypeEnum[]{SALE_CONVENTION_DESCRIPTION};
    /**
     * 峰会流程文件组中文
     */
    public static final FileTypeEnum[] CONVENTIONPROCEDURE = new FileTypeEnum[]{SALE_CONVENTION_PROCEDURE_DESCRIPTION};
    /**
     * 评论文件组中文
     */
    public static final FileTypeEnum[] COMMENT = new FileTypeEnum[]{COMMENT_COMMENT};
    /**
     * 活动文件组中文
     */
    public static final FileTypeEnum[] EVENT = new FileTypeEnum[]{SALE_EVENT_FILE};
    /**
     * 留学住宿文件组
     */
    public static final FileTypeEnum[] STUDENTACCOMMODATION = new FileTypeEnum[]{STUDENT_ACCOMMODATION_PIC, STUDENT_ACCOMMODATION_MOV, STUDENT_ACCOMMODATION_FILE};
    /**
     * 留学保险文件组
     */
    public static final FileTypeEnum[] STUDENTINSURANCE = new FileTypeEnum[]{STUDENT_INSURANCE_PIC,
            STUDENT_INSURANCE_MOV,
            STUDENT_INSURANCE_FILE};


    public static final FileTypeEnum[] COMPETITION_ITEM_MEDIA_TYPE = new FileTypeEnum[]{COMPETITION_ITEM_LOGO,COMPETITION_ITEM_BANNER};
    /**
     * 帮助中心中文
     */
    public static final FileTypeEnum[] HELP = new FileTypeEnum[]{HELP_FILE};
    /**
     * 留学服务费附件类型
     */
    public static final FileTypeEnum[] M_STUDENT_SERVICE_FEE_MEDIA_TYPE = new FileTypeEnum[]{M_STUDENT_SERVICE_FEE_INVOICE, M_STUDENT_SERVICE_FEE_MEDIA};
    /**
     * 留学服务费成本支出附件类型
     */
    public static final FileTypeEnum[] M_STUDENT_SERVICE_FEE_COST_MEDIA_TYPE = new FileTypeEnum[]{M_STUDENT_SERVICE_FEE_COST_INVOICE};

    public String key;
    public String value;
    FileTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValue(String key) {
        FileTypeEnum[] fileTypeEnums = values();
        for (FileTypeEnum fileTypeEnum : fileTypeEnums) {
            if (fileTypeEnum.key.equals(key)) {
                return LocaleMessageUtils.getMessage(fileTypeEnum.key());
            }
        }
        return null;
    }

    public static String getKey(String value) {
        FileTypeEnum[] fileTypeEnums = values();
        for (FileTypeEnum fileTypeEnum : fileTypeEnums) {
            if (fileTypeEnum.value().equals(value)) {
                return fileTypeEnum.key();
            }
        }
        return null;
    }

    /**
     * 将枚举转换为list
     *
     * @param enums
     * @return
     */
    public static List<Map<String, Object>> enums2Arrays(FileTypeEnum[] enums) {
        if (enums == null) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Arrays.stream(enums).forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("key", item.key);
            map.put("value", item.value);
            list.add(map);
        });
        return list;
    }

    /**
     * 将枚举转换为list
     *
     * @param enums
     * @return
     */
    public static List<Map<String, Object>> enumsTranslation2Arrays(FileTypeEnum[] enums) {
        if (enums == null) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Arrays.stream(enums).forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("key", item.key);
            map.put("value", LocaleMessageUtils.getMessage(item.key));
            list.add(map);
        });
        return list;
    }

    private String key() {
        return this.key;
    }

    private String value() {
        return this.value;
    }
}
