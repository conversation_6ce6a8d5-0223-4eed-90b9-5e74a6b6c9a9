package com.get.common.eunms;

import com.get.common.utils.LocaleMessageUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @DATE: 2021/6/18
 * @TIME: 16:21
 * @Description:学习计划附件类型
 **/
public enum StudentItemMediaTypeEnum {
    /**
     * 附件类型
     */
    SUBMITTED_MEDIA("submitted_media", "申请提交完成凭证资料", 2L),
    APP_RECEIVED_MEDIA("app_received_media", "院校已收件凭证资料", 3L),
    ADMITTED_MEDIA("admitted_media", "已录取收到OFFER凭证资料", 4L),
    DEPOSIT_MEDIA("deposit_media", "已付押金凭证资料", 5L),
    CASCOE_MEDIA("cascoe_media", "CAS/COE凭证资料", 6L),
    VISA_SUBMITTED_MEDIA("visa_submitted_media", "递交签证完成凭证资料", 7L),
    VISA_GRANTED_MEDIA("visa_granted_media", "获得签证凭证资料", 10L),
    ENTRANCE_SUCCESS_MEDIA("entrance_success_media", "入学登记完成凭证资料", 8L),
    ENTRANCE_FAIL_MEDIA("entrance_fail_media", "入学失败凭证资料", 9L),
    NOTIFIED_PAYMENT_MEDIA("notified_payment_media", "已通知缴费凭证资料", 11L),
    REGISTERED_MEDIA("registered_media", "已挂号凭证资料", 12L),
    APPLICATION_FOR_EXTENSION_MEDIA("application_for_extension_media", "申请延期凭证资料", 13L),
    POSTPONED_MEDIA("postponed_media", "已延期凭证资料", 14L),
    APPLY_FOR_DEPOSIT_REFUND_MEDIA("apply_for_deposit_refund_media", "申请退押金凭证资料", 15L),
//    VISA_SUBMITTED_MEDIA_MY_SG("visa_submitted_media_my_sg", "递交签证完成(马来西亚、新加坡)", 16L),
//    M_STUDENT_SERVICE_FEE("m_student_service_fee_media","留学服务费资料",17L),
    TUITION_MEDIA("tuition_media", "已付学费凭证资料", 17L),
    STEP_OFFER_ACCEPTED("offer_accepted_media", "已接受Offer", 19L),
    //缺资料
    MISSING_MEDIA("missing_media", "缺资料", null),

    ;

    public static final StudentItemMediaTypeEnum[] MEDIA_TYPE = new StudentItemMediaTypeEnum[]{SUBMITTED_MEDIA, APP_RECEIVED_MEDIA, ADMITTED_MEDIA, DEPOSIT_MEDIA, CASCOE_MEDIA,
            VISA_SUBMITTED_MEDIA, VISA_GRANTED_MEDIA, ENTRANCE_SUCCESS_MEDIA, ENTRANCE_FAIL_MEDIA, NOTIFIED_PAYMENT_MEDIA, REGISTERED_MEDIA,
            APPLICATION_FOR_EXTENSION_MEDIA, POSTPONED_MEDIA, APPLY_FOR_DEPOSIT_REFUND_MEDIA,TUITION_MEDIA, STEP_OFFER_ACCEPTED,MISSING_MEDIA};

    public static final StudentItemMediaTypeEnum[] MEDIA_MAIN_TYPE = new StudentItemMediaTypeEnum[]{ADMITTED_MEDIA,CASCOE_MEDIA,ENTRANCE_SUCCESS_MEDIA
    };

    public String key;
    public String value;
    public Long itemId;


    StudentItemMediaTypeEnum(String key, String value, Long itemId) {
        this.key = key;
        this.value = value;
        this.itemId = itemId;
    }

    public static String getValue(String key) {
        StudentItemMediaTypeEnum[] mediaTypeEnums = values();
        for (StudentItemMediaTypeEnum mediaTypeEnum : mediaTypeEnums) {
            if (mediaTypeEnum.key.equals(key)) {
                return LocaleMessageUtils.getMessage(mediaTypeEnum.key());
            }
        }
        return null;
    }

    public static String getKey(String value) {
        StudentItemMediaTypeEnum[] mediaTypeEnums = values();
        for (StudentItemMediaTypeEnum mediaTypeEnum : mediaTypeEnums) {
            if (mediaTypeEnum.value().equals(value)) {
                return mediaTypeEnum.key();
            }
        }
        return null;
    }

    public static String getKeyByStepId(Long stepId) {
        StudentItemMediaTypeEnum[] mediaTypeEnums = values();
        for (StudentItemMediaTypeEnum mediaTypeEnum : mediaTypeEnums) {
            if (mediaTypeEnum.itemId.equals(stepId)) {
                return mediaTypeEnum.key();
            }
        }
        return null;
    }

    /**
     * 将枚举转换为list
     *
     * @param enums
     * @return
     */
    public static List<String> enums2ArraysList(StudentItemMediaTypeEnum[] enums) {
        if (enums == null) {
            return new ArrayList<>();
        }
        List<String> list = new ArrayList<>();
        Arrays.stream(enums).forEach(item -> {
            list.add(item.key);
        });
        return list;
    }

    /**
     * 将枚举转换为list
     *
     * @param enums
     * @return
     */
    public static List<Map<String, Object>> enums2Arrays(StudentItemMediaTypeEnum[] enums) {
        if (enums == null) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Arrays.stream(enums).forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("key", item.key);
            map.put("value", item.value);
            list.add(map);
        });
        return list;
    }

    /**
     * 将枚举转换为list
     *
     * @param enums
     * @return
     */
    public static List<Map<String, Object>> enumsTranslation2Arrays(StudentItemMediaTypeEnum[] enums) {
        if (enums == null) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Arrays.stream(enums).forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("key", item.key);
            map.put("value", LocaleMessageUtils.getMessage(item.key()));
            map.put("itemId", item.itemId);
            list.add(map);
        });
        return list;
    }

    private String key() {
        return this.key;
    }

    private String value() {
        return this.value;
    }

}

