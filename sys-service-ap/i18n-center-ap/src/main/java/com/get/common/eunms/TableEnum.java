package com.get.common.eunms;

import com.get.common.utils.LocaleMessageUtils;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @DATE: 2020/8/5
 * @TIME: 16:27
 * @Description:表枚举
 **/
public enum TableEnum {
    /**
     * 学校中心
     */
    INSTITUTION("m_institution", "学校"),
    INSTITUTION_SCHOLARSHIP("m_institution_scholarship","奖学金"),
    INSTITUTION_APP_INFO_FEE("m_institution_app_fee","申请信息"),
    INSTITUTION_DEAD_LINE_INFO("m_institution_deadline_info","申请截止新信息"),
    INSTITUTION_FACULTY("m_institution_faculty", "学院"),
    INSTITUTION_ZONE("m_institution_zone", "校区"),
    INSTITUTION_MAJOR("m_institution_major", "专业"),
    INSTITUTION_COURSE("m_institution_course", "课程"),
    INSTITUTION_CONTRACT("m_contract", "合同"),
    INSTITUTION_CONTRACT_FORMULA("m_contract_formula", "合同公式"),
    INSTITUTION_INFO("m_institution_info", "资讯"),
    INSTITUTION_FAQ("m_institution_faq", "常见问题"),
    INSTITUTION_ALUMNUS("m_institution_alumnus", "知名校友"),
    INSTITUTION_PROVIDER("m_institution_provider", "学校提供商"),
    INSTITUTION_PROVIDER_CHANNEL("m_institution_channel", "学校渠道"),
    IAE_INSTITUTION_PROVIDER_CHANNEL("m_institution_channel", "学校佣金合同方"),
    INSTITUTION_PROVIDER_ACCOUNT("m_institution_provider_account", "学校提供商合同账户"),
    INSTITUTION_COUNTRY("u_area_country", "国家"),
    INSTITUTION_AREA_STATE("u_area_state", "州省"),
    NEWS("s_news", "新闻"),
    NEWS_TYPE("u_news_type", "新闻类型"),
    INSTITUTION_TYPE("u_institution_type", "学校类型"),
    INSTITUTION_MAJOR_LEVEL("u_major_level", "课程级别"),
    INSTITUTION_COURSE_TYPE("u_course_type", "课程类型"),
    INSTITUTION_COURSE_TYPE_GROUP("u_course_type_group", "课程类型组别"),
    INSTITUTION_INFO_TYPE("u_info_type", "资讯类型"),
    INSTITUTION_COUNTRY_INFO("u_area_country_info", "国家资讯"),
    INSTITUTION_COUNTRY_INFO_TYPE("u_area_country_info_type", "国家资讯类型"),
    INSTITUTION_AREA_CITY("u_area_city", "城市"),
    INSTITUTION_AREA_CITY_INFO("u_area_city_info", "城市资讯"),
    INSTITUTION_AREA_CITY_INFO_TYPE("u_area_city_info_type", "城市资讯类型"),
    INSTITUTION_AREA_CITY_DIVISION("u_area_city_division", "城市区域"),
    INSTITUTION_IENGINE_SCORE("s_institution_iengine_score", "智选引擎分数"),
    INSTITUTION_AREA_REGION("u_area_region", "大区"),
    INSTITUTION_APP_INFO("s_app_info", "申请资料"),
    INSTITUTION_NEWS_BULLETIN_BOARD("institution_news_bulletin_board","MSO公告栏"),
    GEOGRAPHICAL_DIVISION("geographical_division","地理划分"),
    /**
     * 权限中心
     */
    PERMISSION_STAFF("m_staff", "员工"),
    PERMISSION_EVENT("m_staff_hr_event", "员工人事记录"),
    PERMISSION_CONTRACT("m_staff_contract", "员工合同"),
    PERMISSION_COMPANY("m_company", "公司"),

    /**
     * 财务中心
     */
    FINANCE_COMPANY_PROFIT_AND_LOSS_ITEM("m_company_profit_and_loss_item", "公司损益表项套账"),
    FINANCE_STAGE_ACCOUNTING_ITEM_VALUE("m_stage_accounting_item_value", "科目汇总表统计数值"),
    FINANCE_STAGE_BALANCE_SHEET_VALUE("m_stage_balance_sheet_value", "资产负债表统计数值"),
    FINANCE_STAGE_PROFIT_AND_LOSS_VALUE("m_stage_profit_and_loss_value", "损益表统计数值"),
    FINANCE_INVOICES("m_invoice", "发票"),
    FINANCE_PROVIDER("m_provider", "供应商"),
    FINANCE_PAYMENT_FORM("m_payment_form", "付款单"),
    FINANCE_PAYMENT_FORM_ITEM("m_payment_form_item", "付款单子项"),
    FINANCE_RECEIPT_FORM("m_receipt_form", "收款单"),
    FINANCE_RECEIPT_FORM_ITEM("m_receipt_form_item", "收款单子项"),
    FINANCE_EXPENSE_CLAIM_FORM("m_expense_claim_form", "费用报销单"),
    FINANCE_PREPAY_APPLICATION_FORM("m_prepay_application_form", "借款申请单"),
    FINANCE_TRAVEL_CLAIM_FORM("m_travel_claim_form", "差旅报销单"),
    FINANCE_PAYMENT_APPLICATION_FORM("m_payment_application_form", "支付申请单"),
    /**
     * 办公中心
     */
    OFFICE_LEAVE_APPLICATION_FORM("m_leave_application_form", "工休申请单"),
    M_TASK("m_task","自定义任务表"),
    M_TASK_ITEM("m_task_item","自定义任务子任务表"),

    /**
     * 佣金结算
     */
    SETTLEMENT_COMMISSION_NOTICE("SETTLEMENT_COMMISSION_NOTICE","佣金结算通知，value1=0/1为总开关，0不发，1发，value2=通知邮件地址"),
    STUDENT_OFFER_ITEM_COMMISSION_NOTICE("STUDENT_OFFER_ITEM_COMMISSION_NOTICE","学习计划佣金结算通知"),
    INSURANCE_COMMISSION_NOTICE("INSURANCE_COMMISSION_NOTICE","留学保险佣金结算通知"),
    ACCOMMODATION_COMMISSION_NOTICE("ACCOMMODATION_COMMISSION_NOTICE","留学住宿佣金结算通知"),
    SERVICE_FEE_COMMISSION_NOTICE("SERVICE_FEE_COMMISSION_NOTICE","服务费佣金结算通知"),
    /**
     * 人才中心
     */
    RESUME_RESUME("m_resume", "人才简历"),
    RESUME_ATTACHMENT("m_resume_attachment", "简历附件"),

    /**
     * 销售中心
     */
    SALE_APP_STUDENT("m_app_student", "partner学生"),
    BUSINESS_LICENSE("agent_business_license","代理营业执照"),
    SALE_CONVENTION("m_convention", "峰会"),
    SALE_CONVENTION_PROCEDURE("m_convention_procedure", "峰会流程"),
    SALE_AGENT("m_agent", "学生代理"),
    SALE_STUDENT("m_student", "学生"),
    BUSINESS_CHANNEL_INS("m_business_channel_ins", "留学保险渠道"),
    BUSINESS_CHANNEL_ACC("m_business_channel_acc", "留学住宿渠道"),
    SALE_CONTRACT("m_agent_contract", "学生代理合同"),
    SALE_STUDENT_OFFER("m_student_offer", "留学申请方案"),
    CLIENT_OFFER("m_client_offer","留学咨询方案"),
    SALE_STUDENT_OFFER_ITEM("m_student_offer_item", "留学申请计划"),
    SALE_STUDENT_OFFER_ITEM_STEP("r_student_offer_item", "学生申请方案项目与步骤"),
    SALE_RECEIVABLE("m_receivable_plan", "应收计划"),
    SALE_PAYABLE("m_payable_plan", "应付计划"),
    SALE_EVENT("m_event", "活动"),
    SALE_CONVENTION_AWARD("m_convention_award", "奖品"),
    SALE_STUDENT_ACCOMMODATION("m_student_accommodation", "留学住宿"),
    SALE_STUDENT_INSURANCE("m_student_insurance", "留学保险"),
    SALE_STUDENT_SERVICE_FEE("m_student_service_fee","留学服务费"),
    SALE_BUSINESS_PROVIDER("m_business_provider", "业务提供商"),
    SALE_BUSINESS_PROVIDER_ACCOUNT("m_business_provider_account", "业务提供商合同账户"),
    SALE_EVENT_BILL("m_event_bill", "申请活动费用"),
    SALE_EVENT_INCENTIVE("m_event_incentive", "奖励推广活动"),
    CONVENTION_GOPRO_NUCLEIC_ACID("m_gopro_nucleic_acid", "GoPro核酸资料"),
    SALE_CLIENT("m_client", "客户"),
    SALE_INCENTIVE_POLICY("m_incentive_policy", "奖励政策"),
    APP_AGENT_CONTRACT_ACCOUNT("m_app_agent_contract_account", "学生代理申请合同账户"),
    APP_AGENT("m_app_agent", "学生代理申请"),
    APP_AGENT_APPROVE_COMMENT("m_app_agent_approve_comment", "学生代理申请审批意见"),
    R_STUDENT_TO_CLIENT_APPROVAL("r_student_to_client_approval","学生客户审批表"),
    AGENT_CONTRACT_ACCOUNT("m_agent_contract_account","学生代理合同账户表"),

    EVENT_PLAN_REGISTRATION("m_event_plan_registration","活动报名表"),
    EVENT_PLAN_THEME("m_event_plan_theme","活动年度计划主题表"),
    EVENT_PLAN_THEME_ONLINE("m_event_plan_theme_online","活动计划主题，线上活动类型项目表"),
    EVENT_PLAN_THEME_OFFLINE_ITEM("m_event_plan_theme_offline_item","活动计划主题，线下活动类型项目子项表"),
    EVENT_PLAN_THEME_WORKSHOP("m_event_plan_theme_workshop","活动计划主题，线下专访类型项目表"),
    EVENT_PLAN_THEME_OFFLINE("m_event_plan_theme_offline","活动计划主题，线下活动类型项目表"),
    VOTING_LIKE_COUNT("m_like_count","峰会集赞投票截图"),
    SUMMIT_REGISTRATION ("m_convention_registration","峰会报名册表"),
    /**
     * 工作流中心
     */
    WORKFLOW_MPAY("m_payment_application_form", "支付单表事由或物品"),
    ACT_HI_TASKINST("act_hi_taskinst", "工作流历史任务表"),
    /**
     * 业务平台配置中心
     */
    AGENT_MODULE_INFO("m_agent_module_info", "代理模块信息设定/国家资讯配置"),
    AGENT_INFO("m_agent_info", "代理配置表"),
    AGENT_CONTRACT("m_agent_contract", "学生代理合同表"),
    APP_INSTITUTION_CHARACTER_ITEM("u_app_institution_character_item", "学校特殊申请信息项目(课程动态表单配置详情配置)"),
    U_STUDENT_ATTACHMENT("u_student_attachment", "学生附件类型"),
    U_APP_FORM_DIVISION("u_app_form_division", "申请表单板块"),
    M_SITEMAP("m_sitemap", "网站地图"),
    M_SITEMAP_PAGE_TEMPLATE("m_sitemap_page_template", "页面模板"),
    /**
     * 帮助中心
     */
    HELP("m_help", "帮助信息"),
    HELP_TYPE("u_help_type", "帮助类型"),

    /**
     * 注册中心
     */
    PRIVATE_POLICY("m_privacy_policy", "私隐条例"),
    /**
     * 考试中心
     */
    SCORE_TITLE("u_score_title", "排名称号"),
    /**
     * 投票中心
     */
    VOTING_ITEM_OPTION("m_voting_item_option", "投票选项图片"),
    /**
     * 投票中心投票项管理
     */
    VOTING_ITEM("m_voting_item", "投票项管理图片"),

    /**
     * 应收计划类型 其他收入
     */
    SALE_OTHER_INCOME("other", "其他收入"),
    /**
     * 澳小保
     */
    INSURANCE_ORDER("m_insurance_order", "留学保险（澳小保）"),



    /**
     * PMP中心
     */
    INSTITUTION_PROVIDER_CONTRACT("m_institution_provider_contract", "学校供应商合同表") ,

    /**
     * 竞赛中心
     */
    COMPETITION("m_competition","竞赛主题"),
    COMPETITION_ITEM("m_competition_item","竞赛项目"),
    COMPETITION_ITEM_JUDGER("m_competition_item_judger","竞赛项目评委"),

    /**
     * 提醒中心
     */
    REMIND_TASK("m_remind_task", "提醒任务"),

    TEMPLATE("m_template","简历模板"),
    RESUME_TRANSCRIPT("m_resume_transcript","简历成绩单"),
    RESUME_AWARD_INFORMATION("m_resume_award_information","简历活动单"),
    USER("m_user","SG普通用户表" );




    /**
     * sg简历资料
     */

    public static final TableEnum[] SG_TYPE = new TableEnum[]{TEMPLATE,RESUME_TRANSCRIPT};

    /**
     * 申請资料表类型
     */
    public static final TableEnum[] APP_INFO_TYPE = new TableEnum[]{INSTITUTION_COURSE};
    /**
     * 联系人目标类型
     */
    public static final TableEnum[] INSTITUTION_TYPES = new TableEnum[]{INSTITUTION, INSTITUTION_PROVIDER};
    /**
     * 联系人目标类型
     */
    public static final TableEnum[] SALE_TYPE = new TableEnum[]{SALE_AGENT};
    /**
     * 新闻目标类型
     */
    public static final TableEnum[] NEW_TARGET_TYPE = new TableEnum[]{INSTITUTION_COUNTRY, INSTITUTION_PROVIDER, INSTITUTION, INSTITUTION_COURSE,INSTITUTION_NEWS_BULLETIN_BOARD};
    /**
     * 收款目标类型
     */
    public static final TableEnum[] RECEIPT_TARGET_TYPE = new TableEnum[]{INSTITUTION_PROVIDER};
    /**
     * 付款目标类型
     */
    public static final TableEnum[] PAY_TARGET_TYPE = new TableEnum[]{SALE_AGENT,INSTITUTION_PROVIDER,SALE_BUSINESS_PROVIDER};
    /**
     * 应收/付计划类型
     */
    public static final TableEnum[] SALE_TARGET_TYPE = new TableEnum[]{SALE_STUDENT_OFFER_ITEM, SALE_STUDENT_INSURANCE, SALE_STUDENT_ACCOMMODATION,SALE_STUDENT_SERVICE_FEE,INSTITUTION_PROVIDER};
    /**
     * 应收计划类型
     */
    public static final TableEnum[] SALE_RECEIVABLE_TARGET_TYPE = new TableEnum[]{SALE_STUDENT_OFFER_ITEM, SALE_STUDENT_INSURANCE, SALE_STUDENT_ACCOMMODATION,SALE_STUDENT_SERVICE_FEE, INSTITUTION_PROVIDER,INSURANCE_ORDER
//            INSTITUTION_PROVIDER_CHANNEL,BUSINESS_CHANNEL_INS,
//            BUSINESS_CHANNEL_ACC
    };

    /**
     * 学生留学服务费 收款方类型
     */
    public static final TableEnum[] TYPE_KEY_RECEIVABLE = new TableEnum[]{SALE_STUDENT, INSTITUTION_PROVIDER};


    /**
     * 待审核应收计划类型
     */
    public static final TableEnum[] SALE_RECEIVABLE_AUDIT_TARGET_TYPE = new TableEnum[]{SALE_STUDENT_OFFER_ITEM, SALE_STUDENT_INSURANCE
            , SALE_STUDENT_ACCOMMODATION
            ,SALE_STUDENT_SERVICE_FEE
    };
    /**
     * 应付计划类型
     */
    public static final TableEnum[] SALE_PAYABLE_TARGET_TYPE = new TableEnum[]{SALE_STUDENT_OFFER_ITEM, SALE_STUDENT_INSURANCE, SALE_STUDENT_ACCOMMODATION,SALE_STUDENT_SERVICE_FEE,INSTITUTION_PROVIDER,SALE_BUSINESS_PROVIDER, INSURANCE_ORDER};
    /**
     * 佣金应付计划类型
     */
    public static final TableEnum[] SALE_COMMISSION_PAYABLE_TARGET_TYPE = new TableEnum[]{SALE_STUDENT_OFFER_ITEM, SALE_STUDENT_INSURANCE, SALE_STUDENT_ACCOMMODATION,SALE_STUDENT_SERVICE_FEE};
    /**
     * 支付对象
     */
    public static final TableEnum[] PAY_OBJECT_TYPE = new TableEnum[]{FINANCE_PROVIDER};
    /**
     * 佣金结算类型
     */
    public static final TableEnum[] COMMISSION_SETTLEMENT_TYPE = new TableEnum[]{SALE_STUDENT_OFFER_ITEM, SALE_STUDENT_ACCOMMODATION, SALE_STUDENT_INSURANCE,SALE_STUDENT_SERVICE_FEE};
    /**
     * 业务类型
     */
    public static final TableEnum[] BUSINESS_TYPE = new TableEnum[]{SALE_STUDENT_OFFER, SALE_STUDENT_ACCOMMODATION, SALE_STUDENT_INSURANCE};

    public static final TableEnum[] S_P = {INSTITUTION_COUNTRY,INSTITUTION,INSTITUTION_FACULTY,INSTITUTION_COURSE_TYPE_GROUP,INSTITUTION_COURSE_TYPE,INSTITUTION_MAJOR_LEVEL,INSTITUTION_COURSE};
    /**
     *
     * vip配置类型
     */
    public static final TableEnum[] VIP_CONFIG_TYPE = new TableEnum[]{INSTITUTION_PROVIDER,INSTITUTION_COUNTRY};

    /**
     * 业务标签类型
     */
    public static final TableEnum[] NAME_LABEL = new TableEnum[]{INSTITUTION};

    public String key;
    public String value;

    TableEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValue(String key) {
        TableEnum[] fileTypeEnums = values();
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        for (TableEnum fileTypeEnum : fileTypeEnums) {
            if (fileTypeEnum.key.equals(key)) {
                if (fileTypeEnum.key.equals(TableEnum.INSTITUTION_PROVIDER_CHANNEL.key) && fkCompanyId==3) {
                    return TableEnum.IAE_INSTITUTION_PROVIDER_CHANNEL.value;
                }
                return LocaleMessageUtils.getMessage(fileTypeEnum.key());
            }
        }
        return null;
    }

    public static String getValue(String key,String local) {
        TableEnum[] fileTypeEnums = values();
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        for (TableEnum fileTypeEnum : fileTypeEnums) {
            if (fileTypeEnum.key.equals(key)) {
                if (fileTypeEnum.key.equals(TableEnum.INSTITUTION_PROVIDER_CHANNEL.key) && fkCompanyId==3) {
                    return TableEnum.IAE_INSTITUTION_PROVIDER_CHANNEL.value;
                }
                return LocaleMessageUtils.getMessage(local,fileTypeEnum.key);
            }
        }
        return null;
    }

    public static String getKey(String value) {
        TableEnum[] fileTypeEnums = values();
        for (TableEnum fileTypeEnum : fileTypeEnums) {
            if (fileTypeEnum.value().equals(value)) {
                return fileTypeEnum.key();
            }
        }
        return null;
    }

    public static String getValueByKey(String key, TableEnum[] enums,String local) {
        List<TableEnum> list = Arrays.asList(enums).stream().filter(e -> e.key().equals(key)).collect(Collectors.toList());
        String value = "";
        if (GeneralTool.isNotEmpty(list)) {
            value = list.get(0).name();
            String i18nString = LocaleMessageUtils.getMessage(local,list.get(0).name());
            if (GeneralTool.isNotEmpty(i18nString)) {
                value = i18nString;
            }
        }
//        return GeneralTool.isNotEmpty(list) ? LocaleMessageUtils.getMessage(list.get(0).name()) : "";
        return value;
    }

    /**
     * 将枚举转换为list
     *
     * @param enums
     * @return
     */
    public static List<Map<String, Object>> enums2Arrays(TableEnum[] enums) {
        if (GeneralTool.isEmpty(enums)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        Arrays.stream(enums).forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            if (item.key.equals(TableEnum.INSTITUTION_PROVIDER_CHANNEL.key) && fkCompanyId == 3) {
                item = TableEnum.IAE_INSTITUTION_PROVIDER_CHANNEL;
            }
            map.put("key", item.key);
            map.put("value", item.value);
            list.add(map);
        });
        return list;
    }

    /**
     * 将枚举转换为list
     *
     * @param enums
     * @return
     */
    public static List<Map<String, Object>> enumsTranslation2Arrays(TableEnum[] enums) {
        if (GeneralTool.isEmpty(enums)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        Arrays.stream(enums).forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            if (item.key.equals(TableEnum.INSTITUTION_PROVIDER_CHANNEL.key) && fkCompanyId==3) {
                item = TableEnum.IAE_INSTITUTION_PROVIDER_CHANNEL;
                map.put("key", item.key);
                map.put("value", item.value);
            }else {
                map.put("key", item.key);
                map.put("value", LocaleMessageUtils.getMessage(item.key()));
            }
            list.add(map);
        });
        return list;
    }

    public static List<Map<String, Object>> enumsTranslation2ArraysByName(TableEnum[] enums) {
        if (GeneralTool.isEmpty(enums)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Arrays.stream(enums).forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            map.put("key", item.key);
            map.put("value", LocaleMessageUtils.getMessage(item.name()));
            list.add(map);
        });
        return list;
    }

    /**
     * 根据key获取 keyName 获取value
     *
     * @param key
     * @return
     */
    public static String getValueByKey(String key, TableEnum[] enums) {
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        if (key.equals(TableEnum.INSTITUTION_PROVIDER_CHANNEL.key) && fkCompanyId==3) {
            return TableEnum.IAE_INSTITUTION_PROVIDER_CHANNEL.value;
        }
        List<TableEnum> list = Arrays.stream(enums).filter(e -> e.key().equals(key)).collect(Collectors.toList());
        return GeneralTool.isNotEmpty(list) ? LocaleMessageUtils.getMessage(list.get(0).name()) : "";
    }

    public static String getValue(String key, TableEnum[] enums) {
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        if (key.equals(TableEnum.INSTITUTION_PROVIDER_CHANNEL.key) && fkCompanyId==3) {
            return TableEnum.IAE_INSTITUTION_PROVIDER_CHANNEL.value;
        }
        List<TableEnum> list = Arrays.stream(enums).filter(e -> e.key().equals(key)).collect(Collectors.toList());
        return GeneralTool.isNotEmpty(list) ? list.get(0).name() : "";
    }

    public static String getInitValue(String key, TableEnum[] enums) {
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        if (key.equals(TableEnum.INSTITUTION_PROVIDER_CHANNEL.key) && fkCompanyId==3) {
            return TableEnum.IAE_INSTITUTION_PROVIDER_CHANNEL.value;
        }
        List<TableEnum> list = Arrays.stream(enums).filter(e -> e.key().equals(key)).collect(Collectors.toList());
        return GeneralTool.isNotEmpty(list) ? list.get(0).value : "";
    }

    private String key() {
        return this.key;
    }

    private String value() {
        return this.value;
    }
}
