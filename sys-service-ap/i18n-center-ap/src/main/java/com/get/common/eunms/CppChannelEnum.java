package com.get.common.eunms;

/**
 * CPP渠道枚举
 *
 * <AUTHOR>
 * @date 2022/2/25 11:53
 */
public enum CppChannelEnum {
    OFFER_51(18L, 40L, "51offer"),
    ACIC(10L, 2L, "ACIC"),
    AMERIGO_TAIWAN(31L, 41L, "Amerigo Taiwan"),
    BEO(15L, 3L, "BEO"),
    BURGEON(34L, 39L, "Burgeon"),
    CANACHIEVE(21L, 4L, "Canachieve 加成"),
    DLT(29L, 5L, "DLT"),
    EDU_GLOBAL(17L, 6L, "Edu Global"),
    FONTON(20L, 7L, "FONTON方通"),
    GEA(7L, 9L, "GEA"),
    GEN(8L, 10L, "GEN"),
    GOES(23L, 42L, "GOES侨谊"),
    GZL(28L, 43L, "GZL广之旅"),
    GOLDEN_ARROW(19L, 11L, "Golden Arrow"),
    GOSTUDY(27L, 12L, "Gostudy"),
    <PERSON>OP<PERSON>(22L, 13L, "Hope科博"),
    HUATONG(33L, 44L, "Huatong"),
    IAE_AU(3L, 45L, "IAE AU"),
    IAE_CN(2L, 46L, "IAE CN"),
    IAE_G(1L, 15L, "IAE G"),
    RIA(36L, 50L, "RIA美高"),
    IAE_NZ(5L, 46L, "IAE NZ"),
    IAE_UK(4L, 47L, "IAE UK"),
    IAE_US(6L, 48L, "IAE US"),
    IDP(11L, 16L, "IDP"),
    INDEX(14L, 17L, "INDEX"),
    INDEX_TAIWAN(25L, 18L, "Index Taiwan"),
    JJL(13L, 49L, "JJL"),
    COOPERATIVE_COLLEGES(12L, 20L, "N/A合作院校"),
    UKEAS(26L, 23L, "UKEAS"),
    UKEAS_ORANGE(24L, 23L, "UKEAS柳橙"),
    UPP(9L, 51L, "UPP"),
    WISDOM(16L, 52L, "Wisdom"),
    XUE_SHENG(30L, 24L, "Xuesheng学圣"),
    SI_UK(32L, 21L, "SI-UK"),
    NOT_FILLED(1000L, 25L, "未填"),
    SHANGHAI_AOXING(40L, 53L, "上海澳星"),
    HOME_CHANNEL(37L, 1L, "158"),
    HKOSC(38L, 38L, "HKOSC"),
    IAE_TW(39L, 35L, "IAE TW");


    /**
     * cpp渠道id
     */
    public Long key;
    /**
     * BMS渠道id
     */
    public Long channelId;

    public String value;


    CppChannelEnum(Long key, Long channelId, String value) {
        this.key = key;
        this.channelId = channelId;
        this.value = value;
    }

    public static Long getChannelId(Long key) {
        CppChannelEnum[] cppChannelEnums = values();
        for (CppChannelEnum projectKeyEnum : cppChannelEnums) {
            if (projectKeyEnum.key.equals(key)) {
                return projectKeyEnum.channelId();
            }
        }
        return null;
    }

    public static String getValue(Long key) {
        CppChannelEnum[] cppChannelEnums = values();
        for (CppChannelEnum projectKeyEnum : cppChannelEnums) {
            if (projectKeyEnum.key.equals(key)) {
                return projectKeyEnum.value();
            }
        }
        return null;
    }

    private Long key() {
        return this.key;
    }

    private String value() {
        return this.value;
    }

    private Long channelId() {
        return this.channelId;
    }
}
