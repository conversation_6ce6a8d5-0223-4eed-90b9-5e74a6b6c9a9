package com.get.common.eunms;


import com.get.common.utils.LocaleMessageUtils;
import com.get.core.secure.utils.SecureUtil;
import com.get.core.tool.utils.GeneralTool;

import java.lang.reflect.Field;
import java.util.*;
import java.util.stream.Collectors;

//import com.get.core.tool.utils.LocaleMessageUtils;

/**
 * <AUTHOR>
 * @DATE: 2020/9/2
 * @TIME: 12:12
 * @Description:项目Key枚举
 **/
public enum ProjectKeyEnum {
    //峰会报名抄送邮箱
    GEA_CONVENTION_REGISTRATION("GEA_CONVENTION_REGISTRATION", "峰会报名抄送邮箱 or 峰会早鸟价配置"),
    //汇率-系统参数key
    GET_EXCHANGE_RATE("GET_EXCHANGE_RATE", "1/0(开启/关闭)"),
    GET_EXCHANGE_RATE_TIME("GET_EXCHANGE_RATE_TIME", "每天获取时间"),
    EXCHANGE_FROM_CURRENCY_TYPE("EXCHANGE_FROM_CURRENCY_TYPE", "参照币种编号"),
    EXCHANGE_TO_CURRENCY_TYPE("EXCHANGE_TO_CURRENCY_TYPE ", "目标币种编号"),
    //桌台类型
    CONVENTION_TRAINING_TABLE("CONVENTION_TRAINING_TABLE", "培训桌"),
    CONVENTION_DINNER_TABLE("CONVENTION_DINNER_TABLE", "晚宴桌"),
    //申请资料类型
    APP_COURSE_WEBSITE("app_course_website", "课程详细网址"),
    APP_COURSE_REQ_WEBSITE("app_course_req_website", "课程要求网址"),
    APP_COURSE_OTHER_WEBSITE("app_course_other_website", "课程其他网址"),
    SCHOLARSHIP_INFO("scholarship_info", "奖学金信息"),
    SCHOLARSHIP_WEBSITE("scholarship_website", "奖学金网址"),
    APP_UCAS_CODE("app_ucas_code", "UCAS CODE"),
    APP_CRICOS_CODE("app_cricos_code", "CRICOS CODE"),
    APP_PROGRAM_CODE("app_program_code", "PROGRAM CODE"),
    ADMISSION_REQUIREMENTS("admission_requirements", "入学试收生要求"),
    PERSONAL_STATEMENT("personal_statement", "个人陈述"),
    INTERVIEW_PREPARATION("interview_preparation", "面试准备"),
    CNCC("cncc", "无犯罪记录证明书"),

    //附件路径前缀
    APPENDIX_SRC_PREFIX("APPENDIX_SRC_PREFIX", "附件路径前缀"),
    //文件路径前缀
    FILE_SRC_PREFIX("FILE_SRC_PREFIX", "文件路径前缀"),

    SUMMIT_REGISTRATION_FORM("SUMMIT_REGISTRATION_FORM","Hti峰会参展商报名申请表单配置项"),

    AGENT_PROJECT_ROLE("AGENT_PROJECT_ROLE", "系统创建代理时，可分配项目成员角色"),
    AGENT_CONTRACT_CONFIG("AGENT_CONTRACT_CONFIG", "合同续期配置"),
    REMINDER_TASK_LINK_WF_TAKE("REMINDER_TASK_LINK_WF_TAKE", "提醒中心链接设置：工作流签取"),
    REMINDER_TASK_LINK_WF_APPROVE("REMINDER_TASK_LINK_WF_APPROVE", "提醒中心链接设置：工作流审批"),
    REMINDER_TASK_LINK_OFI_VIEW("REMINDER_TASK_LINK_OFI_VIEW", "提醒中心链接设置：学习计划详细"),
    REMINDER_EMAIL_AGENT_APP_REJECTED("REMINDER_EMAIL_AGENT_APP_REJECTED", "发邮件提醒代理申请拒绝"),
    REMINDER_EMAIL_AGENT_OFFER_ITEM_DEADLINE("REMINDER_EMAIL_AGENT_OFFER_ITEM_DEADLINE","代理联系人邮件提醒功能"),
    AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE("AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE","接受Offer截止提醒（代理版）"),
    AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE("AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE","支付押金截止提醒（代理版）"),
    AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE_ENG("AGENT_STUDENT_OFFER_ACCEP_DDL_NOTICE_ENG","接受Offer截止提醒（代理版）台湾"),
    AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE_ENG("AGENT_STUDENT_OFFER_DEPOSIT_DDL_NOTICE_ENG","支付押金截止提醒（代理版）台湾"),
    AGENT_CONTRACT_NUMBER_RULE("AGENT_CONTRACT_NUMBER_RULE", "代理合同编号规则，没设置值，按默认方式，否则按：设置值+[Year]+000000（6位，按当年最大值递增，每年重新计算）"),
    EVENT_TOTAL_BUDGET_LIMIT("EVENT_TOTAL_BUDGET_LIMIT","活动汇总批核预算费用警戒值（单位为百分比），value1=警戒值，0为不设置"),
    //平台类型
    GET_MSO("get_mso", "MSO"),
    GET_ISSUE("get_issue", "ISSUE"),
    GET_ISSUE_OLD("get_issue_old", "ISSUE_OLD"),
    GET_IB("get_ib", "IBS"),
    GET_BMS("get_bms", "BMS"),

    APP_SCHOOL_GATE("APP_SCHOOL_GATE","APP_SCHOOL_GATE"),

    GET_HKISO("get_hkiso","HKISO"),
    //IAE_APP("iae_app", "IAE-APP"),
    GET_MP_EXAM("get_mp_exam", "GET_MP_EXAM"),

    //考试中心
    EXAM_LINK("EXAM_LINK","考卷连接"),

    //m_payable_plan_review
    SALE_PAYABLE_REVIEW("m_payable_plan_review","应付计划审核"),
    SALE_PAYABLE_NO_REVIEW("m_payable_plan_no_review","应付计划未审核"),

    CPP("get_cpp", "CPP"),

    APP_ID("app_id", "APP_ID"),
    APP_SECRET("app_secret", "APP_SECRET"),
    WHITE_LIST_STUDENT_OFFER_ITEM_STEP("WHITE_LIST_STUDENT_OFFER_ITEM_STEP", "WHITE_LIST_STUDENT_OFFER_ITEM_STEP"),
    //活动汇总费用发起默认通知名单
    EVENT_BILL_DEFAULT_NOTICE("EVENT_BILL_DEFAULT_NOTICE", "EVENT_BILL_DEFAULT_NOTICE"),

    //成功客户列表，留学申请OS晚于开学时间高亮提醒
    SUCCESSFUL_LIST_LATE_OS_REMIND("SUCCESSFUL_LIST_LATE_OS_REMIND","SUCCESSFUL_LIST_LATE_OS_REMIND"),

    //应收计划列表，留学申请OS晚于开学时间高亮提醒
    AR_LIST_LATE_OS_REMIND("AR_LIST_LATE_OS_REMIND","AR_LIST_LATE_OS_REMIND"),

    //学生详细页代理绑定，是否需要根据登录人限制代理选择开关
    STUDENT_AGENT_BINDING_LIST_LIMIT("STUDENT_AGENT_BINDING_LIST_LIMIT","STUDENT_AGENT_BINDING_LIST_LIMIT"),
    //代理身份证限制
    AGENT_INFO_OPT_LIMIT("AGENT_INFO_OPT_LIMIT","AGENT_INFO_OPT_LIMIT"),
    //限制用户只可以访问所属公司，不能访问所属公司旗下子公司，没设置的分公司表示没有，设置值为员工id
    COMPANY_LIMIT("COMPANY_LIMIT","COMPANY_LIMIT"),
    STUDENT_TO_CLIENT_APPROVER_DEFAULT("STUDENT_TO_CLIENT_APPROVER_DEFAULT","STUDENT_TO_CLIENT_APPROVER_DEFAULT"),

    FINANCE_OPENING_TIME_COUNT("FINANCE_OPENING_TIME_COUNT","FINANCE_OPENING_TIME_COUNT"),

    //考勤日期范围
    ATTENDANCE_RANGE_TIME("ATTENDANCE_RANGE_TIME","ATTENDANCE_RANGE_TIME"),
    //工休单，允许延迟提交时间限制，单位为小时，-1为不限制，0为没有延迟时间。3个数字分别为：发起人，审批人，最后审批人
    LEAVE_APPLICATION_FORM_SUBMIT_LIMIT("LEAVE_APPLICATION_FORM_SUBMIT_LIMIT","LEAVE_APPLICATION_FORM_SUBMIT_LIMIT"),
    //工休申请单审批人时长限制添加白名单功能
    LEAVE_APPLICATION_FORM_SUBMIT_WHITE_LIST("LEAVE_APPLICATION_FORM_SUBMIT_WHITE_LIST","LEAVE_APPLICATION_FORM_SUBMIT_WHITE_LIST"),

    //发送新闻电邮配置key
    NEWS_EMAIL_DP("NEWS_EMAIL_DP","NEWS_EMAIL_DP"),
    IAE_CRM_SECRETID("IAE_CRM_SECRETID","IAE_CRM_SECRETID"),
    HTI_AGENT_BMS_TO_CRM("HTI_AGENT_BMS_TO_CRM","HTI_AGENT_BMS_TO_CRM"),
    NEWS_EMAIL_STAFF("NEWS_EMAIL_STAFF", "NEWS_EMAIL_STAFF"),
    //代理申请在线表单3项差异设置
    APPLY_AGENT_ONLINE_FORM_LIMIT("APPLY_AGENT_ONLINE_FORM_LIMIT","APPLY_AGENT_ONLINE_FORM_LIMIT"),

    //HTI员工业绩延迟计算设置
    STAFF_COMMISSION_STEP_DELAY_CONFIG("STAFF_COMMISSION_STEP_DELAY_CONFIG", "STAFF_COMMISSION_STEP_DELAY_CONFIG"),

    // 在线代理申请表单过滤DB
    BD_DDL_FILTER("BD_DDL_FILTER", "BD_DDL_FILTER"),
    // 留学保险详细页，操作限制，显示【服务提供商/产品】【支付方式】录入，value1=0/1为总开关，0不开启，1开启
    STUDENT_INSURANCE_DETAIL_OPT_LIMIT("STUDENT_INSURANCE_DETAIL_OPT_LIMIT", "STUDENT_INSURANCE_DETAIL_OPT_LIMIT"),

    // 留学服务费，税金设置，国家编号+税率，若有相关设置，服务费输入按税金方式显示
    STUDENT_SERVICE_FEE_TAXES("STUDENT_SERVICE_FEE_TAXES", "STUDENT_SERVICE_FEE_TAXES"),

    // 活动汇总，当活动时间及活动状态发生变化时，进行邮件提醒，value1=0/1为总开关，0不开启，1开启，value2=提醒人员StaffId
    REMINDER_EMAIL_EVENT("REMINDER_EMAIL_EVENT", "REMINDER_EMAIL_EVENT"),

    //提供商修改类型
    UPDATE_INSTITUTION_GROUP("UPDATE_INSTITUTION_GROUP", "更新集团"),
    UPDATE_INSTITUTION_CHANNEL("UPDATE_INSTITUTION_CHANNEL", "更新渠道"),

    //业务平台代理模块信息设定 模块类型
    COUNTRY_CHANNEL("COUNTRY_CHANNEL", "国家频道"),
    INFO_CHANNEL_COUNTRY("INFO_CHANNEL_COUNTRY", "资讯频道_国家"),
    INFO_CHANNEL_NEWS("INFO_CHANNEL_NEWS", "资讯频道_新闻"),


    //业务平台 代理配置 代理推荐信息目标类型
    INDEX_INSTITUTION("INDEX_INSTITUTION", "首页推荐学校"),
    BRIDGE_INSTITUTION("BRIDGE_INSTITUTION","桥梁学校"),
    DSE_INSTITUTION("DSE_INSTITUTION", "DSE推荐学校"),
    DSE_NEWS("DSE_NEWS", "DSE推荐新闻"),
    INDEX_NEWS("INDEX_NEWS", "首页推荐新闻"),
    WEBSITE_NEWS("WEBSITE_NEWS", "网站推荐新闻"),
    COURSE_GROUP_RECOMMEND_INSTITUTIONS("COURSE_GROUP_RECOMMEND_INSTITUTIONS","课程组推荐学校"),
    COURSE_GROUP_RECOMMEND_NEWS("COURSE_GROUP_RECOMMEND_NEWS","课程组推荐新闻"),
    INSTITUTION_COURSE("INSTITUTION_COURSE","课程推荐"),
    PROXY_EXAM_RECOMMENDATION("PROXY_EXAM_RECOMMENDATION","考试推荐"),
    AGENCY_INFORMATION_RECOMMENDATION("AGENCY_INFORMATION_RECOMMENDATION","资讯推荐"),

    //文件类型
    PDF("PDF", "PDF"),
    JPG("JPG", "JPG"),
    JPEG("JPEG", "JPEG"),
    GIF("GIF", "GIF"),
    PNG("PNG", "PNG"),

    PUSH_FLAG("PushFlag", "学习计划推送"),
    //语言类型
    ZH_CN("zh-cn", "zh-cn"),
    ZH_HK("zh-hk", "zh-hk"),
    ZH_TW("zh-tw", "zh-tw"),
    EN_US("en-us", "en-us"),

    //付款單目标类型
    INSTITUTION_PROVIDER("m_institution_provider", "学校提供商"),
    BUSINESS_CHANNEL_INS("m_business_channel_ins", "留学保险渠道"),
    BUSINESS_PROVIDER_INS("m_business_provider_ins", "留学保险提供商"),
    M_STUDENT("m_student","客户（学生）"),
    BUSINESS_CHANNEL_ACC("m_business_channel_acc", "留学住宿渠道"),
    BUSINESS_PROVIDER("m_business_provider_acc","留学住宿提供商"),
    INSTITUTION_PROVIDER_CHANNEL("m_institution_channel", "学校渠道"),
    IAE_INSTITUTION_PROVIDER_CHANNEL("m_institution_channel", "学校佣金合同方"),
    SALE_STUDENT_OFFER_ITEM("m_student_offer_item","留学申请计划"),

    INSURANCE_ORDER("m_insurance_order", "留学保险（澳小保）"),

    //销售中心统计报表名称
    STUDENT_APPLICATION_STATISTICS("student_application_statistics","周报统计报表"),
    EMAIL_STATISTICS("email_statistics","邮件统计"),
    BATCH_MATCH_EMAIL("batch_match_email","批量匹配邮件"),


    //ISSUE申请状态枚举
    N("N", "N-等待运行"),
    T("T", "T-运行中"),
    C("C", "C-已完成"),
    E("E", "E-错误"),
    K("K", "K-手动错误，不需要处理"),
    S("S", "S-资料错误，需要人手检查继续提交"),
    R("R", "R-系统特殊问题，待处理"),

    /**
     * 帮助中心文件中心
     */
    HELP_FILE("help_file", "帮助信息资料"),
    //国家业务区域
    COUNTRY_BUSINESS_GLB("GLB", "全球"),
    COUNTRY_BUSINESS_US("US", "美国"),
    COUNTRY_BUSINESS_UK("UK", "英国"),
    COUNTRY_BUSINESS_ANZ("ANZ", "澳新"),
    COUNTRY_BUSINESS_CAN("CAN", "加拿大"),
    COUNTRY_BUSINESS_EUASIA("EUASIA", "欧亚"),

    //市场部编号
    MARKETING_DEPARTMENT("GD002", "市场部"),
    MARKETING_DEPARTMENT_INSTITUTION("GD003", "市场部-校代"),
    MARKETING_DEPARTMENT_SUPPORT("GD004", "市场支持部"),
    IT_DEPARTMENT("GD0012", "IT部"),
    //iae业务部门
    IAE_ID07("ID07","美加部"),
    IAE_ID08("ID08","英爱荷部"),
    IAE_ID09("ID09","澳新部"),
    IAE_ID11("ID11","对外发展部"),
    IAE_ID12("ID12","日本部"),

    //学习计划状态步骤
    STEP_NEW_APP("STEP_NEW_APP", "新申请（New App）"),
    STEP_SUBMITTED("STEP_SUBMITTED", "提交完成（Submitted）"),
    STEP_APP_RECEIVED("STEP_APP_RECEIVED", "院校已收件（App Received）"),
    STEP_ADMITTED("STEP_ADMITTED", "已录取（Admitted）"),
    STEP_NOTIFIED("STEP_NOTIFIED","已通知缴费"),
    STEP_DEPOSIT_PAID("STEP_DEPOSIT_PAID", "已付押金（Deposit Paid）"),
    STEP_TUITION_PAID("STEP_TUITION_PAID","已付学费"),
    STEP_APPLICATION_EXTENSION("STEP_APPLICATION_EXTENSION", "申请延期（App For Extension）"),
    STEP_POSTPONED("STEP_POSTPONED", "已延期（Postponed）"),
    STEP_APPLY_REFUND("STEP_APPLY_REFUND", "申请退押金（Apply Deposit Refund）"),
    STEP_OFFER_SELECTION("STEP_OFFER_SELECTION", "收到签证函（CAS/COE/I20）"),
    STEP_VISA_SUBMITTED("STEP_VISA_SUBMITTED", "递交签证完成（Visa Submitted）"),
    STEP_VISA_GRANTED("STEP_VISA_GRANTED", "获得签证（Visa Granted）"),
    STEP_ENROLLED("STEP_ENROLLED", "入学登记完成（Enrolled）"),
    STEP_FAILURE("STEP_FAILURE", "入学失败（Failure）"),
    STEP_ENROLLED_TBC("STEP_ENROLLED_TBC", "入学待确认（Enrolled TBC）"),

    //HTI业绩
    STEP_SUBMITTED_DELAY("STEP_SUBMITTED_DELAY", "延迟提交"),
    STEP_ADMITTED_DELAY("STEP_ADMITTED_DELAY", "延迟反馈"),

    //业绩统计类型
    CREATE_COUNT("CREATE_COUNT","新建学生"),
    APPLICATION_COUNT("APPLICATION_COUNT","处理申请（含加申）"),
    CONFIRMATION_COUNT("CONFIRMATION_COUNT","定校量（按学校）"),
    SUCCESS_COUNT("SUCCESS_COUNT","成功入学量（按学校）"),
    CONFIRMATION_BY_STUDENT_COUNT("CONFIRMATION_BY_STUDENT_COUNT","定校量（按学生）"),
    SUCCESS_BY_STUDENT_COUNT("SUCCESS_BY_STUDENT_COUNT","成功入学量（按学生）"),

    //提成后补签证
    STEP_VISA_GRANTED_BACK("STEP_VISA_GRANTED_BACK", "后补签证"),

    //禁止记录入学登记完成步骤操作的api-key
    STEP_ENROLLED_PROHIBITED("StudentOfferItemStepProhibitEnrolled","禁止记录入学登记完成步骤操作的api-key"),

    //允许入学失败步骤操作的api-key
    STEP_FAILURE_PERMIT("StudentOfferItemStepPermitFailure","允许入学失败步骤操作的api-key"),

    //代理申请排行是否查询所有代理信息api-key
    AGENT_RANKING_LIST_SHOW_ALL("AgentRankingListShowAll","代理申请排行是否查询所有代理信息api-key"),

    //财务学习计划隐藏api-key
    STUDENT_OFFER_ITEM_FINANCIAL_HIDING("StudentOfferItemFinancialHiding","财务学习计划隐藏api-key"),

    STUDENT_OFFER_ITEM_HIDE_RE_KEY("StudentOfferItemFinancialHiding","学习计划信息隐藏api-key"),

    OFFER_PROJECT_MEMBERS_LIMIT("OFFER_PROJECT_MEMBERS_LIMIT","value1=0/1为总开关，0不开启，1开启，value2=限制必填的角色key"),

    STEP_OS_OPT_LIMIT("STEP_OS_OPT_LIMIT","value1=（课程长度类型+长度，必填）0/1为总开关，0不开启，1开启。value2=（显示购买保险方式，澳洲必选）0/1为总开关，0不开启，1开启。value3=（显示支付方式）0/1为总开关，0不开启，1开启"),

    AGENT_HIDE_KEY("AgentStaffController.getAgentList","代理信息隐藏api-key"),

    //入学登记完成是否闭关其余学习方案标记
    OFFER_STEP_CLOSE_ACTION("OFFER_STEP_CLOSE_ACTION", "学习计划步骤，设置关闭其他方案开关，0=关闭/1=打开"),

    //菜单链接类型
    MENU_LINK_TYPE_BLANK("BLANK", "不需要链接"),
    MENU_LINK_TYPE_URL("URL", "网址链接"),
    MENU_LINK_TYPE_COMPONENT("COMPONENT", "模块链接"),
    MENU_LINK_TYPE_NEWS_INFO("NEWS_INFO", "新闻资讯"),
    MENU_LINK_TYPE_COUNTRY_INFO("COUNTRY_INFO", "国家资讯"),
    MENU_LINK_TYPE_COUNTRY_CHANNEL("COUNTRY_CHANNEL", "国家频道"),
    COUNTRY_ABOUT("COUNTRY_ABOUT", "升学国家标题"),
    NEWS_ABOUT("NEWS_ABOUT", "留学资讯标题"),
    NEWS_DETAILS_ID("NEWS_DETAILS_ID", "新闻详情"),
    PAGE_TEMPLATE("PAGE_TEMPLATE", "页面模板"),

    //收款费用类型type_key 枚举
    ACTIVITY("ACTIVITY", "活动"),


    //用户语言-翻译枚举
    EN("en", "en-us"),


    //微信二维码
    WX_APP_ID("APP_ID", "微信APPID"),
    WX_APP_SECRET("APP_SECRET", "微信SECRET"),

    AGENT_NEWS_EMAIL("AGENT_NEWS_EMAIL", "代理新闻邮件模板"),

    //提醒模板
    CUSTOM_EMAIL_REMIND_KEY("CUSTOM", "自定义模板"),
    FOLLOW_UP_APPOINTMENT_EMAIL_REMIND_KEY("FOLLOW_UP_APPOINTMENT","提醒回访模板"),
    WORKFLOW_EMAIL_REMIND_KEY("WORKFLOW", "流程模板"),
    STUDENT_ACCEP_OFFER_EMAIL_REMIND_KEY("STUDENT_ACCEP_OFFER", "学生接受Offer模板"),
    STUDENT_PAY_DEPOSIT_REMIND_KEY("STUDENT_PAY_DEPOSIT", "学生支付押金模板"),
    STUDENT_ACCEP_OFFER_EMAIL_REMIND_EDG_KEY("STUDENT_ACCEP_OFFER_ENG", "学生接受Offer模板"),
    STUDENT_PAY_DEPOSIT_REMIND_EDG_KEY("STUDENT_PAY_DEPOSIT_ENG", "学生支付押金模板"),
    STAFF_BIRTHDAY_CARE("STAFF_BIRTHDAY_CARE", "员工生日关怀模板"),
    OFFER_ITEM_COMMISSION_NOTICE("OFFER_ITEM_COMMISSION_NOTICE","申请计划更新通知佣金部"),
    AGENT_CONTRACT_UPDATE_NOTICE("AGENT_CONTRACT_UPDATE_NOTICE","代理合同更新通知"),
    STAFF_CONTRACT_EXPIRE("STAFF_CONTRACT_EXPIRE", "劳动合同到期模板"),
    DEADLINE_APPLY_COMMISSION("DEADLINE_APPLY_COMMISSION", "申请佣金截止模板"),

    PROVIDER_CONTRACT_EXPIRE("PROVIDER_CONTRACT_EXPIRE","学校提供商合同到期模板"),

    SAVE_NEW_STUDENT_OFFER_ITEM_STEP("SAVE_NEW_STUDENT_OFFER_ITEM_STEP", "学生申请方案项目记录下一步骤模板"),
    DEFER_ENTRANCE_EMAIL_REMIND_KEY("DEFER_ENTRANCE_REMIND", "延期入学提醒模板"),
    WORKFLOW_STUDENT_OFFER("WORKFLOW_STUDENT_OFFER", "申请方案终止作废工作流提醒模板"),
    EMPLOYEE_BIRTHDAY("EMPLOYEE_BIRTHDAY","员工生日"),
    OFFER_ITEM_INSTITION_COURSE_NOTICE("OFFER_ITEM_INSTITION_COURSE_NOTICE","学生课程重复通知模板"),

    //入读意向通知模板  Deposit Paid
    MAIN_COURSE_ADMITTED_NOTICE("MAIN_COURSE_ADMITTED_NOTICE","主课最高申请状态到达已确认步骤通知模板"),
    MAIN_COURSE_DEPOSIT_PAID_BACHELOR_NOTICE("MAIN_COURSE_DEPOSIT_PAID_BACHELOR_NOTICE","主课最高申请状态到达已付押金步骤（本科课程）通知模板"),
    MAIN_COURSE_DEPOSIT_PAID_PG_NOTICE("MAIN_COURSE_DEPOSIT_PAID_PG_NOTICE","主课最高申请状态到达已付押金步骤（研究生课程）通知模板"),
    MAIN_COURSE_CAS_NOTICE("MAIN_COURSE_CAS_NOTICE","主课最高申请状态到达收到签证函通知模板"),
    LANGUAGE_COURSE_CAS_OPENED_NOTICE("LANGUAGE_COURSE_CAS_OPENED_NOTICE","语言课最高申请状态到达收到签证函或入学登记完成且已过开学时间通知模板"),
    LANGUAGE_COURSE_CAS_NOT_OPENED_NOTICE("LANGUAGE_COURSE_CAS_NOT_OPENED_NOTICE","语言课最高申请状态到达收到签证函或入学登记完成且未过开学时间通知模板"),
    LANGUAGE_COURSE_ADMITTED_OPENED_NOTICE("LANGUAGE_COURSE_ADMITTED_OPENED_NOTICE","语言课最高申请状态到达已录取且已过开学时间通知模板"),
    LANGUAGE_COURSE_ADMITTED_NOT_OPENED_NOTICE("LANGUAGE_COURSE_ADMITTED_NOT_OPENED_NOTICE","语言课最高申请状态到达已录取且未过开学时间通知模板"),

    // 留学服务费批量实收类型
    SERVICE_FEE_FULL_RECEIPT("1", "按服务费金额全额实收"),
    RECEIVABLE_PLAN_RECEIPT("2", "按应收计划进行实收"),
    CUSTOM_AMOUNT_RECEIPT("3", "自定义金额实收"),
    // 留学服务费批量实付类型
    SERVICE_FEE_RATE_PAYMENT("1", "按服务费金额比率实付"),
    PAYABLE_PLAN_PAYMENT("2", "按应付计划进行实付"),
    CUSTOM_AMOUNT_PAYMENT("3", "自定义金额实付"),

    //业务类型
    M_STUDENT_INSURANCE("m_student_insurance", "留学保险"),
    M_STUDENT_ACCOMMODATION("m_student_accommodation", "留学住宿"),
    M_STUDENT_SERVICE_FEE("m_student_service_fee", "留学服务费"),
    M_STUDENT_SERVICE_FEE_COST("m_student_service_fee_cost", "留学服务费"),
    CLIENT_SOURCE("client_source", "本地市场来源"),
    //在家办公申请
    HOME_OFFICE("onlineWork", "在家办公"),

    //请假类型
    ANNUAL_VACATION("annualVacation", "年假"),
    TAKE_DEFERRED_HOLIDAYS("takeDeferredHolidays", "补休"),
    OVERTIEM("overtime", "加班"),
    DISEASE_VACATION("diseaseVacation", "病假"),
    LEAVE_VACATION("leaveVacation", "无薪事假"),
    ONLINE_WORK("onlineWork", "在家办公"),
    OUTSOURCING("outsourcing", "外出申请"),
    EVECTION("evection", "出差"),
    PREMARITAL_EXAMINATION("premaritalExamination", "婚前体检"),
    MARRIAGE_VACATION("marriageVacation", "婚假"),
    MATERNITY_CHECK_VACATION("maternityCheckVacation", "产检假"),
    MATERNITY_VACATION("maternityVacation", "产假"),
    ESCORT_VACATION("escortVacation", "陪产假"),
    FUNERAL_VACATION("funeralVacation", "丧假"),
    WORK_RELATED_VACATION("workRelatedVacation", "工伤假"),

    //考勤统计权限key
    ATTENDANCE_STATISTICS_SHOW_ALL("AttendanceStatisticsShowAll","考勤统计查看全部api-key,当前公司的所有人考勤库存数据均可查看"),
    ATTENDANCE_STATISTICS_SHOW_DEPARTMENT("AttendanceStatisticsShowDepartment","查看所属部门数据api-key,登录人部门内所有库存数据均可查看"),
    //休假管理权限key
    LEAVE_STOCKS_SHOW_ALL("LeaveStocksShowAll","休假管理查看全部api-key,当前公司的所有人考勤库存数据均可查看"),
    LEAVE_STOCKS_SHOW_DEPARTMENT("LeaveStocksShowDepartment","休假管理查看全部api-key,当前公司的所有人考勤库存数据均可查看"),


    CUSTOM_COLUMN_JSON("CUSTOM_COLUMN_JSON","自定义列JSON"),

    //提醒模块类型
    LEAVE_APPLICATION_FORM_TO_SIGN("officeLeaveApplicationForm.toSign", "工休单待签"),
    LEAVE_APPLICATION_FORM_TO_DO("officeLeaveApplicationForm.toDo", "工休单待办"),

    WORKFLOW_CENTER_TO_SIGN("workflowCenterToSign", "工作流待签"),
    WORKFLOW_CENTER_TO_DO("workflowCenterToDo", "工作流待办"),
    OFFICE_CENTER_APPLICATION("officeCenterApplication", "工休单我的申请"),
    OFFICE_CENTER_APPROVAL("officeCenterApproval", "工休单我的审批"),
    OFFICE_CENTER_ALL_FORM("officeCenterAllForm", "工休单所有表单"),
    WORKFLOW_CENTER("workflowCenter", "工作流中心"),
    INSTITUTION_CENTER("institutionCenter", "学校中心"),
    SALE_CENTER("saleCenter", "销售中心"),
    FINANCE_CENTER("financeCenter", "财务中心"),
    OFFICE_CENTER("officeCenter", "办公中心"),
    RESUME_CENTER("resumeCenter", "人才中心"),
    REMINDER_CENTER("reminderCenter", "提醒中心"),
    PERMISSION_CENTER("permissionCenter", "权限中心"),
    HELP_CENTER("helpCenter", "帮助中心"),
    EXAM_CENTER("examCenter", "考试中心"),
    VOTE_CENTER("voteCenter", "投票中心"),
    SYSTEM_CENTER("systemCenter", "系统中心"),
    BUSINESS_CONFIG_CENTER("businessConfigCenter", "平台配置中心"),
    LOG_CENTER("logCenter", "日志中心"),
    //学历
    KINDERGARTEN("10", "幼儿园"),
    PRIMARY_SCHOOL("20", "小学"),
    JUNIOR_HIGH_SCHOOL("30", "初中"),
    SECONDARY_SPECIALIZED_SCHOOL("40", "中专"),
    TECHNICAL_SCHOOL("41","技校"),
    HIGH_SCHOOL_VOCATIONAL_HIGH_SCHOOL("42", "高中（职高）"),
    HIGH_SCHOOL("50", "高中（普高）"),
    HIGH_SCHOOL_ALEVEL("51", "高中（ALevel）"),
    HIGH_SCHOOL_AP("52", "高中（AP）"),
    HIGH_SCHOOL_IB("53", "高中（IB）"),
    HIGH_SCHOOL_AUSTRALIAN_COLLEGE_ENTRANCE_EXAMINATION_SYSTEM("54", "高中（澳洲高考体系）"),
    HIGH_SCHOOL_CANADIAN_HIGH_SCHOOL_DIPLOMA("55","高中（加拿大高中文凭）"),
    HIGH_SCHOOL_NEW_ZEALAND_NCEA("56","高中（新西兰NCEA）"),
    HIGH_SCHOOL_HKCEE("57","高中（HKCEE）"),
    UNDERGRADUATE_FOUNDATION("60", "本科预科"),
    CERTIFICATE("61","Certificate"),
    DIPLOMA("62","Diploma"),
    ADVANCED_DIPLOMA("63","Advanced Diploma"),
    JUNIOR_COLLEGE("70", "大专"),
    HND_UK("71", "HND（UK）"),

    ASSOCIATE_DEGREE("81","副学士"),
    UPGRADING_FROM_COLLEGE_TO_UNIVERSITY("82","专升本"),
    UNDERGRADUATE("83", "本科"),
    GRADUATE_STUDENT("90", "研究生/硕士预科"),
    MASTER("91", "硕士"),
    PRE_DOCTORAL_DEGREE("100","博士预科"),
    DOCTORAL_STUDENT("101", "博士在读"),
    DOCTOR("102", "博士"),
    POST_DOCTORAL("103", "博士后"),
    //成功客户列表状态
    A_R_AND_A_P_NOT_CREATED("A_R_AND_A_P_NOT_CREATED", "未创建应收和应付（不含无佣金）"),
    A_R_NOT_CREATED("A_R_NOT_CREATED", "未创建应收（不含无佣金）"),
    A_P_NOT_CREATED("A_P_NOT_CREATED", "未创建应付（不含无佣金）"),
    A_R_CREATED("A_R_CREATED", "已创建应收"),
    A_P_CREATED("A_P_CREATED", "已创建应付"),
    A_R_AND_A_P_CREATED("A_R_AND_A_P_CREATED", "已创建应收和应付"),
    NO_COMMISSION("NO_COMMISSION", "无佣金"),

    EVENT_BILL_NOTICE("EVENT_BILL_NOTICE","活动费用汇总邮件模板"),
    EVENT_INCENTIVE_NOTICE("EVENT_INCENTIVE_NOTICE","奖励推广活动邮件模板"),
    APP_AGENT_ADD_NOTICE("APP_AGENT_ADD_NOTICE","代理申请表单邮件提醒"),
    APP_AGENT_CONTACT_PERSON_NOTICE("APP_AGENT_CONTACT_PERSON_NOTICE","代理申请表单联系人邮件提醒"),
    ENROL_FAILURE_NOTICE("ENROL_FAILURE_NOTICE","成功客户列表入学失败邮件提醒"),
    AGENT_STUDENT_OFFER_DDL_NOTICE("AGENT_STUDENT_OFFER_DDL_NOTICE","支付押金截至/接受offer截至代理联系人邮件提醒"),
    CLIENT_APPROVAL_NOTICE("CLIENT_APPROVAL_NOTICE","学生客户审批通知"),
    STUDENT_RESOURCE_CREATED_NOTICE("STUDENT_RESOURCE_CREATED_NOTICE","学生资源新增学生提醒"),

    COMPETITION_REGISTRATION_NOTICE("COMPETITION_REGISTRATION_NOTICE","竞赛报名提醒通知"),
    //工休单工作流提醒
    HTI_WORKFLOW_LEAVE_FORM("HTI_WORKFLOW_LEAVE_FORM","工休单工作流提醒"),
    //公休日志操作类型
    ANNUAL_REFRESH_ANNUAL_LEAVE("ANNUAL_REFRESH_ANNUAL_LEAVE", "年度刷新年假"),

    // 申请计划费用代付文案（ARC）邮件提醒
    STUDENT_OFFER_ITEM_PAYMENT_ARC_NOTICE("STUDENT_OFFER_ITEM_PAYMENT_ARC_NOTICE", "申请计划费用代付文案（ARC）邮件提醒"),
    // 学生资源过期模版
    STUDENT_RESOURCE_EXPIRED_NOTICE("STUDENT_RESOURCE_EXPIRED_NOTICE", "学生资源过期模版"),
    // HTI 当活动变化邮件提醒
    HTI_EVENT_CHANGE_NOTICE("HTI_EVENT_CHANGE_NOTICE", "HTI活动变化邮件提醒"),

    OVERTIME_TO_INCREASE_COMPENSATORY_TIME_OFF("OVERTIME_TO_INCREASE_COMPENSATORY_TIME_OFF", "加班增加补休"),
    COMPENSATORY_LEAVE_DEDUCTION("COMPENSATORY_LEAVE_DEDUCTION", "补休请假扣减"),
    ANNUAL_LEAVE_DEDUCTION("ANNUAL_LEAVE_DEDUCTION", "年假请假扣减"),
    WORKFLOW_CANCELLATION("WORKFLOW_CANCELLATION", "工作流撤单"),
    HUMAN_RESOURCES_UPDATE("HUMAN_RESOURCES_UPDATE", "人事手动调整"),
    HUMAN_RESOURCES_ADD("HUMAN_RESOURCES_ADD", "人事手动新增"),
    EXPIRY_DATE_RESET("EXPIRY_DATE_RESET", "超有效期清零"),
    UNPAID_LEAVE_DEDUCTION("UNPAID_LEAVE_DEDUCTION", "无薪事假扣减"),
    SICK_LEAVE_DEDUCTION("SICK_LEAVE_DEDUCTION", "病假扣减"),
    ONLINE_WORK_ADD("ONLINE_WORK_ADD", "在家办公增加"),
    OUTSOURCING_ADD("OUTSOURCING_ADD", "外出申请增加"),
    EVECTION_ADD("EVECTION_ADD", "出差增加"),
    PREMARITAL_EXAMINATION_DEDUCTION("PREMARITAL_EXAMINATION_DEDUCTION", "婚前体检扣减"),
    MARRIAGE_VACATION_DEDUCTION("MARRIAGE_VACATION_DEDUCTION", "婚假扣减"),
    MATERNITY_CHECK_VACATION_DEDUCTION("MATERNITY_CHECK_VACATION_DEDUCTION", "产检假扣减"),
    MATERNITY_VACATION_DEDUCTION("MATERNITY_VACATION_DEDUCTION", "产假扣减"),
    ESCORT_VACATION_DEDUCTION("ESCORT_VACATION_DEDUCTION", "陪产假扣减"),
    FUNERAL_VACATION_DEDUCTION("FUNERAL_VACATION_DEDUCTION", "丧假扣减"),
    WORK_RELATED_VACATION_DEDUCTION("WORK_RELATED_VACATION_DEDUCTION", "工伤假扣减"),
    SICK_LEAVE_QUARTERLY_REFRESH("SICK_LEAVE_QUARTERLY_REFRESH", "病假季度刷新"),
    SYSTEM_ADD_STOCK("SYSTEM_ADD_STOCK", "系统回补作废库存"),

    //平台类型
    SCHOOL_GATE("SCHOOL_GATE","SCHOOL_GATE"),


    SUCCESSFUL_CUSTOM_ENROLL_FAILURE("SUCCESSFUL_CUSTOM_ENROLL_FAILURE","成功客户列表入学失败"),
    APPLICATION_STEPS_ENROLL_FAILURE("APPLICATION_STEPS_ENROLL_FAILURE","申请步骤设置入学失败"),
    BATCH_ENROLL_FAILURE("BATCH_ENROLL_FAILURE","设置一键入学失败"),

    //提醒中心财务邮箱地址
    REMINDER_FINANCE_EMAIL("REMINDER_FINANCE_EMAIL","提醒中心财务邮箱地址"),

    //当创建申请计划时邮件提醒，value1=相同学校+课程（忽略-1），对创建人直属上司进行提醒：0不开启，1开启。
    REMINDER_EMAIL_CREATE_OFFER_ITEM("REMINDER_EMAIL_CREATE_OFFER_ITEM","提醒中心创建offer邮件提醒"),

    //KPI考核人员涉及部门配置
    KPI_PLAN_STAFF_DEPARTMENT("KPI_PLAN_STAFF_DEPARTMENT","KPI考核人员涉及部门配置"),

    //是否开启增加收到申请资料时间开关，value1=0/1为总开关，0不开启，1开启
    ADD_RECEIVED_APPLICATION_DATA_DATE("ADD_RECEIVED_APPLICATION_DATA_DATE","是否开启增加收到申请资料时间开关，value1=0/1为总开关，0不开启，1开启"),

    //提成申请服务费类型
    COMMISSION_STUDENT_SERVICE_FEE("APPLICATION+VISA_SERVICE_FEE","申请+签证服务费"),

    //提成步骤：其他
//    COMMISSION_STEP_OTHERS("OTHER","其他"),
    COMMISSION_STEP_OTHER_INSTITUTION("OTHER_INSTITUTION","其他-院校加申"),
    COMMISSION_STEP_OTHER_MAJOR("OTHER_MAJOR","其他-专业方向加申"),
    COMMISSION_STEP_ADDAPP("ADD_APPLICATION","加申"),
    COMMISSION_STEP_INSTITUTION("COLLEGE_COMMISSION","院校佣金"),

    SETTLEMENT_DOWNLOAD_FORM_JUMPTO_STEP3("SETTLEMENT_DOWNLOAD_FORM_JUMPTO_STEP3","佣金结算下载对账单直接跳转到第三步，value1=0/1为总开关，0不开启，1开启"),
    REMINDER_EMAIL_CREATE_STUDENT_OFFER("REMINDER_EMAIL_CREATE_STUDENT_OFFER","发邮件提醒项目成员，value1=0/1为总开关，0不发，1发"),
    REMINDER_COMMISSION_NOTICE("REMINDER_COMMISSION_NOTICE","申请计划佣金通知"),
    REMINDER_EMAIL_STEP_ADMITTED("REMINDER_EMAIL_STEP_ADMITTED","发邮件提醒项目成员，value1=0/1为总开关，0不发，1发"),
    REMINDER_EMAIL_STEP_APPLY_REFUND("REMINDER_EMAIL_STEP_APPLY_REFUND","邮件提醒佣金账户，value1=0/1为总开关，0不发，1发，value2=佣金账户(根据不同国家通知)"),
    REMINDER_EMAIL_EDIT_OFFER_ITEM_AFTER_ARAP("REMINDER_EMAIL_EDIT_OFFER_ITEM_AFTER_ARAP","发邮件提醒佣金部，当申请计划已经创建应收应付后，还需要修改字段时"),
    REMINDER_EMAIL_STEP_OFFER_SELECTION("REMINDER_EMAIL_STEP_OFFER_SELECTION","发邮件提醒项目成员+佣金账户，value1=0/1为总开关，0不发，1发，value2=佣金账户(根据不同国家通知)"),
    REMINDER_EMAIL_STEP_FAILURE("REMINDER_EMAIL_STEP_FAILURE","发邮件提醒佣金账户，入学失败（Failure/Rejected），value1=0/1为总开关，0不发，1发，value2=佣金账户(根据不同国家通知)"),
    NEWS_EMAIL_NOTICE("NEWS_EMAIL_NOTICE","新增新闻信息邮件"),
    STEP_ENROLLED_OPT_LIMIT("STEP_ENROLLED_OPT_LIMIT","【入学登记完成】步骤操作限制（上传凭证，必传），value1=0/1为总开关，0不开启，1开启"),
    AUTO_RELATION_RECEIPT_ENROLLED("AUTO_RELATION_RECEIPT_ENROLLED","留学申请业务，学费佣金到账后自动关绑入学Enrolled步骤，value1=0/1为总开关，0不开启，1开启"),
    RECEIPT_FORM_RECEIPT_DATE_TO_INVOICE("RECEIPT_FORM_RECEIPT_DATE_TO_INVOICE","当绑定实收时，收款单收款日期同步到发票实收日期"),
    ONE_CLICK_FAILURE_STATUS("ONE_CLICK_FAILURE_STATUS","ONE_CLICK_FAILURE_STATUS"),
    ASSIGN_PROJECT_MEMBERS_LIMIT("ASSIGN_PROJECT_MEMBERS_LIMIT","分配项目成员限制开关，value1=0/1为总开关，0不开启，1开启，value2=禁止修改项目成员的状态"),
    SUCCESSFUL_LIST_DEFAULT_STEP("SUCCESSFUL_LIST_DEFAULT_STEP","成功客户列表，默认步骤显示设置"),
    STEP_FALLBACK_OPT_LIMIT("STEP_FALLBACK_OPT_LIMIT","设置特殊步骤禁止回退操作显示"),
    SEND_NEWS_EMAIL_ACCOUNT("SEND_NEWS_EMAIL_ACCOUNT","发送新闻邮件账号配置"),
    REMINDER_EMAIL_CLIENT("REMINDER_EMAIL_CLIENT","作废学生资源邮件通知配置"),
    SYNC_STEP_UK_SUB_TO_MAIN("SYNC_STEP_UK_SUB_TO_MAIN","子课同步到主计划步骤"),
    RECEIPT_FORM_OPT_LIMIT("RECEIPT_FORM_OPT_LIMIT","收款单，操作限制，收款日期必填，value1=0/1为总开关，0不开启，1开启"),
    SUCCESSFUL_LIST_NO_COMMISSION_REMARK("SUCCESSFUL_LIST_NO_COMMISSION_REMARK","成功客户列表，设置无佣金，备注必填开关，value1=0/1为总开关，0不开启，1开启"),
    SUCCESSFUL_LIST_PROJECT_MEMBER_SHOW("SUCCESSFUL_LIST_PROJECT_MEMBER_SHOW", "成功客户列表，项目成员显示列设置，有值才显示该列"),
    //gea发票和hit发票config key
    INVOICE_LIST_LATE_RECEIVE("INVOICE_LIST_LATE_RECEIVE","发票列表，迟收款高亮提醒：value1=发票创建时间（开始提醒时间），value2=迟收款多长时间提醒（天）"),
    EDIT_OFFER_ITEM_AFTER_ARAP_LIMIT("EDIT_OFFER_ITEM_AFTER_ARAP_LIMIT","当申请计划实收实付后限制不能进行编辑，但财务或佣金部除外。value1=设置允许编辑部门"),

    SETTLEMENT_COMMISSION_LIST_LIMIT("SETTLEMENT_COMMISSION_LIST_LIMIT", "佣金结算列表显示限制，增加显示预付标记的应付计划开关，value1=0/1为总开关，0不开启，1开启"),

    PAY_IN_ADVANCE_SERVICE_FEE("PAY_IN_ADVANCE_SERVICE_FEE", "预付手续费"),
    STEP_OS_EDIT_LIMIT("STEP_OS_EDIT_LIMIT ","【OS】后的步骤编辑，需要进行限制，这里设置哪些步骤需要限制"),
    STUDENT_SHARED_PATH_REQUIRED("STUDENT_SHARED_PATH_REQUIRED ","学生共享文件夹路径必填限制"),

    ADD_EVENT_PLAN_REGISTRATION_EMAIL("ADD_EVENT_PLAN_REGISTRATION_EMAIL","学校提交活动年度计划报名表单邮件通知模板"),

    //来源类型
    CLIENT_SOURCE_TYPE_BMS("bms_student_num", "AIS"),
    CLIENT_SOURCE_TYPE_BMS_NOT_OS("bms_student_num_not_os","AIS NOT OS"),
    CLIENT_SOURCE_TYPE_CRM("crm_contract_num","CRM"),
    CLIENT_SOURCE_TYPE_AGENT("m_agent","渠道代理（Agent）"),
    CLIENT_SOURCE_TYPE_BUSINESS_PROVIDER("m_business_provider","本地市场来源（Local Sources）"),
//    CLIENT_SOURCE_TYPE_MARKETING_DP("marketing_dp","市场部"),
//    CLIENT_SOURCE_TYPE_CLASSMATES("classmates","同学介绍"),
//    CLIENT_SOURCE_TYPE_OTHER("other","其他"),

    //无任何业务类型
    NO_FUNDING_PLAN("1","无任何业务类型"),
    //只有留学申请方案，没有申请计划
    NO_APPLICATION_PLAN("2","只有留学申请方案，没有申请计划"),
    //只有留学申请计划
    APPLICATION_PLAN_ONLY("3","只有留学申请计划"),
    //只有留学保险
    APPLICATION_PLAN_AND_INSURANCE_ONLY("4","只有留学保险"),
    //只有留学住宿
    APPLICATION_PLAN_AND_ACCOMMODATION_ONLY("5","只有留学住宿"),
    //只有留学服务费
    APPLICATION_PLAN_AND_SERVICE_FEE_ONLY("6","只有留学服务费"),
    //含留学申请计划
    APPLICATION_PLAN("7","含留学申请计划"),
    //含留学保险
    APPLICATION_PLAN_AND_INSURANCE("8","含留学保险"),
    //含留学住宿
    APPLICATION_PLAN_AND_ACCOMMODATION("9","含留学住宿"),
    //含留学服务费
    APPLICATION_PLAN_AND_SERVICE_FEE("10","含留学服务费"),

    //发送新闻代理邮件
    SEND_NEWS_AGENT_EMAIL("SEND_NEWS_AGENT_EMAIL","发送新闻代理邮件"),


    //竞赛中心自助情况
    NO_FUNDING("full_fee","無津貼"),
    HALF_FEE("half_fee","半津 - 半價報名"),
    FULL_COST("funding","全津 - 免費報名"),

    //prize_type奖品类型
    PRIZE_TYPE_PERCENT("1","100分奖品"),
    PRIZE_TYPE_SEVENTY("2","70分奖品"),

    // 易思汇支付支持的币种
    EASY_TRANSFER_USE_CURRENCY_USD("USD", "美元（USD）"),
    EASY_TRANSFER_USE_CURRENCY_EUR("EUR", "欧元（EUR）"),
    EASY_TRANSFER_USE_CURRENCY_GBP("GBP", "英镑（GBP）"),
    EASY_TRANSFER_USE_CURRENCY_CAD("CAD", "加元（CAD）"),
    EASY_TRANSFER_USE_CURRENCY_AUD("AUD", "澳大利亚元（AUD）"),
    EASY_TRANSFER_USE_CURRENCY_JPY("JPY", "日元（JPY）"),
    EASY_TRANSFER_USE_CURRENCY_HKD("HKD", "港元（HKD）"),
    EASY_TRANSFER_USE_CURRENCY_CHF("CHF", "瑞士法郎（CHF）"),
    EASY_TRANSFER_USE_CURRENCY_SGD("SGD", "新加坡元（SGD）"),
    EASY_TRANSFER_USE_CURRENCY_NZD("NZD", "新西兰元（NZD）"),
    EASY_TRANSFER_USE_CURRENCY_KRW("KRW", "韩元（KRW）"),
    EASY_TRANSFER_USE_CURRENCY_THB("THB", "泰铢（THB）"),
    EASY_TRANSFER_USE_CURRENCY_SEK("SEK", "瑞典克朗（SEK）"),
    EASY_TRANSFER_USE_CURRENCY_MYR("MYR", "马来西亚林吉特（MYR）"),
    EASY_TRANSFER_USE_CURRENCY_DKK("DKK", "丹麦克朗（DKK）"),

    //劳动合同提醒参数
    REMINDER_EMAIL_CONTRACT_EXPIRATION("REMINDER_EMAIL_CONTRACT_EXPIRATION","REMINDER_EMAIL_CONTRACT_EXPIRATION"),

    //活动计划报名邮件提醒
    REMINDER_EMAIL_EVENT_PLAN_REGISTRATION("REMINDER_EMAIL_EVENT_PLAN_REGISTRATION","REMINDER_EMAIL_EVENT_PLAN_REGISTRATION"),

    //学校提供商合同到期提醒
    REMINDER_EMAIL_PROVIDER_CONTRACT_EXPIRATION("REMINDER_EMAIL_PROVIDER_CONTRACT_EXPIRATION","REMINDER_EMAIL_PROVIDER_CONTRACT_EXPIRATION"),

    //邮件提醒，根据业务数据所属分公司，设置使用不同语言版本的邮件模板（zh/en）
    REMINDER_EMAIL_LANGUAGE_VERSION("REMINDER_EMAIL_LANGUAGE_VERSION","REMINDER_EMAIL_LANGUAGE_VERSION"),

    // 邮件提醒，根据业务数据所属分公司，设置使用不同语言邮件提醒时间
    REMINDER_EMAIL_AGENT_CONTRACT_EXPIRATION("REMINDER_EMAIL_AGENT_CONTRACT_EXPIRATION","REMINDER_EMAIL_AGENT_CONTRACT_EXPIRATION"),

    //学生资源新增学生提醒，value1=默认通知员工id（BD助理），空为不通知
    REMINDER_EMAIL_CLIENT_ADD("REMINDER_EMAIL_CLIENT_ADD","REMINDER_EMAIL_CLIENT_ADD"),

    //PMP各公司佣金折算比例（单位为百分比）
    PMP_AGENT_COMMISSION_RATE("PMP_AGENT_COMMISSION_RATE","PMP_AGENT_COMMISSION_RATE"),

    //PMP审批人通知，value1=默认审批人列表，选择后发邮件通知
    REMINDER_EMAIL_PMP_APPROVE("REMINDER_EMAIL_PMP_APPROVE","REMINDER_EMAIL_PMP_APPROVE");
    ;

    /**
     * 易思汇支持的币种
     */
    public static final ProjectKeyEnum[] EASY_TRANSFER_USE_CURRENCY = new ProjectKeyEnum[]{
            EASY_TRANSFER_USE_CURRENCY_USD,
            EASY_TRANSFER_USE_CURRENCY_EUR,
            EASY_TRANSFER_USE_CURRENCY_GBP,
            EASY_TRANSFER_USE_CURRENCY_CAD,
            EASY_TRANSFER_USE_CURRENCY_AUD,
            EASY_TRANSFER_USE_CURRENCY_JPY,
            EASY_TRANSFER_USE_CURRENCY_HKD,
            EASY_TRANSFER_USE_CURRENCY_CHF,
            EASY_TRANSFER_USE_CURRENCY_SGD,
            EASY_TRANSFER_USE_CURRENCY_NZD,
            EASY_TRANSFER_USE_CURRENCY_KRW,
            EASY_TRANSFER_USE_CURRENCY_THB,
            EASY_TRANSFER_USE_CURRENCY_SEK,
            EASY_TRANSFER_USE_CURRENCY_MYR,
            EASY_TRANSFER_USE_CURRENCY_DKK
    };

    /**
     * prize_type奖品类型
     */
    public static final ProjectKeyEnum[] PRIZE_TYPE = new ProjectKeyEnum[]{PRIZE_TYPE_PERCENT,PRIZE_TYPE_SEVENTY};
    /**
     * 资助类型
     */
    public static final ProjectKeyEnum[] TYPE_OF_FUNDING = new ProjectKeyEnum[]{NO_FUNDING,HALF_FEE,FULL_COST};

    /**
     * 业务类型
     */
    public static final ProjectKeyEnum[] BUSINESS_TYPE = new ProjectKeyEnum[]{M_STUDENT_INSURANCE, M_STUDENT_ACCOMMODATION,M_STUDENT_SERVICE_FEE_COST,CLIENT_SOURCE};
    /**
     * 平台类型
     */
    public static final ProjectKeyEnum[] PLATFORM_TYPE = new ProjectKeyEnum[]{GET_MSO, GET_ISSUE,GET_ISSUE_OLD, GET_IB, GET_BMS, GET_MP_EXAM, CPP,GET_HKISO,APP_SCHOOL_GATE};
    /**
     * 学生来源
     */
    public static final ProjectKeyEnum[] STUDENT_SOURCE = new ProjectKeyEnum[]{GET_BMS, GET_MSO, GET_ISSUE,GET_ISSUE_OLD,GET_HKISO};
    /**
     * 申请资料类型中文
     */
    public static final ProjectKeyEnum[] APP_INFO_TYPE = new ProjectKeyEnum[]{APP_COURSE_WEBSITE,
            APP_COURSE_REQ_WEBSITE,
            APP_COURSE_OTHER_WEBSITE,
            SCHOLARSHIP_INFO,
            SCHOLARSHIP_WEBSITE,
            APP_UCAS_CODE,
            APP_CRICOS_CODE,
            APP_PROGRAM_CODE,
            ADMISSION_REQUIREMENTS,
            PERSONAL_STATEMENT,
            INTERVIEW_PREPARATION,
            CNCC
    };


    //学历
    public static final ProjectKeyEnum[] EDUCATION = {
            KINDERGARTEN,
            PRIMARY_SCHOOL,
            JUNIOR_HIGH_SCHOOL,
            SECONDARY_SPECIALIZED_SCHOOL,
            TECHNICAL_SCHOOL,
            HIGH_SCHOOL_VOCATIONAL_HIGH_SCHOOL,
            HIGH_SCHOOL,
            HIGH_SCHOOL_ALEVEL,
            HIGH_SCHOOL_AP,
            HIGH_SCHOOL_IB,
            HIGH_SCHOOL_AUSTRALIAN_COLLEGE_ENTRANCE_EXAMINATION_SYSTEM,
            HIGH_SCHOOL_CANADIAN_HIGH_SCHOOL_DIPLOMA,
            HIGH_SCHOOL_NEW_ZEALAND_NCEA,
            HIGH_SCHOOL_HKCEE,
            UNDERGRADUATE_FOUNDATION,
            CERTIFICATE,
            DIPLOMA,
            ADVANCED_DIPLOMA,
            JUNIOR_COLLEGE,
            HND_UK,
            ASSOCIATE_DEGREE,
            UPGRADING_FROM_COLLEGE_TO_UNIVERSITY,
            UNDERGRADUATE,
            GRADUATE_STUDENT,
            MASTER,
            PRE_DOCTORAL_DEGREE,
            DOCTORAL_STUDENT,
            DOCTOR,
            POST_DOCTORAL
    };
    /**
     * 语言类型
     */
    public static final ProjectKeyEnum[] LANGUAGE_TYPE = new ProjectKeyEnum[]{ZH_CN, ZH_HK, ZH_TW, EN_US};
    /**
     * 收款目标类型
     */
    public static final ProjectKeyEnum[] RECEIPT_TARGET_TYPE = new ProjectKeyEnum[]{
            INSTITUTION_PROVIDER,
//            INSTITUTION_PROVIDER_CHANNEL,
//            BUSINESS_CHANNEL_INS,
            BUSINESS_PROVIDER_INS,
//            BUSINESS_CHANNEL_ACC,
            BUSINESS_PROVIDER,
            M_STUDENT
    };

    /**
     * 国家资讯模块类型
     */
    public static final ProjectKeyEnum[] AGENT_MODULE = new ProjectKeyEnum[]{INFO_CHANNEL_NEWS, INFO_CHANNEL_COUNTRY, COUNTRY_CHANNEL};
    /**
     * 公国家业务区域
     */
    public static final ProjectKeyEnum[] COUNTRY_BUSINESS_AREA = new ProjectKeyEnum[]{COUNTRY_BUSINESS_GLB, COUNTRY_BUSINESS_US, COUNTRY_BUSINESS_UK, COUNTRY_BUSINESS_ANZ, COUNTRY_BUSINESS_CAN, COUNTRY_BUSINESS_EUASIA};
    /**
     * 文件类型
     */
    public static final ProjectKeyEnum[] FILE_EXTENSION = new ProjectKeyEnum[]{PNG, GIF, JPEG, JPG, PDF};
    /**
     * 市场部部门编号
     */
    public static final ProjectKeyEnum[] MARKETING_DEPARTMENT_NUM_GEA = new ProjectKeyEnum[]{MARKETING_DEPARTMENT, MARKETING_DEPARTMENT_INSTITUTION};
    /**
     * 菜单链接类型
     */
    public static final ProjectKeyEnum[] MENU_LINK_TYPE = new ProjectKeyEnum[]{MENU_LINK_TYPE_BLANK, MENU_LINK_TYPE_URL, MENU_LINK_TYPE_COMPONENT,
            MENU_LINK_TYPE_NEWS_INFO, MENU_LINK_TYPE_COUNTRY_INFO, MENU_LINK_TYPE_COUNTRY_CHANNEL, COUNTRY_ABOUT, NEWS_ABOUT, NEWS_DETAILS_ID, INFO_CHANNEL_NEWS, PAGE_TEMPLATE};
    /**
     * 工休日志操作类型
     */
    public static final ProjectKeyEnum[] OPT_TYPE = new ProjectKeyEnum[]{ANNUAL_REFRESH_ANNUAL_LEAVE, OVERTIME_TO_INCREASE_COMPENSATORY_TIME_OFF, COMPENSATORY_LEAVE_DEDUCTION,
            ANNUAL_LEAVE_DEDUCTION, WORKFLOW_CANCELLATION, HUMAN_RESOURCES_UPDATE, HUMAN_RESOURCES_ADD, EXPIRY_DATE_RESET, UNPAID_LEAVE_DEDUCTION, SICK_LEAVE_DEDUCTION, SICK_LEAVE_QUARTERLY_REFRESH};
    /**
     * 工休日志操作类型
     */
    public static final ProjectKeyEnum[] REMINDER_MODULAR = new ProjectKeyEnum[]{INSTITUTION_CENTER, SALE_CENTER, FINANCE_CENTER, RESUME_CENTER, REMINDER_CENTER, PERMISSION_CENTER, HELP_CENTER, EXAM_CENTER, VOTE_CENTER, SYSTEM_CENTER, BUSINESS_CONFIG_CENTER, LOG_CENTER};

    public static final ProjectKeyEnum[] REMINDER_COUNT = new ProjectKeyEnum[]{WORKFLOW_CENTER_TO_SIGN, WORKFLOW_CENTER_TO_DO, WORKFLOW_CENTER,OFFICE_CENTER};

    /**
     * 应收应付状态
     */
    public static final ProjectKeyEnum[] A_R_AND_A_P_STATUS = new ProjectKeyEnum[]{A_R_AND_A_P_NOT_CREATED, A_R_NOT_CREATED, A_P_NOT_CREATED, A_R_CREATED, A_P_CREATED, A_R_AND_A_P_CREATED, NO_COMMISSION};
    /**
     * ISSUE 状态
     */
    public static final ProjectKeyEnum[] ISSUE_STATUS = new ProjectKeyEnum[]{N, T, C, E, K, S, R};

    /**
     * 发送邮件模板key
     */
    public static final ProjectKeyEnum[] SPECIAL_REMINDER_TEMPLATE_KEY = new ProjectKeyEnum[]{EVENT_BILL_NOTICE,EVENT_INCENTIVE_NOTICE};
    /**
     * 平台类型
     */
    public static final ProjectKeyEnum[] PLAT_FORM = new ProjectKeyEnum[]{SCHOOL_GATE};
    /**
     * iae业务部门
     */
    public static final ProjectKeyEnum[] IAE_BUSINESS_DEPARTMENT = new ProjectKeyEnum[]{
            IAE_ID07,
            IAE_ID08,
            IAE_ID09,
            IAE_ID11,
            IAE_ID12};

    /**
     *  业绩统计GEA定校量相关步骤列表
     */
    public static final ProjectKeyEnum[] GEA_CONFIRMATION_STATISTICS_STEP = new  ProjectKeyEnum[]{STEP_DEPOSIT_PAID,STEP_OFFER_SELECTION,STEP_VISA_SUBMITTED,STEP_VISA_GRANTED,STEP_ENROLLED};

    /**
     * COE后置步骤
     */
    public static final ProjectKeyEnum[] COE_POST_STEP = new  ProjectKeyEnum[]{STEP_OFFER_SELECTION,STEP_VISA_SUBMITTED,STEP_VISA_GRANTED,STEP_ENROLLED};
    /**
     *  业绩统计GEA成功量相关步骤列表
     */
    public static final ProjectKeyEnum[] GEA_SUCCESS_STATISTICS_STEP = new  ProjectKeyEnum[]{STEP_ENROLLED};
    /**
     *  业绩统计IAE定校量相关步骤列表
     */
    public static final ProjectKeyEnum[] IAE_CONFIRMATION_STATISTICS_STEP = new  ProjectKeyEnum[]{STEP_OFFER_SELECTION,STEP_VISA_SUBMITTED,STEP_VISA_GRANTED,STEP_ENROLLED};
    /**
     *  业绩统计IAE成功量相关步骤列表
     */
    public static final ProjectKeyEnum[] IAE_SUCCESS_STATISTICS_STEP = new  ProjectKeyEnum[]{STEP_ENROLLED};

    /**
     *  业绩统计HTI定校量相关步骤列表
     */
    public static final ProjectKeyEnum[] HTI_CONFIRMATION_STATISTICS_STEP = new  ProjectKeyEnum[]{STEP_DEPOSIT_PAID,STEP_OFFER_SELECTION,STEP_VISA_SUBMITTED,STEP_VISA_GRANTED,STEP_ENROLLED};
    /**
     *  业绩统计HTI成功量相关步骤列表
     */
    public static final ProjectKeyEnum[] HTI_SUCCESS_STATISTICS_STEP = new  ProjectKeyEnum[]{STEP_OFFER_SELECTION,STEP_VISA_SUBMITTED,STEP_VISA_GRANTED,STEP_ENROLLED};



    /**
     *  延迟入学添加提醒任务 相关步骤列表
     */
    public static final ProjectKeyEnum[] GEA_DEFERENTRANCE_STEP = new  ProjectKeyEnum[]{STEP_DEPOSIT_PAID,STEP_TUITION_PAID,STEP_APPLICATION_EXTENSION,STEP_POSTPONED,STEP_APPLY_REFUND,STEP_OFFER_SELECTION,STEP_VISA_SUBMITTED,STEP_VISA_GRANTED,STEP_ENROLLED};

    /**
     * 子课同步步骤到主课
     */
    public static final ProjectKeyEnum[] SYN_MAIN_TYPE = new  ProjectKeyEnum[]{STEP_ADMITTED,STEP_OFFER_SELECTION,STEP_ENROLLED};

    /**
     * 待审核申请步骤
     */
    public static final ProjectKeyEnum[] AUDIT_STEP = new  ProjectKeyEnum[]{STEP_OFFER_SELECTION,STEP_VISA_SUBMITTED,STEP_VISA_GRANTED,STEP_ENROLLED};

    /**
     *   业务类型
     */
    public static final ProjectKeyEnum[] BUSINESS_SELECT_TYPE = new  ProjectKeyEnum[]{NO_FUNDING_PLAN,NO_APPLICATION_PLAN,
            APPLICATION_PLAN_ONLY,APPLICATION_PLAN_AND_INSURANCE_ONLY,APPLICATION_PLAN_AND_ACCOMMODATION_ONLY,
            APPLICATION_PLAN_AND_SERVICE_FEE_ONLY,APPLICATION_PLAN,APPLICATION_PLAN_AND_INSURANCE,APPLICATION_PLAN_AND_ACCOMMODATION,
            APPLICATION_PLAN_AND_SERVICE_FEE};


    /**
     * 来源类型
     */
    public static final ProjectKeyEnum[] CLIENT_SOURCE_TYPE = new  ProjectKeyEnum[]{CLIENT_SOURCE_TYPE_BMS,CLIENT_SOURCE_TYPE_BMS_NOT_OS,CLIENT_SOURCE_TYPE_CRM,CLIENT_SOURCE_TYPE_AGENT,CLIENT_SOURCE_TYPE_BUSINESS_PROVIDER};

    /**
     * 公开对象
     */
    // public static final ProjectKeyEnum[] PUBLIC_OBJECTS= new ProjectKeyEnum[]{NOT_PUBLIC, PUBLIC,PUBLIC_STUDENTS,PUBLIC_AGENCY};
    public String key;
    public String value;
    ProjectKeyEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public static String getValue(String key) {
        ProjectKeyEnum[] projectKeyEnums = values();
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(key) && fkCompanyId == 3) {
            return ProjectKeyEnum.IAE_INSTITUTION_PROVIDER_CHANNEL.value;
        }
        for (ProjectKeyEnum projectKeyEnum : projectKeyEnums) {
            if (projectKeyEnum.key.equals(key)) {
                return LocaleMessageUtils.getMessage(projectKeyEnum.key());
            }
        }
        return null;
    }

    /**
     * 直接取出原始value,不执行国际化
     *
     * @Date 14:09 2021/11/26
     * <AUTHOR>
     */
    public static String getInitialValue(String key) {
        ProjectKeyEnum[] projectKeyEnums = values();
        if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(key)) {
            return ProjectKeyEnum.IAE_INSTITUTION_PROVIDER_CHANNEL.value;
        }
        for (ProjectKeyEnum projectKeyEnum : projectKeyEnums) {
            if (projectKeyEnum.key.equals(key)) {
                return projectKeyEnum.value;
            }
        }
        return null;
    }

    public static String getInitialValue(String key,ProjectKeyEnum[] projectKeyEnums) {
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        if (ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key.equals(key) && fkCompanyId == 3) {
            return ProjectKeyEnum.IAE_INSTITUTION_PROVIDER_CHANNEL.value;
        }
        for (ProjectKeyEnum projectKeyEnum : projectKeyEnums) {
            if (projectKeyEnum.key.equals(key)) {
                return projectKeyEnum.value;
            }
        }
        return null;
    }

    public static String getKey(String value) {
        ProjectKeyEnum[] projectKeyEnums = values();
        for (ProjectKeyEnum projectKeyEnum : projectKeyEnums) {
            if (projectKeyEnum.value().equals(value)) {
                return projectKeyEnum.key();
            }
        }
        return null;
    }

    /**
     * 将枚举转换为list
     *
     * @param enums
     * @return
     */
    public static List<Map<String, Object>> enums2Arrays(ProjectKeyEnum[] enums) {
        if (GeneralTool.isEmpty(enums)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        Arrays.stream(enums).forEach(item -> {
            Map<String, Object> map = new HashMap<>();
//            if (item.key.equals(ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key) && fkCompanyId==3) {
//                item = ProjectKeyEnum.IAE_INSTITUTION_PROVIDER_CHANNEL;
//            }
            map.put("key", item.key);
            map.put("value", item.value);
            list.add(map);
        });
        return list;
    }

    /**
     * 将枚举转换为list
     *
     * @param enums
     * @return
     */
    public static List<Map<String, Object>> enumsTranslation2Arrays(ProjectKeyEnum[] enums) {
        if (GeneralTool.isEmpty(enums)) {
            return new ArrayList<>();
        }
        List<Map<String, Object>> list = new ArrayList<>();
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        Arrays.stream(enums).forEach(item -> {
            Map<String, Object> map = new HashMap<>();
            if (item.key.equals(ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key) && fkCompanyId==3) {
                item = ProjectKeyEnum.IAE_INSTITUTION_PROVIDER_CHANNEL;
                map.put("key", item.key);
                map.put("value", item.value);
            }else {
                map.put("key", item.key);
                map.put("value", LocaleMessageUtils.getMessage(item.key));
            }

            list.add(map);
        });
        return list;
    }

    public static List<String> getValuesByKeys(ProjectKeyEnum[] enums) {
        if (GeneralTool.isEmpty(enums)) {
            return new ArrayList<>();
        }
        List<String> list = new ArrayList<>();
        Arrays.stream(enums).forEach(item -> {
            list.add(item.key);
        });
        return list;
    }

    /**
     * 根据key获取value
     *
     * @return
     */
    public static String getValueByKey(String key, ProjectKeyEnum[] enums) {
//        Long fkCompanyId = SecureUtil.getFkCompanyId();
//        if (key.equals(ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key) && fkCompanyId==3) {
//            return ProjectKeyEnum.IAE_INSTITUTION_PROVIDER_CHANNEL.value;
//        }
        List<ProjectKeyEnum> list = Arrays.asList(enums).stream().filter(e -> e.key().equals(key)).collect(Collectors.toList());
        return GeneralTool.isNotEmpty(list) ? LocaleMessageUtils.getMessage(list.get(0).key()) : "";
    }
    public static String getValueByKey(String key, ProjectKeyEnum[] enums,String local) {
        Long fkCompanyId = SecureUtil.getFkCompanyId();
        if (key.equals(ProjectKeyEnum.INSTITUTION_PROVIDER_CHANNEL.key) && fkCompanyId==3) {
            return ProjectKeyEnum.IAE_INSTITUTION_PROVIDER_CHANNEL.value;
        }
        List<ProjectKeyEnum> list = Arrays.asList(enums).stream().filter(e -> e.key().equals(key)).collect(Collectors.toList());
        return GeneralTool.isNotEmpty(list) ? LocaleMessageUtils.getMessage(local,list.get(0).key()) : "";
    }

    private String key() {
        return this.key;
    }

    private String value() {
        return this.value;
    }

    public static ProjectKeyEnum[] getDynamicArray(String parameter) {
        try {
            // 使用反射获取类的字段
            Field field = ProjectKeyEnum.class.getField(parameter);

            // 获取字段的值（数组）
            Object value = field.get(null);

            // 将值转换为 ProjectKeyEnum[] 类型
            if (value instanceof ProjectKeyEnum[]) {
                return (ProjectKeyEnum[]) value;
            }
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
        }

        // 如果无法获取相应的数组数据，则返回空数组
        return new ProjectKeyEnum[0];
    }
}
