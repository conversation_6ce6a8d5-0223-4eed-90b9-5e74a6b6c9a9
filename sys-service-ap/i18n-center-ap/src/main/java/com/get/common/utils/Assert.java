package com.get.common.utils;

import com.get.core.log.exception.GetServiceException;
import com.get.core.tool.utils.GeneralTool;

public final class Assert {
    private Assert() {
    }

    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new GetServiceException(message);
        }
    }

    public static void isNull(Object object, String message) {
        if (GeneralTool.isNotEmpty(object)) {
            throw new GetServiceException(message);
        }
    }

    public static void notNull(Object object, String message) {
        if (GeneralTool.isEmpty(object)) {
            throw new GetServiceException(message);
        }
    }
}