package com.get.common.eunms;

/**
 * @Description: netty消息推送类型枚举
 * @Param
 * @return
 * <AUTHOR>
 */
public enum MsgActionEnum {
    /**
     * 1, "第一次(或重连)初始化连接"
     */
    CONNECT(1, "第一次(或重连)初始化连接"),

    /**
     * 2, "客户端保持心跳"
     */
    KEEPALIVE(2, "客户端保持心跳"),

    /**
     * 3, "消息签收"
     */
    SEARCH(3, "条件查询");


    public Integer key;
    public String value;

    MsgActionEnum(Integer type, String value) {
        this.key = type;
        this.value = value;
    }

    public Integer getType() {
        return key;
    }
}
